{"model_info": {"filename": "resnet_small_74.2Kparams_perf0.850_small_20250601_175634", "model_path": "test_models\\resnet_small_74.2Kparams_perf0.850_small_20250601_175634.pt", "save_timestamp": "2025-06-01T17:56:34.412410", "parameter_stats": {"total_parameters": 74176, "trainable_parameters": 74176, "non_trainable_parameters": 0, "layer_parameters": {"input_layer.weight": 32768, "input_layer.bias": 128, "hidden_layers.0.0.weight": 16384, "hidden_layers.0.0.bias": 128, "hidden_layers.1.0.weight": 16384, "hidden_layers.1.0.bias": 128, "output_layer.weight": 8192, "output_layer.bias": 64}}}, "training_info": {"epoch": null, "performance": 0.85, "tag": "small"}, "model_architecture": {"model_class": "SimpleResNet", "model_modules": ["", "input_layer", "hidden_layers", "hidden_layers.0", "hidden_layers.0.0", "hidden_layers.0.1", "hidden_layers.0.2", "hidden_layers.1", "hidden_layers.1.0", "hidden_layers.1.1", "hidden_layers.1.2", "output_layer", "relu", "dropout"]}}