#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
GTO正则化方法比较工具

比较不同GTO正则化方法（KL散度、JS散度、L2距离）和自适应权重机制的效果。
"""

import os
import argparse
import time
import yaml
import logging
import json
import numpy as np
from typing import Dict, Any, List, Optional
import matplotlib.pyplot as plt
from collections import defaultdict

import torch

from cardgame_ai.games.doudizhu import DouDizhuEnvironment
from cardgame_ai.algorithms.efficient_zero import EfficientZero
from cardgame_ai.algorithms.gto_approximation import GTOPolicy, GTORegularizer
from cardgame_ai.evaluation import Evaluator
from cardgame_ai.training.gto_trainer import GTOTrainer


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='比较不同GTO正则化方法的效果')
    
    # 基本参数
    parser.add_argument('--config', type=str, default='configs/doudizhu/efficient_zero_config.yaml',
                        help='配置文件路径')
    parser.add_argument('--output_dir', type=str, default='results/gto_comparison',
                        help='输出目录')
    parser.add_argument('--num_episodes', type=int, default=100,
                        help='训练回合数')
    parser.add_argument('--eval_interval', type=int, default=10,
                        help='评估间隔（回合数）')
    parser.add_argument('--seed', type=int, default=42,
                        help='随机种子')
    
    # GTO正则化参数
    parser.add_argument('--gto_policy_path', type=str, default=None,
                      help='GTO策略文件路径')
    parser.add_argument('--methods', type=str, default='kl,js,l2',
                      help='要比较的正则化方法，逗号分隔')
    parser.add_argument('--weights', type=str, default='0.1,0.05,0.01',
                      help='要比较的正则化权重，逗号分隔')
    parser.add_argument('--adaptive', action='store_true',
                      help='是否测试自适应权重')
    
    return parser.parse_args()


def load_config(config_path: str) -> Dict[str, Any]:
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config


def create_experiment_variants(args) -> List[Dict[str, Any]]:
    """创建实验变体"""
    methods = args.methods.split(',')
    weights = [float(w) for w in args.weights.split(',')]
    
    variants = []
    
    # 添加基线（无正则化）
    variants.append({
        'name': 'baseline',
        'use_gto_regularization': False,
        'gto_regularization_method': None,
        'gto_regularization_weight': 0.0,
        'gto_adaptive_weight': False
    })
    
    # 添加各种正则化方法和权重的组合
    for method in methods:
        for weight in weights:
            # 固定权重
            variants.append({
                'name': f'{method}_w{weight}',
                'use_gto_regularization': True,
                'gto_regularization_method': method,
                'gto_regularization_weight': weight,
                'gto_adaptive_weight': False
            })
            
            # 自适应权重（如果启用）
            if args.adaptive:
                variants.append({
                    'name': f'{method}_w{weight}_adaptive',
                    'use_gto_regularization': True,
                    'gto_regularization_method': method,
                    'gto_regularization_weight': weight,
                    'gto_adaptive_weight': True,
                    'gto_adaptive_weight_params': {
                        'max_weight': min(weight * 3, 0.5),
                        'min_weight': max(weight * 0.1, 0.001),
                        'decay_rate': 0.995,
                        'increase_rate': 1.01,
                        'performance_threshold': 0.05,
                        'anneal_steps': 5000
                    }
                })
    
    return variants


def create_agent(config: Dict[str, Any], variant: Dict[str, Any], device: str = None):
    """创建代理"""
    # 获取环境
    env = DouDizhuEnvironment()
    observation_shape = env.observation_space.shape
    action_shape = (env.action_space.n,)
    
    # 创建代理
    agent = EfficientZero(
        state_shape=observation_shape,
        action_shape=action_shape,
        hidden_dim=config['model'].get('hidden_dim', 256),
        state_dim=config['model'].get('state_dim', 64),
        num_simulations=config['mcts'].get('num_simulations', 50),
        batch_size=config['training'].get('batch_size', 128),
        learning_rate=config['training'].get('learning_rate', 0.001),
        
        # GTO正则化参数
        use_gto_regularization=variant['use_gto_regularization'],
        gto_policy_path=variant.get('gto_policy_path', None),
        gto_regularization_method=variant.get('gto_regularization_method', 'kl'),
        gto_regularization_weight=variant.get('gto_regularization_weight', 0.1),
        gto_adaptive_weight=variant.get('gto_adaptive_weight', False),
        gto_adaptive_weight_params=variant.get('gto_adaptive_weight_params', None),
        
        device=device
    )
    
    return agent, env


def run_experiment(config: Dict[str, Any], variant: Dict[str, Any], args, device: str = None) -> Dict[str, Any]:
    """运行单个实验变体"""
    # 创建目录
    output_dir = os.path.join(args.output_dir, variant['name'])
    os.makedirs(output_dir, exist_ok=True)
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        filename=os.path.join(output_dir, 'experiment.log'),
        filemode='w'
    )
    console = logging.StreamHandler()
    console.setLevel(logging.INFO)
    logging.getLogger('').addHandler(console)
    
    # 创建代理和环境
    variant_with_path = variant.copy()
    variant_with_path['gto_policy_path'] = args.gto_policy_path
    agent, env = create_agent(config, variant_with_path, device)
    
    # 创建训练器
    trainer = GTOTrainer(
        save_path=output_dir,
        save_interval=args.eval_interval,
        log_interval=1,
        gto_policy_path=args.gto_policy_path,
        gto_regularization_weight=variant['gto_regularization_weight'] if variant['use_gto_regularization'] else 0.0,
        gto_regularization_method=variant['gto_regularization_method'] if variant['use_gto_regularization'] else 'kl',
        gto_adaptive_weight=variant['gto_adaptive_weight'],
        gto_adaptive_weight_params=variant.get('gto_adaptive_weight_params', None)
    )
    
    # 创建评估器
    evaluator = Evaluator()
    
    def eval_func(env, agent, episode):
        """评估函数"""
        return evaluator.evaluate_agent(env, agent, num_games=20)
    
    # 训练
    metrics = trainer.train_with_self_play(
        env=env,
        agent=agent,
        num_episodes=args.num_episodes,
        games_per_episode=5,
        eval_func=eval_func,
        eval_interval=args.eval_interval,
        batch_size=config['training'].get('batch_size', 128),
        epochs_per_episode=1
    )
    
    # 保存最终模型和指标
    trainer.save(agent, args.num_episodes)
    
    # 保存指标
    with open(os.path.join(output_dir, 'metrics.json'), 'w', encoding='utf-8') as f:
        # 转换numpy数组为列表以便JSON序列化
        metrics_json = {}
        for key, value in metrics.items():
            if isinstance(value, list) and value and isinstance(value[0], dict):
                # 转换字典列表中的numpy值
                new_list = []
                for item in value:
                    new_item = {}
                    for k, v in item.items():
                        if isinstance(v, (np.int32, np.int64, np.float32, np.float64)):
                            new_item[k] = v.item()
                        else:
                            new_item[k] = v
                    new_list.append(new_item)
                metrics_json[key] = new_list
            else:
                metrics_json[key] = value
        
        json.dump(metrics_json, f, indent=2)
    
    return metrics


def compare_metrics(metrics_by_variant: Dict[str, Dict[str, Any]], args):
    """比较不同变体的指标"""
    # 提取评估指标
    eval_metrics = {}
    for name, metrics in metrics_by_variant.items():
        if 'eval_metrics' in metrics:
            episodes = []
            win_rates = []
            
            # 提取评估回合和胜率
            for i, metric in enumerate(metrics['eval_metrics']):
                episode = (i + 1) * args.eval_interval
                win_rate = metric.get('win_rate', 0.0)
                
                episodes.append(episode)
                win_rates.append(win_rate)
            
            eval_metrics[name] = {
                'episodes': episodes,
                'win_rates': win_rates
            }
    
    # 绘制胜率比较图
    plt.figure(figsize=(12, 8))
    
    for name, data in eval_metrics.items():
        plt.plot(data['episodes'], data['win_rates'], marker='o', label=name)
    
    plt.title('不同GTO正则化方法的胜率比较')
    plt.xlabel('训练回合')
    plt.ylabel('胜率')
    plt.legend()
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # 保存图表
    plt.savefig(os.path.join(args.output_dir, 'win_rate_comparison.png'))
    plt.close()
    
    # 提取训练损失
    training_losses = {}
    for name, metrics in metrics_by_variant.items():
        if 'episode_losses' in metrics:
            episodes = []
            policy_losses = []
            value_losses = []
            gto_losses = []
            
            for i, losses in enumerate(metrics['episode_losses']):
                episode = i + 1
                policy_loss = np.mean([loss.get('policy_loss', 0.0) for loss in losses]) if losses else 0
                value_loss = np.mean([loss.get('value_loss', 0.0) for loss in losses]) if losses else 0
                gto_loss = np.mean([loss.get('gto_loss', 0.0) for loss in losses]) if losses else 0
                
                episodes.append(episode)
                policy_losses.append(policy_loss)
                value_losses.append(value_loss)
                gto_losses.append(gto_loss)
            
            training_losses[name] = {
                'episodes': episodes,
                'policy_losses': policy_losses,
                'value_losses': value_losses,
                'gto_losses': gto_losses
            }
    
    # 绘制GTO损失比较图
    plt.figure(figsize=(12, 8))
    
    for name, data in training_losses.items():
        if 'baseline' not in name:  # 基线没有GTO损失
            plt.plot(data['episodes'], data['gto_losses'], marker='.', label=name)
    
    plt.title('不同GTO正则化方法的GTO损失比较')
    plt.xlabel('训练回合')
    plt.ylabel('GTO损失')
    plt.legend()
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # 保存图表
    plt.savefig(os.path.join(args.output_dir, 'gto_loss_comparison.png'))
    plt.close()
    
    # 生成结果摘要
    summary = {}
    for name, data in eval_metrics.items():
        summary[name] = {
            'final_win_rate': data['win_rates'][-1] if data['win_rates'] else 0,
            'max_win_rate': max(data['win_rates']) if data['win_rates'] else 0,
            'avg_win_rate': np.mean(data['win_rates']) if data['win_rates'] else 0
        }
        
        # 添加GTO损失
        if name in training_losses and training_losses[name]['gto_losses']:
            summary[name]['final_gto_loss'] = training_losses[name]['gto_losses'][-1]
            summary[name]['avg_gto_loss'] = np.mean(training_losses[name]['gto_losses'])
    
    # 保存摘要
    with open(os.path.join(args.output_dir, 'summary.json'), 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2)
    
    # 打印结果摘要
    print("\n===== 实验结果摘要 =====")
    print(f"{'变体名称':<25} {'最终胜率':<12} {'最大胜率':<12} {'平均胜率':<12}")
    print("-" * 65)
    
    for name, metrics in summary.items():
        print(f"{name:<25} {metrics['final_win_rate']:<12.4f} {metrics['max_win_rate']:<12.4f} {metrics['avg_win_rate']:<12.4f}")


def main():
    """主函数"""
    args = parse_args()
    
    # 设置随机种子
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(args.seed)
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 加载配置
    config = load_config(args.config)
    
    # 创建实验变体
    variants = create_experiment_variants(args)
    
    # 运行实验
    metrics_by_variant = {}
    for variant in variants:
        print(f"\n===== 运行实验变体: {variant['name']} =====")
        metrics = run_experiment(
            config=config,
            variant=variant,
            args=args,
            device='cuda' if torch.cuda.is_available() else 'cpu'
        )
        metrics_by_variant[variant['name']] = metrics
    
    # 比较结果
    compare_metrics(metrics_by_variant, args)
    
    print(f"\n所有实验完成，结果保存在: {args.output_dir}")


if __name__ == '__main__':
    main() 