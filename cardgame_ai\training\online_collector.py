#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
在线数据收集器模块

提供实时数据收集功能，捕获人机对战过程中的交互数据，
包括状态、动作、奖励、反馈和偏好等信息，用于在线RLHF闭环学习。
"""

import os
import time
import logging
import numpy as np
from typing import Dict, List, Tuple, Any, Optional, Union
from collections import deque

from cardgame_ai.core.base import Experience, Batch
from cardgame_ai.algorithms.replay_buffer import ReplayBuffer

# 设置日志
logger = logging.getLogger(__name__)


class OnlineDataCollector:
    """
    在线数据收集器

    实时收集人机对战过程中的交互数据，包括状态、动作、奖励、反馈和偏好等信息，
    并将这些数据存储在回放缓冲区中，用于在线RLHF闭环学习。
    """

    def __init__(
        self,
        buffer_size: int = 10000,
        use_prioritized_replay: bool = True,
        alpha: float = 0.6,
        beta: float = 0.4,
        beta_increment: float = 0.001,
        epsilon: float = 1e-6,
        feedback_weight: float = 1.0,
        preference_weight: float = 1.0,
        imitation_weight: float = 0.5,
        save_path: Optional[str] = None,
        save_interval: int = 1000,
        device: str = "cpu"
    ):
        """
        初始化在线数据收集器

        Args:
            buffer_size: 回放缓冲区大小
            use_prioritized_replay: 是否使用优先级回放
            alpha: 优先级指数，控制采样概率与优先级的关系
            beta: 重要性采样指数，用于修正优先级采样的偏差
            beta_increment: beta的增量，随着训练进行逐渐增加beta
            epsilon: 小常数，防止优先级为0
            feedback_weight: 反馈权重，控制反馈损失的重要性
            preference_weight: 偏好权重，控制偏好损失的重要性
            imitation_weight: 模仿权重，控制模仿损失的重要性
            save_path: 数据保存路径，如果为None则不保存
            save_interval: 数据保存间隔（收集多少条数据后保存一次）
            device: 计算设备
        """
        self.buffer_size = buffer_size
        self.use_prioritized_replay = use_prioritized_replay
        self.alpha = alpha
        self.beta = beta
        self.beta_increment = beta_increment
        self.epsilon = epsilon
        self.feedback_weight = feedback_weight
        self.preference_weight = preference_weight
        self.imitation_weight = imitation_weight
        self.save_path = save_path
        self.save_interval = save_interval
        self.device = device

        # 创建回放缓冲区
        if use_prioritized_replay:
            from cardgame_ai.algorithms.replay_buffer import PrioritizedReplayBuffer
            self.buffer = PrioritizedReplayBuffer(
                capacity=buffer_size,
                alpha=alpha,
                beta=beta,
                beta_increment=beta_increment,
                epsilon=epsilon
            )
        else:
            self.buffer = ReplayBuffer(capacity=buffer_size)

        # 创建单独的人类反馈缓冲区
        self.feedback_buffer = {
            "feedback": [],  # 存储评分反馈
            "preference": [],  # 存储偏好反馈
            "imitation": []  # 存储模仿学习数据
        }

        # 统计信息
        self.stats = {
            "collected_steps": 0,
            "collected_episodes": 0,
            "collected_feedback": 0,
            "collected_preferences": 0,
            "collected_imitations": 0,
            "last_save_time": time.time()
        }

        # 创建保存目录
        if save_path is not None:
            os.makedirs(save_path, exist_ok=True)
            logger.info(f"创建数据保存目录: {save_path}")

        logger.info(f"初始化在线数据收集器: buffer_size={buffer_size}, use_prioritized_replay={use_prioritized_replay}")

    def collect_step(
        self,
        state: Any,
        action: Any,
        reward: float,
        next_state: Any,
        done: bool,
        human_feedback: Optional[Dict[str, Any]] = None,
        preference_data: Optional[Dict[str, Any]] = None,
        info: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        收集一步交互数据

        Args:
            state: 当前状态
            action: 执行的动作
            reward: 获得的奖励
            next_state: 下一个状态
            done: 是否结束
            human_feedback: 人类反馈，可选，包含评分等信息
            preference_data: 偏好数据，可选，包含偏好比较信息
            info: 额外信息，可选
        """
        # 确保info是字典
        if info is None:
            info = {}

        # 添加人类反馈到info
        if human_feedback is not None:
            info["human_feedback"] = human_feedback
            self.stats["collected_feedback"] += 1

            # 将反馈添加到反馈缓冲区
            if "score" in human_feedback:
                self.feedback_buffer["feedback"].append({
                    "state": state,
                    "action": action,
                    "feedback_score": human_feedback["score"]
                })

        # 添加偏好数据到info
        if preference_data is not None:
            info["preference_data"] = preference_data
            self.stats["collected_preferences"] += 1

            # 将偏好添加到偏好缓冲区
            if "preferred_state" in preference_data and "rejected_state" in preference_data:
                self.feedback_buffer["preference"].append({
                    "preferred_state": preference_data["preferred_state"],
                    "rejected_state": preference_data["rejected_state"]
                })

        # 创建经验对象
        experience = Experience(
            state=state,
            action=action,
            reward=reward,
            next_state=next_state,
            done=done,
            info=info
        )

        # 添加到回放缓冲区
        self.buffer.add(experience)

        # 更新统计信息
        self.stats["collected_steps"] += 1
        if done:
            self.stats["collected_episodes"] += 1

        # 检查是否需要保存数据
        if self.save_path is not None and self.stats["collected_steps"] % self.save_interval == 0:
            self._save_data()

    def collect_imitation_data(
        self,
        state: Any,
        human_action: Any,
        info: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        收集模仿学习数据

        Args:
            state: 当前状态
            human_action: 人类执行的动作
            info: 额外信息，可选
        """
        # 更新统计信息
        self.stats["collected_imitations"] += 1

        # 将模仿数据添加到模仿缓冲区
        self.feedback_buffer["imitation"].append({
            "state": state,
            "human_action": human_action,
            "info": info
        })

    def collect_preference_data(
        self,
        preferred_trajectory: List[Experience],
        rejected_trajectory: List[Experience]
    ) -> None:
        """
        收集偏好数据

        Args:
            preferred_trajectory: 偏好的轨迹
            rejected_trajectory: 拒绝的轨迹
        """
        # 更新统计信息
        self.stats["collected_preferences"] += 1

        # 将偏好数据添加到偏好缓冲区
        self.feedback_buffer["preference"].append({
            "preferred_trajectory": preferred_trajectory,
            "rejected_trajectory": rejected_trajectory
        })

    def get_batch(self, batch_size: int) -> Union[Batch, Tuple[Batch, np.ndarray, np.ndarray]]:
        """
        获取批次数据

        Args:
            batch_size: 批次大小

        Returns:
            Union[Batch, Tuple[Batch, np.ndarray, np.ndarray]]:
                如果使用优先级回放，返回(批次, 索引, 权重)；
                否则返回批次
        """
        return self.buffer.sample(batch_size)

    def get_human_feedback_batch(self, batch_size: int) -> Dict[str, Any]:
        """
        获取人类反馈批次

        Args:
            batch_size: 批次大小

        Returns:
            Dict[str, Any]: 人类反馈批次，包含以下字段：
                - feedback_states: 反馈状态
                - feedback_actions: 反馈动作
                - feedback_scores: 反馈评分
                - preferred_states: 偏好状态
                - rejected_states: 拒绝状态
                - imitation_states: 模仿状态
                - human_actions: 人类动作
        """
        # 初始化批次数据
        batch = {
            "feedback_states": [],
            "feedback_actions": [],
            "feedback_scores": [],
            "preferred_states": [],
            "rejected_states": [],
            "imitation_states": [],
            "human_actions": []
        }

        # 采样反馈数据
        if self.feedback_buffer["feedback"]:
            feedback_indices = np.random.choice(
                len(self.feedback_buffer["feedback"]),
                min(batch_size, len(self.feedback_buffer["feedback"])),
                replace=False
            )
            for idx in feedback_indices:
                feedback = self.feedback_buffer["feedback"][idx]
                batch["feedback_states"].append(feedback["state"])
                batch["feedback_actions"].append(feedback["action"])
                batch["feedback_scores"].append(feedback["feedback_score"])

        # 采样偏好数据
        if self.feedback_buffer["preference"]:
            preference_indices = np.random.choice(
                len(self.feedback_buffer["preference"]),
                min(batch_size, len(self.feedback_buffer["preference"])),
                replace=False
            )
            for idx in preference_indices:
                preference = self.feedback_buffer["preference"][idx]
                batch["preferred_states"].append(preference["preferred_state"])
                batch["rejected_states"].append(preference["rejected_state"])

        # 采样模仿数据
        if self.feedback_buffer["imitation"]:
            imitation_indices = np.random.choice(
                len(self.feedback_buffer["imitation"]),
                min(batch_size, len(self.feedback_buffer["imitation"])),
                replace=False
            )
            for idx in imitation_indices:
                imitation = self.feedback_buffer["imitation"][idx]
                batch["imitation_states"].append(imitation["state"])
                batch["human_actions"].append(imitation["human_action"])

        return batch

    def update_priorities(self, indices: np.ndarray, priorities: np.ndarray) -> None:
        """
        更新优先级

        Args:
            indices: 索引数组
            priorities: 优先级数组
        """
        if self.use_prioritized_replay:
            self.buffer.update_priorities(indices, priorities)

    def _save_data(self) -> None:
        """
        保存数据
        """
        if self.save_path is None:
            return

        # 创建保存文件名
        timestamp = int(time.time())
        filename = f"online_data_{timestamp}.npz"
        filepath = os.path.join(self.save_path, filename)

        # 准备数据
        data = {
            "stats": self.stats,
            "feedback": self.feedback_buffer["feedback"],
            "preference": self.feedback_buffer["preference"],
            "imitation": self.feedback_buffer["imitation"]
        }

        # 保存数据
        try:
            np.savez_compressed(filepath, **data)
            logger.info(f"数据已保存到: {filepath}")
            self.stats["last_save_time"] = time.time()
        except Exception as e:
            logger.error(f"保存数据失败: {e}")

    def __len__(self) -> int:
        """
        获取缓冲区大小

        Returns:
            int: 缓冲区大小
        """
        return len(self.buffer)

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        return self.stats.copy()

    def clear(self) -> None:
        """
        清空缓冲区
        """
        if hasattr(self.buffer, "clear"):
            self.buffer.clear()
        else:
            # 如果缓冲区没有clear方法，重新创建
            if self.use_prioritized_replay:
                from cardgame_ai.algorithms.replay_buffer import PrioritizedReplayBuffer
                self.buffer = PrioritizedReplayBuffer(
                    capacity=self.buffer_size,
                    alpha=self.alpha,
                    beta=self.beta,
                    beta_increment=self.beta_increment,
                    epsilon=self.epsilon
                )
            else:
                self.buffer = ReplayBuffer(capacity=self.buffer_size)

        # 清空反馈缓冲区
        self.feedback_buffer = {
            "feedback": [],
            "preference": [],
            "imitation": []
        }

        logger.info("缓冲区已清空")
