"""
MCTS日志增强模块

提供MCTS算法的详细调试日志功能，包括：
- UCB计算过程追踪
- 节点扩展详情记录  
- 搜索路径完整追踪
- 性能统计监控
- 可配置的日志级别和输出格式

主要组件：
- MCTSLogger: 主日志器类，提供所有日志记录功能
- LogConfig: 日志配置管理器
- PerformanceMonitor: 性能监控器

使用示例：
    from cardgame_ai.algorithms.mcts_logging import MCTSLogger, LogConfig
    
    config = LogConfig(level='DEBUG', enable_debug=True)
    logger = MCTSLogger(config=config)
    logger.log_ucb_calculation(parent_node, children_scores, selected_action)
"""

import os
import time
import json
import uuid
import math
import logging
import threading
import hashlib
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
from dataclasses import dataclass
from collections import defaultdict, deque
from abc import ABC, abstractmethod


def generate_session_id() -> str:
    """生成唯一的MCTS会话ID"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    hash_suffix = hashlib.md5(str(time.time()).encode()).hexdigest()[:4]
    return f"mcts_{timestamp}_{hash_suffix}"


def format_timestamp(timestamp: Optional[float] = None) -> str:
    """格式化时间戳为ISO格式字符串"""
    if timestamp is None:
        timestamp = time.time()
    return datetime.fromtimestamp(timestamp).isoformat()


def safe_serialize(obj: Any) -> Any:
    """安全序列化对象，处理不可序列化的类型"""
    if obj is None:
        return None
    
    if isinstance(obj, (str, int, float, bool)):
        return obj
    
    if isinstance(obj, (list, tuple)):
        return [safe_serialize(item) for item in obj]
    
    if isinstance(obj, dict):
        return {str(key): safe_serialize(value) for key, value in obj.items()}
    
    try:
        import numpy as np
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        if isinstance(obj, (np.integer, np.floating)):
            return obj.item()
    except ImportError:
        pass
    
    try:
        import torch
        if isinstance(obj, torch.Tensor):
            return obj.detach().cpu().numpy().tolist()
    except ImportError:
        pass
    
    if hasattr(obj, '__dict__'):
        return safe_serialize(obj.__dict__)
    
    return str(obj)


@dataclass
class LogConfig:
    """MCTS日志配置类"""
    
    # 基础配置
    enabled: bool = True
    level: str = "INFO"  # DEBUG, INFO, WARNING, ERROR
    output_format: str = "json"  # json, text
    
    # 功能开关
    enable_ucb_logging: bool = True
    enable_expansion_logging: bool = True
    enable_path_logging: bool = True
    enable_performance_logging: bool = True
    enable_simulation_logging: bool = True
    
    # 输出配置
    log_to_file: bool = True
    log_to_console: bool = False
    log_file_path: str = "logs/mcts_debug.log"
    max_log_file_size: str = "100MB"
    log_rotation_count: int = 5
    
    # 性能配置
    async_logging: bool = True
    buffer_size: int = 1000
    flush_interval: float = 1.0  # 秒
    
    # 详细程度配置
    max_children_logged: int = 20
    max_path_depth_logged: int = 50
    include_game_state: bool = True
    include_timestamps: bool = True
    
    # 过滤配置
    min_visit_count_for_logging: int = 1
    log_only_best_actions: bool = False
    
    def validate(self) -> bool:
        """验证配置的有效性"""
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR']
        if self.level.upper() not in valid_levels:
            return False
        
        valid_formats = ['json', 'text']
        if self.output_format.lower() not in valid_formats:
            return False
        
        if self.max_children_logged < 1:
            return False
        
        if self.max_path_depth_logged < 1:
            return False
        
        if self.buffer_size < 1:
            return False
        
        if self.flush_interval <= 0:
            return False
        
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'enabled': self.enabled,
            'level': self.level,
            'output_format': self.output_format,
            'enable_ucb_logging': self.enable_ucb_logging,
            'enable_expansion_logging': self.enable_expansion_logging,
            'enable_path_logging': self.enable_path_logging,
            'enable_performance_logging': self.enable_performance_logging,
            'enable_simulation_logging': self.enable_simulation_logging,
            'log_to_file': self.log_to_file,
            'log_to_console': self.log_to_console,
            'log_file_path': self.log_file_path,
            'max_log_file_size': self.max_log_file_size,
            'log_rotation_count': self.log_rotation_count,
            'async_logging': self.async_logging,
            'buffer_size': self.buffer_size,
            'flush_interval': self.flush_interval,
            'max_children_logged': self.max_children_logged,
            'max_path_depth_logged': self.max_path_depth_logged,
            'include_game_state': self.include_game_state,
            'include_timestamps': self.include_timestamps,
            'min_visit_count_for_logging': self.min_visit_count_for_logging,
            'log_only_best_actions': self.log_only_best_actions,
        }


class PerformanceTimer:
    """性能计时器"""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
    
    def start(self) -> None:
        """开始计时"""
        self.start_time = time.perf_counter()
    
    def stop(self) -> float:
        """停止计时并返回耗时"""
        if self.start_time is None:
            raise ValueError("计时器未启动")
        
        self.end_time = time.perf_counter()
        return self.end_time - self.start_time
    
    def elapsed(self) -> float:
        """获取当前耗时（不停止计时器）"""
        if self.start_time is None:
            raise ValueError("计时器未启动")
        
        return time.perf_counter() - self.start_time


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    
    # 搜索统计
    total_search_time: float = 0.0
    total_simulations: int = 0
    average_simulation_time: float = 0.0
    
    # UCB计算统计
    total_ucb_calculations: int = 0
    total_ucb_time: float = 0.0
    average_ucb_time: float = 0.0
    
    # 节点扩展统计
    total_expansions: int = 0
    total_expansion_time: float = 0.0
    average_expansion_time: float = 0.0
    
    # 内存统计
    peak_memory_usage: int = 0
    current_memory_usage: int = 0
    
    # 搜索树统计
    max_tree_depth: int = 0
    total_nodes_created: int = 0
    total_nodes_visited: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'total_search_time': self.total_search_time,
            'total_simulations': self.total_simulations,
            'average_simulation_time': self.average_simulation_time,
            'total_ucb_calculations': self.total_ucb_calculations,
            'total_ucb_time': self.total_ucb_time,
            'average_ucb_time': self.average_ucb_time,
            'total_expansions': self.total_expansions,
            'total_expansion_time': self.total_expansion_time,
            'average_expansion_time': self.average_expansion_time,
            'peak_memory_usage': self.peak_memory_usage,
            'current_memory_usage': self.current_memory_usage,
            'max_tree_depth': self.max_tree_depth,
            'total_nodes_created': self.total_nodes_created,
            'total_nodes_visited': self.total_nodes_visited,
        }


class PerformanceMonitor:
    """MCTS性能监控器"""
    
    def __init__(self, enable_memory_monitoring: bool = True):
        self.enable_memory_monitoring = enable_memory_monitoring
        self.metrics = PerformanceMetrics()
        self.search_start_times = {}
        self.ucb_start_times = {}
        self.expansion_start_times = {}
        self.lock = threading.Lock()
    
    def start_search_timing(self, search_id: str) -> None:
        """开始搜索计时"""
        with self.lock:
            self.search_start_times[search_id] = time.perf_counter()
    
    def end_search_timing(self, search_id: str, num_simulations: int) -> float:
        """结束搜索计时"""
        end_time = time.perf_counter()
        
        with self.lock:
            start_time = self.search_start_times.pop(search_id, end_time)
            search_time = end_time - start_time
            
            self.metrics.total_search_time += search_time
            self.metrics.total_simulations += num_simulations
            
            if self.metrics.total_simulations > 0:
                self.metrics.average_simulation_time = (
                    self.metrics.total_search_time / self.metrics.total_simulations
                )
        
        return search_time
    
    def start_ucb_timing(self, ucb_id: str) -> None:
        """开始UCB计算计时"""
        with self.lock:
            self.ucb_start_times[ucb_id] = time.perf_counter()
    
    def end_ucb_timing(self, ucb_id: str) -> float:
        """结束UCB计算计时"""
        end_time = time.perf_counter()
        
        with self.lock:
            start_time = self.ucb_start_times.pop(ucb_id, end_time)
            ucb_time = end_time - start_time
            
            self.metrics.total_ucb_calculations += 1
            self.metrics.total_ucb_time += ucb_time
            self.metrics.average_ucb_time = (
                self.metrics.total_ucb_time / self.metrics.total_ucb_calculations
            )
        
        return ucb_time
    
    def start_expansion_timing(self, expansion_id: str) -> None:
        """开始节点扩展计时"""
        with self.lock:
            self.expansion_start_times[expansion_id] = time.perf_counter()
    
    def end_expansion_timing(self, expansion_id: str, num_children: int) -> float:
        """结束节点扩展计时"""
        end_time = time.perf_counter()
        
        with self.lock:
            start_time = self.expansion_start_times.pop(expansion_id, end_time)
            expansion_time = end_time - start_time
            
            self.metrics.total_expansions += 1
            self.metrics.total_expansion_time += expansion_time
            self.metrics.average_expansion_time = (
                self.metrics.total_expansion_time / self.metrics.total_expansions
            )
            
            self.metrics.total_nodes_created += num_children
        
        return expansion_time
    
    def record_tree_depth(self, depth: int) -> None:
        """记录搜索树深度"""
        with self.lock:
            self.metrics.max_tree_depth = max(self.metrics.max_tree_depth, depth)
    
    def record_node_visit(self) -> None:
        """记录节点访问"""
        self.metrics.total_nodes_visited += 1
    
    def get_current_metrics(self) -> PerformanceMetrics:
        """获取当前性能指标"""
        with self.lock:
            return self.metrics
    
    def generate_performance_report(self) -> Dict[str, Any]:
        """生成详细的性能报告"""
        metrics = self.get_current_metrics()
        
        return {
            'summary': {
                'total_search_time': f"{metrics.total_search_time:.3f}s",
                'total_simulations': metrics.total_simulations,
                'average_simulation_time': f"{metrics.average_simulation_time*1000:.2f}ms",
                'max_tree_depth': metrics.max_tree_depth
            },
            'detailed_metrics': metrics.to_dict(),
            'efficiency_ratios': {
                'simulations_per_second': (
                    metrics.total_simulations / metrics.total_search_time 
                    if metrics.total_search_time > 0 else 0
                ),
                'ucb_calculations_per_simulation': (
                    metrics.total_ucb_calculations / metrics.total_simulations
                    if metrics.total_simulations > 0 else 0
                ),
                'expansions_per_simulation': (
                    metrics.total_expansions / metrics.total_simulations
                    if metrics.total_simulations > 0 else 0
                )
            }
        }


class MCTSFormatter(ABC):
    """MCTS日志格式化器基类"""

    @abstractmethod
    def format_log(self, timestamp: float, session_id: str, log_type: str, data: Dict[str, Any]) -> str:
        """格式化日志"""
        pass


class JSONFormatter(MCTSFormatter):
    """JSON格式化器"""

    def __init__(self, indent: Optional[int] = None):
        self.indent = indent

    def format_log(self, timestamp: float, session_id: str, log_type: str, data: Dict[str, Any]) -> str:
        """格式化为JSON字符串"""
        log_data = {
            'timestamp': format_timestamp(timestamp),
            'session_id': session_id,
            'level': 'DEBUG',
            'type': log_type,
            'data': safe_serialize(data)
        }

        return json.dumps(log_data, indent=self.indent, ensure_ascii=False)


class TextFormatter(MCTSFormatter):
    """文本格式化器"""

    def __init__(self, include_timestamp: bool = True, include_session_id: bool = True):
        self.include_timestamp = include_timestamp
        self.include_session_id = include_session_id

    def format_log(self, timestamp: float, session_id: str, log_type: str, data: Dict[str, Any]) -> str:
        """格式化为文本字符串"""
        parts = []

        if self.include_timestamp:
            parts.append(f"[{format_timestamp(timestamp)}]")

        if self.include_session_id:
            parts.append(f"[{session_id}]")

        parts.append(f"[{log_type.upper()}]")

        header = " ".join(parts)
        body = self._format_data(data)

        return f"{header}\n{body}\n"

    def _format_data(self, data: Dict[str, Any], indent: int = 0) -> str:
        """格式化数据为文本"""
        lines = []
        indent_str = "  " * indent

        for key, value in data.items():
            if isinstance(value, dict):
                lines.append(f"{indent_str}{key}:")
                lines.append(self._format_data(value, indent + 1))
            elif isinstance(value, list):
                lines.append(f"{indent_str}{key}: [{len(value)} items]")
                for i, item in enumerate(value[:5]):
                    if isinstance(item, dict):
                        lines.append(f"{indent_str}  [{i}]:")
                        lines.append(self._format_data(item, indent + 2))
                    else:
                        lines.append(f"{indent_str}  [{i}]: {item}")
                if len(value) > 5:
                    lines.append(f"{indent_str}  ... 还有 {len(value) - 5} 个项目")
            else:
                str_value = str(value)
                if len(str_value) > 120:
                    str_value = str_value[:120] + "..."
                lines.append(f"{indent_str}{key}: {str_value}")

        return "\n".join(lines)


class MCTSLogger:
    """MCTS专用日志器"""

    def __init__(self, config: Optional[LogConfig] = None, session_id: Optional[str] = None):
        """初始化MCTS日志器"""
        self.config = config or LogConfig()
        self.session_id = session_id or generate_session_id()

        if not self.config.validate():
            raise ValueError("日志配置验证失败")

        # 创建格式化器
        if self.config.output_format == 'json':
            self.formatter = JSONFormatter(indent=2)
        else:
            self.formatter = TextFormatter()

        # 创建性能监控器
        self.performance_monitor = PerformanceMonitor(
            enable_memory_monitoring=self.config.enable_performance_logging
        )

        # 设置日志器
        self.logger = self._setup_logger()

        # 异步日志缓冲
        self.log_buffer = []
        self.buffer_lock = threading.Lock()
        self.flush_timer = None

        # 性能计时器
        self.timers = {}

        # 统计计数器
        self.stats = {
            'ucb_logs': 0,
            'expansion_logs': 0,
            'path_logs': 0,
            'simulation_logs': 0,
            'performance_logs': 0
        }

        # 启动异步日志处理
        if self.config.async_logging:
            self._start_async_logging()

    def _setup_logger(self) -> logging.Logger:
        """设置Python日志器"""
        logger = logging.getLogger(f"mcts_logger_{self.session_id}")
        logger.setLevel(getattr(logging, self.config.level.upper()))

        # 清除现有处理器
        logger.handlers.clear()

        # 添加文件处理器
        if self.config.log_to_file:
            log_path = Path(self.config.log_file_path)
            log_path.parent.mkdir(parents=True, exist_ok=True)

            file_handler = logging.FileHandler(log_path, encoding='utf-8')
            file_handler.setLevel(getattr(logging, self.config.level.upper()))
            logger.addHandler(file_handler)

        # 添加控制台处理器
        if self.config.log_to_console:
            console_handler = logging.StreamHandler()
            console_handler.setLevel(getattr(logging, self.config.level.upper()))
            logger.addHandler(console_handler)

        return logger

    def _start_async_logging(self) -> None:
        """启动异步日志处理"""
        if self.flush_timer:
            self.flush_timer.cancel()

        self.flush_timer = threading.Timer(
            self.config.flush_interval,
            self._flush_log_buffer
        )
        self.flush_timer.daemon = True
        self.flush_timer.start()

    def _flush_log_buffer(self) -> None:
        """刷新日志缓冲区"""
        with self.buffer_lock:
            if self.log_buffer:
                for log_entry in self.log_buffer:
                    self.logger.debug(log_entry)
                self.log_buffer.clear()

        # 重新启动定时器
        if self.config.async_logging:
            self._start_async_logging()

    def _log_message(self, message: str) -> None:
        """记录日志消息"""
        if not self.config.enabled:
            return

        if self.config.async_logging:
            with self.buffer_lock:
                self.log_buffer.append(message)

                # 如果缓冲区满了，立即刷新
                if len(self.log_buffer) >= self.config.buffer_size:
                    self._flush_log_buffer()
        else:
            self.logger.debug(message)

    def log_ucb_calculation(self,
                           parent_node: Any,
                           children_scores: List[Dict[str, Any]],
                           selected_action: int,
                           game_context: Optional[Dict[str, Any]] = None,
                           timing_id: Optional[str] = None) -> None:
        """记录UCB计算过程"""
        if not self.config.enable_ucb_logging:
            return

        # 结束计时
        ucb_time = 0.0
        if timing_id and timing_id in self.timers:
            ucb_time = self.timers[timing_id].stop()
            self.performance_monitor.end_ucb_timing(timing_id)
            del self.timers[timing_id]

        # 过滤和限制子节点数据
        filtered_children = children_scores[:self.config.max_children_logged]

        # 如果只记录最佳动作
        if self.config.log_only_best_actions:
            filtered_children = [
                child for child in filtered_children
                if child.get('action') == selected_action
            ]

        log_data = {
            'parent_visits': getattr(parent_node, 'visit_count', 0),
            'children_count': len(children_scores),
            'children_scores': safe_serialize(filtered_children),
            'selected_action': selected_action,
            'ucb_calculation_time': ucb_time,
            'game_context': safe_serialize(game_context) if game_context else {}
        }

        # 添加游戏状态信息
        if self.config.include_game_state and hasattr(parent_node, 'state'):
            log_data['game_state'] = safe_serialize(parent_node.state)

        message = self.formatter.format_log(
            time.time(),
            self.session_id,
            'ucb_calculation',
            log_data
        )

        self._log_message(message)
        self.stats['ucb_logs'] += 1

    def log_node_expansion(self,
                          node: Any,
                          policy_output: Dict[str, Any],
                          expansion_time: float,
                          num_children: int,
                          timing_id: Optional[str] = None) -> None:
        """记录节点扩展过程"""
        if not self.config.enable_expansion_logging:
            return

        # 结束计时
        if timing_id:
            self.performance_monitor.end_expansion_timing(timing_id, num_children)

        log_data = {
            'node_visits_before': getattr(node, 'visit_count', 0),
            'policy_output': safe_serialize(policy_output),
            'num_children_added': num_children,
            'expansion_time': expansion_time
        }

        # 添加节点状态信息
        if self.config.include_game_state and hasattr(node, 'state'):
            log_data['node_state'] = safe_serialize(node.state)

        message = self.formatter.format_log(
            time.time(),
            self.session_id,
            'node_expansion',
            log_data
        )

        self._log_message(message)
        self.stats['expansion_logs'] += 1

    def log_search_path(self,
                       path: List[Any],
                       path_value: float,
                       depth: int) -> None:
        """记录搜索路径"""
        if not self.config.enable_path_logging:
            return

        # 限制路径深度
        limited_path = path[:self.config.max_path_depth_logged]

        # 记录树深度
        self.performance_monitor.record_tree_depth(depth)

        log_data = {
            'path_length': len(path),
            'path_value': path_value,
            'depth': depth,
            'path_nodes': []
        }

        # 记录路径中的关键节点信息
        for i, node in enumerate(limited_path):
            node_info = {
                'step': i,
                'visits': getattr(node, 'visit_count', 0),
                'value': getattr(node, 'value', 0.0) if not callable(getattr(node, 'value', None)) else (getattr(node, 'value')() if getattr(node, 'value', None) else 0.0)
            }

            # 添加状态信息（如果配置允许）
            if self.config.include_game_state and hasattr(node, 'state'):
                node_info['state'] = safe_serialize(node.state)

            log_data['path_nodes'].append(node_info)

        message = self.formatter.format_log(
            time.time(),
            self.session_id,
            'search_path',
            log_data
        )

        self._log_message(message)
        self.stats['path_logs'] += 1

    def log_simulation_result(self,
                             start_state: Any,
                             result_value: float,
                             simulation_time: float,
                             simulation_steps: int) -> None:
        """记录模拟结果"""
        if not self.config.enable_simulation_logging:
            return

        log_data = {
            'result_value': result_value,
            'simulation_time': simulation_time,
            'simulation_steps': simulation_steps,
            'start_state': safe_serialize(start_state) if self.config.include_game_state else None
        }

        message = self.formatter.format_log(
            time.time(),
            self.session_id,
            'simulation_result',
            log_data
        )

        self._log_message(message)
        self.stats['simulation_logs'] += 1

    def log_performance_stats(self, additional_stats: Optional[Dict[str, Any]] = None) -> None:
        """记录性能统计"""
        if not self.config.enable_performance_logging:
            return

        # 获取性能报告
        performance_report = self.performance_monitor.generate_performance_report()

        log_data = {
            'performance_report': performance_report,
            'logging_stats': self.stats.copy(),
            'session_id': self.session_id
        }

        if additional_stats:
            log_data['additional_stats'] = safe_serialize(additional_stats)

        message = self.formatter.format_log(
            time.time(),
            self.session_id,
            'performance_stats',
            log_data
        )

        self._log_message(message)
        self.stats['performance_logs'] += 1

    def start_search_timing(self, search_id: Optional[str] = None) -> str:
        """开始搜索计时"""
        if search_id is None:
            search_id = str(uuid.uuid4())

        self.performance_monitor.start_search_timing(search_id)
        return search_id

    def end_search_timing(self, search_id: str, num_simulations: int) -> float:
        """结束搜索计时"""
        return self.performance_monitor.end_search_timing(search_id, num_simulations)

    def start_ucb_timing(self, ucb_id: Optional[str] = None) -> str:
        """开始UCB计算计时"""
        if ucb_id is None:
            ucb_id = str(uuid.uuid4())

        self.timers[ucb_id] = PerformanceTimer()
        self.timers[ucb_id].start()
        self.performance_monitor.start_ucb_timing(ucb_id)
        return ucb_id

    def start_expansion_timing(self, expansion_id: Optional[str] = None) -> str:
        """开始节点扩展计时"""
        if expansion_id is None:
            expansion_id = str(uuid.uuid4())

        self.performance_monitor.start_expansion_timing(expansion_id)
        return expansion_id

    def get_session_stats(self) -> Dict[str, Any]:
        """获取会话统计信息"""
        return {
            'session_id': self.session_id,
            'config': self.config.to_dict(),
            'logging_stats': self.stats.copy(),
            'performance_metrics': self.performance_monitor.get_current_metrics().to_dict()
        }

    def close(self) -> None:
        """关闭日志器，清理资源"""
        # 刷新缓冲区
        self._flush_log_buffer()

        # 停止定时器
        if self.flush_timer:
            self.flush_timer.cancel()

        # 记录最终性能统计
        self.log_performance_stats({'session_closed': True})

        # 最终刷新
        self._flush_log_buffer()

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()


# 默认配置实例
DEFAULT_CONFIG = LogConfig()
