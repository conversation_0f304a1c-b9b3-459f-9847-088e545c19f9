"""
策略融合模块

实现策略融合技术，将多个专家策略融合成一个统一的策略，提高整体性能。
"""
import os
import logging
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Any, Optional, Union, Callable
from collections import defaultdict

from cardgame_ai.core.base import State, Action
from cardgame_ai.utils.logger import get_logger

logger = logging.getLogger(__name__)


class PolicyFusion:
    """
    策略融合
    
    将多个专家策略融合成单一策略，使用上下文感知的加权机制选择最佳决策。
    """
    
    def __init__(
        self,
        policies: List[nn.Module],
        fusion_network: Optional[nn.Module] = None,
        device: str = 'cuda' if torch.cuda.is_available() else 'cpu'
    ):
        """
        初始化策略融合
        
        Args:
            policies: 策略模型列表
            fusion_network: 融合网络，用于根据上下文确定权重
            device: 计算设备
        """
        self.policies = policies
        self.fusion_network = fusion_network
        self.device = device
        self.n_policies = len(policies)
        
        # 将模型移到指定设备
        for i, policy in enumerate(self.policies):
            self.policies[i] = policy.to(device)
        
        if self.fusion_network is not None:
            self.fusion_network = self.fusion_network.to(device)
            # 优化器
            self.optimizer = torch.optim.Adam(self.fusion_network.parameters(), lr=0.001)
        else:
            # 如果没有提供融合网络，使用均匀权重
            self.weights = torch.ones(self.n_policies, device=device) / self.n_policies
        
        # 统计信息
        self.stats = {
            "fusion_loss": [],
            "weights_history": [],
            "updates": 0
        }
        
        logger.info(f"策略融合初始化完成，策略数量: {self.n_policies}")
    
    def compute_weights(self, state: torch.Tensor) -> torch.Tensor:
        """
        计算融合权重
        
        Args:
            state: 状态张量
        
        Returns:
            每个策略的权重
        """
        if self.fusion_network is not None:
            # 使用融合网络计算权重
            logits = self.fusion_network(state)
            weights = F.softmax(logits, dim=-1)
            return weights
        else:
            # 使用预定义的均匀权重
            return self.weights.expand(state.shape[0], -1)
    
    def fuse(self, state: torch.Tensor) -> torch.Tensor:
        """
        融合多个策略的输出
        
        Args:
            state: 状态张量
        
        Returns:
            融合后的输出
        """
        # 确保输入在正确的设备上
        state = state.to(self.device)
        
        # 获取每个策略的输出
        all_outputs = []
        with torch.no_grad():
            for policy in self.policies:
                policy.eval()
                output = policy(state)
                all_outputs.append(output)
        
        # 将所有输出堆叠在一起
        # 假设每个输出形状为 [batch_size, output_dim]
        stacked_outputs = torch.stack(all_outputs, dim=1)  # [batch_size, n_policies, output_dim]
        
        # 计算权重
        weights = self.compute_weights(state)  # [batch_size, n_policies]
        
        # 扩展权重维度以便广播
        weights = weights.unsqueeze(-1)  # [batch_size, n_policies, 1]
        
        # 加权求和
        fused_output = (stacked_outputs * weights).sum(dim=1)  # [batch_size, output_dim]
        
        # 记录权重
        if len(self.stats["weights_history"]) < 1000:  # 限制历史记录长度
            self.stats["weights_history"].append(weights.mean(dim=0).squeeze().cpu().numpy())
        
        return fused_output
    
    def update(
        self,
        states: torch.Tensor,
        actions: torch.Tensor,
        rewards: torch.Tensor
    ) -> Dict[str, float]:
        """
        更新融合网络
        
        Args:
            states: 状态张量
            actions: 动作张量
            rewards: 奖励张量
        
        Returns:
            更新指标
        """
        if self.fusion_network is None:
            return {"fusion_loss": 0.0}
        
        # 确保输入在正确的设备上
        states = states.to(self.device)
        actions = actions.to(self.device)
        rewards = rewards.to(self.device)
        
        # 计算每个策略的价值
        all_values = []
        with torch.no_grad():
            for policy in self.policies:
                policy.eval()
                logits = policy(states)
                # 计算每个动作的概率
                probs = F.softmax(logits, dim=-1)
                # 提取所选动作的概率
                selected_probs = torch.gather(probs, 1, actions.unsqueeze(1)).squeeze()
                # 估计价值 (使用奖励作为价值的代理)
                values = selected_probs * rewards
                all_values.append(values)
        
        # 将所有价值堆叠在一起
        stacked_values = torch.stack(all_values, dim=1)  # [batch_size, n_policies]
        
        # 计算融合权重
        weights = self.compute_weights(states)  # [batch_size, n_policies]
        
        # 计算加权价值
        weighted_values = (weights * stacked_values).sum(dim=1)  # [batch_size]
        
        # 计算损失 (最大化加权价值)
        fusion_loss = -weighted_values.mean()
        
        # 更新融合网络
        self.optimizer.zero_grad()
        fusion_loss.backward()
        self.optimizer.step()
        
        # 更新统计信息
        self.stats["fusion_loss"].append(fusion_loss.item())
        self.stats["updates"] += 1
        
        return {"fusion_loss": fusion_loss.item()}
    
    def save(self, path: str) -> None:
        """
        保存模型
        
        Args:
            path: 保存路径
        """
        state_dict = {
            "weights": self.weights.cpu() if self.fusion_network is None else None,
            "stats": self.stats
        }
        
        if self.fusion_network is not None:
            state_dict["fusion_network"] = self.fusion_network.state_dict()
            state_dict["optimizer"] = self.optimizer.state_dict()
        
        torch.save(state_dict, path)
        logger.info(f"策略融合模型已保存到 {path}")
    
    def load(self, path: str) -> None:
        """
        加载模型
        
        Args:
            path: 加载路径
        """
        if not os.path.exists(path):
            logger.warning(f"模型文件不存在: {path}")
            return
            
        try:
            state_dict = torch.load(path, map_location=self.device)
            
            if state_dict["weights"] is not None:
                self.weights = state_dict["weights"].to(self.device)
            
            if "fusion_network" in state_dict and self.fusion_network is not None:
                self.fusion_network.load_state_dict(state_dict["fusion_network"])
                self.optimizer.load_state_dict(state_dict["optimizer"])
            
            self.stats = state_dict.get("stats", self.stats)
            
            logger.info(f"策略融合模型已从 {path} 加载")
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            统计信息字典
        """
        avg_fusion_loss = np.mean(self.stats["fusion_loss"][-100:]) if self.stats["fusion_loss"] else 0
        
        # 计算最近权重的平均值
        if self.stats["weights_history"]:
            recent_weights = np.array(self.stats["weights_history"][-100:])
            avg_weights = recent_weights.mean(axis=0)
        else:
            avg_weights = (self.weights.cpu().numpy() if self.fusion_network is None 
                          else np.ones(self.n_policies) / self.n_policies)
        
        return {
            "avg_fusion_loss": avg_fusion_loss,
            "avg_weights": avg_weights.tolist(),
            "updates": self.stats["updates"]
        }