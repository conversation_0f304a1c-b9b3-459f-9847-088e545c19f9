#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
斗地主AI优化训练脚本

基于架构文档和PRD需求，实现优化的EfficientZero训练流程。
包含以下优化特性：
- 动态MCTS预算分配 (100-200次模拟)
- 增强的多智能体协作机制
- 改进的奖励机制和价值网络
- 分布式训练支持
- 实时性能监控

使用方法:
    python scripts/optimized_training.py --config configs/training/efficient_zero.yaml
"""

import os
import sys
import argparse
import logging
import time
import torch
import hydra
from omegaconf import DictConfig, OmegaConf
from pathlib import Path
from typing import Dict, Any, Optional

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from cardgame_ai.algorithms.efficient_zero import EfficientZero
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.training.distributed import DistributedTrainer
from cardgame_ai.evaluation.evaluators import PerformanceEvaluator
from cardgame_ai.utils.logging import setup_logging
from cardgame_ai.utils.monitoring import TrainingMonitor

logger = logging.getLogger(__name__)


class OptimizedTrainingPipeline:
    """
    优化的训练流水线
    
    集成了所有优化特性的训练管理器
    """
    
    def __init__(self, config: DictConfig):
        """
        初始化训练流水线
        
        Args:
            config: Hydra配置对象
        """
        self.config = config
        self.device = self._setup_device()
        self.monitor = TrainingMonitor(config.monitoring)
        
        # 初始化组件
        self._setup_environment()
        self._setup_algorithm()
        self._setup_trainer()
        self._setup_evaluator()
        
        logger.info("优化训练流水线初始化完成")
    
    def _setup_device(self) -> torch.device:
        """设置计算设备"""
        if self.config.device.type == "auto":
            device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        else:
            device = torch.device(self.config.device.type)
        
        if device.type == "cuda":
            torch.cuda.set_device(self.config.device.ids[0])
            logger.info(f"使用GPU设备: {device}, 可用GPU: {self.config.device.ids}")
        else:
            logger.info(f"使用CPU设备: {device}")
        
        return device
    
    def _setup_environment(self):
        """设置游戏环境"""
        self.env = DouDizhuEnvironment()
        logger.info("斗地主游戏环境初始化完成")
    
    def _setup_algorithm(self):
        """设置优化的EfficientZero算法"""
        algo_config = self.config.training.algorithm.efficient_zero
        
        # 优化的算法参数
        self.algorithm = EfficientZero(
            state_shape=self.env.observation_space.shape,
            action_shape=self.env.action_space.shape,
            
            # 核心优化参数
            num_simulations=algo_config.num_simulations,  # 100-200
            batch_size=self.config.training.batch_size,   # 256
            learning_rate=self.config.training.learning_rate,  # 0.0005
            
            # 动态预算分配
            use_dynamic_budget=algo_config.dynamic_budget,
            dynamic_budget_config=algo_config.budget_allocation,
            
            # 多智能体协作
            use_belief_state=True,
            
            # 分布式价值网络
            use_distributional_value=algo_config.value_network.type == "distributional",
            value_support_size=algo_config.value_network.support_size,
            value_min=algo_config.value_network.value_min,
            value_max=algo_config.value_network.value_max,
            
            # 模型架构优化
            hidden_dim=algo_config.model.representation.channels,
            use_resnet=algo_config.model.representation.type == "resnet",
            
            device=str(self.device)
        )
        
        logger.info("优化的EfficientZero算法初始化完成")
        logger.info(f"MCTS模拟次数: {algo_config.num_simulations}")
        logger.info(f"批次大小: {self.config.training.batch_size}")
        logger.info(f"学习率: {self.config.training.learning_rate}")
    
    def _setup_trainer(self):
        """设置分布式训练器"""
        if self.config.distributed.data_parallel:
            self.trainer = DistributedTrainer(
                algorithm=self.algorithm,
                config=self.config.distributed,
                device=self.device
            )
            logger.info("分布式训练器初始化完成")
        else:
            self.trainer = None
            logger.info("使用单机训练模式")
    
    def _setup_evaluator(self):
        """设置性能评估器"""
        self.evaluator = PerformanceEvaluator(
            algorithm=self.algorithm,
            environment=self.env,
            config=self.config.evaluation
        )
        logger.info("性能评估器初始化完成")
    
    def train(self):
        """执行优化训练"""
        logger.info("开始优化训练流程")
        
        # 训练配置
        num_epochs = self.config.training.epochs
        eval_frequency = self.config.evaluation.eval_frequency
        save_frequency = self.config.checkpoint.save_frequency
        
        best_performance = 0.0
        
        for epoch in range(num_epochs):
            epoch_start_time = time.time()
            
            # 训练一个epoch
            if self.trainer:
                # 分布式训练
                train_metrics = self.trainer.train_epoch(epoch)
            else:
                # 单机训练
                train_metrics = self._train_epoch(epoch)
            
            # 记录训练指标
            self.monitor.log_metrics(train_metrics, step=epoch, prefix="train")
            
            # 定期评估
            if epoch % eval_frequency == 0:
                eval_metrics = self.evaluator.evaluate()
                self.monitor.log_metrics(eval_metrics, step=epoch, prefix="eval")
                
                # 检查是否是最佳性能
                current_performance = eval_metrics.get('win_rate', 0.0)
                if current_performance > best_performance:
                    best_performance = current_performance
                    self._save_best_model(epoch, current_performance)
            
            # 定期保存检查点
            if epoch % save_frequency == 0:
                self._save_checkpoint(epoch)
            
            # 记录epoch时间
            epoch_time = time.time() - epoch_start_time
            logger.info(f"Epoch {epoch}/{num_epochs} 完成，耗时: {epoch_time:.2f}s")
            
            # 早停检查
            if self._should_early_stop(eval_metrics if epoch % eval_frequency == 0 else None):
                logger.info(f"早停触发，在epoch {epoch}停止训练")
                break
        
        logger.info("训练完成")
        return best_performance
    
    def _train_epoch(self, epoch: int) -> Dict[str, float]:
        """训练一个epoch（单机模式）"""
        epoch_metrics = {
            'total_loss': 0.0,
            'value_loss': 0.0,
            'policy_loss': 0.0,
            'games_played': 0
        }
        
        # 自对弈收集数据
        num_games = self.config.self_play.num_games
        for game_idx in range(num_games):
            # 执行一局游戏
            game_data = self._play_game()
            
            # 添加到回放缓冲区
            self.algorithm.replay_buffer.add_game(game_data)
            
            epoch_metrics['games_played'] += 1
        
        # 训练更新
        num_updates = self.config.training.updates_per_epoch
        for update_idx in range(num_updates):
            # 采样批次数据
            batch = self.algorithm.replay_buffer.sample(self.config.training.batch_size)
            
            # 训练更新
            loss_dict = self.algorithm.train(batch)
            
            # 累积损失
            for key, value in loss_dict.items():
                if key in epoch_metrics:
                    epoch_metrics[key] += value
        
        # 平均化损失
        for key in ['total_loss', 'value_loss', 'policy_loss']:
            if key in epoch_metrics:
                epoch_metrics[key] /= num_updates
        
        return epoch_metrics
    
    def _play_game(self) -> Dict[str, Any]:
        """执行一局自对弈游戏"""
        state = self.env.reset()
        game_data = {
            'states': [],
            'actions': [],
            'rewards': [],
            'policies': [],
            'values': []
        }
        
        done = False
        while not done:
            # AI决策
            action, policy, value = self.algorithm.act(state, training=True)
            
            # 执行动作
            next_state, reward, done, _ = self.env.step(action)
            
            # 记录数据
            game_data['states'].append(state)
            game_data['actions'].append(action)
            game_data['rewards'].append(reward)
            game_data['policies'].append(policy)
            game_data['values'].append(value)
            
            state = next_state
        
        return game_data
    
    def _save_checkpoint(self, epoch: int):
        """保存训练检查点"""
        checkpoint_dir = Path(self.config.checkpoint.checkpoint_dir)
        checkpoint_dir.mkdir(parents=True, exist_ok=True)
        
        checkpoint_path = checkpoint_dir / f"checkpoint_epoch_{epoch}.pt"
        
        torch.save({
            'epoch': epoch,
            'model_state_dict': self.algorithm.model.state_dict(),
            'optimizer_state_dict': self.algorithm.optimizer.state_dict(),
            'config': OmegaConf.to_yaml(self.config)
        }, checkpoint_path)
        
        logger.info(f"检查点已保存: {checkpoint_path}")
    
    def _save_best_model(self, epoch: int, performance: float):
        """保存最佳模型"""
        model_dir = Path(self.config.model.save_dir)
        model_dir.mkdir(parents=True, exist_ok=True)
        
        best_model_path = model_dir / "best_model.pt"
        
        torch.save({
            'epoch': epoch,
            'performance': performance,
            'model_state_dict': self.algorithm.model.state_dict(),
            'config': OmegaConf.to_yaml(self.config)
        }, best_model_path)
        
        logger.info(f"最佳模型已保存: {best_model_path}, 性能: {performance:.4f}")
    
    def _should_early_stop(self, eval_metrics: Optional[Dict[str, float]]) -> bool:
        """检查是否应该早停"""
        if not self.config.early_stopping.enabled or eval_metrics is None:
            return False
        
        # 简化的早停逻辑
        monitor_metric = self.config.early_stopping.monitor
        if monitor_metric in eval_metrics:
            current_value = eval_metrics[monitor_metric]
            # 这里可以实现更复杂的早停逻辑
            return False
        
        return False


@hydra.main(version_base=None, config_path="../configs", config_name="base")
def main(cfg: DictConfig) -> None:
    """
    主训练函数
    
    Args:
        cfg: Hydra配置对象
    """
    # 设置日志
    setup_logging(cfg.logging.level, cfg.logging.dir)
    
    logger.info("=" * 50)
    logger.info("斗地主AI优化训练开始")
    logger.info("=" * 50)
    
    # 创建训练流水线
    pipeline = OptimizedTrainingPipeline(cfg)
    
    # 执行训练
    try:
        best_performance = pipeline.train()
        logger.info(f"训练完成，最佳性能: {best_performance:.4f}")
    except Exception as e:
        logger.error(f"训练过程中出现错误: {e}")
        raise
    
    logger.info("=" * 50)
    logger.info("斗地主AI优化训练结束")
    logger.info("=" * 50)


if __name__ == "__main__":
    main()
