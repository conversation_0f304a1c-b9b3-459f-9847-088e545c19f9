#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
随机代理模块

实现一个简单的随机代理，用于测试和基准比较。
"""

import random
from typing import List, Any, Dict, Optional, Union

from cardgame_ai.core.agent import Agent


class RandomAgent(Agent):
    """
    随机代理类

    随机选择合法动作的代理，用于测试和基准比较。
    """

    def __init__(self, action_space: Any = None, seed: Optional[int] = None):
        """
        初始化随机代理

        Args:
            action_space: 动作空间
            seed: 随机种子
        """
        self.action_space = action_space
        self.rng = random.Random(seed)

    def act(self, observation: Any, legal_actions: List[Any], is_training: bool = False,
            temperature: float = 1.0) -> Any:
        """
        选择动作

        Args:
            observation: 观察
            legal_actions: 合法动作列表
            is_training: 是否处于训练模式
            temperature: 温度参数，控制随机性

        Returns:
            选择的动作
        """
        if not legal_actions:
            return None

        # 随机选择一个合法动作
        return self.rng.choice(legal_actions)

    def learn(self, experiences: List[Dict[str, Any]]) -> Dict[str, float]:
        """
        学习

        Args:
            experiences: 经验列表

        Returns:
            学习指标
        """
        # 随机代理不需要学习
        return {"loss": 0.0}

    def train(self, is_training: bool = True) -> None:
        """
        设置训练模式

        Args:
            is_training: 是否处于训练模式
        """
        # 随机代理不需要训练模式
        pass

    def save(self, path: str) -> None:
        """
        保存模型

        Args:
            path: 保存路径
        """
        # 随机代理不需要保存模型
        pass

    def load(self, path: str) -> None:
        """
        加载模型

        Args:
            path: 加载路径
        """
        # 随机代理不需要加载模型
        pass
