"""
农民协作奖励机制模块

实现农民协作奖励机制，促进两个农民模型之间的有效协作，
包括共享奖励、协作行为的额外奖励等。
"""
import numpy as np
from typing import Dict, List, Tuple, Any, Optional, Union

from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType
from cardgame_ai.games.doudizhu.card import Card


class FarmerCooperation:
    """
    农民协作机制

    促进两个农民模型之间的有效协作，包括共享奖励、协作行为的额外奖励等。
    """

    def __init__(self, cooperation_weight: float = 0.5, team_reward_weight: float = 0.7,
                 control_transfer_weight: float = 0.15, sacrifice_weight: float = 0.2):
        """
        初始化农民协作机制

        Args:
            cooperation_weight: 协作奖励权重
            team_reward_weight: 团队奖励权重
            control_transfer_weight: 控制转移奖励权重
            sacrifice_weight: 牺牲行为奖励权重
        """
        self.cooperation_weight = cooperation_weight
        self.team_reward_weight = team_reward_weight
        self.control_transfer_weight = control_transfer_weight
        self.sacrifice_weight = sacrifice_weight

        # 存储历史动作
        self.action_history = {}  # 玩家ID -> 动作历史

        # 协作统计
        self.cooperation_stats = {
            "effective_follow_ups": 0,
            "strategic_passes": 0,
            "information_sharing": 0,
            "control_transfers": 0,
            "sacrifices": 0,
            "total_actions": 0
        }

    def calculate_cooperation_reward(
        self,
        agent_id: str,
        action: Any,
        game_state: Any,
        other_farmer_id: str
    ) -> float:
        """
        计算农民协作奖励

        Args:
            agent_id: 当前农民ID
            action: 当前农民的动作
            game_state: 游戏状态
            other_farmer_id: 另一个农民ID

        Returns:
            协作奖励值
        """
        # 更新动作历史
        if agent_id not in self.action_history:
            self.action_history[agent_id] = []
        self.action_history[agent_id].append(action)

        # 更新统计
        self.cooperation_stats["total_actions"] += 1

        # 识别协作行为
        behavior_scores = self.identify_cooperative_behavior(agent_id, action, game_state, other_farmer_id)

        # 计算奖励
        cooperation_reward = 0.0

        # 有效接牌奖励
        if behavior_scores["effective_follow_up"] > 0:
            cooperation_reward += 0.1
            self.cooperation_stats["effective_follow_ups"] += 1

        # 策略性让牌奖励
        if behavior_scores["strategic_pass"] > 0:
            cooperation_reward += 0.05
            self.cooperation_stats["strategic_passes"] += 1

        # 信息传递奖励
        if behavior_scores["information_sharing"] > 0:
            cooperation_reward += 0.1
            self.cooperation_stats["information_sharing"] += 1

        # 控制转移奖励
        if behavior_scores["control_transfer"] > 0:
            cooperation_reward += self.control_transfer_weight
            self.cooperation_stats["control_transfers"] += 1

        # 牺牲行为奖励
        if behavior_scores["sacrifice"] > 0:
            cooperation_reward += self.sacrifice_weight
            self.cooperation_stats["sacrifices"] += 1

        return cooperation_reward * self.cooperation_weight

    def is_effective_follow_up(self, action: Any, last_action: Any, game_state: Any) -> bool:
        """
        检查是否有效接牌

        判断当前动作是否有效地接上了队友的上一个动作。

        Args:
            action: 当前动作
            last_action: 队友的上一个动作
            game_state: 游戏状态

        Returns:
            是否有效接牌
        """
        # 如果是CardGroup类型，直接使用
        if isinstance(action, CardGroup) and isinstance(last_action, CardGroup):
            # 如果队友出了单牌，我们跟上更大的单牌
            if last_action.type == CardGroupType.SINGLE and action.type == CardGroupType.SINGLE:
                return action.cards[0].rank > last_action.cards[0].rank

            # 如果队友出了对子，我们跟上更大的对子
            if last_action.type == CardGroupType.PAIR and action.type == CardGroupType.PAIR:
                return action.cards[0].rank > last_action.cards[0].rank

            # 如果队友出了顺子，我们跟上更大的顺子
            if last_action.type == CardGroupType.STRAIGHT and action.type == CardGroupType.STRAIGHT:
                return action.cards[0].rank > last_action.cards[0].rank

        # 如果是其他类型，需要根据具体实现判断
        # 这里简化处理，返回False
        return False

    def is_strategic_pass(self, action: Any, game_state: Any, other_farmer_id: str) -> bool:
        """
        检查是否有策略性让牌

        判断当前动作是否是策略性地选择不出牌，让队友出牌。

        Args:
            action: 当前动作
            game_state: 游戏状态
            other_farmer_id: 另一个农民ID

        Returns:
            是否策略性让牌
        """
        # 如果是"不出"动作
        if action is None or (isinstance(action, str) and action.lower() in ["pass", "不出"]):
            # 检查是否有能出的牌
            # 这里需要根据具体实现判断
            # 简化处理，假设有50%的概率是策略性让牌
            return np.random.random() < 0.5

        return False

    def contains_information(self, action: Any, game_state: Any) -> bool:
        """
        检查是否有信息传递

        判断当前动作是否包含了对队友有用的信息。

        Args:
            action: 当前动作
            game_state: 游戏状态

        Returns:
            是否包含信息
        """
        # 如果是CardGroup类型，直接使用
        if isinstance(action, CardGroup):
            # 出单张2或大小王可能表示没有更小的牌
            if action.type == CardGroupType.SINGLE and action.cards[0].rank >= 12:  # 假设12是2
                return True

            # 出炸弹可能表示想速战速决
            if action.type == CardGroupType.BOMB:
                return True

        # 如果是其他类型，需要根据具体实现判断
        # 这里简化处理，返回False
        return False

    def share_team_reward(self, rewards: Dict[str, float], farmer_ids: List[str]) -> Dict[str, float]:
        """
        共享团队奖励

        将农民的奖励部分共享，促进团队合作。

        Args:
            rewards: 原始奖励字典，键为智能体ID，值为奖励值
            farmer_ids: 农民ID列表

        Returns:
            共享后的奖励字典
        """
        if len(farmer_ids) <= 1:
            return rewards

        # 计算农民的平均奖励
        farmer_rewards = [rewards.get(farmer_id, 0.0) for farmer_id in farmer_ids]
        avg_farmer_reward = sum(farmer_rewards) / len(farmer_rewards)

        # 共享奖励
        shared_rewards = rewards.copy()
        for farmer_id in farmer_ids:
            original_reward = rewards.get(farmer_id, 0.0)
            # 混合原始奖励和平均奖励
            shared_rewards[farmer_id] = (
                self.team_reward_weight * avg_farmer_reward +
                (1 - self.team_reward_weight) * original_reward
            )

        return shared_rewards

    def reset(self) -> None:
        """重置历史记录和统计"""
        self.action_history = {}
        self.cooperation_stats = {
            "effective_follow_ups": 0,
            "strategic_passes": 0,
            "information_sharing": 0,
            "control_transfers": 0,
            "sacrifices": 0,
            "total_actions": 0
        }

    def get_cooperation_stats(self) -> Dict[str, Any]:
        """
        获取协作统计

        Returns:
            协作统计字典
        """
        stats = self.cooperation_stats.copy()

        # 计算比例
        total = stats["total_actions"]
        if total > 0:
            stats["effective_follow_up_rate"] = stats["effective_follow_ups"] / total
            stats["strategic_pass_rate"] = stats["strategic_passes"] / total
            stats["information_sharing_rate"] = stats["information_sharing"] / total
            stats["control_transfer_rate"] = stats["control_transfers"] / total
            stats["sacrifice_rate"] = stats["sacrifices"] / total

        return stats

    def get_cooperation_score(self) -> float:
        """
        计算协作分数

        返回一个0到1之间的分数，表示协作程度。

        Returns:
            协作分数
        """
        stats = self.get_cooperation_stats()

        # 如果没有动作，返回0
        if stats["total_actions"] == 0:
            return 0.0

        # 计算加权分数
        score = (
            0.3 * stats.get("effective_follow_up_rate", 0.0) +
            0.15 * stats.get("strategic_pass_rate", 0.0) +
            0.15 * stats.get("information_sharing_rate", 0.0) +
            0.2 * stats.get("control_transfer_rate", 0.0) +
            0.2 * stats.get("sacrifice_rate", 0.0)
        )

        return min(1.0, max(0.0, score))

    def identify_cooperative_behavior(
        self,
        agent_id: str,
        action: Any,
        game_state: Any,
        other_farmer_id: str
    ) -> Dict[str, float]:
        """
        识别协作行为并返回各种行为的得分

        Args:
            agent_id: 当前农民ID
            action: 当前农民的动作
            game_state: 游戏状态
            other_farmer_id: 另一个农民ID

        Returns:
            协作行为得分字典
        """
        behavior_scores = {
            "effective_follow_up": 0.0,
            "strategic_pass": 0.0,
            "information_sharing": 0.0,
            "control_transfer": 0.0,
            "sacrifice": 0.0
        }

        # 检查是否有效接牌
        if len(self.action_history.get(other_farmer_id, [])) > 0:
            last_action = self.action_history[other_farmer_id][-1]
            if self.is_effective_follow_up(action, last_action, game_state):
                behavior_scores["effective_follow_up"] = 1.0

        # 检查是否有策略性让牌
        if self.is_strategic_pass(action, game_state, other_farmer_id):
            behavior_scores["strategic_pass"] = 1.0

        # 检查是否有信息传递
        if self.contains_information(action, game_state):
            behavior_scores["information_sharing"] = 1.0

        # 检查是否有控制转移
        if self._is_control_transfer(action, game_state, other_farmer_id):
            behavior_scores["control_transfer"] = 1.0

        # 检查是否有牺牲行为
        if self._is_sacrifice(action, game_state, other_farmer_id):
            behavior_scores["sacrifice"] = 1.0

        return behavior_scores

    def _is_control_transfer(self, action: Any, game_state: Any, other_farmer_id: str) -> bool:
        """
        检查是否有控制转移

        判断当前动作是否将控制权从地主转移给队友。

        Args:
            action: 当前动作
            game_state: 游戏状态
            other_farmer_id: 另一个农民ID

        Returns:
            是否控制转移
        """
        # 如果是CardGroup类型，直接使用
        if isinstance(action, CardGroup):
            # 如果当前动作是大牌，可能是控制转移
            if action.type in [CardGroupType.BOMB, CardGroupType.ROCKET]:
                return True

            # 如果当前动作是单牌，且是大牌，可能是控制转移
            if action.type == CardGroupType.SINGLE and action.cards[0].rank >= 12:  # 假设12是2
                return True

        # 如果是其他类型，需要根据具体实现判断
        # 这里简化处理，返回False
        return False

    def _is_sacrifice(self, action: Any, game_state: Any, other_farmer_id: str) -> bool:
        """
        检查是否有牺牲行为

        判断当前动作是否是牺牲自己的利益，帮助队友。

        Args:
            action: 当前动作
            game_state: 游戏状态
            other_farmer_id: 另一个农民ID

        Returns:
            是否牺牲行为
        """
        # 如果是"不出"动作，且有能出的牌，可能是牺牲行为
        if action is None or (isinstance(action, str) and action.lower() in ["pass", "不出"]):
            # 检查是否有能出的牌
            # 这里需要根据具体实现判断
            # 简化处理，假设有30%的概率是牺牲行为
            return np.random.random() < 0.3

        # 如果是CardGroup类型，直接使用
        if isinstance(action, CardGroup):
            # 如果当前动作是小牌，且有大牌可出，可能是牺牲行为
            # 这里需要根据具体实现判断
            # 简化处理，假设有20%的概率是牺牲行为
            return np.random.random() < 0.2

        return False