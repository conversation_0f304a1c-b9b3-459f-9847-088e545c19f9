#!/usr/bin/env python3
"""
测试MCTS修复的有效性

这个测试脚本验证：
1. Node.value()方法能正确处理异常tensor状态
2. _backpropagate方法能安全处理各种数据类型
3. MCTS不会回退到简化策略
"""

import sys
import os
import numpy as np
import torch
import unittest
from unittest.mock import Mock, patch

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from cardgame_ai.algorithms.mcts import Node, MCTS
from cardgame_ai.algorithms.efficient_zero_algorithm import EfficientZero


class TestMCTSFix(unittest.TestCase):
    """测试MCTS修复"""
    
    def setUp(self):
        """设置测试环境"""
        self.node = Node(prior=0.5, use_value_distribution=False)
        self.dist_node = Node(prior=0.5, use_value_distribution=True, value_support_size=10)
        
    def test_node_value_with_normal_scalar(self):
        """测试正常标量值"""
        self.node.value_sum = 10.0
        self.node.visit_count = 5
        self.assertEqual(self.node.value(), 2.0)
        
    def test_node_value_with_zero_visits(self):
        """测试零访问次数"""
        self.node.value_sum = 10.0
        self.node.visit_count = 0
        self.assertEqual(self.node.value(), 0.0)
        
    def test_node_value_with_nan_scalar(self):
        """测试NaN标量值"""
        self.node.value_sum = float('nan')
        self.node.visit_count = 5
        self.assertEqual(self.node.value(), 0.0)
        
    def test_node_value_with_inf_scalar(self):
        """测试无穷大标量值"""
        self.node.value_sum = float('inf')
        self.node.visit_count = 5
        self.assertEqual(self.node.value(), 0.0)
        
    def test_node_value_with_normal_tensor(self):
        """测试正常tensor值"""
        self.node.value_sum = torch.tensor([1.0, 2.0, 3.0])
        self.node.visit_count = 2
        expected = torch.tensor([1.0, 2.0, 3.0]).mean().item() / 2
        self.assertEqual(self.node.value(), expected)
        
    def test_node_value_with_nan_tensor(self):
        """测试包含NaN的tensor"""
        self.node.value_sum = torch.tensor([1.0, float('nan'), 3.0])
        self.node.visit_count = 2
        self.assertEqual(self.node.value(), 0.0)
        
    def test_node_value_with_inf_tensor(self):
        """测试包含无穷大的tensor"""
        self.node.value_sum = torch.tensor([1.0, float('inf'), 3.0])
        self.node.visit_count = 2
        self.assertEqual(self.node.value(), 0.0)
        
    def test_node_value_with_empty_tensor(self):
        """测试空tensor"""
        self.node.value_sum = torch.tensor([])
        self.node.visit_count = 2
        self.assertEqual(self.node.value(), 0.0)
        
    def test_node_value_with_normal_numpy(self):
        """测试正常numpy数组"""
        self.node.value_sum = np.array([1.0, 2.0, 3.0])
        self.node.visit_count = 2
        expected = np.mean([1.0, 2.0, 3.0]) / 2
        self.assertEqual(self.node.value(), expected)
        
    def test_node_value_with_nan_numpy(self):
        """测试包含NaN的numpy数组"""
        self.node.value_sum = np.array([1.0, np.nan, 3.0])
        self.node.visit_count = 2
        self.assertEqual(self.node.value(), 0.0)
        
    def test_node_value_with_inf_numpy(self):
        """测试包含无穷大的numpy数组"""
        self.node.value_sum = np.array([1.0, np.inf, 3.0])
        self.node.visit_count = 2
        self.assertEqual(self.node.value(), 0.0)
        
    def test_node_value_with_empty_numpy(self):
        """测试空numpy数组"""
        self.node.value_sum = np.array([])
        self.node.visit_count = 2
        self.assertEqual(self.node.value(), 0.0)
        
    def test_backpropagate_with_normal_values(self):
        """测试正常值的反向传播"""
        mcts = MCTS(num_simulations=10)
        search_path = [self.node]
        value = 1.0
        
        # 模拟反向传播
        result = mcts._backpropagate(search_path, value, 0.9)
        
        # 检查节点状态
        self.assertEqual(self.node.visit_count, 1)
        self.assertEqual(self.node.value_sum, 1.0)
        
    def test_backpropagate_with_nan_values(self):
        """测试NaN值的反向传播"""
        mcts = MCTS(num_simulations=10)
        search_path = [self.node]
        value = float('nan')
        
        # 模拟反向传播
        result = mcts._backpropagate(search_path, value, 0.9)
        
        # 检查节点状态 - 应该被安全处理
        self.assertEqual(self.node.visit_count, 1)
        # value_sum应该被重置为安全值
        self.assertFalse(np.isnan(self.node.value_sum))
        
    def test_backpropagate_with_tensor_values(self):
        """测试tensor值的反向传播"""
        mcts = MCTS(num_simulations=10)
        search_path = [self.dist_node]
        value = torch.tensor([1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0])
        
        # 模拟反向传播
        result = mcts._backpropagate(search_path, value, 0.9)
        
        # 检查节点状态
        self.assertEqual(self.dist_node.visit_count, 1)
        self.assertTrue(isinstance(self.dist_node.value_sum, np.ndarray))
        
    def test_efficient_zero_no_fallback(self):
        """测试EfficientZero不会回退到简化策略"""
        # 创建模拟的EfficientZero实例
        with patch('cardgame_ai.algorithms.efficient_zero_algorithm.EfficientZeroModel') as mock_model:
            with patch('cardgame_ai.algorithms.efficient_zero_algorithm.MCTS') as mock_mcts_class:
                # 设置MCTS模拟失败
                mock_mcts = Mock()
                mock_mcts.run.side_effect = Exception("模拟MCTS失败")
                mock_mcts_class.return_value = mock_mcts
                
                # 创建EfficientZero实例
                ez = EfficientZero(
                    observation_space_size=100,
                    action_space_size=10,
                    hidden_size=64
                )
                
                # 创建模拟状态
                mock_state = Mock()
                mock_state.get_observation.return_value = np.random.random(100)
                mock_state.get_legal_actions.return_value = [0, 1, 2]
                
                # 测试act方法应该抛出异常而不是回退
                with self.assertRaises(RuntimeError) as context:
                    ez.act(mock_state)
                
                # 验证异常消息
                self.assertIn("MCTS执行失败，无法继续训练", str(context.exception))


def run_mcts_stress_test():
    """运行MCTS压力测试"""
    print("开始MCTS压力测试...")
    
    # 创建大量节点并测试各种异常情况
    test_cases = [
        ("正常值", 1.0),
        ("NaN值", float('nan')),
        ("正无穷", float('inf')),
        ("负无穷", float('-inf')),
        ("零值", 0.0),
        ("负值", -1.0),
    ]
    
    for name, value in test_cases:
        print(f"测试 {name}...")
        node = Node(prior=0.5)
        node.value_sum = value
        node.visit_count = 5
        
        try:
            result = node.value()
            print(f"  结果: {result}")
            assert not np.isnan(result), f"{name} 产生了NaN结果"
            assert not np.isinf(result), f"{name} 产生了无穷大结果"
        except Exception as e:
            print(f"  错误: {e}")
            raise
    
    print("MCTS压力测试完成！")


if __name__ == '__main__':
    # 运行单元测试
    print("运行MCTS修复单元测试...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # 运行压力测试
    run_mcts_stress_test()
    
    print("\n所有测试完成！MCTS修复验证成功。")
