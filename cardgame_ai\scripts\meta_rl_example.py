#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
元强化学习示例脚本

展示如何使用元强化学习训练器，实现快速适应机制。
"""

import os
import sys
import argparse
import logging
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import time
import random
from typing import Dict, List, Tuple, Any, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from cardgame_ai.training.meta_rl_trainer import MetaRLTrainer
from cardgame_ai.utils.task_sampler import TaskSampler, Task
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.algorithms.muzero import MuZero

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
# 抑制 meta_rl_trainer 模块的转换警告
logging.getLogger('cardgame_ai.training.meta_rl_trainer').setLevel(logging.ERROR)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='元强化学习示例')

    parser.add_argument('--meta_algorithm', type=str, default='maml',
                        choices=['maml', 'reptile', 'both'],
                        help='元算法，支持 maml、reptile 或 both')
    parser.add_argument('--inner_lr', type=float, default=0.01,
                        help='内循环学习率')
    parser.add_argument('--meta_lr', type=float, default=0.001,
                        help='元学习率')
    parser.add_argument('--num_inner_steps', type=int, default=5,
                        help='内循环步数')
    parser.add_argument('--num_meta_steps', type=int, default=100,
                        help='元训练步数')
    parser.add_argument('--num_tasks', type=int, default=5,
                        help='任务数量')
    parser.add_argument('--batch_size', type=int, default=32,
                        help='批次大小')
    parser.add_argument('--save_path', type=str, default='models/meta_rl',
                        help='保存路径')
    parser.add_argument('--load_path', type=str, default=None,
                        help='加载路径')
    parser.add_argument('--seed', type=int, default=42,
                        help='随机种子')
    parser.add_argument('--device', type=str, default=None,
                        help='设备')

    return parser.parse_args()


class SimpleModel(nn.Module):
    """简单模型"""

    def __init__(self, input_dim: int = 128, hidden_dim: int = 64, output_dim: int = 32):
        """
        初始化简单模型

        Args:
            input_dim: 输入维度
            hidden_dim: 隐藏层维度
            output_dim: 输出维度
        """
        super().__init__()
        self.init_args = (input_dim, hidden_dim, output_dim)
        self.init_kwargs = {}

        self.fc1 = nn.Linear(input_dim, hidden_dim)
        self.fc2 = nn.Linear(hidden_dim, hidden_dim)
        self.fc3 = nn.Linear(hidden_dim, output_dim)

    def forward(self, x):
        """前向传播"""
        x = F.relu(self.fc1(x))
        x = F.relu(self.fc2(x))
        x = self.fc3(x)
        return x


def generate_dummy_data(input_dim: int, output_dim: int, num_samples: int) -> Dict[str, Any]:
    """
    生成虚拟数据

    Args:
        input_dim: 输入维度
        output_dim: 输出维度
        num_samples: 样本数量

    Returns:
        虚拟数据
    """
    # 生成随机输入
    observations = np.random.randn(num_samples, input_dim).astype(np.float32)

    # 生成随机动作
    actions = np.random.randint(0, output_dim, size=num_samples).astype(np.int64)

    return {
        "observations": observations,
        "actions": actions
    }


def create_opponent_tasks(task_sampler: TaskSampler, num_tasks: int = 5,
                        input_dim: int = 128, output_dim: int = 32,
                        samples_per_task: int = 100) -> List[Task]:
    """
    创建对手任务

    Args:
        task_sampler: 任务采样器
        num_tasks: 任务数量
        input_dim: 输入维度
        output_dim: 输出维度
        samples_per_task: 每个任务的样本数量

    Returns:
        任务列表
    """
    # 生成对手任务
    tasks = task_sampler.generate_opponent_tasks(num_tasks)

    # 为每个任务生成数据
    for task in tasks:
        # 生成支持集数据
        support_data = generate_dummy_data(input_dim, output_dim, samples_per_task)
        for _ in range(samples_per_task):
            task.add_support_data(support_data)

        # 生成查询集数据
        query_data = generate_dummy_data(input_dim, output_dim, samples_per_task)
        for _ in range(samples_per_task // 2):  # 查询集通常小于支持集
            task.add_query_data(query_data)

    return tasks


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()

    # 设置随机种子
    random.seed(args.seed)
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)

    # 设置设备
    device = args.device
    if device is None:
        device = 'cuda' if torch.cuda.is_available() else 'cpu'

    # 创建模型
    input_dim = 128
    hidden_dim = 64
    output_dim = 32
    model = SimpleModel(input_dim, hidden_dim, output_dim)
    model.to(device)

    # 创建元优化器
    meta_optimizer = torch.optim.Adam(model.parameters(), lr=args.meta_lr)

    # 创建任务采样器
    task_sampler = TaskSampler()

    # 创建对手任务
    create_opponent_tasks(
        task_sampler=task_sampler,
        num_tasks=args.num_tasks,
        input_dim=input_dim,
        output_dim=output_dim,
        samples_per_task=args.batch_size
    )

    # 创建元强化学习训练器
    meta_rl_trainer = MetaRLTrainer(
        base_model=model,
        meta_optimizer=meta_optimizer,
        task_sampler=task_sampler,
        inner_lr=args.inner_lr,
        num_inner_steps=args.num_inner_steps,
        meta_algorithm=args.meta_algorithm,
        device=device
    )

    # 加载模型（如果有）
    if args.load_path and os.path.exists(args.load_path):
        meta_rl_trainer.load(args.load_path)
        logger.info(f"已加载模型: {args.load_path}")

    # 元训练
    logger.info(f"开始元训练: {args.num_meta_steps}步")
    start_time = time.time()

    for step in range(args.num_meta_steps):
        # 执行元训练步骤
        meta_loss = meta_rl_trainer.meta_train_step(args.num_tasks)

        # 输出进度
        if (step + 1) % 10 == 0 or step == 0:
            logger.info(f"步骤 {step + 1}/{args.num_meta_steps}, 元损失: {meta_loss:.4f}")

    # 输出训练时间
    train_time = time.time() - start_time
    logger.info(f"元训练完成，用时: {train_time:.2f}秒")

    # 保存模型
    if args.save_path:
        os.makedirs(args.save_path, exist_ok=True)
        meta_rl_trainer.save(args.save_path)
        logger.info(f"已保存模型: {args.save_path}")

    # 测试快速适应
    logger.info("测试快速适应")

    # 采样一个新任务
    new_task = task_sampler.generate_opponent_tasks(1)[0]

    # 为新任务生成数据
    support_data = generate_dummy_data(input_dim, output_dim, args.batch_size)
    for _ in range(args.batch_size):
        new_task.add_support_data(support_data)

    query_data = generate_dummy_data(input_dim, output_dim, args.batch_size)
    for _ in range(args.batch_size // 2):
        new_task.add_query_data(query_data)

    # 评估基础模型
    base_results = meta_rl_trainer.evaluate(new_task)
    logger.info(f"基础模型性能: 损失={base_results['loss']:.4f}, 准确率={base_results['accuracy']:.4f}")

    # 快速适应
    logger.info(f"执行快速适应: {args.num_inner_steps}步")
    adapted_model = meta_rl_trainer.adapt(new_task, args.num_inner_steps)

    # 评估适应后的模型
    adapted_results = meta_rl_trainer.evaluate(new_task, adapted_model)
    logger.info(f"适应后模型性能: 损失={adapted_results['loss']:.4f}, 准确率={adapted_results['accuracy']:.4f}")

    # 计算提升
    loss_improvement = base_results['loss'] - adapted_results['loss']
    accuracy_improvement = adapted_results['accuracy'] - base_results['accuracy']
    logger.info(f"性能提升: 损失减少={loss_improvement:.4f}, 准确率提升={accuracy_improvement:.4f}")

    # 输出统计信息
    logger.info("\n统计信息:")
    logger.info(f"元更新次数: {meta_rl_trainer.stats['meta_updates']}")
    logger.info(f"内部更新次数: {meta_rl_trainer.stats['inner_updates']}")
    logger.info(f"平均元损失: {np.mean(meta_rl_trainer.stats['meta_losses']):.4f}")
    logger.info(f"平均适应时间: {np.mean(meta_rl_trainer.stats['adaptation_times']):.4f}秒")
    logger.info(f"平均元更新时间: {np.mean(meta_rl_trainer.stats['meta_update_times']):.4f}秒")

    return 0


if __name__ == "__main__":
    main()
