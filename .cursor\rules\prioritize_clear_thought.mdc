---
description:
globs:
alwaysApply: false
---
# 优先使用 Clear-Thought 工具进行分析和决策

在处理代码分析、策略制定、问题解决或实施修改等任务时，应优先考虑使用 `mcp_clear-thought` 系列工具。这些工具提供结构化的思考框架，有助于进行更深入、全面的分析和决策。

## 具体工具使用场景建议：

*   **`mcp_clear-thought_sequentialthinking`**: 当需要将复杂问题分解为多个步骤，进行逐步分析和规划时使用。适用于需要详细计划和迭代思考的任务。
*   **`mcp_clear-thought_mentalmodel`**: 当需要从特定思维模型（如第一性原理、机会成本等）出发分析问题时使用。有助于从不同角度审视问题。
*   **`mcp_clear-thought_debuggingapproach`**: 当需要系统性地调试和解决技术问题时使用。提供多种调试方法（如二分查找、逆向工程等）的结构化流程。
*   **`mcp_clear-thought_collaborativereasoning`**: 当需要模拟多方协作、汇集不同专家观点来解决复杂问题时使用。有助于进行全面的利弊分析和决策。
*   **`mcp_clear-thought_decisionframework`**: 当需要进行结构化的决策分析，评估不同选项的优劣时使用。适用于需要在多个标准和约束下做出最优选择的场景。
*   **`mcp_clear-thought_metacognitivemonitoring`**: 当需要对自身的知识、推理过程和结论进行反思和评估时使用。有助于识别潜在的偏见和知识盲区。
*   **`mcp_clear-thought_scientificmethod`**: 当需要通过科学方法（观察、提问、假设、实验、分析、结论）来探究问题或验证方案时使用。
*   **`mcp_clear-thought_structuredargumentation`**: 当需要构建或分析复杂的论证结构，评估论点的有效性和说服力时使用。
*   **`mcp_clear-thought_visualreasoning`**: 当需要通过可视化方式（如图表、流程图）来辅助思考、分析和沟通时使用。

**核心原则**：在任务开始阶段，首先评估是否可以使用上述任一 `clear-thought` 工具来构建解决问题的框架。如果适用，应优先选择并遵循其结构化流程。即使任务看起来简单，尝试使用这些工具也可能带来更深刻的洞察和更优的解决方案。
