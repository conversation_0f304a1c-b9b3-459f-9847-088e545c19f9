"""
动态计算预算分配示例脚本

展示如何使用关键决策点检测和动态计算预算分配功能。
"""

import os
import sys
import argparse
import logging
import numpy as np
import torch
import time
import random
from typing import Dict, List, Any, Optional

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入必要的模块
from cardgame_ai.algorithms.hybrid_decision_system import HybridDecisionSystem
from cardgame_ai.algorithms.key_moment_detector import KeyMomentDetector
from cardgame_ai.algorithms.dynamic_budget_allocator import DynamicBudgetAllocator
from cardgame_ai.algorithms.efficient_zero import EfficientZero
from cardgame_ai.algorithms.rule_based import RuleBasedAgent
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.games.doudizhu.state import DouDizhuState


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="动态计算预算分配示例")
    
    # 模型参数
    parser.add_argument("--model_path", type=str, default=None, help="模型路径")
    parser.add_argument("--key_moment_model", type=str, default=None, help="关键决策点检测器模型路径")
    
    # 预算参数
    parser.add_argument("--base_simulations", type=int, default=50, help="基础模拟次数")
    parser.add_argument("--critical_multiplier", type=int, default=10, help="关键决策点时的模拟次数倍数")
    parser.add_argument("--critical_threshold", type=float, default=0.7, help="关键决策点阈值")
    parser.add_argument("--max_budget", type=int, default=5000, help="最大计算预算")
    parser.add_argument("--adaptive_scaling", action="store_true", help="是否使用自适应缩放")
    
    # 游戏参数
    parser.add_argument("--num_games", type=int, default=5, help="游戏数量")
    parser.add_argument("--seed", type=int, default=None, help="随机种子")
    
    return parser.parse_args()


def simulate_game(agent: HybridDecisionSystem, env: DouDizhuEnvironment):
    """
    模拟一局游戏
    
    Args:
        agent: 混合决策系统
        env: 游戏环境
    """
    # 重置环境
    state = env.reset()
    done = False
    
    # 游戏循环
    while not done:
        # 获取当前玩家
        player_id = state.get_player_id()
        
        # 获取合法动作
        legal_actions = env.get_legal_actions(state)
        
        # 使用混合决策系统做出决策
        action = agent.act(state, legal_actions)
        
        # 执行动作
        next_state, reward, done, info = env.step(action)
        
        # 更新状态
        state = next_state
    
    # 获取游戏结果
    payoffs = env.get_payoffs(state)
    winner = np.argmax(payoffs)
    
    logger.info(f"游戏结束，获胜玩家: {winner}")
    
    # 打印组件使用统计
    stats = agent.get_stats()
    logger.info(f"组件使用统计:")
    for name, count in stats["component_usage"].items():
        logger.info(f"  {name}: {count}")
    
    # 打印关键决策点统计
    if "key_moment_detection" in stats:
        key_moment_stats = stats["key_moment_detection"]
        logger.info(f"关键决策点统计:")
        logger.info(f"  关键决策点数量: {key_moment_stats['critical_moments']}")
        logger.info(f"  关键决策点比例: {key_moment_stats['critical_ratio']:.4f}")
        logger.info(f"  平均关键程度: {key_moment_stats['avg_criticality']:.4f}")
    
    # 打印动态预算分配统计
    if "dynamic_budget" in stats:
        budget_stats = stats["dynamic_budget"]
        logger.info(f"动态预算分配统计:")
        logger.info(f"  总分配次数: {budget_stats['total_allocations']}")
        logger.info(f"  关键决策点分配次数: {budget_stats['critical_allocations']}")
        logger.info(f"  关键决策点分配比例: {budget_stats['critical_ratio']:.4f}")
        logger.info(f"  平均预算: {budget_stats['avg_budget']:.2f}")
        logger.info(f"  最大预算: {budget_stats['max_budget_used']}")


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 设置随机种子
    if args.seed is not None:
        np.random.seed(args.seed)
        torch.manual_seed(args.seed)
        random.seed(args.seed)
    
    # 创建游戏环境
    env = DouDizhuEnvironment()
    
    # 创建模型
    model = None
    if args.model_path:
        model = EfficientZero(model_path=args.model_path)
        logger.info(f"已加载模型: {args.model_path}")
    
    # 创建规则代理
    rule_agent = RuleBasedAgent()
    
    # 创建关键决策点检测器
    key_moment_detector = None
    if args.key_moment_model:
        key_moment_detector = KeyMomentDetector.load(args.key_moment_model)
        logger.info(f"已加载关键决策点检测器: {args.key_moment_model}")
    else:
        # 创建默认的关键决策点检测器
        state_dim = env.observation_space.shape[0]
        key_moment_detector = KeyMomentDetector(
            state_dim=state_dim,
            hidden_dims=[256, 128, 64],
            output_dim=1,
            use_attention=True,
            use_history=True
        )
        logger.info("已创建默认关键决策点检测器")
    
    # 创建动态预算分配器
    dynamic_budget_allocator = DynamicBudgetAllocator(
        key_moment_detector=key_moment_detector,
        base_budget=args.base_simulations,
        max_budget=args.max_budget,
        amplification_factor=args.critical_multiplier,
        critical_threshold=args.critical_threshold,
        adaptive_scaling=args.adaptive_scaling
    )
    logger.info("已创建动态预算分配器")
    
    # 创建混合决策系统
    hybrid_system = HybridDecisionSystem(
        neural_network_model=model,
        search_model=model,
        rule_agent=rule_agent,
        key_moment_detector=key_moment_detector,
        dynamic_budget_allocator=dynamic_budget_allocator,
        meta_strategy="adaptive",
        base_mcts_simulations=args.base_simulations,
        critical_mcts_multiplier=args.critical_multiplier,
        critical_threshold=args.critical_threshold
    )
    logger.info("已创建混合决策系统")
    
    # 模拟游戏
    for i in range(args.num_games):
        logger.info(f"开始游戏 {i+1}/{args.num_games}")
        simulate_game(hybrid_system, env)
    
    # 打印最终统计信息
    stats = hybrid_system.get_stats()
    logger.info(f"总决策次数: {stats['decisions']}")
    
    if "key_moment_detection" in stats:
        key_moment_stats = stats["key_moment_detection"]
        logger.info(f"总关键决策点数量: {key_moment_stats['critical_moments']}")
        logger.info(f"关键决策点比例: {key_moment_stats['critical_ratio']:.4f}")
    
    if "dynamic_budget" in stats:
        budget_stats = stats["dynamic_budget"]
        logger.info(f"总预算分配次数: {budget_stats['total_allocations']}")
        logger.info(f"平均预算: {budget_stats['avg_budget']:.2f}")
    
    return 0


if __name__ == "__main__":
    main()
