{"model_info": {"filename": "resnet_large_2.2Mparams_perf0.950_large_20250601_175634", "model_path": "test_models\\resnet_large_2.2Mparams_perf0.950_large_20250601_175634.pt", "save_timestamp": "2025-06-01T17:56:34.441488", "parameter_stats": {"total_parameters": 2232064, "trainable_parameters": 2232064, "non_trainable_parameters": 0, "layer_parameters": {"input_layer.weight": 524288, "input_layer.bias": 512, "hidden_layers.0.0.weight": 262144, "hidden_layers.0.0.bias": 512, "hidden_layers.1.0.weight": 262144, "hidden_layers.1.0.bias": 512, "hidden_layers.2.0.weight": 262144, "hidden_layers.2.0.bias": 512, "hidden_layers.3.0.weight": 262144, "hidden_layers.3.0.bias": 512, "hidden_layers.4.0.weight": 262144, "hidden_layers.4.0.bias": 512, "hidden_layers.5.0.weight": 262144, "hidden_layers.5.0.bias": 512, "output_layer.weight": 131072, "output_layer.bias": 256}}}, "training_info": {"epoch": null, "performance": 0.95, "tag": "large"}, "model_architecture": {"model_class": "SimpleResNet", "model_modules": ["", "input_layer", "hidden_layers", "hidden_layers.0", "hidden_layers.0.0", "hidden_layers.0.1", "hidden_layers.0.2", "hidden_layers.1", "hidden_layers.1.0", "hidden_layers.1.1", "hidden_layers.1.2", "hidden_layers.2", "hidden_layers.2.0", "hidden_layers.2.1", "hidden_layers.2.2", "hidden_layers.3", "hidden_layers.3.0", "hidden_layers.3.1", "hidden_layers.3.2", "hidden_layers.4", "hidden_layers.4.0", "hidden_layers.4.1", "hidden_layers.4.2", "hidden_layers.5", "hidden_layers.5.0", "hidden_layers.5.1", "hidden_layers.5.2", "output_layer", "relu", "dropout"]}}