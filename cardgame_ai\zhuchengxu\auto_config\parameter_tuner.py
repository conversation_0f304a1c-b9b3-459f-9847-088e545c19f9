"""
参数调优模块

根据硬件规格自动计算最优训练参数，
包括批次大小、学习率、MCTS模拟次数等关键参数。
"""

import math
from typing import Dict, List, Any, Optional
from .utils import CrossPlatformUtils

class ParameterTuner:
    """参数调优器"""
    
    def __init__(self):
        self.logger = CrossPlatformUtils.setup_logging()
        
        # GPU性能因子表 (相对于RTX 3080的性能倍数)
        self.gpu_performance_factors = {
            'rtx 3080': 1.0,
            'rtx 4090': 1.5,
            'a100': 2.0,
            'v100': 1.8,
            'rtx 3090': 1.2,
            'rtx 4080': 1.3,
            'rtx 3070': 0.8,
            'rtx 3060': 0.6,
            'gtx 1080': 0.5,
            'gtx 1070': 0.4
        }
        
        # 基础参数配置
        self.base_params = {
            'batch_size': 128,
            'learning_rate': 0.001,
            'mcts_simulations': 50,
            'num_workers': 8,
            'prefetch_factor': 4
        }
    
    def tune_all_parameters(self, hardware_info: Dict[str, Any], 
                           algorithm: str = 'efficient_zero') -> Dict[str, Any]:
        """
        根据硬件信息调优所有参数
        
        Args:
            hardware_info: 硬件信息字典
            algorithm: 算法类型
            
        Returns:
            调优后的参数字典
        """
        self.logger.info(f"开始为算法 {algorithm} 调优参数...")
        
        gpu_specs = hardware_info.get('gpu', [])
        cpu_specs = hardware_info.get('cpu', {})
        memory_specs = hardware_info.get('memory', {})
        
        # 计算各项参数
        tuned_params = {
            'batch_size': self.calculate_batch_size(gpu_specs, memory_specs),
            'learning_rate': self.calculate_learning_rate(gpu_specs),
            'mcts_simulations': self.calculate_mcts_simulations(gpu_specs),
            'num_workers': self.calculate_num_workers(cpu_specs, gpu_specs),
            'prefetch_factor': self.calculate_prefetch_factor(cpu_specs, gpu_specs),
            'mixed_precision': self.should_use_mixed_precision(gpu_specs),
            'distributed': self.should_use_distributed(gpu_specs),
            'gradient_accumulation_steps': self.calculate_gradient_accumulation(gpu_specs),
            'memory_optimization': self.get_memory_optimization_settings(gpu_specs, memory_specs)
        }
        
        # 算法特定调优
        if algorithm == 'efficient_zero':
            tuned_params.update(self.tune_efficient_zero_params(gpu_specs))
        
        self.logger.info("参数调优完成")
        self._log_tuned_parameters(tuned_params)
        
        return tuned_params
    
    def calculate_batch_size(self, gpu_specs: List[Dict], memory_specs: Dict) -> int:
        """
        计算最优批次大小
        
        Args:
            gpu_specs: GPU规格列表
            memory_specs: 内存规格
            
        Returns:
            最优批次大小
        """
        if not gpu_specs:
            return self.base_params['batch_size']
        
        # 计算总GPU显存
        total_gpu_memory = sum(gpu.get('memory_total_gb', 0) for gpu in gpu_specs)
        gpu_count = len(gpu_specs)
        
        # 基于显存的批次大小计算 (每GB显存约支持25-30个样本)
        memory_factor = total_gpu_memory * 25
        
        # 基于GPU数量的线性扩展
        gpu_factor = gpu_count
        
        # 计算建议批次大小
        suggested_batch_size = int(self.base_params['batch_size'] * memory_factor / 10 * gpu_factor)
        
        # 应用约束条件
        min_batch_size = 64 * gpu_count
        max_batch_size = 1024 * gpu_count
        
        # 确保批次大小是GPU数量的倍数（分布式训练要求）
        batch_size = max(min_batch_size, min(suggested_batch_size, max_batch_size))
        batch_size = (batch_size // gpu_count) * gpu_count
        
        self.logger.info(f"计算批次大小: {total_gpu_memory}GB显存 -> {batch_size}")
        return batch_size
    
    def calculate_learning_rate(self, gpu_specs: List[Dict]) -> float:
        """
        计算最优学习率
        
        Args:
            gpu_specs: GPU规格列表
            
        Returns:
            最优学习率
        """
        if not gpu_specs:
            return self.base_params['learning_rate']
        
        # 基础学习率
        base_lr = self.base_params['learning_rate']
        
        # 根据GPU数量调整学习率 (线性缩放规则)
        gpu_count = len(gpu_specs)
        if gpu_count > 1:
            # 多GPU时使用平方根缩放，避免学习率过大
            lr_scale = math.sqrt(gpu_count)
            learning_rate = base_lr * lr_scale
        else:
            learning_rate = base_lr
        
        # 限制学习率范围
        learning_rate = max(0.0001, min(learning_rate, 0.01))
        
        self.logger.info(f"计算学习率: {gpu_count}个GPU -> {learning_rate}")
        return round(learning_rate, 6)
    
    def calculate_mcts_simulations(self, gpu_specs: List[Dict]) -> int:
        """
        计算MCTS模拟次数
        
        Args:
            gpu_specs: GPU规格列表
            
        Returns:
            最优MCTS模拟次数
        """
        if not gpu_specs:
            return self.base_params['mcts_simulations']
        
        base_simulations = self.base_params['mcts_simulations']
        total_performance = 0
        
        for gpu in gpu_specs:
            gpu_name = gpu.get('name', '').lower()
            performance_factor = 1.0
            
            # 查找匹配的性能因子
            for gpu_model, factor in self.gpu_performance_factors.items():
                if gpu_model in gpu_name:
                    performance_factor = factor
                    break
            
            total_performance += performance_factor
        
        # 计算模拟次数
        simulations = int(base_simulations * total_performance)
        
        # 应用约束条件
        min_simulations = 25
        max_simulations = 500
        simulations = max(min_simulations, min(simulations, max_simulations))
        
        self.logger.info(f"计算MCTS模拟次数: {total_performance:.1f}x性能 -> {simulations}")
        return simulations
    
    def calculate_num_workers(self, cpu_specs: Dict, gpu_specs: List[Dict]) -> int:
        """
        计算数据加载工作线程数
        
        Args:
            cpu_specs: CPU规格
            gpu_specs: GPU规格列表
            
        Returns:
            最优工作线程数
        """
        cpu_cores = cpu_specs.get('physical_cores', 8)
        gpu_count = len(gpu_specs) if gpu_specs else 1
        
        # 基于CPU核心数和GPU数量计算
        # 一般经验：每个GPU配2-4个worker，但不超过CPU核心数的一半
        workers_per_gpu = 3
        suggested_workers = gpu_count * workers_per_gpu
        
        # 限制在CPU核心数的75%以内
        max_workers = max(1, int(cpu_cores * 0.75))
        num_workers = min(suggested_workers, max_workers)
        
        # 确保至少有1个worker
        num_workers = max(1, num_workers)
        
        self.logger.info(f"计算工作线程数: {cpu_cores}核心, {gpu_count}GPU -> {num_workers}")
        return num_workers
    
    def calculate_prefetch_factor(self, cpu_specs: Dict, gpu_specs: List[Dict]) -> int:
        """
        计算预取因子
        
        Args:
            cpu_specs: CPU规格
            gpu_specs: GPU规格列表
            
        Returns:
            最优预取因子
        """
        # 基于内存和GPU性能调整预取因子
        gpu_count = len(gpu_specs) if gpu_specs else 1
        
        if gpu_count == 1:
            prefetch_factor = 4
        elif gpu_count <= 4:
            prefetch_factor = 6
        else:
            prefetch_factor = 8
        
        return prefetch_factor
    
    def should_use_mixed_precision(self, gpu_specs: List[Dict]) -> bool:
        """
        判断是否应该使用混合精度训练
        
        Args:
            gpu_specs: GPU规格列表
            
        Returns:
            是否使用混合精度
        """
        if not gpu_specs:
            return False
        
        # 检查GPU是否支持Tensor Core (计算能力 >= 7.0)
        for gpu in gpu_specs:
            gpu_name = gpu.get('name', '').lower()
            
            # RTX 20系列及以上、A100、V100等支持混合精度
            modern_gpus = ['rtx 20', 'rtx 30', 'rtx 40', 'a100', 'v100', 'a40', 'a6000']
            
            for modern_gpu in modern_gpus:
                if modern_gpu in gpu_name:
                    return True
        
        return False
    
    def should_use_distributed(self, gpu_specs: List[Dict]) -> bool:
        """
        判断是否应该使用分布式训练
        
        Args:
            gpu_specs: GPU规格列表
            
        Returns:
            是否使用分布式训练
        """
        return len(gpu_specs) > 1
    
    def calculate_gradient_accumulation(self, gpu_specs: List[Dict]) -> int:
        """
        计算梯度累积步数
        
        Args:
            gpu_specs: GPU规格列表
            
        Returns:
            梯度累积步数
        """
        if not gpu_specs:
            return 1
        
        # 对于显存较小的GPU，使用梯度累积来模拟更大的批次
        min_memory = min(gpu.get('memory_total_gb', 10) for gpu in gpu_specs)
        
        if min_memory < 8:
            return 4
        elif min_memory < 12:
            return 2
        else:
            return 1

    def get_memory_optimization_settings(self, gpu_specs: List[Dict],
                                       memory_specs: Dict) -> Dict[str, Any]:
        """
        获取内存优化设置

        Args:
            gpu_specs: GPU规格列表
            memory_specs: 内存规格

        Returns:
            内存优化设置字典
        """
        if not gpu_specs:
            return {}

        min_gpu_memory = min(gpu.get('memory_total_gb', 10) for gpu in gpu_specs)
        system_memory = memory_specs.get('total_gb', 16)

        settings = {
            'pin_memory': True,
            'non_blocking': True,
            'persistent_workers': True
        }

        # 根据GPU显存调整设置
        if min_gpu_memory < 8:
            settings.update({
                'gradient_checkpointing': True,
                'cpu_offload': True,
                'cache_size_gb': min(2, system_memory * 0.1)
            })
        elif min_gpu_memory < 16:
            settings.update({
                'gradient_checkpointing': False,
                'cpu_offload': False,
                'cache_size_gb': min(4, system_memory * 0.15)
            })
        else:
            settings.update({
                'gradient_checkpointing': False,
                'cpu_offload': False,
                'cache_size_gb': min(8, system_memory * 0.2)
            })

        return settings

    def tune_efficient_zero_params(self, gpu_specs: List[Dict]) -> Dict[str, Any]:
        """
        EfficientZero算法特定参数调优

        Args:
            gpu_specs: GPU规格列表

        Returns:
            EfficientZero特定参数
        """
        if not gpu_specs:
            return {}

        gpu_count = len(gpu_specs)
        total_memory = sum(gpu.get('memory_total_gb', 0) for gpu in gpu_specs)

        # EfficientZero特定参数
        params = {
            'replay_buffer_size': self._calculate_replay_buffer_size(total_memory),
            'unroll_steps': self._calculate_unroll_steps(gpu_count),
            'td_steps': self._calculate_td_steps(gpu_count),
            'model_buffer_size': self._calculate_model_buffer_size(total_memory),
            'reanalyze_ratio': self._calculate_reanalyze_ratio(gpu_count)
        }

        return params

    def _calculate_replay_buffer_size(self, total_memory_gb: float) -> int:
        """计算回放缓冲区大小"""
        # 基于总显存计算，每GB显存约支持10000个样本
        base_size = int(total_memory_gb * 10000)
        return max(50000, min(base_size, 500000))

    def _calculate_unroll_steps(self, gpu_count: int) -> int:
        """计算展开步数"""
        # 多GPU时可以使用更长的展开步数
        if gpu_count >= 4:
            return 10
        elif gpu_count >= 2:
            return 8
        else:
            return 5

    def _calculate_td_steps(self, gpu_count: int) -> int:
        """计算TD步数"""
        # 基于GPU数量调整TD步数
        return min(10, max(3, gpu_count * 2))

    def _calculate_model_buffer_size(self, total_memory_gb: float) -> int:
        """计算模型缓冲区大小"""
        base_size = int(total_memory_gb * 5000)
        return max(10000, min(base_size, 100000))

    def _calculate_reanalyze_ratio(self, gpu_count: int) -> float:
        """计算重新分析比例"""
        # 多GPU时可以使用更高的重新分析比例
        if gpu_count >= 4:
            return 0.8
        elif gpu_count >= 2:
            return 0.6
        else:
            return 0.4

    def _log_tuned_parameters(self, params: Dict[str, Any]) -> None:
        """记录调优后的参数"""
        self.logger.info("=== 参数调优结果 ===")
        for key, value in params.items():
            if isinstance(value, dict):
                self.logger.info(f"{key}:")
                for sub_key, sub_value in value.items():
                    self.logger.info(f"  {sub_key}: {sub_value}")
            else:
                self.logger.info(f"{key}: {value}")
        self.logger.info("==================")

    def validate_parameters(self, params: Dict[str, Any],
                          hardware_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证和修正参数

        Args:
            params: 待验证的参数
            hardware_info: 硬件信息

        Returns:
            验证后的参数
        """
        validated_params = params.copy()
        gpu_specs = hardware_info.get('gpu', [])

        # 验证批次大小
        if 'batch_size' in validated_params:
            batch_size = validated_params['batch_size']
            gpu_count = len(gpu_specs) if gpu_specs else 1

            # 确保批次大小是GPU数量的倍数
            if batch_size % gpu_count != 0:
                validated_params['batch_size'] = (batch_size // gpu_count + 1) * gpu_count
                self.logger.warning(f"批次大小已调整为GPU数量的倍数: {validated_params['batch_size']}")

        # 验证学习率范围
        if 'learning_rate' in validated_params:
            lr = validated_params['learning_rate']
            if lr < 0.0001 or lr > 0.01:
                validated_params['learning_rate'] = max(0.0001, min(lr, 0.01))
                self.logger.warning(f"学习率已调整到合理范围: {validated_params['learning_rate']}")

        # 验证工作线程数
        if 'num_workers' in validated_params:
            num_workers = validated_params['num_workers']
            if num_workers < 0:
                validated_params['num_workers'] = 0
                self.logger.warning("工作线程数已调整为0")

        return validated_params

    def get_optimization_recommendations(self, hardware_info: Dict[str, Any]) -> List[str]:
        """
        获取优化建议

        Args:
            hardware_info: 硬件信息

        Returns:
            优化建议列表
        """
        recommendations = []
        gpu_specs = hardware_info.get('gpu', [])
        cpu_specs = hardware_info.get('cpu', {})
        memory_specs = hardware_info.get('memory', {})

        if not gpu_specs:
            recommendations.append("建议使用GPU进行训练以获得更好的性能")
        else:
            min_gpu_memory = min(gpu.get('memory_total_gb', 0) for gpu in gpu_specs)
            if min_gpu_memory < 8:
                recommendations.append("GPU显存较小，建议启用梯度检查点和CPU卸载")

            if len(gpu_specs) > 1:
                recommendations.append("检测到多GPU，建议启用分布式训练")

        cpu_cores = cpu_specs.get('physical_cores', 0)
        if cpu_cores < 8:
            recommendations.append("CPU核心数较少，建议减少数据加载工作线程数")

        system_memory = memory_specs.get('total_gb', 0)
        if system_memory < 16:
            recommendations.append("系统内存较小，建议减少缓存大小和预取因子")

        return recommendations
