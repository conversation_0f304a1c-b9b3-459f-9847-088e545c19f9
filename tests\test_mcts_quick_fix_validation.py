#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
MCTS快速修复验证脚本

该脚本用于快速验证MCTS紧急修复是否有效，使用较小的模拟次数进行测试。

使用方法:
    python tests/test_mcts_quick_fix_validation.py

作者: BMad构架师
版本: v1.0 (紧急修复验证版)
"""

import os
import sys
import time
import logging
from pathlib import Path

# 添加项目路径到sys.path
current_dir = Path(__file__).parent.parent
sys.path.insert(0, str(current_dir))

from cardgame_ai.algorithms.mcts import MCTS
from cardgame_ai.games.doudizhu.state import DouDizhuState, GamePhase
from cardgame_ai.games.doudizhu.action import BidAction
from cardgame_ai.games.doudizhu.deck import Deck

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class MockModel:
    """模拟模型，用于测试MCTS"""
    
    def __init__(self):
        self.call_count = 0
    
    def represent(self, state):
        """模拟表示网络方法"""
        import torch
        # 返回随机隐藏状态
        hidden_state = torch.randn(1, 256)  # 假设隐藏状态维度为256
        return hidden_state
    
    def predict(self, hidden_state):
        """模拟预测方法"""
        self.call_count += 1
        # 返回随机策略和价值
        import random
        import torch
        policy_logits = torch.randn(1, 162)  # 斗地主动作空间大小
        value = torch.tensor([[random.random() - 0.5]])
        return policy_logits, value
    
    def dynamics(self, hidden_state, action):
        """模拟动态网络方法"""
        import torch
        # 返回下一个隐藏状态和奖励
        next_hidden_state = torch.randn_like(hidden_state)
        reward = torch.tensor([[0.0]])
        return next_hidden_state, reward


def create_test_state():
    """创建测试用的游戏状态"""
    deck = Deck()
    hands, landlord_cards = deck.deal(3)
    
    state = DouDizhuState(
        hands=hands,
        landlord_cards=landlord_cards,
        landlord=None,
        current_player=0,
        game_phase=GamePhase.BIDDING,
        bid_history=[],
        grab_history=[],
        highest_bidder=None,
        highest_bid=0,
        last_move=None,
        last_player=None,
        num_passes=0,
        history=[],
        played_cards=[]
    )
    
    return state


def test_basic_functionality():
    """测试基本功能"""
    logger.info("🔧 测试1: 基本功能验证")
    
    # 创建MCTS实例，使用较小的模拟次数
    mcts = MCTS(num_simulations=10)
    model = MockModel()
    state = create_test_state()
    
    start_time = time.time()
    
    try:
        # 运行MCTS
        visit_counts, action_probs = mcts.run(
            root_state=state,
            model=model
        )
        
        elapsed_time = time.time() - start_time
        logger.info(f"✅ 基本功能测试通过: 10次模拟耗时 {elapsed_time:.2f}s")
        logger.info(f"   访问计数: {len(visit_counts)} 个动作")
        logger.info(f"   模型调用次数: {model.call_count}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 基本功能测试失败: {e}")
        return False


def test_progress_logging():
    """测试进度日志"""
    logger.info("📊 测试2: 进度日志验证")
    
    # 创建MCTS实例，使用25次模拟来触发进度日志
    mcts = MCTS(num_simulations=25)
    model = MockModel()
    state = create_test_state()
    
    try:
        # 运行MCTS
        visit_counts, action_probs = mcts.run(
            root_state=state,
            model=model
        )
        
        logger.info(f"✅ 进度日志测试通过: 完成了 {mcts.actual_simulations} 次模拟")
        return True
        
    except Exception as e:
        logger.error(f"❌ 进度日志测试失败: {e}")
        return False


def test_timeout_mechanism():
    """测试超时机制（使用短超时）"""
    logger.info("⏰ 测试3: 超时机制验证")
    
    # 创建MCTS实例
    mcts = MCTS(num_simulations=100)  # 使用较多模拟次数
    model = MockModel()
    state = create_test_state()
    
    start_time = time.time()
    
    try:
        # 运行MCTS，设置1秒超时
        visit_counts, action_probs = mcts.run(
            root_state=state,
            model=model,
            max_time_ms=1000  # 1秒超时
        )
        
        elapsed_time = time.time() - start_time
        logger.info(f"✅ 超时机制测试通过: 耗时 {elapsed_time:.2f}s")
        
        if elapsed_time > 2:  # 允许1秒误差
            logger.warning(f"⚠️ 超时机制可能有问题: 预期1s，实际{elapsed_time:.2f}s")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 超时机制测试失败: {e}")
        return False


def test_reduced_simulations():
    """测试降低的模拟次数配置"""
    logger.info("📉 测试4: 降低模拟次数验证")
    
    # 创建MCTS实例，使用配置文件中的新设置
    mcts = MCTS(num_simulations=50)  # 新的默认值
    model = MockModel()
    state = create_test_state()
    
    start_time = time.time()
    
    try:
        # 运行MCTS
        visit_counts, action_probs = mcts.run(
            root_state=state,
            model=model
        )
        
        elapsed_time = time.time() - start_time
        logger.info(f"✅ 降低模拟次数测试通过: 50次模拟耗时 {elapsed_time:.2f}s")
        
        if elapsed_time > 5:  # 50次模拟应该很快完成
            logger.warning(f"⚠️ 性能可能仍有问题: 50次模拟耗时{elapsed_time:.2f}s")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 降低模拟次数测试失败: {e}")
        return False


def main():
    """主测试函数"""
    logger.info("🎯 开始MCTS快速修复验证")
    logger.info("=" * 50)
    
    test_results = []
    
    # 运行所有测试
    test_results.append(("基本功能", test_basic_functionality()))
    test_results.append(("进度日志", test_progress_logging()))
    test_results.append(("超时机制", test_timeout_mechanism()))
    test_results.append(("降低模拟次数", test_reduced_simulations()))
    
    # 汇总结果
    logger.info("=" * 50)
    logger.info("📊 测试结果汇总:")
    
    passed = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n总计: {passed}/{len(test_results)} 项测试通过")
    
    if passed >= 3:  # 至少3项测试通过就认为修复有效
        logger.info("🎉 MCTS紧急修复基本有效！可以尝试重新运行训练。")
        logger.info("💡 建议：如果训练仍有问题，可以进一步降低MCTS模拟次数到20-30次。")
        return 0
    else:
        logger.error("⚠️ 多项测试失败，需要进一步调试。")
        return 1


if __name__ == "__main__":
    sys.exit(main())
