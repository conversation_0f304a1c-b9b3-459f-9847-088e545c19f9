#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
轨迹收集示例脚本

展示如何使用轨迹收集器收集完整对局轨迹。
"""

import os
import sys
import argparse
import logging
import time
from typing import Dict, Any, List

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from cardgame_ai.interface.config import InterfaceConfig
from cardgame_ai.utils.trajectory_collector import TrajectoryCollector
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.core.agent import RandomAgent


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='轨迹收集示例')
    
    parser.add_argument('--num_games', type=int, default=5,
                        help='游戏数量')
    parser.add_argument('--save_dir', type=str, default='data/trajectories',
                        help='轨迹保存目录')
    parser.add_argument('--format', type=str, default='json', choices=['json', 'pickle'],
                        help='轨迹保存格式')
    parser.add_argument('--auto_save', action='store_true',
                        help='是否自动保存轨迹')
    parser.add_argument('--save_interval', type=int, default=10,
                        help='自动保存间隔（步数）')
    parser.add_argument('--compress', action='store_true',
                        help='是否压缩轨迹数据')
    
    return parser.parse_args()


def simulate_game(trajectory_collector: TrajectoryCollector, game_id: str):
    """
    模拟一局游戏并收集轨迹
    
    Args:
        trajectory_collector: 轨迹收集器
        game_id: 游戏ID
    """
    # 创建游戏环境
    env = DouDizhuEnvironment()
    
    # 创建随机代理
    agents = {
        "landlord": RandomAgent(env),
        "farmer1": RandomAgent(env),
        "farmer2": RandomAgent(env)
    }
    
    # 初始化游戏信息
    trajectory_collector.add_game_info(
        game_id=game_id,
        game_type="doudizhu",
        player_role="observer",
        ai_model="random"
    )
    
    # 重置环境
    state = env.reset()
    done = False
    
    # 游戏循环
    while not done:
        # 获取当前玩家
        current_player = state.current_player
        
        # 获取合法动作
        legal_actions = env.get_legal_actions(state)
        
        # 选择动作
        agent = agents[current_player]
        action = agent.act(state, legal_actions)
        
        # 执行动作
        next_state, reward, done, info = env.step(state, action)
        
        # 记录轨迹
        trajectory_collector.add_step(
            state=state,
            action=action,
            reward=reward,
            next_state=next_state,
            done=done,
            info=info
        )
        
        # 更新状态
        state = next_state
    
    # 结束游戏
    trajectory_collector.end_game()
    
    # 返回轨迹保存路径
    return trajectory_collector.save()


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)
    
    # 确保保存目录存在
    os.makedirs(args.save_dir, exist_ok=True)
    
    # 创建轨迹收集器
    trajectory_collector = TrajectoryCollector(
        save_dir=args.save_dir,
        format=args.format,
        auto_save=args.auto_save,
        save_interval=args.save_interval,
        compress=args.compress
    )
    
    # 模拟多局游戏
    for i in range(args.num_games):
        game_id = f"game_{int(time.time())}_{i}"
        logger.info(f"开始游戏 {i+1}/{args.num_games} (ID: {game_id})")
        
        # 模拟游戏
        trajectory_path = simulate_game(trajectory_collector, game_id)
        
        logger.info(f"游戏 {i+1}/{args.num_games} 结束，轨迹已保存到: {trajectory_path}")
        
        # 分析轨迹
        analysis = trajectory_collector.analyze()
        logger.info(f"轨迹分析结果: {analysis}")
        
        # 清空轨迹收集器，准备下一局游戏
        trajectory_collector.clear()
    
    logger.info(f"已完成 {args.num_games} 局游戏的轨迹收集")


if __name__ == "__main__":
    main()
