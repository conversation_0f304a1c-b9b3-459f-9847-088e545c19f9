"""
测试DeploymentManager中脚本路径验证和process_id问题的修复

该测试验证两个CodeRabbit发现的问题：
1. script_path may still be invalid – fail fast before spawning subprocess
2. Process ID is never bubbled up – monitoring receives None
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import tempfile
import shutil
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path


class MockUtils:
    """模拟工具类"""
    def __init__(self, is_windows=False):
        self._is_windows = is_windows
    
    def is_windows(self):
        return self._is_windows
    
    def execute_command(self, cmd):
        # 模拟命令执行成功
        return {'success': True, 'stdout': 'mock output', 'stderr': ''}


class MockLogger:
    """模拟日志记录器"""
    def __init__(self):
        self.messages = []
    
    def info(self, msg):
        self.messages.append(('INFO', msg))
    
    def warning(self, msg):
        self.messages.append(('WARNING', msg))
    
    def error(self, msg):
        self.messages.append(('ERROR', msg))


def test_script_path_validation():
    """测试脚本路径验证修复"""
    print("=== 测试脚本路径验证修复 ===\n")
    
    # 导入DeploymentManager
    from cardgame_ai.zhuchengxu.auto_config.deployment_manager import DeploymentManager
    
    # 创建临时目录和文件
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建模拟的配置文件
        config_path = os.path.join(temp_dir, "test_config.yaml")
        with open(config_path, 'w', encoding='utf-8') as f:
            f.write("test: config")
        
        # 测试1: 不存在的脚本文件（应该失败）
        print("测试1: 不存在的脚本文件（应该fail fast）")
        try:
            manager = DeploymentManager()
            manager.utils = MockUtils(is_windows=True)
            manager.logger = MockLogger()
            
            # 调用_launch_training方法，使用不存在的脚本
            result = manager._launch_training(config_path, "nonexistent_script.py")
            
            # 验证结果
            assert result['success'] == False, f"应该失败，但结果: {result}"
            assert len(result['errors']) > 0, "应该包含错误信息"
            
            # 检查错误消息
            error_found = False
            for error in result['errors']:
                if "训练脚本未找到" in error and "nonexistent_script.py" in error:
                    error_found = True
                    break
            
            assert error_found, f"应该包含脚本未找到的错误，实际错误: {result['errors']}"
            print(f"✓ 不存在脚本测试通过，错误: {result['errors'][0]}")
        
        except Exception as e:
            print(f"✗ 不存在脚本测试失败: {e}")
        
        # 测试2: 存在的脚本文件（应该成功找到）
        print("\n测试2: 存在的脚本文件（应该成功找到）")
        try:
            # 创建cardgame_ai/zhuchengxu目录结构
            cardgame_dir = os.path.join(temp_dir, "cardgame_ai", "zhuchengxu")
            os.makedirs(cardgame_dir, exist_ok=True)
            
            # 创建测试脚本
            script_path = os.path.join(cardgame_dir, "test_script.py")
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write("print('test script')")
            
            # 切换到临时目录
            original_cwd = os.getcwd()
            os.chdir(temp_dir)
            
            try:
                manager = DeploymentManager()
                manager.utils = MockUtils(is_windows=True)
                manager.logger = MockLogger()
                
                # 模拟subprocess.Popen
                with patch('subprocess.Popen') as mock_popen:
                    mock_process = Mock()
                    mock_process.pid = 12345
                    mock_popen.return_value = mock_process
                    
                    # 模拟open函数
                    with patch('builtins.open', create=True) as mock_open:
                        mock_file = Mock()
                        mock_open.return_value.__enter__.return_value = mock_file
                        
                        # 调用_launch_training方法
                        result = manager._launch_training(config_path, "test_script.py")
                        
                        # 验证结果
                        assert result['success'] == True, f"应该成功，但结果: {result}"
                        assert 'process_id' in result, "结果中应该包含process_id字段"
                        assert result['process_id'] == 12345, f"process_id应该是12345，实际: {result['process_id']}"
                        
                        print(f"✓ 存在脚本测试通过，process_id: {result['process_id']}")
            
            finally:
                os.chdir(original_cwd)
        
        except Exception as e:
            print(f"✗ 存在脚本测试失败: {e}")
            import traceback
            traceback.print_exc()


def test_process_id_propagation():
    """测试process_id传播修复"""
    print("\n=== 测试process_id传播修复 ===\n")
    
    from cardgame_ai.zhuchengxu.auto_config.deployment_manager import DeploymentManager
    
    # 创建临时目录和文件
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建模拟的配置文件
        config_path = os.path.join(temp_dir, "test_config.yaml")
        with open(config_path, 'w', encoding='utf-8') as f:
            f.write("test: config")
        
        # 创建模拟的训练脚本
        script_path = os.path.join(temp_dir, "test_script.py")
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write("print('test script')")
        
        # 测试1: 验证process_id在deploy_local中正确传播
        print("测试1: 验证process_id在deploy_local中正确传播")
        try:
            manager = DeploymentManager()
            manager.utils = MockUtils(is_windows=True)
            manager.logger = MockLogger()
            
            # 模拟所有检查方法返回成功
            manager._check_environment = Mock(return_value={'success': True, 'errors': []})
            manager._validate_config_file = Mock(return_value={'success': True, 'errors': []})
            manager._check_dependencies = Mock(return_value={'success': True, 'errors': []})
            
            # 模拟_launch_training返回成功结果（包含process_id）
            mock_launch_result = {
                'success': True,
                'errors': [],
                'process_id': 54321,
                'log_file': '/tmp/test.log',
                'command': 'python test_script.py --config test_config.yaml'
            }
            manager._launch_training = Mock(return_value=mock_launch_result)
            
            # 模拟_start_monitoring方法
            manager._start_monitoring = Mock()
            
            # 调用deploy_local方法
            result = manager.deploy_local(
                config_path=config_path,
                training_script="test_script.py",
                dry_run=False,
                monitor=True
            )
            
            # 验证结果
            assert result['success'] == True, f"部署应该成功，但结果: {result}"
            assert 'process_id' in result, "结果中应该包含process_id字段"
            assert result['process_id'] == 54321, f"process_id应该是54321，实际: {result['process_id']}"
            
            # 验证_start_monitoring被正确调用
            manager._start_monitoring.assert_called_once_with(54321)
            
            print(f"✓ process_id传播测试通过，process_id: {result['process_id']}")
        
        except Exception as e:
            print(f"✗ process_id传播测试失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 测试2: 验证没有process_id时监控不会被调用
        print("\n测试2: 验证失败情况下监控不会被调用")
        try:
            manager = DeploymentManager()
            manager.utils = MockUtils(is_windows=True)
            manager.logger = MockLogger()
            
            # 模拟所有检查方法返回成功
            manager._check_environment = Mock(return_value={'success': True, 'errors': []})
            manager._validate_config_file = Mock(return_value={'success': True, 'errors': []})
            manager._check_dependencies = Mock(return_value={'success': True, 'errors': []})
            
            # 模拟_launch_training返回失败结果
            mock_launch_result = {
                'success': False,
                'errors': ['启动失败'],
                'process_id': None
            }
            manager._launch_training = Mock(return_value=mock_launch_result)
            
            # 模拟_start_monitoring方法
            manager._start_monitoring = Mock()
            
            # 调用deploy_local方法
            result = manager.deploy_local(
                config_path=config_path,
                training_script="test_script.py",
                dry_run=False,
                monitor=True
            )
            
            # 验证结果
            assert result['success'] == False, f"部署应该失败，但结果: {result}"
            
            # 验证_start_monitoring没有被调用
            manager._start_monitoring.assert_not_called()
            
            print("✓ 失败情况下监控不被调用测试通过")
        
        except Exception as e:
            print(f"✗ 失败情况测试失败: {e}")


if __name__ == "__main__":
    print("=== 测试DeploymentManager脚本验证和process_id修复 ===\n")
    
    try:
        test_script_path_validation()
        test_process_id_propagation()
        
        print("\n=== 所有测试完成 ===")
        print("\n修复总结:")
        print("- 添加了脚本路径存在性检查，fail fast原则")
        print("- 修复了process_id未传播的问题")
        print("- 确保监控功能能够正确接收process_id")
        print("- 提供清晰的错误消息指出脚本未找到")
        print("- 防止启动不存在的脚本导致的静默失败")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
