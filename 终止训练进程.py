#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import io
# 设置标准输出编码为UTF-8
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

"""
训练进程自动终止工具

用于自动查找和终止正在运行的训练进程。
默认运行即直接终止所有训练相关进程，无需确认。

使用方法:
    python 终止训练进程.py                    # 🚀 默认：直接终止所有训练进程
    python 终止训练进程.py --list            # 📋 仅列出进程，不终止
    python 终止训练进程.py --no-kill         # 📊 仅显示信息，不终止
    python 终止训练进程.py --interactive     # 🔧 交互式选择模式
    python 终止训练进程.py --emergency       # 🚨 紧急清理所有相关进程
    python 终止训练进程.py --pid 12345       # 🎯 终止指定PID的进程
    python 终止训练进程.py --force           # ⚡ 强制终止模式
"""

import sys
import psutil
import argparse
from typing import List, Dict, Any
import os
import glob


def check_locked_files() -> List[str]:
    """检查被占用的训练相关文件"""
    locked_files = []

    # 检查常见的训练日志和数据文件
    file_patterns = [
        'logs/*.log',
        'logs/mcts_debug.log',
        'logs/training.log',
        'logs/efficient_zero.log',
        'models/*.pth',
        'models/*.pt',
        'checkpoints/*.pth',
        'data/*.pkl',
        'data/*.json',
        '*.lock',
        'training_*.log'
    ]

    for pattern in file_patterns:
        try:
            for file_path in glob.glob(pattern):
                if os.path.exists(file_path):
                    try:
                        # 尝试以写入模式打开文件来检查是否被占用
                        with open(file_path, 'a'):
                            pass
                    except (PermissionError, IOError):
                        locked_files.append(file_path)
        except Exception:
            continue

    return locked_files


def find_processes_using_files(file_paths: List[str]) -> List[Dict[str, Any]]:
    """查找正在使用指定文件的进程"""
    file_processes = []

    for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'open_files']):
        try:
            proc_info = proc.info
            open_files = proc_info.get('open_files', [])

            if not open_files:
                continue

            # 检查进程是否打开了被锁定的文件
            for open_file in open_files:
                file_path = open_file.path.lower()
                for locked_file in file_paths:
                    if locked_file.lower() in file_path or os.path.basename(locked_file.lower()) in file_path:
                        file_processes.append({
                            'pid': proc_info['pid'],
                            'name': proc_info['name'],
                            'cmdline': proc_info.get('cmdline', []),
                            'locked_file': locked_file,
                            'open_file_path': open_file.path
                        })
                        break

        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            continue

    return file_processes


def find_training_processes() -> List[Dict[str, Any]]:
    """查找所有训练相关的进程"""
    training_processes = []

    # 训练相关的关键词 - 扩展版本
    training_keywords = [
        # 主要训练脚本
        'main_training.py',
        'efficient_zero',
        'muzero',
        'doudizhu',
        'training',
        'cardgame_ai',
        'auto_deploy.py',
        'quick_start.py',

        # 算法相关
        'mcts',
        'monte_carlo',
        'tree_search',
        'neural_network',
        'reinforcement_learning',

        # 测试文件
        'test_mcts',
        'test_training',
        'test_efficient_zero',
        'test_muzero',
        'test_doudizhu',
        'test_cardgame',
        'emergency_fix',

        # 框架和库
        'torch',
        'pytorch',
        'tensorflow',
        'cuda',
        'tensorboard',
        'wandb',

        # Python进程（需要进一步过滤）
        'python.exe',  # Windows Python进程
        'python3',     # Linux Python进程
        'python',      # 通用Python进程

        # 其他可能的关键词
        'agent',
        'model',
        'network',
        'replay_buffer',
        'experience'
    ]
    
    try:
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time', 'cpu_percent', 'memory_info']):
            try:
                proc_info = proc.info
                cmdline = proc_info.get('cmdline', [])
                
                if not cmdline:
                    continue
                
                # 检查命令行是否包含训练相关关键词
                cmdline_str = ' '.join(cmdline).lower()

                # 优先检查高优先级关键词（更精确的匹配）
                high_priority_keywords = [
                    'main_training.py', 'efficient_zero', 'muzero', 'doudizhu',
                    'cardgame_ai', 'auto_deploy.py', 'quick_start.py',
                    'test_mcts', 'test_training', 'test_efficient_zero', 'test_muzero',
                    'test_doudizhu', 'test_cardgame', 'emergency_fix'
                ]

                # 检查是否匹配高优先级关键词
                matched_keyword = None
                for keyword in high_priority_keywords:
                    if keyword in cmdline_str:
                        matched_keyword = keyword
                        break

                # 如果没有匹配高优先级关键词，检查其他关键词但需要更严格的条件
                if not matched_keyword:
                    for keyword in training_keywords:
                        if keyword in cmdline_str:
                            # 对于通用关键词，需要额外验证
                            if keyword in ['python.exe', 'python3', 'python']:
                                # Python进程需要包含训练相关的路径或文件名
                                if any(train_word in cmdline_str for train_word in
                                      ['training', 'mcts', 'efficient_zero', 'muzero', 'doudizhu', 'cardgame']):
                                    matched_keyword = keyword
                                    break
                            elif keyword in ['torch', 'pytorch', 'cuda']:
                                # 深度学习框架需要在训练相关目录中
                                if any(train_word in cmdline_str for train_word in
                                      ['training', 'model', 'neural', 'learn']):
                                    matched_keyword = keyword
                                    break
                            elif keyword in ['agent', 'model', 'network', 'experience']:
                                # 通用词汇需要在AI/ML相关上下文中，但排除IDE和浏览器
                                if (any(train_word in cmdline_str for train_word in
                                       ['ai', 'ml', 'training', 'neural', 'reinforcement', 'mcts']) and
                                    not any(exclude_word in cmdline_str for exclude_word in
                                           ['vscode', 'code.exe', 'cursor', 'chrome', 'edge', 'qq', 'wechat', 'browser'])):
                                    matched_keyword = keyword
                                    break
                            else:
                                matched_keyword = keyword
                                break

                if matched_keyword:
                    # 获取更多进程信息
                    try:
                        cpu_percent = proc.cpu_percent()
                        memory_mb = proc_info['memory_info'].rss / 1024 / 1024

                        training_processes.append({
                            'pid': proc_info['pid'],
                            'name': proc_info['name'],
                            'cmdline': cmdline,
                            'cmdline_str': ' '.join(cmdline),
                            'create_time': proc_info['create_time'],
                            'cpu_percent': cpu_percent,
                            'memory_mb': memory_mb,
                            'keyword_matched': matched_keyword
                        })
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
                            
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue
                
    except Exception as e:
        print(f"搜索进程时出错: {e}")

    return training_processes


def display_processes(processes: List[Dict[str, Any]]):
    """显示进程信息"""
    if not processes:
        print("未找到正在运行的训练进程")
        return

    print(f"找到 {len(processes)} 个训练相关进程:")
    print("=" * 80)
    
    for i, proc in enumerate(processes, 1):
        from datetime import datetime
        create_time = datetime.fromtimestamp(proc['create_time'])
        
        print(f"[{i}] PID: {proc['pid']}")
        print(f"    名称: {proc['name']}")
        print(f"    命令: {proc['cmdline_str'][:100]}{'...' if len(proc['cmdline_str']) > 100 else ''}")
        print(f"    启动时间: {create_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"    CPU使用率: {proc['cpu_percent']:.1f}%")
        print(f"    内存使用: {proc['memory_mb']:.1f} MB")
        print(f"    匹配关键词: {proc['keyword_matched']}")
        print()


def terminate_process(pid: int, force: bool = False) -> bool:
    """终止指定进程及其子进程"""
    try:
        proc = psutil.Process(pid)
        proc_name = proc.name()

        print(f"正在终止进程 PID: {pid} ({proc_name})")

        # 查找并终止所有子进程
        children = []
        try:
            children = proc.children(recursive=True)
            if children:
                print(f"发现 {len(children)} 个子进程")
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass

        if force:
            # 强制终止所有子进程
            for child in children:
                try:
                    print(f"强制终止子进程 {child.pid}")
                    child.kill()
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass

            # 强制终止主进程
            proc.kill()
            print(f"已强制终止主进程 {pid}")
        else:
            # 优雅终止子进程
            for child in children:
                try:
                    print(f"优雅终止子进程 {child.pid}")
                    child.terminate()
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass

            # 优雅终止主进程
            proc.terminate()

            # 等待进程结束
            try:
                proc.wait(timeout=5)
                print(f"进程 {pid} 已优雅终止")
            except psutil.TimeoutExpired:
                print(f"进程 {pid} 未响应，强制终止...")

                # 强制终止所有子进程
                for child in children:
                    try:
                        child.kill()
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass

                # 强制终止主进程
                proc.kill()
                proc.wait()
                print(f"进程 {pid} 已强制终止")

        # 释放文件句柄
        import time
        time.sleep(1)  # 等待系统清理资源

        return True

    except psutil.NoSuchProcess:
        print(f"进程 {pid} 不存在")
        return False
    except psutil.AccessDenied:
        print(f"没有权限终止进程 {pid}")
        return False
    except Exception as e:
        print(f"终止进程 {pid} 时出错: {e}")
        return False


def terminate_all_training_processes(force: bool = False) -> int:
    """终止所有训练进程"""
    processes = find_training_processes()
    
    if not processes:
        print("没有找到需要终止的训练进程")
        return 0

    print(f"准备终止 {len(processes)} 个训练进程...")

    terminated_count = 0
    for proc in processes:
        if terminate_process(proc['pid'], force):
            terminated_count += 1

    print(f"已终止 {terminated_count}/{len(processes)} 个进程")
    return terminated_count


def interactive_terminate():
    """交互式终止进程"""
    processes = find_training_processes()
    
    if not processes:
        print("✅ 没有找到正在运行的训练进程")
        return
    
    display_processes(processes)
    
    while True:
        try:
            choice = input("\n请选择操作:\n"
                          "  输入进程编号 (1-{}) 终止单个进程\n"
                          "  输入 'all' 终止所有进程\n"
                          "  输入 'q' 退出\n"
                          "选择: ".format(len(processes)))
            
            if choice.lower() == 'q':
                print("👋 退出")
                break
            elif choice.lower() == 'all':
                confirm = input("⚠️ 确认终止所有训练进程? (y/N): ")
                if confirm.lower() == 'y':
                    terminate_all_training_processes()
                break
            else:
                try:
                    index = int(choice) - 1
                    if 0 <= index < len(processes):
                        proc = processes[index]
                        confirm = input(f"⚠️ 确认终止进程 {proc['pid']} ({proc['name']})? (y/N): ")
                        if confirm.lower() == 'y':
                            terminate_process(proc['pid'])
                    else:
                        print("❌ 无效的进程编号")
                except ValueError:
                    print("❌ 请输入有效的数字")
                    
        except KeyboardInterrupt:
            print("\n👋 退出")
            break


def emergency_cleanup():
    """紧急清理：强制终止所有可能的训练进程"""
    print("🚨 执行紧急清理...")

    # 训练相关的关键词匹配
    training_patterns = ['cardgame_ai', 'doudizhu', 'training', 'mcts', 'efficient_zero', 'muzero']

    killed_count = 0
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            proc_info = proc.info
            cmdline = proc_info.get('cmdline', [])

            if not cmdline:
                continue

            cmdline_str = ' '.join(cmdline).lower()

            # 检查是否包含训练相关内容
            if any(pattern in cmdline_str for pattern in training_patterns):
                print(f"🔥 紧急终止进程: {proc_info['pid']} - {cmdline_str[:100]}...")
                try:
                    proc.kill()
                    killed_count += 1
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass

        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            continue

    print(f"✅ 紧急清理完成，终止了 {killed_count} 个进程")

    # 等待系统清理
    import time
    time.sleep(2)
    print("🧹 系统资源清理完成")


def main():
    """主函数 - 默认直接终止所有训练进程"""
    parser = argparse.ArgumentParser(description="训练进程终止工具 - 默认直接终止所有训练进程")
    parser.add_argument('--list', action='store_true', help='仅列出所有训练进程，不终止')
    parser.add_argument('--pid', type=int, help='终止指定PID的进程')
    parser.add_argument('--force', action='store_true', help='强制终止进程')
    parser.add_argument('--interactive', action='store_true', help='交互式模式')
    parser.add_argument('--emergency', action='store_true', help='🚨 紧急清理所有相关进程')
    parser.add_argument('--no-kill', action='store_true', help='不执行终止操作，仅显示进程信息')

    args = parser.parse_args()

    print("🚨 训练进程自动终止工具")
    print("=" * 40)

    if args.emergency:
        # 紧急清理模式 - 直接执行，无需确认
        print("🚨 执行紧急清理模式...")
        emergency_cleanup()

    elif args.list:
        # 仅列出进程，不终止
        print("📋 列出所有训练进程:")
        processes = find_training_processes()
        display_processes(processes)

    elif args.pid:
        # 终止指定进程
        print(f"🎯 终止指定进程 PID: {args.pid}")
        terminate_process(args.pid, args.force)

    elif args.interactive:
        # 交互式模式
        print("🔧 进入交互式模式:")
        interactive_terminate()

    elif args.no_kill:
        # 仅显示进程信息，不终止
        print("📊 显示进程信息 (不执行终止操作):")
        processes = find_training_processes()
        display_processes(processes)

    else:
        # 默认行为：直接终止所有训练进程
        print("🚀 默认模式：自动终止所有训练进程")
        print("⚡ 正在搜索并终止训练进程...")

        # 1. 检查被占用的文件
        print("🔍 检查被占用的训练文件...")
        locked_files = check_locked_files()
        if locked_files:
            print(f"⚠️ 发现 {len(locked_files)} 个被占用的文件:")
            for file_path in locked_files:
                print(f"   📄 {file_path}")

            # 查找占用文件的进程
            print("🔍 查找占用文件的进程...")
            file_processes = find_processes_using_files(locked_files)
            if file_processes:
                print(f"🎯 发现 {len(file_processes)} 个占用文件的进程:")
                for proc in file_processes:
                    cmdline_str = ' '.join(proc['cmdline']) if proc['cmdline'] else proc['name']
                    print(f"   🔒 PID {proc['pid']}: {cmdline_str[:80]}...")
                    print(f"      占用文件: {proc['locked_file']}")

        # 2. 直接执行终止操作，无需确认
        terminated_count = terminate_all_training_processes(args.force)

        # 3. 再次检查文件占用情况
        if locked_files:
            print("🔄 重新检查文件占用情况...")
            import time
            time.sleep(1)
            remaining_locked = check_locked_files()
            if remaining_locked:
                print(f"⚠️ 仍有 {len(remaining_locked)} 个文件被占用:")
                for file_path in remaining_locked:
                    print(f"   📄 {file_path}")
                print("💡 可能需要使用 --emergency 模式进行强制清理")
            else:
                print("✅ 所有文件占用已解除")

        if terminated_count > 0:
            print(f"✅ 成功终止 {terminated_count} 个训练进程")
            print("🧹 正在清理系统资源...")
            import time
            time.sleep(2)
            print("✨ 清理完成！")
        else:
            print("✅ 没有找到需要终止的训练进程")

        print("\n💡 其他可用选项:")
        print("   python 终止训练进程.py --list         # 仅列出进程，不终止")
        print("   python 终止训练进程.py --interactive  # 交互式选择")
        print("   python 终止训练进程.py --emergency    # 紧急清理模式")
        print("   python 终止训练进程.py --no-kill      # 仅显示信息，不终止")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()
