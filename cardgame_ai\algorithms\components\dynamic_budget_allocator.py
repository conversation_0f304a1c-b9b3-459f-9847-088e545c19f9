"""
动态计算预算分配模块

该模块实现了基于关键时刻检测的动态计算预算分配机制，
使得MCTS和其他规划算法能够在关键决策点投入更多计算资源。
"""
import logging
from typing import Dict, Any, Optional, Union, List

from cardgame_ai.core.base import State, Action
from cardgame_ai.algorithms.components.key_moment_detector import KeyMomentDetector
from cardgame_ai.algorithms.components.base_component import DecisionComponent

# 配置日志
logger = logging.getLogger(__name__)


class DynamicBudgetAllocator(DecisionComponent):
    """
    动态计算预算分配器

    该组件负责根据关键时刻检测器的结果动态调整MCTS/规划器的计算预算，
    使得系统能够在关键决策点投入更多的计算资源，在非关键点节省资源。
    """

    def __init__(
        self,
        name: str = "dynamic_budget_allocator",
        key_moment_detector: Optional[KeyMomentDetector] = None,
        default_budget: int = 50,
        high_budget: int = 100,
        low_budget: int = 30,
        key_moment_factor: float = 2.0,
        non_key_moment_factor: float = 0.6,
        adaptive_mode: bool = False,
        min_budget: int = 10,
        max_budget: int = 200,
        use_budget_pool: bool = False,
        budget_pool_size: int = 1000,
        budget_pool_refresh_interval: int = 100,
        game_stage_weights: Optional[Dict[str, float]] = None,
        player_role_weights: Optional[Dict[str, float]] = None,
    ):
        """
        初始化动态计算预算分配器

        Args:
            name: 组件名称
            key_moment_detector: 关键时刻检测器实例
            default_budget: 默认模拟次数
            high_budget: 关键时刻的高模拟次数
            low_budget: 非关键时刻的低模拟次数
            key_moment_factor: 关键时刻预算乘数
            non_key_moment_factor: 非关键时刻预算乘数
            adaptive_mode: 是否使用自适应预算模式
            min_budget: 最小预算限制
            max_budget: 最大预算限制
            use_budget_pool: 是否使用预算池（将节省的计算资源存入池中供关键时刻使用）
            budget_pool_size: 预算池大小
            budget_pool_refresh_interval: 预算池刷新间隔
            game_stage_weights: 游戏阶段权重
            player_role_weights: 玩家角色权重
        """
        super().__init__(name)
        self.key_moment_detector = key_moment_detector
        self.default_budget = default_budget
        self.high_budget = high_budget
        self.low_budget = low_budget
        self.key_moment_factor = key_moment_factor
        self.non_key_moment_factor = non_key_moment_factor
        self.adaptive_mode = adaptive_mode
        self.min_budget = min_budget
        self.max_budget = max_budget
        self.use_budget_pool = use_budget_pool
        self.budget_pool_size = budget_pool_size
        self.budget_pool_refresh_interval = budget_pool_refresh_interval

        # 游戏阶段和玩家角色权重
        self.game_stage_weights = game_stage_weights or {
            "early": 0.8,  # 早期阶段
            "mid": 1.0,    # 中期阶段
            "late": 1.2    # 晚期阶段
        }
        self.player_role_weights = player_role_weights or {
            "landlord": 1.1,   # 地主
            "farmer": 0.9      # 农民
        }

        # 预算池初始化
        self.budget_pool = 0
        self.decision_counter = 0

        # 统计信息
        self.stats.update({
            "key_moments_detected": 0,
            "total_decisions": 0,
            "total_budget_allocated": 0,
            "avg_budget_per_decision": 0,
            "budget_pool_usage": 0,
            "high_budget_allocations": 0,
            "low_budget_allocations": 0,
            "default_budget_allocations": 0
        })

    def set_key_moment_detector(self, detector: KeyMomentDetector):
        """
        设置关键时刻检测器

        Args:
            detector: 关键时刻检测器实例
        """
        self.key_moment_detector = detector
        logger.info(f"设置关键时刻检测器: {detector.name}")

    def allocate_budget(
        self,
        state: State,
        context: Optional[Dict[str, Any]] = None,
        explain: bool = False
    ) -> Union[Dict[str, Any], tuple]:
        """
        分配计算预算

        根据当前状态和上下文，决定应当分配的计算资源。

        Args:
            state: 当前游戏状态
            context: 额外上下文信息，如游戏阶段、玩家角色等
            explain: 是否返回解释信息

        Returns:
            如果explain=False，返回包含预算信息的字典
            如果explain=True，返回(预算字典, 解释数据)元组
        """
        # 初始化默认预算
        budget = self.default_budget
        budget_factor = 1.0
        explanation_data = {}
        is_key_moment = False

        # 更新统计信息
        self.stats["total_decisions"] += 1
        self.decision_counter += 1

        # 检测关键时刻
        if self.key_moment_detector is not None:
            if explain:
                is_key_moment, key_moment_explanation = self.key_moment_detector.decide(state, [], explain=True)
                explanation_data["key_moment_detection"] = key_moment_explanation
            else:
                is_key_moment = self.key_moment_detector.decide(state, [])

            if is_key_moment:
                budget = self.high_budget
                budget_factor = self.key_moment_factor
                self.stats["key_moments_detected"] += 1
                self.stats["high_budget_allocations"] += 1
            else:
                budget = self.low_budget
                budget_factor = self.non_key_moment_factor
                self.stats["low_budget_allocations"] += 1
        else:
            self.stats["default_budget_allocations"] += 1

        # 应用上下文调整
        if context:
            # 游戏阶段调整
            if "game_stage" in context and context["game_stage"] in self.game_stage_weights:
                stage_factor = self.game_stage_weights[context["game_stage"]]
                budget_factor *= stage_factor
                if explain:
                    explanation_data["game_stage_factor"] = stage_factor

            # 玩家角色调整
            if "player_role" in context and context["player_role"] in self.player_role_weights:
                role_factor = self.player_role_weights[context["player_role"]]
                budget_factor *= role_factor
                if explain:
                    explanation_data["player_role_factor"] = role_factor

            # 手牌数量调整 (越少手牌越重要)
            if "cards_left" in context and isinstance(context["cards_left"], int):
                cards_left = context["cards_left"]
                if cards_left <= 5:
                    cards_factor = 1.0 + (5 - cards_left) * 0.1
                    budget_factor *= cards_factor
                    if explain:
                        explanation_data["cards_left_factor"] = cards_factor

        # 应用预算池调整
        if self.use_budget_pool:
            # 如果是关键时刻且预算池有资源，增加预算
            if is_key_moment and self.budget_pool > 0:
                pool_usage = min(self.budget_pool, self.high_budget - budget)
                budget += pool_usage
                self.budget_pool -= pool_usage
                self.stats["budget_pool_usage"] += pool_usage
                if explain:
                    explanation_data["budget_pool_usage"] = pool_usage
            # 如果不是关键时刻，将节省的资源存入预算池
            elif not is_key_moment:
                saved_budget = int(self.default_budget - budget)
                if saved_budget > 0:
                    pool_addition = min(saved_budget, self.budget_pool_size - self.budget_pool)
                    self.budget_pool += pool_addition
                    if explain:
                        explanation_data["budget_pool_addition"] = pool_addition

            # 定期刷新预算池，避免过度累积
            if self.decision_counter >= self.budget_pool_refresh_interval:
                self.budget_pool = max(0, self.budget_pool - int(self.budget_pool * 0.2))
                self.decision_counter = 0
                if explain:
                    explanation_data["budget_pool_refresh"] = True

        # 计算最终预算
        final_budget = int(self.default_budget * budget_factor)

        # 确保预算在限制范围内
        final_budget = max(self.min_budget, min(final_budget, self.max_budget))

        # 更新统计信息
        self.stats["total_budget_allocated"] += final_budget
        self.stats["avg_budget_per_decision"] = self.stats["total_budget_allocated"] / self.stats["total_decisions"]

        # 构建返回结果
        budget_info = {
            "num_simulations": final_budget,
            "budget_factor": budget_factor,
            "is_key_moment": is_key_moment
        }

        if explain:
            explanation_data.update({
                "initial_budget": budget,
                "final_budget": final_budget,
                "budget_factor": budget_factor,
                "is_key_moment": is_key_moment,
                "statistics": {
                    "total_decisions": self.stats["total_decisions"],
                    "key_moments_ratio": self.stats["key_moments_detected"] / self.stats["total_decisions"],
                    "avg_budget": self.stats["avg_budget_per_decision"],
                    "budget_pool": self.budget_pool
                }
            })
            return budget_info, explanation_data
        else:
            return budget_info

    def get_stats(self) -> Dict[str, Any]:
        """
        获取组件统计信息

        Returns:
            包含统计信息的字典
        """
        stats = super().get_stats()

        # 添加特定统计
        if self.stats["total_decisions"] > 0:
            stats["key_moment_ratio"] = self.stats["key_moments_detected"] / self.stats["total_decisions"]
            stats["high_budget_ratio"] = self.stats["high_budget_allocations"] / self.stats["total_decisions"]
            stats["low_budget_ratio"] = self.stats["low_budget_allocations"] / self.stats["total_decisions"]
            stats["default_budget_ratio"] = self.stats["default_budget_allocations"] / self.stats["total_decisions"]

        if self.use_budget_pool:
            stats["current_budget_pool"] = self.budget_pool

        return stats

    def decide(self, state: State, legal_actions: List[Action], explain: bool = False) -> Union[Dict[str, Any], tuple]:
        """
        实现抽象基类要求的decide方法

        将allocate_budget的逻辑适配到decide接口，用于预算分配决策。

        Args:
            state: 当前状态
            legal_actions: 合法动作列表
            explain: 是否启用解释模式

        Returns:
            如果explain=False，返回预算配置字典
            如果explain=True，返回(预算配置, 解释数据)元组
        """
        # 构建上下文信息
        context = {
            'legal_actions_count': len(legal_actions),
            'game_stage': getattr(state, 'game_phase', None),
            'player_role': getattr(state, 'current_player_role', None),
            'cards_left': getattr(state, 'cards_left', None)
        }

        # 调用allocate_budget方法
        if explain:
            budget_info, explanation = self.allocate_budget(state, context, explain=True)
            return budget_info, explanation
        else:
            budget_info = self.allocate_budget(state, context, explain=False)
            return budget_info