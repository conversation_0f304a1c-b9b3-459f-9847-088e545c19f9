#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
完整对局批次训练示例脚本

展示如何使用完整对局批次训练与闭环更新功能，对模型进行持续学习和改进。
"""

import os
import sys
import argparse
import logging
import time
import glob
import random
from typing import Dict, List, Any, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from cardgame_ai.utils.data_loader import load_trajectory_data, load_trajectory_to_batch
from cardgame_ai.utils.trajectory_collector import TrajectoryCollector
from cardgame_ai.algorithms.integrated_ai_system import IntegratedAISystem
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='完整对局批次训练示例')
    
    parser.add_argument('--model_path', type=str, default=None,
                        help='模型路径')
    parser.add_argument('--trajectory_dir', type=str, default='data/trajectories',
                        help='轨迹目录路径')
    parser.add_argument('--batch_size', type=int, default=32,
                        help='批次大小')
    parser.add_argument('--num_epochs', type=int, default=10,
                        help='训练轮数')
    parser.add_argument('--learning_rate', type=float, default=0.001,
                        help='学习率')
    parser.add_argument('--save_interval', type=int, default=5,
                        help='保存间隔（轮）')
    parser.add_argument('--use_experience_format', action='store_true',
                        help='是否使用Experience格式')
    parser.add_argument('--file_pattern', type=str, default='*.json',
                        help='轨迹文件模式')
    parser.add_argument('--max_trajectories', type=int, default=100,
                        help='最大轨迹数')
    
    return parser.parse_args()


def load_all_trajectories(trajectory_dir: str, file_pattern: str, max_trajectories: int = None) -> List[Dict[str, Any]]:
    """
    加载所有轨迹数据
    
    Args:
        trajectory_dir: 轨迹目录路径
        file_pattern: 轨迹文件模式
        max_trajectories: 最大轨迹数
        
    Returns:
        List[Dict[str, Any]]: 轨迹数据列表
    """
    # 获取所有轨迹文件
    pattern = os.path.join(trajectory_dir, file_pattern)
    all_files = glob.glob(pattern)
    
    # 如果有最大轨迹数限制，随机选择文件
    if max_trajectories is not None and len(all_files) > max_trajectories:
        all_files = random.sample(all_files, max_trajectories)
    
    # 加载轨迹数据
    trajectories = []
    for filepath in all_files:
        logger.info(f"加载轨迹: {filepath}")
        batch = load_trajectory_data(filepath, convert_to_tensor=False)
        if batch and "observations" in batch and len(batch["observations"]) > 0:
            trajectories.append(batch)
    
    logger.info(f"共加载 {len(trajectories)} 个轨迹")
    return trajectories


def combine_trajectories(trajectories: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    合并轨迹数据
    
    Args:
        trajectories: 轨迹数据列表
        
    Returns:
        Dict[str, Any]: 合并后的轨迹数据
    """
    if not trajectories:
        return {}
    
    # 初始化合并后的轨迹数据
    combined = {
        "observations": [],
        "actions": [],
        "rewards": [],
        "next_observations": [],
        "dones": [],
        "infos": []
    }
    
    # 合并轨迹数据
    for trajectory in trajectories:
        for key in combined.keys():
            if key in trajectory:
                combined[key].extend(trajectory[key])
    
    # 如果有人类反馈，也合并
    human_feedback = []
    for trajectory in trajectories:
        if "human_feedback" in trajectory:
            human_feedback.extend(trajectory["human_feedback"])
    
    if human_feedback:
        combined["human_feedback"] = human_feedback
    
    logger.info(f"合并后的轨迹数据: {len(combined['observations'])} 步")
    return combined


def create_batches(combined: Dict[str, Any], batch_size: int) -> List[Dict[str, Any]]:
    """
    创建训练批次
    
    Args:
        combined: 合并后的轨迹数据
        batch_size: 批次大小
        
    Returns:
        List[Dict[str, Any]]: 训练批次列表
    """
    if not combined or "observations" not in combined or len(combined["observations"]) == 0:
        return []
    
    # 获取数据长度
    data_length = len(combined["observations"])
    
    # 创建索引列表并打乱
    indices = list(range(data_length))
    random.shuffle(indices)
    
    # 创建批次
    batches = []
    for i in range(0, data_length, batch_size):
        # 获取当前批次的索引
        batch_indices = indices[i:min(i + batch_size, data_length)]
        
        # 创建批次
        batch = {}
        for key in combined.keys():
            if key == "human_feedback":
                # 人类反馈需要特殊处理
                continue
            
            batch[key] = [combined[key][j] for j in batch_indices]
        
        # 如果有人类反馈，也添加到批次
        if "human_feedback" in combined:
            # 这里简化处理，假设人类反馈与观察一一对应
            # 实际应用中可能需要更复杂的处理
            if len(combined["human_feedback"]) >= data_length:
                batch["human_feedback"] = [combined["human_feedback"][j] for j in batch_indices]
        
        batches.append(batch)
    
    logger.info(f"创建了 {len(batches)} 个训练批次")
    return batches


def train_model(model, batches: List[Dict[str, Any]], num_epochs: int, learning_rate: float,
               use_experience_format: bool, save_path: Optional[str] = None,
               save_interval: int = 5) -> Dict[str, Any]:
    """
    训练模型
    
    Args:
        model: 模型
        batches: 训练批次列表
        num_epochs: 训练轮数
        learning_rate: 学习率
        use_experience_format: 是否使用Experience格式
        save_path: 保存路径
        save_interval: 保存间隔（轮）
        
    Returns:
        Dict[str, Any]: 训练结果
    """
    # 记录开始时间
    start_time = time.time()
    
    # 训练结果
    results = {
        "epoch_losses": [],
        "total_updates": 0,
        "training_time": 0
    }
    
    logger.info(f"开始训练: {num_epochs} 轮, {len(batches)} 批次")
    
    for epoch in range(num_epochs):
        epoch_start_time = time.time()
        epoch_losses = []
        
        # 打乱批次顺序
        random.shuffle(batches)
        
        # 训练每个批次
        for i, batch in enumerate(batches):
            # 转换为张量
            from cardgame_ai.utils.data_loader import _convert_batch_to_tensor
            tensor_batch = _convert_batch_to_tensor(batch)
            
            # 如果使用Experience格式，则转换为Batch对象
            if use_experience_format:
                from cardgame_ai.utils.data_loader import _convert_batch_to_experiences, Batch
                experiences = _convert_batch_to_experiences(batch)
                tensor_batch = Batch(experiences)
            
            # 提取人类反馈（如果有）
            human_feedback_batch = None
            if "human_feedback" in batch:
                human_feedback_batch = batch["human_feedback"]
            
            # 训练模型
            update_result = model.train(
                tensor_batch,
                human_feedback_batch=human_feedback_batch,
                learning_rate=learning_rate
            )
            
            # 记录损失
            if isinstance(update_result, dict):
                # 如果返回的是字典，提取损失
                if "efficient_zero" in update_result:
                    loss = update_result["efficient_zero"].get("loss", 0.0)
                elif "enhanced_mappo" in update_result:
                    loss = update_result["enhanced_mappo"].get("loss", 0.0)
                else:
                    loss = update_result.get("loss", 0.0)
            else:
                # 如果返回的不是字典，假设是损失
                loss = update_result
            
            epoch_losses.append(loss)
            results["total_updates"] += 1
            
            # 输出进度
            if (i + 1) % 10 == 0 or i == len(batches) - 1:
                logger.info(f"轮 {epoch + 1}/{num_epochs}, 批次 {i + 1}/{len(batches)}, 损失: {loss:.4f}")
        
        # 计算平均损失
        avg_loss = sum(epoch_losses) / len(epoch_losses) if epoch_losses else 0.0
        results["epoch_losses"].append(avg_loss)
        
        # 输出轮结果
        epoch_time = time.time() - epoch_start_time
        logger.info(f"轮 {epoch + 1}/{num_epochs} 完成, 平均损失: {avg_loss:.4f}, 用时: {epoch_time:.2f}秒")
        
        # 保存模型
        if save_path and (epoch + 1) % save_interval == 0:
            epoch_save_path = f"{save_path}.epoch{epoch + 1}"
            model.save(epoch_save_path)
            logger.info(f"已保存模型: {epoch_save_path}")
    
    # 计算总训练时间
    results["training_time"] = time.time() - start_time
    
    # 输出训练结果
    logger.info(f"训练完成: {results['total_updates']} 次更新, 用时: {results['training_time']:.2f}秒")
    logger.info(f"最终损失: {results['epoch_losses'][-1]:.4f}")
    
    # 保存最终模型
    if save_path:
        model.save(save_path)
        logger.info(f"已保存最终模型: {save_path}")
    
    return results


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 创建游戏环境
    env = DouDizhuEnvironment()
    
    # 获取观察和动作空间
    observation_shape = env.observation_space.shape
    action_shape = (env.action_space.n,)
    
    # 创建集成AI系统
    model = IntegratedAISystem(
        state_shape=observation_shape,
        action_shape=action_shape,
        hidden_dim=128,
        state_dim=64,
        use_resnet=False,
        use_belief_state=True,
        use_information_value=True,
        use_intrinsic_motivation=True,
        use_human_feedback=True
    )
    
    # 如果有预训练模型，加载参数
    if args.model_path and os.path.exists(args.model_path):
        model.load(args.model_path)
        logger.info(f"已加载预训练模型: {args.model_path}")
    
    # 确保轨迹目录存在
    os.makedirs(args.trajectory_dir, exist_ok=True)
    
    # 加载所有轨迹数据
    trajectories = load_all_trajectories(
        args.trajectory_dir,
        args.file_pattern,
        args.max_trajectories
    )
    
    if not trajectories:
        logger.warning(f"没有找到轨迹数据: {args.trajectory_dir}")
        return
    
    # 合并轨迹数据
    combined = combine_trajectories(trajectories)
    
    if not combined or "observations" not in combined or len(combined["observations"]) == 0:
        logger.warning("合并后的轨迹数据为空")
        return
    
    # 创建训练批次
    batches = create_batches(combined, args.batch_size)
    
    if not batches:
        logger.warning("没有创建训练批次")
        return
    
    # 设置保存路径
    if args.model_path:
        save_path = args.model_path + ".trained"
    else:
        save_path = "models/integrated_ai_system_trained.pt"
    
    # 确保保存目录存在
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    
    # 训练模型
    results = train_model(
        model=model,
        batches=batches,
        num_epochs=args.num_epochs,
        learning_rate=args.learning_rate,
        use_experience_format=args.use_experience_format,
        save_path=save_path,
        save_interval=args.save_interval
    )
    
    # 输出训练结果
    logger.info("训练结果:")
    logger.info(f"总更新次数: {results['total_updates']}")
    logger.info(f"训练时间: {results['training_time']:.2f}秒")
    logger.info(f"每轮损失: {[f'{loss:.4f}' for loss in results['epoch_losses']]}")
    logger.info(f"已保存模型: {save_path}")


if __name__ == "__main__":
    main()
