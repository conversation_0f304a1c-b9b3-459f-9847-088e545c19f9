#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
自适应计算时间 (ACT) 示例脚本

展示如何使用自适应计算时间 (ACT) 功能。
"""

import os
import sys
import argparse
import logging
import numpy as np
import torch
import time
from typing import Dict, List, Any, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from cardgame_ai.algorithms.hybrid_decision_system import HybridDecisionSystem
from cardgame_ai.algorithms.key_moment_detector import KeyMomentDetector
from cardgame_ai.algorithms.symbolic_reasoning import SymbolicReasoningComponent
from cardgame_ai.algorithms.efficient_zero import EfficientZero
from cardgame_ai.algorithms.rule_based import RuleBasedAgent
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.games.doudizhu.state import DouDizhuState
from cardgame_ai.games.doudizhu.card import Card, CardRank, CardSuit
from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='自适应计算时间 (ACT) 示例')
    
    parser.add_argument('--model_path', type=str, default=None,
                        help='模型路径')
    parser.add_argument('--key_moment_model', type=str, default=None,
                        help='关键决策点检测器模型路径')
    parser.add_argument('--num_games', type=int, default=5,
                        help='游戏数量')
    parser.add_argument('--seed', type=int, default=None,
                        help='随机种子')
    parser.add_argument('--base_simulations', type=int, default=100,
                        help='基础MCTS模拟次数')
    parser.add_argument('--act_min_simulations', type=int, default=20,
                        help='ACT最小模拟次数')
    parser.add_argument('--act_confidence_threshold', type=float, default=0.95,
                        help='ACT置信度阈值')
    parser.add_argument('--act_visit_threshold', type=int, default=30,
                        help='ACT访问次数阈值')
    parser.add_argument('--max_time_ms', type=int, default=None,
                        help='最大搜索时间（毫秒）')
    parser.add_argument('--disable_act', action='store_true',
                        help='禁用ACT')
    
    return parser.parse_args()


def compare_act_performance(hybrid_system_with_act: HybridDecisionSystem, hybrid_system_without_act: HybridDecisionSystem, env: DouDizhuEnvironment, num_games: int = 5):
    """
    比较启用和禁用ACT的性能
    
    Args:
        hybrid_system_with_act: 启用ACT的混合决策系统
        hybrid_system_without_act: 禁用ACT的混合决策系统
        env: 游戏环境
        num_games: 游戏数量
    """
    # 记录总时间和总奖励
    total_time_with_act = 0
    total_time_without_act = 0
    total_reward_with_act = 0
    total_reward_without_act = 0
    
    # 模拟游戏
    for i in range(num_games):
        logger.info(f"开始游戏 {i+1}/{num_games}")
        
        # 重置环境
        state = env.reset()
        
        # 使用启用ACT的系统
        start_time = time.time()
        done = False
        total_reward = 0
        
        while not done:
            # 获取合法动作
            legal_actions = env.get_legal_actions()
            
            # 使用混合决策系统做出决策
            action = hybrid_system_with_act.act(state, legal_actions)
            
            # 执行动作
            state, reward, done, info = env.step(action)
            total_reward += reward
        
        # 记录时间和奖励
        end_time = time.time()
        time_spent = end_time - start_time
        total_time_with_act += time_spent
        total_reward_with_act += total_reward
        
        logger.info(f"启用ACT：游戏用时 {time_spent:.2f}秒，总奖励 {total_reward}")
        
        # 重置环境
        state = env.reset()
        
        # 使用禁用ACT的系统
        start_time = time.time()
        done = False
        total_reward = 0
        
        while not done:
            # 获取合法动作
            legal_actions = env.get_legal_actions()
            
            # 使用混合决策系统做出决策
            action = hybrid_system_without_act.act(state, legal_actions)
            
            # 执行动作
            state, reward, done, info = env.step(action)
            total_reward += reward
        
        # 记录时间和奖励
        end_time = time.time()
        time_spent = end_time - start_time
        total_time_without_act += time_spent
        total_reward_without_act += total_reward
        
        logger.info(f"禁用ACT：游戏用时 {time_spent:.2f}秒，总奖励 {total_reward}")
    
    # 计算平均时间和平均奖励
    avg_time_with_act = total_time_with_act / num_games
    avg_time_without_act = total_time_without_act / num_games
    avg_reward_with_act = total_reward_with_act / num_games
    avg_reward_without_act = total_reward_without_act / num_games
    
    # 计算时间节省比例
    time_saved_ratio = 1.0 - (avg_time_with_act / avg_time_without_act) if avg_time_without_act > 0 else 0
    
    # 打印比较结果
    logger.info("\n性能比较结果:")
    logger.info(f"启用ACT：平均游戏用时 {avg_time_with_act:.2f}秒，平均奖励 {avg_reward_with_act:.2f}")
    logger.info(f"禁用ACT：平均游戏用时 {avg_time_without_act:.2f}秒，平均奖励 {avg_reward_without_act:.2f}")
    logger.info(f"时间节省比例：{time_saved_ratio:.2%}")
    
    # 打印ACT统计信息
    stats_with_act = hybrid_system_with_act.get_stats()
    if "act_info" in stats_with_act:
        act_info = stats_with_act["act_info"]
        logger.info("\nACT统计信息:")
        logger.info(f"提前终止次数：{act_info.get('early_stops', 0)}")
        logger.info(f"提前终止比例：{act_info.get('early_stop_ratio', 0):.2%}")
        logger.info(f"平均节省计算资源比例：{act_info.get('avg_saved_ratio', 0):.2%}")


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 设置随机种子
    if args.seed is not None:
        np.random.seed(args.seed)
        torch.manual_seed(args.seed)
    
    # 创建游戏环境
    env = DouDizhuEnvironment()
    
    # 创建模型
    model = EfficientZero(model_path=args.model_path)
    
    # 创建规则代理
    rule_agent = RuleBasedAgent()
    
    # 创建关键决策点检测器
    key_moment_detector = None
    if args.key_moment_model:
        key_moment_detector = KeyMomentDetector.load(args.key_moment_model)
        logger.info(f"已加载关键决策点检测器: {args.key_moment_model}")
    
    # 创建符号推理组件
    symbolic_component = SymbolicReasoningComponent(
        use_guaranteed_win_solver=True,
        use_card_counting=True
    )
    
    # 创建启用ACT的混合决策系统
    hybrid_system_with_act = HybridDecisionSystem(
        neural_network_model=model,
        search_model=model,
        rule_agent=rule_agent,
        key_moment_detector=key_moment_detector,
        symbolic_component=symbolic_component,
        meta_strategy="adaptive",
        base_mcts_simulations=args.base_simulations,
        use_act=not args.disable_act,
        act_min_simulations=args.act_min_simulations,
        act_confidence_threshold=args.act_confidence_threshold,
        act_visit_threshold=args.act_visit_threshold,
        max_time_ms=args.max_time_ms
    )
    
    # 创建禁用ACT的混合决策系统
    hybrid_system_without_act = HybridDecisionSystem(
        neural_network_model=model,
        search_model=model,
        rule_agent=rule_agent,
        key_moment_detector=key_moment_detector,
        symbolic_component=symbolic_component,
        meta_strategy="adaptive",
        base_mcts_simulations=args.base_simulations,
        use_act=False
    )
    
    # 比较启用和禁用ACT的性能
    compare_act_performance(hybrid_system_with_act, hybrid_system_without_act, env, args.num_games)
    
    return 0


if __name__ == "__main__":
    main()
