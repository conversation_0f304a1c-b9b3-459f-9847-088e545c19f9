"""
学习率调度器模块

提供各种学习率调度策略，用于动态调整训练过程中的学习率。
"""
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any


class LRScheduler(ABC):
    """
    学习率调度器接口
    
    定义学习率调度器的标准接口。
    """
    
    @abstractmethod
    def step(self, metrics: Optional[Dict[str, Any]] = None) -> float:
        """
        更新学习率
        
        Args:
            metrics (Optional[Dict[str, Any]], optional): 训练指标. Defaults to None.
            
        Returns:
            float: 新的学习率
        """
        pass
    
    @property
    @abstractmethod
    def lr(self) -> float:
        """
        获取当前学习率
        
        Returns:
            float: 当前学习率
        """
        pass


class ConstantLR(LRScheduler):
    """
    常数学习率调度器
    
    保持学习率不变。
    """
    
    def __init__(self, initial_lr: float):
        """
        初始化常数学习率调度器
        
        Args:
            initial_lr (float): 初始学习率
        """
        self._lr = initial_lr
    
    def step(self, metrics: Optional[Dict[str, Any]] = None) -> float:
        """
        更新学习率（不变）
        
        Args:
            metrics (Optional[Dict[str, Any]], optional): 训练指标. Defaults to None.
            
        Returns:
            float: 当前学习率
        """
        return self._lr
    
    @property
    def lr(self) -> float:
        """
        获取当前学习率
        
        Returns:
            float: 当前学习率
        """
        return self._lr


class ExponentialLR(LRScheduler):
    """
    指数学习率调度器
    
    学习率按指数衰减。
    """
    
    def __init__(self, initial_lr: float, decay_rate: float):
        """
        初始化指数学习率调度器
        
        Args:
            initial_lr (float): 初始学习率
            decay_rate (float): 衰减率
        """
        self._initial_lr = initial_lr
        self._lr = initial_lr
        self._decay_rate = decay_rate
        self._step_count = 0
    
    def step(self, metrics: Optional[Dict[str, Any]] = None) -> float:
        """
        更新学习率
        
        Args:
            metrics (Optional[Dict[str, Any]], optional): 训练指标. Defaults to None.
            
        Returns:
            float: 新的学习率
        """
        self._step_count += 1
        self._lr = self._initial_lr * (self._decay_rate ** self._step_count)
        return self._lr
    
    @property
    def lr(self) -> float:
        """
        获取当前学习率
        
        Returns:
            float: 当前学习率
        """
        return self._lr


class StepLR(LRScheduler):
    """
    步进学习率调度器
    
    每隔一定步数，学习率乘以一个因子。
    """
    
    def __init__(self, initial_lr: float, step_size: int, gamma: float):
        """
        初始化步进学习率调度器
        
        Args:
            initial_lr (float): 初始学习率
            step_size (int): 步长
            gamma (float): 衰减因子
        """
        self._initial_lr = initial_lr
        self._lr = initial_lr
        self._step_size = step_size
        self._gamma = gamma
        self._step_count = 0
    
    def step(self, metrics: Optional[Dict[str, Any]] = None) -> float:
        """
        更新学习率
        
        Args:
            metrics (Optional[Dict[str, Any]], optional): 训练指标. Defaults to None.
            
        Returns:
            float: 新的学习率
        """
        self._step_count += 1
        if self._step_count % self._step_size == 0:
            self._lr = self._lr * self._gamma
        return self._lr
    
    @property
    def lr(self) -> float:
        """
        获取当前学习率
        
        Returns:
            float: 当前学习率
        """
        return self._lr


class CosineAnnealingLR(LRScheduler):
    """
    余弦退火学习率调度器
    
    学习率按余弦函数从初始值衰减到最小值，然后重新开始。
    """
    
    def __init__(self, initial_lr: float, t_max: int, eta_min: float = 0):
        """
        初始化余弦退火学习率调度器
        
        Args:
            initial_lr (float): 初始学习率
            t_max (int): 半周期长度
            eta_min (float, optional): 最小学习率. Defaults to 0.
        """
        self._initial_lr = initial_lr
        self._lr = initial_lr
        self._t_max = t_max
        self._eta_min = eta_min
        self._step_count = 0
    
    def step(self, metrics: Optional[Dict[str, Any]] = None) -> float:
        """
        更新学习率
        
        Args:
            metrics (Optional[Dict[str, Any]], optional): 训练指标. Defaults to None.
            
        Returns:
            float: 新的学习率
        """
        import math
        
        self._step_count += 1
        t = self._step_count % self._t_max
        cos_factor = 0.5 * (1 + math.cos(math.pi * t / self._t_max))
        self._lr = self._eta_min + (self._initial_lr - self._eta_min) * cos_factor
        return self._lr
    
    @property
    def lr(self) -> float:
        """
        获取当前学习率
        
        Returns:
            float: 当前学习率
        """
        return self._lr


class ReduceLROnPlateau(LRScheduler):
    """
    性能平稳时降低学习率的调度器
    
    当指标停止改善时，降低学习率。
    """
    
    def __init__(
        self,
        initial_lr: float,
        factor: float = 0.1,
        patience: int = 10,
        threshold: float = 1e-4,
        mode: str = 'min',
        min_lr: float = 0
    ):
        """
        初始化ReduceLROnPlateau调度器
        
        Args:
            initial_lr (float): 初始学习率
            factor (float, optional): 学习率降低因子. Defaults to 0.1.
            patience (int, optional): 忍耐次数. Defaults to 10.
            threshold (float, optional): 衡量改进的阈值. Defaults to 1e-4.
            mode (str, optional): 模式，'min'或'max'. Defaults to 'min'.
            min_lr (float, optional): 最小学习率. Defaults to 0.
        """
        self._lr = initial_lr
        self._factor = factor
        self._patience = patience
        self._threshold = threshold
        self._mode = mode
        self._min_lr = min_lr
        
        self._best_value = float('inf') if mode == 'min' else -float('inf')
        self._bad_epochs = 0
    
    def step(self, metrics: Optional[Dict[str, Any]] = None) -> float:
        """
        更新学习率
        
        Args:
            metrics (Optional[Dict[str, Any]], optional): 训练指标. Defaults to None.
            
        Returns:
            float: 新的学习率
        """
        if metrics is None or 'loss' not in metrics:
            return self._lr
        
        current_value = metrics['loss']
        
        # 检查是否有改进
        if (self._mode == 'min' and current_value < self._best_value - self._threshold) or \
           (self._mode == 'max' and current_value > self._best_value + self._threshold):
            # 有改进，重置计数器
            self._best_value = current_value
            self._bad_epochs = 0
        else:
            # 没有改进，增加计数器
            self._bad_epochs += 1
        
        # 检查是否需要降低学习率
        if self._bad_epochs > self._patience:
            # 降低学习率
            old_lr = self._lr
            self._lr = max(self._lr * self._factor, self._min_lr)
            self._bad_epochs = 0
            
            # 如果学习率没有变化，说明已经达到最小值
            if old_lr == self._lr:
                self._bad_epochs = 0
        
        return self._lr
    
    @property
    def lr(self) -> float:
        """
        获取当前学习率
        
        Returns:
            float: 当前学习率
        """
        return self._lr


class StepLRScheduler(StepLR):
    """
    步进学习率调度器
    
    每隔一定步数，学习率乘以一个因子。
    与StepLR功能相同，提供兼容性。
    """
    pass