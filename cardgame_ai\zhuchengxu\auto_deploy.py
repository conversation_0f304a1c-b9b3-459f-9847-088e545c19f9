#!/usr/bin/env python3
"""
斗地主AI自动部署脚本

支持Windows和Ubuntu系统的自动化部署，
包括硬件检测、参数调优、配置生成和训练启动。

使用方法:
    # 本地部署
    python auto_deploy.py
    
    # 指定目标服务器
    python auto_deploy.py --target server1.example.com
    
    # 批量部署
    python auto_deploy.py --targets servers.txt --parallel
    
    # 预览配置
    python auto_deploy.py --dry-run --show-config

作者: Alex (全栈开发工程师)
版本: v1.0
"""

import os
import sys
import argparse
import json
import time
import signal
import atexit
from pathlib import Path
from typing import Dict, List, Any, Optional

# 添加项目路径到sys.path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from auto_config import (
    HardwareDetector,
    ParameterTuner,
    ConfigTemplateEngine,
    DeploymentManager,
    CrossPlatformUtils
)

class AutoDeployManager:
    """自动部署管理器"""
    
    def __init__(self):
        self.logger = CrossPlatformUtils.setup_logging()
        self.hardware_detector = HardwareDetector()
        self.parameter_tuner = ParameterTuner()
        self.template_engine = ConfigTemplateEngine()
        self.deployment_manager = DeploymentManager()
        
        self.logger.info("自动部署管理器初始化完成")
    
    def auto_deploy(self, target: Optional[str] = None,
                   algorithm: str = 'efficient_zero',
                   environment: str = 'doudizhu',
                   dry_run: bool = False,
                   show_config: bool = False,
                   monitor: bool = False,
                   config_name: Optional[str] = None) -> Dict[str, Any]:
        """
        执行自动部署
        
        Args:
            target: 目标服务器地址，None表示本地部署
            algorithm: 算法类型
            environment: 环境类型
            dry_run: 是否为预演模式
            show_config: 是否显示生成的配置
            monitor: 是否启用监控
            config_name: 配置文件名，None则使用默认名称
            
        Returns:
            部署结果字典
        """
        self.logger.info("🚀 开始自动部署流程...")
        
        deployment_result = {
            'success': False,
            'target': target or 'localhost',
            'algorithm': algorithm,
            'environment': environment,
            'start_time': time.time(),
            'steps': {},
            'errors': [],
            'warnings': []
        }
        
        try:
            # 步骤1: 硬件检测
            self.logger.info("📊 步骤1: 硬件检测")
            hardware_info = self.hardware_detector.detect_all_hardware()
            deployment_result['steps']['hardware_detection'] = {
                'success': True,
                'data': hardware_info
            }
            
            # 显示硬件信息摘要
            self._display_hardware_summary(hardware_info)
            
            # 步骤2: 参数调优
            self.logger.info("⚙️ 步骤2: 参数调优")
            tuned_params = self.parameter_tuner.tune_all_parameters(
                hardware_info, algorithm
            )
            deployment_result['steps']['parameter_tuning'] = {
                'success': True,
                'data': tuned_params
            }
            
            # 显示优化建议
            recommendations = self.parameter_tuner.get_optimization_recommendations(hardware_info)
            if recommendations:
                self.logger.info("💡 优化建议:")
                for rec in recommendations:
                    self.logger.info(f"  - {rec}")
            
            # 步骤3: 配置生成
            self.logger.info("📄 步骤3: 配置生成")
            
            # 初始化模板
            self.template_engine.initialize_templates()
            
            # 生成配置
            config = self.template_engine.render_config(
                template_name="auto_config_template_fixed.yaml",
                hardware_info=hardware_info,
                tuned_params=tuned_params,
                algorithm=algorithm,
                environment=environment
            )
            
            # 验证配置
            validation_errors = self.template_engine.validate_config(config)
            if validation_errors:
                deployment_result['errors'].extend(validation_errors)
                deployment_result['steps']['config_generation'] = {
                    'success': False,
                    'errors': validation_errors
                }
                return deployment_result
            
            deployment_result['steps']['config_generation'] = {
                'success': True,
                'data': config
            }
            
            # 保存配置文件
            if config_name:
                config_path = config_name if config_name.endswith('.yaml') else f"{config_name}.yaml"
            else:
                # 使用固定的默认文件名
                config_path = f"auto_config_{algorithm}_{environment}.yaml"

            # 检查文件是否存在，如果存在则备份
            if os.path.exists(config_path) and not dry_run:
                backup_path = f"{config_path}.backup"
                if os.path.exists(backup_path):
                    os.remove(backup_path)
                os.rename(config_path, backup_path)
                self.logger.info(f"已备份现有配置文件到: {backup_path}")

            self.template_engine.save_config(config, config_path)
            deployment_result['config_path'] = config_path
            
            # 显示配置
            if show_config:
                self._display_config_summary(config)
            
            # 步骤4: 部署执行
            if not dry_run:
                self.logger.info("🚀 步骤4: 部署执行")
                
                if target:
                    # 远程部署
                    deploy_result = self.deployment_manager.deploy_remote(
                        target_host=target,
                        config_path=config_path
                    )
                else:
                    # 本地部署
                    deploy_result = self.deployment_manager.deploy_local(
                        config_path=config_path,
                        monitor=monitor
                    )
                
                deployment_result['steps']['deployment'] = deploy_result
                deployment_result['success'] = deploy_result['success']
                
                if deploy_result['success']:
                    self.logger.info("✅ 部署成功完成！")
                else:
                    self.logger.error("❌ 部署失败")
                    deployment_result['errors'].extend(deploy_result.get('errors', []))
            else:
                self.logger.info("🔍 预演模式: 所有步骤验证通过，实际部署将会成功")
                deployment_result['success'] = True
                deployment_result['dry_run'] = True
        
        except Exception as e:
            self.logger.error(f"❌ 自动部署过程中发生错误: {e}")
            deployment_result['errors'].append(str(e))
        
        deployment_result['end_time'] = time.time()
        deployment_result['duration'] = deployment_result['end_time'] - deployment_result['start_time']
        
        return deployment_result
    
    def batch_deploy(self, targets_file: str, parallel: bool = True) -> Dict[str, Any]:
        """
        批量部署
        
        Args:
            targets_file: 目标列表文件路径
            parallel: 是否并行部署
            
        Returns:
            批量部署结果
        """
        self.logger.info(f"📋 开始批量部署: {targets_file}")
        
        try:
            # 读取目标列表
            targets = self._load_targets_file(targets_file)
            
            # 执行批量部署
            batch_result = self.deployment_manager.batch_deploy(
                targets=targets,
                parallel=parallel
            )
            
            return batch_result
            
        except Exception as e:
            self.logger.error(f"❌ 批量部署失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _display_hardware_summary(self, hardware_info: Dict[str, Any]) -> None:
        """显示硬件信息摘要"""
        gpu_specs = hardware_info.get('gpu', [])
        cpu_specs = hardware_info.get('cpu', {})
        memory_specs = hardware_info.get('memory', {})
        
        self.logger.info("🖥️ 硬件配置摘要:")
        
        if gpu_specs:
            self.logger.info(f"  GPU: {len(gpu_specs)}x {gpu_specs[0].get('name', 'Unknown')}")
            total_memory = sum(gpu.get('memory_total_gb', 0) for gpu in gpu_specs)
            self.logger.info(f"  显存: {total_memory}GB 总计")
        else:
            self.logger.info("  GPU: 未检测到")
        
        self.logger.info(f"  CPU: {cpu_specs.get('physical_cores', 'Unknown')}核心")
        self.logger.info(f"  内存: {memory_specs.get('total_gb', 'Unknown')}GB")
    
    def _display_config_summary(self, config: Dict[str, Any]) -> None:
        """显示配置摘要"""
        self.logger.info("📄 生成的配置摘要:")
        
        training = config.get('training', {})
        mcts = config.get('mcts', {})
        device = config.get('device', {})
        
        self.logger.info(f"  批次大小: {training.get('batch_size', 'Unknown')}")
        self.logger.info(f"  学习率: {training.get('learning_rate', 'Unknown')}")
        self.logger.info(f"  工作线程: {training.get('num_workers', 'Unknown')}")
        self.logger.info(f"  MCTS模拟: {mcts.get('num_simulations', 'Unknown')}")
        self.logger.info(f"  设备类型: {device.get('type', 'Unknown')}")
        
        if device.get('type') == 'cuda':
            gpu_ids = device.get('ids', [])
            self.logger.info(f"  GPU设备: {gpu_ids}")
    
    def _load_targets_file(self, targets_file: str) -> List[Dict[str, str]]:
        """加载目标列表文件"""
        targets = []
        
        with open(targets_file, 'r', encoding='utf-8') as f:
            if targets_file.endswith('.json'):
                targets = json.load(f)
            else:
                # 简单文本格式，每行一个主机
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        targets.append({
                            'host': line,
                            'config_path': 'auto_generated_config.yaml'
                        })
        
        return targets


def setup_signal_handlers():
    """设置信号处理器，确保可以通过Ctrl+C终止训练进程"""
    def signal_handler(signum, frame):
        print(f"\n🛑 接收到终止信号 ({signum})，正在清理...")
        print("💡 如果训练进程仍在运行，请使用: python 终止训练进程.py --kill-all")
        print("👋 程序已退出")
        sys.exit(0)

    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
    if hasattr(signal, 'SIGTERM'):
        signal.signal(signal.SIGTERM, signal_handler)  # 终止信号


def main():
    """主函数"""
    # 设置信号处理器
    setup_signal_handlers()

    parser = argparse.ArgumentParser(
        description="斗地主AI自动部署工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python auto_deploy.py                                    # 本地部署
  python auto_deploy.py --target server1.example.com      # 远程部署
  python auto_deploy.py --targets servers.txt --parallel  # 批量部署
  python auto_deploy.py --dry-run --show-config          # 预览配置
  python auto_deploy.py --algorithm efficient_zero       # 指定算法
        """
    )

    # 基础参数
    parser.add_argument('--target', type=str, help='目标服务器地址 (用于远程部署)')
    parser.add_argument('--targets', type=str, help='目标列表文件路径 (用于批量部署)')
    parser.add_argument('--algorithm', type=str, default='efficient_zero',
                       choices=['efficient_zero', 'muzero'],
                       help='算法类型 (默认: efficient_zero)')
    parser.add_argument('--environment', type=str, default='doudizhu',
                       help='环境类型 (默认: doudizhu)')

    # 部署选项
    parser.add_argument('--dry-run', action='store_true',
                       help='预演模式，不实际执行部署')
    parser.add_argument('--show-config', action='store_true',
                       help='显示生成的配置文件内容')
    parser.add_argument('--monitor', action='store_true',
                       help='启用训练监控')
    parser.add_argument('--parallel', action='store_true',
                       help='并行批量部署')
    parser.add_argument('--config-name', type=str,
                       help='指定配置文件名 (默认: auto_config_{algorithm}_{environment}.yaml)')

    # 日志选项
    parser.add_argument('--log-level', type=str, default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别 (默认: INFO)')
    parser.add_argument('--log-file', type=str,
                       help='日志文件路径')

    # 硬件检测选项
    parser.add_argument('--hardware-only', action='store_true',
                       help='仅执行硬件检测')
    parser.add_argument('--save-hardware-info', type=str,
                       help='保存硬件信息到指定文件')

    args = parser.parse_args()

    # 设置日志
    import logging
    log_level = getattr(logging, args.log_level.upper())
    logger = CrossPlatformUtils.setup_logging(args.log_file, log_level)

    # 显示欢迎信息
    logger.info("=" * 60)
    logger.info("🎯 斗地主AI自动部署工具 v1.0")
    logger.info("🔧 支持Windows和Ubuntu系统")
    logger.info("🚀 自动硬件检测、参数调优、配置生成")
    logger.info("=" * 60)

    try:
        # 创建自动部署管理器
        deploy_manager = AutoDeployManager()

        # 仅硬件检测模式
        if args.hardware_only:
            logger.info("🔍 执行硬件检测...")
            hardware_info = deploy_manager.hardware_detector.detect_all_hardware()

            # 显示硬件信息
            deploy_manager._display_hardware_summary(hardware_info)

            # 保存硬件信息
            if args.save_hardware_info:
                deploy_manager.hardware_detector.save_hardware_info(args.save_hardware_info)
                logger.info(f"💾 硬件信息已保存到: {args.save_hardware_info}")

            return 0

        # 批量部署模式
        if args.targets:
            logger.info(f"📋 批量部署模式: {args.targets}")
            result = deploy_manager.batch_deploy(args.targets, args.parallel)

            # 显示批量部署结果
            logger.info("📊 批量部署结果:")
            logger.info(f"  总目标数: {result.get('total_targets', 0)}")
            logger.info(f"  成功部署: {result.get('successful_deployments', 0)}")
            logger.info(f"  失败部署: {result.get('failed_deployments', 0)}")
            logger.info(f"  总耗时: {result.get('duration', 0):.2f}秒")

            return 0 if result.get('success', False) else 1

        # 单目标部署模式
        logger.info(f"🎯 单目标部署模式: {args.target or 'localhost'}")
        result = deploy_manager.auto_deploy(
            target=args.target,
            algorithm=args.algorithm,
            environment=args.environment,
            dry_run=args.dry_run,
            show_config=args.show_config,
            monitor=args.monitor,
            config_name=args.config_name
        )

        # 显示部署结果
        logger.info("📊 部署结果:")
        logger.info(f"  目标: {result.get('target', 'Unknown')}")
        logger.info(f"  算法: {result.get('algorithm', 'Unknown')}")
        logger.info(f"  状态: {'✅ 成功' if result.get('success', False) else '❌ 失败'}")
        logger.info(f"  耗时: {result.get('duration', 0):.2f}秒")

        if result.get('config_path'):
            logger.info(f"  配置文件: {result['config_path']}")

        # 如果部署成功，显示进程管理信息
        if result.get('success', False) and not args.dry_run:
            deployment_steps = result.get('steps', {})
            deployment_info = deployment_steps.get('deployment', {})

            if deployment_info.get('process_id'):
                logger.info("🔧 进程管理:")
                logger.info(f"  训练进程PID: {deployment_info['process_id']}")
                if deployment_info.get('log_file'):
                    logger.info(f"  日志文件: {deployment_info['log_file']}")
                logger.info("💡 进程控制提示:")
                logger.info("  - 训练进程已独立运行，关闭此终端不会影响训练")
                logger.info("  - 终止训练进程: python 终止训练进程.py --kill-all")
                logger.info("  - 查看训练进程: python 终止训练进程.py")
                logger.info("  - 查看模型信息: python 查看模型信息.py")

        # 显示错误信息
        if result.get('errors'):
            logger.error("❌ 错误信息:")
            for error in result['errors']:
                logger.error(f"  - {error}")

        # 显示警告信息
        if result.get('warnings'):
            logger.warning("⚠️ 警告信息:")
            for warning in result['warnings']:
                logger.warning(f"  - {warning}")

        return 0 if result.get('success', False) else 1

    except KeyboardInterrupt:
        logger.info("⏹️ 用户中断部署")
        return 130
    except Exception as e:
        logger.error(f"❌ 部署过程中发生未预期的错误: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
