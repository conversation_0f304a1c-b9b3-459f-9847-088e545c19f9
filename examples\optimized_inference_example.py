#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
优化推理示例

展示如何使用优化版EfficientZero进行推理。
"""

import os
import sys
import argparse
import logging
import torch
import numpy as np
import time
from typing import Dict, List, Tuple, Optional, Any, Union

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cardgame_ai.algorithms.efficient_zero import EfficientZero
from cardgame_ai.deployment.optimized_inference import OptimizedEfficientZero
from cardgame_ai.deployment.optimize_model import (
    export_to_onnx,
    convert_onnx_to_tensorrt,
    benchmark_inference
)
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def parse_args():
    """
    解析命令行参数

    Returns:
        argparse.Namespace: 命令行参数
    """
    parser = argparse.ArgumentParser(description='优化推理示例')
    parser.add_argument('--model_path', type=str, required=True, help='模型路径')
    parser.add_argument('--output_dir', type=str, default='optimized_models', help='优化模型保存目录')
    parser.add_argument('--device', type=str, default='cuda' if torch.cuda.is_available() else 'cpu', help='计算设备')
    parser.add_argument('--model_type', type=str, default='pytorch', choices=['pytorch', 'onnx', 'tensorrt'], help='模型类型')
    parser.add_argument('--num_games', type=int, default=10, help='测试游戏数量')
    parser.add_argument('--export_onnx', action='store_true', help='是否导出ONNX模型')
    parser.add_argument('--convert_tensorrt', action='store_true', help='是否转换为TensorRT模型')
    parser.add_argument('--precision', type=str, default='fp16', choices=['fp32', 'fp16', 'int8'], help='TensorRT精度模式')
    return parser.parse_args()


def export_models(model_path: str, output_dir: str, device: str, precision: str):
    """
    导出优化模型

    Args:
        model_path: 原始模型路径
        output_dir: 输出目录
        device: 计算设备
        precision: TensorRT精度模式
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 加载原始模型
    logger.info(f"加载原始模型: {model_path}")
    model = torch.load(model_path)
    model.eval()
    model.to(device)

    # 准备示例输入
    dummy_input = torch.randn(1, 656, device=device)

    # 导出ONNX模型
    onnx_path = os.path.join(output_dir, 'model.onnx')
    logger.info(f"导出ONNX模型: {onnx_path}")
    export_to_onnx(
        model=model,
        dummy_input=dummy_input,
        save_path=onnx_path,
        input_names=['input'],
        output_names=['policy_logits', 'value'],
        dynamic_axes={
            'input': {0: 'batch_size'},
            'policy_logits': {0: 'batch_size'},
            'value': {0: 'batch_size'}
        }
    )

    # 转换为TensorRT模型
    if device == 'cuda':
        try:
            tensorrt_path = os.path.join(output_dir, f'model_{precision}.trt')
            logger.info(f"转换为TensorRT模型 ({precision}): {tensorrt_path}")
            convert_onnx_to_tensorrt(
                onnx_path=onnx_path,
                save_path=tensorrt_path,
                precision=precision
            )
        except ImportError:
            logger.warning("TensorRT相关库未安装，无法转换为TensorRT模型")
    else:
        logger.warning("TensorRT仅支持CUDA设备，跳过TensorRT转换")

    logger.info("模型导出完成")

    return onnx_path, os.path.join(output_dir, f'model_{precision}.trt') if device == 'cuda' else None


def play_game(model, env: DouDizhuEnvironment, max_steps: int = 1000):
    """
    使用模型玩一局游戏

    Args:
        model: 模型
        env: 环境
        max_steps: 最大步数

    Returns:
        Tuple[bool, int]: 是否完成游戏、步数
    """
    state = env.reset()
    done = False
    step_count = 0

    while not done and step_count < max_steps:
        # 获取合法动作
        legal_actions = env.get_legal_actions(state)

        # 使用模型选择动作
        policy, _ = model.predict(state)  # 忽略价值

        # 选择最高概率的合法动作
        action_probs = {action: policy[i] for i, action in enumerate(legal_actions)}
        action = max(action_probs.items(), key=lambda x: x[1])[0]

        # 执行动作
        state, _, done, _ = env.step(action)  # 忽略奖励和信息
        step_count += 1

    return done, step_count


def main():
    """
    主函数
    """
    # 解析命令行参数
    args = parse_args()

    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)

    # 导出优化模型
    onnx_path = None
    tensorrt_path = None

    if args.export_onnx or args.convert_tensorrt:
        onnx_path, tensorrt_path = export_models(
            model_path=args.model_path,
            output_dir=args.output_dir,
            device=args.device,
            precision=args.precision
        )

    # 根据模型类型选择模型路径
    if args.model_type == 'onnx' and onnx_path:
        model_path = onnx_path
    elif args.model_type == 'tensorrt' and tensorrt_path:
        model_path = tensorrt_path
    else:
        model_path = args.model_path

    # 创建优化版EfficientZero
    logger.info(f"创建优化版EfficientZero，模型类型: {args.model_type}")
    model = OptimizedEfficientZero(
        model_path=model_path,
        model_type=args.model_type,
        device=args.device
    )

    # 创建环境
    env = DouDizhuEnvironment()

    # 测试推理性能
    logger.info("测试推理性能...")
    state = env.reset()

    # 预热
    for _ in range(10):
        _ = model.predict(state)

    # 计时
    start_time = time.time()
    for _ in range(100):
        _ = model.predict(state)
    inference_time = time.time() - start_time

    logger.info(f"推理性能: {inference_time / 100 * 1000:.4f} ms/帧, {100 / inference_time:.2f} FPS")

    # 玩游戏
    logger.info(f"开始玩 {args.num_games} 局游戏...")
    total_steps = 0
    completed_games = 0

    for i in range(args.num_games):
        done, steps = play_game(model, env)
        total_steps += steps
        if done:
            completed_games += 1

        logger.info(f"游戏 {i+1}/{args.num_games}: {'完成' if done else '未完成'}, 步数: {steps}")

    # 打印统计信息
    logger.info(f"完成率: {completed_games / args.num_games * 100:.2f}%")
    logger.info(f"平均步数: {total_steps / args.num_games:.2f}")

    # 打印推理统计信息
    stats = model.get_stats()
    logger.info(f"推理统计信息:")
    logger.info(f"  平均推理时间: {stats['avg_inference_time'] * 1000:.4f} ms")
    logger.info(f"  吞吐量: {stats['fps']:.2f} FPS")
    logger.info(f"  推理次数: {stats['inference_count']}")

    logger.info("优化推理示例完成")


if __name__ == "__main__":
    main()
