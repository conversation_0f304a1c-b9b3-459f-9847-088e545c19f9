#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
GTO正则化比较运行脚本

提供预设的配置选项来运行GTO正则化比较实验。
"""

import os
import argparse
import subprocess
import datetime
import json


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='运行GTO正则化比较实验')
    
    parser.add_argument('--experiment_type', type=str, default='quick',
                        choices=['quick', 'full', 'adaptive_only', 'custom'],
                        help='实验类型：快速测试(quick)、完整测试(full)、仅自适应(adaptive_only)或自定义(custom)')
    
    parser.add_argument('--gto_policy_path', type=str, required=True,
                        help='GTO策略文件路径（必填项）')
    
    parser.add_argument('--output_dir', type=str, default=None,
                        help='输出目录，默认为results/gto_comparison_<timestamp>')
    
    # 自定义实验参数
    parser.add_argument('--methods', type=str, default='kl,js,l2',
                        help='要比较的正则化方法，逗号分隔')
    parser.add_argument('--weights', type=str, default='0.1,0.05,0.01',
                        help='要比较的正则化权重，逗号分隔')
    parser.add_argument('--num_episodes', type=int, default=100,
                        help='训练回合数')
    parser.add_argument('--eval_interval', type=int, default=10,
                        help='评估间隔（回合数）')
    parser.add_argument('--enable_adaptive', action='store_true',
                        help='是否启用自适应权重')
    
    return parser.parse_args()


def get_experiment_config(experiment_type, args):
    """获取实验配置"""
    config = {}
    
    if experiment_type == 'quick':
        # 快速测试：只使用KL和JS散度，权重为0.1
        config = {
            'methods': 'kl,js',
            'weights': '0.1',
            'num_episodes': 50,
            'eval_interval': 5,
            'enable_adaptive': False
        }
    
    elif experiment_type == 'full':
        # 完整测试：使用所有方法，多个权重值
        config = {
            'methods': 'kl,js,l2',
            'weights': '0.1,0.05,0.01',
            'num_episodes': 200,
            'eval_interval': 10,
            'enable_adaptive': True
        }
    
    elif experiment_type == 'adaptive_only':
        # 仅测试自适应权重
        config = {
            'methods': 'kl,js,l2',
            'weights': '0.1',
            'num_episodes': 150,
            'eval_interval': 10,
            'enable_adaptive': True
        }
    
    elif experiment_type == 'custom':
        # 自定义配置
        config = {
            'methods': args.methods,
            'weights': args.weights,
            'num_episodes': args.num_episodes,
            'eval_interval': args.eval_interval,
            'enable_adaptive': args.enable_adaptive
        }
    
    return config


def main():
    """主函数"""
    args = parse_args()
    
    # 获取实验配置
    config = get_experiment_config(args.experiment_type, args)
    
    # 设置输出目录
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = args.output_dir or f"results/gto_comparison_{timestamp}"
    
    # 确保GTO策略文件存在
    if not os.path.exists(args.gto_policy_path):
        raise FileNotFoundError(f"GTO策略文件不存在: {args.gto_policy_path}")
    
    # 构建命令
    cmd = [
        'python', 'scripts/compare_gto_regularization.py',
        '--gto_policy_path', args.gto_policy_path,
        '--output_dir', output_dir,
        '--methods', config['methods'],
        '--weights', config['weights'],
        '--num_episodes', str(config['num_episodes']),
        '--eval_interval', str(config['eval_interval'])
    ]
    
    # 添加自适应选项
    if config['enable_adaptive']:
        cmd.append('--adaptive')
    
    # 打印实验配置
    print(f"\n===== GTO正则化比较实验配置 =====")
    print(f"实验类型: {args.experiment_type}")
    print(f"正则化方法: {config['methods']}")
    print(f"正则化权重: {config['weights']}")
    print(f"训练回合数: {config['num_episodes']}")
    print(f"评估间隔: {config['eval_interval']}")
    print(f"启用自适应权重: {'是' if config['enable_adaptive'] else '否'}")
    print(f"输出目录: {output_dir}")
    print(f"GTO策略路径: {args.gto_policy_path}")
    print("=" * 40)
    
    # 执行命令
    print("\n开始执行比较实验...")
    try:
        subprocess.run(cmd, check=True)
        print(f"\n实验完成！结果已保存至 {output_dir}")
        
        # 尝试打开摘要文件
        try:
            summary_path = os.path.join(output_dir, 'summary.json')
            if os.path.exists(summary_path):
                with open(summary_path, 'r', encoding='utf-8') as f:
                    summary = json.load(f)
                
                print("\n===== 实验结果摘要 =====")
                print(f"{'变体名称':<25} {'最终胜率':<12} {'最大胜率':<12} {'平均胜率':<12}")
                print("-" * 65)
                
                # 找出最佳变体
                best_variant = None
                best_win_rate = -1
                
                for name, metrics in summary.items():
                    print(f"{name:<25} {metrics['final_win_rate']:<12.4f} {metrics['max_win_rate']:<12.4f} {metrics['avg_win_rate']:<12.4f}")
                    
                    if metrics['final_win_rate'] > best_win_rate:
                        best_win_rate = metrics['final_win_rate']
                        best_variant = name
                
                if best_variant:
                    print("\n最佳变体: ", best_variant, f"(最终胜率: {best_win_rate:.4f})")
        except Exception as e:
            print(f"无法读取摘要文件: {e}")
            
    except subprocess.CalledProcessError as e:
        print(f"实验执行失败: {e}")


if __name__ == '__main__':
    main() 