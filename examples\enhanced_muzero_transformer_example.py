#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
增强版MuZeroTransformer示例

该脚本展示如何在MuZeroTransformer中使用增强版Transformer架构。
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional, Any, Union

from cardgame_ai.algorithms.muzero_transformer import MuZeroTransformer
from cardgame_ai.algorithms.enhanced_transformer import (
    EnhancedTransformerEncoder,
    EnhancedPositionalEncoding
)


class EnhancedMuZeroTransformerModel(nn.Module):
    """
    增强版MuZeroTransformer模型
    
    使用增强版Transformer架构实现MuZero算法。
    """
    
    def __init__(
        self,
        input_dim: int,
        action_dim: int,
        state_dim: int = 64,
        hidden_dim: int = 128,
        num_heads: int = 4,
        num_layers: int = 2,
        dropout: float = 0.1,
        seq_len: int = 1,
        use_relative_pos: bool = True,
        pos_encoding_type: str = 'sinusoidal',
        pre_norm: bool = True
    ):
        """
        初始化增强版MuZeroTransformer模型
        
        Args:
            input_dim (int): 输入维度
            action_dim (int): 动作维度
            state_dim (int, optional): 状态维度. Defaults to 64.
            hidden_dim (int, optional): 隐藏层维度. Defaults to 128.
            num_heads (int, optional): 注意力头数量. Defaults to 4.
            num_layers (int, optional): Transformer层数. Defaults to 2.
            dropout (float, optional): Dropout比率. Defaults to 0.1.
            seq_len (int, optional): 序列长度. Defaults to 1.
            use_relative_pos (bool, optional): 是否使用相对位置编码. Defaults to True.
            pos_encoding_type (str, optional): 位置编码类型. Defaults to 'sinusoidal'.
            pre_norm (bool, optional): 是否使用前置层正规化. Defaults to True.
        """
        super(EnhancedMuZeroTransformerModel, self).__init__()
        
        self.input_dim = input_dim
        self.action_dim = action_dim
        self.state_dim = state_dim
        self.hidden_dim = hidden_dim
        self.seq_len = seq_len
        
        # 表示网络
        self.representation_network = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim)
        )
        
        # 增强版Transformer编码器
        self.transformer_encoder = EnhancedTransformerEncoder(
            hidden_dim=hidden_dim,
            num_layers=num_layers,
            num_heads=num_heads,
            ff_dim=hidden_dim * 4,
            dropout=dropout,
            activation='gelu',
            pos_encoding_type=pos_encoding_type,
            use_relative_pos=use_relative_pos,
            max_seq_len=seq_len * 10,  # 设置足够大的最大序列长度
            output_attention=False,
            pre_norm=pre_norm
        )
        
        # 状态投影
        self.state_projection = nn.Linear(hidden_dim, state_dim)
        
        # 动态网络
        self.dynamics_network = nn.Sequential(
            nn.Linear(state_dim + action_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim)
        )
        
        # 奖励预测
        self.reward_predictor = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1)
        )
        
        # 策略预测
        self.policy_predictor = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, action_dim)
        )
        
        # 价值预测
        self.value_predictor = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1)
        )
    
    def initial_inference(self, observation: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        初始推理
        
        Args:
            observation (torch.Tensor): 观察张量，形状为 [batch_size, input_dim]
            
        Returns:
            Dict[str, torch.Tensor]: 包含状态、策略和价值的字典
        """
        batch_size = observation.shape[0]
        
        # 表示网络
        hidden = self.representation_network(observation)
        
        # 调整形状以适应Transformer
        if hidden.dim() == 2:
            hidden = hidden.unsqueeze(1)  # [batch_size, 1, hidden_dim]
        
        # Transformer编码器
        hidden = self.transformer_encoder(hidden)
        
        # 如果序列长度为1，则去掉序列维度
        if hidden.shape[1] == 1:
            hidden = hidden.squeeze(1)  # [batch_size, hidden_dim]
        
        # 状态投影
        state = self.state_projection(hidden)
        
        # 策略预测
        policy_logits = self.policy_predictor(hidden)
        
        # 价值预测
        value = self.value_predictor(hidden)
        
        return {
            "state": state,
            "policy_logits": policy_logits,
            "value": value
        }
    
    def dynamics(self, state: torch.Tensor, action: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        动态函数
        
        Args:
            state (torch.Tensor): 状态张量，形状为 [batch_size, state_dim]
            action (torch.Tensor): 动作张量，形状为 [batch_size]
            
        Returns:
            Tuple[torch.Tensor, torch.Tensor]: (下一个状态, 奖励)
        """
        batch_size = state.shape[0]
        
        # 将动作转换为one-hot编码
        action_one_hot = F.one_hot(action, self.action_dim).float()
        
        # 连接状态和动作
        state_action = torch.cat([state, action_one_hot], dim=1)
        
        # 动态网络
        hidden = self.dynamics_network(state_action)
        
        # 调整形状以适应Transformer
        if hidden.dim() == 2:
            hidden = hidden.unsqueeze(1)  # [batch_size, 1, hidden_dim]
        
        # Transformer编码器
        hidden = self.transformer_encoder(hidden)
        
        # 如果序列长度为1，则去掉序列维度
        if hidden.shape[1] == 1:
            hidden = hidden.squeeze(1)  # [batch_size, hidden_dim]
        
        # 状态投影
        next_state = self.state_projection(hidden)
        
        # 奖励预测
        reward = self.reward_predictor(hidden)
        
        return next_state, reward
    
    def prediction(self, state: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        预测函数
        
        Args:
            state (torch.Tensor): 状态张量，形状为 [batch_size, state_dim]
            
        Returns:
            Tuple[torch.Tensor, torch.Tensor]: (策略, 价值)
        """
        # 将状态投影到隐藏空间
        hidden = F.relu(nn.Linear(self.state_dim, self.hidden_dim)(state))
        
        # 调整形状以适应Transformer
        if hidden.dim() == 2:
            hidden = hidden.unsqueeze(1)  # [batch_size, 1, hidden_dim]
        
        # Transformer编码器
        hidden = self.transformer_encoder(hidden)
        
        # 如果序列长度为1，则去掉序列维度
        if hidden.shape[1] == 1:
            hidden = hidden.squeeze(1)  # [batch_size, hidden_dim]
        
        # 策略预测
        policy_logits = self.policy_predictor(hidden)
        
        # 价值预测
        value = self.value_predictor(hidden)
        
        return policy_logits, value


class EnhancedMuZeroTransformer(MuZeroTransformer):
    """
    增强版MuZeroTransformer算法
    
    使用增强版Transformer架构实现MuZero算法。
    """
    
    def __init__(
        self,
        input_dim: int,
        action_dim: int,
        state_dim: int = 64,
        hidden_dim: int = 128,
        num_simulations: int = 50,
        discount: float = 0.99,
        dirichlet_alpha: float = 0.25,
        exploration_fraction: float = 0.25,
        pb_c_base: float = 19652,
        pb_c_init: float = 1.25,
        weight_decay: float = 1e-4,
        lr: float = 0.001,
        num_heads: int = 4,
        num_layers: int = 2,
        dropout: float = 0.1,
        seq_len: int = 1,
        device: torch.device = None,
        use_relative_pos: bool = True,
        pos_encoding_type: str = 'sinusoidal',
        pre_norm: bool = True
    ):
        """
        初始化增强版MuZeroTransformer算法
        
        Args:
            input_dim (int): 输入维度
            action_dim (int): 动作维度
            state_dim (int, optional): 状态维度. Defaults to 64.
            hidden_dim (int, optional): 隐藏层维度. Defaults to 128.
            num_simulations (int, optional): MCTS模拟次数. Defaults to 50.
            discount (float, optional): 折扣因子. Defaults to 0.99.
            dirichlet_alpha (float, optional): Dirichlet噪声参数. Defaults to 0.25.
            exploration_fraction (float, optional): 探索比例. Defaults to 0.25.
            pb_c_base (float, optional): PUCT常数基数. Defaults to 19652.
            pb_c_init (float, optional): PUCT初始常数. Defaults to 1.25.
            weight_decay (float, optional): 权重衰减. Defaults to 1e-4.
            lr (float, optional): 学习率. Defaults to 0.001.
            num_heads (int, optional): 注意力头数量. Defaults to 4.
            num_layers (int, optional): Transformer层数. Defaults to 2.
            dropout (float, optional): Dropout比率. Defaults to 0.1.
            seq_len (int, optional): 序列长度. Defaults to 1.
            device (torch.device, optional): 计算设备. Defaults to None.
            use_relative_pos (bool, optional): 是否使用相对位置编码. Defaults to True.
            pos_encoding_type (str, optional): 位置编码类型. Defaults to 'sinusoidal'.
            pre_norm (bool, optional): 是否使用前置层正规化. Defaults to True.
        """
        # 设置设备
        if device is None:
            device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 创建增强版MuZeroTransformer模型
        model = EnhancedMuZeroTransformerModel(
            input_dim=input_dim,
            action_dim=action_dim,
            state_dim=state_dim,
            hidden_dim=hidden_dim,
            num_heads=num_heads,
            num_layers=num_layers,
            dropout=dropout,
            seq_len=seq_len,
            use_relative_pos=use_relative_pos,
            pos_encoding_type=pos_encoding_type,
            pre_norm=pre_norm
        )
        
        # 调用父类初始化
        super(EnhancedMuZeroTransformer, self).__init__(
            input_dim=input_dim,
            action_dim=action_dim,
            state_dim=state_dim,
            hidden_dim=hidden_dim,
            num_simulations=num_simulations,
            discount=discount,
            dirichlet_alpha=dirichlet_alpha,
            exploration_fraction=exploration_fraction,
            pb_c_base=pb_c_base,
            pb_c_init=pb_c_init,
            weight_decay=weight_decay,
            lr=lr,
            num_heads=num_heads,
            num_layers=num_layers,
            dropout=dropout,
            seq_len=seq_len,
            device=device
        )
        
        # 替换模型
        self.model = model.to(device)
        
        # 创建优化器
        self.optimizer = torch.optim.Adam(
            self.model.parameters(),
            lr=lr,
            weight_decay=weight_decay
        )


# 示例用法
if __name__ == "__main__":
    # 创建增强版MuZeroTransformer
    muzero = EnhancedMuZeroTransformer(
        input_dim=512,
        action_dim=27472,
        state_dim=256,
        hidden_dim=512,
        num_heads=8,
        num_layers=6,
        dropout=0.1,
        seq_len=10,
        use_relative_pos=True,
        pos_encoding_type='sinusoidal',
        pre_norm=True
    )
    
    print("增强版MuZeroTransformer创建成功！")
    print(f"设备: {muzero.device}")
    print(f"模型参数数量: {sum(p.numel() for p in muzero.model.parameters())}")
    
    # 创建随机输入
    batch_size = 2
    observation = torch.randn(batch_size, 512)
    
    # 初始推理
    output = muzero.model.initial_inference(observation)
    
    print(f"状态形状: {output['state'].shape}")
    print(f"策略形状: {output['policy_logits'].shape}")
    print(f"价值形状: {output['value'].shape}")
