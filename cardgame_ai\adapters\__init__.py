"""
适配器模块

该模块包含各种适配器类，用于连接现有组件与新定义的接口。
适配器模式允许不兼容的接口协同工作，实现系统的解耦和重构。

主要适配器:
- EfficientZeroTrainingAdapter: 训练服务适配器
- UnifiedConfigAdapter: 配置管理适配器
- EnhancedLoggingAdapter: 日志服务适配器
- PerformanceMonitorAdapter: 监控服务适配器
- OptimizedDataAdapter: 数据服务适配器

设计目标:
- 保持现有功能完全兼容
- 提供标准化的接口实现
- 支持渐进式重构
- 实现fail-fast原则

作者: Full Stack Dev James + Architect Timmy
版本: v1.0
"""

from .training_adapter import EfficientZeroTrainingAdapter
from .config_adapter import UnifiedConfigAdapter
from .logging_adapter import EnhancedLoggingAdapter
from .monitoring_adapter import PerformanceMonitorAdapter
from .data_adapter import OptimizedDataAdapter

__version__ = "1.0.0"
__author__ = "Full Stack Dev James + Architect Timmy"

__all__ = [
    "EfficientZeroTrainingAdapter",
    "UnifiedConfigAdapter", 
    "EnhancedLoggingAdapter",
    "PerformanceMonitorAdapter",
    "OptimizedDataAdapter"
]
