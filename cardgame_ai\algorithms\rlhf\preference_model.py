#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RLHF偏好模型模块

实现基于人类反馈的强化学习(RLHF)中的偏好模型，
用于学习人类偏好并指导AI模型的训练。
"""

import os
import logging
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Any, Optional, Union

# 设置日志
logger = logging.getLogger(__name__)


class PreferenceNetwork(nn.Module):
    """
    偏好网络

    学习人类偏好的神经网络模型，用于RLHF训练。
    """

    def __init__(
        self,
        state_dim: int,
        hidden_dim: int = 128,
        dropout: float = 0.1,
        use_action_info: bool = True,
        action_dim: Optional[int] = None
    ):
        """
        初始化偏好网络

        Args:
            state_dim: 状态维度
            hidden_dim: 隐藏层维度
            dropout: Dropout比例
            use_action_info: 是否使用动作信息
            action_dim: 动作维度，如果use_action_info为True则必须提供
        """
        super(PreferenceNetwork, self).__init__()

        self.state_dim = state_dim
        self.hidden_dim = hidden_dim
        self.dropout = dropout
        self.use_action_info = use_action_info
        self.action_dim = action_dim

        # 检查参数
        if use_action_info and action_dim is None:
            raise ValueError("如果use_action_info为True，则必须提供action_dim")

        # 计算输入维度
        input_dim = state_dim
        if use_action_info:
            input_dim += action_dim

        # 构建网络
        self.network = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, 1)
        )

    def forward(
        self,
        states: torch.Tensor,
        actions: Optional[torch.Tensor] = None
    ) -> torch.Tensor:
        """
        前向传播

        Args:
            states: 状态张量，形状为[batch_size, state_dim]
            actions: 动作张量，形状为[batch_size, action_dim]，如果use_action_info为True则必须提供

        Returns:
            torch.Tensor: 偏好得分，形状为[batch_size, 1]
        """
        # 检查参数
        if self.use_action_info and actions is None:
            raise ValueError("如果use_action_info为True，则必须提供actions")

        # 准备输入
        if self.use_action_info:
            inputs = torch.cat([states, actions], dim=1)
        else:
            inputs = states

        # 前向传播
        return self.network(inputs)


class PreferenceModel:
    """
    偏好模型

    基于人类反馈的强化学习(RLHF)中的偏好模型，
    用于学习人类偏好并指导AI模型的训练。
    """

    def __init__(
        self,
        state_dim: int,
        hidden_dim: int = 128,
        dropout: float = 0.1,
        use_action_info: bool = True,
        action_dim: Optional[int] = None,
        learning_rate: float = 1e-4,
        weight_decay: float = 1e-5,
        device: str = "cuda" if torch.cuda.is_available() else "cpu"
    ):
        """
        初始化偏好模型

        Args:
            state_dim: 状态维度
            hidden_dim: 隐藏层维度
            dropout: Dropout比例
            use_action_info: 是否使用动作信息
            action_dim: 动作维度，如果use_action_info为True则必须提供
            learning_rate: 学习率
            weight_decay: 权重衰减
            device: 计算设备
        """
        self.state_dim = state_dim
        self.hidden_dim = hidden_dim
        self.dropout = dropout
        self.use_action_info = use_action_info
        self.action_dim = action_dim
        self.learning_rate = learning_rate
        self.weight_decay = weight_decay
        self.device = device

        # 创建偏好网络
        self.network = PreferenceNetwork(
            state_dim=state_dim,
            hidden_dim=hidden_dim,
            dropout=dropout,
            use_action_info=use_action_info,
            action_dim=action_dim
        ).to(device)

        # 创建优化器
        self.optimizer = torch.optim.Adam(
            self.network.parameters(),
            lr=learning_rate,
            weight_decay=weight_decay
        )

        # 统计信息
        self.train_steps = 0
        self.stats = {
            "train_loss": [],
            "val_loss": [],
            "accuracy": []
        }

    def train_step(
        self,
        preferred_states: torch.Tensor,
        rejected_states: torch.Tensor,
        preferred_actions: Optional[torch.Tensor] = None,
        rejected_actions: Optional[torch.Tensor] = None
    ) -> float:
        """
        训练一步

        Args:
            preferred_states: 偏好状态张量，形状为[batch_size, state_dim]
            rejected_states: 拒绝状态张量，形状为[batch_size, state_dim]
            preferred_actions: 偏好动作张量，形状为[batch_size, action_dim]，如果use_action_info为True则必须提供
            rejected_actions: 拒绝动作张量，形状为[batch_size, action_dim]，如果use_action_info为True则必须提供

        Returns:
            float: 训练损失
        """
        # 将数据移动到设备
        preferred_states = preferred_states.to(self.device)
        rejected_states = rejected_states.to(self.device)

        if self.use_action_info:
            if preferred_actions is None or rejected_actions is None:
                raise ValueError("如果use_action_info为True，则必须提供preferred_actions和rejected_actions")
            preferred_actions = preferred_actions.to(self.device)
            rejected_actions = rejected_actions.to(self.device)

        # 设置为训练模式
        self.network.train()

        # 前向传播
        preferred_values = self.network(preferred_states, preferred_actions)
        rejected_values = self.network(rejected_states, rejected_actions)

        # 计算损失（使用Bradley-Terry模型）
        # 目标：使preferred_values > rejected_values
        loss = F.softplus(rejected_values - preferred_values).mean()

        # 反向传播和优化
        self.optimizer.zero_grad()
        loss.backward()
        self.optimizer.step()

        # 更新统计信息
        self.train_steps += 1
        self.stats["train_loss"].append(loss.item())

        return loss.item()

    def evaluate(
        self,
        preferred_states: torch.Tensor,
        rejected_states: torch.Tensor,
        preferred_actions: Optional[torch.Tensor] = None,
        rejected_actions: Optional[torch.Tensor] = None
    ) -> Tuple[float, float]:
        """
        评估模型

        Args:
            preferred_states: 偏好状态张量，形状为[batch_size, state_dim]
            rejected_states: 拒绝状态张量，形状为[batch_size, state_dim]
            preferred_actions: 偏好动作张量，形状为[batch_size, action_dim]，如果use_action_info为True则必须提供
            rejected_actions: 拒绝动作张量，形状为[batch_size, action_dim]，如果use_action_info为True则必须提供

        Returns:
            Tuple[float, float]: (损失, 准确率)
        """
        # 将数据移动到设备
        preferred_states = preferred_states.to(self.device)
        rejected_states = rejected_states.to(self.device)

        if self.use_action_info:
            if preferred_actions is None or rejected_actions is None:
                raise ValueError("如果use_action_info为True，则必须提供preferred_actions和rejected_actions")
            preferred_actions = preferred_actions.to(self.device)
            rejected_actions = rejected_actions.to(self.device)

        # 设置为评估模式
        self.network.eval()

        with torch.no_grad():
            # 前向传播
            preferred_values = self.network(preferred_states, preferred_actions)
            rejected_values = self.network(rejected_states, rejected_actions)

            # 计算损失（使用Bradley-Terry模型）
            loss = F.softplus(rejected_values - preferred_values).mean()

            # 计算准确率（预测正确的比例）
            correct = (preferred_values > rejected_values).float().mean()

        # 更新统计信息
        self.stats["val_loss"].append(loss.item())
        self.stats["accuracy"].append(correct.item())

        return loss.item(), correct.item()

    def predict(
        self,
        states: torch.Tensor,
        actions: Optional[torch.Tensor] = None
    ) -> torch.Tensor:
        """
        预测偏好得分

        Args:
            states: 状态张量，形状为[batch_size, state_dim]
            actions: 动作张量，形状为[batch_size, action_dim]，如果use_action_info为True则必须提供

        Returns:
            torch.Tensor: 偏好得分，形状为[batch_size, 1]
        """
        # 将数据移动到设备
        states = states.to(self.device)

        if self.use_action_info:
            if actions is None:
                raise ValueError("如果use_action_info为True，则必须提供actions")
            actions = actions.to(self.device)

        # 设置为评估模式
        self.network.eval()

        with torch.no_grad():
            # 前向传播
            values = self.network(states, actions)

        return values

    def save(self, path: str) -> None:
        """
        保存模型

        Args:
            path: 保存路径
        """
        # 创建目录
        os.makedirs(os.path.dirname(path), exist_ok=True)

        # 准备保存数据
        state_dict = {
            "network": self.network.state_dict(),
            "optimizer": self.optimizer.state_dict(),
            "train_steps": self.train_steps,
            "stats": self.stats,
            "config": {
                "state_dim": self.state_dim,
                "hidden_dim": self.hidden_dim,
                "dropout": self.dropout,
                "use_action_info": self.use_action_info,
                "action_dim": self.action_dim,
                "learning_rate": self.learning_rate,
                "weight_decay": self.weight_decay
            }
        }

        # 保存模型
        torch.save(state_dict, path)
        logger.info(f"模型已保存到: {path}")

    @classmethod
    def load(cls, path: str, device: str = "cuda" if torch.cuda.is_available() else "cpu") -> "PreferenceModel":
        """
        加载模型

        Args:
            path: 加载路径
            device: 计算设备

        Returns:
            PreferenceModel: 加载的模型
        """
        # 检查文件是否存在
        if not os.path.exists(path):
            raise FileNotFoundError(f"模型文件不存在: {path}")

        # 加载模型
        state_dict = torch.load(path, map_location=device)

        # 获取配置
        config = state_dict["config"]

        # 创建模型
        model = cls(
            state_dim=config["state_dim"],
            hidden_dim=config["hidden_dim"],
            dropout=config["dropout"],
            use_action_info=config["use_action_info"],
            action_dim=config["action_dim"],
            learning_rate=config["learning_rate"],
            weight_decay=config["weight_decay"],
            device=device
        )

        # 加载网络参数
        model.network.load_state_dict(state_dict["network"])

        # 加载优化器参数
        model.optimizer.load_state_dict(state_dict["optimizer"])

        # 加载训练步数和统计信息
        model.train_steps = state_dict["train_steps"]
        model.stats = state_dict["stats"]

        logger.info(f"模型已从 {path} 加载，训练步数: {model.train_steps}")

        return model