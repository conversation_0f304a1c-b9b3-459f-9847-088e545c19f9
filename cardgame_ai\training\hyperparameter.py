"""
超参数调整系统模块

实现超参数调整系统，包括网格搜索、随机搜索、贝叶斯优化和Population Based Training。
"""
import os
import time
import json
import logging
import datetime
import random
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from typing import Dict, Any, List, Tuple, Optional, Union, Callable
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor

try:
    import optuna
    from optuna.samplers import TPESampler
    OPTUNA_AVAILABLE = True
except ImportError:
    OPTUNA_AVAILABLE = False


class HyperparameterOptimizer:
    """
    超参数优化器
    
    实现多种超参数优化方法，用于自动调整模型参数。
    """
    
    def __init__(self, save_path: str = 'hyperopt', max_workers: int = None, 
                 use_multiprocessing: bool = False):
        """
        初始化超参数优化器
        
        Args:
            save_path (str, optional): 结果保存路径. Defaults to 'hyperopt'.
            max_workers (int, optional): 最大并行工作数. Defaults to None.
            use_multiprocessing (bool, optional): 是否使用多进程. Defaults to False.
        """
        self.save_path = save_path
        self.max_workers = max_workers
        self.use_multiprocessing = use_multiprocessing
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 创建保存目录
        os.makedirs(save_path, exist_ok=True)
        
        # 保存优化结果的文件
        self.results_file = os.path.join(save_path, 'optimization_results.jsonl')
        
        # 用于可视化的结果列表
        self.all_results = []
    
    def grid_search(self, param_grid: Dict[str, List[Any]], objective_func: Callable[[Dict[str, Any]], float],
                  maximize: bool = True, parallel: bool = True) -> Dict[str, Any]:
        """
        网格搜索
        
        Args:
            param_grid (Dict[str, List[Any]]): 参数网格
            objective_func (Callable[[Dict[str, Any]], float]): 目标函数
            maximize (bool, optional): 是否最大化目标. Defaults to True.
            parallel (bool, optional): 是否并行. Defaults to True.
            
        Returns:
            Dict[str, Any]: 最优参数和结果
        """
        start_time = time.time()
        
        # 生成所有参数组合
        param_combinations = self._generate_grid_combinations(param_grid)
        
        self.logger.info(f"开始网格搜索，共 {len(param_combinations)} 种参数组合")
        
        # 评估所有参数组合
        results = self._evaluate_combinations(param_combinations, objective_func, parallel)
        
        # 找到最优结果
        if maximize:
            best_idx = np.argmax([r['objective'] for r in results])
        else:
            best_idx = np.argmin([r['objective'] for r in results])
        
        best_result = results[best_idx]
        
        # 添加搜索方法信息
        best_result['method'] = 'grid_search'
        best_result['maximize'] = maximize
        best_result['num_trials'] = len(param_combinations)
        best_result['total_time'] = time.time() - start_time
        
        # 保存结果
        self._save_result(best_result)
        
        # 添加到结果列表
        self.all_results.extend(results)
        
        self.logger.info(
            f"网格搜索完成 | "
            f"最优目标值: {best_result['objective']:.6f} | "
            f"最优参数: {best_result['params']} | "
            f"总时间: {best_result['total_time']:.2f}s"
        )
        
        return best_result
    
    def random_search(self, param_ranges: Dict[str, Tuple], objective_func: Callable[[Dict[str, Any]], float],
                    num_trials: int = 10, maximize: bool = True, parallel: bool = True) -> Dict[str, Any]:
        """
        随机搜索
        
        Args:
            param_ranges (Dict[str, Tuple]): 参数范围，可以是(min, max)或选项列表
            objective_func (Callable[[Dict[str, Any]], float]): 目标函数
            num_trials (int, optional): 试验次数. Defaults to 10.
            maximize (bool, optional): 是否最大化目标. Defaults to True.
            parallel (bool, optional): 是否并行. Defaults to True.
            
        Returns:
            Dict[str, Any]: 最优参数和结果
        """
        start_time = time.time()
        
        # 生成随机参数组合
        param_combinations = []
        for _ in range(num_trials):
            params = {}
            for param_name, param_range in param_ranges.items():
                if isinstance(param_range, tuple) and len(param_range) == 2:
                    # 范围参数
                    min_val, max_val = param_range
                    if isinstance(min_val, int) and isinstance(max_val, int):
                        # 整数范围
                        params[param_name] = random.randint(min_val, max_val)
                    else:
                        # 浮点数范围
                        params[param_name] = random.uniform(min_val, max_val)
                elif isinstance(param_range, list):
                    # 列表选项
                    params[param_name] = random.choice(param_range)
                else:
                    raise ValueError(f"不支持的参数范围格式: {param_range}")
            param_combinations.append(params)
        
        self.logger.info(f"开始随机搜索，共 {num_trials} 次试验")
        
        # 评估所有参数组合
        results = self._evaluate_combinations(param_combinations, objective_func, parallel)
        
        # 找到最优结果
        if maximize:
            best_idx = np.argmax([r['objective'] for r in results])
        else:
            best_idx = np.argmin([r['objective'] for r in results])
        
        best_result = results[best_idx]
        
        # 添加搜索方法信息
        best_result['method'] = 'random_search'
        best_result['maximize'] = maximize
        best_result['num_trials'] = num_trials
        best_result['total_time'] = time.time() - start_time
        
        # 保存结果
        self._save_result(best_result)
        
        # 添加到结果列表
        self.all_results.extend(results)
        
        self.logger.info(
            f"随机搜索完成 | "
            f"最优目标值: {best_result['objective']:.6f} | "
            f"最优参数: {best_result['params']} | "
            f"总时间: {best_result['total_time']:.2f}s"
        )
        
        return best_result
    
    def bayesian_optimization(self, param_ranges: Dict[str, Tuple], objective_func: Callable[[Dict[str, Any]], float],
                           num_trials: int = 10, maximize: bool = True) -> Dict[str, Any]:
        """
        贝叶斯优化
        
        Args:
            param_ranges (Dict[str, Tuple]): 参数范围，可以是(min, max)或选项列表
            objective_func (Callable[[Dict[str, Any]], float]): 目标函数
            num_trials (int, optional): 试验次数. Defaults to 10.
            maximize (bool, optional): 是否最大化目标. Defaults to True.
            
        Returns:
            Dict[str, Any]: 最优参数和结果
        """
        if not OPTUNA_AVAILABLE:
            self.logger.warning("未安装Optuna库，无法使用贝叶斯优化。将退回到随机搜索。")
            return self.random_search(param_ranges, objective_func, num_trials, maximize)
        
        start_time = time.time()
        
        # 创建学习方向
        direction = 'maximize' if maximize else 'minimize'
        
        # 创建Optuna研究
        study = optuna.create_study(direction=direction, sampler=TPESampler())
        
        # 定义目标函数包装器
        def optuna_objective(trial):
            params = {}
            for param_name, param_range in param_ranges.items():
                if isinstance(param_range, tuple) and len(param_range) == 2:
                    # 范围参数
                    min_val, max_val = param_range
                    if isinstance(min_val, int) and isinstance(max_val, int):
                        # 整数范围
                        params[param_name] = trial.suggest_int(param_name, min_val, max_val)
                    else:
                        # 浮点数范围
                        params[param_name] = trial.suggest_float(param_name, min_val, max_val)
                elif isinstance(param_range, list):
                    # 列表选项
                    params[param_name] = trial.suggest_categorical(param_name, param_range)
                else:
                    raise ValueError(f"不支持的参数范围格式: {param_range}")
            
            # 评估参数组合
            return objective_func(params)
        
        self.logger.info(f"开始贝叶斯优化，共 {num_trials} 次试验")
        
        # 运行优化
        study.optimize(optuna_objective, n_trials=num_trials)
        
        # 获取最优结果
        best_params = study.best_params
        best_value = study.best_value
        
        best_result = {
            'params': best_params,
            'objective': best_value,
            'method': 'bayesian_optimization',
            'maximize': maximize,
            'num_trials': num_trials,
            'total_time': time.time() - start_time
        }
        
        # 保存结果
        self._save_result(best_result)
        
        # 收集所有试验结果并添加到结果列表
        for trial in study.trials:
            if trial.state == optuna.trial.TrialState.COMPLETE:
                result = {
                    'params': trial.params,
                    'objective': trial.value,
                    'trial_id': trial.number
                }
                self.all_results.append(result)
        
        self.logger.info(
            f"贝叶斯优化完成 | "
            f"最优目标值: {best_value:.6f} | "
            f"最优参数: {best_params} | "
            f"总时间: {best_result['total_time']:.2f}s"
        )
        
        return best_result
    
    def population_based_training(self, initial_population: List[Dict[str, Any]], 
                               objective_func: Callable[[Dict[str, Any]], float],
                               num_generations: int = 5, population_size: int = 10,
                               mutation_rate: float = 0.1, maximize: bool = True,
                               parallel: bool = True) -> Dict[str, Any]:
        """
        基于种群的训练 (PBT)
        
        Args:
            initial_population (List[Dict[str, Any]]): 初始种群
            objective_func (Callable[[Dict[str, Any]], float]): 目标函数
            num_generations (int, optional): 代数. Defaults to 5.
            population_size (int, optional): 种群大小. Defaults to 10.
            mutation_rate (float, optional): 变异率. Defaults to 0.1.
            maximize (bool, optional): 是否最大化目标. Defaults to True.
            parallel (bool, optional): 是否并行. Defaults to True.
            
        Returns:
            Dict[str, Any]: 最优参数和结果
        """
        start_time = time.time()
        
        # 确保初始种群大小正确
        if len(initial_population) < population_size:
            # 复制种群以达到所需大小
            initial_population = initial_population * (population_size // len(initial_population) + 1)
        initial_population = initial_population[:population_size]
        
        # 当前种群
        population = initial_population.copy()
        
        # 所有世代的结果
        all_generation_results = []
        
        self.logger.info(f"开始基于种群的训练，种群大小: {population_size}，世代数: {num_generations}")
        
        for generation in range(num_generations):
            gen_start_time = time.time()
            
            # 评估当前种群
            results = self._evaluate_combinations(population, objective_func, parallel)
            
            # 记录当前代的结果
            for i, (params, result) in enumerate(zip(population, results)):
                result['generation'] = generation
                result['population_index'] = i
                all_generation_results.append(result)
            
            # 找到优秀个体
            result_values = [r['objective'] for r in results]
            if maximize:
                elite_indices = np.argsort(result_values)[-population_size // 2:]
            else:
                elite_indices = np.argsort(result_values)[:population_size // 2]
            
            # 精英保留和选择、变异
            elite_population = [population[i] for i in elite_indices]
            new_population = elite_population.copy()
            
            # 变异和交叉生成新个体
            while len(new_population) < population_size:
                # 随机选择一个精英个体
                parent = random.choice(elite_population)
                
                # 创建子代
                child = parent.copy()
                
                # 变异
                for param_name in child:
                    if random.random() < mutation_rate:
                        # 随机变异
                        if isinstance(child[param_name], int):
                            # 整数参数，上下浮动20%
                            delta = max(1, int(abs(child[param_name]) * 0.2))
                            child[param_name] += random.randint(-delta, delta)
                        elif isinstance(child[param_name], float):
                            # 浮点参数，上下浮动20%
                            delta = abs(child[param_name]) * 0.2
                            child[param_name] += random.uniform(-delta, delta)
                
                new_population.append(child)
            
            # 更新种群
            population = new_population
            
            # 找到当前代的最佳结果
            if maximize:
                best_idx = np.argmax(result_values)
            else:
                best_idx = np.argmin(result_values)
            
            best_gen_result = results[best_idx]
            best_gen_params = population[best_idx]
            
            gen_time = time.time() - gen_start_time
            self.logger.info(
                f"第 {generation + 1}/{num_generations} 代完成 | "
                f"最优目标值: {best_gen_result['objective']:.6f} | "
                f"时间: {gen_time:.2f}s"
            )
        
        # 找到所有代中的最优结果
        all_objectives = [r['objective'] for r in all_generation_results]
        if maximize:
            best_idx = np.argmax(all_objectives)
        else:
            best_idx = np.argmin(all_objectives)
        
        best_result = all_generation_results[best_idx]
        
        # 添加PBT特定信息
        best_result['method'] = 'population_based_training'
        best_result['maximize'] = maximize
        best_result['num_generations'] = num_generations
        best_result['population_size'] = population_size
        best_result['mutation_rate'] = mutation_rate
        best_result['total_time'] = time.time() - start_time
        
        # 保存结果
        self._save_result(best_result)
        
        # 添加到结果列表
        self.all_results.extend(all_generation_results)
        
        self.logger.info(
            f"基于种群的训练完成 | "
            f"最优目标值: {best_result['objective']:.6f} | "
            f"最优参数: {best_result['params']} | "
            f"最优代数: {best_result['generation'] + 1} | "
            f"总时间: {best_result['total_time']:.2f}s"
        )
        
        return best_result
    
    def visualize_results(self, top_n: int = 10) -> None:
        """
        可视化优化结果
        
        Args:
            top_n (int, optional): 显示前N个结果. Defaults to 10.
        """
        if not self.all_results:
            self.logger.warning("没有优化结果可供可视化")
            return
        
        # 创建DataFrame
        df = pd.DataFrame(self.all_results)
        
        # 确保有目标值和参数
        if 'objective' not in df.columns or 'params' not in df.columns:
            self.logger.warning("优化结果缺少必要字段，无法可视化")
            return
        
        # 参数列展开
        params_df = pd.json_normalize(df['params'].apply(lambda x: x if isinstance(x, dict) else {}))
        df = pd.concat([df.drop('params', axis=1), params_df], axis=1)
        
        # 创建时间戳
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建目标值分布图
        plt.figure(figsize=(10, 6))
        plt.hist(df['objective'], bins=20, alpha=0.7)
        plt.title('Objective Value Distribution')
        plt.xlabel('Objective Value')
        plt.ylabel('Frequency')
        plt.grid(True, alpha=0.3)
        
        # 保存图形
        save_path = os.path.join(self.save_path, f"objective_dist_{timestamp}.png")
        plt.savefig(save_path)
        plt.close()
        
        # 创建参数重要性图（如果有多个试验）
        if len(df) > 1 and 'method' in df.columns:
            # 按方法分组
            for method, method_df in df.groupby('method'):
                # 找出参数列
                param_cols = [col for col in method_df.columns 
                             if col not in ['objective', 'method', 'maximize', 'num_trials', 
                                            'total_time', 'generation', 'population_index', 'trial_id']]
                
                if param_cols:
                    plt.figure(figsize=(12, 8))
                    
                    # 按目标值排序并获取前N个
                    if 'maximize' in method_df.columns and method_df['maximize'].iloc[0]:
                        top_df = method_df.nlargest(min(top_n, len(method_df)), 'objective')
                    else:
                        top_df = method_df.nsmallest(min(top_n, len(method_df)), 'objective')
                    
                    # 绘制参数与目标值的关系
                    for i, param in enumerate(param_cols):
                        plt.subplot(len(param_cols), 1, i + 1)
                        plt.scatter(method_df[param], method_df['objective'], alpha=0.6)
                        
                        # 标记Top-N
                        plt.scatter(top_df[param], top_df['objective'], color='red', 
                                   s=100, alpha=0.8, label=f'Top {top_n}')
                        
                        plt.title(f'{param} vs Objective')
                        plt.xlabel(param)
                        plt.ylabel('Objective')
                        plt.grid(True, alpha=0.3)
                        if i == 0:
                            plt.legend()
                    
                    plt.tight_layout()
                    
                    # 保存图形
                    save_path = os.path.join(self.save_path, f"param_importance_{method}_{timestamp}.png")
                    plt.savefig(save_path)
                    plt.close()
        
        # 如果有PBT结果，绘制进化过程
        if 'generation' in df.columns:
            plt.figure(figsize=(10, 6))
            
            pbt_df = df[df['method'] == 'population_based_training'].copy()
            if len(pbt_df) > 0:
                # 按世代分组计算统计信息
                gen_stats = pbt_df.groupby('generation')['objective'].agg(['mean', 'min', 'max', 'std']).reset_index()
                
                # 绘制进化趋势
                plt.errorbar(gen_stats['generation'], gen_stats['mean'], 
                            yerr=gen_stats['std'], fmt='o-', label='Mean ± Std')
                plt.plot(gen_stats['generation'], gen_stats['min'], 's--', label='Min')
                plt.plot(gen_stats['generation'], gen_stats['max'], '^--', label='Max')
                
                plt.title('Population Based Training Evolution')
                plt.xlabel('Generation')
                plt.ylabel('Objective Value')
                plt.legend()
                plt.grid(True, alpha=0.3)
                
                # 保存图形
                save_path = os.path.join(self.save_path, f"pbt_evolution_{timestamp}.png")
                plt.savefig(save_path)
                plt.close()
        
        self.logger.info(f"优化结果可视化已保存至 {self.save_path}")
    
    def _generate_grid_combinations(self, param_grid: Dict[str, List[Any]]) -> List[Dict[str, Any]]:
        """
        生成参数网格的所有组合
        
        Args:
            param_grid (Dict[str, List[Any]]): 参数网格
            
        Returns:
            List[Dict[str, Any]]: 参数组合列表
        """
        keys = list(param_grid.keys())
        values = list(param_grid.values())
        
        # 递归生成组合
        def _generate_combinations(index: int, current: Dict[str, Any]) -> List[Dict[str, Any]]:
            if index == len(keys):
                return [current.copy()]
            
            result = []
            for value in values[index]:
                current[keys[index]] = value
                result.extend(_generate_combinations(index + 1, current))
            
            return result
        
        return _generate_combinations(0, {})
    
    def _evaluate_combinations(self, param_combinations: List[Dict[str, Any]], 
                              objective_func: Callable[[Dict[str, Any]], float],
                              parallel: bool = True) -> List[Dict[str, Any]]:
        """
        评估参数组合
        
        Args:
            param_combinations (List[Dict[str, Any]]): 参数组合列表
            objective_func (Callable[[Dict[str, Any]], float]): 目标函数
            parallel (bool, optional): 是否并行. Defaults to True.
            
        Returns:
            List[Dict[str, Any]]: 评估结果列表
        """
        results = []
        
        if parallel and len(param_combinations) > 1:
            # 确定执行器类型
            executor_class = ProcessPoolExecutor if self.use_multiprocessing else ThreadPoolExecutor
            max_workers = min(self.max_workers or os.cpu_count(), len(param_combinations))
            
            # 定义评估函数
            def _evaluate_single_params(params):
                try:
                    objective = objective_func(params)
                    return {
                        'params': params,
                        'objective': objective
                    }
                except Exception as e:
                    self.logger.error(f"评估参数 {params} 时出错: {str(e)}")
                    return {
                        'params': params,
                        'objective': float('-inf') if self.maximize else float('inf'),
                        'error': str(e)
                    }
            
            # 并行评估
            with executor_class(max_workers=max_workers) as executor:
                futures = [executor.submit(_evaluate_single_params, params) for params in param_combinations]
                
                # 收集结果
                for i, future in enumerate(futures):
                    try:
                        result = future.result()
                        results.append(result)
                        
                        # 日志
                        if (i + 1) % 10 == 0 or (i + 1) == len(param_combinations):
                            self.logger.info(f"已评估 {i + 1}/{len(param_combinations)} 种参数组合")
                    except Exception as e:
                        self.logger.error(f"获取评估结果 {i} 时出错: {str(e)}")
                        results.append({
                            'params': param_combinations[i],
                            'objective': float('-inf') if self.maximize else float('inf'),
                            'error': str(e)
                        })
        else:
            # 顺序评估
            for i, params in enumerate(param_combinations):
                try:
                    objective = objective_func(params)
                    results.append({
                        'params': params,
                        'objective': objective
                    })
                except Exception as e:
                    self.logger.error(f"评估参数 {params} 时出错: {str(e)}")
                    results.append({
                        'params': params,
                        'objective': float('-inf') if self.maximize else float('inf'),
                        'error': str(e)
                    })
                
                # 日志
                if (i + 1) % 10 == 0 or (i + 1) == len(param_combinations):
                    self.logger.info(f"已评估 {i + 1}/{len(param_combinations)} 种参数组合")
        
        return results
    
    def _save_result(self, result: Dict[str, Any]) -> None:
        """
        保存优化结果
        
        Args:
            result (Dict[str, Any]): 优化结果
        """
        # 添加时间戳
        result['timestamp'] = datetime.datetime.now().isoformat()
        
        # 追加到结果文件
        with open(self.results_file, 'a') as f:
            f.write(json.dumps(result) + '\n')
        
        # 单独保存最优结果
        result_file = os.path.join(
            self.save_path, 
            f"best_result_{result['method']}_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        )
        
        with open(result_file, 'w') as f:
            json.dump(result, f, indent=2)
        
        self.logger.info(f"优化结果已保存至 {result_file}") 