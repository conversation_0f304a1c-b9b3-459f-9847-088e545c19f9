#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
性能监控模块

提供训练过程的性能监控功能：
- GPU利用率监控
- 内存使用监控
- 训练吞吐量统计
- 实时性能报告
"""

import time
import logging
import threading
from typing import Dict, List, Any, Optional
import numpy as np
import torch
import psutil
from collections import deque, defaultdict

logger = logging.getLogger(__name__)


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, monitor_interval: float = 1.0, history_size: int = 1000):
        """
        初始化性能监控器
        
        Args:
            monitor_interval: 监控间隔（秒）
            history_size: 历史记录大小
        """
        self.monitor_interval = monitor_interval
        self.history_size = history_size
        
        # 性能指标存储
        self.metrics = defaultdict(lambda: deque(maxlen=history_size))
        
        # 监控状态
        self.monitoring = False
        self.monitor_thread = None
        self.lock = threading.Lock()
        
        # 训练统计
        self.training_start_time = None
        self.step_count = 0
        self.epoch_count = 0
        
        logger.info(f"初始化性能监控器，监控间隔: {monitor_interval}s")
    
    def start_monitoring(self):
        """开始监控"""
        if not self.monitoring:
            self.monitoring = True
            self.monitor_thread = threading.Thread(target=self._monitor_worker, daemon=True)
            self.monitor_thread.start()
            logger.info("开始性能监控")
    
    def stop_monitoring(self):
        """停止监控"""
        if self.monitoring:
            self.monitoring = False
            if self.monitor_thread:
                self.monitor_thread.join(timeout=5.0)
            logger.info("停止性能监控")
    
    def _monitor_worker(self):
        """监控工作线程"""
        while self.monitoring:
            try:
                # 收集系统指标
                self._collect_system_metrics()
                
                # 收集GPU指标
                if torch.cuda.is_available():
                    self._collect_gpu_metrics()
                
                time.sleep(self.monitor_interval)
                
            except Exception as e:
                logger.error(f"监控线程错误: {e}")
                time.sleep(1.0)
    
    def _collect_system_metrics(self):
        """收集系统指标"""
        current_time = time.time()
        
        with self.lock:
            # CPU使用率
            cpu_percent = psutil.cpu_percent()
            self.metrics['cpu_usage'].append((current_time, cpu_percent))
            
            # 内存使用
            memory = psutil.virtual_memory()
            self.metrics['memory_usage'].append((current_time, memory.percent))
            self.metrics['memory_available_gb'].append((current_time, memory.available / (1024**3)))
            
            # 磁盘IO
            disk_io = psutil.disk_io_counters()
            if disk_io:
                self.metrics['disk_read_mb_s'].append((current_time, disk_io.read_bytes / (1024**2)))
                self.metrics['disk_write_mb_s'].append((current_time, disk_io.write_bytes / (1024**2)))
    
    def _collect_gpu_metrics(self):
        """收集GPU指标"""
        current_time = time.time()
        
        try:
            with self.lock:
                for i in range(torch.cuda.device_count()):
                    # GPU内存使用
                    memory_allocated = torch.cuda.memory_allocated(i) / (1024**3)  # GB
                    memory_reserved = torch.cuda.memory_reserved(i) / (1024**3)   # GB
                    max_memory = torch.cuda.max_memory_allocated(i) / (1024**3)   # GB
                    
                    self.metrics[f'gpu_{i}_memory_allocated_gb'].append((current_time, memory_allocated))
                    self.metrics[f'gpu_{i}_memory_reserved_gb'].append((current_time, memory_reserved))
                    self.metrics[f'gpu_{i}_max_memory_gb'].append((current_time, max_memory))
                    
                    # GPU利用率（需要nvidia-ml-py库）
                    try:
                        import pynvml
                        if not hasattr(self, '_nvml_initialized'):
                            pynvml.nvmlInit()
                            self._nvml_initialized = True
                        
                        handle = pynvml.nvmlDeviceGetHandleByIndex(i)
                        utilization = pynvml.nvmlDeviceGetUtilizationRates(handle)
                        temperature = pynvml.nvmlDeviceGetTemperature(handle, pynvml.NVML_TEMPERATURE_GPU)
                        
                        self.metrics[f'gpu_{i}_utilization'].append((current_time, utilization.gpu))
                        self.metrics[f'gpu_{i}_memory_utilization'].append((current_time, utilization.memory))
                        self.metrics[f'gpu_{i}_temperature'].append((current_time, temperature))
                        
                    except ImportError:
                        # 如果没有pynvml，跳过GPU利用率监控
                        pass
                    except Exception as e:
                        logger.debug(f"GPU利用率监控错误: {e}")
                        
        except Exception as e:
            logger.warning(f"GPU指标收集错误: {e}")
    
    def log_training_step(self, step_metrics: Dict[str, Any]):
        """记录训练步骤指标"""
        current_time = time.time()
        
        with self.lock:
            self.step_count += 1
            
            # 记录训练指标
            for key, value in step_metrics.items():
                if isinstance(value, (int, float)):
                    self.metrics[f'training_{key}'].append((current_time, value))
            
            # 计算训练吞吐量
            if self.training_start_time is None:
                self.training_start_time = current_time
            
            elapsed_time = current_time - self.training_start_time
            if elapsed_time > 0:
                steps_per_second = self.step_count / elapsed_time
                self.metrics['training_throughput_steps_per_sec'].append((current_time, steps_per_second))
    
    def log_epoch_completion(self, epoch_metrics: Dict[str, Any]):
        """记录epoch完成指标"""
        current_time = time.time()
        
        with self.lock:
            self.epoch_count += 1
            
            # 记录epoch指标
            for key, value in epoch_metrics.items():
                if isinstance(value, (int, float)):
                    self.metrics[f'epoch_{key}'].append((current_time, value))
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """获取当前性能指标"""
        current_metrics = {}
        
        with self.lock:
            for metric_name, values in self.metrics.items():
                if values:
                    # 获取最新值
                    latest_value = values[-1][1]
                    current_metrics[metric_name] = latest_value
                    
                    # 计算统计信息
                    if len(values) >= 10:
                        recent_values = [v[1] for v in list(values)[-10:]]
                        current_metrics[f'{metric_name}_avg_10'] = np.mean(recent_values)
                        current_metrics[f'{metric_name}_std_10'] = np.std(recent_values)
        
        return current_metrics
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        summary = {
            'monitoring_duration': 0.0,
            'total_steps': self.step_count,
            'total_epochs': self.epoch_count,
            'avg_throughput': 0.0,
            'gpu_summary': {},
            'system_summary': {}
        }
        
        with self.lock:
            # 计算监控时长
            if self.training_start_time:
                summary['monitoring_duration'] = time.time() - self.training_start_time
            
            # 计算平均吞吐量
            throughput_values = [v[1] for v in self.metrics['training_throughput_steps_per_sec']]
            if throughput_values:
                summary['avg_throughput'] = np.mean(throughput_values)
            
            # GPU摘要
            if torch.cuda.is_available():
                for i in range(torch.cuda.device_count()):
                    gpu_memory_key = f'gpu_{i}_memory_allocated_gb'
                    gpu_util_key = f'gpu_{i}_utilization'
                    
                    gpu_info = {}
                    if gpu_memory_key in self.metrics and self.metrics[gpu_memory_key]:
                        memory_values = [v[1] for v in self.metrics[gpu_memory_key]]
                        gpu_info['avg_memory_gb'] = np.mean(memory_values)
                        gpu_info['max_memory_gb'] = np.max(memory_values)
                    
                    if gpu_util_key in self.metrics and self.metrics[gpu_util_key]:
                        util_values = [v[1] for v in self.metrics[gpu_util_key]]
                        gpu_info['avg_utilization'] = np.mean(util_values)
                        gpu_info['max_utilization'] = np.max(util_values)
                    
                    if gpu_info:
                        summary['gpu_summary'][f'gpu_{i}'] = gpu_info
            
            # 系统摘要
            if 'cpu_usage' in self.metrics and self.metrics['cpu_usage']:
                cpu_values = [v[1] for v in self.metrics['cpu_usage']]
                summary['system_summary']['avg_cpu_usage'] = np.mean(cpu_values)
                summary['system_summary']['max_cpu_usage'] = np.max(cpu_values)
            
            if 'memory_usage' in self.metrics and self.metrics['memory_usage']:
                memory_values = [v[1] for v in self.metrics['memory_usage']]
                summary['system_summary']['avg_memory_usage'] = np.mean(memory_values)
                summary['system_summary']['max_memory_usage'] = np.max(memory_values)
        
        return summary
    
    def generate_performance_report(self) -> str:
        """生成性能报告"""
        summary = self.get_performance_summary()
        current_metrics = self.get_current_metrics()
        
        report = []
        report.append("=" * 60)
        report.append("性能监控报告")
        report.append("=" * 60)
        
        # 基本信息
        report.append(f"监控时长: {summary['monitoring_duration']:.1f}秒")
        report.append(f"总训练步数: {summary['total_steps']}")
        report.append(f"总epoch数: {summary['total_epochs']}")
        report.append(f"平均训练吞吐量: {summary['avg_throughput']:.2f} 步/秒")
        report.append("")
        
        # GPU信息
        if summary['gpu_summary']:
            report.append("GPU性能:")
            for gpu_name, gpu_info in summary['gpu_summary'].items():
                report.append(f"  {gpu_name}:")
                if 'avg_memory_gb' in gpu_info:
                    report.append(f"    平均内存使用: {gpu_info['avg_memory_gb']:.2f}GB")
                    report.append(f"    最大内存使用: {gpu_info['max_memory_gb']:.2f}GB")
                if 'avg_utilization' in gpu_info:
                    report.append(f"    平均利用率: {gpu_info['avg_utilization']:.1f}%")
                    report.append(f"    最大利用率: {gpu_info['max_utilization']:.1f}%")
            report.append("")
        
        # 系统信息
        if summary['system_summary']:
            report.append("系统性能:")
            sys_summary = summary['system_summary']
            if 'avg_cpu_usage' in sys_summary:
                report.append(f"  平均CPU使用率: {sys_summary['avg_cpu_usage']:.1f}%")
                report.append(f"  最大CPU使用率: {sys_summary['max_cpu_usage']:.1f}%")
            if 'avg_memory_usage' in sys_summary:
                report.append(f"  平均内存使用率: {sys_summary['avg_memory_usage']:.1f}%")
                report.append(f"  最大内存使用率: {sys_summary['max_memory_usage']:.1f}%")
            report.append("")
        
        # 当前状态
        report.append("当前状态:")
        for key, value in current_metrics.items():
            if not key.endswith('_avg_10') and not key.endswith('_std_10'):
                if isinstance(value, float):
                    report.append(f"  {key}: {value:.3f}")
                else:
                    report.append(f"  {key}: {value}")
        
        report.append("=" * 60)
        
        return "\n".join(report)
    
    def save_metrics_to_file(self, filepath: str):
        """保存指标到文件"""
        import json
        
        # 准备数据
        data = {}
        with self.lock:
            for metric_name, values in self.metrics.items():
                data[metric_name] = [(t, v) for t, v in values]
        
        # 添加摘要信息
        data['summary'] = self.get_performance_summary()
        
        # 保存到文件
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"性能指标已保存到: {filepath}")


# 全局性能监控器实例
_global_monitor = None

def get_global_monitor() -> PerformanceMonitor:
    """获取全局性能监控器"""
    global _global_monitor
    if _global_monitor is None:
        _global_monitor = PerformanceMonitor()
    return _global_monitor

def start_global_monitoring():
    """启动全局性能监控"""
    monitor = get_global_monitor()
    monitor.start_monitoring()

def stop_global_monitoring():
    """停止全局性能监控"""
    monitor = get_global_monitor()
    monitor.stop_monitoring()

def log_training_metrics(metrics: Dict[str, Any]):
    """记录训练指标到全局监控器"""
    monitor = get_global_monitor()
    monitor.log_training_step(metrics)
