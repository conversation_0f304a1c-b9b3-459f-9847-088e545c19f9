#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
斗地主AI主训练脚本

这是斗地主AI项目的主要训练入口，集成了所有优化特性和最佳实践。
该脚本是项目重构后的统一训练接口，提供完整的训练功能。

使用方法:
    1. IDE直接运行（推荐）:
       直接在IDE中运行此文件，将使用优化配置启动训练

    2. 命令行运行:
       python main_training.py
       python main_training.py --device cuda:0
       python main_training.py --resume
       python main_training.py --config configs/training/optimized.yaml

核心特性:
    - EfficientZero算法优化 (MCTS: 50→100-200次模拟)
    - 增强多智能体协作机制 (农民协作优化)
    - 分布式训练支持 (可选)
    - 实时性能监控 (TensorBoard集成)
    - 动态奖励机制 (团队协作优化)
    - 完整的错误处理和日志系统
    - 统一的配置管理系统

项目结构:
    - main_training.py: 主训练脚本 (当前文件)
    - quick_start.py: 快速启动脚本
    - legacy/: 兼容性脚本目录
    - docs/: 项目文档
"""

import os
import sys
import random
import logging
import argparse
import traceback
from datetime import datetime
from typing import Dict, Any, Optional, Tuple
from pathlib import Path

# 导入numpy - 必须可用，否则立即失败
import numpy as np

# 导入yaml - 必须可用，否则立即失败
import yaml

# 添加项目根目录到路径
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(script_dir, '../..'))
sys.path.insert(0, project_root)

# 导入现有系统组件 - 必须可用，否则立即失败
from cardgame_ai.utils.unified_config_manager import UnifiedConfigManager
from cardgame_ai.utils.enhanced_logger import setup_training_logger
from cardgame_ai.utils.training_parameter_manager import TrainingParameterManager, TrainingParameters
from cardgame_ai.training.unified_reward_system import UnifiedRewardSystem

# 导入优化组件
from cardgame_ai.utils.optimized_data_loader import create_optimized_dataloader
from cardgame_ai.training.dynamic_optimizer import PerformanceOptimizer
from cardgame_ai.algorithms.mcts import MCTS
from cardgame_ai.utils.performance_monitor import get_global_monitor, start_global_monitoring, stop_global_monitoring

# 导入训练模块 - 必须可用，否则立即失败
from cardgame_ai.algorithms.efficient_zero import train_efficient_zero

# 常量定义
DEFAULT_GAME = 'doudizhu'
DEFAULT_ALGO = 'efficient_zero'
DEFAULT_CONFIG_PATH = 'configs/doudizhu/efficient_zero_config.yaml'
OPTIMIZED_CONFIG_PATH = 'configs/training/optimized.yaml'


class SimpleMonitor:
    """简化的训练监控器"""

    def __init__(self):
        """初始化监控器"""
        self.metrics_history = []

    def log_metrics(self, metrics: Dict[str, float], step: int, prefix: str = ""):
        """记录训练指标"""
        # 简化的指标记录
        self.metrics_history.append({
            'step': step,
            'prefix': prefix,
            'metrics': metrics.copy()
        })


class OptimizedTrainingSystem:
    """集成优化训练系统"""

    def __init__(self):
        """初始化训练系统"""
        self.project_root = project_root
        self.logger = None
        self.monitor = None
        self.algorithm = None
        self.trainer = None
        self.evaluator = None

        # 优化组件
        self.performance_monitor = None
        self.performance_optimizer = None
        self.mcts = None
        self.optimized_dataloader = None

        print(f"优化训练系统初始化")
        print(f"   所有组件已加载并验证")
        print(f"   优化组件已准备就绪")

    def setup_logging(self, log_level: str = "INFO", log_dir: str = "logs") -> None:
        """设置日志系统"""
        # 使用基础日志系统 (简化版本)
        log_path = Path(log_dir)
        log_path.mkdir(parents=True, exist_ok=True)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        log_file = log_path / f'optimized_training_{timestamp}.log'

        logging.basicConfig(
            level=getattr(logging, log_level.upper()),
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        self.logger.info("优化训练系统日志初始化完成")

    def detect_device(self) -> str:
        """自动检测可用设备"""
        try:
            import torch
            if torch.cuda.is_available():
                device_count = torch.cuda.device_count()
                device = f"cuda:0"
                self.logger.info(f"检测到 {device_count} 个CUDA设备，使用: {device}")
                return device
            else:
                self.logger.warning("未检测到CUDA设备，使用CPU")
                return "cpu"
        except ImportError:
            self.logger.warning("PyTorch未安装，使用CPU")
            return "cpu"

    def load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        # 尝试加载优化配置
        optimized_config_path = os.path.join(self.project_root, OPTIMIZED_CONFIG_PATH)
        if os.path.exists(optimized_config_path):
            self.logger.info(f"使用优化配置: {OPTIMIZED_CONFIG_PATH}")
            config_path = optimized_config_path
        else:
            # 使用默认配置
            default_config_path = os.path.join(self.project_root, config_path)
            if os.path.exists(default_config_path):
                self.logger.info(f"使用默认配置: {config_path}")
                config_path = default_config_path
            else:
                # 配置文件不存在，立即失败
                raise FileNotFoundError(f"配置文件不存在: {config_path}，无法继续训练")

        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.endswith('.yaml') or config_path.endswith('.yml'):
                    config = yaml.safe_load(f)
                else:
                    # 不支持非YAML配置文件，立即失败
                    raise ValueError(f"不支持的配置文件格式: {config_path}，只支持YAML格式")

            self.logger.info("配置文件加载成功")
            return config
        except Exception as e:
            self.logger.error(f"配置文件加载失败: {e}")
            # 不使用默认配置，立即抛出异常
            raise RuntimeError(f"配置文件加载失败，无法继续训练: {e}") from e

    def get_default_config(self) -> Dict[str, Any]:
        """获取默认优化配置"""
        return {
            'game': DEFAULT_GAME,
            'algorithm': DEFAULT_ALGO,
            'device': 'auto',
            'training': {
                'epochs': 1000,
                'batch_size': 256,  # 优化：从128提升至256
                'learning_rate': 0.0005,  # 优化：精细调整
                'num_simulations': 100,  # 优化：从50提升至100
                'save_frequency': 100,
                'eval_frequency': 50,
                'log_frequency': 10
            },
            'multi_agent': {
                'farmer_cooperation': {
                    'enabled': True,
                    'cooperation_weight': 0.8,  # 优化：从0.7提升
                    'team_reward_weight': 0.9   # 优化：从0.8提升
                }
            },
            'distributed': {
                'enabled': False,
                'num_workers': 4
            },
            'monitoring': {
                'enabled': True,
                'tensorboard': {'enabled': True},
                'wandb': {'enabled': False}
            }
        }

    def run_training(self, config: Dict[str, Any], device: str, resume: bool = False) -> bool:
        """运行优化训练"""
        try:
            self.logger.info("=" * 60)
            self.logger.info("🎯 开始斗地主AI优化训练")
            self.logger.info("=" * 60)

            # 显示优化特性
            self._show_optimization_features(config)

            # 设置随机种子
            self._set_random_seed(config.get('seed', 42))

            # 初始化优化组件
            self._initialize_optimization_components(config, device)

            # 启动性能监控
            if self.performance_monitor:
                start_global_monitoring()
                self.logger.info("全局性能监控已启动")

            # 执行优化训练
            success = self._execute_optimized_training_loop(config, device, resume)

            if success:
                self.logger.info("训练完成！")
                # 生成性能报告
                if self.performance_monitor:
                    report = self.performance_monitor.generate_performance_report()
                    self.logger.info(f"性能报告:\n{report}")
                return True
            else:
                self.logger.error("训练失败")
                return False

        except Exception as e:
            self.logger.error(f"训练过程中发生错误: {e}")
            self.logger.error(traceback.format_exc())
            # 不返回False，直接抛出异常
            raise RuntimeError(f"训练过程失败: {e}") from e
        finally:
            # 停止性能监控
            if self.performance_monitor:
                stop_global_monitoring()
                self.logger.info("性能监控已停止")

    def _show_optimization_features(self, config: Dict[str, Any]) -> None:
        """显示优化特性"""
        self.logger.info("🚀 启用的优化特性:")

        training_config = config.get('training', {})
        multi_agent_config = config.get('multi_agent', {})

        features = [
            f"MCTS模拟次数: {training_config.get('num_simulations', 50)} (优化: 50→100+)",
            f"批次大小: {training_config.get('batch_size', 128)} (优化: 128→256)",
            f"学习率: {training_config.get('learning_rate', 0.001)} (优化: 精细调整)",
            f"农民协作权重: {multi_agent_config.get('farmer_cooperation', {}).get('cooperation_weight', 0.7)} (优化: 0.7→0.8)",
            f"团队奖励权重: {multi_agent_config.get('farmer_cooperation', {}).get('team_reward_weight', 0.8)} (优化: 0.8→0.9)",
            f"监控系统: {'启用' if config.get('monitoring', {}).get('enabled', True) else '禁用'}",
            f"分布式训练: {'启用' if config.get('distributed', {}).get('enabled', False) else '禁用'}"
        ]

        for feature in features:
            self.logger.info(f"   {feature}")

    def _set_random_seed(self, seed: int) -> None:
        """设置随机种子"""
        random.seed(seed)
        np.random.seed(seed)

        try:
            import torch
            torch.manual_seed(seed)
            if torch.cuda.is_available():
                torch.cuda.manual_seed_all(seed)
        except ImportError:
            pass

        self.logger.info(f"随机种子设置为: {seed}")

    def _initialize_optimization_components(self, config: Dict[str, Any], device: str) -> None:
        """初始化优化组件"""
        self.logger.info("初始化优化组件...")

        # 初始化性能监控器
        self.performance_monitor = get_global_monitor()

        # 初始化MCTS
        mcts_config = config.get('mcts', {})
        self.mcts = MCTS(
            num_simulations=mcts_config.get('num_simulations', 120),
            discount=mcts_config.get('discount', 0.997),
            dirichlet_alpha=mcts_config.get('dirichlet_alpha', 0.3),
            exploration_fraction=mcts_config.get('exploration_fraction', 0.25),
            pb_c_base=mcts_config.get('pb_c_base', 19652),
            pb_c_init=mcts_config.get('c_puct', 1.25),  # 配置文件中使用c_puct
            root_exploration_noise=mcts_config.get('root_exploration_noise', True),
            use_belief_state=mcts_config.get('use_belief_state', False),
            use_information_value=mcts_config.get('use_information_value', False)
        )
        self.logger.info(f"MCTS初始化完成: {mcts_config.get('num_simulations', 120)}次模拟，日志增强功能已启用")

        # 初始化优化数据加载器配置
        data_config = config.get('data', {})
        training_config = config.get('training', {})

        # 安全获取batch_size，支持整数和字典格式
        batch_size_config = training_config.get('batch_size', 256)
        if isinstance(batch_size_config, dict):
            batch_size = batch_size_config.get('base', 320)
        else:
            batch_size = batch_size_config

        self.data_config = {
            'batch_size': batch_size,
            'num_workers': data_config.get('num_workers', 16),
            'pin_memory': data_config.get('pin_memory', True),
            'prefetch_factor': data_config.get('prefetch_factor', 8),
            'persistent_workers': data_config.get('persistent_workers', True),
            'cache_size_gb': data_config.get('cache_size_gb', 12),
            'use_memory_map': data_config.get('use_memory_map', True),
            'compression': data_config.get('compression', 'lz4')
        }
        self.logger.info(f"数据加载器配置: batch_size={self.data_config['batch_size']}, workers={self.data_config['num_workers']}")

        self.logger.info("所有优化组件初始化完成")

    def _execute_optimized_training_loop(self, config: Dict[str, Any], device: str, resume: bool) -> bool:
        """执行优化训练循环"""
        self.logger.info("开始优化训练循环")
        self.logger.info(f"使用设备: {device}")
        if resume:
            self.logger.info("恢复训练模式")

        # 记录优化配置
        self._log_optimization_config(config)

        # 调用真实训练模块 - 必须成功，否则立即失败
        self.logger.info("开始真实训练...")
        try:
            # 构建训练配置，确保设备格式正确
            train_config = config.copy()
            # 确保设备配置正确传递，避免"auto"字符串
            if device.startswith('cuda'):
                train_config['device'] = device
            elif device == 'cpu':
                train_config['device'] = 'cpu'
            else:
                train_config['device'] = 'cuda:0'  # 默认使用第一个GPU

            train_config['resume'] = resume

            # 修复配置兼容性问题
            self._fix_config_compatibility(train_config)

            # 添加优化组件配置
            train_config['optimization'] = {
                'use_mcts': True,
                'use_optimized_dataloader': True,
                'use_performance_monitor': True,
                'mcts_instance': self.mcts,
                'data_config': self.data_config
            }

            self.logger.info(f"调用真实训练模块，设备: {train_config['device']}")
            # 调用真实的训练函数
            result = train_efficient_zero(DEFAULT_GAME, train_config)

            if result != 0:
                raise RuntimeError(f"训练失败，返回码: {result}")

            return True
        except Exception as e:
            self.logger.error(f"训练模块调用失败: {e}")
            # 不允许回退到模拟模式，立即抛出异常
            raise RuntimeError(f"训练失败，无法继续: {e}") from e

    def _log_optimization_config(self, config: Dict[str, Any]) -> None:
        """记录优化配置"""
        self.logger.info("=" * 40)
        self.logger.info("优化配置详情:")

        # MCTS优化
        mcts_config = config.get('mcts', {})
        self.logger.info(f"MCTS优化:")
        self.logger.info(f"  模拟次数: {mcts_config.get('num_simulations', 120)}")
        self.logger.info(f"  并行线程: {mcts_config.get('parallel_threads', 6)}")
        self.logger.info(f"  批量推理: {mcts_config.get('batch_size_inference', 24)}")

        # 数据流水线优化
        self.logger.info(f"数据流水线优化:")
        self.logger.info(f"  批次大小: {self.data_config['batch_size']}")
        self.logger.info(f"  工作线程: {self.data_config['num_workers']}")
        self.logger.info(f"  预取因子: {self.data_config['prefetch_factor']}")
        self.logger.info(f"  缓存大小: {self.data_config['cache_size_gb']}GB")

        # 训练优化
        training_config = config.get('training', {})
        self.logger.info(f"训练优化:")

        # 安全获取learning_rate
        lr_config = training_config.get('learning_rate', 0.001)
        if isinstance(lr_config, dict):
            lr_value = lr_config.get('initial', 0.0008)
        else:
            lr_value = lr_config
        self.logger.info(f"  学习率: {lr_value}")

        # 安全获取gradient配置
        gradient_config = training_config.get('gradient', {})
        if isinstance(gradient_config, dict):
            clip_norm = gradient_config.get('clip_norm', 5.0)
        else:
            clip_norm = 5.0
        self.logger.info(f"  梯度裁剪: {clip_norm}")

        # 安全获取mixed_precision配置
        device_config = config.get('device', {})
        if isinstance(device_config, dict):
            mixed_precision = device_config.get('mixed_precision', True)
        else:
            mixed_precision = True
        self.logger.info(f"  混合精度: {mixed_precision}")

        self.logger.info("=" * 40)

    def _fix_config_compatibility(self, config: Dict[str, Any]) -> None:
        """修复配置兼容性问题"""
        # 修复learning_rate配置
        training_config = config.get('training', {})
        if 'learning_rate' in training_config:
            lr_config = training_config['learning_rate']
            if isinstance(lr_config, dict):
                # 如果是字典，提取initial值
                training_config['learning_rate'] = lr_config.get('initial', 0.0008)
                self.logger.info(f"修复learning_rate配置: {lr_config} -> {training_config['learning_rate']}")

        # 修复batch_size配置
        if 'batch_size' in training_config:
            batch_config = training_config['batch_size']
            if isinstance(batch_config, dict):
                # 如果是字典，提取base值
                training_config['batch_size'] = batch_config.get('base', 320)
                self.logger.info(f"修复batch_size配置: {batch_config} -> {training_config['batch_size']}")

        # 修复MCTS配置
        if 'mcts' in config:
            mcts_config = config['mcts']
            if 'num_simulations' in mcts_config:
                training_config['num_simulations'] = mcts_config['num_simulations']
                self.logger.info(f"应用MCTS模拟次数: {mcts_config['num_simulations']}")

    def _execute_training_loop(self, config: Dict[str, Any], device: str, resume: bool) -> bool:
        """执行训练循环"""
        self.logger.info("开始训练循环")
        self.logger.info(f"使用设备: {device}")
        if resume:
            self.logger.info("恢复训练模式")

        # 调用真实训练模块 - 必须成功，否则立即失败
        self.logger.info("开始真实训练...")
        try:
            # 构建训练配置，确保设备格式正确
            train_config = config.copy()
            # 确保设备配置正确传递，避免"auto"字符串
            if device.startswith('cuda'):
                train_config['device'] = device
            elif device == 'cpu':
                train_config['device'] = 'cpu'
            else:
                train_config['device'] = 'cuda:0'  # 默认使用第一个GPU

            train_config['resume'] = resume

            self.logger.info(f"调用真实训练模块，设备: {train_config['device']}")
            # 调用真实的训练函数
            result = train_efficient_zero(DEFAULT_GAME, train_config)

            if result != 0:
                raise RuntimeError(f"训练失败，返回码: {result}")

            return True
        except Exception as e:
            self.logger.error(f"训练模块调用失败: {e}")
            # 不允许回退到模拟模式，立即抛出异常
            raise RuntimeError(f"训练失败，无法继续: {e}") from e


def parse_arguments() -> argparse.Namespace:
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='斗地主AI优化训练系统',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main_training.py                                    # 使用默认配置
  python main_training.py --device cuda:0                   # 指定GPU设备
  python main_training.py --resume                          # 恢复训练
  python main_training.py --config configs/training/custom.yaml  # 自定义配置
        """
    )

    parser.add_argument(
        '--config', '-c',
        type=str,
        default=DEFAULT_CONFIG_PATH,
        help=f'配置文件路径 (默认: {DEFAULT_CONFIG_PATH})'
    )

    parser.add_argument(
        '--device', '-d',
        type=str,
        default='auto',
        help='计算设备 (auto, cpu, cuda:0, etc.) (默认: auto)'
    )

    parser.add_argument(
        '--resume', '-r',
        action='store_true',
        help='恢复之前的训练'
    )

    parser.add_argument(
        '--log-level',
        type=str,
        default='INFO',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        help='日志级别 (默认: INFO)'
    )

    parser.add_argument(
        '--log-dir',
        type=str,
        default='logs',
        help='日志目录 (默认: logs)'
    )

    return parser.parse_args()


def main():
    """主函数"""
    print("斗地主AI优化训练系统")
    print("=" * 50)

    # 解析命令行参数
    args = parse_arguments()

    # 初始化训练系统
    training_system = OptimizedTrainingSystem()

    # 设置日志
    training_system.setup_logging(args.log_level, args.log_dir)

    try:
        # 加载配置
        config = training_system.load_config(args.config)

        # 检测设备
        if args.device == 'auto':
            device = training_system.detect_device()
        else:
            device = args.device

        # 运行训练 - 如果失败会直接抛出异常
        training_system.run_training(config, device, args.resume)
        print("\n训练成功完成！")
        sys.exit(0)

    except KeyboardInterrupt:
        print("\n用户中断训练")
        sys.exit(0)
    except Exception as e:
        print(f"\n系统错误: {e}")
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()