"""
王炸处理模块

提供处理王炸残局的专门决策逻辑。
"""

from typing import List, Optional, Tuple, Dict, Any

from cardgame_ai.core.base import State, Action
from cardgame_ai.games.doudizhu.state import DouDizhuState
from cardgame_ai.games.doudizhu.card import Card, CardRank, CardSuit
from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType


def is_king_bomb_scenario(state: State) -> bool:
    """
    检测是否是王炸残局场景
    
    王炸残局场景是指有玩家手中有大小王，且游戏处于残局状态。
    
    Args:
        state: 游戏状态
        
    Returns:
        bool: 是否是王炸残局场景
    """
    if not isinstance(state, DouDizhuState):
        return False
    
    # 检查是否是残局（至少有一个玩家手牌数量较少）
    is_endgame = False
    for hand in state.hands:
        if 0 < len(hand) <= 5:
            is_endgame = True
            break
    
    if not is_endgame:
        return False
    
    # 检查当前玩家是否有大小王
    current_player = state.current_player
    hand = state.hands[current_player]
    
    has_small_joker = False
    has_big_joker = False
    
    for card in hand:
        if card.rank == CardRank.SMALL_JOKER:
            has_small_joker = True
        elif card.rank == CardRank.BIG_JOKER:
            has_big_joker = True
    
    # 如果有大小王，则是王炸残局场景
    return has_small_joker and has_big_joker


def handle_king_bomb_endgame(state: State, player_id: int) -> Optional[Action]:
    """
    处理王炸残局
    
    在王炸残局中，根据特定规则决定是否使用王炸。
    
    Args:
        state: 游戏状态
        player_id: 玩家ID
        
    Returns:
        Optional[Action]: 决策动作，如果不适用则返回None
    """
    if not isinstance(state, DouDizhuState) or state.current_player != player_id:
        return None
    
    # 检查是否是王炸残局场景
    if not is_king_bomb_scenario(state):
        return None
    
    # 获取当前玩家的手牌
    hand = state.hands[player_id]
    
    # 找出大小王
    small_joker = None
    big_joker = None
    
    for card in hand:
        if card.rank == CardRank.SMALL_JOKER:
            small_joker = card
        elif card.rank == CardRank.BIG_JOKER:
            big_joker = card
    
    # 如果没有大小王，返回None
    if not small_joker or not big_joker:
        return None
    
    # 决策逻辑：
    # 1. 如果是地主，且手牌数量为2（只有大小王），直接出王炸
    # 2. 如果是农民，且队友手牌数量为0或1，考虑出王炸
    # 3. 如果对手出了较大的牌（如2或炸弹），考虑出王炸
    # 4. 如果自己手牌数量为2，且包含大小王，考虑出王炸
    
    # 创建王炸牌组
    king_bomb = CardGroup([small_joker, big_joker])
    
    # 检查是否是地主
    is_landlord = (player_id == state.landlord)
    
    # 如果是地主，且手牌数量为2（只有大小王），直接出王炸
    if is_landlord and len(hand) == 2:
        return king_bomb
    
    # 如果是农民，检查队友情况
    if not is_landlord:
        # 找出队友
        teammate_id = (player_id + 1) % 3 if (player_id + 1) % 3 != state.landlord else (player_id + 2) % 3
        
        # 如果队友手牌数量为0或1，考虑出王炸
        if len(state.hands[teammate_id]) <= 1:
            # 如果自己手牌数量为2（只有大小王），直接出王炸
            if len(hand) == 2:
                return king_bomb
            # 如果自己手牌数量较少，且没有其他更好的出牌选择，考虑出王炸
            elif len(hand) <= 4:
                # 检查是否有其他更好的出牌选择
                if state.last_move and state.last_move.card_type != CardGroupType.PASS:
                    # 如果上家出的是大牌（如2或炸弹），出王炸
                    if state.last_move.card_type == CardGroupType.BOMB or \
                       (state.last_move.card_type == CardGroupType.SINGLE and state.last_move.cards[0].rank == CardRank.TWO) or \
                       (state.last_move.card_type == CardGroupType.PAIR and state.last_move.cards[0].rank == CardRank.TWO):
                        return king_bomb
    
    # 如果对手出了较大的牌（如2或炸弹），考虑出王炸
    if state.last_move and state.last_move.card_type != CardGroupType.PASS:
        # 如果上家出的是炸弹，出王炸
        if state.last_move.card_type == CardGroupType.BOMB:
            return king_bomb
        
        # 如果上家出的是大牌（如2），且自己手牌数量较少，考虑出王炸
        if len(hand) <= 4 and \
           ((state.last_move.card_type == CardGroupType.SINGLE and state.last_move.cards[0].rank == CardRank.TWO) or \
            (state.last_move.card_type == CardGroupType.PAIR and state.last_move.cards[0].rank == CardRank.TWO)):
            return king_bomb
    
    # 如果自己手牌数量为2，且包含大小王，考虑出王炸
    if len(hand) == 2:
        return king_bomb
    
    # 如果没有特殊情况，返回None，让一般决策逻辑处理
    return None
