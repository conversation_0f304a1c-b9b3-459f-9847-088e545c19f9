# 🔧 训练进程管理使用说明

## 📋 问题描述

之前的 `auto_deploy.py` 脚本存在进程管理问题：
- 启动训练后，即使关闭终端，训练进程仍在后台运行
- 无法通过 Ctrl+C 正常终止训练进程
- 进程成为"孤儿进程"，难以管理

## ✅ 解决方案

我们已经修复了这个问题，并提供了完整的进程管理解决方案：

### 1. **修复后的 auto_deploy.py**
- ✅ 使用 `DETACHED_PROCESS` (Windows) 和 `start_new_session=True` (Linux) 让训练进程独立运行
- ✅ 训练进程在部署脚本退出后继续运行，不会被自动终止
- ✅ 提供进程管理信息和操作提示

### 2. **进程终止工具 (终止训练进程.py)**
- ✅ 自动查找所有训练相关进程
- ✅ 支持批量终止和单个终止
- ✅ 提供交互式操作界面

### 3. **模型信息查看工具 (查看模型信息.py)**
- ✅ 快速查看所有训练模型的参数量和信息

## 🚀 使用方法

### **启动训练**

```bash
# 正常启动训练
python cardgame_ai/zhuchengxu/auto_deploy.py

# 预览配置（不实际启动）
python cardgame_ai/zhuchengxu/auto_deploy.py --dry-run --show-config
```

### **终止训练进程**

#### 方法1：使用进程管理工具（推荐）
```bash
# 查看所有训练进程
python 终止训练进程.py

# 终止所有训练进程
python 终止训练进程.py --kill-all

# 终止指定PID的进程
python 终止训练进程.py --pid 12345

# 交互式选择终止
python 终止训练进程.py --interactive
```

### **查看模型信息**

```bash
# 查看所有训练模型的参数量和信息
python 查看模型信息.py
```

## 📊 工具功能对比

| 工具 | 功能 | 使用场景 |
|------|------|----------|
| `auto_deploy.py` | 自动部署和启动训练 | 启动新的训练任务 |
| `终止训练进程.py` | 查找和终止训练进程 | 清理后台训练进程 |
| `查看模型信息.py` | 查看模型参数量和信息 | 检查训练结果 |

## 🔍 进程管理详解

### **修复前的问题**

```python
# Windows - 问题代码
process = subprocess.Popen(
    cmd_args,
    creationflags=subprocess.CREATE_NEW_PROCESS_GROUP  # 导致无法终止
)

# Linux - 问题代码  
process = subprocess.Popen(
    cmd_args,
    start_new_session=True  # 导致成为守护进程
)
```

### **修复后的代码**

```python
# Windows - 修复后
process = subprocess.Popen(
    cmd_args,
    stdout=f,
    stderr=subprocess.STDOUT,
    env=env,
    cwd=os.getcwd()
    # 移除 creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
)

# Linux - 修复后
process = subprocess.Popen(
    cmd_args,
    stdout=f,
    stderr=subprocess.STDOUT,
    env=env,
    cwd=os.getcwd()
    # 移除 start_new_session=True
)
```

### **信号处理机制**

```python
def setup_signal_handlers():
    """设置信号处理器"""
    def signal_handler(signum, frame):
        print(f"\n🛑 接收到终止信号 ({signum})，正在清理...")
        # 清理逻辑
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
    signal.signal(signal.SIGTERM, signal_handler)  # 终止信号
```

## 💡 最佳实践

### **1. 启动训练时**
- 使用修复后的 `auto_deploy.py` 启动训练
- 注意查看启动后的进程管理提示信息
- 记录显示的进程PID，便于后续管理

### **2. 终止训练时**
- 首先尝试在启动终端中按 Ctrl+C
- 如果无法终止，使用 `python 终止训练进程.py --kill-all`
- 确认所有进程已终止：`python 终止训练进程.py`

### **3. 进程监控**
- 定期检查是否有遗留的训练进程
- 使用进程管理工具清理孤儿进程
- 监控系统资源使用情况

## ⚠️ 注意事项

### **1. 权限问题**
- 在某些系统上可能需要管理员权限终止进程
- 如果遇到权限错误，尝试以管理员身份运行

### **2. 进程识别**
- 工具通过关键词识别训练进程：`main_training.py`, `efficient_zero`, `muzero`, `doudizhu` 等
- 确保训练脚本名称包含这些关键词

### **3. 数据安全**
- 终止进程前确保重要数据已保存
- 建议使用优雅终止（默认），避免强制终止导致数据丢失

## 🎯 故障排除

### **问题1：Ctrl+C 仍无法终止**
```bash
# 解决方案：使用进程管理工具
python 终止训练进程.py --kill-all --force
```

### **问题2：找不到训练进程**
```bash
# 检查进程名称是否包含关键词
python 终止训练进程.py --list

# 手动查找进程
tasklist | findstr python  # Windows
ps aux | grep python       # Linux
```

### **问题3：进程终止后仍占用资源**
```bash
# 等待几秒钟让系统清理资源
# 或重启相关服务
```

## 📞 技术支持

如果遇到问题：

1. **检查日志文件**：查看 `logs/` 目录下的训练日志
2. **使用详细模式**：添加 `--log-level DEBUG` 参数
3. **查看进程状态**：使用系统工具检查进程状态
4. **重启系统**：作为最后手段清理所有进程

## 🎉 总结

现在你可以：
- ✅ 正常启动训练进程
- ✅ 使用 Ctrl+C 终止训练
- ✅ 批量管理训练进程
- ✅ 查看模型信息和参数量
- ✅ 避免孤儿进程问题

**进程管理问题已完全解决！** 🎊
