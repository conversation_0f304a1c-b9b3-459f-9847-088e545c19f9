#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
经验蒸馏模块

实现经验蒸馏，将专家经验转化为学习信号，加速模型的学习过程。
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import logging
import os
import random
from typing import List, Dict, Any, Tuple, Optional, Union, Callable
from collections import deque, defaultdict
import time

from cardgame_ai.core.base import Experience, Batch, State, Action
from cardgame_ai.utils.model_saver import ModelSaver
from cardgame_ai.core.agent import Agent

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ModelRegistry:
    """
    模型注册表

    管理历史模型，支持模型版本控制和检索。
    """

    def __init__(self, max_models: int = 5, registry_path: str = "model_registry"):
        """
        初始化模型注册表

        Args:
            max_models (int, optional): 最大保存模型数量. Defaults to 5.
            registry_path (str, optional): 注册表路径. Defaults to "model_registry".
        """
        self.max_models = max_models
        self.registry_path = registry_path
        self.models = []  # 存储模型路径和元数据

        # 创建注册表目录
        os.makedirs(registry_path, exist_ok=True)

        # 加载现有模型信息
        self._load_registry()

    def _load_registry(self) -> None:
        """
        加载注册表信息
        """
        registry_file = os.path.join(self.registry_path, "registry.pkl")
        if os.path.exists(registry_file):
            try:
                self.models = ModelSaver.load_object(registry_file)
                logger.info(f"已加载模型注册表，包含 {len(self.models)} 个模型")
            except Exception as e:
                logger.error(f"加载模型注册表失败: {e}")
                self.models = []

    def _save_registry(self) -> None:
        """
        保存注册表信息
        """
        registry_file = os.path.join(self.registry_path, "registry.pkl")
        try:
            ModelSaver.save_object(self.models, registry_file)
            logger.info(f"已保存模型注册表，包含 {len(self.models)} 个模型")
        except Exception as e:
            logger.error(f"保存模型注册表失败: {e}")

    def register_model(self, model_path: str, metadata: Dict[str, Any]) -> None:
        """
        注册模型

        Args:
            model_path (str): 模型路径
            metadata (Dict[str, Any]): 模型元数据，如算法类型、训练步数、性能指标等
        """
        # 添加时间戳
        metadata["timestamp"] = time.time()

        # 添加到注册表
        self.models.append({
            "path": model_path,
            "metadata": metadata
        })

        # 如果超过最大模型数量，删除最旧的模型
        if len(self.models) > self.max_models:
            oldest_model = self.models.pop(0)
            logger.info(f"删除最旧的模型: {oldest_model['path']}")

        # 保存注册表
        self._save_registry()

    def get_models(self, filter_func: Optional[Callable[[Dict[str, Any]], bool]] = None) -> List[Dict[str, Any]]:
        """
        获取模型列表

        Args:
            filter_func (Optional[Callable[[Dict[str, Any]], bool]], optional): 过滤函数. Defaults to None.

        Returns:
            List[Dict[str, Any]]: 模型列表
        """
        if filter_func is None:
            return self.models
        else:
            return [model for model in self.models if filter_func(model["metadata"])]

    def get_latest_model(self) -> Optional[Dict[str, Any]]:
        """
        获取最新模型

        Returns:
            Optional[Dict[str, Any]]: 最新模型信息
        """
        if not self.models:
            return None

        # 按时间戳排序
        sorted_models = sorted(self.models, key=lambda x: x["metadata"].get("timestamp", 0), reverse=True)
        return sorted_models[0]

    def get_best_model(self, metric_key: str, higher_better: bool = True) -> Optional[Dict[str, Any]]:
        """
        获取最佳模型

        Args:
            metric_key (str): 指标键名
            higher_better (bool, optional): 指标值越高越好. Defaults to True.

        Returns:
            Optional[Dict[str, Any]]: 最佳模型信息
        """
        if not self.models:
            return None

        # 过滤有指标的模型
        valid_models = [model for model in self.models if metric_key in model["metadata"]]
        if not valid_models:
            return None

        # 按指标排序
        if higher_better:
            sorted_models = sorted(valid_models, key=lambda x: x["metadata"][metric_key], reverse=True)
        else:
            sorted_models = sorted(valid_models, key=lambda x: x["metadata"][metric_key])

        return sorted_models[0]


class ExperienceDistillation:
    """
    经验蒸馏
    
    将专家经验转化为学习信号，加速模型的学习过程。通过模仿学习和监督信号，
    使模型能够从高质量经验中快速获取知识。
    """
    
    def __init__(
        self,
        student_model: nn.Module,
        expert_experiences: Optional[List[Experience]] = None,
        distillation_type: str = "behavioral",
        loss_weights: Optional[Dict[str, float]] = None,
        device: str = 'cuda' if torch.cuda.is_available() else 'cpu'
    ):
        """
        初始化经验蒸馏
        
        Args:
            student_model: 学生模型
            expert_experiences: 专家经验，可选
            distillation_type: 蒸馏类型，可以是 "behavioral"(行为克隆) 或 "reward"(奖励指导)
            loss_weights: 损失权重字典，可选
            device: 计算设备
        """
        self.student_model = student_model.to(device)
        self.expert_experiences = expert_experiences if expert_experiences else []
        self.distillation_type = distillation_type
        self.device = device
        
        # 设置默认损失权重
        default_weights = {
            "policy_weight": 1.0,
            "value_weight": 0.5,
            "reward_weight": 0.3,
            "regularization_weight": 0.01
        }
        self.loss_weights = default_weights
        if loss_weights:
            self.loss_weights.update(loss_weights)
        
        # 优化器
        self.optimizer = torch.optim.Adam(self.student_model.parameters(), lr=0.001)
        
        # 经验缓冲区
        self.buffer = deque(maxlen=10000)
        if expert_experiences:
            self.buffer.extend(expert_experiences)
        
        # 统计信息
        self.stats = {
            "distillation_steps": 0,
            "policy_losses": [],
            "value_losses": [],
            "reward_losses": [],
            "total_losses": []
        }
        
        logger.info(f"经验蒸馏初始化完成，蒸馏类型: {distillation_type}")
    
    def add_experiences(self, experiences: List[Experience]) -> None:
        """
        添加专家经验
        
        Args:
            experiences: 专家经验列表
        """
        self.buffer.extend(experiences)
        logger.info(f"添加了 {len(experiences)} 条专家经验，缓冲区大小: {len(self.buffer)}")
    
    def distill(self, batch_size: int = 64, steps: int = 1) -> Dict[str, float]:
        """
        执行经验蒸馏
        
        Args:
            batch_size: 批次大小
            steps: 蒸馏步数
            
        Returns:
            蒸馏指标
        """
        if len(self.buffer) < batch_size:
            logger.warning(f"经验不足，当前经验数: {len(self.buffer)}，需要: {batch_size}")
            return {"total_loss": 0.0}
        
        total_metrics = defaultdict(float)
        
        for _ in range(steps):
            # 采样批次
            batch_indices = np.random.choice(len(self.buffer), batch_size, replace=False)
            batch = [self.buffer[i] for i in batch_indices]
            
            # 执行蒸馏
            metrics = self._distill_batch(batch)
            
            # 累积指标
            for k, v in metrics.items():
                total_metrics[k] += v
            
            # 更新统计信息
            self.stats["distillation_steps"] += 1
            self.stats["policy_losses"].append(metrics.get("policy_loss", 0.0))
            self.stats["value_losses"].append(metrics.get("value_loss", 0.0))
            self.stats["reward_losses"].append(metrics.get("reward_loss", 0.0))
            self.stats["total_losses"].append(metrics.get("total_loss", 0.0))
        
        # 计算平均指标
        for k in total_metrics:
            total_metrics[k] /= steps
        
        return total_metrics
    
    def _distill_batch(self, batch: List[Experience]) -> Dict[str, float]:
        """
        蒸馏单个批次
        
        Args:
            batch: 经验批次
            
        Returns:
            蒸馏指标
        """
        self.student_model.train()
        self.optimizer.zero_grad()
        
        # 提取状态、动作、奖励
        states = []
        actions = []
        rewards = []
        next_states = []
        dones = []
        
        for exp in batch:
            if isinstance(exp.state, State):
                states.append(exp.state.to_array())
            else:
                states.append(exp.state)
                
            if isinstance(exp.action, Action):
                actions.append(exp.action.to_index())
            else:
                actions.append(exp.action)
                
            rewards.append(exp.reward)
            
            if hasattr(exp, 'next_state') and exp.next_state is not None:
                if isinstance(exp.next_state, State):
                    next_states.append(exp.next_state.to_array())
                else:
                    next_states.append(exp.next_state)
            else:
                # 如果没有下一个状态，使用当前状态
                if isinstance(exp.state, State):
                    next_states.append(exp.state.to_array())
                else:
                    next_states.append(exp.state)
            
            if hasattr(exp, 'done'):
                dones.append(float(exp.done))
            else:
                dones.append(0.0)
        
        # 转换为张量
        states = torch.tensor(np.array(states), dtype=torch.float32, device=self.device)
        actions = torch.tensor(np.array(actions), dtype=torch.long, device=self.device)
        rewards = torch.tensor(np.array(rewards), dtype=torch.float32, device=self.device)
        next_states = torch.tensor(np.array(next_states), dtype=torch.float32, device=self.device)
        dones = torch.tensor(np.array(dones), dtype=torch.float32, device=self.device)
        
        # 前向传播
        outputs = self.student_model(states)
        
        # 根据模型输出类型提取策略和价值
        if isinstance(outputs, tuple) and len(outputs) >= 2:
            logits, values = outputs[0], outputs[1]
        elif isinstance(outputs, dict):
            logits = outputs.get('policy', outputs.get('logits', None))
            values = outputs.get('value', outputs.get('values', None))
        else:
            # 假设输出只是策略
            logits = outputs
            values = None
        
        # 计算损失
        metrics = {}
        total_loss = 0.0
        
        # 策略损失(行为克隆)
        if logits is not None:
            # 将动作转换为one-hot表示
            batch_size = actions.size(0)
            action_dim = logits.size(1)
            actions_one_hot = torch.zeros(batch_size, action_dim, device=self.device)
            actions_one_hot.scatter_(1, actions.unsqueeze(1), 1)
            
            # 计算交叉熵损失
            policy_loss = F.cross_entropy(logits, actions)
            metrics["policy_loss"] = policy_loss.item()
            total_loss += self.loss_weights["policy_weight"] * policy_loss
        
        # 价值损失
        if values is not None and self.distillation_type == "reward":
            # 使用奖励作为价值目标
            value_targets = rewards
            
            # 计算MSE损失
            value_loss = F.mse_loss(values.squeeze(), value_targets)
            metrics["value_loss"] = value_loss.item()
            total_loss += self.loss_weights["value_weight"] * value_loss
            
            # 计算next_state的价值
            if hasattr(self.student_model, 'forward_value'):
                next_values = self.student_model.forward_value(next_states).squeeze()
            else:
                with torch.no_grad():
                    next_outputs = self.student_model(next_states)
                    if isinstance(next_outputs, tuple) and len(next_outputs) >= 2:
                        next_values = next_outputs[1].squeeze()
                    elif isinstance(next_outputs, dict):
                        next_values = next_outputs.get('value', next_outputs.get('values', None))
                        if next_values is not None:
                            next_values = next_values.squeeze()
                    else:
                        next_values = None
            
            # 计算TD损失
            if next_values is not None:
                gamma = 0.99  # 折扣因子
                expected_values = rewards + gamma * next_values * (1.0 - dones)
                td_loss = F.mse_loss(values.squeeze(), expected_values.detach())
                metrics["td_loss"] = td_loss.item()
                total_loss += self.loss_weights["reward_weight"] * td_loss
        
        # 正则化损失
        l2_reg = torch.tensor(0.0, device=self.device)
        for param in self.student_model.parameters():
            l2_reg += torch.norm(param)
        metrics["reg_loss"] = l2_reg.item()
        total_loss += self.loss_weights["regularization_weight"] * l2_reg
        
        # 反向传播和优化
        total_loss.backward()
        self.optimizer.step()
        
        metrics["total_loss"] = total_loss.item()
        return metrics
    
    def evaluate(self, eval_states: Union[List[State], List[np.ndarray]], expert_policies: List[np.ndarray]) -> Dict[str, float]:
        """
        评估蒸馏效果
        
        Args:
            eval_states: 评估状态列表
            expert_policies: 专家策略列表
            
        Returns:
            评估指标
        """
        self.student_model.eval()
        
        # 转换状态为张量
        state_tensors = []
        for state in eval_states:
            if isinstance(state, State):
                state_tensors.append(state.to_array())
            else:
                state_tensors.append(state)
        
        states = torch.tensor(np.array(state_tensors), dtype=torch.float32, device=self.device)
        expert_policies = torch.tensor(np.array(expert_policies), dtype=torch.float32, device=self.device)
        
        # 获取学生模型的策略
        with torch.no_grad():
            outputs = self.student_model(states)
            
            if isinstance(outputs, tuple) and len(outputs) >= 2:
                logits = outputs[0]
            elif isinstance(outputs, dict):
                logits = outputs.get('policy', outputs.get('logits', None))
            else:
                logits = outputs
            
            student_policies = F.softmax(logits, dim=1)
        
        # 计算KL散度
        kl_div = F.kl_div(
            torch.log(student_policies + 1e-10),
            expert_policies,
            reduction='batchmean'
        ).item()
        
        # 计算策略差异(L1范数)
        policy_diff = torch.mean(torch.abs(student_policies - expert_policies)).item()
        
        return {
            "kl_divergence": kl_div,
            "policy_difference": policy_diff
        }
    
    def save(self, path: str) -> None:
        """
        保存模型和统计信息
        
        Args:
            path: 保存路径
        """
        state_dict = {
            "student_model": self.student_model.state_dict(),
            "optimizer": self.optimizer.state_dict(),
            "stats": self.stats,
            "config": {
                "distillation_type": self.distillation_type,
                "loss_weights": self.loss_weights
            }
        }
        torch.save(state_dict, path)
        logger.info(f"经验蒸馏模型已保存到 {path}")
    
    def load(self, path: str) -> None:
        """
        加载模型和统计信息
        
        Args:
            path: 加载路径
        """
        if not os.path.exists(path):
            logger.warning(f"模型文件不存在: {path}")
            return
            
        try:
            state_dict = torch.load(path, map_location=self.device)
            self.student_model.load_state_dict(state_dict["student_model"])
            self.optimizer.load_state_dict(state_dict["optimizer"])
            self.stats = state_dict["stats"]
            
            # 加载配置
            config = state_dict.get("config", {})
            self.distillation_type = config.get("distillation_type", self.distillation_type)
            if "loss_weights" in config:
                self.loss_weights.update(config["loss_weights"])
            
            logger.info(f"经验蒸馏模型已从 {path} 加载")
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            统计信息
        """
        # 计算最近损失的平均值
        avg_policy_loss = np.mean(self.stats["policy_losses"][-100:]) if self.stats["policy_losses"] else 0.0
        avg_value_loss = np.mean(self.stats["value_losses"][-100:]) if self.stats["value_losses"] else 0.0
        avg_reward_loss = np.mean(self.stats["reward_losses"][-100:]) if self.stats["reward_losses"] else 0.0
        avg_total_loss = np.mean(self.stats["total_losses"][-100:]) if self.stats["total_losses"] else 0.0
        
        return {
            "distillation_steps": self.stats["distillation_steps"],
            "avg_policy_loss": avg_policy_loss,
            "avg_value_loss": avg_value_loss,
            "avg_reward_loss": avg_reward_loss,
            "avg_total_loss": avg_total_loss,
            "buffer_size": len(self.buffer),
            "distillation_type": self.distillation_type,
            "loss_weights": self.loss_weights
        }


class DistillationTrainer:
    """
    蒸馏训练器

    将经验蒸馏功能集成到强化学习算法中。
    """

    def __init__(self, algorithm: Any, model_registry: ModelRegistry, distillation_config: Dict[str, Any] = None):
        """
        初始化蒸馏训练器

        Args:
            algorithm (Any): 强化学习算法
            model_registry (ModelRegistry): 模型注册表
            distillation_config (Dict[str, Any], optional): 蒸馏配置. Defaults to None.
        """
        self.algorithm = algorithm
        self.model_registry = model_registry

        # 默认配置
        default_config = {
            "enabled": True,
            "teacher_selection": "latest",  # "latest", "best", "random"
            "metric_key": "reward",  # 用于选择最佳模型的指标
            "higher_better": True,  # 指标值越高越好
            "distillation_temp": 2.0,
            "distillation_alpha": 0.5,
            "use_soft_targets": True,
            "distillation_interval": 1000,  # 蒸馏间隔（步数）
            "distillation_batch_size": 32,
            "distillation_epochs": 5
        }

        # 合并配置
        self.config = default_config.copy()
        if distillation_config:
            self.config.update(distillation_config)

        # 初始化统计信息
        self.stats = {
            "train_steps": 0,
            "distillation_count": 0,
            "last_distillation_step": 0
        }

        # 初始化蒸馏器
        self.distillation = None

    def train_step(self, experience: Experience) -> Dict[str, float]:
        """
        训练步骤

        Args:
            experience (Experience): 经验数据

        Returns:
            Dict[str, float]: 训练指标
        """
        # 更新统计信息
        self.stats["train_steps"] += 1

        # 正常训练
        train_stats = self.algorithm.update(experience)

        # 检查是否需要进行蒸馏
        if self._should_distill():
            distill_stats = self._perform_distillation()
            # 合并统计信息
            train_stats.update({f"distill_{k}": v for k, v in distill_stats.items()})

        return train_stats

    def train_batch(self, batch: Batch) -> Dict[str, float]:
        """
        批量训练

        Args:
            batch (Batch): 经验批次

        Returns:
            Dict[str, float]: 训练指标
        """
        # 更新统计信息
        self.stats["train_steps"] += len(batch)

        # 正常训练
        train_stats = self.algorithm.update(batch)

        # 检查是否需要进行蒸馏
        if self._should_distill():
            distill_stats = self._perform_distillation()
            # 合并统计信息
            train_stats.update({f"distill_{k}": v for k, v in distill_stats.items()})

        return train_stats

    def _should_distill(self) -> bool:
        """
        检查是否需要进行蒸馏

        Returns:
            bool: 是否需要进行蒸馏
        """
        # 如果禁用了蒸馏，直接返回否
        if not self.config["enabled"]:
            return False

        # 检查是否有可用的教师模型
        teacher_model = self._get_teacher_model()
        if teacher_model is None:
            return False

        # 检查是否达到蒸馏间隔
        steps_since_last_distill = self.stats["train_steps"] - self.stats["last_distillation_step"]
        return steps_since_last_distill >= self.config["distillation_interval"]

    def _get_teacher_model(self) -> Optional[Any]:
        """
        获取教师模型

        Returns:
            Optional[Any]: 教师模型
        """
        # 获取模型信息
        model_info = None

        if self.config["teacher_selection"] == "latest":
            model_info = self.model_registry.get_latest_model()
        elif self.config["teacher_selection"] == "best":
            model_info = self.model_registry.get_best_model(
                self.config["metric_key"],
                self.config["higher_better"]
            )
        elif self.config["teacher_selection"] == "random":
            models = self.model_registry.get_models()
            if models:
                model_info = random.choice(models)

        if model_info is None:
            return None

        # 加载模型
        try:
            # 创建一个新的算法实例
            teacher_model = self._create_algorithm_instance()

            # 加载模型
            teacher_model.load(model_info["path"])

            logger.info(f"加载教师模型: {model_info['path']}")
            return teacher_model
        except Exception as e:
            logger.error(f"加载教师模型失败: {e}")
            return None

    def _create_algorithm_instance(self) -> Any:
        """
        创建算法实例

        Returns:
            Any: 算法实例
        """
        # 获取算法类
        algorithm_class = self.algorithm.__class__

        # 获取初始化参数
        if hasattr(self.algorithm, "get_config"):
            config = self.algorithm.get_config()
            return algorithm_class(**config)
        else:
            # 如果没有get_config方法，尝试复制关键属性
            if hasattr(self.algorithm, "state_dim") and hasattr(self.algorithm, "action_dim"):
                # DQN类算法
                return algorithm_class(
                    state_dim=self.algorithm.state_dim,
                    action_dim=self.algorithm.action_dim
                )
            else:
                # 默认情况，直接创建无参数实例
                return algorithm_class()

    def _perform_distillation(self) -> Dict[str, float]:
        """
        执行蒸馏

        Returns:
            Dict[str, float]: 蒸馏指标
        """
        # 获取教师模型
        teacher_model = self._get_teacher_model()
        if teacher_model is None:
            logger.warning("没有可用的教师模型，跳过蒸馏")
            return {"skipped": 1.0}

        # 创建蒸馏器
        self.distillation = ExperienceDistillation(
            student_model=self.algorithm,
            expert_experiences=self.config["expert_experiences"],
            distillation_type=self.config["distillation_type"],
            loss_weights=self.config["loss_weights"],
            device=self.config["device"]
        )

        # 从经验回放缓冲区采样数据
        if hasattr(self.algorithm, "replay_buffer") and hasattr(self.algorithm.replay_buffer, "sample"):
            # 采样数据
            batch_size = min(self.config["distillation_batch_size"], len(self.algorithm.replay_buffer))
            if batch_size == 0:
                logger.warning("经验回放缓冲区为空，跳过蒸馏")
                return {"skipped": 1.0}

            # 执行多轮蒸馏
            total_stats = {}
            for epoch in range(self.config["distillation_epochs"]):
                # 采样数据
                try:
                    # 处理不同类型的回放缓冲区
                    if hasattr(self.algorithm.replay_buffer, "sample") and callable(self.algorithm.replay_buffer.sample):
                        if len(self.algorithm.replay_buffer.sample.__code__.co_varnames) > 1:
                            # PrioritizedReplayBuffer类型
                            batch, _, _ = self.algorithm.replay_buffer.sample(batch_size)
                        else:
                            # 标准ReplayBuffer类型
                            batch = self.algorithm.replay_buffer.sample(batch_size)
                    else:
                        logger.warning("无法从经验回放缓冲区采样数据，跳过蒸馏")
                        return {"skipped": 1.0}
                except Exception as e:
                    logger.error(f"采样数据失败: {e}")
                    return {"error": 1.0}

                # 执行蒸馏
                epoch_stats = self.distillation.distill(batch_size=batch_size)

                # 更新统计信息
                for k, v in epoch_stats.items():
                    if k in total_stats:
                        total_stats[k] += v
                    else:
                        total_stats[k] = v

            # 计算平均值
            for k in total_stats:
                total_stats[k] /= self.config["distillation_epochs"]

            # 更新统计信息
            self.stats["distillation_count"] += 1
            self.stats["last_distillation_step"] = self.stats["train_steps"]

            logger.info(f"完成第{self.stats['distillation_count']}次蒸馏，平均损失: {total_stats.get('total_loss', 0.0):.4f}")

            return total_stats
        else:
            logger.warning("算法没有经验回放缓冲区，跳过蒸馏")
            return {"skipped": 1.0}

    def save(self, path: str) -> None:
        """
        保存模型

        Args:
            path (str): 保存路径
        """
        # 保存算法模型
        self.algorithm.save(path)

        # 注册模型
        metadata = {
            "algorithm": self.algorithm.name if hasattr(self.algorithm, "name") else self.algorithm.__class__.__name__,
            "train_steps": self.stats["train_steps"],
            "distillation_count": self.stats["distillation_count"]
        }

        # 如果有评估指标，添加到元数据
        if hasattr(self.algorithm, "evaluate") and callable(self.algorithm.evaluate):
            try:
                evaluation = self.algorithm.evaluate()
                metadata.update(evaluation)
            except Exception as e:
                logger.error(f"评估模型失败: {e}")

        # 注册模型
        self.model_registry.register_model(path, metadata)

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        stats = self.stats.copy()

        # 添加蒸馏器统计信息
        if self.distillation is not None:
            distillation_stats = self.distillation.get_stats()
            stats["distillation"] = distillation_stats

        return stats

    def __str__(self) -> str:
        """
        转换为字符串表示

        Returns:
            str: 字符串表示
        """
        return f"DistillationTrainer(algorithm={self.algorithm.__class__.__name__}, " \
               f"steps={self.stats['train_steps']}, distillations={self.stats['distillation_count']})"