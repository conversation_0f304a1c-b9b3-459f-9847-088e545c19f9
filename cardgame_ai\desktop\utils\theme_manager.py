#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
主题管理器

负责管理UI主题和样式，仅支持浅色主题。
"""

import os
import logging
from typing import Dict, Any, Optional

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QObject, Signal, QFile, QTextStream

logger = logging.getLogger(__name__)


class ThemeManager(QObject):
    """主题管理器类"""

    # 主题变更信号
    theme_changed = Signal(str)

    # 主题颜色方案
    THEMES = {
        "light": {
            "main_bg": "#F5F7FA",  # 主背景色
            "nav_bg_start": "#E0E6ED",  # 侧边导航背景渐变起始色
            "nav_bg_end": "#D0DAE6",  # 侧边导航背景渐变结束色
            "accent": "#2980B9",  # 强调色
            "text_primary": "#2C3E50",  # 主要文本颜色
            "text_secondary": "#7F8C8D",  # 次要文本颜色
            "card_bg": "#FFFFFF",  # 卡片背景色
            "border": "#E0E6ED",  # 边框颜色
            "success": "#27AE60",  # 成功色
            "warning": "#F39C12",  # 警告色
            "error": "#E74C3C",  # 错误色
            "disabled": "#BDC3C7"  # 禁用色
        }
    }

    def __init__(self):
        """初始化主题管理器"""
        super().__init__()
        self._current_theme = "light"  # 默认使用浅色主题
        self._styles_dir = self._get_styles_dir()
        logger.info(f"主题管理器初始化，样式目录：{self._styles_dir}")

    def _get_styles_dir(self) -> str:
        """
        获取样式目录

        Returns:
            str: 样式目录路径
        """
        # 获取当前文件所在目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        # 获取项目根目录
        desktop_dir = os.path.dirname(current_dir)
        # 获取样式目录
        styles_dir = os.path.join(desktop_dir, "resources", "styles")

        # 确保目录存在
        os.makedirs(styles_dir, exist_ok=True)

        return styles_dir

    def get_current_theme(self) -> str:
        """
        获取当前主题

        Returns:
            str: 当前主题名称
        """
        return self._current_theme

    def get_theme_color(self, color_name: str) -> str:
        """
        获取主题颜色

        Args:
            color_name (str): 颜色名称

        Returns:
            str: 颜色值
        """
        theme_colors = self.THEMES.get(self._current_theme, {})
        return theme_colors.get(color_name, "#000000")

    def get_theme_colors(self) -> Dict[str, str]:
        """
        获取当前主题的所有颜色

        Returns:
            Dict[str, str]: 颜色字典
        """
        return self.THEMES.get(self._current_theme, {}).copy()

    def set_theme(self, theme_name: str) -> bool:
        """
        设置主题

        Args:
            theme_name (str): 主题名称

        Returns:
            bool: 是否设置成功
        """
        # 强制使用 light 主题
        theme_name = "light"

        self._current_theme = theme_name
        logger.info(f"主题已设置为：{theme_name}")

        # 发送主题变更信号
        self.theme_changed.emit(theme_name)

        return True

    def apply_theme(self, app: QApplication) -> bool:
        """
        应用主题到应用程序

        Args:
            app (QApplication): 应用程序实例

        Returns:
            bool: 是否应用成功
        """
        # 强制使用 light 主题
        self._current_theme = "light" 
        theme_file = os.path.join(self._styles_dir, "light_theme.qss")

        if not os.path.exists(theme_file):
            logger.warning(f"主题文件不存在：{theme_file}")
            return False

        try:
            # 读取样式表文件
            style_file = QFile(theme_file)
            if style_file.open(QFile.ReadOnly | QFile.Text):
                stream = QTextStream(style_file)
                style_sheet = stream.readAll()

                # 应用样式表
                app.setStyleSheet(style_sheet)
                logger.info(f"已应用主题：{self._current_theme}")
                return True
            else:
                logger.error(f"无法打开主题文件：{theme_file}")
                return False
        except Exception as e:
            logger.error(f"应用主题时出错：{e}")
            return False

    def get_available_themes(self) -> list:
        """
        获取可用主题列表

        Returns:
            list: 可用主题列表
        """
        # 只返回 light 主题
        return ["light"]


# 创建全局主题管理器实例
theme_manager = ThemeManager()
