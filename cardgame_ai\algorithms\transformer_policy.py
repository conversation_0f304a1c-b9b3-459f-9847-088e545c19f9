"""
基于Transformer的策略网络模块

实现基于Transformer架构的策略网络，用于处理斗地主等卡牌游戏中的序列决策问题。
利用注意力机制捕捉游戏状态中的长期依赖关系，提升AI决策能力。
"""
import os
import math
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional, Any, Union
from torch.cuda.amp import autocast, GradScaler

from cardgame_ai.core.base import State, Action, Experience, Batch
from cardgame_ai.core.algorithm import PolicyBasedAlgorithm
from cardgame_ai.algorithms.transformer_network import (
    TransformerEncoder,
    PositionalEncoding,
    MultiHeadAttention
)


class StateEncoder:
    """
    游戏状态编码器

    将游戏状态转换为适合Transformer处理的序列表示。
    """

    def __init__(self, input_dim: int, seq_len: int = 100):
        """
        初始化状态编码器

        Args:
            input_dim (int): 输入维度
            seq_len (int, optional): 序列长度. Defaults to 100.
        """
        self.input_dim = input_dim
        self.seq_len = seq_len

    def encode_hand_cards(self, cards: List[int]) -> np.ndarray:
        """
        编码手牌

        Args:
            cards (List[int]): 手牌列表

        Returns:
            np.ndarray: 编码后的手牌表示
        """
        encoding = np.zeros(54)  # 标准扑克牌54张（包括大小王）
        for card in cards:
            encoding[card] = 1
        return encoding

    def encode_card_play_history(self, history: List[Tuple[int, List[int]]]) -> np.ndarray:
        """
        编码出牌历史

        Args:
            history (List[Tuple[int, List[int]]]): 出牌历史，每项为(玩家ID, 出的牌)

        Returns:
            np.ndarray: 编码后的出牌历史表示
        """
        history_len = min(len(history), self.seq_len - 1)  # 预留一个位置给手牌
        encoding = np.zeros((history_len, self.input_dim))

        for i in range(history_len):
            player_id, cards = history[-(history_len-i)]  # 从最近的历史开始

            # 玩家ID的one-hot编码
            player_encoding = np.zeros(3)  # 假设最多3个玩家（斗地主）
            if player_id < 3:
                player_encoding[player_id] = 1

            # 牌的编码
            cards_encoding = self.encode_hand_cards(cards)

            # 合并编码
            encoding[i, :3] = player_encoding
            encoding[i, 3:57] = cards_encoding

        return encoding

    def encode_state(self, state: Dict[str, Any]) -> np.ndarray:
        """
        编码完整游戏状态

        Args:
            state (Dict[str, Any]): 游戏状态

        Returns:
            np.ndarray: 编码后的状态表示
        """
        # 编码手牌
        hand_cards = state.get('hand_cards', [])
        hand_encoding = self.encode_hand_cards(hand_cards)

        # 编码出牌历史
        history = state.get('history', [])
        history_encoding = self.encode_card_play_history(history)

        # 编码其他信息
        player_role = state.get('role', 0)  # 0: 地主, 1/2: 农民
        num_cards_left = state.get('num_cards_left', [])  # 每个玩家剩余的牌数

        # 角色编码
        role_encoding = np.zeros(3)
        role_encoding[player_role] = 1

        # 剩余牌数编码
        cards_left_encoding = np.array(num_cards_left) / 20.0  # 归一化

        # 合并所有编码
        state_encoding = np.zeros((self.seq_len, self.input_dim))

        # 第一个位置放手牌信息
        state_encoding[0, :54] = hand_encoding
        state_encoding[0, 54:57] = role_encoding
        state_encoding[0, 57:60] = cards_left_encoding

        # 后续位置放历史信息
        history_len = min(len(history), self.seq_len - 1)
        state_encoding[1:history_len+1] = history_encoding

        return state_encoding


class TransformerPolicyNetwork(nn.Module):
    """
    基于Transformer的策略网络

    使用Transformer架构处理卡牌游戏中的序列信息，提升模型对长期依赖的捕捉能力。
    支持融合对手模型表示以提升针对性决策能力。
    """

    def __init__(self,
                 input_dim: int,
                 action_dim: int,
                 hidden_dim: int = 256,
                 num_heads: int = 4,
                 num_layers: int = 4,
                 ff_dim: int = 512,
                 dropout: float = 0.1,
                 seq_len: int = 100,
                 opponent_dim: int = 32,
                 use_opponent_modeling: bool = False):
        """
        初始化Transformer策略网络

        Args:
            input_dim (int): 输入维度
            action_dim (int): 动作维度
            hidden_dim (int, optional): 隐藏层维度. Defaults to 256.
            num_heads (int, optional): 注意力头数量. Defaults to 4.
            num_layers (int, optional): Transformer层数量. Defaults to 4.
            ff_dim (int, optional): 前馈网络维度. Defaults to 512.
            dropout (float, optional): Dropout比率. Defaults to 0.1.
            seq_len (int, optional): 序列长度. Defaults to 100.
            opponent_dim (int, optional): 对手表示维度. Defaults to 32.
            use_opponent_modeling (bool, optional): 是否使用对手建模. Defaults to False.
        """
        super(TransformerPolicyNetwork, self).__init__()

        # 设置对手建模相关参数
        self.use_opponent_modeling = use_opponent_modeling
        self.opponent_dim = opponent_dim

        # 输入嵌入层
        self.embedding = nn.Linear(input_dim, hidden_dim)

        # 对手表示嵌入层（如果启用）
        if use_opponent_modeling:
            self.opponent_embedding = nn.Linear(opponent_dim, hidden_dim // 2)
            # 对手表示与状态特征融合层
            self.fusion_layer = nn.Linear(hidden_dim + hidden_dim // 2, hidden_dim)

        # 位置编码
        self.positional_encoding = PositionalEncoding(hidden_dim, max_seq_length=seq_len, dropout=dropout)

        # Transformer编码器
        self.transformer = TransformerEncoder(
            hidden_dim=hidden_dim,
            num_heads=num_heads,
            ff_dim=ff_dim,
            num_layers=num_layers,
            dropout=dropout
        )

        # 策略头（动作预测）
        self.policy_head = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, action_dim)
        )

        # 价值头（状态价值评估）
        self.value_head = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, 1)
        )

    def forward(self, x: torch.Tensor, opponent_representation: Optional[torch.Tensor] = None, 
                explain: bool = False) -> Union[Tuple[torch.Tensor, torch.Tensor], Tuple[torch.Tensor, torch.Tensor, Dict[str, Any]]]:
        """
        前向传播

        Args:
            x (torch.Tensor): 输入张量，形状为 [batch_size, seq_len, input_dim]
            opponent_representation (Optional[torch.Tensor]): 对手模型表示，形状为 [batch_size, opponent_dim]
            explain (bool, optional): 是否返回解释数据. Defaults to False.

        Returns:
            Union[Tuple[torch.Tensor, torch.Tensor], Tuple[torch.Tensor, torch.Tensor, Dict[str, Any]]]:
                如果explain=False，返回策略对数和价值；
                如果explain=True，返回策略对数、价值和解释数据
        """
        # 初始化解释数据
        explanation_data = {} if explain else None

        # 嵌入
        embedded = self.embedding(x)

        # 如果需要解释，记录嵌入层输出
        if explain:
            explanation_data['embedding_output'] = {
                'shape': embedded.shape,
                'mean': embedded.mean().item(),
                'std': embedded.std().item()
            }

        # 如果启用了对手建模并且提供了对手表示
        if self.use_opponent_modeling and opponent_representation is not None:
            # 确保对手表示有批次维度
            if opponent_representation.dim() == 1:
                opponent_representation = opponent_representation.unsqueeze(0)
                
            # 确保批次大小匹配
            if opponent_representation.size(0) != embedded.size(0):
                # 如果批次不匹配，则扩展对手表示到相同的批次大小
                opponent_representation = opponent_representation.expand(embedded.size(0), -1)
                
            # 嵌入对手表示
            opponent_embedded = self.opponent_embedding(opponent_representation)
            
            # 记录对手嵌入数据（如果需要解释）
            if explain:
                explanation_data['opponent_embedding_output'] = {
                    'shape': opponent_embedded.shape,
                    'mean': opponent_embedded.mean().item(),
                    'std': opponent_embedded.std().item()
                }
            
            # 将对手表示与每个序列位置的特征融合
            # 首先，将对手嵌入扩展为与embedded相同的序列长度
            opponent_expanded = opponent_embedded.unsqueeze(1).expand(-1, embedded.size(1), -1)
            
            # 沿特征维度拼接
            combined = torch.cat([embedded, opponent_expanded], dim=2)
            
            # 通过融合层处理
            embedded = self.fusion_layer(combined)
            
            # 记录融合后的数据（如果需要解释）
            if explain:
                explanation_data['fusion_output'] = {
                    'shape': embedded.shape,
                    'mean': embedded.mean().item(),
                    'std': embedded.std().item()
                }

        # 添加位置编码
        pos_encoded = self.positional_encoding(embedded)

        # Transformer编码
        transformer_output = self.transformer(pos_encoded)

        # 如果需要解释，记录Transformer输出
        if explain:
            explanation_data['transformer_output'] = {
                'shape': transformer_output.shape,
                'mean': transformer_output.mean().item(),
                'std': transformer_output.std().item()
            }

        # 使用序列的第一个位置（对应手牌信息）进行预测
        features = transformer_output[:, 0, :]

        # 策略预测
        policy_logits = self.policy_head(features)

        # 价值预测
        value = self.value_head(features).squeeze(-1)

        # 如果需要解释，记录网络输出
        if explain:
            # 获取最高概率的动作
            if policy_logits.dim() > 1:
                probs = F.softmax(policy_logits, dim=1)
                top_probs, top_indices = torch.topk(probs[0], min(5, probs.size(1)))
            else:
                probs = F.softmax(policy_logits, dim=0)
                top_probs, top_indices = torch.topk(probs, min(5, probs.size(0)))

            explanation_data['policy_output'] = {
                'shape': policy_logits.shape,
                'top_actions': [(idx.item(), prob.item()) for idx, prob in zip(top_indices, top_probs)]
            }
            explanation_data['value_output'] = {
                'value': value[0].item() if value.dim() > 0 else value.item()
            }
            
            # 如果使用了对手建模，记录对手信息影响
            if self.use_opponent_modeling and opponent_representation is not None:
                explanation_data['opponent_influence'] = {
                    'used': True,
                    'representation_norm': opponent_representation.norm().item()
                }

        if explain:
            return policy_logits, value, explanation_data
        else:
            return policy_logits, value


class TransformerPolicy(PolicyBasedAlgorithm):
    """
    基于Transformer的策略算法

    利用Transformer架构的强大表达能力，为卡牌游戏设计的策略梯度算法。
    支持在线对手建模与其融合，能够针对不同对手动态调整策略。
    """

    def __init__(self,
                 state_shape: Tuple[int, ...],
                 action_shape: Tuple[int, ...],
                 hidden_dim: int = 256,
                 num_heads: int = 4,
                 num_layers: int = 4,
                 ff_dim: int = 512,
                 lr: float = 1e-4,
                 gamma: float = 0.99,
                 gae_lambda: float = 0.95,
                 clip_ratio: float = 0.2,
                 value_coef: float = 0.5,
                 entropy_coef: float = 0.01,
                 max_grad_norm: float = 0.5,
                 update_epochs: int = 4,
                 batch_size: int = 64,
                 seq_len: int = 100,
                 device: torch.device = None,
                 use_mixed_precision: bool = False,
                 use_model_quantization: bool = False,
                 use_rlhf: bool = False,
                 use_opponent_modeling: bool = False,
                 opponent_dim: int = 32):
        """
        初始化Transformer策略算法

        Args:
            state_shape (Tuple[int, ...]): 状态空间形状
            action_shape (Tuple[int, ...]): 动作空间形状
            hidden_dim (int, optional): 隐藏层维度. Defaults to 256.
            num_heads (int, optional): 注意力头数量. Defaults to 4.
            num_layers (int, optional): Transformer层数量. Defaults to 4.
            ff_dim (int, optional): 前馈网络维度. Defaults to 512.
            lr (float, optional): 学习率. Defaults to 1e-4.
            gamma (float, optional): 折扣因子. Defaults to 0.99.
            gae_lambda (float, optional): GAE λ参数. Defaults to 0.95.
            clip_ratio (float, optional): PPO剪裁比率. Defaults to 0.2.
            value_coef (float, optional): 价值损失系数. Defaults to 0.5.
            entropy_coef (float, optional): 熵损失系数. Defaults to 0.01.
            max_grad_norm (float, optional): 梯度裁剪阈值. Defaults to 0.5.
            update_epochs (int, optional): 每批次数据的更新轮次. Defaults to 4.
            batch_size (int, optional): 批次大小. Defaults to 64.
            seq_len (int, optional): 序列长度. Defaults to 100.
            device (torch.device, optional): 计算设备. Defaults to None.
            use_mixed_precision (bool, optional): 是否使用混合精度训练. Defaults to False.
            use_model_quantization (bool, optional): 是否使用模型量化. Defaults to False.
            use_rlhf (bool, optional): 是否使用人类反馈强化学习. Defaults to False.
            use_opponent_modeling (bool, optional): 是否使用对手建模. Defaults to False.
            opponent_dim (int, optional): 对手表示维度. Defaults to 32.
        """
        super().__init__()

        # 设置设备
        if device is None:
            self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        else:
            self.device = device

        # 状态和动作空间
        self.state_shape = state_shape
        self.action_shape = action_shape

        # 从元组提取具体维度（例如，对于1D数组，提取第一个值）
        if len(state_shape) == 1:
            self.state_dim = state_shape[0]
        else:
            # 对于2D或更高维，可能需要更复杂的处理
            self.state_dim = np.prod(state_shape)

        if len(action_shape) == 0:  # 离散动作空间
            self.action_dim = 1
        elif len(action_shape) == 1:  # 1D动作空间
            self.action_dim = action_shape[0]
        else:
            # 对于2D或更高维，可能需要更复杂的处理
            self.action_dim = np.prod(action_shape)

        # 超参数
        self.hidden_dim = hidden_dim
        self.lr = lr
        self.gamma = gamma
        self.gae_lambda = gae_lambda
        self.clip_ratio = clip_ratio
        self.value_coef = value_coef
        self.entropy_coef = entropy_coef
        self.max_grad_norm = max_grad_norm
        self.update_epochs = update_epochs
        self.batch_size = batch_size
        self.seq_len = seq_len
        self.use_mixed_precision = use_mixed_precision
        self.use_model_quantization = use_model_quantization
        self.use_rlhf = use_rlhf
        
        # 对手建模相关参数
        self.use_opponent_modeling = use_opponent_modeling
        self.opponent_dim = opponent_dim
        self.current_opponent_representation = None

        # 创建Transformer策略网络
        self.network = TransformerPolicyNetwork(
            input_dim=self.state_dim,
            action_dim=self.action_dim,
            hidden_dim=hidden_dim,
            num_heads=num_heads,
            num_layers=num_layers,
            ff_dim=ff_dim,
            seq_len=seq_len,
            opponent_dim=opponent_dim,
            use_opponent_modeling=use_opponent_modeling
        ).to(self.device)

        # 优化器
        self.optimizer = optim.Adam(self.network.parameters(), lr=lr)

        # 混合精度训练
        if self.use_mixed_precision:
            self.scaler = GradScaler()

        # 如果需要，应用模型量化
        if self.use_model_quantization:
            self._quantize_model()

        # 经验缓冲区
        self.buffer = {
            'states': [],
            'actions': [],
            'action_log_probs': [],
            'rewards': [],
            'values': [],
            'dones': []
        }

        # 人类反馈数据
        if self.use_rlhf:
            self.human_feedback_buffer = []
            self.reward_model = nn.Sequential(
                nn.Linear(self.state_dim + self.action_dim, hidden_dim),
                nn.ReLU(),
                nn.Linear(hidden_dim, 1)
            ).to(self.device)
            self.reward_optimizer = optim.Adam(self.reward_model.parameters(), lr=lr)

        # 状态编码器
        self.state_encoder = StateEncoder(input_dim=self.state_dim, seq_len=seq_len)

    @property
    def name(self) -> str:
        """
        获取算法名称

        Returns:
            str: 算法名称
        """
        return "TransformerPolicy"

    def _quantize_model(self) -> None:
        """
        量化模型以减少内存使用和提高推理速度
        """
        self.network.qconfig = torch.quantization.get_default_qconfig('fbgemm')
        torch.quantization.prepare(self.network, inplace=True)

        # 注意：完整量化需要校准步骤，通常在一些样本数据上运行模型
        # 这里简化处理，实际使用时应该添加校准过程
        torch.quantization.convert(self.network, inplace=True)

    def _preprocess_state(self, state: Union[State, np.ndarray, Dict]) -> torch.Tensor:
        """
        预处理状态为网络输入格式

        Args:
            state (Union[State, np.ndarray, Dict]): 原始状态

        Returns:
            torch.Tensor: 预处理后的状态张量
        """
        # 如果已经是张量，直接返回
        if isinstance(state, torch.Tensor):
            return state

        # 如果是Numpy数组
        if isinstance(state, np.ndarray):
            # 根据维度调整形状
            if len(state.shape) == 1:  # 如果是1D数组，添加批次维度
                state_tensor = torch.FloatTensor(state).unsqueeze(0)
            elif len(state.shape) == a2:  # 如果已经是2D数组 [seq_len, feature_dim]
                state_tensor = torch.FloatTensor(state).unsqueeze(0)  # 添加批次维度
            else:
                state_tensor = torch.FloatTensor(state)
        # 如果是字典（可能包含不同的状态组件）
        elif isinstance(state, dict):
            # 使用状态编码器处理
            encoded_state = self.state_encoder.encode_state(state)
            state_tensor = torch.FloatTensor(encoded_state).unsqueeze(0)  # 添加批次维度
        # 如果是自定义状态对象
        else:
            try:
                # 尝试将状态转换为向量表示
                state_vector = state.vectorize()
                # 根据向量的类型进行转换
                if isinstance(state_vector, torch.Tensor):
                    state_tensor = state_vector
                else:
                    state_tensor = torch.FloatTensor(state_vector)
                # 确保有批次维度
                if state_tensor.dim() == 1:
                    state_tensor = state_tensor.unsqueeze(0)
            except:
                raise ValueError(f"无法处理的状态类型: {type(state)}")

        return state_tensor.to(self.device)

    def set_opponent_representation(self, opponent_representation: Union[torch.Tensor, np.ndarray, Dict[str, Any]]) -> None:
        """
        设置当前对手表示，供后续预测使用

        Args:
            opponent_representation: 对手表示。可以是张量、NumPy数组或包含'representation'键的字典
        """
        if not self.use_opponent_modeling:
            return
            
        # 处理不同类型的输入
        if isinstance(opponent_representation, Dict):
            # 如果是字典，提取表示数组
            if 'representation' in opponent_representation:
                representation = opponent_representation['representation']
            else:
                raise ValueError("对手表示字典必须包含'representation'键")
        else:
            representation = opponent_representation
            
        # 转换为张量
        if isinstance(representation, np.ndarray):
            representation_tensor = torch.FloatTensor(representation)
        elif isinstance(representation, torch.Tensor):
            representation_tensor = representation
        else:
            raise ValueError(f"不支持的对手表示类型: {type(representation)}")
            
        # 确保形状正确
        if representation_tensor.dim() > 1 and representation_tensor.shape[1] != self.opponent_dim:
            raise ValueError(f"对手表示维度不匹配: 预期 {self.opponent_dim}, 得到 {representation_tensor.shape[1]}")
        elif representation_tensor.dim() == 1 and representation_tensor.shape[0] != self.opponent_dim:
            raise ValueError(f"对手表示维度不匹配: 预期 {self.opponent_dim}, 得到 {representation_tensor.shape[0]}")
            
        # 存储表示
        self.current_opponent_representation = representation_tensor.to(self.device)

    def predict(self, state: Union[State, np.ndarray, Dict], explain: bool = False) -> Union[Tuple[List[float], float], Tuple[List[float], float, Dict[str, Any]]]:
        """
        预测给定状态的动作概率和状态价值

        Args:
            state (Union[State, np.ndarray, Dict]): 输入状态
            explain (bool, optional): 是否返回解释数据. Defaults to False.

        Returns:
            Union[Tuple[List[float], float], Tuple[List[float], float, Dict[str, Any]]]:
                动作概率列表、状态价值，以及可选的解释数据
        """
        # 预处理状态
        state_tensor = self._preprocess_state(state)

        # 模型推理
        with torch.no_grad():
            if self.use_mixed_precision:
                with autocast():
                    if self.use_opponent_modeling and self.current_opponent_representation is not None:
                        policy_logits, value, *extra = self.network(state_tensor, 
                                                                   self.current_opponent_representation, 
                                                                   explain=explain)
                    else:
                        policy_logits, value, *extra = self.network(state_tensor, explain=explain)
            else:
                if self.use_opponent_modeling and self.current_opponent_representation is not None:
                    policy_logits, value, *extra = self.network(state_tensor, 
                                                               self.current_opponent_representation, 
                                                               explain=explain)
                else:
                    policy_logits, value, *extra = self.network(state_tensor, explain=explain)

        # 将logits转换为概率
        policy = F.softmax(policy_logits, dim=1)[0].cpu().numpy().tolist()
        value = value.item()

        if explain:
            return policy, value, extra[0]
        else:
            return policy, value

    def _add_to_buffer(self, state, action, action_log_prob, reward, value, done):
        """
        添加经验到缓冲区
        """
        self.buffer['states'].append(state)
        self.buffer['actions'].append(action)
        self.buffer['action_log_probs'].append(action_log_prob)
        self.buffer['rewards'].append(reward)
        self.buffer['values'].append(value)
        self.buffer['dones'].append(done)

    def update(self, experience: Union[Experience, Batch]) -> Dict[str, float]:
        """
        使用经验数据更新模型

        Args:
            experience (Union[Experience, Batch]): 单个经验或经验批次

        Returns:
            Dict[str, float]: 更新指标，如损失值等
        """
        # 如果是单个经验，添加到缓冲区
        if isinstance(experience, Experience):
            # 预处理状态
            state_tensor = self._preprocess_state(experience.state)

            # 使用网络预测动作概率和价值
            with torch.no_grad():
                policy_logits, value = self.network(state_tensor, explain=False)
                action_probs = F.softmax(policy_logits, dim=-1)
                action_log_prob = torch.log(action_probs[0, experience.action] + 1e-8)

            # 添加到缓冲区
            self._add_to_buffer(
                state_tensor,
                experience.action,
                action_log_prob.item(),
                experience.reward,
                value.item(),
                experience.done
            )

            # 经验缓冲区未满，暂不更新
            if len(self.buffer['states']) < self.batch_size:
                return {
                    'policy_loss': 0.0,
                    'value_loss': 0.0,
                    'entropy': 0.0
                }

            # 经验缓冲区满，执行更新
            return self._update_policy()

        # 如果是批次数据，直接执行更新
        elif isinstance(experience, Batch):
            states = []
            actions = []
            rewards = []
            dones = []

            for i in range(len(experience.states)):
                states.append(self._preprocess_state(experience.states[i]))
                actions.append(experience.actions[i])
                rewards.append(experience.rewards[i])
                dones.append(experience.dones[i])

            # 使用网络预测价值和动作概率
            with torch.no_grad():
                values = []
                action_log_probs = []

                for i in range(len(states)):
                    policy_logits, value = self.network(states[i], explain=False)
                    action_probs = F.softmax(policy_logits, dim=-1)
                    action_log_prob = torch.log(action_probs[0, actions[i]] + 1e-8)

                    values.append(value.item())
                    action_log_probs.append(action_log_prob.item())

            # 更新经验缓冲区
            self.buffer['states'] = states
            self.buffer['actions'] = actions
            self.buffer['rewards'] = rewards
            self.buffer['dones'] = dones
            self.buffer['values'] = values
            self.buffer['action_log_probs'] = action_log_probs

            # 执行更新
            return self._update_policy()

        else:
            raise ValueError(f"Unsupported experience type: {type(experience)}")

    def _compute_gae(self, rewards, values, dones, next_value):
        """
        计算广义优势估计(GAE)
        """
        advantages = []
        gae = 0

        for i in reversed(range(len(rewards))):
            if i == len(rewards) - 1:
                next_val = next_value
            else:
                next_val = values[i + 1]

            delta = rewards[i] + self.gamma * next_val * (1 - dones[i]) - values[i]
            gae = delta + self.gamma * self.gae_lambda * (1 - dones[i]) * gae
            advantages.insert(0, gae)

        return advantages

    def _update_policy(self) -> Dict[str, float]:
        """
        更新策略网络

        使用缓冲区中的经验数据，计算策略损失和价值损失，
        并使用PPO算法更新网络参数。

        Returns:
            Dict[str, float]: 包含各种损失值的字典
        """
        # 将经验数据转换为张量
        states = torch.FloatTensor(np.array(self.buffer['states'])).to(self.device)
        actions = torch.LongTensor(np.array(self.buffer['actions'])).to(self.device)
        old_log_probs = torch.FloatTensor(np.array(self.buffer['action_log_probs'])).to(self.device)
        rewards = torch.FloatTensor(np.array(self.buffer['rewards'])).to(self.device)
        values = torch.FloatTensor(np.array(self.buffer['values'])).to(self.device)
        dones = torch.FloatTensor(np.array(self.buffer['dones'])).to(self.device)

        # 计算优势函数
        with torch.no_grad():
            # 获取最后一个状态的价值
            if len(self.buffer['states']) > 0:
                last_state = self._preprocess_state(self.buffer['states'][-1])
                if self.use_opponent_modeling and self.current_opponent_representation is not None:
                    _, last_value = self.network(last_state, self.current_opponent_representation)
                else:
                    _, last_value = self.network(last_state)
                last_value = last_value.item()
            else:
                last_value = 0.0

            # 计算GAE
            advantages = self._compute_gae(rewards.cpu().numpy(), values.cpu().numpy(), 
                                         dones.cpu().numpy(), last_value)
            advantages = torch.FloatTensor(advantages).to(self.device)
            returns = advantages + values

            # 标准化优势
            advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)

        # 更新循环
        total_policy_loss = 0
        total_value_loss = 0
        total_entropy_loss = 0
        total_loss = 0
        
        # 创建数据集和数据加载器
        dataset = torch.utils.data.TensorDataset(states, actions, old_log_probs, returns, advantages)
        dataloader = torch.utils.data.DataLoader(dataset, batch_size=self.batch_size, shuffle=True)

        for _ in range(self.update_epochs):
            for batch_states, batch_actions, batch_old_log_probs, batch_returns, batch_advantages in dataloader:
                # 前向传播
                if self.use_mixed_precision:
                    with autocast():
                        if self.use_opponent_modeling and self.current_opponent_representation is not None:
                            policy_logits, values = self.network(batch_states, self.current_opponent_representation)
                        else:
                            policy_logits, values = self.network(batch_states)
                else:
                    if self.use_opponent_modeling and self.current_opponent_representation is not None:
                        policy_logits, values = self.network(batch_states, self.current_opponent_representation)
                    else:
                        policy_logits, values = self.network(batch_states)

                # 计算损失
                policy_loss, value_loss, entropy_loss = self._compute_losses(
                    policy_logits, values, batch_actions, batch_old_log_probs, 
                    batch_returns, batch_advantages
                )
                
                # 总损失
                loss = policy_loss + self.value_coef * value_loss - self.entropy_coef * entropy_loss

                # 反向传播
                self.optimizer.zero_grad()
                
                if self.use_mixed_precision:
                    self.scaler.scale(loss).backward()
                    self.scaler.unscale_(self.optimizer)
                    nn.utils.clip_grad_norm_(self.network.parameters(), self.max_grad_norm)
                    self.scaler.step(self.optimizer)
                    self.scaler.update()
                else:
                    loss.backward()
                    nn.utils.clip_grad_norm_(self.network.parameters(), self.max_grad_norm)
                    self.optimizer.step()

                # 累计损失
                total_policy_loss += policy_loss.item()
                total_value_loss += value_loss.item()
                total_entropy_loss += entropy_loss.item()
                total_loss += loss.item()

        # 计算平均损失
        num_batches = len(dataloader) * self.update_epochs
        avg_policy_loss = total_policy_loss / num_batches
        avg_value_loss = total_value_loss / num_batches
        avg_entropy_loss = total_entropy_loss / num_batches
        avg_loss = total_loss / num_batches

        # 清空缓冲区
        for key in self.buffer:
            self.buffer[key] = []

        # 返回训练统计信息
        return {
            'policy_loss': avg_policy_loss,
            'value_loss': avg_value_loss,
            'entropy_loss': avg_entropy_loss,
            'total_loss': avg_loss
        }

    def add_human_preference(self, trajectory1: List[Experience], trajectory2: List[Experience], preference: int) -> None:
        """
        添加人类反馈

        Args:
            trajectory1 (List[Experience]): 第一个轨迹
            trajectory2 (List[Experience]): 第二个轨迹
            preference (int): 偏好，1表示偏好轨迹1，2表示偏好轨迹2，0表示无偏好
        """
        if not self.use_rlhf:
            return

        # 保存人类偏好数据
        self.human_feedback_buffer.append((trajectory1, trajectory2, preference))

        # 如果缓冲区太大，移除最旧的数据
        if len(self.human_feedback_buffer) > 1000:
            self.human_feedback_buffer.pop(0)

    def update_from_human_feedback(self) -> Dict[str, float]:
        """
        从人类反馈中学习

        Returns:
            Dict[str, float]: 更新指标
        """
        if not self.use_rlhf or len(self.human_feedback_buffer) == 0:
            return {'rlhf_loss': 0.0}

        self.network.train()
        total_loss = 0.0

        # 随机抽样反馈数据
        batch_size = min(len(self.human_feedback_buffer), 8)
        indices = np.random.choice(len(self.human_feedback_buffer), batch_size, replace=False)

        for idx in indices:
            trajectory1, trajectory2, preference = self.human_feedback_buffer[idx]

            # 如果没有偏好，跳过
            if preference == 0:
                continue

            # 计算轨迹1和轨迹2的价值
            values1 = []
            values2 = []

            with torch.no_grad():
                for exp in trajectory1:
                    state_tensor = self._preprocess_state(exp.state)
                    _, value = self.network(state_tensor)
                    values1.append(value.item())

                for exp in trajectory2:
                    state_tensor = self._preprocess_state(exp.state)
                    _, value = self.network(state_tensor)
                    values2.append(value.item())

            # 计算轨迹总价值
            total_value1 = sum(values1)
            total_value2 = sum(values2)

            # 计算偏好损失（使用Bradley-Terry模型）
            if preference == 1:
                # 偏好轨迹1
                prob = torch.sigmoid(torch.tensor(total_value1 - total_value2))
                loss = -torch.log(prob)
            else:
                # 偏好轨迹2
                prob = torch.sigmoid(torch.tensor(total_value2 - total_value1))
                loss = -torch.log(prob)

            # 反向传播
            self.optimizer.zero_grad()
            loss.backward()
            nn.utils.clip_grad_norm_(self.network.parameters(), self.max_grad_norm)
            self.optimizer.step()

            total_loss += loss.item()

        return {'rlhf_loss': total_loss / batch_size}

    def save(self, path: str) -> None:
        """
        保存模型

        Args:
            path (str): 保存路径
        """
        os.makedirs(os.path.dirname(path), exist_ok=True)

        save_dict = {
            'network': self.network.state_dict(),
            'optimizer': self.optimizer.state_dict(),
            'config': {
                'state_shape': self.state_shape,
                'action_shape': self.action_shape,
                'gamma': self.gamma,
                'gae_lambda': self.gae_lambda,
                'clip_ratio': self.clip_ratio,
                'value_coef': self.value_coef,
                'entropy_coef': self.entropy_coef,
                'max_grad_norm': self.max_grad_norm,
                'update_epochs': self.update_epochs,
                'batch_size': self.batch_size,
                'seq_len': self.seq_len,
                'use_mixed_precision': self.use_mixed_precision,
                'use_model_quantization': self.use_model_quantization,
                'use_rlhf': self.use_rlhf,
                'use_opponent_modeling': self.use_opponent_modeling,
                'opponent_dim': self.opponent_dim
            }
        }

        torch.save(save_dict, path)

    def load(self, path: str) -> None:
        """
        加载模型

        Args:
            path (str): 加载路径
        """
        if not os.path.exists(path):
            raise FileNotFoundError(f"Model file not found: {path}")

        data = torch.load(path, map_location=self.device)

        # 加载配置
        config = data.get('config', {})

        # 更新配置
        self.state_shape = config.get('state_shape', self.state_shape)
        self.action_shape = config.get('action_shape', self.action_shape)
        self.gamma = config.get('gamma', self.gamma)
        self.gae_lambda = config.get('gae_lambda', self.gae_lambda)
        self.clip_ratio = config.get('clip_ratio', self.clip_ratio)
        self.value_coef = config.get('value_coef', self.value_coef)
        self.entropy_coef = config.get('entropy_coef', self.entropy_coef)
        self.max_grad_norm = config.get('max_grad_norm', self.max_grad_norm)
        self.update_epochs = config.get('update_epochs', self.update_epochs)
        self.batch_size = config.get('batch_size', self.batch_size)
        self.seq_len = config.get('seq_len', self.seq_len)
        self.use_mixed_precision = config.get('use_mixed_precision', self.use_mixed_precision)
        self.use_model_quantization = config.get('use_model_quantization', self.use_model_quantization)
        self.use_rlhf = config.get('use_rlhf', self.use_rlhf)
        self.use_opponent_modeling = config.get('use_opponent_modeling', self.use_opponent_modeling)
        self.opponent_dim = config.get('opponent_dim', self.opponent_dim)

        # 加载模型参数
        self.network.load_state_dict(data['network'])

        # 加载优化器参数
        if 'optimizer' in data:
            self.optimizer.load_state_dict(data['optimizer'])