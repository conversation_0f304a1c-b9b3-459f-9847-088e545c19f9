"""
因果推断示例脚本

展示如何使用因果推断模块分析AI决策过程中的因果关系。
"""

import os
import sys
import logging
import numpy as np
from typing import Dict, List, Any, Optional

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from cardgame_ai.algorithms.mcts import MCTS
from cardgame_ai.algorithms.explanation_manager import ExplanationManager
from cardgame_ai.algorithms.causal_inference import CausalInferenceModule
from cardgame_ai.games.doudizhu.state import DouDizhuState
from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType
from cardgame_ai.games.doudizhu.card import Card
from cardgame_ai.games.doudizhu.game import DouDizhuGame
from cardgame_ai.models.muzero.muzero_model import MuZeroModel
from cardgame_ai.ui.trust_visualization_enhanced import EnhancedTrustVisualizationWidget


def create_test_state() -> DouDizhuState:
    """创建测试状态"""
    # 创建一个简化的斗地主状态
    game = DouDizhuGame()
    state = game.get_init_state()
    
    # 设置玩家手牌
    state.player_hands = {
        "player0": [Card("3", "hearts"), Card("4", "hearts"), Card("5", "hearts"), 
                   Card("6", "hearts"), Card("7", "hearts"), Card("8", "hearts")],
        "player1": [Card("3", "spades"), Card("4", "spades"), Card("5", "spades"), 
                   Card("6", "spades"), Card("7", "spades"), Card("8", "spades")],
        "player2": [Card("3", "diamonds"), Card("4", "diamonds"), Card("5", "diamonds"), 
                   Card("6", "diamonds"), Card("7", "diamonds"), Card("8", "diamonds")]
    }
    
    # 设置地主
    state.landlord = "player0"
    
    # 设置当前玩家
    state.current_player = "player0"
    
    # 设置底牌
    state.bottom_cards = [Card("A", "clubs"), Card("2", "clubs"), Card("joker", "small")]
    
    # 设置历史动作
    state.history = []
    
    return state


def create_test_model() -> MuZeroModel:
    """创建测试模型"""
    # 创建一个简化的MuZero模型
    model = MuZeroModel(
        observation_shape=(54 * 4,),
        action_space_size=54 * 3,
        hidden_state_size=128,
        value_support_size=601,
        reward_support_size=601
    )
    
    return model


def main():
    """主函数"""
    logger.info("启动因果推断示例...")
    
    # 创建解释管理器，启用因果推断
    explanation_manager = ExplanationManager(
        enabled=True,
        detail_level="high",
        visualization_enabled=True,
        enable_causal_inference=True,
        causal_confidence_threshold=0.6
    )
    
    # 创建MCTS搜索器
    mcts = MCTS(
        num_simulations=50,
        discount=0.997,
        dirichlet_alpha=0.25,
        exploration_fraction=0.25,
        pb_c_base=19652,
        pb_c_init=1.25,
        root_exploration_noise=True,
        use_belief_state=True,
        use_information_value=True,
        information_value_weight=0.3,
        use_act=True
    )
    
    # 创建测试状态
    state = create_test_state()
    
    # 创建测试模型
    model = create_test_model()
    
    # 获取合法动作
    legal_actions = state.get_legal_actions()
    
    # 创建动作掩码
    actions_mask = [i in legal_actions for i in range(54 * 3)]
    
    # 使用MCTS进行搜索，启用解释模式
    logger.info("使用MCTS进行搜索...")
    visit_counts, pi, explanation_data = mcts.run(
        root_state=state,
        model=model,
        temperature=1.0,
        actions_mask=actions_mask,
        explain=True
    )
    
    # 获取最佳动作
    best_action = max(visit_counts.items(), key=lambda x: x[1])[0]
    logger.info(f"最佳动作: {best_action}")
    
    # 创建备选动作列表
    alternative_actions = []
    for action_id, count in visit_counts.items():
        if action_id != best_action and count > 0:
            # 将动作ID转换为CardGroup对象
            card_group = CardGroup.from_action_id(action_id)
            alternative_actions.append(card_group)
    
    # 将最佳动作ID转换为CardGroup对象
    best_card_group = CardGroup.from_action_id(best_action)
    
    # 使用因果推断模块分析动作影响
    logger.info("使用因果推断模块分析动作影响...")
    causal_analysis = explanation_manager.analyze_causal_impact(
        state=state,
        action=best_card_group,
        alternative_actions=alternative_actions,
        search_results=explanation_data
    )
    
    # 将因果分析结果添加到解释数据中
    explanation_data["causal_analysis"] = causal_analysis
    
    # 解释决策
    logger.info("解释决策...")
    explanation = explanation_manager.explain_decision(
        decision_data=explanation_data,
        decision_type="mcts",
        context={
            "state": str(state),
            "legal_actions": legal_actions,
            "best_action": best_action
        }
    )
    
    # 打印因果分析结果
    logger.info("因果分析结果:")
    if causal_analysis:
        logger.info(f"置信度: {causal_analysis.get('confidence', 0.0):.2f}")
        logger.info(f"摘要: {causal_analysis.get('summary', '无摘要')}")
        
        # 打印因果效应
        causal_effects = causal_analysis.get("causal_effects", [])
        for i, effect in enumerate(causal_effects):
            logger.info(f"效应 {i+1}:")
            logger.info(f"  效应: {effect.get('effect', '')}")
            logger.info(f"  备选动作: {effect.get('alternative_action', '')}")
            logger.info(f"  置信度: {effect.get('confidence', 0.0):.2f}")
    else:
        logger.info("未生成因果分析结果")
    
    # 打印统计信息
    logger.info("统计信息:")
    stats = explanation_manager.get_stats()
    for key, value in stats.items():
        logger.info(f"{key}: {value}")
    
    logger.info("因果推断示例完成")


if __name__ == "__main__":
    main()
