"""
核心模块导出

导出核心模块的接口和类，以便其他模块可以方便地导入。
"""

# 基础数据结构
from cardgame_ai.core.base import State, Action, Experience, Batch

# 游戏环境接口
from cardgame_ai.core.environment import Environment, MultiAgentEnvironment

# 代理接口
from cardgame_ai.core.agent import Agent, RandomAgent, HumanAgent

# 策略接口
from cardgame_ai.core.policy import Policy, RandomPolicy

# 学习算法接口
from cardgame_ai.core.algorithm import (
    Algorithm,
    ValueBasedAlgorithm,
    PolicyBasedAlgorithm,
    ModelBasedAlgorithm
)

# 训练器接口
from cardgame_ai.core.trainer import Trainer, BaseTrainer

# 评估器接口
from cardgame_ai.core.evaluator import Evaluator, BaseEvaluator, TournamentEvaluator

# 版本信息
__version__ = '0.1.0'
