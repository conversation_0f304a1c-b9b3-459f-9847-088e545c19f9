#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
自动经验收集脚本

定期自动执行人机交互数据的收集、处理和存储任务。
支持作为守护进程运行或通过计划任务定期执行。
"""

import os
import sys
import time
import json
import logging
import argparse
import datetime
import subprocess
import signal
import glob
from pathlib import Path
from typing import Dict, List, Any, Optional

# 添加项目根目录到系统路径，以便导入项目模块
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

from scripts.process_human_data import HumanDataProcessor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(project_root, 'logs', 'auto_collect.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class AutoCollector:
    """自动数据收集器类"""
    
    def __init__(
        self,
        input_dirs: List[str],
        output_dir: str,
        game_type: str = 'doudizhu',
        interval: int = 3600,  # 默认1小时
        file_patterns: List[str] = None,
        buffer_capacity: int = 100000,
        min_quality_score: float = 0.3,
        max_files_per_run: int = 100,
        archive_dir: Optional[str] = None
    ):
        """
        初始化自动数据收集器
        
        Args:
            input_dirs: 输入数据目录列表
            output_dir: 输出数据目录
            game_type: 游戏类型，默认为斗地主
            interval: 收集间隔（秒），默认为1小时
            file_patterns: 文件匹配模式列表，默认为['*.json', '*.log']
            buffer_capacity: 经验回放缓冲区容量
            min_quality_score: 最低数据质量分数
            max_files_per_run: 单次运行处理的最大文件数
            archive_dir: 归档目录，用于存放已处理的文件，如果为None则不进行归档
        """
        self.input_dirs = input_dirs
        self.output_dir = output_dir
        self.game_type = game_type
        self.interval = interval
        self.file_patterns = file_patterns or ['*.json', '*.log']
        self.buffer_capacity = buffer_capacity
        self.min_quality_score = min_quality_score
        self.max_files_per_run = max_files_per_run
        self.archive_dir = archive_dir
        
        # 已处理文件的记录
        self.processed_files_record = os.path.join(output_dir, 'processed_files.json')
        self.processed_files = self._load_processed_files()
        
        # 运行状态
        self.running = False
        self.last_run_time = 0
        
        # 创建必要的目录
        os.makedirs(output_dir, exist_ok=True)
        
        if archive_dir:
            os.makedirs(archive_dir, exist_ok=True)
        
        # 创建日志目录
        os.makedirs(os.path.join(project_root, 'logs'), exist_ok=True)
        
        logger.info(f"自动数据收集器初始化完成，监控目录：{input_dirs}，输出目录：{output_dir}")
    
    def _load_processed_files(self) -> Dict[str, str]:
        """
        加载已处理文件记录
        
        Returns:
            Dict[str, str]: 已处理文件记录，格式为{文件路径: 处理时间}
        """
        if not os.path.exists(self.processed_files_record):
            return {}
        
        try:
            with open(self.processed_files_record, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载已处理文件记录时出错：{e}")
            return {}
    
    def _save_processed_files(self) -> None:
        """保存已处理文件记录"""
        try:
            with open(self.processed_files_record, 'w', encoding='utf-8') as f:
                json.dump(self.processed_files, f, indent=2)
        except Exception as e:
            logger.error(f"保存已处理文件记录时出错：{e}")
    
    def _get_new_files(self) -> List[str]:
        """
        获取所有新文件
        
        Returns:
            List[str]: 新文件路径列表
        """
        new_files = []
        
        for input_dir in self.input_dirs:
            if not os.path.exists(input_dir):
                logger.warning(f"输入目录不存在：{input_dir}")
                continue
            
            for pattern in self.file_patterns:
                file_paths = glob.glob(os.path.join(input_dir, '**', pattern), recursive=True)
                
                for file_path in file_paths:
                    if file_path not in self.processed_files:
                        new_files.append(file_path)
        
        # 按文件修改时间排序，优先处理较早的文件
        new_files.sort(key=lambda x: os.path.getmtime(x))
        
        # 限制单次处理的文件数量
        return new_files[:self.max_files_per_run]
    
    def _archive_file(self, file_path: str) -> bool:
        """
        归档已处理的文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否成功归档
        """
        if not self.archive_dir:
            return True
        
        try:
            # 创建与原始目录结构相同的归档路径
            rel_path = os.path.relpath(file_path, project_root)
            archive_path = os.path.join(self.archive_dir, rel_path)
            
            # 创建目标目录
            os.makedirs(os.path.dirname(archive_path), exist_ok=True)
            
            # 移动文件
            os.rename(file_path, archive_path)
            
            logger.info(f"文件已归档：{file_path} -> {archive_path}")
            return True
        except Exception as e:
            logger.error(f"归档文件时出错：{file_path}, 错误：{e}")
            return False
    
    def process_new_files(self) -> None:
        """处理新文件"""
        # 获取新文件
        new_files = self._get_new_files()
        
        if not new_files:
            logger.info("没有发现新文件")
            return
        
        logger.info(f"发现 {len(new_files)} 个新文件")
        
        # 创建处理器
        processor = HumanDataProcessor(
            input_dir="",  # 不使用输入目录
            output_dir=self.output_dir,
            game_type=self.game_type,
            buffer_capacity=self.buffer_capacity,
            min_quality_score=self.min_quality_score
        )
        
        # 处理每个文件
        for file_path in new_files:
            try:
                logger.info(f"处理文件：{file_path}")
                
                # 直接处理单个文件
                processor._process_single_file(file_path)
                
                # 记录处理时间
                self.processed_files[file_path] = datetime.datetime.now().isoformat()
                
                # 如果设置了归档目录，则归档文件
                if self.archive_dir:
                    self._archive_file(file_path)
            except Exception as e:
                logger.error(f"处理文件时出错：{file_path}, 错误：{e}")
        
        # 保存经验回放缓冲区
        if len(processor.replay_buffer) > 0:
            # 生成带有时间戳的文件名
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{self.game_type}_experiences_{timestamp}.json"
            
            # 保存缓冲区
            processor.save_buffer(filename)
        
        # 打印处理统计信息
        processor.print_stats()
        
        # 保存已处理文件记录
        self._save_processed_files()
    
    def run_once(self) -> None:
        """执行一次数据收集和处理"""
        logger.info("开始执行数据收集和处理...")
        
        try:
            self.process_new_files()
            self.last_run_time = time.time()
            logger.info("数据收集和处理完成")
        except Exception as e:
            logger.error(f"执行数据收集和处理时出错：{e}")
    
    def run_continuously(self) -> None:
        """持续运行数据收集和处理"""
        self.running = True
        
        # 注册信号处理
        signal.signal(signal.SIGINT, self._handle_signal)
        signal.signal(signal.SIGTERM, self._handle_signal)
        
        logger.info(f"开始持续运行数据收集和处理，间隔：{self.interval}秒")
        
        while self.running:
            # 执行一次数据收集和处理
            self.run_once()
            
            # 等待下一次执行
            logger.info(f"等待下一次执行，间隔：{self.interval}秒")
            
            # 使用分段睡眠，以便能够及时响应中断信号
            for _ in range(min(self.interval, 3600) // 10):
                if not self.running:
                    break
                time.sleep(10)
            
            # 如果间隔大于一小时，继续睡眠剩余时间
            if self.interval > 3600 and self.running:
                time.sleep(self.interval - 3600)
    
    def _handle_signal(self, signum, frame) -> None:
        """
        处理信号
        
        Args:
            signum: 信号编号
            frame: 当前帧
        """
        logger.info(f"接收到信号 {signum}，停止运行...")
        self.running = False


def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='自动执行人机交互数据的收集和处理')
    parser.add_argument('--input', '-i', type=str, action='append', required=True,
                       help='输入数据目录，可多次指定')
    parser.add_argument('--output', '-o', type=str, required=True,
                       help='输出数据目录')
    parser.add_argument('--game', '-g', type=str, default='doudizhu',
                       help='游戏类型，默认为斗地主')
    parser.add_argument('--interval', type=int, default=3600,
                       help='收集间隔（秒），默认为1小时')
    parser.add_argument('--patterns', '-p', type=str, action='append',
                       help='文件匹配模式，可多次指定，默认为["*.json", "*.log"]')
    parser.add_argument('--capacity', '-c', type=int, default=100000,
                       help='经验回放缓冲区容量')
    parser.add_argument('--quality', '-q', type=float, default=0.3,
                       help='最低数据质量分数')
    parser.add_argument('--max-files', '-m', type=int, default=100,
                       help='单次运行处理的最大文件数')
    parser.add_argument('--archive', '-a', type=str,
                       help='归档目录，用于存放已处理的文件，如果不指定则不进行归档')
    parser.add_argument('--once', action='store_true',
                       help='只执行一次，不持续运行')
    parser.add_argument('--daemon', '-d', action='store_true',
                       help='作为守护进程运行')
    
    args = parser.parse_args()
    
    # 创建自动收集器
    collector = AutoCollector(
        input_dirs=args.input,
        output_dir=args.output,
        game_type=args.game,
        interval=args.interval,
        file_patterns=args.patterns,
        buffer_capacity=args.capacity,
        min_quality_score=args.quality,
        max_files_per_run=args.max_files,
        archive_dir=args.archive
    )
    
    # 运行收集器
    if args.once:
        collector.run_once()
    else:
        if args.daemon:
            # 作为守护进程运行
            import daemon
            
            # 确保日志目录存在
            log_dir = os.path.join(project_root, 'logs')
            os.makedirs(log_dir, exist_ok=True)
            
            # 设置守护进程上下文
            context = daemon.DaemonContext(
                working_directory=project_root,
                umask=0o022,
                pidfile=None,
                stderr=open(os.path.join(log_dir, 'auto_collect_error.log'), 'a+'),
                stdout=open(os.path.join(log_dir, 'auto_collect_output.log'), 'a+')
            )
            
            # 启动守护进程
            with context:
                collector.run_continuously()
        else:
            # 直接运行
            collector.run_continuously()


if __name__ == '__main__':
    main() 