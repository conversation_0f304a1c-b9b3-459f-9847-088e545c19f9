"""
基于自对弈历史的策略演化

利用自对弈历史中的不同阶段的模型作为多样化的对手策略。
"""

import os
import glob
import random
import logging
import numpy as np
import torch
from typing import Dict, List, Tuple, Any, Optional, Union, Callable

from cardgame_ai.core.agent import Agent
from cardgame_ai.core.state import State
from cardgame_ai.core.policy import Policy
from cardgame_ai.utils.model_saver import ModelSaver

# 配置日志
logger = logging.getLogger(__name__)


class SelfPlayEvolution:
    """
    自对弈演化

    使用自对弈历史中的不同阶段的模型作为多样化的对手策略。
    """

    def __init__(
        self,
        checkpoint_dir: str,
        sampling_strategy: str = 'latest_k',
        k: int = 5,
        model_class: Optional[Any] = None,
        model_config: Optional[Dict[str, Any]] = None,
        device: str = None
    ):
        """
        初始化自对弈演化

        Args:
            checkpoint_dir: 检查点目录
            sampling_strategy: 采样策略，可选值为'latest_k'（最新的k个）, 'uniform'（均匀采样）,
                              'weighted'（加权采样，越新的权重越高）, 'elo'（基于Elo评分采样）
            k: 采样数量
            model_class: 模型类
            model_config: 模型配置
            device: 计算设备
        """
        # 设置设备
        self.device = device if device else ('cuda' if torch.cuda.is_available() else 'cpu')

        # 保存参数
        self.checkpoint_dir = checkpoint_dir
        self.sampling_strategy = sampling_strategy
        self.k = k
        self.model_class = model_class
        self.model_config = model_config if model_config else {}

        # 查找检查点
        self.available_checkpoints = self._find_checkpoints()

        # 初始化Elo评分
        self.elo_ratings = {checkpoint: 1500 for checkpoint in self.available_checkpoints}

        # 初始化统计信息
        self.stats = {
            "sampling_history": [],
            "elo_history": {}
        }

        logger.info(f"找到 {len(self.available_checkpoints)} 个检查点")

    def _find_checkpoints(self) -> List[str]:
        """
        查找检查点

        Returns:
            检查点列表
        """
        # 检查目录是否存在
        if not os.path.exists(self.checkpoint_dir):
            logger.warning(f"检查点目录不存在: {self.checkpoint_dir}")
            return []

        # 查找所有检查点
        checkpoint_pattern = os.path.join(self.checkpoint_dir, "*.pt")
        checkpoints = glob.glob(checkpoint_pattern)

        # 按修改时间排序
        checkpoints.sort(key=os.path.getmtime)

        return checkpoints

    def _select_checkpoint(self) -> str:
        """
        选择检查点

        根据采样策略选择一个检查点。

        Returns:
            选择的检查点路径
        """
        if not self.available_checkpoints:
            raise ValueError("没有可用的检查点")

        if self.sampling_strategy == 'latest_k':
            # 选择最新的k个检查点
            k = min(self.k, len(self.available_checkpoints))
            latest_checkpoints = self.available_checkpoints[-k:]
            return random.choice(latest_checkpoints)

        elif self.sampling_strategy == 'uniform':
            # 均匀采样
            return random.choice(self.available_checkpoints)

        elif self.sampling_strategy == 'weighted':
            # 加权采样，越新的权重越高
            weights = np.arange(1, len(self.available_checkpoints) + 1)
            weights = weights / np.sum(weights)  # 归一化

            # 选择检查点
            idx = np.random.choice(len(self.available_checkpoints), p=weights)
            return self.available_checkpoints[idx]

        elif self.sampling_strategy == 'elo':
            # 基于Elo评分采样
            if not self.elo_ratings:
                # 如果没有Elo评分，使用均匀采样
                return random.choice(self.available_checkpoints)

            # 计算Elo评分的softmax
            elo_values = np.array([self.elo_ratings[checkpoint] for checkpoint in self.available_checkpoints])
            elo_softmax = np.exp(elo_values / 100) / np.sum(np.exp(elo_values / 100))

            # 选择检查点
            idx = np.random.choice(len(self.available_checkpoints), p=elo_softmax)
            return self.available_checkpoints[idx]

        else:
            raise ValueError(f"不支持的采样策略: {self.sampling_strategy}")

    def _load_policy_from_checkpoint(self, checkpoint_path: str) -> Any:
        """
        从检查点加载策略

        Args:
            checkpoint_path: 检查点路径

        Returns:
            加载的策略
        """
        try:
            # 如果没有提供模型类，直接加载检查点
            if self.model_class is None:
                return ModelSaver.load_checkpoint(checkpoint_path)

            # 创建模型实例
            model = self.model_class(**self.model_config)

            # 加载模型参数
            model = ModelSaver.load_model(model, checkpoint_path)

            # 移动到设备
            model = model.to(self.device)

            return model

        except Exception as e:
            logger.error(f"加载检查点失败: {checkpoint_path}, 错误: {e}")
            return None

    def sample_opponent_policy(self) -> Any:
        """
        采样对手策略

        Returns:
            采样的对手策略
        """
        # 选择检查点
        checkpoint_path = self._select_checkpoint()

        # 加载策略
        policy = self._load_policy_from_checkpoint(checkpoint_path)

        # 记录采样历史
        self.stats["sampling_history"].append({
            "timestamp": np.datetime64('now'),
            "checkpoint": checkpoint_path,
            "strategy": self.sampling_strategy
        })

        return policy

    def update_elo_rating(self, checkpoint_path: str, opponent_path: str, result: float) -> None:
        """
        更新Elo评分

        Args:
            checkpoint_path: 检查点路径
            opponent_path: 对手检查点路径
            result: 比赛结果，1表示胜利，0.5表示平局，0表示失败
        """
        if checkpoint_path not in self.elo_ratings or opponent_path not in self.elo_ratings:
            logger.warning(f"检查点不存在: {checkpoint_path} 或 {opponent_path}")
            return

        # 计算期望得分
        rating_diff = self.elo_ratings[opponent_path] - self.elo_ratings[checkpoint_path]
        expected_score = 1 / (1 + 10 ** (rating_diff / 400))

        # 更新Elo评分
        k = 32  # K因子
        self.elo_ratings[checkpoint_path] += k * (result - expected_score)

        # 记录Elo历史
        if checkpoint_path not in self.stats["elo_history"]:
            self.stats["elo_history"][checkpoint_path] = []

        self.stats["elo_history"][checkpoint_path].append({
            "timestamp": np.datetime64('now'),
            "opponent": opponent_path,
            "result": result,
            "new_rating": self.elo_ratings[checkpoint_path]
        })

    def get_top_k_policies(self, k: int = 5) -> List[Any]:
        """
        获取Elo评分最高的k个策略

        Args:
            k: 策略数量

        Returns:
            Elo评分最高的k个策略
        """
        if not self.elo_ratings:
            # 如果没有Elo评分，使用最新的k个检查点
            k = min(k, len(self.available_checkpoints))
            checkpoints = self.available_checkpoints[-k:]
        else:
            # 按Elo评分排序
            sorted_checkpoints = sorted(
                self.elo_ratings.keys(),
                key=lambda x: self.elo_ratings[x],
                reverse=True
            )
            checkpoints = sorted_checkpoints[:k]

        # 加载策略
        policies = []
        for checkpoint in checkpoints:
            policy = self._load_policy_from_checkpoint(checkpoint)
            if policy is not None:
                policies.append(policy)

        return policies

    def refresh_checkpoints(self) -> None:
        """
        刷新检查点列表
        """
        # 查找检查点
        new_checkpoints = self._find_checkpoints()

        # 找出新增的检查点
        new_added = [cp for cp in new_checkpoints if cp not in self.available_checkpoints]

        # 更新检查点列表
        self.available_checkpoints = new_checkpoints

        # 为新增的检查点初始化Elo评分
        for checkpoint in new_added:
            self.elo_ratings[checkpoint] = 1500

        logger.info(f"刷新检查点列表，共 {len(self.available_checkpoints)} 个检查点，新增 {len(new_added)} 个")

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            统计信息
        """
        return {
            "num_checkpoints": len(self.available_checkpoints),
            "sampling_strategy": self.sampling_strategy,
            "sampling_count": len(self.stats["sampling_history"]),
            "elo_ratings": self.elo_ratings,
            "latest_checkpoint": self.available_checkpoints[-1] if self.available_checkpoints else None
        }


class EvolutionaryPolicyGenerator:
    """
    演化策略生成器

    使用演化算法生成多样化且具有挑战性的对手策略。
    """

    def __init__(
        self,
        base_policy: Any,
        mutation_rate: float = 0.1,
        mutation_strength: float = 0.2,
        population_size: int = 10,
        selection_pressure: float = 0.5,
        device: str = None
    ):
        """
        初始化演化策略生成器

        Args:
            base_policy: 基础策略
            mutation_rate: 变异率
            mutation_strength: 变异强度
            population_size: 种群大小
            selection_pressure: 选择压力
            device: 计算设备
        """
        # 设置设备
        self.device = device if device else ('cuda' if torch.cuda.is_available() else 'cpu')

        # 保存参数
        self.base_policy = base_policy
        self.mutation_rate = mutation_rate
        self.mutation_strength = mutation_strength
        self.population_size = population_size
        self.selection_pressure = selection_pressure

        # 初始化种群
        self.population = self._initialize_population()

        # 初始化适应度
        self.fitness = [0.0] * self.population_size

        # 初始化统计信息
        self.stats = {
            "generation": 0,
            "best_fitness": [],
            "avg_fitness": [],
            "mutation_history": []
        }

    def _initialize_population(self) -> List[Any]:
        """
        初始化种群

        Returns:
            初始化的种群
        """
        population = [self.base_policy]

        # 创建变异的策略
        for _ in range(self.population_size - 1):
            # 复制基础策略
            mutated_policy = self._clone_policy(self.base_policy)

            # 变异
            mutated_policy = self._mutate_policy(mutated_policy)

            # 添加到种群
            population.append(mutated_policy)

        return population

    def _clone_policy(self, policy: Any) -> Any:
        """
        克隆策略

        Args:
            policy: 策略

        Returns:
            克隆的策略
        """
        # 如果策略是PyTorch模型
        if isinstance(policy, torch.nn.Module):
            # 创建新模型
            cloned_policy = type(policy)(**(policy.config if hasattr(policy, 'config') else {}))

            # 复制参数
            cloned_policy.load_state_dict(policy.state_dict())

            # 移动到设备
            cloned_policy = cloned_policy.to(self.device)

            return cloned_policy

        # 如果策略是字典
        elif isinstance(policy, dict):
            return {k: v.clone() if isinstance(v, torch.Tensor) else v for k, v in policy.items()}

        # 如果策略是其他类型
        else:
            # 尝试使用copy方法
            if hasattr(policy, 'copy'):
                return policy.copy()

            # 尝试使用deepcopy
            import copy
            return copy.deepcopy(policy)

    def _mutate_policy(self, policy: Any) -> Any:
        """
        变异策略

        Args:
            policy: 策略

        Returns:
            变异后的策略
        """
        # 如果策略是PyTorch模型
        if isinstance(policy, torch.nn.Module):
            # 遍历所有参数
            for name, param in policy.named_parameters():
                # 以一定概率变异
                if np.random.random() < self.mutation_rate:
                    # 添加高斯噪声
                    noise = torch.randn_like(param) * self.mutation_strength
                    param.data += noise

                    # 记录变异历史
                    self.stats["mutation_history"].append({
                        "generation": self.stats["generation"],
                        "param": name,
                        "mutation_strength": self.mutation_strength
                    })

        # 如果策略是字典
        elif isinstance(policy, dict):
            for key, value in policy.items():
                if isinstance(value, torch.Tensor):
                    # 以一定概率变异
                    if np.random.random() < self.mutation_rate:
                        # 添加高斯噪声
                        noise = torch.randn_like(value) * self.mutation_strength
                        policy[key] = value + noise

        return policy

    def update_fitness(self, fitness_values: List[float]) -> None:
        """
        更新适应度

        Args:
            fitness_values: 适应度值列表
        """
        if len(fitness_values) != self.population_size:
            raise ValueError(f"适应度值数量 ({len(fitness_values)}) 与种群大小 ({self.population_size}) 不匹配")

        # 更新适应度
        self.fitness = fitness_values

        # 更新统计信息
        self.stats["best_fitness"].append(max(self.fitness))
        self.stats["avg_fitness"].append(sum(self.fitness) / len(self.fitness))

    def evolve(self) -> None:
        """
        进化种群
        """
        # 增加代数
        self.stats["generation"] += 1

        # 如果没有适应度值，无法进化
        if all(f == 0.0 for f in self.fitness):
            logger.warning("所有适应度值为0，无法进化")
            return

        # 计算选择概率
        fitness_array = np.array(self.fitness)
        fitness_array = fitness_array - np.min(fitness_array)  # 确保所有值非负
        if np.sum(fitness_array) == 0:
            # 如果所有适应度值相同，使用均匀概率
            selection_probs = np.ones(self.population_size) / self.population_size
        else:
            # 否则，根据适应度计算概率
            selection_probs = fitness_array / np.sum(fitness_array)

        # 应用选择压力
        selection_probs = selection_probs ** self.selection_pressure
        selection_probs = selection_probs / np.sum(selection_probs)

        # 选择和变异
        new_population = []

        # 保留最佳个体
        best_idx = np.argmax(self.fitness)
        new_population.append(self.population[best_idx])

        # 生成其余个体
        for _ in range(self.population_size - 1):
            # 选择父代
            parent_idx = np.random.choice(self.population_size, p=selection_probs)
            parent = self.population[parent_idx]

            # 克隆
            offspring = self._clone_policy(parent)

            # 变异
            offspring = self._mutate_policy(offspring)

            # 添加到新种群
            new_population.append(offspring)

        # 更新种群
        self.population = new_population

        # 重置适应度
        self.fitness = [0.0] * self.population_size

    def get_best_policy(self) -> Any:
        """
        获取最佳策略

        Returns:
            最佳策略
        """
        if all(f == 0.0 for f in self.fitness):
            # 如果没有适应度值，返回第一个策略
            return self.population[0]

        # 返回适应度最高的策略
        best_idx = np.argmax(self.fitness)
        return self.population[best_idx]

    def get_diverse_policies(self, k: int = 5) -> List[Any]:
        """
        获取多样化的策略

        Args:
            k: 策略数量

        Returns:
            多样化的策略
        """
        if k >= self.population_size:
            # 如果请求的数量大于等于种群大小，返回整个种群
            return self.population

        # 如果有适应度值，按适应度排序
        if not all(f == 0.0 for f in self.fitness):
            # 按适应度排序
            sorted_indices = np.argsort(self.fitness)[::-1]

            # 返回适应度最高的k个策略
            return [self.population[idx] for idx in sorted_indices[:k]]

        # 否则，随机选择k个策略
        indices = np.random.choice(self.population_size, size=k, replace=False)
        return [self.population[idx] for idx in indices]

    def generate_opponent_policy(self, style: str = 'best') -> Any:
        """
        生成对手策略

        Args:
            style: 策略风格，可选值为'best'（最佳）, 'random'（随机）, 'diverse'（多样化）

        Returns:
            生成的对手策略
        """
        if style == 'best':
            # 返回最佳策略
            return self.get_best_policy()

        elif style == 'random':
            # 随机选择一个策略
            idx = np.random.choice(self.population_size)
            return self.population[idx]

        elif style == 'diverse':
            # 获取多样化的策略
            diverse_policies = self.get_diverse_policies(k=3)

            # 随机选择一个
            return random.choice(diverse_policies)

        else:
            raise ValueError(f"不支持的策略风格: {style}")

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            统计信息
        """
        return {
            "generation": self.stats["generation"],
            "population_size": self.population_size,
            "mutation_rate": self.mutation_rate,
            "mutation_strength": self.mutation_strength,
            "selection_pressure": self.selection_pressure,
            "best_fitness": self.stats["best_fitness"][-1] if self.stats["best_fitness"] else 0.0,
            "avg_fitness": self.stats["avg_fitness"][-1] if self.stats["avg_fitness"] else 0.0,
            "mutation_count": len(self.stats["mutation_history"])
        }