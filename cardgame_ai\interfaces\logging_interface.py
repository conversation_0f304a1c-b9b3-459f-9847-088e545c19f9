"""
日志服务接口定义

该接口定义了日志服务的标准契约，用于解耦zhuchengxu模块与具体日志实现的依赖关系。
通过该接口，zhuchengxu模块可以与任何实现了LoggingInterface的日志服务进行交互。

设计目标:
- 解耦zhuchengxu与setup_training_logger的直接依赖
- 提供标准化的日志服务接口
- 支持多种日志输出和格式
- 实现日志的分级和过滤

作者: Architect Timmy
版本: v1.0
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Union, TextIO
from dataclasses import dataclass
from enum import Enum
from pathlib import Path


class LogLevel(Enum):
    """日志级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class LogFormat(Enum):
    """日志格式枚举"""
    SIMPLE = "simple"          # 简单格式
    DETAILED = "detailed"      # 详细格式
    JSON = "json"             # JSON格式
    STRUCTURED = "structured"  # 结构化格式


@dataclass
class LogEntry:
    """日志条目数据类"""
    timestamp: str                          # 时间戳
    level: LogLevel                         # 日志级别
    logger_name: str                        # 记录器名称
    message: str                           # 日志消息
    module: Optional[str] = None           # 模块名
    function: Optional[str] = None         # 函数名
    line_number: Optional[int] = None      # 行号
    extra_data: Optional[Dict[str, Any]] = None  # 额外数据


@dataclass
class LoggerConfig:
    """日志器配置"""
    name: str                              # 记录器名称
    level: LogLevel                        # 日志级别
    format: LogFormat                      # 日志格式
    output_file: Optional[Path] = None     # 输出文件
    max_file_size: int = 10 * 1024 * 1024 # 最大文件大小(字节)
    backup_count: int = 5                  # 备份文件数量
    console_output: bool = True            # 是否输出到控制台
    colored_output: bool = True            # 是否彩色输出


class LoggingInterface(ABC):
    """日志服务接口
    
    定义了日志服务必须实现的标准方法，用于解耦zhuchengxu模块与具体日志实现。
    
    实现该接口的类必须提供:
    1. 多级别日志记录功能
    2. 多输出目标支持
    3. 日志格式化和过滤
    4. 日志轮转和管理
    
    注意:
        所有方法都必须是线程安全的，支持并发调用。
        日志记录不应该影响主程序的性能。
    """
    
    @abstractmethod
    def create_logger(self, config: LoggerConfig) -> str:
        """创建日志记录器
        
        Args:
            config: 日志器配置
            
        Returns:
            str: 日志器ID
            
        注意:
            应该支持多个独立的日志记录器。
        """
        pass
    
    @abstractmethod
    def get_logger(self, logger_id: str) -> Optional['LoggingInterface']:
        """获取日志记录器
        
        Args:
            logger_id: 日志器ID
            
        Returns:
            Optional[LoggingInterface]: 日志记录器，不存在返回None
        """
        pass
    
    @abstractmethod
    def debug(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
        """记录调试日志
        
        Args:
            message: 日志消息
            extra: 额外数据
        """
        pass
    
    @abstractmethod
    def info(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
        """记录信息日志
        
        Args:
            message: 日志消息
            extra: 额外数据
        """
        pass
    
    @abstractmethod
    def warning(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
        """记录警告日志
        
        Args:
            message: 日志消息
            extra: 额外数据
        """
        pass
    
    @abstractmethod
    def error(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
        """记录错误日志
        
        Args:
            message: 日志消息
            extra: 额外数据
        """
        pass
    
    @abstractmethod
    def critical(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
        """记录严重错误日志
        
        Args:
            message: 日志消息
            extra: 额外数据
        """
        pass
    
    @abstractmethod
    def log(self, level: LogLevel, message: str, 
           extra: Optional[Dict[str, Any]] = None) -> None:
        """记录指定级别的日志
        
        Args:
            level: 日志级别
            message: 日志消息
            extra: 额外数据
        """
        pass
    
    @abstractmethod
    def set_level(self, level: LogLevel) -> None:
        """设置日志级别
        
        Args:
            level: 日志级别
        """
        pass
    
    @abstractmethod
    def get_level(self) -> LogLevel:
        """获取当前日志级别
        
        Returns:
            LogLevel: 当前日志级别
        """
        pass
    
    @abstractmethod
    def add_handler(self, handler_type: str, **kwargs) -> str:
        """添加日志处理器
        
        Args:
            handler_type: 处理器类型 ("file", "console", "stream", "syslog")
            **kwargs: 处理器配置参数
            
        Returns:
            str: 处理器ID
        """
        pass
    
    @abstractmethod
    def remove_handler(self, handler_id: str) -> bool:
        """移除日志处理器
        
        Args:
            handler_id: 处理器ID
            
        Returns:
            bool: 是否成功移除
        """
        pass
    
    @abstractmethod
    def set_format(self, format_string: str) -> None:
        """设置日志格式
        
        Args:
            format_string: 格式字符串
        """
        pass
    
    @abstractmethod
    def add_filter(self, filter_func: callable) -> str:
        """添加日志过滤器
        
        Args:
            filter_func: 过滤函数，接收LogEntry参数，返回bool
            
        Returns:
            str: 过滤器ID
        """
        pass
    
    @abstractmethod
    def remove_filter(self, filter_id: str) -> bool:
        """移除日志过滤器
        
        Args:
            filter_id: 过滤器ID
            
        Returns:
            bool: 是否成功移除
        """
        pass
    
    @abstractmethod
    def flush(self) -> None:
        """刷新日志缓冲区"""
        pass
    
    @abstractmethod
    def close(self) -> None:
        """关闭日志记录器"""
        pass
    
    @abstractmethod
    def get_log_entries(self, start_time: Optional[str] = None,
                       end_time: Optional[str] = None,
                       level: Optional[LogLevel] = None,
                       limit: Optional[int] = None) -> List[LogEntry]:
        """获取日志条目
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            level: 日志级别过滤
            limit: 返回条目数限制
            
        Returns:
            List[LogEntry]: 日志条目列表
        """
        pass
    
    @abstractmethod
    def export_logs(self, output_path: Union[str, Path], 
                   format: str = "text",
                   start_time: Optional[str] = None,
                   end_time: Optional[str] = None) -> bool:
        """导出日志
        
        Args:
            output_path: 输出路径
            format: 导出格式 ("text", "json", "csv")
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            bool: 是否成功导出
        """
        pass
    
    @abstractmethod
    def rotate_logs(self) -> bool:
        """轮转日志文件
        
        Returns:
            bool: 是否成功轮转
        """
        pass
    
    @abstractmethod
    def get_stats(self) -> Dict[str, Any]:
        """获取日志统计信息
        
        Returns:
            Dict[str, Any]: 统计信息，包含:
                - total_entries: 总条目数
                - entries_by_level: 各级别条目数
                - file_size: 日志文件大小
                - oldest_entry: 最早条目时间
                - newest_entry: 最新条目时间
        """
        pass
