"""
深度联合信念追踪器模块

利用神经网络处理历史出牌序列和场面信息，生成多人博弈中的联合信念分布。
考虑玩家间的信息关系，捕捉手牌分布的相互约束。
"""

import time
import logging
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional, Any, Tuple, Union

from cardgame_ai.games.common.belief_state import BeliefState, BeliefSource
from cardgame_ai.games.doudizhu.card import Card
from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType, get_card_group_type
from cardgame_ai.algorithms.belief_tracking.deep_belief import HistoryEncoder, ActionEncoder, StateEncoder
from cardgame_ai.algorithms.belief_tracking.joint_belief import JointBeliefState

# 配置日志
logger = logging.getLogger(__name__)


class JointBeliefNetwork(nn.Module):
    """
    联合信念网络

    结合历史动作序列和当前状态，预测多个玩家的联合手牌概率分布。
    """

    def __init__(
        self,
        card_vocab_size: int,
        action_embedding_dim: int,
        history_hidden_dim: int,
        state_input_dim: int,
        state_hidden_dim: int,
        num_players: int,
        output_dim: int,
        history_encoder_type: str = "lstm",
        history_num_layers: int = 2,
        state_num_layers: int = 2,
        dropout: float = 0.1
    ):
        """
        初始化联合信念网络

        Args:
            card_vocab_size: 牌词汇表大小
            action_embedding_dim: 动作嵌入维度
            history_hidden_dim: 历史编码器隐藏层维度
            state_input_dim: 状态输入维度
            state_hidden_dim: 状态编码器隐藏层维度
            num_players: 玩家数量
            output_dim: 每个玩家的输出维度（通常等于牌的数量）
            history_encoder_type: 历史编码器类型
            history_num_layers: 历史编码器层数
            state_num_layers: 状态编码器层数
            dropout: Dropout比率
        """
        super().__init__()
        self.card_vocab_size = card_vocab_size
        self.output_dim = output_dim
        self.num_players = num_players

        # 动作编码器
        self.action_encoder = ActionEncoder(
            card_vocab_size=card_vocab_size,
            embedding_dim=action_embedding_dim
        )

        # 历史编码器
        self.history_encoder = HistoryEncoder(
            input_dim=action_embedding_dim,
            hidden_dim=history_hidden_dim,
            num_layers=history_num_layers,
            dropout=dropout,
            encoder_type=history_encoder_type
        )

        # 状态编码器
        self.state_encoder = StateEncoder(
            input_dim=state_input_dim,
            hidden_dim=state_hidden_dim,
            num_layers=state_num_layers
        )

        # 组合网络
        self.combiner = nn.Sequential(
            nn.Linear(history_hidden_dim + state_hidden_dim, history_hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(history_hidden_dim, history_hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout)
        )

        # 为每个玩家创建独立的输出层
        self.player_output_layers = nn.ModuleList([
            nn.Linear(history_hidden_dim // 2, output_dim)
            for _ in range(num_players)
        ])

        # 关系因子网络（预测玩家对之间的关系）
        num_player_pairs = num_players * (num_players - 1) // 2
        self.relation_network = nn.Sequential(
            nn.Linear(history_hidden_dim // 2, history_hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(history_hidden_dim, num_player_pairs * output_dim)
        )

        # 初始化权重
        self._init_weights()

    def _init_weights(self):
        """初始化网络权重"""
        for name, param in self.named_parameters():
            if 'weight' in name:
                nn.init.xavier_uniform_(param)
            elif 'bias' in name:
                nn.init.zeros_(param)

    def forward(
        self,
        action_history: List[Dict[str, Any]],
        state: torch.Tensor,
        action_lengths: Optional[torch.Tensor] = None
    ) -> Tuple[List[torch.Tensor], List[torch.Tensor]]:
        """
        前向传播

        Args:
            action_history: 动作历史，每个元素是一个包含动作信息的字典
            state: 当前状态，形状为[batch_size, state_input_dim]
            action_lengths: 动作序列长度

        Returns:
            Tuple[List[torch.Tensor], List[torch.Tensor]]:
                - 每个玩家的手牌概率分布列表，每个元素形状为[batch_size, output_dim]
                - 玩家对之间的关系因子列表，每个元素形状为[batch_size, output_dim]
        """
        batch_size = state.size(0)

        # 编码动作历史
        action_embeddings = []
        for batch_idx in range(batch_size):
            # 获取当前批次的动作历史
            batch_actions = action_history[batch_idx]
            seq_len = len(batch_actions)

            # 编码每个动作
            batch_embeddings = []
            for i in range(seq_len):
                action = batch_actions[i]
                cards = action.get('cards')
                card_type = action.get('card_type')
                is_pass = action.get('is_pass', False)

                # 编码单个动作
                action_embedding = self.action_encoder(cards, card_type, is_pass)
                batch_embeddings.append(action_embedding)

            # 如果序列为空，添加一个零向量
            if not batch_embeddings:
                batch_embeddings.append(torch.zeros(self.action_encoder.embedding_dim, device=state.device))

            # 堆叠当前批次的动作嵌入
            batch_embeddings = torch.stack(batch_embeddings)  # [seq_len, embedding_dim]
            action_embeddings.append(batch_embeddings)

        # 填充序列到相同长度
        max_seq_len = max(emb.size(0) for emb in action_embeddings)
        padded_embeddings = []
        for emb in action_embeddings:
            seq_len = emb.size(0)
            if seq_len < max_seq_len:
                padding = torch.zeros(max_seq_len - seq_len, emb.size(1), device=emb.device)
                padded_emb = torch.cat([emb, padding], dim=0)
            else:
                padded_emb = emb
            padded_embeddings.append(padded_emb)

        # 堆叠所有批次的动作嵌入
        padded_embeddings = torch.stack(padded_embeddings)  # [batch_size, max_seq_len, embedding_dim]

        # 编码历史
        if action_lengths is None:
            # 如果没有提供序列长度，假设所有序列都是满长度
            history_encoding = self.history_encoder(padded_embeddings)  # [batch_size, history_hidden_dim]
        else:
            history_encoding = self.history_encoder(padded_embeddings, action_lengths)  # [batch_size, history_hidden_dim]

        # 编码状态
        state_encoding = self.state_encoder(state)  # [batch_size, state_hidden_dim]

        # 组合历史和状态编码
        combined = torch.cat([history_encoding, state_encoding], dim=1)  # [batch_size, history_hidden_dim + state_hidden_dim]
        combined_encoding = self.combiner(combined)  # [batch_size, history_hidden_dim // 2]

        # 预测每个玩家的手牌概率分布
        player_probabilities = []
        for i in range(self.num_players):
            logits = self.player_output_layers[i](combined_encoding)  # [batch_size, output_dim]
            probabilities = torch.sigmoid(logits)  # 使用sigmoid得到独立概率
            player_probabilities.append(probabilities)

        # 预测玩家对之间的关系因子
        relation_output = self.relation_network(combined_encoding)  # [batch_size, num_player_pairs * output_dim]

        # 重塑关系输出
        num_player_pairs = self.num_players * (self.num_players - 1) // 2
        relation_output = relation_output.view(batch_size, num_player_pairs, self.output_dim)

        # 分离每个玩家对的关系因子
        relation_factors = []
        pair_idx = 0
        for i in range(self.num_players):
            for j in range(i+1, self.num_players):
                relation_factor = relation_output[:, pair_idx, :]  # [batch_size, output_dim]
                relation_factors.append(relation_factor)
                pair_idx += 1

        return player_probabilities, relation_factors


class DeepJointBeliefTracker:
    """
    深度联合信念追踪器

    使用神经网络模型追踪多人博弈中所有玩家的联合手牌概率分布。
    考虑玩家间的信息关系，捕捉手牌分布的相互约束。
    """

    def __init__(
        self,
        player_ids: List[str],
        model: JointBeliefNetwork,
        card_mapping: List[str],
        device: str = "cpu",
        initial_belief: Optional[JointBeliefState] = None,
        initial_hand_sizes: Optional[Dict[str, int]] = None
    ):
        """
        初始化深度联合信念追踪器

        Args:
            player_ids: 所有玩家的ID列表
            model: 联合信念网络模型
            card_mapping: 牌到索引的映射列表
            device: 计算设备
            initial_belief: 初始联合信念状态
            initial_hand_sizes: 初始手牌数量，格式为{player_id: size}
        """
        self.player_ids = player_ids
        self.model = model
        self.card_mapping = card_mapping
        self.device = device
        self.num_players = len(player_ids)

        # 将模型移动到指定设备
        self.model.to(self.device)

        # 创建联合信念状态
        if initial_belief is not None:
            self.belief = initial_belief
        else:
            # 创建初始联合信念状态
            self.belief = JointBeliefState(
                player_ids=player_ids,
                all_cards=card_mapping,
                initial_hand_sizes=initial_hand_sizes
            )

        # 记录动作历史
        self.action_history = []

        # 记录更新历史
        self.update_history = []

        # 记录上次更新时间
        self.last_update_time = time.time()

    def update(
        self,
        opponent_action: Optional[List[Card]],
        public_knowledge: Dict[str, Any],
        current_state: Optional[np.ndarray] = None,
        acting_player_id: Optional[str] = None
    ) -> None:
        """
        根据对手的动作和公共信息更新联合信念状态

        Args:
            opponent_action: 对手的出牌动作，如果是None表示对手选择不出牌
            public_knowledge: 公共信息，包括已知的公共牌、历史出牌等
            current_state: 当前游戏状态的向量表示
            acting_player_id: 执行动作的玩家ID
        """
        current_time = time.time()

        # 记录动作历史
        action_info = self._process_action(opponent_action, public_knowledge, acting_player_id)
        self.action_history.append(action_info)

        # 如果对手出牌了，更新手牌数量估计
        if opponent_action is not None and len(opponent_action) > 0 and acting_player_id is not None:
            if acting_player_id in self.belief.hand_sizes:
                self.belief.hand_sizes[acting_player_id] -= len(opponent_action)

                # 更新独立信念状态的手牌数量
                belief = self.belief.individual_beliefs.get(acting_player_id)
                if belief:
                    belief.estimated_hand_length = self.belief.hand_sizes[acting_player_id]

        # 使用神经网络更新联合信念状态
        if current_state is not None:
            # 准备输入数据
            state_tensor = torch.tensor(current_state, dtype=torch.float32).unsqueeze(0).to(self.device)
            action_history_batch = [self.action_history]

            # 使用模型预测
            with torch.no_grad():
                player_probabilities, relation_factors = self.model(action_history_batch, state_tensor)

                # 更新每个玩家的独立信念状态
                for i, player_id in enumerate(self.player_ids):
                    if i < len(player_probabilities):
                        probabilities = player_probabilities[i].squeeze(0).cpu().numpy()

                        # 更新信念状态
                        new_probs = {}
                        for j, card in enumerate(self.card_mapping):
                            if j < len(probabilities):
                                new_probs[card] = float(probabilities[j])

                        # 获取独立信念状态
                        belief = self.belief.individual_beliefs.get(player_id)
                        if belief:
                            # 应用概率更新
                            belief.update_probabilities(new_probs)

                            # 更新元数据
                            belief.source = BeliefSource.NEURAL
                            belief.confidence = 0.8  # 神经网络预测的置信度较高

                # 更新联合因子
                pair_idx = 0
                for i, player1 in enumerate(self.player_ids):
                    for j in range(i+1, len(self.player_ids)):
                        player2 = self.player_ids[j]

                        if pair_idx < len(relation_factors):
                            # 获取关系因子
                            relation_factor = relation_factors[pair_idx].squeeze(0).cpu().numpy()

                            # 更新联合因子
                            factor_key = (player1, player2)
                            if factor_key in self.belief.joint_factors:
                                # 将关系因子重塑为矩阵
                                factor_matrix = np.zeros((self.belief.num_cards, self.belief.num_cards))

                                # 填充对角线以外的元素
                                for k in range(self.belief.num_cards):
                                    for l in range(self.belief.num_cards):
                                        if k != l and k < len(relation_factor):
                                            factor_matrix[k, l] = relation_factor[k]

                                # 应用约束：同一张牌不能同时被两个玩家持有
                                for k in range(self.belief.num_cards):
                                    factor_matrix[k, k] = 0.0

                                # 归一化
                                factor_sum = np.sum(factor_matrix)
                                if factor_sum > 0:
                                    factor_matrix /= factor_sum

                                # 更新联合因子
                                self.belief.joint_factors[factor_key] = factor_matrix

                            pair_idx += 1

                # 更新元数据
                self.belief.source = BeliefSource.NEURAL
                self.belief.confidence = 0.8  # 神经网络预测的置信度较高
                self.belief.last_updated = current_time
                self.belief.version += 1
        else:
            # 如果没有提供当前状态，使用基础方法更新
            self._update_with_basic_method(opponent_action, public_knowledge, acting_player_id)

        # 记录更新历史
        update_record = {
            'time': current_time,
            'action': None if opponent_action is None else [str(card) for card in opponent_action],
            'acting_player': acting_player_id,
            'public_knowledge': public_knowledge,
            'elapsed_time': current_time - self.last_update_time
        }
        self.update_history.append(update_record)

        # 更新上次更新时间
        self.last_update_time = current_time

    def _process_action(
        self,
        action: Optional[List[Card]],
        public_knowledge: Dict[str, Any],
        acting_player_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        处理动作，转换为模型可用的格式

        Args:
            action: 动作中的牌列表
            public_knowledge: 公共信息
            acting_player_id: 执行动作的玩家ID

        Returns:
            Dict[str, Any]: 处理后的动作信息
        """
        action_info = {
            'time': time.time(),
            'player_id': acting_player_id,
            'is_pass': action is None or len(action) == 0
        }

        if action is not None and len(action) > 0:
            # 记录出牌信息
            action_info['cards'] = [str(card) for card in action]

            # 尝试获取牌型
            try:
                card_group = CardGroup([card.get_id() for card in action])
                card_type = get_card_group_type(card_group)
                action_info['card_type'] = card_type
            except:
                action_info['card_type'] = None

        # 添加公共信息
        action_info['public_knowledge'] = public_knowledge

        return action_info

    def _update_with_basic_method(
        self,
        opponent_action: Optional[List[Card]],
        public_knowledge: Dict[str, Any],
        acting_player_id: Optional[str] = None
    ) -> None:
        """
        使用基础方法更新联合信念状态

        Args:
            opponent_action: 对手的出牌动作
            public_knowledge: 公共信息
            acting_player_id: 执行动作的玩家ID
        """
        # 如果有出牌动作
        if opponent_action is not None and len(opponent_action) > 0 and acting_player_id is not None:
            # 转换为字符串表示
            action_cards = [str(card) for card in opponent_action]

            # 更新联合信念状态
            self.belief.update_from_action(acting_player_id, action_cards)

        # 如果有公共信息中的已知牌
        if 'known_cards' in public_knowledge:
            known_cards = public_knowledge['known_cards']

            # 更新联合信念状态
            self.belief.update_from_observation(known_cards)

    def get_individual_belief(self, player_id: str) -> Optional[BeliefState]:
        """
        获取指定玩家的独立信念状态

        Args:
            player_id: 玩家ID

        Returns:
            Optional[BeliefState]: 独立信念状态，如果不存在则返回None
        """
        return self.belief.get_individual_belief(player_id)

    def get_joint_probability(self, card_assignments: Dict[str, str]) -> float:
        """
        获取特定牌分配的联合概率

        Args:
            card_assignments: 牌分配，格式为{player_id: card}

        Returns:
            float: 联合概率
        """
        return self.belief.get_joint_probability(card_assignments)

    def sample_joint_assignment(self, num_samples: int = 1) -> List[Dict[str, List[str]]]:
        """
        从联合信念分布中采样牌的分配

        Args:
            num_samples: 采样数量

        Returns:
            List[Dict[str, List[str]]]: 采样的牌分配列表，每个元素是一个字典{player_id: [card1, card2, ...]}
        """
        return self.belief.sample_joint_assignment(num_samples)

    def get_most_likely_assignment(self) -> Dict[str, List[str]]:
        """
        获取最可能的牌分配

        Returns:
            Dict[str, List[str]]: 最可能的牌分配，格式为{player_id: [card1, card2, ...]}
        """
        return self.belief.get_most_likely_assignment()

    def to_dict(self) -> Dict:
        """
        将深度联合信念追踪器转换为字典表示

        Returns:
            Dict: 深度联合信念追踪器的字典表示
        """
        return {
            'player_ids': self.player_ids,
            'card_mapping': self.card_mapping,
            'belief': self.belief.to_dict(),
            'action_history': self.action_history,
            'update_history': self.update_history,
            'last_update_time': self.last_update_time
        }

    @classmethod
    def from_dict(cls, data: Dict, model: JointBeliefNetwork, device: str = "cpu") -> 'DeepJointBeliefTracker':
        """
        从字典创建深度联合信念追踪器

        Args:
            data: 深度联合信念追踪器的字典表示
            model: 联合信念网络模型
            device: 计算设备

        Returns:
            DeepJointBeliefTracker: 创建的深度联合信念追踪器对象
        """
        # 创建联合信念状态
        belief = JointBeliefState.from_dict(data['belief'])

        # 创建追踪器
        tracker = cls(
            player_ids=data['player_ids'],
            model=model,
            card_mapping=data['card_mapping'],
            device=device,
            initial_belief=belief
        )

        # 设置历史记录
        if 'action_history' in data:
            tracker.action_history = data['action_history']

        if 'update_history' in data:
            tracker.update_history = data['update_history']

        if 'last_update_time' in data:
            tracker.last_update_time = data['last_update_time']

        return tracker
