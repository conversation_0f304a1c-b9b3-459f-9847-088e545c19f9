"""
近端策略优化算法模块

实现近端策略优化(PPO)算法，一种基于策略的强化学习算法。
"""
import os
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from typing import Dict, Any, List, Tuple, Optional, Union

from cardgame_ai.core.base import State, Action, Experience, Batch
from cardgame_ai.core.algorithm import PolicyBasedAlgorithm


class PPONetwork(nn.Module):
    """
    PPO网络
    
    包含策略网络和价值网络，用于PPO算法。
    """
    
    def __init__(self, state_dim: int, action_dim: int, hidden_dims: List[int] = [128, 128], shared_layers: bool = True):
        """
        初始化PPO网络
        
        Args:
            state_dim (int): 状态维度
            action_dim (int): 动作维度
            hidden_dims (List[int], optional): 隐藏层维度. Defaults to [128, 128].
            shared_layers (bool, optional): 是否共享特征提取层. Defaults to True.
        """
        super(PPONetwork, self).__init__()
        
        self.shared_layers = shared_layers
        
        if shared_layers:
            # 共享特征提取层
            self.feature_layer = nn.Sequential(
                nn.Linear(state_dim, hidden_dims[0]),
                nn.ReLU()
            )
            
            # 策略头
            policy_layers = []
            policy_input_dim = hidden_dims[0]
            
            for i in range(1, len(hidden_dims)):
                policy_layers.append(nn.Linear(policy_input_dim, hidden_dims[i]))
                policy_layers.append(nn.ReLU())
                policy_input_dim = hidden_dims[i]
            
            policy_layers.append(nn.Linear(policy_input_dim, action_dim))
            self.policy_head = nn.Sequential(*policy_layers)
            
            # 价值头
            value_layers = []
            value_input_dim = hidden_dims[0]
            
            for i in range(1, len(hidden_dims)):
                value_layers.append(nn.Linear(value_input_dim, hidden_dims[i]))
                value_layers.append(nn.ReLU())
                value_input_dim = hidden_dims[i]
            
            value_layers.append(nn.Linear(value_input_dim, 1))
            self.value_head = nn.Sequential(*value_layers)
        else:
            # 独立的策略网络
            policy_layers = []
            policy_input_dim = state_dim
            
            for hidden_dim in hidden_dims:
                policy_layers.append(nn.Linear(policy_input_dim, hidden_dim))
                policy_layers.append(nn.ReLU())
                policy_input_dim = hidden_dim
            
            policy_layers.append(nn.Linear(policy_input_dim, action_dim))
            self.policy_net = nn.Sequential(*policy_layers)
            
            # 独立的价值网络
            value_layers = []
            value_input_dim = state_dim
            
            for hidden_dim in hidden_dims:
                value_layers.append(nn.Linear(value_input_dim, hidden_dim))
                value_layers.append(nn.ReLU())
                value_input_dim = hidden_dim
            
            value_layers.append(nn.Linear(value_input_dim, 1))
            self.value_net = nn.Sequential(*value_layers)
    
    def forward(self, state: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            state (torch.Tensor): 状态张量
            
        Returns:
            Tuple[torch.Tensor, torch.Tensor]: 动作对数概率、状态价值
        """
        if self.shared_layers:
            features = self.feature_layer(state)
            action_logits = self.policy_head(features)
            state_values = self.value_head(features)
        else:
            action_logits = self.policy_net(state)
            state_values = self.value_net(state)
        
        return action_logits, state_values
    
    def get_action_probs(self, state: torch.Tensor) -> torch.Tensor:
        """
        获取动作概率分布
        
        Args:
            state (torch.Tensor): 状态张量
            
        Returns:
            torch.Tensor: 动作概率分布
        """
        action_logits, _ = self.forward(state)
        return F.softmax(action_logits, dim=-1)
    
    def get_action_log_probs(self, state: torch.Tensor) -> torch.Tensor:
        """
        获取动作对数概率分布
        
        Args:
            state (torch.Tensor): 状态张量
            
        Returns:
            torch.Tensor: 动作对数概率分布
        """
        action_logits, _ = self.forward(state)
        return F.log_softmax(action_logits, dim=-1)
    
    def get_value(self, state: torch.Tensor) -> torch.Tensor:
        """
        获取状态价值
        
        Args:
            state (torch.Tensor): 状态张量
            
        Returns:
            torch.Tensor: 状态价值
        """
        _, value = self.forward(state)
        return value


class PPO(PolicyBasedAlgorithm):
    """
    近端策略优化(PPO)算法
    
    实现PPO算法，使用裁剪目标函数，减少策略更新的过大变化。
    """
    
    def __init__(
        self, 
        state_shape: Tuple[int, ...], 
        action_shape: Tuple[int, ...],
        hidden_dims: List[int] = [128, 128],
        learning_rate: float = 0.0003,
        gamma: float = 0.99,
        gae_lambda: float = 0.95,
        clip_ratio: float = 0.2,
        value_coef: float = 0.5,
        entropy_coef: float = 0.01,
        max_grad_norm: float = 0.5,
        update_epochs: int = 4,
        shared_network: bool = True,
        batch_size: int = 64,
        device: str = None
    ):
        """
        初始化PPO算法
        
        Args:
            state_shape (Tuple[int, ...]): 状态形状
            action_shape (Tuple[int, ...]): 动作形状
            hidden_dims (List[int], optional): 隐藏层维度. Defaults to [128, 128].
            learning_rate (float, optional): 学习率. Defaults to 0.0003.
            gamma (float, optional): 折扣因子. Defaults to 0.99.
            gae_lambda (float, optional): GAE平滑参数. Defaults to 0.95.
            clip_ratio (float, optional): PPO裁剪比例. Defaults to 0.2.
            value_coef (float, optional): 价值损失系数. Defaults to 0.5.
            entropy_coef (float, optional): 熵正则化系数. Defaults to 0.01.
            max_grad_norm (float, optional): 梯度裁剪范数. Defaults to 0.5.
            update_epochs (int, optional): 每次更新的轮数. Defaults to 4.
            shared_network (bool, optional): 是否使用共享网络. Defaults to True.
            batch_size (int, optional): 批次大小. Defaults to 64.
            device (str, optional): 设备. Defaults to None.
        """
        super().__init__(state_shape, action_shape, gamma)
        
        # 设置设备
        self.device = device if device is not None else ('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 计算状态和动作维度
        self.state_dim = np.prod(state_shape)
        self.action_dim = np.prod(action_shape)
        
        # 创建PPO网络
        self.network = PPONetwork(self.state_dim, self.action_dim, hidden_dims, shared_network).to(self.device)
        
        # 创建优化器
        self.optimizer = optim.Adam(self.network.parameters(), lr=learning_rate)
        
        # 设置超参数
        self.gae_lambda = gae_lambda
        self.clip_ratio = clip_ratio
        self.value_coef = value_coef
        self.entropy_coef = entropy_coef
        self.max_grad_norm = max_grad_norm
        self.update_epochs = update_epochs
        self.batch_size = batch_size
        
        # 存储训练数据
        self.states = []
        self.actions = []
        self.action_log_probs = []
        self.rewards = []
        self.values = []
        self.dones = []
    
    def update(self, experience: Union[Experience, Batch]) -> Dict[str, float]:
        """
        使用经验数据更新模型
        
        Args:
            experience (Union[Experience, Batch]): 单个经验或经验批次
            
        Returns:
            Dict[str, float]: 更新指标，如损失值等
        """
        # 存储经验数据
        if isinstance(experience, Experience):
            self._store_experience(experience)
        elif isinstance(experience, Batch):
            for exp in experience.experiences:
                self._store_experience(exp)
        
        # 检查是否有足够的数据更新
        if len(self.states) < self.batch_size:
            return {'policy_loss': 0.0, 'value_loss': 0.0, 'entropy': 0.0}
        
        # 处理所有存储的数据
        return self._update_policy()
    
    def predict(self, state: Union[State, np.ndarray]) -> Tuple[List[float], float]:
        """
        预测状态的动作概率分布和价值
        
        Args:
            state (Union[State, np.ndarray]): 游戏状态或观察
            
        Returns:
            Tuple[List[float], float]: 动作概率分布、状态价值
        """
        # 处理状态
        if isinstance(state, State):
            # 将状态转换为特征向量
            state_array = state.to_dict().get('observation', np.zeros(self.state_dim))
            state_tensor = torch.FloatTensor(state_array).to(self.device)
        else:
            state_tensor = torch.FloatTensor(state).to(self.device)
        
        # 重塑状态
        if state_tensor.dim() == 1:
            state_tensor = state_tensor.unsqueeze(0)
        
        # 预测动作概率和价值
        with torch.no_grad():
            action_probs = self.network.get_action_probs(state_tensor).cpu().numpy()[0]
            value = self.network.get_value(state_tensor).cpu().numpy()[0][0]
        
        return list(action_probs), float(value)
    
    def save(self, path: str) -> None:
        """
        保存模型
        
        Args:
            path (str): 保存路径
        """
        # 确保目录存在
        os.makedirs(os.path.dirname(path), exist_ok=True)
        
        # 保存模型
        torch.save({
            'network_state_dict': self.network.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict()
        }, path)
    
    def load(self, path: str) -> None:
        """
        加载模型
        
        Args:
            path (str): 加载路径
        """
        # 加载模型
        checkpoint = torch.load(path, map_location=self.device)
        
        self.network.load_state_dict(checkpoint['network_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
    
    @property
    def name(self) -> str:
        """
        获取算法名称
        
        Returns:
            str: 算法名称
        """
        return "PPO"
    
    def _store_experience(self, experience: Experience) -> None:
        """
        存储经验数据
        
        Args:
            experience (Experience): 经验
        """
        # 处理状态
        if isinstance(experience.state, State):
            state_array = experience.state.to_dict().get('observation', np.zeros(self.state_dim))
        else:
            state_array = experience.state
        
        # 处理动作
        if isinstance(experience.action, Action):
            action = experience.action.to_dict().get('index', 0)
        else:
            action = experience.action
        
        # 转换为张量并预测动作概率和价值
        state_tensor = torch.FloatTensor(state_array).unsqueeze(0).to(self.device)
        with torch.no_grad():
            action_log_probs = self.network.get_action_log_probs(state_tensor)
            action_log_prob = action_log_probs[0, action].cpu().item()
            value = self.network.get_value(state_tensor).cpu().item()
        
        # 存储数据
        self.states.append(state_array)
        self.actions.append(action)
        self.action_log_probs.append(action_log_prob)
        self.rewards.append(experience.reward)
        self.values.append(value)
        self.dones.append(float(experience.done))
    
    def _update_policy(self) -> Dict[str, float]:
        """
        更新策略
        
        Returns:
            Dict[str, float]: 更新指标
        """
        # 计算优势和回报
        advantages, returns = self._compute_advantages_and_returns()
        
        # 转换为张量
        states = torch.FloatTensor(self.states).to(self.device)
        actions = torch.LongTensor(self.actions).to(self.device)
        old_action_log_probs = torch.FloatTensor(self.action_log_probs).to(self.device)
        returns = torch.FloatTensor(returns).to(self.device)
        advantages = torch.FloatTensor(advantages).to(self.device)
        
        # 归一化优势
        advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)
        
        # 多轮更新
        total_policy_loss = 0
        total_value_loss = 0
        total_entropy = 0
        
        for _ in range(self.update_epochs):
            # 生成批次索引
            indices = torch.randperm(len(states))
            
            # 按批次更新
            for start_idx in range(0, len(states), self.batch_size):
                # 获取批次索引
                batch_indices = indices[start_idx:start_idx + self.batch_size]
                
                # 提取批次数据
                batch_states = states[batch_indices]
                batch_actions = actions[batch_indices]
                batch_old_action_log_probs = old_action_log_probs[batch_indices]
                batch_returns = returns[batch_indices]
                batch_advantages = advantages[batch_indices]
                
                # 计算新的动作对数概率和价值
                action_logits, values = self.network(batch_states)
                values = values.squeeze(-1)
                action_probs = F.softmax(action_logits, dim=-1)
                action_log_probs = F.log_softmax(action_logits, dim=-1)
                batch_new_action_log_probs = action_log_probs.gather(1, batch_actions.unsqueeze(1)).squeeze(1)
                
                # 计算比率和裁剪目标
                ratio = torch.exp(batch_new_action_log_probs - batch_old_action_log_probs)
                surr1 = ratio * batch_advantages
                surr2 = torch.clamp(ratio, 1.0 - self.clip_ratio, 1.0 + self.clip_ratio) * batch_advantages
                
                # 计算策略损失
                policy_loss = -torch.min(surr1, surr2).mean()
                
                # 计算价值损失
                value_loss = F.mse_loss(values, batch_returns)
                
                # 计算熵
                entropy = -(action_probs * action_log_probs).sum(dim=-1).mean()
                
                # 计算总损失
                loss = policy_loss + self.value_coef * value_loss - self.entropy_coef * entropy
                
                # 更新网络
                self.optimizer.zero_grad()
                loss.backward()
                nn.utils.clip_grad_norm_(self.network.parameters(), self.max_grad_norm)
                self.optimizer.step()
                
                # 累加损失
                total_policy_loss += policy_loss.item()
                total_value_loss += value_loss.item()
                total_entropy += entropy.item()
        
        # 清空存储的数据
        self.states = []
        self.actions = []
        self.action_log_probs = []
        self.rewards = []
        self.values = []
        self.dones = []
        
        # 计算平均损失
        avg_policy_loss = total_policy_loss / (self.update_epochs * len(states) / self.batch_size)
        avg_value_loss = total_value_loss / (self.update_epochs * len(states) / self.batch_size)
        avg_entropy = total_entropy / (self.update_epochs * len(states) / self.batch_size)
        
        return {
            'policy_loss': avg_policy_loss,
            'value_loss': avg_value_loss,
            'entropy': avg_entropy
        }
    
    def _compute_advantages_and_returns(self) -> Tuple[np.ndarray, np.ndarray]:
        """
        计算广义优势估计(GAE)和回报
        
        Returns:
            Tuple[np.ndarray, np.ndarray]: 优势和回报
        """
        # 添加最后的值作为引导值
        values = np.array(self.values + [0.0])
        rewards = np.array(self.rewards)
        dones = np.array(self.dones)
        
        # 计算TD误差和优势
        deltas = rewards + self.gamma * values[1:] * (1 - dones) - values[:-1]
        
        # 计算GAE
        advantages = np.zeros_like(rewards)
        gae = 0
        for t in reversed(range(len(rewards))):
            gae = deltas[t] + self.gamma * self.gae_lambda * (1 - dones[t]) * gae
            advantages[t] = gae
        
        # 计算回报
        returns = advantages + np.array(self.values)
        
        return advantages, returns 