# MCTS日志增强功能使用指南

## 概述

MCTS日志增强功能为MCTS算法提供了详细的调试日志记录能力，帮助开发者深入了解MCTS搜索过程，优化算法性能。

## 主要功能

### 1. UCB计算过程追踪
- 记录每次UCB计算的详细信息
- 包含所有子节点的分数、访问次数、先验概率
- 记录最终选择的动作和计算时间

### 2. 节点扩展详情记录
- 记录节点扩展过程的详细信息
- 包含策略网络输出、新增子节点数量
- 记录扩展耗时和内存使用情况

### 3. 搜索路径完整追踪
- 记录完整的搜索路径
- 包含路径中每个节点的访问次数和价值
- 支持路径深度限制和状态信息记录

### 4. 性能统计监控
- 实时监控搜索性能指标
- 包含搜索时间、模拟次数、效率比率
- 支持内存使用监控和树深度统计

### 5. 可配置的日志系统
- 支持JSON和文本两种输出格式
- 可配置的日志级别和功能开关
- 支持异步日志和性能优化

## 快速开始

### 基本使用

```python
from cardgame_ai.algorithms.mcts_logging import MCTSLogger, LogConfig

# 创建日志配置
config = LogConfig(
    enabled=True,
    level="DEBUG",
    output_format="json",
    log_to_file=True,
    log_to_console=True
)

# 创建日志器
with MCTSLogger(config=config) as logger:
    # 记录UCB计算
    logger.log_ucb_calculation(
        parent_node=node,
        children_scores=scores,
        selected_action=action
    )
    
    # 记录节点扩展
    logger.log_node_expansion(
        node=node,
        policy_output=policy,
        expansion_time=0.001,
        num_children=5
    )
    
    # 记录搜索路径
    logger.log_search_path(
        path=search_path,
        path_value=0.7,
        depth=10
    )
```

### 与MCTS算法集成

MCTS日志功能已经集成到主要MCTS实现中，会自动记录搜索过程：

```python
from cardgame_ai.algorithms.mcts import MCTS

# MCTS会自动使用日志功能
mcts = MCTS(...)
action = mcts.run(state, num_simulations=1000)
```

## 配置选项

### 基础配置
- `enabled`: 是否启用日志记录
- `level`: 日志级别 (DEBUG, INFO, WARNING, ERROR)
- `output_format`: 输出格式 (json, text)

### 功能开关
- `enable_ucb_logging`: 是否记录UCB计算
- `enable_expansion_logging`: 是否记录节点扩展
- `enable_path_logging`: 是否记录搜索路径
- `enable_performance_logging`: 是否记录性能统计

### 输出配置
- `log_to_file`: 是否输出到文件
- `log_to_console`: 是否输出到控制台
- `log_file_path`: 日志文件路径

### 性能配置
- `async_logging`: 是否使用异步日志
- `buffer_size`: 日志缓冲区大小
- `flush_interval`: 刷新间隔

### 详细程度配置
- `max_children_logged`: UCB计算时最多记录的子节点数
- `max_path_depth_logged`: 搜索路径最大记录深度
- `include_game_state`: 是否包含游戏状态信息

## 日志格式

### JSON格式示例

```json
{
  "timestamp": "2025-06-03T19:58:05.343825",
  "session_id": "mcts_20250603_195805_2654",
  "level": "DEBUG",
  "type": "ucb_calculation",
  "data": {
    "parent_visits": 10,
    "children_count": 3,
    "children_scores": [
      {
        "action": 0,
        "visits": 5,
        "value": 0.6,
        "prior": 0.3,
        "ucb_score": 0.8
      }
    ],
    "selected_action": 0,
    "ucb_calculation_time": 0.001
  }
}
```

### 文本格式示例

```
[2025-06-03T19:58:05.343825] [mcts_20250603_195805_2654] [UCB_CALCULATION]
parent_visits: 10
children_count: 3
selected_action: 0
ucb_calculation_time: 0.001
```

## 性能影响

MCTS日志系统经过优化，对性能影响最小：

- **异步日志**: 使用异步写入，避免阻塞主线程
- **缓冲机制**: 批量写入日志，减少I/O操作
- **可配置过滤**: 支持按访问次数等条件过滤日志
- **性能监控**: 实时监控日志系统本身的性能影响

## 示例和演示

运行演示脚本查看完整功能：

```bash
python examples/mcts_logging_demo.py
```

运行测试验证功能：

```bash
python -m pytest tests/test_mcts_logging.py -v
```

## 日志分析

生成的日志可以用于：

1. **算法调试**: 分析UCB计算过程，发现算法问题
2. **性能优化**: 监控搜索效率，识别性能瓶颈
3. **行为分析**: 理解MCTS的搜索策略和决策过程
4. **参数调优**: 基于详细日志调整MCTS参数

## 注意事项

1. **存储空间**: 详细日志会占用较多存储空间，建议定期清理
2. **性能影响**: 虽然已优化，但大量日志仍可能影响性能
3. **隐私保护**: 日志可能包含游戏状态信息，注意数据保护
4. **配置管理**: 生产环境建议关闭详细日志，只保留性能统计

## 故障排除

### 常见问题

1. **导入失败**: 确保已正确安装依赖
2. **日志文件权限**: 确保有写入日志目录的权限
3. **内存使用过高**: 调整缓冲区大小或关闭部分日志功能
4. **性能下降**: 启用异步日志或减少日志详细程度

### 调试建议

1. 先使用演示脚本验证功能
2. 从简单配置开始，逐步增加功能
3. 监控日志文件大小和系统性能
4. 根据需要调整配置参数

## 更多信息

- 查看技术债务评估报告了解设计原理
- 查看实施路线图了解开发过程
- 查看测试用例了解详细用法
- 查看配置文件了解所有选项
