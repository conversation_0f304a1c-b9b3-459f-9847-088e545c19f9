# 斗地主AI项目统一依赖文件
# 合并了核心依赖、自动部署系统依赖和完整功能依赖
# 更新时间: 2025-06-01 (合并所有依赖文件)
#
# ============================================================================
# 依赖分级说明
# ============================================================================
#
# 🔥 核心依赖 (必需) - 标记为 [CORE]
#    运行训练脚本和自动部署系统的最小依赖集合
#    包括: torch, numpy, pyyaml, jinja2, psutil, nvidia-ml-py
#
# 🚀 自动部署系统 (推荐) - 标记为 [AUTO-DEPLOY]
#    自动硬件检测、参数调优、配置生成功能
#    包括: 硬件检测、模板引擎、跨平台支持
#
# 📊 可视化和监控 (推荐) - 标记为 [VIZ]
#    训练监控、数据可视化、进度显示
#    包括: matplotlib, seaborn, plotly, tqdm
#
# 🧪 开发和测试 (可选) - 标记为 [DEV]
#    代码质量、测试框架、调试工具
#    包括: pytest, black, flake8, memory-profiler
#
# 🎯 高级功能 (可选) - 标记为 [ADVANCED]
#    图神经网络、贝叶斯优化、元学习等
#    包括: torch-geometric, bayesian-optimization, higher
#
# ============================================================================
# 安装方式
# ============================================================================
#
# 完整安装 (推荐):
# pip install -r requirements.txt
#
# 最小安装 (仅核心功能):
# pip install torch>=2.0.0 numpy>=1.21.0 PyYAML>=6.0 Jinja2>=3.0.0 psutil>=5.8.0 nvidia-ml-py>=12.535.133
#
# 核心 + 自动部署:
# pip install torch>=2.0.0 numpy>=1.21.0 PyYAML>=6.0 Jinja2>=3.0.0 psutil>=5.8.0 nvidia-ml-py>=12.535.133 tqdm>=4.62.0 colorlog>=6.0.0

# ============================================================================
# 核心机器学习框架 [CORE] 🔥
# ============================================================================

# PyTorch 深度学习框架 (核心依赖)
torch>=2.0.0                    # [CORE] 深度学习框架
torchvision>=0.15.0             # [CORE] 计算机视觉
torchaudio>=2.0.0               # [CORE] 音频处理

# NumPy 数值计算库 (基础依赖)
numpy>=1.21.0                   # [CORE] 数值计算

# SciPy 科学计算库
scipy>=1.7.0                    # [VIZ] 科学计算

# ============================================================================
# 强化学习和游戏环境 [ADVANCED] 🎯
# ============================================================================

# OpenAI Gym 强化学习环境接口
gym>=0.21.0                     # [ADVANCED] 强化学习环境
gymnasium>=0.26.0               # [ADVANCED] 新版Gym

# 图神经网络库 (用于GNN模型)
torch-geometric>=2.3.0          # [ADVANCED] 图神经网络
torch-scatter>=2.1.0            # [ADVANCED] 图计算加速
torch-sparse>=0.6.0             # [ADVANCED] 稀疏张量

# 网络图处理库
networkx>=2.6.0                 # [ADVANCED] 图算法

# ============================================================================
# 数据处理和分析 [VIZ] 📊
# ============================================================================

# Pandas 数据分析库
pandas>=1.3.0                   # [VIZ] 数据分析

# 数据验证库
pydantic>=1.8.0                 # [DEV] 数据验证

# JSON处理增强
jsonschema>=4.0.0               # [DEV] JSON验证

# ============================================================================
# 可视化和绘图 [VIZ] 📊
# ============================================================================

# Matplotlib 绘图库
matplotlib>=3.5.0               # [VIZ] 基础绘图

# Seaborn 统计可视化
seaborn>=0.11.0                 # [VIZ] 统计可视化

# Plotly 交互式可视化
plotly>=5.0.0                   # [VIZ] 交互式图表

# ============================================================================
# 用户界面 [ADVANCED] 🎯
# ============================================================================

# PySide6 Qt界面框架
PySide6>=6.4.0                  # [ADVANCED] GUI界面

# ============================================================================
# 进度条和日志 [AUTO-DEPLOY] 🚀
# ============================================================================

# 进度条库
tqdm>=4.62.0                    # [AUTO-DEPLOY] 进度显示

# 日志配置增强
colorlog>=6.0.0                 # [AUTO-DEPLOY] 彩色日志

# ============================================================================
# 配置文件处理 [CORE] 🔥
# ============================================================================

# YAML配置文件处理
PyYAML>=6.0                     # [CORE] YAML配置

# TOML配置文件处理
toml>=0.10.0                    # [DEV] TOML配置

# Jinja2模板引擎 (用于自动配置生成)
Jinja2>=3.0.0                   # [AUTO-DEPLOY] 模板引擎

# ============================================================================
# 网络和通信 [DEV] 🧪
# ============================================================================

# HTTP请求库
requests>=2.26.0                # [DEV] HTTP请求

# WebSocket支持
websockets>=10.0                # [ADVANCED] WebSocket

# ============================================================================
# 数据库和存储 [ADVANCED] 🎯
# ============================================================================

# SQLite数据库增强
sqlite3                         # [ADVANCED] 数据库

# 内存数据库
redis>=4.0.0                    # [ADVANCED] 缓存数据库

# ============================================================================
# 并行计算和多进程 [CORE] 🔥
# ============================================================================

# 多进程增强
multiprocessing-logging>=0.3.0  # [DEV] 多进程日志

# 作业队列
joblib>=1.1.0                   # [VIZ] 并行计算

# 进程池增强
psutil>=5.8.0                   # [CORE] 系统信息

# GPU监控和检测 (用于自动硬件检测)
nvidia-ml-py>=12.535.133        # [AUTO-DEPLOY] GPU检测

# ============================================================================
# 数学和统计
# ============================================================================

# 统计分布库
statsmodels>=0.13.0

# 优化算法库
scikit-optimize>=0.9.0

# 机器学习库
scikit-learn>=1.0.0

# ============================================================================
# 文件处理和压缩
# ============================================================================

# 压缩文件处理
zipfile36>=0.1.3

# 文件监控
watchdog>=2.1.0

# ============================================================================
# 时间和日期处理
# ============================================================================

# 时间处理增强
python-dateutil>=2.8.0

# ============================================================================
# 系统和平台
# ============================================================================

# 跨平台路径处理
pathlib2>=2.3.0

# 系统信息获取
platform

# 操作系统接口
os

# ============================================================================
# 开发和调试工具
# ============================================================================

# 内存分析
memory-profiler>=0.60.0

# 性能分析
line-profiler>=3.3.0

# ============================================================================
# 测试框架
# ============================================================================

# 单元测试框架
pytest>=7.0.0
pytest-cov>=3.0.0
pytest-xdist>=2.5.0

# ============================================================================
# 代码质量工具
# ============================================================================

# 代码格式化
black>=22.0.0
isort>=5.10.0

# 代码检查
flake8>=4.0.0
pylint>=2.12.0

# 类型检查
mypy>=0.950

# ============================================================================
# 文档生成
# ============================================================================

# 文档生成工具
sphinx>=4.0.0
sphinx-rtd-theme>=1.0.0

# Markdown文档
mkdocs>=1.4.0
mkdocs-material>=8.5.0

# ============================================================================
# 打包和分发
# ============================================================================

# 应用打包
PyInstaller>=5.7.0

# 包管理
setuptools>=60.0.0
wheel>=0.37.0

# ============================================================================
# 特殊算法库
# ============================================================================

# 高阶梯度计算 (用于MAML等元学习算法)
higher>=0.2.1

# 贝叶斯优化
bayesian-optimization>=1.4.0

# 遗传算法
deap>=1.3.0

# ============================================================================
# 图像处理 (如果需要可视化卡牌等)
# ============================================================================

# 图像处理库
Pillow>=8.3.0

# OpenCV (可选，用于高级图像处理)
opencv-python>=4.5.0

# ============================================================================
# 音频处理 (如果需要音效)
# ============================================================================

# 音频处理库
soundfile>=0.10.0

# ============================================================================
# 加密和安全
# ============================================================================

# 加密库
cryptography>=3.4.0

# ============================================================================
# 其他实用工具
# ============================================================================

# 命令行参数解析增强
click>=8.0.0

# 环境变量管理
python-dotenv>=0.19.0

# 缓存工具
cachetools>=4.2.0

# 重试机制
tenacity>=8.0.0

# 随机数生成增强
randomgen>=1.21.0

# ============================================================================
# 自动部署系统依赖 (新增)
# ============================================================================

# 以上依赖已包含自动部署系统所需的核心依赖:
# - PyYAML>=6.0 (配置文件处理)
# - Jinja2>=3.0.0 (模板引擎)
# - psutil>=5.8.0 (系统信息获取)
# - nvidia-ml-py>=12.535.133 (GPU检测)

# 自动部署系统功能:
# 1. 硬件检测: GPU、CPU、内存、存储自动检测
# 2. 参数调优: 基于硬件配置自动优化训练参数
# 3. 配置生成: 动态生成YAML配置文件
# 4. 部署管理: 本地/远程/批量部署支持
# 5. 跨平台: Windows和Ubuntu系统支持

# 使用方法:
# python cardgame_ai/zhuchengxu/auto_deploy.py
# python cardgame_ai/zhuchengxu/auto_deploy.py --help

# ============================================================================
# 版本兼容性说明
# ============================================================================

# Python版本要求: >=3.8
# CUDA版本要求: >=11.8 (如果使用GPU)
# 操作系统: Windows 10+, Linux, macOS

# 自动部署系统测试环境:
# - Windows 11 + RTX 3080 (20GB) + 64GB RAM ✅
# - Ubuntu 24.04 + 多GPU配置 (待测试)
# - 单GPU和多GPU配置均支持

# ============================================================================
# 快速安装指南
# ============================================================================

# 🔥 最小核心安装 (仅运行训练和自动部署):
# pip install torch>=2.0.0 torchvision>=0.15.0 torchaudio>=2.0.0 numpy>=1.21.0 PyYAML>=6.0 Jinja2>=3.0.0 psutil>=5.8.0 nvidia-ml-py>=12.535.133 tqdm>=4.62.0 colorlog>=6.0.0

# 📊 核心 + 可视化:
# pip install torch>=2.0.0 torchvision>=0.15.0 numpy>=1.21.0 PyYAML>=6.0 Jinja2>=3.0.0 psutil>=5.8.0 nvidia-ml-py>=12.535.133 tqdm>=4.62.0 colorlog>=6.0.0 matplotlib>=3.5.0 pandas>=1.3.0

# 🧪 核心 + 开发工具:
# pip install torch>=2.0.0 torchvision>=0.15.0 numpy>=1.21.0 PyYAML>=6.0 Jinja2>=3.0.0 psutil>=5.8.0 nvidia-ml-py>=12.535.133 tqdm>=4.62.0 colorlog>=6.0.0 pytest>=7.0.0 scikit-learn>=1.0.0

# 🚀 完整安装 (推荐):
# pip install -r requirements.txt

# ============================================================================
# 依赖统计
# ============================================================================

# 总依赖数: ~80个包
# 核心依赖 [CORE]: 8个包 (~100MB)
# 自动部署 [AUTO-DEPLOY]: +2个包 (~10MB)
# 可视化 [VIZ]: +5个包 (~200MB)
# 开发工具 [DEV]: +10个包 (~100MB)
# 高级功能 [ADVANCED]: +20个包 (~500MB)

# 建议安装顺序:
# 1. 先安装核心依赖测试基本功能
# 2. 再安装自动部署系统依赖
# 3. 根据需要添加可视化和开发工具
# 4. 最后安装高级功能依赖

# ============================================================================
# 故障排除
# ============================================================================

# 如果遇到安装问题:
# 1. 更新pip: python -m pip install --upgrade pip
# 2. 清理缓存: pip cache purge
# 3. 分批安装: 先安装核心依赖，再安装其他
# 4. 使用国内镜像: pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 常见问题:
# - torch安装失败: 访问 https://pytorch.org/ 获取适合的安装命令
# - nvidia-ml-py安装失败: 确保已安装NVIDIA驱动
# - 某些包在Windows上安装失败: 考虑使用conda安装
