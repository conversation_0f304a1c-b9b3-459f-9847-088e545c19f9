#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
优化数据加载模块

提供高性能的数据加载和预处理功能，包括：
- 多进程数据加载
- 特征缓存系统
- 批次预组装
- 内存优化
- HDF5压缩存储
- 内存映射文件
- 流水线数据加载
"""

import os
import time
import logging
import threading
import multiprocessing
from typing import Dict, List, Any, Optional, Tuple, Union
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
import h5py
import pickle
import lz4.frame
import mmap
from collections import deque
import psutil

from cardgame_ai.core.base import Experience, Batch

logger = logging.getLogger(__name__)


class OptimizedFeatureCache:
    """优化的特征缓存系统"""
    
    def __init__(self, max_size_gb: float = 8.0):
        """
        初始化特征缓存
        
        Args:
            max_size_gb: 最大缓存大小(GB)
        """
        self.max_size_bytes = int(max_size_gb * 1024 * 1024 * 1024)
        self.cache = {}
        self.access_times = {}
        self.current_size = 0
        self.lock = threading.RLock()
        
        logger.info(f"初始化特征缓存，最大大小: {max_size_gb}GB")
    
    def get(self, key: str) -> Optional[np.ndarray]:
        """获取缓存的特征"""
        with self.lock:
            if key in self.cache:
                self.access_times[key] = time.time()
                return self.cache[key]
            return None
    
    def put(self, key: str, features: np.ndarray) -> None:
        """存储特征到缓存"""
        with self.lock:
            feature_size = features.nbytes
            
            # 检查是否需要清理缓存
            while self.current_size + feature_size > self.max_size_bytes and self.cache:
                self._evict_lru()
            
            # 存储特征
            self.cache[key] = features.copy()
            self.access_times[key] = time.time()
            self.current_size += feature_size
    
    def _evict_lru(self) -> None:
        """移除最近最少使用的特征"""
        if not self.cache:
            return
        
        # 找到最久未访问的key
        oldest_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
        
        # 移除特征
        features = self.cache.pop(oldest_key)
        self.access_times.pop(oldest_key)
        self.current_size -= features.nbytes
        
        logger.debug(f"从缓存中移除特征: {oldest_key}")


class StreamingDataset(Dataset):
    """流式数据集，支持大规模数据加载"""
    
    def __init__(self, data_path: str, chunk_size: int = 1024, 
                 use_memory_map: bool = True, compression: str = "lz4"):
        """
        初始化流式数据集
        
        Args:
            data_path: 数据文件路径
            chunk_size: 数据块大小
            use_memory_map: 是否使用内存映射
            compression: 压缩格式
        """
        self.data_path = data_path
        self.chunk_size = chunk_size
        self.use_memory_map = use_memory_map
        self.compression = compression
        
        # 初始化数据索引
        self._build_index()
        
        # 特征缓存
        self.feature_cache = OptimizedFeatureCache()
        
        logger.info(f"初始化流式数据集: {data_path}, 样本数: {len(self.index)}")
    
    def _build_index(self) -> None:
        """构建数据索引"""
        self.index = []
        
        if self.data_path.endswith('.h5') or self.data_path.endswith('.hdf5'):
            # HDF5文件索引
            with h5py.File(self.data_path, 'r') as f:
                if 'experiences' in f:
                    self.index = list(range(len(f['experiences'])))
        else:
            # 其他格式文件索引
            if os.path.exists(self.data_path):
                # 简单的行数统计
                with open(self.data_path, 'rb') as f:
                    self.index = list(range(sum(1 for _ in f)))
    
    def __len__(self) -> int:
        """返回数据集大小"""
        return len(self.index)
    
    def __getitem__(self, idx: int) -> Dict[str, Any]:
        """获取单个样本"""
        # 检查缓存
        cache_key = f"{self.data_path}_{idx}"
        cached_features = self.feature_cache.get(cache_key)
        if cached_features is not None:
            return {'features': cached_features, 'index': idx}
        
        # 加载数据
        data = self._load_sample(idx)
        
        # 预处理特征
        features = self._preprocess_features(data)
        
        # 缓存特征
        self.feature_cache.put(cache_key, features)
        
        return {'features': features, 'index': idx}
    
    def _load_sample(self, idx: int) -> Dict[str, Any]:
        """加载单个样本数据"""
        if self.data_path.endswith('.h5') or self.data_path.endswith('.hdf5'):
            # 从HDF5文件加载
            with h5py.File(self.data_path, 'r') as f:
                if 'experiences' in f:
                    return dict(f['experiences'][idx])
        
        # 其他格式的加载逻辑
        return {}
    
    def _preprocess_features(self, data: Dict[str, Any]) -> np.ndarray:
        """预处理特征数据"""
        # 这里实现具体的特征预处理逻辑
        # 根据斗地主游戏的特点进行特征提取
        
        # 示例：创建基础特征向量
        features = np.zeros(656, dtype=np.float32)  # 斗地主标准特征维度
        
        # 如果有状态数据，进行特征提取
        if 'state' in data:
            state_data = data['state']
            # 实现状态特征提取逻辑
            pass
        
        return features


class OptimizedDataLoader:
    """优化的数据加载器"""
    
    def __init__(self, dataset: Dataset, batch_size: int = 256, 
                 num_workers: int = 12, pin_memory: bool = True,
                 prefetch_factor: int = 6, persistent_workers: bool = True):
        """
        初始化优化数据加载器
        
        Args:
            dataset: 数据集
            batch_size: 批次大小
            num_workers: 工作进程数
            pin_memory: 是否固定内存
            prefetch_factor: 预取因子
            persistent_workers: 是否持久化worker
        """
        self.dataset = dataset
        self.batch_size = batch_size
        self.num_workers = num_workers
        self.pin_memory = pin_memory
        self.prefetch_factor = prefetch_factor
        self.persistent_workers = persistent_workers
        
        # 创建PyTorch数据加载器
        self.dataloader = DataLoader(
            dataset=dataset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=num_workers,
            pin_memory=pin_memory,
            prefetch_factor=prefetch_factor,
            persistent_workers=persistent_workers,
            multiprocessing_context='spawn' if num_workers > 0 else None,
            drop_last=True  # 确保批次大小一致
        )
        
        # 预取队列
        self.prefetch_queue = deque(maxlen=prefetch_factor * 2)
        self.prefetch_thread = None
        self.stop_prefetch = threading.Event()
        
        logger.info(f"初始化优化数据加载器: batch_size={batch_size}, num_workers={num_workers}")
    
    def start_prefetch(self) -> None:
        """启动预取线程"""
        if self.prefetch_thread is None or not self.prefetch_thread.is_alive():
            self.stop_prefetch.clear()
            self.prefetch_thread = threading.Thread(target=self._prefetch_worker, daemon=True)
            self.prefetch_thread.start()
            logger.info("启动数据预取线程")
    
    def stop_prefetch_thread(self) -> None:
        """停止预取线程"""
        if self.prefetch_thread and self.prefetch_thread.is_alive():
            self.stop_prefetch.set()
            self.prefetch_thread.join(timeout=5.0)
            logger.info("停止数据预取线程")
    
    def _prefetch_worker(self) -> None:
        """预取工作线程"""
        dataloader_iter = iter(self.dataloader)
        
        while not self.stop_prefetch.is_set():
            try:
                # 如果队列未满，预取下一个批次
                if len(self.prefetch_queue) < self.prefetch_queue.maxlen:
                    batch = next(dataloader_iter)
                    self.prefetch_queue.append(batch)
                else:
                    time.sleep(0.001)  # 短暂休眠
            except StopIteration:
                # 重新创建迭代器
                dataloader_iter = iter(self.dataloader)
            except Exception as e:
                logger.error(f"预取线程错误: {e}")
                time.sleep(0.1)
    
    def get_batch(self) -> Optional[Dict[str, Any]]:
        """获取一个批次的数据"""
        if self.prefetch_queue:
            return self.prefetch_queue.popleft()
        
        # 如果预取队列为空，直接从数据加载器获取
        try:
            return next(iter(self.dataloader))
        except StopIteration:
            return None
    
    def __iter__(self):
        """迭代器接口"""
        return iter(self.dataloader)
    
    def __len__(self) -> int:
        """返回批次数量"""
        return len(self.dataloader)


def create_optimized_dataloader(data_path: str, config: Dict[str, Any]) -> OptimizedDataLoader:
    """
    创建优化的数据加载器
    
    Args:
        data_path: 数据文件路径
        config: 配置参数
        
    Returns:
        OptimizedDataLoader: 优化的数据加载器
    """
    # 创建流式数据集
    dataset = StreamingDataset(
        data_path=data_path,
        chunk_size=config.get('chunk_size', 1024),
        use_memory_map=config.get('use_memory_map', True),
        compression=config.get('compression', 'lz4')
    )
    
    # 创建优化数据加载器
    dataloader = OptimizedDataLoader(
        dataset=dataset,
        batch_size=config.get('batch_size', 256),
        num_workers=config.get('num_workers', 12),
        pin_memory=config.get('pin_memory', True),
        prefetch_factor=config.get('prefetch_factor', 6),
        persistent_workers=config.get('persistent_workers', True)
    )
    
    # 启动预取
    dataloader.start_prefetch()
    
    return dataloader
