"""
训练参数管理器

提供统一的参数验证、传递和管理功能，确保训练脚本参数的一致性。
"""

import os
import logging
from typing import Dict, Any, Optional, List, Union, Tuple
from dataclasses import dataclass, field
import argparse

logger = logging.getLogger(__name__)


@dataclass
class TrainingParameters:
    """训练参数数据类"""
    # 基础参数
    game: str = "doudizhu"
    algorithm: str = "efficient_zero"
    config_file: str = "configs/doudizhu/efficient_zero_config.yaml"
    device: Optional[str] = None
    
    # 训练控制参数
    resume: bool = False
    checkpoint_dir: Optional[str] = None
    num_workers: int = 4
    force_continue: bool = False
    
    # 环境检查参数
    skip_dependency_check: bool = False
    skip_environment_check: bool = False
    environment_warn_only: bool = False
    min_gpu_memory: float = 4.0
    min_system_memory: float = 8.0
    min_cpu_cores: int = 4
    
    # 日志参数
    log_level: str = "INFO"
    log_dir: str = "logs"
    enable_structured_logging: bool = True
    enable_metrics_logging: bool = True
    
    # 奖励系统参数
    reward_config_file: Optional[str] = None
    enable_unified_rewards: bool = True
    
    # 验证状态
    _validated: bool = field(default=False, init=False)
    _validation_errors: List[str] = field(default_factory=list, init=False)


class TrainingParameterManager:
    """训练参数管理器"""
    
    def __init__(self, project_root: Optional[str] = None):
        """
        初始化参数管理器
        
        Args:
            project_root: 项目根目录
        """
        self.project_root = project_root or self._find_project_root()
        self.valid_games = ['doudizhu', 'poker']
        self.valid_algorithms = ['dqn', 'muzero', 'efficient_zero', 'mcts']
        
    def _find_project_root(self) -> str:
        """查找项目根目录"""
        current_dir = os.path.dirname(os.path.abspath(__file__))
        while current_dir != os.path.dirname(current_dir):
            if os.path.exists(os.path.join(current_dir, 'cardgame_ai')):
                return current_dir
            current_dir = os.path.dirname(current_dir)
        return os.getcwd()
    
    def create_argument_parser(self) -> argparse.ArgumentParser:
        """创建标准化的参数解析器"""
        parser = argparse.ArgumentParser(
            description='斗地主AI训练系统',
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
使用示例:
  基础训练:
    python train_main.py --game doudizhu --algo efficient_zero
    
  指定配置文件:
    python train_main.py --game doudizhu --algo efficient_zero --config configs/my_config.yaml
    
  多GPU训练:
    python train_main.py --game doudizhu --algo efficient_zero --device cuda:0,cuda:1
    
  断点续训:
    python train_main.py --game doudizhu --algo efficient_zero --resume --checkpoint-dir models/checkpoints
            """
        )
        
        # 基础参数组
        basic_group = parser.add_argument_group('基础参数')
        basic_group.add_argument(
            '--game', type=str, required=True, choices=self.valid_games,
            help='要训练的游戏类型'
        )
        basic_group.add_argument(
            '--algo', type=str, required=True, choices=self.valid_algorithms,
            help='要使用的训练算法'
        )
        basic_group.add_argument(
            '--config', type=str, 
            help='配置文件路径（如不指定将根据游戏和算法自动选择）'
        )
        basic_group.add_argument(
            '--device', type=str,
            help='训练设备 (例如: "cuda:0", "cpu", "cuda:0,cuda:1")'
        )
        
        # 训练控制参数组
        training_group = parser.add_argument_group('训练控制')
        training_group.add_argument(
            '--resume', action='store_true',
            help='从最新checkpoint恢复训练'
        )
        training_group.add_argument(
            '--checkpoint-dir', type=str,
            help='checkpoint保存目录'
        )
        training_group.add_argument(
            '--num-workers', type=int, default=4,
            help='DataLoader工作进程数'
        )
        training_group.add_argument(
            '--force-continue', action='store_true',
            help='强制继续执行，忽略检查失败'
        )
        
        # 环境检查参数组
        env_group = parser.add_argument_group('环境检查')
        env_group.add_argument(
            '--skip-dependency-check', action='store_true',
            help='跳过依赖检查'
        )
        env_group.add_argument(
            '--skip-environment-check', action='store_true',
            help='跳过环境检查'
        )
        env_group.add_argument(
            '--environment-warn-only', action='store_true',
            help='环境检查失败时仅警告'
        )
        env_group.add_argument(
            '--min-gpu-memory', type=float, default=4.0,
            help='最低GPU显存要求(GB)'
        )
        env_group.add_argument(
            '--min-system-memory', type=float, default=8.0,
            help='最低系统内存要求(GB)'
        )
        env_group.add_argument(
            '--min-cpu-cores', type=int, default=4,
            help='最低CPU核心数要求'
        )
        
        # 日志参数组
        log_group = parser.add_argument_group('日志配置')
        log_group.add_argument(
            '--log-level', type=str, default='INFO',
            choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
            help='日志级别'
        )
        log_group.add_argument(
            '--log-dir', type=str, default='logs',
            help='日志目录'
        )
        log_group.add_argument(
            '--disable-structured-logging', action='store_true',
            help='禁用结构化日志'
        )
        log_group.add_argument(
            '--disable-metrics-logging', action='store_true',
            help='禁用指标日志'
        )
        
        # 奖励系统参数组
        reward_group = parser.add_argument_group('奖励系统')
        reward_group.add_argument(
            '--reward-config', type=str,
            help='奖励配置文件路径'
        )
        reward_group.add_argument(
            '--disable-unified-rewards', action='store_true',
            help='禁用统一奖励系统'
        )
        
        return parser
    
    def parse_and_validate(self, args: Optional[List[str]] = None) -> TrainingParameters:
        """
        解析并验证参数
        
        Args:
            args: 命令行参数列表，如果为None则从sys.argv解析
            
        Returns:
            验证后的训练参数
        """
        parser = self.create_argument_parser()
        parsed_args = parser.parse_args(args)
        
        # 转换为TrainingParameters对象
        params = TrainingParameters(
            game=parsed_args.game,
            algorithm=parsed_args.algo,
            config_file=parsed_args.config or self._get_default_config(parsed_args.game, parsed_args.algo),
            device=parsed_args.device,
            resume=parsed_args.resume,
            checkpoint_dir=parsed_args.checkpoint_dir,
            num_workers=parsed_args.num_workers,
            force_continue=parsed_args.force_continue,
            skip_dependency_check=parsed_args.skip_dependency_check,
            skip_environment_check=parsed_args.skip_environment_check,
            environment_warn_only=parsed_args.environment_warn_only,
            min_gpu_memory=parsed_args.min_gpu_memory,
            min_system_memory=parsed_args.min_system_memory,
            min_cpu_cores=parsed_args.min_cpu_cores,
            log_level=parsed_args.log_level,
            log_dir=parsed_args.log_dir,
            enable_structured_logging=not parsed_args.disable_structured_logging,
            enable_metrics_logging=not parsed_args.disable_metrics_logging,
            reward_config_file=parsed_args.reward_config,
            enable_unified_rewards=not parsed_args.disable_unified_rewards
        )
        
        # 验证参数
        self._validate_parameters(params)
        
        return params
    
    def _get_default_config(self, game: str, algorithm: str) -> str:
        """获取默认配置文件路径"""
        config_map = {
            ('doudizhu', 'efficient_zero'): 'configs/doudizhu/efficient_zero_config.yaml',
            ('doudizhu', 'muzero'): 'configs/doudizhu/muzero_config.yaml',
            ('doudizhu', 'dqn'): 'configs/doudizhu/dqn_config.yaml',
            ('doudizhu', 'mcts'): 'configs/doudizhu/mcts_config.yaml',
        }
        
        return config_map.get((game, algorithm), 'configs/doudizhu/efficient_zero_config.yaml')
    
    def _validate_parameters(self, params: TrainingParameters) -> None:
        """验证参数的有效性"""
        errors = []
        
        # 验证游戏类型
        if params.game not in self.valid_games:
            errors.append(f"无效的游戏类型: {params.game}")
            
        # 验证算法类型
        if params.algorithm not in self.valid_algorithms:
            errors.append(f"无效的算法类型: {params.algorithm}")
            
        # 验证配置文件
        config_path = params.config_file
        if not os.path.isabs(config_path):
            config_path = os.path.join(self.project_root, config_path)
        if not os.path.exists(config_path):
            errors.append(f"配置文件不存在: {config_path}")
            
        # 验证设备格式
        if params.device:
            if not self._validate_device_format(params.device):
                errors.append(f"无效的设备格式: {params.device}")
                
        # 验证数值参数
        if params.num_workers < 0:
            errors.append("num_workers必须为非负数")
        if params.min_gpu_memory < 0:
            errors.append("min_gpu_memory必须为非负数")
        if params.min_system_memory < 0:
            errors.append("min_system_memory必须为非负数")
        if params.min_cpu_cores < 1:
            errors.append("min_cpu_cores必须至少为1")
            
        # 验证奖励配置文件
        if params.reward_config_file:
            reward_config_path = params.reward_config_file
            if not os.path.isabs(reward_config_path):
                reward_config_path = os.path.join(self.project_root, reward_config_path)
            if not os.path.exists(reward_config_path):
                errors.append(f"奖励配置文件不存在: {reward_config_path}")
        
        # 更新验证状态
        params._validation_errors = errors
        params._validated = len(errors) == 0
        
        if errors:
            error_msg = "参数验证失败:\n" + "\n".join(f"  - {error}" for error in errors)
            logger.error(error_msg)
            if not params.force_continue:
                raise ValueError(error_msg)
    
    def _validate_device_format(self, device: str) -> bool:
        """验证设备格式"""
        if device.lower() == 'cpu':
            return True
            
        if device.lower() == 'cuda':
            return True
            
        if device.lower().startswith('cuda:'):
            try:
                device_id = int(device.split(':')[1])
                return device_id >= 0
            except (ValueError, IndexError):
                return False
                
        # 多GPU格式 (cuda:0,cuda:1)
        if ',' in device:
            devices = [d.strip() for d in device.split(',')]
            return all(self._validate_device_format(d) for d in devices)
            
        return False
    
    def to_dict(self, params: TrainingParameters) -> Dict[str, Any]:
        """将参数转换为字典格式"""
        return {
            'game': params.game,
            'algorithm': params.algorithm,
            'config_file': params.config_file,
            'device': params.device,
            'resume': params.resume,
            'checkpoint_dir': params.checkpoint_dir,
            'num_workers': params.num_workers,
            'force_continue': params.force_continue,
            'skip_dependency_check': params.skip_dependency_check,
            'skip_environment_check': params.skip_environment_check,
            'environment_warn_only': params.environment_warn_only,
            'min_gpu_memory': params.min_gpu_memory,
            'min_system_memory': params.min_system_memory,
            'min_cpu_cores': params.min_cpu_cores,
            'log_level': params.log_level,
            'log_dir': params.log_dir,
            'enable_structured_logging': params.enable_structured_logging,
            'enable_metrics_logging': params.enable_metrics_logging,
            'reward_config_file': params.reward_config_file,
            'enable_unified_rewards': params.enable_unified_rewards,
            'validated': params._validated,
            'validation_errors': params._validation_errors
        }
    
    def merge_with_config(
        self, 
        params: TrainingParameters, 
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        将参数与配置文件合并
        
        Args:
            params: 训练参数
            config: 配置文件内容
            
        Returns:
            合并后的配置
        """
        merged_config = config.copy()
        
        # 更新基础配置
        if params.device:
            merged_config['device'] = params.device
            
        # 更新checkpoint配置
        if 'checkpoint' not in merged_config:
            merged_config['checkpoint'] = {}
        if params.checkpoint_dir:
            merged_config['checkpoint']['dir'] = params.checkpoint_dir
        merged_config['resume'] = params.resume
        
        # 更新日志配置
        if 'logging' not in merged_config:
            merged_config['logging'] = {}
        merged_config['logging'].update({
            'level': params.log_level,
            'log_dir': params.log_dir,
            'structured': params.enable_structured_logging,
            'metrics': params.enable_metrics_logging
        })
        
        # 更新奖励配置
        if params.enable_unified_rewards:
            merged_config['unified_rewards'] = {
                'enabled': True,
                'config_file': params.reward_config_file or 'configs/doudizhu/reward_config.json'
            }
            
        return merged_config
