# 配置文件说明

## 📁 目录结构

```
configs/
├── base.yaml               # 🔧 项目基础配置
├── training/               # 📁 训练相关配置
│   ├── efficient_zero.yaml # EfficientZero训练配置
│   └── optimized.yaml      # 优化训练配置
├── algorithms/             # 📁 算法特定配置
│   └── efficient_zero/     # EfficientZero算法配置
│       ├── base.yaml       # 基础算法配置
│       └── training.yaml   # 训练特定配置
├── environments/           # 📁 环境特定配置
│   └── doudizhu/          # 斗地主环境配置
│       ├── efficient_zero_config.yaml
│       ├── hrl_config.yaml
│       ├── random.yaml
│       └── reward_config.json
└── hardware/              # 📁 硬件特定配置
    ├── single_gpu.yaml    # 单GPU配置
    └── multi_gpu.yaml     # 多GPU配置
```

## 🔧 配置文件说明

### base.yaml
项目的基础配置文件，定义了：
- 项目基本信息
- 默认配置引用
- 设备配置
- 日志配置

### training/ 目录
包含训练相关的配置：
- **efficient_zero.yaml**: 标准EfficientZero训练配置
- **optimized.yaml**: 优化的训练配置，包含性能优化参数

### algorithms/ 目录
包含算法特定的配置：
- **efficient_zero/**: EfficientZero算法的详细配置
  - **base.yaml**: 算法基础参数
  - **training.yaml**: 训练特定参数

### environments/ 目录
包含环境特定的配置：
- **doudizhu/**: 斗地主游戏环境配置
  - 游戏规则配置
  - 奖励机制配置
  - 环境参数配置

### hardware/ 目录
包含硬件特定的配置：
- **single_gpu.yaml**: 单GPU训练配置
- **multi_gpu.yaml**: 多GPU分布式训练配置

## 🎯 配置选择指南

### 根据硬件选择
```bash
# 单GPU训练
python main_training.py --config configs/base.yaml

# 多GPU训练
python main_training.py --config configs/hardware/multi_gpu.yaml
```

### 根据训练目标选择
```bash
# 标准训练
python main_training.py --config configs/training/efficient_zero.yaml

# 优化训练
python main_training.py --config configs/training/optimized.yaml
```

### 自定义配置
```bash
# 使用自定义配置
python main_training.py --config path/to/your/config.yaml
```

## ⚙️ 配置参数说明

### 关键参数
- **batch_size**: 训练批次大小
- **learning_rate**: 学习率
- **num_simulations**: MCTS模拟次数
- **device**: 计算设备配置
- **distributed**: 分布式训练配置

### 优化参数
- **mixed_precision**: 混合精度训练
- **gradient_checkpointing**: 梯度检查点
- **cache_size_gb**: 缓存大小
- **num_workers**: 数据加载线程数

## 🔄 配置继承

配置文件支持继承机制：
1. **base.yaml** 作为基础配置
2. 特定配置文件可以覆盖基础配置
3. 命令行参数具有最高优先级

## 📝 配置示例

### 最小配置
```yaml
device:
  type: "cuda"
  ids: [0]

training:
  batch_size: 256
  learning_rate: 0.0005
  epochs: 1000

mcts:
  num_simulations: 100
```

### 完整配置
参考 `training/optimized.yaml` 获取完整的配置示例。
