"""
随机工具模块

提供随机数生成功能。
"""
import numpy as np
import random
import torch
from typing import Optional, Union, List, Tuple, Any


def set_seed(seed: Optional[int] = None) -> int:
    """
    设置随机种子
    
    Args:
        seed (Optional[int], optional): 随机种子. Defaults to None.
        
    Returns:
        int: 使用的随机种子
    """
    if seed is None:
        seed = random.randint(0, 2**32 - 1)
    
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
    
    return seed


def get_rng(seed: Optional[int] = None) -> np.random.RandomState:
    """
    获取随机数生成器
    
    Args:
        seed (Optional[int], optional): 随机种子. Defaults to None.
        
    Returns:
        np.random.RandomState: 随机数生成器
    """
    return np.random.RandomState(seed)


def shuffle_list(lst: List[Any], rng: Optional[np.random.RandomState] = None) -> List[Any]:
    """
    打乱列表
    
    Args:
        lst (List[Any]): 要打乱的列表
        rng (Optional[np.random.RandomState], optional): 随机数生成器. Defaults to None.
        
    Returns:
        List[Any]: 打乱后的列表
    """
    if rng is None:
        rng = np.random
    
    lst_copy = lst.copy()
    rng.shuffle(lst_copy)
    return lst_copy
