"""
GTO正则化训练模块

提供支持GTO正则化的增强训练器。
"""

import os
import time
import logging
import numpy as np
from typing import Dict, Any, List, Tuple, Optional, Union, Callable

import torch

from cardgame_ai.core.environment import Environment
from cardgame_ai.core.agent import Agent
from cardgame_ai.training.trainer import AdvancedTrainer
from cardgame_ai.algorithms.gto_approximation import GTOPolicy, GTORegularizer
from cardgame_ai.utils.gto_monitoring import GTOMonitor, visualize_gto_distance_distribution, visualize_gto_policy_heatmap


class GTOTrainer(AdvancedTrainer):
    """
    GTO正则化训练器
    
    扩展高级训练器，增加GTO正则化支持。
    """
    
    def __init__(
        self,
        save_path: str = 'models',
        save_interval: int = 100,
        log_interval: int = 10,
        checkpoint_interval: int = 1000,
        max_checkpoints: int = 10,
        patience: int = 10,
        min_delta: float = 0.001,
        early_stop_metric: str = 'eval_win_rate',
        gto_policy_path: Optional[str] = None,
        gto_regularization_weight: float = 0.1,
        gto_regularization_method: str = 'kl',
        gto_adaptive_weight: bool = False,
        gto_adaptive_weight_params: Optional[Dict[str, float]] = None,
        gto_feature_extractor: Optional[Callable[[Any], str]] = None,
        gto_monitor_enabled: bool = True,
        gto_monitor_interval: int = 100,
        gto_visualization_interval: int = 1000
    ):
        """
        初始化GTO训练器
        
        Args:
            save_path: 模型保存路径
            save_interval: 保存间隔（回合数）
            log_interval: 日志间隔（回合数）
            checkpoint_interval: 检查点保存间隔
            max_checkpoints: 最大保存检查点数
            patience: 早停耐心值
            min_delta: 早停最小提升阈值
            early_stop_metric: 早停监控指标
            gto_policy_path: GTO策略文件路径
            gto_regularization_weight: GTO正则化权重
            gto_regularization_method: GTO正则化方法
            gto_adaptive_weight: 是否使用自适应权重
            gto_adaptive_weight_params: 自适应权重参数
            gto_feature_extractor: GTO特征提取器函数
            gto_monitor_enabled: 是否启用GTO监控
            gto_monitor_interval: GTO监控间隔（步数）
            gto_visualization_interval: GTO可视化间隔（步数）
        """
        super().__init__(
            save_path=save_path,
            save_interval=save_interval,
            log_interval=log_interval,
            checkpoint_interval=checkpoint_interval,
            max_checkpoints=max_checkpoints,
            patience=patience,
            min_delta=min_delta,
            early_stop_metric=early_stop_metric
        )
        
        # GTO相关设置
        self.gto_policy_path = gto_policy_path
        self.gto_regularization_weight = gto_regularization_weight
        self.gto_regularization_method = gto_regularization_method
        self.gto_adaptive_weight = gto_adaptive_weight
        self.gto_adaptive_weight_params = gto_adaptive_weight_params
        self.gto_feature_extractor = gto_feature_extractor
        
        # 创建GTO策略和正则化器
        self.gto_policy = None
        self.gto_regularizer = None
        if gto_policy_path:
            self._initialize_gto_components()
        
        # GTO监控设置
        self.gto_monitor_enabled = gto_monitor_enabled
        self.gto_monitor = None
        if gto_monitor_enabled:
            self.gto_monitor = GTOMonitor(
                save_path=save_path,
                regularizer=self.gto_regularizer,
                log_interval=gto_monitor_interval,
                visualization_interval=gto_visualization_interval
            )
        
        # 训练步数计数
        self.train_steps = 0
    
    def _initialize_gto_components(self):
        """初始化GTO组件（策略和正则化器）"""
        try:
            # 初始化GTO策略
            self.gto_policy = GTOPolicy(
                policy_path=self.gto_policy_path,
                feature_extractor=self.gto_feature_extractor
            )
            
            # 初始化GTO正则化器
            self.gto_regularizer = GTORegularizer(
                gto_policy=self.gto_policy,
                regularization_weight=self.gto_regularization_weight,
                regularization_method=self.gto_regularization_method,
                adaptive_weight=self.gto_adaptive_weight,
                adaptive_weight_params=self.gto_adaptive_weight_params
            )
            
            logging.info(f"GTO组件初始化成功，策略路径: {self.gto_policy_path}, "
                        f"正则化方法: {self.gto_regularization_method}, "
                        f"权重: {self.gto_regularization_weight}, "
                        f"自适应权重: {self.gto_adaptive_weight}")
            
            # 如果已经创建了监控器，则设置正则化器
            if self.gto_monitor:
                self.gto_monitor.set_regularizer(self.gto_regularizer)
        except Exception as e:
            logging.error(f"初始化GTO组件时出错: {e}")
            self.gto_policy = None
            self.gto_regularizer = None
    
    def configure_agent_gto(self, agent: Agent) -> None:
        """
        配置代理的GTO正则化设置
        
        Args:
            agent: 要配置的代理
        """
        if hasattr(agent, 'use_gto_regularization'):
            agent.use_gto_regularization = True
        
        if hasattr(agent, 'gto_policy') and self.gto_policy is not None:
            agent.gto_policy = self.gto_policy
        
        if hasattr(agent, 'gto_regularizer') and self.gto_regularizer is not None:
            agent.gto_regularizer = self.gto_regularizer
        
        if hasattr(agent, 'gto_regularization_weight'):
            agent.gto_regularization_weight = self.gto_regularization_weight
        
        if hasattr(agent, 'gto_regularization_method'):
            agent.gto_regularization_method = self.gto_regularization_method
        
        if hasattr(agent, 'gto_adaptive_weight'):
            agent.gto_adaptive_weight = self.gto_adaptive_weight
    
    def train_with_experiences(self, agent: Agent, experiences: List, 
                              batch_size: int = 32, num_epochs: int = 1, 
                              shuffle: bool = True,
                              rlhf_data_path: Optional[str] = None,
                              rlhf_batch_size: Optional[int] = None,
                              rlhf_weight: float = 0.1) -> Dict[str, Any]:
        """
        使用已有经验数据训练代理，支持GTO正则化
        
        Args:
            agent: 要训练的代理
            experiences: 经验数据列表
            batch_size: 批大小
            num_epochs: 训练轮次
            shuffle: 是否打乱数据
            rlhf_data_path: RLHF数据路径
            rlhf_batch_size: RLHF批次大小
            rlhf_weight: RLHF损失权重
            
        Returns:
            Dict[str, Any]: 训练结果
        """
        # 配置代理的GTO正则化设置
        self.configure_agent_gto(agent)
        
        # 使用父类进行训练
        metrics = super().train_with_experiences(
            agent=agent,
            experiences=experiences,
            batch_size=batch_size,
            num_epochs=num_epochs,
            shuffle=shuffle,
            rlhf_data_path=rlhf_data_path,
            rlhf_batch_size=rlhf_batch_size,
            rlhf_weight=rlhf_weight
        )
        
        # 收集并记录GTO相关指标
        self._log_gto_metrics(agent, metrics)
        
        return metrics
    
    def train_with_self_play(self, env: Environment, agent: Agent, 
                           num_episodes: int, games_per_episode: int = 10,
                           eval_func: Optional[Callable[[Environment, Agent, int], Dict[str, Any]]] = None,
                           eval_interval: int = 100,
                           self_play_temp: float = 1.0,
                           batch_size: int = 32,
                           epochs_per_episode: int = 1,
                           max_buffer_size: int = 100000,
                           save_experiences: bool = True,
                           parallel: bool = True,
                           rlhf_data_path: Optional[str] = None,
                           rlhf_batch_size: Optional[int] = None,
                           rlhf_weight: float = 0.1) -> Dict[str, Any]:
        """
        使用自我对弈训练代理，支持GTO正则化
        
        Args:
            env: 游戏环境
            agent: 要训练的代理
            num_episodes: 训练的回合数
            games_per_episode: 每回合的游戏数
            eval_func: 评估函数
            eval_interval: 评估间隔
            self_play_temp: 自我对弈温度参数
            batch_size: 批大小
            epochs_per_episode: 每回合训练轮次
            max_buffer_size: 最大缓冲区大小
            save_experiences: 是否保存经验
            parallel: 是否并行自我对弈
            rlhf_data_path: RLHF数据路径
            rlhf_batch_size: RLHF批次大小
            rlhf_weight: RLHF损失权重
            
        Returns:
            Dict[str, Any]: 训练结果
        """
        # 配置代理的GTO正则化设置
        self.configure_agent_gto(agent)
        
        # 使用父类进行训练
        metrics = super().train_with_self_play(
            env=env,
            agent=agent,
            num_episodes=num_episodes,
            games_per_episode=games_per_episode,
            eval_func=eval_func,
            eval_interval=eval_interval,
            self_play_temp=self_play_temp,
            batch_size=batch_size,
            epochs_per_episode=epochs_per_episode,
            max_buffer_size=max_buffer_size,
            save_experiences=save_experiences,
            parallel=parallel,
            rlhf_data_path=rlhf_data_path,
            rlhf_batch_size=rlhf_batch_size,
            rlhf_weight=rlhf_weight
        )
        
        return metrics
    
    def _log_gto_metrics(self, agent: Agent, metrics: Dict[str, Any]) -> None:
        """
        记录GTO相关指标
        
        Args:
            agent: 训练的代理
            metrics: 训练指标
        """
        # 如果没有启用GTO监控则返回
        if not self.gto_monitor_enabled or self.gto_monitor is None:
            return
        
        # 记录基本指标
        step = self.train_steps
        self.train_steps += 1
        
        if 'losses' in metrics and metrics['losses']:
            # 获取最后一轮的损失
            last_epoch_losses = metrics['losses'][-1]
            gto_loss = last_epoch_losses.get('gto_loss', None)
            
            # 获取GTO权重
            gto_weight = self.gto_regularizer.get_current_weight() if self.gto_regularizer else None
            
            # 记录到监控器
            self.gto_monitor.log_metrics(
                step=step,
                loss=gto_loss,
                weight=gto_weight
            )
        
        # 定期生成详细的GTO距离分布可视化
        if step % 1000 == 0 and self.gto_regularizer is not None and hasattr(agent, 'model'):
            try:
                # 生成一批测试数据
                test_env = self._create_test_env()
                test_states = []
                legal_actions_list = []
                
                for _ in range(50):
                    state = test_env.reset()
                    legal_actions = test_env.get_legal_actions()
                    test_states.append(state)
                    legal_actions_list.append(legal_actions)
                
                # 获取模型预测
                inputs = torch.FloatTensor(np.array(test_states)).to(agent.device)
                if hasattr(agent.model, 'representation'):
                    hidden_states = agent.model.representation(inputs)
                    policy_logits, _ = agent.model.predict(hidden_states)
                    
                    # 生成距离分布可视化
                    vis_path = os.path.join(self.save_path, 'gto_monitoring', f'distance_dist_step_{step}.png')
                    visualize_gto_distance_distribution(
                        regularizer=self.gto_regularizer,
                        states=test_states,
                        legal_actions_list=legal_actions_list,
                        predicted_policies=policy_logits,
                        output_path=vis_path
                    )
                    
                    # 计算与记录平均距离
                    mean_dist, _, _ = self.gto_regularizer.compute_policy_distance(
                        states=test_states,
                        legal_actions_list=legal_actions_list,
                        predicted_policies=policy_logits
                    )
                    
                    # 记录到监控器
                    self.gto_monitor.log_metrics(
                        step=step,
                        distance=mean_dist,
                        weight=self.gto_regularizer.get_current_weight(),
                        loss=metrics['losses'][-1].get('gto_loss', None)
                    )
                    
                    # 生成GTO策略热力图
                    if step % 5000 == 0 and self.gto_policy is not None:
                        heatmap_path = os.path.join(self.save_path, 'gto_monitoring', f'gto_heatmap_step_{step}.png')
                        visualize_gto_policy_heatmap(
                            gto_policy=self.gto_policy,
                            env=test_env,
                            num_states=20,
                            output_path=heatmap_path
                        )
            except Exception as e:
                logging.error(f"生成GTO指标可视化时出错: {e}")
    
    def _create_test_env(self):
        """创建测试环境"""
        try:
            from cardgame_ai.games.doudizhu import DouDizhuEnvironment
            return DouDizhuEnvironment()
        except ImportError:
            logging.warning("无法导入DouDizhuEnvironment，将尝试其他环境")
            
            try:
                # 尝试从游戏模块导入任何可用的环境
                import importlib
                from cardgame_ai.games import common
                
                # 尝试找到一个可用的环境类
                for module_name in dir(common):
                    if 'Environment' in module_name:
                        module = importlib.import_module(f'cardgame_ai.games.common.{module_name}')
                        env_class = getattr(module, module_name)
                        return env_class()
                
                # 如果找不到，返回None
                return None
            except Exception as e:
                logging.error(f"创建测试环境时出错: {e}")
                return None
    
    def save(self, agent: Agent, step: int, metrics: Dict[str, Any] = None) -> str:
        """
        保存代理和GTO相关设置
        
        Args:
            agent: 要保存的代理
            step: 当前步数
            metrics: 训练指标

        Returns:
            str: 保存路径
        """
        # 使用父类保存代理
        save_path = super().save(agent, step, metrics)
        
        # 保存GTO相关设置
        gto_config = {
            'gto_policy_path': self.gto_policy_path,
            'gto_regularization_weight': self.gto_regularization_weight,
            'gto_regularization_method': self.gto_regularization_method,
            'gto_adaptive_weight': self.gto_adaptive_weight,
            'gto_adaptive_weight_params': self.gto_adaptive_weight_params,
            'current_weight': self.gto_regularizer.get_current_weight() if self.gto_regularizer else None
        }
        
        # 保存配置
        import json
        gto_config_path = os.path.join(os.path.dirname(save_path), 'gto_config.json')
        with open(gto_config_path, 'w', encoding='utf-8') as f:
            json.dump(gto_config, f, indent=2)
        
        return save_path
    
    def load(self, agent: Agent, path: str) -> bool:
        """
        加载代理和GTO相关设置
        
        Args:
            agent: 要加载的代理
            path: 加载路径

        Returns:
            bool: 是否加载成功
        """
        # 使用父类加载代理
        success = super().load(agent, path)
        
        if success:
            # 加载GTO相关设置
            import json
            gto_config_path = os.path.join(os.path.dirname(path), 'gto_config.json')
            if os.path.exists(gto_config_path):
                try:
                    with open(gto_config_path, 'r', encoding='utf-8') as f:
                        gto_config = json.load(f)
                    
                    # 更新GTO配置
                    self.gto_policy_path = gto_config.get('gto_policy_path', self.gto_policy_path)
                    self.gto_regularization_weight = gto_config.get('gto_regularization_weight', self.gto_regularization_weight)
                    self.gto_regularization_method = gto_config.get('gto_regularization_method', self.gto_regularization_method)
                    self.gto_adaptive_weight = gto_config.get('gto_adaptive_weight', self.gto_adaptive_weight)
                    self.gto_adaptive_weight_params = gto_config.get('gto_adaptive_weight_params', self.gto_adaptive_weight_params)
                    
                    # 重新初始化GTO组件
                    self._initialize_gto_components()
                    
                    # 如果有当前权重，则设置
                    current_weight = gto_config.get('current_weight')
                    if current_weight is not None and self.gto_regularizer is not None:
                        self.gto_regularizer.regularization_weight = current_weight
                    
                    # 配置代理
                    self.configure_agent_gto(agent)
                    
                    logging.info(f"GTO配置加载成功: {gto_config_path}")
                except Exception as e:
                    logging.error(f"加载GTO配置时出错: {e}")
            
            # 加载GTO监控指标
            if self.gto_monitor:
                self.gto_monitor.load()
        
        return success 