#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
斗地主AI集成系统高级功能示例

这个脚本展示了如何使用集成系统的各种高级功能，如混合决策系统、元强化学习、自适应神经架构等。
"""

import os
import sys
import time
import logging
import argparse
import numpy as np
import torch
import random

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cardgame_ai.integrated_system import IntegratedAISystem
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.utils.logger import Logger

# 导入决策层组件
from cardgame_ai.algorithms.hybrid_decision_system import (
    HybridDecisionSystem, DecisionComponent,
    NeuralNetworkComponent, SearchComponent, RuleComponent, MetaController
)
from cardgame_ai.algorithms.meta_reinforcement_learning import (
    MetaReinforcementLearning, PolicyDistillation,
    PolicyFusion, AdaptiveExploration
)

# 导入自适应神经架构组件
from cardgame_ai.algorithms.adaptive_neural_architecture import (
    NeuralArchitectureSearch, DynamicNetworkExtension,
    ConditionalComputationPath, ModularNetworkDesign
)

# 导入多智能体组件
from cardgame_ai.algorithms.enhanced_mappo import (
    EnhancedMAPPO, EnhancedMAPPONetwork,
    MultiHeadCritic, CreditAssignment
)
from cardgame_ai.algorithms.implicit_communication import (
    ImplicitCommunicationMechanism, CardPatternRecognizer,
    ImplicitSignalEncoder, ImplicitSignalDecoder, IntentionInferenceModule
)


def setup_seed(seed):
    """
    设置随机种子，确保结果可复现
    
    Args:
        seed: 随机种子
    """
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)
    torch.backends.cudnn.deterministic = True


def demo_hybrid_decision_system(args):
    """
    混合决策系统示例
    
    Args:
        args: 命令行参数
    """
    logger = Logger("INFO")
    logger.info("混合决策系统示例")
    
    # 设置随机种子
    setup_seed(args.seed)
    
    # 创建集成系统
    config = {
        "decision": {
            "hybrid": {
                "use_neural_network": True,
                "use_search": True,
                "use_rule": True,
                "meta_controller_strategy": args.meta_strategy
            }
        },
        "log_level": "INFO"
    }
    
    system = IntegratedAISystem(config)
    
    # 创建环境
    env = DouDizhuEnvironment(seed=args.seed)
    
    # 运行游戏
    state = env.reset()
    done = False
    total_reward = 0
    component_usage = {
        "neural_network": 0,
        "search": 0,
        "rule": 0
    }
    
    while not done:
        # 获取合法动作
        legal_actions = env.get_legal_actions()
        
        # 记录当前使用的决策组件
        if system.hybrid_decision_system:
            component = system.hybrid_decision_system.select_component(state, strategy=args.meta_strategy)
            component_name = component.__class__.__name__
            if "Neural" in component_name:
                component_usage["neural_network"] += 1
            elif "Search" in component_name:
                component_usage["search"] += 1
            elif "Rule" in component_name:
                component_usage["rule"] += 1
        
        # 选择动作
        action = system.select_action(state, legal_actions)
        
        # 执行动作
        state, reward, done, info = env.step(action)
        
        # 更新总奖励
        total_reward += reward
        
        # 渲染环境
        if args.render:
            env.render()
    
    # 输出结果
    logger.info(f"游戏结束，总奖励: {total_reward:.4f}")
    logger.info("决策组件使用情况:")
    total_steps = sum(component_usage.values())
    for component, count in component_usage.items():
        logger.info(f"  {component}: {count} 次 ({count / total_steps * 100:.2f}%)")


def demo_meta_reinforcement_learning(args):
    """
    元强化学习示例
    
    Args:
        args: 命令行参数
    """
    logger = Logger("INFO")
    logger.info("元强化学习示例")
    
    # 设置随机种子
    setup_seed(args.seed)
    
    # 创建集成系统
    config = {
        "decision": {
            "meta": {
                "use_policy_distillation": True,
                "use_policy_fusion": True,
                "use_adaptive_exploration": True,
                "use_meta_controller": True
            }
        },
        "log_level": "INFO"
    }
    
    system = IntegratedAISystem(config)
    
    # 创建环境
    env = DouDizhuEnvironment(seed=args.seed)
    
    # 训练
    logger.info("开始训练")
    training_stats = system.train(env, args.episodes, args.eval_interval)
    
    # 输出训练结果
    logger.info(f"训练完成，最终胜率: {training_stats['win_rates'][-1]:.4f}")
    
    # 测试元强化学习的适应能力
    logger.info("测试元强化学习的适应能力")
    
    # 创建不同难度的环境
    envs = [
        DouDizhuEnvironment(seed=args.seed + i) for i in range(3)
    ]
    
    for i, test_env in enumerate(envs):
        logger.info(f"测试环境 {i+1}")
        
        # 测试性能
        performance = system.test_performance(test_env, args.test_episodes, False)
        
        # 输出性能指标
        logger.info(f"胜率: {performance['win_rate']:.4f}")
        logger.info(f"平均奖励: {performance['average_reward']:.4f}")
        logger.info(f"决策质量: {performance['decision_quality']:.4f}")


def demo_adaptive_neural_architecture(args):
    """
    自适应神经架构示例
    
    Args:
        args: 命令行参数
    """
    logger = Logger("INFO")
    logger.info("自适应神经架构示例")
    
    # 设置随机种子
    setup_seed(args.seed)
    
    # 创建集成系统
    config = {
        "core": {
            "adaptive_architecture": {
                "enable_nas": True,
                "enable_dynamic_extension": True,
                "enable_conditional_computation": True,
                "enable_modular_design": True
            }
        },
        "log_level": "INFO"
    }
    
    system = IntegratedAISystem(config)
    
    # 创建环境
    env = DouDizhuEnvironment(seed=args.seed)
    
    # 训练前测试性能
    logger.info("训练前测试性能")
    before_performance = system.test_performance(env, args.test_episodes, False)
    
    # 训练
    logger.info("开始训练")
    training_stats = system.train(env, args.episodes, args.eval_interval)
    
    # 训练后测试性能
    logger.info("训练后测试性能")
    after_performance = system.test_performance(env, args.test_episodes, False)
    
    # 输出性能对比
    logger.info("性能对比:")
    logger.info(f"训练前胜率: {before_performance['win_rate']:.4f}, 训练后胜率: {after_performance['win_rate']:.4f}")
    logger.info(f"训练前决策质量: {before_performance['decision_quality']:.4f}, 训练后决策质量: {after_performance['decision_quality']:.4f}")
    logger.info(f"训练前推理时间: {before_performance['inference_time'] * 1000:.4f} ms, 训练后推理时间: {after_performance['inference_time'] * 1000:.4f} ms")
    
    # 输出架构信息
    if system.architecture_search:
        logger.info("神经架构搜索信息:")
        logger.info(f"搜索空间大小: {system.architecture_search.search_space_size}")
        logger.info(f"最佳架构: {system.architecture_search.best_architecture}")
    
    if system.dynamic_extension:
        logger.info("动态网络扩展信息:")
        logger.info(f"扩展次数: {system.dynamic_extension.extension_count}")
        logger.info(f"当前网络大小: {system.dynamic_extension.current_size}")


def demo_multi_agent_cooperation(args):
    """
    多智能体协作示例
    
    Args:
        args: 命令行参数
    """
    logger = Logger("INFO")
    logger.info("多智能体协作示例")
    
    # 设置随机种子
    setup_seed(args.seed)
    
    # 创建集成系统
    config = {
        "multi_agent": {
            "mappo": {
                "use_role_specific_policy": True,
                "use_centralized_critic": True,
                "use_credit_assignment": True
            },
            "communication": {
                "use_implicit_communication": True,
                "use_intention_inference": True,
                "use_meta_communication": True
            },
            "cooperation": {
                "use_joint_policy_optimization": True,
                "use_role_aware_critic": True,
                "use_collaborative_exploration": True
            },
            "team": {
                "use_hierarchical_decision": True,
                "use_team_value_decomposition": True,
                "use_role_specialization": True,
                "use_communication_channel": True
            }
        },
        "log_level": "INFO"
    }
    
    system = IntegratedAISystem(config)
    
    # 创建环境
    env = DouDizhuEnvironment(seed=args.seed)
    
    # 训练
    logger.info("开始训练")
    training_stats = system.train(env, args.episodes, args.eval_interval)
    
    # 输出训练结果
    logger.info(f"训练完成，最终胜率: {training_stats['win_rates'][-1]:.4f}")
    
    # 测试农民协作效果
    logger.info("测试农民协作效果")
    
    # 创建测试环境
    test_env = DouDizhuEnvironment(seed=args.seed + 100)
    
    # 运行多局游戏
    farmer_win_count = 0
    landlord_win_count = 0
    total_games = args.test_episodes
    
    for i in range(total_games):
        state = test_env.reset()
        done = False
        
        while not done:
            # 获取合法动作
            legal_actions = test_env.get_legal_actions()
            
            # 选择动作
            action = system.select_action(state, legal_actions)
            
            # 执行动作
            state, reward, done, info = test_env.step(action)
        
        # 判断胜负
        if test_env.state.landlord == 0:  # AI是地主
            if reward > 0:
                landlord_win_count += 1
        else:  # AI是农民
            if reward > 0:
                farmer_win_count += 1
    
    # 输出结果
    logger.info(f"总局数: {total_games}")
    logger.info(f"作为地主胜率: {landlord_win_count / total_games:.4f}")
    logger.info(f"作为农民胜率: {farmer_win_count / total_games:.4f}")
    
    # 输出多智能体协作信息
    if system.implicit_communication:
        logger.info("隐式通信信息:")
        logger.info(f"信号发送次数: {system.implicit_communication.signal_count}")
        logger.info(f"信号识别准确率: {system.implicit_communication.recognition_accuracy:.4f}")
    
    if system.role_specific_network:
        logger.info("角色特定策略信息:")
        logger.info(f"地主策略使用次数: {system.role_specific_network.landlord_usage}")
        logger.info(f"农民策略使用次数: {system.role_specific_network.farmer_usage}")


def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description="斗地主AI集成系统高级功能示例")
    
    # 通用参数
    parser.add_argument("--demo", type=str, default="hybrid", 
                        choices=["hybrid", "meta", "adaptive", "multi_agent"],
                        help="示例类型: hybrid (混合决策系统), meta (元强化学习), adaptive (自适应神经架构), multi_agent (多智能体协作)")
    parser.add_argument("--seed", type=int, default=42, help="随机种子")
    parser.add_argument("--render", action="store_true", help="是否渲染环境")
    
    # 混合决策系统参数
    parser.add_argument("--meta_strategy", type=str, default="adaptive", 
                        choices=["fixed", "random", "adaptive", "ucb"],
                        help="元控制器策略: fixed, random, adaptive, ucb")
    
    # 训练参数
    parser.add_argument("--episodes", type=int, default=1000, help="训练轮数")
    parser.add_argument("--eval_interval", type=int, default=100, help="评估间隔")
    parser.add_argument("--test_episodes", type=int, default=100, help="测试轮数")
    
    args = parser.parse_args()
    
    # 根据示例类型执行相应的功能
    if args.demo == "hybrid":
        demo_hybrid_decision_system(args)
    elif args.demo == "meta":
        demo_meta_reinforcement_learning(args)
    elif args.demo == "adaptive":
        demo_adaptive_neural_architecture(args)
    elif args.demo == "multi_agent":
        demo_multi_agent_cooperation(args)
    else:
        raise ValueError(f"不支持的示例类型: {args.demo}")


if __name__ == "__main__":
    main()
