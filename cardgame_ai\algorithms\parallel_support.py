"""
并行支持模块

提供并行化工具用于加速训练和推理过程。实现了并行自对弈、并行蒙特卡洛树搜索、
并行环境运行和并行优先经验回放缓冲区等功能。
"""

import multiprocessing as mp
import threading
import numpy as np
import torch
import torch.nn as nn
import time
import queue
import logging
from typing import List, Dict, Tuple, Any, Optional, Union, Callable, Type
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor

from cardgame_ai.core.base import State, Action, Experience
from cardgame_ai.core.environment import Environment
from cardgame_ai.algorithms.mcts import MCTS
from cardgame_ai.algorithms.replay_buffer import PrioritizedReplayBuffer

logger = logging.getLogger(__name__)


class ParallelSelfPlay:
    """
    并行自对弈
    
    使用多个进程或线程并行执行自对弈，提高数据生成效率。
    """
    
    def __init__(
        self, 
        env_creator: Callable[[], Environment],
        agent_creator: Callable[[], Any],
        num_workers: int = 4,
        use_processes: bool = True,
        batch_size: int = 32,
        max_queue_size: int = 1000,
        seed: Optional[int] = None
    ):
        """
        初始化并行自对弈
        
        Args:
            env_creator: 创建环境的函数
            agent_creator: 创建智能体的函数
            num_workers: 工作进程或线程数量
            use_processes: 是否使用进程而不是线程
            batch_size: 每个工作进程生成的批次大小
            max_queue_size: 最大队列大小
            seed: 随机种子
        """
        self.env_creator = env_creator
        self.agent_creator = agent_creator
        self.num_workers = num_workers
        self.use_processes = use_processes
        self.batch_size = batch_size
        self.max_queue_size = max_queue_size
        self.seed = seed
        
        # 初始化队列和工作单元
        self.experience_queue = mp.Queue(maxsize=max_queue_size) if use_processes else queue.Queue(maxsize=max_queue_size)
        self.stop_flag = mp.Event() if use_processes else threading.Event()
        self.workers = []
        
        # 计数器
        self.episodes_completed = mp.Value('i', 0) if use_processes else 0
        self.steps_completed = mp.Value('i', 0) if use_processes else 0
        
        logger.info(f"初始化并行自对弈，工作单元数: {num_workers}，使用{'进程' if use_processes else '线程'}")
    
    def start(self) -> None:
        """
        启动并行自对弈
        """
        if self.use_processes:
            # 使用进程
            for i in range(self.num_workers):
                worker_seed = self.seed + i if self.seed is not None else None
                p = mp.Process(
                    target=self._worker_process,
                    args=(i, worker_seed, self.experience_queue, self.stop_flag, self.episodes_completed, self.steps_completed)
                )
                p.daemon = True
                p.start()
                self.workers.append(p)
        else:
            # 使用线程
            with ThreadPoolExecutor(max_workers=self.num_workers) as executor:
                for i in range(self.num_workers):
                    worker_seed = self.seed + i if self.seed is not None else None
                    executor.submit(self._worker_thread, i, worker_seed)
        
        logger.info(f"并行自对弈启动完成，{self.num_workers}个工作单元已启动")
    
    def _worker_process(self, worker_id: int, seed: Optional[int], experience_queue, stop_flag, episodes_counter, steps_counter) -> None:
        """
        工作进程函数
        
        Args:
            worker_id: 工作进程ID
            seed: 随机种子
            experience_queue: 经验队列
            stop_flag: 停止标志
            episodes_counter: 已完成的对局数量
            steps_counter: 已完成的步数
        """
        if seed is not None:
            np.random.seed(seed)
            torch.manual_seed(seed)
        
        # 创建环境和智能体
        env = self.env_creator()
        agent = self.agent_creator()
        
        # 执行自对弈直到收到停止信号
        while not stop_flag.is_set():
            # 生成一批经验
            experiences = []
            state = env.reset()
            done = False
            
            while not done and len(experiences) < self.batch_size:
                # 选择动作
                action = agent.select_action(state)
                
                # 执行动作
                next_state, reward, done, info = env.step(action)
                
                # 创建经验
                experience = Experience(state, action, reward, next_state, done, info)
                experiences.append(experience)
                
                # 更新状态
                state = next_state
                
                # 更新步数计数器
                with steps_counter.get_lock():
                    steps_counter.value += 1
            
            # 如果一局结束，更新对局计数器
            if done:
                with episodes_counter.get_lock():
                    episodes_counter.value += 1
            
            # 将经验放入队列
            if not stop_flag.is_set():
                try:
                    experience_queue.put(experiences, block=True, timeout=1.0)
                except queue.Full:
                    # 队列已满，跳过本批次
                    pass
    
    def _worker_thread(self, worker_id: int, seed: Optional[int]) -> None:
        """
        工作线程函数
        
        Args:
            worker_id: 工作线程ID
            seed: 随机种子
        """
        if seed is not None:
            np.random.seed(seed)
            torch.manual_seed(seed)
        
        # 创建环境和智能体
        env = self.env_creator()
        agent = self.agent_creator()
        
        # 执行自对弈直到收到停止信号
        while not self.stop_flag.is_set():
            # 生成一批经验
            experiences = []
            state = env.reset()
            done = False
            
            while not done and len(experiences) < self.batch_size:
                # 选择动作
                action = agent.select_action(state)
                
                # 执行动作
                next_state, reward, done, info = env.step(action)
                
                # 创建经验
                experience = Experience(state, action, reward, next_state, done, info)
                experiences.append(experience)
                
                # 更新状态
                state = next_state
                
                # 更新步数计数器
                self.steps_completed += 1
            
            # 如果一局结束，更新对局计数器
            if done:
                self.episodes_completed += 1
            
            # 将经验放入队列
            if not self.stop_flag.is_set():
                try:
                    self.experience_queue.put(experiences, block=True, timeout=1.0)
                except queue.Full:
                    # 队列已满，跳过本批次
                    pass
    
    def get_experiences(self, timeout: float = 1.0) -> List[Experience]:
        """
        获取生成的经验
        
        Args:
            timeout: 等待超时时间
            
        Returns:
            List[Experience]: 经验列表或None（如果队列为空）
        """
        try:
            return self.experience_queue.get(block=True, timeout=timeout)
        except (queue.Empty, mp.queues.Empty):
            return None
    
    def get_stats(self) -> Dict[str, int]:
        """
        获取统计信息
        
        Returns:
            Dict[str, int]: 统计信息字典
        """
        episodes = self.episodes_completed.value if self.use_processes else self.episodes_completed
        steps = self.steps_completed.value if self.use_processes else self.steps_completed
        
        return {
            'episodes_completed': episodes,
            'steps_completed': steps,
            'queue_size': self.experience_queue.qsize() if not self.use_processes else '未知'
        }
    
    def stop(self) -> None:
        """
        停止并行自对弈
        """
        # 设置停止标志
        self.stop_flag.set()
        
        # 等待所有进程结束
        if self.use_processes:
            for p in self.workers:
                p.join(timeout=5.0)
                if p.is_alive():
                    p.terminate()
            
            # 清空队列
            while not self.experience_queue.empty():
                try:
                    self.experience_queue.get_nowait()
                except (queue.Empty, mp.queues.Empty):
                    break
        
        logger.info("并行自对弈已停止")


class ParallelMCTS:
    """
    并行蒙特卡洛树搜索
    
    使用多个线程并行执行MCTS的模拟步骤，提高搜索效率。
    """
    
    def __init__(
        self,
        mcts: MCTS,
        num_workers: int = 4,
        batch_size: int = 8
    ):
        """
        初始化并行MCTS
        
        Args:
            mcts: MCTS实例
            num_workers: 工作线程数量
            batch_size: 批处理大小
        """
        self.mcts = mcts
        self.num_workers = num_workers
        self.batch_size = batch_size
        
        logger.info(f"初始化并行MCTS，工作线程数: {num_workers}，批处理大小: {batch_size}")
    
    def search(self, state: State, num_simulations: int) -> Action:
        """
        执行并行搜索
        
        Args:
            state: 当前状态
            num_simulations: 模拟次数
            
        Returns:
            Action: 最佳动作
        """
        # 将搜索任务分配给多个线程
        simulations_per_worker = max(1, num_simulations // self.num_workers)
        remaining = num_simulations - simulations_per_worker * self.num_workers
        
        # 创建任务列表
        tasks = [simulations_per_worker] * self.num_workers
        for i in range(remaining):
            tasks[i] += 1
        
        # 使用线程池执行搜索
        with ThreadPoolExecutor(max_workers=self.num_workers) as executor:
            futures = [
                executor.submit(self._worker_search, state, simulations)
                for simulations in tasks
            ]
            
            # 等待所有任务完成
            for future in futures:
                future.result()
        
        # 从MCTS中获取最佳动作
        return self.mcts.select_action(state)
    
    def _worker_search(self, state: State, num_simulations: int) -> None:
        """
        工作线程搜索函数
        
        Args:
            state: 当前状态
            num_simulations: 模拟次数
        """
        # 执行指定次数的模拟
        for _ in range(0, num_simulations, self.batch_size):
            batch_size = min(self.batch_size, num_simulations - _)
            self.mcts.batch_search(state, batch_size)


class ParallelEnvironment:
    """
    并行环境
    
    同时运行多个环境实例，提高数据收集效率。
    """
    
    def __init__(
        self,
        env_creator: Callable[[], Environment],
        num_envs: int = 4,
        use_processes: bool = False,
        seed: Optional[int] = None
    ):
        """
        初始化并行环境
        
        Args:
            env_creator: 创建环境的函数
            num_envs: 环境数量
            use_processes: 是否使用进程而不是线程
            seed: 随机种子
        """
        self.env_creator = env_creator
        self.num_envs = num_envs
        self.use_processes = use_processes
        self.seed = seed
        
        # 创建环境
        if use_processes:
            # 使用进程间共享对象
            self.action_queues = [mp.Queue() for _ in range(num_envs)]
            self.result_queues = [mp.Queue() for _ in range(num_envs)]
            self.proc_envs = []
            
            # 创建进程
            for i in range(num_envs):
                worker_seed = seed + i if seed is not None else None
                p = mp.Process(
                    target=self._env_process,
                    args=(i, worker_seed, self.action_queues[i], self.result_queues[i])
                )
                p.daemon = True
                p.start()
                self.proc_envs.append(p)
        else:
            # 直接在当前进程中创建多个环境
            self.envs = []
            for i in range(num_envs):
                worker_seed = seed + i if seed is not None else None
                env = env_creator()
                if worker_seed is not None:
                    env.seed(worker_seed)
                self.envs.append(env)
        
        # 保存当前状态
        self.states = [None] * num_envs
        
        logger.info(f"初始化并行环境，环境数量: {num_envs}，使用{'进程' if use_processes else '线程'}")
    
    def _env_process(self, env_id: int, seed: Optional[int], action_queue: mp.Queue, result_queue: mp.Queue) -> None:
        """
        环境进程函数
        
        Args:
            env_id: 环境ID
            seed: 随机种子
            action_queue: 动作队列
            result_queue: 结果队列
        """
        # 设置随机种子
        if seed is not None:
            np.random.seed(seed)
        
        # 创建环境
        env = self.env_creator()
        if seed is not None:
            env.seed(seed)
        
        # 初始状态
        state = env.reset()
        result_queue.put(('reset', state, None, False, {}))
        
        # 持续处理命令
        while True:
            try:
                cmd, action = action_queue.get()
                
                if cmd == 'step':
                    next_state, reward, done, info = env.step(action)
                    result_queue.put(('step', next_state, reward, done, info))
                    
                    # 如果游戏结束，自动重置
                    if done:
                        state = env.reset()
                        result_queue.put(('reset', state, None, False, {}))
                
                elif cmd == 'reset':
                    state = env.reset()
                    result_queue.put(('reset', state, None, False, {}))
                
                elif cmd == 'close':
                    env.close()
                    break
                
                else:
                    result_queue.put(('error', f"未知命令: {cmd}", None, None, {}))
            
            except Exception as e:
                # 发生错误时通知主进程
                result_queue.put(('error', str(e), None, None, {}))
    
    def reset(self) -> List[State]:
        """
        重置所有环境
        
        Returns:
            List[State]: 初始状态列表
        """
        if self.use_processes:
            # 向所有环境发送重置命令
            for i in range(self.num_envs):
                self.action_queues[i].put(('reset', None))
            
            # 获取所有环境的初始状态
            states = []
            for i in range(self.num_envs):
                cmd, state, _, _, _ = self.result_queues[i].get()
                assert cmd == 'reset', f"预期接收重置结果，实际接收: {cmd}"
                states.append(state)
                self.states[i] = state
            
            return states
        else:
            # 直接重置所有环境
            states = [env.reset() for env in self.envs]
            self.states = states
            return states
    
    def step(self, actions: List[Action]) -> Tuple[List[State], List[float], List[bool], List[Dict]]:
        """
        在所有环境中执行动作
        
        Args:
            actions: 动作列表，每个环境一个动作
            
        Returns:
            Tuple[List[State], List[float], List[bool], List[Dict]]:
                下一状态列表、奖励列表、结束标志列表、信息字典列表
        """
        assert len(actions) == self.num_envs, f"动作数量 ({len(actions)}) 与环境数量 ({self.num_envs}) 不匹配"
        
        if self.use_processes:
            # 向所有环境发送步进命令
            for i in range(self.num_envs):
                self.action_queues[i].put(('step', actions[i]))
            
            # 获取所有环境的结果
            next_states, rewards, dones, infos = [], [], [], []
            for i in range(self.num_envs):
                cmd, next_state, reward, done, info = self.result_queues[i].get()
                assert cmd == 'step', f"预期接收步进结果，实际接收: {cmd}"
                next_states.append(next_state)
                rewards.append(reward)
                dones.append(done)
                infos.append(info)
                self.states[i] = next_state
            
            return next_states, rewards, dones, infos
        else:
            # 直接在所有环境中执行动作
            results = [env.step(action) for env, action in zip(self.envs, actions)]
            next_states, rewards, dones, infos = zip(*results)
            self.states = list(next_states)
            
            return list(next_states), list(rewards), list(dones), list(infos)
    
    def get_states(self) -> List[State]:
        """
        获取当前所有环境的状态
        
        Returns:
            List[State]: 状态列表
        """
        return self.states
    
    def close(self) -> None:
        """
        关闭所有环境
        """
        if self.use_processes:
            # 向所有环境发送关闭命令
            for i in range(self.num_envs):
                self.action_queues[i].put(('close', None))
            
            # 等待所有进程结束
            for p in self.proc_envs:
                p.join(timeout=5.0)
                if p.is_alive():
                    p.terminate()
            
            # 清空队列
            for queue in self.action_queues + self.result_queues:
                while not queue.empty():
                    try:
                        queue.get_nowait()
                    except (queue.Empty, mp.queues.Empty):
                        break
        else:
            # 直接关闭所有环境
            for env in self.envs:
                env.close()
        
        logger.info("并行环境已关闭")


class ParallelPrioritizedReplayBuffer:
    """
    并行优先经验回放缓冲区
    
    实现线程安全的优先经验回放缓冲区，支持多线程/进程并发写入和采样。
    """
    
    def __init__(
        self,
        capacity: int = 100000,
        alpha: float = 0.6,
        beta: float = 0.4,
        beta_annealing: float = 0.001,
        eps: float = 1e-6
    ):
        """
        初始化并行优先经验回放缓冲区
        
        Args:
            capacity: 缓冲区容量
            alpha: 优先级指数，控制采样概率与TD误差的关系
            beta: 重要性采样指数，用于纠正优先级采样引入的偏差
            beta_annealing: beta的退火率，控制beta如何随时间增加到1
            eps: 小常数，防止优先级为0
        """
        self.buffer = PrioritizedReplayBuffer(capacity, alpha, beta, beta_annealing, eps)
        self.lock = threading.RLock()
        
        logger.info(f"初始化并行优先经验回放缓冲区，容量: {capacity}")
    
    def add(self, experience: Experience, priority: Optional[float] = None) -> None:
        """
        添加经验到缓冲区
        
        Args:
            experience: 经验
            priority: 优先级，默认为最大优先级
        """
        with self.lock:
            self.buffer.add(experience, priority)
    
    def add_batch(self, experiences: List[Experience], priorities: Optional[List[float]] = None) -> None:
        """
        批量添加经验到缓冲区
        
        Args:
            experiences: 经验列表
            priorities: 优先级列表，默认为最大优先级
        """
        with self.lock:
            self.buffer.add_batch(experiences, priorities)
    
    def sample(self, batch_size: int) -> Tuple[List[Experience], List[int], List[float]]:
        """
        从缓冲区采样一批经验
        
        Args:
            batch_size: 批大小
            
        Returns:
            Tuple[List[Experience], List[int], List[float]]:
                经验列表、索引列表、重要性权重列表
        """
        with self.lock:
            return self.buffer.sample(batch_size)
    
    def update_priorities(self, indices: List[int], priorities: List[float]) -> None:
        """
        更新经验的优先级
        
        Args:
            indices: 索引列表
            priorities: 新优先级列表
        """
        with self.lock:
            self.buffer.update_priorities(indices, priorities)
    
    def __len__(self) -> int:
        """
        获取缓冲区中的经验数量
        
        Returns:
            int: 经验数量
        """
        with self.lock:
            return len(self.buffer) 