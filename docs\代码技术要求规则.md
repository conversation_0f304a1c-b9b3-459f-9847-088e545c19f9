# 代码技术要求规则

## 1. 代码架构与组织

### 1.1 模块化设计
- **单一职责原则**：每个模块必须职责专一，功能明确
- **切片化架构**：将复杂功能拆分为独立的、可维护的模块
- **文件大小控制**：在不负面影响性能和质量的前提下，单个文件代码行数控制在1000行以内

### 1.2 代码组织结构
- 按功能模块划分目录结构
- 相关功能模块就近放置
- 测试文件统一放置在根目录的 `tests` 文件夹中

### 1.3 文件存放规范
- **避免混乱存放**：每个模块、每个文件的存放必须避免混乱
- **明确归属**：每个文件都应有明确的功能归属和存放位置
- **逻辑分组**：相同功能或相关功能的文件应放在同一目录下
- **命名规范**：文件和目录命名应清晰表达其功能和用途
- **层次清晰**：目录结构应层次分明，避免过深或过浅的嵌套

## 2. 代码质量与规范

### 2.1 严谨性要求
- **严禁模拟行为**：禁止使用模拟环境、模拟执行等一系列模拟操作
- **严禁假设行为**：所有逻辑必须基于实际情况，不允许假设性处理
- **严禁降级处理**：
  - 禁止降级处理问题
  - 禁止创建简化程序、脚本、代码
  - 遇到问题时必须努力找到根本原因并寻找解决方案修复

### 2.2 高质量代码要求
- **需求高质量代码**：始终追求代码的高质量标准
- **严禁简化功能**：
  - 尽量不要创建简化代码功能
  - 特别是在会产生负面影响的情况下严禁简化
  - 严禁在会降低体验、性能、质量的情况下进行功能简化
- **完整功能实现**：
  - 确保功能的完整性和健壮性
  - 不允许为了快速实现而牺牲代码质量
  - 优先考虑长期维护性和扩展性

### 2.3 问题处理原则
- **fail-fast原则**：快速失败，不允许降级处理
- **根因分析**：必须找到问题的根本原因
- **完整解决**：严禁偏离用户要求核心，必须完整解决问题
- **网络搜索辅助**：可使用网络搜索来寻找解决办法

## 3. 代码注释与文档

### 3.1 注释要求
- **详细的中文注释**：为所有代码添加详细的中文注释
- **文件说明**：每个文件必须说明其用途和功能
- **参数说明**：详细说明每个参数的作用、影响和依赖关系
- **函数说明**：详细说明每个函数的功能、参数、返回值和副作用

### 3.2 注释目的
- 供AI读取分析代码时避免错觉和混乱
- 提高代码可维护性和可读性
- 便于团队协作和知识传承

## 4. 开发环境与工具

### 4.1 环境配置
- **虚拟环境路径**：`E:\youyou\kaifa\xiangmu\hid5\bqq\.conda`
- **跨平台兼容**：支持Windows和Ubuntu 24.0系统
- **自动化配置**：优先使用自动化配置管理

### 4.2 包管理
- 使用适当的包管理器进行依赖管理
- 禁止手动编辑包配置文件
- 确保依赖版本的一致性和兼容性

## 5. 测试与验证

### 5.1 测试组织
- 所有测试相关程序和脚本必须放在根目录的 `tests` 文件夹
- 禁止在其他位置随意存储测试文件

### 5.2 测试要求
- 编写完整的单元测试
- 确保测试覆盖率
- 验证所有功能的正确性

### 5.3 模拟数据使用规范
- **测试代码中的模拟使用**：
  - 只允许在测试代码和脚本中使用随机(模拟)信息用于不同情况下的测试
  - 测试代码可以使用模拟数据、模拟环境、模拟信息进行各种场景测试
- **项目代码中的严格禁止**：
  - 绝不允许在项目正式代码中使用模拟的数据、信息、环境等
  - 项目代码必须基于真实数据和真实环境运行
- **代码隔离要求**：
  - 测试代码绝不允许影响正常正式的代码
  - 测试代码与项目代码必须严格隔离
  - 测试代码的模拟行为不得泄露到生产环境

## 6. 性能与优化

### 6.1 性能要求
- 性能、智能性和胜率为不可妥协的约束条件
- 在系统改进过程中不允许降低这些指标
- 优化重点：数据管道/预处理、批量大小/学习率调优、MCTS搜索效率

### 6.2 训练架构要求
- **不需要分布式训练**：使用主流的同步训练即可
- 优先选择同步训练架构而非异步训练
- 训练架构应简洁高效，避免过度复杂化

### 6.3 优化原则
- 基于实际性能数据进行优化
- 量化性能改进效果
- 避免过早优化

## 7. 代码提交与版本控制

### 7.1 代码标准
- 遵循代码格式化标准（Black/Flake8/MyPy）
- 确保代码质量检查通过
- 完整的测试验证

### 7.2 文档维护
- 及时更新相关文档
- 过时文档移至 `.待彻底删除文件区域` 文件夹
- 保持文档与代码的一致性