#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
增强版自我对弈

支持使用对手分布切换器来获取对手，提高主策略的泛化能力和鲁棒性。
"""

import os
import time
import logging
import random
import numpy as np
from typing import Dict, List, Any, Optional, Union, Tuple

from cardgame_ai.training.self_play import SelfPlay
from cardgame_ai.utils.opponent_distribution_switcher import OpponentDistributionSwitcher
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.core.base import Agent

# 配置日志
logger = logging.getLogger(__name__)

class EnhancedSelfPlay(SelfPlay):
    """
    增强版自我对弈
    
    支持使用对手分布切换器来获取对手，提高主策略的泛化能力和鲁棒性。
    """
    
    def __init__(self, save_path: Optional[str] = None):
        """
        初始化增强版自我对弈
        
        Args:
            save_path: 经验数据保存路径
        """
        super().__init__(save_path)
        self.opponent_switcher = None
        
    def set_opponent_switcher(self, opponent_switcher: OpponentDistributionSwitcher):
        """
        设置对手分布切换器
        
        Args:
            opponent_switcher: 对手分布切换器
        """
        self.opponent_switcher = opponent_switcher
        
    def generate_experience_with_opponent_switcher(
        self, 
        env: DouDizhuEnvironment, 
        agent: Agent, 
        num_games: int,
        temperature: float = 1.0, 
        save: bool = True,
        parallel: bool = True, 
        disable_tqdm: bool = False,
        bidding_handler: Optional[Any] = None
    ) -> List[Dict[str, Any]]:
        """
        使用对手分布切换器生成斗地主自我对弈经验数据
        
        Args:
            env: 斗地主游戏环境
            agent: 用于自我对弈的代理
            num_games: 对弈的游戏数
            temperature: 温度参数，控制动作采样的随机性
            save: 是否保存经验数据
            parallel: 是否并行生成
            disable_tqdm: 是否禁用进度条
            bidding_handler: 叫地主/抢地主阶段处理器
            
        Returns:
            生成的经验数据
        """
        if self.opponent_switcher is None:
            self.logger.warning("未设置对手分布切换器，使用普通自我对弈")
            return self.generate_experience(
                env=env,
                agent=agent,
                num_games=num_games,
                temperature=temperature,
                save=save,
                parallel=parallel,
                disable_tqdm=disable_tqdm,
                bidding_handler=bidding_handler
            )
            
        start_time = time.time()
        all_experiences = []
        
        # 顺序生成游戏（暂不支持并行，因为需要处理完整的游戏流程）
        self.logger.info(f"开始生成带对手分布切换的斗地主自我对弈数据，游戏数: {num_games}")
        
        # 创建进度条
        if not disable_tqdm:
            try:
                from tqdm import tqdm
                game_iterator = tqdm(range(num_games), desc="生成对局")
            except ImportError:
                game_iterator = range(num_games)
                self.logger.warning("未安装tqdm，无法显示进度条")
        else:
            game_iterator = range(num_games)
        
        for i in game_iterator:
            # 获取当前对手
            opponent = self.opponent_switcher.get_opponent()
            
            # 进行一局游戏
            game_experiences = self._play_doudizhu_game_with_opponent(
                env=env,
                agent=agent,
                opponent=opponent,
                temperature=temperature,
                bidding_handler=bidding_handler
            )
            
            # 更新对手分布切换器
            # 这里使用游戏结果作为性能指标，例如主策略的胜率
            # 简单起见，这里假设经验数据中包含游戏结果
            if game_experiences and 'reward' in game_experiences[-1]:
                metrics = game_experiences[-1]['reward']
                self.opponent_switcher.update(metrics)
            
            all_experiences.extend(game_experiences)
            
            if (i + 1) % 10 == 0 or (i + 1) == num_games:
                elapsed_time = time.time() - start_time
                self.logger.info(
                    f"已完成 {i + 1}/{num_games} 局游戏 | "
                    f"时间: {elapsed_time:.2f}s | "
                    f"平均每局时间: {elapsed_time / (i + 1):.2f}s | "
                    f"当前对手分布: {self.opponent_switcher.current_distribution}"
                )
                
        # 保存经验数据
        if save and self.save_path:
            save_file = os.path.join(self.save_path, f"experiences_{int(time.time())}.pkl")
            self.save_experiences(all_experiences, save_file)
            self.logger.info(f"保存经验数据: {save_file}")
            
        return all_experiences
    
    def _play_doudizhu_game_with_opponent(
        self,
        env: DouDizhuEnvironment,
        agent: Agent,
        opponent: Agent,
        temperature: float = 1.0,
        bidding_handler: Optional[Any] = None
    ) -> List[Dict[str, Any]]:
        """
        使用指定对手进行一局斗地主游戏
        
        Args:
            env: 斗地主游戏环境
            agent: 主策略代理
            opponent: 对手代理
            temperature: 温度参数，控制动作采样的随机性
            bidding_handler: 叫地主/抢地主阶段处理器
            
        Returns:
            游戏经验数据
        """
        # 重置环境
        state = env.reset()
        
        # 确定角色（地主和农民）
        # 这里假设有一个叫地主/抢地主阶段处理器
        if bidding_handler:
            landlord_id = bidding_handler.handle_bidding(env, agent, opponent)
        else:
            # 随机选择地主
            landlord_id = random.randint(0, 2)
            
        # 分配代理
        agents = {}
        if landlord_id == 0:
            # 主策略作为地主
            agents[0] = agent  # 地主
            agents[1] = opponent  # 农民1
            agents[2] = opponent  # 农民2
        else:
            # 主策略作为农民
            agents[0] = opponent  # 地主
            agents[1] = agent if landlord_id != 1 else opponent  # 农民1
            agents[2] = agent if landlord_id != 2 else opponent  # 农民2
            
        # 游戏循环
        done = False
        experiences = []
        
        while not done:
            # 获取当前玩家
            current_player = state.current_player
            
            # 获取当前代理
            current_agent = agents[current_player]
            
            # 获取合法动作
            legal_actions = env.get_legal_actions(state)
            
            # 选择动作
            action = current_agent.act(state, legal_actions, is_training=True, temperature=temperature)
            
            # 执行动作
            next_state, reward, done, info = env.step(action)
            
            # 记录经验
            experience = {
                'state': state,
                'action': action,
                'reward': reward,
                'next_state': next_state,
                'done': done,
                'info': info,
                'player': current_player,
                'agent_type': 'main' if current_agent == agent else 'opponent'
            }
            experiences.append(experience)
            
            # 更新状态
            state = next_state
            
        return experiences
