"""
配置工具模块

提供配置管理功能。
"""
import os
import json
import yaml
from typing import Dict, Any, Optional


class ConfigManager:
    """
    配置管理器
    
    管理配置信息，支持JSON和YAML格式。
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_path (Optional[str], optional): 配置文件路径. Defaults to None.
        """
        self.config = {}
        
        if config_path:
            self.load_config(config_path)
    
    def load_config(self, config_path: str) -> Dict[str, Any]:
        """
        加载配置文件
        
        Args:
            config_path (str): 配置文件路径
            
        Returns:
            Dict[str, Any]: 配置信息
        """
        # 检查文件是否存在
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        # 根据文件扩展名选择解析方法
        _, ext = os.path.splitext(config_path)
        
        if ext.lower() in ['.json']:
            # 解析JSON文件
            with open(config_path, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
        elif ext.lower() in ['.yaml', '.yml']:
            # 解析YAML文件
            with open(config_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
        else:
            raise ValueError(f"不支持的配置文件格式: {ext}")
        
        return self.config
    
    def save_config(self, config_path: str) -> None:
        """
        保存配置文件
        
        Args:
            config_path (str): 配置文件路径
        """
        # 创建目录
        os.makedirs(os.path.dirname(config_path), exist_ok=True)
        
        # 根据文件扩展名选择序列化方法
        _, ext = os.path.splitext(config_path)
        
        if ext.lower() in ['.json']:
            # 序列化为JSON
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        elif ext.lower() in ['.yaml', '.yml']:
            # 序列化为YAML
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False)
        else:
            raise ValueError(f"不支持的配置文件格式: {ext}")
    
    def get_value(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key (str): 配置键，支持点号分隔的嵌套键
            default (Any, optional): 默认值. Defaults to None.
            
        Returns:
            Any: 配置值
        """
        # 处理嵌套键
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set_value(self, key: str, value: Any) -> None:
        """
        设置配置值
        
        Args:
            key (str): 配置键，支持点号分隔的嵌套键
            value (Any): 配置值
        """
        # 处理嵌套键
        keys = key.split('.')
        config = self.config
        
        # 遍历除最后一个键之外的所有键
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # 设置最后一个键的值
        config[keys[-1]] = value
