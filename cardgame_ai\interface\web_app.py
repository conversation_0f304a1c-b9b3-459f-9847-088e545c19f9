"""
简化版测试网页应用模块

提供基于Flask的Web界面，用于斗地主AI测试。
移除了用户系统和游戏大厅，直接提供测试配置和游戏界面。
"""
import os
import json
import time
import logging
from typing import Dict, Any, Optional, List
from flask import Flask, render_template, request, jsonify, redirect, url_for
from flask_socketio import SocketIO, emit

from cardgame_ai.interface.config import InterfaceConfig
from cardgame_ai.interface.game_server import GameServer
from cardgame_ai.core.agent import Agent, RandomAgent
from cardgame_ai.games.doudizhu import DouDizhuEnvironment


def create_app(config: Optional[InterfaceConfig] = None) -> Flask:
    """
    创建Flask应用程序

    Args:
        config (Optional[InterfaceConfig], optional): 界面配置. Defaults to None.

    Returns:
        Flask: Flask应用程序
    """
    # 创建Flask应用
    app = Flask(__name__,
                template_folder=os.path.join(os.path.dirname(__file__), 'templates'),
                static_folder=os.path.join(os.path.dirname(__file__), 'static'))

    # 配置应用
    if config is None:
        config = InterfaceConfig()

    app.secret_key = config.get('server.secret_key')

    # 创建Socket.IO实例
    socketio = SocketIO(app, cors_allowed_origins="*")

    # 创建游戏服务器
    game_server = GameServer(config)

    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)

    # 主页路由 - 直接提供测试配置和游戏界面
    @app.route('/')
    def index():
        """测试主页"""
        return render_template('test_page.html',
                              available_models=config.get('game.available_models'),
                              theme=config.get('ui.theme'))

    # 牌局测试界面路由
    @app.route('/game_test')
    def game_test():
        """牌局测试界面"""
        return render_template('game_test.html',
                              available_models=config.get('game.available_models'),
                              theme=config.get('ui.theme'))

    # 创建游戏API
    @app.route('/api/create_game', methods=['POST'])
    def create_game():
        """创建游戏API"""
        data = request.json

        # 获取游戏参数
        game_type = data.get('game_type', 'doudizhu')
        player_role = data.get('player_role', 'random')  # random, landlord, farmer
        ai_model = data.get('ai_model', config.get('game.default_ai_model'))

        # 创建游戏
        game_id = game_server.create_game(game_type, player_role, ai_model)

        return jsonify({'success': True, 'game_id': game_id})

    # 获取游戏状态API
    @app.route('/api/game_state/<game_id>', methods=['GET'])
    def get_game_state(game_id):
        """获取游戏状态API"""
        if not game_server.game_exists(game_id):
            return jsonify({'success': False, 'error': '游戏不存在'})

        # 获取游戏状态
        state = game_server.get_game_state(game_id)

        # 转换为JSON友好格式
        state_json = game_server.state_to_json(game_id, state)

        return jsonify({'success': True, 'state': state_json})

    # 执行动作API
    @app.route('/api/play_action/<game_id>', methods=['POST'])
    def play_action(game_id):
        """执行动作API"""
        if not game_server.game_exists(game_id):
            return jsonify({'success': False, 'error': '游戏不存在'})

        data = request.json
        action_data = data.get('action')
        explain = data.get('explain', True)  # 默认启用解释模式

        # 执行动作
        try:
            result = game_server.play_action(game_id, action_data)

            # 获取最新游戏状态
            state = game_server.get_game_state(game_id)
            state_json = game_server.state_to_json(game_id, state)

            # 构建响应
            response = {
                'success': True,
                'result': result,
                'state': state_json
            }

            # 如果有解释数据，添加到响应中
            if 'explanation_data' in result:
                response['explanation_data'] = result['explanation_data']

            return jsonify(response)
        except Exception as e:
            return jsonify({'success': False, 'error': str(e)})

    # 出牌API
    @app.route('/api/play_cards/<game_id>', methods=['POST'])
    def play_cards(game_id):
        """出牌API"""
        if not game_server.game_exists(game_id):
            return jsonify({'success': False, 'error': '游戏不存在'})

        data = request.json
        cards = data.get('cards', [])

        try:
            result = game_server.play_cards(game_id, cards)
            # 获取最新游戏状态
            state = game_server.get_game_state(game_id)
            state_json = game_server.state_to_json(game_id, state)

            # 广播游戏更新
            socketio.emit('game_update', {
                'game_id': game_id,
                'state': state_json
            }, broadcast=True)

            return jsonify({'success': True, 'state': state_json})
        except Exception as e:
            return jsonify({'success': False, 'error': str(e)})

    # 不出API
    @app.route('/api/pass/<game_id>', methods=['POST'])
    def pass_cards(game_id):
        """不出API"""
        if not game_server.game_exists(game_id):
            return jsonify({'success': False, 'error': '游戏不存在'})

        try:
            result = game_server.pass_cards(game_id)
            # 获取最新游戏状态
            state = game_server.get_game_state(game_id)
            state_json = game_server.state_to_json(game_id, state)

            # 广播游戏更新
            socketio.emit('game_update', {
                'game_id': game_id,
                'state': state_json
            }, broadcast=True)

            return jsonify({'success': True, 'state': state_json})
        except Exception as e:
            return jsonify({'success': False, 'error': str(e)})

    # 提示API
    @app.route('/api/hint/<game_id>', methods=['GET'])
    def get_hint(game_id):
        """提示API"""
        if not game_server.game_exists(game_id):
            return jsonify({'success': False, 'error': '游戏不存在'})

        try:
            hint_cards = game_server.get_hint(game_id)
            return jsonify({'success': True, 'hint': hint_cards})
        except Exception as e:
            return jsonify({'success': False, 'error': str(e)})

    # 获取合法动作API
    @app.route('/api/legal_actions/<game_id>', methods=['GET'])
    def get_legal_actions(game_id):
        """获取合法动作API"""
        if not game_server.game_exists(game_id):
            return jsonify({'success': False, 'error': '游戏不存在'})

        # 获取合法动作
        legal_actions = game_server.get_legal_actions(game_id)

        # 转换为JSON友好格式
        actions_json = [game_server.action_to_json(game_id, action) for action in legal_actions]

        return jsonify({'success': True, 'actions': actions_json})

    # 重置游戏API
    @app.route('/api/reset_game/<game_id>', methods=['POST'])
    def reset_game(game_id):
        """重置游戏API"""
        if not game_server.game_exists(game_id):
            return jsonify({'success': False, 'error': '游戏不存在'})

        # 重置游戏
        game_server.reset_game(game_id)

        return jsonify({'success': True})

    # 获取可用模型API
    @app.route('/api/available_models', methods=['GET'])
    def get_available_models():
        """获取可用模型API"""
        models = config.get('game.available_models')
        return jsonify({'success': True, 'models': models})

    # Socket.IO事件处理
    @socketio.on('connect')
    def handle_connect():
        """客户端连接事件"""
        logger.info(f"客户端连接: {request.sid}")

    @socketio.on('disconnect')
    def handle_disconnect():
        """客户端断开连接事件"""
        logger.info(f"客户端断开连接: {request.sid}")

    @socketio.on('join_game')
    def handle_join_game(data):
        """加入游戏事件"""
        game_id = data.get('game_id')
        if game_id and game_server.game_exists(game_id):
            logger.info(f"客户端 {request.sid} 加入游戏 {game_id}")
            emit('game_update', {'game_id': game_id, 'message': '成功加入游戏'})
        else:
            emit('error', {'message': '游戏不存在'})

    @socketio.on('game_action')
    def handle_game_action(data):
        """游戏动作事件"""
        game_id = data.get('game_id')
        action = data.get('action')
        explain = data.get('explain', True)  # 默认启用解释模式

        if not game_id or not game_server.game_exists(game_id):
            emit('error', {'message': '游戏不存在'})
            return

        try:
            result = game_server.play_action(game_id, action)
            state = game_server.get_game_state(game_id)
            state_json = game_server.state_to_json(game_id, state)

            # 构建响应数据
            response_data = {
                'game_id': game_id,
                'state': state_json,
                'result': result
            }

            # 如果有解释数据，添加到响应中
            if 'explanation_data' in result:
                response_data['explanation_data'] = result['explanation_data']

            emit('game_update', response_data, broadcast=True)
        except Exception as e:
            emit('error', {'message': str(e)})

    # 错误处理
    @app.errorhandler(404)
    def page_not_found(e):
        """404错误处理"""
        return render_template('error.html', error="页面不存在"), 404

    @app.errorhandler(500)
    def internal_server_error(e):
        """500错误处理"""
        return render_template('error.html', error="服务器内部错误"), 500

    return app


def main():
    """主函数"""
    config = InterfaceConfig()
    app = create_app(config)
    app.config['start_time'] = time.time()

    # 使用Socket.IO运行服务器
    socketio = SocketIO(app)
    socketio.run(app, host=config.get('server.host'), port=config.get('server.port'), debug=config.get('server.debug'))


if __name__ == "__main__":
    main()