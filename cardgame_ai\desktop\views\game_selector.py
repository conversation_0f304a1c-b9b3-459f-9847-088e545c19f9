#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
游戏选择组件

支持选择不同的游戏，考虑多游戏扩展性。
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional

from PySide6.QtWidgets import (
    QWidget, QHBoxLayout, QVBoxLayout, QLabel, QComboBox,
    QPushButton, QTabBar, QStackedWidget, QSpacerItem, QSizePolicy
)
from PySide6.QtCore import Qt, Signal, Slot, QSize
from PySide6.QtGui import QIcon, QFont, QPixmap

logger = logging.getLogger(__name__)


class GameSelector(QWidget):
    """游戏选择组件类"""

    # 游戏选择变化信号
    game_changed = Signal(str)

    def __init__(self, config, parent=None):
        """
        初始化游戏选择组件

        Args:
            config: 客户端配置
            parent: 父部件
        """
        super().__init__(parent)

        # 保存配置
        self.config = config

        # 设置对象名称
        self.setObjectName("gameSelector")

        # 游戏列表
        self.games = []

        # 当前选择的游戏
        self.current_game = ""

        # 选择模式：combo（下拉菜单）或tab（标签页）
        self.selection_mode = "combo"

        # 初始化UI
        self.setup_ui()

        # 加载游戏列表
        self.load_games()

        logger.info("游戏选择组件初始化完成")

    def setup_ui(self):
        """设置UI布局"""
        # 创建主布局
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # 创建标签
        label = QLabel("选择游戏:")
        label.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        main_layout.addWidget(label)

        # 创建游戏选择控件
        if self.selection_mode == "combo":
            # 使用下拉菜单
            self.game_combo = QComboBox()
            self.game_combo.setMinimumWidth(150)
            self.game_combo.currentTextChanged.connect(self.on_game_changed)
            main_layout.addWidget(self.game_combo)
        else:
            # 使用标签页
            self.game_tab = QTabBar()
            self.game_tab.setExpanding(False)
            self.game_tab.setDrawBase(True)
            self.game_tab.currentChanged.connect(self.on_tab_changed)
            main_layout.addWidget(self.game_tab)

        # 添加间隔
        main_layout.addSpacerItem(QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum))

        # 创建刷新按钮
        refresh_button = QPushButton("刷新")
        refresh_button.setToolTip("刷新游戏列表")
        refresh_button.clicked.connect(self.load_games)
        main_layout.addWidget(refresh_button)

        logger.info("游戏选择组件UI布局设置完成")

    def load_games(self):
        """加载游戏列表"""
        try:
            # 清空游戏列表
            self.games = []

            # 从配置文件加载游戏列表
            games_config = self.load_games_config()

            if games_config:
                # 更新游戏列表
                self.games = games_config

                # 更新UI
                self.update_ui()

                logger.info(f"加载游戏列表成功，共{len(self.games)}个游戏")
            else:
                # 使用默认游戏列表
                self.use_default_games()
        except Exception as e:
            logger.error(f"加载游戏列表失败：{e}")

            # 使用默认游戏列表
            self.use_default_games()

    def load_games_config(self) -> List[Dict[str, Any]]:
        """
        从配置文件加载游戏列表

        Returns:
            List[Dict[str, Any]]: 游戏列表
        """
        # 游戏配置文件路径
        games_config_path = os.path.join(
            self.config.get("paths.configs", "configs"),
            "games.json"
        )

        # 检查配置文件是否存在
        if not os.path.exists(games_config_path):
            logger.warning(f"游戏配置文件不存在：{games_config_path}")
            return []

        try:
            # 加载配置文件
            with open(games_config_path, "r", encoding="utf-8") as f:
                games_config = json.load(f)

            # 验证配置文件格式
            if not isinstance(games_config, list):
                logger.warning(f"游戏配置文件格式错误：{games_config_path}")
                return []

            # 验证每个游戏配置
            valid_games = []
            for game in games_config:
                if isinstance(game, dict) and "id" in game and "name" in game:
                    valid_games.append(game)
                else:
                    logger.warning(f"游戏配置格式错误：{game}")

            return valid_games
        except Exception as e:
            logger.error(f"加载游戏配置文件失败：{e}")
            return []

    def use_default_games(self):
        """使用默认游戏列表"""
        # 默认游戏列表
        self.games = [
            {
                "id": "doudizhu_classic",
                "name": "斗地主经典版",
                "icon": "desktop/resources/icons/doudizhu.png",
                "description": "中国传统扑克牌游戏，一名地主对抗两名农民",
                "parameters": {
                    "num_players": 3,
                    "num_cards": 54,
                    "max_steps": 1000
                }
            },
            {
                "id": "doudizhu_2v1",
                "name": "斗地主二打一版",
                "icon": "desktop/resources/icons/doudizhu.png",
                "description": "斗地主变种，两名玩家对抗一名玩家",
                "parameters": {
                    "num_players": 3,
                    "num_cards": 54,
                    "max_steps": 1000
                }
            }
        ]

        # 更新UI
        self.update_ui()

        logger.info(f"使用默认游戏列表，共{len(self.games)}个游戏")

    def update_ui(self):
        """更新UI"""
        if self.selection_mode == "combo":
            # 清空下拉菜单
            self.game_combo.clear()

            # 添加游戏到下拉菜单
            for game in self.games:
                # 获取游戏图标
                icon_path = game.get("icon", "")
                if icon_path and os.path.exists(icon_path):
                    self.game_combo.addItem(QIcon(icon_path), game["name"])
                else:
                    self.game_combo.addItem(game["name"])

            # 选择第一个游戏
            if self.games:
                self.game_combo.setCurrentIndex(0)
                self.current_game = self.games[0]["id"]
        else:
            # 清空标签页
            self.game_tab.clear()

            # 添加游戏到标签页
            for game in self.games:
                # 获取游戏图标
                icon_path = game.get("icon", "")
                if icon_path and os.path.exists(icon_path):
                    self.game_tab.addTab(QIcon(icon_path), game["name"])
                else:
                    self.game_tab.addTab(game["name"])

            # 选择第一个游戏
            if self.games:
                self.game_tab.setCurrentIndex(0)
                self.current_game = self.games[0]["id"]

    def on_game_changed(self, game_name):
        """
        游戏选择变化处理（下拉菜单模式）

        Args:
            game_name: 游戏名称
        """
        # 查找游戏ID
        for game in self.games:
            if game["name"] == game_name:
                self.current_game = game["id"]

                # 发送游戏变化信号
                self.game_changed.emit(self.current_game)

                logger.info(f"选择游戏：{game_name}（{self.current_game}）")
                break

    def on_tab_changed(self, index):
        """
        游戏选择变化处理（标签页模式）

        Args:
            index: 标签页索引
        """
        if 0 <= index < len(self.games):
            self.current_game = self.games[index]["id"]

            # 发送游戏变化信号
            self.game_changed.emit(self.current_game)

            logger.info(f"选择游戏：{self.games[index]['name']}（{self.current_game}）")

    def get_current_game(self) -> str:
        """
        获取当前选择的游戏ID

        Returns:
            str: 游戏ID
        """
        return self.current_game

    def get_current_game_info(self) -> Dict[str, Any]:
        """
        获取当前选择的游戏信息

        Returns:
            Dict[str, Any]: 游戏信息
        """
        for game in self.games:
            if game["id"] == self.current_game:
                return game

        return {}

    def set_selection_mode(self, mode: str):
        """
        设置选择模式

        Args:
            mode (str): 选择模式，combo（下拉菜单）或tab（标签页）
        """
        if mode in ["combo", "tab"] and mode != self.selection_mode:
            self.selection_mode = mode

            # 重新设置UI
            self.setup_ui()

            # 更新UI
            self.update_ui()

            logger.info(f"设置选择模式：{mode}")

    def select_game(self, game_id: str) -> bool:
        """
        选择游戏

        Args:
            game_id (str): 游戏ID

        Returns:
            bool: 是否选择成功
        """
        for i, game in enumerate(self.games):
            if game["id"] == game_id:
                if self.selection_mode == "combo":
                    self.game_combo.setCurrentIndex(i)
                else:
                    self.game_tab.setCurrentIndex(i)

                self.current_game = game_id

                # 发送游戏变化信号
                self.game_changed.emit(self.current_game)

                logger.info(f"选择游戏：{game['name']}（{game_id}）")
                return True

        logger.warning(f"游戏不存在：{game_id}")
        return False
