"""
信息价值评估模块

提供评估获取特定信息价值的工具和方法，用于指导探索或信息收集行为。
"""
from typing import Dict, List, Optional, Any, Tuple, Union, Callable
import numpy as np
import torch
import logging

from cardgame_ai.games.common.belief_state import BeliefState, BeliefSource

# 配置日志
logger = logging.getLogger(__name__)


class InformationValueCalculator:
    """
    信息价值计算器

    提供多种方法计算获取特定信息的价值，用于指导探索或信息收集行为。
    """

    @staticmethod
    def basic_information_value(belief_state: BeliefState, card: str) -> float:
        """
        计算基本信息价值（基于概率不确定性）

        Args:
            belief_state (BeliefState): 信念状态
            card (str): 牌的表示

        Returns:
            float: 信息价值，越高表示获取该信息越有价值
        """
        # 信息价值与概率的不确定性相关
        # 当概率接近0.5时，不确定性最大，信息价值最高
        return belief_state.information_value(card)

    @staticmethod
    def entropy_reduction(belief_state: BeliefState, card: str) -> float:
        """
        计算熵减少（基于信息熵）

        Args:
            belief_state (BeliefState): 信念状态
            card (str): 牌的表示

        Returns:
            float: 信息价值，越高表示获取该信息越有价值
        """
        # 计算原始熵
        original_entropy = belief_state.entropy()

        # 创建两个假设的信念状态
        # 假设1：牌在对手手中
        positive_belief = InformationValueCalculator._create_hypothetical_belief(belief_state, card, True)
        positive_entropy = positive_belief.entropy()

        # 假设2：牌不在对手手中
        negative_belief = InformationValueCalculator._create_hypothetical_belief(belief_state, card, False)
        negative_entropy = negative_belief.entropy()

        # 计算期望熵减少
        prob = belief_state.get_probability(card)
        expected_entropy = prob * positive_entropy + (1 - prob) * negative_entropy

        # 熵减少 = 原始熵 - 期望熵
        entropy_reduction = original_entropy - expected_entropy

        # 确保熵减少为非负值
        return max(0.0, entropy_reduction)

    @staticmethod
    def action_distribution_change(
        belief_state: BeliefState,
        card: str,
        current_state: np.ndarray,
        policy_function: Callable[[BeliefState, np.ndarray], np.ndarray]
    ) -> float:
        """
        计算动作分布变化（基于KL散度）

        Args:
            belief_state (BeliefState): 信念状态
            card (str): 牌的表示
            current_state (np.ndarray): 当前游戏状态的向量表示
            policy_function (Callable): 策略函数，接受信念状态和当前状态，返回动作分布

        Returns:
            float: 信息价值，越高表示获取该信息越有价值
        """
        # 获取原始动作分布
        original_probs = policy_function(belief_state, current_state)

        # 创建两个假设的信念状态
        # 假设1：牌在对手手中
        positive_belief = InformationValueCalculator._create_hypothetical_belief(belief_state, card, True)
        positive_probs = policy_function(positive_belief, current_state)

        # 假设2：牌不在对手手中
        negative_belief = InformationValueCalculator._create_hypothetical_belief(belief_state, card, False)
        negative_probs = policy_function(negative_belief, current_state)

        # 计算KL散度
        kl_positive = InformationValueCalculator._calculate_kl_divergence(positive_probs, original_probs)
        kl_negative = InformationValueCalculator._calculate_kl_divergence(negative_probs, original_probs)

        # 计算期望KL散度
        prob = belief_state.get_probability(card)
        expected_kl = prob * kl_positive + (1 - prob) * kl_negative

        # 确保KL散度为非负值
        return max(0.0, expected_kl)

    @staticmethod
    def combined_information_value(
        belief_state: BeliefState,
        card: str,
        current_state: Optional[np.ndarray] = None,
        policy_function: Optional[Callable[[BeliefState, np.ndarray], np.ndarray]] = None,
        weights: Optional[Dict[str, float]] = None
    ) -> float:
        """
        计算组合信息价值

        Args:
            belief_state (BeliefState): 信念状态
            card (str): 牌的表示
            current_state (Optional[np.ndarray], optional): 当前游戏状态的向量表示. Defaults to None.
            policy_function (Optional[Callable], optional): 策略函数. Defaults to None.
            weights (Optional[Dict[str, float]], optional): 各种方法的权重. Defaults to None.

        Returns:
            float: 信息价值，越高表示获取该信息越有价值
        """
        # 默认权重
        if weights is None:
            weights = {
                'basic': 0.3,
                'entropy': 0.3,
                'action': 0.4
            }

        # 计算基本信息价值
        basic_value = InformationValueCalculator.basic_information_value(belief_state, card)

        # 如果没有提供额外信息，直接返回基本信息价值
        if current_state is None or policy_function is None:
            return basic_value

        # 计算熵减少
        entropy_value = InformationValueCalculator.entropy_reduction(belief_state, card)

        # 计算动作分布变化
        action_value = InformationValueCalculator.action_distribution_change(
            belief_state, card, current_state, policy_function
        )

        # 组合信息价值
        combined_value = (
            weights['basic'] * basic_value +
            weights['entropy'] * entropy_value +
            weights['action'] * action_value
        )

        return combined_value

    @staticmethod
    def calculate_all_cards(
        belief_state: BeliefState,
        all_cards: List[str],
        current_state: Optional[np.ndarray] = None,
        policy_function: Optional[Callable[[BeliefState, np.ndarray], np.ndarray]] = None,
        method: str = 'combined',
        top_n: Optional[int] = None
    ) -> Dict[str, float]:
        """
        计算所有牌的信息价值

        Args:
            belief_state (BeliefState): 信念状态
            all_cards (List[str]): 所有牌的列表
            current_state (Optional[np.ndarray], optional): 当前游戏状态的向量表示. Defaults to None.
            policy_function (Optional[Callable], optional): 策略函数. Defaults to None.
            method (str, optional): 计算方法，可选'basic'、'entropy'、'action'或'combined'. Defaults to 'combined'.
            top_n (Optional[int], optional): 返回信息价值最高的前N张牌. Defaults to None.

        Returns:
            Dict[str, float]: 牌到信息价值的映射
        """
        # 选择计算方法
        if method == 'basic':
            value_function = lambda card: InformationValueCalculator.basic_information_value(belief_state, card)
        elif method == 'entropy':
            value_function = lambda card: InformationValueCalculator.entropy_reduction(belief_state, card)
        elif method == 'action' and current_state is not None and policy_function is not None:
            value_function = lambda card: InformationValueCalculator.action_distribution_change(
                belief_state, card, current_state, policy_function
            )
        else:  # 'combined' or fallback
            value_function = lambda card: InformationValueCalculator.combined_information_value(
                belief_state, card, current_state, policy_function
            )

        # 计算所有牌的信息价值
        info_values = {}
        for card in all_cards:
            # 跳过已知的牌（概率为0或1）
            prob = belief_state.get_probability(card)
            if prob < 0.01 or prob > 0.99:
                info_values[card] = 0.0
                continue

            # 计算信息价值
            try:
                info_values[card] = value_function(card)
            except Exception as e:
                logger.warning(f"计算牌 {card} 的信息价值时出错: {e}")
                info_values[card] = 0.0

        # 如果指定了top_n，只返回信息价值最高的前N张牌
        if top_n is not None and top_n > 0:
            sorted_values = sorted(info_values.items(), key=lambda x: x[1], reverse=True)
            return dict(sorted_values[:top_n])

        return info_values

    @staticmethod
    def _create_hypothetical_belief(belief_state: BeliefState, card: str, is_present: bool) -> BeliefState:
        """
        创建假设的信念状态

        Args:
            belief_state (BeliefState): 原始信念状态
            card (str): 牌的表示
            is_present (bool): 是否假设该牌在对手手中

        Returns:
            BeliefState: 假设的信念状态
        """
        # 复制当前信念状态的概率分布
        new_probs = belief_state.card_probabilities.copy()

        # 根据假设更新概率
        if is_present:
            # 假设牌在对手手中
            new_probs[card] = 1.0
        else:
            # 假设牌不在对手手中
            new_probs[card] = 0.0

        # 创建新的信念状态
        hypothetical_belief = BeliefState(
            player_id=belief_state.player_id,
            card_probabilities=new_probs,
            estimated_hand_length=belief_state.estimated_hand_length,
            source=BeliefSource.INFERENCE,
            confidence=0.9  # 假设的信念状态置信度较高
        )

        # 归一化概率
        hypothetical_belief.normalize_probabilities()

        return hypothetical_belief

    @staticmethod
    def _calculate_kl_divergence(p: np.ndarray, q: np.ndarray) -> float:
        """
        计算KL散度

        Args:
            p (np.ndarray): 分布P
            q (np.ndarray): 分布Q

        Returns:
            float: KL(P||Q)
        """
        # 避免除以零
        p = np.clip(p, 1e-10, 1.0)
        q = np.clip(q, 1e-10, 1.0)

        # 计算KL散度
        return np.sum(p * np.log(p / q))


# 便捷函数
def calculate_information_value(
    belief_state: BeliefState,
    card: str,
    current_state: Optional[np.ndarray] = None,
    policy_function: Optional[Callable[[BeliefState, np.ndarray], np.ndarray]] = None,
    method: str = 'combined'
) -> float:
    """
    计算获取特定牌信息的价值

    Args:
        belief_state (BeliefState): 信念状态
        card (str): 牌的表示
        current_state (Optional[np.ndarray], optional): 当前游戏状态的向量表示. Defaults to None.
        policy_function (Optional[Callable], optional): 策略函数. Defaults to None.
        method (str, optional): 计算方法，可选'basic'、'entropy'、'action'或'combined'. Defaults to 'combined'.

    Returns:
        float: 信息价值，越高表示获取该信息越有价值
    """
    if method == 'basic':
        return InformationValueCalculator.basic_information_value(belief_state, card)
    elif method == 'entropy':
        return InformationValueCalculator.entropy_reduction(belief_state, card)
    elif method == 'action' and current_state is not None and policy_function is not None:
        return InformationValueCalculator.action_distribution_change(
            belief_state, card, current_state, policy_function
        )
    else:  # 'combined' or fallback
        return InformationValueCalculator.combined_information_value(
            belief_state, card, current_state, policy_function
        )


def calculate_all_cards_information_value(
    belief_state: BeliefState,
    all_cards: List[str],
    current_state: Optional[np.ndarray] = None,
    policy_function: Optional[Callable[[BeliefState, np.ndarray], np.ndarray]] = None,
    method: str = 'combined',
    top_n: Optional[int] = None
) -> Dict[str, float]:
    """
    计算所有牌的信息价值

    Args:
        belief_state (BeliefState): 信念状态
        all_cards (List[str]): 所有牌的列表
        current_state (Optional[np.ndarray], optional): 当前游戏状态的向量表示. Defaults to None.
        policy_function (Optional[Callable], optional): 策略函数. Defaults to None.
        method (str, optional): 计算方法，可选'basic'、'entropy'、'action'或'combined'. Defaults to 'combined'.
        top_n (Optional[int], optional): 返回信息价值最高的前N张牌. Defaults to None.

    Returns:
        Dict[str, float]: 牌到信息价值的映射
    """
    return InformationValueCalculator.calculate_all_cards(
        belief_state, all_cards, current_state, policy_function, method, top_n
    )
