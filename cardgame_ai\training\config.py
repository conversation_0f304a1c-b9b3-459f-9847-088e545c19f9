"""
训练配置模块

提供训练配置类，用于管理训练参数。
"""
from typing import Dict, Any, List, Tuple, Optional, Union
import os
import json
import yaml


class TrainingConfig:
    """
    训练配置类
    
    管理训练参数，支持从文件加载和保存到文件。
    """
    
    def __init__(self, **kwargs):
        """
        初始化训练配置
        
        Args:
            **kwargs: 配置参数
        """
        # 通用参数
        self.experiment_name = kwargs.get('experiment_name', 'default')
        self.algorithm = kwargs.get('algorithm', 'dqn')
        self.seed = kwargs.get('seed', 42)
        self.device = kwargs.get('device', 'auto')
        
        # 路径参数
        self.save_dir = kwargs.get('save_dir', 'results')
        self.model_dir = kwargs.get('model_dir', os.path.join(self.save_dir, self.experiment_name, 'models'))
        self.log_dir = kwargs.get('log_dir', os.path.join(self.save_dir, self.experiment_name, 'logs'))
        self.tensorboard_dir = kwargs.get('tensorboard_dir', os.path.join(self.save_dir, self.experiment_name, 'tensorboard'))
        
        # 训练参数
        self.num_episodes = kwargs.get('num_episodes', 10000)
        self.max_steps_per_episode = kwargs.get('max_steps_per_episode', 1000)
        self.batch_size = kwargs.get('batch_size', 64)
        self.learning_rate = kwargs.get('learning_rate', 0.001)
        self.gamma = kwargs.get('gamma', 0.99)
        
        # 网络参数
        self.hidden_dims = kwargs.get('hidden_dims', [128, 128])
        
        # 经验回放参数
        self.buffer_size = kwargs.get('buffer_size', 10000)
        self.prioritized_replay = kwargs.get('prioritized_replay', False)
        self.alpha = kwargs.get('alpha', 0.6)
        self.beta = kwargs.get('beta', 0.4)
        self.beta_increment = kwargs.get('beta_increment', 0.001)
        
        # DQN参数
        self.epsilon = kwargs.get('epsilon', 0.1)
        self.epsilon_decay = kwargs.get('epsilon_decay', 0.995)
        self.epsilon_min = kwargs.get('epsilon_min', 0.01)
        self.target_update_frequency = kwargs.get('target_update_frequency', 100)
        self.double_dqn = kwargs.get('double_dqn', False)
        self.dueling_dqn = kwargs.get('dueling_dqn', False)
        
        # PPO参数
        self.gae_lambda = kwargs.get('gae_lambda', 0.95)
        self.clip_ratio = kwargs.get('clip_ratio', 0.2)
        self.value_coef = kwargs.get('value_coef', 0.5)
        self.entropy_coef = kwargs.get('entropy_coef', 0.01)
        self.max_grad_norm = kwargs.get('max_grad_norm', 0.5)
        self.update_epochs = kwargs.get('update_epochs', 4)
        self.shared_network = kwargs.get('shared_network', True)
        
        # 自我对弈参数
        self.self_play = kwargs.get('self_play', False)
        self.self_play_interval = kwargs.get('self_play_interval', 100)
        self.self_play_games = kwargs.get('self_play_games', 100)
        self.history_size = kwargs.get('history_size', 20)
        self.sampling_strategy = kwargs.get('sampling_strategy', 'latest')
        
        # 评估参数
        self.eval_interval = kwargs.get('eval_interval', 100)
        self.eval_episodes = kwargs.get('eval_episodes', 10)
        
        # 回调参数
        self.save_interval = kwargs.get('save_interval', 100)
        self.log_interval = kwargs.get('log_interval', 10)
        self.early_stopping = kwargs.get('early_stopping', False)
        self.patience = kwargs.get('patience', 10)
        self.min_delta = kwargs.get('min_delta', 0.01)
        
        # 其他参数
        for key, value in kwargs.items():
            if not hasattr(self, key):
                setattr(self, key, value)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典
        
        Returns:
            Dict[str, Any]: 配置字典
        """
        return {k: v for k, v in self.__dict__.items() if not k.startswith('_')}
    
    def save(self, path: str) -> None:
        """
        保存配置到文件
        
        Args:
            path (str): 文件路径
        """
        # 创建目录
        os.makedirs(os.path.dirname(path), exist_ok=True)
        
        # 根据文件扩展名选择保存格式
        _, ext = os.path.splitext(path)
        
        if ext.lower() in ['.json']:
            with open(path, 'w', encoding='utf-8') as f:
                json.dump(self.to_dict(), f, indent=2, ensure_ascii=False)
        elif ext.lower() in ['.yaml', '.yml']:
            with open(path, 'w', encoding='utf-8') as f:
                yaml.dump(self.to_dict(), f, default_flow_style=False)
        else:
            raise ValueError(f"不支持的文件格式: {ext}")
    
    @classmethod
    def load(cls, path: str) -> 'TrainingConfig':
        """
        从文件加载配置
        
        Args:
            path (str): 文件路径
            
        Returns:
            TrainingConfig: 配置对象
        """
        # 检查文件是否存在
        if not os.path.exists(path):
            raise FileNotFoundError(f"配置文件不存在: {path}")
        
        # 根据文件扩展名选择加载格式
        _, ext = os.path.splitext(path)
        
        if ext.lower() in ['.json']:
            with open(path, 'r', encoding='utf-8') as f:
                config_dict = json.load(f)
        elif ext.lower() in ['.yaml', '.yml']:
            with open(path, 'r', encoding='utf-8') as f:
                config_dict = yaml.safe_load(f)
        else:
            raise ValueError(f"不支持的文件格式: {ext}")
        
        return cls(**config_dict)
    
    def __str__(self) -> str:
        """
        转换为字符串表示
        
        Returns:
            str: 字符串表示
        """
        return f"TrainingConfig(experiment_name={self.experiment_name}, algorithm={self.algorithm})"
    
    def __repr__(self) -> str:
        """
        转换为详细字符串表示
        
        Returns:
            str: 详细字符串表示
        """
        return f"TrainingConfig({self.to_dict()})"
