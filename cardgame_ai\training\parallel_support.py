"""
并行化支持模块

提供高效的并行化支持，提高算法的计算效率和资源利用率。包括实现并行自我对弈、
并行MCTS搜索、并行经验回放和并行环境模拟等技术，使算法能够更高效地运行。
"""
import os
import time
import logging
import random
import threading
import multiprocessing
from typing import Dict, List, Tuple, Any, Optional, Union, Callable
import numpy as np
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor, as_completed
import torch

# 添加 Ray 支持
try:
    import ray
except ImportError:
    ray = None

from cardgame_ai.core.base import State, Action, Experience, Batch
from cardgame_ai.core.environment import Environment
from cardgame_ai.core.agent import Agent
from cardgame_ai.algorithms.mcts import MCTS, Node

# 设置日志
logger = logging.getLogger(__name__)


class ParallelSelfPlay:
    """
    并行自我对弈系统

    实现高效的并行自我对弈，支持多进程和多线程模式，
    以及异步经验收集和分布式运行。
    """

    def __init__(
        self,
        save_path: str = 'experiences',
        max_workers: Optional[int] = None,
        use_multiprocessing: bool = True,
        batch_size: int = 16,
        async_collection: bool = False,
        shared_memory: bool = False,
        distributed: bool = False
    ):
        """
        初始化并行自我对弈系统

        Args:
            save_path: 经验数据保存路径
            max_workers: 最大并行工作数，默认为可用CPU核心数
            use_multiprocessing: 是否使用多进程，如果为False则使用多线程
            batch_size: 批处理大小，用于并行处理
            async_collection: 是否使用异步经验收集
            shared_memory: 是否使用共享内存，只在use_multiprocessing=True时有效
            distributed: 是否使用分布式模式
        """
        self.save_path = save_path
        self.max_workers = max_workers or multiprocessing.cpu_count()
        self.use_multiprocessing = use_multiprocessing
        self.batch_size = batch_size
        self.async_collection = async_collection
        self.shared_memory = shared_memory and use_multiprocessing
        self.distributed = distributed

        # 创建保存目录
        os.makedirs(save_path, exist_ok=True)

        # 异步收集器
        self.async_collector = None
        if self.async_collection:
            self.async_collector = self._create_async_collector()

        # 共享内存管理器
        self.shared_memory_manager = None
        if self.shared_memory:
            self.shared_memory_manager = multiprocessing.Manager()

        # 分布式运行管理器
        self.distributed_manager = None
        if self.distributed:
            self.distributed_manager = self._create_distributed_manager()

        # 初始化 Ray 分布式
        self.ray_enabled = False
        if self.distributed and ray is not None:
            if not ray.is_initialized():
                ray.init(ignore_reinit_error=True)
            self.ray_enabled = True

    def _create_async_collector(self) -> Any:
        """
        创建异步经验收集器

        Returns:
            异步经验收集器
        """
        # 创建线程安全的队列
        if self.shared_memory:
            # 使用进程安全的队列
            return self.shared_memory_manager.Queue()
        else:
            # 使用线程安全的队列
            import queue
            return queue.Queue()

    def _create_distributed_manager(self) -> Any:
        """
        创建分布式运行管理器

        Returns:
            分布式运行管理器
        """
        # 如果支持PyTorch分布式，使用DistributedDataParallel
        if torch.distributed.is_available():
            return {
                'is_initialized': torch.distributed.is_initialized(),
                'world_size': torch.distributed.get_world_size() if torch.distributed.is_initialized() else 1,
                'rank': torch.distributed.get_rank() if torch.distributed.is_initialized() else 0
            }
        return None

    def generate_experience(
        self,
        env: Environment,
        agent: Agent,
        num_games: int,
        temperature: float = 1.0,
        save: bool = True,
        disable_tqdm: bool = False
    ) -> List[Experience]:
        """
        生成并行自我对弈经验数据

        Args:
            env: 游戏环境
            agent: 用于自我对弈的代理
            num_games: 对弈的游戏数
            temperature: 温度参数，控制动作采样的随机性
            save: 是否保存经验数据
            disable_tqdm: 是否禁用进度条

        Returns:
            生成的经验数据
        """
        start_time = time.time()
        all_experiences = []

        # 计算每个进程/线程的游戏数
        games_per_worker = max(1, num_games // self.max_workers)
        remaining_games = num_games % self.max_workers

        # 创建游戏分配
        game_assignments = [games_per_worker] * self.max_workers
        for i in range(remaining_games):
            game_assignments[i] += 1

        # 过滤掉游戏数为0的工作者
        game_assignments = [games for games in game_assignments if games > 0]
        actual_workers = len(game_assignments)

        logger.info(f"开始并行生成自我对弈数据，游戏数: {num_games}，工作者数: {actual_workers}")

        # 定义单游戏对弈函数
        def _play_single_game(env_copy: Environment, agent_copy: Agent) -> List[Experience]:
            """
            运行单局游戏并收集经验

            Args:
                env_copy: 环境副本
                agent_copy: 代理副本

            Returns:
                收集的经验
            """
            # 初始化环境
            state = env_copy.reset()
            done = False
            game_experiences = []

            # 运行游戏
            while not done:
                # 获取动作
                action = agent_copy.select_action(state, temperature=temperature)

                # 执行动作
                next_state, reward, done, info = env_copy.step(action)

                # 创建经验
                experience = Experience(state, action, reward, next_state, done, info)
                game_experiences.append(experience)

                # 更新状态
                state = next_state

            return game_experiences

        # 定义批处理函数
        def _play_batch_games(num_games_batch: int) -> List[Experience]:
            """
            运行一批游戏

            Args:
                num_games_batch: 批次中的游戏数

            Returns:
                收集的经验
            """
            
            # 新增 Ray 分布式逻辑
            if self.ray_enabled:
                from ray.util import ActorPool
                actors = [RemoteBatchWorker.remote(env, agent) for _ in game_assignments]
                pool = ActorPool(actors)
                distributed_exps = []
                for result in pool.map(lambda actor, n: actor.run.remote(n, temperature), game_assignments):
                    distributed_exps.extend(result)
                return distributed_exps

            batch_experiences = []

            # 创建环境和代理的副本
            env_copy = env.clone() if hasattr(env, 'clone') else env
            agent_copy = agent  # 假设代理是线程安全的

            # 运行指定数量的游戏
            for _ in range(num_games_batch):
                game_experiences = _play_single_game(env_copy, agent_copy)
                batch_experiences.extend(game_experiences)

                # 如果使用异步收集，将经验添加到队列
                if self.async_collection:
                    self.async_collector.put(game_experiences)

            return batch_experiences

        # 如果使用异步收集，启动收集线程
        collector_thread = None
        if self.async_collection:
            # 创建异步收集线程
            def _collector_thread_func():
                while True:
                    try:
                        # 从队列中获取经验
                        experiences = self.async_collector.get(timeout=1.0)
                        all_experiences.extend(experiences)

                        # 标记任务完成
                        self.async_collector.task_done()
                    except Exception as e:
                        # 如果队列为空或超时，检查是否应该结束
                        if collector_stop_event.is_set():
                            break

            # 创建停止事件
            collector_stop_event = threading.Event()

            # 启动收集线程
            collector_thread = threading.Thread(target=_collector_thread_func, daemon=True)
            collector_thread.start()

        # 确定执行器类型
        executor_class = ProcessPoolExecutor if self.use_multiprocessing else ThreadPoolExecutor

        # 并行执行游戏
        with executor_class(max_workers=actual_workers) as executor:
            # 提交批处理任务
            futures = [executor.submit(_play_batch_games, num_games_batch)
                      for num_games_batch in game_assignments]

            # 创建进度条
            if not disable_tqdm:
                from tqdm import tqdm
                futures_iterator = tqdm(as_completed(futures), total=len(futures), desc="生成游戏")
            else:
                futures_iterator = as_completed(futures)

            # 收集结果
            completed = 0
            for future in futures_iterator:
                try:
                    # 如果不使用异步收集，直接获取结果
                    if not self.async_collection:
                        batch_experiences = future.result()
                        all_experiences.extend(batch_experiences)

                    # 更新完成计数
                    completed += 1

                    # 打印进度
                    if disable_tqdm and (completed % 5 == 0 or completed == len(futures)):
                        elapsed_time = time.time() - start_time
                        logger.info(
                            f"已完成 {completed}/{len(futures)} 批游戏 | "
                            f"时间: {elapsed_time:.2f}s"
                        )
                except Exception as e:
                    logger.error(f"游戏批次生成失败: {str(e)}")

        # 如果使用异步收集，等待收集完成
        if self.async_collection and collector_thread is not None:
            # 等待队列清空
            if hasattr(self.async_collector, 'join'):
                self.async_collector.join(timeout=10.0)

            # 停止收集线程
            collector_stop_event.set()
            collector_thread.join(timeout=5.0)

        # 计算统计信息
        total_time = time.time() - start_time
        experiences_per_second = len(all_experiences) / max(1.0, total_time)
        games_per_second = num_games / max(1.0, total_time)

        logger.info(
            f"并行自我对弈完成 | "
            f"总游戏数: {num_games} | "
            f"总经验数: {len(all_experiences)} | "
            f"总时间: {total_time:.2f}s | "
            f"每秒游戏数: {games_per_second:.2f} | "
            f"每秒经验数: {experiences_per_second:.2f}"
        )

        # 如果需要保存经验
        if save:
            self._save_experiences(all_experiences)

        return all_experiences

    def _save_experiences(self, experiences: List[Experience]) -> None:
        """
        保存经验数据

        Args:
            experiences: 经验数据
        """
        # 创建保存路径
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        save_path = os.path.join(self.save_path, f"experiences_{timestamp}.npz")

        # 将经验转换为可序列化的格式
        serialized_experiences = [exp.to_dict() if hasattr(exp, 'to_dict') else exp for exp in experiences]

        # 保存经验
        np.savez_compressed(save_path, experiences=serialized_experiences)

        logger.info(f"已保存 {len(experiences)} 个经验到 {save_path}")

    def generate_multi_agent_experience(
        self,
        env: Environment,
        agents: List[Agent],
        num_games: int,
        temperature: float = 1.0,
        save: bool = True,
        disable_tqdm: bool = False
    ) -> Dict[int, List[Experience]]:
        """
        生成并行多代理自我对弈经验数据

        Args:
            env: 游戏环境
            agents: 代理列表，每个位置对应一个玩家
            num_games: 对弈的游戏数
            temperature: 温度参数
            save: 是否保存经验数据
            disable_tqdm: 是否禁用进度条

        Returns:
            生成的经验数据，按玩家ID组织
        """
        # 类似于generate_experience的实现，但针对多代理场景
        # 简化起见，这里省略具体实现
        # 实际实现时需要根据多代理的特性进行调整

        # 返回空字典作为占位符
        return {i: [] for i in range(len(agents))}


class ParallelMCTS:
    """
    并行MCTS搜索

    实现高效的并行MCTS搜索，支持树并行、叶节点并行和根并行等模式。
    """

    def __init__(
        self,
        num_simulations: int = 50,
        num_parallel: int = 4,
        parallel_mode: str = 'leaf',
        use_gpu: bool = True,
        batch_size: int = 16,
        use_multiprocessing: bool = False,
        max_workers: Optional[int] = None
    ):
        """
        初始化并行MCTS搜索

        Args:
            num_simulations: 模拟次数
            num_parallel: 并行数量
            parallel_mode: 并行模式，支持'leaf'、'root'、'tree'
            use_gpu: 是否使用GPU
            batch_size: 批处理大小
            use_multiprocessing: 是否使用多进程
            max_workers: 最大并行工作数，默认为可用CPU核心数
        """
        self.num_simulations = num_simulations
        self.num_parallel = min(num_parallel, num_simulations)
        self.parallel_mode = parallel_mode
        self.use_gpu = use_gpu and torch.cuda.is_available()
        self.batch_size = batch_size
        self.use_multiprocessing = use_multiprocessing
        self.max_workers = max_workers or multiprocessing.cpu_count()

        # 创建基础MCTS搜索器
        from cardgame_ai.algorithms.mcts import MCTS
        self.base_mcts = MCTS(num_simulations=1)  # 每个并行实例只运行一次模拟

        # 创建线程池
        self.executor = ThreadPoolExecutor(max_workers=self.max_workers) if not use_multiprocessing else None

    def run(
        self,
        root_state: Any,
        model: Any,
        temperature: float = 1.0,
        actions_mask: Optional[List[int]] = None,
        add_exploration_noise: bool = True,
        dirichlet_alpha: float = 0.3,
        exploration_fraction: float = 0.25
    ) -> Tuple[Dict[int, int], Dict[int, float]]:
        """
        运行并行MCTS搜索

        Args:
            root_state: 根状态
            model: 用于预测的模型
            temperature: 温度参数
            actions_mask: 合法动作掩码
            add_exploration_noise: 是否添加探索噪声
            dirichlet_alpha: 狂热参数阿尔法
            exploration_fraction: 探索比例

        Returns:
            访问计数和策略
        """
        # 根据并行模式选择不同的并行策略
        if self.parallel_mode == 'leaf':
            return self._run_leaf_parallel(
                root_state, model, temperature, actions_mask,
                add_exploration_noise, dirichlet_alpha, exploration_fraction
            )
        elif self.parallel_mode == 'root':
            return self._run_root_parallel(
                root_state, model, temperature, actions_mask,
                add_exploration_noise, dirichlet_alpha, exploration_fraction
            )
        elif self.parallel_mode == 'tree':
            return self._run_tree_parallel(
                root_state, model, temperature, actions_mask,
                add_exploration_noise, dirichlet_alpha, exploration_fraction
            )
        else:
            raise ValueError(f"不支持的并行模式: {self.parallel_mode}")

    def _run_leaf_parallel(
        self,
        root_state: Any,
        model: Any,
        temperature: float,
        actions_mask: Optional[List[int]],
        add_exploration_noise: bool,
        dirichlet_alpha: float,
        exploration_fraction: float
    ) -> Tuple[Dict[int, int], Dict[int, float]]:
        """
        运行叶节点并行MCTS搜索

        在叶节点并行模式下，我们并行计算多个叶节点的评估值。

        Args:
            root_state: 根状态
            model: 用于预测的模型
            temperature: 温度参数
            actions_mask: 合法动作掩码
            add_exploration_noise: 是否添加探索噪声
            dirichlet_alpha: 狂热参数阿尔法
            exploration_fraction: 探索比例

        Returns:
            访问计数和策略
        """
        # 创建根节点
        root = Node(0)

        # 使用模型表示状态
        hidden_state = model.represent(root_state)
        policy_logits, value = model.predict(hidden_state)

        # 展开根节点
        root.expand(policy_logits, value, actions_mask)

        # 添加探索噪声
        if add_exploration_noise:
            noise = np.random.dirichlet([dirichlet_alpha] * root.action_space_size())
            for i, action in enumerate(root.children.keys()):
                root.children[action].prior = root.children[action].prior * (1 - exploration_fraction) + noise[i] * exploration_fraction

        # 创建叶节点批处理器
        leaf_batch_evaluator = self._create_leaf_batch_evaluator(model)

        # 运行模拟
        for _ in range(self.num_simulations):
            # 收集叶节点
            leaf_nodes = []
            search_paths = []

            # 并行选择多个叶节点
            for _ in range(self.num_parallel):
                # 选择叶节点
                path = []
                node = root
                while node.expanded():
                    action = node.select_action()
                    path.append((node, action))
                    node = node.children[action]

                # 添加到叶节点列表
                leaf_nodes.append(node)
                search_paths.append(path)

            # 并行评估叶节点
            leaf_states = [path[-1][0].state for path in search_paths]
            leaf_actions = [path[-1][1] for path in search_paths]

            # 批量评估叶节点
            next_states, rewards, policies, values = leaf_batch_evaluator(leaf_states, leaf_actions)

            # 并行展开和反向传播
            for i, (node, path) in enumerate(zip(leaf_nodes, search_paths)):
                # 展开叶节点
                node.expand(policies[i], values[i])
                node.state = next_states[i]

                # 反向传播
                value = values[i]
                for parent, action in reversed(path):
                    parent.children[action].value_sum += value
                    parent.children[action].visit_count += 1
                    value = rewards[i] + 0.99 * value  # 使用折扣因子

        # 计算访问计数和策略
        visit_counts = {action: child.visit_count for action, child in root.children.items()}
        policy = self._get_policy(root, temperature)

        return visit_counts, policy

    def _create_leaf_batch_evaluator(self, model: Any) -> Callable:
        """
        创建叶节点批处理评估器

        Args:
            model: 用于预测的模型

        Returns:
            叶节点批处理评估器
        """
        def evaluate_batch(states, actions):
            # 创建批处理输入
            batch_size = len(states)
            batch_states = torch.stack(states) if isinstance(states[0], torch.Tensor) else torch.tensor(states)
            batch_actions = torch.tensor(actions)

            # 移动到设备
            device = torch.device('cuda' if self.use_gpu else 'cpu')
            batch_states = batch_states.to(device)
            batch_actions = batch_actions.to(device)

            # 执行模型预测
            with torch.no_grad():
                # 使用动力学模型预测下一个状态和奖励
                next_states, rewards = model.dynamics(batch_states, batch_actions)

                # 预测下一个状态的策略和价值
                policy_logits, values = model.predict(next_states)

            # 转换为CPU并转换为列表
            next_states = next_states.cpu().numpy()
            rewards = rewards.cpu().numpy()
            policy_logits = policy_logits.cpu().numpy()
            values = values.cpu().numpy()

            return next_states, rewards, policy_logits, values

        return evaluate_batch

    def _run_root_parallel(
        self,
        root_state: Any,
        model: Any,
        temperature: float,
        actions_mask: Optional[List[int]],
        add_exploration_noise: bool,
        dirichlet_alpha: float,
        exploration_fraction: float
    ) -> Tuple[Dict[int, int], Dict[int, float]]:
        """
        运行根并行MCTS搜索

        在根并行模式下，我们并行运行多个独立的MCTS搜索，然后合并结果。

        Args:
            root_state: 根状态
            model: 用于预测的模型
            temperature: 温度参数
            actions_mask: 合法动作掩码
            add_exploration_noise: 是否添加探索噪声
            dirichlet_alpha: 狂热参数阿尔法
            exploration_fraction: 探索比例

        Returns:
            访问计数和策略
        """
        # 计算每个并行实例的模拟次数
        simulations_per_worker = max(1, self.num_simulations // self.num_parallel)
        remaining_simulations = self.num_simulations % self.num_parallel

        # 创建模拟分配
        simulation_assignments = [simulations_per_worker] * self.num_parallel
        for i in range(remaining_simulations):
            simulation_assignments[i] += 1

        # 定义单个根搜索函数
        def _run_single_root_search(num_simulations):
            # 创建一个新的MCTS实例
            mcts = MCTS(
                num_simulations=num_simulations,
                discount=0.99,
                dirichlet_alpha=dirichlet_alpha,
                exploration_fraction=exploration_fraction
            )

            # 运行搜索
            return mcts.run(
                root_state=root_state,
                model=model,
                temperature=1.0,  # 使用温度1.0收集访问计数
                actions_mask=actions_mask,
                add_exploration_noise=add_exploration_noise
            )

        # 并行运行根搜索
        futures = []
        with ThreadPoolExecutor(max_workers=self.num_parallel) as executor:
            for num_sims in simulation_assignments:
                futures.append(executor.submit(_run_single_root_search, num_sims))

        # 收集结果
        all_visit_counts = {}
        for future in futures:
            visit_counts, _ = future.result()

            # 合并访问计数
            for action, count in visit_counts.items():
                if action in all_visit_counts:
                    all_visit_counts[action] += count
                else:
                    all_visit_counts[action] = count

        # 计算策略
        policy = self._compute_policy_from_counts(all_visit_counts, temperature)

        return all_visit_counts, policy

    def _run_tree_parallel(
        self,
        root_state: Any,
        model: Any,
        temperature: float,
        actions_mask: Optional[List[int]],
        add_exploration_noise: bool,
        dirichlet_alpha: float,
        exploration_fraction: float
    ) -> Tuple[Dict[int, int], Dict[int, float]]:
        """
        运行树并行MCTS搜索

        在树并行模式下，我们并行处理树的不同分支。

        Args:
            root_state: 根状态
            model: 用于预测的模型
            temperature: 温度参数
            actions_mask: 合法动作掩码
            add_exploration_noise: 是否添加探索噪声
            dirichlet_alpha: 狂热参数阿尔法
            exploration_fraction: 探索比例

        Returns:
            访问计数和策略
        """
        # 创建根节点
        root = Node(0)

        # 使用模型表示状态
        hidden_state = model.represent(root_state)
        policy_logits, value = model.predict(hidden_state)

        # 展开根节点
        root.expand(policy_logits, value, actions_mask)

        # 添加探索噪声
        if add_exploration_noise:
            noise = np.random.dirichlet([dirichlet_alpha] * root.action_space_size())
            for i, action in enumerate(root.children.keys()):
                root.children[action].prior = root.children[action].prior * (1 - exploration_fraction) + noise[i] * exploration_fraction

        # 定义并行处理分支的函数
        def _process_branch(action):
            # 获取子节点
            child = root.children[action]

            # 创建一个新的MCTS实例
            mcts = MCTS(
                num_simulations=self.num_simulations // len(root.children),
                discount=0.99,
                dirichlet_alpha=dirichlet_alpha,
                exploration_fraction=exploration_fraction
            )

            # 运行子树搜索
            child_state = model.dynamics(hidden_state, action)[0]  # 获取子状态
            visit_counts, _ = mcts.run(
                root_state=child_state,
                model=model,
                temperature=1.0,
                actions_mask=None,  # 子节点不需要动作掩码
                add_exploration_noise=False  # 子节点不需要探索噪声
            )

            # 更新子节点的访问计数和值
            child.visit_count = sum(visit_counts.values())
            child.value_sum = child.value * child.visit_count

            return action, child.visit_count

        # 并行处理每个分支
        futures = []
        with ThreadPoolExecutor(max_workers=self.num_parallel) as executor:
            for action in root.children.keys():
                futures.append(executor.submit(_process_branch, action))

        # 收集结果
        visit_counts = {}
        for future in futures:
            action, count = future.result()
            visit_counts[action] = count

        # 计算策略
        policy = self._compute_policy_from_counts(visit_counts, temperature)

        return visit_counts, policy

    def _compute_policy_from_counts(self, visit_counts: Dict[int, int], temperature: float) -> Dict[int, float]:
        """
        根据访问计数计算策略

        Args:
            visit_counts: 访问计数
            temperature: 温度参数

        Returns:
            策略
        """
        # 如果温度接近0，选择访问计数最高的动作
        if temperature < 0.01:
            action = max(visit_counts.items(), key=lambda x: x[1])[0]
            policy = {a: 1.0 if a == action else 0.0 for a in visit_counts.keys()}
            return policy

        # 应用温度
        counts = np.array([visit_counts.get(a, 0) for a in visit_counts.keys()])
        if temperature != 1.0:
            counts = counts ** (1.0 / temperature)

        # 归一化
        total = counts.sum()
        if total > 0:
            probs = counts / total
        else:
            # 如果所有计数都为0，使用均匀分布
            probs = np.ones_like(counts) / len(counts)

        # 创建策略字典
        policy = {a: float(probs[i]) for i, a in enumerate(visit_counts.keys())}

        return policy

    def _get_policy(self, root: Node, temperature: float) -> Dict[int, float]:
        """
        根据根节点和温度获取策略

        Args:
            root: 根节点
            temperature: 温度参数

        Returns:
            策略
        """
        # 获取访问计数
        visit_counts = {action: child.visit_count for action, child in root.children.items()}

        # 计算策略
        return self._compute_policy_from_counts(visit_counts, temperature)


class ParallelEnvironment:
    """
    并行环境模拟

    实现高效的并行环境模拟，支持向量化和并行执行多个环境实例。
    """

    def __init__(
        self,
        env_class: Any,
        num_envs: int = 4,
        use_multiprocessing: bool = False,
        max_workers: Optional[int] = None,
        vectorized: bool = True,
        shared_memory: bool = False,
        env_args: Optional[Dict[str, Any]] = None
    ):
        """
        初始化并行环境模拟

        Args:
            env_class: 环境类
            num_envs: 环境数量
            use_multiprocessing: 是否使用多进程
            max_workers: 最大并行工作数，默认为可用CPU核心数
            vectorized: 是否使用向量化环境
            shared_memory: 是否使用共享内存，只在use_multiprocessing=True时有效
            env_args: 环境创建参数
        """
        self.env_class = env_class
        self.num_envs = num_envs
        self.use_multiprocessing = use_multiprocessing
        self.max_workers = max_workers or multiprocessing.cpu_count()
        self.vectorized = vectorized
        self.shared_memory = shared_memory and use_multiprocessing
        self.env_args = env_args or {}

        # 创建环境
        self.envs = self._create_environments()

        # 创建执行器
        self.executor = None
        if not self.vectorized:
            executor_class = ProcessPoolExecutor if use_multiprocessing else ThreadPoolExecutor
            self.executor = executor_class(max_workers=min(self.max_workers, self.num_envs))

        # 创建共享内存管理器
        self.shared_memory_manager = None
        if self.shared_memory:
            self.shared_memory_manager = multiprocessing.Manager()

    def _create_environments(self) -> List[Any]:
        """
        创建环境

        Returns:
            环境列表
        """
        envs = []

        # 创建环境
        for _ in range(self.num_envs):
            env = self.env_class(**self.env_args)
            envs.append(env)

        return envs

    def reset(self) -> List[Any]:
        """
        重置所有环境

        Returns:
            初始状态列表
        """
        if self.vectorized:
            # 向量化模式
            return [env.reset() for env in self.envs]
        else:
            # 并行模式
            futures = [self.executor.submit(env.reset) for env in self.envs]
            return [future.result() for future in futures]

    def step(self, actions: List[Any]) -> Tuple[List[Any], List[float], List[bool], List[Dict[str, Any]]]:
        """
        并行执行动作

        Args:
            actions: 动作列表

        Returns:
            下一个状态列表、奖励列表、完成标志列表和信息列表
        """
        if self.vectorized:
            # 向量化模式
            results = [env.step(action) for env, action in zip(self.envs, actions)]
        else:
            # 并行模式
            futures = [self.executor.submit(env.step, action) for env, action in zip(self.envs, actions)]
            results = [future.result() for future in futures]

        # 分解结果
        next_states, rewards, dones, infos = zip(*results)

        return list(next_states), list(rewards), list(dones), list(infos)

    def close(self) -> None:
        """
        关闭环境
        """
        # 关闭所有环境
        for env in self.envs:
            if hasattr(env, 'close'):
                env.close()

        # 关闭执行器
        if self.executor is not None:
            self.executor.shutdown()

        # 关闭共享内存管理器
        if self.shared_memory_manager is not None:
            self.shared_memory_manager.shutdown()


def test_parallel_support():
    """
    测试并行化支持
    """
    import time
    import numpy as np
    from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
    from cardgame_ai.algorithms.random_agent import RandomAgent

    # 设置随机种子以确保可重现性
    np.random.seed(42)
    random.seed(42)

    # 测试并行自我对弈
    print("\n=== 测试并行自我对弈 ===")

    # 创建环境和代理
    env = DouDizhuEnvironment()
    agent = RandomAgent()

    # 创建并行自我对弈系统
    parallel_self_play = ParallelSelfPlay(
        save_path='experiences',
        max_workers=4,
        use_multiprocessing=False,
        batch_size=2
    )

    # 生成经验
    num_games = 10
    start_time = time.time()
    experiences = parallel_self_play.generate_experience(
        env=env,
        agent=agent,
        num_games=num_games,
        temperature=1.0,
        save=False,
        disable_tqdm=True
    )
    parallel_time = time.time() - start_time

    print(f"并行自我对弈时间: {parallel_time:.4f}s")
    print(f"生成的经验数量: {len(experiences)}")

    # 测试并行环境模拟
    print("\n=== 测试并行环境模拟 ===")

    # 创建并行环境
    parallel_env = ParallelEnvironment(
        env_class=DouDizhuEnvironment,
        num_envs=4,
        vectorized=True
    )

    # 重置环境
    states = parallel_env.reset()
    print(f"环境数量: {len(parallel_env.envs)}")
    print(f"状态数量: {len(states)}")

    # 执行动作
    actions = [agent.select_action(state) for state in states]
    next_states, rewards, dones, infos = parallel_env.step(actions)

    print(f"下一个状态数量: {len(next_states)}")
    print(f"奖励数量: {len(rewards)}")
    print(f"完成标志数量: {len(dones)}")
    print(f"信息数量: {len(infos)}")

    # 关闭环境
    parallel_env.close()

    # 测试并行MCTS搜索
    print("\n=== 测试并行MCTS搜索 ===")

    # 简化起见，这里不实际运行MCTS，因为需要实现完整的MuZero模型
    print("并行MCTS搜索需要完整的MuZero模型，这里略过实际测试")

    return {
        'parallel_self_play': parallel_self_play,
        'parallel_env': parallel_env
    }


if __name__ == "__main__":
    # 如果直接运行这个文件，则执行测试
    test_parallel_support()

# Ray Worker for 分布式经验生成
if ray is not None:
    @ray.remote
    class RemoteBatchWorker:
        def __init__(self, env: Environment, agent: Agent):
            self.env = env.clone() if hasattr(env, 'clone') else env
            self.agent = agent

        def run(self, num_games: int, temperature: float):
            experiences = []
            for _ in range(num_games):
                state = self.env.reset()
                done = False
                while not done:
                    action = self.agent.select_action(state, temperature=temperature)
                    next_state, reward, done, info = self.env.step(action)
                    experiences.append(Experience(state, action, reward, next_state, done, info))
                    state = next_state
            return experiences
