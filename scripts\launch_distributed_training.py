#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
多节点分布式训练启动脚本

用于在多个节点上启动分布式训练。
支持自动发现和连接Ray集群。
"""

import os
import sys
import argparse
import logging
import time
import ray
import socket
import json
from typing import Dict, List, Any, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def parse_args():
    """
    解析命令行参数
    
    Returns:
        argparse.Namespace: 命令行参数
    """
    parser = argparse.ArgumentParser(description='多节点分布式训练启动脚本')
    
    # Ray集群参数
    parser.add_argument('--head', action='store_true', help='是否为头节点')
    parser.add_argument('--head_address', type=str, default=None, help='头节点地址，格式为IP:PORT')
    parser.add_argument('--redis_password', type=str, default=None, help='Redis密码')
    parser.add_argument('--num_cpus', type=int, default=None, help='CPU数量')
    parser.add_argument('--num_gpus', type=int, default=None, help='GPU数量')
    
    # 训练参数
    parser.add_argument('--config_file', type=str, default=None, help='配置文件路径')
    parser.add_argument('--num_iterations', type=int, default=1000, help='训练迭代次数')
    parser.add_argument('--episodes_per_iteration', type=int, default=32, help='每次迭代收集的回合数')
    parser.add_argument('--train_steps_per_iteration', type=int, default=1, help='每次迭代的训练步数')
    parser.add_argument('--batch_size', type=int, default=256, help='批次大小')
    parser.add_argument('--save_interval', type=int, default=100, help='保存间隔（迭代次数）')
    
    # 分布式参数
    parser.add_argument('--num_actors', type=int, default=16, help='Actor数量')
    parser.add_argument('--num_learners', type=int, default=1, help='Learner数量')
    
    # 其他参数
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    parser.add_argument('--save_dir', type=str, default='models/distributed', help='保存目录')
    parser.add_argument('--resume', action='store_true', help='是否从检查点恢复训练')
    parser.add_argument('--model_path', type=str, default=None, help='模型路径，用于恢复训练')
    
    return parser.parse_args()


def load_config(config_file: str) -> Dict[str, Any]:
    """
    加载配置文件
    
    Args:
        config_file: 配置文件路径
        
    Returns:
        Dict[str, Any]: 配置字典
    """
    with open(config_file, 'r') as f:
        config = json.load(f)
    return config


def get_local_ip() -> str:
    """
    获取本机IP地址
    
    Returns:
        str: IP地址
    """
    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    try:
        # 不需要真正连接
        s.connect(('**************', 1))
        ip = s.getsockname()[0]
    except Exception:
        ip = '127.0.0.1'
    finally:
        s.close()
    return ip


def start_ray_head(redis_password: Optional[str] = None, num_cpus: Optional[int] = None, num_gpus: Optional[int] = None) -> str:
    """
    启动Ray头节点
    
    Args:
        redis_password: Redis密码
        num_cpus: CPU数量
        num_gpus: GPU数量
        
    Returns:
        str: 头节点地址
    """
    # 构建启动命令
    command = "ray start --head"
    
    if redis_password is not None:
        command += f" --redis-password={redis_password}"
        
    if num_cpus is not None:
        command += f" --num-cpus={num_cpus}"
        
    if num_gpus is not None:
        command += f" --num-gpus={num_gpus}"
        
    # 启动头节点
    logger.info(f"启动Ray头节点: {command}")
    os.system(command)
    
    # 获取头节点地址
    local_ip = get_local_ip()
    head_address = f"{local_ip}:6379"
    
    logger.info(f"Ray头节点已启动: {head_address}")
    
    return head_address


def start_ray_worker(head_address: str, redis_password: Optional[str] = None, num_cpus: Optional[int] = None, num_gpus: Optional[int] = None) -> None:
    """
    启动Ray工作节点
    
    Args:
        head_address: 头节点地址
        redis_password: Redis密码
        num_cpus: CPU数量
        num_gpus: GPU数量
    """
    # 构建启动命令
    command = f"ray start --address={head_address}"
    
    if redis_password is not None:
        command += f" --redis-password={redis_password}"
        
    if num_cpus is not None:
        command += f" --num-cpus={num_cpus}"
        
    if num_gpus is not None:
        command += f" --num-gpus={num_gpus}"
        
    # 启动工作节点
    logger.info(f"启动Ray工作节点: {command}")
    os.system(command)
    
    logger.info(f"Ray工作节点已连接到头节点: {head_address}")


def main():
    """
    主函数
    """
    # 解析命令行参数
    args = parse_args()
    
    # 加载配置文件
    config = {}
    if args.config_file is not None:
        config = load_config(args.config_file)
        
    # 启动Ray
    if args.head:
        # 启动头节点
        head_address = start_ray_head(args.redis_password, args.num_cpus, args.num_gpus)
        
        # 初始化Ray
        if not ray.is_initialized():
            if args.redis_password is not None:
                ray.init(address=head_address, _redis_password=args.redis_password)
            else:
                ray.init(address=head_address)
                
        # 等待集群准备就绪
        logger.info("等待Ray集群准备就绪...")
        time.sleep(10)
        
        # 启动训练
        from examples.large_scale_distributed_training import main as train_main
        train_main()
    else:
        # 启动工作节点
        if args.head_address is None:
            logger.error("必须指定头节点地址")
            sys.exit(1)
            
        start_ray_worker(args.head_address, args.redis_password, args.num_cpus, args.num_gpus)
        
        # 工作节点不需要执行训练代码
        logger.info("工作节点已启动，等待任务...")
        
        # 保持进程运行
        while True:
            time.sleep(60)


if __name__ == "__main__":
    main()
