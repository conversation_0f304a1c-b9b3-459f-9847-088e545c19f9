"""
MCTS主日志器模块

提供MCTS算法的核心日志记录功能，包括：
- UCB计算过程追踪
- 节点扩展详情记录
- 搜索路径完整追踪
- 模拟结果记录
- 性能统计监控
"""

import time
import logging
import threading
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
import uuid

from .config import LogConfig, DEFAULT_CONFIG
from .formatters import create_formatter, MCTSFormatter
from .performance_monitor import PerformanceMonitor
from .utils import generate_session_id, safe_serialize, PerformanceTimer


class MCTSLogger:
    """
    MCTS专用日志器
    
    提供结构化的MCTS搜索过程日志记录功能，支持：
    - 详细的UCB计算过程追踪
    - 节点扩展过程记录
    - 完整搜索路径追踪
    - 模拟结果和性能统计
    - 可配置的日志级别和输出格式
    - 异步日志写入和性能优化
    """
    
    def __init__(self, 
                 config: Optional[LogConfig] = None,
                 session_id: Optional[str] = None):
        """
        初始化MCTS日志器
        
        Args:
            config: 日志配置，如果为None则使用默认配置
            session_id: 会话ID，如果为None则自动生成
        """
        self.config = config or DEFAULT_CONFIG
        self.session_id = session_id or generate_session_id()
        
        # 验证配置
        if not self.config.validate():
            raise ValueError("日志配置验证失败")
        
        # 创建格式化器
        self.formatter = create_formatter(
            self.config.output_format,
            indent=2 if self.config.output_format == 'json' else None
        )
        
        # 创建性能监控器
        self.performance_monitor = PerformanceMonitor(
            enable_memory_monitoring=self.config.enable_performance_logging
        )
        
        # 设置日志器
        self.logger = self._setup_logger()
        
        # 异步日志缓冲
        self.log_buffer = []
        self.buffer_lock = threading.Lock()
        self.flush_timer = None
        
        # 性能计时器
        self.timers = {}
        
        # 统计计数器
        self.stats = {
            'ucb_logs': 0,
            'expansion_logs': 0,
            'path_logs': 0,
            'simulation_logs': 0,
            'performance_logs': 0
        }
        
        # 启动异步日志处理
        if self.config.async_logging:
            self._start_async_logging()
    
    def _setup_logger(self) -> logging.Logger:
        """
        设置Python日志器
        
        Returns:
            logging.Logger: 配置好的日志器
        """
        logger = logging.getLogger(f"mcts_logger_{self.session_id}")
        logger.setLevel(getattr(logging, self.config.level.upper()))
        
        # 清除现有处理器
        logger.handlers.clear()
        
        # 添加文件处理器
        if self.config.log_to_file:
            log_path = Path(self.config.log_file_path)
            log_path.parent.mkdir(parents=True, exist_ok=True)
            
            file_handler = logging.FileHandler(log_path, encoding='utf-8')
            file_handler.setLevel(getattr(logging, self.config.level.upper()))
            logger.addHandler(file_handler)
        
        # 添加控制台处理器
        if self.config.log_to_console:
            console_handler = logging.StreamHandler()
            console_handler.setLevel(getattr(logging, self.config.level.upper()))
            logger.addHandler(console_handler)
        
        return logger
    
    def _start_async_logging(self) -> None:
        """启动异步日志处理"""
        if self.flush_timer:
            self.flush_timer.cancel()
        
        self.flush_timer = threading.Timer(
            self.config.flush_interval,
            self._flush_log_buffer
        )
        self.flush_timer.daemon = True
        self.flush_timer.start()
    
    def _flush_log_buffer(self) -> None:
        """刷新日志缓冲区"""
        with self.buffer_lock:
            if self.log_buffer:
                for log_entry in self.log_buffer:
                    self.logger.debug(log_entry)
                self.log_buffer.clear()
        
        # 重新启动定时器
        if self.config.async_logging:
            self._start_async_logging()
    
    def _log_message(self, message: str) -> None:
        """
        记录日志消息
        
        Args:
            message: 日志消息
        """
        if not self.config.enabled:
            return
        
        if self.config.async_logging:
            with self.buffer_lock:
                self.log_buffer.append(message)
                
                # 如果缓冲区满了，立即刷新
                if len(self.log_buffer) >= self.config.buffer_size:
                    self._flush_log_buffer()
        else:
            self.logger.debug(message)
    
    def log_ucb_calculation(self,
                           parent_node: Any,
                           children_scores: List[Dict[str, Any]],
                           selected_action: int,
                           game_context: Optional[Dict[str, Any]] = None,
                           timing_id: Optional[str] = None) -> None:
        """
        记录UCB计算过程
        
        Args:
            parent_node: 父节点对象
            children_scores: 子节点UCB分数列表
            selected_action: 最终选择的动作
            game_context: 游戏上下文信息
            timing_id: 计时ID，用于性能监控
        """
        if not self.config.enable_ucb_logging:
            return
        
        # 结束计时
        ucb_time = 0.0
        if timing_id and timing_id in self.timers:
            ucb_time = self.timers[timing_id].stop()
            self.performance_monitor.end_ucb_timing(timing_id)
            del self.timers[timing_id]
        
        # 过滤和限制子节点数据
        filtered_children = children_scores[:self.config.max_children_logged]
        
        # 如果只记录最佳动作
        if self.config.log_only_best_actions:
            filtered_children = [
                child for child in filtered_children 
                if child.get('action') == selected_action
            ]
        
        log_data = {
            'parent_visits': getattr(parent_node, 'visit_count', 0),
            'children_count': len(children_scores),
            'children_scores': safe_serialize(filtered_children),
            'selected_action': selected_action,
            'ucb_calculation_time': ucb_time,
            'game_context': safe_serialize(game_context) if game_context else {}
        }
        
        # 添加游戏状态信息
        if self.config.include_game_state and hasattr(parent_node, 'state'):
            log_data['game_state'] = safe_serialize(parent_node.state)
        
        message = self.formatter.format_ucb_log(
            time.time(), 
            self.session_id, 
            log_data
        )
        
        self._log_message(message)
        self.stats['ucb_logs'] += 1
    
    def log_node_expansion(self,
                          node: Any,
                          policy_output: Dict[str, Any],
                          expansion_time: float,
                          num_children: int,
                          timing_id: Optional[str] = None) -> None:
        """
        记录节点扩展过程
        
        Args:
            node: 被扩展的节点
            policy_output: 策略网络输出
            expansion_time: 扩展耗时
            num_children: 新增子节点数量
            timing_id: 计时ID
        """
        if not self.config.enable_expansion_logging:
            return
        
        # 结束计时
        if timing_id:
            self.performance_monitor.end_expansion_timing(timing_id, num_children)
        
        log_data = {
            'node_visits_before': getattr(node, 'visit_count', 0),
            'policy_output': safe_serialize(policy_output),
            'num_children_added': num_children,
            'expansion_time': expansion_time,
            'memory_usage': self.performance_monitor.get_current_metrics().current_memory_usage
        }
        
        # 添加节点状态信息
        if self.config.include_game_state and hasattr(node, 'state'):
            log_data['node_state'] = safe_serialize(node.state)
        
        message = self.formatter.format_expansion_log(
            time.time(),
            self.session_id,
            log_data
        )
        
        self._log_message(message)
        self.stats['expansion_logs'] += 1
    
    def log_search_path(self,
                       path: List[Any],
                       path_value: float,
                       depth: int) -> None:
        """
        记录搜索路径
        
        Args:
            path: 搜索路径节点列表
            path_value: 路径价值
            depth: 路径深度
        """
        if not self.config.enable_path_logging:
            return
        
        # 限制路径深度
        limited_path = path[:self.config.max_path_depth_logged]
        
        # 记录树深度
        self.performance_monitor.record_tree_depth(depth)
        
        log_data = {
            'path_length': len(path),
            'path_value': path_value,
            'depth': depth,
            'path_nodes': []
        }
        
        # 记录路径中的关键节点信息
        for i, node in enumerate(limited_path):
            node_info = {
                'step': i,
                'visits': getattr(node, 'visit_count', 0),
                'value': getattr(node, 'value', 0.0)
            }
            
            # 添加状态信息（如果配置允许）
            if self.config.include_game_state and hasattr(node, 'state'):
                node_info['state'] = safe_serialize(node.state)
            
            log_data['path_nodes'].append(node_info)
        
        message = self.formatter.format_path_log(
            time.time(),
            self.session_id,
            log_data
        )
        
        self._log_message(message)
        self.stats['path_logs'] += 1
    
    def log_simulation_result(self,
                             start_state: Any,
                             result_value: float,
                             simulation_time: float,
                             simulation_steps: int) -> None:
        """
        记录模拟结果
        
        Args:
            start_state: 模拟起始状态
            result_value: 模拟结果价值
            simulation_time: 模拟耗时
            simulation_steps: 模拟步数
        """
        if not self.config.enable_simulation_logging:
            return
        
        log_data = {
            'result_value': result_value,
            'simulation_time': simulation_time,
            'simulation_steps': simulation_steps,
            'start_state': safe_serialize(start_state) if self.config.include_game_state else None
        }
        
        message = self.formatter.format_simulation_log(
            time.time(),
            self.session_id,
            log_data
        )
        
        self._log_message(message)
        self.stats['simulation_logs'] += 1
    
    def log_performance_stats(self, additional_stats: Optional[Dict[str, Any]] = None) -> None:
        """
        记录性能统计
        
        Args:
            additional_stats: 额外的统计信息
        """
        if not self.config.enable_performance_logging:
            return
        
        # 获取性能报告
        performance_report = self.performance_monitor.generate_performance_report()
        
        log_data = {
            'performance_report': performance_report,
            'logging_stats': self.stats.copy(),
            'session_id': self.session_id
        }
        
        if additional_stats:
            log_data['additional_stats'] = safe_serialize(additional_stats)
        
        message = self.formatter.format_performance_log(
            time.time(),
            self.session_id,
            log_data
        )
        
        self._log_message(message)
        self.stats['performance_logs'] += 1
    
    def start_search_timing(self, search_id: Optional[str] = None) -> str:
        """
        开始搜索计时
        
        Args:
            search_id: 搜索ID，如果为None则自动生成
            
        Returns:
            str: 搜索ID
        """
        if search_id is None:
            search_id = str(uuid.uuid4())
        
        self.performance_monitor.start_search_timing(search_id)
        return search_id
    
    def end_search_timing(self, search_id: str, num_simulations: int) -> float:
        """
        结束搜索计时
        
        Args:
            search_id: 搜索ID
            num_simulations: 模拟次数
            
        Returns:
            float: 搜索耗时
        """
        return self.performance_monitor.end_search_timing(search_id, num_simulations)
    
    def start_ucb_timing(self, ucb_id: Optional[str] = None) -> str:
        """
        开始UCB计算计时
        
        Args:
            ucb_id: UCB计算ID
            
        Returns:
            str: UCB计算ID
        """
        if ucb_id is None:
            ucb_id = str(uuid.uuid4())
        
        self.timers[ucb_id] = PerformanceTimer()
        self.timers[ucb_id].start()
        self.performance_monitor.start_ucb_timing(ucb_id)
        return ucb_id
    
    def start_expansion_timing(self, expansion_id: Optional[str] = None) -> str:
        """
        开始节点扩展计时
        
        Args:
            expansion_id: 扩展ID
            
        Returns:
            str: 扩展ID
        """
        if expansion_id is None:
            expansion_id = str(uuid.uuid4())
        
        self.performance_monitor.start_expansion_timing(expansion_id)
        return expansion_id
    
    def get_session_stats(self) -> Dict[str, Any]:
        """
        获取会话统计信息
        
        Returns:
            Dict[str, Any]: 会话统计
        """
        return {
            'session_id': self.session_id,
            'config': self.config.to_dict(),
            'logging_stats': self.stats.copy(),
            'performance_metrics': self.performance_monitor.get_current_metrics().to_dict()
        }
    
    def close(self) -> None:
        """关闭日志器，清理资源"""
        # 刷新缓冲区
        self._flush_log_buffer()
        
        # 停止定时器
        if self.flush_timer:
            self.flush_timer.cancel()
        
        # 记录最终性能统计
        self.log_performance_stats({'session_closed': True})
        
        # 最终刷新
        self._flush_log_buffer()
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()
