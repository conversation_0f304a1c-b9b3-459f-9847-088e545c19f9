#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCTS性能分析报告生成脚本
读取mcts_profile.py生成的性能数据，计算热点函数、内存使用趋势，并生成Markdown格式报告和可视化图表
"""
import os
import pstats
import json
import matplotlib.pyplot as plt
from pathlib import Path


def parse_memory_log(log_path):
    """
    解析memory_profiler日志，提取内存使用数据
    返回列表：如时间点序号和内存使用（MiB）
    """
    times = []
    usages = []
    if not os.path.exists(log_path):
        return times, usages
    with open(log_path, 'r') as f:
        for line in f:
            # 过滤包含MiB且不为表头的行
            if 'MiB' in line and line.strip().startswith('Memory') is False and line.strip().startswith('Filename') is False:
                parts = line.split()
                try:
                    mi_index = parts.index('MiB')
                    usage = float(parts[mi_index-1])
                    usages.append(usage)
                    times.append(len(times))
                except ValueError:
                    continue
    return times, usages


def generate_cpu_pie(pstats_path, output_dir):
    """
    使用cProfile数据生成CPU时间占比饼图，保存为cpu_time_pie.png
    返回文件名
    """
    stats = pstats.Stats(pstats_path)
    stats.strip_dirs().sort_stats('cumtime')
    # 获取前10个函数及其累计时间
    items = [(func, stat[3]) for func, stat in stats.stats.items()]
    # stat tuple: (cc, nc, tt, ct, callers)
    items.sort(key=lambda x: x[1], reverse=True)
    top10 = items[:10]
    labels = [f"{func[2]} ({ct:.3f}s)" for func, ct in top10]
    sizes = [ct for _, ct in top10]
    plt.figure(figsize=(8, 8))
    plt.pie(sizes, labels=labels, autopct='%1.1f%%')
    plt.title('CPU函数累计时间占比（Top 10）')
    cpu_img = output_dir / 'cpu_time_pie.png'
    plt.savefig(cpu_img)
    plt.close()
    return cpu_img.name


def generate_memory_plot(times, usages, output_dir):
    """
    使用内存日志数据生成折线图，保存为memory_usage.png
    返回文件名
    """
    if not times or not usages:
        return None
    plt.figure()
    plt.plot(times, usages, marker='o')
    plt.xlabel('样本序号')
    plt.ylabel('内存使用 (MiB)')
    plt.title('内存使用趋势')
    mem_img = output_dir / 'memory_usage.png'
    plt.savefig(mem_img)
    plt.close()
    return mem_img.name


def generate_report():
    # 定位文件路径
    script_dir = Path(__file__).parent
    pstats_file = script_dir / 'mcts_cpu.pstats'
    mem_log = script_dir / 'mcts_profile.py.log'
    gpu_trace = script_dir / 'mcts_gpu_trace.json'
    # 创建输出目录
    report_dir = script_dir / 'report'
    report_dir.mkdir(exist_ok=True)

    # 生成图表
    cpu_img = generate_cpu_pie(pstats_file, report_dir)
    times, usages = parse_memory_log(mem_log)
    mem_img = generate_memory_plot(times, usages, report_dir)

    # 读取GPU trace
    gpu_section = ''
    if gpu_trace.exists():
        gpu_section = f"* GPU 跟踪文件：{gpu_trace.name}，可在 Chrome Trace  中查看。"

    # 编写 Markdown 报告
    report_md = report_dir / 'mcts_performance_report.md'
    with open(report_md, 'w', encoding='utf-8') as f:
        f.write('# MCTS 性能分析报告\n')
        f.write('\n')
        f.write('## CPU 剖析（cProfile）\n')
        if cpu_img:
            f.write(f'![CPU 时间占比]({cpu_img})\n')
        f.write('\n')
        f.write('## 内存剖析（memory_profiler）\n')
        if mem_img:
            f.write(f'![内存使用趋势]({mem_img})\n')
        f.write('\n')
        f.write('## GPU 剖析\n')
        f.write(gpu_section + '\n')

    print(f'性能分析报告生成完成，目录：{report_dir}')


if __name__ == '__main__':
    generate_report() 