"""
Transformer策略评估脚本

评估基于Transformer架构的强化学习代理，并与基线策略进行对比。
"""
import os
import sys
import json
import time
import logging
import argparse
import numpy as np
import torch
from typing import Dict, List, Any, Optional

from cardgame_ai.core.agent import Agent
from cardgame_ai.algorithms.transformer_policy import TransformerPolicy
from cardgame_ai.algorithms.rule_based import RuleBasedAgent
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.evaluation.evaluator import ComprehensiveEvaluator
from cardgame_ai.utils.logger import setup_logger
from cardgame_ai.utils.visualization import plot_comparison

# 设置日志
logger = setup_logger("transformer_evaluation", level=logging.INFO)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="Transformer策略评估脚本")

    # 模型参数
    parser.add_argument("--model_path", type=str, required=True, help="Transformer模型路径")
    parser.add_argument("--config", type=str, default="config/transformer_policy_config.json",
                        help="配置文件路径")

    # 评估参数
    parser.add_argument("--num_games", type=int, default=100, help="评估游戏局数")
    parser.add_argument("--save_dir", type=str, default="results/transformer_eval",
                        help="结果保存目录")
    parser.add_argument("--visualize", action="store_true", help="是否可视化结果")

    # 基线模型
    parser.add_argument("--baseline_models", type=str, nargs="+",
                        default=["rule_based", "random"],
                        help="基线模型列表")

    # 其他参数
    parser.add_argument("--seed", type=int, default=42, help="随机种子")
    parser.add_argument("--device", type=str, default="auto", help="设备: cpu, cuda, auto")

    return parser.parse_args()


def load_config(config_path):
    """加载配置"""
    if os.path.exists(config_path):
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        logger.info(f"已加载配置文件: {config_path}")
        return config
    else:
        logger.warning(f"配置文件不存在: {config_path}，使用默认配置")
        return {
            "policy": {
                "type": "transformer",
                "hidden_dim": 256,
                "num_heads": 4,
                "num_layers": 4,
                "ff_dim": 512,
                "seq_len": 100
            },
            "evaluation": {
                "metrics": ["win_rate", "avg_reward", "avg_game_length"],
                "visualize": True,
                "save_games": True
            }
        }


def load_transformer_policy(model_path, config, state_shape, action_shape, device):
    """加载Transformer策略"""
    policy_config = config["policy"]

    # 创建Transformer策略
    policy = TransformerPolicy(
        state_shape=state_shape,
        action_shape=action_shape,
        hidden_dim=policy_config.get("hidden_dim", 256),
        num_heads=policy_config.get("num_heads", 4),
        num_layers=policy_config.get("num_layers", 4),
        ff_dim=policy_config.get("ff_dim", 512),
        device=device
    )

    # 加载模型
    if os.path.exists(model_path):
        policy.load(model_path)
        logger.info(f"已加载模型: {model_path}")
    else:
        logger.error(f"模型文件不存在: {model_path}")
        raise FileNotFoundError(f"模型文件不存在: {model_path}")

    return policy


def create_baseline_agent(baseline_type, env):
    """创建基线代理"""
    if baseline_type == "rule_based":
        # 创建基于规则的代理
        algorithm = RuleBasedAgent()
        return Agent(algorithm=algorithm, name="rule_based")
    elif baseline_type == "random":
        # 创建随机代理
        class RandomAgent:
            def act(self, state, legal_actions):
                return np.random.choice(legal_actions) if legal_actions else None
        return Agent(algorithm=RandomAgent(), name="random")
    else:
        logger.warning(f"不支持的基线类型: {baseline_type}，使用随机代理")
        class RandomAgent:
            def act(self, state, legal_actions):
                return np.random.choice(legal_actions) if legal_actions else None
        return Agent(algorithm=RandomAgent(), name="random")


def evaluate_against_baseline(env, transformer_agent, baseline_agent, num_games=100):
    """与基线代理对比评估"""
    # 统计信息
    transformer_wins = 0
    baseline_wins = 0
    draws = 0
    total_rewards = 0
    game_lengths = []

    for game in range(num_games):
        # 重置环境
        state = env.reset()
        done = False
        steps = 0

        # 游戏循环
        while not done:
            # 获取当前玩家
            current_player = state.get_player_id()

            # 获取合法动作
            legal_actions = env.get_legal_actions(state)

            # 选择动作
            if current_player == 0:  # 假设0是我们的代理
                action = transformer_agent.act(state, legal_actions)
            else:  # 其他玩家使用基线代理
                action = baseline_agent.act(state, legal_actions)

            # 执行动作
            next_state, reward, done, info = env.step(action)

            # 更新状态
            state = next_state
            steps += 1

        # 游戏结束，更新统计信息
        game_lengths.append(steps)

        # 获取游戏结果
        if hasattr(env, 'get_winner'):
            winner = env.get_winner()
            if winner == 0:  # Transformer代理获胜
                transformer_wins += 1
                total_rewards += 1
            elif winner == -1:  # 平局
                draws += 1
            else:  # 基线代理获胜
                baseline_wins += 1
                total_rewards -= 1

    # 计算统计指标
    win_rate = transformer_wins / num_games
    avg_reward = total_rewards / num_games
    avg_game_length = sum(game_lengths) / len(game_lengths) if game_lengths else 0

    # 返回评估结果
    return {
        "win_rate": win_rate,
        "avg_reward": avg_reward,
        "avg_game_length": avg_game_length,
        "transformer_wins": transformer_wins,
        "baseline_wins": baseline_wins,
        "draws": draws,
        "num_games": num_games
    }


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()

    # 设置随机种子
    if args.seed is not None:
        np.random.seed(args.seed)
        torch.manual_seed(args.seed)
        torch.cuda.manual_seed_all(args.seed)

    # 设置设备
    if args.device == "auto":
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    else:
        device = torch.device(args.device)
    logger.info(f"使用设备: {device}")

    # 加载配置
    config = load_config(args.config)

    # 创建环境
    env = DouDizhuEnvironment()

    # 获取状态和动作空间
    state_shape = env.observation_space.shape
    action_shape = env.action_space.shape

    # 加载Transformer策略
    transformer_policy = load_transformer_policy(args.model_path, config, state_shape, action_shape, device)
    transformer_agent = Agent(algorithm=transformer_policy, name="transformer")

    # 创建评估器
    evaluator = ComprehensiveEvaluator(save_path=args.save_dir)

    # 评估结果
    results = {}

    # 与基线模型对比评估
    logger.info("开始与基线模型对比评估...")
    for baseline_type in args.baseline_models:
        logger.info(f"评估与 {baseline_type} 的对比...")

        # 创建基线代理
        baseline_agent = create_baseline_agent(baseline_type, env)

        # 评估
        eval_result = evaluate_against_baseline(
            env, transformer_agent, baseline_agent, args.num_games
        )

        # 打印结果
        logger.info(f"与 {baseline_type} 对比结果:")
        logger.info(f"  胜率: {eval_result['win_rate']:.4f}")
        logger.info(f"  平均奖励: {eval_result['avg_reward']:.4f}")
        logger.info(f"  平均游戏长度: {eval_result['avg_game_length']:.2f}")
        logger.info(f"  Transformer胜场: {eval_result['transformer_wins']}")
        logger.info(f"  {baseline_type}胜场: {eval_result['baseline_wins']}")
        logger.info(f"  平局: {eval_result['draws']}")

        # 保存结果
        results[baseline_type] = eval_result

    # 保存评估结果
    os.makedirs(args.save_dir, exist_ok=True)
    result_path = os.path.join(args.save_dir, "evaluation_results.json")
    with open(result_path, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2)
    logger.info(f"评估结果已保存到: {result_path}")

    # 可视化结果
    if args.visualize:
        # 绘制胜率对比图
        win_rates = {model: results[model]["win_rate"] for model in results}
        plot_comparison(
            win_rates,
            title="Transformer vs Baselines - Win Rate",
            ylabel="Win Rate",
            save_path=os.path.join(args.save_dir, "win_rate_comparison.png")
        )
        logger.info("已生成胜率对比图")

    return 0


if __name__ == "__main__":
    sys.exit(main())