"""
组件管理器模块

提供统一的组件管理机制，负责组件的创建、初始化和管理，
实现组件依赖注入，减少组件间耦合，提高系统灵活性和可扩展性。
"""

import os
import time
import logging
import threading
from typing import Dict, Any, Callable, Optional, Type, TypeVar, Generic

# 设置日志
logger = logging.getLogger(__name__)

# 定义组件类型变量
T = TypeVar('T')


class ComponentManager:
    """
    组件管理器类
    
    实现单例模式，负责管理系统中的所有组件，
    提供组件注册、获取和依赖注入功能。
    """
    
    _instance = None
    _lock = threading.Lock()
    
    @classmethod
    def get_instance(cls):
        """
        获取组件管理器实例
        
        使用双重检查锁定模式实现线程安全的单例
        
        Returns:
            ComponentManager: 组件管理器实例
        """
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = ComponentManager()
        return cls._instance
    
    def __init__(self):
        """初始化组件管理器"""
        self.components = {}  # 组件注册表
        self.factories = {}   # 组件工厂
        self.dependencies = {}  # 组件依赖关系
        self.config = {}      # 组件配置
        
    def register_component(self, component_type: str, component: Any) -> None:
        """
        注册组件
        
        Args:
            component_type: 组件类型
            component: 组件实例
        """
        self.components[component_type] = component
        logger.debug(f"已注册组件: {component_type}")
        
    def register_factory(self, component_type: str, factory_func: Callable[..., Any]) -> None:
        """
        注册组件工厂
        
        Args:
            component_type: 组件类型
            factory_func: 工厂函数，用于创建组件
        """
        self.factories[component_type] = factory_func
        logger.debug(f"已注册组件工厂: {component_type}")
        
    def register_dependency(self, component_type: str, dependencies: Dict[str, str]) -> None:
        """
        注册组件依赖
        
        Args:
            component_type: 组件类型
            dependencies: 依赖字典，键为参数名，值为依赖的组件类型
        """
        self.dependencies[component_type] = dependencies
        logger.debug(f"已注册组件依赖: {component_type} -> {dependencies}")
        
    def set_config(self, component_type: str, config: Dict[str, Any]) -> None:
        """
        设置组件配置
        
        Args:
            component_type: 组件类型
            config: 配置字典
        """
        self.config[component_type] = config
        logger.debug(f"已设置组件配置: {component_type}")
        
    def get_component(self, component_type: str, **kwargs) -> Any:
        """
        获取组件
        
        如果组件已存在，直接返回；否则，使用工厂创建
        
        Args:
            component_type: 组件类型
            **kwargs: 创建组件的额外参数
            
        Returns:
            组件实例
            
        Raises:
            ValueError: 如果组件类型未知
        """
        # 如果组件已存在，直接返回
        if component_type in self.components:
            return self.components[component_type]
        
        # 否则，使用工厂创建
        if component_type in self.factories:
            # 处理依赖
            if component_type in self.dependencies:
                for param_name, dep_type in self.dependencies[component_type].items():
                    if param_name not in kwargs:
                        kwargs[param_name] = self.get_component(dep_type)
            
            # 添加配置
            if component_type in self.config:
                for key, value in self.config[component_type].items():
                    if key not in kwargs:
                        kwargs[key] = value
            
            # 创建组件
            component = self.factories[component_type](**kwargs)
            self.components[component_type] = component
            logger.info(f"已创建组件: {component_type}")
            return component
        
        raise ValueError(f"未知的组件类型: {component_type}")
    
    def get_or_create_component(self, component_type: str, **kwargs) -> Any:
        """
        获取或创建组件
        
        与get_component相同，但更明确表达意图
        
        Args:
            component_type: 组件类型
            **kwargs: 创建组件的额外参数
            
        Returns:
            组件实例
        """
        return self.get_component(component_type, **kwargs)
    
    def has_component(self, component_type: str) -> bool:
        """
        检查组件是否存在
        
        Args:
            component_type: 组件类型
            
        Returns:
            bool: 组件是否存在
        """
        return component_type in self.components
    
    def remove_component(self, component_type: str) -> None:
        """
        移除组件
        
        Args:
            component_type: 组件类型
        """
        if component_type in self.components:
            del self.components[component_type]
            logger.debug(f"已移除组件: {component_type}")
    
    def clear_components(self) -> None:
        """清除所有组件"""
        self.components.clear()
        logger.debug("已清除所有组件")


# 创建全局组件管理器实例
component_manager = ComponentManager.get_instance()
