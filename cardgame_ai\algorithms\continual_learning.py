"""
连续学习模块

提供防止灾难性遗忘的技术，如弹性权重固化 (EWC) 和 L2 正则化，
确保模型在适应新数据的同时，不会丢失在旧数据上学到的关键知识。
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import logging
import os
import json
from typing import Dict, Any, Optional, List, Tuple, Union, Callable

# 配置日志
logger = logging.getLogger(__name__)

class EWC:
    """
    弹性权重固化 (Elastic Weight Consolidation)

    通过计算Fisher信息矩阵来确定参数的重要性，并在训练新任务时
    对重要参数的变化施加惩罚，防止灾难性遗忘。
    """

    def __init__(
        self,
        model: nn.Mo<PERSON><PERSON>,
        dataloader: Any,
        fisher_importance: float = 1000.0,
        fisher_estimation_samples: int = 200,
        device: Optional[torch.device] = None
    ):
        """
        初始化EWC

        Args:
            model: 要保护的模型
            dataloader: 旧任务数据加载器
            fisher_importance: Fisher重要性系数，控制惩罚强度
            fisher_estimation_samples: 用于估计Fisher矩阵的样本数量
            device: 计算设备
        """
        self.model = model
        self.dataloader = dataloader
        self.fisher_importance = fisher_importance
        self.fisher_estimation_samples = fisher_estimation_samples
        self.device = device if device is not None else torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        # 存储旧任务的最优参数
        self.optimal_params = {n: p.clone().detach() for n, p in model.named_parameters() if p.requires_grad}

        # 计算Fisher信息矩阵
        self.fisher_matrix = self._calculate_fisher()

        logger.info(f"EWC初始化完成，Fisher重要性系数: {fisher_importance}")
        logger.info(f"参数数量: {len(self.optimal_params)}")
        logger.info(f"使用设备: {self.device}")

    def _calculate_fisher(self) -> Dict[str, torch.Tensor]:
        """
        计算Fisher信息矩阵

        Returns:
            Dict[str, torch.Tensor]: 参数名到Fisher矩阵的映射
        """
        logger.info("开始计算Fisher信息矩阵...")

        # 初始化Fisher矩阵
        fisher = {n: torch.zeros_like(p, device=self.device)
                 for n, p in self.model.named_parameters() if p.requires_grad}

        # 设置模型为评估模式
        self.model.eval()

        # 样本计数器
        sample_count = 0

        # 遍历数据集
        for batch in self.dataloader:
            # 限制样本数量
            if sample_count >= self.fisher_estimation_samples:
                break

            try:
                # 处理不同格式的批次数据
                if isinstance(batch, dict):
                    # 字典格式的批次
                    observations = batch.get('observations')
                    if observations is None:
                        continue

                    # 转换为张量
                    if not isinstance(observations, torch.Tensor):
                        observations = torch.FloatTensor(observations).to(self.device)
                else:
                    # 假设batch是可迭代的Experience对象集合
                    observations = torch.FloatTensor([exp.state for exp in batch]).to(self.device)

                # 前向传播
                self.model.zero_grad()

                # 获取隐藏状态
                hidden_states = self.model.representation_network(observations)

                # 预测策略和价值
                policy_logits, _ = self.model.prediction_network(hidden_states)

                # 计算对数似然（这里使用策略输出的对数概率）
                log_probs = F.log_softmax(policy_logits, dim=1)

                # 对每个样本的对数似然求和
                sample_log_likelihood = log_probs.sum()

                # 反向传播
                sample_log_likelihood.backward()

                # 更新Fisher矩阵
                for name, param in self.model.named_parameters():
                    if param.grad is not None and param.requires_grad:
                        fisher[name] += param.grad.data.pow(2)

                # 更新样本计数
                sample_count += observations.size(0)
            except Exception as e:
                logger.warning(f"计算Fisher矩阵时出错: {e}")
                continue

        # 如果没有处理任何样本，发出警告
        if sample_count == 0:
            logger.warning("没有处理任何样本来计算Fisher矩阵，使用单位矩阵")
            return {n: torch.ones_like(p, device=self.device) for n, p in self.model.named_parameters() if p.requires_grad}

        # 对Fisher矩阵进行归一化
        for name in fisher:
            fisher[name] = fisher[name] / sample_count

        logger.info(f"Fisher信息矩阵计算完成，使用了{sample_count}个样本")

        return fisher

    def penalty(self, model: nn.Module) -> torch.Tensor:
        """
        计算EWC惩罚项

        Args:
            model: 当前模型

        Returns:
            torch.Tensor: EWC惩罚项
        """
        loss = 0
        for name, param in model.named_parameters():
            if name in self.fisher_matrix and name in self.optimal_params and param.requires_grad:
                # 计算参数变化的平方
                delta = (param - self.optimal_params[name].to(param.device)).pow(2)
                # 加权求和
                loss += (self.fisher_matrix[name].to(param.device) * delta).sum()

        # 应用重要性系数
        return self.fisher_importance * loss

    def update_model(self, model: nn.Module) -> None:
        """
        更新EWC的模型参数和Fisher矩阵

        在新任务训练完成后，可以调用此方法更新EWC的模型参数和Fisher矩阵，
        使其适应新的任务，同时保留对旧任务的记忆。

        Args:
            model: 新的模型
        """
        # 更新最优参数
        self.optimal_params = {n: p.clone().detach() for n, p in model.named_parameters() if p.requires_grad}

        # 重新计算Fisher矩阵
        self.fisher_matrix = self._calculate_fisher()

        logger.info("EWC模型参数和Fisher矩阵已更新")

    def save_state(self, save_path: str) -> None:
        """
        保存EWC状态到文件

        保存Fisher矩阵和最优参数，以便以后恢复EWC状态。

        Args:
            save_path: 保存路径，不包括文件扩展名
        """
        # 创建目录（如果不存在）
        os.makedirs(os.path.dirname(save_path), exist_ok=True)

        # 保存Fisher矩阵
        fisher_save_path = f"{save_path}_fisher.pt"
        fisher_dict = {name: tensor.cpu() for name, tensor in self.fisher_matrix.items()}
        torch.save(fisher_dict, fisher_save_path)
        
        # 保存最优参数
        params_save_path = f"{save_path}_params.pt"
        params_dict = {name: tensor.cpu() for name, tensor in self.optimal_params.items()}
        torch.save(params_dict, params_save_path)
        
        # 保存配置信息
        config_save_path = f"{save_path}_config.json"
        config = {
            "fisher_importance": self.fisher_importance,
            "fisher_estimation_samples": self.fisher_estimation_samples
        }
        with open(config_save_path, 'w') as f:
            json.dump(config, f)
        
        logger.info(f"EWC状态已保存到 {save_path}")

    def load_state(self, load_path: str, model: Optional[nn.Module] = None) -> bool:
        """
        从文件加载EWC状态

        恢复Fisher矩阵和最优参数。

        Args:
            load_path: 加载路径，不包括文件扩展名
            model: 可选的新模型，如果提供，则更新EWC的模型引用

        Returns:
            bool: 是否成功加载
        """
        try:
            # 加载Fisher矩阵
            fisher_load_path = f"{load_path}_fisher.pt"
            fisher_dict = torch.load(fisher_load_path, map_location=self.device)
            self.fisher_matrix = fisher_dict
            
            # 加载最优参数
            params_load_path = f"{load_path}_params.pt"
            params_dict = torch.load(params_load_path, map_location=self.device)
            self.optimal_params = params_dict
            
            # 加载配置信息
            config_load_path = f"{load_path}_config.json"
            with open(config_load_path, 'r') as f:
                config = json.load(f)
            
            self.fisher_importance = config.get("fisher_importance", self.fisher_importance)
            self.fisher_estimation_samples = config.get("fisher_estimation_samples", self.fisher_estimation_samples)
            
            # 更新模型（如果提供）
            if model is not None:
                self.model = model
            
            logger.info(f"EWC状态已从 {load_path} 加载")
            return True
            
        except Exception as e:
            logger.error(f"加载EWC状态时出错: {e}")
            return False

    def update_importance(self, new_fisher_matrix: Dict[str, torch.Tensor], 
                          weight: float = 0.5) -> None:
        """
        更新参数重要性

        通过合并新的Fisher矩阵来更新参数重要性，可以指定权重来控制新旧Fisher矩阵的影响。

        Args:
            new_fisher_matrix: 新的Fisher矩阵
            weight: 新Fisher矩阵的权重 (0-1)，0表示完全使用旧矩阵，1表示完全使用新矩阵
        """
        # 验证权重范围
        weight = max(0.0, min(1.0, weight))
        
        # 合并Fisher矩阵
        for name in self.fisher_matrix:
            if name in new_fisher_matrix:
                # 确保张量在同一设备上
                new_tensor = new_fisher_matrix[name].to(self.fisher_matrix[name].device)
                # 加权平均
                self.fisher_matrix[name] = (1 - weight) * self.fisher_matrix[name] + weight * new_tensor
        
        logger.info(f"参数重要性已更新，新Fisher矩阵权重: {weight}")


class L2Regularization:
    """
    L2正则化

    通过对模型参数的L2范数施加惩罚，防止过拟合和灾难性遗忘。
    """

    def __init__(self, model: nn.Module, weight_decay: float = 0.01):
        """
        初始化L2正则化

        Args:
            model: 要保护的模型
            weight_decay: 权重衰减系数
        """
        self.model = model
        self.weight_decay = weight_decay

        # 存储初始参数
        self.initial_params = {n: p.clone().detach() for n, p in model.named_parameters() if p.requires_grad}

        logger.info(f"L2正则化初始化完成，权重衰减系数: {weight_decay}")

    def penalty(self, model: nn.Module) -> torch.Tensor:
        """
        计算L2正则化惩罚项

        Args:
            model: 当前模型

        Returns:
            torch.Tensor: L2正则化惩罚项
        """
        # 计算L2范数
        l2_norm = 0
        for name, param in model.named_parameters():
            if param.requires_grad:
                l2_norm += param.pow(2).sum()

        return self.weight_decay * l2_norm

    def penalty_with_reference(self, model: nn.Module) -> torch.Tensor:
        """
        计算相对于初始参数的L2正则化惩罚项

        Args:
            model: 当前模型

        Returns:
            torch.Tensor: L2正则化惩罚项
        """
        # 计算相对于初始参数的L2范数
        l2_norm = 0
        for name, param in model.named_parameters():
            if name in self.initial_params and param.requires_grad:
                delta = param - self.initial_params[name].to(param.device)
                l2_norm += delta.pow(2).sum()

        return self.weight_decay * l2_norm
