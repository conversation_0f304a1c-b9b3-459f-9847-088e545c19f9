"""
分阶段训练策略模块

实现分阶段训练策略，包括基础训练、角色专一化训练、农民协作训练、对抗训练和微调验证五个阶段，
确保模型能够逐步提高性能。
"""
import os
import time
import logging
import numpy as np
from typing import Dict, List, Tuple, Any, Optional, Union, Callable

from cardgame_ai.core.agent import Agent
from cardgame_ai.core.environment import Environment
from cardgame_ai.core.base import Batch
from cardgame_ai.training.role_assignment_controller import RoleAssignmentController
from cardgame_ai.training.farmer_cooperation import FarmerCooperation
from cardgame_ai.training.bidding_phase_handler import BiddingPhaseHandler
from cardgame_ai.algorithms.role_specific_mappo import RoleSpecificMAPPO


class TrainingPhase:
    """
    训练阶段基类

    定义训练阶段的基本接口和通用功能。
    """

    def __init__(
        self,
        name: str,
        num_episodes: int,
        eval_interval: int = 100,
        success_threshold: float = 0.6
    ):
        """
        初始化训练阶段

        Args:
            name: 阶段名称
            num_episodes: 训练回合数
            eval_interval: 评估间隔回合数
            success_threshold: 成功阈值
        """
        self.name = name
        self.num_episodes = num_episodes
        self.eval_interval = eval_interval
        self.success_threshold = success_threshold

        # 阶段状态
        self.current_episode = 0
        self.is_completed = False
        self.metrics = {}

        # 阶段特定的配置
        self.config = {}

    def start(self) -> None:
        """开始阶段"""
        self.current_episode = 0
        self.is_completed = False
        self.metrics = {}

        logging.info(f"开始{self.name}阶段，计划训练{self.num_episodes}回合")

    def update(self, episode_metrics: Dict[str, Any]) -> None:
        """
        更新阶段状态

        Args:
            episode_metrics: 回合指标
        """
        self.current_episode += 1

        # 更新指标
        for key, value in episode_metrics.items():
            if key not in self.metrics:
                self.metrics[key] = []
            self.metrics[key].append(value)

        # 检查是否完成
        if self.current_episode >= self.num_episodes:
            self.is_completed = True
            logging.info(f"{self.name}阶段完成，共训练{self.current_episode}回合")

    def should_evaluate(self) -> bool:
        """
        是否应该进行评估

        Returns:
            是否应该评估
        """
        return self.current_episode % self.eval_interval == 0

    def is_successful(self, eval_metrics: Dict[str, float]) -> bool:
        """
        检查阶段是否成功

        Args:
            eval_metrics: 评估指标

        Returns:
            是否成功
        """
        # 默认实现，子类可以重写
        if "win_rate" in eval_metrics:
            return eval_metrics["win_rate"] >= self.success_threshold
        return False

    def get_config(self) -> Dict[str, Any]:
        """
        获取阶段配置

        Returns:
            阶段配置字典
        """
        return {
            "name": self.name,
            "num_episodes": self.num_episodes,
            "eval_interval": self.eval_interval,
            "success_threshold": self.success_threshold,
            "current_episode": self.current_episode,
            "is_completed": self.is_completed
        }

    def get_summary(self) -> Dict[str, Any]:
        """
        获取阶段摘要

        Returns:
            阶段摘要字典
        """
        summary = self.get_config()

        # 添加指标摘要
        metrics_summary = {}
        for key, values in self.metrics.items():
            if values:
                metrics_summary[key] = {
                    "mean": np.mean(values),
                    "min": np.min(values),
                    "max": np.max(values),
                    "last": values[-1]
                }

        summary["metrics"] = metrics_summary

        return summary


class BasicTrainingPhase(TrainingPhase):
    """
    基础训练阶段

    使用自我对弈生成基础经验，每个模型在其专属角色上进行初步训练。
    """

    def __init__(
        self,
        num_episodes: int = 1000,
        eval_interval: int = 100,
        success_threshold: float = 0.5
    ):
        """
        初始化基础训练阶段

        Args:
            num_episodes: 训练回合数
            eval_interval: 评估间隔回合数
            success_threshold: 成功阈值
        """
        super().__init__(
            name="基础训练",
            num_episodes=num_episodes,
            eval_interval=eval_interval,
            success_threshold=success_threshold
        )

        # 基础训练阶段的特定配置
        self.config = {
            "learning_rate": 0.001,
            "batch_size": 64,
            "update_epochs": 4,
            "clip_ratio": 0.2,
            "entropy_coef": 0.01
        }

    def is_successful(self, eval_metrics: Dict[str, float]) -> bool:
        """
        检查阶段是否成功

        Args:
            eval_metrics: 评估指标

        Returns:
            是否成功
        """
        # 基础训练阶段的成功标准：胜率达到阈值
        if "win_rate" in eval_metrics:
            return eval_metrics["win_rate"] >= self.success_threshold
        return False


class RoleSpecificTrainingPhase(TrainingPhase):
    """
    角色专一化训练阶段

    严格控制角色分配，确保每个模型只在其专属角色上训练。
    """

    def __init__(
        self,
        num_episodes: int = 2000,
        eval_interval: int = 100,
        success_threshold: float = 0.6
    ):
        """
        初始化角色专一化训练阶段

        Args:
            num_episodes: 训练回合数
            eval_interval: 评估间隔回合数
            success_threshold: 成功阈值
        """
        super().__init__(
            name="角色专一化训练",
            num_episodes=num_episodes,
            eval_interval=eval_interval,
            success_threshold=success_threshold
        )

        # 角色专一化训练阶段的特定配置
        self.config = {
            "learning_rate": 0.0005,  # 降低学习率
            "batch_size": 128,
            "update_epochs": 6,
            "clip_ratio": 0.15,
            "entropy_coef": 0.005,  # 降低熏正则化
            "role_control": True,  # 启用角色控制
            "separate_buffers": True  # 使用分离的经验回放缓冲区
        }

    def is_successful(self, eval_metrics: Dict[str, float]) -> bool:
        """
        检查阶段是否成功

        Args:
            eval_metrics: 评估指标

        Returns:
            是否成功
        """
        # 角色专一化训练阶段的成功标准：胜率和角色适应性
        if "win_rate" in eval_metrics and "role_adaptation" in eval_metrics:
            return (eval_metrics["win_rate"] >= self.success_threshold and
                    eval_metrics["role_adaptation"] >= 0.7)
        return False


class FarmerCooperationTrainingPhase(TrainingPhase):
    """
    农民协作训练阶段

    专注于提高两个农民模型之间的协作。
    """

    def __init__(
        self,
        num_episodes: int = 1500,
        eval_interval: int = 100,
        success_threshold: float = 0.65,
        cooperation_threshold: float = 0.6
    ):
        """
        初始化农民协作训练阶段

        Args:
            num_episodes: 训练回合数
            eval_interval: 评估间隔回合数
            success_threshold: 成功阈值
            cooperation_threshold: 协作阈值
        """
        super().__init__(
            name="农民协作训练",
            num_episodes=num_episodes,
            eval_interval=eval_interval,
            success_threshold=success_threshold
        )

        self.cooperation_threshold = cooperation_threshold

        # 农民协作训练阶段的特定配置
        self.config = {
            "learning_rate": 0.0003,
            "batch_size": 128,
            "update_epochs": 8,
            "clip_ratio": 0.1,
            "entropy_coef": 0.003,
            "cooperation_weight": 0.7,  # 协作奖励权重
            "team_reward_weight": 0.8,  # 团队奖励权重
            "fixed_landlord": True  # 使用固定的地主AI
        }

    def is_successful(self, eval_metrics: Dict[str, float]) -> bool:
        """
        检查阶段是否成功

        Args:
            eval_metrics: 评估指标

        Returns:
            是否成功
        """
        # 农民协作训练阶段的成功标准：农民胜率和协作分数
        if "farmer_win_rate" in eval_metrics and "cooperation_score" in eval_metrics:
            return (eval_metrics["farmer_win_rate"] >= self.success_threshold and
                    eval_metrics["cooperation_score"] >= self.cooperation_threshold)
        return False


class FineTuningPhase(TrainingPhase):
    """
    微调验证阶段

    基于交叉验证结果进行针对性微调，解决发现的问题。
    """

    def __init__(
        self,
        num_episodes: int = 1000,
        eval_interval: int = 50,
        success_threshold: float = 0.75
    ):
        """
        初始化微调验证阶段

        Args:
            num_episodes: 训练回合数
            eval_interval: 评估间隔回合数
            success_threshold: 成功阈值
        """
        super().__init__(
            name="微调验证",
            num_episodes=num_episodes,
            eval_interval=eval_interval,
            success_threshold=success_threshold
        )

        # 微调验证阶段的特定配置
        self.config = {
            "learning_rate": 0.0001,  # 非常低的学习率
            "batch_size": 512,
            "update_epochs": 12,
            "clip_ratio": 0.05,
            "entropy_coef": 0.001,
            "cross_validation": True,  # 启用交叉验证
            "role_confusion_penalty": 0.2  # 角色混淆惩罚
        }

    def is_successful(self, eval_metrics: Dict[str, float]) -> bool:
        """
        检查阶段是否成功

        Args:
            eval_metrics: 评估指标

        Returns:
            是否成功
        """
        # 微调验证阶段的成功标准：高胜率和低角色混淆
        if "win_rate" in eval_metrics and "role_confusion_score" in eval_metrics:
            return (eval_metrics["win_rate"] >= self.success_threshold and
                    eval_metrics["role_confusion_score"] <= 0.2)
        return False


class AdversarialTrainingPhase(TrainingPhase):
    """
    对抗训练阶段

    三个模型相互对抗，动态调整难度，确保平衡的胜率。
    """

    def __init__(
        self,
        num_episodes: int = 3000,
        eval_interval: int = 100,
        success_threshold: float = 0.7,
        balance_threshold: float = 0.4
    ):
        """
        初始化对抗训练阶段

        Args:
            num_episodes: 训练回合数
            eval_interval: 评估间隔回合数
            success_threshold: 成功阈值
            balance_threshold: 平衡阈值，地主胜率应该在[balance_threshold, 1-balance_threshold]范围内
        """
        super().__init__(
            name="对抗训练",
            num_episodes=num_episodes,
            eval_interval=eval_interval,
            success_threshold=success_threshold
        )

        self.balance_threshold = balance_threshold

        # 对抗训练阶段的特定配置
        self.config = {
            "learning_rate": 0.0002,
            "batch_size": 256,
            "update_epochs": 10,
            "clip_ratio": 0.1,
            "entropy_coef": 0.002,
            "adaptive_difficulty": True,  # 自适应难度
            "opponent_modeling": True  # 对手建模
        }

    def is_successful(self, eval_metrics: Dict[str, float]) -> bool:
        """
        检查阶段是否成功

        Args:
            eval_metrics: 评估指标

        Returns:
            是否成功
        """
        # 对抗训练阶段的成功标准：总体胜率和平衡性
        if "win_rate" in eval_metrics and "landlord_win_rate" in eval_metrics:
            landlord_win_rate = eval_metrics["landlord_win_rate"]
            is_balanced = self.balance_threshold <= landlord_win_rate <= (1 - self.balance_threshold)
            return eval_metrics["win_rate"] >= self.success_threshold and is_balanced
        return False





class PhasedTrainingStrategy:
    """
    分阶段训练策略

    管理分阶段训练的整体流程，包括阶段转换、配置调整和训练进度跟踪。
    """

    def __init__(self, env: Optional[Environment] = None, agents: Optional[Dict[str, Agent]] = None):
        """
        初始化分阶段训练策略

        Args:
            env: 游戏环境，可选
            agents: 智能体字典，可选
        """
        # 初始化各阶段
        self.phases = [
            BasicTrainingPhase(),
            RoleSpecificTrainingPhase(),
            FarmerCooperationTrainingPhase(),
            AdversarialTrainingPhase(),
            FineTuningPhase()
        ]

        # 当前阶段索引
        self.current_phase_index = 0

        # 训练状态
        self.is_completed = False

        # 环境和智能体
        self.env = env
        self.agents = agents

        # 阶段特定的组件
        self.role_controller = None
        self.farmer_cooperation = None
        self.bidding_handler = None

        # 训练统计
        self.stats = {
            "total_episodes": 0,
            "phase_transitions": 0,
            "start_time": time.time(),
            "phase_history": [],
            "best_metrics": {}
        }

    def get_current_phase(self) -> Optional[TrainingPhase]:
        """
        获取当前阶段

        Returns:
            当前阶段，如果所有阶段已完成则返回None
        """
        if self.is_completed or self.current_phase_index >= len(self.phases):
            return None
        return self.phases[self.current_phase_index]

    def start(self) -> None:
        """开始训练"""
        self.current_phase_index = 0
        self.is_completed = False
        self.stats["start_time"] = time.time()
        self.stats["total_episodes"] = 0
        self.stats["phase_transitions"] = 0
        self.stats["phase_history"] = []
        self.stats["best_metrics"] = {}

        # 初始化阶段特定的组件
        self._initialize_phase_components()

        # 开始第一个阶段
        self.get_current_phase().start()

        logging.info(f"开始分阶段训练，共{len(self.phases)}个阶段")

    def _initialize_phase_components(self) -> None:
        """
        初始化阶段特定的组件
        """
        if self.env is None:
            logging.warning("环境未设置，无法初始化阶段组件")
            return

        # 初始化角色分配控制器
        current_phase = self.get_current_phase()
        if current_phase.name == "角色专一化训练" or current_phase.name == "农民协作训练":
            # 对于角色专一化和农民协作阶段，启用角色控制
            target_distribution = None
            if current_phase.name == "角色专一化训练":
                # 均匀分布
                target_distribution = {"landlord": 1/3, "farmer1": 1/3, "farmer2": 1/3}
            elif current_phase.name == "农民协作训练":
                # 偏向农民角色
                target_distribution = {"landlord": 0.2, "farmer1": 0.4, "farmer2": 0.4}

            self.role_controller = RoleAssignmentController(
                env=self.env,
                control_episodes=current_phase.num_episodes // 2,  # 控制前半阶段
                target_distribution=target_distribution
            )
            logging.info(f"初始化角色分配控制器，目标分布: {target_distribution}")

        # 初始化农民协作机制
        if current_phase.name == "农民协作训练":
            self.farmer_cooperation = FarmerCooperation(
                cooperation_weight=current_phase.config.get("cooperation_weight", 0.7),
                team_reward_weight=current_phase.config.get("team_reward_weight", 0.8)
            )
            logging.info("初始化农民协作机制")

        # 初始化叫地主/抢地主阶段处理器
        self.bidding_handler = BiddingPhaseHandler(
            bid_reward_weight=0.2,
            grab_reward_weight=0.3,
            landlord_bid_threshold=0.6,
            farmer_bid_threshold=0.4
        )
        logging.info("初始化叫地主/抢地主阶段处理器")

    def update(self, episode_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新训练状态

        Args:
            episode_metrics: 回合指标

        Returns:
            更新后的状态信息
        """
        # 更新当前阶段
        current_phase = self.get_current_phase()
        current_phase.update(episode_metrics)

        # 更新总回合数
        self.stats["total_episodes"] += 1

        # 更新最佳指标
        self._update_best_metrics(episode_metrics)

        # 检查是否需要进行评估
        should_evaluate = current_phase.should_evaluate()

        # 检查是否完成当前阶段
        if current_phase.is_completed:
            # 记录阶段历史
            phase_summary = current_phase.get_summary()
            self.stats["phase_history"].append({
                "phase": current_phase.name,
                "episodes": current_phase.current_episode,
                "metrics": phase_summary["metrics"],
                "config": current_phase.config,
                "duration": time.time() - self.stats["start_time"]
            })

            # 进入下一阶段
            self.current_phase_index += 1
            self.stats["phase_transitions"] += 1

            # 检查是否完成所有阶段
            if self.current_phase_index >= len(self.phases):
                self.is_completed = True
                logging.info("分阶段训练完成！")
                logging.info(f"最终最佳指标: {self.stats['best_metrics']}")
            else:
                # 开始下一阶段
                next_phase = self.get_current_phase()
                next_phase.start()
                logging.info(f"进入新阶段：{next_phase.name}，计划训练{next_phase.num_episodes}回合")

                # 强制进行评估
                should_evaluate = True

                # 重新初始化阶段组件
                self._initialize_phase_components()

                # 应用阶段特定的配置
                self._apply_phase_config(next_phase)

        # 返回状态信息
        result = {
            "total_episodes": self.stats["total_episodes"],
            "is_completed": self.is_completed,
            "should_evaluate": should_evaluate,
            "best_metrics": self.stats["best_metrics"]
        }

        # 添加当前阶段的信息（如果有）
        current_phase = self.get_current_phase()
        if current_phase:
            result.update({
                "current_phase": current_phase.name,
                "current_episode": current_phase.current_episode,
                "phase_config": current_phase.config
            })
        else:
            result.update({
                "current_phase": "completed",
                "current_episode": 0,
                "phase_config": {}
            })

        return result

    def _update_best_metrics(self, episode_metrics: Dict[str, Any]) -> None:
        """
        更新最佳指标

        Args:
            episode_metrics: 回合指标
        """
        for key, value in episode_metrics.items():
            if isinstance(value, (int, float)):
                # 对于数值型指标，记录最大值
                if key not in self.stats["best_metrics"] or value > self.stats["best_metrics"][key]:
                    self.stats["best_metrics"][key] = value
                    logging.info(f"新的最佳指标: {key} = {value}")

    def _apply_phase_config(self, phase: TrainingPhase) -> None:
        """
        应用阶段特定的配置

        Args:
            phase: 训练阶段
        """
        if self.agents is None:
            logging.warning("智能体未设置，无法应用阶段配置")
            return

        config = phase.config
        logging.info(f"应用阶段配置: {config}")

        # 应用学习率
        if "learning_rate" in config:
            for agent_id, agent in self.agents.items():
                if hasattr(agent, "set_learning_rate"):
                    agent.set_learning_rate(config["learning_rate"])
                    logging.info(f"设置智能体 {agent_id} 的学习率为 {config['learning_rate']}")

        # 应用熵正则化系数
        if "entropy_coef" in config:
            for agent_id, agent in self.agents.items():
                if hasattr(agent, "set_entropy_coef"):
                    agent.set_entropy_coef(config["entropy_coef"])
                    logging.info(f"设置智能体 {agent_id} 的熵正则化系数为 {config['entropy_coef']}")

        # 应用其他算法特定的配置
        for key, value in config.items():
            if key in ["learning_rate", "entropy_coef"]:
                continue  # 这些已经处理过

            for agent_id, agent in self.agents.items():
                if hasattr(agent, f"set_{key}"):
                    getattr(agent, f"set_{key}")(value)
                    logging.info(f"设置智能体 {agent_id} 的 {key} 为 {value}")

    def get_summary(self) -> Dict[str, Any]:
        """
        获取训练摘要

        Returns:
            训练摘要字典
        """
        current_time = time.time()
        duration = current_time - self.stats["start_time"]
        hours, remainder = divmod(duration, 3600)
        minutes, seconds = divmod(remainder, 60)
        duration_str = f"{int(hours)}h {int(minutes)}m {int(seconds)}s"

        # 计算平均每阶段时间
        avg_phase_duration = 0
        if self.stats["phase_transitions"] > 0:
            avg_phase_duration = duration / self.stats["phase_transitions"]

        # 计算平均每回合时间
        avg_episode_duration = 0
        if self.stats["total_episodes"] > 0:
            avg_episode_duration = duration / self.stats["total_episodes"]

        # 收集阶段进度信息
        phase_progress = []
        for i, phase in enumerate(self.phases):
            if i < self.current_phase_index:
                # 已完成的阶段
                progress = 1.0
            elif i == self.current_phase_index and not self.is_completed:
                # 当前阶段
                progress = phase.current_episode / phase.num_episodes
            else:
                # 未开始的阶段
                progress = 0.0

            phase_progress.append({
                "phase": phase.name,
                "progress": progress,
                "episodes_completed": phase.current_episode if i <= self.current_phase_index else 0,
                "episodes_total": phase.num_episodes
            })

        # 收集最佳指标
        best_metrics = self.stats["best_metrics"]

        return {
            "total_episodes": self.stats["total_episodes"],
            "phase_transitions": self.stats["phase_transitions"],
            "duration": duration,
            "duration_str": duration_str,
            "avg_phase_duration": avg_phase_duration,
            "avg_episode_duration": avg_episode_duration,
            "is_completed": self.is_completed,
            "current_phase": self.get_current_phase().name if self.get_current_phase() else "completed",
            "phase_history": self.stats["phase_history"],
            "phase_progress": phase_progress,
            "best_metrics": best_metrics,
            "current_phase_summary": self.get_current_phase().get_summary() if self.get_current_phase() else None,
            "total_progress": sum(p["progress"] for p in phase_progress) / len(self.phases)
        }

    def visualize_progress(self) -> str:
        """
        可视化训练进度

        Returns:
            进度可视化字符串
        """
        summary = self.get_summary()
        progress_str = []

        # 添加标题
        progress_str.append("\n=== 分阶段训练进度 ===\n")

        # 添加总体信息
        progress_str.append(f"\u603b回合数: {summary['total_episodes']}")
        progress_str.append(f"\u8bad练时间: {summary['duration_str']}")
        progress_str.append(f"\u5f53前阶段: {summary['current_phase']}")
        progress_str.append(f"\u603b进度: {summary['total_progress']:.1%}" + (" (已完成)" if summary['is_completed'] else "") + "\n")

        # 添加各阶段进度
        progress_str.append("\u9636段进度:")
        for phase_info in summary["phase_progress"]:
            phase_name = phase_info["phase"]
            progress = phase_info["progress"]
            episodes_completed = phase_info["episodes_completed"]
            episodes_total = phase_info["episodes_total"]

            # 创建进度条
            bar_length = 30
            filled_length = int(bar_length * progress)
            bar = "#" * filled_length + "-" * (bar_length - filled_length)

            progress_str.append(f"  {phase_name}: [{bar}] {progress:.1%} ({episodes_completed}/{episodes_total})")

        # 添加最佳指标
        if summary["best_metrics"]:
            progress_str.append("\n\u6700佳指标:")
            for key, value in summary["best_metrics"].items():
                progress_str.append(f"  {key}: {value:.4f}")

        return "\n".join(progress_str)
