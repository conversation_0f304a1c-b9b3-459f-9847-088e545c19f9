"""
高级CFR模块

提供用于计算近似GTO策略的高级CFR算法，
包括CFR+和折扣CFR(DCFR)变体，收敛速度更快，
生成的策略质量更高。
"""

import numpy as np
import logging
import time
from typing import Dict, Any, Optional, List, Tuple, Union, Callable

from .gto_policy import GTOPolicy

# 配置日志
logger = logging.getLogger(__name__)

class AdvancedCFR:
    """
    高级CFR类
    
    实现多种高级CFR变体，如CFR+和折扣CFR(DCFR)，用于快速收敛到近似GTO策略。
    相比简化CFR，收敛速度更快，生成的策略质量更高。
    """
    
    def __init__(
        self,
        num_actions: int,
        utility_function: Callable[[str, int], float],
        is_terminal: Callable[[str], bool],
        get_legal_actions: Callable[[str], List[int]],
        get_next_state: Callable[[str, int], str],
        algorithm: str = 'cfr+',  # 'cfr+', 'dcfr'
        max_iterations: int = 1000,
        pruning_threshold: float = 0.0,  # 用于剪枝的阈值
        linear_averaging: bool = True, # 是否使用线性加权平均
        # DCFR参数
        alpha_regret: float = 1.5,
        beta_strategy: float = 0.5,
        gamma_discount: float = 2.0
    ):
        """
        初始化高级CFR
        
        Args:
            num_actions: 动作空间大小
            utility_function: 效用函数，接收状态和动作，返回效用值
            is_terminal: 判断状态是否为终止状态的函数
            get_legal_actions: 获取合法动作的函数
            get_next_state: 获取下一个状态的函数
            algorithm: 使用的算法变体，目前支持'cfr+'和'dcfr'
            max_iterations: 最大迭代次数
            pruning_threshold: 用于剪枝的阈值，遗憾小于此值的动作将被剪枝
            linear_averaging: 是否使用线性加权平均
            alpha_regret: DCFR的遗憾递增指数
            beta_strategy: DCFR的策略递增指数
            gamma_discount: DCFR的折扣参数
        """
        self.num_actions = num_actions
        self.utility_function = utility_function
        self.is_terminal = is_terminal
        self.get_legal_actions = get_legal_actions
        self.get_next_state = get_next_state
        self.algorithm = algorithm.lower()
        self.max_iterations = max_iterations
        self.pruning_threshold = pruning_threshold
        self.linear_averaging = linear_averaging
        
        # DCFR参数
        self.alpha_regret = alpha_regret
        self.beta_strategy = beta_strategy
        self.gamma_discount = gamma_discount
        
        # 累积遗憾和策略
        self.cumulative_regrets = {}
        self.cumulative_strategy = {}
        
        # 当前策略
        self.current_strategy = {}
        
        # 状态访问次数
        self.state_visits = {}
        
        # 验证算法参数
        if self.algorithm not in ['cfr+', 'dcfr']:
            raise ValueError(f"不支持的算法: {algorithm}，仅支持'cfr+'和'dcfr'")
            
        logger.info(f"初始化高级CFR，算法: {algorithm}，动作空间大小: {num_actions}，"
                  f"最大迭代次数: {max_iterations}")
        if self.algorithm == 'dcfr':
            logger.info(f"DCFR参数：alpha_regret={alpha_regret}, beta_strategy={beta_strategy}, "
                      f"gamma_discount={gamma_discount}")
    
    def _get_strategy(self, state_key: str, iteration: int = 0) -> np.ndarray:
        """
        根据累积遗憾计算当前策略
        
        Args:
            state_key: 状态的唯一标识符
            iteration: 当前迭代次数
            
        Returns:
            当前策略分布
        """
        # 如果状态不存在，初始化
        if state_key not in self.cumulative_regrets:
            self.cumulative_regrets[state_key] = np.zeros(self.num_actions)
            self.cumulative_strategy[state_key] = np.zeros(self.num_actions)
            self.state_visits[state_key] = 0
        
        # 更新状态访问次数
        self.state_visits[state_key] += 1
        
        # 获取合法动作
        legal_actions = self.get_legal_actions(state_key)
        
        # 根据算法选择不同的遗憾处理方式
        if self.algorithm == 'cfr+':
            # CFR+：正遗憾和遗憾匹配
            regrets = np.maximum(0, self.cumulative_regrets[state_key])
        elif self.algorithm == 'dcfr':
            # DCFR：折扣遗憾
            if iteration > 0:
                discount = (iteration / (iteration + 1)) ** self.gamma_discount
                self.cumulative_regrets[state_key] *= discount
            
            # 计算正遗憾
            regrets = np.maximum(0, self.cumulative_regrets[state_key])
        
        # 计算遗憾和
        regret_sum = np.sum(regrets[legal_actions])
        
        # 计算策略
        strategy = np.zeros(self.num_actions)
        if regret_sum > 0:
            for action in legal_actions:
                strategy[action] = regrets[action] / regret_sum
        else:
            # 如果没有正遗憾，使用均匀分布
            for action in legal_actions:
                strategy[action] = 1.0 / len(legal_actions)
        
        # 应用剪枝阈值
        if self.pruning_threshold > 0:
            for action in legal_actions:
                if regrets[action] < self.pruning_threshold:
                    strategy[action] = 0.0
            
            # 重新归一化策略
            strategy_sum = np.sum(strategy[legal_actions])
            if strategy_sum > 0:
                strategy[legal_actions] /= strategy_sum
            else:
                # 如果所有动作都被剪枝，使用均匀分布
                strategy[legal_actions] = 1.0 / len(legal_actions)
        
        return strategy
    
    def _cfr(self, state_key: str, player_reach_prob: float, opponent_reach_prob: float, iteration: int) -> float:
        """
        执行CFR递归
        
        Args:
            state_key: 状态的唯一标识符
            player_reach_prob: 当前玩家的到达概率
            opponent_reach_prob: 对手的到达概率
            iteration: 当前迭代次数
            
        Returns:
            状态的期望效用
        """
        # 如果是终止状态，返回效用
        if self.is_terminal(state_key):
            return self.utility_function(state_key, -1)  # -1表示终止状态，无需动作
        
        # 获取合法动作
        legal_actions = self.get_legal_actions(state_key)
        
        # 获取当前策略
        strategy = self._get_strategy(state_key, iteration)
        
        # 更新累积策略
        if self.algorithm == 'cfr+':
            # CFR+: 使用线性加权平均
            if self.linear_averaging:
                weight = iteration + 1
            else:
                weight = 1.0
            self.cumulative_strategy[state_key] += weight * opponent_reach_prob * strategy
        elif self.algorithm == 'dcfr':
            # DCFR: 应用策略折扣
            if iteration > 0:
                weight = ((iteration + 1) / iteration) ** self.beta_strategy
            else:
                weight = 1.0
            self.cumulative_strategy[state_key] += weight * opponent_reach_prob * strategy
        
        # 计算每个动作的反事实值
        action_values = np.zeros(self.num_actions)
        expected_value = 0.0
        
        # 递归计算每个动作的值
        for action in legal_actions:
            next_state = self.get_next_state(state_key, action)
            action_values[action] = self._cfr(
                next_state,
                player_reach_prob * strategy[action],
                opponent_reach_prob,
                iteration
            )
            expected_value += strategy[action] * action_values[action]
        
        # 更新累积遗憾
        if player_reach_prob > 0:
            for action in legal_actions:
                regret = action_values[action] - expected_value
                
                if self.algorithm == 'cfr+':
                    # CFR+: 立即加正遗憾
                    self.cumulative_regrets[state_key][action] += opponent_reach_prob * max(0, regret)
                elif self.algorithm == 'dcfr':
                    # DCFR: 标准遗憾更新，后续会应用折扣
                    self.cumulative_regrets[state_key][action] += opponent_reach_prob * regret
                    
                    # 应用遗憾递增
                    if regret > 0 and iteration > 0:
                        self.cumulative_regrets[state_key][action] *= (iteration / (iteration + 1)) ** self.alpha_regret
        
        return expected_value
    
    def train(self, initial_state: str, callback: Optional[Callable[[int, GTOPolicy], bool]] = None) -> GTOPolicy:
        """
        训练CFR算法
        
        Args:
            initial_state: 初始状态的唯一标识符
            callback: 回调函数，每次迭代后调用，接收迭代次数和当前策略，返回是否继续训练
            
        Returns:
            训练得到的GTO策略
        """
        logger.info(f"开始训练高级CFR算法，算法: {self.algorithm}，初始状态: {initial_state}")
        start_time = time.time()
        
        # 清空之前的训练结果
        self.cumulative_regrets = {}
        self.cumulative_strategy = {}
        self.current_strategy = {}
        self.state_visits = {}
        
        # 创建GTO策略
        gto_policy = GTOPolicy()
        
        # 计算当前GTO策略
        def compute_current_policy():
            current_policy = GTOPolicy()
            
            for state_key, cumulative_strategy in self.cumulative_strategy.items():
                # 获取合法动作
                legal_actions = self.get_legal_actions(state_key)
                
                # 计算策略
                strategy_sum = np.sum(cumulative_strategy[legal_actions])
                if strategy_sum > 0:
                    normalized_strategy = np.zeros(self.num_actions)
                    for action in legal_actions:
                        normalized_strategy[action] = cumulative_strategy[action] / strategy_sum
                    current_policy.set_policy(state_key, normalized_strategy)
            
            return current_policy
        
        # 执行CFR迭代
        for i in range(self.max_iterations):
            # 记录迭代开始时间
            iter_start_time = time.time()
            
            # 执行一次递归
            self._cfr(initial_state, 1.0, 1.0, i)
            
            # 迭代结束，记录时间和信息
            iter_end_time = time.time()
            iter_duration = iter_end_time - iter_start_time
            
            # 每100次迭代打印一次信息，或者在最后一次迭代
            if (i + 1) % 100 == 0 or i == 0 or i == self.max_iterations - 1:
                # 计算状态数量
                num_states = len(self.cumulative_strategy)
                
                logger.info(f"CFR迭代: {i + 1}/{self.max_iterations}, "
                          f"耗时: {iter_duration:.2f}秒, "
                          f"累计耗时: {time.time() - start_time:.2f}秒, "
                          f"状态数: {num_states}")
            
            # 如果有回调函数，计算当前策略并调用回调
            if callback is not None:
                current_policy = compute_current_policy()
                if not callback(i, current_policy):
                    logger.info(f"回调函数请求停止训练，已完成 {i + 1} 次迭代")
                    break
        
        # 计算最终GTO策略
        for state_key, cumulative_strategy in self.cumulative_strategy.items():
            # 获取合法动作
            legal_actions = self.get_legal_actions(state_key)
            
            # 计算策略
            strategy_sum = np.sum(cumulative_strategy[legal_actions])
            if strategy_sum > 0:
                normalized_strategy = np.zeros(self.num_actions)
                for action in legal_actions:
                    normalized_strategy[action] = cumulative_strategy[action] / strategy_sum
                gto_policy.set_policy(state_key, normalized_strategy)
        
        total_time = time.time() - start_time
        logger.info(f"CFR训练完成，总耗时: {total_time:.2f}秒, "
                   f"生成GTO策略，包含 {len(gto_policy.policy)} 个状态")
        
        return gto_policy
    
    def prune_game_tree(self, max_depth: int = 3, min_visits: int = 5) -> None:
        """
        剪枝游戏树，减少状态空间
        
        Args:
            max_depth: 最大深度
            min_visits: 最小访问次数，访问次数少于此值的状态将被剪枝
        """
        logger.info(f"剪枝游戏树，最大深度: {max_depth}, 最小访问次数: {min_visits}")
        
        # 获取需要保留的状态
        states_to_keep = set()
        
        for state_key, visits in self.state_visits.items():
            # 计算状态深度（简单估计）
            depth = state_key.count('|')
            
            # 如果访问次数足够且深度不超过最大深度，则保留
            if visits >= min_visits and depth <= max_depth:
                states_to_keep.add(state_key)
        
        # 剪枝
        pruned_count = 0
        for state_key in list(self.cumulative_regrets.keys()):
            if state_key not in states_to_keep:
                del self.cumulative_regrets[state_key]
                pruned_count += 1
        
        for state_key in list(self.cumulative_strategy.keys()):
            if state_key not in states_to_keep:
                del self.cumulative_strategy[state_key]
        
        logger.info(f"剪枝完成，剪掉 {pruned_count} 个状态，剩余 {len(states_to_keep)} 个状态")
    
    def compute_exploitability(self, gto_policy: GTOPolicy, initial_state: str) -> float:
        """
        计算策略的可剥削性
        
        Args:
            gto_policy: 要评估的GTO策略
            initial_state: 初始状态
            
        Returns:
            可剥削性值（越低越好）
        """
        logger.info("计算策略可剥削性...")
        
        # 这是一个简化的可剥削性计算
        # 在实际应用中，需要计算最佳响应值并与预期值比较
        # 这里仅实现一个示例框架
        
        # 实现最佳响应值计算
        def compute_best_response_value(state_key: str, policy_player: bool = True) -> float:
            """计算最佳响应值"""
            if self.is_terminal(state_key):
                return self.utility_function(state_key, -1)
            
            legal_actions = self.get_legal_actions(state_key)
            
            if policy_player:
                # 策略玩家的回合，使用GTO策略
                policy = gto_policy.get_policy(state_key, legal_actions)
                if policy is None:
                    policy = np.ones(len(legal_actions)) / len(legal_actions)
                
                value = 0.0
                for i, action in enumerate(legal_actions):
                    next_state = self.get_next_state(state_key, action)
                    value += policy[action] * compute_best_response_value(next_state, not policy_player)
                return value
            else:
                # 最佳响应玩家的回合，选择最佳动作
                best_value = float('-inf')
                for action in legal_actions:
                    next_state = self.get_next_state(state_key, action)
                    value = compute_best_response_value(next_state, not policy_player)
                    best_value = max(best_value, value)
                return best_value
        
        # 计算最佳响应值
        br_value = compute_best_response_value(initial_state)
        
        # 计算可剥削性（简化，实际应用中可能不同）
        exploitability = abs(br_value)
        
        logger.info(f"策略可剥削性: {exploitability}")
        
        return exploitability 