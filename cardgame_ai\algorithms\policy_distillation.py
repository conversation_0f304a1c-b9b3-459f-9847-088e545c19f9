"""
策略蒸馏模块

实现策略蒸馏技术，允许将知识从一个复杂模型(教师)转移到一个较小模型(学生)。
这有助于减少推理时间并提高模型部署效率。
"""
import os
import logging
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Any, Optional, Union, Callable
from collections import defaultdict

from cardgame_ai.core.agent import Agent
from cardgame_ai.core.base import State, Action, Experience, Batch
from cardgame_ai.utils.logger import get_logger

logger = logging.getLogger(__name__)


class PolicyDistillation:
    """
    策略蒸馏
    
    通过模仿教师模型的行为决策来训练学生模型，从而将教师模型的知识迁移到学生模型。
    """
    
    def __init__(
        self,
        student_model: nn.Module,
        teacher_model: nn.Module,
        temperature: float = 2.0,
        alpha: float = 0.5,
        device: str = 'cuda' if torch.cuda.is_available() else 'cpu'
    ):
        """
        初始化策略蒸馏
        
        Args:
            student_model: 学生模型
            teacher_model: 教师模型
            temperature: 软目标的温度参数
            alpha: 平衡蒸馏损失和任务损失的权重
            device: 计算设备
        """
        self.student_model = student_model
        self.teacher_model = teacher_model
        self.temperature = temperature
        self.alpha = alpha
        self.device = device
        
        # 设置模型到指定设备
        self.student_model.to(device)
        self.teacher_model.to(device)
        
        # 设置教师模型为评估模式
        self.teacher_model.eval()
        
        # 优化器
        self.optimizer = torch.optim.Adam(self.student_model.parameters(), lr=0.001)
        
        # 统计信息
        self.stats = {
            "distillation_loss": [],
            "task_loss": [],
            "total_loss": [],
            "updates": 0
        }
        
        logger.info(f"策略蒸馏初始化完成，温度参数: {temperature}, alpha: {alpha}")
    
    def compute_distillation_loss(
        self,
        student_logits: torch.Tensor,
        teacher_logits: torch.Tensor
    ) -> torch.Tensor:
        """
        计算蒸馏损失
        
        Args:
            student_logits: 学生模型的输出logits
            teacher_logits: 教师模型的输出logits
            
        Returns:
            蒸馏损失
        """
        # 应用温度缩放
        soft_student = F.log_softmax(student_logits / self.temperature, dim=-1)
        soft_teacher = F.softmax(teacher_logits / self.temperature, dim=-1)
        
        # 计算KL散度
        distillation_loss = F.kl_div(soft_student, soft_teacher, reduction='batchmean') * (self.temperature ** 2)
        
        return distillation_loss
    
    def update(
        self,
        states: torch.Tensor,
        actions: torch.Tensor = None,
        rewards: torch.Tensor = None,
        task_loss_fn: Optional[Callable] = None
    ) -> Dict[str, float]:
        """
        更新学生模型
        
        Args:
            states: 状态张量
            actions: 动作张量，用于计算任务损失
            rewards: 奖励张量，用于计算任务损失
            task_loss_fn: 任务损失函数，如果为None则只使用蒸馏损失
            
        Returns:
            包含各种损失的字典
        """
        # 确保输入在正确的设备上
        states = states.to(self.device)
        if actions is not None:
            actions = actions.to(self.device)
        if rewards is not None:
            rewards = rewards.to(self.device)
        
        # 获取学生模型和教师模型的输出
        self.student_model.train()
        student_output = self.student_model(states)
        
        with torch.no_grad():
            self.teacher_model.eval()
            teacher_output = self.teacher_model(states)
        
        # 提取logits
        student_logits = student_output
        teacher_logits = teacher_output
        
        # 计算蒸馏损失
        distillation_loss = self.compute_distillation_loss(student_logits, teacher_logits)
        
        # 计算任务损失（如果提供了任务损失函数）
        task_loss = torch.tensor(0.0, device=self.device)
        if task_loss_fn is not None and actions is not None:
            task_loss = task_loss_fn(student_output, actions, rewards)
        
        # 计算总损失
        total_loss = self.alpha * distillation_loss + (1 - self.alpha) * task_loss
        
        # 优化器更新
        self.optimizer.zero_grad()
        total_loss.backward()
        self.optimizer.step()
        
        # 更新统计信息
        self.stats["distillation_loss"].append(distillation_loss.item())
        self.stats["task_loss"].append(task_loss.item())
        self.stats["total_loss"].append(total_loss.item())
        self.stats["updates"] += 1
        
        return {
            "distillation_loss": distillation_loss.item(),
            "task_loss": task_loss.item(),
            "total_loss": total_loss.item()
        }
    
    def predict(self, state: Union[torch.Tensor, np.ndarray]) -> torch.Tensor:
        """
        使用学生模型进行预测
        
        Args:
            state: 输入状态
            
        Returns:
            模型输出
        """
        # 确保输入是张量并在正确设备上
        if isinstance(state, np.ndarray):
            state = torch.from_numpy(state).float().to(self.device)
        else:
            state = state.to(self.device)
        
        # 如果输入是单个状态，添加批次维度
        if state.dim() == 3:  # 假设输入是 [C, H, W] 格式的图像或特征
            state = state.unsqueeze(0)
        
        # 使用学生模型预测
        self.student_model.eval()
        with torch.no_grad():
            output = self.student_model(state)
        
        return output
    
    def save(self, path: str) -> None:
        """
        保存学生模型和训练状态
        
        Args:
            path: 保存路径
        """
        state_dict = {
            "student_model": self.student_model.state_dict(),
            "optimizer": self.optimizer.state_dict(),
            "temperature": self.temperature,
            "alpha": self.alpha,
            "stats": self.stats
        }
        torch.save(state_dict, path)
        logger.info(f"策略蒸馏模型已保存到 {path}")
    
    def load(self, path: str) -> None:
        """
        加载学生模型和训练状态
        
        Args:
            path: 加载路径
        """
        if not os.path.exists(path):
            logger.warning(f"模型文件不存在: {path}")
            return
            
        try:
            state_dict = torch.load(path, map_location=self.device)
            self.student_model.load_state_dict(state_dict["student_model"])
            self.optimizer.load_state_dict(state_dict["optimizer"])
            self.temperature = state_dict.get("temperature", self.temperature)
            self.alpha = state_dict.get("alpha", self.alpha)
            self.stats = state_dict.get("stats", self.stats)
            logger.info(f"策略蒸馏模型已从 {path} 加载")
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取训练统计信息
        
        Returns:
            统计信息字典
        """
        # 计算平均损失
        avg_distillation_loss = np.mean(self.stats["distillation_loss"][-100:]) if self.stats["distillation_loss"] else 0
        avg_task_loss = np.mean(self.stats["task_loss"][-100:]) if self.stats["task_loss"] else 0
        avg_total_loss = np.mean(self.stats["total_loss"][-100:]) if self.stats["total_loss"] else 0
        
        return {
            "avg_distillation_loss": avg_distillation_loss,
            "avg_task_loss": avg_task_loss,
            "avg_total_loss": avg_total_loss,
            "updates": self.stats["updates"]
        }

