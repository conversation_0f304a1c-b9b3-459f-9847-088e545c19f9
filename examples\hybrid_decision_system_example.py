#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
混合决策系统示例

演示如何使用混合决策系统，结合神经网络、搜索和规则的优势。
"""
import os
import sys
import argparse
import logging
import numpy as np
import torch
import time
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cardgame_ai.algorithms import (
    EfficientZero, MCTS, RuleBasedAgent, HybridDecisionSystem,
    NeuralNetworkComponent, SearchComponent, RuleComponent
)
from cardgame_ai.core.agent import Agent
from cardgame_ai.games.doudizhu import DouDizhuEnvironment


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser(description="混合决策系统示例")
    parser.add_argument("--model_path", type=str, default=None, help="神经网络模型路径")
    parser.add_argument("--meta_strategy", type=str, default="adaptive", 
                        choices=["fixed", "random", "adaptive", "ucb"], help="元策略")
    parser.add_argument("--num_games", type=int, default=10, help="游戏局数")
    parser.add_argument("--seed", type=int, default=42, help="随机种子")
    parser.add_argument("--log_level", type=str, default="INFO", 
                        choices=["DEBUG", "INFO", "WARNING", "ERROR"], help="日志级别")
    
    return parser.parse_args()


def setup_logging(log_level):
    """
    设置日志
    """
    numeric_level = getattr(logging, log_level.upper(), None)
    if not isinstance(numeric_level, int):
        raise ValueError(f"Invalid log level: {log_level}")
    
    logging.basicConfig(
        level=numeric_level,
        format="%(asctime)s [%(levelname)s] %(message)s",
        handlers=[
            logging.StreamHandler()
        ]
    )


def create_agents(model_path, meta_strategy, seed):
    """
    创建代理
    
    Args:
        model_path: 神经网络模型路径
        meta_strategy: 元策略
        seed: 随机种子
        
    Returns:
        agents: 代理字典
    """
    # 设置随机种子
    np.random.seed(seed)
    torch.manual_seed(seed)
    
    # 创建规则代理
    rule_agent = RuleBasedAgent(seed=seed)
    
    # 创建神经网络模型
    neural_network_model = None
    if model_path and os.path.exists(model_path):
        # 这里应该加载实际的模型，这里简化处理
        logging.info(f"加载神经网络模型: {model_path}")
        # neural_network_model = EfficientZero.load(model_path)
    
    # 创建搜索模型
    # 这里简化处理，实际应该使用神经网络模型或专门的搜索模型
    search_model = None
    
    # 创建混合决策系统
    hybrid_agent = HybridDecisionSystem(
        neural_network_model=neural_network_model,
        search_model=search_model,
        rule_agent=rule_agent,
        meta_strategy=meta_strategy
    )
    
    # 创建代理字典
    agents = {
        "landlord": hybrid_agent,
        "farmer1": rule_agent,
        "farmer2": rule_agent
    }
    
    return agents


def evaluate_agents(agents, num_games, seed):
    """
    评估代理
    
    Args:
        agents: 代理字典
        num_games: 游戏局数
        seed: 随机种子
        
    Returns:
        results: 评估结果
    """
    # 创建环境
    env = DouDizhuEnvironment(seed=seed)
    
    # 初始化结果
    results = {
        "landlord_wins": 0,
        "farmer_wins": 0,
        "game_lengths": [],
        "component_usage": {
            "neural_network": 0,
            "search": 0,
            "rule": 0
        }
    }
    
    # 运行游戏
    for i in range(num_games):
        logging.info(f"游戏 {i+1}/{num_games}")
        
        # 重置环境
        state = env.reset()
        done = False
        step = 0
        
        # 运行一局游戏
        while not done:
            # 获取当前玩家
            current_player = env.get_current_player()
            
            # 获取代理
            if current_player == 0:
                agent = agents["landlord"]
            elif current_player == 1:
                agent = agents["farmer1"]
            else:
                agent = agents["farmer2"]
            
            # 获取合法动作
            legal_actions = env.get_legal_actions()
            
            # 代理做出决策
            action = agent.act(state, legal_actions)
            
            # 执行动作
            state, reward, done, info = env.step(action)
            step += 1
        
        # 记录结果
        results["game_lengths"].append(step)
        if reward[0] > 0:
            results["landlord_wins"] += 1
        else:
            results["farmer_wins"] += 1
        
        # 如果使用了混合决策系统，记录组件使用情况
        if isinstance(agents["landlord"], HybridDecisionSystem):
            stats = agents["landlord"].get_stats()
            for component, count in stats["component_usage"].items():
                if component in results["component_usage"]:
                    results["component_usage"][component] += count
    
    return results


def main():
    """
    主函数
    """
    # 解析命令行参数
    args = parse_args()
    
    # 设置日志
    setup_logging(args.log_level)
    
    # 创建代理
    agents = create_agents(args.model_path, args.meta_strategy, args.seed)
    
    # 评估代理
    results = evaluate_agents(agents, args.num_games, args.seed)
    
    # 打印结果
    logging.info("评估结果:")
    logging.info(f"  地主胜率: {results['landlord_wins'] / args.num_games:.2f}")
    logging.info(f"  农民胜率: {results['farmer_wins'] / args.num_games:.2f}")
    logging.info(f"  平均游戏长度: {np.mean(results['game_lengths']):.2f}")
    
    # 如果使用了混合决策系统，打印组件使用情况
    if isinstance(agents["landlord"], HybridDecisionSystem):
        total_decisions = sum(results["component_usage"].values())
        if total_decisions > 0:
            logging.info("组件使用情况:")
            for component, count in results["component_usage"].items():
                logging.info(f"  {component}: {count / total_decisions:.2f}")
        
        # 打印混合决策系统的统计信息
        logging.info("混合决策系统统计信息:")
        stats = agents["landlord"].get_stats()
        logging.info(f"  决策次数: {stats['decisions']}")
        logging.info(f"  平均奖励: {np.mean(stats['rewards']) if stats['rewards'] else 0:.2f}")


if __name__ == "__main__":
    main()
