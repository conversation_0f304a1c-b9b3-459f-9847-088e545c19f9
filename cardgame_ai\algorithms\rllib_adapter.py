"""
RLlib适配器模块

将现有的模型适配到RLlib的API中，支持分布式训练。
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Union

from ray.rllib.models import ModelV2
from ray.rllib.models.torch.torch_modelv2 import TorchModelV2
from ray.rllib.policy.sample_batch import SampleBatch
from ray.rllib.utils.annotations import override
from ray.rllib.utils.typing import ModelConfigDict, TensorType

from cardgame_ai.algorithms.efficient_zero import EfficientZeroModel


class RLlibEfficientZeroModel(TorchModelV2, nn.Module):
    """
    RLlib兼容的EfficientZero模型
    
    将EfficientZero模型适配到RLlib的API中，支持分布式训练。
    """
    
    def __init__(
        self,
        obs_space,
        action_space,
        num_outputs,
        model_config,
        name,
        **kwargs
    ):
        """
        初始化模型
        
        Args:
            obs_space: 观察空间
            action_space: 动作空间
            num_outputs: 输出维度
            model_config: 模型配置
            name: 模型名称
        """
        TorchModelV2.__init__(
            self, obs_space, action_space, num_outputs, model_config, name
        )
        nn.Module.__init__(self)
        
        # 获取模型配置
        hidden_dim = model_config.get("hidden_dim", 256)
        state_dim = model_config.get("state_dim", 64)
        use_resnet = model_config.get("use_resnet", True)
        projection_dim = model_config.get("projection_dim", 256)
        prediction_dim = model_config.get("prediction_dim", 128)
        value_prefix_length = model_config.get("value_prefix_length", 5)
        
        # 创建EfficientZero模型
        self.efficient_zero_model = EfficientZeroModel(
            observation_shape=obs_space.shape,
            action_shape=(action_space.n,),
            hidden_dim=hidden_dim,
            state_dim=state_dim,
            use_resnet=use_resnet,
            projection_dim=projection_dim,
            prediction_dim=prediction_dim,
            value_prefix_length=value_prefix_length,
            device="cuda" if torch.cuda.is_available() else "cpu"
        )
        
        # 存储值函数输出
        self._value_out = None
        
    @override(TorchModelV2)
    def forward(
        self,
        input_dict: Dict[str, TensorType],
        state: List[TensorType],
        seq_lens: TensorType
    ) -> Tuple[TensorType, List[TensorType]]:
        """
        前向传播
        
        Args:
            input_dict: 输入字典
            state: 隐藏状态
            seq_lens: 序列长度
            
        Returns:
            Tuple[TensorType, List[TensorType]]: 策略输出和新的隐藏状态
        """
        # 获取观察
        obs = input_dict["obs"].float()
        
        # 获取动作掩码（如果有）
        action_mask = input_dict.get("action_mask")
        
        # 使用EfficientZero模型进行推理
        # 1. 表示网络：将观察转换为隐藏状态
        hidden_state = self.efficient_zero_model.representation_network(obs)
        
        # 2. 预测网络：预测策略和价值
        policy_logits, value = self.efficient_zero_model.prediction_network(hidden_state)
        
        # 存储值函数输出
        self._value_out = value
        
        # 如果有动作掩码，应用掩码
        if action_mask is not None:
            # 将非法动作的logits设为一个很小的值
            policy_logits = policy_logits + torch.log(action_mask + 1e-10)
            
        return policy_logits, state
        
    @override(TorchModelV2)
    def value_function(self) -> TensorType:
        """
        值函数
        
        Returns:
            TensorType: 值函数输出
        """
        assert self._value_out is not None, "必须先调用forward()方法"
        return self._value_out.squeeze(-1)
        
    def get_initial_state(self) -> List[np.ndarray]:
        """
        获取初始隐藏状态
        
        Returns:
            List[np.ndarray]: 初始隐藏状态
        """
        return []
        
    def import_from_efficient_zero(self, efficient_zero_model: EfficientZeroModel) -> None:
        """
        从EfficientZero模型导入参数
        
        Args:
            efficient_zero_model: EfficientZero模型
        """
        # 复制表示网络参数
        self.efficient_zero_model.representation_network.load_state_dict(
            efficient_zero_model.representation_network.state_dict()
        )
        
        # 复制预测网络参数
        self.efficient_zero_model.prediction_network.load_state_dict(
            efficient_zero_model.prediction_network.state_dict()
        )
        
        # 复制动态网络参数
        self.efficient_zero_model.dynamics_network.load_state_dict(
            efficient_zero_model.dynamics_network.state_dict()
        )
        
        # 如果有自监督模块，也复制其参数
        if hasattr(efficient_zero_model, "self_supervised_module") and hasattr(self.efficient_zero_model, "self_supervised_module"):
            self.efficient_zero_model.self_supervised_module.load_state_dict(
                efficient_zero_model.self_supervised_module.state_dict()
            )
            
    def export_to_efficient_zero(self) -> EfficientZeroModel:
        """
        导出为EfficientZero模型
        
        Returns:
            EfficientZeroModel: EfficientZero模型
        """
        return self.efficient_zero_model
