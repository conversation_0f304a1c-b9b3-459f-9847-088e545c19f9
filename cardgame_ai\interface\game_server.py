"""
游戏服务器模块

提供游戏服务器功能，连接Web界面和游戏引擎。
"""
import os
import uuid
import json
import time
import logging
from typing import Dict, Any, List, Optional, Tuple

from cardgame_ai.interface.config import InterfaceConfig
from cardgame_ai.games.doudizhu import DouDizhuEnvironment, CardGroup, Card
from cardgame_ai.core.agent import Agent, RandomAgent
from cardgame_ai.algorithms.dqn import DQN
from cardgame_ai.algorithms.ppo import PPO
from cardgame_ai.algorithms.muzero import MuZero
from cardgame_ai.algorithms.efficient_zero import EfficientZero
from cardgame_ai.algorithms.transformer_policy import TransformerPolicy
from cardgame_ai.algorithms.mcts_agent import MCTSAgent
from cardgame_ai.utils.trajectory_collector import TrajectoryCollector


class GameServer:
    """
    游戏服务器类

    管理游戏会话和与游戏引擎的交互。
    """

    def __init__(self, config: InterfaceConfig):
        """
        初始化游戏服务器

        Args:
            config (InterfaceConfig): 界面配置
        """
        self.config = config
        self.games = {}  # 游戏ID -> 游戏会话
        self.logger = logging.getLogger(__name__)

        # 确保数据目录存在
        data_dir = config.get('data_collection.data_dir')
        if data_dir:
            os.makedirs(data_dir, exist_ok=True)

        # 初始化轨迹收集器配置
        self.trajectory_enabled = config.get('data_collection.trajectory.enabled', True)
        if self.trajectory_enabled:
            trajectory_save_dir = config.get('data_collection.trajectory.save_dir', 'data/trajectories')
            os.makedirs(trajectory_save_dir, exist_ok=True)
            self.logger.info(f"轨迹收集已启用，保存目录: {trajectory_save_dir}")

    def create_game(self, game_type: str, player_role: str, ai_model: str) -> str:
        """
        创建游戏会话

        Args:
            game_type (str): 游戏类型（如"doudizhu"）
            player_role (str): 玩家角色（如"landlord"、"farmer"或"random"）
            ai_model (str): AI模型类型

        Returns:
            str: 游戏ID

        Raises:
            ValueError: 如果参数无效
        """
        # 验证参数
        if game_type != "doudizhu":
            raise ValueError(f"不支持的游戏类型: {game_type}")

        if player_role not in ["landlord", "farmer", "random"]:
            raise ValueError(f"无效的玩家角色: {player_role}")

        if ai_model not in self.config.get('game.available_models'):
            raise ValueError(f"无效的AI模型: {ai_model}")

        # 创建游戏ID
        game_id = str(uuid.uuid4())

        # 创建游戏环境
        env = DouDizhuEnvironment()

        # 创建AI代理
        ai_agents = self._create_ai_agents(ai_model, env)

        # 确定玩家角色
        if player_role == "random":
            import random
            player_role = random.choice(["landlord", "farmer"])

        # 创建轨迹收集器
        trajectory_collector = None
        if self.trajectory_enabled:
            trajectory_format = self.config.get('data_collection.trajectory.format', 'json')
            auto_save = self.config.get('data_collection.trajectory.auto_save', True)
            save_interval = self.config.get('data_collection.trajectory.save_interval', 5)
            compress = self.config.get('data_collection.trajectory.compress', False)
            save_dir = self.config.get('data_collection.trajectory.save_dir', 'data/trajectories')

            trajectory_collector = TrajectoryCollector(
                save_dir=save_dir,
                format=trajectory_format,
                auto_save=auto_save,
                save_interval=save_interval,
                compress=compress
            )

            # 初始化游戏信息
            trajectory_collector.add_game_info(
                game_id=game_id,
                game_type=game_type,
                player_role=player_role,
                ai_model=ai_model
            )

            self.logger.info(f"已为游戏 {game_id} 创建轨迹收集器")

        # 创建游戏会话
        self.games[game_id] = {
            "id": game_id,
            "type": game_type,
            "env": env,
            "player_role": player_role,
            "ai_model": ai_model,
            "ai_agents": ai_agents,
            "state": env.reset(),
            "history": [],
            "feedback": [],
            "start_time": time.time(),
            "last_action_time": time.time(),
            "trajectory_collector": trajectory_collector
        }

        self.logger.info(f"创建游戏: {game_id} (类型={game_type}, 角色={player_role}, AI={ai_model})")

        return game_id

    def _create_ai_agents(self, model_type: str, env: DouDizhuEnvironment) -> Dict[str, Agent]:
        """
        创建AI代理

        Args:
            model_type (str): AI模型类型
            env (DouDizhuEnvironment): 游戏环境

        Returns:
            Dict[str, Agent]: 角色 -> AI代理的映射
        """
        agents = {}

        if model_type == "random":
            agents["landlord"] = RandomAgent(env)
            agents["farmer1"] = RandomAgent(env)
            agents["farmer2"] = RandomAgent(env)
        else:
            # 根据模型类型创建对应的代理
            model_classes = {
                "dqn": DQN,
                "ppo": PPO,
                "muzero": MuZero,
                "efficientzero": EfficientZero,
                "transformer-rl": TransformerPolicy,
                "mcts": MCTSAgent
            }

            model_class = model_classes.get(model_type)
            if model_class is None:
                self.logger.warning(f"未知的模型类型: {model_type}，使用随机代理")
                model_class = RandomAgent

            # 加载模型
            model_path = os.path.join("models", model_type)

            try:
                agents["landlord"] = model_class(env)
                agents["landlord"].load(os.path.join(model_path, "landlord"))

                agents["farmer1"] = model_class(env)
                agents["farmer1"].load(os.path.join(model_path, "farmer"))

                agents["farmer2"] = model_class(env)
                agents["farmer2"].load(os.path.join(model_path, "farmer"))
            except Exception as e:
                self.logger.error(f"加载模型失败: {str(e)}，使用随机代理")
                agents["landlord"] = RandomAgent(env)
                agents["farmer1"] = RandomAgent(env)
                agents["farmer2"] = RandomAgent(env)

        return agents

    def game_exists(self, game_id: str) -> bool:
        """
        检查游戏是否存在

        Args:
            game_id (str): 游戏ID

        Returns:
            bool: 游戏是否存在
        """
        return game_id in self.games

    def get_game_state(self, game_id: str) -> Any:
        """
        获取游戏状态

        Args:
            game_id (str): 游戏ID

        Returns:
            Any: 游戏状态

        Raises:
            KeyError: 如果游戏不存在
        """
        if not self.game_exists(game_id):
            raise KeyError(f"游戏不存在: {game_id}")

        return self.games[game_id]["state"]

    def state_to_json(self, game_id: str, state: Any) -> Dict[str, Any]:
        """
        将游戏状态转换为JSON格式

        Args:
            game_id (str): 游戏ID
            state (Any): 游戏状态

        Returns:
            Dict[str, Any]: JSON格式的游戏状态
        """
        game = self.games[game_id]
        env = game["env"]
        player_role = game["player_role"]

        # 获取当前回合的玩家
        current_player = state.current_player

        # 判断是否为玩家回合
        is_player_turn = False
        if (player_role == "landlord" and current_player == "landlord") or \
           (player_role == "farmer" and current_player.startswith("farmer")):
            is_player_turn = True

        # 转换手牌
        hands = {}
        for role, cards in state.hands.items():
            hands[role] = [str(card) for card in cards]

        # 转换公共信息
        public_info = {
            "landlord_cards": [str(card) for card in state.landlord_cards],
            "played_cards": {role: [str(card) for card in cards] for role, cards in state.played_cards.items()},
            "last_move": {role: str(card_group) for role, card_group in state.last_move.items()},
            "current_player": current_player,
            "game_phase": state.game_phase
        }

        # 构建JSON状态
        state_json = {
            "hands": hands,
            "public_info": public_info,
            "player_role": player_role,
            "is_player_turn": is_player_turn,
            "game_over": env.is_terminal(state),
            "winner": state.winner if hasattr(state, "winner") else None,
            "legal_actions": [self.action_to_json(game_id, action) for action in env.get_legal_actions(state)] if is_player_turn else []
        }

        return state_json

    def action_to_json(self, game_id: str, action: Any) -> Dict[str, Any]:
        """
        将动作转换为JSON格式

        Args:
            game_id (str): 游戏ID
            action (Any): 动作

        Returns:
            Dict[str, Any]: JSON格式的动作
        """
        if isinstance(action, CardGroup):
            return {
                "type": action.type.name,
                "cards": [str(card) for card in action.cards],
                "value": action.value,
                "display_text": str(action)
            }
        elif action is None or action == "pass":
            return {
                "type": "PASS",
                "cards": [],
                "value": 0,
                "display_text": "不出"
            }
        else:
            return {
                "type": "UNKNOWN",
                "display_text": str(action)
            }

    def play_action(self, game_id: str, action_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行动作

        Args:
            game_id (str): 游戏ID
            action_data (Dict[str, Any]): 动作数据

        Returns:
            Dict[str, Any]: 动作结果

        Raises:
            KeyError: 如果游戏不存在
            ValueError: 如果动作无效
        """
        if not self.game_exists(game_id):
            raise KeyError(f"游戏不存在: {game_id}")

        game = self.games[game_id]
        env = game["env"]
        state = game["state"]

        # 解析动作
        action = self._parse_action(action_data, env)

        # 执行动作
        try:
            new_state, reward, done, info = env.step(state, action)

            # 更新游戏状态
            game["state"] = new_state
            game["last_action_time"] = time.time()

            # 记录历史
            history_entry = {
                "player": state.current_player,
                "action": self.action_to_json(game_id, action),
                "timestamp": time.time()
            }
            game["history"].append(history_entry)

            # 记录轨迹数据
            if game["trajectory_collector"]:
                game["trajectory_collector"].add_step(
                    state=state,
                    action=action,
                    reward=reward,
                    next_state=new_state,
                    done=done,
                    info=info
                )

            # 如果游戏没有结束且下一个玩家是AI
            ai_results = []
            while not done and not self._is_human_player_turn(game_id):
                ai_result = self._play_ai_turn(game_id)
                ai_results.append(ai_result)

                # 更新状态和结束标志
                state = game["state"]
                done = env.is_terminal(state)

            return {
                "success": True,
                "state": self.state_to_json(game_id, game["state"]),
                "ai_actions": ai_results
            }

        except Exception as e:
            self.logger.error(f"执行动作失败: {str(e)}")
            raise ValueError(f"无效的动作: {str(e)}")

    def _parse_action(self, action_data: Dict[str, Any], env: DouDizhuEnvironment) -> Any:
        """
        解析动作数据

        Args:
            action_data (Dict[str, Any]): 动作数据
            env (DouDizhuEnvironment): 游戏环境

        Returns:
            Any: 解析后的动作

        Raises:
            ValueError: 如果动作数据无效
        """
        action_type = action_data.get("type", "").upper()

        if action_type == "PASS":
            return "pass"

        # 解析卡牌
        cards_str = action_data.get("cards", [])
        if not cards_str:
            raise ValueError("卡牌列表为空")

        cards = [Card.from_string(s) for s in cards_str]

        # 创建卡牌组
        try:
            card_group = CardGroup.from_cards(cards)
            return card_group
        except Exception as e:
            raise ValueError(f"无效的卡牌组合: {str(e)}")

    def _is_human_player_turn(self, game_id: str) -> bool:
        """
        判断是否是人类玩家回合

        Args:
            game_id (str): 游戏ID

        Returns:
            bool: 是否是人类玩家回合
        """
        game = self.games[game_id]
        state = game["state"]
        player_role = game["player_role"]
        current_player = state.current_player

        if player_role == "landlord":
            return current_player == "landlord"
        else:  # player_role == "farmer"
            return current_player.startswith("farmer")

    def _play_ai_turn(self, game_id: str) -> Dict[str, Any]:
        """
        AI回合

        Args:
            game_id (str): 游戏ID

        Returns:
            Dict[str, Any]: AI动作结果
        """
        game = self.games[game_id]
        env = game["env"]
        state = game["state"]
        ai_agents = game["ai_agents"]

        current_player = state.current_player

        # 获取AI代理
        agent = ai_agents[current_player]

        # 获取合法动作
        legal_actions = env.get_legal_actions(state)

        # AI思考时间
        time.sleep(self.config.get('game.ai_thinking_time', 1.0))

        # 选择动作（启用解释模式）
        explanation_data = None
        if hasattr(agent, 'act_with_explanation'):
            # 如果代理支持解释模式
            action, explanation_data = agent.act_with_explanation(state, legal_actions)
        else:
            # 如果代理不支持解释模式，使用普通模式
            action = agent.act(state, legal_actions)

        # 执行动作
        new_state, reward, done, info = env.step(state, action)

        # 更新游戏状态
        game["state"] = new_state
        game["last_action_time"] = time.time()

        # 记录历史
        history_entry = {
            "player": current_player,
            "action": self.action_to_json(game_id, action),
            "timestamp": time.time(),
            "is_ai": True
        }

        # 如果有解释数据，添加到历史记录中
        if explanation_data:
            history_entry["explanation_data"] = explanation_data

        game["history"].append(history_entry)

        # 记录轨迹数据
        if game["trajectory_collector"]:
            game["trajectory_collector"].add_step(
                state=state,
                action=action,
                reward=reward,
                next_state=new_state,
                done=done,
                info=info
            )

        result = {
            "player": current_player,
            "action": self.action_to_json(game_id, action)
        }

        # 如果有解释数据，添加到结果中
        if explanation_data:
            result["explanation_data"] = explanation_data

        return result

    def get_legal_actions(self, game_id: str) -> List[Any]:
        """
        获取合法动作

        Args:
            game_id (str): 游戏ID

        Returns:
            List[Any]: 合法动作列表

        Raises:
            KeyError: 如果游戏不存在
        """
        if not self.game_exists(game_id):
            raise KeyError(f"游戏不存在: {game_id}")

        game = self.games[game_id]
        env = game["env"]
        state = game["state"]

        return env.get_legal_actions(state)

    def get_active_connections(self) -> List[str]:
        """
        获取活跃连接列表

        Returns:
            List[str]: 活跃连接ID列表
        """
        # 这里简化处理，将每个游戏会话视为一个连接
        # 实际应用中可能需要更复杂的连接管理
        return list(self.games.keys())

    def get_game_history(self, game_id: str) -> List[Dict[str, Any]]:
        """
        获取游戏历史

        Args:
            game_id (str): 游戏ID

        Returns:
            List[Dict[str, Any]]: 游戏历史

        Raises:
            KeyError: 如果游戏不存在
        """
        if not self.game_exists(game_id):
            raise KeyError(f"游戏不存在: {game_id}")

        return self.games[game_id]["history"]

    def save_feedback(self, game_id: str, feedback: Dict[str, Any]) -> None:
        """
        保存反馈

        Args:
            game_id (str): 游戏ID
            feedback (Dict[str, Any]): 反馈数据

        Raises:
            KeyError: 如果游戏不存在
        """
        if not self.game_exists(game_id):
            raise KeyError(f"游戏不存在: {game_id}")

        game = self.games[game_id]

        # 添加时间戳
        feedback["timestamp"] = time.time()

        # 保存反馈
        game["feedback"].append(feedback)

        # 如果启用了数据收集
        if self.config.get('data_collection.enabled') and self.config.get('data_collection.save_feedback'):
            data_dir = self.config.get('data_collection.data_dir')
            if data_dir:
                feedback_dir = os.path.join(data_dir, "feedback")
                os.makedirs(feedback_dir, exist_ok=True)

                # 保存反馈
                feedback_file = os.path.join(feedback_dir, f"{game_id}_feedback.json")
                with open(feedback_file, 'w', encoding='utf-8') as f:
                    json.dump(game["feedback"], f, indent=4, ensure_ascii=False)

    def reset_game(self, game_id: str) -> None:
        """
        重置游戏

        Args:
            game_id (str): 游戏ID

        Raises:
            KeyError: 如果游戏不存在
        """
        if not self.game_exists(game_id):
            raise KeyError(f"游戏不存在: {game_id}")

        game = self.games[game_id]
        env = game["env"]

        # 结束当前轨迹收集
        if game.get("trajectory_collector"):
            game["trajectory_collector"].end_game()
            self.logger.info(f"游戏 {game_id} 的轨迹收集已结束")

        # 保存当前游戏数据（如果启用了数据收集）
        if self.config.get('data_collection.enabled') and self.config.get('data_collection.save_games'):
            self._save_game_data(game_id)

        # 重置游戏状态
        game["state"] = env.reset()
        game["history"] = []
        game["last_action_time"] = time.time()

        # 重新创建轨迹收集器
        if self.trajectory_enabled:
            trajectory_format = self.config.get('data_collection.trajectory.format', 'json')
            auto_save = self.config.get('data_collection.trajectory.auto_save', True)
            save_interval = self.config.get('data_collection.trajectory.save_interval', 5)
            compress = self.config.get('data_collection.trajectory.compress', False)
            save_dir = self.config.get('data_collection.trajectory.save_dir', 'data/trajectories')

            trajectory_collector = TrajectoryCollector(
                save_dir=save_dir,
                format=trajectory_format,
                auto_save=auto_save,
                save_interval=save_interval,
                compress=compress
            )

            # 初始化游戏信息
            trajectory_collector.add_game_info(
                game_id=game_id,
                game_type=game["type"],
                player_role=game["player_role"],
                ai_model=game["ai_model"]
            )

            game["trajectory_collector"] = trajectory_collector
            self.logger.info(f"已为重置的游戏 {game_id} 创建新的轨迹收集器")

        self.logger.info(f"重置游戏: {game_id}")

    def end_game(self, game_id: str) -> None:
        """
        结束游戏，保存游戏数据和轨迹

        Args:
            game_id (str): 游戏ID

        Raises:
            KeyError: 如果游戏不存在
        """
        if not self.game_exists(game_id):
            raise KeyError(f"游戏不存在: {game_id}")

        game = self.games[game_id]

        # 结束轨迹收集
        if game.get("trajectory_collector"):
            game["trajectory_collector"].end_game()
            self.logger.info(f"游戏 {game_id} 的轨迹收集已结束")

        # 保存游戏数据
        if self.config.get('data_collection.enabled') and self.config.get('data_collection.save_games'):
            self._save_game_data(game_id)

        self.logger.info(f"游戏 {game_id} 已结束")

    def _save_game_data(self, game_id: str) -> None:
        """
        保存游戏数据

        Args:
            game_id (str): 游戏ID
        """
        game = self.games[game_id]

        data_dir = self.config.get('data_collection.data_dir')
        if not data_dir:
            return

        games_dir = os.path.join(data_dir, "games")
        os.makedirs(games_dir, exist_ok=True)

        # 准备游戏数据
        game_data = {
            "id": game["id"],
            "type": game["type"],
            "player_role": game["player_role"],
            "ai_model": game["ai_model"],
            "start_time": game["start_time"],
            "end_time": time.time(),
            "duration": time.time() - game["start_time"],
            "history": game["history"],
            "feedback": game["feedback"]
        }

        # 保存游戏数据
        game_file = os.path.join(games_dir, f"{game_id}.json")
        with open(game_file, 'w', encoding='utf-8') as f:
            json.dump(game_data, f, indent=4, ensure_ascii=False)

        self.logger.info(f"保存游戏数据: {game_file}")

    def play_cards(self, game_id: str, cards: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        玩家出牌

        Args:
            game_id (str): 游戏ID
            cards (List[Dict[str, Any]]): 要出的牌列表，每张牌包含suit和rank

        Returns:
            Dict[str, Any]: 出牌结果

        Raises:
            KeyError: 如果游戏不存在
            ValueError: 如果不是玩家回合或者出牌不合法
        """
        if not self.game_exists(game_id):
            raise KeyError(f"游戏不存在: {game_id}")

        game = self.games[game_id]
        env = game["env"]
        state = game["state"]

        # 检查是否是玩家回合
        if not self._is_human_player_turn(game_id):
            raise ValueError("不是玩家回合")

        # 将传入的卡牌转换为游戏引擎可识别的格式
        game_cards = []
        for card_data in cards:
            suit = card_data.get('suit', '')
            rank = card_data.get('rank', '')

            if not suit or not rank:
                continue

            card = Card(suit, rank)
            game_cards.append(card)

        # 创建卡牌组
        card_group = CardGroup(game_cards)

        # 检查出牌是否合法
        if not env.is_valid_action(card_group, state):
            raise ValueError("出牌不合法")

        # 执行出牌
        new_state, reward, done, info = env.step(card_group)

        # 更新游戏会话
        game["state"] = new_state
        game["last_action_time"] = time.time()

        # 记录历史
        game["history"].append({
            "player": "player",
            "action": {"type": "play", "cards": cards},
            "timestamp": time.time()
        })

        # 记录轨迹数据
        if game.get("trajectory_collector"):
            game["trajectory_collector"].add_step(
                state=state,
                action=card_group,
                reward=reward,
                next_state=new_state,
                done=done,
                info=info
            )

        result = {
            "action": "play",
            "cards": cards,
            "valid": True,
            "done": done
        }

        # 如果游戏没有结束且轮到AI，执行AI回合
        if not done and not self._is_human_player_turn(game_id):
            ai_result = self._play_ai_turn(game_id)
            result["ai_action"] = ai_result

        return result

    def pass_cards(self, game_id: str) -> Dict[str, Any]:
        """
        玩家不出牌

        Args:
            game_id (str): 游戏ID

        Returns:
            Dict[str, Any]: 操作结果

        Raises:
            KeyError: 如果游戏不存在
            ValueError: 如果不是玩家回合或者不能不出
        """
        if not self.game_exists(game_id):
            raise KeyError(f"游戏不存在: {game_id}")

        game = self.games[game_id]
        env = game["env"]
        state = game["state"]

        # 检查是否是玩家回合
        if not self._is_human_player_turn(game_id):
            raise ValueError("不是玩家回合")

        # 检查是否可以不出
        if not env.can_pass(state):
            raise ValueError("当前不能不出")

        # 执行不出
        new_state, reward, done, info = env.step(None)  # None表示不出

        # 更新游戏会话
        game["state"] = new_state
        game["last_action_time"] = time.time()

        # 记录历史
        game["history"].append({
            "player": "player",
            "action": {"type": "pass"},
            "timestamp": time.time()
        })

        # 记录轨迹数据
        if game.get("trajectory_collector"):
            game["trajectory_collector"].add_step(
                state=state,
                action=None,  # None表示不出
                reward=reward,
                next_state=new_state,
                done=done,
                info=info
            )

        result = {
            "action": "pass",
            "valid": True,
            "done": done
        }

        # 如果游戏没有结束且轮到AI，执行AI回合
        if not done and not self._is_human_player_turn(game_id):
            ai_result = self._play_ai_turn(game_id)
            result["ai_action"] = ai_result

        return result

    def get_hint(self, game_id: str) -> List[Dict[str, Any]]:
        """
        获取出牌提示

        Args:
            game_id (str): 游戏ID

        Returns:
            List[Dict[str, Any]]: 建议出的牌列表

        Raises:
            KeyError: 如果游戏不存在
            ValueError: 如果不是玩家回合
        """
        if not self.game_exists(game_id):
            raise KeyError(f"游戏不存在: {game_id}")

        game = self.games[game_id]
        env = game["env"]
        state = game["state"]

        # 检查是否是玩家回合
        if not self._is_human_player_turn(game_id):
            raise ValueError("不是玩家回合")

        # 获取合法动作
        legal_actions = env.get_legal_actions(state)

        # 如果没有合法动作，返回空列表
        if not legal_actions or len(legal_actions) == 0:
            return []

        # 选择第一个不是"不出"的合法动作作为提示
        hint_action = None
        for action in legal_actions:
            if action is not None:  # None表示不出
                hint_action = action
                break

        # 如果没有找到合适的提示（所有合法动作都是"不出"），返回空列表
        if hint_action is None:
            return []

        # 将提示动作转换为卡牌列表
        hint_cards = []
        for card in hint_action.cards:
            hint_cards.append({
                "suit": card.suit,
                "rank": card.rank,
                "is_joker": card.is_joker,
                "color": card.color if card.is_joker else None
            })

        return hint_cards