"""
神经网络决策组件模块

提供基于神经网络的决策能力。
"""
import time
import logging
import random
import numpy as np
from typing import Dict, List, Any, Optional, Union, Tuple

from cardgame_ai.core.base import State, Action
from cardgame_ai.algorithms.components.base_component import DecisionComponent


class NeuralNetworkComponent(DecisionComponent):
    """
    神经网络决策组件

    封装神经网络模型，提供基于神经网络的决策能力。
    """

    def __init__(self, model: Any, temperature: float = 1.0):
        """
        初始化神经网络组件

        Args:
            model: 神经网络模型
            temperature: 温度参数，控制探索程度
        """
        super().__init__("neural_network")
        self.model = model
        self.temperature = temperature

        # 添加特定统计信息
        self.stats.update({
            "avg_value": 0.0,
            "avg_policy_entropy": 0.0
        })

    def decide(self, state: State, legal_actions: List[Action], explain: bool = False) -> Union[Action, Tuple[Action, Dict[str, Any]]]:
        """
        使用神经网络做出决策

        Args:
            state: 当前状态
            legal_actions: 合法动作列表
            explain: 是否启用解释模式

        Returns:
            如果explain=False，返回选择的动作
            如果explain=True，返回(选择的动作, 解释数据)元组
        """
        start_time = time.time()

        try:
            # 预处理状态
            processed_state = self._preprocess_state(state)

            # 获取神经网络预测
            policy_logits, value = self.model.predict(processed_state)

            # 创建动作掩码
            action_mask = self._create_action_mask(legal_actions)

            # 应用动作掩码
            masked_logits = policy_logits * action_mask

            # 应用温度参数
            if self.temperature > 0:
                masked_logits = masked_logits / self.temperature

            # 计算策略
            policy = self._compute_policy(masked_logits)

            # 计算熵
            entropy = self._compute_entropy(policy)

            # 选择动作
            action_idx = self._select_action(policy)
            action = legal_actions[action_idx]

            # 更新统计信息
            time_spent = time.time() - start_time
            self.update_stats(time_spent)
            self._update_specific_stats(value, entropy)

            # 如果启用解释模式，返回解释数据
            if explain:
                explanation_data = self._generate_explanation(
                    state, legal_actions, policy_logits, value, policy, action_idx
                )
                return action, explanation_data
            else:
                return action

        except Exception as e:
            logging.error(f"Neural network decision error: {e}")
            time_spent = time.time() - start_time
            self.update_stats(time_spent, success=False)

            # 出错时返回随机动作
            action = random.choice(legal_actions) if legal_actions else None
            
            if explain:
                explanation_data = {
                    "error": str(e),
                    "component_name": self.name,
                    "success": False
                }
                return action, explanation_data
            else:
                return action

    def _preprocess_state(self, state: State) -> np.ndarray:
        """
        预处理状态

        Args:
            state: 当前状态

        Returns:
            预处理后的状态
        """
        # 实际实现应根据具体模型需求进行预处理
        # 这里仅为示例
        return np.array(state.to_dict())

    def _create_action_mask(self, legal_actions: List[Action]) -> np.ndarray:
        """
        创建动作掩码

        Args:
            legal_actions: 合法动作列表

        Returns:
            动作掩码
        """
        # 实际实现应根据具体模型需求创建掩码
        # 这里仅为示例
        action_mask = np.zeros(len(legal_actions))
        for i in range(len(legal_actions)):
            action_mask[i] = 1.0
        return action_mask

    def _compute_policy(self, logits: np.ndarray) -> np.ndarray:
        """
        计算策略

        Args:
            logits: 策略logits

        Returns:
            策略概率分布
        """
        # 使用softmax计算策略
        exp_logits = np.exp(logits - np.max(logits))
        policy = exp_logits / np.sum(exp_logits)
        return policy

    def _compute_entropy(self, policy: np.ndarray) -> float:
        """
        计算策略熵

        Args:
            policy: 策略概率分布

        Returns:
            策略熵
        """
        # 计算策略熵
        entropy = -np.sum(policy * np.log(policy + 1e-10))
        return entropy

    def _select_action(self, policy: np.ndarray) -> int:
        """
        根据策略选择动作

        Args:
            policy: 策略概率分布

        Returns:
            选择的动作索引
        """
        # 根据策略采样动作
        action_idx = np.random.choice(len(policy), p=policy)
        return action_idx

    def _update_specific_stats(self, value: float, entropy: float):
        """
        更新特定统计信息

        Args:
            value: 价值估计
            entropy: 策略熵
        """
        # 更新平均价值
        self.stats["avg_value"] = (self.stats["avg_value"] * (self.stats["calls"] - 1) + value) / self.stats["calls"]

        # 更新平均策略熵
        if "avg_policy_entropy" in self.stats:
            self.stats["avg_policy_entropy"] = (self.stats["avg_policy_entropy"] * (self.stats["calls"] - 1) + entropy) / self.stats["calls"]
        else:
            self.stats["avg_policy_entropy"] = entropy

    def _generate_explanation(
        self, state: State, legal_actions: List[Action], 
        policy_logits: np.ndarray, value: float, 
        policy: np.ndarray, selected_action_idx: int
    ) -> Dict[str, Any]:
        """
        生成解释数据

        Args:
            state: 当前状态
            legal_actions: 合法动作列表
            policy_logits: 策略logits
            value: 价值估计
            policy: 策略概率分布
            selected_action_idx: 选择的动作索引

        Returns:
            解释数据
        """
        # 获取基础解释数据
        explanation_data = self.get_explanation_data()

        # 添加神经网络特定解释数据
        explanation_data.update({
            "value_estimate": float(value),
            "policy_entropy": float(self._compute_entropy(policy)),
            "selected_action": {
                "index": int(selected_action_idx),
                "probability": float(policy[selected_action_idx])
            },
            "top_actions": self._get_top_actions(legal_actions, policy),
            "feature_importance": self._get_feature_importance(state),
            "confidence": float(policy[selected_action_idx])
        })

        return explanation_data

    def _get_top_actions(self, legal_actions: List[Action], policy: np.ndarray, top_k: int = 5) -> List[Dict[str, Any]]:
        """
        获取概率最高的前K个动作

        Args:
            legal_actions: 合法动作列表
            policy: 策略概率分布
            top_k: 返回的动作数量

        Returns:
            前K个动作的信息
        """
        # 获取概率最高的前K个动作
        top_indices = np.argsort(policy)[::-1][:top_k]
        top_actions = []

        for idx in top_indices:
            top_actions.append({
                "action": str(legal_actions[idx]),
                "probability": float(policy[idx])
            })

        return top_actions

    def _get_feature_importance(self, state: State) -> Dict[str, float]:
        """
        获取特征重要性

        Args:
            state: 当前状态

        Returns:
            特征重要性
        """
        # 实际实现应根据具体模型提供特征重要性
        # 这里仅为示例
        return {
            "hand_cards": 0.8,
            "played_cards": 0.6,
            "remaining_cards": 0.4,
            "position": 0.3
        }
