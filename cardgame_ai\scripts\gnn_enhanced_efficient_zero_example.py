#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
GNN增强版EfficientZero示例脚本

展示如何使用GNN增强版EfficientZero模型进行训练和推理。
"""

import os
import sys
import argparse
import logging
import numpy as np
import torch
from typing import Dict, List, Any, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from cardgame_ai.models.gnn_enhanced_efficient_zero import GNNEnhancedEfficientZeroModel
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.games.doudizhu.state import DouDizhuState

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='GNN增强版EfficientZero示例')

    parser.add_argument('--state_dim', type=int, default=64,
                        help='状态维度')
    parser.add_argument('--hidden_dim', type=int, default=256,
                        help='隐藏层维度')
    parser.add_argument('--node_feature_dim', type=int, default=19,
                        help='节点特征维度')
    parser.add_argument('--gnn_hidden_dim', type=int, default=128,
                        help='GNN隐藏层维度')
    parser.add_argument('--gnn_output_dim', type=int, default=64,
                        help='GNN输出维度')
    parser.add_argument('--gnn_type', type=str, default='gcn',
                        help='GNN类型，可选值为gcn, gat, sage')
    parser.add_argument('--gnn_layers', type=int, default=2,
                        help='GNN层数')
    parser.add_argument('--use_belief_state', action='store_true',
                        help='是否使用信念状态')
    parser.add_argument('--seed', type=int, default=42,
                        help='随机种子')

    return parser.parse_args()


def main():
    """主函数"""
    args = parse_args()

    # 设置随机种子
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)

    # 创建环境
    env = DouDizhuEnvironment()

    # 获取观察和动作空间
    observation_shape = env.observation_space.shape
    action_shape = (env.action_space.n,)

    # 创建GNN增强版EfficientZero模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = GNNEnhancedEfficientZeroModel(
        observation_shape=observation_shape,
        action_shape=action_shape,
        hidden_dim=args.hidden_dim,
        state_dim=args.state_dim,
        node_feature_dim=args.node_feature_dim,
        gnn_hidden_dim=args.gnn_hidden_dim,
        gnn_output_dim=args.gnn_output_dim,
        gnn_type=args.gnn_type,
        gnn_layers=args.gnn_layers,
        use_belief_state=args.use_belief_state,
        device=device
    )

    logger.info(f"创建GNN增强版EfficientZero模型: {model.__class__.__name__}")
    logger.info(f"模型参数: state_dim={args.state_dim}, hidden_dim={args.hidden_dim}")
    logger.info(f"GNN参数: node_feature_dim={args.node_feature_dim}, gnn_hidden_dim={args.gnn_hidden_dim}, gnn_output_dim={args.gnn_output_dim}")
    logger.info(f"GNN类型: {args.gnn_type}, GNN层数: {args.gnn_layers}")
    logger.info(f"使用信念状态: {args.use_belief_state}")

    # 重置环境
    state = env.reset()

    # 创建虚拟观察
    dummy_observation = torch.zeros(1, *observation_shape).to(device)

    # 测试表示函数（不使用GNN）
    logger.info("测试表示函数（不使用GNN）...")
    original_state = model.representation_network(dummy_observation)
    logger.info(f"原始表示形状: {original_state.shape}")

    # 测试表示函数（使用GNN）
    logger.info("测试表示函数（使用GNN）...")
    enhanced_state = model.represent(dummy_observation, state, player_id=0)
    logger.info(f"增强表示形状: {enhanced_state.shape}")

    # 测试预测函数
    logger.info("测试预测函数...")
    policy_logits, value = model.prediction_network(enhanced_state)
    logger.info(f"策略输出形状: {policy_logits.shape}")
    logger.info(f"价值输出形状: {value.shape}")

    # 测试动态函数
    logger.info("测试动态函数...")
    action = torch.zeros(1, 1, dtype=torch.long).to(device)
    next_state, value_prefix = model.dynamics(enhanced_state, action)
    logger.info(f"下一个状态形状: {next_state.shape}")
    logger.info(f"值前缀形状: {value_prefix.shape}")

    # 计算模型参数数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)

    logger.info(f"模型总参数数量: {total_params:,}")
    logger.info(f"可训练参数数量: {trainable_params:,}")

    # 计算GNN编码器参数数量
    gnn_params = sum(p.numel() for p in model.gnn_encoder.parameters())
    logger.info(f"GNN编码器参数数量: {gnn_params:,} ({gnn_params / total_params:.2%})")

    return 0


if __name__ == "__main__":
    main()
