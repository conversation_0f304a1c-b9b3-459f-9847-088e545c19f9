"""
残局特化处理模块

为特定类型的关键残局（如王炸处理、单张控制等）提供专门的决策模块。
"""

import logging
import random
import time
from typing import Dict, List, Optional, Tuple, Any, Union

from cardgame_ai.core.base import State, Action
from cardgame_ai.games.doudizhu.state import <PERSON>u<PERSON><PERSON><PERSON>State
from cardgame_ai.games.doudizhu.card import Card, CardRank
from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType
from cardgame_ai.algorithms.endgame_types import EndgameType, get_endgame_type, is_endgame
from cardgame_ai.algorithms.components.base_component import DecisionComponent


class EndgameSpecialist(DecisionComponent):
    """
    残局特化处理组件

    为特定类型的关键残局提供专门的决策逻辑。
    """

    def __init__(self):
        """初始化残局特化处理组件"""
        super().__init__("endgame_specialist")

        # 添加特定统计信息
        self.stats.update({
            "endgame_decisions": 0,
            "endgame_types_used": {},
            "success_rate_by_type": {}
        })

        # 注册各种残局处理器
        self.handlers = {
            EndgameType.KING_BOMB: self._handle_king_bomb,
            EndgameType.SINGLE_CARD_CONTROL: self._handle_single_card_control,
            EndgameType.BOMB_ENDGAME: self._handle_bomb_endgame,
            EndgameType.STRAIGHT_CONTROL: self._handle_straight_control,
            EndgameType.PAIR_CONTROL: self._handle_pair_control,
            EndgameType.TRIO_CONTROL: self._handle_trio_control,
            EndgameType.AIRPLANE_CONTROL: self._handle_airplane_control,
            EndgameType.FINAL_ATTACK: self._handle_final_attack,
            EndgameType.GENERAL_ENDGAME: self._handle_general_endgame
        }

    def decide(self, state: State, legal_actions: List[Action], explain: bool = False) -> Union[Action, Tuple[Action, Dict[str, Any]]]:
        """
        为残局做出决策

        Args:
            state: 当前游戏状态
            legal_actions: 合法动作列表
            explain: 是否启用解释模式

        Returns:
            如果explain=False，返回选择的动作
            如果explain=True，返回(选择的动作, 解释数据)元组
        """
        start_time = time.time()

        # 检查是否是残局
        if not is_endgame(state):
            # 不是残局，随机选择一个动作
            action = random.choice(legal_actions) if legal_actions else None
            time_spent = time.time() - start_time
            self.update_stats(time_spent, success=False)

            if explain:
                explanation = {
                    "component": self.name,
                    "is_endgame": False,
                    "message": "不是残局，无法使用残局特化处理"
                }
                return action, explanation
            return action

        # 获取残局类型
        endgame_type = get_endgame_type(state)

        # 更新统计信息
        self.stats["endgame_decisions"] += 1
        if endgame_type.name not in self.stats["endgame_types_used"]:
            self.stats["endgame_types_used"][endgame_type.name] = 0
        self.stats["endgame_types_used"][endgame_type.name] += 1

        # 使用对应的处理器处理残局
        if endgame_type in self.handlers:
            handler = self.handlers[endgame_type]
            result = handler(state, legal_actions)

            # 如果处理器返回None，则随机选择一个动作
            if result is None:
                action = random.choice(legal_actions) if legal_actions else None
            else:
                action, confidence = result

                # 更新成功率统计
                if endgame_type.name not in self.stats["success_rate_by_type"]:
                    self.stats["success_rate_by_type"][endgame_type.name] = {
                        "attempts": 0,
                        "successes": 0
                    }

                self.stats["success_rate_by_type"][endgame_type.name]["attempts"] += 1
                if confidence > 0.5:  # 如果置信度大于0.5，认为是成功的决策
                    self.stats["success_rate_by_type"][endgame_type.name]["successes"] += 1
        else:
            # 没有对应的处理器，随机选择一个动作
            action = random.choice(legal_actions) if legal_actions else None

        # 更新统计信息
        time_spent = time.time() - start_time
        self.update_stats(time_spent)

        # 如果需要解释，返回解释数据
        if explain:
            explanation = {
                "component": self.name,
                "is_endgame": True,
                "endgame_type": endgame_type.name,
                "confidence": getattr(result, "confidence", 0.5) if result else 0.0,
                "time_spent": time_spent
            }
            return action, explanation

        return action

    def _handle_king_bomb(self, state: State, legal_actions: List[Action]) -> Optional[Tuple[Action, float]]:
        """
        处理王炸残局

        Args:
            state: 当前游戏状态
            legal_actions: 合法动作列表

        Returns:
            Optional[Tuple[Action, float]]: (选择的动作, 置信度)，如果无法处理则返回None
        """
        if not isinstance(state, DouDizhuState):
            return None

        # 获取当前玩家的手牌
        current_player = state.current_player
        hand = state.hands[current_player]

        # 检查是否有大小王
        has_small_joker = False
        has_big_joker = False
        small_joker = None
        big_joker = None

        for card in hand:
            if card.rank == CardRank.SMALL_JOKER:
                has_small_joker = True
                small_joker = card
            elif card.rank == CardRank.BIG_JOKER:
                has_big_joker = True
                big_joker = card

        # 如果有大小王
        if has_small_joker and has_big_joker:
            # 检查是否有王炸动作
            for action in legal_actions:
                if isinstance(action, CardGroup) and action.card_type == CardGroupType.ROCKET:
                    # 如果是最后两张牌，直接出王炸
                    if len(hand) == 2:
                        return action, 1.0

                    # 如果对手只剩下很少的牌，也出王炸
                    other_players = [i for i in range(len(state.hands)) if i != current_player]
                    for other_player in other_players:
                        if 0 < len(state.hands[other_player]) <= 4:
                            return action, 0.9

                    # 如果上家出的是炸弹，出王炸
                    if state.last_move and state.last_move.card_type == CardGroupType.BOMB:
                        return action, 0.8

                    # 其他情况，保留王炸
                    return None

        return None

    def _handle_single_card_control(self, state: State, legal_actions: List[Action]) -> Optional[Tuple[Action, float]]:
        """
        处理单张控制残局

        Args:
            state: 当前游戏状态
            legal_actions: 合法动作列表

        Returns:
            Optional[Tuple[Action, float]]: (选择的动作, 置信度)，如果无法处理则返回None
        """
        if not isinstance(state, DouDizhuState):
            return None

        # 获取当前玩家的手牌
        current_player = state.current_player
        hand = state.hands[current_player]

        # 如果手牌数量很少，且主要是单张
        if len(hand) <= 5:
            # 统计单张牌的数量
            rank_count = {}

            for card in hand:
                if card.rank not in rank_count:
                    rank_count[card.rank] = 0
                rank_count[card.rank] += 1

            # 计算单张牌的比例
            single_count = sum(1 for count in rank_count.values() if count == 1)
            single_ratio = single_count / len(hand) if hand else 0

            # 如果单张牌比例超过60%
            if single_ratio >= 0.6:
                # 如果是自己的回合（没有上家出牌）
                if not state.last_move or state.last_player != current_player:
                    # 找出最小的单张牌
                    singles = [card for card in hand if rank_count[card.rank] == 1]
                    if singles:
                        min_single = min(singles, key=lambda card: card.rank.value)

                        # 找出对应的动作
                        for action in legal_actions:
                            if isinstance(action, CardGroup) and action.card_type == CardGroupType.SINGLE:
                                if len(action.cards) == 1 and action.cards[0].rank == min_single.rank:
                                    return action, 0.8

                # 如果上家出的是单张
                elif state.last_move and state.last_move.card_type == CardGroupType.SINGLE:
                    # 找出比上家大的最小单张
                    last_rank = state.last_move.cards[0].rank
                    singles = [card for card in hand if rank_count[card.rank] == 1 and card.rank.value > last_rank.value]

                    if singles:
                        min_single = min(singles, key=lambda card: card.rank.value)

                        # 找出对应的动作
                        for action in legal_actions:
                            if isinstance(action, CardGroup) and action.card_type == CardGroupType.SINGLE:
                                if len(action.cards) == 1 and action.cards[0].rank == min_single.rank:
                                    return action, 0.8

        return None

    def _handle_bomb_endgame(self, state: State, legal_actions: List[Action]) -> Optional[Tuple[Action, float]]:
        """
        处理炸弹残局

        Args:
            state: 当前游戏状态
            legal_actions: 合法动作列表

        Returns:
            Optional[Tuple[Action, float]]: (选择的动作, 置信度)，如果无法处理则返回None
        """
        if not isinstance(state, DouDizhuState):
            return None

        # 获取当前玩家的手牌
        current_player = state.current_player
        hand = state.hands[current_player]

        # 统计手牌中的炸弹
        rank_count = {}
        for card in hand:
            if card.rank not in rank_count:
                rank_count[card.rank] = 0
            rank_count[card.rank] += 1

        bombs = [rank for rank, count in rank_count.items() if count == 4]

        # 如果有炸弹
        if bombs:
            # 如果是最后4张牌，且是炸弹，直接出炸弹
            if len(hand) == 4 and len(bombs) == 1:
                for action in legal_actions:
                    if isinstance(action, CardGroup) and action.card_type == CardGroupType.BOMB:
                        return action, 1.0

            # 如果对手只剩下很少的牌，考虑出炸弹
            other_players = [i for i in range(len(state.hands)) if i != current_player]
            for other_player in other_players:
                if 0 < len(state.hands[other_player]) <= 4:
                    # 如果上家出的牌很强，出炸弹
                    if state.last_move and state.last_move.card_type in [CardGroupType.BOMB, CardGroupType.STRAIGHT, CardGroupType.PAIR_STRAIGHT]:
                        for action in legal_actions:
                            if isinstance(action, CardGroup) and action.card_type == CardGroupType.BOMB:
                                return action, 0.9

            # 如果上家出的是炸弹，考虑出更大的炸弹
            if state.last_move and state.last_move.card_type == CardGroupType.BOMB:
                last_bomb_rank = state.last_move.cards[0].rank
                bigger_bombs = [rank for rank in bombs if rank.value > last_bomb_rank.value]

                if bigger_bombs:
                    min_bigger_bomb = min(bigger_bombs, key=lambda r: r.value)

                    for action in legal_actions:
                        if isinstance(action, CardGroup) and action.card_type == CardGroupType.BOMB:
                            if action.cards[0].rank == min_bigger_bomb:
                                return action, 0.8

        return None

    def _handle_final_attack(self, state: State, legal_actions: List[Action]) -> Optional[Tuple[Action, float]]:
        """
        处理最后攻击残局

        Args:
            state: 当前游戏状态
            legal_actions: 合法动作列表

        Returns:
            Optional[Tuple[Action, float]]: (选择的动作, 置信度)，如果无法处理则返回None
        """
        if not isinstance(state, DouDizhuState):
            return None

        # 获取当前玩家的手牌
        current_player = state.current_player
        hand = state.hands[current_player]

        # 如果手牌数量很少（小于等于3张）
        if 0 < len(hand) <= 3:
            # 如果是自己的回合（没有上家出牌）
            if not state.last_move or state.last_player != current_player:
                # 如果只有一张牌，直接出
                if len(hand) == 1:
                    for action in legal_actions:
                        if isinstance(action, CardGroup) and action.card_type == CardGroupType.SINGLE:
                            return action, 1.0

                # 如果有两张相同的牌，出对子
                if len(hand) == 2:
                    if hand[0].rank == hand[1].rank:
                        for action in legal_actions:
                            if isinstance(action, CardGroup) and action.card_type == CardGroupType.PAIR:
                                return action, 1.0
                    else:
                        # 出最小的单张
                        min_card = min(hand, key=lambda card: card.rank.value)
                        for action in legal_actions:
                            if isinstance(action, CardGroup) and action.card_type == CardGroupType.SINGLE:
                                if len(action.cards) == 1 and action.cards[0].rank == min_card.rank:
                                    return action, 0.9

                # 如果有三张相同的牌，出三张
                if len(hand) == 3:
                    if hand[0].rank == hand[1].rank == hand[2].rank:
                        for action in legal_actions:
                            if isinstance(action, CardGroup) and action.card_type == CardGroupType.TRIO:
                                return action, 1.0

            # 如果上家出牌，尝试出比上家大的牌
            elif state.last_move:
                # 如果上家出的是单张
                if state.last_move.card_type == CardGroupType.SINGLE:
                    # 找出比上家大的最小单张
                    last_rank = state.last_move.cards[0].rank
                    bigger_cards = [card for card in hand if card.rank.value > last_rank.value]

                    if bigger_cards:
                        min_bigger_card = min(bigger_cards, key=lambda card: card.rank.value)
                        for action in legal_actions:
                            if isinstance(action, CardGroup) and action.card_type == CardGroupType.SINGLE:
                                if len(action.cards) == 1 and action.cards[0].rank == min_bigger_card.rank:
                                    return action, 0.9

                # 如果上家出的是对子，且我们有更大的对子
                if state.last_move.card_type == CardGroupType.PAIR and len(hand) >= 2:
                    # 统计手牌
                    rank_count = {}
                    for card in hand:
                        if card.rank not in rank_count:
                            rank_count[card.rank] = 0
                        rank_count[card.rank] += 1

                    # 找出所有对子
                    pairs = [rank for rank, count in rank_count.items() if count >= 2]

                    if pairs:
                        # 找出比上家大的最小对子
                        last_pair_rank = state.last_move.cards[0].rank
                        bigger_pairs = [rank for rank in pairs if rank.value > last_pair_rank.value]

                        if bigger_pairs:
                            min_bigger_pair = min(bigger_pairs, key=lambda r: r.value)
                            for action in legal_actions:
                                if isinstance(action, CardGroup) and action.card_type == CardGroupType.PAIR:
                                    if action.cards[0].rank == min_bigger_pair:
                                        return action, 0.9

        return None

    def _handle_straight_control(self, state: State, legal_actions: List[Action]) -> Optional[Tuple[Action, float]]:
        """
        处理顺子控制残局

        Args:
            state: 当前游戏状态
            legal_actions: 合法动作列表

        Returns:
            Optional[Tuple[Action, float]]: (选择的动作, 置信度)，如果无法处理则返回None
        """
        if not isinstance(state, DouDizhuState):
            return None

        # 如果是自己的回合（没有上家出牌）
        if not state.last_move or state.last_player != state.current_player:
            # 找出所有顺子动作
            straight_actions = [action for action in legal_actions
                               if isinstance(action, CardGroup) and action.card_type == CardGroupType.STRAIGHT]

            if straight_actions:
                # 选择最长的顺子
                longest_straight = max(straight_actions, key=lambda action: len(action.cards))
                return longest_straight, 0.8

        # 如果上家出的是顺子
        elif state.last_move and state.last_move.card_type == CardGroupType.STRAIGHT:
            # 找出所有能打过上家顺子的顺子动作
            valid_straight_actions = [action for action in legal_actions
                                     if isinstance(action, CardGroup) and action.card_type == CardGroupType.STRAIGHT
                                     and len(action.cards) == len(state.last_move.cards)]

            if valid_straight_actions:
                # 选择最小的顺子
                min_straight = min(valid_straight_actions,
                                  key=lambda action: action.cards[0].rank.value)
                return min_straight, 0.7

        return None

    def _handle_pair_control(self, state: State, legal_actions: List[Action]) -> Optional[Tuple[Action, float]]:
        """
        处理对子控制残局

        Args:
            state: 当前游戏状态
            legal_actions: 合法动作列表

        Returns:
            Optional[Tuple[Action, float]]: (选择的动作, 置信度)，如果无法处理则返回None
        """
        if not isinstance(state, DouDizhuState):
            return None

        # 如果是自己的回合（没有上家出牌）
        if not state.last_move or state.last_player != state.current_player:
            # 找出所有对子动作
            pair_actions = [action for action in legal_actions
                           if isinstance(action, CardGroup) and action.card_type == CardGroupType.PAIR]

            if pair_actions:
                # 选择最小的对子
                min_pair = min(pair_actions, key=lambda action: action.cards[0].rank.value)
                return min_pair, 0.8

        # 如果上家出的是对子
        elif state.last_move and state.last_move.card_type == CardGroupType.PAIR:
            # 找出所有能打过上家对子的对子动作
            valid_pair_actions = [action for action in legal_actions
                                 if isinstance(action, CardGroup) and action.card_type == CardGroupType.PAIR]

            if valid_pair_actions:
                # 选择最小的对子
                min_pair = min(valid_pair_actions, key=lambda action: action.cards[0].rank.value)
                return min_pair, 0.7

        return None

    def _handle_trio_control(self, state: State, legal_actions: List[Action]) -> Optional[Tuple[Action, float]]:
        """
        处理三张控制残局

        Args:
            state: 当前游戏状态
            legal_actions: 合法动作列表

        Returns:
            Optional[Tuple[Action, float]]: (选择的动作, 置信度)，如果无法处理则返回None
        """
        if not isinstance(state, DouDizhuState):
            return None

        # 如果是自己的回合（没有上家出牌）
        if not state.last_move or state.last_player != state.current_player:
            # 找出所有三张动作
            trio_actions = [action for action in legal_actions
                           if isinstance(action, CardGroup) and action.card_type == CardGroupType.TRIO]

            if trio_actions:
                # 选择最小的三张
                min_trio = min(trio_actions, key=lambda action: action.cards[0].rank.value)
                return min_trio, 0.8

        # 如果上家出的是三张
        elif state.last_move and state.last_move.card_type == CardGroupType.TRIO:
            # 找出所有能打过上家三张的三张动作
            valid_trio_actions = [action for action in legal_actions
                                 if isinstance(action, CardGroup) and action.card_type == CardGroupType.TRIO]

            if valid_trio_actions:
                # 选择最小的三张
                min_trio = min(valid_trio_actions, key=lambda action: action.cards[0].rank.value)
                return min_trio, 0.7

        return None

    def _handle_airplane_control(self, state: State, legal_actions: List[Action]) -> Optional[Tuple[Action, float]]:
        """
        处理飞机控制残局

        Args:
            state: 当前游戏状态
            legal_actions: 合法动作列表

        Returns:
            Optional[Tuple[Action, float]]: (选择的动作, 置信度)，如果无法处理则返回None
        """
        if not isinstance(state, DouDizhuState):
            return None

        # 如果是自己的回合（没有上家出牌）
        if not state.last_move or state.last_player != state.current_player:
            # 找出所有飞机动作
            airplane_actions = [action for action in legal_actions
                               if isinstance(action, CardGroup) and action.card_type == CardGroupType.AIRPLANE]

            if airplane_actions:
                # 选择最长的飞机
                longest_airplane = max(airplane_actions, key=lambda action: len(action.cards))
                return longest_airplane, 0.8

        # 如果上家出的是飞机
        elif state.last_move and state.last_move.card_type == CardGroupType.AIRPLANE:
            # 找出所有能打过上家飞机的飞机动作
            valid_airplane_actions = [action for action in legal_actions
                                     if isinstance(action, CardGroup) and action.card_type == CardGroupType.AIRPLANE
                                     and len(action.cards) == len(state.last_move.cards)]

            if valid_airplane_actions:
                # 选择最小的飞机
                min_airplane = min(valid_airplane_actions,
                                  key=lambda action: action.cards[0].rank.value)
                return min_airplane, 0.7

        return None

    def _handle_general_endgame(self, state: State, legal_actions: List[Action]) -> Optional[Tuple[Action, float]]:
        """
        处理一般残局

        Args:
            state: 当前游戏状态
            legal_actions: 合法动作列表

        Returns:
            Optional[Tuple[Action, float]]: (选择的动作, 置信度)，如果无法处理则返回None
        """
        if not isinstance(state, DouDizhuState):
            return None

        # 获取当前玩家的手牌
        current_player = state.current_player
        hand = state.hands[current_player]

        # 如果手牌数量很少
        if len(hand) <= 8:
            # 如果是自己的回合（没有上家出牌）
            if not state.last_move or state.last_player != current_player:
                # 尝试出最小的单张
                single_actions = [action for action in legal_actions
                                 if isinstance(action, CardGroup) and action.card_type == CardGroupType.SINGLE]

                if single_actions:
                    min_single = min(single_actions, key=lambda action: action.cards[0].rank.value)
                    return min_single, 0.6

            # 如果上家出牌，尝试出比上家大的最小牌
            elif state.last_move:
                # 按照牌型分类动作
                actions_by_type = {}
                for action in legal_actions:
                    if isinstance(action, CardGroup):
                        if action.card_type not in actions_by_type:
                            actions_by_type[action.card_type] = []
                        actions_by_type[action.card_type].append(action)

                # 如果有相同牌型的动作，选择最小的
                if state.last_move.card_type in actions_by_type:
                    same_type_actions = actions_by_type[state.last_move.card_type]
                    if same_type_actions:
                        min_action = min(same_type_actions, key=lambda action: action.cards[0].rank.value)
                        return min_action, 0.6

        return None

    def get_stats(self) -> Dict[str, Any]:
        """
        获取残局特化处理组件的统计信息
        """
        # 返回组件内部的统计数据副本
        return self.stats.copy()