[flake8]
max-line-length = 88
extend-ignore = 
    # E203: whitespace before ':' (conflicts with black)
    E203,
    # E501: line too long (handled by black)
    E501,
    # W503: line break before binary operator (conflicts with black)
    W503,
    # E402: module level import not at top of file
    E402,
    # F401: imported but unused (handled by isort and mypy)
    F401,
exclude = 
    .git,
    __pycache__,
    .venv,
    .conda,
    build,
    dist,
    *.egg-info,
    .mypy_cache,
    .pytest_cache,
    logs,
    models,
    data,
    htmlcov,
    .coverage,
    migrations,
per-file-ignores =
    # __init__.py files can have unused imports
    __init__.py:F401,F403
    # Test files can have unused imports and long lines
    tests/*:F401,F403,E501
    # Configuration files can have long lines
    configs/*:E501
    # Scripts can have more relaxed rules
    scripts/*:E501,F401
max-complexity = 10
docstring-convention = google
# Additional flake8 plugins
select = 
    # pycodestyle errors
    E,
    # pycodestyle warnings  
    W,
    # pyflakes
    F,
    # mccabe complexity
    C,
    # docstring conventions (if flake8-docstrings is installed)
    D,
    # naming conventions (if pep8-naming is installed)
    N,
    # import order (if flake8-import-order is installed)
    I,
ignore-names = 
    setUp,
    tearDown,
    setUpClass,
    tearDownClass,
    setUpModule,
    tearDownModule,
    asyncSetUp,
    asyncTearDown,
    setUpTestData,
    failureException,
    longMessage,
    maxDiff
