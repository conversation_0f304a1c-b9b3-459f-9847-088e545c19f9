repos:
  # 基础代码质量检查
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
        args: [--markdown-linebreak-ext=md]
      - id: end-of-file-fixer
      - id: check-yaml
        args: [--allow-multiple-documents]
      - id: check-json
      - id: check-toml
      - id: check-xml
      - id: check-added-large-files
        args: [--maxkb=10240]  # 10MB limit
      - id: check-merge-conflict
      - id: debug-statements
      - id: check-docstring-first
      - id: check-executables-have-shebangs
      - id: check-shebang-scripts-are-executable
      - id: mixed-line-ending
        args: [--fix=lf]
      - id: requirements-txt-fixer

  # Python代码格式化
  - repo: https://github.com/psf/black
    rev: 22.12.0
    hooks:
      - id: black
        language_version: python3
        args: [--line-length=88]

  # 导入排序
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: [--profile, black, --line-length=88]

  # 代码质量检查
  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        additional_dependencies:
          - flake8-docstrings
          - flake8-import-order
          - flake8-bugbear
          - flake8-comprehensions
          - flake8-simplify
          - pep8-naming
        args: [--max-line-length=88]

  # 类型检查
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.0.0
    hooks:
      - id: mypy
        additional_dependencies:
          - types-requests
          - types-PyYAML
          - types-setuptools
        args: [--strict, --ignore-missing-imports]
        exclude: ^(tests/|scripts/|docs/)

  # 安全检查
  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.4
    hooks:
      - id: bandit
        args: [-r, ., -f, json, -o, bandit-report.json]
        exclude: ^tests/

  # 依赖安全检查
  - repo: https://github.com/Lucas-C/pre-commit-hooks-safety
    rev: v1.3.2
    hooks:
      - id: python-safety-dependencies-check
        files: requirements.*\.txt$

  # YAML格式化
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.0.0-alpha.4
    hooks:
      - id: prettier
        types: [yaml]
        exclude: ^(\.github/|configs/)

  # Jupyter Notebook清理
  - repo: https://github.com/nbQA-dev/nbQA
    rev: 1.6.1
    hooks:
      - id: nbqa-black
        additional_dependencies: [black==22.12.0]
      - id: nbqa-isort
        additional_dependencies: [isort==5.12.0]
      - id: nbqa-flake8
        additional_dependencies: [flake8==6.0.0]

  # 文档检查
  - repo: https://github.com/pycqa/pydocstyle
    rev: 6.3.0
    hooks:
      - id: pydocstyle
        args: [--convention=google]
        exclude: ^(tests/|scripts/|migrations/)

  # 拼写检查
  - repo: https://github.com/codespell-project/codespell
    rev: v2.2.2
    hooks:
      - id: codespell
        args: [--write-changes]
        exclude: ^(\.git/|logs/|models/|data/)

  # 提交信息检查
  - repo: https://github.com/commitizen-tools/commitizen
    rev: v2.40.0
    hooks:
      - id: commitizen
        stages: [commit-msg]

# 全局配置
default_stages: [commit]
fail_fast: false
default_language_version:
  python: python3.10

# CI配置
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit.com hooks
    
    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit autoupdate'
  autoupdate_schedule: weekly
  skip: []
  submodules: false
