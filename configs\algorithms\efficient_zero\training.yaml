# EfficientZero算法训练配置
# 基于架构文档和PRD需求优化的参数配置

# @package training
defaults:
  - base_training

# 算法基本配置
algorithm:
  name: "efficient_zero"
  type: "model_based"
  
  # EfficientZero特定参数
  efficient_zero:
    # MCTS配置 - 从50提升至100-200
    num_simulations: 100  # MCTS模拟次数
    max_simulations: 200  # 最大模拟次数(动态调整)
    
    # 搜索配置
    c_puct: 1.25  # UCB公式中的探索常数
    dirichlet_alpha: 0.3  # Dirichlet噪声参数
    exploration_fraction: 0.25  # 探索比例
    
    # 动态预算分配
    dynamic_budget: true
    budget_allocation:
      early_game: 0.4  # 前期分配比例
      mid_game: 0.4    # 中期分配比例
      late_game: 0.2   # 后期分配比例
    
    # 价值网络配置
    value_network:
      type: "distributional"  # categorical, scalar, distributional
      support_size: 601  # 分布式价值网络支持大小
      value_min: -300
      value_max: 300
    
    # 模型架构
    model:
      # 表示网络
      representation:
        type: "resnet"
        blocks: 6  # ResNet块数量
        channels: 256  # 通道数
        downsample: false
      
      # 动态网络
      dynamics:
        type: "resnet"
        blocks: 6
        channels: 256
        reward_head: true
      
      # 预测网络
      prediction:
        type: "resnet"
        blocks: 6
        channels: 256
        policy_head: true
        value_head: true

# 训练超参数
training:
  # 基础训练参数
  epochs: 1000
  batch_size: 256  # 从128提升至256
  learning_rate: 0.0005  # 精细调整
  weight_decay: 1e-4
  
  # 学习率调度
  lr_scheduler:
    type: "cosine"  # cosine, step, exponential
    warmup_epochs: 10
    min_lr: 1e-6
    
  # 优化器配置
  optimizer:
    type: "adamw"
    betas: [0.9, 0.999]
    eps: 1e-8
    amsgrad: false
  
  # 梯度配置
  gradient:
    clip_norm: 10.0  # 梯度裁剪
    accumulation_steps: 1  # 梯度累积步数
  
  # 正则化
  regularization:
    dropout: 0.0
    label_smoothing: 0.0
    mixup_alpha: 0.0

# 自对弈配置
self_play:
  # 游戏配置
  num_games: 1000  # 每轮自对弈游戏数
  max_moves: 1000  # 最大步数
  
  # 数据收集
  data_collection:
    buffer_size: 100000  # 经验回放缓冲区大小
    min_buffer_size: 10000  # 开始训练的最小缓冲区大小
    sample_ratio: 0.1  # 采样比例
  
  # 对手配置
  opponents:
    self_ratio: 0.8  # 自对弈比例
    historical_ratio: 0.2  # 历史模型比例
    random_ratio: 0.0  # 随机对手比例

# 多智能体协作配置
multi_agent:
  # 农民协作
  farmer_cooperation:
    enabled: true
    cooperation_weight: 0.8  # 从0.7提升
    team_reward_weight: 0.9  # 从0.8提升
    
    # 协作机制
    mechanisms:
      - "shared_value"      # 共享价值函数
      - "communication"     # 通信机制
      - "role_specialization"  # 角色专门化
    
    # 通信配置
    communication:
      enabled: true
      message_dim: 64
      max_messages: 3
      attention_heads: 4
  
  # 角色管理
  role_management:
    dynamic_roles: true
    role_switch_threshold: 0.1
    specialization_bonus: 0.1

# 奖励机制配置
reward:
  # 基础奖励
  terminal_reward: 2.0  # 胜负奖励
  
  # 过程奖励
  process_rewards:
    # 炸弹奖励机制
    bomb_reward: 0.1  # 炸弹奖励
    rocket_reward: 0.15  # 火箭奖励
    
    # 合作奖励
    cooperation_reward: 0.5  # 农民合作奖励
    
    # 进度奖励
    progress_reward: 0.01  # 游戏进度奖励
  
  # 奖励塑形
  reward_shaping:
    enabled: true
    potential_based: true  # 基于势能的奖励塑形
    discount_factor: 0.99

# 评估配置
evaluation:
  # 评估频率
  eval_frequency: 100  # 每N个训练步骤评估一次
  eval_episodes: 100   # 每次评估的游戏局数
  
  # 评估对手
  opponents:
    - type: "random"
      weight: 0.1
    - type: "rule_based"
      weight: 0.3
    - type: "historical"
      weight: 0.6
  
  # 评估指标
  metrics:
    - "win_rate"
    - "average_score"
    - "game_length"
    - "cooperation_efficiency"

# 分布式训练配置
distributed:
  # 并行配置
  data_parallel: true
  model_parallel: false
  
  # 工作节点配置
  num_workers: 4  # 训练工作节点数
  num_actors: 16  # 自对弈actor数量
  
  # 同步配置
  sync_frequency: 10  # 模型同步频率
  async_update: false  # 是否异步更新

# 检查点配置
checkpoint:
  save_frequency: 1000  # 保存频率
  keep_last: 5         # 保留最近N个检查点
  save_best: true      # 保存最佳模型
  
  # 检查点内容
  save_optimizer: true
  save_scheduler: true
  save_random_state: true

# 早停配置
early_stopping:
  enabled: true
  patience: 50  # 容忍轮数
  min_delta: 0.001  # 最小改进
  monitor: "eval_win_rate"  # 监控指标
  mode: "max"  # max或min

# 实验特定配置
experiment:
  # 实验标识
  name: "efficient_zero_optimization"
  tags: ["efficient_zero", "multi_agent", "cooperation"]
  
  # 超参数搜索
  hyperparameter_search:
    enabled: false
    method: "random"  # random, grid, bayesian
    num_trials: 50
    
    # 搜索空间
    search_space:
      learning_rate: [1e-5, 1e-3]
      batch_size: [128, 256, 512]
      num_simulations: [50, 100, 200]
      cooperation_weight: [0.5, 0.9]

# 调试和监控
debug:
  # 训练监控
  log_frequency: 100  # 日志记录频率
  plot_frequency: 1000  # 绘图频率
  
  # 性能分析
  profile_training: false
  profile_inference: false
  
  # 梯度监控
  monitor_gradients: false
  gradient_histogram: false

# 资源管理
resources:
  # GPU配置
  gpu:
    memory_fraction: 0.9  # GPU内存使用比例
    allow_growth: true    # 允许内存增长
  
  # CPU配置
  cpu:
    num_threads: 8  # CPU线程数
    
  # 内存配置
  memory:
    max_usage: "32GB"  # 最大内存使用
    swap_usage: false  # 是否使用交换空间
