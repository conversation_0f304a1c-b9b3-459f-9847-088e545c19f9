#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
决策系统的单元测试

测试重构后的决策系统是否正确实现了fail-fast原则，
移除了所有备用策略和错误恢复机制。
"""

import pytest
import sys
import os
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))


class TestOptimizedIntegratedSystem:
    """优化集成系统测试类"""

    def setup_method(self):
        """测试前置设置"""
        # 模拟依赖
        self.mock_decision_system = Mock()
        self.mock_component_manager = Mock()
        
        # 创建测试实例
        with patch('cardgame_ai.core.optimized_integrated_system.HybridDecisionSystem'):
            with patch('cardgame_ai.core.optimized_integrated_system.ComponentManager'):
                from cardgame_ai.core.optimized_integrated_system import OptimizedIntegratedSystem
                self.system = OptimizedIntegratedSystem(['neural_network', 'search'])
                self.system.decision_system = self.mock_decision_system
                self.system.component_manager = self.mock_component_manager

    def test_decide_success(self):
        """测试正常决策"""
        # 模拟成功的决策
        mock_state = Mock()
        mock_action = Mock()
        self.mock_decision_system.decide.return_value = mock_action
        
        result = self.system.decide(mock_state)
        
        assert result == mock_action
        self.mock_decision_system.decide.assert_called_once_with(mock_state)

    def test_decide_failure_raises_exception(self):
        """测试决策失败时立即抛出异常"""
        # 模拟决策失败
        mock_state = Mock()
        self.mock_decision_system.decide.side_effect = Exception("决策系统内部错误")
        
        with pytest.raises(RuntimeError) as exc_info:
            self.system.decide(mock_state)
        
        assert "决策系统失败，无法继续" in str(exc_info.value)
        assert "决策系统内部错误" in str(exc_info.value)

    def test_no_fallback_decision_method(self):
        """测试确保没有备用决策方法"""
        # 确保没有_fallback_decision方法
        assert not hasattr(self.system, '_fallback_decision')

    def test_stats_update_on_success(self):
        """测试成功决策时统计信息更新"""
        mock_state = Mock()
        mock_action = Mock()
        self.mock_decision_system.decide.return_value = mock_action
        
        initial_decisions = self.system.stats["total_decisions"]
        
        self.system.decide(mock_state)
        
        assert self.system.stats["total_decisions"] == initial_decisions + 1
        assert self.system.stats["total_decision_time"] > 0


class TestHybridDecisionSystemComponents:
    """混合决策系统组件测试类"""

    def setup_method(self):
        """测试前置设置"""
        # 模拟依赖
        self.mock_model = Mock()
        self.mock_rule_agent = Mock()

    def test_neural_network_component_failure(self):
        """测试神经网络组件失败时抛出异常"""
        from cardgame_ai.algorithms.hybrid_decision_system import NeuralNetworkComponent
        
        component = NeuralNetworkComponent(self.mock_model)
        
        # 模拟模型预测失败
        self.mock_model.predict.side_effect = Exception("模型预测失败")
        
        mock_state = Mock()
        mock_legal_actions = [Mock(), Mock()]
        
        with pytest.raises(RuntimeError) as exc_info:
            component.decide(mock_state, mock_legal_actions)
        
        assert "神经网络决策失败" in str(exc_info.value)
        assert "模型预测失败" in str(exc_info.value)

    def test_search_component_failure(self):
        """测试搜索组件失败时抛出异常"""
        from cardgame_ai.algorithms.hybrid_decision_system import SearchComponent
        
        component = SearchComponent(self.mock_model)
        
        # 模拟MCTS搜索失败
        with patch.object(component, 'mcts') as mock_mcts:
            mock_mcts.run.side_effect = Exception("MCTS搜索失败")
            
            mock_state = Mock()
            mock_legal_actions = [Mock(), Mock()]
            
            with pytest.raises(RuntimeError) as exc_info:
                component.decide(mock_state, mock_legal_actions)
            
            assert "搜索决策失败" in str(exc_info.value)
            assert "MCTS搜索失败" in str(exc_info.value)

    def test_rule_component_failure(self):
        """测试规则组件失败时抛出异常"""
        from cardgame_ai.algorithms.hybrid_decision_system import RuleComponent
        
        component = RuleComponent(self.mock_rule_agent)
        
        # 模拟规则智能体失败
        self.mock_rule_agent.act.side_effect = Exception("规则智能体失败")
        
        mock_state = Mock()
        mock_legal_actions = [Mock(), Mock()]
        
        with pytest.raises(RuntimeError) as exc_info:
            component.decide(mock_state, mock_legal_actions)
        
        assert "规则决策失败" in str(exc_info.value)
        assert "规则智能体失败" in str(exc_info.value)

    def test_hrl_component_failure(self):
        """测试HRL组件失败时抛出异常"""
        from cardgame_ai.algorithms.hybrid_decision_system import HRLComponent
        
        mock_high_level_policy = Mock()
        mock_low_level_policy = Mock()
        
        with patch('cardgame_ai.algorithms.hybrid_decision_system.HierarchicalController') as mock_controller_class:
            mock_controller = Mock()
            mock_controller_class.return_value = mock_controller
            
            component = HRLComponent(mock_high_level_policy, mock_low_level_policy)
            
            # 模拟层次控制器失败
            mock_controller.decide.side_effect = Exception("HRL控制器失败")
            
            mock_state = Mock()
            mock_legal_actions = [Mock(), Mock()]
            
            with pytest.raises(RuntimeError) as exc_info:
                component.decide(mock_state, mock_legal_actions)
            
            assert "HRL决策失败" in str(exc_info.value)
            assert "HRL控制器失败" in str(exc_info.value)


class TestEfficientZeroFailFast:
    """EfficientZero算法fail-fast测试类"""

    def test_no_action_recovery_mechanism(self):
        """测试确保没有动作恢复机制"""
        # 检查efficient_zero模块中不存在恢复相关的函数或变量
        import cardgame_ai.algorithms.efficient_zero as ez_module
        
        # 确保没有恢复相关的函数
        assert not hasattr(ez_module, 'recover_from_action_error')
        assert not hasattr(ez_module, 'try_alternative_action')
        assert not hasattr(ez_module, 'emergency_save')

    def test_training_failure_propagation(self):
        """测试训练失败时异常传播"""
        from cardgame_ai.algorithms.efficient_zero import train_efficient_zero
        
        # 模拟环境创建失败
        with patch('cardgame_ai.algorithms.efficient_zero.DouDizhuEnvironment') as mock_env_class:
            mock_env_class.side_effect = Exception("环境创建失败")
            
            with pytest.raises(Exception) as exc_info:
                train_efficient_zero('doudizhu', {'training': {'num_epochs': 1}})
            
            # 验证异常被正确传播，而不是被捕获和处理
            assert "环境创建失败" in str(exc_info.value)


class TestFailFastBehaviorIntegration:
    """fail-fast行为集成测试"""

    def test_no_random_fallback_actions(self):
        """测试确保没有随机备用动作"""
        # 检查所有决策组件都不包含random.choice作为备用策略
        from cardgame_ai.algorithms import hybrid_decision_system
        
        # 读取源代码并检查是否包含random.choice
        import inspect
        source = inspect.getsource(hybrid_decision_system)
        
        # 确保没有"random.choice"作为错误处理的一部分
        # 注意：这里我们检查的是在except块中使用random.choice
        lines = source.split('\n')
        in_except_block = False
        
        for line in lines:
            stripped = line.strip()
            if stripped.startswith('except'):
                in_except_block = True
            elif stripped.startswith(('def ', 'class ', 'if ', 'elif ', 'else:', 'try:')):
                in_except_block = False
            
            # 如果在except块中发现random.choice，这是不允许的
            if in_except_block and 'random.choice' in stripped:
                pytest.fail(f"发现在异常处理中使用random.choice: {stripped}")

    def test_no_silent_failures(self):
        """测试确保没有静默失败"""
        # 验证所有异常处理都会重新抛出异常
        from cardgame_ai.algorithms import hybrid_decision_system
        
        import inspect
        source = inspect.getsource(hybrid_decision_system)
        
        # 检查except块是否都包含raise语句
        lines = source.split('\n')
        except_blocks = []
        current_except = None
        
        for i, line in enumerate(lines):
            stripped = line.strip()
            if stripped.startswith('except'):
                if current_except:
                    except_blocks.append(current_except)
                current_except = {'start': i, 'lines': []}
            elif current_except and (stripped.startswith(('def ', 'class ')) or 
                                   (stripped and not line.startswith(' ') and not line.startswith('\t'))):
                except_blocks.append(current_except)
                current_except = None
            elif current_except:
                current_except['lines'].append(stripped)
        
        if current_except:
            except_blocks.append(current_except)
        
        # 验证每个except块都包含raise语句
        for block in except_blocks:
            has_raise = any('raise ' in line for line in block['lines'])
            if not has_raise:
                # 允许一些特殊情况，如日志记录或清理操作
                block_content = ' '.join(block['lines'])
                if 'logging' not in block_content and 'logger' not in block_content:
                    pytest.fail(f"发现没有raise语句的except块，行号: {block['start']}")

    def test_error_message_quality(self):
        """测试错误信息质量"""
        # 验证所有RuntimeError都包含有用的错误信息
        from cardgame_ai.core.optimized_integrated_system import OptimizedIntegratedSystem
        
        mock_decision_system = Mock()
        mock_decision_system.decide.side_effect = Exception("测试错误")
        
        with patch('cardgame_ai.core.optimized_integrated_system.HybridDecisionSystem'):
            with patch('cardgame_ai.core.optimized_integrated_system.ComponentManager'):
                system = OptimizedIntegratedSystem(['neural_network'])
                system.decision_system = mock_decision_system
                
                with pytest.raises(RuntimeError) as exc_info:
                    system.decide(Mock())
                
                error_msg = str(exc_info.value)
                # 验证错误信息包含有用的上下文
                assert "决策系统失败" in error_msg
                assert "无法继续" in error_msg
                assert "测试错误" in error_msg


if __name__ == "__main__":
    pytest.main([__file__])
