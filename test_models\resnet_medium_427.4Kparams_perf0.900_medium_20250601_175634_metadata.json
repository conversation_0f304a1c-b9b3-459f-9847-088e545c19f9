{"model_info": {"filename": "resnet_medium_427.4Kparams_perf0.900_medium_20250601_175634", "model_path": "test_models\\resnet_medium_427.4Kparams_perf0.900_medium_20250601_175634.pt", "save_timestamp": "2025-06-01T17:56:34.423765", "parameter_stats": {"total_parameters": 427392, "trainable_parameters": 427392, "non_trainable_parameters": 0, "layer_parameters": {"input_layer.weight": 131072, "input_layer.bias": 256, "hidden_layers.0.0.weight": 65536, "hidden_layers.0.0.bias": 256, "hidden_layers.1.0.weight": 65536, "hidden_layers.1.0.bias": 256, "hidden_layers.2.0.weight": 65536, "hidden_layers.2.0.bias": 256, "hidden_layers.3.0.weight": 65536, "hidden_layers.3.0.bias": 256, "output_layer.weight": 32768, "output_layer.bias": 128}}}, "training_info": {"epoch": null, "performance": 0.9, "tag": "medium"}, "model_architecture": {"model_class": "SimpleResNet", "model_modules": ["", "input_layer", "hidden_layers", "hidden_layers.0", "hidden_layers.0.0", "hidden_layers.0.1", "hidden_layers.0.2", "hidden_layers.1", "hidden_layers.1.0", "hidden_layers.1.1", "hidden_layers.1.2", "hidden_layers.2", "hidden_layers.2.0", "hidden_layers.2.1", "hidden_layers.2.2", "hidden_layers.3", "hidden_layers.3.0", "hidden_layers.3.1", "hidden_layers.3.2", "output_layer", "relu", "dropout"]}}