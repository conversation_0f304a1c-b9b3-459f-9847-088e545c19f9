"""
随机代理模块

实现一个简单的随机代理，用于测试和基准比较。
"""
import random
from typing import Any, Optional, List, Dict

from cardgame_ai.core.agent import Agent


class RandomAgent(Agent):
    """
    随机代理
    
    实现一个简单的随机代理，随机选择合法动作。
    """
    
    def __init__(self, seed: Optional[int] = None):
        """
        初始化随机代理
        
        Args:
            seed: 随机种子，用于可重现性
        """
        super().__init__()
        self.seed = seed
        if seed is not None:
            random.seed(seed)
    
    def select_action(self, state: Any, temperature: float = 1.0) -> Any:
        """
        选择动作
        
        Args:
            state: 当前状态
            temperature: 温度参数，对随机代理无效
            
        Returns:
            选择的动作
        """
        # 获取合法动作
        legal_actions = self.get_legal_actions(state)
        
        # 随机选择一个合法动作
        return random.choice(legal_actions)
    
    def get_legal_actions(self, state: Any) -> List[Any]:
        """
        获取合法动作
        
        Args:
            state: 当前状态
            
        Returns:
            合法动作列表
        """
        # 如果状态有get_legal_actions方法，使用它
        if hasattr(state, 'get_legal_actions'):
            return state.get_legal_actions()
        
        # 如果状态是字典并且包含legal_actions字段
        if isinstance(state, dict) and 'legal_actions' in state:
            return state['legal_actions']
        
        # 如果状态有legal_actions属性
        if hasattr(state, 'legal_actions'):
            return state.legal_actions
        
        # 默认返回空列表
        return [0]  # 默认返回一个动作，避免随机选择出错
    
    def update(self, experience: Any) -> None:
        """
        更新代理
        
        Args:
            experience: 经验数据
        """
        # 随机代理不需要更新
        pass
    
    def save(self, path: str) -> None:
        """
        保存代理
        
        Args:
            path: 保存路径
        """
        # 随机代理不需要保存
        pass
    
    def load(self, path: str) -> None:
        """
        加载代理
        
        Args:
            path: 加载路径
        """
        # 随机代理不需要加载
        pass
