#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
分布式训练协调器模块

提供高效的分布式训练协调器实现，管理Actor和Learner的协作。
使用Ray作为分布式计算框架，实现高效的数据传输和共享。
"""

import os
import time
import logging
import numpy as np
import random
import torch
from typing import Dict, List, Tuple, Any, Optional, Union
import ray
import threading
import json
from datetime import datetime

from cardgame_ai.distributed.actor_worker import SelfPlayActor
from cardgame_ai.distributed.learner_worker import LearnerWorker
from cardgame_ai.distributed.distributed_replay_buffer import DistributedReplayBuffer

# 设置日志
logger = logging.getLogger(__name__)


class DistributedTrainingCoordinator:
    """
    分布式训练协调器

    管理Actor和Learner的协作，协调分布式训练过程。
    """

    def __init__(
        self,
        model_config: Dict[str, Any],
        mcts_config: Dict[str, Any],
        optimizer_config: Dict[str, Any],
        env_config: Dict[str, Any] = None,
        replay_buffer_config: Dict[str, Any] = None,
        num_actors: int = 16,
        num_learners: int = 1,
        actor_device: str = "cpu",
        learner_device: str = "cuda",
        save_dir: str = "models/distributed",
        seed: Optional[int] = None
    ):
        """
        初始化分布式训练协调器

        Args:
            model_config: 模型配置
            mcts_config: MCTS配置
            optimizer_config: 优化器配置
            env_config: 环境配置
            replay_buffer_config: 经验回放缓冲区配置
            num_actors: Actor数量
            num_learners: Learner数量
            actor_device: Actor设备
            learner_device: Learner设备
            save_dir: 保存目录
            seed: 随机种子
        """
        self.model_config = model_config
        self.mcts_config = mcts_config
        self.optimizer_config = optimizer_config
        self.env_config = env_config or {}
        self.replay_buffer_config = replay_buffer_config or {}
        self.num_actors = num_actors
        self.num_learners = num_learners
        self.actor_device = actor_device
        self.learner_device = learner_device
        self.save_dir = save_dir
        self.seed = seed

        # 创建保存目录
        os.makedirs(save_dir, exist_ok=True)

        # 设置随机种子
        if seed is not None:
            np.random.seed(seed)
            torch.manual_seed(seed)
            random.seed(seed)

        # 初始化Ray
        if not ray.is_initialized():
            ray.init()

        # 创建经验回放缓冲区
        self.replay_buffer = self._create_replay_buffer()

        # 创建Actor
        self.actors = self._create_actors()

        # 创建Learner
        self.learners = self._create_learners()

        # 创建模型
        self.model = self._create_model()

        # 初始化权重
        self._initialize_weights()

        # 统计信息
        self.stats = {
            "train_steps": 0,
            "train_loss": 0.0,
            "episodes_played": 0,
            "experiences_collected": 0,
            "start_time": time.time(),
            "last_save_time": time.time(),
            "last_update_time": time.time()
        }

        # 停止标志
        self.stop_flag = False

        # 权重更新线程
        self.weight_update_thread = None

        # 权重更新间隔（秒）
        self.weight_update_interval = 10.0

        # 统计信息更新间隔（秒）
        self.stats_update_interval = 5.0

        # 保存间隔（秒）
        self.save_interval = 600.0  # 10分钟

    def _create_replay_buffer(self) -> ray.ObjectRef:
        """
        创建经验回放缓冲区

        Returns:
            ray.ObjectRef: 经验回放缓冲区引用
        """
        # 获取配置
        total_capacity = self.replay_buffer_config.get("total_capacity", 1000000)
        num_shards = self.replay_buffer_config.get("num_shards", 8)
        alpha = self.replay_buffer_config.get("alpha", 0.6)
        beta = self.replay_buffer_config.get("beta", 0.4)
        beta_increment = self.replay_buffer_config.get("beta_increment", 0.001)
        epsilon = self.replay_buffer_config.get("epsilon", 1e-6)

        # 创建经验回放缓冲区
        return DistributedReplayBuffer.remote(
            total_capacity=total_capacity,
            num_shards=num_shards,
            alpha=alpha,
            beta=beta,
            beta_increment=beta_increment,
            epsilon=epsilon
        )

    def _create_actors(self) -> List[ray.ObjectRef]:
        """
        创建Actor

        Returns:
            List[ray.ObjectRef]: Actor引用列表
        """
        actors = []

        for i in range(self.num_actors):
            # 创建Actor
            actor = SelfPlayActor.remote(
                model_config=self.model_config,
                mcts_config=self.mcts_config,
                env_config=self.env_config,
                actor_id=i,
                device=self.actor_device,
                seed=self.seed + i if self.seed is not None else None
            )

            actors.append(actor)

        return actors

    def _create_learners(self) -> List[ray.ObjectRef]:
        """
        创建Learner

        Returns:
            List[ray.ObjectRef]: Learner引用列表
        """
        learners = []

        for i in range(self.num_learners):
            # 创建Learner
            learner = LearnerWorker.remote(
                model_config=self.model_config,
                optimizer_config=self.optimizer_config,
                replay_buffer=self.replay_buffer,
                device=self.learner_device,
                seed=self.seed + self.num_actors + i if self.seed is not None else None
            )

            learners.append(learner)

        return learners

    def _create_model(self) -> torch.nn.Module:
        """
        创建模型

        Returns:
            torch.nn.Module: 模型
        """
        # 导入模型
        from cardgame_ai.algorithms.efficient_zero import EfficientZeroModel

        # 获取观察和动作空间
        observation_shape = self.model_config.get("observation_shape", (656,))
        action_shape = self.model_config.get("action_shape", (1000,))

        # 创建模型
        model = EfficientZeroModel(
            observation_shape=observation_shape,
            action_shape=action_shape,
            hidden_dim=self.model_config.get("hidden_dim", 256),
            state_dim=self.model_config.get("state_dim", 64),
            use_resnet=self.model_config.get("use_resnet", True),
            projection_dim=self.model_config.get("projection_dim", 256),
            prediction_dim=self.model_config.get("prediction_dim", 128),
            value_prefix_length=self.model_config.get("value_prefix_length", 5),
            device="cpu"  # 协调器使用CPU
        )

        # 设置为评估模式
        model.eval()

        return model

    def _initialize_weights(self) -> None:
        """
        初始化权重
        """
        # 获取模型权重
        weights = {k: v.cpu() for k, v in self.model.state_dict().items()}

        # 更新Actor权重
        update_ops = []
        for actor in self.actors:
            update_ops.append(actor.update_weights.remote(weights))

        # 更新Learner权重
        for learner in self.learners:
            update_ops.append(learner.update_weights.remote(weights))

        # 等待所有更新操作完成
        ray.get(update_ops)

    def _update_weights(self) -> None:
        """
        更新权重
        """
        while not self.stop_flag:
            try:
                # 获取Learner权重
                weights = ray.get(self.learners[0].get_weights.remote())

                # 更新模型权重
                self.model.load_state_dict(weights)

                # 更新Actor权重
                update_ops = []
                for actor in self.actors:
                    update_ops.append(actor.update_weights.remote(weights))

                # 等待所有更新操作完成
                ray.get(update_ops)

                # 更新统计信息
                self.stats["last_update_time"] = time.time()

                # 保存模型
                current_time = time.time()
                if current_time - self.stats["last_save_time"] >= self.save_interval:
                    self.save_model()
                    self.stats["last_save_time"] = current_time

                # 休眠
                time.sleep(self.weight_update_interval)
            except Exception as e:
                logger.error(f"权重更新线程异常: {e}")
                time.sleep(1.0)

    def _collect_experiences(self, num_episodes_per_actor: int = 1) -> None:
        """
        收集经验

        Args:
            num_episodes_per_actor: 每个Actor收集的回合数
        """
        # 并行收集经验
        rollout_ops = []
        for actor in self.actors:
            rollout_ops.append(
                actor.rollout.remote(
                    num_episodes=num_episodes_per_actor,
                    temperature=1.0,
                    temperature_drop_step=20,
                    add_exploration_noise=True
                )
            )

        # 等待所有收集操作完成
        all_experiences = ray.get(rollout_ops)

        # 添加到经验回放缓冲区
        for experiences_list in all_experiences:
            for experiences in experiences_list:
                # 添加到经验回放缓冲区
                ray.get(self.replay_buffer.add_batch.remote(experiences))

                # 更新统计信息
                self.stats["episodes_played"] += 1
                self.stats["experiences_collected"] += len(experiences)

    def _train_step(self, batch_size: int = 256, num_unroll_steps: int = 5, td_steps: int = 10) -> Dict[str, float]:
        """
        执行一步训练

        Args:
            batch_size: 批次大小
            num_unroll_steps: 展开步数
            td_steps: 时序差分步数

        Returns:
            Dict[str, float]: 损失字典
        """
        # 并行训练
        train_ops = []
        for learner in self.learners:
            train_ops.append(
                learner.train_step.remote(
                    batch_size=batch_size,
                    num_unroll_steps=num_unroll_steps,
                    td_steps=td_steps
                )
            )

        # 等待所有训练操作完成
        all_losses = ray.get(train_ops)

        # 计算平均损失
        avg_losses = {}
        for k in all_losses[0]:
            avg_losses[k] = sum(losses[k] for losses in all_losses) / len(all_losses)

        # 更新统计信息
        self.stats["train_steps"] += 1
        self.stats["train_loss"] = avg_losses["total_loss"]

        return avg_losses

    def _update_stats(self) -> None:
        """
        更新统计信息
        """
        # 获取Actor统计信息
        actor_stats_ops = []
        for actor in self.actors:
            actor_stats_ops.append(actor.get_stats.remote())

        # 获取Learner统计信息
        learner_stats_ops = []
        for learner in self.learners:
            learner_stats_ops.append(learner.get_stats.remote())

        # 获取经验回放缓冲区统计信息
        buffer_stats_op = self.replay_buffer.get_buffer_stats.remote()

        # 等待所有统计信息操作完成
        actor_stats_list = ray.get(actor_stats_ops)
        learner_stats_list = ray.get(learner_stats_ops)
        buffer_stats = ray.get(buffer_stats_op)

        # 更新统计信息
        self.stats["actor_stats"] = actor_stats_list
        self.stats["learner_stats"] = learner_stats_list
        self.stats["buffer_stats"] = buffer_stats

        # 计算总体统计信息
        self.stats["episodes_played"] = sum(stats["episodes_played"] for stats in actor_stats_list)
        self.stats["experiences_collected"] = sum(stats["experiences_collected"] for stats in actor_stats_list)
        self.stats["train_steps"] = sum(stats["train_steps"] for stats in learner_stats_list)
        self.stats["train_loss"] = sum(stats["train_loss"] for stats in learner_stats_list) / len(learner_stats_list)

    def train(
        self,
        num_iterations: int,
        episodes_per_iteration: int = 16,
        train_steps_per_iteration: int = 1,
        batch_size: int = 256,
        num_unroll_steps: int = 5,
        td_steps: int = 10,
        min_replay_size: int = 10000,
        save_interval: int = 100
    ) -> None:
        """
        训练模型

        Args:
            num_iterations: 迭代次数
            episodes_per_iteration: 每次迭代收集的回合数
            train_steps_per_iteration: 每次迭代的训练步数
            batch_size: 批次大小
            num_unroll_steps: 展开步数
            td_steps: 时序差分步数
            min_replay_size: 最小经验回放大小
            save_interval: 保存间隔（迭代次数）
        """
        # 启动权重更新线程
        self.weight_update_thread = threading.Thread(target=self._update_weights)
        self.weight_update_thread.daemon = True
        self.weight_update_thread.start()

        # 初始填充经验回放缓冲区
        logger.info(f"初始填充经验回放缓冲区，目标大小: {min_replay_size}")
        while ray.get(self.replay_buffer.get_size.remote()) < min_replay_size:
            self._collect_experiences(num_episodes_per_actor=1)
            logger.info(f"经验回放缓冲区大小: {ray.get(self.replay_buffer.get_size.remote())}")

        # 训练循环
        logger.info(f"开始训练，迭代次数: {num_iterations}")
        for i in range(num_iterations):
            # 收集经验
            self._collect_experiences(num_episodes_per_actor=episodes_per_iteration // self.num_actors)

            # 训练模型
            for _ in range(train_steps_per_iteration):
                losses = self._train_step(batch_size, num_unroll_steps, td_steps)

            # 更新统计信息
            if i % 10 == 0:
                self._update_stats()

            # 打印进度
            logger.info(f"迭代 {i + 1}/{num_iterations}")
            logger.info(f"  经验回放缓冲区大小: {ray.get(self.replay_buffer.get_size.remote())}")
            logger.info(f"  训练损失: {losses}")
            logger.info(f"  回合数: {self.stats['episodes_played']}")
            logger.info(f"  经验数: {self.stats['experiences_collected']}")

            # 保存模型
            if (i + 1) % save_interval == 0:
                self.save_model()

        # 保存最终模型
        self.save_model()

        # 停止权重更新线程
        self.stop_flag = True
        if self.weight_update_thread is not None:
            self.weight_update_thread.join()

    def save_model(self) -> str:
        """
        保存模型

        Returns:
            str: 模型保存路径
        """
        # 创建保存目录
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        save_path = os.path.join(self.save_dir, f"model_{timestamp}.pt")

        # 保存模型
        torch.save(self.model.state_dict(), save_path)

        # 保存配置
        config_path = os.path.join(self.save_dir, f"config_{timestamp}.json")
        config = {
            "model_config": self.model_config,
            "mcts_config": self.mcts_config,
            "optimizer_config": self.optimizer_config,
            "env_config": self.env_config,
            "replay_buffer_config": self.replay_buffer_config,
            "num_actors": self.num_actors,
            "num_learners": self.num_learners,
            "actor_device": self.actor_device,
            "learner_device": self.learner_device,
            "seed": self.seed,
            "stats": {k: v for k, v in self.stats.items() if not isinstance(v, list) and not isinstance(v, dict)}
        }

        with open(config_path, "w") as f:
            json.dump(config, f, indent=4)

        logger.info(f"模型已保存到: {save_path}")
        logger.info(f"配置已保存到: {config_path}")

        return save_path

    def load_model(self, model_path: str) -> None:
        """
        加载模型

        Args:
            model_path: 模型路径
        """
        # 加载模型
        self.model.load_state_dict(torch.load(model_path))

        # 初始化权重
        self._initialize_weights()

        logger.info(f"模型已加载: {model_path}")

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        # 更新统计信息
        self._update_stats()

        # 计算运行时间
        current_time = time.time()
        run_time = current_time - self.stats["start_time"]

        # 添加运行时间信息
        stats = dict(self.stats)
        stats["run_time"] = run_time

        # 计算每秒经验数
        if run_time > 0:
            stats["experiences_per_second"] = stats["experiences_collected"] / run_time

        # 计算每秒训练步数
        if run_time > 0:
            stats["train_steps_per_second"] = stats["train_steps"] / run_time

        return stats

    def shutdown(self) -> None:
        """
        关闭协调器
        """
        # 停止权重更新线程
        self.stop_flag = True
        if self.weight_update_thread is not None:
            self.weight_update_thread.join()

        # 关闭Ray
        if ray.is_initialized():
            ray.shutdown()
