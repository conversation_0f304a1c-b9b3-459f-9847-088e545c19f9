#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
斗地主AI集成系统主脚本

这个脚本用于初始化集成系统，并使用它进行训练和推理。
"""

import os
import sys
import time
import logging
import argparse
import numpy as np
import torch
import random

from cardgame_ai.integrated_system import IntegratedAISystem
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.utils.logger import Logger


def setup_seed(seed):
    """
    设置随机种子，确保结果可复现
    
    Args:
        seed: 随机种子
    """
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)
    torch.backends.cudnn.deterministic = True


def train(args):
    """
    训练功能
    
    Args:
        args: 命令行参数
    """
    logger = Logger("INFO")
    logger.info("开始训练")
    
    # 设置随机种子
    setup_seed(args.seed)
    
    # 创建集成系统
    config = {
        "core": {
            "efficient_zero": {
                "model_type": args.model_type,
                "model_size": args.model_size,
                "use_self_supervision": args.use_self_supervision,
                "consistency_loss_weight": args.consistency_loss_weight,
                "mixed_precision": args.mixed_precision
            }
        },
        "optimization": {
            "parallel": {
                "num_workers": args.num_workers
            }
        },
        "log_level": args.log_level
    }
    
    system = IntegratedAISystem(config)
    
    # 创建环境
    env = DouDizhuEnvironment(seed=args.seed)
    
    # 训练
    start_time = time.time()
    training_stats = system.train(env, args.episodes, args.eval_interval)
    end_time = time.time()
    
    # 输出训练结果
    logger.info(f"训练完成，用时 {end_time - start_time:.2f} 秒")
    logger.info(f"最终胜率: {training_stats['win_rates'][-1]:.4f}")
    
    # 保存模型
    if args.save_model:
        model_path = os.path.join(args.model_dir, f"model_{int(time.time())}.pt")
        torch.save(system.efficient_zero.model.state_dict(), model_path)
        logger.info(f"模型已保存到 {model_path}")


def inference(args):
    """
    推理功能
    
    Args:
        args: 命令行参数
    """
    logger = Logger("INFO")
    logger.info("开始推理")
    
    # 设置随机种子
    setup_seed(args.seed)
    
    # 创建集成系统
    config = {
        "core": {
            "efficient_zero": {
                "model_type": args.model_type,
                "model_size": args.model_size,
                "use_self_supervision": False,  # 推理时不需要自监督
                "mixed_precision": args.mixed_precision
            }
        },
        "log_level": args.log_level
    }
    
    system = IntegratedAISystem(config)
    
    # 加载模型
    if args.model_path:
        system.efficient_zero.model.load_state_dict(torch.load(args.model_path))
        logger.info(f"模型已加载: {args.model_path}")
    
    # 创建环境
    env = DouDizhuEnvironment(seed=args.seed)
    
    # 推理
    state = env.reset()
    done = False
    total_reward = 0
    
    while not done:
        # 获取合法动作
        legal_actions = env.get_legal_actions()
        
        # 选择动作
        action = system.select_action(state, legal_actions)
        
        # 执行动作
        state, reward, done, info = env.step(action)
        
        # 更新总奖励
        total_reward += reward
        
        # 渲染环境
        if args.render:
            env.render()
    
    # 输出结果
    logger.info(f"推理完成，总奖励: {total_reward:.4f}")
    
    # 如果是地主，检查是否获胜
    if env.state.landlord == 0:  # 假设AI是玩家0
        if total_reward > 0:
            logger.info("作为地主获胜")
        else:
            logger.info("作为地主失败")
    else:
        if total_reward > 0:
            logger.info("作为农民获胜")
        else:
            logger.info("作为农民失败")


def test_performance(args):
    """
    性能测试功能
    
    Args:
        args: 命令行参数
    """
    logger = Logger("INFO")
    logger.info("开始性能测试")
    
    # 设置随机种子
    setup_seed(args.seed)
    
    # 创建集成系统
    config = {
        "core": {
            "efficient_zero": {
                "model_type": args.model_type,
                "model_size": args.model_size,
                "use_self_supervision": False,  # 测试时不需要自监督
                "mixed_precision": args.mixed_precision
            }
        },
        "log_level": args.log_level
    }
    
    system = IntegratedAISystem(config)
    
    # 加载模型
    if args.model_path:
        system.efficient_zero.model.load_state_dict(torch.load(args.model_path))
        logger.info(f"模型已加载: {args.model_path}")
    
    # 创建环境
    env = DouDizhuEnvironment(seed=args.seed)
    
    # 性能测试
    performance = system.test_performance(env, args.episodes, args.verbose)
    
    # 输出性能指标
    logger.info("性能测试结果:")
    logger.info(f"胜率: {performance['win_rate']:.4f}")
    logger.info(f"平均奖励: {performance['average_reward']:.4f}")
    logger.info(f"平均步数: {performance['average_steps']:.4f}")
    logger.info(f"推理时间: {performance['inference_time'] * 1000:.4f} ms")
    logger.info(f"内存使用: {performance['memory_usage']:.4f} MB")
    logger.info(f"决策质量: {performance['decision_quality']:.4f}")
    
    # 如果需要优化性能
    if args.optimize:
        logger.info("开始优化性能")
        system.optimize_performance()
        
        # 再次测试性能
        logger.info("优化后性能测试:")
        performance = system.test_performance(env, args.episodes, args.verbose)
        
        # 输出优化后的性能指标
        logger.info("优化后性能测试结果:")
        logger.info(f"胜率: {performance['win_rate']:.4f}")
        logger.info(f"平均奖励: {performance['average_reward']:.4f}")
        logger.info(f"平均步数: {performance['average_steps']:.4f}")
        logger.info(f"推理时间: {performance['inference_time'] * 1000:.4f} ms")
        logger.info(f"内存使用: {performance['memory_usage']:.4f} MB")
        logger.info(f"决策质量: {performance['decision_quality']:.4f}")


def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description="斗地主AI集成系统")
    
    # 通用参数
    parser.add_argument("--mode", type=str, default="train", choices=["train", "inference", "test"],
                        help="运行模式: train, inference, test")
    parser.add_argument("--seed", type=int, default=42, help="随机种子")
    parser.add_argument("--log_level", type=str, default="INFO", help="日志级别")
    parser.add_argument("--model_type", type=str, default="resnet", help="模型类型")
    parser.add_argument("--model_size", type=str, default="medium", help="模型大小")
    parser.add_argument("--mixed_precision", action="store_true", help="是否使用混合精度")
    
    # 训练参数
    parser.add_argument("--episodes", type=int, default=1000, help="训练轮数")
    parser.add_argument("--eval_interval", type=int, default=100, help="评估间隔")
    parser.add_argument("--use_self_supervision", action="store_true", help="是否使用自监督")
    parser.add_argument("--consistency_loss_weight", type=float, default=0.1, help="一致性损失权重")
    parser.add_argument("--num_workers", type=int, default=8, help="并行工作进程数")
    parser.add_argument("--save_model", action="store_true", help="是否保存模型")
    parser.add_argument("--model_dir", type=str, default="models", help="模型保存目录")
    
    # 推理参数
    parser.add_argument("--model_path", type=str, default=None, help="模型路径")
    parser.add_argument("--render", action="store_true", help="是否渲染环境")
    
    # 测试参数
    parser.add_argument("--verbose", action="store_true", help="是否输出详细信息")
    parser.add_argument("--optimize", action="store_true", help="是否优化性能")
    
    args = parser.parse_args()
    
    # 创建模型保存目录
    if args.save_model and not os.path.exists(args.model_dir):
        os.makedirs(args.model_dir)
    
    # 根据模式执行相应的功能
    if args.mode == "train":
        train(args)
    elif args.mode == "inference":
        inference(args)
    elif args.mode == "test":
        test_performance(args)
    else:
        raise ValueError(f"不支持的模式: {args.mode}")


if __name__ == "__main__":
    main()
