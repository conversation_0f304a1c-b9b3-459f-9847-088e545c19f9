#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
斗地主游戏自我对弈模块

实现斗地主游戏的自我对弈机制，包括完整的游戏流程（发牌、叫地主、抢地主和出牌阶段）。
"""

import os
import time
import logging
import random
from typing import Dict, Any, List, Tuple, Optional, Union
import numpy as np

from cardgame_ai.core.base import Experience
from cardgame_ai.core.agent import Agent
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.games.doudizhu.state import GamePhase
from cardgame_ai.games.doudizhu.action import BidAction, GrabAction
from cardgame_ai.training.self_play import SelfPlay
from cardgame_ai.training.bidding_phase_handler import BiddingPhaseHandler
from cardgame_ai.training.online_collector import OnlineDataCollector


def play_doudizhu_game(env: DouDizhuEnvironment, agent: Agent, temperature: float = 1.0,
                   bidding_handler: Optional[BiddingPhaseHandler] = None,
                   collector: Optional[OnlineDataCollector] = None) -> List[Dict[str, Any]]:
    """
    运行单局斗地主游戏并收集经验，包括完整的游戏流程（发牌、叫地主、抢地主和出牌阶段）

    Args:
        env (DouDizhuEnvironment): 斗地主游戏环境
        agent (Agent): 用于自我对弈的代理
        temperature (float, optional): 温度参数，控制动作采样的随机性. Defaults to 1.0.
        bidding_handler (Optional[BiddingPhaseHandler], optional): 叫地主/抢地主阶段处理器. Defaults to None.
        collector (Optional[OnlineDataCollector], optional): 在线数据收集器. Defaults to None.

    Returns:
        List[Dict[str, Any]]: 经验数据列表
    """
    # 重置环境
    state = env.reset()
    done = False
    experiences = []

    # 如果没有提供叫地主/抢地主阶段处理器，创建一个默认的
    if bidding_handler is None:
        bidding_handler = BiddingPhaseHandler()

    # 游戏循环
    while not done:
        # 获取当前玩家
        current_player = state.current_player

        # 获取合法动作
        legal_actions = env.get_legal_actions(state)

        # 获取观察
        observation = env.get_observation(state)

        # 根据游戏阶段选择动作
        if state.game_phase == GamePhase.BIDDING:
            # 叫地主阶段
            action = agent.act(observation, legal_actions, is_training=True, temperature=temperature)
        elif state.game_phase == GamePhase.GRABBING:
            # 抢地主阶段
            action = agent.act(observation, legal_actions, is_training=True, temperature=temperature)
        elif state.game_phase == GamePhase.PLAYING:
            # 出牌阶段
            action = agent.act(observation, legal_actions, is_training=True, temperature=temperature)
        else:
            # 发牌阶段（通常不需要玩家操作）
            action = None

        # 执行动作
        next_state, reward, done, info = env.step(action)

        # 收集人类反馈数据用于 RLHF
        if collector is not None:
            collector.collect_step(state, action, reward, next_state, done,
                                   human_feedback=info.get('human_feedback', None))

        # 根据游戏阶段处理经验
        if state.game_phase == GamePhase.BIDDING:
            # 确定当前角色
            role = "landlord" if state.current_player == 0 else "farmer"

            # 使用叫地主阶段处理器收集经验
            experience = bidding_handler.collect_bidding_experience(
                state=state,
                action=action,
                reward=reward,
                next_state=next_state,
                done=done,
                role=role
            )

            # 添加额外信息
            experience['info'] = info
            experience['game_phase'] = state.game_phase.name

        elif state.game_phase == GamePhase.GRABBING:
            # 确定当前角色
            role = "landlord" if state.current_player == 0 else "farmer"

            # 使用抢地主阶段处理器收集经验
            experience = bidding_handler.collect_grabbing_experience(
                state=state,
                action=action,
                reward=reward,
                next_state=next_state,
                done=done,
                role=role
            )

            # 添加额外信息
            experience['info'] = info
            experience['game_phase'] = state.game_phase.name

        else:
            # 出牌阶段或其他阶段，使用普通经验
            experience = {
                'state': state,
                'action': action,
                'reward': reward,
                'next_state': next_state,
                'done': done,
                'info': info,
                'game_phase': state.game_phase.name
            }

        experiences.append(experience)

        # 更新状态
        state = next_state

    return experiences


class DouDizhuSelfPlay(SelfPlay):
    """斗地主游戏自我对弈类"""

    def generate_experience(self, env: DouDizhuEnvironment, agent: Agent, num_games: int,
                           temperature: float = 1.0, save: bool = True,
                           parallel: bool = True, disable_tqdm: bool = False,
                           bidding_handler: Optional[BiddingPhaseHandler] = None) -> List[Dict[str, Any]]:
        """
        生成斗地主自我对弈经验数据

        Args:
            env (DouDizhuEnvironment): 斗地主游戏环境
            agent (Agent): 用于自我对弈的代理
            num_games (int): 对弈的游戏数
            temperature (float, optional): 温度参数，控制动作采样的随机性. Defaults to 1.0.
            save (bool, optional): 是否保存经验数据. Defaults to True.
            parallel (bool, optional): 是否并行生成. Defaults to True.
            disable_tqdm (bool, optional): 是否禁用进度条. Defaults to False.
            bidding_handler (Optional[BiddingPhaseHandler], optional): 叫地主/抢地主阶段处理器. Defaults to None.

        Returns:
            List[Dict[str, Any]]: 生成的经验数据
        """
        start_time = time.time()
        all_experiences = []

        # 顺序生成游戏（暂不支持并行，因为需要处理完整的游戏流程）
        self.logger.info(f"开始生成斗地主自我对弈数据，游戏数: {num_games}")

        # 初始化在线数据收集器
        collector = OnlineDataCollector(save_path=os.path.join(self.save_path, 'online_feedback'),
                                        buffer_size=10000)
        for i in range(num_games):
            game_experiences = play_doudizhu_game(env, agent, temperature, bidding_handler, collector)
            all_experiences.extend(game_experiences)

            if (i + 1) % 10 == 0 or (i + 1) == num_games:
                elapsed_time = time.time() - start_time
                self.logger.info(
                    f"已完成 {i + 1}/{num_games} 局游戏 | "
                    f"时间: {elapsed_time:.2f}s | "
                    f"平均每局时间: {elapsed_time / (i + 1):.2f}s"
                )

        # 保存经验数据
        if save and self.save_path:
            save_file = os.path.join(self.save_path, f"experiences_{int(time.time())}.pkl")
            self.save_experiences(all_experiences, save_file)
            self.logger.info(f"保存经验数据: {save_file}")

        return all_experiences
