"""
手牌信息价值评估模块

为每张未知牌计算"信息价值"，评估其对当前局势的影响程度。
"""

from typing import Dict, List, Optional, Any, Tuple, Union, Callable
import numpy as np
import torch
import logging
from collections import defaultdict

from cardgame_ai.games.common.belief_state import BeliefState, BeliefSource
from cardgame_ai.algorithms.information_value import (
    InformationValueCalculator,
    calculate_information_value,
    calculate_all_cards_information_value
)
from cardgame_ai.games.doudizhu.card import Card

# 配置日志
logger = logging.getLogger(__name__)


class CardInformationValueEstimator:
    """
    手牌信息价值评估器

    为每张未知牌计算"信息价值"，评估其对当前局势的影响程度。
    """

    def __init__(
        self,
        method: str = "combined",
        weights: Optional[Dict[str, float]] = None,
        context_aware: bool = True,
        use_decision_impact: bool = True
    ):
        """
        初始化手牌信息价值评估器

        Args:
            method: 评估方法，可选'basic'、'entropy'、'action'或'combined'
            weights: 各种方法的权重，仅在method='combined'时有效
            context_aware: 是否考虑上下文信息（如当前局势、历史动作等）
            use_decision_impact: 是否考虑对决策的影响
        """
        self.method = method
        self.weights = weights or {
            'basic': 0.3,
            'entropy': 0.3,
            'action': 0.4
        }
        self.context_aware = context_aware
        self.use_decision_impact = use_decision_impact

        # 缓存最近的评估结果
        self.cache = {}

        logger.info(f"初始化手牌信息价值评估器: method={method}, context_aware={context_aware}")

    def estimate_card_value(
        self,
        belief_state: BeliefState,
        card: Union[str, Card],
        current_state: Optional[np.ndarray] = None,
        policy_function: Optional[Callable] = None,
        game_context: Optional[Dict[str, Any]] = None
    ) -> float:
        """
        评估单张牌的信息价值

        Args:
            belief_state: 信念状态
            card: 牌对象或牌字符串
            current_state: 当前游戏状态的向量表示
            policy_function: 策略函数，用于评估动作分布变化
            game_context: 游戏上下文信息，包括当前局势、历史动作等

        Returns:
            float: 信息价值，越高表示获取该信息越有价值
        """
        # 转换Card对象为字符串
        card_str = str(card) if isinstance(card, Card) else card

        # 检查缓存
        cache_key = (card_str, id(belief_state))
        if cache_key in self.cache:
            return self.cache[cache_key]

        # 基础信息价值
        base_value = calculate_information_value(
            belief_state,
            card_str,
            current_state,
            policy_function,
            self.method
        )

        # 如果启用了上下文感知，考虑游戏上下文
        context_value = 0.0
        if self.context_aware and game_context:
            context_value = self._evaluate_context_value(card_str, game_context)

        # 如果启用了决策影响评估，考虑对决策的影响
        decision_value = 0.0
        if self.use_decision_impact and policy_function and current_state is not None:
            decision_value = self._evaluate_decision_impact(
                belief_state, card_str, current_state, policy_function, game_context
            )

        # 组合信息价值
        final_value = base_value + 0.2 * context_value + 0.3 * decision_value

        # 缓存结果
        self.cache[cache_key] = final_value

        return final_value

    def _evaluate_context_value(self, card: str, game_context: Dict[str, Any]) -> float:
        """
        评估牌在当前游戏上下文中的价值

        Args:
            card: 牌字符串
            game_context: 游戏上下文信息

        Returns:
            float: 上下文价值，越高表示在当前上下文中越重要
        """
        context_value = 0.0

        # 考虑游戏阶段
        game_stage = game_context.get('game_stage', 'mid')
        if game_stage == 'early':
            # 早期阶段，高牌和关键牌更重要
            if '2' in card or 'JOKER' in card:
                context_value += 0.3
        elif game_stage == 'late':
            # 晚期阶段，所有牌都很重要
            context_value += 0.2

        # 考虑剩余牌数
        remaining_cards = game_context.get('remaining_cards', {})
        player_remaining = remaining_cards.get('player', 0)
        opponent_remaining = remaining_cards.get('opponent', 0)

        # 如果对手牌少，信息价值更高
        if opponent_remaining < 5:
            context_value += 0.4
        elif opponent_remaining < 10:
            context_value += 0.2

        # 考虑历史动作
        history = game_context.get('history', [])
        if history:
            # 如果最近有出过相同点数的牌，信息价值更高
            recent_actions = history[-5:]  # 最近5个动作
            for action in recent_actions:
                action_cards = action.get('cards', [])
                for action_card in action_cards:
                    if self._same_rank(card, action_card):
                        context_value += 0.1
                        break

        return min(1.0, context_value)  # 确保不超过1.0

    def _evaluate_decision_impact(
        self,
        belief_state: BeliefState,
        card: str,
        current_state: np.ndarray,
        policy_function: Callable,
        game_context: Optional[Dict[str, Any]] = None
    ) -> float:
        """
        评估牌对决策的影响

        Args:
            belief_state: 信念状态
            card: 牌字符串
            current_state: 当前游戏状态的向量表示
            policy_function: 策略函数
            game_context: 游戏上下文信息

        Returns:
            float: 决策影响价值，越高表示对决策影响越大
        """
        # 获取原始动作分布
        original_probs = policy_function(belief_state, current_state)

        # 创建两个假设的信念状态
        # 假设1：牌在对手手中
        positive_belief = self._create_hypothetical_belief(belief_state, card, True)
        positive_probs = policy_function(positive_belief, current_state)

        # 假设2：牌不在对手手中
        negative_belief = self._create_hypothetical_belief(belief_state, card, False)
        negative_probs = policy_function(negative_belief, current_state)

        # 计算KL散度
        kl_positive = self._calculate_kl_divergence(positive_probs, original_probs)
        kl_negative = self._calculate_kl_divergence(negative_probs, original_probs)

        # 计算决策影响
        decision_impact = max(kl_positive, kl_negative)

        return min(1.0, decision_impact)  # 确保不超过1.0

    def _create_hypothetical_belief(
        self,
        belief_state: BeliefState,
        card: str,
        has_card: bool
    ) -> BeliefState:
        """
        创建假设的信念状态

        Args:
            belief_state: 原始信念状态
            card: 牌字符串
            has_card: 是否假设对手有这张牌

        Returns:
            BeliefState: 假设的信念状态
        """
        # 创建信念状态的副本
        new_belief = BeliefState()
        new_belief.card_probabilities = belief_state.card_probabilities.copy()

        # 根据假设修改概率
        if has_card:
            new_belief.card_probabilities[card] = 1.0
        else:
            new_belief.card_probabilities[card] = 0.0

        # 重新归一化概率
        new_belief.normalize_probabilities()

        return new_belief

    def _calculate_kl_divergence(self, p: np.ndarray, q: np.ndarray) -> float:
        """
        计算KL散度

        Args:
            p: 概率分布P
            q: 概率分布Q

        Returns:
            float: KL散度
        """
        # 避免除以零
        p = np.clip(p, 1e-10, 1.0)
        q = np.clip(q, 1e-10, 1.0)

        # 计算KL散度
        kl = np.sum(p * np.log(p / q))

        return float(kl)

    def _same_rank(self, card1: str, card2: str) -> bool:
        """
        判断两张牌是否同点数

        Args:
            card1: 第一张牌
            card2: 第二张牌

        Returns:
            bool: 是否同点数
        """
        # 提取点数
        rank1 = card1.split('_')[-1] if '_' in card1 else card1[-1] if len(card1) > 1 else card1
        rank2 = card2.split('_')[-1] if '_' in card2 else card2[-1] if len(card2) > 1 else card2

        return rank1 == rank2

    def estimate_all_cards(
        self,
        belief_state: BeliefState,
        cards: List[Union[str, Card]],
        current_state: Optional[np.ndarray] = None,
        policy_function: Optional[Callable] = None,
        game_context: Optional[Dict[str, Any]] = None,
        top_n: Optional[int] = None
    ) -> Dict[str, float]:
        """
        评估多张牌的信息价值

        Args:
            belief_state: 信念状态
            cards: 牌列表
            current_state: 当前游戏状态的向量表示
            policy_function: 策略函数
            game_context: 游戏上下文信息
            top_n: 返回信息价值最高的前N张牌

        Returns:
            Dict[str, float]: 牌到信息价值的映射
        """
        # 评估所有牌的信息价值
        info_values = {}
        for card in cards:
            card_str = str(card) if isinstance(card, Card) else card

            # 跳过已知的牌（概率为0或1）
            prob = belief_state.get_probability(card_str)
            if prob < 0.01 or prob > 0.99:
                info_values[card_str] = 0.0
                continue

            # 评估信息价值
            try:
                info_values[card_str] = self.estimate_card_value(
                    belief_state, card_str, current_state, policy_function, game_context
                )
            except Exception as e:
                logger.warning(f"评估牌 {card_str} 的信息价值时出错: {e}")
                info_values[card_str] = 0.0

        # 如果指定了top_n，只返回信息价值最高的前N张牌
        if top_n is not None and top_n > 0:
            sorted_values = sorted(info_values.items(), key=lambda x: x[1], reverse=True)
            return dict(sorted_values[:top_n])

        return info_values
