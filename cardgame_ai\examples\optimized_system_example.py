"""
优化系统架构示例

演示如何使用优化的系统架构和算法交互。
"""

import os
import time
import logging
import argparse
import numpy as np
from typing import Dict, Any, List, Optional

from cardgame_ai.core.base import State, Action
from cardgame_ai.core.component_manager import ComponentManager
from cardgame_ai.core.optimized_hybrid_system import OptimizedHybridDecisionSystem
from cardgame_ai.core.optimized_integrated_system import OptimizedIntegratedAISystem
from cardgame_ai.algorithms.mcts import MCTSAgent
from cardgame_ai.algorithms.efficient_zero import EfficientZeroAgent
from cardgame_ai.algorithms.rule_based import RuleBasedAgent
from cardgame_ai.algorithms.random_agent import RandomAgent
from cardgame_ai.core.environment import DouDizhuEnv

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def register_components():
    """注册组件到组件管理器"""
    component_manager = ComponentManager.get_instance()

    # 注册MCTS代理工厂
    def mcts_factory(**kwargs):
        return MCTSAgent(
            num_simulations=kwargs.get("num_simulations", 1000),
            exploration_weight=kwargs.get("exploration_weight", 1.0),
            resources=kwargs.get("resources", {})
        )
    component_manager.register_factory("mcts", mcts_factory)

    # 注册EfficientZero代理工厂
    def efficient_zero_factory(**kwargs):
        return EfficientZeroAgent(
            model_path=kwargs.get("model_path", "models/efficient_zero_latest.pt"),
            resources=kwargs.get("resources", {})
        )
    component_manager.register_factory("efficient_zero", efficient_zero_factory)

    # 注册规则基础代理工厂
    def rule_based_factory(**kwargs):
        return RuleBasedAgent()
    component_manager.register_factory("rule_based", rule_based_factory)

    # 注册随机代理工厂
    def random_factory(**kwargs):
        return RandomAgent()
    component_manager.register_factory("random", random_factory)

    logger.info("组件注册完成")


def run_benchmark(
    num_games: int = 10,
    use_cache: bool = True,
    use_resource_allocation: bool = True,
    use_similarity_cache: bool = True
):
    """
    运行基准测试

    Args:
        num_games: 游戏局数
        use_cache: 是否使用缓存
        use_resource_allocation: 是否使用资源分配
        use_similarity_cache: 是否使用相似度缓存
    """
    logger.info(f"开始基准测试，游戏局数: {num_games}")

    # 创建环境
    env = DouDizhuEnv()

    # 创建优化的集成AI系统
    system = OptimizedIntegratedAISystem(
        use_cache=use_cache,
        use_resource_allocation=use_resource_allocation,
        use_similarity_cache=use_similarity_cache
    )

    # 运行游戏
    total_time = 0.0
    total_decisions = 0

    for game_idx in range(num_games):
        logger.info(f"开始游戏 {game_idx + 1}/{num_games}")

        # 重置环境
        state = env.reset()
        done = False

        # 游戏循环
        while not done:
            # 获取当前玩家
            current_player = env.get_current_player()

            # 做出决策
            start_time = time.time()
            action = system.decide(state)
            decision_time = time.time() - start_time

            # 更新统计信息
            total_time += decision_time
            total_decisions += 1

            # 执行动作
            state, reward, done, info = env.step(action)

        logger.info(f"游戏 {game_idx + 1} 完成")

    # 输出统计信息
    avg_decision_time = total_time / total_decisions if total_decisions > 0 else 0.0
    logger.info(f"基准测试完成，平均决策时间: {avg_decision_time:.6f}秒")

    # 输出系统统计信息
    stats = system.get_stats()
    logger.info(f"系统统计信息: {stats}")

    return stats


def compare_configurations(num_games: int = 5):
    """
    比较不同配置的性能

    Args:
        num_games: 每个配置的游戏局数
    """
    logger.info("开始比较不同配置的性能")

    # 定义配置
    configs = [
        {"name": "基础配置", "use_cache": False, "use_resource_allocation": False, "use_similarity_cache": False},
        {"name": "仅缓存", "use_cache": True, "use_resource_allocation": False, "use_similarity_cache": False},
        {"name": "缓存+资源分配", "use_cache": True, "use_resource_allocation": True, "use_similarity_cache": False},
        {"name": "完整优化", "use_cache": True, "use_resource_allocation": True, "use_similarity_cache": True}
    ]

    # 运行每个配置
    results = {}

    for config in configs:
        logger.info(f"测试配置: {config['name']}")
        stats = run_benchmark(
            num_games=num_games,
            use_cache=config["use_cache"],
            use_resource_allocation=config["use_resource_allocation"],
            use_similarity_cache=config["use_similarity_cache"]
        )
        results[config["name"]] = stats

    # 比较结果
    logger.info("性能比较结果:")
    for name, stats in results.items():
        logger.info(f"{name}: 平均决策时间 = {stats['avg_decision_time']:.6f}秒")

    return results


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="优化系统架构示例")
    parser.add_argument("--mode", type=str, default="benchmark", choices=["benchmark", "compare"],
                        help="运行模式: benchmark(基准测试) 或 compare(比较配置)")
    parser.add_argument("--num_games", type=int, default=5,
                        help="游戏局数")
    parser.add_argument("--use_cache", action="store_true", default=True,
                        help="是否使用缓存")
    parser.add_argument("--use_resource_allocation", action="store_true", default=True,
                        help="是否使用资源分配")
    parser.add_argument("--use_similarity_cache", action="store_true", default=True,
                        help="是否使用相似度缓存")
    args = parser.parse_args()

    # 注册组件
    register_components()

    # 运行指定模式
    if args.mode == "benchmark":
        run_benchmark(
            num_games=args.num_games,
            use_cache=args.use_cache,
            use_resource_allocation=args.use_resource_allocation,
            use_similarity_cache=args.use_similarity_cache
        )
    elif args.mode == "compare":
        compare_configurations(num_games=args.num_games)
    else:
        logger.error(f"未知的运行模式: {args.mode}")


if __name__ == "__main__":
    main()
