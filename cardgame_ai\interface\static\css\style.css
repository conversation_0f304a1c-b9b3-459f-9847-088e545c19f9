/* 
 * AI棋牌强化学习框架 - 通用样式
 */

/* 全局设置 */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --card-bg: #ffffff;
    --text-color: #212529;
    --header-bg: #e9ecef;
    --border-color: #dee2e6;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --transition-speed: 0.3s;
}

/* 暗色主题 */
[data-theme="dark"] {
    --primary-color: #0d6efd;
    --secondary-color: #5a6268;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #2b3035;
    --dark-color: #1a1d20;
    --card-bg: #2c3034;
    --text-color: #e6e6e6;
    --header-bg: #212529;
    --border-color: #495057;
    --shadow-color: rgba(0, 0, 0, 0.3);
}

body {
    font-family: "Microsoft YaHei", "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    color: var(--text-color);
    background-color: var(--light-color);
    line-height: 1.5;
    transition: background-color var(--transition-speed) ease;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
}

/* 卡片样式 */
.card {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    box-shadow: 0 0.25rem 0.5rem var(--shadow-color);
    transition: box-shadow var(--transition-speed) ease;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem var(--shadow-color);
}

.card-header {
    background-color: var(--header-bg);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem;
}

.card-body {
    padding: 1.5rem;
}

/* 按钮样式 */
.btn {
    display: inline-block;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    user-select: none;
    border: 1px solid transparent;
    padding: 0.5rem 1rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: 0.25rem;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    cursor: pointer;
}

.btn:focus, .btn:hover {
    text-decoration: none;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn-primary {
    color: #fff;
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-secondary {
    color: #fff;
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-success {
    color: #fff;
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-danger {
    color: #fff;
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-warning {
    color: #212529;
    background-color: var(--warning-color);
    border-color: var(--warning-color);
}

.btn-info {
    color: #fff;
    background-color: var(--info-color);
    border-color: var(--info-color);
}

/* 表单样式 */
.form-label {
    display: inline-block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-control {
    display: block;
    width: 100%;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    line-height: 1.5;
    color: var(--text-color);
    background-color: var(--card-bg);
    background-clip: padding-box;
    border: 1px solid var(--border-color);
    border-radius: 0.25rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    color: var(--text-color);
    background-color: var(--card-bg);
    border-color: var(--primary-color);
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-select {
    display: block;
    width: 100%;
    padding: 0.375rem 2.25rem 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: var(--text-color);
    background-color: var(--card-bg);
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
    border: 1px solid var(--border-color);
    border-radius: 0.25rem;
    appearance: none;
}

.form-text {
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: var(--secondary-color);
}

/* 辅助类 */
.text-center {
    text-align: center;
}

.mt-1 { margin-top: 0.25rem !important; }
.mt-2 { margin-top: 0.5rem !important; }
.mt-3 { margin-top: 1rem !important; }
.mt-4 { margin-top: 1.5rem !important; }
.mt-5 { margin-top: 3rem !important; }

.mb-1 { margin-bottom: 0.25rem !important; }
.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 1rem !important; }
.mb-4 { margin-bottom: 1.5rem !important; }
.mb-5 { margin-bottom: 3rem !important; }

.py-1 { padding-top: 0.25rem !important; padding-bottom: 0.25rem !important; }
.py-2 { padding-top: 0.5rem !important; padding-bottom: 0.5rem !important; }
.py-3 { padding-top: 1rem !important; padding-bottom: 1rem !important; }
.py-4 { padding-top: 1.5rem !important; padding-bottom: 1.5rem !important; }
.py-5 { padding-top: 3rem !important; padding-bottom: 3rem !important; }

.px-1 { padding-left: 0.25rem !important; padding-right: 0.25rem !important; }
.px-2 { padding-left: 0.5rem !important; padding-right: 0.5rem !important; }
.px-3 { padding-left: 1rem !important; padding-right: 1rem !important; }
.px-4 { padding-left: 1.5rem !important; padding-right: 1.5rem !important; }
.px-5 { padding-left: 3rem !important; padding-right: 3rem !important; }

/* 响应式断点 */
@media (max-width: 576px) {
    .container {
        padding: 0.5rem;
    }
    
    .card-body {
        padding: 1rem;
    }
}

@media (max-width: 768px) {
    h1 {
        font-size: 1.75rem;
    }
    
    h2 {
        font-size: 1.5rem;
    }
}

/* 自定义元素 */
.page-title {
    font-weight: 700;
    margin-bottom: 1.5rem;
}

.error-icon {
    font-size: 4rem;
    color: var(--danger-color);
}

.toast {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 9999;
    max-width: 350px;
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 0.25rem;
    box-shadow: 0 0.25rem 0.75rem var(--shadow-color);
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.toast.show {
    opacity: 1;
} 