@echo off
REM 自动收集人机交互数据示例脚本

REM 激活虚拟环境
call venv\Scripts\activate.bat

REM 设置输入和输出目录
set INPUT_DIR=data\human_games
set OUTPUT_DIR=data\processed
set ARCHIVE_DIR=data\archived

REM 运行自动收集脚本
python scripts\auto_collect_experiences.py ^
    --input %INPUT_DIR% ^
    --output %OUTPUT_DIR% ^
    --game doudizhu ^
    --interval 3600 ^
    --patterns "*.json" ^
    --patterns "*.log" ^
    --capacity 100000 ^
    --quality 0.3 ^
    --max-files 100 ^
    --archive %ARCHIVE_DIR%

REM 添加此行，防止脚本立即关闭
pause 