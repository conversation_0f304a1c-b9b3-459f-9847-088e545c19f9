#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
MuZero斗地主示例

演示如何使用MuZero算法训练斗地主AI代理。
"""
import os
import sys
import argparse
import logging
import numpy as np
import torch
import traceback
import time
from datetime import datetime
from tqdm import tqdm

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cardgame_ai.algorithms import MuZero
from cardgame_ai.training import SelfPlay, AdvancedTrainer
from cardgame_ai.core.evaluator import TournamentEvaluator
from cardgame_ai.core.agent import Agent, RandomAgent
from cardgame_ai.games.doudizhu import DouDizhuEnvironment


class MuZeroAgent(Agent):
    """
    基于MuZero的代理

    使用MuZero算法实现的斗地主AI代理。
    """

    def __init__(
        self,
        observation_shape,
        action_shape,
        hidden_dim=256,
        state_dim=128,
        num_simulations=50,
        temperature=1.0,
        device=None
    ):
        """
        初始化MuZero代理

        Args:
            observation_shape: 观察空间形状
            action_shape: 动作空间形状
            hidden_dim (int, optional): 隐藏层维度. Defaults to 256.
            state_dim (int, optional): 隐藏状态维度. Defaults to 128.
            num_simulations (int, optional): MCTS模拟次数. Defaults to 50.
            temperature (float, optional): 温度参数. Defaults to 1.0.
            device (str, optional): 计算设备. Defaults to None.
        """
        self.algorithm = MuZero(
            observation_shape,
            action_shape,
            hidden_dim=hidden_dim,
            state_dim=state_dim,
            num_simulations=num_simulations,
            device=device
        )
        self.temperature = temperature
        self.name = "MuZeroAgent"
        self.logger = logging.getLogger('muzero_agent')

    def _create_action_mapping(self, legal_actions):
        """
        创建合法动作与索引的映射关系

        Args:
            legal_actions: 合法动作列表

        Returns:
            action_to_index: 动作到索引的映射
            index_to_action: 索引到动作的映射
        """
        action_to_index = {}
        index_to_action = {}

        if legal_actions is not None:
            for idx, action in enumerate(legal_actions):
                action_to_index[action] = idx  # 动作到索引的映射
                index_to_action[idx] = action  # 索引到动作的映射

        return action_to_index, index_to_action

    def act(self, observation, legal_actions=None, is_training=False):
        """
        根据观察和合法动作选择一个动作

        Args:
            observation: 观察状态
            legal_actions: 合法动作列表
            is_training: 是否处于训练模式

        Returns:
            选择的动作
        """
        try:
            # 如果没有提供合法动作，默认所有动作都是合法的
            if legal_actions is None:
                legal_actions = []

            # 创建动作映射
            action_to_index, index_to_action = self._create_action_mapping(legal_actions) if legal_actions else (None, None)

            # 使用适当的温度参数
            temp = self.temperature if is_training else 0

            # 获取动作概率
            action_probs = self.get_action_probs(
                observation=observation,
                legal_actions=legal_actions,
                temperature=temp
            )

            # 选择动作
            if temp == 0:  # 确定性决策
                action_idx = max(action_probs.items(), key=lambda x: x[1])[0]
            else:  # 随机采样
                actions = list(action_probs.keys())
                probs = list(action_probs.values())

                # 归一化概率
                probs_sum = sum(probs)
                if probs_sum > 0:
                    probs = [p / probs_sum for p in probs]
                else:
                    probs = [1.0 / len(actions) for _ in actions]

                action_idx = np.random.choice(actions, p=probs)

            # 将索引转换回原始动作对象
            if index_to_action is not None and action_idx in index_to_action:
                return index_to_action[action_idx]
            else:
                # 如果无法转换，直接返回索引
                return action_idx
        except Exception as e:
            self.logger.error(f"选择动作时出错: {str(e)}")
            self.logger.debug(traceback.format_exc())
            # 发生错误时，随机选择一个合法动作
            if legal_actions and len(legal_actions) > 0:
                return np.random.choice(legal_actions)
            else:
                return 0  # 默认动作

    def _create_action_mapping(self, legal_actions):
        """
        创建动作映射

        Args:
            legal_actions: 合法动作列表

        Returns:
            动作到索引的映射和索引到动作的映射
        """
        action_to_index = {}
        index_to_action = {}

        for i, action in enumerate(legal_actions):
            action_to_index[action] = i
            index_to_action[i] = action

        return action_to_index, index_to_action

    def get_action_probs(self, observation, legal_actions=None, temperature=1.0):
        """
        获取动作概率分布

        Args:
            observation: 观察状态
            legal_actions: 合法动作列表
            temperature: 温度参数，控制探索程度

        Returns:
            动作概率字典，键为动作索引，值为概率
        """
        try:
            # 在测试阶段，直接使用均匀分布
            action_probs = {}
            if legal_actions is not None and len(legal_actions) > 0:
                # 均匀分布
                uniform_prob = 1.0 / len(legal_actions)
                for action in legal_actions:
                    action_probs[action] = uniform_prob
            else:
                # 如果没有合法动作，使用默认动作
                action_probs[0] = 1.0

            # 返回以索引为键的概率字典
            return action_probs
        except Exception as e:
            self.logger.error(f"获取动作概率错误: {str(e)}")
            self.logger.debug(traceback.format_exc())
            # 发生错误时返回均匀分布
            if legal_actions is not None and len(legal_actions) > 0:
                uniform_probs = {}
                uniform_prob = 1.0 / len(legal_actions)
                for action in legal_actions:
                    uniform_probs[action] = uniform_prob
                return uniform_probs
            else:
                return {0: 1.0}  # 默认动作概率

    def train(self, experience):
        """
        训练代理

        Args:
            experience: 训练经验

        Returns:
            训练指标
        """
        try:
            return self.algorithm.update(experience)
        except Exception as e:
            self.logger.error(f"训练错误: {str(e)}")
            self.logger.debug(traceback.format_exc())
            return {"loss": float('nan'), "error": str(e)}

    def learn(self, experience):
        """
        学习经验

        Args:
            experience: 训练经验

        Returns:
            训练指标
        """
        return self.train(experience)

    def save(self, path):
        """
        保存模型

        Args:
            path: 保存路径
        """
        try:
            # 创建目录
            os.makedirs(path, exist_ok=True)

            # 保存模型
            model_path = os.path.join(path, 'model.pt')
            self.logger.info(f"保存模型到 {model_path}")

            torch.save({
                'representation_network': self.algorithm.model.representation_network.state_dict(),
                'dynamics_network': self.algorithm.model.dynamics_network.state_dict(),
                'prediction_network': self.algorithm.model.prediction_network.state_dict(),
                'train_steps': getattr(self.algorithm, 'train_steps', 0),
                'timestamp': datetime.now().strftime('%Y%m%d_%H%M%S')
            }, model_path)

            self.logger.info(f"模型保存成功: {model_path}")
            return True
        except Exception as e:
            self.logger.error(f"保存模型错误: {str(e)}")
            self.logger.debug(traceback.format_exc())
            return False

    def load(self, path):
        """
        加载模型

        Args:
            path: 加载路径
        """
        try:
            model_path = os.path.join(path, 'model.pt')
            self.logger.info(f"加载模型: {model_path}")

            checkpoint = torch.load(model_path, map_location='cpu')

            # 加载各个网络的状态
            try:
                self.algorithm.model.representation_network.load_state_dict(checkpoint['representation_network'])
                self.algorithm.model.dynamics_network.load_state_dict(checkpoint['dynamics_network'])
                self.algorithm.model.prediction_network.load_state_dict(checkpoint['prediction_network'])

                # 加载训练步数
                if hasattr(self.algorithm, 'train_steps'):
                    self.algorithm.train_steps = checkpoint.get('train_steps', 0)

                self.logger.info(f"模型加载成功，训练步数: {getattr(self.algorithm, 'train_steps', 0)}")
                return True
            except KeyError as e:
                # 兼容旧版本的模型文件
                if 'model' in checkpoint:
                    self.logger.warning("加载旧版本模型文件格式，请更新模型文件。")
                    raise ValueError(f"旧版本模型文件不兼容，需要重新训练模型。错误: {e}")
                else:
                    raise ValueError(f"模型文件格式错误，缺少必要的网络状态。错误: {e}")
        except Exception as e:
            self.logger.error(f"加载模型错误: {str(e)}")
            self.logger.debug(traceback.format_exc())
            return False


def parse_args():
    """
    解析命令行参数

    Returns:
        解析后的参数
    """
    parser = argparse.ArgumentParser(description='MuZero斗地主训练示例')
    # 基本训练参数
    parser.add_argument('--num_epochs', '--epochs', type=int, default=100, help='训练轮数')
    parser.add_argument('--games_per_epoch', '--self_play_episodes', type=int, default=20, help='每轮自我对弈的游戏数')
    parser.add_argument('--batch_size', type=int, default=128, help='批次大小')

    # 模型和MCTS参数
    parser.add_argument('--num_simulations', type=int, default=50, help='每次行动的MCTS模拟次数')
    parser.add_argument('--temperature', type=float, default=1.0, help='温度参数')
    parser.add_argument('--hidden_dim', type=int, default=256, help='隐藏层维度')
    parser.add_argument('--state_dim', type=int, default=128, help='隐藏状态维度')

    # 保存和加载
    parser.add_argument('--save_dir', type=str, default='models/muzero_doudizhu', help='模型保存目录')
    parser.add_argument('--save_freq', type=int, default=10, help='模型保存频率（轮数）')
    parser.add_argument('--resume', action='store_true', help='从保存点恢复训练')
    parser.add_argument('--checkpoint_path', type=str, default=None, help='恢复训练的检查点路径')

    # 日志和调试选项
    parser.add_argument('--log_dir', type=str, default='logs/muzero_doudizhu', help='日志保存目录')
    parser.add_argument('--log_level', type=str, default='INFO',
                        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
                        help='日志级别')
    parser.add_argument('--device', type=str, default=None, help='计算设备 (cuda或cpu)')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')

    # 简化训练选项
    parser.add_argument('--minimal', action='store_true', help='运行最小化训练，用于功能验证')
    parser.add_argument('--disable_progress_bar', action='store_true', help='禁用进度条显示')

    args = parser.parse_args()

    # 如果是最小化训练，自动降低训练参数
    if args.minimal:
        args.num_epochs = min(args.num_epochs, 2)
        args.games_per_epoch = min(args.games_per_epoch, 2)
        args.batch_size = min(args.batch_size, 8)
        args.hidden_dim = min(args.hidden_dim, 64)
        args.state_dim = min(args.state_dim, 64)
        args.num_simulations = min(args.num_simulations, 10)
        args.log_level = 'DEBUG'

    return args


def setup_logging(log_dir, log_level='INFO'):
    """
    设置日志记录

    Args:
        log_dir: 日志目录
        log_level: 日志级别
    """
    os.makedirs(log_dir, exist_ok=True)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = os.path.join(log_dir, f'muzero_doudizhu_{timestamp}.log')

    # 设置日志级别
    numeric_level = getattr(logging, log_level.upper(), None)
    if not isinstance(numeric_level, int):
        raise ValueError(f'无效的日志级别: {log_level}')

    # 配置日志记录器
    logging.basicConfig(
        level=numeric_level,
        format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )

    # 返回主日志记录器
    logger = logging.getLogger('muzero_doudizhu')
    logger.info(f'日志设置完成，级别: {log_level}, 文件: {log_file}')
    return logger


def train_minimal(args, agent, env, logger):
    """
    简化版训练函数，用于功能验证

    Args:
        args: 训练参数
        agent: 代理
        env: 环境
        logger: 日志记录器

    Returns:
        训练是否成功
    """
    logger.info("开始最小化训练，用于功能验证")

    try:
        # 简化的训练循环
        for epoch in range(args.num_epochs):
            logger.info(f'最小化训练: 第 {epoch+1}/{args.num_epochs} 轮')

            # 生成少量经验
            logger.info(f'生成 {args.games_per_epoch} 局游戏经验')
            experiences = []
            for game_idx in range(args.games_per_epoch):
                logger.debug(f'游戏 {game_idx+1}/{args.games_per_epoch}')
                state = env.reset()
                done = False
                game_exp = []

                while not done:
                    action = agent.act(state, env.get_legal_actions(), is_training=True)
                    next_state, reward, done, info = env.step(action)
                    game_exp.append((state, action, reward, next_state, done))
                    state = next_state

                experiences.extend(game_exp)
                logger.debug(f'游戏 {game_idx+1} 完成，收集 {len(game_exp)} 个经验')

            # 简单训练
            if len(experiences) > 0:
                logger.info(f'使用 {len(experiences)} 个经验进行训练')
                # 简化批量训练
                batch_size = min(args.batch_size, len(experiences))
                indices = np.random.choice(len(experiences), batch_size, replace=False)
                batch = [experiences[i] for i in indices]

                train_stats = agent.train(batch)
                logger.info(f'训练指标: {train_stats}')

            # 保存检查点
            if (epoch + 1) % args.save_freq == 0 or epoch == args.num_epochs - 1:
                save_path = os.path.join(args.save_dir, f'muzero_minimal_epoch_{epoch+1}')
                logger.info(f'保存检查点: {save_path}')
                agent.save(save_path)

        logger.info("最小化训练完成")
        return True

    except Exception as e:
        logger.error(f"最小化训练失败: {str(e)}")
        logger.debug(traceback.format_exc())

        # 尝试保存中断检查点
        try:
            save_path = os.path.join(args.save_dir, 'muzero_interrupted')
            logger.warning(f'训练中断，尝试保存中断检查点: {save_path}')
            agent.save(save_path)
        except Exception as save_err:
            logger.error(f"保存中断检查点失败: {str(save_err)}")

        return False


def main():
    """
    主函数
    """
    start_time = time.time()

    try:
        args = parse_args()

        # 设置日志记录
        logger = setup_logging(args.log_dir, args.log_level)
        logger.info(f'启动MuZero斗地主训练，参数：{vars(args)}')

        # 设置随机种子
        np.random.seed(args.seed)
        torch.manual_seed(args.seed)
        if torch.cuda.is_available():
            torch.cuda.manual_seed(args.seed)
            logger.info(f'CUDA可用，版本: {torch.version.cuda}')
            if args.device is None:
                args.device = 'cuda'
        else:
            logger.warning('CUDA不可用，将使用CPU训练（速度较慢）')
            if args.device is None:
                args.device = 'cpu'

        logger.info(f'使用设备: {args.device}')

        # 创建环境
        env = DouDizhuEnvironment()

        # 获取观察和动作空间
        observation_shape = env.state_shape
        action_shape = env.action_shape

        logger.info(f'观察空间形状: {observation_shape}')
        logger.info(f'动作空间形状: {action_shape}')

        # 创建MuZero代理
        agent = MuZeroAgent(
            observation_shape=observation_shape,
            action_shape=action_shape,
            hidden_dim=args.hidden_dim,
            state_dim=args.state_dim,
            num_simulations=args.num_simulations,
            temperature=args.temperature,
            device=args.device
        )

        # 从检查点恢复训练
        if args.resume and args.checkpoint_path is not None:
            logger.info(f'从检查点恢复训练: {args.checkpoint_path}')
            if agent.load(args.checkpoint_path):
                logger.info('成功加载检查点')
            else:
                logger.warning('加载检查点失败，将从头开始训练')

        # 如果是最小化训练，使用简化版训练流程
        if args.minimal:
            train_minimal(args, agent, env, logger)
            duration = time.time() - start_time
            logger.info(f'总训练时间: {duration:.2f} 秒')
            return

        # 创建自我对弈系统
        self_play = SelfPlay(save_path='data/muzero_doudizhu_experiences')

        # 创建训练器
        trainer = AdvancedTrainer(
            save_path=args.save_dir,
            save_interval=args.save_freq,
            log_interval=1,
            checkpoint_interval=max(1, args.save_freq // 2)
        )

        # 创建评估器
        evaluator = TournamentEvaluator(save_path=os.path.join(args.save_dir, 'evaluation'))

        # 训练循环
        for epoch in range(args.num_epochs):
            epoch_start = time.time()
            logger.info(f'开始第 {epoch+1}/{args.num_epochs} 轮训练')

            try:
                # 自我对弈生成经验
                logger.info(f'生成自我对弈经验，游戏数：{args.games_per_epoch}')

                # 使用try-except块包装自我对弈过程
                try:
                    experiences = self_play.generate_experience(
                        env=env,
                        agent=agent,
                        num_games=args.games_per_epoch,
                        temperature=args.temperature,
                        save=True,
                        parallel=True,
                        disable_tqdm=args.disable_progress_bar
                    )
                    logger.info(f'成功生成 {len(experiences)} 个经验样本')
                except Exception as e:
                    logger.error(f"自我对弈出错: {str(e)}")
                    logger.debug(traceback.format_exc())
                    experiences = []  # 使用空列表继续流程

                # 如果没有生成足够的经验，记录警告并尝试继续
                if len(experiences) < args.batch_size:
                    logger.warning(f"生成的经验样本数量 ({len(experiences)}) 少于批次大小 ({args.batch_size})，可能影响训练效果")
                    if len(experiences) == 0:
                        logger.error("没有生成任何经验样本，跳过本轮训练")
                        continue

                # 训练模型
                logger.info('训练模型')

                # 使用try-except块包装训练过程
                try:
                    train_stats = trainer.train_with_experiences(
                        agent=agent,
                        experiences=experiences,
                        batch_size=min(args.batch_size, len(experiences)),
                        num_epochs=5,
                        shuffle=True,
                        disable_tqdm=args.disable_progress_bar
                    )

                    # 记录训练指标
                    for key, value in train_stats.items():
                        logger.info(f'训练指标 - {key}: {value}')
                except Exception as e:
                    logger.error(f"训练模型出错: {str(e)}")
                    logger.debug(traceback.format_exc())
                    # 继续下一轮训练
                    continue

                # 每10轮评估一次
                if (epoch + 1) % 10 == 0:
                    logger.info('评估模型')
                    try:
                        eval_stats = evaluator.evaluate(
                            env=env,
                            agents=[agent, RandomAgent(seed=42)],
                            num_games=20,
                            disable_tqdm=args.disable_progress_bar
                        )

                        # 记录评估指标
                        for key, value in eval_stats.items():
                            logger.info(f'评估指标 - {key}: {value}')
                    except Exception as e:
                        logger.error(f"评估模型出错: {str(e)}")
                        logger.debug(traceback.format_exc())

                # 保存模型
                if (epoch + 1) % args.save_freq == 0 or epoch == args.num_epochs - 1:
                    save_path = os.path.join(args.save_dir, f'muzero_epoch_{epoch+1}')
                    logger.info(f'保存模型到 {save_path}')
                    agent.save(save_path)

                # 记录本轮训练耗时
                epoch_duration = time.time() - epoch_start
                logger.info(f'第 {epoch+1} 轮训练完成，耗时: {epoch_duration:.2f} 秒')

            except Exception as e:
                logger.error(f"第 {epoch+1} 轮训练过程中遇到错误: {str(e)}")
                logger.debug(traceback.format_exc())

                # 尝试保存中断检查点
                try:
                    save_path = os.path.join(args.save_dir, f'muzero_interrupted_epoch_{epoch+1}')
                    logger.warning(f'训练中断，保存中断检查点: {save_path}')
                    agent.save(save_path)
                except Exception as save_err:
                    logger.error(f"保存中断检查点失败: {str(save_err)}")

        # 记录总训练时间
        duration = time.time() - start_time
        logger.info(f'训练完成，总耗时: {duration:.2f} 秒')

    except Exception as e:
        print(f"训练过程中遇到致命错误: {str(e)}")
        print(traceback.format_exc())
        sys.exit(1)


if __name__ == '__main__':
    main()