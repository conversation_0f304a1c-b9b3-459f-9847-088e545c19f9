"""
训练联合信念网络脚本

用于训练多人博弈中的联合信念分布预测模型。
"""

import os
import sys
import argparse
import logging
import time
import json
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from typing import Dict, List, Tuple, Any, Optional

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cardgame_ai.algorithms.belief_tracking.deep_joint_belief import JointBeliefNetwork
from cardgame_ai.games.doudizhu.card import Card
from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType, get_card_group_type

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class JointBeliefDataset(Dataset):
    """
    联合信念数据集

    用于加载和处理联合信念分布训练数据。
    """

    def __init__(
        self,
        data_file: str,
        card_mapping: List[str],
        num_players: int,
        max_seq_len: int = 100
    ):
        """
        初始化联合信念数据集

        Args:
            data_file: 数据文件路径
            card_mapping: 牌到索引的映射列表
            num_players: 玩家数量
            max_seq_len: 最大序列长度
        """
        self.data_file = data_file
        self.card_mapping = card_mapping
        self.card_to_idx = {card: idx for idx, card in enumerate(card_mapping)}
        self.num_players = num_players
        self.max_seq_len = max_seq_len

        # 加载数据
        self.data = self._load_data()

        logger.info(f"加载了 {len(self.data)} 条训练数据")

    def _load_data(self) -> List[Dict[str, Any]]:
        """
        加载数据

        Returns:
            List[Dict[str, Any]]: 加载的数据列表
        """
        data = []

        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        # 解析JSON数据
                        item = json.loads(line.strip())
                        data.append(item)
                    except json.JSONDecodeError:
                        logger.warning(f"无法解析JSON数据: {line}")
                        continue
        except Exception as e:
            logger.error(f"加载数据失败: {e}")

        return data

    def __len__(self) -> int:
        """
        获取数据集长度

        Returns:
            int: 数据集长度
        """
        return len(self.data)

    def __getitem__(self, idx: int) -> Tuple[Dict[str, Any], np.ndarray, List[np.ndarray], List[np.ndarray]]:
        """
        获取数据项

        Args:
            idx: 数据索引

        Returns:
            Tuple[Dict[str, Any], np.ndarray, List[np.ndarray], List[np.ndarray]]:
                - 动作历史
                - 状态
                - 玩家手牌概率分布列表
                - 玩家对关系因子列表
        """
        item = self.data[idx]

        # 提取动作历史
        action_history = item.get('action_history', [])

        # 截断或填充动作历史
        if len(action_history) > self.max_seq_len:
            action_history = action_history[-self.max_seq_len:]

        # 提取状态
        state = np.array(item.get('state', []), dtype=np.float32)

        # 提取玩家手牌概率分布
        player_distributions = []
        for i in range(self.num_players):
            player_key = f'player_{i}_distribution'
            if player_key in item:
                distribution = np.array(item[player_key], dtype=np.float32)
            else:
                distribution = np.zeros(len(self.card_mapping), dtype=np.float32)
            player_distributions.append(distribution)

        # 提取玩家对关系因子
        relation_factors = []
        num_player_pairs = self.num_players * (self.num_players - 1) // 2
        for i in range(num_player_pairs):
            relation_key = f'relation_factor_{i}'
            if relation_key in item:
                factor = np.array(item[relation_key], dtype=np.float32)
            else:
                factor = np.zeros(len(self.card_mapping), dtype=np.float32)
            relation_factors.append(factor)

        return action_history, state, player_distributions, relation_factors


def collate_fn(batch: List[Tuple[Dict[str, Any], np.ndarray, List[np.ndarray], List[np.ndarray]]]) -> Tuple[List[Dict[str, Any]], torch.Tensor, List[torch.Tensor], List[torch.Tensor]]:
    """
    数据批次整理函数

    Args:
        batch: 数据批次

    Returns:
        Tuple[List[Dict[str, Any]], torch.Tensor, List[torch.Tensor], List[torch.Tensor]]:
            - 动作历史列表
            - 状态张量
            - 玩家手牌概率分布张量列表
            - 玩家对关系因子张量列表
    """
    action_histories, states, player_distributions_list, relation_factors_list = zip(*batch)

    # 转换状态为张量
    states_tensor = torch.tensor(np.stack(states), dtype=torch.float32)

    # 转换玩家手牌概率分布为张量
    player_distributions_tensors = []
    for i in range(len(player_distributions_list[0])):
        player_distributions = [item[i] for item in player_distributions_list]
        player_distributions_tensor = torch.tensor(np.stack(player_distributions), dtype=torch.float32)
        player_distributions_tensors.append(player_distributions_tensor)

    # 转换玩家对关系因子为张量
    relation_factors_tensors = []
    for i in range(len(relation_factors_list[0])):
        relation_factors = [item[i] for item in relation_factors_list]
        relation_factors_tensor = torch.tensor(np.stack(relation_factors), dtype=torch.float32)
        relation_factors_tensors.append(relation_factors_tensor)

    return action_histories, states_tensor, player_distributions_tensors, relation_factors_tensors


def train(
    model: JointBeliefNetwork,
    train_loader: DataLoader,
    val_loader: Optional[DataLoader],
    optimizer: optim.Optimizer,
    criterion: nn.Module,
    device: torch.device,
    num_epochs: int,
    save_dir: str,
    save_interval: int = 10
) -> None:
    """
    训练联合信念网络

    Args:
        model: 联合信念网络模型
        train_loader: 训练数据加载器
        val_loader: 验证数据加载器
        optimizer: 优化器
        criterion: 损失函数
        device: 计算设备
        num_epochs: 训练轮数
        save_dir: 模型保存目录
        save_interval: 模型保存间隔
    """
    # 创建保存目录
    os.makedirs(save_dir, exist_ok=True)

    # 记录训练开始时间
    start_time = time.time()

    # 记录最佳验证损失
    best_val_loss = float('inf')

    # 训练循环
    for epoch in range(num_epochs):
        # 训练模式
        model.train()

        # 记录训练损失
        train_loss = 0.0
        train_player_losses = [0.0] * model.num_players
        train_relation_losses = 0.0

        # 训练一个轮次
        for batch_idx, (action_histories, states, player_distributions, relation_factors) in enumerate(train_loader):
            # 将数据移动到设备
            states = states.to(device)
            player_distributions = [dist.to(device) for dist in player_distributions]
            relation_factors = [factor.to(device) for factor in relation_factors]

            # 清零梯度
            optimizer.zero_grad()

            # 前向传播
            player_probs, relation_probs = model(action_histories, states)

            # 计算玩家手牌概率分布损失
            player_loss = 0.0
            for i in range(model.num_players):
                player_loss_i = criterion(player_probs[i], player_distributions[i])
                player_loss += player_loss_i
                train_player_losses[i] += player_loss_i.item()

            # 计算玩家对关系因子损失
            relation_loss = 0.0
            for i in range(len(relation_probs)):
                relation_loss += criterion(relation_probs[i], relation_factors[i])
            train_relation_losses += relation_loss.item()

            # 计算总损失
            loss = player_loss + relation_loss
            train_loss += loss.item()

            # 反向传播
            loss.backward()

            # 更新参数
            optimizer.step()

            # 打印进度
            if (batch_idx + 1) % 10 == 0:
                logger.info(f"Epoch {epoch+1}/{num_epochs}, Batch {batch_idx+1}/{len(train_loader)}, Loss: {loss.item():.4f}")

        # 计算平均训练损失
        train_loss /= len(train_loader)
        train_player_losses = [loss / len(train_loader) for loss in train_player_losses]
        train_relation_losses /= len(train_loader)

        # 打印训练损失
        logger.info(f"Epoch {epoch+1}/{num_epochs}, Train Loss: {train_loss:.4f}")
        for i in range(model.num_players):
            logger.info(f"  Player {i} Loss: {train_player_losses[i]:.4f}")
        logger.info(f"  Relation Loss: {train_relation_losses:.4f}")

        # 验证
        if val_loader is not None:
            # 评估模式
            model.eval()

            # 记录验证损失
            val_loss = 0.0
            val_player_losses = [0.0] * model.num_players
            val_relation_losses = 0.0

            # 验证一个轮次
            with torch.no_grad():
                for action_histories, states, player_distributions, relation_factors in val_loader:
                    # 将数据移动到设备
                    states = states.to(device)
                    player_distributions = [dist.to(device) for dist in player_distributions]
                    relation_factors = [factor.to(device) for factor in relation_factors]

                    # 前向传播
                    player_probs, relation_probs = model(action_histories, states)

                    # 计算玩家手牌概率分布损失
                    player_loss = 0.0
                    for i in range(model.num_players):
                        player_loss_i = criterion(player_probs[i], player_distributions[i])
                        player_loss += player_loss_i
                        val_player_losses[i] += player_loss_i.item()

                    # 计算玩家对关系因子损失
                    relation_loss = 0.0
                    for i in range(len(relation_probs)):
                        relation_loss += criterion(relation_probs[i], relation_factors[i])
                    val_relation_losses += relation_loss.item()

                    # 计算总损失
                    loss = player_loss + relation_loss
                    val_loss += loss.item()

            # 计算平均验证损失
            val_loss /= len(val_loader)
            val_player_losses = [loss / len(val_loader) for loss in val_player_losses]
            val_relation_losses /= len(val_loader)

            # 打印验证损失
            logger.info(f"Epoch {epoch+1}/{num_epochs}, Val Loss: {val_loss:.4f}")
            for i in range(model.num_players):
                logger.info(f"  Player {i} Loss: {val_player_losses[i]:.4f}")
            logger.info(f"  Relation Loss: {val_relation_losses:.4f}")

            # 保存最佳模型
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                model_path = os.path.join(save_dir, 'best_model.pth')
                torch.save({
                    'epoch': epoch + 1,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'loss': val_loss,
                }, model_path)
                logger.info(f"保存最佳模型到 {model_path}")

        # 定期保存模型
        if (epoch + 1) % save_interval == 0:
            model_path = os.path.join(save_dir, f'model_epoch_{epoch+1}.pth')
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'loss': train_loss,
            }, model_path)
            logger.info(f"保存模型到 {model_path}")

    # 保存最终模型
    model_path = os.path.join(save_dir, 'final_model.pth')
    torch.save({
        'epoch': num_epochs,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'loss': train_loss,
    }, model_path)
    logger.info(f"保存最终模型到 {model_path}")

    # 记录训练结束时间
    end_time = time.time()
    logger.info(f"训练完成，耗时 {end_time - start_time:.2f} 秒")


def main():
    """
    主函数
    """
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='训练联合信念网络')
    parser.add_argument('--train_data', type=str, required=True, help='训练数据文件路径')
    parser.add_argument('--val_data', type=str, help='验证数据文件路径')
    parser.add_argument('--card_mapping', type=str, required=True, help='牌到索引的映射文件路径')
    parser.add_argument('--num_players', type=int, default=3, help='玩家数量')
    parser.add_argument('--batch_size', type=int, default=32, help='批次大小')
    parser.add_argument('--num_epochs', type=int, default=100, help='训练轮数')
    parser.add_argument('--learning_rate', type=float, default=0.001, help='学习率')
    parser.add_argument('--save_dir', type=str, default='models/joint_belief', help='模型保存目录')
    parser.add_argument('--save_interval', type=int, default=10, help='模型保存间隔')
    parser.add_argument('--device', type=str, default='cuda' if torch.cuda.is_available() else 'cpu', help='计算设备')
    parser.add_argument('--card_vocab_size', type=int, default=54, help='牌词汇表大小')
    parser.add_argument('--action_embedding_dim', type=int, default=64, help='动作嵌入维度')
    parser.add_argument('--history_hidden_dim', type=int, default=128, help='历史编码器隐藏层维度')
    parser.add_argument('--state_input_dim', type=int, default=256, help='状态输入维度')
    parser.add_argument('--state_hidden_dim', type=int, default=128, help='状态编码器隐藏层维度')
    parser.add_argument('--history_encoder_type', type=str, default='lstm', choices=['lstm', 'gru', 'transformer'], help='历史编码器类型')
    parser.add_argument('--history_num_layers', type=int, default=2, help='历史编码器层数')
    parser.add_argument('--state_num_layers', type=int, default=2, help='状态编码器层数')
    parser.add_argument('--dropout', type=float, default=0.1, help='Dropout比率')
    args = parser.parse_args()

    # 设置设备
    device = torch.device(args.device)
    logger.info(f"使用设备: {device}")

    # 加载牌到索引的映射
    try:
        with open(args.card_mapping, 'r', encoding='utf-8') as f:
            card_mapping = json.load(f)
        logger.info(f"加载了 {len(card_mapping)} 张牌的映射")
    except Exception as e:
        logger.error(f"加载牌到索引的映射失败: {e}")
        card_mapping = [f"card_{i}" for i in range(args.card_vocab_size)]
        logger.warning(f"使用默认牌到索引的映射")

    # 创建数据集
    train_dataset = JointBeliefDataset(
        data_file=args.train_data,
        card_mapping=card_mapping,
        num_players=args.num_players
    )

    # 创建数据加载器
    train_loader = DataLoader(
        dataset=train_dataset,
        batch_size=args.batch_size,
        shuffle=True,
        collate_fn=collate_fn
    )

    # 创建验证数据加载器
    val_loader = None
    if args.val_data:
        val_dataset = JointBeliefDataset(
            data_file=args.val_data,
            card_mapping=card_mapping,
            num_players=args.num_players
        )

        val_loader = DataLoader(
            dataset=val_dataset,
            batch_size=args.batch_size,
            shuffle=False,
            collate_fn=collate_fn
        )

    # 创建模型
    model = JointBeliefNetwork(
        card_vocab_size=args.card_vocab_size,
        action_embedding_dim=args.action_embedding_dim,
        history_hidden_dim=args.history_hidden_dim,
        state_input_dim=args.state_input_dim,
        state_hidden_dim=args.state_hidden_dim,
        num_players=args.num_players,
        output_dim=len(card_mapping),
        history_encoder_type=args.history_encoder_type,
        history_num_layers=args.history_num_layers,
        state_num_layers=args.state_num_layers,
        dropout=args.dropout
    ).to(device)

    # 创建优化器
    optimizer = optim.Adam(model.parameters(), lr=args.learning_rate)

    # 创建损失函数
    criterion = nn.BCELoss()

    # 训练模型
    train(
        model=model,
        train_loader=train_loader,
        val_loader=val_loader,
        optimizer=optimizer,
        criterion=criterion,
        device=device,
        num_epochs=args.num_epochs,
        save_dir=args.save_dir,
        save_interval=args.save_interval
    )


if __name__ == '__main__':
    main()
