#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
打包脚本

用于自动化打包过程，包括更新版本号、清理旧的构建文件、运行PyInstaller、创建发布包等。
"""

import os
import sys
import shutil
import argparse
import subprocess
import platform
import re
from datetime import datetime

# 添加项目根目录到路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.dirname(project_root))

# 导入版本信息
from cardgame_ai.version import __version__


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='打包脚本')
    parser.add_argument('--version', type=str, help='版本号（格式：X.Y.Z）')
    parser.add_argument('--clean', action='store_true', help='清理旧的构建文件')
    parser.add_argument('--no-build', action='store_true', help='不运行构建过程')
    parser.add_argument('--no-package', action='store_true', help='不创建发布包')
    parser.add_argument('--release-notes', action='store_true', help='生成发布说明')
    return parser.parse_args()


def update_version(version):
    """更新版本号"""
    if not version:
        return False

    # 验证版本号格式
    if not re.match(r'^\d+\.\d+\.\d+$', version):
        print(f'错误：版本号格式无效（{version}），应为X.Y.Z')
        return False

    # 更新version.py文件
    version_file = os.path.join(project_root, 'version.py')
    with open(version_file, 'r', encoding='utf-8') as f:
        content = f.read()

    content = re.sub(r'__version__ = "[^"]+"', f'__version__ = "{version}"', content)

    with open(version_file, 'w', encoding='utf-8') as f:
        f.write(content)

    print(f'版本号已更新为：{version}')
    return True


def clean_build():
    """清理旧的构建文件"""
    build_dir = os.path.join(project_root, 'build')
    dist_dir = os.path.join(project_root, 'dist')

    if os.path.exists(build_dir):
        shutil.rmtree(build_dir)
        print(f'已删除目录：{build_dir}')

    if os.path.exists(dist_dir):
        shutil.rmtree(dist_dir)
        print(f'已删除目录：{dist_dir}')


def run_pyinstaller():
    """运行PyInstaller"""
    spec_file = os.path.join(project_root, 'scripts', 'cardgame_ai.spec')
    cmd = ['pyinstaller', '--clean', spec_file]

    print(f'运行命令：{" ".join(cmd)}')
    result = subprocess.run(cmd, cwd=project_root)

    if result.returncode != 0:
        print('错误：PyInstaller运行失败')
        return False

    print('PyInstaller运行成功')
    return True


def create_package():
    """创建发布包"""
    dist_dir = os.path.join(project_root, 'dist')
    app_name = f"CardGameAI-{__version__}"
    app_dir = os.path.join(dist_dir, app_name)

    if not os.path.exists(app_dir):
        print(f'错误：应用程序目录不存在：{app_dir}')
        return False

    # 创建发布包目录
    releases_dir = os.path.join(project_root, 'releases')
    os.makedirs(releases_dir, exist_ok=True)

    # 确定操作系统
    system = platform.system().lower()
    if system == 'windows':
        package_format = 'zip'
        package_name = f"{app_name}-windows.{package_format}"
    elif system == 'linux':
        package_format = 'tar.gz'
        package_name = f"{app_name}-linux.{package_format}"
    elif system == 'darwin':
        package_format = 'tar.gz'
        package_name = f"{app_name}-macos.{package_format}"
    else:
        print(f'错误：不支持的操作系统：{system}')
        return False

    package_path = os.path.join(releases_dir, package_name)

    # 创建发布包
    if package_format == 'zip':
        shutil.make_archive(package_path[:-4], 'zip', dist_dir, app_name)
    else:
        shutil.make_archive(package_path[:-7], 'gztar', dist_dir, app_name)

    print(f'发布包已创建：{package_path}')
    return True


def generate_release_notes():
    """生成发布说明"""
    template_file = os.path.join(project_root, 'RELEASE_NOTES.md')
    if not os.path.exists(template_file):
        print(f'错误：发布说明模板不存在：{template_file}')
        return False

    with open(template_file, 'r', encoding='utf-8') as f:
        template = f.read()

    # 替换模板中的变量
    release_notes = template.replace('{{VERSION}}', __version__)
    release_notes = release_notes.replace('{{DATE}}', datetime.now().strftime('%Y-%m-%d'))

    # 保存发布说明
    release_notes_file = os.path.join(project_root, 'releases', f'RELEASE_NOTES_{__version__}.md')
    with open(release_notes_file, 'w', encoding='utf-8') as f:
        f.write(release_notes)

    print(f'发布说明已生成：{release_notes_file}')
    return True


def main():
    """主函数"""
    args = parse_args()

    # 更新版本号
    if args.version:
        if not update_version(args.version):
            return 1

    # 清理旧的构建文件
    if args.clean:
        clean_build()

    # 运行PyInstaller
    if not args.no_build:
        if not run_pyinstaller():
            return 1

    # 创建发布包
    if not args.no_package:
        if not create_package():
            return 1

    # 生成发布说明
    if args.release_notes:
        if not generate_release_notes():
            return 1

    print('打包过程完成')
    return 0


if __name__ == '__main__':
    sys.exit(main())
