"""
数据服务接口定义

该接口定义了数据服务的标准契约，用于解耦zhuchengxu模块与具体数据实现的依赖关系。
通过该接口，zhuchengxu模块可以与任何实现了DataInterface的数据服务进行交互。

设计目标:
- 解耦zhuchengxu与optimized_data_loader的直接依赖
- 提供标准化的数据服务接口
- 支持多种数据源和格式
- 实现数据的加载、预处理和缓存

作者: Architect Timmy
版本: v1.0
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Union, Iterator, Tuple
from dataclasses import dataclass
from enum import Enum
from pathlib import Path


class DataFormat(Enum):
    """数据格式枚举"""
    NUMPY = "numpy"
    TENSOR = "tensor"
    PICKLE = "pickle"
    JSON = "json"
    CSV = "csv"
    HDF5 = "hdf5"


class DataSplit(Enum):
    """数据分割枚举"""
    TRAIN = "train"
    VALIDATION = "validation"
    TEST = "test"
    ALL = "all"


@dataclass
class DataLoadResult:
    """数据加载结果数据类"""
    success: bool                          # 是否成功
    data: Optional[Any] = None             # 加载的数据
    metadata: Optional[Dict[str, Any]] = None  # 数据元信息
    error_message: Optional[str] = None    # 错误消息
    load_time: Optional[float] = None      # 加载时间(秒)
    data_size: Optional[int] = None        # 数据大小


@dataclass
class DatasetInfo:
    """数据集信息"""
    name: str                              # 数据集名称
    version: str                           # 版本
    description: str                       # 描述
    total_samples: int                     # 总样本数
    features: List[str]                    # 特征列表
    labels: List[str]                      # 标签列表
    data_format: DataFormat                # 数据格式
    file_size: int                         # 文件大小(字节)
    created_at: str                        # 创建时间
    updated_at: str                        # 更新时间


class DataInterface(ABC):
    """数据服务接口
    
    定义了数据服务必须实现的标准方法，用于解耦zhuchengxu模块与具体数据实现。
    
    实现该接口的类必须提供:
    1. 数据加载和保存功能
    2. 数据预处理和转换功能
    3. 数据缓存和管理功能
    4. 数据验证和统计功能
    
    注意:
        所有方法都必须是线程安全的，支持并发调用。
        数据加载应该支持懒加载和批量加载。
    """
    
    @abstractmethod
    def load_dataset(self, dataset_name: str, split: DataSplit = DataSplit.ALL,
                    batch_size: Optional[int] = None,
                    shuffle: bool = False) -> DataLoadResult:
        """加载数据集
        
        Args:
            dataset_name: 数据集名称
            split: 数据分割
            batch_size: 批次大小，None表示加载全部
            shuffle: 是否打乱数据
            
        Returns:
            DataLoadResult: 加载结果
        """
        pass
    
    @abstractmethod
    def save_dataset(self, data: Any, dataset_name: str,
                    format: DataFormat = DataFormat.PICKLE,
                    metadata: Optional[Dict[str, Any]] = None) -> bool:
        """保存数据集
        
        Args:
            data: 要保存的数据
            dataset_name: 数据集名称
            format: 保存格式
            metadata: 元数据
            
        Returns:
            bool: 是否成功保存
        """
        pass
    
    @abstractmethod
    def create_dataloader(self, dataset_name: str, batch_size: int,
                         shuffle: bool = True, num_workers: int = 0,
                         **kwargs) -> Iterator:
        """创建数据加载器
        
        Args:
            dataset_name: 数据集名称
            batch_size: 批次大小
            shuffle: 是否打乱
            num_workers: 工作进程数
            **kwargs: 其他参数
            
        Returns:
            Iterator: 数据加载器迭代器
        """
        pass
    
    @abstractmethod
    def preprocess_data(self, data: Any, preprocessing_config: Dict[str, Any]) -> Any:
        """预处理数据
        
        Args:
            data: 原始数据
            preprocessing_config: 预处理配置
            
        Returns:
            Any: 预处理后的数据
        """
        pass
    
    @abstractmethod
    def validate_data(self, data: Any, schema: Optional[Dict[str, Any]] = None) -> bool:
        """验证数据
        
        Args:
            data: 要验证的数据
            schema: 验证模式
            
        Returns:
            bool: 数据是否有效
        """
        pass
    
    @abstractmethod
    def get_dataset_info(self, dataset_name: str) -> Optional[DatasetInfo]:
        """获取数据集信息
        
        Args:
            dataset_name: 数据集名称
            
        Returns:
            Optional[DatasetInfo]: 数据集信息，不存在返回None
        """
        pass
    
    @abstractmethod
    def list_datasets(self) -> List[str]:
        """列出所有数据集
        
        Returns:
            List[str]: 数据集名称列表
        """
        pass
    
    @abstractmethod
    def delete_dataset(self, dataset_name: str) -> bool:
        """删除数据集
        
        Args:
            dataset_name: 数据集名称
            
        Returns:
            bool: 是否成功删除
        """
        pass
    
    @abstractmethod
    def split_dataset(self, dataset_name: str, 
                     train_ratio: float = 0.8,
                     val_ratio: float = 0.1,
                     test_ratio: float = 0.1,
                     random_seed: Optional[int] = None) -> bool:
        """分割数据集
        
        Args:
            dataset_name: 数据集名称
            train_ratio: 训练集比例
            val_ratio: 验证集比例
            test_ratio: 测试集比例
            random_seed: 随机种子
            
        Returns:
            bool: 是否成功分割
        """
        pass
    
    @abstractmethod
    def merge_datasets(self, dataset_names: List[str], 
                      output_name: str) -> bool:
        """合并数据集
        
        Args:
            dataset_names: 要合并的数据集名称列表
            output_name: 输出数据集名称
            
        Returns:
            bool: 是否成功合并
        """
        pass
    
    @abstractmethod
    def sample_dataset(self, dataset_name: str, sample_size: int,
                      method: str = "random") -> DataLoadResult:
        """采样数据集
        
        Args:
            dataset_name: 数据集名称
            sample_size: 采样大小
            method: 采样方法 ("random", "stratified", "systematic")
            
        Returns:
            DataLoadResult: 采样结果
        """
        pass
    
    @abstractmethod
    def get_data_statistics(self, dataset_name: str) -> Dict[str, Any]:
        """获取数据统计信息
        
        Args:
            dataset_name: 数据集名称
            
        Returns:
            Dict[str, Any]: 统计信息，包含:
                - sample_count: 样本数量
                - feature_stats: 特征统计
                - label_distribution: 标签分布
                - missing_values: 缺失值统计
        """
        pass
    
    @abstractmethod
    def cache_dataset(self, dataset_name: str, cache_key: Optional[str] = None) -> bool:
        """缓存数据集
        
        Args:
            dataset_name: 数据集名称
            cache_key: 缓存键，None表示使用默认键
            
        Returns:
            bool: 是否成功缓存
        """
        pass
    
    @abstractmethod
    def clear_cache(self, cache_key: Optional[str] = None) -> bool:
        """清除缓存
        
        Args:
            cache_key: 缓存键，None表示清除所有缓存
            
        Returns:
            bool: 是否成功清除
        """
        pass
    
    @abstractmethod
    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息
        
        Returns:
            Dict[str, Any]: 缓存信息，包含:
                - cache_size: 缓存大小
                - cached_datasets: 已缓存的数据集
                - hit_rate: 缓存命中率
        """
        pass
    
    @abstractmethod
    def transform_data(self, data: Any, transformations: List[Dict[str, Any]]) -> Any:
        """转换数据
        
        Args:
            data: 原始数据
            transformations: 转换配置列表
            
        Returns:
            Any: 转换后的数据
        """
        pass
    
    @abstractmethod
    def export_data(self, dataset_name: str, output_path: Union[str, Path],
                   format: DataFormat = DataFormat.CSV) -> bool:
        """导出数据
        
        Args:
            dataset_name: 数据集名称
            output_path: 输出路径
            format: 导出格式
            
        Returns:
            bool: 是否成功导出
        """
        pass
    
    @abstractmethod
    def import_data(self, input_path: Union[str, Path], dataset_name: str,
                   format: Optional[DataFormat] = None) -> DataLoadResult:
        """导入数据
        
        Args:
            input_path: 输入路径
            dataset_name: 数据集名称
            format: 数据格式，None表示自动检测
            
        Returns:
            DataLoadResult: 导入结果
        """
        pass
    
    @abstractmethod
    def backup_dataset(self, dataset_name: str, 
                      backup_path: Optional[Union[str, Path]] = None) -> str:
        """备份数据集
        
        Args:
            dataset_name: 数据集名称
            backup_path: 备份路径，None表示使用默认路径
            
        Returns:
            str: 备份文件路径
        """
        pass
    
    @abstractmethod
    def restore_dataset(self, backup_path: Union[str, Path], 
                       dataset_name: str) -> bool:
        """恢复数据集
        
        Args:
            backup_path: 备份文件路径
            dataset_name: 数据集名称
            
        Returns:
            bool: 是否成功恢复
        """
        pass
