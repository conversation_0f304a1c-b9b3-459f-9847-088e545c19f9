"""
交叉验证模块

实现交叉验证方法，包括角色交换验证、对抗自我验证、与规则型AI对比和人类专家评估，
用于评估训练效果并指导模型改进。
"""
import os
import time
import logging
import numpy as np
from typing import Dict, List, Tuple, Any, Optional, Union, Callable

from cardgame_ai.core.agent import Agent
from cardgame_ai.core.environment import Environment
from cardgame_ai.algorithms.role_specific_mappo import RoleSpecificMAPPO
from cardgame_ai.algorithms.rule_based import RuleBasedAgent


class CrossValidator:
    """
    交叉验证器

    实现多种交叉验证方法，用于评估训练效果并指导模型改进。
    """

    def __init__(self, env: Environment):
        """
        初始化交叉验证器

        Args:
            env: 游戏环境
        """
        self.env = env

        # 验证结果
        self.results = {}

        # 验证统计
        self.stats = {
            "total_validations": 0,
            "total_games": 0,
            "validation_time": 0
        }

    def role_swap_validation(
        self,
        landlord_model: RoleSpecificMAPPO,
        farmer1_model: RoleSpecificMAPPO,
        farmer2_model: RoleSpecificMAPPO,
        num_games: int = 100
    ) -> Dict[str, float]:
        """
        角色交换验证

        让每个模型在非专长角色上进行测试，检测模型是否过度专门化或存在角色错觉。

        Args:
            landlord_model: 地主模型
            farmer1_model: 农民1模型
            farmer2_model: 农民2模型
            num_games: 验证游戏局数

        Returns:
            验证结果字典
        """
        logging.info(f"开始角色交换验证，计划进行{num_games}局游戏...")
        start_time = time.time()

        # 正常角色分配的验证（基准）
        normal_results = self._evaluate_models(
            landlord_model=landlord_model,
            farmer1_model=farmer1_model,
            farmer2_model=farmer2_model,
            num_games=num_games // 3
        )

        # 角色交换验证1：地主模型扮演农民1
        swap1_results = self._evaluate_models(
            landlord_model=farmer1_model,  # 农民1模型扮演地主
            farmer1_model=landlord_model,  # 地主模型扮演农民1
            farmer2_model=farmer2_model,   # 农民2模型扮演农民2
            num_games=num_games // 3
        )

        # 角色交换验证2：地主模型扮演农民2
        swap2_results = self._evaluate_models(
            landlord_model=farmer2_model,  # 农民2模型扮演地主
            farmer1_model=farmer1_model,   # 农民1模型扮演农民1
            farmer2_model=landlord_model,  # 地主模型扮演农民2
            num_games=num_games // 3
        )

        # 计算角色混淆分数
        role_confusion_score = self._calculate_role_confusion(
            normal_results, swap1_results, swap2_results
        )

        # 合并结果
        results = {
            "normal": normal_results,
            "swap1": swap1_results,
            "swap2": swap2_results,
            "role_confusion_score": role_confusion_score
        }

        # 更新统计
        self.stats["total_validations"] += 1
        self.stats["total_games"] += num_games
        self.stats["validation_time"] += time.time() - start_time

        # 保存结果
        self.results["role_swap"] = results

        logging.info(f"角色交换验证完成，角色混淆分数：{role_confusion_score:.4f}")

        return results

    def self_play_validation(
        self,
        old_landlord_model: RoleSpecificMAPPO,
        old_farmer1_model: RoleSpecificMAPPO,
        old_farmer2_model: RoleSpecificMAPPO,
        new_landlord_model: RoleSpecificMAPPO,
        new_farmer1_model: RoleSpecificMAPPO,
        new_farmer2_model: RoleSpecificMAPPO,
        num_games: int = 100
    ) -> Dict[str, float]:
        """
        对抗自我验证

        不同版本的模型相互对抗，测量模型的进步。

        Args:
            old_landlord_model: 旧版地主模型
            old_farmer1_model: 旧版农民1模型
            old_farmer2_model: 旧版农民2模型
            new_landlord_model: 新版地主模型
            new_farmer1_model: 新版农民1模型
            new_farmer2_model: 新版农民2模型
            num_games: 验证游戏局数

        Returns:
            验证结果字典
        """
        logging.info(f"开始对抗自我验证，计划进行{num_games}局游戏...")
        start_time = time.time()

        # 新版地主对抗旧版农民
        new_landlord_results = self._evaluate_models(
            landlord_model=new_landlord_model,
            farmer1_model=old_farmer1_model,
            farmer2_model=old_farmer2_model,
            num_games=num_games // 2
        )

        # 旧版地主对抗新版农民
        new_farmers_results = self._evaluate_models(
            landlord_model=old_landlord_model,
            farmer1_model=new_farmer1_model,
            farmer2_model=new_farmer2_model,
            num_games=num_games // 2
        )

        # 计算进步分数
        progress_score = self._calculate_progress(
            new_landlord_results, new_farmers_results
        )

        # 合并结果
        results = {
            "new_landlord": new_landlord_results,
            "new_farmers": new_farmers_results,
            "progress_score": progress_score
        }

        # 更新统计
        self.stats["total_validations"] += 1
        self.stats["total_games"] += num_games
        self.stats["validation_time"] += time.time() - start_time

        # 保存结果
        self.results["self_play"] = results

        logging.info(f"对抗自我验证完成，进步分数：{progress_score:.4f}")

        return results

    def _evaluate_models(
        self,
        landlord_model: RoleSpecificMAPPO,
        farmer1_model: RoleSpecificMAPPO,
        farmer2_model: RoleSpecificMAPPO,
        num_games: int = 100
    ) -> Dict[str, float]:
        """
        评估模型

        让三个模型相互对抗，计算胜率等指标。

        Args:
            landlord_model: 地主模型
            farmer1_model: 农民1模型
            farmer2_model: 农民2模型
            num_games: 游戏局数

        Returns:
            评估结果字典
        """
        # 创建智能体
        agents = {
            "landlord": Agent(algorithm=landlord_model, name="landlord"),
            "farmer1": Agent(algorithm=farmer1_model, name="farmer1"),
            "farmer2": Agent(algorithm=farmer2_model, name="farmer2")
        }

        # 初始化统计
        stats = {
            "landlord_wins": 0,
            "farmers_wins": 0,
            "total_games": 0,
            "avg_game_length": 0,
            "landlord_avg_reward": 0,
            "farmers_avg_reward": 0
        }

        # 进行游戏
        for i in range(num_games):
            # 重置环境
            state = self.env.reset()

            # 游戏循环
            game_length = 0
            rewards = {"landlord": 0, "farmer1": 0, "farmer2": 0}

            while True:
                # 获取当前玩家
                current_player = state.current_player

                # 确定角色
                if current_player == state.landlord:
                    role = "landlord"
                elif current_player == (state.landlord + 1) % 3:
                    role = "farmer1"
                else:
                    role = "farmer2"

                # 使用对应智能体做决策
                agent = agents[role]
                action = agent.act(state)

                # 执行动作
                next_state, reward, done, info = self.env.step(action)

                # 更新统计
                rewards[role] += reward
                game_length += 1

                # 检查游戏是否结束
                if done:
                    break

                # 更新状态
                state = next_state

            # 更新统计
            stats["total_games"] += 1
            stats["avg_game_length"] += game_length
            stats["landlord_avg_reward"] += rewards["landlord"]
            stats["farmers_avg_reward"] += (rewards["farmer1"] + rewards["farmer2"]) / 2

            # 更新胜率
            if rewards["landlord"] > 0:
                stats["landlord_wins"] += 1
            else:
                stats["farmers_wins"] += 1

        # 计算平均值
        if stats["total_games"] > 0:
            stats["avg_game_length"] /= stats["total_games"]
            stats["landlord_avg_reward"] /= stats["total_games"]
            stats["farmers_avg_reward"] /= stats["total_games"]
            stats["landlord_win_rate"] = stats["landlord_wins"] / stats["total_games"]
            stats["farmers_win_rate"] = stats["farmers_wins"] / stats["total_games"]

        return stats

    def _calculate_role_confusion(
        self,
        normal_results: Dict[str, float],
        swap1_results: Dict[str, float],
        swap2_results: Dict[str, float]
    ) -> float:
        """
        计算角色混淆分数

        基于正常角色分配和角色交换后的性能差异计算角色混淆分数。
        分数越低表示角色适应性越强，越高表示角色混淆越严重。

        Args:
            normal_results: 正常角色分配的结果
            swap1_results: 角色交换1的结果
            swap2_results: 角色交换2的结果

        Returns:
            角色混淆分数，0到1之间的值
        """
        # 提取胜率
        normal_landlord_win_rate = normal_results.get("landlord_win_rate", 0.5)
        normal_farmers_win_rate = normal_results.get("farmers_win_rate", 0.5)

        swap1_landlord_win_rate = swap1_results.get("landlord_win_rate", 0.5)
        swap1_farmers_win_rate = swap1_results.get("farmers_win_rate", 0.5)

        swap2_landlord_win_rate = swap2_results.get("landlord_win_rate", 0.5)
        swap2_farmers_win_rate = swap2_results.get("farmers_win_rate", 0.5)

        # 计算角色交换后的性能下降
        landlord_drop1 = max(0, normal_landlord_win_rate - swap1_farmers_win_rate)
        landlord_drop2 = max(0, normal_landlord_win_rate - swap2_farmers_win_rate)

        farmer1_drop = max(0, normal_farmers_win_rate - swap1_landlord_win_rate)
        farmer2_drop = max(0, normal_farmers_win_rate - swap2_landlord_win_rate)

        # 计算平均性能下降
        avg_drop = (landlord_drop1 + landlord_drop2 + farmer1_drop + farmer2_drop) / 4

        # 将下降转换为角色混淆分数（0到1之间）
        # 下降越大，混淆越严重
        confusion_score = min(1.0, avg_drop * 2)  # 乘以2是为了放大分数范围

        return confusion_score

    def _calculate_progress(
        self,
        new_landlord_results: Dict[str, float],
        new_farmers_results: Dict[str, float]
    ) -> float:
        """
        计算进步分数

        基于新版模型对抗旧版模型的性能计算进步分数。
        分数越高表示进步越显著。

        Args:
            new_landlord_results: 新版地主对抗旧版农民的结果
            new_farmers_results: 旧版地主对抗新版农民的结果

        Returns:
            进步分数，0到1之间的值
        """
        # 提取胜率
        new_landlord_win_rate = new_landlord_results.get("landlord_win_rate", 0.5)
        new_farmers_win_rate = new_farmers_results.get("farmers_win_rate", 0.5)

        # 计算进步分数
        # 如果新版地主和新版农民都比旧版对手强，则进步显著
        landlord_progress = max(0, new_landlord_win_rate - 0.5)  # 0.5是平衡胜率
        farmers_progress = max(0, new_farmers_win_rate - 0.5)  # 0.5是平衡胜率

        # 综合进步分数
        progress_score = min(1.0, (landlord_progress + farmers_progress))  # 最大为1

        return progress_score

    def rule_based_validation(
        self,
        landlord_model: RoleSpecificMAPPO,
        farmer1_model: RoleSpecificMAPPO,
        farmer2_model: RoleSpecificMAPPO,
        difficulty: str = 'medium',
        num_games: int = 100
    ) -> Dict[str, Any]:
        """
        与规则型AI对比验证

        将训练的模型与不同难度的规则型AI进行对比，提供稳定的基准评估。

        Args:
            landlord_model: 地主模型
            farmer1_model: 农民1模型
            farmer2_model: 农民2模型
            difficulty: 规则型AI的难度，可选 'easy', 'medium', 'hard'
            num_games: 验证游戏局数

        Returns:
            验证结果字典
        """
        logging.info(f"开始与规则型AI对比验证，难度：{difficulty}，计划进行{num_games}局游戏...")
        start_time = time.time()

        # 创建规则型AI
        rule_based_agent = RuleBasedAgent(difficulty=difficulty)

        # 测试1：模型地主 vs 规则型农民
        model_landlord_results = self._evaluate_with_rule_based(
            landlord_model=landlord_model,
            farmer1_model=None,
            farmer2_model=None,
            rule_based_agent=rule_based_agent,
            rule_based_roles=["farmer1", "farmer2"],
            num_games=num_games // 2
        )

        # 测试2：规则型地主 vs 模型农民
        model_farmers_results = self._evaluate_with_rule_based(
            landlord_model=None,
            farmer1_model=farmer1_model,
            farmer2_model=farmer2_model,
            rule_based_agent=rule_based_agent,
            rule_based_roles=["landlord"],
            num_games=num_games // 2
        )

        # 计算相对于规则型AI的性能分数
        performance_score = self._calculate_rule_based_performance(
            model_landlord_results, model_farmers_results, difficulty
        )

        # 合并结果
        results = {
            "model_landlord": model_landlord_results,
            "model_farmers": model_farmers_results,
            "performance_score": performance_score,
            "difficulty": difficulty
        }

        # 更新统计
        self.stats["total_validations"] += 1
        self.stats["total_games"] += num_games
        self.stats["validation_time"] += time.time() - start_time

        # 保存结果
        self.results[f"rule_based_{difficulty}"] = results

        logging.info(f"与规则型AI对比验证完成，性能分数：{performance_score:.4f}")

        return results

    def _evaluate_with_rule_based(
        self,
        landlord_model: Optional[RoleSpecificMAPPO],
        farmer1_model: Optional[RoleSpecificMAPPO],
        farmer2_model: Optional[RoleSpecificMAPPO],
        rule_based_agent: RuleBasedAgent,
        rule_based_roles: List[str],
        num_games: int = 50
    ) -> Dict[str, float]:
        """
        评估模型与规则型AI的对抗

        让模型与规则型AI对抗，计算胜率等指标。

        Args:
            landlord_model: 地主模型，如果为None则使用规则型AI
            farmer1_model: 农民1模型，如果为None则使用规则型AI
            farmer2_model: 农民2模型，如果为None则使用规则型AI
            rule_based_agent: 规则型AI代理
            rule_based_roles: 规则型AI扮演的角色列表
            num_games: 游戏局数

        Returns:
            评估结果字典
        """
        # 创建智能体
        agents = {}

        # 根据角色分配模型或规则型AI
        if "landlord" in rule_based_roles:
            agents["landlord"] = rule_based_agent
        else:
            agents["landlord"] = Agent(algorithm=landlord_model, name="landlord")

        if "farmer1" in rule_based_roles:
            agents["farmer1"] = rule_based_agent
        else:
            agents["farmer1"] = Agent(algorithm=farmer1_model, name="farmer1")

        if "farmer2" in rule_based_roles:
            agents["farmer2"] = rule_based_agent
        else:
            agents["farmer2"] = Agent(algorithm=farmer2_model, name="farmer2")

        # 初始化统计
        stats = {
            "landlord_wins": 0,
            "farmers_wins": 0,
            "total_games": 0,
            "avg_game_length": 0,
            "landlord_avg_reward": 0,
            "farmers_avg_reward": 0,
            "model_wins": 0,  # 模型获胜次数
            "rule_based_wins": 0  # 规则型AI获胜次数
        }

        # 进行游戏
        for i in range(num_games):
            # 重置环境
            state = self.env.reset()

            # 游戏循环
            game_length = 0
            rewards = {"landlord": 0, "farmer1": 0, "farmer2": 0}

            while True:
                # 获取当前玩家
                current_player = state.current_player

                # 确定角色
                if current_player == state.landlord:
                    role = "landlord"
                elif current_player == (state.landlord + 1) % 3:
                    role = "farmer1"
                else:
                    role = "farmer2"

                # 使用对应智能体做决策
                agent = agents[role]
                action = agent.act(state)

                # 执行动作
                next_state, reward, done, info = self.env.step(action)

                # 更新统计
                rewards[role] += reward
                game_length += 1

                # 检查游戏是否结束
                if done:
                    break

                # 更新状态
                state = next_state

            # 更新统计
            stats["total_games"] += 1
            stats["avg_game_length"] += game_length
            stats["landlord_avg_reward"] += rewards["landlord"]
            stats["farmers_avg_reward"] += (rewards["farmer1"] + rewards["farmer2"]) / 2

            # 更新胜率
            if rewards["landlord"] > 0:
                stats["landlord_wins"] += 1
                if "landlord" not in rule_based_roles:
                    stats["model_wins"] += 1
                else:
                    stats["rule_based_wins"] += 1
            else:
                stats["farmers_wins"] += 1
                if "farmer1" not in rule_based_roles or "farmer2" not in rule_based_roles:
                    stats["model_wins"] += 1
                else:
                    stats["rule_based_wins"] += 1

        # 计算平均值
        if stats["total_games"] > 0:
            stats["avg_game_length"] /= stats["total_games"]
            stats["landlord_avg_reward"] /= stats["total_games"]
            stats["farmers_avg_reward"] /= stats["total_games"]
            stats["landlord_win_rate"] = stats["landlord_wins"] / stats["total_games"]
            stats["farmers_win_rate"] = stats["farmers_wins"] / stats["total_games"]
            stats["model_win_rate"] = stats["model_wins"] / stats["total_games"]
            stats["rule_based_win_rate"] = stats["rule_based_wins"] / stats["total_games"]

        return stats

    def _calculate_rule_based_performance(
        self,
        model_landlord_results: Dict[str, float],
        model_farmers_results: Dict[str, float],
        difficulty: str
    ) -> float:
        """
        计算相对于规则型AI的性能分数

        基于模型对抗规则型AI的胜率计算性能分数。
        分数越高表示模型相对于规则型AI越强。

        Args:
            model_landlord_results: 模型地主对抗规则型农民的结果
            model_farmers_results: 规则型地主对抗模型农民的结果
            difficulty: 规则型AI的难度

        Returns:
            性能分数，0到1之间的值
        """
        # 提取胜率
        model_landlord_win_rate = model_landlord_results.get("model_win_rate", 0.5)
        model_farmers_win_rate = model_farmers_results.get("model_win_rate", 0.5)

        # 根据难度设置基准胜率
        if difficulty == 'easy':
            baseline_win_rate = 0.7  # 对抗简单AI应该有较高胜率
        elif difficulty == 'hard':
            baseline_win_rate = 0.3  # 对抗困难AI的基准胜率较低
        else:  # medium
            baseline_win_rate = 0.5  # 对抗中等AI的基准胜率

        # 计算相对于基准的性能
        landlord_performance = max(0, (model_landlord_win_rate - baseline_win_rate) / (1 - baseline_win_rate))
        farmers_performance = max(0, (model_farmers_win_rate - baseline_win_rate) / (1 - baseline_win_rate))

        # 综合性能分数
        performance_score = min(1.0, (landlord_performance + farmers_performance) / 2)

        return performance_score
