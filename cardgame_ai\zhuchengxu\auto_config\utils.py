"""
跨平台工具函数

提供Windows和Ubuntu系统兼容的工具函数，
包括命令执行、路径处理、系统信息获取等。
"""

import os
import sys
import platform
import subprocess
import logging
from typing import Dict, List, Optional, Union
from pathlib import Path

class CrossPlatformUtils:
    """跨平台工具类"""
    
    @staticmethod
    def get_system_info() -> Dict[str, str]:
        """获取系统基本信息"""
        return {
            'system': platform.system(),  # Windows, Linux
            'release': platform.release(),
            'version': platform.version(),
            'machine': platform.machine(),  # x86_64, AMD64
            'processor': platform.processor(),
            'python_version': platform.python_version()
        }
    
    @staticmethod
    def is_windows() -> bool:
        """判断是否为Windows系统"""
        return platform.system().lower() == 'windows'
    
    @staticmethod
    def is_linux() -> bool:
        """判断是否为Linux系统"""
        return platform.system().lower() == 'linux'
    
    @staticmethod
    def execute_command(command: Union[str, List[str]], 
                       shell: bool = None,
                       capture_output: bool = True,
                       timeout: int = 30) -> Dict[str, Union[str, int]]:
        """
        跨平台执行命令
        
        Args:
            command: 要执行的命令
            shell: 是否使用shell执行，None时自动判断
            capture_output: 是否捕获输出
            timeout: 超时时间(秒)
            
        Returns:
            包含返回码、标准输出、标准错误的字典
        """
        if shell is None:
            shell = CrossPlatformUtils.is_windows()
        
        try:
            if isinstance(command, str) and not shell:
                command = command.split()
            
            result = subprocess.run(
                command,
                shell=shell,
                capture_output=capture_output,
                text=True,
                timeout=timeout,
                encoding='utf-8',
                errors='ignore'
            )
            
            return {
                'returncode': result.returncode,
                'stdout': result.stdout.strip() if result.stdout else '',
                'stderr': result.stderr.strip() if result.stderr else '',
                'success': result.returncode == 0
            }
            
        except subprocess.TimeoutExpired:
            return {
                'returncode': -1,
                'stdout': '',
                'stderr': f'Command timeout after {timeout} seconds',
                'success': False
            }
        except Exception as e:
            return {
                'returncode': -1,
                'stdout': '',
                'stderr': str(e),
                'success': False
            }
    
    @staticmethod
    def get_nvidia_smi_command() -> str:
        """获取nvidia-smi命令路径"""
        if CrossPlatformUtils.is_windows():
            # Windows下nvidia-smi通常在PATH中或CUDA安装目录
            possible_paths = [
                'nvidia-smi',
                'C:\\Program Files\\NVIDIA Corporation\\NVSMI\\nvidia-smi.exe',
                'C:\\Windows\\System32\\nvidia-smi.exe'
            ]
        else:
            # Linux下nvidia-smi通常在/usr/bin或/usr/local/cuda/bin
            possible_paths = [
                'nvidia-smi',
                '/usr/bin/nvidia-smi',
                '/usr/local/cuda/bin/nvidia-smi'
            ]
        
        for path in possible_paths:
            result = CrossPlatformUtils.execute_command(
                f'"{path}" --version' if ' ' in path else f'{path} --version',
                timeout=5
            )
            if result['success']:
                return path
        
        return 'nvidia-smi'  # 默认返回，让调用者处理错误
    
    @staticmethod
    def normalize_path(path: Union[str, Path]) -> str:
        """标准化路径，处理Windows和Linux的差异"""
        return str(Path(path).resolve())
    
    @staticmethod
    def setup_logging(log_file: Optional[str] = None, 
                     level: int = logging.INFO) -> logging.Logger:
        """设置跨平台日志记录"""
        logger = logging.getLogger('auto_config')
        logger.setLevel(level)
        
        # 避免重复添加handler
        if logger.handlers:
            return logger
        
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 控制台输出
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # 文件输出
        if log_file:
            log_path = CrossPlatformUtils.normalize_path(log_file)
            os.makedirs(os.path.dirname(log_path), exist_ok=True)
            
            file_handler = logging.FileHandler(log_path, encoding='utf-8')
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        
        return logger
