"""
在线对手建模器模块

提供基于统计的在线对手建模功能，用于跟踪和预测对手行为模式。
"""
from collections import deque, Counter
from typing import Dict, List, Optional, Any, Hashable, Tuple, Union
import logging
import numpy as np
import torch

# 导入基础 Action 类或类型定义 (假设存在)
# from cardgame_ai.core.base import Action # 取消注释并根据实际情况调整
# 临时代替，需要根据项目中 Action 的实际表示修改
Action = Hashable

class OnlineOpponentModeler:
    """
    一个在线对手建模器，基于对手最近的动作统计来预测未来行为。
    
    该建模器支持：
    1. 动态窗口大小的行为跟踪
    2. 计算基于历史的动作先验概率
    3. 与MCTS算法集成的接口
    4. 基于上下文的更新功能
    5. 生成标准化对手表示
    """

    def __init__(self, window_size: int = 10, decay_factor: float = 0.95, enable_logging: bool = False, 
                 representation_dim: int = 32):
        """
        初始化建模器

        Args:
            window_size (int): 用于统计的最近动作窗口大小。
            decay_factor (float): 历史动作权重衰减因子，范围[0,1]。
                                  越接近1，越重视历史数据；越接近0，越重视最新数据。
            enable_logging (bool): 是否启用日志记录功能。
            representation_dim (int): 对手表示的维度。
        """
        if not isinstance(window_size, int) or window_size <= 0:
            raise ValueError("window_size 必须是正整数")
        if not 0.0 <= decay_factor <= 1.0:
            raise ValueError("decay_factor 必须在0到1之间")
            
        self.window_size = window_size
        self.decay_factor = decay_factor
        self.enable_logging = enable_logging
        self.logger = logging.getLogger(__name__) if enable_logging else None
        self.representation_dim = representation_dim
        
        # 存储每个对手最近的动作历史
        # key: player_id, value: deque(maxlen=window_size)
        self.recent_actions: Dict[Any, deque] = {}
        
        # 存储每个对手的动作计数器
        # key: player_id, value: Counter
        self.action_counts: Dict[Any, Counter] = {}
        
        # 存储每个玩家在不同游戏上下文下的动作计数
        # key: (player_id, context_key), value: Counter
        self.context_action_counts: Dict[Tuple[Any, str], Counter] = {}
        
        # 统计信息
        self.stats = {
            "total_updates": 0,
            "players_tracked": 0,
            "context_keys_tracked": 0
        }
        
        # 对手类型的预定义标签
        self.opponent_types = {
            0: "aggressive",    # 激进型对手
            1: "conservative",  # 保守型对手
            2: "balanced",      # 平衡型对手
            3: "unpredictable", # 不可预测型对手
            4: "adaptive"       # 适应性对手
        }

    def update(
        self,
        player_id: Any,
        action: Action,
        game_state: Optional[Any] = None,
        belief_state: Optional[Any] = None
    ) -> None:
        """
        根据对手的最新动作更新模型。

        Args:
            player_id: 对手的唯一标识符。
            action: 对手执行的动作。
            game_state: 当前游戏状态（可选）。
            belief_state: 当前信念状态（可选）。
        """
        # 初始化该玩家的记录（如果尚未存在）
        if player_id not in self.recent_actions:
            self.recent_actions[player_id] = deque(maxlen=self.window_size)
            self.action_counts[player_id] = Counter()
            self.stats["players_tracked"] += 1

        # 如果窗口已满，需要移除最旧的动作计数
        if len(self.recent_actions[player_id]) == self.window_size:
            oldest_action = self.recent_actions[player_id][0]
            if self.action_counts[player_id][oldest_action] > 0:
                 self.action_counts[player_id][oldest_action] -= 1
                 # 如果计数为0，可以从Counter中移除以节省空间
                 if self.action_counts[player_id][oldest_action] == 0:
                     del self.action_counts[player_id][oldest_action]

        # 添加新动作并更新计数
        self.recent_actions[player_id].append(action)
        self.action_counts[player_id][action] += 1
        
        # 更新统计信息
        self.stats["total_updates"] += 1
        
        # 记录日志（如果启用）
        if self.enable_logging:
            self.logger.debug(f"已更新玩家 {player_id} 的动作: {action}")

    def context_aware_update(
        self,
        player_id: Any,
        action: Action,
        context_key: str,
        game_state: Optional[Any] = None
    ) -> None:
        """
        根据特定游戏上下文更新对手模型。
        
        这允许建模器在不同游戏情境下更准确地追踪对手行为。
        例如，可以分别追踪对手在"领先"、"落后"、"手牌数少"等情况下的行为。

        Args:
            player_id: 对手的唯一标识符。
            action: 对手执行的动作。
            context_key: 描述当前游戏上下文的字符串键。
            game_state: 当前游戏状态（可选）。
        """
        # 常规更新
        self.update(player_id, action, game_state)
        
        # 上下文相关更新
        context_pair = (player_id, context_key)
        if context_pair not in self.context_action_counts:
            self.context_action_counts[context_pair] = Counter()
            self.stats["context_keys_tracked"] += 1
            
        # 使用衰减因子更新计数
        for key in list(self.context_action_counts[context_pair].keys()):
            self.context_action_counts[context_pair][key] *= self.decay_factor
            
        # 增加当前动作计数
        self.context_action_counts[context_pair][action] += 1
        
        # 记录日志
        if self.enable_logging:
            self.logger.debug(f"已在上下文 {context_key} 中更新玩家 {player_id} 的动作: {action}")

    def get_prior(
        self,
        player_id: Any,
        possible_actions: List[Action],
        context_key: Optional[str] = None
    ) -> Dict[Action, float]:
        """
        根据当前模型估计对手采取各种可能动作的先验概率。

        Args:
            player_id: 对手的唯一标识符。
            possible_actions: 对手当前所有合法的可能动作列表。
            context_key: 可选的上下文键，用于获取特定情境下的先验。

        Returns:
            一个字典，键是可能的动作，值是对应的估计先验概率。
            如果该玩家没有历史记录，则返回均匀分布。
        """
        # 如果提供了上下文键，优先使用上下文相关的计数
        if context_key and (player_id, context_key) in self.context_action_counts:
            counts = self.context_action_counts[(player_id, context_key)]
            if sum(counts.values()) > 0:  # 确保有足够的数据
                return self._calculate_prior_from_counts(counts, possible_actions)
        
        # 回退到一般计数
        if player_id not in self.action_counts or not self.action_counts[player_id]:
            # 没有历史记录，返回均匀分布
            num_possible = len(possible_actions)
            if num_possible == 0:
                return {}
            uniform_prob = 1.0 / num_possible
            return {action: uniform_prob for action in possible_actions}

        return self._calculate_prior_from_counts(self.action_counts[player_id], possible_actions)

    def _calculate_prior_from_counts(
        self, 
        counts: Counter, 
        possible_actions: List[Action]
    ) -> Dict[Action, float]:
        """
        从动作计数器计算先验概率。
        
        Args:
            counts: 动作计数器。
            possible_actions: 可能的动作列表。
            
        Returns:
            动作到先验概率的映射字典。
        """
        # 计算先验概率
        prior_dict: Dict[Action, float] = {}
        total_observed_count = sum(counts.values())

        # 使用拉普拉斯平滑 (加1平滑)
        num_possible = len(possible_actions)
        denominator = total_observed_count + num_possible

        observed_possible_counts = Counter()
        for action in possible_actions:
            observed_possible_counts[action] = counts.get(action, 0)

        total_normalized_prob = 0.0
        for action in possible_actions:
            prob = (observed_possible_counts[action] + 1) / denominator
            prior_dict[action] = prob
            total_normalized_prob += prob

        # 归一化确保概率和为1 (理论上不需要，但可以增加数值稳定性)
        if total_normalized_prob > 0 and abs(total_normalized_prob - 1.0) > 1e-6:
             scale_factor = 1.0 / total_normalized_prob
             for action in prior_dict:
                 prior_dict[action] *= scale_factor

        # 确保所有可能动作都在字典中（即使概率接近0）
        for action in possible_actions:
            if action not in prior_dict:
                prior_dict[action] = 0.0  # 或一个极小值

        return prior_dict

    def convert_to_mcts_priors(
        self, 
        player_id: Any, 
        possible_actions: List[Action],
        action_to_id_func: Optional[callable] = None,
        context_key: Optional[str] = None
    ) -> Dict[int, float]:
        """
        将动作先验概率转换为MCTS算法使用的格式。
        
        MCTS算法通常使用整数ID来表示动作，此方法将动作对象映射到相应的ID。

        Args:
            player_id: 对手的唯一标识符。
            possible_actions: 对手当前所有合法的可能动作列表。
            action_to_id_func: 将动作对象转换为整数ID的函数。如果为None，则使用hash值。
            context_key: 可选的上下文键，用于获取特定情境下的先验。
            
        Returns:
            一个字典，键是动作ID，值是对应的先验概率。
        """
        # 获取动作先验
        action_priors = self.get_prior(player_id, possible_actions, context_key)
        
        # 定义默认的动作到ID的转换函数
        def default_action_to_id(action):
            if action is None:
                return 0
            if isinstance(action, (int, np.integer)):
                return int(action)
            # 使用哈希值作为ID
            return abs(hash(str(action))) % 10000
        
        # 使用提供的函数或默认函数
        convert_func = action_to_id_func or default_action_to_id
        
        # 转换为ID映射
        id_priors = {}
        for action, prior in action_priors.items():
            action_id = convert_func(action)
            id_priors[action_id] = prior
            
        return id_priors

    def get_action_info(
        self, 
        player_id: Any, 
        action: Action,
        context_key: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取特定动作的详细统计信息。
        
        Args:
            player_id: 对手的唯一标识符。
            action: 要查询的动作。
            context_key: 可选的上下文键。
            
        Returns:
            包含该动作统计信息的字典。
        """
        result = {
            "action": action,
            "count": 0,
            "frequency": 0.0,
            "recency": [],
            "context_count": 0,
            "context_frequency": 0.0
        }
        
        # 检查玩家是否存在
        if player_id not in self.action_counts:
            return result
            
        # 获取基础统计
        counts = self.action_counts[player_id]
        total_count = sum(counts.values()) or 1  # 避免除零错误
        action_count = counts.get(action, 0)
        
        result["count"] = action_count
        result["frequency"] = action_count / total_count
        
        # 获取最近出现情况
        if player_id in self.recent_actions:
            recent_actions = list(self.recent_actions[player_id])
            result["recency"] = [i for i, a in enumerate(recent_actions) if a == action]
            
        # 获取上下文相关统计
        if context_key and (player_id, context_key) in self.context_action_counts:
            context_counts = self.context_action_counts[(player_id, context_key)]
            context_total = sum(context_counts.values()) or 1
            context_action_count = context_counts.get(action, 0)
            
            result["context_count"] = context_action_count
            result["context_frequency"] = context_action_count / context_total
            
        return result
    
    def get_player_profile(self, player_id: Any) -> Dict[str, Any]:
        """
        生成玩家的行为概况。
        
        Args:
            player_id: 对手的唯一标识符。
            
        Returns:
            包含玩家行为模式的字典。
        """
        if player_id not in self.action_counts:
            return {"player_id": player_id, "tracked": False}
            
        # 基本信息
        profile = {
            "player_id": player_id,
            "tracked": True,
            "total_actions_tracked": sum(self.action_counts[player_id].values()),
            "unique_actions": len(self.action_counts[player_id]),
            "most_common_actions": self.action_counts[player_id].most_common(5),
            "contexts_tracked": []
        }
        
        # 上下文信息
        for (pid, context), _ in self.context_action_counts.items():
            if pid == player_id:
                profile["contexts_tracked"].append(context)
                
        return profile

    def reset(self, player_id: Optional[Any] = None) -> None:
        """
        重置指定玩家或所有玩家的建模状态。

        Args:
            player_id: 要重置的玩家ID。如果为None，则重置所有玩家。
        """
        if player_id is not None:
            if player_id in self.recent_actions:
                del self.recent_actions[player_id]
            if player_id in self.action_counts:
                del self.action_counts[player_id]
                
            # 重置上下文相关数据
            context_keys = [k for k in self.context_action_counts.keys() if k[0] == player_id]
            for key in context_keys:
                del self.context_action_counts[key]
                
            if self.enable_logging:
                self.logger.info(f"已重置玩家 {player_id} 的建模数据")
        else:
            self.recent_actions.clear()
            self.action_counts.clear()
            self.context_action_counts.clear()
            
            # 重置统计信息
            self.stats = {
                "total_updates": 0,
                "players_tracked": 0,
                "context_keys_tracked": 0
            }
            
            if self.enable_logging:
                self.logger.info("已重置所有建模数据")

    def get_model(
        self,
        player_id: Any,
        context_key: Optional[str] = None,
        as_tensor: bool = False,
        include_labels: bool = False
    ) -> Union[Dict[str, Any], torch.Tensor, np.ndarray]:
        """
        获取对手的标准化表示，可用于决策系统输入。
        
        Args:
            player_id: 对手的唯一标识符。
            context_key: 可选的上下文键，用于获取特定情境下的表示。
            as_tensor: 是否返回PyTorch张量，默认为False返回字典或NumPy数组。
            include_labels: 是否在字典中包含标签，仅当as_tensor=False时有效。
            
        Returns:
            如果as_tensor=True，返回对手表示的张量；
            否则，返回包含多种对手特征的字典或NumPy数组。
        """
        # 检查玩家是否存在
        if player_id not in self.action_counts:
            # 如果玩家不存在，返回默认表示
            if as_tensor:
                return torch.zeros(self.representation_dim)
            else:
                default_rep = {
                    "representation": np.zeros(self.representation_dim),
                    "is_known": False,
                    "tracking_time": 0
                }
                if include_labels:
                    default_rep["type"] = "unknown"
                    default_rep["type_id"] = -1
                return default_rep
        
        # 获取玩家概况
        profile = self.get_player_profile(player_id)
        
        # 分析并提取特征
        # 1. 行动多样性: 独特动作数与总动作数之比
        action_diversity = min(1.0, profile.get("unique_actions", 0) / max(1, profile.get("total_actions_tracked", 1)))
        
        # 2. 提取最常见动作的频率分布
        top_actions = profile.get("most_common_actions", [])
        top_action_freqs = []
        for _ in range(5):  # 确保始终有5个元素
            if top_actions:
                _, count = top_actions.pop(0)
                freq = count / max(1, profile.get("total_actions_tracked", 1))
                top_action_freqs.append(freq)
            else:
                top_action_freqs.append(0.0)
        
        # 3. 动作重复率: 最常用动作的使用频率
        repetitiveness = top_action_freqs[0] if top_action_freqs else 0.0
        
        # 4. 上下文敏感度: 跟踪的上下文数量
        context_sensitivity = min(1.0, len(profile.get("contexts_tracked", [])) / 5.0)  # 假设最多5个上下文
        
        # 确定对手类型
        opponent_type_id = self._determine_opponent_type(action_diversity, repetitiveness, context_sensitivity)
        opponent_type = self.opponent_types.get(opponent_type_id, "unknown")
        
        # 构建特征向量
        features = np.array([
            action_diversity,
            repetitiveness,
            context_sensitivity,
            *top_action_freqs,
            profile.get("total_actions_tracked", 0) / 100.0,  # 归一化的跟踪动作总数
        ])
        
        # 如果提供了上下文，获取上下文特定特征
        if context_key and (player_id, context_key) in self.context_action_counts:
            context_counts = self.context_action_counts[(player_id, context_key)]
            context_total = sum(context_counts.values()) or 1
            
            # 上下文中最常见动作的频率
            context_common = context_counts.most_common(1)
            context_top_freq = context_common[0][1] / context_total if context_common else 0.0
            
            # 添加上下文特定特征
            context_features = np.array([
                context_top_freq,
                len(context_counts) / max(1, profile.get("unique_actions", 1)),  # 上下文动作多样性比
                context_total / max(1, profile.get("total_actions_tracked", 1))  # 上下文数据占比
            ])
            
            # 扩展特征向量
            features = np.concatenate([features, context_features])
        else:
            # 如果没有上下文数据，添加零填充
            features = np.concatenate([features, np.zeros(3)])
        
        # 确保维度正确
        if len(features) < self.representation_dim:
            # 如果特征不足，用零填充
            features = np.pad(features, (0, self.representation_dim - len(features)))
        elif len(features) > self.representation_dim:
            # 如果特征过多，截断
            features = features[:self.representation_dim]
        
        # 返回结果
        if as_tensor:
            return torch.FloatTensor(features)
        
        result = {
            "representation": features,
            "is_known": True,
            "tracking_time": profile.get("total_actions_tracked", 0)
        }
        
        if include_labels:
            result["type"] = opponent_type
            result["type_id"] = opponent_type_id
            
        return result
    
    def _determine_opponent_type(self, diversity: float, repetitiveness: float, context_sensitivity: float) -> int:
        """
        基于行为特征确定对手类型。
        
        Args:
            diversity: 动作多样性。
            repetitiveness: 动作重复率。
            context_sensitivity: 上下文敏感度。
            
        Returns:
            对手类型ID。
        """
        # 激进型对手: 多样性低、重复率高
        if diversity < 0.3 and repetitiveness > 0.6:
            return 0
            
        # 保守型对手: 多样性适中、重复率适中、上下文敏感度高
        if 0.3 <= diversity <= 0.5 and 0.4 <= repetitiveness <= 0.6 and context_sensitivity > 0.6:
            return 1
            
        # 平衡型对手: 多样性适中、重复率适中、上下文敏感度适中
        if 0.3 <= diversity <= 0.6 and 0.3 <= repetitiveness <= 0.5 and 0.3 <= context_sensitivity <= 0.6:
            return 2
            
        # 不可预测型对手: 多样性高、重复率低
        if diversity > 0.7 and repetitiveness < 0.3:
            return 3
            
        # 适应性对手: 上下文敏感度高
        if context_sensitivity > 0.7:
            return 4
            
        # 默认为平衡型
        return 2 