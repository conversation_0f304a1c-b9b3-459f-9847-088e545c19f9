# 项目结构文档

## 概述

本文档定义了斗地主AI优化项目的标准目录结构和文件组织规范。

## 根目录结构

```
cardgame_ai/
├── .github/                    # CI/CD工作流
│   └── workflows/
│       ├── training.yml        # 训练流水线
│       ├── testing.yml         # 测试流水线
│       └── quality.yml         # 代码质量检查
├── .ai/                        # AI开发工具
│   └── TODO-revert.md          # 调试日志
├── algorithms/                 # 核心AI算法
│   ├── __init__.py
│   ├── efficient_zero/         # EfficientZero算法实现
│   │   ├── __init__.py
│   │   ├── core.py            # 核心算法
│   │   ├── model.py           # 神经网络模型
│   │   ├── mcts.py            # MCTS搜索
│   │   └── config.py          # 算法配置
│   ├── multi_agent/           # 多智能体协作
│   │   ├── __init__.py
│   │   ├── mappo.py           # MAPPO算法
│   │   ├── cooperation.py     # 协作机制
│   │   └── communication.py   # 通信协议
│   ├── mcts/                  # 蒙特卡洛树搜索
│   │   ├── __init__.py
│   │   ├── search.py          # 搜索算法
│   │   ├── node.py            # 搜索节点
│   │   └── policies.py        # 搜索策略
│   └── components/            # 算法组件
│       ├── __init__.py
│       ├── networks.py        # 神经网络组件
│       ├── optimizers.py      # 优化器
│       └── schedulers.py      # 调度器
├── training/                  # 训练系统
│   ├── __init__.py
│   ├── distributed/           # 分布式训练
│   │   ├── __init__.py
│   │   ├── trainer.py         # 分布式训练器
│   │   ├── worker.py          # 训练工作节点
│   │   └── coordinator.py     # 训练协调器
│   ├── self_play/            # 自对弈训练
│   │   ├── __init__.py
│   │   ├── engine.py          # 自对弈引擎
│   │   └── collector.py       # 数据收集器
│   ├── config/               # 训练配置
│   │   ├── __init__.py
│   │   ├── base.py            # 基础配置
│   │   └── experiments/       # 实验配置
│   └── schedulers/           # 训练调度
│       ├── __init__.py
│       └── phased.py          # 分阶段训练
├── evaluation/               # 评估系统
│   ├── __init__.py
│   ├── evaluators/           # 评估器
│   │   ├── __init__.py
│   │   ├── performance.py     # 性能评估
│   │   └── benchmark.py       # 基准测试
│   ├── benchmarks/           # 基准测试
│   │   ├── __init__.py
│   │   └── human_expert.py    # 人类专家基准
│   └── metrics/              # 性能指标
│       ├── __init__.py
│       ├── win_rate.py        # 胜率指标
│       └── efficiency.py     # 效率指标
├── games/                    # 游戏环境
│   ├── __init__.py
│   └── doudizhu/            # 斗地主游戏实现
│       ├── __init__.py
│       ├── environment.py     # 游戏环境
│       ├── state.py          # 游戏状态
│       ├── action.py         # 游戏动作
│       └── rules.py          # 游戏规则
├── utils/                    # 工具库
│   ├── __init__.py
│   ├── config/              # 配置管理
│   │   ├── __init__.py
│   │   ├── manager.py         # 配置管理器
│   │   └── validator.py       # 配置验证
│   ├── logging/             # 日志系统
│   │   ├── __init__.py
│   │   ├── logger.py          # 日志记录器
│   │   └── formatters.py      # 日志格式化
│   └── monitoring/          # 监控工具
│       ├── __init__.py
│       ├── metrics.py         # 指标收集
│       └── dashboard.py       # 监控面板
├── interface/               # 外部接口
│   ├── __init__.py
│   ├── api/                 # API接口
│   │   ├── __init__.py
│   │   ├── training.py        # 训练API
│   │   └── evaluation.py      # 评估API
│   └── cli/                 # 命令行工具
│       ├── __init__.py
│       ├── train.py           # 训练命令
│       └── evaluate.py        # 评估命令
├── tests/                   # 测试代码
│   ├── __init__.py
│   ├── unit/                # 单元测试
│   │   ├── test_algorithms/   # 算法测试
│   │   ├── test_training/     # 训练测试
│   │   └── test_utils/        # 工具测试
│   ├── integration/         # 集成测试
│   │   ├── test_training_pipeline.py
│   │   └── test_evaluation_pipeline.py
│   └── performance/         # 性能测试
│       ├── test_training_speed.py
│       └── test_inference_speed.py
├── configs/                 # 配置文件
│   ├── base.yaml            # 基础配置
│   ├── training/            # 训练配置
│   │   ├── efficient_zero.yaml
│   │   └── multi_agent.yaml
│   └── evaluation/          # 评估配置
│       └── benchmark.yaml
├── models/                  # 模型存储
│   ├── checkpoints/         # 训练检查点
│   ├── pretrained/          # 预训练模型
│   └── exports/             # 导出模型
├── logs/                    # 日志文件
│   ├── training/            # 训练日志
│   ├── evaluation/          # 评估日志
│   └── system/              # 系统日志
├── docs/                    # 项目文档
│   ├── project-structure.md  # 本文档
│   ├── operational-guidelines.md
│   ├── tech-stack.md
│   ├── stories/             # 开发故事
│   └── checklists/          # 检查清单
├── scripts/                 # 工具脚本
│   ├── setup.py             # 环境设置
│   ├── train.py             # 训练脚本
│   └── evaluate.py          # 评估脚本
├── requirements.txt         # Python依赖
├── pyproject.toml          # 项目配置
├── .gitignore              # Git忽略文件
├── .pre-commit-config.yaml # 预提交钩子
└── README.md               # 项目说明
```

## 文件命名规范

### Python文件
- 模块文件：`snake_case.py`
- 类文件：`snake_case.py` (类名使用PascalCase)
- 测试文件：`test_*.py`
- 配置文件：`snake_case.py`

### 配置文件
- YAML配置：`snake_case.yaml`
- JSON配置：`snake_case.json`
- 环境配置：`.env.example`

### 文档文件
- Markdown文档：`kebab-case.md`
- 故事文件：`{epic}.{story}.story.md`
- 检查清单：`kebab-case-checklist.txt`

## 模块导入规范

### 相对导入
```python
# 同级模块
from .module import Class

# 上级模块
from ..parent import function

# 子模块
from .submodule.module import Class
```

### 绝对导入
```python
# 从项目根导入
from cardgame_ai.algorithms.efficient_zero import EfficientZero
from cardgame_ai.training.distributed import DistributedTrainer
```

## 配置文件组织

### 层次化配置
- `configs/base.yaml`: 基础配置
- `configs/training/`: 训练相关配置
- `configs/evaluation/`: 评估相关配置
- `configs/experiments/`: 实验配置

### 环境特定配置
- `configs/dev/`: 开发环境配置
- `configs/staging/`: 测试环境配置
- `configs/prod/`: 生产环境配置

## 日志文件组织

### 按功能分类
- `logs/training/`: 训练日志
- `logs/evaluation/`: 评估日志
- `logs/system/`: 系统日志

### 按时间归档
- 日志文件格式：`{component}_{date}.log`
- 自动轮转和压缩
- 保留策略：30天

## 模型文件组织

### 检查点管理
- `models/checkpoints/{experiment_id}/`: 实验检查点
- `models/checkpoints/latest/`: 最新检查点
- `models/checkpoints/best/`: 最佳性能检查点

### 版本控制
- 模型版本格式：`v{major}.{minor}.{patch}`
- 元数据文件：`model_info.json`
- 性能指标：`performance_metrics.json`
