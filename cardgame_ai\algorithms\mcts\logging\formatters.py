"""
MCTS日志格式化器模块

提供多种日志格式化器，支持：
- JSON格式输出
- 文本格式输出
- 自定义格式化
- 结构化数据处理
"""

import json
import time
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union
from datetime import datetime

from .utils import safe_serialize, truncate_data, format_timestamp


class MCTSFormatter(ABC):
    """
    MCTS日志格式化器基类
    
    定义了所有格式化器必须实现的接口。
    """
    
    @abstractmethod
    def format_ucb_log(self, timestamp: float, session_id: str, data: Dict[str, Any]) -> str:
        """格式化UCB计算日志"""
        pass
    
    @abstractmethod
    def format_expansion_log(self, timestamp: float, session_id: str, data: Dict[str, Any]) -> str:
        """格式化节点扩展日志"""
        pass
    
    @abstractmethod
    def format_path_log(self, timestamp: float, session_id: str, data: Dict[str, Any]) -> str:
        """格式化搜索路径日志"""
        pass
    
    @abstractmethod
    def format_simulation_log(self, timestamp: float, session_id: str, data: Dict[str, Any]) -> str:
        """格式化模拟结果日志"""
        pass
    
    @abstractmethod
    def format_performance_log(self, timestamp: float, session_id: str, data: Dict[str, Any]) -> str:
        """格式化性能统计日志"""
        pass


class JSONFormatter(MCTSFormatter):
    """
    JSON格式化器
    
    将日志数据格式化为结构化的JSON格式，便于程序解析和分析。
    """
    
    def __init__(self, 
                 indent: Optional[int] = None,
                 ensure_ascii: bool = False,
                 max_data_length: int = 10000):
        """
        初始化JSON格式化器
        
        Args:
            indent: JSON缩进，None表示紧凑格式
            ensure_ascii: 是否确保ASCII编码
            max_data_length: 最大数据长度，超过会被截断
        """
        self.indent = indent
        self.ensure_ascii = ensure_ascii
        self.max_data_length = max_data_length
    
    def _create_base_log(self, timestamp: float, session_id: str, log_type: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建基础日志结构
        
        Args:
            timestamp: 时间戳
            session_id: 会话ID
            log_type: 日志类型
            data: 日志数据
            
        Returns:
            Dict[str, Any]: 基础日志结构
        """
        # 安全序列化数据
        safe_data = safe_serialize(data)
        
        # 截断过长的数据
        truncated_data = truncate_data(safe_data, self.max_data_length)
        
        return {
            'timestamp': format_timestamp(timestamp),
            'session_id': session_id,
            'level': 'DEBUG',
            'type': log_type,
            'data': truncated_data
        }
    
    def _format_json(self, log_data: Dict[str, Any]) -> str:
        """
        格式化为JSON字符串
        
        Args:
            log_data: 日志数据
            
        Returns:
            str: JSON字符串
        """
        return json.dumps(
            log_data,
            indent=self.indent,
            ensure_ascii=self.ensure_ascii,
            separators=(',', ':') if self.indent is None else (',', ': ')
        )
    
    def format_ucb_log(self, timestamp: float, session_id: str, data: Dict[str, Any]) -> str:
        """
        格式化UCB计算日志
        
        Args:
            timestamp: 时间戳
            session_id: 会话ID
            data: UCB计算数据
            
        Returns:
            str: 格式化的JSON字符串
        """
        log_data = self._create_base_log(timestamp, session_id, 'ucb_calculation', data)
        return self._format_json(log_data)
    
    def format_expansion_log(self, timestamp: float, session_id: str, data: Dict[str, Any]) -> str:
        """
        格式化节点扩展日志
        
        Args:
            timestamp: 时间戳
            session_id: 会话ID
            data: 节点扩展数据
            
        Returns:
            str: 格式化的JSON字符串
        """
        log_data = self._create_base_log(timestamp, session_id, 'node_expansion', data)
        return self._format_json(log_data)
    
    def format_path_log(self, timestamp: float, session_id: str, data: Dict[str, Any]) -> str:
        """
        格式化搜索路径日志
        
        Args:
            timestamp: 时间戳
            session_id: 会话ID
            data: 搜索路径数据
            
        Returns:
            str: 格式化的JSON字符串
        """
        log_data = self._create_base_log(timestamp, session_id, 'search_path', data)
        return self._format_json(log_data)
    
    def format_simulation_log(self, timestamp: float, session_id: str, data: Dict[str, Any]) -> str:
        """
        格式化模拟结果日志
        
        Args:
            timestamp: 时间戳
            session_id: 会话ID
            data: 模拟结果数据
            
        Returns:
            str: 格式化的JSON字符串
        """
        log_data = self._create_base_log(timestamp, session_id, 'simulation_result', data)
        return self._format_json(log_data)
    
    def format_performance_log(self, timestamp: float, session_id: str, data: Dict[str, Any]) -> str:
        """
        格式化性能统计日志
        
        Args:
            timestamp: 时间戳
            session_id: 会话ID
            data: 性能统计数据
            
        Returns:
            str: 格式化的JSON字符串
        """
        log_data = self._create_base_log(timestamp, session_id, 'performance_stats', data)
        log_data['level'] = 'INFO'  # 性能日志使用INFO级别
        return self._format_json(log_data)


class TextFormatter(MCTSFormatter):
    """
    文本格式化器
    
    将日志数据格式化为人类可读的文本格式，便于直接阅读和调试。
    """
    
    def __init__(self, 
                 include_timestamp: bool = True,
                 include_session_id: bool = True,
                 max_line_length: int = 120):
        """
        初始化文本格式化器
        
        Args:
            include_timestamp: 是否包含时间戳
            include_session_id: 是否包含会话ID
            max_line_length: 最大行长度
        """
        self.include_timestamp = include_timestamp
        self.include_session_id = include_session_id
        self.max_line_length = max_line_length
    
    def _format_header(self, timestamp: float, session_id: str, log_type: str) -> str:
        """
        格式化日志头部
        
        Args:
            timestamp: 时间戳
            session_id: 会话ID
            log_type: 日志类型
            
        Returns:
            str: 格式化的头部
        """
        parts = []
        
        if self.include_timestamp:
            parts.append(f"[{format_timestamp(timestamp)}]")
        
        if self.include_session_id:
            parts.append(f"[{session_id}]")
        
        parts.append(f"[{log_type.upper()}]")
        
        return " ".join(parts)
    
    def _format_data(self, data: Dict[str, Any], indent: int = 0) -> str:
        """
        格式化数据为文本
        
        Args:
            data: 要格式化的数据
            indent: 缩进级别
            
        Returns:
            str: 格式化的文本
        """
        lines = []
        indent_str = "  " * indent
        
        for key, value in data.items():
            if isinstance(value, dict):
                lines.append(f"{indent_str}{key}:")
                lines.append(self._format_data(value, indent + 1))
            elif isinstance(value, list):
                lines.append(f"{indent_str}{key}: [{len(value)} items]")
                for i, item in enumerate(value[:5]):  # 只显示前5个项目
                    if isinstance(item, dict):
                        lines.append(f"{indent_str}  [{i}]:")
                        lines.append(self._format_data(item, indent + 2))
                    else:
                        lines.append(f"{indent_str}  [{i}]: {item}")
                if len(value) > 5:
                    lines.append(f"{indent_str}  ... 还有 {len(value) - 5} 个项目")
            else:
                # 截断过长的值
                str_value = str(value)
                if len(str_value) > self.max_line_length:
                    str_value = str_value[:self.max_line_length] + "..."
                lines.append(f"{indent_str}{key}: {str_value}")
        
        return "\n".join(lines)
    
    def format_ucb_log(self, timestamp: float, session_id: str, data: Dict[str, Any]) -> str:
        """格式化UCB计算日志"""
        header = self._format_header(timestamp, session_id, 'ucb_calculation')
        body = self._format_data(data)
        return f"{header}\n{body}\n"
    
    def format_expansion_log(self, timestamp: float, session_id: str, data: Dict[str, Any]) -> str:
        """格式化节点扩展日志"""
        header = self._format_header(timestamp, session_id, 'node_expansion')
        body = self._format_data(data)
        return f"{header}\n{body}\n"
    
    def format_path_log(self, timestamp: float, session_id: str, data: Dict[str, Any]) -> str:
        """格式化搜索路径日志"""
        header = self._format_header(timestamp, session_id, 'search_path')
        body = self._format_data(data)
        return f"{header}\n{body}\n"
    
    def format_simulation_log(self, timestamp: float, session_id: str, data: Dict[str, Any]) -> str:
        """格式化模拟结果日志"""
        header = self._format_header(timestamp, session_id, 'simulation_result')
        body = self._format_data(data)
        return f"{header}\n{body}\n"
    
    def format_performance_log(self, timestamp: float, session_id: str, data: Dict[str, Any]) -> str:
        """格式化性能统计日志"""
        header = self._format_header(timestamp, session_id, 'performance_stats')
        body = self._format_data(data)
        return f"{header}\n{body}\n"


def create_formatter(format_type: str, **kwargs) -> MCTSFormatter:
    """
    创建格式化器工厂函数
    
    Args:
        format_type: 格式化器类型 ('json' 或 'text')
        **kwargs: 格式化器参数
        
    Returns:
        MCTSFormatter: 格式化器实例
    """
    if format_type.lower() == 'json':
        return JSONFormatter(**kwargs)
    elif format_type.lower() == 'text':
        return TextFormatter(**kwargs)
    else:
        raise ValueError(f"不支持的格式化器类型: {format_type}")
