"""
对抗式对手生成模块

提供多种方法生成多样化且具有挑战性的对手策略，包括：
1. 基于GAN的策略生成
2. 基于自对弈历史的策略演化
3. 基于人类数据的模仿学习

这些生成的对手可用于更鲁棒的训练。
"""

from cardgame_ai.algorithms.opponent_generation.gan_policy_generator import (
    Generator, Discriminator, GANPolicyGenerator
)
from cardgame_ai.algorithms.opponent_generation.self_play_evolution import (
    SelfPlayEvolution, EvolutionaryPolicyGenerator
)
from cardgame_ai.algorithms.opponent_generation.human_style_generator import (
    HumanStyleGenerator, StyleTransferNetwork
)
