"""
SimSiam风格的自监督表示学习损失函数

该模块实现了基于SimSiam的自监督表示学习损失函数，用于EfficientZero算法中增强表示学习能力。
通过最小化预测状态表示与实际观察到的状态表示之间的差异，提高模型的样本效率。

参考论文：
- "Exploring Simple Siamese Representation Learning" (<PERSON> et al., 2020)
- "Mastering Atari Games with Limited Data" (Ye et al., 2021)
"""

import torch
import torch.nn as nn
import torch.nn.functional as F


class SimSiamLoss(nn.Module):
    """
    SimSiam风格的自监督表示学习损失函数
    
    通过最小化预测状态表示与目标状态表示之间的余弦相似度损失，
    实现自监督表示学习，提高模型的样本效率。
    """
    
    def __init__(self, normalize=True):
        """
        初始化SimSiam损失函数
        
        Args:
            normalize (bool, optional): 是否对输入进行L2归一化. Defaults to True.
        """
        super(SimSiam<PERSON>oss, self).__init__()
        self.normalize = normalize
        self.criterion = nn.CosineSimilarity(dim=1)
        
    def forward(self, p, z):
        """
        计算SimSiam损失
        
        Args:
            p (torch.Tensor): 预测的表示向量
            z (torch.Tensor): 目标表示向量（通常是detached的，不传递梯度）
            
        Returns:
            torch.Tensor: 损失值
        """
        if self.normalize:
            p = F.normalize(p, dim=1)
            z = F.normalize(z, dim=1)
            
        # 负余弦相似度损失
        return -self.criterion(p, z).mean()


class EfficientZeroSelfSupervisedLoss(nn.Module):
    """
    EfficientZero的自监督表示学习损失函数
    
    结合了SimSiam风格的自监督学习损失和一致性损失，用于增强表示学习能力。
    """
    
    def __init__(self, similarity_weight=1.0, normalize=True):
        """
        初始化EfficientZero自监督损失函数
        
        Args:
            similarity_weight (float, optional): 相似性损失的权重. Defaults to 1.0.
            normalize (bool, optional): 是否对输入进行L2归一化. Defaults to True.
        """
        super(EfficientZeroSelfSupervisedLoss, self).__init__()
        self.similarity_weight = similarity_weight
        self.normalize = normalize
        self.simsiam_loss = SimSiamLoss(normalize=normalize)
        
    def forward(self, predicted_state, target_state):
        """
        计算EfficientZero自监督损失
        
        Args:
            predicted_state (torch.Tensor): 预测的状态表示
            target_state (torch.Tensor): 目标状态表示（通常是detached的，不传递梯度）
            
        Returns:
            torch.Tensor: 损失值
        """
        # 计算SimSiam风格的损失
        loss = self.simsiam_loss(predicted_state, target_state)
        
        # 返回加权损失
        return self.similarity_weight * loss


class ProjectionNetwork(nn.Module):
    """
    投影网络
    
    将状态表示映射到潜在空间，用于自监督学习。
    """
    
    def __init__(self, input_dim, hidden_dim=256, output_dim=128):
        """
        初始化投影网络
        
        Args:
            input_dim (int): 输入维度
            hidden_dim (int, optional): 隐藏层维度. Defaults to 256.
            output_dim (int, optional): 输出维度. Defaults to 128.
        """
        super(ProjectionNetwork, self).__init__()
        
        self.net = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.BatchNorm1d(hidden_dim),
            nn.ReLU(inplace=True),
            nn.Linear(hidden_dim, output_dim)
        )
        
    def forward(self, x):
        """
        前向传播
        
        Args:
            x (torch.Tensor): 输入状态表示
            
        Returns:
            torch.Tensor: 投影后的表示
        """
        return self.net(x)


class PredictionNetwork(nn.Module):
    """
    预测网络
    
    预测目标投影，用于自监督学习。
    """
    
    def __init__(self, input_dim, hidden_dim=128, output_dim=128):
        """
        初始化预测网络
        
        Args:
            input_dim (int): 输入维度
            hidden_dim (int, optional): 隐藏层维度. Defaults to 128.
            output_dim (int, optional): 输出维度. Defaults to 128.
        """
        super(PredictionNetwork, self).__init__()
        
        self.net = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.BatchNorm1d(hidden_dim),
            nn.ReLU(inplace=True),
            nn.Linear(hidden_dim, output_dim)
        )
        
    def forward(self, x):
        """
        前向传播
        
        Args:
            x (torch.Tensor): 输入投影表示
            
        Returns:
            torch.Tensor: 预测的表示
        """
        return self.net(x)


class SelfSupervisedModule(nn.Module):
    """
    自监督学习模块
    
    结合投影网络和预测网络，实现EfficientZero中的自监督表示学习。
    """
    
    def __init__(self, state_dim, projection_dim=256, prediction_dim=128):
        """
        初始化自监督学习模块
        
        Args:
            state_dim (int): 状态维度
            projection_dim (int, optional): 投影维度. Defaults to 256.
            prediction_dim (int, optional): 预测网络隐藏层维度. Defaults to 128.
        """
        super(SelfSupervisedModule, self).__init__()
        
        # 投影网络
        self.projector = ProjectionNetwork(
            input_dim=state_dim,
            hidden_dim=projection_dim,
            output_dim=projection_dim
        )
        
        # 预测网络
        self.predictor = PredictionNetwork(
            input_dim=projection_dim,
            hidden_dim=prediction_dim,
            output_dim=projection_dim
        )
        
        # 损失函数
        self.loss_fn = EfficientZeroSelfSupervisedLoss()
        
    def forward(self, state1, state2):
        """
        前向传播
        
        Args:
            state1 (torch.Tensor): 第一个状态表示
            state2 (torch.Tensor): 第二个状态表示
            
        Returns:
            Tuple[torch.Tensor, torch.Tensor]: 预测表示和投影表示
        """
        # 计算投影
        z1 = self.projector(state1)
        z2 = self.projector(state2)
        
        # 计算预测
        p1 = self.predictor(z1)
        p2 = self.predictor(z2)
        
        return p1, p2, z1, z2
    
    def compute_loss(self, p1, p2, z1, z2):
        """
        计算自监督损失
        
        Args:
            p1 (torch.Tensor): 第一个预测表示
            p2 (torch.Tensor): 第二个预测表示
            z1 (torch.Tensor): 第一个投影表示
            z2 (torch.Tensor): 第二个投影表示
            
        Returns:
            torch.Tensor: 损失值
        """
        # 停止梯度传播
        z1 = z1.detach()
        z2 = z2.detach()
        
        # 对称损失
        loss = (self.loss_fn(p1, z2) + self.loss_fn(p2, z1)) * 0.5
        
        return loss
