"""
图神经网络训练脚本

训练图神经网络模型，用于捕捉牌与牌之间的相互约束与语义关系。
"""

import os
import time
import logging
import argparse
import numpy as np
from typing import Dict, List, Tuple, Any, Optional, Union

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader

from cardgame_ai.models.gnn_card_model import GNNCardModel
from cardgame_ai.data.graph_builder import CardGraphBuilder
from cardgame_ai.utils.graph_utils import networkx_to_pyg
from cardgame_ai.games.doudizhu.card import Card
from cardgame_ai.games.doudizhu.state import DouDizhuState
from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType
from cardgame_ai.games.doudizhu.utils import cards_to_str, str_to_cards


class CardDataset(Dataset):
    """
    手牌数据集

    用于训练图神经网络模型的手牌数据集。
    """

    def __init__(self, data_path: str, graph_builder: CardGraphBuilder):
        """
        初始化手牌数据集

        Args:
            data_path: 数据文件路径
            graph_builder: 图构建器
        """
        self.data_path = data_path
        self.graph_builder = graph_builder
        self.samples = []

        self._load_data()

    def _load_data(self):
        """加载数据"""
        if not os.path.exists(self.data_path):
            raise FileNotFoundError(f"数据文件不存在: {self.data_path}")

        logging.info(f"加载数据: {self.data_path}")

        # 加载数据
        with open(self.data_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line:
                    continue

                # 解析数据行
                parts = line.split('\t')
                if len(parts) < 2:
                    continue

                # 解析手牌和标签
                cards_str = parts[0]
                label = int(parts[1])

                # 转换为Card对象
                cards = str_to_cards(cards_str)

                # 添加到样本列表
                self.samples.append((cards, label))

        logging.info(f"加载了 {len(self.samples)} 个样本")

    def __len__(self):
        """获取数据集大小"""
        return len(self.samples)

    def __getitem__(self, idx):
        """获取数据样本"""
        cards, label = self.samples[idx]

        # 构建图
        graph = self.graph_builder.build_graph(cards, 'doudizhu')

        # 转换为PyTorch Geometric数据对象
        graph_data = networkx_to_pyg(graph)

        return graph_data, label


def collate_fn(batch):
    """
    数据批次整理函数

    Args:
        batch: 数据批次

    Returns:
        整理后的数据批次
    """
    try:
        from torch_geometric.data import Batch

        graphs, labels = zip(*batch)
        batched_graphs = Batch.from_data_list(list(graphs))
        labels = torch.tensor(labels, dtype=torch.long)

        return batched_graphs, labels
    except ImportError:
        logging.error("PyTorch Geometric不可用，请安装相关依赖")
        raise


def train(model, train_loader, optimizer, criterion, device, epoch):
    """
    训练一个epoch

    Args:
        model: 模型
        train_loader: 训练数据加载器
        optimizer: 优化器
        criterion: 损失函数
        device: 计算设备
        epoch: 当前epoch

    Returns:
        float: 训练损失
    """
    model.train()
    total_loss = 0
    correct = 0
    total = 0

    for batch_idx, (graphs, labels) in enumerate(train_loader):
        # 移动数据到设备
        graphs = graphs.to(device)
        labels = labels.to(device)

        # 清除梯度
        optimizer.zero_grad()

        # 前向传播
        outputs = model(graphs)

        # 计算损失
        loss = criterion(outputs, labels)

        # 反向传播
        loss.backward()

        # 更新参数
        optimizer.step()

        # 统计
        total_loss += loss.item()
        _, predicted = outputs.max(1)
        total += labels.size(0)
        correct += predicted.eq(labels).sum().item()

        # 打印进度
        if (batch_idx + 1) % 10 == 0 or (batch_idx + 1) == len(train_loader):
            logging.info(f'Epoch: {epoch} [{batch_idx+1}/{len(train_loader)}] '
                        f'Loss: {total_loss/(batch_idx+1):.4f} '
                        f'Acc: {100.*correct/total:.2f}%')

    return total_loss / len(train_loader)


def validate(model, val_loader, criterion, device):
    """
    验证模型

    Args:
        model: 模型
        val_loader: 验证数据加载器
        criterion: 损失函数
        device: 计算设备

    Returns:
        Tuple[float, float]: (验证损失, 验证准确率)
    """
    model.eval()
    total_loss = 0
    correct = 0
    total = 0

    with torch.no_grad():
        for batch_idx, (graphs, labels) in enumerate(val_loader):
            # 移动数据到设备
            graphs = graphs.to(device)
            labels = labels.to(device)

            # 前向传播
            outputs = model(graphs)

            # 计算损失
            loss = criterion(outputs, labels)

            # 统计
            total_loss += loss.item()
            _, predicted = outputs.max(1)
            total += labels.size(0)
            correct += predicted.eq(labels).sum().item()

    # 计算准确率
    acc = 100. * correct / total

    logging.info(f'Validation Loss: {total_loss/len(val_loader):.4f} Acc: {acc:.2f}%')

    return total_loss / len(val_loader), acc


def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='训练图神经网络模型')
    parser.add_argument('--train-data', type=str, required=True, help='训练数据文件路径')
    parser.add_argument('--val-data', type=str, help='验证数据文件路径')
    parser.add_argument('--model-dir', type=str, default='models', help='模型保存目录')
    parser.add_argument('--batch-size', type=int, default=64, help='批次大小')
    parser.add_argument('--epochs', type=int, default=50, help='训练轮数')
    parser.add_argument('--lr', type=float, default=0.001, help='学习率')
    parser.add_argument('--hidden-dim', type=int, default=128, help='隐藏层维度')
    parser.add_argument('--output-dim', type=int, default=64, help='输出维度')
    parser.add_argument('--num-layers', type=int, default=2, help='GNN层数')
    parser.add_argument('--gnn-type', type=str, default='gcn', choices=['gcn', 'gat', 'sage'], help='GNN类型')
    parser.add_argument('--dropout', type=float, default=0.1, help='Dropout比率')
    parser.add_argument('--pooling', type=str, default='mean', choices=['mean', 'max', 'add', 'attention'], help='池化方法')
    parser.add_argument('--graph-strategy', type=str, default='semantic',
                       choices=['semantic', 'hierarchical', 'complete'], help='图构建策略')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    parser.add_argument('--log-interval', type=int, default=10, help='日志打印间隔')
    args = parser.parse_args()

    # 设置随机种子
    torch.manual_seed(args.seed)
    np.random.seed(args.seed)

    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(os.path.join(args.model_dir, 'train.log'))
        ]
    )

    # 创建模型保存目录
    os.makedirs(args.model_dir, exist_ok=True)

    # 创建图构建器
    graph_builder = CardGraphBuilder(strategy=args.graph_strategy)

    # 创建数据集
    train_dataset = CardDataset(args.train_data, graph_builder)
    train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True,
                             collate_fn=collate_fn, num_workers=4)

    if args.val_data:
        val_dataset = CardDataset(args.val_data, graph_builder)
        val_loader = DataLoader(val_dataset, batch_size=args.batch_size, shuffle=False,
                               collate_fn=collate_fn, num_workers=4)
    else:
        val_loader = None

    # 创建模型
    model = GNNCardModel(
        hidden_dim=args.hidden_dim,
        output_dim=args.output_dim,
        num_layers=args.num_layers,
        gnn_type=args.gnn_type,
        dropout=args.dropout,
        pooling=args.pooling,
        graph_strategy=args.graph_strategy
    ).to(device)

    # 创建优化器和损失函数
    optimizer = optim.Adam(model.parameters(), lr=args.lr)
    criterion = nn.CrossEntropyLoss()

    # 训练模型
    best_acc = 0
    for epoch in range(1, args.epochs + 1):
        # 训练
        train_loss = train(model, train_loader, optimizer, criterion, device, epoch)

        # 验证
        if val_loader:
            val_loss, val_acc = validate(model, val_loader, criterion, device)

            # 保存最佳模型
            if val_acc > best_acc:
                best_acc = val_acc
                torch.save(model.state_dict(), os.path.join(args.model_dir, 'best_model.pth'))
                logging.info(f'Saved best model with accuracy: {best_acc:.2f}%')

        # 保存最新模型
        torch.save(model.state_dict(), os.path.join(args.model_dir, 'latest_model.pth'))

    logging.info('Training completed!')


if __name__ == '__main__':
    main()
