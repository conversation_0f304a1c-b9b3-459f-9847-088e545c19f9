"""
可视化配置模块

定义可视化系统的配置，包括颜色主题、图表尺寸和保存路径等设置。
"""
import os
import json
from typing import Dict, Any, List, Tuple, Optional, Union
import matplotlib.pyplot as plt
import seaborn as sns


# 基础颜色主题配置
COLOR_THEMES = {
    'default': {
        'primary': '#1f77b4',
        'secondary': '#ff7f0e',
        'tertiary': '#2ca02c',
        'quaternary': '#d62728',
        'quinary': '#9467bd',
        'palette': 'tab10',
    },
    'pastel': {
        'primary': '#8dd3c7',
        'secondary': '#ffffb3',
        'tertiary': '#bebada',
        'quaternary': '#fb8072',
        'quinary': '#80b1d3',
        'palette': 'pastel',
    },
    'dark': {
        'primary': '#1b9e77',
        'secondary': '#d95f02',
        'tertiary': '#7570b3',
        'quaternary': '#e7298a',
        'quinary': '#66a61e',
        'palette': 'dark',
    },
    'colorblind': {
        'primary': '#0072b2',
        'secondary': '#e69f00',
        'tertiary': '#009e73',
        'quaternary': '#cc79a7',
        'quinary': '#56b4e9',
        'palette': 'colorblind',
    }
}

# 图表尺寸配置
FIGURE_SIZES = {
    'small': (8, 6),
    'medium': (12, 8),
    'large': (16, 10),
    'wide': (18, 6),
    'tall': (8, 12),
    'square': (10, 10),
    'dashboard': (18, 10),
    'poster': (20, 15),
}

# 文字大小配置
FONT_SIZES = {
    'small': {
        'title': 14,
        'axis_label': 12,
        'tick_label': 10,
        'legend': 10,
        'annotation': 9,
    },
    'medium': {
        'title': 16,
        'axis_label': 14,
        'tick_label': 12,
        'legend': 12,
        'annotation': 11,
    },
    'large': {
        'title': 18,
        'axis_label': 16,
        'tick_label': 14,
        'legend': 14,
        'annotation': 13,
    }
}

# 保存格式配置
SAVE_FORMATS = {
    'png': {
        'extension': 'png',
        'dpi': 300,
        'transparent': False,
    },
    'svg': {
        'extension': 'svg',
        'dpi': 300,
        'transparent': True,
    },
    'pdf': {
        'extension': 'pdf',
        'dpi': 300,
        'transparent': False,
    },
    'jpg': {
        'extension': 'jpg',
        'dpi': 300,
        'quality': 95,
        'transparent': False,
    }
}

# 默认配置
DEFAULT_CONFIG = {
    'theme': 'default',
    'figure_size': 'medium',
    'font_size': 'medium',
    'save_format': 'png',
    'save_dir': 'visualizations',
    'show_grid': True,
    'use_dark_background': False,
    'style': 'whitegrid',
    'context': 'notebook',
    'despine': True,
    'use_tex': False,
}


class VisualizationConfig:
    """
    可视化配置管理
    
    管理可视化设置，包括主题、尺寸、字体和保存选项等。
    """
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化可视化配置
        
        Args:
            config_file (Optional[str], optional): 配置文件路径. Defaults to None.
        """
        # 加载默认配置
        self.config = DEFAULT_CONFIG.copy()
        
        # 如果提供了配置文件，则从文件加载配置
        if config_file and os.path.exists(config_file):
            self.load_config(config_file)
        
        # 创建保存目录
        self.ensure_save_dir()
        
        # 应用配置到Matplotlib和Seaborn
        self.apply_config()
    
    def load_config(self, config_file: str) -> None:
        """
        从文件加载配置
        
        Args:
            config_file (str): 配置文件路径
        """
        try:
            with open(config_file, 'r') as f:
                loaded_config = json.load(f)
                
                # 更新配置
                self.config.update(loaded_config)
                
            print(f"已从 {config_file} 加载配置")
        except Exception as e:
            print(f"加载配置文件时出错: {str(e)}")
    
    def save_config(self, config_file: str) -> None:
        """
        保存配置到文件
        
        Args:
            config_file (str): 配置文件路径
        """
        try:
            with open(config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
                
            print(f"已保存配置到 {config_file}")
        except Exception as e:
            print(f"保存配置文件时出错: {str(e)}")
    
    def update_config(self, new_config: Dict[str, Any]) -> None:
        """
        更新配置
        
        Args:
            new_config (Dict[str, Any]): 新配置
        """
        self.config.update(new_config)
        self.apply_config()
        
        # 确保保存目录存在
        self.ensure_save_dir()
    
    def get_theme_colors(self) -> Dict[str, str]:
        """
        获取主题颜色
        
        Returns:
            Dict[str, str]: 主题颜色字典
        """
        theme_name = self.config.get('theme', 'default')
        return COLOR_THEMES.get(theme_name, COLOR_THEMES['default'])
    
    def get_figure_size(self, size_name: Optional[str] = None) -> Tuple[int, int]:
        """
        获取图形尺寸
        
        Args:
            size_name (Optional[str], optional): 尺寸名称. Defaults to None.
            
        Returns:
            Tuple[int, int]: 图形尺寸(宽, 高)
        """
        if not size_name:
            size_name = self.config.get('figure_size', 'medium')
            
        return FIGURE_SIZES.get(size_name, FIGURE_SIZES['medium'])
    
    def get_font_sizes(self, size_name: Optional[str] = None) -> Dict[str, int]:
        """
        获取字体大小
        
        Args:
            size_name (Optional[str], optional): 字体大小名称. Defaults to None.
            
        Returns:
            Dict[str, int]: 字体大小字典
        """
        if not size_name:
            size_name = self.config.get('font_size', 'medium')
            
        return FONT_SIZES.get(size_name, FONT_SIZES['medium'])
    
    def get_save_format(self, format_name: Optional[str] = None) -> Dict[str, Any]:
        """
        获取保存格式
        
        Args:
            format_name (Optional[str], optional): 格式名称. Defaults to None.
            
        Returns:
            Dict[str, Any]: 保存格式字典
        """
        if not format_name:
            format_name = self.config.get('save_format', 'png')
            
        return SAVE_FORMATS.get(format_name, SAVE_FORMATS['png'])
    
    def get_save_path(self, filename: str, subdir: Optional[str] = None) -> str:
        """
        获取保存路径
        
        Args:
            filename (str): 文件名
            subdir (Optional[str], optional): 子目录. Defaults to None.
            
        Returns:
            str: 完整保存路径
        """
        save_dir = self.config.get('save_dir', 'visualizations')
        
        if subdir:
            save_dir = os.path.join(save_dir, subdir)
            os.makedirs(save_dir, exist_ok=True)
        
        # 获取保存格式
        format_name = self.config.get('save_format', 'png')
        format_info = self.get_save_format(format_name)
        extension = format_info.get('extension', 'png')
        
        # 如果文件名没有扩展名，添加扩展名
        if not filename.endswith(f'.{extension}'):
            filename = f"{filename}.{extension}"
        
        return os.path.join(save_dir, filename)
    
    def ensure_save_dir(self) -> None:
        """
        确保保存目录存在
        """
        save_dir = self.config.get('save_dir', 'visualizations')
        os.makedirs(save_dir, exist_ok=True)
    
    def apply_config(self) -> None:
        """
        应用配置到Matplotlib和Seaborn
        """
        # 设置Seaborn样式
        style = self.config.get('style', 'whitegrid')
        context = self.config.get('context', 'notebook')
        theme = self.config.get('theme', 'default')
        
        sns.set_theme(style=style, context=context, palette=COLOR_THEMES[theme]['palette'])
        
        # 设置Matplotlib参数
        font_sizes = self.get_font_sizes()
        
        plt.rcParams.update({
            'figure.figsize': self.get_figure_size(),
            'font.size': font_sizes['axis_label'],
            'axes.titlesize': font_sizes['title'],
            'axes.labelsize': font_sizes['axis_label'],
            'xtick.labelsize': font_sizes['tick_label'],
            'ytick.labelsize': font_sizes['tick_label'],
            'legend.fontsize': font_sizes['legend'],
            'figure.titlesize': font_sizes['title'],
            'axes.grid': self.config.get('show_grid', True),
            'figure.facecolor': 'black' if self.config.get('use_dark_background', False) else 'white',
            'axes.facecolor': 'black' if self.config.get('use_dark_background', False) else 'white',
            'text.usetex': self.config.get('use_tex', False),
        })
    
    def get_save_kwargs(self) -> Dict[str, Any]:
        """
        获取图形保存参数
        
        Returns:
            Dict[str, Any]: 保存参数字典
        """
        format_name = self.config.get('save_format', 'png')
        format_info = self.get_save_format(format_name)
        
        kwargs = {
            'dpi': format_info.get('dpi', 300),
            'transparent': format_info.get('transparent', False),
        }
        
        # 对于JPG格式，添加quality参数
        if format_name == 'jpg':
            kwargs['quality'] = format_info.get('quality', 95)
        
        return kwargs
    
    def create_figure(self, size_name: Optional[str] = None) -> Tuple[plt.Figure, plt.Axes]:
        """
        创建图形和轴
        
        Args:
            size_name (Optional[str], optional): 尺寸名称. Defaults to None.
            
        Returns:
            Tuple[plt.Figure, plt.Axes]: 图形和轴
        """
        fig_size = self.get_figure_size(size_name)
        fig, ax = plt.subplots(figsize=fig_size)
        
        # 应用配置
        if self.config.get('despine', True):
            sns.despine(fig=fig)
        
        return fig, ax
    
    def save_figure(self, fig: plt.Figure, filename: str, subdir: Optional[str] = None) -> str:
        """
        保存图形
        
        Args:
            fig (plt.Figure): 图形对象
            filename (str): 文件名
            subdir (Optional[str], optional): 子目录. Defaults to None.
            
        Returns:
            str: 保存的文件路径
        """
        save_path = self.get_save_path(filename, subdir)
        save_kwargs = self.get_save_kwargs()
        
        fig.savefig(save_path, **save_kwargs)
        print(f"图形已保存至 {save_path}")
        
        return save_path 