#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
反事实推理示例脚本

展示如何使用反事实推理功能，在规划阶段进行显式的反事实思考，
评估"如果对手没有那张牌会怎样"或"如果我走了另一条路会怎样"，
以改进当前决策。
"""

import os
import sys
import argparse
import logging
import numpy as np
from typing import Dict, List, Any, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from cardgame_ai.algorithms.counterfactual_reasoning import CounterfactualMCTS, CounterfactualAssumption
from cardgame_ai.algorithms.belief_tracking.bayesian_belief import BayesianBeliefTracker
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.games.doudizhu.state import DouDizhuState
from cardgame_ai.algorithms.muzero import MuZero

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='反事实推理示例')
    
    parser.add_argument('--model_path', type=str, default=None,
                        help='MuZero模型路径')
    parser.add_argument('--num_simulations', type=int, default=50,
                        help='MCTS模拟次数')
    parser.add_argument('--counterfactual_weight', type=float, default=0.3,
                        help='反事实推理权重')
    parser.add_argument('--max_counterfactual_assumptions', type=int, default=3,
                        help='最大反事实假设数量')
    parser.add_argument('--seed', type=int, default=None,
                        help='随机种子')
    
    return parser.parse_args()


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 设置随机种子
    if args.seed is not None:
        np.random.seed(args.seed)
    
    # 创建游戏环境
    env = DouDizhuEnvironment()
    
    # 获取观察和动作空间
    observation_shape = env.observation_space.shape
    action_shape = (env.action_space.n,)
    
    # 创建MuZero模型
    model = MuZero(
        state_shape=observation_shape,
        action_shape=action_shape,
        hidden_dim=128,
        state_dim=64,
        use_resnet=False
    )
    
    # 如果有预训练模型，加载参数
    if args.model_path:
        model.load(args.model_path)
        logger.info(f"已加载预训练模型: {args.model_path}")
    
    # 创建标准MCTS和反事实MCTS
    standard_mcts = CounterfactualMCTS(
        num_simulations=args.num_simulations,
        use_belief_state=False,  # 禁用反事实推理
        counterfactual_weight=0.0
    )
    
    counterfactual_mcts = CounterfactualMCTS(
        num_simulations=args.num_simulations,
        use_belief_state=True,
        use_information_value=True,
        counterfactual_weight=args.counterfactual_weight,
        max_counterfactual_assumptions=args.max_counterfactual_assumptions
    )
    
    # 创建信念追踪器
    belief_trackers = {}
    for player_id in ["landlord", "peasant1", "peasant2"]:
        belief_trackers[player_id] = BayesianBeliefTracker(player_id=player_id)
    
    # 重置环境
    state = env.reset()
    
    # 获取当前玩家ID
    current_player_id = state.current_player
    
    # 获取合法动作掩码
    legal_actions = env.get_legal_actions(state)
    actions_mask = [i in legal_actions for i in range(env.action_space.n)]
    
    # 使用标准MCTS进行搜索
    logger.info("使用标准MCTS进行搜索...")
    standard_visit_counts, standard_pi = standard_mcts.run(
        root_state=state,
        model=model,
        temperature=1.0,
        actions_mask=actions_mask,
        current_player_id=current_player_id
    )
    
    # 获取标准MCTS的最佳动作
    standard_best_action = max(standard_visit_counts.items(), key=lambda x: x[1])[0]
    logger.info(f"标准MCTS最佳动作: {standard_best_action}")
    
    # 使用反事实MCTS进行搜索
    logger.info("使用反事实MCTS进行搜索...")
    cf_visit_counts, cf_pi, explanation_data = counterfactual_mcts.run(
        root_state=state,
        model=model,
        temperature=1.0,
        actions_mask=actions_mask,
        belief_trackers=belief_trackers,
        current_player_id=current_player_id,
        explain=True
    )
    
    # 获取反事实MCTS的最佳动作
    cf_best_action = max(cf_visit_counts.items(), key=lambda x: x[1])[0]
    logger.info(f"反事实MCTS最佳动作: {cf_best_action}")
    
    # 分析反事实推理结果
    logger.info("\n反事实推理结果分析:")
    
    if "counterfactual_results" in explanation_data:
        for assumption, result in explanation_data["counterfactual_results"].items():
            cf_action = result["best_action"]
            logger.info(f"假设: {assumption}")
            logger.info(f"  最佳动作: {cf_action}")
            
            # 计算与标准MCTS的差异
            if cf_action != standard_best_action:
                logger.info(f"  与标准MCTS不同!")
                
                # 获取动作的访问次数
                standard_count = standard_visit_counts.get(cf_action, 0)
                cf_count = result["visit_counts"].get(cf_action, 0)
                
                # 计算访问次数变化
                count_change = cf_count - standard_count
                logger.info(f"  访问次数变化: {count_change} ({standard_count} -> {cf_count})")
    
    # 比较标准MCTS和反事实MCTS的结果
    logger.info("\n标准MCTS vs 反事实MCTS:")
    
    # 如果最佳动作不同，分析原因
    if standard_best_action != cf_best_action:
        logger.info(f"最佳动作不同: {standard_best_action} vs {cf_best_action}")
        
        # 分析访问次数变化
        for action in set(list(standard_visit_counts.keys()) + list(cf_visit_counts.keys())):
            standard_count = standard_visit_counts.get(action, 0)
            cf_count = cf_visit_counts.get(action, 0)
            
            if standard_count != cf_count:
                count_change = cf_count - standard_count
                logger.info(f"动作 {action} 访问次数变化: {count_change} ({standard_count} -> {cf_count})")
    else:
        logger.info(f"最佳动作相同: {standard_best_action}")
        
        # 分析策略分布变化
        for action in set(list(standard_pi.keys()) + list(cf_pi.keys())):
            standard_prob = standard_pi.get(action, 0.0)
            cf_prob = cf_pi.get(action, 0.0)
            
            if abs(standard_prob - cf_prob) > 0.01:
                prob_change = cf_prob - standard_prob
                logger.info(f"动作 {action} 概率变化: {prob_change:.4f} ({standard_prob:.4f} -> {cf_prob:.4f})")
    
    # 执行最佳动作
    logger.info(f"\n执行反事实MCTS的最佳动作: {cf_best_action}")
    next_state, reward, done, info = env.step(cf_best_action)
    
    # 更新信念追踪器
    for player_id, tracker in belief_trackers.items():
        tracker.update(cf_best_action, info.get("public_info", {}))
    
    logger.info("示例完成!")


if __name__ == "__main__":
    main()
