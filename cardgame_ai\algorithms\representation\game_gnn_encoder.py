"""
游戏状态GNN编码器模块

使用图神经网络编码整个游戏状态图，提高模型对复杂游戏状态的理解。
"""

import torch
import torch.nn as nn
from cardgame_ai.common.game_graph import GameGraphBuilder
from cardgame_ai.utils.graph_utils import networkx_to_pyg
from cardgame_ai.models.gnn_encoder import create_gnn_encoder
from typing import Any


class GameGNNEncoder(nn.Module):
    """
    游戏状态图神经网络编码器

    使用 GameGraphBuilder 构建图结构，并通过 GNN 编码生成图级嵌入。
    """
    def __init__(
        self,
        node_feature_dim: int,
        hidden_dim: int = 128,
        output_dim: int = 64,
        num_layers: int = 2,
        gnn_type: str = 'gcn',
        dropout: float = 0.1,
        pooling: str = 'mean',
        use_batch_norm: bool = True,
        residual: bool = True,
        device: str = None
    ):
        super(GameGNNEncoder, self).__init__()
        # 选择设备
        self.device = device if device else ('cuda' if torch.cuda.is_available() else 'cpu')
        # 图构建器
        self.graph_builder = GameGraphBuilder()
        # 创建底层 GNN 编码器
        self.encoder = create_gnn_encoder(
            node_feature_dim=node_feature_dim,
            hidden_dim=hidden_dim,
            output_dim=output_dim,
            num_layers=num_layers,
            gnn_type=gnn_type,
            dropout=dropout,
            pooling=pooling,
            use_batch_norm=use_batch_norm,
            residual=residual
        ).to(self.device)

    def forward(self, game_state: Any, player_id: int) -> torch.Tensor:
        """
        对游戏状态进行 GNN 编码

        Args:
            game_state: 游戏状态对象
            player_id: 当前玩家 ID

        Returns:
            torch.Tensor: 图级嵌入, 形状为 (batch_size, output_dim)，单图时 batch_size=1
        """
        # 构建 NetworkX 图
        G = self.graph_builder.build_graph(game_state, player_id)
        # 转换为 PyG 数据对象
        data = networkx_to_pyg(G)
        data = data.to(self.device)
        # 返回编码结果
        return self.encoder(data) 