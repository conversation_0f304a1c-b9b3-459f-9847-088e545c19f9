#!/bin/bash
# 打包脚本（Linux/Mac版）

# 激活虚拟环境
source venv/bin/activate

# 设置环境变量
export PYTHONPATH=$PYTHONPATH:$(pwd)

# 默认参数
VERSION=""
CLEAN=""
NO_BUILD=""
NO_PACKAGE=""
RELEASE_NOTES=""

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --version)
            VERSION="--version $2"
            shift 2
            ;;
        --clean)
            CLEAN="--clean"
            shift
            ;;
        --no-build)
            NO_BUILD="--no-build"
            shift
            ;;
        --no-package)
            NO_PACKAGE="--no-package"
            shift
            ;;
        --release-notes)
            RELEASE_NOTES="--release-notes"
            shift
            ;;
        *)
            shift
            ;;
    esac
done

echo "运行打包脚本..."

# 运行打包脚本
python scripts/build.py $VERSION $CLEAN $NO_BUILD $NO_PACKAGE $RELEASE_NOTES

# 检查打包结果
if [ $? -eq 0 ]; then
    echo "打包成功完成！"
else
    echo "打包失败，错误代码: $?"
fi

# 退出虚拟环境
deactivate

exit $?
