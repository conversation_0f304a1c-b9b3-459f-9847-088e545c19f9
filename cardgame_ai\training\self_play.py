"""
自我对弈系统模块

实现自我对弈机制，包括多代理对弈环境、经验收集和并行自我对弈，用于生成训练数据。
"""
import os
import time
import logging
import random
import threading
import multiprocessing
from typing import Dict, Any, List, Tuple, Optional, Union, Callable
import numpy as np
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor

from cardgame_ai.core.base import State, Action, Experience, Batch
from cardgame_ai.core.environment import Environment
from cardgame_ai.core.agent import Agent


def _play_single_game(env: Environment, agent: Agent, temperature: float = 1.0) -> List[Experience]:
    """
    运行单局游戏并收集经验

    Args:
        env (Environment): 游戏环境
        agent (Agent): 用于自我对弈的代理
        temperature (float, optional): 温度参数，控制动作采样的随机性. Defaults to 1.0.

    Returns:
        List[Experience]: 收集的经验数据
    """
    state = env.reset()
    done = False
    experiences = []

    while not done:
        # 获取当前玩家
        player_id = state.get_player_id()

        # 获取合法动作
        legal_actions = env.get_legal_actions(state)
        observation = env.get_observation(state)

        # 使用温度参数获取动作
        if temperature == 0.0:
            # 确定性选择
            action = agent.act(observation, legal_actions, is_training=False)
        else:
            # 根据温度采样
            action_probs = agent.get_action_probs(observation, legal_actions, temperature)
            if isinstance(action_probs, dict):
                actions = list(action_probs.keys())
                probs = list(action_probs.values())
            else:
                # 如果返回的是列表或numpy数组，则需要根据合法动作过滤
                all_action_probs = action_probs
                actions = legal_actions
                probs = [all_action_probs[a] for a in legal_actions]

            # 归一化概率
            probs_sum = sum(probs)
            if probs_sum > 0:
                probs = [p / probs_sum for p in probs]
            else:
                # 如果所有概率为0，则使用均匀分布
                probs = [1.0 / len(actions) for _ in actions]

            # 采样动作
            action = random.choices(actions, weights=probs, k=1)[0]

        # 执行动作
        next_state, reward, done, info = env.step(action)

        # 创建经验
        experience = Experience(state, action, reward, next_state, done, info)
        experiences.append(experience)

        # 更新状态
        state = next_state

    return experiences


class SelfPlay:
    """
    自我对弈系统

    实现自我对弈、经验收集和并行处理功能。
    """

    def __init__(self, save_path: str = 'experiences', max_workers: int = None,
                 use_multiprocessing: bool = False):
        """
        初始化自我对弈系统

        Args:
            save_path (str, optional): 经验数据保存路径. Defaults to 'experiences'.
            max_workers (int, optional): 最大并行工作数. Defaults to None.
            use_multiprocessing (bool, optional): 是否使用多进程. Defaults to False.
        """
        self.save_path = save_path
        self.max_workers = max_workers or multiprocessing.cpu_count()
        self.use_multiprocessing = use_multiprocessing
        self.logger = logging.getLogger(self.__class__.__name__)

        # 创建保存目录
        os.makedirs(save_path, exist_ok=True)

    def generate_experience(self, env: Environment, agent: Agent, num_games: int,
                           temperature: float = 1.0, save: bool = True,
                           parallel: bool = True, disable_tqdm: bool = False) -> List[Experience]:
        """
        生成自我对弈经验数据

        Args:
            env (Environment): 游戏环境
            agent (Agent): 用于自我对弈的代理
            num_games (int): 对弈的游戏数
            temperature (float, optional): 温度参数，控制动作采样的随机性. Defaults to 1.0.
            save (bool, optional): 是否保存经验数据. Defaults to True.
            parallel (bool, optional): 是否并行生成. Defaults to True.
            disable_tqdm (bool, optional): 是否禁用进度条. Defaults to False.

        Returns:
            List[Experience]: 生成的经验数据
        """
        start_time = time.time()
        all_experiences = []

        if parallel:
            self.logger.info(f"开始并行生成自我对弈数据，游戏数: {num_games}，最大并行数: {self.max_workers}")

            # 确定执行器类型
            executor_class = ProcessPoolExecutor if self.use_multiprocessing else ThreadPoolExecutor

            # 创建环境和代理的副本
            def _play_game(_):
                # 创建环境副本
                env_copy = env.clone() if hasattr(env, 'clone') else env
                # 代理无需复制，假设它是线程安全的
                return _play_single_game(env_copy, agent, temperature)

            # 并行生成游戏
            with executor_class(max_workers=self.max_workers) as executor:
                futures = [executor.submit(_play_game, i) for i in range(num_games)]

                # 创建进度条
                if not disable_tqdm:
                    from tqdm import tqdm
                    futures_iterator = tqdm(futures, total=num_games, desc="生成游戏")
                else:
                    futures_iterator = futures

                # 收集结果
                for i, future in enumerate(futures_iterator):
                    try:
                        game_experiences = future.result()
                        all_experiences.extend(game_experiences)

                        if disable_tqdm and ((i + 1) % 10 == 0 or (i + 1) == num_games):
                            elapsed_time = time.time() - start_time
                            self.logger.info(
                                f"已完成 {i + 1}/{num_games} 局游戏 | "
                                f"时间: {elapsed_time:.2f}s | "
                                f"平均每局时间: {elapsed_time / (i + 1):.2f}s"
                            )
                    except Exception as e:
                        self.logger.error(f"游戏 {i} 生成失败: {str(e)}")

        else:
            self.logger.info(f"开始顺序生成自我对弈数据，游戏数: {num_games}")

            # 创建进度条
            if not disable_tqdm:
                from tqdm import tqdm
                game_iterator = tqdm(range(num_games), desc="生成游戏")
            else:
                game_iterator = range(num_games)

            # 顺序生成游戏
            for i in game_iterator:
                game_experiences = _play_single_game(env, agent, temperature)
                all_experiences.extend(game_experiences)

                if disable_tqdm and ((i + 1) % 10 == 0 or (i + 1) == num_games):
                    elapsed_time = time.time() - start_time
                    self.logger.info(
                        f"已完成 {i + 1}/{num_games} 局游戏 | "
                        f"时间: {elapsed_time:.2f}s | "
                        f"平均每局时间: {elapsed_time / (i + 1):.2f}s"
                    )

        # 保存经验数据
        if save:
            timestamp = int(time.time())
            save_file = os.path.join(self.save_path, f"experiences_{timestamp}.npz")

            # 将经验转换为可存储的格式
            exp_data = {
                'states': [exp.state for exp in all_experiences],
                'actions': [exp.action for exp in all_experiences],
                'rewards': [exp.reward for exp in all_experiences],
                'next_states': [exp.next_state for exp in all_experiences],
                'dones': [exp.done for exp in all_experiences],
                'infos': [exp.info for exp in all_experiences]
            }

            np.savez_compressed(save_file, **exp_data)
            self.logger.info(f"经验数据已保存至 {save_file}")

        total_time = time.time() - start_time
        self.logger.info(
            f"自我对弈完成 | 总游戏数: {num_games} | "
            f"总经验数: {len(all_experiences)} | "
            f"总时间: {total_time:.2f}s | "
            f"平均每局时间: {total_time / num_games:.2f}s"
        )

        return all_experiences

    def load_experiences(self, file_path: str) -> List[Experience]:
        """
        加载经验数据

        Args:
            file_path (str): 经验数据文件路径

        Returns:
            List[Experience]: 加载的经验数据
        """
        self.logger.info(f"加载经验数据: {file_path}")

        # 加载经验数据
        exp_data = np.load(file_path, allow_pickle=True)

        # 转换为经验列表
        experiences = []
        for i in range(len(exp_data['states'])):
            exp = Experience(
                state=exp_data['states'][i],
                action=exp_data['actions'][i],
                reward=exp_data['rewards'][i],
                next_state=exp_data['next_states'][i],
                done=exp_data['dones'][i],
                info=exp_data['infos'][i]
            )
            experiences.append(exp)

        self.logger.info(f"已加载 {len(experiences)} 条经验数据")

        return experiences

    def get_latest_experiences(self, n: int = 1) -> List[str]:
        """
        获取最新的n个经验数据文件

        Args:
            n (int, optional): 返回的文件数. Defaults to 1.

        Returns:
            List[str]: 经验数据文件路径列表
        """
        files = [os.path.join(self.save_path, f) for f in os.listdir(self.save_path)
                if f.startswith('experiences_') and f.endswith('.npz')]

        # 按修改时间排序
        files.sort(key=os.path.getmtime, reverse=True)

        return files[:n] if n > 0 else files


class MultiAgentSelfPlay(SelfPlay):
    """
    多代理自我对弈系统

    支持多智能体的自我对弈，适用于斗地主等多人游戏。
    """

    def generate_experience(self, env: Environment, agents: List[Agent], num_games: int,
                           temperature: float = 1.0, save: bool = True,
                           parallel: bool = False, disable_tqdm: bool = False) -> Dict[int, List[Experience]]:
        """
        生成多代理自我对弈经验数据

        Args:
            env (Environment): 游戏环境
            agents (List[Agent]): 代理列表，每个位置对应一个玩家
            num_games (int): 对弈的游戏数
            temperature (float, optional): 温度参数. Defaults to 1.0.
            save (bool, optional): 是否保存经验. Defaults to True.
            parallel (bool, optional): 是否并行. Defaults to False.
            disable_tqdm (bool, optional): 是否禁用进度条. Defaults to False.

        Returns:
            Dict[int, List[Experience]]: 每个代理的经验数据
        """
        start_time = time.time()
        all_experiences = {i: [] for i in range(len(agents))}

        # 确保代理数量与玩家数量一致
        assert len(agents) == env.num_players, f"代理数量({len(agents)})与玩家数量({env.num_players})不一致"

        def _play_multi_agent_game():
            state = env.reset()
            done = False
            game_experiences = {i: [] for i in range(len(agents))}

            while not done:
                player_id = state.get_player_id()
                agent = agents[player_id]

                legal_actions = env.get_legal_actions(state)
                observation = env.get_observation(state)

                # 根据温度获取动作
                if temperature == 0.0:
                    action = agent.act(observation, legal_actions, is_training=False)
                else:
                    action_probs = agent.get_action_probs(observation, legal_actions, temperature)
                    if isinstance(action_probs, dict):
                        actions = list(action_probs.keys())
                        probs = list(action_probs.values())
                    else:
                        all_action_probs = action_probs
                        actions = legal_actions
                        probs = [all_action_probs[a] for a in legal_actions]

                    # 归一化概率
                    probs_sum = sum(probs)
                    if probs_sum > 0:
                        probs = [p / probs_sum for p in probs]
                    else:
                        probs = [1.0 / len(actions) for _ in actions]

                    action = random.choices(actions, weights=probs, k=1)[0]

                # 执行动作
                next_state, reward, done, info = env.step(action)

                # 创建经验
                experience = Experience(state, action, reward, next_state, done, info)
                game_experiences[player_id].append(experience)

                # 更新状态
                state = next_state

            return game_experiences

        if parallel:
            self.logger.info(f"开始并行生成多代理自我对弈数据，游戏数: {num_games}")

            # 确定执行器类型
            executor_class = ProcessPoolExecutor if self.use_multiprocessing else ThreadPoolExecutor

            def _play_game(_):
                env_copy = env.clone() if hasattr(env, 'clone') else env
                return _play_multi_agent_game()

            with executor_class(max_workers=self.max_workers) as executor:
                futures = [executor.submit(_play_game, i) for i in range(num_games)]

                # 创建进度条
                if not disable_tqdm:
                    from tqdm import tqdm
                    futures_iterator = tqdm(futures, total=num_games, desc="生成游戏")
                else:
                    futures_iterator = futures

                for i, future in enumerate(futures_iterator):
                    try:
                        game_experiences = future.result()
                        for player_id, exps in game_experiences.items():
                            all_experiences[player_id].extend(exps)

                        if disable_tqdm and ((i + 1) % 10 == 0 or (i + 1) == num_games):
                            elapsed_time = time.time() - start_time
                            self.logger.info(
                                f"已完成 {i + 1}/{num_games} 局游戏 | "
                                f"时间: {elapsed_time:.2f}s"
                            )
                    except Exception as e:
                        self.logger.error(f"游戏 {i} 生成失败: {str(e)}")
        else:
            self.logger.info(f"开始顺序生成多代理自我对弈数据，游戏数: {num_games}")

            # 创建进度条
            if not disable_tqdm:
                from tqdm import tqdm
                game_iterator = tqdm(range(num_games), desc="生成游戏")
            else:
                game_iterator = range(num_games)

            for i in game_iterator:
                game_experiences = _play_multi_agent_game()
                for player_id, exps in game_experiences.items():
                    all_experiences[player_id].extend(exps)

                if disable_tqdm and ((i + 1) % 10 == 0 or (i + 1) == num_games):
                    elapsed_time = time.time() - start_time
                    self.logger.info(
                        f"已完成 {i + 1}/{num_games} 局游戏 | "
                        f"时间: {elapsed_time:.2f}s"
                    )

        # 保存经验数据
        if save:
            timestamp = int(time.time())
            for player_id, exps in all_experiences.items():
                save_file = os.path.join(self.save_path, f"experiences_player{player_id}_{timestamp}.npz")

                exp_data = {
                    'states': [exp.state for exp in exps],
                    'actions': [exp.action for exp in exps],
                    'rewards': [exp.reward for exp in exps],
                    'next_states': [exp.next_state for exp in exps],
                    'dones': [exp.done for exp in exps],
                    'infos': [exp.info for exp in exps]
                }

                np.savez_compressed(save_file, **exp_data)
                self.logger.info(f"玩家{player_id}的经验数据已保存至 {save_file}")

        total_time = time.time() - start_time
        for player_id, exps in all_experiences.items():
            self.logger.info(f"玩家{player_id}总经验数: {len(exps)}")

        self.logger.info(
            f"多代理自我对弈完成 | 总游戏数: {num_games} | "
            f"总时间: {total_time:.2f}s | "
            f"平均每局时间: {total_time / num_games:.2f}s"
        )

        return all_experiences