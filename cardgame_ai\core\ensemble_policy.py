"""
集成策略模块

提供组合多个策略的功能，包括投票、加权平均等方法。
"""

import logging
import numpy as np
from typing import Dict, List, Any, Optional, Union, Tuple

from cardgame_ai.core.policy import Policy
from cardgame_ai.core.base import State, Action
from cardgame_ai.core.expert_pool import ExpertPolicyPool

# 配置日志
logger = logging.getLogger(__name__)


class EnsemblePolicy(Policy):
    """
    集成策略

    组合多个策略，使用投票、加权平均等方法选择动作。
    """

    def __init__(self, name: str, policies: Dict[str, Policy],
                weights: Optional[Dict[str, float]] = None,
                method: str = 'weighted_average'):
        """
        初始化集成策略

        Args:
            name: 策略名称
            policies: 策略字典，键为策略名称，值为策略实例
            weights: 权重字典，键为策略名称，值为权重
            method: 集成方法，可选值：'weighted_average', 'voting', 'stacking'
        """
        super().__init__(name=name)

        self.policies = policies
        self.method = method

        # 初始化权重
        if weights is None:
            # 平均权重
            self.weights = {name: 1.0 / len(policies) for name in policies}
        else:
            # 归一化权重
            total_weight = sum(weights.values())
            self.weights = {name: weight / total_weight for name, weight in weights.items()}

        logger.info(f"已创建集成策略 '{name}'，包含 {len(policies)} 个策略，使用 '{method}' 方法")

    @classmethod
    def from_expert_pool(cls, name: str, expert_pool: ExpertPolicyPool,
                        expert_names: List[str], weights: Optional[List[float]] = None,
                        method: str = 'weighted_average') -> 'EnsemblePolicy':
        """
        从专家池创建集成策略

        Args:
            name: 策略名称
            expert_pool: 专家池
            expert_names: 专家名称列表
            weights: 权重列表，与专家名称列表对应
            method: 集成方法

        Returns:
            集成策略实例
        """
        # 获取专家策略
        policies = {}
        for expert_name in expert_names:
            expert = expert_pool.get_expert(expert_name)
            if expert is None:
                logger.warning(f"专家 '{expert_name}' 不存在，将被忽略")
                continue
            policies[expert_name] = expert

        # 创建权重字典
        if weights is not None:
            if len(weights) != len(expert_names):
                logger.warning(f"权重列表长度 ({len(weights)}) 与专家列表长度 ({len(expert_names)}) 不匹配，将使用平均权重")
                weights_dict = None
            else:
                weights_dict = {name: weight for name, weight in zip(expert_names, weights) if name in policies}
        else:
            weights_dict = None

        # 创建集成策略
        return cls(name=name, policies=policies, weights=weights_dict, method=method)

    def act(self, state: State, legal_actions: List[int]) -> int:
        """
        选择动作

        Args:
            state: 游戏状态
            legal_actions: 合法动作列表

        Returns:
            选择的动作
        """
        if not legal_actions:
            logger.warning("没有合法动作可选")
            return -1

        if len(legal_actions) == 1:
            return legal_actions[0]

        # 根据集成方法选择动作
        if self.method == 'weighted_average':
            return self._weighted_average(state, legal_actions)
        elif self.method == 'voting':
            return self._voting(state, legal_actions)
        elif self.method == 'stacking':
            return self._stacking(state, legal_actions)
        else:
            logger.warning(f"未知的集成方法 '{self.method}'，将使用加权平均")
            return self._weighted_average(state, legal_actions)

    def _weighted_average(self, state: State, legal_actions: List[int]) -> int:
        """
        使用加权平均选择动作

        Args:
            state: 游戏状态
            legal_actions: 合法动作列表

        Returns:
            选择的动作
        """
        # 初始化动作概率
        action_probs = np.zeros(max(legal_actions) + 1)

        # 收集每个策略的动作概率
        for name, policy in self.policies.items():
            try:
                # 获取策略的动作概率
                if hasattr(policy, 'get_action_probs'):
                    probs = policy.get_action_probs(state, legal_actions)
                else:
                    # 如果策略没有提供动作概率，则使用独热编码
                    action = policy.act(state, legal_actions)
                    probs = np.zeros(max(legal_actions) + 1)
                    if action >= 0 and action <= max(legal_actions):
                        probs[action] = 1.0

                # 加权累加
                weight = self.weights.get(name, 0.0)
                action_probs += weight * probs

            except Exception as e:
                logger.error(f"获取策略 '{name}' 的动作概率时出错: {e}")

        # 只考虑合法动作
        for action in range(len(action_probs)):
            if action not in legal_actions:
                action_probs[action] = 0.0

        # 归一化
        if np.sum(action_probs) > 0:
            action_probs = action_probs / np.sum(action_probs)
        else:
            # 如果所有策略都失败，则使用均匀分布
            for action in legal_actions:
                action_probs[action] = 1.0 / len(legal_actions)

        # 选择概率最高的动作
        return int(np.argmax(action_probs))

    def _voting(self, state: State, legal_actions: List[int]) -> int:
        """
        使用投票选择动作

        Args:
            state: 游戏状态
            legal_actions: 合法动作列表

        Returns:
            选择的动作
        """
        # 初始化投票计数
        votes = {action: 0.0 for action in legal_actions}

        # 收集每个策略的投票
        for name, policy in self.policies.items():
            try:
                # 获取策略的动作
                action = policy.act(state, legal_actions)

                # 如果是合法动作，则计票
                if action in legal_actions:
                    weight = self.weights.get(name, 0.0)
                    votes[action] += weight

            except Exception as e:
                logger.error(f"获取策略 '{name}' 的动作时出错: {e}")

        # 选择得票最高的动作
        if votes:
            return max(votes.items(), key=lambda x: x[1])[0]
        else:
            # 如果所有策略都失败，则随机选择
            return np.random.choice(legal_actions)

    def _stacking(self, state: State, legal_actions: List[int]) -> int:
        """
        使用堆叠（stacking）选择动作

        注意：此方法需要预先训练一个元模型，这里使用简化版本

        Args:
            state: 游戏状态
            legal_actions: 合法动作列表

        Returns:
            选择的动作
        """
        # 简化版本：使用加权平均
        return self._weighted_average(state, legal_actions)

    def get_action_probs(self, state: State, legal_actions: List[int]) -> np.ndarray:
        """
        获取动作概率分布

        Args:
            state: 游戏状态
            legal_actions: 合法动作列表

        Returns:
            动作概率分布
        """
        # 初始化动作概率
        action_probs = np.zeros(max(legal_actions) + 1)

        # 收集每个策略的动作概率
        for name, policy in self.policies.items():
            try:
                # 获取策略的动作概率
                if hasattr(policy, 'get_action_probs'):
                    probs = policy.get_action_probs(state, legal_actions)
                else:
                    # 如果策略没有提供动作概率，则使用独热编码
                    action = policy.act(state, legal_actions)
                    probs = np.zeros(max(legal_actions) + 1)
                    if action >= 0 and action <= max(legal_actions):
                        probs[action] = 1.0

                # 加权累加
                weight = self.weights.get(name, 0.0)
                action_probs += weight * probs

            except Exception as e:
                logger.error(f"获取策略 '{name}' 的动作概率时出错: {e}")

        # 只考虑合法动作
        for action in range(len(action_probs)):
            if action not in legal_actions:
                action_probs[action] = 0.0

        # 归一化
        if np.sum(action_probs) > 0:
            action_probs = action_probs / np.sum(action_probs)
        else:
            # 如果所有策略都失败，则使用均匀分布
            for action in legal_actions:
                action_probs[action] = 1.0 / len(legal_actions)

        return action_probs
