#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
强制终止训练进程脚本

专门用于强制终止卡住的训练进程，释放文件句柄。

使用方法:
    python force_kill_training.py
"""

import psutil
import time
import os


def force_kill_all_training():
    """强制终止所有训练相关进程"""
    print("开始强制终止所有训练进程...")
    
    # 训练相关关键词
    keywords = [
        'cardgame_ai', 'doudizhu', 'training', 'mcts', 'efficient_zero',
        'main_training.py', 'auto_deploy.py', 'quick_start.py'
    ]
    
    killed_count = 0
    
    # 查找并终止所有相关进程
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            proc_info = proc.info
            cmdline = proc_info.get('cmdline', [])
            
            if not cmdline:
                continue
                
            cmdline_str = ' '.join(cmdline).lower()
            
            # 检查是否包含训练相关关键词
            if any(keyword in cmdline_str for keyword in keywords):
                pid = proc_info['pid']
                print(f"强制终止进程: PID={pid}, CMD={cmdline_str[:80]}...")
                
                try:
                    # 获取进程对象
                    process = psutil.Process(pid)
                    
                    # 终止所有子进程
                    children = process.children(recursive=True)
                    for child in children:
                        try:
                            child.kill()
                            print(f"  终止子进程: {child.pid}")
                        except:
                            pass
                    
                    # 终止主进程
                    process.kill()
                    killed_count += 1
                    print(f"  已终止主进程: {pid}")
                    
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
                    
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            continue
    
    print(f"强制终止完成，共终止 {killed_count} 个进程")
    
    # 等待系统清理资源
    print("等待系统清理资源...")
    time.sleep(3)
    
    # 尝试删除可能被锁定的日志文件
    try_cleanup_log_files()
    
    print("清理完成！")


def try_cleanup_log_files():
    """尝试清理被锁定的日志文件"""
    print("尝试清理日志文件...")
    
    log_dirs = ['logs', 'cardgame_ai/logs']
    
    for log_dir in log_dirs:
        if os.path.exists(log_dir):
            try:
                for filename in os.listdir(log_dir):
                    if 'mcts' in filename.lower() and filename.endswith('.log'):
                        filepath = os.path.join(log_dir, filename)
                        try:
                            # 尝试删除文件
                            os.remove(filepath)
                            print(f"  删除日志文件: {filepath}")
                        except PermissionError:
                            print(f"  日志文件仍被占用: {filepath}")
                        except Exception as e:
                            print(f"  删除日志文件失败: {filepath}, 错误: {e}")
            except Exception as e:
                print(f"  扫描日志目录失败: {log_dir}, 错误: {e}")


def main():
    """主函数"""
    print("=" * 50)
    print("强制终止训练进程工具")
    print("=" * 50)
    
    # 确认操作
    confirm = input("确认强制终止所有训练进程? (y/N): ")
    if confirm.lower() != 'y':
        print("已取消操作")
        return
    
    # 执行强制终止
    force_kill_all_training()
    
    # 再次检查是否还有残留进程
    print("\n检查是否还有残留进程...")
    remaining = []
    keywords = ['cardgame_ai', 'doudizhu', 'training', 'mcts', 'efficient_zero']
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            proc_info = proc.info
            cmdline = proc_info.get('cmdline', [])
            
            if cmdline:
                cmdline_str = ' '.join(cmdline).lower()
                if any(keyword in cmdline_str for keyword in keywords):
                    remaining.append(f"PID={proc_info['pid']}: {cmdline_str[:60]}...")
        except:
            continue
    
    if remaining:
        print(f"发现 {len(remaining)} 个残留进程:")
        for proc_info in remaining:
            print(f"  {proc_info}")
        print("\n如果仍有问题，请重启系统或手动终止这些进程")
    else:
        print("所有训练进程已清理完毕！")
    
    print("\n现在可以重新启动训练了。")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n用户取消操作")
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()
