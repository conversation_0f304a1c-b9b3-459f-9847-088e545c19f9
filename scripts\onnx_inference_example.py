#!/usr/bin/env python
"""
ONNX模型推理示例脚本

这个脚本演示了如何使用ONNX Runtime加载和使用导出的ONNX模型进行推理。
包含了不同类型模型的推理示例，包括ValuePolicyNet和Transformer模型。
"""

import os
import sys
import argparse
import logging
import numpy as np
import onnxruntime as ort
from typing import Dict, List, Any, Tuple

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("onnx_inference")


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="使用ONNX Runtime进行模型推理")
    
    parser.add_argument(
        "--model_path", 
        type=str, 
        required=True,
        help="ONNX模型文件路径 (.onnx)"
    )
    
    parser.add_argument(
        "--model_type", 
        type=str, 
        default="value_policy_net",
        choices=[
            "value_policy_net", 
            "dueling_value_policy_net", 
            "enhanced_transformer", 
            "enhanced_value_policy_net",
            "efficient_zero", 
            "enhanced_efficient_zero", 
            "gnn_enhanced_efficient_zero"
        ],
        help="模型类型"
    )
    
    parser.add_argument(
        "--state_dim", 
        type=int, 
        default=342,
        help="状态维度"
    )
    
    parser.add_argument(
        "--belief_dim", 
        type=int, 
        default=162,
        help="信念状态维度"
    )
    
    parser.add_argument(
        "--action_dim", 
        type=int, 
        default=54,
        help="动作维度"
    )
    
    parser.add_argument(
        "--seq_len", 
        type=int, 
        default=100,
        help="Transformer模型的序列长度"
    )
    
    parser.add_argument(
        "--verbose", 
        action="store_true",
        help="是否输出详细日志"
    )
    
    return parser.parse_args()


def create_sample_input(args) -> Dict[str, np.ndarray]:
    """
    创建模型推理的示例输入

    Args:
        args: 命令行参数

    Returns:
        Dict[str, np.ndarray]: 模型输入数据字典
    """
    # 初始化输入字典
    inputs = {}
    
    # 根据模型类型创建不同的输入
    if args.model_type in ["value_policy_net", "dueling_value_policy_net", "enhanced_value_policy_net"]:
        # ValuePolicyNet系列模型
        inputs["state_input"] = np.random.randn(1, args.state_dim).astype(np.float32)
        inputs["belief_input"] = np.random.randn(1, args.belief_dim).astype(np.float32)
        
    elif args.model_type in ["enhanced_transformer"]:
        # Transformer模型
        inputs["x"] = np.random.randn(1, args.seq_len, args.state_dim).astype(np.float32)
        
    elif args.model_type in ["efficient_zero", "enhanced_efficient_zero", "gnn_enhanced_efficient_zero"]:
        # EfficientZero系列模型
        inputs["state"] = np.random.randn(1, args.state_dim).astype(np.float32)
    
    logger.info(f"已创建示例输入: {[(k, v.shape) for k, v in inputs.items()]}")
    return inputs


def run_inference(model_path: str, inputs: Dict[str, np.ndarray]) -> Tuple[np.ndarray, np.ndarray]:
    """
    使用ONNX Runtime运行模型推理

    Args:
        model_path: ONNX模型文件路径
        inputs: 输入数据字典

    Returns:
        Tuple[np.ndarray, np.ndarray]: 策略对数和价值
    """
    # 创建ONNX Runtime推理会话
    logger.info(f"加载ONNX模型: {model_path}")
    try:
        sess_options = ort.SessionOptions()
        sess_options.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL
        session = ort.InferenceSession(
            model_path, 
            sess_options=sess_options, 
            providers=["CPUExecutionProvider"]
        )
        logger.info("已创建ONNX Runtime会话")
    except Exception as e:
        logger.error(f"加载ONNX模型失败: {e}")
        raise
    
    # 获取模型输入名称
    input_names = [input_meta.name for input_meta in session.get_inputs()]
    logger.info(f"模型输入名称: {input_names}")
    
    # 确保输入名称与提供的输入数据匹配
    for name in input_names:
        if name not in inputs:
            logger.warning(f"模型需要输入'{name}'，但未提供")
            # 可以在这里处理缺失的输入
    
    # 运行推理
    logger.info("开始推理...")
    try:
        outputs = session.run(None, inputs)
        logger.info(f"推理成功，输出形状: {[o.shape for o in outputs]}")
    except Exception as e:
        logger.error(f"推理失败: {e}")
        raise
    
    # 解析输出
    policy_logits, value = outputs
    return policy_logits, value


def process_output(policy_logits: np.ndarray, value: np.ndarray, args) -> Dict[str, Any]:
    """
    处理模型输出

    Args:
        policy_logits: 策略对数
        value: 价值
        args: 命令行参数

    Returns:
        Dict[str, Any]: 处理后的结果
    """
    # 计算动作概率
    policy_probs = np.exp(policy_logits) / np.sum(np.exp(policy_logits), axis=-1, keepdims=True)
    
    # 获取前5个最高概率的动作
    top_action_indices = np.argsort(policy_probs[0])[::-1][:5]
    top_action_probs = policy_probs[0][top_action_indices]
    
    # 创建结果字典
    result = {
        "value": float(value[0]),
        "top_actions": [
            {"action_id": int(idx), "probability": float(prob)} 
            for idx, prob in zip(top_action_indices, top_action_probs)
        ]
    }
    
    return result


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 设置日志级别
    if args.verbose:
        logger.setLevel(logging.DEBUG)
    
    # 检查模型文件是否存在
    if not os.path.exists(args.model_path):
        logger.error(f"模型文件不存在: {args.model_path}")
        return
    
    # 创建示例输入
    inputs = create_sample_input(args)
    
    # 运行推理
    try:
        policy_logits, value = run_inference(args.model_path, inputs)
    except Exception as e:
        logger.error(f"运行推理失败: {e}")
        return
    
    # 处理输出
    result = process_output(policy_logits, value, args)
    
    # 打印结果
    logger.info(f"推理结果:")
    logger.info(f"  价值评估: {result['value']:.4f}")
    logger.info(f"  前5个高概率动作:")
    for i, action in enumerate(result['top_actions']):
        logger.info(f"    #{i+1}: 动作ID {action['action_id']}, 概率 {action['probability']:.4f}")
    
    # 根据模型类型打印额外的信息
    if args.model_type in ["value_policy_net", "dueling_value_policy_net", "enhanced_value_policy_net"]:
        logger.info(f"  模型类型: {args.model_type}")
        logger.info(f"  状态维度: {args.state_dim}")
        logger.info(f"  信念维度: {args.belief_dim}")
    elif args.model_type in ["enhanced_transformer"]:
        logger.info(f"  模型类型: {args.model_type}")
        logger.info(f"  序列长度: {args.seq_len}")


def run_demo():
    """
    运行一个集成的演示，展示如何在代码中使用ONNX模型
    """
    print("=" * 50)
    print("ONNX模型集成演示")
    print("=" * 50)
    
    # 演示代码
    print("\n以下是如何在自己的代码中集成ONNX模型的示例:")
    print("""
import onnxruntime as ort
import numpy as np

# 1. 加载ONNX模型
session = ort.InferenceSession("models/onnx/your_model.onnx")

# 2. 准备输入数据 (根据模型类型调整)
input_data = {
    "state_input": np.random.randn(1, 342).astype(np.float32),
    "belief_input": np.random.randn(1, 162).astype(np.float32)
}

# 3. 运行推理
outputs = session.run(None, input_data)
policy_logits, value = outputs

# 4. 处理输出结果
policy_probs = np.exp(policy_logits) / np.sum(np.exp(policy_logits), axis=-1, keepdims=True)
top_action_indices = np.argsort(policy_probs[0])[::-1][:5]
top_action_probs = policy_probs[0][top_action_indices]

print(f"价值评估: {float(value[0]):.4f}")
print("前5个高概率动作:")
for i, (idx, prob) in enumerate(zip(top_action_indices, top_action_probs)):
    print(f"  #{i+1}: 动作ID {int(idx)}, 概率 {float(prob):.4f}")
""")
    print("=" * 50)


if __name__ == "__main__":
    if len(sys.argv) > 1:
        main()
    else:
        # 如果没有参数，运行演示代码
        run_demo()
        print("\n使用方法: python onnx_inference_example.py --model_path <ONNX模型路径> --model_type <模型类型>")
        print("详细帮助: python onnx_inference_example.py --help") 