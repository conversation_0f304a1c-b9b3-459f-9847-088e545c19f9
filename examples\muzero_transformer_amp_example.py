#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MuZeroTransformerAMP使用示例

该脚本展示如何在实际项目中使用MuZeroTransformerAMP进行混合精度训练。
"""

import os
import torch
import random
import argparse
import numpy as np
from tqdm import tqdm
from datetime import datetime

from cardgame_ai.core.replay_buffer import ReplayBuffer
from cardgame_ai.core.self_play import SelfPlay
from cardgame_ai.games.doudizhu import DouDizhuEnvironment, DouDizhuState
from cardgame_ai.games.doudizhu.card_group import CardGroup
from muzero_transformer_amp import MuZeroTransformerAMP


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='MuZeroTransformerAMP斗地主示例')
    
    # 模型参数
    parser.add_argument('--input_dim', type=int, default=512,
                        help='输入维度 (默认: 512)')
    parser.add_argument('--action_dim', type=int, default=27472,
                        help='动作维度 (默认: 27472)')
    parser.add_argument('--state_dim', type=int, default=256,
                        help='状态维度 (默认: 256)')
    parser.add_argument('--hidden_dim', type=int, default=512,
                        help='隐藏层维度 (默认: 512)')
    parser.add_argument('--num_heads', type=int, default=8,
                        help='注意力头数量 (默认: 8)')
    parser.add_argument('--num_layers', type=int, default=6,
                        help='Transformer层数 (默认: 6)')
    parser.add_argument('--seq_len', type=int, default=10,
                        help='序列长度 (默认: 10)')
    
    # 训练参数
    parser.add_argument('--num_simulations', type=int, default=50,
                        help='MCTS模拟次数 (默认: 50)')
    parser.add_argument('--batch_size', type=int, default=128,
                        help='批次大小 (默认: 128)')
    parser.add_argument('--num_epochs', type=int, default=10,
                        help='训练轮数 (默认: 10)')
    parser.add_argument('--lr', type=float, default=0.001,
                        help='学习率 (默认: 0.001)')
    parser.add_argument('--weight_decay', type=float, default=1e-4,
                        help='权重衰减 (默认: 1e-4)')
    parser.add_argument('--discount', type=float, default=0.997,
                        help='折扣因子 (默认: 0.997)')
    parser.add_argument('--dirichlet_alpha', type=float, default=0.25,
                        help='Dirichlet噪声参数 (默认: 0.25)')
    parser.add_argument('--exploration_fraction', type=float, default=0.25,
                        help='探索比例 (默认: 0.25)')
    parser.add_argument('--use_mixed_precision', action='store_true',
                        help='是否使用混合精度训练 (默认: False)')
    parser.add_argument('--seed', type=int, default=42,
                        help='随机种子 (默认: 42)')
    
    # 自我对弈参数
    parser.add_argument('--num_self_play_games', type=int, default=100,
                        help='自我对弈游戏数量 (默认: 100)')
    parser.add_argument('--num_actors', type=int, default=4,
                        help='并行执行的actor数量 (默认: 4)')
    
    # 输出参数
    parser.add_argument('--model_dir', type=str, default='models',
                        help='模型保存目录 (默认: models)')
    parser.add_argument('--load_model', action='store_true',
                        help='是否加载已有模型 (默认: False)')
    parser.add_argument('--model_path', type=str, default=None,
                        help='要加载的模型路径 (默认: None)')
    
    return parser.parse_args()


def create_agent(args):
    """创建MuZeroTransformerAMP智能体"""
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    agent = MuZeroTransformerAMP(
        input_dim=args.input_dim,
        action_dim=args.action_dim,
        state_dim=args.state_dim,
        hidden_dim=args.hidden_dim,
        num_simulations=args.num_simulations,
        discount=args.discount,
        dirichlet_alpha=args.dirichlet_alpha,
        exploration_fraction=args.exploration_fraction,
        lr=args.lr,
        weight_decay=args.weight_decay,
        num_heads=args.num_heads,
        num_layers=args.num_layers,
        dropout=0.1,
        seq_len=args.seq_len,
        device=device,
        use_mixed_precision=args.use_mixed_precision
    )
    
    # 加载预训练模型（如果有）
    if args.load_model and args.model_path and os.path.exists(args.model_path):
        print(f"加载模型：{args.model_path}")
        agent.load(args.model_path)
    
    return agent


def self_play(agent, env, num_games, args):
    """执行自我对弈"""
    replay_buffer = ReplayBuffer(capacity=10000)
    
    for game_idx in tqdm(range(num_games), desc="自我对弈"):
        state = env.reset()
        done = False
        game_history = []
        
        while not done:
            # 预测动作
            action = agent.predict_action(state, temperature=1.0)
            
            # 执行动作
            next_state, reward, done, info = env.step(action)
            
            # 记录经验
            experience = {
                'state': state,
                'action': action,
                'reward': reward,
                'next_state': next_state,
                'done': done,
                'info': info
            }
            game_history.append(experience)
            
            # 更新状态
            state = next_state
        
        # 计算回报
        returns = []
        G = 0
        for experience in reversed(game_history):
            G = experience['reward'] + args.discount * G
            returns.insert(0, G)
        
        # 添加到回放缓冲区
        for i, experience in enumerate(game_history):
            experience['value'] = returns[i]
            replay_buffer.add(experience)
    
    return replay_buffer


def train(agent, replay_buffer, args):
    """训练智能体"""
    for epoch in range(args.num_epochs):
        total_loss = 0
        num_batches = 0
        
        # 创建数据加载器
        indices = list(range(len(replay_buffer)))
        random.shuffle(indices)
        
        # 按批次训练
        for i in range(0, len(indices), args.batch_size):
            batch_indices = indices[i:i+args.batch_size]
            if len(batch_indices) < args.batch_size:
                continue
            
            # 准备批次数据
            batch = replay_buffer.sample_batch(batch_indices)
            
            # 更新模型
            losses = agent.update(batch)
            total_loss += losses['total_loss']
            num_batches += 1
            
            # 打印进度
            if num_batches % 10 == 0:
                print(f"Epoch {epoch+1}/{args.num_epochs}, Batch {num_batches}, Loss: {losses['total_loss']:.4f}")
        
        # 打印轮次结果
        avg_loss = total_loss / num_batches if num_batches > 0 else 0
        print(f"Epoch {epoch+1}/{args.num_epochs} 完成, 平均损失: {avg_loss:.4f}")
        
        # 保存模型
        model_path = os.path.join(args.model_dir, f"muzero_transformer_amp_epoch_{epoch+1}.pt")
        agent.save(model_path)
        print(f"模型已保存到 {model_path}")


def main():
    """主函数"""
    args = parse_args()
    
    # 设置随机种子
    random.seed(args.seed)
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(args.seed)
    
    # 创建模型目录
    os.makedirs(args.model_dir, exist_ok=True)
    
    # 创建环境
    env = DouDizhuEnvironment()
    
    # 创建智能体
    agent = create_agent(args)
    
    # 打印训练配置
    print("训练配置:")
    print(f"- 使用混合精度训练: {args.use_mixed_precision}")
    print(f"- 设备: {agent.device}")
    print(f"- 批次大小: {args.batch_size}")
    print(f"- 学习率: {args.lr}")
    print(f"- Transformer层数: {args.num_layers}")
    print(f"- 注意力头数量: {args.num_heads}")
    
    # 自我对弈
    print(f"开始自我对弈 ({args.num_self_play_games} 局游戏)...")
    replay_buffer = self_play(agent, env, args.num_self_play_games, args)
    
    # 训练模型
    print(f"开始训练 ({args.num_epochs} 轮)...")
    train(agent, replay_buffer, args)
    
    print("训练完成!")


if __name__ == "__main__":
    main()
