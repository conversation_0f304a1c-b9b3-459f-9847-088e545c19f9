{"analysis": {"total_trials": 2, "successful_trials": 2, "failed_trials": 0, "avg_elapsed_time": 0.323045015335083, "avg_num_experiences": 700.5, "avg_landlord_win_rate": 0.45, "avg_farmer_win_rate": 0.55, "avg_game_length": 92.94999999999999, "avg_game_length_std": 6.071823610583689, "min_game_length": 75, "max_game_length": 106, "avg_memory_usage": 1.638671875, "avg_reward": 0.01625642808189046, "best_params": {"temperature": 1.7971924277897826, "num_games": 10, "batch_size": 64, "learning_rate": 0.0004801546150723435, "gamma": 0.9867569437012212, "save_experiences": false, "parallel": true}, "worst_params": {"temperature": 1.4838659047215808, "num_games": 5, "batch_size": 128, "learning_rate": 0.0006142207303154123, "gamma": 0.914026012660939, "save_experiences": false, "parallel": false}, "fastest_params": {"temperature": 1.4838659047215808, "num_games": 5, "batch_size": 128, "learning_rate": 0.0006142207303154123, "gamma": 0.914026012660939, "save_experiences": false, "parallel": false}, "slowest_params": {"temperature": 1.7971924277897826, "num_games": 10, "batch_size": 64, "learning_rate": 0.0004801546150723435, "gamma": 0.9867569437012212, "save_experiences": false, "parallel": true}, "best_landlord_win_rate": 0.5, "worst_landlord_win_rate": 0.4, "fastest_time": 0.22368693351745605, "slowest_time": 0.42240309715270996, "common_errors": {}, "parameter_correlations": {}, "phase_distribution": {"BIDDING": 0.032119914346895075, "GRABBING": 0.032119914346895075, "PLAYING": 0.9357601713062098}, "success_rate": 1.0, "max_memory_usage": 1.7578125, "min_memory_usage": 1.51953125, "positive_reward_ratio": 0.05924339757316203, "negative_reward_ratio": 0.015703069236259814, "zero_reward_ratio": 0.9250535331905781}, "bugs": [{"id": 1, "trial": 1, "type": "reward_distribution_issue", "description": "奖励分布不平衡: 零奖励占比92.36%", "severity": "medium", "possible_cause": "奖励函数可能设计不合理，导致奖励分布过于集中", "fix_suggestion": "重新设计奖励函数，使奖励分布更加均衡"}, {"id": 2, "trial": 2, "type": "reward_distribution_issue", "description": "奖励分布不平衡: 零奖励占比92.58%", "severity": "medium", "possible_cause": "奖励函数可能设计不合理，导致奖励分布过于集中", "fix_suggestion": "重新设计奖励函数，使奖励分布更加均衡"}], "timestamp": "2025-04-29 02:40:15", "num_trials": 2, "total_time": 0.811309814453125, "successful_trials": 2, "failed_trials": 0}