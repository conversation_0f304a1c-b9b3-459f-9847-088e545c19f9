"""
使用MuZeroTransformer进行斗地主游戏的示例

本示例展示如何将基于Transformer的MuZero算法应用于中国传统扑克牌游戏 - 斗地主。
"""
import os
import torch
import random
import argparse
import numpy as np
from tqdm import tqdm
from datetime import datetime

from cardgame_ai.core.replay_buffer import ReplayBuffer
from cardgame_ai.core.self_play import SelfPlay
from cardgame_ai.games.doudizhu import DouDizhuEnvironment, DouDizhuState
from cardgame_ai.games.doudizhu.card_group import CardGroup
from cardgame_ai.algorithms.muzero_transformer import MuZeroTransformer


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='使用MuZeroTransformer进行斗地主游戏')

    # 训练参数
    parser.add_argument('--train', action='store_true', help='是否训练模型')
    parser.add_argument('--epochs', type=int, default=100, help='训练轮数')
    parser.add_argument('--games_per_epoch', type=int, default=10, help='每轮训练的游戏数')
    parser.add_argument('--batch_size', type=int, default=32, help='批次大小')
    parser.add_argument('--memory_size', type=int, default=10000, help='经验回放缓冲区大小')

    # 模型参数
    parser.add_argument('--lr', type=float, default=0.001, help='学习率')
    parser.add_argument('--state_dim', type=int, default=128, help='状态维度')
    parser.add_argument('--hidden_dim', type=int, default=256, help='隐藏层维度')
    parser.add_argument('--num_heads', type=int, default=4, help='多头注意力头数')
    parser.add_argument('--num_layers', type=int, default=3, help='Transformer层数')
    parser.add_argument('--dropout', type=float, default=0.1, help='Dropout比率')
    parser.add_argument('--seq_len', type=int, default=10, help='序列长度')

    # MCTS参数
    parser.add_argument('--num_simulations', type=int, default=50, help='每步MCTS模拟次数')
    parser.add_argument('--discount', type=float, default=0.997, help='折扣因子')
    parser.add_argument('--dirichlet_alpha', type=float, default=0.25, help='Dirichlet噪声参数')
    parser.add_argument('--exploration_fraction', type=float, default=0.25, help='探索比例')

    # 保存/加载参数
    parser.add_argument('--model_path', type=str, default='models/muzero_transformer_doudizhu.pt', help='模型路径')
    parser.add_argument('--load_model', action='store_true', help='是否加载已有模型')

    # 测试参数
    parser.add_argument('--test_games', type=int, default=10, help='测试游戏数')
    parser.add_argument('--render', action='store_true', help='是否渲染游戏界面')

    args = parser.parse_args()
    return args


def set_seed(seed=42):
    """设置随机种子以确保结果可复现"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False


def create_agent(args, input_dim, action_dim):
    """创建MuZeroTransformer智能体"""
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    agent = MuZeroTransformer(
        input_dim=input_dim,
        action_dim=action_dim,
        state_dim=args.state_dim,
        hidden_dim=args.hidden_dim,
        num_simulations=args.num_simulations,
        discount=args.discount,
        dirichlet_alpha=args.dirichlet_alpha,
        exploration_fraction=args.exploration_fraction,
        lr=args.lr,
        num_heads=args.num_heads,
        num_layers=args.num_layers,
        dropout=args.dropout,
        seq_len=args.seq_len,
        device=device
    )

    # 加载预训练模型（如果有）
    if args.load_model and os.path.exists(args.model_path):
        print(f"加载模型：{args.model_path}")
        agent.load(args.model_path)

    return agent


def train(args):
    """训练MuZeroTransformer模型"""
    print("开始训练MuZeroTransformer斗地主智能体...")

    # 创建游戏环境
    game = DouDizhuEnvironment()
    input_dim = game.observation_space.shape[0]  # 使用observation_space.shape获取观察维度
    action_dim = game.action_space.n  # 使用action_space.n获取动作维度

    # 创建智能体
    agent = create_agent(args, input_dim, action_dim)

    # 创建经验回放缓冲区
    replay_buffer = ReplayBuffer(capacity=args.memory_size)

    # 创建自对弈环境
    self_play = SelfPlay(game=game, agent=agent)

    # 确保模型保存目录存在
    os.makedirs(os.path.dirname(args.model_path), exist_ok=True)

    # 训练循环
    for epoch in range(args.epochs):
        print(f"\n第 {epoch+1}/{args.epochs} 轮训练")

        # 自对弈收集数据
        print("进行自对弈...")
        experiences = []
        for _ in tqdm(range(args.games_per_epoch)):
            game_experiences = self_play.play_game(temperature=1.0)
            experiences.extend(game_experiences)
            replay_buffer.add_experiences(game_experiences)

        print(f"收集了 {len(experiences)} 条新经验，总经验数: {len(replay_buffer)}")

        # 更新模型
        print("更新模型...")
        total_loss = 0
        policy_loss = 0
        value_loss = 0
        reward_loss = 0

        num_batches = min(200, len(replay_buffer) // args.batch_size)
        for _ in tqdm(range(num_batches)):
            batch = replay_buffer.sample(args.batch_size)
            losses = agent.update(batch)

            total_loss += losses["total_loss"]
            policy_loss += losses["policy_loss"]
            value_loss += losses["value_loss"]
            reward_loss += losses["reward_loss"]

        avg_total_loss = total_loss / num_batches
        avg_policy_loss = policy_loss / num_batches
        avg_value_loss = value_loss / num_batches
        avg_reward_loss = reward_loss / num_batches

        print(f"损失：总体 = {avg_total_loss:.4f}, 策略 = {avg_policy_loss:.4f}, 价值 = {avg_value_loss:.4f}, 奖励 = {avg_reward_loss:.4f}")

        # 每10轮保存一次模型，或最后一轮
        if (epoch + 1) % 10 == 0 or epoch == args.epochs - 1:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            model_path = args.model_path.replace('.pt', f'_epoch{epoch+1}_{timestamp}.pt')
            agent.save(model_path)
            print(f"模型已保存至: {model_path}")

    print("训练完成!")


def test(args):
    """测试训练好的MuZeroTransformer模型"""
    print("开始测试MuZeroTransformer斗地主智能体...")

    # 创建游戏环境
    game = DouDizhuEnvironment()
    input_dim = game.observation_space.shape[0]  # 使用observation_space.shape获取观察维度
    action_dim = game.action_space.n  # 使用action_space.n获取动作维度

    # 创建智能体
    agent = create_agent(args, input_dim, action_dim)

    # 如果没有模型文件，则退出
    if not os.path.exists(args.model_path):
        print(f"模型文件 {args.model_path} 不存在，请先训练模型或提供正确的模型路径。")
        return

    # 加载模型
    agent.load(args.model_path)
    print(f"已加载模型: {args.model_path}")

    # 进行测试游戏
    print(f"进行 {args.test_games} 场测试游戏...")

    wins = 0
    for game_idx in range(args.test_games):
        print(f"\n游戏 {game_idx+1}/{args.test_games}")

        # 初始化游戏状态
        state = game.reset()
        done = False

        # 如果需要，渲染初始状态
        if args.render:
            game.render()

        # 游戏循环
        while not done:
            # 当前玩家
            current_player = state.current_player

            # 获取动作
            if current_player == 0:  # 假设玩家0是我们的智能体
                action = agent.predict_action(state, temperature=0.1, deterministic=True)
            else:
                # 其他玩家使用随机策略
                valid_actions = game.get_legal_actions(state)
                action = random.choice(valid_actions)

            # 执行动作
            next_state, reward, done, info = game.step(action)

            # 如果需要，渲染状态
            if args.render:
                game.render()
                print(f"玩家 {current_player} 执行动作: {action}")
                if done:
                    payoffs = info.get("payoffs", [0, 0, 0])
                    winner = next(i for i, p in enumerate(payoffs) if p > 0) if any(p > 0 for p in payoffs) else -1
                    print(f"游戏结束，赢家: 玩家 {winner}")

            # 更新状态
            state = next_state

        # 判断是否获胜
        payoffs = info.get("payoffs", [0, 0, 0])
        if payoffs[0] > 0:  # 我们的智能体获胜
            wins += 1
            print(f"游戏 {game_idx+1}: 获胜!")
        else:
            print(f"游戏 {game_idx+1}: 失败.")

    # 输出总结
    win_rate = wins / args.test_games
    print(f"\n测试结果: {wins} 胜 / {args.test_games} 局，胜率: {win_rate:.2%}")


def main():
    """主函数"""
    args = parse_args()
    set_seed(42)

    if args.train:
        train(args)

    test(args)


if __name__ == "__main__":
    main()