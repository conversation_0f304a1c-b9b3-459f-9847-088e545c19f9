"""
增强版Transformer网络模块

实现增强的Transformer架构，包括改进的注意力机制、位置编码和可视化功能。
主要用于提高模型处理复杂游戏状态的能力。
"""
import math
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional, Any, Union
import matplotlib.pyplot as plt
import seaborn as sns


def visualize_attention(attention_weights, tokens=None, save_path=None):
    """
    可视化注意力权重

    Args:
        attention_weights (torch.Tensor): 注意力权重张量，形状为 [batch_size, num_heads, seq_len_q, seq_len_k]
        tokens (List[str], optional): 序列标记. Defaults to None.
        save_path (str, optional): 保存路径. Defaults to None.
    """
    # 取第一个批次的注意力权重
    if isinstance(attention_weights, torch.Tensor):
        attention_weights = attention_weights.detach().cpu().numpy()

    attention_weights = attention_weights[0]  # [num_heads, seq_len_q, seq_len_k]
    num_heads = attention_weights.shape[0]

    fig, axes = plt.subplots(nrows=math.ceil(num_heads/2), ncols=2, figsize=(15, num_heads * 2))
    axes = axes.flatten()

    for h in range(num_heads):
        im = axes[h].imshow(attention_weights[h], cmap='viridis')
        axes[h].set_title(f'Head {h+1}')

        if tokens is not None:
            axes[h].set_xticks(np.arange(len(tokens)))
            axes[h].set_yticks(np.arange(len(tokens)))
            axes[h].set_xticklabels(tokens, rotation=45, ha='right')
            axes[h].set_yticklabels(tokens)

    # 隐藏多余的子图
    for h in range(num_heads, len(axes)):
        axes[h].axis('off')

    plt.tight_layout()
    fig.colorbar(im, ax=axes.ravel().tolist())

    if save_path:
        plt.savefig(save_path)
    plt.show()


class EnhancedMultiHeadAttention(nn.Module):
    """
    增强版多头注意力机制

    相比标准多头注意力，增加了以下功能：
    1. 支持更多注意力头
    2. 添加注意力dropout
    3. 支持注意力可视化
    4. 实现注意力权重输出
    """

    def __init__(
        self,
        hidden_dim: int,
        num_heads: int,
        dropout: float = 0.1,
        output_attention: bool = False
    ):
        """
        初始化增强版多头注意力层

        Args:
            hidden_dim (int): 隐藏层维度，必须能被num_heads整除
            num_heads (int): 注意力头数量
            dropout (float, optional): Dropout比率. Defaults to 0.1.
            output_attention (bool, optional): 是否输出注意力权重. Defaults to False.
        """
        super(EnhancedMultiHeadAttention, self).__init__()
        assert hidden_dim % num_heads == 0, "隐藏层维度必须能被注意力头数量整除"

        self.hidden_dim = hidden_dim
        self.num_heads = num_heads
        self.head_dim = hidden_dim // num_heads
        self.output_attention = output_attention

        # 线性投影层
        self.query = nn.Linear(hidden_dim, hidden_dim)
        self.key = nn.Linear(hidden_dim, hidden_dim)
        self.value = nn.Linear(hidden_dim, hidden_dim)

        # 输出投影
        self.output = nn.Linear(hidden_dim, hidden_dim)

        # Dropout层
        self.attn_dropout = nn.Dropout(dropout)
        self.output_dropout = nn.Dropout(dropout)

        # 用于缩放点积注意力
        self.scale = 1 / (self.head_dim ** 0.5)

    def forward(
        self,
        query: torch.Tensor,
        key: torch.Tensor,
        value: torch.Tensor,
        mask: Optional[torch.Tensor] = None
    ) -> Union[torch.Tensor, Tuple[torch.Tensor, torch.Tensor]]:
        """
        前向传播

        Args:
            query (torch.Tensor): 查询张量，形状为 [batch_size, seq_len_q, hidden_dim]
            key (torch.Tensor): 键张量，形状为 [batch_size, seq_len_k, hidden_dim]
            value (torch.Tensor): 值张量，形状为 [batch_size, seq_len_v, hidden_dim]
            mask (Optional[torch.Tensor], optional): 掩码张量. Defaults to None.

        Returns:
            Union[torch.Tensor, Tuple[torch.Tensor, torch.Tensor]]:
                如果output_attention为False，返回注意力输出
                如果output_attention为True，返回(注意力输出, 注意力权重)
        """
        batch_size = query.shape[0]

        # 线性投影并分割为多个头
        Q = self.query(query).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        K = self.key(key).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        V = self.value(value).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)

        # 计算注意力分数
        scores = torch.matmul(Q, K.transpose(-2, -1)) * self.scale

        # 应用掩码（如果提供）
        if mask is not None:
            # 扩展掩码以适应多头注意力
            if mask.dim() == 3:  # [batch_size, seq_len_q, seq_len_k]
                mask = mask.unsqueeze(1)  # [batch_size, 1, seq_len_q, seq_len_k]
            scores = scores.masked_fill(mask == 0, -1e9)

        # 计算注意力权重
        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.attn_dropout(attention_weights)

        # 应用注意力权重
        out = torch.matmul(attention_weights, V)

        # 重塑并连接所有头
        out = out.transpose(1, 2).contiguous().view(batch_size, -1, self.hidden_dim)

        # 最终线性投影
        out = self.output(out)
        out = self.output_dropout(out)

        if self.output_attention:
            return out, attention_weights
        return out


class EnhancedPositionalEncoding(nn.Module):
    """
    增强版位置编码

    支持三种位置编码方式：
    1. 标准正弦位置编码（绝对位置）
    2. 可学习位置编码（可训练的嵌入）
    3. 相对位置编码（相对位置信息）
    """

    def __init__(
        self,
        d_model: int,
        max_seq_len: int = 5000,
        dropout: float = 0.1,
        encoding_type: str = 'sinusoidal'
    ):
        """
        初始化增强版位置编码

        Args:
            d_model (int): 模型维度
            max_seq_len (int, optional): 最大序列长度. Defaults to 5000.
            dropout (float, optional): Dropout比率. Defaults to 0.1.
            encoding_type (str, optional): 编码类型, 可选 'sinusoidal', 'learned', 'relative'. Defaults to 'sinusoidal'.
        """
        super(EnhancedPositionalEncoding, self).__init__()
        self.encoding_type = encoding_type
        self.dropout = nn.Dropout(p=dropout)

        if encoding_type == 'sinusoidal':
            # 标准正弦位置编码
            pe = torch.zeros(max_seq_len, d_model)
            position = torch.arange(0, max_seq_len, dtype=torch.float).unsqueeze(1)
            div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
            pe[:, 0::2] = torch.sin(position * div_term)
            pe[:, 1::2] = torch.cos(position * div_term)
            pe = pe.unsqueeze(0)  # [1, max_seq_len, d_model]
            self.register_buffer('pe', pe)
        elif encoding_type == 'learned':
            # 可学习位置编码
            self.pe = nn.Parameter(torch.randn(1, max_seq_len, d_model))
        elif encoding_type == 'relative':
            # 相对位置编码
            self.relative_positions_bias = nn.Parameter(torch.randn(2 * max_seq_len - 1, d_model))
            positions = torch.arange(max_seq_len)
            relative_positions = positions.unsqueeze(1) - positions.unsqueeze(0)  # [seq_len, seq_len]
            relative_positions += max_seq_len - 1  # 将范围调整为 [0, 2*max_seq_len-2]
            self.register_buffer('relative_positions', relative_positions)
        else:
            raise ValueError(f"不支持的位置编码类型: {encoding_type}")

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播

        Args:
            x (torch.Tensor): 输入张量，形状为 [batch_size, seq_len, d_model]

        Returns:
            torch.Tensor: 添加位置编码后的张量
        """
        if self.encoding_type in ['sinusoidal', 'learned']:
            seq_len = x.size(1)
            # 添加位置编码
            x = x + self.pe[:, :seq_len, :]
            return self.dropout(x)
        elif self.encoding_type == 'relative':
            # 相对位置编码在注意力计算中使用，这里只返回原始输入
            return self.dropout(x)

    def get_relative_attention_bias(self, seq_len: int) -> torch.Tensor:
        """
        获取相对位置注意力偏置

        Args:
            seq_len (int): 序列长度

        Returns:
            torch.Tensor: 相对位置注意力偏置张量，形状为 [seq_len, seq_len, d_model]
        """
        if self.encoding_type != 'relative':
            raise ValueError("只有相对位置编码才支持此方法")

        # 获取当前序列长度的相对位置
        relative_positions = self.relative_positions[:seq_len, :seq_len]  # [seq_len, seq_len]

        # 获取相应的位置编码
        return self.relative_positions_bias[relative_positions]  # [seq_len, seq_len, d_model]


class EnhancedMultiHeadAttentionWithRelPos(EnhancedMultiHeadAttention):
    """
    支持相对位置编码的增强版多头注意力

    扩展原始的多头注意力，添加了相对位置编码支持。
    """

    def __init__(
        self,
        hidden_dim: int,
        num_heads: int,
        dropout: float = 0.1,
        output_attention: bool = False,
        max_seq_len: int = 5000
    ):
        """
        初始化支持相对位置编码的增强版多头注意力层

        Args:
            hidden_dim (int): 隐藏层维度，必须能被num_heads整除
            num_heads (int): 注意力头数量
            dropout (float, optional): Dropout比率. Defaults to 0.1.
            output_attention (bool, optional): 是否输出注意力权重. Defaults to False.
            max_seq_len (int, optional): 最大序列长度. Defaults to 5000.
        """
        super(EnhancedMultiHeadAttentionWithRelPos, self).__init__(
            hidden_dim=hidden_dim,
            num_heads=num_heads,
            dropout=dropout,
            output_attention=output_attention
        )

        # 相对位置编码参数
        self.max_seq_len = max_seq_len

        # 相对位置编码偏置
        self.relative_attention_bias = nn.Parameter(torch.randn(2 * max_seq_len - 1, num_heads))
        positions = torch.arange(max_seq_len)
        relative_positions = positions.unsqueeze(1) - positions.unsqueeze(0)  # [seq_len, seq_len]
        relative_positions += max_seq_len - 1  # 将范围调整为 [0, 2*max_seq_len-2]
        self.register_buffer('relative_positions', relative_positions)

    def forward(
        self,
        query: torch.Tensor,
        key: torch.Tensor,
        value: torch.Tensor,
        mask: Optional[torch.Tensor] = None
    ) -> Union[torch.Tensor, Tuple[torch.Tensor, torch.Tensor]]:
        """
        前向传播，支持相对位置编码

        Args:
            query (torch.Tensor): 查询张量，形状为 [batch_size, seq_len_q, hidden_dim]
            key (torch.Tensor): 键张量，形状为 [batch_size, seq_len_k, hidden_dim]
            value (torch.Tensor): 值张量，形状为 [batch_size, seq_len_v, hidden_dim]
            mask (Optional[torch.Tensor], optional): 掩码张量. Defaults to None.

        Returns:
            Union[torch.Tensor, Tuple[torch.Tensor, torch.Tensor]]:
                如果output_attention为False，返回注意力输出
                如果output_attention为True，返回(注意力输出, 注意力权重)
        """
        batch_size = query.shape[0]
        seq_len_q = query.shape[1]
        seq_len_k = key.shape[1]

        # 线性投影并分割为多个头
        Q = self.query(query).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        K = self.key(key).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        V = self.value(value).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)

        # 计算注意力分数
        scores = torch.matmul(Q, K.transpose(-2, -1)) * self.scale

        # 添加相对位置编码偏置
        # 获取当前序列长度的相对位置
        relative_positions = self.relative_positions[:seq_len_q, :seq_len_k]  # [seq_len_q, seq_len_k]
        # 获取相应的位置编码偏置
        rel_pos_bias = self.relative_attention_bias[relative_positions]  # [seq_len_q, seq_len_k, num_heads]
        # 调整维度顺序并添加到注意力分数
        rel_pos_bias = rel_pos_bias.permute(2, 0, 1).unsqueeze(0)  # [1, num_heads, seq_len_q, seq_len_k]
        scores = scores + rel_pos_bias

        # 应用掩码（如果提供）
        if mask is not None:
            # 扩展掩码以适应多头注意力
            if mask.dim() == 3:  # [batch_size, seq_len_q, seq_len_k]
                mask = mask.unsqueeze(1)  # [batch_size, 1, seq_len_q, seq_len_k]
            scores = scores.masked_fill(mask == 0, -1e9)

        # 计算注意力权重
        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.attn_dropout(attention_weights)

        # 应用注意力权重
        out = torch.matmul(attention_weights, V)

        # 重塑并连接所有头
        out = out.transpose(1, 2).contiguous().view(batch_size, -1, self.hidden_dim)

        # 最终线性投影
        out = self.output(out)
        out = self.output_dropout(out)

        if self.output_attention:
            return out, attention_weights
        return out


class EnhancedTransformerEncoderLayer(nn.Module):
    """
    增强版Transformer编码器层

    包含增强的多头注意力、前馈网络和层正规化。
    """

    def __init__(
        self,
        hidden_dim: int,
        num_heads: int,
        ff_dim: int = None,
        dropout: float = 0.1,
        activation: str = 'gelu',
        use_relative_pos: bool = False,
        max_seq_len: int = 5000,
        output_attention: bool = False,
        pre_norm: bool = False
    ):
        """
        初始化增强版Transformer编码器层

        Args:
            hidden_dim (int): 隐藏层维度
            num_heads (int): 注意力头数量
            ff_dim (int, optional): 前馈网络维度. Defaults to None (使用4*hidden_dim).
            dropout (float, optional): Dropout比率. Defaults to 0.1.
            activation (str, optional): 激活函数. Defaults to 'gelu'.
            use_relative_pos (bool, optional): 是否使用相对位置编码. Defaults to False.
            max_seq_len (int, optional): 最大序列长度. Defaults to 5000.
            output_attention (bool, optional): 是否输出注意力权重. Defaults to False.
            pre_norm (bool, optional): 是否使用前置层正规化. Defaults to False.
        """
        super(EnhancedTransformerEncoderLayer, self).__init__()

        # 设置前馈网络维度
        if ff_dim is None:
            ff_dim = 4 * hidden_dim

        # 设置激活函数
        if activation == 'gelu':
            self.activation = F.gelu
        elif activation == 'relu':
            self.activation = F.relu
        else:
            raise ValueError(f"不支持的激活函数: {activation}")

        # 注意力层
        if use_relative_pos:
            self.attention = EnhancedMultiHeadAttentionWithRelPos(
                hidden_dim=hidden_dim,
                num_heads=num_heads,
                dropout=dropout,
                output_attention=output_attention,
                max_seq_len=max_seq_len
            )
        else:
            self.attention = EnhancedMultiHeadAttention(
                hidden_dim=hidden_dim,
                num_heads=num_heads,
                dropout=dropout,
                output_attention=output_attention
            )

        # 前馈网络
        self.feed_forward = nn.Sequential(
            nn.Linear(hidden_dim, ff_dim),
            nn.Dropout(dropout),
            nn.GELU() if activation == 'gelu' else nn.ReLU(),
            nn.Linear(ff_dim, hidden_dim),
            nn.Dropout(dropout)
        )

        # 层正规化
        self.norm1 = nn.LayerNorm(hidden_dim)
        self.norm2 = nn.LayerNorm(hidden_dim)

        # 设置是否使用前置层正规化
        self.pre_norm = pre_norm
        self.output_attention = output_attention

    def forward(
        self,
        x: torch.Tensor,
        mask: Optional[torch.Tensor] = None
    ) -> Union[torch.Tensor, Tuple[torch.Tensor, torch.Tensor]]:
        """
        前向传播

        Args:
            x (torch.Tensor): 输入张量，形状为 [batch_size, seq_len, hidden_dim]
            mask (Optional[torch.Tensor], optional): 注意力掩码. Defaults to None.

        Returns:
            Union[torch.Tensor, Tuple[torch.Tensor, torch.Tensor]]:
                如果output_attention为False，返回层输出
                如果output_attention为True，返回(层输出, 注意力权重)
        """
        # 注意力块
        if self.pre_norm:
            # 前置层正规化
            norm_x = self.norm1(x)
            if self.output_attention:
                attn_output, attn_weights = self.attention(norm_x, norm_x, norm_x, mask)
            else:
                attn_output = self.attention(norm_x, norm_x, norm_x, mask)
            x = x + attn_output
        else:
            # 后置层正规化
            if self.output_attention:
                attn_output, attn_weights = self.attention(x, x, x, mask)
            else:
                attn_output = self.attention(x, x, x, mask)
            x = self.norm1(x + attn_output)

        # 前馈网络块
        if self.pre_norm:
            # 前置层正规化
            norm_x = self.norm2(x)
            ff_output = self.feed_forward(norm_x)
            x = x + ff_output
        else:
            # 后置层正规化
            ff_output = self.feed_forward(x)
            x = self.norm2(x + ff_output)

        if self.output_attention:
            return x, attn_weights
        return x


class EnhancedTransformerEncoder(nn.Module):
    """
    增强版Transformer编码器

    包含多个增强版Transformer编码器层和位置编码。
    """

    def __init__(
        self,
        hidden_dim: int,
        num_layers: int,
        num_heads: int,
        ff_dim: int = None,
        dropout: float = 0.1,
        activation: str = 'gelu',
        pos_encoding_type: str = 'sinusoidal',
        use_relative_pos: bool = False,
        max_seq_len: int = 5000,
        output_attention: bool = False,
        pre_norm: bool = False
    ):
        """
        初始化增强版Transformer编码器

        Args:
            hidden_dim (int): 隐藏层维度
            num_layers (int): 编码器层数量
            num_heads (int): 注意力头数量
            ff_dim (int, optional): 前馈网络维度. Defaults to None (使用4*hidden_dim).
            dropout (float, optional): Dropout比率. Defaults to 0.1.
            activation (str, optional): 激活函数. Defaults to 'gelu'.
            pos_encoding_type (str, optional): 位置编码类型. Defaults to 'sinusoidal'.
            use_relative_pos (bool, optional): 是否使用相对位置编码. Defaults to False.
            max_seq_len (int, optional): 最大序列长度. Defaults to 5000.
            output_attention (bool, optional): 是否输出注意力权重. Defaults to False.
            pre_norm (bool, optional): 是否使用前置层正规化. Defaults to False.
        """
        super(EnhancedTransformerEncoder, self).__init__()

        # 位置编码
        self.pos_encoding = EnhancedPositionalEncoding(
            d_model=hidden_dim,
            max_seq_len=max_seq_len,
            dropout=dropout,
            encoding_type=pos_encoding_type
        )

        # 编码器层
        self.layers = nn.ModuleList([
            EnhancedTransformerEncoderLayer(
                hidden_dim=hidden_dim,
                num_heads=num_heads,
                ff_dim=ff_dim,
                dropout=dropout,
                activation=activation,
                use_relative_pos=use_relative_pos,
                max_seq_len=max_seq_len,
                output_attention=output_attention,
                pre_norm=pre_norm
            ) for _ in range(num_layers)
        ])

        # 最终层正规化（如果使用前置层正规化）
        self.final_norm = nn.LayerNorm(hidden_dim) if pre_norm else None

        # 设置
        self.output_attention = output_attention
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers

    def forward(
        self,
        x: torch.Tensor,
        mask: Optional[torch.Tensor] = None
    ) -> Union[torch.Tensor, Tuple[torch.Tensor, List[torch.Tensor]]]:
        """
        前向传播

        Args:
            x (torch.Tensor): 输入张量，形状为 [batch_size, seq_len, hidden_dim]
            mask (Optional[torch.Tensor], optional): 注意力掩码. Defaults to None.

        Returns:
            Union[torch.Tensor, Tuple[torch.Tensor, List[torch.Tensor]]]:
                如果output_attention为False，返回编码器输出
                如果output_attention为True，返回(编码器输出, 所有层的注意力权重)
        """
        # 添加位置编码
        x = self.pos_encoding(x)

        # 收集注意力权重（如果需要）
        attentions = [] if self.output_attention else None

        # 通过所有编码器层
        for layer in self.layers:
            if self.output_attention:
                x, attn = layer(x, mask)
                attentions.append(attn)
            else:
                x = layer(x, mask)

        # 应用最终层正规化（如果使用前置层正规化）
        if self.final_norm is not None:
            x = self.final_norm(x)

        if self.output_attention:
            return x, attentions
        return x