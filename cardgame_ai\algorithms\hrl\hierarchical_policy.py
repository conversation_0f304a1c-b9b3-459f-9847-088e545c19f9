"""
层次化策略模块

实现层次化强化学习中的层次化策略，组合高层和低层策略网络。
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Any, Optional, Union

from cardgame_ai.games.doudizhu.card_group import CardGroupType, CardGroup
from cardgame_ai.algorithms.hrl.high_level_policy import HighLevelPolicy
from cardgame_ai.algorithms.hrl.low_level_policy import LowLevelPolicy


class HierarchicalPolicy(nn.Module):
    """
    层次化策略
    
    组合高层和低层策略网络，提供统一的接口。
    """
    
    def __init__(
        self, 
        state_dim: int, 
        low_level_action_dim: int,
        high_level_hidden_dims: List[int] = [256, 128],
        low_level_hidden_dims: List[int] = [256, 128],
        dropout: float = 0.1
    ):
        """
        初始化层次化策略
        
        Args:
            state_dim: 状态维度
            low_level_action_dim: 低层动作维度（具体牌张组合的数量）
            high_level_hidden_dims: 高层策略网络隐藏层维度列表
            low_level_hidden_dims: 低层策略网络隐藏层维度列表
            dropout: Dropout概率
        """
        super().__init__()
        
        # 创建高层策略网络
        self.high_level_policy = HighLevelPolicy(
            state_dim=state_dim,
            hidden_dims=high_level_hidden_dims,
            dropout=dropout
        )
        
        # 创建低层策略网络
        self.low_level_policy = LowLevelPolicy(
            state_dim=state_dim,
            low_level_action_dim=low_level_action_dim,
            hidden_dims=low_level_hidden_dims,
            dropout=dropout
        )
        
        # 保存参数
        self.state_dim = state_dim
        self.low_level_action_dim = low_level_action_dim
        self.high_level_action_dim = self.high_level_policy.high_level_action_dim
    
    def forward(
        self, 
        state: torch.Tensor
    ) -> Tuple[torch.Tensor, List[torch.Tensor]]:
        """
        前向传播
        
        Args:
            state: 状态张量
            
        Returns:
            高层动作的logits和每个高层动作对应的低层动作logits列表
        """
        # 确保输入形状正确
        if state.dim() == 1:
            state = state.unsqueeze(0)  # 添加批次维度
        
        # 获取高层动作的logits
        high_level_logits = self.high_level_policy(state)
        
        # 对每个可能的高层动作，计算低层动作的logits
        low_level_logits = []
        
        for high_level_action in range(self.high_level_action_dim):
            high_level_action_tensor = torch.full((state.size(0),), high_level_action, dtype=torch.long, device=state.device)
            low_level_logits.append(self.low_level_policy(state, high_level_action_tensor))
        
        return high_level_logits, low_level_logits
    
    def act(
        self, 
        state: torch.Tensor, 
        legal_high_level_actions: Optional[List[int]] = None, 
        legal_low_level_actions: Optional[Dict[int, List[int]]] = None, 
        temperature: float = 1.0
    ) -> Tuple[Tuple[int, int], Tuple[torch.Tensor, torch.Tensor]]:
        """
        根据状态选择动作
        
        Args:
            state: 状态张量
            legal_high_level_actions: 合法的高层动作列表
            legal_low_level_actions: 每个高层动作对应的合法低层动作列表
            temperature: 温度参数，控制探索程度
            
        Returns:
            选择的动作（高层动作和低层动作）和动作概率
        """
        # 确保输入形状正确
        if state.dim() == 1:
            state = state.unsqueeze(0)  # 添加批次维度
        
        # 选择高层动作
        high_level_action, high_level_probs = self.high_level_policy.act(
            state, legal_high_level_actions, temperature
        )
        
        # 获取当前高层动作对应的合法低层动作
        current_legal_low_level_actions = None
        if legal_low_level_actions is not None and high_level_action in legal_low_level_actions:
            current_legal_low_level_actions = legal_low_level_actions[high_level_action]
        
        # 选择低层动作
        low_level_action, low_level_probs = self.low_level_policy.act(
            state, high_level_action, current_legal_low_level_actions, temperature
        )
        
        return (high_level_action, low_level_action), (high_level_probs, low_level_probs)
    
    def get_card_group_from_actions(
        self, 
        high_level_action: int, 
        low_level_action: int, 
        hand_cards: List[Any]
    ) -> CardGroup:
        """
        从高层和低层动作获取对应的牌组
        
        Args:
            high_level_action: 高层动作
            low_level_action: 低层动作
            hand_cards: 手牌列表
            
        Returns:
            对应的牌组
        """
        # 获取牌型
        card_type = self.high_level_policy.get_card_type_from_action(high_level_action)
        
        # 如果是不出牌，返回空牌组
        if card_type == CardGroupType.PASS:
            return CardGroup([])
        
        # 根据低层动作和牌型选择具体的牌张组合
        # 注意：这里需要根据具体的低层动作编码方式进行实现
        # 这里只是一个示例，实际实现可能需要更复杂的逻辑
        
        # 假设低层动作直接对应手牌的索引组合
        # 实际实现中，可能需要更复杂的映射关系
        selected_cards = []
        
        # 这里需要根据具体的低层动作编码方式进行实现
        # 例如，可以使用预先计算的所有可能的牌组合，然后根据低层动作选择对应的组合
        
        # 创建牌组
        return CardGroup(selected_cards, card_type)
    
    def save(self, path: str) -> None:
        """
        保存模型
        
        Args:
            path: 保存路径
        """
        torch.save({
            "high_level_policy": self.high_level_policy.state_dict(),
            "low_level_policy": self.low_level_policy.state_dict()
        }, path)
    
    def load(self, path: str) -> None:
        """
        加载模型
        
        Args:
            path: 加载路径
        """
        checkpoint = torch.load(path)
        self.high_level_policy.load_state_dict(checkpoint["high_level_policy"])
        self.low_level_policy.load_state_dict(checkpoint["low_level_policy"])
