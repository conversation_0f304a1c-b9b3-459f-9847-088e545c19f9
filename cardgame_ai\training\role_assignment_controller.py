"""
角色分配控制模块

实现角色分配控制机制，确保在训练过程中每个模型都能获得足够的专属角色训练数据，
解决训练数据不平衡问题。
"""
import numpy as np
import logging
from typing import Dict, Any, List, Optional, Tuple

from cardgame_ai.core.environment import Environment
from cardgame_ai.games.doudizhu.state import GamePhase
from cardgame_ai.games.doudizhu.action import BidAction


class RoleAssignmentController:
    """
    角色分配控制器

    控制游戏中的角色分配，确保训练数据平衡。
    """

    def __init__(self, env: Environment, control_episodes: int = 1000, target_distribution: Optional[Dict[str, float]] = None):
        """
        初始化角色分配控制器

        Args:
            env: 游戏环境
            control_episodes: 强制控制角色分配的回合数
            target_distribution: 目标角色分布，默认为均匀分布
        """
        self.env = env
        self.control_episodes = control_episodes

        # 角色计数
        self.role_counts = {"landlord": 0, "farmer1": 0, "farmer2": 0}

        # 目标分布
        if target_distribution is None:
            self.target_distribution = {"landlord": 1/3, "farmer1": 1/3, "farmer2": 1/3}
        else:
            # 归一化目标分布
            total = sum(target_distribution.values())
            self.target_distribution = {role: prob/total for role, prob in target_distribution.items()}

        # 当前回合数
        self.current_episode = 0

    def assign_roles(self, episode: int) -> Any:
        """
        控制角色分配

        Args:
            episode: 当前回合数

        Returns:
            控制后的游戏状态
        """
        self.current_episode = episode

        # 重置环境
        state = self.env.reset()

        # 前control_episodes回合，强制控制角色分配
        if episode < self.control_episodes:
            # 根据当前回合决定谁是地主
            landlord_id = self._determine_landlord_id(episode)

            # 强制设置地主
            state = self._force_landlord(state, landlord_id)
        else:
            # 超过控制期后，根据当前分布动态调整
            current_distribution = self.get_role_distribution()

            # 计算各角色与目标分布的差距
            gaps = {role: self.target_distribution[role] - current_distribution[role]
                   for role in self.target_distribution}

            # 找出差距最大的角色
            max_gap_role = max(gaps, key=gaps.get)

            # 如果差距超过阈值，尝试控制
            if gaps[max_gap_role] > 0.1:  # 10%的阈值
                if max_gap_role == "landlord":
                    landlord_id = 0
                elif max_gap_role == "farmer1":
                    landlord_id = 1
                elif max_gap_role == "farmer2":
                    landlord_id = 2

                # 尝试控制，但不强制
                state = self._try_control_landlord(state, landlord_id)

        return state

    def _determine_landlord_id(self, episode: int) -> int:
        """
        确定地主ID

        Args:
            episode: 当前回合数

        Returns:
            地主ID
        """
        # 简单的轮流策略
        return episode % 3

    def _force_landlord(self, state: Any, landlord_id: int) -> Any:
        """
        强制设置地主

        通过修改叫分历史或直接设置地主来实现。

        Args:
            state: 当前游戏状态
            landlord_id: 目标地主ID

        Returns:
            修改后的游戏状态
        """
        # 这里需要根据具体环境实现修改
        # 以下是一个示例实现，假设环境支持直接设置地主

        # 检查游戏阶段
        if hasattr(state, 'game_phase') and state.game_phase == GamePhase.BIDDING:
            # 在叫地主阶段，修改叫分历史
            # 让目标地主叫3分，其他人不叫

            # 创建新的叫分历史 - 确保所有玩家都有机会叫分
            new_bid_history = []

            # 从当前玩家开始
            current_player = state.current_player

            # 记录原始玩家顺序，用于调试
            player_order = [(current_player + i) % 3 for i in range(3)]
            logging.debug(f"角色分配控制: 玩家顺序={player_order}, 目标地主={landlord_id}")

            # 按顺序叫分 - 确保所有三个玩家都参与
            for i in range(3):
                player_id = (current_player + i) % 3
                if player_id == landlord_id:
                    # 目标地主叫3分
                    new_bid_history.append((player_id, 3))
                else:
                    # 其他人不叫
                    new_bid_history.append((player_id, 0))

            # 更新状态 - 使用正常的游戏流程而不是直接修改
            # 确保每个玩家都有机会叫分，但目标地主叫3分，其他人不叫
            # 这样可以保持游戏逻辑的完整性
            state.bid_history = new_bid_history
            state.highest_bidder = landlord_id
            state.highest_bid = 3

            # 确保所有玩家都已经叫过分后再进入下一阶段
            players_who_bid = set(player for player, _ in new_bid_history)
            if len(players_who_bid) >= 3:  # 所有玩家都已操作
                # 进入出牌阶段
                if hasattr(state, 'game_phase'):
                    state.game_phase = GamePhase.PLAYING

                # 设置地主
                state.landlord = landlord_id

                # 分配底牌
                if hasattr(state, 'hands') and hasattr(state, 'landlord_cards'):
                    state.hands[landlord_id].extend(state.landlord_cards)
                    state.landlord_cards = []

        return state

    def _try_control_landlord(self, state: Any, landlord_id: int) -> Any:
        """
        尝试控制地主，但不强制

        通过修改叫分概率来影响地主选择。

        Args:
            state: 当前游戏状态
            landlord_id: 目标地主ID

        Returns:
            可能修改后的游戏状态
        """
        # 这里可以实现一些软控制策略
        # 例如，修改叫分概率，但不直接设置结果

        return state

    def update_counts(self, landlord_id: int) -> None:
        """
        更新角色计数

        Args:
            landlord_id: 地主ID
        """
        self.role_counts["landlord"] += 1 if landlord_id == 0 else 0
        self.role_counts["farmer1"] += 1 if landlord_id == 1 else 0
        self.role_counts["farmer2"] += 1 if landlord_id == 2 else 0

    def get_role_distribution(self) -> Dict[str, float]:
        """
        获取角色分布

        Returns:
            角色分布字典，键为角色名称，值为比例
        """
        total = sum(self.role_counts.values())
        if total == 0:
            return {role: 0.0 for role in self.role_counts}

        return {role: count/total for role, count in self.role_counts.items()}

    def get_role_counts(self) -> Dict[str, int]:
        """
        获取角色计数

        Returns:
            角色计数字典，键为角色名称，值为计数
        """
        return self.role_counts.copy()

    def reset_counts(self) -> None:
        """重置角色计数"""
        self.role_counts = {"landlord": 0, "farmer1": 0, "farmer2": 0}

    def get_imbalance_score(self) -> float:
        """
        计算不平衡分数

        返回当前分布与目标分布的差异程度。

        Returns:
            不平衡分数，0表示完全平衡，越大表示越不平衡
        """
        current_distribution = self.get_role_distribution()

        # 计算与目标分布的欧氏距离
        score = 0.0
        for role in self.target_distribution:
            score += (self.target_distribution[role] - current_distribution.get(role, 0.0)) ** 2

        return score ** 0.5
