#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
分布式Actor Worker模块

提供高效的分布式Actor Worker实现，支持大规模并行自对弈和经验收集。
使用Ray作为分布式计算框架，实现高效的数据传输和共享。
"""

import os
import time
import logging
import numpy as np
import random
import torch
from typing import Dict, List, Tuple, Any, Optional, Union
import ray

from cardgame_ai.core.base import State, Action, Experience
from cardgame_ai.core.environment import Environment
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.algorithms.efficient_zero import EfficientZeroModel
from cardgame_ai.algorithms.mcts import MCTS

# 设置日志
logger = logging.getLogger(__name__)


@ray.remote
class SelfPlayActor:
    """
    自对弈Actor

    作为Ray Actor运行，执行自对弈并生成经验数据。
    支持模型权重更新和经验数据收集。
    """

    def __init__(
        self,
        model_config: Dict[str, Any],
        mcts_config: Dict[str, Any],
        env_config: Dict[str, Any] = None,
        actor_id: int = 0,
        device: str = "cpu",
        seed: Optional[int] = None
    ):
        """
        初始化自对弈Actor

        Args:
            model_config: 模型配置
            mcts_config: MCTS配置
            env_config: 环境配置
            actor_id: Actor ID
            device: 计算设备
            seed: 随机种子
        """
        self.actor_id = actor_id
        self.device = device

        # 设置随机种子
        if seed is not None:
            np.random.seed(seed + actor_id)
            torch.manual_seed(seed + actor_id)
            random.seed(seed + actor_id)

        # 创建环境
        self.env_config = env_config or {}
        self.env = self._create_environment()

        # 创建模型
        self.model_config = model_config
        self.model = self._create_model()

        # 创建MCTS
        self.mcts_config = mcts_config
        self.mcts = self._create_mcts()

        # 统计信息
        self.stats = {
            "episodes_played": 0,
            "steps_played": 0,
            "experiences_collected": 0,
            "avg_episode_length": 0,
            "avg_episode_reward": 0,
            "win_rate": 0,
            "start_time": time.time(),
            "last_update_time": time.time()
        }

    def _create_environment(self) -> Environment:
        """
        创建环境

        Returns:
            Environment: 环境
        """
        return DouDizhuEnvironment(seed=self.env_config.get("seed", None))

    def _create_model(self) -> EfficientZeroModel:
        """
        创建模型

        Returns:
            EfficientZeroModel: 模型
        """
        # 获取观察和动作空间
        observation_shape = self.env.observation_shape
        action_shape = self.env.action_shape

        # 创建模型
        model = EfficientZeroModel(
            observation_shape=observation_shape,
            action_shape=action_shape,
            hidden_dim=self.model_config.get("hidden_dim", 256),
            state_dim=self.model_config.get("state_dim", 64),
            use_resnet=self.model_config.get("use_resnet", True),
            projection_dim=self.model_config.get("projection_dim", 256),
            prediction_dim=self.model_config.get("prediction_dim", 128),
            value_prefix_length=self.model_config.get("value_prefix_length", 5),
            device=self.device
        )

        # 设置为评估模式
        model.eval()

        return model

    def _create_mcts(self) -> MCTS:
        """
        创建MCTS

        Returns:
            MCTS: MCTS
        """
        return MCTS(
            num_simulations=self.mcts_config.get("num_simulations", 50),
            discount=self.mcts_config.get("discount", 0.997),
            dirichlet_alpha=self.mcts_config.get("dirichlet_alpha", 0.3),
            exploration_fraction=self.mcts_config.get("exploration_fraction", 0.25)
        )

    def update_weights(self, weights: Dict[str, torch.Tensor]) -> None:
        """
        更新模型权重

        Args:
            weights: 模型权重
        """
        # 加载权重
        self.model.load_state_dict(weights)

        # 更新统计信息
        self.stats["last_update_time"] = time.time()

    def play_episode(
        self,
        temperature: float = 1.0,
        temperature_drop_step: Optional[int] = None,
        add_exploration_noise: bool = True
    ) -> Tuple[List[Experience], float, int, bool]:
        """
        进行一局自对弈

        Args:
            temperature: 温度参数
            temperature_drop_step: 温度下降步数，如果为None则不下降
            add_exploration_noise: 是否添加探索噪声

        Returns:
            Tuple[List[Experience], float, int, bool]: 经验列表、奖励、步数、是否获胜
        """
        # 重置环境
        state = self.env.reset()
        done = False
        step_count = 0
        experiences = []

        # 游戏循环
        while not done:
            # 获取当前玩家
            current_player = state.current_player

            # 获取合法动作
            legal_actions = self.env.get_legal_actions(state)

            # 如果温度下降，则在指定步数后降低温度
            current_temperature = temperature
            if temperature_drop_step is not None and step_count >= temperature_drop_step:
                current_temperature = 0.1

            # 使用MCTS选择动作
            root = self.mcts.run(
                state,
                self.model,
                legal_actions=legal_actions,
                add_exploration_noise=add_exploration_noise,
                temperature=current_temperature
            )

            # 获取访问计数和策略
            visit_counts = root.children_visit_counts
            policy = self.mcts.get_policy(root, temperature=current_temperature)

            # 选择动作
            action_idx = np.random.choice(len(legal_actions), p=policy)
            action = legal_actions[action_idx]

            # 执行动作
            next_state, reward, done, info = self.env.step(action)

            # 创建经验
            experience = Experience(
                state=state,
                action=action,
                reward=reward,
                next_state=next_state,
                done=done,
                policy=policy,
                value=root.value(),
                player=current_player
            )

            # 添加经验
            experiences.append(experience)

            # 更新状态
            state = next_state
            step_count += 1

        # 更新统计信息
        self.stats["episodes_played"] += 1
        self.stats["steps_played"] += step_count
        self.stats["experiences_collected"] += len(experiences)

        # 计算平均回合长度
        self.stats["avg_episode_length"] = (
            (self.stats["avg_episode_length"] * (self.stats["episodes_played"] - 1) + step_count) /
            self.stats["episodes_played"]
        )

        # 计算平均回合奖励
        final_reward = experiences[-1].reward
        self.stats["avg_episode_reward"] = (
            (self.stats["avg_episode_reward"] * (self.stats["episodes_played"] - 1) + final_reward) /
            self.stats["episodes_played"]
        )

        # 计算胜率
        is_win = final_reward > 0
        self.stats["win_rate"] = (
            (self.stats["win_rate"] * (self.stats["episodes_played"] - 1) + (1 if is_win else 0)) /
            self.stats["episodes_played"]
        )

        return experiences, final_reward, step_count, is_win

    def rollout(
        self,
        num_episodes: int,
        temperature: float = 1.0,
        temperature_drop_step: Optional[int] = None,
        add_exploration_noise: bool = True
    ) -> List[List[Experience]]:
        """
        进行多局自对弈

        Args:
            num_episodes: 回合数
            temperature: 温度参数
            temperature_drop_step: 温度下降步数，如果为None则不下降
            add_exploration_noise: 是否添加探索噪声

        Returns:
            List[List[Experience]]: 经验列表的列表
        """
        all_experiences = []

        for _ in range(num_episodes):
            experiences, _, _, _ = self.play_episode(
                temperature=temperature,
                temperature_drop_step=temperature_drop_step,
                add_exploration_noise=add_exploration_noise
            )
            all_experiences.append(experiences)

        return all_experiences

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        # 计算运行时间
        current_time = time.time()
        run_time = current_time - self.stats["start_time"]
        time_since_last_update = current_time - self.stats["last_update_time"]

        # 添加运行时间信息
        stats = dict(self.stats)
        stats["run_time"] = run_time
        stats["time_since_last_update"] = time_since_last_update

        # 计算每秒经验数
        if run_time > 0:
            stats["experiences_per_second"] = stats["experiences_collected"] / run_time

        # 计算每秒回合数
        if run_time > 0:
            stats["episodes_per_second"] = stats["episodes_played"] / run_time

        return stats
