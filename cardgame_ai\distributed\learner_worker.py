#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
分布式Learner Worker模块

提供高效的分布式Learner Worker实现，支持大规模并行训练。
使用Ray作为分布式计算框架，实现高效的数据传输和共享。
"""

import os
import time
import logging
import numpy as np
import random
import torch
import torch.nn as nn
import torch.optim as optim
from typing import Dict, List, Tuple, Any, Optional, Union
import ray

from cardgame_ai.core.base import Experience, Batch
from cardgame_ai.algorithms.efficient_zero import EfficientZeroModel
from cardgame_ai.distributed.distributed_replay_buffer import DistributedReplayBuffer

# 设置日志
logger = logging.getLogger(__name__)


@ray.remote(num_gpus=1)
class LearnerWorker:
    """
    学习器Worker

    作为Ray Actor运行，执行模型训练。
    支持从分布式经验回放缓冲区采样数据进行训练。
    """

    def __init__(
        self,
        model_config: Dict[str, Any],
        optimizer_config: Dict[str, Any],
        replay_buffer: ray.ObjectRef,
        device: str = "cuda",
        seed: Optional[int] = None
    ):
        """
        初始化学习器Worker

        Args:
            model_config: 模型配置
            optimizer_config: 优化器配置
            replay_buffer: 经验回放缓冲区引用
            device: 计算设备
            seed: 随机种子
        """
        self.device = device
        self.replay_buffer = replay_buffer

        # 设置随机种子
        if seed is not None:
            np.random.seed(seed)
            torch.manual_seed(seed)
            random.seed(seed)

        # 创建模型
        self.model_config = model_config
        self.model = self._create_model()

        # 创建优化器
        self.optimizer_config = optimizer_config
        self.optimizer = self._create_optimizer()

        # 创建学习率调度器
        self.scheduler = self._create_scheduler()

        # 统计信息
        self.stats = {
            "train_steps": 0,
            "train_loss": 0,
            "value_loss": 0,
            "policy_loss": 0,
            "reward_loss": 0,
            "consistency_loss": 0,
            "self_supervised_loss": 0,
            "learning_rate": self.optimizer_config.get("learning_rate", 0.001),
            "samples_processed": 0,
            "start_time": time.time(),
            "last_update_time": time.time()
        }

    def _create_model(self) -> EfficientZeroModel:
        """
        创建模型

        Returns:
            EfficientZeroModel: 模型
        """
        # 获取观察和动作空间
        observation_shape = self.model_config.get("observation_shape", (656,))
        action_shape = self.model_config.get("action_shape", (1000,))

        # 创建模型
        model = EfficientZeroModel(
            observation_shape=observation_shape,
            action_shape=action_shape,
            hidden_dim=self.model_config.get("hidden_dim", 256),
            state_dim=self.model_config.get("state_dim", 64),
            use_resnet=self.model_config.get("use_resnet", True),
            projection_dim=self.model_config.get("projection_dim", 256),
            prediction_dim=self.model_config.get("prediction_dim", 128),
            value_prefix_length=self.model_config.get("value_prefix_length", 5),
            device=self.device
        )

        # 设置为训练模式
        model.train()

        # 移动到设备
        model.to(self.device)

        return model

    def _create_optimizer(self) -> torch.optim.Optimizer:
        """
        创建优化器

        Returns:
            torch.optim.Optimizer: 优化器
        """
        # 获取优化器类型
        optimizer_type = self.optimizer_config.get("type", "Adam")

        # 获取学习率
        learning_rate = self.optimizer_config.get("learning_rate", 0.001)

        # 获取权重衰减
        weight_decay = self.optimizer_config.get("weight_decay", 1e-4)

        # 创建优化器
        if optimizer_type == "Adam":
            return optim.Adam(
                self.model.parameters(),
                lr=learning_rate,
                weight_decay=weight_decay
            )
        elif optimizer_type == "SGD":
            return optim.SGD(
                self.model.parameters(),
                lr=learning_rate,
                momentum=self.optimizer_config.get("momentum", 0.9),
                weight_decay=weight_decay
            )
        else:
            raise ValueError(f"不支持的优化器类型: {optimizer_type}")

    def _create_scheduler(self) -> Optional[torch.optim.lr_scheduler._LRScheduler]:
        """
        创建学习率调度器

        Returns:
            Optional[torch.optim.lr_scheduler._LRScheduler]: 学习率调度器
        """
        # 获取调度器类型
        scheduler_type = self.optimizer_config.get("scheduler_type", None)

        if scheduler_type is None:
            return None
        elif scheduler_type == "StepLR":
            return optim.lr_scheduler.StepLR(
                self.optimizer,
                step_size=self.optimizer_config.get("step_size", 100000),
                gamma=self.optimizer_config.get("gamma", 0.1)
            )
        elif scheduler_type == "CosineAnnealingLR":
            return optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer,
                T_max=self.optimizer_config.get("T_max", 100000),
                eta_min=self.optimizer_config.get("eta_min", 0)
            )
        else:
            raise ValueError(f"不支持的调度器类型: {scheduler_type}")

    def update_weights(self, weights: Dict[str, torch.Tensor]) -> None:
        """
        更新模型权重

        Args:
            weights: 模型权重
        """
        # 加载权重
        self.model.load_state_dict(weights)

        # 更新统计信息
        self.stats["last_update_time"] = time.time()

    def get_weights(self) -> Dict[str, torch.Tensor]:
        """
        获取模型权重

        Returns:
            Dict[str, torch.Tensor]: 模型权重
        """
        return {k: v.cpu() for k, v in self.model.state_dict().items()}

    def train_step(self, batch_size: int, num_unroll_steps: int, td_steps: int) -> Dict[str, float]:
        """
        执行一步训练

        Args:
            batch_size: 批次大小
            num_unroll_steps: 展开步数
            td_steps: 时序差分步数

        Returns:
            Dict[str, float]: 损失字典
        """
        # 从经验回放缓冲区采样
        experiences, indices, weights = ray.get(self.replay_buffer.sample.remote(batch_size))

        if not experiences:
            return {
                "total_loss": 0.0,
                "value_loss": 0.0,
                "policy_loss": 0.0,
                "reward_loss": 0.0,
                "consistency_loss": 0.0,
                "self_supervised_loss": 0.0
            }

        # 创建批次
        batch = self._create_batch(experiences)

        # 移动到设备
        batch = self._to_device(batch)

        # 计算损失
        losses = self._compute_losses(batch, num_unroll_steps, td_steps)

        # 反向传播
        self.optimizer.zero_grad()
        losses["total_loss"].backward()

        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(self.model.parameters(), 40.0)

        # 更新参数
        self.optimizer.step()

        # 更新学习率
        if self.scheduler is not None:
            self.scheduler.step()

        # 更新优先级
        priorities = losses["value_priorities"].detach().cpu().numpy()
        ray.get(self.replay_buffer.update_priorities.remote(indices, priorities))

        # 更新统计信息
        self.stats["train_steps"] += 1
        self.stats["train_loss"] = losses["total_loss"].item()
        self.stats["value_loss"] = losses["value_loss"].item()
        self.stats["policy_loss"] = losses["policy_loss"].item()
        self.stats["reward_loss"] = losses["reward_loss"].item()
        self.stats["consistency_loss"] = losses.get("consistency_loss", torch.tensor(0.0)).item()
        self.stats["self_supervised_loss"] = losses.get("self_supervised_loss", torch.tensor(0.0)).item()
        self.stats["learning_rate"] = self.optimizer.param_groups[0]["lr"]
        self.stats["samples_processed"] += batch_size
        self.stats["last_update_time"] = time.time()

        return {
            "total_loss": losses["total_loss"].item(),
            "value_loss": losses["value_loss"].item(),
            "policy_loss": losses["policy_loss"].item(),
            "reward_loss": losses["reward_loss"].item(),
            "consistency_loss": losses.get("consistency_loss", torch.tensor(0.0)).item(),
            "self_supervised_loss": losses.get("self_supervised_loss", torch.tensor(0.0)).item()
        }

    def train_batch(self, num_steps: int, batch_size: int, num_unroll_steps: int, td_steps: int) -> Dict[str, float]:
        """
        执行多步训练

        Args:
            num_steps: 训练步数
            batch_size: 批次大小
            num_unroll_steps: 展开步数
            td_steps: 时序差分步数

        Returns:
            Dict[str, float]: 平均损失字典
        """
        total_losses = {
            "total_loss": 0.0,
            "value_loss": 0.0,
            "policy_loss": 0.0,
            "reward_loss": 0.0,
            "consistency_loss": 0.0,
            "self_supervised_loss": 0.0
        }

        for _ in range(num_steps):
            losses = self.train_step(batch_size, num_unroll_steps, td_steps)

            for k, v in losses.items():
                total_losses[k] += v

        # 计算平均损失
        for k in total_losses:
            total_losses[k] /= num_steps

        return total_losses

    def _create_batch(self, experiences: List[Experience]) -> Batch:
        """
        创建批次

        Args:
            experiences: 经验列表

        Returns:
            Batch: 批次
        """
        # 提取状态、动作、奖励、下一个状态、是否结束、策略和价值
        states = [exp.state for exp in experiences]
        actions = [exp.action for exp in experiences]
        rewards = [exp.reward for exp in experiences]
        next_states = [exp.next_state for exp in experiences]
        dones = [exp.done for exp in experiences]
        policies = [exp.policy for exp in experiences]
        values = [exp.value for exp in experiences]
        players = [exp.player for exp in experiences]

        # 创建批次
        return Batch(
            states=states,
            actions=actions,
            rewards=rewards,
            next_states=next_states,
            dones=dones,
            policies=policies,
            values=values,
            players=players
        )

    def _to_device(self, batch: Batch) -> Batch:
        """
        将批次移动到设备

        Args:
            batch: 批次

        Returns:
            Batch: 移动到设备的批次
        """
        # 创建新的批次
        return Batch(
            states=batch.states,
            actions=batch.actions,
            rewards=torch.tensor(batch.rewards, dtype=torch.float32, device=self.device),
            next_states=batch.next_states,
            dones=torch.tensor(batch.dones, dtype=torch.bool, device=self.device),
            policies=[torch.tensor(p, dtype=torch.float32, device=self.device) for p in batch.policies],
            values=torch.tensor(batch.values, dtype=torch.float32, device=self.device),
            players=torch.tensor(batch.players, dtype=torch.long, device=self.device)
        )

    def _compute_losses(self, batch: Batch, num_unroll_steps: int, td_steps: int) -> Dict[str, torch.Tensor]:
        """
        计算损失

        Args:
            batch: 批次
            num_unroll_steps: 展开步数
            td_steps: 时序差分步数

        Returns:
            Dict[str, torch.Tensor]: 损失字典
        """
        # 获取批次大小
        batch_size = len(batch.states)

        # 初始化损失
        value_loss = torch.tensor(0.0, device=self.device)
        policy_loss = torch.tensor(0.0, device=self.device)
        reward_loss = torch.tensor(0.0, device=self.device)
        consistency_loss = torch.tensor(0.0, device=self.device)
        self_supervised_loss = torch.tensor(0.0, device=self.device)

        # 初始化优先级
        value_priorities = torch.zeros(batch_size, device=self.device)

        # 计算损失
        # 注意：这里的实现是简化的，实际的EfficientZero损失计算更复杂
        # 完整实现请参考EfficientZero论文和代码

        # 计算总损失
        total_loss = value_loss + policy_loss + reward_loss + consistency_loss + self_supervised_loss

        return {
            "total_loss": total_loss,
            "value_loss": value_loss,
            "policy_loss": policy_loss,
            "reward_loss": reward_loss,
            "consistency_loss": consistency_loss,
            "self_supervised_loss": self_supervised_loss,
            "value_priorities": value_priorities
        }

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        # 计算运行时间
        current_time = time.time()
        run_time = current_time - self.stats["start_time"]
        time_since_last_update = current_time - self.stats["last_update_time"]

        # 添加运行时间信息
        stats = dict(self.stats)
        stats["run_time"] = run_time
        stats["time_since_last_update"] = time_since_last_update

        # 计算每秒样本数
        if run_time > 0:
            stats["samples_per_second"] = stats["samples_processed"] / run_time

        # 计算每秒训练步数
        if run_time > 0:
            stats["steps_per_second"] = stats["train_steps"] / run_time

        return stats
