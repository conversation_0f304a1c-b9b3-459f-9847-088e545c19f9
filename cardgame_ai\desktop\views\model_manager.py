#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
模型管理组件

管理已训练的模型，包括保存、加载、删除等功能。
"""

import os
import json
import shutil
import logging
import datetime
from typing import Dict, List, Any, Optional, Tuple

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QListWidget, QListWidgetItem, QGroupBox, QFormLayout,
    QLineEdit, QTextEdit, QFileDialog, QMessageBox,
    QMenu, QInputDialog, QSplitter, QFrame
)
from PySide6.QtCore import Qt, Signal, Slot, QSize
from PySide6.QtGui import QFont, QIcon, QAction

logger = logging.getLogger(__name__)


class ModelManager(QWidget):
    """模型管理组件类"""

    # 模型操作信号
    model_loaded = Signal(str, Dict[str, Any])  # 模型加载信号，发送模型路径和模型信息
    model_saved = Signal(str, Dict[str, Any])   # 模型保存信号，发送模型路径和模型信息
    model_deleted = Signal(str)                 # 模型删除信号，发送模型路径

    def __init__(self, config, parent=None):
        """
        初始化模型管理组件

        Args:
            config: 客户端配置
            parent: 父部件
        """
        super().__init__(parent)

        # 保存配置
        self.config = config

        # 设置对象名称
        self.setObjectName("modelManager")

        # 模型列表
        self.models = []

        # 当前选中的模型
        self.current_model = None

        # 初始化UI
        self.setup_ui()

        # 加载模型列表
        self.load_models()

        logger.info("模型管理组件初始化完成")

    def setup_ui(self):
        """设置UI布局"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(10)

        # 创建标题标签
        title_label = QLabel("模型管理")
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)

        # 创建分割器
        splitter = QSplitter(Qt.Vertical)

        # 创建模型列表区域
        model_list_group = QGroupBox("模型列表")
        model_list_layout = QVBoxLayout(model_list_group)

        # 创建模型列表
        self.model_list = QListWidget()
        self.model_list.setMinimumHeight(200)
        self.model_list.itemClicked.connect(self.on_model_selected)
        self.model_list.setContextMenuPolicy(Qt.CustomContextMenu)
        self.model_list.customContextMenuRequested.connect(self.show_context_menu)
        model_list_layout.addWidget(self.model_list)

        # 添加模型列表区域到分割器
        splitter.addWidget(model_list_group)

        # 创建模型详情区域
        model_details_group = QGroupBox("模型详情")
        model_details_layout = QFormLayout(model_details_group)

        # 创建模型详情标签
        self.model_name_label = QLabel("")
        model_details_layout.addRow("模型名称:", self.model_name_label)

        self.model_type_label = QLabel("")
        model_details_layout.addRow("模型类型:", self.model_type_label)

        self.model_game_label = QLabel("")
        model_details_layout.addRow("游戏类型:", self.model_game_label)

        self.model_version_label = QLabel("")
        model_details_layout.addRow("版本:", self.model_version_label)

        self.model_created_label = QLabel("")
        model_details_layout.addRow("创建时间:", self.model_created_label)

        self.model_path_label = QLabel("")
        self.model_path_label.setWordWrap(True)
        model_details_layout.addRow("路径:", self.model_path_label)

        self.model_description_label = QLabel("")
        self.model_description_label.setWordWrap(True)
        model_details_layout.addRow("描述:", self.model_description_label)

        # 添加模型详情区域到分割器
        splitter.addWidget(model_details_group)

        # 添加分割器到主布局
        main_layout.addWidget(splitter)

        # 创建按钮区域
        button_layout = QHBoxLayout()

        # 创建保存按钮
        self.save_button = QPushButton("保存模型")
        self.save_button.clicked.connect(self.save_model)
        button_layout.addWidget(self.save_button)

        # 创建加载按钮
        self.load_button = QPushButton("加载模型")
        self.load_button.clicked.connect(self.load_model)
        button_layout.addWidget(self.load_button)

        # 创建删除按钮
        self.delete_button = QPushButton("删除模型")
        self.delete_button.clicked.connect(self.delete_model)
        self.delete_button.setEnabled(False)  # 初始时禁用删除按钮
        button_layout.addWidget(self.delete_button)

        # 创建导出按钮
        self.export_button = QPushButton("导出模型")
        self.export_button.clicked.connect(self.export_model)
        self.export_button.setEnabled(False)  # 初始时禁用导出按钮
        button_layout.addWidget(self.export_button)

        # 添加按钮区域到主布局
        main_layout.addLayout(button_layout)

        logger.info("模型管理组件UI布局设置完成")

    def load_models(self):
        """加载模型列表"""
        try:
            # 获取模型目录
            models_dir = self.config.get("paths.models", "models")

            # 确保目录存在
            os.makedirs(models_dir, exist_ok=True)

            # 清空模型列表
            self.models = []
            self.model_list.clear()

            # 遍历模型目录
            for model_name in os.listdir(models_dir):
                model_path = os.path.join(models_dir, model_name)

                # 检查是否为目录
                if os.path.isdir(model_path):
                    # 检查模型信息文件是否存在
                    info_file = os.path.join(model_path, "info.json")
                    if os.path.exists(info_file):
                        try:
                            # 加载模型信息
                            with open(info_file, "r", encoding="utf-8") as f:
                                model_info = json.load(f)

                            # 添加模型路径
                            model_info["path"] = model_path

                            # 添加到模型列表
                            self.models.append(model_info)

                            # 创建列表项
                            item = QListWidgetItem(model_info.get("name", model_name))
                            item.setData(Qt.UserRole, model_info)
                            self.model_list.addItem(item)
                        except Exception as e:
                            logger.error(f"加载模型信息失败：{e}")

            # 更新UI
            self.update_ui()

            logger.info(f"加载模型列表成功，共{len(self.models)}个模型")
        except Exception as e:
            logger.error(f"加载模型列表失败：{e}")

    def update_ui(self):
        """更新UI"""
        # 检查是否有选中的模型
        if self.current_model:
            # 启用删除和导出按钮
            self.delete_button.setEnabled(True)
            self.export_button.setEnabled(True)
        else:
            # 禁用删除和导出按钮
            self.delete_button.setEnabled(False)
            self.export_button.setEnabled(False)

            # 清空模型详情
            self.clear_model_details()

    def clear_model_details(self):
        """清空模型详情"""
        self.model_name_label.setText("")
        self.model_type_label.setText("")
        self.model_game_label.setText("")
        self.model_version_label.setText("")
        self.model_created_label.setText("")
        self.model_path_label.setText("")
        self.model_description_label.setText("")

    def on_model_selected(self, item):
        """
        模型选择处理

        Args:
            item: 列表项
        """
        # 获取模型信息
        model_info = item.data(Qt.UserRole)

        # 保存当前选中的模型
        self.current_model = model_info

        # 更新模型详情
        self.update_model_details(model_info)

        # 更新UI
        self.update_ui()

        logger.info(f"选择模型：{model_info.get('name', '')}")

    def update_model_details(self, model_info):
        """
        更新模型详情

        Args:
            model_info: 模型信息
        """
        # 更新模型详情标签
        self.model_name_label.setText(model_info.get("name", ""))
        self.model_type_label.setText(model_info.get("model_type", ""))
        self.model_game_label.setText(model_info.get("game_type", ""))
        self.model_version_label.setText(model_info.get("version", ""))

        # 格式化创建时间
        created_at = model_info.get("created_at", 0)
        if created_at:
            created_time = datetime.datetime.fromtimestamp(created_at)
            self.model_created_label.setText(created_time.strftime("%Y-%m-%d %H:%M:%S"))
        else:
            self.model_created_label.setText("")

        self.model_path_label.setText(model_info.get("path", ""))
        self.model_description_label.setText(model_info.get("description", ""))

    def save_model(self):
        """保存模型"""
        try:
            # 获取模型目录
            models_dir = self.config.get("paths.models", "models")

            # 确保目录存在
            os.makedirs(models_dir, exist_ok=True)

            # 获取模型信息
            model_name, ok = QInputDialog.getText(
                self,
                "保存模型",
                "请输入模型名称:",
                QLineEdit.Normal,
                ""
            )

            if not ok or not model_name:
                return

            # 获取模型类型
            model_type, ok = QInputDialog.getText(
                self,
                "保存模型",
                "请输入模型类型:",
                QLineEdit.Normal,
                "DQN"
            )

            if not ok:
                return

            # 获取游戏类型
            game_type, ok = QInputDialog.getText(
                self,
                "保存模型",
                "请输入游戏类型:",
                QLineEdit.Normal,
                "斗地主"
            )

            if not ok:
                return

            # 获取版本
            version, ok = QInputDialog.getText(
                self,
                "保存模型",
                "请输入版本:",
                QLineEdit.Normal,
                "1.0.0"
            )

            if not ok:
                return

            # 获取描述
            description, ok = QInputDialog.getText(
                self,
                "保存模型",
                "请输入描述:",
                QLineEdit.Normal,
                ""
            )

            if not ok:
                return

            # 创建模型目录
            model_dir = os.path.join(models_dir, model_name)
            os.makedirs(model_dir, exist_ok=True)

            # 创建模型信息
            model_info = {
                "name": model_name,
                "model_type": model_type,
                "game_type": game_type,
                "version": version,
                "description": description,
                "created_at": int(datetime.datetime.now().timestamp())
            }

            # 保存模型信息
            info_file = os.path.join(model_dir, "info.json")
            with open(info_file, "w", encoding="utf-8") as f:
                json.dump(model_info, f, indent=4, ensure_ascii=False)

            # 保存模型文件
            # 这里只是创建一个空的模型文件，实际应用中应该保存真正的模型
            model_file = os.path.join(model_dir, "model.pt")
            with open(model_file, "w", encoding="utf-8") as f:
                f.write("")

            # 添加模型路径
            model_info["path"] = model_dir

            # 发送模型保存信号
            self.model_saved.emit(model_dir, model_info)

            # 重新加载模型列表
            self.load_models()

            # 显示成功消息
            QMessageBox.information(self, "保存成功", f"模型已保存到：{model_dir}")

            logger.info(f"保存模型成功：{model_name}")
        except Exception as e:
            logger.error(f"保存模型失败：{e}")

            # 显示错误消息
            QMessageBox.critical(self, "保存失败", f"保存模型失败：{e}")

    def load_model(self):
        """加载模型"""
        try:
            # 获取模型文件
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "加载模型",
                os.path.expanduser("~"),
                "模型文件 (*.pt *.pth *.h5 *.model);;所有文件 (*.*)"
            )

            if not file_path:
                return

            # 获取模型目录
            models_dir = self.config.get("paths.models", "models")

            # 确保目录存在
            os.makedirs(models_dir, exist_ok=True)

            # 获取模型名称
            model_name, ok = QInputDialog.getText(
                self,
                "加载模型",
                "请输入模型名称:",
                QLineEdit.Normal,
                os.path.basename(os.path.splitext(file_path)[0])
            )

            if not ok or not model_name:
                return

            # 获取模型类型
            model_type, ok = QInputDialog.getText(
                self,
                "加载模型",
                "请输入模型类型:",
                QLineEdit.Normal,
                "DQN"
            )

            if not ok:
                return

            # 获取游戏类型
            game_type, ok = QInputDialog.getText(
                self,
                "加载模型",
                "请输入游戏类型:",
                QLineEdit.Normal,
                "斗地主"
            )

            if not ok:
                return

            # 获取版本
            version, ok = QInputDialog.getText(
                self,
                "加载模型",
                "请输入版本:",
                QLineEdit.Normal,
                "1.0.0"
            )

            if not ok:
                return

            # 获取描述
            description, ok = QInputDialog.getText(
                self,
                "加载模型",
                "请输入描述:",
                QLineEdit.Normal,
                ""
            )

            if not ok:
                return

            # 创建模型目录
            model_dir = os.path.join(models_dir, model_name)
            os.makedirs(model_dir, exist_ok=True)

            # 复制模型文件
            model_file = os.path.join(model_dir, "model.pt")
            shutil.copy(file_path, model_file)

            # 创建模型信息
            model_info = {
                "name": model_name,
                "model_type": model_type,
                "game_type": game_type,
                "version": version,
                "description": description,
                "created_at": int(datetime.datetime.now().timestamp())
            }

            # 保存模型信息
            info_file = os.path.join(model_dir, "info.json")
            with open(info_file, "w", encoding="utf-8") as f:
                json.dump(model_info, f, indent=4, ensure_ascii=False)

            # 添加模型路径
            model_info["path"] = model_dir

            # 发送模型加载信号
            self.model_loaded.emit(model_dir, model_info)

            # 重新加载模型列表
            self.load_models()

            # 显示成功消息
            QMessageBox.information(self, "加载成功", f"模型已加载到：{model_dir}")

            logger.info(f"加载模型成功：{model_name}")
        except Exception as e:
            logger.error(f"加载模型失败：{e}")

            # 显示错误消息
            QMessageBox.critical(self, "加载失败", f"加载模型失败：{e}")

    def delete_model(self):
        """删除模型"""
        # 检查是否有选中的模型
        if not self.current_model:
            return

        # 获取模型信息
        model_info = self.current_model
        model_name = model_info.get("name", "")
        model_path = model_info.get("path", "")

        # 确认对话框
        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除模型\"{model_name}\"吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        try:
            # 删除模型目录
            if os.path.exists(model_path):
                shutil.rmtree(model_path)

            # 发送模型删除信号
            self.model_deleted.emit(model_path)

            # 清除当前选中的模型
            self.current_model = None

            # 重新加载模型列表
            self.load_models()

            # 显示成功消息
            QMessageBox.information(self, "删除成功", f"模型\"{model_name}\"已删除")

            logger.info(f"删除模型成功：{model_name}")
        except Exception as e:
            logger.error(f"删除模型失败：{e}")

            # 显示错误消息
            QMessageBox.critical(self, "删除失败", f"删除模型失败：{e}")

    def export_model(self):
        """导出模型"""
        # 检查是否有选中的模型
        if not self.current_model:
            return

        # 获取模型信息
        model_info = self.current_model
        model_name = model_info.get("name", "")
        model_path = model_info.get("path", "")

        # 获取模型文件
        model_file = os.path.join(model_path, "model.pt")

        # 检查模型文件是否存在
        if not os.path.exists(model_file):
            QMessageBox.critical(self, "导出失败", f"模型文件不存在：{model_file}")
            return

        # 获取保存路径
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "导出模型",
            os.path.join(os.path.expanduser("~"), f"{model_name}.pt"),
            "模型文件 (*.pt *.pth *.h5 *.model);;所有文件 (*.*)"
        )

        if not file_path:
            return

        try:
            # 复制模型文件
            shutil.copy(model_file, file_path)

            # 显示成功消息
            QMessageBox.information(self, "导出成功", f"模型已导出到：{file_path}")

            logger.info(f"导出模型成功：{model_name} -> {file_path}")
        except Exception as e:
            logger.error(f"导出模型失败：{e}")

            # 显示错误消息
            QMessageBox.critical(self, "导出失败", f"导出模型失败：{e}")

    def show_context_menu(self, pos):
        """
        显示上下文菜单

        Args:
            pos: 位置
        """
        # 获取选中的项
        item = self.model_list.itemAt(pos)

        if not item:
            return

        # 获取模型信息
        model_info = item.data(Qt.UserRole)

        # 创建上下文菜单
        menu = QMenu(self)

        # 添加加载动作
        load_action = QAction("加载模型", self)
        load_action.triggered.connect(lambda: self.load_selected_model(model_info))
        menu.addAction(load_action)

        # 添加删除动作
        delete_action = QAction("删除模型", self)
        delete_action.triggered.connect(lambda: self.delete_selected_model(model_info))
        menu.addAction(delete_action)

        # 添加导出动作
        export_action = QAction("导出模型", self)
        export_action.triggered.connect(lambda: self.export_selected_model(model_info))
        menu.addAction(export_action)

        # 显示上下文菜单
        menu.exec_(self.model_list.mapToGlobal(pos))

    def load_selected_model(self, model_info):
        """
        加载选中的模型

        Args:
            model_info: 模型信息
        """
        # 获取模型路径
        model_path = model_info.get("path", "")

        # 发送模型加载信号
        self.model_loaded.emit(model_path, model_info)

        # 显示成功消息
        QMessageBox.information(self, "加载成功", f"模型已加载：{model_info.get('name', '')}")

        logger.info(f"加载模型成功：{model_info.get('name', '')}")

    def delete_selected_model(self, model_info):
        """
        删除选中的模型

        Args:
            model_info: 模型信息
        """
        # 保存当前选中的模型
        self.current_model = model_info

        # 删除模型
        self.delete_model()

    def export_selected_model(self, model_info):
        """
        导出选中的模型

        Args:
            model_info: 模型信息
        """
        # 保存当前选中的模型
        self.current_model = model_info

        # 导出模型
        self.export_model()
