# 技术栈文档

## 概述

本文档详细说明了斗地主AI优化项目使用的技术栈，包括核心依赖、开发工具和部署环境。

## 核心技术栈

### 编程语言
- **Python**: 3.8-3.11
  - 主要开发语言
  - AI/ML生态系统成熟
  - 丰富的第三方库支持

### 深度学习框架
- **PyTorch**: 1.12+
  - 动态计算图
  - 优秀的CUDA集成
  - 研究友好的API设计
  - 强大的分布式训练支持

### 分布式计算
- **Ray**: 2.0+
  - 原生Python支持
  - 易于扩展的分布式框架
  - 内置的超参数调优
  - 强大的集群管理能力

### GPU计算
- **CUDA**: 11.0+
  - NVIDIA GPU标准
  - 高性能并行计算
  - 深度学习优化
- **cuDNN**: 8.0+
  - 深度神经网络库
  - 优化的卷积操作
  - 内存效率优化

## 核心依赖库

### 科学计算
```python
# requirements.txt 核心依赖
torch>=1.12.0
torchvision>=0.13.0
ray[default]>=2.0.0
numpy>=1.21.0
scipy>=1.8.0
pandas>=1.4.0
```

### AI/ML专用库
```python
# AI算法相关
gymnasium>=0.26.0          # 强化学习环境
stable-baselines3>=1.6.0   # 强化学习算法
transformers>=4.20.0       # Transformer模型
tensorboard>=2.8.0         # 训练监控
wandb>=0.13.0              # 实验跟踪(可选)
```

### 配置和工具
```python
# 配置管理
hydra-core>=1.2.0          # 配置管理框架
omegaconf>=2.2.0           # 配置对象

# 日志和监控
structlog>=22.1.0          # 结构化日志
rich>=12.0.0               # 终端美化
tqdm>=4.64.0               # 进度条

# 数据处理
h5py>=3.7.0                # HDF5数据格式
pillow>=9.0.0              # 图像处理
opencv-python>=4.6.0       # 计算机视觉
```

### 开发工具
```python
# 代码质量
black>=22.0.0              # 代码格式化
flake8>=4.0.0              # 代码检查
mypy>=0.950                # 类型检查
isort>=5.10.0              # 导入排序
pre-commit>=2.19.0         # Git钩子

# 测试框架
pytest>=7.0.0              # 测试框架
pytest-cov>=3.0.0          # 覆盖率测试
pytest-mock>=3.7.0         # Mock测试
pytest-asyncio>=0.18.0     # 异步测试
pytest-xdist>=2.5.0        # 并行测试
```

## 开发环境配置

### Conda环境
```yaml
# environment.yml
name: cardgame_ai
channels:
  - pytorch
  - nvidia
  - conda-forge
  - defaults
dependencies:
  - python=3.10
  - pytorch>=1.12.0
  - torchvision
  - torchaudio
  - pytorch-cuda=11.7
  - cudatoolkit=11.7
  - pip
  - pip:
    - ray[default]>=2.0.0
    - hydra-core>=1.2.0
    - structlog>=22.1.0
    - pytest>=7.0.0
    - black>=22.0.0
    - flake8>=4.0.0
    - mypy>=0.950
```

### 项目配置文件

#### pyproject.toml
```toml
[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "cardgame-ai"
version = "1.0.0"
description = "斗地主AI优化项目"
authors = [{name = "AI Team", email = "<EMAIL>"}]
license = {text = "MIT"}
requires-python = ">=3.8"
dependencies = [
    "torch>=1.12.0",
    "ray[default]>=2.0.0",
    "hydra-core>=1.2.0",
    "structlog>=22.1.0",
    "numpy>=1.21.0",
    "pandas>=1.4.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=3.0.0",
    "black>=22.0.0",
    "flake8>=4.0.0",
    "mypy>=0.950",
    "isort>=5.10.0",
    "pre-commit>=2.19.0",
]
monitoring = [
    "tensorboard>=2.8.0",
    "wandb>=0.13.0",
]

[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["cardgame_ai"]

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--cov=cardgame_ai",
    "--cov-report=html",
    "--cov-report=term-missing",
    "--cov-fail-under=80",
    "--strict-markers",
]
markers = [
    "slow: marks tests as slow",
    "integration: marks tests as integration tests",
    "performance: marks tests as performance tests",
    "gpu: marks tests that require GPU",
]

[tool.coverage.run]
source = ["cardgame_ai"]
omit = [
    "*/tests/*",
    "*/test_*.py",
    "setup.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\bProtocol\):",
    "@(abc\.)?abstractmethod",
]
```

#### .pre-commit-config.yaml
```yaml
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: check-merge-conflict
      - id: debug-statements

  - repo: https://github.com/psf/black
    rev: 22.10.0
    hooks:
      - id: black
        language_version: python3

  - repo: https://github.com/pycqa/isort
    rev: 5.11.4
    hooks:
      - id: isort
        args: ["--profile", "black"]

  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        additional_dependencies: [flake8-docstrings]

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v0.991
    hooks:
      - id: mypy
        additional_dependencies: [types-all]
```

## 硬件要求

### 最低配置
- **CPU**: 8核心 Intel/AMD处理器
- **内存**: 32GB RAM
- **GPU**: NVIDIA GTX 1080 Ti (11GB VRAM)
- **存储**: 500GB SSD

### 推荐配置
- **CPU**: 16核心 Intel Xeon/AMD EPYC
- **内存**: 64GB+ RAM
- **GPU**: NVIDIA V100/A100 (32GB+ VRAM)
- **存储**: 2TB+ NVMe SSD
- **网络**: 10Gbps以太网(集群环境)

### 集群配置
- **主节点**: 高性能CPU + 大内存
- **计算节点**: 多GPU + 高速互联
- **存储节点**: 高IOPS存储系统
- **网络**: InfiniBand/高速以太网

## 部署环境

### 容器化
```dockerfile
# Dockerfile
FROM pytorch/pytorch:1.12.1-cuda11.3-cudnn8-devel

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    git \
    wget \
    curl \
    vim \
    htop \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制项目代码
COPY . .

# 设置环境变量
ENV PYTHONPATH=/app
ENV CUDA_VISIBLE_DEVICES=0,1,2,3

# 暴露端口
EXPOSE 8000 6006

# 启动命令
CMD ["python", "scripts/train.py"]
```

### Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  training:
    build: .
    volumes:
      - ./data:/app/data
      - ./models:/app/models
      - ./logs:/app/logs
    environment:
      - CUDA_VISIBLE_DEVICES=0,1,2,3
      - RAY_DISABLE_IMPORT_WARNING=1
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 4
              capabilities: [gpu]

  tensorboard:
    image: tensorflow/tensorflow:latest
    command: tensorboard --logdir=/logs --host=0.0.0.0
    ports:
      - "6006:6006"
    volumes:
      - ./logs:/logs

  monitoring:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
```

## 性能优化

### PyTorch优化
```python
import torch

# 启用优化
torch.backends.cudnn.benchmark = True
torch.backends.cudnn.deterministic = False

# 混合精度训练
from torch.cuda.amp import autocast, GradScaler

scaler = GradScaler()

@autocast()
def forward_pass(model, input_data):
    return model(input_data)
```

### Ray优化
```python
import ray

# Ray集群配置
ray.init(
    address="ray://head-node:10001",
    runtime_env={
        "pip": ["torch>=1.12.0", "ray[default]>=2.0.0"],
        "env_vars": {"CUDA_VISIBLE_DEVICES": "0,1,2,3"}
    }
)
```

## 监控和日志

### TensorBoard集成
```python
from torch.utils.tensorboard import SummaryWriter

writer = SummaryWriter('logs/training')

def log_training_metrics(epoch, loss, accuracy):
    writer.add_scalar('Loss/Train', loss, epoch)
    writer.add_scalar('Accuracy/Train', accuracy, epoch)
```

### Weights & Biases集成
```python
import wandb

wandb.init(
    project="doudizhu-ai-optimization",
    config={
        "learning_rate": 0.001,
        "epochs": 100,
        "batch_size": 32
    }
)

def log_metrics(metrics):
    wandb.log(metrics)
```
