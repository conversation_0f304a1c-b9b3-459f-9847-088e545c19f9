"""
斗地主随机游戏示例

演示如何使用斗地主游戏环境和随机代理进行游戏。
"""
import random
import time
import sys
import os
from typing import List

# 添加项目目录到Python路径
# 获取当前文件的目录路径
# 获取项目根目录路径（当前文件的父目录的父目录）
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cardgame_ai.core.agent import RandomAgent
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.games.doudizhu.card_group import CardGroup


def play_game(env: DouDizhuEnvironment, agents: List[RandomAgent], render: bool = True) -> List[float]:
    """
    进行一局游戏

    Args:
        env (DouDizhuEnvironment): 游戏环境
        agents (List[RandomAgent]): 代理列表
        render (bool, optional): 是否渲染. Defaults to True.

    Returns:
        List[float]: 各玩家的收益
    """
    # 重置环境
    state = env.reset()
    done = False

    if render:
        env.render()
        print("-" * 50)

    # 游戏循环
    while not done:
        # 获取当前玩家
        current_player = state.current_player

        # 获取合法动作
        legal_actions = env.get_legal_actions(state)

        # 获取观察
        observation = env.get_observation(state)

        # 选择动作
        action = agents[current_player].act(observation, legal_actions)

        if render:
            print(f"玩家{current_player}({['农民', '地主'][current_player == state.landlord]})出牌: {action}")

        # 执行动作
        state, reward, done, info = env.step(action)

        if render:
            env.render()
            print("-" * 50)
            time.sleep(0.5)  # 暂停一下，便于观察

    # 获取收益
    payoffs = env.get_payoffs(state)

    if render:
        winner = next(i for i, hand in enumerate(state.hands) if not hand)
        print(f"游戏结束，玩家{winner}({['农民', '地主'][winner == state.landlord]})获胜!")
        print(f"收益: {payoffs}")

    return payoffs


def main():
    """
    主函数
    """
    # 创建环境
    env = DouDizhuEnvironment(seed=42)

    # 创建代理
    agents = [RandomAgent(seed=i) for i in range(3)]

    # 进行游戏
    payoffs = play_game(env, agents)

    # 进行多局游戏统计
    num_games = 100
    landlord_wins = 0
    farmer_wins = 0

    for i in range(num_games):
        payoffs = play_game(env, agents, render=False)
        landlord_idx = env.state.landlord

        if payoffs[landlord_idx] > 0:
            landlord_wins += 1
        else:
            farmer_wins += 1

    print(f"统计结果 ({num_games}局游戏):")
    print(f"地主胜率: {landlord_wins / num_games:.2%}")
    print(f"农民胜率: {farmer_wins / num_games:.2%}")


if __name__ == "__main__":
    main()
