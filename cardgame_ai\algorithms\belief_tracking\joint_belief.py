"""
联合信念分布模块

实现多人博弈中对所有玩家的联合信念分布，捕捉玩家间的信息关系。
"""

from typing import Dict, List, Set, Tuple, Optional, Any, Union
import time
import logging
import numpy as np
import copy

from cardgame_ai.games.common.belief_state import BeliefState, BeliefSource, BeliefStateCollection
from cardgame_ai.games.doudizhu.card import Card, CardRank, CardSuit
from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType, get_card_group_type

# 配置日志
logger = logging.getLogger(__name__)


class JointBeliefState:
    """
    联合信念状态类

    表示多人博弈中对所有玩家手牌的联合概率分布信念。
    捕捉玩家间的信息关系，考虑手牌分布的相互约束。
    """

    def __init__(
        self,
        player_ids: List[str],
        all_cards: List[str],
        known_cards: Dict[str, List[str]] = None,
        initial_hand_sizes: Dict[str, int] = None
    ):
        """
        初始化联合信念状态

        Args:
            player_ids: 所有玩家的ID列表
            all_cards: 所有可能的牌列表
            known_cards: 已知的牌，格式为{player_id: [card1, card2, ...]}
            initial_hand_sizes: 初始手牌数量，格式为{player_id: size}
        """
        self.player_ids = player_ids
        self.all_cards = all_cards
        self.num_players = len(player_ids)
        self.num_cards = len(all_cards)

        # 初始化已知的牌
        self.known_cards = known_cards or {}

        # 初始化手牌数量
        self.hand_sizes = initial_hand_sizes or {}

        # 初始化独立信念状态
        self.individual_beliefs = {}
        for player_id in player_ids:
            # 创建独立信念状态
            excluded_cards = []
            for pid, cards in self.known_cards.items():
                if pid != player_id:  # 排除其他玩家已知的牌
                    excluded_cards.extend(cards)

            self.individual_beliefs[player_id] = BeliefState.create_uniform(
                player_id=player_id,
                all_cards=all_cards,
                excluded_cards=excluded_cards
            )

            # 设置手牌数量
            if player_id in self.hand_sizes:
                self.individual_beliefs[player_id].estimated_hand_length = self.hand_sizes[player_id]

        # 初始化联合概率矩阵
        # 注意：完整的联合概率分布需要非常大的存储空间，这里使用因子化表示
        self.joint_factors = {}
        self.initialize_joint_factors()

        # 元数据
        self.source = BeliefSource.INITIAL
        self.confidence = 0.3  # 初始信念的置信度较低
        self.last_updated = time.time()
        self.version = 0

    def initialize_joint_factors(self):
        """
        初始化联合概率因子

        使用因子化表示联合概率分布，避免存储完整的联合分布矩阵。
        """
        # 初始化玩家对之间的关系因子
        for i, player1 in enumerate(self.player_ids):
            for j in range(i+1, len(self.player_ids)):
                player2 = self.player_ids[j]
                # 创建玩家对之间的关系因子（初始为独立）
                self.joint_factors[(player1, player2)] = np.ones((self.num_cards, self.num_cards)) / self.num_cards**2

                # 应用牌的互斥约束：同一张牌不能同时被两个玩家持有
                for k, card in enumerate(self.all_cards):
                    self.joint_factors[(player1, player2)][k, k] = 0.0

                # 归一化
                factor_sum = np.sum(self.joint_factors[(player1, player2)])
                if factor_sum > 0:
                    self.joint_factors[(player1, player2)] /= factor_sum

    def get_individual_belief(self, player_id: str) -> Optional[BeliefState]:
        """
        获取指定玩家的独立信念状态

        Args:
            player_id: 玩家ID

        Returns:
            Optional[BeliefState]: 独立信念状态，如果不存在则返回None
        """
        return self.individual_beliefs.get(player_id)

    def get_joint_probability(self, card_assignments: Dict[str, str]) -> float:
        """
        获取特定牌分配的联合概率

        Args:
            card_assignments: 牌分配，格式为{player_id: card}

        Returns:
            float: 联合概率
        """
        # 检查分配是否有效（同一张牌不能分配给多个玩家）
        cards = list(card_assignments.values())
        if len(cards) != len(set(cards)):
            return 0.0

        # 计算联合概率
        joint_prob = 1.0

        # 首先考虑独立概率
        for player_id, card in card_assignments.items():
            if player_id in self.individual_beliefs:
                card_prob = self.individual_beliefs[player_id].card_probabilities.get(card, 0.0)
                joint_prob *= card_prob

        # 然后考虑关系因子
        for i, player1 in enumerate(self.player_ids):
            if player1 not in card_assignments:
                continue

            for j in range(i+1, len(self.player_ids)):
                player2 = self.player_ids[j]
                if player2 not in card_assignments:
                    continue

                card1 = card_assignments[player1]
                card2 = card_assignments[player2]

                # 获取卡牌索引
                idx1 = self.all_cards.index(card1)
                idx2 = self.all_cards.index(card2)

                # 应用关系因子
                factor_key = (player1, player2)
                if factor_key in self.joint_factors:
                    factor_value = self.joint_factors[factor_key][idx1, idx2]
                    joint_prob *= factor_value

        return joint_prob

    def update_from_observation(self, observed_cards: Dict[str, List[str]], is_certain: bool = True) -> None:
        """
        根据观察到的牌更新联合信念状态

        Args:
            observed_cards: 观察到的牌，格式为{player_id: [card1, card2, ...]}
            is_certain: 观察是否确定
        """
        # 更新已知的牌
        for player_id, cards in observed_cards.items():
            if player_id not in self.known_cards:
                self.known_cards[player_id] = []

            for card in cards:
                if card not in self.known_cards[player_id]:
                    self.known_cards[player_id].append(card)

        # 更新独立信念状态
        for player_id in self.player_ids:
            # 获取当前玩家的观察
            player_observed = observed_cards.get(player_id, [])

            # 获取其他玩家的观察
            other_observed = []
            for pid, cards in observed_cards.items():
                if pid != player_id:
                    other_observed.extend(cards)

            # 更新独立信念状态
            belief = self.individual_beliefs.get(player_id)
            if belief:
                # 更新手牌数量
                if player_id in observed_cards and belief.estimated_hand_length is not None:
                    belief.estimated_hand_length -= len(observed_cards[player_id])

                # 更新概率分布
                if is_certain:
                    # 如果确定观察到这些牌，则将它们的概率设为0（表示其他玩家不可能有这些牌）
                    for card in other_observed:
                        belief.card_probabilities[card] = 0.0
                else:
                    # 如果不确定，则降低这些牌的概率
                    for card in other_observed:
                        if card in belief.card_probabilities:
                            belief.card_probabilities[card] *= 0.5

                # 重新归一化概率
                belief.normalize_probabilities()

        # 更新联合因子
        self._update_joint_factors()

        # 更新元数据
        self.source = BeliefSource.OBSERVATION
        self.last_updated = time.time()
        self.version += 1

    def update_from_action(self, player_id: str, action: List[str], action_type: Optional[str] = None) -> None:
        """
        根据玩家的动作更新联合信念状态

        Args:
            player_id: 执行动作的玩家ID
            action: 动作中包含的牌列表
            action_type: 动作类型（如"出牌"、"不出"等）
        """
        # 如果是出牌动作
        if action and action_type != "不出":
            # 更新已知的牌
            if player_id not in self.known_cards:
                self.known_cards[player_id] = []

            for card in action:
                if card not in self.known_cards[player_id]:
                    self.known_cards[player_id].append(card)

            # 更新手牌数量
            if player_id in self.hand_sizes:
                self.hand_sizes[player_id] -= len(action)

                # 更新独立信念状态的手牌数量
                belief = self.individual_beliefs.get(player_id)
                if belief:
                    belief.estimated_hand_length = self.hand_sizes[player_id]

            # 更新其他玩家的独立信念状态
            for pid in self.player_ids:
                if pid != player_id:
                    belief = self.individual_beliefs.get(pid)
                    if belief:
                        # 将出牌的概率设为0（表示其他玩家不可能有这些牌）
                        for card in action:
                            belief.card_probabilities[card] = 0.0

                        # 重新归一化概率
                        belief.normalize_probabilities()

        # 如果是"不出"动作，可以进行推理
        elif action_type == "不出":
            # 这里可以添加基于"不出"动作的推理逻辑
            # 例如，如果玩家选择不出牌，可能意味着他没有特定类型的牌
            pass

        # 更新联合因子
        self._update_joint_factors()

        # 更新元数据
        self.source = BeliefSource.INFERENCE
        self.last_updated = time.time()
        self.version += 1

    def _update_joint_factors(self) -> None:
        """
        更新联合概率因子

        基于最新的独立信念状态和约束条件更新联合因子。
        """
        # 更新玩家对之间的关系因子
        for i, player1 in enumerate(self.player_ids):
            for j in range(i+1, len(self.player_ids)):
                player2 = self.player_ids[j]

                # 获取独立信念状态
                belief1 = self.individual_beliefs.get(player1)
                belief2 = self.individual_beliefs.get(player2)

                if not belief1 or not belief2:
                    continue

                # 更新关系因子
                for k, card1 in enumerate(self.all_cards):
                    for l, card2 in enumerate(self.all_cards):
                        # 同一张牌不能同时被两个玩家持有
                        if card1 == card2:
                            self.joint_factors[(player1, player2)][k, l] = 0.0
                            continue

                        # 计算独立概率
                        p1 = belief1.card_probabilities.get(card1, 0.0)
                        p2 = belief2.card_probabilities.get(card2, 0.0)

                        # 更新关系因子（考虑独立概率和约束）
                        self.joint_factors[(player1, player2)][k, l] = p1 * p2

                # 归一化
                factor_sum = np.sum(self.joint_factors[(player1, player2)])
                if factor_sum > 0:
                    self.joint_factors[(player1, player2)] /= factor_sum

    def sample_joint_assignment(self, num_samples: int = 1) -> List[Dict[str, List[str]]]:
        """
        从联合信念分布中采样牌的分配

        Args:
            num_samples: 采样数量

        Returns:
            List[Dict[str, List[str]]]: 采样的牌分配列表，每个元素是一个字典{player_id: [card1, card2, ...]}
        """
        samples = []

        for _ in range(num_samples):
            # 初始化采样结果
            sample = {player_id: [] for player_id in self.player_ids}

            # 创建可用牌的副本
            available_cards = self.all_cards.copy()

            # 为每个玩家分配牌
            for player_id in self.player_ids:
                belief = self.individual_beliefs.get(player_id)
                if not belief or not belief.estimated_hand_length:
                    continue

                # 获取玩家应该分配的牌数
                num_cards = belief.estimated_hand_length

                # 根据信念状态的概率分布采样牌
                for _ in range(min(num_cards, len(available_cards))):
                    # 计算可用牌的概率
                    probs = []
                    for card in available_cards:
                        prob = belief.card_probabilities.get(card, 0.0)
                        probs.append(prob)

                    # 归一化概率
                    probs_sum = sum(probs)
                    if probs_sum > 0:
                        probs = [p / probs_sum for p in probs]
                    else:
                        # 如果所有概率都为0，使用均匀分布
                        probs = [1.0 / len(available_cards)] * len(available_cards)

                    # 采样一张牌
                    card_idx = np.random.choice(len(available_cards), p=probs)
                    card = available_cards[card_idx]

                    # 添加到采样结果
                    sample[player_id].append(card)

                    # 从可用牌中移除
                    available_cards.remove(card)

            samples.append(sample)

        return samples

    def get_most_likely_assignment(self) -> Dict[str, List[str]]:
        """
        获取最可能的牌分配

        Returns:
            Dict[str, List[str]]: 最可能的牌分配，格式为{player_id: [card1, card2, ...]}
        """
        # 使用贪心算法获取最可能的分配
        assignment = {player_id: [] for player_id in self.player_ids}

        # 创建可用牌的副本
        available_cards = self.all_cards.copy()

        # 为每个玩家分配牌
        for player_id in self.player_ids:
            belief = self.individual_beliefs.get(player_id)
            if not belief or not belief.estimated_hand_length:
                continue

            # 获取玩家应该分配的牌数
            num_cards = belief.estimated_hand_length

            # 根据信念状态的概率分布分配牌
            for _ in range(min(num_cards, len(available_cards))):
                # 找出概率最高的牌
                best_card = None
                best_prob = -1

                for card in available_cards:
                    prob = belief.card_probabilities.get(card, 0.0)
                    if prob > best_prob:
                        best_prob = prob
                        best_card = card

                if best_card:
                    # 添加到分配结果
                    assignment[player_id].append(best_card)

                    # 从可用牌中移除
                    available_cards.remove(best_card)

        return assignment

    def to_dict(self) -> Dict:
        """
        将联合信念状态转换为字典表示

        Returns:
            Dict: 联合信念状态的字典表示
        """
        return {
            'player_ids': self.player_ids,
            'all_cards': self.all_cards,
            'known_cards': self.known_cards,
            'hand_sizes': self.hand_sizes,
            'individual_beliefs': {
                player_id: belief.to_dict() for player_id, belief in self.individual_beliefs.items()
            },
            'source': self.source.value,
            'confidence': self.confidence,
            'last_updated': self.last_updated,
            'version': self.version
        }

    @classmethod
    def from_dict(cls, data: Dict) -> 'JointBeliefState':
        """
        从字典创建联合信念状态

        Args:
            data: 联合信念状态的字典表示

        Returns:
            JointBeliefState: 创建的联合信念状态对象
        """
        # 创建基本对象
        joint_belief = cls(
            player_ids=data['player_ids'],
            all_cards=data['all_cards'],
            known_cards=data.get('known_cards', {}),
            initial_hand_sizes=data.get('hand_sizes', {})
        )

        # 设置独立信念状态
        if 'individual_beliefs' in data:
            for player_id, belief_data in data['individual_beliefs'].items():
                joint_belief.individual_beliefs[player_id] = BeliefState.from_dict(belief_data)

        # 设置元数据
        if 'source' in data:
            joint_belief.source = BeliefSource(data['source'])

        if 'confidence' in data:
            joint_belief.confidence = data['confidence']

        if 'last_updated' in data:
            joint_belief.last_updated = data['last_updated']

        if 'version' in data:
            joint_belief.version = data['version']

        # 重新初始化联合因子
        joint_belief.initialize_joint_factors()
        joint_belief._update_joint_factors()

        return joint_belief


class JointBeliefTracker:
    """
    联合信念追踪器

    使用联合信念分布追踪多人博弈中所有玩家的手牌概率分布。
    考虑玩家间的信息关系，捕捉手牌分布的相互约束。
    """

    def __init__(
        self,
        player_ids: List[str],
        all_cards: List[str],
        known_cards: Dict[str, List[str]] = None,
        initial_hand_sizes: Dict[str, int] = None
    ):
        """
        初始化联合信念追踪器

        Args:
            player_ids: 所有玩家的ID列表
            all_cards: 所有可能的牌列表
            known_cards: 已知的牌，格式为{player_id: [card1, card2, ...]}
            initial_hand_sizes: 初始手牌数量，格式为{player_id: size}
        """
        # 创建联合信念状态
        self.joint_belief = JointBeliefState(
            player_ids=player_ids,
            all_cards=all_cards,
            known_cards=known_cards,
            initial_hand_sizes=initial_hand_sizes
        )

        # 记录动作历史
        self.action_history = []

        # 记录更新历史
        self.update_history = []

    def update_from_observation(self, observed_cards: Dict[str, List[str]], is_certain: bool = True) -> None:
        """
        根据观察到的牌更新联合信念状态

        Args:
            observed_cards: 观察到的牌，格式为{player_id: [card1, card2, ...]}
            is_certain: 观察是否确定
        """
        # 更新联合信念状态
        self.joint_belief.update_from_observation(observed_cards, is_certain)

        # 记录更新历史
        update_record = {
            'time': time.time(),
            'type': 'observation',
            'data': observed_cards,
            'is_certain': is_certain
        }
        self.update_history.append(update_record)

    def update_from_action(self, player_id: str, action: List[str], action_type: Optional[str] = None) -> None:
        """
        根据玩家的动作更新联合信念状态

        Args:
            player_id: 执行动作的玩家ID
            action: 动作中包含的牌列表
            action_type: 动作类型（如"出牌"、"不出"等）
        """
        # 更新联合信念状态
        self.joint_belief.update_from_action(player_id, action, action_type)

        # 记录动作历史
        action_record = {
            'time': time.time(),
            'player_id': player_id,
            'action': action,
            'action_type': action_type
        }
        self.action_history.append(action_record)

        # 记录更新历史
        update_record = {
            'time': time.time(),
            'type': 'action',
            'player_id': player_id,
            'action': action,
            'action_type': action_type
        }
        self.update_history.append(update_record)

    def get_individual_belief(self, player_id: str) -> Optional[BeliefState]:
        """
        获取指定玩家的独立信念状态

        Args:
            player_id: 玩家ID

        Returns:
            Optional[BeliefState]: 独立信念状态，如果不存在则返回None
        """
        return self.joint_belief.get_individual_belief(player_id)

    def get_joint_probability(self, card_assignments: Dict[str, str]) -> float:
        """
        获取特定牌分配的联合概率

        Args:
            card_assignments: 牌分配，格式为{player_id: card}

        Returns:
            float: 联合概率
        """
        return self.joint_belief.get_joint_probability(card_assignments)

    def sample_joint_assignment(self, num_samples: int = 1) -> List[Dict[str, List[str]]]:
        """
        从联合信念分布中采样牌的分配

        Args:
            num_samples: 采样数量

        Returns:
            List[Dict[str, List[str]]]: 采样的牌分配列表，每个元素是一个字典{player_id: [card1, card2, ...]}
        """
        return self.joint_belief.sample_joint_assignment(num_samples)

    def get_most_likely_assignment(self) -> Dict[str, List[str]]:
        """
        获取最可能的牌分配

        Returns:
            Dict[str, List[str]]: 最可能的牌分配，格式为{player_id: [card1, card2, ...]}
        """
        return self.joint_belief.get_most_likely_assignment()

    def to_dict(self) -> Dict:
        """
        将联合信念追踪器转换为字典表示

        Returns:
            Dict: 联合信念追踪器的字典表示
        """
        return {
            'joint_belief': self.joint_belief.to_dict(),
            'action_history': self.action_history,
            'update_history': self.update_history
        }

    @classmethod
    def from_dict(cls, data: Dict) -> 'JointBeliefTracker':
        """
        从字典创建联合信念追踪器

        Args:
            data: 联合信念追踪器的字典表示

        Returns:
            JointBeliefTracker: 创建的联合信念追踪器对象
        """
        # 创建联合信念状态
        joint_belief = JointBeliefState.from_dict(data['joint_belief'])

        # 创建追踪器
        tracker = cls(
            player_ids=joint_belief.player_ids,
            all_cards=joint_belief.all_cards,
            known_cards=joint_belief.known_cards,
            initial_hand_sizes=joint_belief.hand_sizes
        )

        # 设置联合信念状态
        tracker.joint_belief = joint_belief

        # 设置历史记录
        if 'action_history' in data:
            tracker.action_history = data['action_history']

        if 'update_history' in data:
            tracker.update_history = data['update_history']

        return tracker
