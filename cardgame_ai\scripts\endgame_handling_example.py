#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
关键残局特化处理示例脚本

展示如何使用关键残局特化处理模块。
"""

import os
import sys
import argparse
import logging
import numpy as np
import torch
from typing import Dict, List, Any, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from cardgame_ai.algorithms.hybrid_decision_system import HybridDecisionSystem
from cardgame_ai.algorithms.key_moment_detector import KeyMomentDetector
from cardgame_ai.algorithms.efficient_zero import EfficientZero
from cardgame_ai.algorithms.rule_based import RuleBasedAgent
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.games.doudizhu.state import DouDizhuState
from cardgame_ai.games.doudizhu.card import Card, CardRank, CardSuit
from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType
from cardgame_ai.algorithms.endgame_modules import (
    is_endgame, get_endgame_type, EndgameType,
    is_king_bomb_scenario, is_single_card_control_scenario
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='关键残局特化处理示例')
    
    parser.add_argument('--model_path', type=str, default=None,
                        help='模型路径')
    parser.add_argument('--key_moment_model', type=str, default=None,
                        help='关键决策点检测器模型路径')
    parser.add_argument('--num_games', type=int, default=5,
                        help='游戏数量')
    parser.add_argument('--seed', type=int, default=None,
                        help='随机种子')
    
    return parser.parse_args()


def create_endgame_state(endgame_type: EndgameType) -> DouDizhuState:
    """
    创建特定类型的残局状态
    
    Args:
        endgame_type: 残局类型
        
    Returns:
        DouDizhuState: 残局状态
    """
    # 创建一个基本的状态
    hands = [[], [], []]
    landlord_cards = []
    
    # 根据残局类型设置手牌
    if endgame_type == EndgameType.KING_BOMB:
        # 玩家0（地主）：大小王 + A
        hands[0] = [
            Card(CardRank.BIG_JOKER, None),
            Card(CardRank.SMALL_JOKER, None),
            Card(CardRank.ACE, CardSuit.SPADE)
        ]
        # 玩家1（农民）：2 + K
        hands[1] = [
            Card(CardRank.TWO, CardSuit.HEART),
            Card(CardRank.KING, CardSuit.DIAMOND)
        ]
        # 玩家2（农民）：单张Q
        hands[2] = [
            Card(CardRank.QUEEN, CardSuit.CLUB)
        ]
        
    elif endgame_type == EndgameType.SINGLE_CARD_CONTROL:
        # 玩家0（地主）：单张3、5、7、9、J
        hands[0] = [
            Card(CardRank.THREE, CardSuit.SPADE),
            Card(CardRank.FIVE, CardSuit.HEART),
            Card(CardRank.SEVEN, CardSuit.DIAMOND),
            Card(CardRank.NINE, CardSuit.CLUB),
            Card(CardRank.JACK, CardSuit.SPADE)
        ]
        # 玩家1（农民）：单张4、6、8、10
        hands[1] = [
            Card(CardRank.FOUR, CardSuit.HEART),
            Card(CardRank.SIX, CardSuit.DIAMOND),
            Card(CardRank.EIGHT, CardSuit.CLUB),
            Card(CardRank.TEN, CardSuit.SPADE)
        ]
        # 玩家2（农民）：单张2
        hands[2] = [
            Card(CardRank.TWO, CardSuit.HEART)
        ]
        
    else:
        # 默认创建一个一般残局
        # 玩家0（地主）：对子8 + 单张K
        hands[0] = [
            Card(CardRank.EIGHT, CardSuit.SPADE),
            Card(CardRank.EIGHT, CardSuit.HEART),
            Card(CardRank.KING, CardSuit.DIAMOND)
        ]
        # 玩家1（农民）：对子9
        hands[1] = [
            Card(CardRank.NINE, CardSuit.CLUB),
            Card(CardRank.NINE, CardSuit.SPADE)
        ]
        # 玩家2（农民）：单张10
        hands[2] = [
            Card(CardRank.TEN, CardSuit.HEART)
        ]
    
    # 创建状态
    state = DouDizhuState(
        hands=hands,
        landlord_cards=landlord_cards,
        landlord=0,  # 玩家0为地主
        current_player=0,  # 当前玩家为玩家0
        game_phase=DouDizhuState.GamePhase.PLAYING,  # 出牌阶段
        bid_history=[],
        grab_history=[],
        highest_bidder=0,
        highest_bid=3,
        last_move=None,
        last_player=None,
        num_passes=0,
        history=[],
        played_cards=[]
    )
    
    return state


def test_endgame_handling(hybrid_system: HybridDecisionSystem):
    """
    测试残局处理
    
    Args:
        hybrid_system: 混合决策系统
    """
    # 测试王炸残局
    logger.info("测试王炸残局处理")
    king_bomb_state = create_endgame_state(EndgameType.KING_BOMB)
    
    # 检查是否是王炸残局
    is_kb_scenario = is_king_bomb_scenario(king_bomb_state)
    logger.info(f"是否是王炸残局场景: {is_kb_scenario}")
    
    # 获取合法动作
    legal_actions = king_bomb_state.get_legal_actions()
    
    # 使用混合决策系统做出决策
    action = hybrid_system.act(king_bomb_state, legal_actions)
    
    # 打印决策结果
    if action:
        logger.info(f"决策动作: {action.card_type.name}")
        if action.cards:
            logger.info(f"决策牌: {[card.rank.name for card in action.cards]}")
    else:
        logger.info("决策动作: 不出")
    
    # 测试单张控制残局
    logger.info("\n测试单张控制残局处理")
    single_card_state = create_endgame_state(EndgameType.SINGLE_CARD_CONTROL)
    
    # 检查是否是单张控制残局
    is_sc_scenario = is_single_card_control_scenario(single_card_state)
    logger.info(f"是否是单张控制残局场景: {is_sc_scenario}")
    
    # 获取合法动作
    legal_actions = single_card_state.get_legal_actions()
    
    # 使用混合决策系统做出决策
    action = hybrid_system.act(single_card_state, legal_actions)
    
    # 打印决策结果
    if action:
        logger.info(f"决策动作: {action.card_type.name}")
        if action.cards:
            logger.info(f"决策牌: {[card.rank.name for card in action.cards]}")
    else:
        logger.info("决策动作: 不出")
    
    # 打印统计信息
    stats = hybrid_system.get_stats()
    
    # 打印残局处理统计
    if "endgame_handling" in stats:
        endgame_stats = stats["endgame_handling"]
        logger.info("\n残局处理统计:")
        logger.info(f"  残局决策次数: {endgame_stats['endgame_decisions']}")
        logger.info(f"  残局决策比例: {endgame_stats['endgame_ratio']:.2f}")
        logger.info("  残局类型统计:")
        for endgame_type, count in endgame_stats['endgame_types'].items():
            logger.info(f"    {endgame_type}: {count}")


def simulate_game(hybrid_system: HybridDecisionSystem, env: DouDizhuEnvironment):
    """
    模拟一局游戏
    
    Args:
        hybrid_system: 混合决策系统
        env: 游戏环境
    """
    # 重置环境
    state = env.reset()
    done = False
    total_reward = 0
    
    # 游戏循环
    while not done:
        # 获取合法动作
        legal_actions = env.get_legal_actions()
        
        # 使用混合决策系统做出决策
        action = hybrid_system.act(state, legal_actions)
        
        # 执行动作
        state, reward, done, info = env.step(action)
        total_reward += reward
    
    # 游戏结束，更新奖励
    hybrid_system.update_reward(total_reward)
    
    # 打印游戏结果
    logger.info(f"游戏结束，总奖励: {total_reward}")


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 设置随机种子
    if args.seed is not None:
        np.random.seed(args.seed)
        torch.manual_seed(args.seed)
    
    # 创建游戏环境
    env = DouDizhuEnvironment()
    
    # 创建模型
    model = EfficientZero(model_path=args.model_path)
    
    # 创建规则代理
    rule_agent = RuleBasedAgent()
    
    # 创建关键决策点检测器
    key_moment_detector = None
    if args.key_moment_model:
        key_moment_detector = KeyMomentDetector.load(args.key_moment_model)
        logger.info(f"已加载关键决策点检测器: {args.key_moment_model}")
    
    # 创建混合决策系统
    hybrid_system = HybridDecisionSystem(
        neural_network_model=model,
        search_model=model,
        rule_agent=rule_agent,
        key_moment_detector=key_moment_detector,
        meta_strategy="adaptive"
    )
    
    # 测试残局处理
    test_endgame_handling(hybrid_system)
    
    # 模拟游戏
    logger.info("\n开始模拟游戏")
    for i in range(args.num_games):
        logger.info(f"开始游戏 {i+1}/{args.num_games}")
        simulate_game(hybrid_system, env)
    
    # 打印最终统计信息
    stats = hybrid_system.get_stats()
    logger.info(f"\n总决策次数: {stats['decisions']}")
    
    if "endgame_handling" in stats:
        endgame_stats = stats["endgame_handling"]
        logger.info(f"总残局决策次数: {endgame_stats['endgame_decisions']}")
        logger.info(f"残局决策比例: {endgame_stats['endgame_ratio']:.2f}")
    
    return 0


if __name__ == "__main__":
    main()
