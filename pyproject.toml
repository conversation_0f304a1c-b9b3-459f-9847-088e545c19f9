[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "cardgame-ai"
version = "1.0.0"
description = "斗地主AI优化项目 - 超人类水平的多智能体协作AI系统"
authors = [{name = "AI Development Team", email = "<EMAIL>"}]
license = {text = "MIT"}
readme = "README.md"
requires-python = ">=3.8"
keywords = ["ai", "reinforcement-learning", "multi-agent", "doudizhu", "efficient-zero"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Games/Entertainment :: Board Games",
]

dependencies = [
    "torch>=1.12.0",
    "torchvision>=0.13.0",
    "ray[default]>=2.0.0",
    "hydra-core>=1.2.0",
    "omegaconf>=2.2.0",
    "structlog>=22.1.0",
    "numpy>=1.21.0",
    "pandas>=1.4.0",
    "scipy>=1.8.0",
    "gymnasium>=0.26.0",
    "stable-baselines3>=1.6.0",
    "tensorboard>=2.8.0",
    "rich>=12.0.0",
    "tqdm>=4.64.0",
    "h5py>=3.7.0",
    "pillow>=9.0.0",
    "opencv-python>=4.6.0",
    "pydantic>=1.9.0",
    "click>=8.0.0",
    "psutil>=5.9.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=3.0.0",
    "pytest-mock>=3.7.0",
    "pytest-asyncio>=0.18.0",
    "pytest-xdist>=2.5.0",
    "black>=22.0.0",
    "flake8>=4.0.0",
    "mypy>=0.950",
    "isort>=5.10.0",
    "pre-commit>=2.19.0",
    "bandit>=1.7.0",
    "safety>=2.0.0",
]
monitoring = [
    "wandb>=0.13.0",
    "mlflow>=1.28.0",
    "prometheus-client>=0.14.0",
]
docs = [
    "sphinx>=5.0.0",
    "sphinx-rtd-theme>=1.0.0",
    "myst-parser>=0.18.0",
]
all = [
    "cardgame-ai[dev,monitoring,docs]"
]

[project.urls]
Homepage = "https://github.com/ai-team/cardgame-ai"
Documentation = "https://cardgame-ai.readthedocs.io/"
Repository = "https://github.com/ai-team/cardgame-ai.git"
"Bug Tracker" = "https://github.com/ai-team/cardgame-ai/issues"

[project.scripts]
cardgame-train = "cardgame_ai.cli.train:main"
cardgame-evaluate = "cardgame_ai.cli.evaluate:main"
cardgame-play = "cardgame_ai.cli.play:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["cardgame_ai*"]
exclude = ["tests*", "docs*", "scripts*"]

[tool.setuptools.package-data]
cardgame_ai = ["configs/*.yaml", "configs/**/*.yaml"]

# Black配置
[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | \.conda
  | build
  | dist
  | models
  | logs
)/
'''

# isort配置
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["cardgame_ai"]
known_third_party = ["torch", "ray", "hydra", "numpy", "pandas"]
sections = ["FUTURE", "STDLIB", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

# MyPy配置
[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true
show_column_numbers = true

[[tool.mypy.overrides]]
module = [
    "ray.*",
    "gymnasium.*",
    "stable_baselines3.*",
    "cv2.*",
    "wandb.*",
    "mlflow.*",
]
ignore_missing_imports = true

# Pytest配置
[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--cov=cardgame_ai",
    "--cov-report=html:htmlcov",
    "--cov-report=term-missing",
    "--cov-report=xml",
    "--cov-fail-under=80",
    "--strict-markers",
    "--strict-config",
    "-ra",
    "--tb=short",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "performance: marks tests as performance tests",
    "gpu: marks tests that require GPU",
    "distributed: marks tests that require distributed setup",
    "unit: marks tests as unit tests",
]
filterwarnings = [
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
]

# Coverage配置
[tool.coverage.run]
source = ["cardgame_ai"]
omit = [
    "*/tests/*",
    "*/test_*.py",
    "*_test.py",
    "setup.py",
    "*/migrations/*",
    "*/venv/*",
    "*/.venv/*",
    "*/.conda/*",
]
branch = true

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "def __str__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if False:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
    "except ImportError:",
    "except ModuleNotFoundError:",
]
show_missing = true
precision = 2

[tool.coverage.html]
directory = "htmlcov"

# Bandit安全检查配置
[tool.bandit]
exclude_dirs = ["tests", "venv", ".venv", ".conda"]
skips = ["B101", "B601"]  # 跳过assert和shell注入检查(在测试中常用)

# Flake8配置(在setup.cfg中定义，因为flake8不支持pyproject.toml)
# 这里仅作为参考，实际配置在.flake8文件中
