#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import io
# 设置标准输出编码为UTF-8
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

"""
清理残留测试进程工具

专门用于清理可能导致MCTS异常的残留测试进程。
这些进程通常是训练结束后仍在运行的测试脚本，会占用日志文件并干扰新的训练。

使用方法:
    python 清理残留测试进程.py                # 🚀 直接清理残留测试进程
    python 清理残留测试进程.py --list        # 📋 仅列出残留进程
    python 清理残留测试进程.py --force       # ⚡ 强制清理模式
"""

import psutil
import argparse
from typing import List, Dict, Any
import os
import glob


def find_residual_test_processes() -> List[Dict[str, Any]]:
    """查找残留的测试进程"""
    residual_processes = []
    
    # 精确的测试进程关键词
    test_keywords = [
        'test_mcts',
        'test_training',
        'test_efficient_zero',
        'test_muzero',
        'test_doudizhu',
        'test_cardgame',
        'emergency_fix',
        'mcts_debug',
        'training_debug'
    ]
    
    try:
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time', 'cpu_percent', 'memory_info']):
            try:
                proc_info = proc.info
                cmdline = proc_info.get('cmdline', [])
                
                if not cmdline:
                    continue
                
                cmdline_str = ' '.join(cmdline).lower()
                
                # 检查是否是Python进程运行测试脚本
                if 'python' in proc_info['name'].lower():
                    for keyword in test_keywords:
                        if keyword in cmdline_str:
                            try:
                                cpu_percent = proc.cpu_percent()
                                memory_mb = proc_info['memory_info'].rss / 1024 / 1024
                                
                                residual_processes.append({
                                    'pid': proc_info['pid'],
                                    'name': proc_info['name'],
                                    'cmdline': cmdline,
                                    'cmdline_str': ' '.join(cmdline),
                                    'create_time': proc_info['create_time'],
                                    'cpu_percent': cpu_percent,
                                    'memory_mb': memory_mb,
                                    'keyword_matched': keyword
                                })
                                break
                            except (psutil.NoSuchProcess, psutil.AccessDenied):
                                continue
                                
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue
                
    except Exception as e:
        print(f"搜索进程时出错: {e}")

    return residual_processes


def check_locked_log_files() -> List[str]:
    """检查被占用的日志文件"""
    locked_files = []
    
    log_patterns = [
        'logs/mcts_debug.log',
        'logs/training.log',
        'logs/efficient_zero.log',
        'logs/*.log'
    ]
    
    for pattern in log_patterns:
        try:
            for file_path in glob.glob(pattern):
                if os.path.exists(file_path):
                    try:
                        with open(file_path, 'a'):
                            pass
                    except (PermissionError, IOError):
                        locked_files.append(file_path)
        except Exception:
            continue
    
    return locked_files


def display_residual_processes(processes: List[Dict[str, Any]]):
    """显示残留进程信息"""
    if not processes:
        print("✅ 没有找到残留的测试进程")
        return

    print(f"🔍 发现 {len(processes)} 个残留测试进程:")
    print("=" * 80)
    
    for i, proc in enumerate(processes, 1):
        from datetime import datetime
        create_time = datetime.fromtimestamp(proc['create_time'])
        
        print(f"[{i}] 🚨 PID: {proc['pid']}")
        print(f"    名称: {proc['name']}")
        print(f"    命令: {proc['cmdline_str']}")
        print(f"    启动时间: {create_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"    CPU使用率: {proc['cpu_percent']:.1f}%")
        print(f"    内存使用: {proc['memory_mb']:.1f} MB")
        print(f"    匹配关键词: {proc['keyword_matched']}")
        print()


def terminate_process_safely(pid: int, force: bool = False) -> bool:
    """安全终止进程"""
    try:
        proc = psutil.Process(pid)
        proc_name = proc.name()

        print(f"🎯 正在终止残留进程 PID: {pid} ({proc_name})")

        # 查找子进程
        children = []
        try:
            children = proc.children(recursive=True)
            if children:
                print(f"📋 发现 {len(children)} 个子进程")
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass

        if force:
            # 强制终止
            for child in children:
                try:
                    child.kill()
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            proc.kill()
            print(f"⚡ 已强制终止进程 {pid}")
        else:
            # 优雅终止
            for child in children:
                try:
                    child.terminate()
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            proc.terminate()
            
            try:
                proc.wait(timeout=3)
                print(f"✅ 进程 {pid} 已优雅终止")
            except psutil.TimeoutExpired:
                print(f"⏰ 进程 {pid} 未响应，强制终止...")
                for child in children:
                    try:
                        child.kill()
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass
                proc.kill()
                proc.wait()
                print(f"⚡ 进程 {pid} 已强制终止")

        # 等待系统清理
        import time
        time.sleep(1)
        return True

    except psutil.NoSuchProcess:
        print(f"❌ 进程 {pid} 不存在")
        return False
    except psutil.AccessDenied:
        print(f"🔒 没有权限终止进程 {pid}")
        return False
    except Exception as e:
        print(f"❌ 终止进程 {pid} 时出错: {e}")
        return False


def clean_residual_processes(force: bool = False) -> int:
    """清理所有残留测试进程"""
    processes = find_residual_test_processes()
    
    if not processes:
        print("✅ 没有找到需要清理的残留测试进程")
        return 0

    print(f"🧹 准备清理 {len(processes)} 个残留测试进程...")

    terminated_count = 0
    for proc in processes:
        if terminate_process_safely(proc['pid'], force):
            terminated_count += 1

    print(f"✅ 已清理 {terminated_count}/{len(processes)} 个残留进程")
    return terminated_count


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="清理残留测试进程工具")
    parser.add_argument('--list', action='store_true', help='仅列出残留进程，不清理')
    parser.add_argument('--force', action='store_true', help='强制清理模式')
    
    args = parser.parse_args()
    
    print("🧹 残留测试进程清理工具")
    print("=" * 40)

    if args.list:
        # 仅列出进程
        print("📋 列出所有残留测试进程:")
        processes = find_residual_test_processes()
        display_residual_processes(processes)
        
        # 检查被占用的文件
        locked_files = check_locked_log_files()
        if locked_files:
            print(f"\n🔒 发现 {len(locked_files)} 个被占用的日志文件:")
            for file_path in locked_files:
                print(f"   📄 {file_path}")
    else:
        # 默认：直接清理残留进程
        print("🚀 开始清理残留测试进程...")
        
        # 检查被占用的文件
        locked_files = check_locked_log_files()
        if locked_files:
            print(f"🔒 发现 {len(locked_files)} 个被占用的日志文件:")
            for file_path in locked_files:
                print(f"   📄 {file_path}")
        
        # 清理残留进程
        terminated_count = clean_residual_processes(args.force)
        
        if terminated_count > 0:
            print("🔄 重新检查文件占用情况...")
            import time
            time.sleep(2)
            remaining_locked = check_locked_log_files()
            if remaining_locked:
                print(f"⚠️ 仍有 {len(remaining_locked)} 个文件被占用:")
                for file_path in remaining_locked:
                    print(f"   📄 {file_path}")
            else:
                print("✅ 所有日志文件占用已解除")
                print("🎉 现在可以正常删除日志文件了！")
        
        print("\n💡 建议:")
        print("   1. 检查 logs/mcts_debug.log 是否可以删除")
        print("   2. 重新启动训练程序")
        print("   3. 观察MCTS是否还有异常")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()
