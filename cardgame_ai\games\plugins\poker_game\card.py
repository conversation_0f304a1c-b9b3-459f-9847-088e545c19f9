"""
扑克牌类模块，定义了扑克牌的基本数据结构和操作。
"""

from enum import Enum, auto
from typing import List, Optional, Dict, Tuple


class CardSuit(Enum):
    """扑克牌花色"""
    SPADE = 4    # 黑桃
    HEART = 3    # 红桃
    CLUB = 2     # 梅花
    DIAMOND = 1  # 方块
    
    def __str__(self):
        """返回花色的中文名称"""
        return {
            CardSuit.SPADE: "黑桃",
            CardSuit.HEART: "红桃",
            CardSuit.CLUB: "梅花",
            CardSuit.DIAMOND: "方块"
        }[self]
    
    @property
    def symbol(self):
        """返回花色的符号"""
        return {
            CardSuit.SPADE: "♠",
            CardSuit.HEART: "♥",
            CardSuit.CLUB: "♣",
            CardSuit.DIAMOND: "♦"
        }[self]
    
    @property
    def color(self):
        """返回花色的颜色"""
        if self in [CardSuit.SPADE, CardSuit.CLUB]:
            return "black"
        return "red"


class CardRank(Enum):
    """扑克牌点数"""
    TWO = 2
    THREE = 3
    FOUR = 4
    FIVE = 5
    SIX = 6
    SEVEN = 7
    EIGHT = 8
    NINE = 9
    TEN = 10
    JACK = 11
    QUEEN = 12
    KING = 13
    ACE = 14  # 在大多数扑克游戏中A是最大的牌
    
    def __str__(self):
        """返回点数的字符表示"""
        return {
            CardRank.TWO: "2",
            CardRank.THREE: "3",
            CardRank.FOUR: "4",
            CardRank.FIVE: "5",
            CardRank.SIX: "6",
            CardRank.SEVEN: "7",
            CardRank.EIGHT: "8",
            CardRank.NINE: "9",
            CardRank.TEN: "10",
            CardRank.JACK: "J",
            CardRank.QUEEN: "Q",
            CardRank.KING: "K",
            CardRank.ACE: "A"
        }[self]


class PokerCard:
    """扑克牌类，表示一张扑克牌"""
    
    def __init__(self, suit: CardSuit, rank: CardRank):
        """
        初始化一张扑克牌
        
        Args:
            suit: 花色
            rank: 点数
        """
        self.suit = suit
        self.rank = rank
    
    def __str__(self):
        """返回牌的字符串表示，例如：♠A"""
        return f"{self.suit.symbol}{self.rank}"
    
    def __repr__(self):
        """返回牌的详细表示"""
        return f"PokerCard({self.suit}, {self.rank})"
    
    def __eq__(self, other):
        """判断两张牌是否相等"""
        if not isinstance(other, PokerCard):
            return False
        return self.suit == other.suit and self.rank == other.rank
    
    def __hash__(self):
        """计算哈希值，用于将牌放入集合或作为字典键"""
        return hash((self.suit, self.rank))
    
    def __lt__(self, other):
        """
        比较两张牌的大小
        
        在大多数扑克游戏中，先比较点数，点数相同再比较花色
        """
        if not isinstance(other, PokerCard):
            return NotImplemented
        if self.rank != other.rank:
            return self.rank.value < other.rank.value
        return self.suit.value < other.suit.value
    
    @property
    def value(self) -> int:
        """获取牌的点数值"""
        return self.rank.value
    
    @classmethod
    def from_string(cls, card_str: str) -> 'PokerCard':
        """
        从字符串创建扑克牌
        
        Args:
            card_str: 牌的字符串表示，如 "SA"(黑桃A), "H10"(红桃10)
            
        Returns:
            PokerCard: 创建的扑克牌对象
            
        Raises:
            ValueError: 如果字符串格式不正确
        """
        if len(card_str) < 2:
            raise ValueError(f"无效的牌表示: {card_str}")
        
        # 解析花色
        suit_char = card_str[0].upper()
        suit_map = {
            'S': CardSuit.SPADE,
            'H': CardSuit.HEART,
            'C': CardSuit.CLUB,
            'D': CardSuit.DIAMOND,
            '♠': CardSuit.SPADE,
            '♥': CardSuit.HEART,
            '♣': CardSuit.CLUB,
            '♦': CardSuit.DIAMOND
        }
        
        if suit_char not in suit_map:
            raise ValueError(f"无效的花色: {suit_char}")
        
        suit = suit_map[suit_char]
        
        # 解析点数
        rank_str = card_str[1:].upper()
        rank_map = {
            '2': CardRank.TWO,
            '3': CardRank.THREE,
            '4': CardRank.FOUR,
            '5': CardRank.FIVE,
            '6': CardRank.SIX,
            '7': CardRank.SEVEN,
            '8': CardRank.EIGHT,
            '9': CardRank.NINE,
            '10': CardRank.TEN,
            'J': CardRank.JACK,
            'Q': CardRank.QUEEN,
            'K': CardRank.KING,
            'A': CardRank.ACE
        }
        
        if rank_str not in rank_map:
            raise ValueError(f"无效的点数: {rank_str}")
        
        rank = rank_map[rank_str]
        
        return cls(suit, rank)


class Deck:
    """扑克牌组类，表示一副或多副扑克牌"""
    
    def __init__(self, num_decks: int = 1, include_jokers: bool = False):
        """
        初始化牌组
        
        Args:
            num_decks: 牌组数量，默认为1副
            include_jokers: 是否包含大小王，默认不包含
        """
        self.cards: List[PokerCard] = []
        self.num_decks = num_decks
        self.include_jokers = include_jokers
        self.reset()
    
    def reset(self):
        """重置牌组到初始状态"""
        self.cards.clear()
        
        # 添加普通牌
        for _ in range(self.num_decks):
            for suit in CardSuit:
                for rank in CardRank:
                    self.cards.append(PokerCard(suit, rank))
        
        # 如果需要，添加大小王
        # 在德州扑克中通常不使用大小王，所以这里暂时不实现
    
    def shuffle(self, seed: Optional[int] = None):
        """
        洗牌
        
        Args:
            seed: 随机种子，用于reproduciblity
        """
        import random
        if seed is not None:
            random.seed(seed)
        random.shuffle(self.cards)
    
    def draw(self, count: int = 1) -> List[PokerCard]:
        """
        从牌组顶部抽取指定数量的牌
        
        Args:
            count: 要抽取的牌数量
            
        Returns:
            List[PokerCard]: 抽取的牌列表
            
        Raises:
            ValueError: 如果牌组中没有足够的牌
        """
        if count > len(self.cards):
            raise ValueError(f"牌组中只有{len(self.cards)}张牌，无法抽取{count}张")
        
        drawn_cards = self.cards[:count]
        self.cards = self.cards[count:]
        return drawn_cards
    
    def draw_one(self) -> PokerCard:
        """
        从牌组顶部抽取一张牌
        
        Returns:
            PokerCard: 抽取的牌
            
        Raises:
            ValueError: 如果牌组为空
        """
        if not self.cards:
            raise ValueError("牌组为空")
        
        return self.draw(1)[0]
    
    def __len__(self):
        """返回牌组中剩余的牌数量"""
        return len(self.cards)
    
    def __str__(self):
        """返回牌组的字符串表示"""
        return f"Deck({len(self.cards)} cards)"
    
    def __repr__(self):
        """返回牌组的详细表示"""
        return f"Deck(num_decks={self.num_decks}, include_jokers={self.include_jokers}, cards_left={len(self.cards)})" 