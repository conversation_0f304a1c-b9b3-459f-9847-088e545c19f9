"""
事件系统模块
提供事件发布-订阅功能，供系统内部组件通信
"""
from typing import Callable, Dict, List, Any

class EventSystem:
    """
    事件系统
    实现简单的发布-订阅机制，用于组件之间事件通信
    """
    def __init__(self):
        # 初始化订阅者字典，key: 事件类型，value: 处理函数列表
        self._subscribers: Dict[str, List[Callable[..., Any]]] = {}

    def subscribe(self, event_type: str, handler: Callable[..., Any]) -> None:
        """
        订阅指定类型的事件
        Args:
            event_type: 事件类型标识
            handler: 事件处理函数
        """
        self._subscribers.setdefault(event_type, []).append(handler)

    def unsubscribe(self, event_type: str, handler: Callable[..., Any]) -> None:
        """
        取消订阅指定类型的事件
        """
        if event_type in self._subscribers:
            handlers = self._subscribers[event_type]
            if handler in handlers:
                handlers.remove(handler)
            if not handlers:
                del self._subscribers[event_type]

    def publish(self, event_type: str, *args: Any, **kwargs: Any) -> None:
        """
        发布事件，通知所有订阅者
        Args:
            event_type: 事件类型标识
            *args: 传递给处理函数的位置参数
            **kwargs: 传递给处理函数的关键字参数
        """
        for handler in list(self._subscribers.get(event_type, [])):
            try:
                handler(*args, **kwargs)
            except Exception as e:
                # 错误处理，避免阻塞其他订阅者
                print(f"EventSystem: 事件处理失败: {e}") 