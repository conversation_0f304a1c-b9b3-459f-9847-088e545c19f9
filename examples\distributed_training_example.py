#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
分布式训练示例

展示如何使用Ray RLlib框架进行分布式强化学习训练。
"""

import os
import sys
import argparse
import logging
import numpy as np
import torch
import ray
from typing import Dict, Any, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ray import tune
from ray.rllib.algorithms.impala import ImpalaConfig
from ray.rllib.models import ModelCatalog
from ray.tune.registry import register_env

from cardgame_ai.environments.rllib_doudizhu_env import RLlibDouDizhuEnv
from cardgame_ai.algorithms.rllib_adapter import RLlibEfficientZeroModel
from cardgame_ai.training.rllib_trainer import register_models_and_envs

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def parse_args():
    """
    解析命令行参数
    
    Returns:
        argparse.Namespace: 命令行参数
    """
    parser = argparse.ArgumentParser(description="分布式训练示例")
    
    # 训练参数
    parser.add_argument("--num_iterations", type=int, default=1000, help="训练迭代次数")
    parser.add_argument("--checkpoint_freq", type=int, default=10, help="检查点保存频率")
    parser.add_argument("--checkpoint_dir", type=str, default="checkpoints", help="检查点保存目录")
    parser.add_argument("--log_dir", type=str, default="logs", help="日志保存目录")
    
    # 资源参数
    parser.add_argument("--num_workers", type=int, default=4, help="工作进程数量")
    parser.add_argument("--num_envs_per_worker", type=int, default=1, help="每个工作进程的环境数量")
    parser.add_argument("--num_gpus", type=float, default=1.0, help="GPU数量")
    parser.add_argument("--num_gpus_per_worker", type=float, default=0.0, help="每个工作进程的GPU数量")
    
    # 算法参数
    parser.add_argument("--learning_rate", type=float, default=0.0005, help="学习率")
    parser.add_argument("--gamma", type=float, default=0.99, help="折扣因子")
    parser.add_argument("--lambda_", type=float, default=0.95, help="GAE参数")
    parser.add_argument("--vf_loss_coeff", type=float, default=0.5, help="值函数损失系数")
    parser.add_argument("--entropy_coeff", type=float, default=0.01, help="熵正则化系数")
    parser.add_argument("--train_batch_size", type=int, default=500, help="训练批次大小")
    
    # 模型参数
    parser.add_argument("--hidden_dim", type=int, default=256, help="隐藏层维度")
    parser.add_argument("--state_dim", type=int, default=64, help="状态维度")
    
    return parser.parse_args()


def main():
    """
    主函数
    """
    # 解析命令行参数
    args = parse_args()
    
    # 创建检查点和日志目录
    os.makedirs(args.checkpoint_dir, exist_ok=True)
    os.makedirs(args.log_dir, exist_ok=True)
    
    # 初始化Ray
    ray.init()
    
    # 注册模型和环境
    register_models_and_envs()
    
    # 创建IMPALA配置
    config = (
        ImpalaConfig()
        .environment(env="doudizhu")
        .framework("torch")
        .rollouts(
            num_rollout_workers=args.num_workers,
            num_envs_per_worker=args.num_envs_per_worker,
            rollout_fragment_length=50,
            batch_mode="truncate_episodes"
        )
        .training(
            train_batch_size=args.train_batch_size,
            lr=args.learning_rate,
            gamma=args.gamma,
            lambda_=args.lambda_,
            vf_loss_coeff=args.vf_loss_coeff,
            entropy_coeff=args.entropy_coeff,
            model={
                "custom_model": "efficient_zero_model",
                "custom_model_config": {
                    "hidden_dim": args.hidden_dim,
                    "state_dim": args.state_dim,
                    "use_resnet": True,
                    "projection_dim": 256,
                    "prediction_dim": 128,
                    "value_prefix_length": 5
                }
            }
        )
        .resources(
            num_gpus=args.num_gpus,
            num_gpus_per_worker=args.num_gpus_per_worker
        )
        .fault_tolerance(
            recreate_failed_workers=True,
            restart_failed_sub_environments=True
        )
        .debugging(
            log_level="INFO",
            logger_config={
                "type": "ray.tune.logger.TBXLogger",
                "logdir": args.log_dir
            }
        )
    )
    
    # 创建训练器
    trainer = config.build()
    
    # 训练循环
    for i in range(args.num_iterations):
        # 训练一个迭代
        result = trainer.train()
        
        # 打印训练结果
        logger.info(f"迭代 {i + 1}/{args.num_iterations}")
        logger.info(f"  训练奖励: {result['episode_reward_mean']}")
        logger.info(f"  训练长度: {result['episode_len_mean']}")
        logger.info(f"  训练时间: {result['time_total_s']}秒")
        
        # 保存检查点
        if (i + 1) % args.checkpoint_freq == 0 or i + 1 == args.num_iterations:
            checkpoint_path = trainer.save(args.checkpoint_dir)
            logger.info(f"  保存检查点: {checkpoint_path}")
    
    # 关闭训练器
    trainer.stop()
    
    # 关闭Ray
    ray.shutdown()
    
    logger.info("训练完成！")


if __name__ == "__main__":
    main()
