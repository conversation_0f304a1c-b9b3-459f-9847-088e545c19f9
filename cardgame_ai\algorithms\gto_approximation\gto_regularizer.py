"""
GTO正则化器模块

提供用于计算GTO正则化损失的类。
"""

import torch
import torch.nn.functional as F
import numpy as np
import logging
from typing import Dict, Any, Optional, List, Tuple, Union, Literal

from .gto_policy import GTOPolicy

# 配置日志
logger = logging.getLogger(__name__)

class GTORegularizer:
    """
    GTO正则化器类
    
    用于计算当前策略与GTO策略之间的偏差，并生成正则化损失。
    """
    
    def __init__(
        self,
        gto_policy: GTOPolicy,
        regularization_weight: float = 0.1,
        regularization_method: Literal['kl', 'js', 'l2'] = 'kl',
        adaptive_weight: bool = False,
        adaptive_weight_params: Optional[Dict[str, float]] = None,
        device: Optional[torch.device] = None
    ):
        """
        初始化GTO正则化器
        
        Args:
            gto_policy: GTO策略
            regularization_weight: 正则化权重
            regularization_method: 正则化方法，可选 'kl'(KL散度), 'js'(JS散度), 'l2'(L2距离)
            adaptive_weight: 是否使用自适应权重
            adaptive_weight_params: 自适应权重参数, 如 {'max_weight': 0.5, 'min_weight': 0.01, 'decay_rate': 0.995}
            device: 计算设备
        """
        self.gto_policy = gto_policy
        self.regularization_weight = regularization_weight
        self.initial_weight = regularization_weight
        self.regularization_method = regularization_method
        self.adaptive_weight = adaptive_weight
        self.device = device if device is not None else torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 自适应权重参数设置
        self.adaptive_weight_params = {
            'max_weight': 0.5,
            'min_weight': 0.01,
            'decay_rate': 0.995,
            'increase_rate': 1.01,
            'performance_threshold': 0.1,
            'anneal_steps': 10000
        }
        if adaptive_weight_params:
            self.adaptive_weight_params.update(adaptive_weight_params)
        
        # 训练步数计数
        self.train_steps = 0
        
        # 性能跟踪
        self.performance_history = []
        
        logger.info(f"初始化GTO正则化器，正则化方法: {regularization_method}, 正则化权重: {regularization_weight}，"
                   f"自适应权重: {adaptive_weight}，设备: {self.device}")
    
    def update_weight(self, performance_metric: Optional[float] = None) -> None:
        """
        更新正则化权重
        
        Args:
            performance_metric: 性能指标，用于自适应调整权重
        """
        if not self.adaptive_weight:
            return
        
        self.train_steps += 1
        
        # 基于训练步数进行权重衰减
        if self.train_steps % self.adaptive_weight_params['anneal_steps'] == 0:
            old_weight = self.regularization_weight
            self.regularization_weight = max(
                self.regularization_weight * self.adaptive_weight_params['decay_rate'],
                self.adaptive_weight_params['min_weight']
            )
            logger.info(f"GTO正则化权重衰减: {old_weight:.6f} -> {self.regularization_weight:.6f}")
        
        # 如果提供了性能指标，基于性能调整权重
        if performance_metric is not None:
            # 记录性能历史
            self.performance_history.append(performance_metric)
            
            # 至少需要有两个数据点才能计算变化
            if len(self.performance_history) >= 2:
                # 计算性能变化
                perf_change = self.performance_history[-1] - self.performance_history[-2]
                threshold = self.adaptive_weight_params['performance_threshold']
                
                old_weight = self.regularization_weight
                
                # 如果性能提升小于阈值，增加正则化权重
                if perf_change < threshold:
                    self.regularization_weight = min(
                        self.regularization_weight * self.adaptive_weight_params['increase_rate'],
                        self.adaptive_weight_params['max_weight']
                    )
                # 如果性能提升明显，减少正则化权重
                else:
                    self.regularization_weight = max(
                        self.regularization_weight * self.adaptive_weight_params['decay_rate'],
                        self.adaptive_weight_params['min_weight']
                    )
                
                if old_weight != self.regularization_weight:
                    logger.info(f"基于性能指标调整GTO正则化权重: {old_weight:.6f} -> {self.regularization_weight:.6f} "
                               f"(性能变化: {perf_change:.6f})")
                
                # 只保留最近10个性能记录
                if len(self.performance_history) > 10:
                    self.performance_history.pop(0)
    
    def compute_loss(
        self,
        states: List[Any],
        legal_actions_list: List[List[int]],
        predicted_policies: torch.Tensor
    ) -> torch.Tensor:
        """
        计算GTO正则化损失
        
        Args:
            states: 状态列表
            legal_actions_list: 每个状态的合法动作列表
            predicted_policies: 预测的策略分布
            
        Returns:
            GTO正则化损失
        """
        batch_size = len(states)
        
        # 获取GTO策略
        gto_policies = []
        for i, state in enumerate(states):
            legal_actions = legal_actions_list[i] if i < len(legal_actions_list) else None
            policy = self.gto_policy.get_policy(state, legal_actions)
            if policy is None or len(policy) != predicted_policies.shape[1]:
                # 如果没有GTO策略或维度不匹配，使用均匀分布
                policy = np.ones(predicted_policies.shape[1]) / predicted_policies.shape[1]
            gto_policies.append(policy)
        
        # 转换为张量
        gto_policies = torch.FloatTensor(np.array(gto_policies)).to(self.device)
        
        # 根据选择的方法计算损失
        if self.regularization_method == 'kl':
            # 计算KL散度: KL(predicted || gto)
            loss = F.kl_div(
                F.log_softmax(predicted_policies, dim=1),
                gto_policies,
                reduction='batchmean',
                log_target=False
            )
        
        elif self.regularization_method == 'js':
            # 计算JS散度: 0.5 * (KL(predicted || mean) + KL(gto || mean))
            # 计算平均策略
            mean_policies = 0.5 * (F.softmax(predicted_policies, dim=1) + gto_policies)
            
            # 计算KL(predicted || mean)
            kl_pred_mean = F.kl_div(
                F.log_softmax(predicted_policies, dim=1),
                mean_policies,
                reduction='batchmean',
                log_target=False
            )
            
            # 计算KL(gto || mean)
            kl_gto_mean = F.kl_div(
                torch.log(gto_policies + 1e-10),  # 避免log(0)
                mean_policies,
                reduction='batchmean',
                log_target=False
            )
            
            # JS散度 = 0.5 * (KL(predicted || mean) + KL(gto || mean))
            loss = 0.5 * (kl_pred_mean + kl_gto_mean)
            
        elif self.regularization_method == 'l2':
            # 计算L2距离
            pred_probs = F.softmax(predicted_policies, dim=1)
            loss = torch.mean(torch.sum((pred_probs - gto_policies) ** 2, dim=1))
        
        else:
            raise ValueError(f"不支持的正则化方法: {self.regularization_method}")
        
        # 应用正则化权重
        return self.regularization_weight * loss
    
    def compute_policy_distance(
        self,
        states: List[Any],
        legal_actions_list: List[List[int]],
        predicted_policies: torch.Tensor
    ) -> Tuple[float, float, float]:
        """
        计算预测策略与GTO策略之间的距离
        
        Args:
            states: 状态列表
            legal_actions_list: 每个状态的合法动作列表
            predicted_policies: 预测的策略分布
            
        Returns:
            Tuple[float, float, float]: (平均距离, 最大距离, 最小距离)
        """
        batch_size = len(states)
        
        # 获取GTO策略
        gto_policies = []
        for i, state in enumerate(states):
            legal_actions = legal_actions_list[i] if i < len(legal_actions_list) else None
            policy = self.gto_policy.get_policy(state, legal_actions)
            if policy is None or len(policy) != predicted_policies.shape[1]:
                # 如果没有GTO策略或维度不匹配，使用均匀分布
                policy = np.ones(predicted_policies.shape[1]) / predicted_policies.shape[1]
            gto_policies.append(policy)
        
        # 转换为张量
        gto_policies = torch.FloatTensor(np.array(gto_policies)).to(self.device)
        
        # 计算每个样本的距离
        distances = []
        pred_probs = F.softmax(predicted_policies, dim=1)
        
        for i in range(batch_size):
            if self.regularization_method == 'kl':
                # KL散度
                dist = F.kl_div(
                    F.log_softmax(predicted_policies[i:i+1], dim=1),
                    gto_policies[i:i+1],
                    reduction='sum',
                    log_target=False
                ).item()
            
            elif self.regularization_method == 'js':
                # JS散度
                mean_policy = 0.5 * (pred_probs[i] + gto_policies[i])
                kl_pred_mean = F.kl_div(
                    F.log_softmax(predicted_policies[i:i+1], dim=1),
                    mean_policy.unsqueeze(0),
                    reduction='sum',
                    log_target=False
                ).item()
                kl_gto_mean = F.kl_div(
                    torch.log(gto_policies[i:i+1] + 1e-10),
                    mean_policy.unsqueeze(0),
                    reduction='sum',
                    log_target=False
                ).item()
                dist = 0.5 * (kl_pred_mean + kl_gto_mean)
            
            elif self.regularization_method == 'l2':
                # L2距离
                dist = torch.sum((pred_probs[i] - gto_policies[i]) ** 2).item()
            
            else:
                raise ValueError(f"不支持的正则化方法: {self.regularization_method}")
            
            distances.append(dist)
        
        # 计算统计信息
        mean_dist = np.mean(distances)
        max_dist = np.max(distances)
        min_dist = np.min(distances)
        
        return mean_dist, max_dist, min_dist
    
    def get_current_weight(self) -> float:
        """
        获取当前正则化权重
        
        Returns:
            当前正则化权重
        """
        return self.regularization_weight
    
    def reset_weight(self) -> None:
        """
        重置正则化权重为初始值
        """
        self.regularization_weight = self.initial_weight
        logger.info(f"GTO正则化权重已重置为初始值: {self.initial_weight}")
