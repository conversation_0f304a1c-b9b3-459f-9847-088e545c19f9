"""
MCTS搜索与手牌信息价值评估集成示例脚本

演示如何将手牌信息价值评估集成到MCTS搜索中。
"""

import os
import sys
import argparse
import logging
import numpy as np
import torch
from typing import Dict, List, Any, Optional, Tuple

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cardgame_ai.algorithms.card_information_value import CardInformationValueEstimator
from cardgame_ai.games.common.belief_state import BeliefState
from cardgame_ai.games.doudizhu.card import Card
from cardgame_ai.games.doudizhu.game import DouDizhuGame
from cardgame_ai.games.doudizhu.state import DouDizhuState
from cardgame_ai.algorithms.efficient_zero import EfficientZeroModel
from cardgame_ai.algorithms.mcts import MCTS
from cardgame_ai.algorithms.belief_tracking.belief_tracker import BeliefTracker

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def parse_args():
    """
    解析命令行参数
    
    Returns:
        argparse.Namespace: 解析后的参数
    """
    parser = argparse.ArgumentParser(description='MCTS搜索与手牌信息价值评估集成示例')
    parser.add_argument('--model_path', type=str, default='models/efficient_zero_model.pth', help='模型路径')
    parser.add_argument('--method', type=str, default='combined', choices=['basic', 'entropy', 'action', 'combined'], help='评估方法')
    parser.add_argument('--context_aware', action='store_true', help='是否考虑上下文信息')
    parser.add_argument('--use_decision_impact', action='store_true', help='是否考虑对决策的影响')
    parser.add_argument('--num_simulations', type=int, default=100, help='MCTS模拟次数')
    parser.add_argument('--top_n', type=int, default=5, help='返回信息价值最高的前N张牌')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    
    return parser.parse_args()


def create_test_state() -> DouDizhuState:
    """
    创建测试状态
    
    Returns:
        DouDizhuState: 测试状态
    """
    # 创建游戏
    game = DouDizhuGame()
    
    # 初始化状态
    state = game.get_init_state()
    
    # 模拟发牌
    landlord_cards = [
        Card.from_string('3'), Card.from_string('3'), Card.from_string('4'), Card.from_string('4'),
        Card.from_string('5'), Card.from_string('6'), Card.from_string('7'), Card.from_string('8'),
        Card.from_string('9'), Card.from_string('10'), Card.from_string('J'), Card.from_string('Q'),
        Card.from_string('K'), Card.from_string('A'), Card.from_string('2'), Card.from_string('2'),
        Card.from_string('JOKER')
    ]
    
    farmer1_cards = [
        Card.from_string('3'), Card.from_string('4'), Card.from_string('5'), Card.from_string('5'),
        Card.from_string('6'), Card.from_string('6'), Card.from_string('7'), Card.from_string('8'),
        Card.from_string('9'), Card.from_string('10'), Card.from_string('J'), Card.from_string('Q'),
        Card.from_string('K'), Card.from_string('A'), Card.from_string('A'), Card.from_string('JOKER')
    ]
    
    farmer2_cards = [
        Card.from_string('3'), Card.from_string('4'), Card.from_string('5'), Card.from_string('6'),
        Card.from_string('7'), Card.from_string('7'), Card.from_string('8'), Card.from_string('8'),
        Card.from_string('9'), Card.from_string('9'), Card.from_string('10'), Card.from_string('10'),
        Card.from_string('J'), Card.from_string('Q'), Card.from_string('K'), Card.from_string('K')
    ]
    
    # 设置手牌
    state.player_cards = {
        0: landlord_cards,
        1: farmer1_cards,
        2: farmer2_cards
    }
    
    # 设置地主
    state.landlord = 0
    
    # 设置当前玩家
    state.current_player = 0
    
    # 设置历史动作
    state.history = []
    
    return state


def load_model(model_path: str) -> EfficientZeroModel:
    """
    加载模型
    
    Args:
        model_path: 模型路径
        
    Returns:
        EfficientZeroModel: 加载的模型
    """
    # 检查模型文件是否存在
    if not os.path.exists(model_path):
        logger.warning(f"模型文件不存在: {model_path}，使用随机初始化的模型")
        # 创建随机初始化的模型
        model = EfficientZeroModel(
            observation_shape=(54 * 4 + 54 * 3,),
            action_shape=(54 * 3,),
            hidden_dim=128,
            state_dim=64
        )
        return model
    
    # 加载模型
    try:
        checkpoint = torch.load(model_path, map_location='cpu')
        model = EfficientZeroModel(
            observation_shape=(54 * 4 + 54 * 3,),
            action_shape=(54 * 3,),
            hidden_dim=128,
            state_dim=64
        )
        model.load_state_dict(checkpoint['model_state_dict'])
        logger.info(f"成功加载模型: {model_path}")
        return model
    except Exception as e:
        logger.error(f"加载模型失败: {e}")
        # 创建随机初始化的模型
        model = EfficientZeroModel(
            observation_shape=(54 * 4 + 54 * 3,),
            action_shape=(54 * 3,),
            hidden_dim=128,
            state_dim=64
        )
        return model


def mcts_search_with_info_value(
    state: DouDizhuState,
    model: EfficientZeroModel,
    mcts: MCTS,
    belief_trackers: Dict[int, BeliefTracker],
    info_value_estimator: CardInformationValueEstimator,
    num_simulations: int,
    temperature: float = 1.0
) -> Tuple[int, Dict[int, int], Dict[str, float]]:
    """
    使用手牌信息价值评估的MCTS搜索
    
    Args:
        state: 游戏状态
        model: 模型
        mcts: MCTS搜索器
        belief_trackers: 信念追踪器字典
        info_value_estimator: 手牌信息价值评估器
        num_simulations: 模拟次数
        temperature: 温度参数
        
    Returns:
        Tuple[int, Dict[int, int], Dict[str, float]]:
            - 最佳动作
            - 访问计数
            - 手牌信息价值
    """
    # 获取当前玩家
    current_player = state.current_player
    
    # 获取合法动作
    legal_actions = state.get_legal_actions()
    
    # 创建动作掩码
    actions_mask = [i in legal_actions for i in range(54 * 3)]
    
    # 使用MCTS进行搜索
    logger.info("使用MCTS进行搜索...")
    visit_counts, _, _ = mcts.run(
        root_state=state,
        model=model,
        temperature=temperature,
        actions_mask=actions_mask
    )
    
    # 获取最佳动作
    best_action = max(visit_counts.items(), key=lambda x: x[1])[0]
    logger.info(f"最佳动作: {best_action}")
    
    # 获取当前玩家的信念追踪器
    belief_tracker = belief_trackers.get(current_player)
    if belief_tracker is None:
        logger.warning(f"未找到玩家 {current_player} 的信念追踪器")
        return best_action, visit_counts, {}
    
    # 获取信念状态
    belief_state = belief_tracker.get_belief_state()
    
    # 创建游戏上下文
    game_context = {
        'game_stage': 'mid',  # 可以根据实际情况确定游戏阶段
        'remaining_cards': {
            'player': len(state.player_cards[current_player]),
            'opponent': len(state.player_cards[(current_player + 1) % 3])
        },
        'history': state.history
    }
    
    # 创建状态向量
    state_vector = state.to_vector()  # 假设DouDizhuState有to_vector方法
    
    # 创建策略函数
    def policy_function(belief_state: BeliefState, state: np.ndarray) -> np.ndarray:
        # 使用模型预测动作概率
        observation = np.concatenate([state, belief_state.to_vector()])
        observation_tensor = torch.tensor(observation, dtype=torch.float32).unsqueeze(0)
        with torch.no_grad():
            policy_logits, _ = model.initial_inference(observation_tensor)
        policy_probs = torch.softmax(policy_logits, dim=1).squeeze(0).numpy()
        return policy_probs
    
    # 获取所有未知牌
    all_cards = []
    for rank in ['3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A', '2']:
        for _ in range(4):
            all_cards.append(Card.from_string(rank))
    all_cards.append(Card.from_string('JOKER'))
    all_cards.append(Card.from_string('JOKER'))
    
    known_cards = state.player_cards[current_player]
    unknown_cards = [card for card in all_cards if card not in known_cards]
    
    # 评估所有未知牌的信息价值
    logger.info(f"评估 {len(unknown_cards)} 张未知牌的信息价值...")
    info_values = info_value_estimator.estimate_all_cards(
        belief_state=belief_state,
        cards=unknown_cards,
        current_state=state_vector,
        policy_function=policy_function,
        game_context=game_context,
        top_n=5
    )
    
    # 打印信息价值最高的牌
    logger.info(f"信息价值最高的 {len(info_values)} 张牌:")
    for card, value in sorted(info_values.items(), key=lambda x: x[1], reverse=True):
        logger.info(f"  {card}: {value:.4f}")
    
    return best_action, visit_counts, info_values


def main():
    """
    主函数
    """
    # 解析命令行参数
    args = parse_args()
    
    # 设置随机种子
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    
    # 创建测试状态
    state = create_test_state()
    
    # 加载模型
    model = load_model(args.model_path)
    
    # 创建MCTS搜索器
    mcts = MCTS(
        num_simulations=args.num_simulations,
        discount=0.99,
        dirichlet_alpha=0.3,
        exploration_fraction=0.25
    )
    
    # 创建信念追踪器
    belief_trackers = {
        0: BeliefTracker(player_id=0, num_cards=54),
        1: BeliefTracker(player_id=1, num_cards=54),
        2: BeliefTracker(player_id=2, num_cards=54)
    }
    
    # 创建手牌信息价值评估器
    info_value_estimator = CardInformationValueEstimator(
        method=args.method,
        context_aware=args.context_aware,
        use_decision_impact=args.use_decision_impact
    )
    
    # 使用手牌信息价值评估的MCTS搜索
    best_action, visit_counts, info_values = mcts_search_with_info_value(
        state=state,
        model=model,
        mcts=mcts,
        belief_trackers=belief_trackers,
        info_value_estimator=info_value_estimator,
        num_simulations=args.num_simulations
    )
    
    # 打印结果
    logger.info(f"最佳动作: {best_action}")
    logger.info("访问计数:")
    for action, count in sorted(visit_counts.items(), key=lambda x: x[1], reverse=True)[:5]:
        logger.info(f"  动作 {action}: {count}")
    
    logger.info("手牌信息价值:")
    for card, value in sorted(info_values.items(), key=lambda x: x[1], reverse=True):
        logger.info(f"  {card}: {value:.4f}")


if __name__ == '__main__':
    main()
