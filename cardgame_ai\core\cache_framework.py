"""
缓存框架模块

提供统一的缓存机制，支持多级缓存、过期策略和相似状态查找，
减少重复计算，提高系统性能。
"""

import os
import time
import logging
import threading
import pickle
from typing import Dict, Any, Optional, Tuple, Callable, List, Union
import numpy as np

# 设置日志
logger = logging.getLogger(__name__)


class CacheEntry:
    """
    缓存条目类

    表示缓存中的单个条目，包含值、时间戳和生存时间信息。
    """

    def __init__(self, key: Any, value: Any, timestamp: float, ttl: Optional[float] = None):
        """
        初始化缓存条目

        Args:
            key: 缓存键
            value: 缓存值
            timestamp: 创建时间戳
            ttl: 生存时间（秒），None表示永不过期
        """
        self.key = key
        self.value = value
        self.timestamp = timestamp
        self.ttl = ttl
        self.access_count = 0  # 访问计数

    def is_expired(self, current_time: float) -> bool:
        """
        检查条目是否过期

        Args:
            current_time: 当前时间戳

        Returns:
            bool: 是否过期
        """
        if self.ttl is None:
            return False
        return (current_time - self.timestamp) > self.ttl


class Cache:
    """
    基本缓存类

    提供基本的缓存功能，支持过期策略和容量限制。
    """

    def __init__(self, capacity: int = 1000, ttl: Optional[float] = None):
        """
        初始化缓存

        Args:
            capacity: 缓存容量
            ttl: 默认生存时间（秒），None表示永不过期
        """
        self.capacity = capacity
        self.default_ttl = ttl
        self.cache = {}  # 缓存字典

        # 统计信息
        self.access_count = 0
        self.hit_count = 0
        self.miss_count = 0
        self.eviction_count = 0

        # 线程锁
        self.lock = threading.RLock()

    def get(self, key: Any, default: Any = None) -> Any:
        """
        获取缓存值

        Args:
            key: 缓存键
            default: 默认值，如果键不存在或已过期则返回

        Returns:
            缓存值或默认值
        """
        with self.lock:
            self.access_count += 1

            # 检查键是否存在
            if key in self.cache:
                entry = self.cache[key]

                # 检查是否过期
                if not entry.is_expired(time.time()):
                    # 更新访问计数
                    entry.access_count += 1
                    self.hit_count += 1
                    return entry.value
                else:
                    # 删除过期条目
                    del self.cache[key]
                    self.miss_count += 1
            else:
                self.miss_count += 1

            return default

    def put(self, key: Any, value: Any, ttl: Optional[float] = None) -> None:
        """
        添加或更新缓存

        Args:
            key: 缓存键
            value: 缓存值
            ttl: 生存时间（秒），None表示使用默认值
        """
        with self.lock:
            # 如果缓存已满，需要淘汰一个条目
            if len(self.cache) >= self.capacity and key not in self.cache:
                self._evict()

            # 添加或更新条目
            self.cache[key] = CacheEntry(
                key=key,
                value=value,
                timestamp=time.time(),
                ttl=ttl if ttl is not None else self.default_ttl
            )

    def _evict(self) -> None:
        """
        淘汰一个缓存条目

        默认使用LRU策略，可以在子类中重写
        """
        # 找到最久未使用的条目
        oldest_key = None
        oldest_timestamp = float('inf')

        for key, entry in self.cache.items():
            if entry.timestamp < oldest_timestamp:
                oldest_timestamp = entry.timestamp
                oldest_key = key

        if oldest_key:
            del self.cache[oldest_key]
            self.eviction_count += 1

    def get_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息

        Returns:
            Dict[str, Any]: 统计信息字典
        """
        with self.lock:
            hit_rate = self.hit_count / self.access_count if self.access_count > 0 else 0.0
            miss_rate = self.miss_count / self.access_count if self.access_count > 0 else 0.0

            return {
                "capacity": self.capacity,
                "size": len(self.cache),
                "access_count": self.access_count,
                "hit_count": self.hit_count,
                "miss_count": self.miss_count,
                "hit_rate": hit_rate,
                "miss_rate": miss_rate,
                "eviction_count": self.eviction_count
            }

    def clear(self) -> None:
        """清空缓存"""
        with self.lock:
            self.cache.clear()


class MultiLevelCache:
    """
    多级缓存类

    组合多个缓存，形成缓存层次结构，提高命中率和性能。
    """

    def __init__(self, levels: List[Cache]):
        """
        初始化多级缓存

        Args:
            levels: 缓存层级列表，按优先级排序（索引0最高）
        """
        self.levels = levels
        self.lock = threading.RLock()

    def get(self, key: Any, default: Any = None) -> Any:
        """
        获取缓存值

        从高优先级缓存开始查找，如果找到则更新低优先级缓存

        Args:
            key: 缓存键
            default: 默认值

        Returns:
            缓存值或默认值
        """
        with self.lock:
            # 从高优先级缓存开始查找
            for i, cache in enumerate(self.levels):
                value = cache.get(key, None)

                if value is not None:
                    # 找到值，更新低优先级缓存
                    for j in range(i):
                        self.levels[j].put(key, value)
                    return value

            # 所有缓存都未命中
            return default

    def put(self, key: Any, value: Any, ttl: Optional[float] = None) -> None:
        """
        添加或更新缓存

        更新所有缓存层级

        Args:
            key: 缓存键
            value: 缓存值
            ttl: 生存时间（秒）
        """
        with self.lock:
            # 更新所有缓存层级
            for cache in self.levels:
                cache.put(key, value, ttl)

    def get_stats(self) -> List[Dict[str, Any]]:
        """
        获取所有缓存层级的统计信息

        Returns:
            List[Dict[str, Any]]: 统计信息列表
        """
        return [cache.get_stats() for cache in self.levels]


class StateSimilarityCache(Cache):
    """
    状态相似度缓存类

    支持基于状态相似度的缓存查找，适用于连续状态空间。
    """

    def __init__(
        self,
        capacity: int = 1000,
        ttl: Optional[float] = None,
        similarity_threshold: float = 0.95,
        similarity_func: Optional[Callable[[Any, Any], float]] = None
    ):
        """
        初始化状态相似度缓存

        Args:
            capacity: 缓存容量
            ttl: 默认生存时间（秒）
            similarity_threshold: 相似度阈值，高于此值视为相似
            similarity_func: 相似度计算函数，如果为None则使用默认函数
        """
        super().__init__(capacity, ttl)
        self.similarity_threshold = similarity_threshold
        self.similarity_func = similarity_func or self._default_similarity

        # 相似度缓存命中统计
        self.similarity_hit_count = 0

    def get(self, key: Any, default: Any = None) -> Any:
        """
        获取缓存值

        首先尝试精确匹配，然后尝试相似度匹配

        Args:
            key: 缓存键
            default: 默认值

        Returns:
            缓存值或默认值
        """
        with self.lock:
            self.access_count += 1

            # 尝试精确匹配
            if key in self.cache:
                entry = self.cache[key]
                if not entry.is_expired(time.time()):
                    entry.access_count += 1
                    self.hit_count += 1
                    return entry.value
                else:
                    del self.cache[key]

            # 尝试相似度匹配
            best_match = None
            best_similarity = 0.0

            for cached_key, entry in self.cache.items():
                # 跳过过期条目
                if entry.is_expired(time.time()):
                    continue

                # 计算相似度
                similarity = self.similarity_func(key, cached_key)

                # 更新最佳匹配
                if similarity > best_similarity and similarity >= self.similarity_threshold:
                    best_similarity = similarity
                    best_match = entry

            # 如果找到相似匹配
            if best_match:
                best_match.access_count += 1
                self.similarity_hit_count += 1
                return best_match.value

            # 未找到匹配
            self.miss_count += 1
            return default

    def _default_similarity(self, state1: Any, state2: Any) -> float:
        """
        默认相似度计算函数

        支持numpy数组、列表、字典和基本类型

        Args:
            state1: 第一个状态
            state2: 第二个状态

        Returns:
            float: 相似度，范围[0, 1]
        """
        # 处理numpy数组
        if isinstance(state1, np.ndarray) and isinstance(state2, np.ndarray):
            if state1.shape != state2.shape:
                return 0.0

            # 计算余弦相似度
            dot_product = np.sum(state1 * state2)
            norm1 = np.sqrt(np.sum(state1 * state1))
            norm2 = np.sqrt(np.sum(state2 * state2))

            if norm1 == 0 or norm2 == 0:
                return 1.0 if norm1 == norm2 else 0.0

            return dot_product / (norm1 * norm2)

        # 处理列表或元组
        elif (isinstance(state1, (list, tuple)) and
              isinstance(state2, (list, tuple))):
            if len(state1) != len(state2):
                return 0.0

            # 递归计算每个元素的相似度
            similarities = [
                self._default_similarity(s1, s2)
                for s1, s2 in zip(state1, state2)
            ]
            return sum(similarities) / len(similarities) if similarities else 1.0

        # 处理字典
        elif isinstance(state1, dict) and isinstance(state2, dict):
            # 获取共同的键
            common_keys = set(state1.keys()) & set(state2.keys())
            all_keys = set(state1.keys()) | set(state2.keys())

            if not common_keys:
                return 0.0

            # 计算共同键的相似度
            key_ratio = len(common_keys) / len(all_keys)
            value_similarities = [
                self._default_similarity(state1[k], state2[k])
                for k in common_keys
            ]
            value_similarity = sum(value_similarities) / len(value_similarities) if value_similarities else 0.0

            return key_ratio * value_similarity

        # 处理基本类型
        else:
            return 1.0 if state1 == state2 else 0.0

    def get_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息

        Returns:
            Dict[str, Any]: 统计信息字典
        """
        stats = super().get_stats()

        with self.lock:
            total_hits = self.hit_count + self.similarity_hit_count
            similarity_hit_rate = self.similarity_hit_count / self.access_count if self.access_count > 0 else 0.0

            stats.update({
                "similarity_hit_count": self.similarity_hit_count,
                "similarity_hit_rate": similarity_hit_rate,
                "total_hit_rate": total_hits / self.access_count if self.access_count > 0 else 0.0,
                "similarity_threshold": self.similarity_threshold
            })

        return stats