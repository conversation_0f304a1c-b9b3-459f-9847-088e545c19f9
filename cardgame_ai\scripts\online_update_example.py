#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
在线更新示例脚本

展示如何使用在线更新器对模型进行持续更新，形成闭环。
"""

import os
import sys
import argparse
import logging
import time
from typing import Dict, List, Any, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from cardgame_ai.training.online_updater import OnlineUpdater, run_online_updater
from cardgame_ai.algorithms.efficient_zero import EfficientZero
from cardgame_ai.algorithms.enhanced_mappo import EnhancedMAPPO
from cardgame_ai.algorithms.integrated_ai_system import IntegratedAISystem
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='在线更新示例')
    
    parser.add_argument('--model_type', type=str, default='efficient_zero',
                        choices=['efficient_zero', 'enhanced_mappo', 'integrated'],
                        help='模型类型')
    parser.add_argument('--model_path', type=str, default=None,
                        help='模型路径')
    parser.add_argument('--trajectory_dir', type=str, default='data/trajectories',
                        help='轨迹目录路径')
    parser.add_argument('--update_interval', type=int, default=60,
                        help='更新间隔（秒）')
    parser.add_argument('--batch_size', type=int, default=32,
                        help='批次大小')
    parser.add_argument('--updates_per_trajectory', type=int, default=5,
                        help='每个轨迹的更新次数')
    parser.add_argument('--max_trajectories_per_update', type=int, default=10,
                        help='每次更新的最大轨迹数')
    parser.add_argument('--max_runtime', type=int, default=3600,
                        help='最大运行时间（秒）')
    parser.add_argument('--use_experience_format', action='store_true',
                        help='是否使用Experience格式')
    parser.add_argument('--file_pattern', type=str, default='*.json',
                        help='轨迹文件模式')
    
    return parser.parse_args()


def create_model(args):
    """创建模型"""
    # 创建游戏环境
    env = DouDizhuEnvironment()
    
    # 获取观察和动作空间
    observation_shape = env.observation_space.shape
    action_shape = (env.action_space.n,)
    
    # 创建模型
    if args.model_type == 'efficient_zero':
        model = EfficientZero(
            state_shape=observation_shape,
            action_shape=action_shape,
            hidden_dim=128,
            state_dim=64,
            use_resnet=False
        )
    elif args.model_type == 'enhanced_mappo':
        model = EnhancedMAPPO(
            state_dim=observation_shape[0],
            action_dim=env.action_space.n,
            hidden_dim=128,
            use_belief_state=True
        )
    elif args.model_type == 'integrated':
        model = IntegratedAISystem(
            state_shape=observation_shape,
            action_shape=action_shape,
            hidden_dim=128,
            state_dim=64,
            use_resnet=False,
            use_belief_state=True
        )
    else:
        raise ValueError(f"未知的模型类型: {args.model_type}")
    
    # 如果有预训练模型，加载参数
    if args.model_path:
        model.load(args.model_path)
        logger.info(f"已加载预训练模型: {args.model_path}")
    
    return model


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 创建模型
    model = create_model(args)
    
    # 创建在线更新器
    updater = OnlineUpdater(
        model=model,
        trajectory_dir=args.trajectory_dir,
        update_interval=args.update_interval,
        batch_size=args.batch_size,
        updates_per_trajectory=args.updates_per_trajectory,
        max_trajectories_per_update=args.max_trajectories_per_update,
        use_experience_format=args.use_experience_format,
        file_pattern=args.file_pattern
    )
    
    # 记录开始时间
    start_time = time.time()
    
    logger.info(f"开始运行在线更新器: {args.trajectory_dir}")
    logger.info(f"模型类型: {args.model_type}")
    logger.info(f"更新间隔: {args.update_interval}秒")
    logger.info(f"最大运行时间: {args.max_runtime}秒")
    
    try:
        while True:
            # 检查是否达到最大运行时间
            if args.max_runtime is not None and time.time() - start_time > args.max_runtime:
                logger.info(f"达到最大运行时间: {args.max_runtime}秒")
                break
            
            # 检查并更新模型
            stats = updater.check_and_update()
            
            # 输出统计信息
            if stats["total_updates"] > 0:
                logger.info(f"更新统计: 总更新次数={stats['total_updates']}, "
                           f"总轨迹数={stats['total_trajectories']}, "
                           f"总步骤数={stats['total_steps']}")
            
            # 等待下一次更新
            time.sleep(1)
    
    except KeyboardInterrupt:
        logger.info("用户中断")
    
    except Exception as e:
        logger.error(f"运行在线更新器时出错: {e}")
    
    finally:
        # 输出最终统计信息
        stats = updater.stats
        elapsed_time = time.time() - start_time
        
        logger.info(f"在线更新器运行结束")
        logger.info(f"运行时间: {elapsed_time:.2f}秒")
        logger.info(f"总更新次数: {stats['total_updates']}")
        logger.info(f"总轨迹数: {stats['total_trajectories']}")
        logger.info(f"总步骤数: {stats['total_steps']}")
        
        if elapsed_time > 0:
            logger.info(f"更新速率: {stats['total_updates'] / elapsed_time:.2f}次/秒")
            logger.info(f"轨迹处理速率: {stats['total_trajectories'] / elapsed_time:.2f}个/秒")
        
        # 保存更新后的模型
        if args.model_path:
            save_path = args.model_path + ".updated"
        else:
            save_path = f"models/{args.model_type}_updated.pt"
        
        # 确保目录存在
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        
        # 保存模型
        model.save(save_path)
        logger.info(f"已保存更新后的模型: {save_path}")


def run_with_helper():
    """使用辅助函数运行在线更新器"""
    # 解析命令行参数
    args = parse_args()
    
    # 创建模型
    model = create_model(args)
    
    # 运行在线更新器
    run_online_updater(
        model=model,
        trajectory_dir=args.trajectory_dir,
        update_interval=args.update_interval,
        max_runtime=args.max_runtime
    )
    
    # 保存更新后的模型
    if args.model_path:
        save_path = args.model_path + ".updated"
    else:
        save_path = f"models/{args.model_type}_updated.pt"
    
    # 确保目录存在
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    
    # 保存模型
    model.save(save_path)
    logger.info(f"已保存更新后的模型: {save_path}")


if __name__ == "__main__":
    # 使用两种方式之一运行
    # main()  # 直接使用OnlineUpdater类
    run_with_helper()  # 使用辅助函数
