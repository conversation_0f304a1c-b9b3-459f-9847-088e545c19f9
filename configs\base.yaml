# 斗地主AI优化项目 - 基础配置文件
# 此文件定义了项目的核心配置参数

defaults:
  - algorithms/efficient_zero: base
  - training: optimized
  - environments/doudizhu: efficient_zero_config
  - hardware: single_gpu
  - _self_

# 项目基本信息
project:
  name: "doudizhu_ai_optimization"
  version: "1.0.0"
  description: "斗地主AI优化项目 - 超人类水平的多智能体协作AI系统"
  author: "AI Development Team"

# 设备配置
device:
  type: "cuda"  # cuda, cpu, auto
  ids: [0, 1, 2, 3]  # GPU设备ID列表
  mixed_precision: true  # 启用混合精度训练
  deterministic: false  # 是否使用确定性算法(影响性能)
  benchmark: true  # 启用cudnn benchmark优化

# 日志配置
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  dir: "logs"
  format: "json"  # json, text
  rotation: "1 day"  # 日志轮转周期
  retention: "30 days"  # 日志保留时间
  structured: true  # 启用结构化日志
  
  # 组件日志级别
  components:
    training: "INFO"
    evaluation: "INFO"
    algorithms: "DEBUG"
    distributed: "INFO"
    monitoring: "INFO"

# 数据配置
data:
  root_dir: "data"
  cache_dir: "${data.root_dir}/cache"
  processed_dir: "${data.root_dir}/processed"
  raw_dir: "${data.root_dir}/raw"
  
  # 数据格式
  format: "hdf5"  # hdf5, pickle, json
  compression: "gzip"
  
  # 数据加载
  num_workers: 4
  pin_memory: true
  prefetch_factor: 2

# 模型配置
model:
  save_dir: "models"
  checkpoint_dir: "${model.save_dir}/checkpoints"
  pretrained_dir: "${model.save_dir}/pretrained"
  export_dir: "${model.save_dir}/exports"
  
  # 检查点管理
  save_frequency: 1000  # 每N步保存一次
  keep_last: 5  # 保留最近N个检查点
  save_best: true  # 保存最佳性能模型
  
  # 模型版本控制
  versioning: true
  metadata: true  # 保存模型元数据

# 分布式配置
distributed:
  backend: "ray"  # ray, torch
  
  # Ray配置
  ray:
    address: null  # Ray集群地址，null表示本地模式
    runtime_env:
      pip: []
      env_vars: {}
    resources:
      num_cpus: null  # null表示使用所有可用CPU
      num_gpus: null  # null表示使用所有可用GPU
      memory: null
      object_store_memory: null
  
  # 通信配置
  communication:
    timeout: 300  # 通信超时时间(秒)
    retry_attempts: 3
    compression: true

# 监控配置
monitoring:
  enabled: true
  
  # TensorBoard
  tensorboard:
    enabled: true
    log_dir: "${logging.dir}/tensorboard"
    update_frequency: 100  # 每N步更新一次
  
  # Weights & Biases (可选)
  wandb:
    enabled: false
    project: "${project.name}"
    entity: null
    tags: []
    notes: ""
  
  # 系统监控
  system:
    enabled: true
    interval: 10  # 监控间隔(秒)
    metrics:
      - cpu_usage
      - memory_usage
      - gpu_usage
      - gpu_memory
      - disk_usage
      - network_io

# 实验配置
experiment:
  name: "default"
  tags: []
  notes: ""
  
  # 随机种子
  seed: 42
  deterministic: false
  
  # 实验跟踪
  tracking:
    enabled: true
    save_config: true
    save_code: true
    save_artifacts: true

# 性能配置
performance:
  # 内存优化
  memory:
    gradient_accumulation: 1
    max_memory_usage: 0.9  # 最大内存使用率
    clear_cache_frequency: 1000  # 清理缓存频率
  
  # 计算优化
  compute:
    compile_model: false  # 启用模型编译(PyTorch 2.0+)
    use_flash_attention: false  # 使用Flash Attention
    gradient_checkpointing: false  # 梯度检查点
  
  # 数据加载优化
  dataloader:
    persistent_workers: true
    multiprocessing_context: "spawn"

# 安全配置
security:
  # 输入验证
  validation:
    strict_mode: true
    max_input_size: 1048576  # 1MB
  
  # 资源限制
  limits:
    max_memory_per_process: "8GB"
    max_gpu_memory_per_process: "16GB"
    max_training_time: "24h"
  
  # 审计日志
  audit:
    enabled: true
    log_level: "INFO"

# 调试配置
debug:
  enabled: false
  
  # 性能分析
  profiling:
    enabled: false
    output_dir: "${logging.dir}/profiling"
    profile_memory: true
    profile_gpu: true
  
  # 调试选项
  options:
    detect_anomaly: false  # PyTorch异常检测
    warn_on_missing_keys: true
    strict_loading: true

# 环境特定配置覆盖
env_overrides:
  development:
    logging.level: "DEBUG"
    debug.enabled: true
    monitoring.wandb.enabled: false
  
  staging:
    logging.level: "INFO"
    debug.enabled: false
    monitoring.wandb.enabled: true
  
  production:
    logging.level: "WARNING"
    debug.enabled: false
    monitoring.wandb.enabled: true
    security.validation.strict_mode: true

# Hydra配置
hydra:
  run:
    dir: "outputs/${now:%Y-%m-%d}/${now:%H-%M-%S}"
  sweep:
    dir: "multirun/${now:%Y-%m-%d}/${now:%H-%M-%S}"
    subdir: "${hydra.job.num}"
  launcher:
    _target_: hydra._internal.BasicLauncher
  sweeper:
    _target_: hydra._internal.BasicSweeper
    max_jobs: null
  help:
    app_name: ${project.name}
    header: "斗地主AI优化项目配置"
  hydra_logging:
    version: 1
    formatters:
      simple:
        format: '[%(levelname)s] - %(message)s'
    handlers:
      console:
        class: logging.StreamHandler
        formatter: simple
        stream: ext://sys.stdout
    root:
      level: INFO
      handlers: [console]
    disable_existing_loggers: false
  job_logging:
    version: 1
    formatters:
      simple:
        format: '[%(levelname)s] - %(message)s'
    handlers:
      console:
        class: logging.StreamHandler
        formatter: simple
        stream: ext://sys.stdout
      file:
        class: logging.FileHandler
        formatter: simple
        filename: ${hydra.runtime.output_dir}/${hydra.job.name}.log
    root:
      level: INFO
      handlers: [console, file]
    disable_existing_loggers: false
