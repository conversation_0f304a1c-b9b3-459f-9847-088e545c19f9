# 优化训练配置文件
# 针对4张2080Ti的高性能训练配置

# 设备配置
device:
  type: "cuda"
  mixed_precision: true
  amp_level: "O1"              # 混合精度级别
  compile_model: true          # PyTorch 2.0编译优化
  channels_last: true          # 内存格式优化
  benchmark: true              # cuDNN基准测试

# 数据流水线优化
data:
  # 数据加载器配置
  num_workers: 16              # 数据加载线程数（从4提升到16）
  pin_memory: true             # 固定内存
  prefetch_factor: 8           # 预取因子（从2提升到8）
  persistent_workers: true     # 持久化worker
  multiprocessing_context: "spawn"
  
  # 缓存配置
  cache_size_gb: 12            # 特征缓存大小12GB
  use_memory_map: true         # 启用内存映射
  preload_ratio: 0.3           # 预加载30%数据
  
  # 存储优化
  format: "hdf5"               # 使用HDF5格式
  compression: "lz4"           # LZ4压缩
  chunk_size: 1024             # 数据块大小

# 训练优化配置
training:
  # 动态批次大小
  batch_size:
    base: 320                  # 基础批次大小（从256提升到320）
    min: 192                   # 最小批次大小
    max: 448                   # 最大批次大小
    auto_scale: true           # 启用自动缩放
    memory_threshold: 0.85     # GPU内存使用阈值
  
  # 学习率优化
  learning_rate:
    initial: 0.0008            # 初始学习率（从0.0005提升）
    warmup_steps: 2000         # 预热步数
    scheduler: "cosine_with_restarts"
    min_lr: 1e-7               # 最小学习率
    restart_period: 10000      # 重启周期
    patience: 20               # 耐心值
    factor: 0.8                # 衰减因子
  
  # 优化器配置
  optimizer:
    type: "adamw"              # 使用AdamW优化器
    weight_decay: 1e-4         # 权重衰减
    betas: [0.9, 0.999]        # Adam参数
    eps: 1e-8
  
  # 梯度优化
  gradient:
    clip_norm: 5.0             # 梯度裁剪（从10.0降低到5.0）
    accumulation_steps: 2      # 梯度累积步数
    sync_bn: true              # 同步BatchNorm

# MCTS搜索优化
mcts:
  # 基础搜索参数
  num_simulations: 150         # 模拟次数（从100提升到150）
  max_simulations: 300         # 最大模拟次数
  
  # UCB公式优化
  c_puct: 1.4                  # UCB常数（从1.25提升）
  pb_c_base: 25000             # 从19652提升
  pb_c_init: 1.5               # 从1.25提升
  
  # 并行搜索
  parallel_threads: 6          # 并行搜索线程数
  batch_size_inference: 24     # 批量推理大小（从16提升）
  
  # 动态预算分配
  dynamic_budget: true
  budget_allocation:
    critical_moments: 2.0      # 关键时刻2倍预算
    early_game: 0.8           # 前期80%预算
    mid_game: 1.2             # 中期120%预算
    late_game: 1.5            # 后期150%预算
  
  # 搜索优化
  optimizations:
    ucb_caching: true          # UCB值缓存
    tree_pruning: true         # 搜索树剪枝
    node_pooling: true         # 节点池化
    path_compression: true     # 路径压缩
    node_pool_size: 15000      # 节点池大小

# 内存优化
memory:
  max_memory_usage: 0.9        # 最大内存使用率
  gradient_checkpointing: true # 梯度检查点
  empty_cache_frequency: 500   # 清空缓存频率
  gc_frequency: 1000           # 垃圾回收频率

# 分布式训练配置
distributed:
  # 同步训练配置
  sync_frequency: 10           # 模型同步频率
  async_update: false          # 使用同步更新
  data_parallel: true          # 数据并行
  
  # 通信优化
  backend: "nccl"              # 通信后端
  bucket_size_mb: 25           # 通信桶大小
  
  # 负载均衡
  dynamic_loss_scaling: true   # 动态损失缩放
  find_unused_parameters: false

# 模型配置优化
model:
  # 网络架构
  representation:
    blocks: 8                  # ResNet块数（从6提升到8）
    channels: 320              # 通道数（从256提升到320）
    dropout: 0.1               # Dropout率
  
  dynamics:
    blocks: 8
    channels: 320
    dropout: 0.1
  
  prediction:
    blocks: 8
    channels: 320
    dropout: 0.1
  
  # 价值网络
  value_network:
    type: "distributional"
    support_size: 601          # 支持大小
    hidden_size: 512           # 隐藏层大小

# 多智能体协作优化
multi_agent:
  farmer_cooperation:
    cooperation_weight: 0.8    # 协作权重
    team_reward_weight: 0.9    # 团队奖励权重
    communication_enabled: true # 启用通信
  
  role_specialization:
    enable_specialization: true # 启用角色专门化
    position_encoding: true     # 位置编码

# 经验回放优化
replay_buffer:
  size: 1000000               # 缓冲区大小
  priority_alpha: 0.6         # 优先级参数
  priority_beta: 0.4          # 重要性权重参数
  priority_epsilon: 1e-6      # 优先级epsilon
  
  # 采样优化
  batch_sampling: "prioritized" # 优先级采样
  replacement: false          # 无放回采样
  
  # 数据增强
  augmentation:
    rotation: true            # 旋转增强
    noise_level: 0.01         # 噪声水平

# 日志和监控
logging:
  level: "INFO"
  log_interval: 100           # 日志间隔
  save_interval: 1000         # 保存间隔
  eval_interval: 5000         # 评估间隔
  
  # 性能监控
  performance_monitoring: true
  monitor_interval: 1.0       # 监控间隔
  
  # TensorBoard
  tensorboard:
    enabled: true
    log_dir: "logs/tensorboard"
    update_freq: 100

# 检查点和保存
checkpoint:
  save_frequency: 2000        # 保存频率
  max_checkpoints: 5          # 最大检查点数
  save_optimizer: true        # 保存优化器状态
  compression: true           # 压缩检查点
  
  # 模型导出
  export_onnx: false          # 导出ONNX格式
  export_torchscript: false   # 导出TorchScript

# 评估配置
evaluation:
  frequency: 10000            # 评估频率
  num_games: 100              # 评估游戏数
  opponents: ["random", "rule_based", "previous_model"]
  
  # 性能指标
  metrics:
    - "win_rate"
    - "average_score"
    - "decision_time"
    - "mcts_depth"

# 实验配置
experiment:
  name: "optimized_training_4x2080ti"
  description: "4张2080Ti优化训练配置"
  tags: ["optimization", "performance", "sync_training"]
  
  # 随机种子
  seed: 42
  deterministic: true         # 确定性训练
  
  # 早停
  early_stopping:
    enabled: true
    patience: 50000           # 早停耐心值
    min_delta: 0.001          # 最小改善
