"""
优化的混合决策系统模块

提供优化的混合决策系统，整合多种算法组件，根据状态特征动态选择最适合的组件，
提高决策质量和系统性能。
"""

import os
import time
import logging
import threading
import numpy as np
from typing import Dict, Any, List, Optional, Callable, Tuple, Union

from cardgame_ai.core.base import State, Action
from cardgame_ai.core.component_manager import ComponentManager
from cardgame_ai.core.component_selector import SmartComponentSelector
from cardgame_ai.core.cache_framework import Cache, StateSimilarityCache, MultiLevelCache
from cardgame_ai.core.resource_allocator import DynamicResourceAllocator

# 设置日志
logger = logging.getLogger(__name__)


class OptimizedHybridDecisionSystem:
    """
    优化的混合决策系统类

    整合多种算法组件，根据状态特征动态选择最适合的组件，
    提高决策质量和系统性能。
    """

    def __init__(
        self,
        component_types: List[str],
        feature_extractor: Optional[Callable[[State], np.ndarray]] = None,
        cache_capacity: int = 10000,
        exploration_factor: float = 0.1,
        use_resource_allocation: bool = True,
        use_similarity_cache: bool = True
    ):
        """
        初始化优化的混合决策系统

        Args:
            component_types: 组件类型列表
            feature_extractor: 特征提取函数
            cache_capacity: 缓存容量
            exploration_factor: 探索因子
            use_resource_allocation: 是否使用资源分配
            use_similarity_cache: 是否使用相似度缓存
        """
        # 保存参数
        self.component_types = component_types
        self.cache_capacity = cache_capacity
        self.use_resource_allocation = use_resource_allocation
        self.use_similarity_cache = use_similarity_cache

        # 获取组件管理器
        self.component_manager = ComponentManager.get_instance()

        # 创建组件选择器
        self.component_selector = SmartComponentSelector(
            components=component_types,
            feature_extractor=feature_extractor,
            exploration_factor=exploration_factor
        )

        # 创建缓存系统
        if use_similarity_cache:
            # 使用多级缓存：精确匹配缓存 + 相似度缓存
            exact_cache = Cache(capacity=cache_capacity)
            similarity_cache = StateSimilarityCache(
                capacity=cache_capacity // 2,
                similarity_threshold=0.9
            )
            self.cache = MultiLevelCache([exact_cache, similarity_cache])
        else:
            # 仅使用精确匹配缓存
            self.cache = Cache(capacity=cache_capacity)

        # 创建资源分配器
        if use_resource_allocation:
            self.resource_allocator = DynamicResourceAllocator()
        else:
            self.resource_allocator = None

        # 性能统计
        self.stats = {
            "total_decisions": 0,
            "cache_hits": 0,
            "component_usage": {comp_type: 0 for comp_type in component_types},
            "avg_decision_time": 0.0,
            "total_decision_time": 0.0
        }

        # 锁
        self.lock = threading.RLock()

        logger.info(f"优化的混合决策系统已初始化，组件类型: {component_types}")

    def decide(self, state: State) -> Action:
        """
        做出决策

        首先尝试从缓存中获取结果，如果缓存未命中，则选择合适的组件进行决策

        Args:
            state: 当前状态

        Returns:
            Action: 选择的动作
        """
        start_time = time.time()

        with self.lock:
            # 更新统计信息
            self.stats["total_decisions"] += 1

            # 尝试从缓存中获取结果
            cache_key = self._get_cache_key(state)
            cached_action = self.cache.get(cache_key)

            if cached_action is not None:
                # 缓存命中
                self.stats["cache_hits"] += 1
                decision_time = time.time() - start_time
                self._update_time_stats(decision_time)
                return cached_action

            # 缓存未命中，选择合适的组件
            component_type = self.component_selector.select_component(state)
            self.stats["component_usage"][component_type] += 1

            # 分配资源
            task_id = f"decide_{id(state)}_{time.time()}"
            resources = self._allocate_resources(task_id, state, component_type)

            try:
                # 获取组件
                component = self.component_manager.get_component(
                    component_type,
                    resources=resources
                )

                # 使用组件做出决策
                action = component.decide(state)

                # 缓存结果
                self.cache.put(cache_key, action)

                # 记录性能
                decision_time = time.time() - start_time
                self._update_performance(component_type, state, action, decision_time)

                return action
            finally:
                # 释放资源
                if self.resource_allocator and resources:
                    self.resource_allocator.release_resources(task_id)

    def _get_cache_key(self, state: State) -> str:
        """
        获取缓存键

        Args:
            state: 状态

        Returns:
            str: 缓存键
        """
        # 如果状态有to_cache_key方法，使用它
        if hasattr(state, "to_cache_key"):
            return state.to_cache_key()

        # 否则使用状态的字符串表示
        return str(state)

    def _allocate_resources(
        self,
        task_id: str,
        state: State,
        component_type: str
    ) -> Dict[str, Any]:
        """
        分配资源

        Args:
            task_id: 任务ID
            state: 状态
            component_type: 组件类型

        Returns:
            Dict[str, Any]: 资源分配结果
        """
        if not self.resource_allocator:
            return {}

        # 估计任务复杂度
        complexity = self._estimate_complexity(state, component_type)

        # 分配资源
        return self.resource_allocator.allocate_resources(
            task_id=task_id,
            task_complexity=complexity,
            priority=1.0  # 默认优先级
        )

    def _estimate_complexity(self, state: State, component_type: str) -> float:
        """
        估计任务复杂度

        Args:
            state: 状态
            component_type: 组件类型

        Returns:
            float: 复杂度，范围[0, 1]
        """
        # 如果状态有get_complexity方法，使用它
        if hasattr(state, "get_complexity"):
            return state.get_complexity()

        # 否则根据组件类型和状态特征估计
        if component_type == "mcts":
            # MCTS复杂度与可用动作数量和游戏阶段有关
            if hasattr(state, "get_legal_actions") and hasattr(state, "get_game_stage"):
                actions = state.get_legal_actions()
                stage = state.get_game_stage()
                return min(1.0, (len(actions) / 20.0) * (stage / 3.0 + 0.5))

        # 默认中等复杂度
        return 0.5

    def _update_performance(
        self,
        component_type: str,
        state: State,
        action: Action,
        decision_time: float
    ) -> None:
        """
        更新组件性能

        Args:
            component_type: 组件类型
            state: 状态
            action: 动作
            decision_time: 决策时间
        """
        # 更新时间统计
        self._update_time_stats(decision_time)

        # 估计奖励（如果可能）
        reward = 0.0
        if hasattr(state, "estimate_action_value"):
            reward = state.estimate_action_value(action)

        # 更新组件选择器的性能记录
        self.component_selector.update_performance(
            component_id=component_type,
            state=state,
            performance={
                "reward": reward,
                "time": decision_time,
                "complexity": self._estimate_complexity(state, component_type)
            }
        )

    def _update_time_stats(self, decision_time: float) -> None:
        """
        更新时间统计

        Args:
            decision_time: 决策时间
        """
        # 更新总决策时间
        self.stats["total_decision_time"] += decision_time

        # 更新平均决策时间
        total_decisions = self.stats["total_decisions"]
        if total_decisions > 0:
            self.stats["avg_decision_time"] = (
                self.stats["total_decision_time"] / total_decisions
            )

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            Dict[str, Any]: 统计信息字典
        """
        with self.lock:
            # 复制统计信息
            stats_copy = self.stats.copy()

            # 添加缓存统计
            if hasattr(self.cache, "get_stats"):
                stats_copy["cache_stats"] = self.cache.get_stats()

            # 添加组件选择器统计
            stats_copy["component_selector_stats"] = self.component_selector.get_stats()

            # 添加资源分配器统计
            if self.resource_allocator:
                stats_copy["resource_allocator_stats"] = {
                    "total_allocated": self.resource_allocator.get_total_allocated_resources()
                }

            return stats_copy

    def clear_cache(self) -> None:
        """清除缓存"""
        with self.lock:
            if hasattr(self.cache, "clear"):
                self.cache.clear()
                logger.info("已清除缓存")

    def reset_stats(self) -> None:
        """重置统计信息"""
        with self.lock:
            self.stats = {
                "total_decisions": 0,
                "cache_hits": 0,
                "component_usage": {comp_type: 0 for comp_type in self.component_types},
                "avg_decision_time": 0.0,
                "total_decision_time": 0.0
            }
            logger.info("已重置统计信息")
