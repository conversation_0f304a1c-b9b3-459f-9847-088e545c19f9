#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
符号推理组件示例脚本

展示如何使用符号推理组件。
"""

import os
import sys
import argparse
import logging
import numpy as np
import torch
from typing import Dict, List, Any, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from cardgame_ai.algorithms.hybrid_decision_system import HybridDecisionSystem
from cardgame_ai.algorithms.key_moment_detector import KeyMomentDetector
from cardgame_ai.algorithms.symbolic_reasoning import SymbolicReasoningComponent
from cardgame_ai.algorithms.efficient_zero import EfficientZero
from cardgame_ai.algorithms.rule_based import RuleBasedAgent
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.games.doudizhu.state import DouDizhuState
from cardgame_ai.games.doudizhu.card import Card, CardRank, CardSuit
from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType
from cardgame_ai.algorithms.endgame_modules import (
    is_endgame, get_endgame_type, EndgameType
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='符号推理组件示例')
    
    parser.add_argument('--model_path', type=str, default=None,
                        help='模型路径')
    parser.add_argument('--key_moment_model', type=str, default=None,
                        help='关键决策点检测器模型路径')
    parser.add_argument('--num_games', type=int, default=5,
                        help='游戏数量')
    parser.add_argument('--seed', type=int, default=None,
                        help='随机种子')
    
    return parser.parse_args()


def create_symbolic_test_state() -> DouDizhuState:
    """
    创建用于测试符号推理的状态
    
    Returns:
        DouDizhuState: 测试状态
    """
    # 创建一个基本的状态
    hands = [[], [], []]
    landlord_cards = []
    
    # 设置手牌
    # 玩家0（地主）：单张A、单张K、单张Q
    hands[0] = [
        Card(CardRank.ACE, CardSuit.SPADE),
        Card(CardRank.KING, CardSuit.HEART),
        Card(CardRank.QUEEN, CardSuit.DIAMOND)
    ]
    # 玩家1（农民）：单张J、单张10
    hands[1] = [
        Card(CardRank.JACK, CardSuit.CLUB),
        Card(CardRank.TEN, CardSuit.SPADE)
    ]
    # 玩家2（农民）：单张9
    hands[2] = [
        Card(CardRank.NINE, CardSuit.HEART)
    ]
    
    # 创建状态
    state = DouDizhuState(
        hands=hands,
        landlord_cards=landlord_cards,
        landlord=0,  # 玩家0为地主
        current_player=0,  # 当前玩家为玩家0
        game_phase=DouDizhuState.GamePhase.PLAYING,  # 出牌阶段
        bid_history=[],
        grab_history=[],
        highest_bidder=0,
        highest_bid=3,
        last_move=None,
        last_player=0,  # 上一个出牌的玩家为玩家0
        num_passes=0,
        history=[],
        played_cards=[]
    )
    
    return state


def test_symbolic_reasoning(hybrid_system: HybridDecisionSystem):
    """
    测试符号推理组件
    
    Args:
        hybrid_system: 混合决策系统
    """
    # 创建测试状态
    state = create_symbolic_test_state()
    
    # 获取合法动作
    legal_actions = state.get_legal_actions()
    
    # 使用混合决策系统做出决策
    action = hybrid_system.act(state, legal_actions)
    
    # 打印决策结果
    if action:
        logger.info(f"决策动作: {action.card_type.name}")
        if action.cards:
            logger.info(f"决策牌: {[card.rank.name for card in action.cards]}")
    else:
        logger.info("决策动作: 不出")
    
    # 打印统计信息
    stats = hybrid_system.get_stats()
    
    # 打印符号推理统计
    if "symbolic_reasoning" in stats:
        symbolic_stats = stats["symbolic_reasoning"]
        logger.info("\n符号推理统计:")
        logger.info(f"  调用次数: {symbolic_stats['calls']}")
        logger.info(f"  成功解决次数: {symbolic_stats['successful_solves']}")
        logger.info(f"  符号推理决策次数: {symbolic_stats['symbolic_decisions']}")
        logger.info(f"  符号推理决策比例: {symbolic_stats['symbolic_ratio']:.2f}")
        logger.info("  解决类型统计:")
        for solve_type, count in symbolic_stats['solve_types'].items():
            logger.info(f"    {solve_type}: {count}")


def simulate_game(hybrid_system: HybridDecisionSystem, env: DouDizhuEnvironment):
    """
    模拟一局游戏
    
    Args:
        hybrid_system: 混合决策系统
        env: 游戏环境
    """
    # 重置环境
    state = env.reset()
    done = False
    total_reward = 0
    
    # 游戏循环
    while not done:
        # 获取合法动作
        legal_actions = env.get_legal_actions()
        
        # 使用混合决策系统做出决策
        action = hybrid_system.act(state, legal_actions)
        
        # 执行动作
        state, reward, done, info = env.step(action)
        total_reward += reward
    
    # 游戏结束，更新奖励
    hybrid_system.update_reward(total_reward)
    
    # 打印游戏结果
    logger.info(f"游戏结束，总奖励: {total_reward}")


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 设置随机种子
    if args.seed is not None:
        np.random.seed(args.seed)
        torch.manual_seed(args.seed)
    
    # 创建游戏环境
    env = DouDizhuEnvironment()
    
    # 创建模型
    model = EfficientZero(model_path=args.model_path)
    
    # 创建规则代理
    rule_agent = RuleBasedAgent()
    
    # 创建关键决策点检测器
    key_moment_detector = None
    if args.key_moment_model:
        key_moment_detector = KeyMomentDetector.load(args.key_moment_model)
        logger.info(f"已加载关键决策点检测器: {args.key_moment_model}")
    
    # 创建符号推理组件
    symbolic_component = SymbolicReasoningComponent(
        use_guaranteed_win_solver=True,
        use_card_counting=True
    )
    logger.info("已创建符号推理组件")
    
    # 创建混合决策系统
    hybrid_system = HybridDecisionSystem(
        neural_network_model=model,
        search_model=model,
        rule_agent=rule_agent,
        key_moment_detector=key_moment_detector,
        symbolic_component=symbolic_component,
        meta_strategy="adaptive"
    )
    
    # 测试符号推理组件
    logger.info("\n测试符号推理组件")
    test_symbolic_reasoning(hybrid_system)
    
    # 模拟游戏
    logger.info("\n开始模拟游戏")
    for i in range(args.num_games):
        logger.info(f"开始游戏 {i+1}/{args.num_games}")
        simulate_game(hybrid_system, env)
    
    # 打印最终统计信息
    stats = hybrid_system.get_stats()
    logger.info(f"\n总决策次数: {stats['decisions']}")
    
    if "symbolic_reasoning" in stats:
        symbolic_stats = stats["symbolic_reasoning"]
        logger.info(f"符号推理决策次数: {symbolic_stats['symbolic_decisions']}")
        logger.info(f"符号推理决策比例: {symbolic_stats['symbolic_ratio']:.2f}")
    
    return 0


if __name__ == "__main__":
    main()
