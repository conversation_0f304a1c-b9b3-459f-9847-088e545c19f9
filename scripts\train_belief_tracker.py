#!/usr/bin/env python
"""
DeepBeliefTracker 训练脚本

用于训练深度信念追踪器模型，将历史序列映射到对手手牌概率分布。
"""

import os
import sys
import argparse
import logging
import json
import time
from datetime import datetime
from typing import Dict, List, Tuple, Any, Optional
import glob
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, random_split
import matplotlib.pyplot as plt

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入必要模块
from cardgame_ai.algorithms.belief_tracking.deep_belief_tracker import TransformerBeliefTracker
from cardgame_ai.games.doudizhu.card import Card, CardRank, CardSuit
from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
)
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="DeepBeliefTracker 训练脚本")
    
    # 数据相关参数
    parser.add_argument('--data_dir', type=str, default='data/human_games',
                        help="游戏日志目录")
    parser.add_argument('--output_dir', type=str, default='models/belief_tracker',
                        help="模型输出目录")
    parser.add_argument('--val_ratio', type=float, default=0.2,
                        help="验证集比例")
    
    # 模型相关参数
    parser.add_argument('--input_dim', type=int, default=256,
                        help="输入特征维度")
    parser.add_argument('--hidden_dim', type=int, default=512,
                        help="隐藏层维度")
    parser.add_argument('--num_heads', type=int, default=8,
                        help="注意力头数")
    parser.add_argument('--num_layers', type=int, default=6,
                        help="Transformer层数")
    parser.add_argument('--dropout', type=float, default=0.1,
                        help="Dropout比率")
    
    # 训练相关参数
    parser.add_argument('--batch_size', type=int, default=32,
                        help="批次大小")
    parser.add_argument('--epochs', type=int, default=50,
                        help="训练轮数")
    parser.add_argument('--lr', type=float, default=0.0001,
                        help="学习率")
    parser.add_argument('--weight_decay', type=float, default=1e-5,
                        help="权重衰减")
    parser.add_argument('--patience', type=int, default=10,
                        help="早停耐心值")
    parser.add_argument('--device', type=str, default=None,
                        help="训练设备 (cuda或cpu)")
    
    # 其他参数
    parser.add_argument('--log_interval', type=int, default=10,
                        help="日志记录间隔（批次）")
    parser.add_argument('--save_interval', type=int, default=5,
                        help="模型保存间隔（轮数）")
    parser.add_argument('--verbose', action='store_true',
                        help="是否显示详细信息")
    
    return parser.parse_args()


class BeliefDataset(Dataset):
    """
    信念追踪数据集

    加载游戏日志，提取历史序列和真实手牌分布作为训练数据。
    """
    
    def __init__(self, data_list: List[Dict[str, Any]]):
        """
        初始化数据集

        Args:
            data_list: 包含历史和真实手牌的数据列表
        """
        self.data = data_list
        
    def __len__(self) -> int:
        """返回数据集大小"""
        return len(self.data)
    
    def __getitem__(self, idx: int) -> Dict[str, Any]:
        """获取一个样本"""
        return self.data[idx]


def create_card_mappings() -> Tuple[Dict[str, int], Dict[int, str], Dict[CardGroupType, int]]:
    """
    创建卡牌和牌型的映射关系

    Returns:
        card_to_idx: 卡牌到索引的映射
        idx_to_card: 索引到卡牌的映射
        card_type_to_idx: 牌型到索引的映射
    """
    # 创建卡牌到索引的映射
    card_to_idx = {}
    idx_to_card = {}
    
    # 初始化标准扑克牌
    idx = 0
    for rank in CardRank:
        for suit in CardSuit:
            # 跳过大小王的花色变体
            if (rank == CardRank.SMALL_JOKER or rank == CardRank.BIG_JOKER) and suit != CardSuit.SPADE:
                continue
            
            card = Card(rank, suit)
            card_str = str(card)
            card_to_idx[card_str] = idx
            idx_to_card[idx] = card_str
            idx += 1
    
    # 为牌型创建映射
    card_type_to_idx = {card_type: i for i, card_type in enumerate(CardGroupType)}
    
    return card_to_idx, idx_to_card, card_type_to_idx


def process_game_log(log_file: str, card_to_idx: Dict[str, int], card_type_to_idx: Dict[CardGroupType, int]) -> List[Dict[str, Any]]:
    """
    处理单个游戏日志文件，提取训练数据

    Args:
        log_file: 日志文件路径
        card_to_idx: 卡牌到索引的映射
        card_type_to_idx: 牌型到索引的映射

    Returns:
        数据列表，每个元素包含历史动作和真实手牌分布
    """
    try:
        # 读取日志文件
        with open(log_file, 'r', encoding='utf-8') as f:
            game_log = json.load(f)
        
        # 提取游戏数据
        training_samples = []
        
        # 假设game_log包含完整的游戏历史和玩家手牌信息
        # 以下逻辑需要根据实际日志格式调整
        if 'history' in game_log and 'hands' in game_log:
            game_history = game_log['history']
            player_hands = game_log['hands']
            
            # 为每个回合创建训练样本
            for turn_idx, turn in enumerate(game_history):
                if turn_idx == 0:
                    # 游戏开始，没有历史动作
                    continue
                
                # 获取当前玩家
                current_player = turn.get('player_id')
                if not current_player:
                    continue
                
                # 获取当前对手
                opponents = [
                    player_id for player_id in player_hands.keys() 
                    if player_id != current_player
                ]
                
                # 为每个对手创建样本
                for opponent_id in opponents:
                    # 提取历史动作序列
                    action_history = []
                    for prev_turn in game_history[:turn_idx]:
                        if prev_turn.get('player_id') != opponent_id:
                            continue
                        
                        # 处理动作
                        action = prev_turn.get('action')
                        if action:
                            processed_action = process_action(action, card_to_idx, card_type_to_idx)
                            action_history.append(processed_action)
                    
                    # 获取当前状态
                    curr_state = process_state(turn.get('state', {}))
                    
                    # 获取对手真实手牌
                    true_hand = player_hands.get(opponent_id, [])
                    hand_vector = create_hand_vector(true_hand, card_to_idx)
                    
                    # 创建样本
                    sample = {
                        'action_history': action_history,
                        'current_state': curr_state,
                        'true_hand': hand_vector
                    }
                    training_samples.append(sample)
        
        return training_samples
                    
    except Exception as e:
        logger.error(f"处理日志文件 {log_file} 时出错: {e}")
        return []


def process_action(action: Dict[str, Any], card_to_idx: Dict[str, int], card_type_to_idx: Dict[CardGroupType, int]) -> Dict[str, Any]:
    """
    处理动作数据

    Args:
        action: 动作数据
        card_to_idx: 卡牌到索引的映射
        card_type_to_idx: 牌型到索引的映射

    Returns:
        处理后的动作信息
    """
    # 初始化结果
    processed = {
        'is_pass': False,
        'cards': None,
        'cards_idx': None,
        'card_type': None,
        'card_type_idx': None,
        'time': time.time()
    }
    
    # 提取数据
    if not action or action.get('pass', False):
        processed['is_pass'] = True
        return processed
    
    # 处理出牌
    cards = action.get('cards', [])
    if cards:
        processed['cards'] = [str(card) for card in cards]
        processed['cards_idx'] = [card_to_idx.get(str(card), 0) for card in cards]
        
        # 获取牌型
        try:
            card_type = action.get('card_type')
            if card_type:
                processed['card_type'] = card_type
                processed['card_type_idx'] = card_type_to_idx.get(card_type, 0)
        except:
            pass
    
    return processed


def process_state(state_data: Dict[str, Any]) -> np.ndarray:
    """
    处理状态数据

    Args:
        state_data: 原始状态数据

    Returns:
        状态向量
    """
    # 此函数需要根据实际游戏状态格式实现
    # 先创建一个简单的占位实现
    
    # 假设state_data['features']是游戏特征
    if 'features' in state_data:
        return np.array(state_data['features'], dtype=np.float32)
    
    # 如果没有特征，返回空数组
    return np.array([], dtype=np.float32)


def create_hand_vector(hand: List[Card], card_to_idx: Dict[str, int]) -> np.ndarray:
    """
    创建手牌向量表示

    Args:
        hand: 手牌列表
        card_to_idx: 卡牌到索引的映射

    Returns:
        手牌的one-hot向量
    """
    # 创建全零向量
    hand_vector = np.zeros(len(card_to_idx), dtype=np.float32)
    
    # 将手牌对应位置设为1
    for card in hand:
        card_str = str(card)
        if card_str in card_to_idx:
            hand_vector[card_to_idx[card_str]] = 1.0
    
    return hand_vector


def load_game_logs(data_dir: str) -> List[Dict[str, Any]]:
    """
    加载所有游戏日志并提取训练数据

    Args:
        data_dir: 数据目录

    Returns:
        训练数据列表
    """
    # 创建卡牌映射
    card_to_idx, idx_to_card, card_type_to_idx = create_card_mappings()
    
    # 获取所有日志文件
    log_files = glob.glob(os.path.join(data_dir, "*.json"))
    logger.info(f"找到 {len(log_files)} 个游戏日志文件")
    
    # 处理所有日志
    all_samples = []
    for log_file in log_files:
        samples = process_game_log(log_file, card_to_idx, card_type_to_idx)
        all_samples.extend(samples)
        logger.info(f"从 {log_file} 提取了 {len(samples)} 个训练样本")
    
    logger.info(f"总共提取了 {len(all_samples)} 个训练样本")
    return all_samples


def collate_fn(batch: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    数据批次整理函数

    Args:
        batch: 样本批次

    Returns:
        整理后的批次数据
    """
    # 提取数据
    action_histories = [sample['action_history'] for sample in batch]
    current_states = [sample['current_state'] for sample in batch]
    true_hands = [sample['true_hand'] for sample in batch]
    
    # 转换为张量
    try:
        # 状态和手牌可以直接堆叠
        state_tensor = torch.tensor(np.stack(current_states), dtype=torch.float32)
        hand_tensor = torch.tensor(np.stack(true_hands), dtype=torch.float32)
        
        # 动作历史需要作为列表传递给模型
        # 不直接转换为张量
        
        return {
            'action_histories': action_histories,
            'states': state_tensor,
            'true_hands': hand_tensor
        }
    except Exception as e:
        logger.error(f"批次整理失败: {e}")
        raise


def kl_divergence(pred_probs: torch.Tensor, true_probs: torch.Tensor, epsilon: float = 1e-8) -> torch.Tensor:
    """
    计算KL散度

    Args:
        pred_probs: 预测概率 [batch_size, num_cards]
        true_probs: 真实概率 [batch_size, num_cards]
        epsilon: 小值，防止除零或log(0)

    Returns:
        KL散度值
    """
    # 添加小值防止数值问题
    pred_probs = pred_probs + epsilon
    true_probs = true_probs + epsilon
    
    # 归一化
    pred_probs = pred_probs / torch.sum(pred_probs, dim=1, keepdim=True)
    true_probs = true_probs / torch.sum(true_probs, dim=1, keepdim=True)
    
    # 计算KL散度: sum(true_probs * log(true_probs / pred_probs))
    kl_div = torch.sum(true_probs * torch.log(true_probs / pred_probs), dim=1)
    
    return torch.mean(kl_div)


def cross_entropy_loss(pred_probs: torch.Tensor, true_probs: torch.Tensor, epsilon: float = 1e-8) -> torch.Tensor:
    """
    计算交叉熵损失

    Args:
        pred_probs: 预测概率 [batch_size, num_cards]
        true_probs: 真实概率 [batch_size, num_cards]
        epsilon: 小值，防止log(0)

    Returns:
        交叉熵损失值
    """
    # 添加小值防止log(0)
    pred_probs = pred_probs + epsilon
    
    # 归一化
    pred_probs = pred_probs / torch.sum(pred_probs, dim=1, keepdim=True)
    
    # 计算交叉熵: -sum(true_probs * log(pred_probs))
    cross_entropy = -torch.sum(true_probs * torch.log(pred_probs), dim=1)
    
    return torch.mean(cross_entropy)


def calculate_accuracy(pred_probs: torch.Tensor, true_probs: torch.Tensor, threshold: float = 0.5) -> float:
    """
    计算准确率

    通过比较预测概率是否超过阈值与真实手牌是否存在，计算准确率。

    Args:
        pred_probs: 预测概率 [batch_size, num_cards]
        true_probs: 真实概率 [batch_size, num_cards]
        threshold: 预测阈值

    Returns:
        准确率百分比
    """
    # 获取预测和真实的二值化表示
    pred_binary = (pred_probs > threshold).float()
    true_binary = (true_probs > 0).float()
    
    # 计算匹配的元素数
    matches = torch.eq(pred_binary, true_binary).float()
    accuracy = torch.mean(matches) * 100.0
    
    return accuracy.item()


def train_model(
    model: torch.nn.Module,
    train_loader: DataLoader,
    val_loader: Optional[DataLoader],
    args: argparse.Namespace
) -> Dict[str, List[float]]:
    """
    训练模型

    Args:
        model: 要训练的模型
        train_loader: 训练数据加载器
        val_loader: 验证数据加载器，可选
        args: 命令行参数

    Returns:
        训练统计信息
    """
    # 确定设备
    device = args.device
    if device is None:
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
    logger.info(f"使用设备: {device}")
    model.to(device)
    
    # 定义优化器
    optimizer = optim.Adam(
        model.parameters(),
        lr=args.lr,
        weight_decay=args.weight_decay
    )
    
    # 学习率调度器
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer,
        mode='min',
        factor=0.5,
        patience=5,
        verbose=True
    )
    
    # 初始化统计信息
    stats = {
        'train_loss': [],
        'train_ce_loss': [],
        'train_kl_loss': [],
        'train_accuracy': [],
        'val_loss': [],
        'val_ce_loss': [],
        'val_kl_loss': [],
        'val_accuracy': []
    }
    
    # 早停变量
    best_val_loss = float('inf')
    no_improvement_count = 0
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 训练循环
    logger.info(f"开始训练，共{args.epochs}轮")
    for epoch in range(1, args.epochs + 1):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_ce_loss = 0.0
        train_kl_loss = 0.0
        train_accuracy = 0.0
        train_count = 0
        
        for batch_idx, batch in enumerate(train_loader):
            # 提取数据
            action_histories = batch['action_histories']
            states = batch['states'].to(device)
            true_hands = batch['true_hands'].to(device)
            
            # 前向传播
            pred_probs = model(action_histories, states)
            
            # 计算损失
            ce_loss = cross_entropy_loss(pred_probs, true_hands)
            kl_loss = kl_divergence(pred_probs, true_hands)
            loss = ce_loss + kl_loss  # 可以添加权重因子
            
            # 反向传播
            optimizer.zero_grad()
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
            
            # 更新参数
            optimizer.step()
            
            # 更新统计信息
            train_loss += loss.item()
            train_ce_loss += ce_loss.item()
            train_kl_loss += kl_loss.item()
            train_accuracy += calculate_accuracy(pred_probs.detach().cpu(), true_hands.cpu())
            train_count += 1
            
            # 打印训练信息
            if batch_idx % args.log_interval == 0:
                logger.info(
                    f"轮次: {epoch}/{args.epochs} "
                    f"[{batch_idx * len(states)}/{len(train_loader.dataset)} "
                    f"({100. * batch_idx / len(train_loader):.0f}%)] "
                    f"损失: {loss.item():.6f} "
                    f"CE: {ce_loss.item():.6f} "
                    f"KL: {kl_loss.item():.6f} "
                    f"准确率: {calculate_accuracy(pred_probs.detach().cpu(), true_hands.cpu()):.2f}%"
                )
        
        # 计算平均训练指标
        train_loss /= train_count
        train_ce_loss /= train_count
        train_kl_loss /= train_count
        train_accuracy /= train_count
        
        # 添加到统计信息
        stats['train_loss'].append(train_loss)
        stats['train_ce_loss'].append(train_ce_loss)
        stats['train_kl_loss'].append(train_kl_loss)
        stats['train_accuracy'].append(train_accuracy)
        
        # 验证阶段
        if val_loader:
            model.eval()
            val_loss = 0.0
            val_ce_loss = 0.0
            val_kl_loss = 0.0
            val_accuracy = 0.0
            val_count = 0
            
            with torch.no_grad():
                for batch in val_loader:
                    # 提取数据
                    action_histories = batch['action_histories']
                    states = batch['states'].to(device)
                    true_hands = batch['true_hands'].to(device)
                    
                    # 前向传播
                    pred_probs = model(action_histories, states)
                    
                    # 计算损失
                    ce_loss = cross_entropy_loss(pred_probs, true_hands)
                    kl_loss = kl_divergence(pred_probs, true_hands)
                    loss = ce_loss + kl_loss
                    
                    # 更新统计信息
                    val_loss += loss.item()
                    val_ce_loss += ce_loss.item()
                    val_kl_loss += kl_loss.item()
                    val_accuracy += calculate_accuracy(pred_probs.cpu(), true_hands.cpu())
                    val_count += 1
            
            # 计算平均验证指标
            val_loss /= val_count
            val_ce_loss /= val_count
            val_kl_loss /= val_count
            val_accuracy /= val_count
            
            # 添加到统计信息
            stats['val_loss'].append(val_loss)
            stats['val_ce_loss'].append(val_ce_loss)
            stats['val_kl_loss'].append(val_kl_loss)
            stats['val_accuracy'].append(val_accuracy)
            
            # 更新学习率
            scheduler.step(val_loss)
            
            # 早停检查
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                no_improvement_count = 0
                
                # 保存最佳模型
                best_model_path = os.path.join(args.output_dir, 'belief_tracker_best.pt')
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'val_loss': val_loss
                }, best_model_path)
                logger.info(f"保存最佳模型: {best_model_path}")
            else:
                no_improvement_count += 1
                if no_improvement_count >= args.patience:
                    logger.info(f"早停: {args.patience} 轮未改善")
                    break
        
        # 定期保存模型
        if epoch % args.save_interval == 0:
            checkpoint_path = os.path.join(args.output_dir, f'belief_tracker_epoch_{epoch}.pt')
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'stats': stats
            }, checkpoint_path)
            logger.info(f"保存检查点: {checkpoint_path}")
        
        # 打印轮次摘要
        logger.info(
            f"轮次: {epoch}/{args.epochs} 完成 "
            f"训练损失: {train_loss:.6f} "
            f"训练准确率: {train_accuracy:.2f}%"
        )
        if val_loader:
            logger.info(
                f"验证损失: {val_loss:.6f} "
                f"验证准确率: {val_accuracy:.2f}%"
            )
    
    # 保存最终模型
    final_model_path = os.path.join(args.output_dir, 'belief_tracker_final.pt')
    torch.save({
        'epoch': args.epochs,
        'model_state_dict': model.state_dict(),
        'stats': stats
    }, final_model_path)
    logger.info(f"保存最终模型: {final_model_path}")
    
    # 绘制训练曲线
    plot_training_curves(stats, args.output_dir)
    
    return stats


def plot_training_curves(stats: Dict[str, List[float]], output_dir: str) -> None:
    """
    绘制训练曲线并保存

    Args:
        stats: 训练统计信息
        output_dir: 输出目录
    """
    # 创建图表目录
    plots_dir = os.path.join(output_dir, 'plots')
    os.makedirs(plots_dir, exist_ok=True)
    
    # 绘制损失曲线
    plt.figure(figsize=(10, 6))
    plt.plot(stats['train_loss'], label='训练损失')
    if stats['val_loss']:
        plt.plot(stats['val_loss'], label='验证损失')
    plt.xlabel('轮次')
    plt.ylabel('损失')
    plt.title('训练与验证损失')
    plt.legend()
    plt.grid(True)
    plt.savefig(os.path.join(plots_dir, 'loss_curve.png'))
    plt.close()
    
    # 绘制准确率曲线
    plt.figure(figsize=(10, 6))
    plt.plot(stats['train_accuracy'], label='训练准确率')
    if stats['val_accuracy']:
        plt.plot(stats['val_accuracy'], label='验证准确率')
    plt.xlabel('轮次')
    plt.ylabel('准确率 (%)')
    plt.title('训练与验证准确率')
    plt.legend()
    plt.grid(True)
    plt.savefig(os.path.join(plots_dir, 'accuracy_curve.png'))
    plt.close()
    
    # 如果有交叉熵和KL散度损失，也绘制它们
    if stats['train_ce_loss'] and stats['train_kl_loss']:
        plt.figure(figsize=(10, 6))
        plt.plot(stats['train_ce_loss'], label='训练CE损失')
        plt.plot(stats['train_kl_loss'], label='训练KL损失')
        if stats['val_ce_loss'] and stats['val_kl_loss']:
            plt.plot(stats['val_ce_loss'], label='验证CE损失')
            plt.plot(stats['val_kl_loss'], label='验证KL损失')
        plt.xlabel('轮次')
        plt.ylabel('损失')
        plt.title('分解损失曲线')
        plt.legend()
        plt.grid(True)
        plt.savefig(os.path.join(plots_dir, 'detailed_loss_curve.png'))
        plt.close()


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 加载游戏日志数据
    data_samples = load_game_logs(args.data_dir)
    
    # 检查是否有足够的数据
    if len(data_samples) == 0:
        logger.error("没有找到有效的训练数据，退出")
        return
    
    # 创建数据集
    dataset = BeliefDataset(data_samples)
    
    # 划分训练集和验证集
    val_size = int(len(dataset) * args.val_ratio)
    train_size = len(dataset) - val_size
    train_dataset, val_dataset = random_split(dataset, [train_size, val_size])
    
    logger.info(f"训练集大小: {train_size}, 验证集大小: {val_size}")
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=args.batch_size,
        shuffle=True,
        collate_fn=collate_fn,
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        collate_fn=collate_fn
    ) if val_size > 0 else None
    
    # 获取输出维度（卡牌数量）
    sample_batch = next(iter(train_loader))
    output_dim = sample_batch['true_hands'].shape[1]
    
    # 创建模型
    model = TransformerBeliefTracker(
        input_dim=args.input_dim,
        hidden_dim=args.hidden_dim,
        output_dim=output_dim,
        num_heads=args.num_heads,
        num_layers=args.num_layers,
        dropout=args.dropout
    )
    
    logger.info(f"创建模型: {model.__class__.__name__}")
    
    # 训练模型
    train_stats = train_model(model, train_loader, val_loader, args)
    
    # 保存训练统计信息
    stats_path = os.path.join(args.output_dir, 'training_stats.json')
    with open(stats_path, 'w') as f:
        json.dump({k: [float(v) for v in vals] for k, vals in train_stats.items()}, f, indent=4)
    
    logger.info(f"训练统计信息已保存至: {stats_path}")
    logger.info("训练完成")


if __name__ == "__main__":
    main() 