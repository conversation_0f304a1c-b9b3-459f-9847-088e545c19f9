# HRL (分层强化学习) 控制器配置文件
# 用于配置分层强化学习中的层次控制器参数

# 基本控制器配置
controller:
  # 动态调度开关 - 是否根据状态复杂度动态选择策略
  dynamic_scheduling: true
  
  # 复杂度阈值 - 高于此值使用高层策略，范围0-1
  # 值越大，系统更倾向于使用低层策略
  complexity_threshold: 0.6
  
  # 置信度阈值 - 高于此值直接使用低层策略，范围0-1
  # 值越小，系统更倾向于直接使用低层策略
  confidence_threshold: 0.75
  
  # 是否使用历史信息
  use_history: true
  
  # 历史窗口大小 - 记住最近几步动作和目标
  history_window: 5
  
  # 是否使用对手建模功能
  use_opponent_modeling: true
  
  # 对手类型调整参数
  opponent_type_adjustments:
    # 激进型对手
    aggressive:
      # 降低复杂度阈值，更倾向于使用高层策略应对激进对手
      complexity_adjust: -0.12
      # 提高置信度阈值，降低直接决策的频率
      confidence_adjust: 0.12
    
    # 保守型对手
    conservative:
      # 提高复杂度阈值，更倾向于使用低层策略应对保守对手
      complexity_adjust: 0.15
      # 降低置信度阈值，增加直接决策的频率
      confidence_adjust: -0.15
    
    # 平衡型对手
    balanced:
      complexity_adjust: 0.0
      confidence_adjust: 0.0
    
    # 不可预测型对手
    unpredictable:
      # 显著降低复杂度阈值，更积极地使用高层策略应对不可预测对手
      complexity_adjust: -0.18
      # 显著提高置信度阈值，减少直接决策的频率
      confidence_adjust: 0.18
    
    # 适应型对手
    adaptive:
      complexity_adjust: -0.08
      confidence_adjust: 0.08
    
    # 未知类型
    unknown:
      complexity_adjust: -0.05
      confidence_adjust: 0.05

# 日志配置
logging:
  # 是否记录详细调试信息
  verbose: false
  
  # 是否记录调度决策的详细信息
  log_scheduling_decisions: true
  
  # 每隔多少步记录一次统计信息
  stats_interval: 50
  
  # 是否保存调度决策分布的可视化图表
  save_decision_distribution: true

# 高级特性配置
features:
  # 是否启用辅助评估网络 - 当设为true时将训练和使用小型网络进行评估
  use_auxiliary_networks: false
  
  # 辅助网络学习率
  auxiliary_learning_rate: 0.001
  
  # 辅助网络更新频率 (每N步更新一次)
  auxiliary_update_frequency: 100
  
  # 是否使用高层目标作为环境表示的一部分
  include_goals_in_state: true
  
  # 是否使用对手建模信息作为环境表示的一部分
  include_opponent_model_in_state: true
  
  # 自适应阈值调整 - 是否根据历史性能自动调整阈值
  adaptive_thresholds: true
  
  # 训练中是否自动微调阈值参数
  auto_tune_thresholds: true
  
  # 自动调整的范围限制
  auto_tune_min_complexity: 0.4
  auto_tune_max_complexity: 0.8
  auto_tune_min_confidence: 0.5
  auto_tune_max_confidence: 0.9
  
  # 自动调整的学习率
  auto_tune_learning_rate: 0.01 