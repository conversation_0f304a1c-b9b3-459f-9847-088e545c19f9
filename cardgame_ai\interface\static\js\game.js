/**
 * AI棋牌强化学习框架 - 游戏脚本
 */
document.addEventListener('DOMContentLoaded', function() {
    // 游戏状态
    const gameState = {
        gameId: document.body.getAttribute('data-game-id'),
        cardStyle: document.body.getAttribute('data-card-style') || 'standard',
        animationSpeed: document.body.getAttribute('data-animation-speed') || 'normal',
        soundEnabled: document.body.getAttribute('data-sound-enabled') === 'True',
        currentState: null,
        selectedCards: [],
        isPlayerTurn: false,
        gameOver: false,
        winner: null,
        startTime: Date.now(),
        turnCount: 0,
        explanationData: null,
        showExplanation: true  // 是否显示解释数据
    };

    // 元素引用
    const elements = {
        gameStatus: document.getElementById('game-status'),
        playBtn: document.getElementById('play-btn'),
        passBtn: document.getElementById('pass-btn'),
        hintBtn: document.getElementById('hint-btn'),
        restartBtn: document.getElementById('restart-btn'),
        exitBtn: document.getElementById('exit-btn'),
        humanCards: document.querySelector('.human-cards'),
        playerHuman: document.getElementById('player-human'),
        playerFarmer1: document.getElementById('player-farmer1'),
        playerFarmer2: document.getElementById('player-farmer2'),
        landlordCards: document.querySelector('.landlord-cards'),
        gameInfo: document.getElementById('game-info'),
        playHistory: document.getElementById('play-history'),
        gameOverModal: new bootstrap.Modal(document.getElementById('game-over-modal')),
        submitFeedbackBtn: document.getElementById('submit-feedback-btn'),
        playAgainBtn: document.getElementById('play-again-btn'),
        backToHomeBtn: document.getElementById('back-to-home-btn')
    };

    // 初始化游戏
    initGame();

    /**
     * 初始化游戏
     */
    function initGame() {
        // 应用卡牌样式
        if (gameState.cardStyle) {
            document.body.classList.add(`card-style-${gameState.cardStyle}`);
        }

        // 绑定按钮事件
        if (elements.playBtn) {
            elements.playBtn.addEventListener('click', handlePlayCards);
        }

        if (elements.passBtn) {
            elements.passBtn.addEventListener('click', handlePass);
        }

        if (elements.hintBtn) {
            elements.hintBtn.addEventListener('click', handleHint);
        }

        if (elements.restartBtn) {
            elements.restartBtn.addEventListener('click', handleRestart);
        }

        if (elements.exitBtn) {
            elements.exitBtn.addEventListener('click', handleExit);
        }

        if (elements.submitFeedbackBtn) {
            elements.submitFeedbackBtn.addEventListener('click', handleSubmitFeedback);
        }

        if (elements.playAgainBtn) {
            elements.playAgainBtn.addEventListener('click', handlePlayAgain);
        }

        if (elements.backToHomeBtn) {
            elements.backToHomeBtn.addEventListener('click', handleBackToHome);
        }

        // 加载游戏状态
        loadGameState();

        // 设置状态轮询
        setInterval(loadGameState, 2000);
    }

    /**
     * 加载游戏状态
     */
    function loadGameState() {
        if (!gameState.gameId || gameState.gameOver) {
            return;
        }

        fetch(`/api/game_state/${gameState.gameId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateGameState(data.state);
                } else {
                    showToast(data.error || '加载游戏状态失败', 'error');
                }
            })
            .catch(error => {
                console.error('加载游戏状态失败:', error);
            });
    }

    /**
     * 更新游戏状态
     * @param {Object} state - 游戏状态数据
     */
    function updateGameState(state) {
        const prevState = gameState.currentState;
        gameState.currentState = state;
        gameState.isPlayerTurn = state.is_player_turn;
        gameState.gameOver = state.game_over;
        gameState.winner = state.winner;

        // 更新游戏状态显示
        updateGameStatusDisplay();

        // 更新玩家角色和手牌
        updatePlayerRoles(state.player_role);
        updateCards(state);

        // 更新游戏信息
        updateGameInfo(state);

        // 更新出牌历史
        if (prevState && prevState.public_info.current_player !== state.public_info.current_player) {
            updatePlayHistory(state, prevState);
        }

        // 启用/禁用操作按钮
        updateActionButtons();

        // 如果游戏结束，显示结果
        if (state.game_over && !prevState?.game_over) {
            showGameResult();
        }
    }

    /**
     * 更新游戏状态显示
     */
    function updateGameStatusDisplay() {
        if (!elements.gameStatus) return;

        const state = gameState.currentState;
        if (!state) {
            elements.gameStatus.textContent = '等待游戏开始...';
            return;
        }

        if (state.game_over) {
            const winner = state.winner;
            if (state.player_role === winner) {
                elements.gameStatus.textContent = '游戏结束 - 你赢了！';
                elements.gameStatus.classList.add('text-success');
            } else {
                elements.gameStatus.textContent = '游戏结束 - 你输了！';
                elements.gameStatus.classList.add('text-danger');
            }
            return;
        }

        if (state.is_player_turn) {
            elements.gameStatus.textContent = '轮到你出牌';
            elements.gameStatus.classList.add('text-primary');
        } else {
            elements.gameStatus.textContent = `等待${state.public_info.current_player === 'landlord' ? '地主' : '农民'}出牌`;
            elements.gameStatus.classList.remove('text-primary');
        }
    }

    /**
     * 处理出牌
     */
    function handlePlayCards() {
        if (!gameState.isPlayerTurn || gameState.selectedCards.length === 0) {
            return;
        }

        const actionData = {
            type: 'PLAY',
            cards: gameState.selectedCards
        };

        playAction(actionData);
    }

    /**
     * 处理不出
     */
    function handlePass() {
        if (!gameState.isPlayerTurn) {
            return;
        }

        const actionData = {
            type: 'PASS',
            cards: []
        };

        playAction(actionData);
    }

    /**
     * 执行动作
     * @param {Object} actionData - 动作数据
     */
    function playAction(actionData) {
        // 禁用按钮
        elements.playBtn.disabled = true;
        elements.passBtn.disabled = true;
        elements.hintBtn.disabled = true;

        fetch(`/api/play_action/${gameState.gameId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ action: actionData })
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 清除选中的牌
                    gameState.selectedCards = [];

                    // 更新游戏状态
                    updateGameState(data.state);

                    // 增加回合计数
                    gameState.turnCount++;

                    // 处理解释数据（如果有）
                    if (data.explanation_data) {
                        gameState.explanationData = data.explanation_data;
                        updateExplanationData(data.explanation_data);
                    }
                } else {
                    throw new Error(data.error || '出牌失败');
                }
            })
            .catch(error => {
                console.error('出牌失败:', error);
                showToast('出牌失败: ' + error.message, 'error');

                // 恢复按钮
                updateActionButtons();
            });
    }

    /**
     * 处理提示
     */
    function handleHint() {
        if (!gameState.isPlayerTurn) {
            return;
        }

        // 获取合法动作
        const legalActions = gameState.currentState.legal_actions;
        if (legalActions && legalActions.length > 0) {
            // 找出合法的出牌动作（非不出）
            const playActions = legalActions.filter(action => action.type !== 'PASS');

            if (playActions.length > 0) {
                // 随机选择一个合法动作
                const randomIndex = Math.floor(Math.random() * playActions.length);
                const suggestedAction = playActions[randomIndex];

                // 清除当前选择
                gameState.selectedCards = [];

                // 选择建议的牌
                if (suggestedAction.cards && suggestedAction.cards.length > 0) {
                    gameState.selectedCards = [...suggestedAction.cards];
                    highlightSelectedCards();
                }

                showToast(`提示: ${suggestedAction.display_text}`, 'info');
            } else {
                showToast('提示: 建议不出', 'info');
            }
        }
    }

    /**
     * 处理重新开始
     */
    function handleRestart() {
        if (confirm('确定要重新开始游戏吗？')) {
            fetch(`/api/reset_game/${gameState.gameId}`, {
                method: 'POST'
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 重置游戏状态
                        gameState.selectedCards = [];
                        gameState.gameOver = false;
                        gameState.winner = null;
                        gameState.startTime = Date.now();
                        gameState.turnCount = 0;

                        // 刷新游戏状态
                        loadGameState();

                        showToast('游戏已重新开始', 'success');
                    } else {
                        showToast(data.error || '重新开始失败', 'error');
                    }
                })
                .catch(error => {
                    console.error('重新开始失败:', error);
                    showToast('重新开始失败: ' + error.message, 'error');
                });
        }
    }

    /**
     * 处理退出游戏
     */
    function handleExit() {
        if (confirm('确定要退出游戏吗？')) {
            window.location.href = '/';
        }
    }

    /**
     * 显示游戏结果
     */
    function showGameResult() {
        if (!elements.gameOverModal) return;

        // 更新结果信息
        const resultMessage = document.querySelector('.result-message');
        if (resultMessage) {
            if (gameState.winner === gameState.currentState.player_role) {
                resultMessage.innerHTML = '<h4 class="text-success">恭喜，你赢了！</h4>';
            } else {
                resultMessage.innerHTML = '<h4 class="text-danger">很遗憾，你输了！</h4>';
            }
        }

        // 更新统计信息
        const turnsCount = document.getElementById('turns-count');
        if (turnsCount) {
            turnsCount.textContent = gameState.turnCount;
        }

        const gameDuration = document.getElementById('game-duration');
        if (gameDuration) {
            const durationSeconds = Math.floor((Date.now() - gameState.startTime) / 1000);
            const minutes = Math.floor(durationSeconds / 60);
            const seconds = durationSeconds % 60;
            gameDuration.textContent = `${minutes}分${seconds}秒`;
        }

        // 显示模态框
        elements.gameOverModal.show();
    }

    /**
     * 更新解释数据
     * @param {Object} explanationData - 解释数据
     */
    function updateExplanationData(explanationData) {
        if (!gameState.showExplanation || !explanationData || !window.AIVisualization) {
            return;
        }

        // 准备可视化数据
        const visualizationData = {
            confidence: calculateConfidence(explanationData),
            component_used: determineComponentUsed(explanationData),
            mcts: extractMctsData(explanationData),
            network: extractNetworkData(explanationData)
        };

        // 更新可视化
        window.AIVisualization.update(visualizationData);
    }

    /**
     * 计算信心评分
     * @param {Object} explanationData - 解释数据
     * @returns {number} 信心评分 (0-1)
     */
    function calculateConfidence(explanationData) {
        // 默认中等信心
        let confidence = 0.5;

        // 如果有MCTS数据
        if (explanationData.mcts_data && explanationData.mcts_data.root_info) {
            const rootInfo = explanationData.mcts_data.root_info;

            // 访问次数越多，信心越高
            if (rootInfo.visit_count > 100) {
                confidence += 0.2;
            }

            // 价值越高，信心越高
            if (rootInfo.value > 0.7) {
                confidence += 0.2;
            } else if (rootInfo.value < 0.3) {
                confidence -= 0.2;
            }
        }

        // 如果有神经网络数据
        if (explanationData.network_data && explanationData.network_data.network_output) {
            const output = explanationData.network_data.network_output;

            // 顶级动作概率越高，信心越高
            if (output.top_actions && output.top_actions.length > 0) {
                const topActionProb = output.top_actions[0].probability;
                if (topActionProb > 0.8) {
                    confidence += 0.2;
                } else if (topActionProb < 0.4) {
                    confidence -= 0.1;
                }
            }
        }

        // 确保信心在0-1范围内
        return Math.max(0, Math.min(1, confidence));
    }

    /**
     * 确定使用的决策组件
     * @param {Object} explanationData - 解释数据
     * @returns {string} 组件名称
     */
    function determineComponentUsed(explanationData) {
        if (explanationData.component_used) {
            return explanationData.component_used;
        }

        if (explanationData.mcts_data && !explanationData.network_data) {
            return "MCTS搜索";
        } else if (!explanationData.mcts_data && explanationData.network_data) {
            return "神经网络";
        } else if (explanationData.mcts_data && explanationData.network_data) {
            return "MCTS+神经网络混合";
        } else if (explanationData.rule_based) {
            return "规则系统";
        }

        return "未知组件";
    }

    /**
     * 提取MCTS数据
     * @param {Object} explanationData - 解释数据
     * @returns {Object} MCTS可视化数据
     */
    function extractMctsData(explanationData) {
        if (!explanationData.mcts_data) {
            return null;
        }

        const mctsData = explanationData.mcts_data;

        // 构建主要变化路径
        let principalVariation = [];
        if (mctsData.principal_variation) {
            principalVariation = mctsData.principal_variation.map(step => ({
                action: step.action,
                node_info: {
                    visit_count: step.visit_count || 0,
                    value: step.value || 0
                }
            }));
        }

        // 构建顶级动作
        let topActions = [];
        if (mctsData.top_actions) {
            topActions = mctsData.top_actions.map(action => ({
                action: action.action,
                visit_count: action.visit_count || 0,
                value: action.value || 0,
                policy_prob: action.prior || 0
            }));
        }

        return {
            root_info: mctsData.root_info || {
                visit_count: 0,
                value: 0
            },
            principal_variation: principalVariation,
            top_actions: topActions
        };
    }

    /**
     * 提取神经网络数据
     * @param {Object} explanationData - 解释数据
     * @returns {Object} 神经网络可视化数据
     */
    function extractNetworkData(explanationData) {
        if (!explanationData.network_data) {
            return null;
        }

        const networkData = explanationData.network_data;

        // 特征维度
        let featureDims = {};
        if (networkData.embedding_output) {
            featureDims.embedding = networkData.embedding_output.shape;
        }

        if (networkData.transformer_output) {
            featureDims.transformer = networkData.transformer_output.shape;
        }

        return {
            network_output: networkData.network_output || {
                value: 0,
                top_actions: []
            },
            feature_dims: featureDims
        };
    }

    /**
     * 显示提示消息
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型（success, error, info）
     */
    function showToast(message, type = 'info') {
        // 检查是否已存在toast容器
        let toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container';
            document.body.appendChild(toastContainer);
        }

        // 创建toast元素
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                ${message}
            </div>
        `;

        // 添加到容器
        toastContainer.appendChild(toast);

        // 显示toast
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);

        // 3秒后隐藏
        setTimeout(() => {
            toast.classList.remove('show');
            // 动画结束后移除元素
            setTimeout(() => {
                toastContainer.removeChild(toast);
            }, 300);
        }, 3000);
    }
});