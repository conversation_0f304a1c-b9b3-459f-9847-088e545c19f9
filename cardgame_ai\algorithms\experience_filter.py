#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
经验过滤模块

提供经验质量评估和过滤机制，用于识别和保留高质量的经验样本，提高训练效率。
"""

import numpy as np
import logging
from typing import List, Dict, Any, Callable, Optional, Tuple, Union
from collections import deque
import time

from cardgame_ai.core.base import Experience, Batch

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ExperienceQualityMetric:
    """
    经验质量评估指标基类

    用于评估经验样本的质量，子类需要实现evaluate方法。
    """

    def __init__(self, name: str):
        """
        初始化经验质量评估指标

        Args:
            name (str): 指标名称
        """
        self.name = name

    def evaluate(self, experience: Experience, **kwargs) -> float:
        """
        评估经验质量

        Args:
            experience (Experience): 经验样本
            **kwargs: 其他参数

        Returns:
            float: 质量评分，范围[0, 1]，值越大表示质量越高
        """
        raise NotImplementedError("子类必须实现evaluate方法")

    def __str__(self) -> str:
        """
        转换为字符串表示

        Returns:
            str: 字符串表示
        """
        return f"{self.__class__.__name__}(name={self.name})"


class TDErrorMetric(ExperienceQualityMetric):
    """
    TD误差质量评估指标

    基于TD误差评估经验质量，TD误差越大表示经验质量越高。
    """

    def __init__(self, name: str = "td_error", min_error: float = 0.0, max_error: float = 10.0):
        """
        初始化TD误差质量评估指标

        Args:
            name (str, optional): 指标名称. Defaults to "td_error".
            min_error (float, optional): 最小误差. Defaults to 0.0.
            max_error (float, optional): 最大误差. Defaults to 10.0.
        """
        super().__init__(name)
        self.min_error = min_error
        self.max_error = max_error

    def evaluate(self, experience: Experience, **kwargs) -> float:
        """
        评估经验质量

        Args:
            experience (Experience): 经验样本
            **kwargs: 其他参数，必须包含td_error

        Returns:
            float: 质量评分，范围[0, 1]，值越大表示质量越高
        """
        # 获取TD误差
        td_error = kwargs.get("td_error", 0.0)

        # 归一化到[0, 1]范围
        normalized_error = (min(max(td_error, self.min_error), self.max_error) - self.min_error) / (self.max_error - self.min_error)

        return normalized_error


class RewardMetric(ExperienceQualityMetric):
    """
    奖励质量评估指标

    基于奖励评估经验质量，奖励越高表示经验质量越高。
    """

    def __init__(self, name: str = "reward", min_reward: float = -10.0, max_reward: float = 10.0):
        """
        初始化奖励质量评估指标

        Args:
            name (str, optional): 指标名称. Defaults to "reward".
            min_reward (float, optional): 最小奖励. Defaults to -10.0.
            max_reward (float, optional): 最大奖励. Defaults to 10.0.
        """
        super().__init__(name)
        self.min_reward = min_reward
        self.max_reward = max_reward

    def evaluate(self, experience: Experience, **kwargs) -> float:
        """
        评估经验质量

        Args:
            experience (Experience): 经验样本
            **kwargs: 其他参数

        Returns:
            float: 质量评分，范围[0, 1]，值越大表示质量越高
        """
        # 获取奖励
        reward = experience.reward

        # 归一化到[0, 1]范围
        normalized_reward = (min(max(reward, self.min_reward), self.max_reward) - self.min_reward) / (self.max_reward - self.min_reward)

        return normalized_reward


class RarityMetric(ExperienceQualityMetric):
    """
    稀有性质量评估指标

    基于经验的稀有性评估质量，越稀有的经验质量越高。
    使用经验的状态-动作对的出现频率来衡量稀有性。
    """

    def __init__(self, name: str = "rarity", history_size: int = 1000, decay_factor: float = 0.99):
        """
        初始化稀有性质量评估指标

        Args:
            name (str, optional): 指标名称. Defaults to "rarity".
            history_size (int, optional): 历史记录大小. Defaults to 1000.
            decay_factor (float, optional): 衰减因子. Defaults to 0.99.
        """
        super().__init__(name)
        self.history_size = history_size
        self.decay_factor = decay_factor
        self.state_action_counts = {}
        self.total_count = 0

    def _get_state_action_key(self, experience: Experience) -> str:
        """
        获取状态-动作对的键

        Args:
            experience (Experience): 经验样本

        Returns:
            str: 状态-动作对的键
        """
        # 简化处理，实际应用中可能需要更复杂的哈希方法
        state_str = str(experience.state)
        action_str = str(experience.action)
        return f"{state_str}_{action_str}"

    def update(self, experience: Experience) -> None:
        """
        更新稀有性统计信息

        Args:
            experience (Experience): 经验样本
        """
        # 获取状态-动作对的键
        key = self._get_state_action_key(experience)

        # 更新计数
        self.state_action_counts[key] = self.state_action_counts.get(key, 0) * self.decay_factor + 1
        self.total_count = self.total_count * self.decay_factor + 1

        # 限制历史记录大小
        if len(self.state_action_counts) > self.history_size:
            # 删除计数最小的项
            min_key = min(self.state_action_counts, key=self.state_action_counts.get)
            del self.state_action_counts[min_key]

    def evaluate(self, experience: Experience, **kwargs) -> float:
        """
        评估经验质量

        Args:
            experience (Experience): 经验样本
            **kwargs: 其他参数

        Returns:
            float: 质量评分，范围[0, 1]，值越大表示质量越高
        """
        # 获取状态-动作对的键
        key = self._get_state_action_key(experience)

        # 获取计数
        count = self.state_action_counts.get(key, 0)

        # 如果总计数为0，返回最高质量
        if self.total_count == 0:
            return 1.0

        # 计算频率
        frequency = count / self.total_count

        # 计算稀有性，频率越低越稀有
        rarity = 1.0 - frequency

        return rarity


class DiversityMetric(ExperienceQualityMetric):
    """
    多样性质量评估指标

    基于经验与已有经验的差异性评估质量，差异性越大表示多样性越高。
    使用经验的状态和动作与已有经验的平均距离来衡量多样性。
    """

    def __init__(self, name: str = "diversity", buffer_size: int = 100, distance_threshold: float = 0.5):
        """
        初始化多样性质量评估指标

        Args:
            name (str, optional): 指标名称. Defaults to "diversity".
            buffer_size (int, optional): 缓冲区大小. Defaults to 100.
            distance_threshold (float, optional): 距离阈值. Defaults to 0.5.
        """
        super().__init__(name)
        self.buffer_size = buffer_size
        self.distance_threshold = distance_threshold
        self.state_buffer = deque(maxlen=buffer_size)
        self.action_buffer = deque(maxlen=buffer_size)

    def update(self, experience: Experience) -> None:
        """
        更新多样性统计信息

        Args:
            experience (Experience): 经验样本
        """
        # 将状态和动作转换为数组
        try:
            state_array = np.array(experience.state, dtype=np.float32)
            action_array = np.array(experience.action, dtype=np.float32)
        except (TypeError, ValueError):
            # 如果无法转换，使用简化的表示
            state_array = np.array([hash(str(experience.state)) % 1000], dtype=np.float32)
            action_array = np.array([hash(str(experience.action)) % 1000], dtype=np.float32)

        # 添加到缓冲区
        self.state_buffer.append(state_array)
        self.action_buffer.append(action_array)

    def _calculate_distance(self, array1: np.ndarray, array2: np.ndarray) -> float:
        """
        计算两个数组的距离

        Args:
            array1 (np.ndarray): 数组1
            array2 (np.ndarray): 数组2

        Returns:
            float: 距离
        """
        # 如果形状不同，返回最大距离
        if array1.shape != array2.shape:
            return 1.0

        # 计算欧几里得距离并归一化
        distance = np.linalg.norm(array1 - array2)
        normalized_distance = min(distance / self.distance_threshold, 1.0)

        return normalized_distance

    def evaluate(self, experience: Experience, **kwargs) -> float:
        """
        评估经验质量

        Args:
            experience (Experience): 经验样本
            **kwargs: 其他参数

        Returns:
            float: 质量评分，范围[0, 1]，值越大表示质量越高
        """
        # 如果缓冲区为空，返回最高质量
        if len(self.state_buffer) == 0:
            return 1.0

        # 将状态和动作转换为数组
        try:
            state_array = np.array(experience.state, dtype=np.float32)
            action_array = np.array(experience.action, dtype=np.float32)
        except (TypeError, ValueError):
            # 如果无法转换，使用简化的表示
            state_array = np.array([hash(str(experience.state)) % 1000], dtype=np.float32)
            action_array = np.array([hash(str(experience.action)) % 1000], dtype=np.float32)

        # 计算与缓冲区中所有经验的平均距离
        state_distances = [self._calculate_distance(state_array, s) for s in self.state_buffer]
        action_distances = [self._calculate_distance(action_array, a) for a in self.action_buffer]

        # 计算平均距离
        avg_state_distance = sum(state_distances) / len(state_distances)
        avg_action_distance = sum(action_distances) / len(action_distances)

        # 组合状态和动作的距离
        diversity = (avg_state_distance + avg_action_distance) / 2.0

        return diversity


class CompositeMetric(ExperienceQualityMetric):
    """
    组合质量评估指标

    将多个质量评估指标组合起来，根据权重计算加权平均质量。
    """

    def __init__(self, name: str = "composite", metrics: List[Tuple[ExperienceQualityMetric, float]] = None):
        """
        初始化组合质量评估指标

        Args:
            name (str, optional): 指标名称. Defaults to "composite".
            metrics (List[Tuple[ExperienceQualityMetric, float]], optional): 指标列表，每个元素是(指标, 权重). Defaults to None.
        """
        super().__init__(name)
        self.metrics = metrics or []

    def add_metric(self, metric: ExperienceQualityMetric, weight: float = 1.0) -> None:
        """
        添加质量评估指标

        Args:
            metric (ExperienceQualityMetric): 质量评估指标
            weight (float, optional): 权重. Defaults to 1.0.
        """
        self.metrics.append((metric, weight))

    def update(self, experience: Experience) -> None:
        """
        更新所有指标的统计信息

        Args:
            experience (Experience): 经验样本
        """
        for metric, _ in self.metrics:
            if hasattr(metric, "update"):
                metric.update(experience)

    def evaluate(self, experience: Experience, **kwargs) -> float:
        """
        评估经验质量

        Args:
            experience (Experience): 经验样本
            **kwargs: 其他参数

        Returns:
            float: 质量评分，范围[0, 1]，值越大表示质量越高
        """
        if not self.metrics:
            return 0.5  # 默认中等质量

        # 计算加权平均质量
        total_weight = sum(weight for _, weight in self.metrics)
        weighted_quality = sum(metric.evaluate(experience, **kwargs) * weight for metric, weight in self.metrics)

        if total_weight > 0:
            return weighted_quality / total_weight
        else:
            return 0.5  # 默认中等质量


class ExperienceFilter:
    """
    经验过滤器

    使用质量评估指标过滤经验，保留高质量的经验样本。
    """

    def __init__(self, quality_metric: ExperienceQualityMetric, threshold: float = 0.5,
                 adaptive_threshold: bool = True, threshold_decay: float = 0.999,
                 min_threshold: float = 0.1, max_threshold: float = 0.9):
        """
        初始化经验过滤器

        Args:
            quality_metric (ExperienceQualityMetric): 质量评估指标
            threshold (float, optional): 过滤阈值. Defaults to 0.5.
            adaptive_threshold (bool, optional): 是否使用自适应阈值. Defaults to True.
            threshold_decay (float, optional): 阈值衰减因子. Defaults to 0.999.
            min_threshold (float, optional): 最小阈值. Defaults to 0.1.
            max_threshold (float, optional): 最大阈值. Defaults to 0.9.
        """
        self.quality_metric = quality_metric
        self.threshold = threshold
        self.adaptive_threshold = adaptive_threshold
        self.threshold_decay = threshold_decay
        self.min_threshold = min_threshold
        self.max_threshold = max_threshold

        # 统计信息
        self.stats = {
            "total": 0,
            "accepted": 0,
            "rejected": 0,
            "quality_history": deque(maxlen=1000),
            "threshold_history": deque(maxlen=1000)
        }

        # 记录当前时间
        self.last_update_time = time.time()

    def filter(self, experience: Experience, **kwargs) -> bool:
        """
        过滤经验

        Args:
            experience (Experience): 经验样本
            **kwargs: 其他参数，传递给质量评估指标

        Returns:
            bool: 是否保留经验，True表示保留，False表示过滤掉
        """
        # 更新统计信息
        self.stats["total"] += 1

        # 评估经验质量
        quality = self.quality_metric.evaluate(experience, **kwargs)

        # 记录质量
        self.stats["quality_history"].append(quality)

        # 更新质量统计指标
        if hasattr(self.quality_metric, "update"):
            self.quality_metric.update(experience)

        # 更新阈值
        self._update_threshold()

        # 记录阈值
        self.stats["threshold_history"].append(self.threshold)

        # 判断是否保留
        keep = quality >= self.threshold

        # 更新统计信息
        if keep:
            self.stats["accepted"] += 1
        else:
            self.stats["rejected"] += 1

        return keep

    def _update_threshold(self) -> None:
        """
        更新阈值
        """
        # 如果不使用自适应阈值，直接返回
        if not self.adaptive_threshold:
            return

        # 计算当前时间
        current_time = time.time()
        time_diff = current_time - self.last_update_time

        # 每秒更新一次
        if time_diff < 1.0:
            return

        # 更新时间
        self.last_update_time = current_time

        # 如果没有足够的质量历史，直接返回
        if len(self.stats["quality_history"]) < 10:
            return

        # 计算平均质量
        avg_quality = sum(self.stats["quality_history"]) / len(self.stats["quality_history"])

        # 计算接受率
        acceptance_rate = self.stats["accepted"] / max(1, self.stats["total"])

        # 调整阈值
        target_acceptance_rate = 0.5  # 目标接受率

        if acceptance_rate > target_acceptance_rate:
            # 如果接受率过高，增加阈值
            self.threshold = min(self.max_threshold, self.threshold * (1.0 / self.threshold_decay))
        elif acceptance_rate < target_acceptance_rate:
            # 如果接受率过低，减小阈值
            self.threshold = max(self.min_threshold, self.threshold * self.threshold_decay)

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        stats = self.stats.copy()

        # 计算接受率
        stats["acceptance_rate"] = stats["accepted"] / max(1, stats["total"])

        # 计算平均质量
        if stats["quality_history"]:
            stats["avg_quality"] = sum(stats["quality_history"]) / len(stats["quality_history"])
        else:
            stats["avg_quality"] = 0.0

        # 计算当前阈值
        stats["current_threshold"] = self.threshold

        return stats

    def reset_stats(self) -> None:
        """
        重置统计信息
        """
        self.stats = {
            "total": 0,
            "accepted": 0,
            "rejected": 0,
            "quality_history": deque(maxlen=1000),
            "threshold_history": deque(maxlen=1000)
        }

    def __str__(self) -> str:
        """
        转换为字符串表示

        Returns:
            str: 字符串表示
        """
        stats = self.get_stats()
        return f"ExperienceFilter(metric={self.quality_metric}, threshold={self.threshold:.2f}, " \
               f"acceptance_rate={stats['acceptance_rate']:.2%}, avg_quality={stats['avg_quality']:.2f})"
