"""
决策解释模式模块

提供详细的决策解释功能，包括决策过程、关键因素和可视化。
"""

import numpy as np
import torch
import matplotlib.pyplot as plt
import io
import base64
from typing import Dict, List, Any, Optional, Union, Tuple
from collections import defaultdict
import logging

from cardgame_ai.algorithms.explanation import DecisionExplainer
from cardgame_ai.algorithms.causal_inference import CausalInferenceModule
from cardgame_ai.games.doudizhu.card import Card
from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType
from cardgame_ai.core.base import State, Action

# 配置日志
logger = logging.getLogger(__name__)


class DecisionExplanationMode:
    """
    决策解释模式

    提供详细的决策解释功能，包括决策过程、关键因素和可视化。
    """

    def __init__(
        self,
        detail_level: str = "medium",
        visualization_enabled: bool = True,
        save_explanations: bool = False,
        save_dir: str = "explanations",
        enable_causal_inference: bool = True,
        causal_confidence_threshold: float = 0.7
    ):
        """
        初始化决策解释模式

        Args:
            detail_level: 解释详细程度，可选值为"low"、"medium"、"high"
            visualization_enabled: 是否启用可视化
            save_explanations: 是否保存解释结果
            save_dir: 解释结果保存目录
            enable_causal_inference: 是否启用因果推断
            causal_confidence_threshold: 因果推断置信度阈值
        """
        self.detail_level = detail_level
        self.visualization_enabled = visualization_enabled
        self.save_explanations = save_explanations
        self.save_dir = save_dir
        self.enable_causal_inference = enable_causal_inference
        self.causal_confidence_threshold = causal_confidence_threshold

        # 创建决策解释器
        self.explainer = DecisionExplainer(verbose=(detail_level == "high"))

        # 创建因果推断模块
        self.causal_inference = None
        if self.enable_causal_inference:
            self.causal_inference = CausalInferenceModule(
                confidence_threshold=self.causal_confidence_threshold
            )
            logger.info("已启用因果推断模块")

        # 解释历史
        self.explanation_history = []

        # 统计信息
        self.stats = defaultdict(int)

        logger.info(f"初始化决策解释模式，详细程度: {detail_level}，因果推断: {enable_causal_inference}")

    def explain_decision(
        self,
        decision_data: Dict[str, Any],
        decision_type: str = "mcts",
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        解释决策

        Args:
            decision_data: 决策数据
            decision_type: 决策类型，可选值为"mcts"、"network"、"rule"、"hybrid"
            context: 决策上下文信息

        Returns:
            Dict[str, Any]: 解释结果
        """
        # 记录统计信息
        self.stats["total_explanations"] += 1
        self.stats[f"{decision_type}_explanations"] += 1

        # 根据决策类型选择解释方法
        if decision_type == "mcts":
            explanation = self.explainer.explain_mcts_decision(decision_data)

            # 如果启用了因果推断，添加因果分析
            if self.enable_causal_inference and self.causal_inference and "causal_analysis" in decision_data:
                explanation["causal_analysis"] = decision_data["causal_analysis"]

        elif decision_type == "network":
            explanation = self.explainer.explain_network_prediction(decision_data)
        elif decision_type == "rule":
            explanation = self._explain_rule_decision(decision_data)
        elif decision_type == "hybrid":
            explanation = self._explain_hybrid_decision(decision_data)
        else:
            logger.warning(f"未知的决策类型: {decision_type}")
            explanation = {"error": f"未知的决策类型: {decision_type}"}

        # 添加上下文信息
        if context:
            explanation["context"] = context

        # 添加元数据
        explanation["meta"] = {
            "decision_type": decision_type,
            "detail_level": self.detail_level,
            "timestamp": np.datetime64('now')
        }

        # 保存解释历史
        self.explanation_history.append(explanation)

        # 如果需要保存解释结果
        if self.save_explanations:
            self._save_explanation(explanation)

        return explanation

    def _explain_rule_decision(self, decision_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        解释基于规则的决策

        Args:
            decision_data: 决策数据

        Returns:
            Dict[str, Any]: 解释结果
        """
        # 提取规则决策信息
        rule_name = decision_data.get("rule_name", "未知规则")
        rule_description = decision_data.get("rule_description", "")
        rule_priority = decision_data.get("rule_priority", 0)
        rule_conditions = decision_data.get("rule_conditions", [])
        rule_actions = decision_data.get("rule_actions", [])

        # 格式化解释结果
        explanation = {
            "summary": f"基于规则 '{rule_name}' (优先级: {rule_priority}) 做出决策",
            "rule_details": {
                "name": rule_name,
                "description": rule_description,
                "priority": rule_priority
            },
            "conditions": self._format_rule_conditions(rule_conditions),
            "actions": self._format_rule_actions(rule_actions),
            "visualizations": {}
        }

        # 如果启用可视化
        if self.visualization_enabled:
            explanation["visualizations"]["rule_priority"] = self._generate_rule_priority_chart(
                rule_name, rule_priority, decision_data.get("all_rules", [])
            )

        return explanation

    def _explain_hybrid_decision(self, decision_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        解释混合决策

        Args:
            decision_data: 决策数据

        Returns:
            Dict[str, Any]: 解释结果
        """
        # 提取混合决策信息
        components = decision_data.get("components", [])
        weights = decision_data.get("weights", [])
        final_decision = decision_data.get("final_decision", {})

        # 格式化解释结果
        explanation = {
            "summary": "基于多个决策组件的混合决策",
            "components": [],
            "weights": {},
            "final_decision": final_decision,
            "visualizations": {}
        }

        # 处理各个组件
        for i, component in enumerate(components):
            component_type = component.get("type", "未知")
            component_data = component.get("data", {})
            component_weight = weights[i] if i < len(weights) else 0.0

            # 根据组件类型解释决策
            if component_type == "mcts":
                component_explanation = self.explainer.explain_mcts_decision(component_data)
            elif component_type == "network":
                component_explanation = self.explainer.explain_network_prediction(component_data)
            elif component_type == "rule":
                component_explanation = self._explain_rule_decision(component_data)
            else:
                component_explanation = {"error": f"未知的组件类型: {component_type}"}

            # 添加组件解释
            explanation["components"].append({
                "type": component_type,
                "weight": component_weight,
                "explanation": component_explanation
            })

            # 记录权重
            explanation["weights"][component_type] = component_weight

        # 如果启用可视化
        if self.visualization_enabled:
            explanation["visualizations"]["component_weights"] = self._generate_component_weights_chart(
                explanation["weights"]
            )

        return explanation

    def _format_rule_conditions(self, conditions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        格式化规则条件

        Args:
            conditions: 规则条件列表

        Returns:
            List[Dict[str, Any]]: 格式化后的规则条件列表
        """
        formatted_conditions = []

        for condition in conditions:
            condition_type = condition.get("type", "未知")
            condition_value = condition.get("value", None)
            condition_result = condition.get("result", False)

            formatted_condition = {
                "type": condition_type,
                "value": condition_value,
                "result": condition_result,
                "description": self._get_condition_description(condition_type, condition_value, condition_result)
            }

            formatted_conditions.append(formatted_condition)

        return formatted_conditions

    def _format_rule_actions(self, actions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        格式化规则动作

        Args:
            actions: 规则动作列表

        Returns:
            List[Dict[str, Any]]: 格式化后的规则动作列表
        """
        formatted_actions = []

        for action in actions:
            action_type = action.get("type", "未知")
            action_value = action.get("value", None)

            formatted_action = {
                "type": action_type,
                "value": action_value,
                "description": self._get_action_description(action_type, action_value)
            }

            formatted_actions.append(formatted_action)

        return formatted_actions

    def _get_condition_description(
        self,
        condition_type: str,
        condition_value: Any,
        condition_result: bool
    ) -> str:
        """
        获取条件描述

        Args:
            condition_type: 条件类型
            condition_value: 条件值
            condition_result: 条件结果

        Returns:
            str: 条件描述
        """
        result_str = "满足" if condition_result else "不满足"

        if condition_type == "has_card_type":
            return f"是否有{condition_value}牌型: {result_str}"
        elif condition_type == "has_card":
            return f"是否有{condition_value}牌: {result_str}"
        elif condition_type == "is_landlord":
            return f"是否为地主: {result_str}"
        elif condition_type == "cards_remaining":
            return f"剩余牌数{condition_value}: {result_str}"
        elif condition_type == "game_stage":
            return f"游戏阶段为{condition_value}: {result_str}"
        else:
            return f"{condition_type}: {condition_value} -> {result_str}"

    def _get_action_description(self, action_type: str, action_value: Any) -> str:
        """
        获取动作描述

        Args:
            action_type: 动作类型
            action_value: 动作值

        Returns:
            str: 动作描述
        """
        if action_type == "play_card":
            return f"出牌: {action_value}"
        elif action_type == "pass":
            return "不出牌"
        elif action_type == "play_card_type":
            return f"出牌类型: {action_value}"
        else:
            return f"{action_type}: {action_value}"

    def _generate_rule_priority_chart(
        self,
        rule_name: str,
        rule_priority: float,
        all_rules: List[Dict[str, Any]]
    ) -> str:
        """
        生成规则优先级图表

        Args:
            rule_name: 规则名称
            rule_priority: 规则优先级
            all_rules: 所有规则列表

        Returns:
            str: Base64编码的图表
        """
        try:
            # 创建图表
            fig, ax = plt.subplots(figsize=(10, 6))

            # 提取规则名称和优先级
            rule_names = [rule.get("name", f"规则{i}") for i, rule in enumerate(all_rules)]
            priorities = [rule.get("priority", 0) for rule in all_rules]

            # 添加当前规则（如果不在列表中）
            if rule_name not in rule_names:
                rule_names.append(rule_name)
                priorities.append(rule_priority)

            # 创建条形图
            bars = ax.barh(rule_names, priorities, color='lightblue')

            # 高亮当前规则
            for i, name in enumerate(rule_names):
                if name == rule_name:
                    bars[i].set_color('orange')

            # 设置标题和标签
            ax.set_title("规则优先级")
            ax.set_xlabel("优先级")
            ax.set_ylabel("规则名称")

            # 添加网格线
            ax.grid(True, linestyle='--', alpha=0.7)

            # 调整布局
            plt.tight_layout()

            # 将图表转换为Base64编码的字符串
            buffer = io.BytesIO()
            plt.savefig(buffer, format='png')
            buffer.seek(0)
            image_base64 = base64.b64encode(buffer.read()).decode('utf-8')
            plt.close(fig)

            return f"data:image/png;base64,{image_base64}"
        except Exception as e:
            logger.error(f"生成规则优先级图表失败: {e}")
            return ""

    def _generate_component_weights_chart(self, weights: Dict[str, float]) -> str:
        """
        生成组件权重图表

        Args:
            weights: 组件权重字典

        Returns:
            str: Base64编码的图表
        """
        try:
            # 创建图表
            fig, ax = plt.subplots(figsize=(8, 8))

            # 提取组件名称和权重
            component_names = list(weights.keys())
            component_weights = list(weights.values())

            # 创建饼图
            ax.pie(
                component_weights,
                labels=component_names,
                autopct='%1.1f%%',
                startangle=90,
                shadow=True
            )

            # 设置标题
            ax.set_title("决策组件权重")

            # 调整布局
            plt.tight_layout()

            # 将图表转换为Base64编码的字符串
            buffer = io.BytesIO()
            plt.savefig(buffer, format='png')
            buffer.seek(0)
            image_base64 = base64.b64encode(buffer.read()).decode('utf-8')
            plt.close(fig)

            return f"data:image/png;base64,{image_base64}"
        except Exception as e:
            logger.error(f"生成组件权重图表失败: {e}")
            return ""

    def _save_explanation(self, explanation: Dict[str, Any]) -> None:
        """
        保存解释结果

        Args:
            explanation: 解释结果
        """
        import os
        import json
        from datetime import datetime

        try:
            # 创建保存目录
            os.makedirs(self.save_dir, exist_ok=True)

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            decision_type = explanation.get("meta", {}).get("decision_type", "unknown")
            filename = f"{timestamp}_{decision_type}_explanation.json"
            filepath = os.path.join(self.save_dir, filename)

            # 保存解释结果
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(explanation, f, ensure_ascii=False, indent=2)

            logger.info(f"解释结果已保存到: {filepath}")
        except Exception as e:
            logger.error(f"保存解释结果失败: {e}")

    def get_explanation_history(
        self,
        limit: Optional[int] = None,
        decision_type: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        获取解释历史

        Args:
            limit: 返回的历史记录数量限制
            decision_type: 决策类型过滤

        Returns:
            List[Dict[str, Any]]: 解释历史
        """
        # 过滤历史记录
        if decision_type:
            filtered_history = [
                explanation for explanation in self.explanation_history
                if explanation.get("meta", {}).get("decision_type") == decision_type
            ]
        else:
            filtered_history = self.explanation_history.copy()

        # 限制返回数量
        if limit is not None and limit > 0:
            return filtered_history[-limit:]

        return filtered_history

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        stats = dict(self.stats)

        # 如果启用了因果推断，添加因果推断模块的统计信息
        if self.enable_causal_inference and self.causal_inference is not None:
            causal_stats = self.causal_inference.get_stats()
            for key, value in causal_stats.items():
                stats[f"causal_{key}"] = value

        return stats

    def clear_history(self) -> None:
        """
        清除历史记录
        """
        self.explanation_history = []
        logger.info("已清除解释历史记录")

    def analyze_causal_impact(
        self,
        state: State,
        action: Action,
        alternative_actions: List[Action],
        search_results: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        分析动作的因果影响

        Args:
            state: 当前游戏状态
            action: 选择的动作
            alternative_actions: 备选动作列表
            search_results: 搜索结果，包含访问计数、价值估计等

        Returns:
            Optional[Dict[str, Any]]: 因果分析结果，如果未启用因果推断则返回None
        """
        # 记录统计信息
        self.stats["causal_analysis_requests"] += 1

        # 如果未启用因果推断，返回None
        if not self.enable_causal_inference or self.causal_inference is None:
            return None

        # 进行因果分析
        analysis = self.causal_inference.analyze_action_impact(
            state=state,
            action=action,
            alternative_actions=alternative_actions,
            search_results=search_results
        )

        return analysis