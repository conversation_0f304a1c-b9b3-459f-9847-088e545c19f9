"""
学习算法接口模块

定义学习算法的接口和抽象类，是框架的核心组件之一。
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Tuple, Optional, Union
import numpy as np
import os

from cardgame_ai.core.base import State, Action, Experience, Batch


class Algorithm(ABC):
    """
    学习算法接口
    
    定义学习算法的标准接口，包括更新模型、预测动作和价值、保存和加载模型等方法。
    所有具体算法都应该实现这个接口。
    """
    
    @abstractmethod
    def update(self, experience: Union[Experience, Batch]) -> Dict[str, float]:
        """
        使用经验数据更新模型
        
        Args:
            experience (Union[Experience, Batch]): 单个经验或经验批次
            
        Returns:
            Dict[str, float]: 更新指标，如损失值等
        """
        pass
    
    @abstractmethod
    def predict(self, state: Union[State, np.ndarray]) -> Tuple[List[float], float]:
        """
        预测状态的动作概率分布和价值
        
        Args:
            state (Union[State, np.ndarray]): 游戏状态或观察
            
        Returns:
            Tuple[List[float], float]: 动作概率分布、状态价值
        """
        pass
    
    @abstractmethod
    def save(self, path: str) -> None:
        """
        保存模型
        
        Args:
            path (str): 保存路径
        """
        pass
    
    @abstractmethod
    def load(self, path: str) -> None:
        """
        加载模型
        
        Args:
            path (str): 加载路径
        """
        pass
    
    @property
    @abstractmethod
    def name(self) -> str:
        """
        获取算法名称
        
        Returns:
            str: 算法名称
        """
        pass


class ValueBasedAlgorithm(Algorithm):
    """
    基于价值的算法抽象类
    
    为基于价值的强化学习算法提供一些通用实现。
    """
    
    def __init__(self, state_shape: Tuple[int, ...], action_shape: Tuple[int, ...], gamma: float = 0.99):
        """
        初始化基于价值的算法
        
        Args:
            state_shape (Tuple[int, ...]): 状态的形状
            action_shape (Tuple[int, ...]): 动作的形状
            gamma (float, optional): 折扣因子. Defaults to 0.99.
        """
        self._state_shape = state_shape
        self._action_shape = action_shape
        self._gamma = gamma
    
    @property
    def gamma(self) -> float:
        """
        获取折扣因子
        
        Returns:
            float: 折扣因子
        """
        return self._gamma
    
    @property
    def state_shape(self) -> Tuple[int, ...]:
        """
        获取状态的形状
        
        Returns:
            Tuple[int, ...]: 状态的形状
        """
        return self._state_shape
    
    @property
    def action_shape(self) -> Tuple[int, ...]:
        """
        获取动作的形状
        
        Returns:
            Tuple[int, ...]: 动作的形状
        """
        return self._action_shape


class PolicyBasedAlgorithm(Algorithm):
    """
    基于策略的算法抽象类
    
    为基于策略的强化学习算法提供一些通用实现。
    """
    
    def __init__(self, state_shape: Tuple[int, ...], action_shape: Tuple[int, ...], gamma: float = 0.99):
        """
        初始化基于策略的算法
        
        Args:
            state_shape (Tuple[int, ...]): 状态的形状
            action_shape (Tuple[int, ...]): 动作的形状
            gamma (float, optional): 折扣因子. Defaults to 0.99.
        """
        self._state_shape = state_shape
        self._action_shape = action_shape
        self._gamma = gamma
    
    @property
    def gamma(self) -> float:
        """
        获取折扣因子
        
        Returns:
            float: 折扣因子
        """
        return self._gamma
    
    @property
    def state_shape(self) -> Tuple[int, ...]:
        """
        获取状态的形状
        
        Returns:
            Tuple[int, ...]: 状态的形状
        """
        return self._state_shape
    
    @property
    def action_shape(self) -> Tuple[int, ...]:
        """
        获取动作的形状
        
        Returns:
            Tuple[int, ...]: 动作的形状
        """
        return self._action_shape


class ModelBasedAlgorithm(Algorithm):
    """
    基于模型的算法抽象类
    
    为基于模型的强化学习算法提供一些通用实现。
    """
    
    def __init__(self, state_shape: Tuple[int, ...], action_shape: Tuple[int, ...], gamma: float = 0.99):
        """
        初始化基于模型的算法
        
        Args:
            state_shape (Tuple[int, ...]): 状态的形状
            action_shape (Tuple[int, ...]): 动作的形状
            gamma (float, optional): 折扣因子. Defaults to 0.99.
        """
        self._state_shape = state_shape
        self._action_shape = action_shape
        self._gamma = gamma
    
    @property
    def gamma(self) -> float:
        """
        获取折扣因子
        
        Returns:
            float: 折扣因子
        """
        return self._gamma
    
    @property
    def state_shape(self) -> Tuple[int, ...]:
        """
        获取状态的形状
        
        Returns:
            Tuple[int, ...]: 状态的形状
        """
        return self._state_shape
    
    @property
    def action_shape(self) -> Tuple[int, ...]:
        """
        获取动作的形状
        
        Returns:
            Tuple[int, ...]: 动作的形状
        """
        return self._action_shape
    
    @abstractmethod
    def predict_next_state(self, state: Union[State, np.ndarray], action: Action) -> Tuple[Union[State, np.ndarray], float]:
        """
        预测执行动作后的下一个状态和奖励
        
        Args:
            state (Union[State, np.ndarray]): 当前状态
            action (Action): 执行的动作
            
        Returns:
            Tuple[Union[State, np.ndarray], float]: 预测的下一个状态和奖励
        """
        pass
