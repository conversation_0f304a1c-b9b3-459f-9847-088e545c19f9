# MCTS日志增强配置文件
# 
# 此配置文件定义了MCTS算法的详细日志记录设置
# 可以根据不同的使用场景调整配置参数

mcts_logging:
  # 基础配置
  enabled: true                    # 是否启用MCTS日志
  level: "DEBUG"                   # 日志级别: DEBUG, INFO, WARNING, ERROR
  output_format: "json"            # 输出格式: json, text
  
  # 功能开关
  enable_ucb_logging: true         # 是否记录UCB计算过程
  enable_expansion_logging: true   # 是否记录节点扩展过程
  enable_path_logging: true        # 是否记录搜索路径
  enable_performance_logging: true # 是否记录性能统计
  enable_simulation_logging: true  # 是否记录模拟结果
  
  # 输出配置
  log_to_file: true                # 是否输出到文件
  log_to_console: false            # 是否输出到控制台
  log_file_path: "logs/mcts_debug.log"  # 日志文件路径
  max_log_file_size: "100MB"       # 最大日志文件大小
  log_rotation_count: 5            # 日志轮转保留数量
  
  # 性能配置
  async_logging: true              # 是否使用异步日志
  buffer_size: 1000                # 日志缓冲区大小
  flush_interval: 1.0              # 刷新间隔（秒）
  
  # 详细程度配置
  max_children_logged: 20          # UCB计算时最多记录的子节点数
  max_path_depth_logged: 50        # 搜索路径最大记录深度
  include_game_state: true         # 是否包含游戏状态信息
  include_timestamps: true         # 是否包含时间戳
  
  # 过滤配置
  min_visit_count_for_logging: 1   # 最小访问次数才记录
  log_only_best_actions: false     # 是否只记录最佳动作

# 开发环境配置
development:
  mcts_logging:
    enabled: true
    level: "DEBUG"
    output_format: "text"
    log_to_console: true
    log_to_file: true
    async_logging: false
    max_children_logged: 10
    max_path_depth_logged: 20

# 生产环境配置
production:
  mcts_logging:
    enabled: true
    level: "INFO"
    output_format: "json"
    log_to_console: false
    log_to_file: true
    async_logging: true
    max_children_logged: 5
    max_path_depth_logged: 10
    enable_ucb_logging: false      # 生产环境关闭详细UCB日志
    enable_path_logging: false     # 生产环境关闭路径日志

# 性能测试配置
performance_test:
  mcts_logging:
    enabled: true
    level: "INFO"
    output_format: "json"
    log_to_console: false
    log_to_file: true
    async_logging: true
    enable_ucb_logging: false
    enable_expansion_logging: false
    enable_path_logging: false
    enable_simulation_logging: false
    enable_performance_logging: true  # 只记录性能统计

# 调试配置
debug:
  mcts_logging:
    enabled: true
    level: "DEBUG"
    output_format: "text"
    log_to_console: true
    log_to_file: true
    async_logging: false
    max_children_logged: 50
    max_path_depth_logged: 100
    include_game_state: true
    include_timestamps: true
    min_visit_count_for_logging: 1
