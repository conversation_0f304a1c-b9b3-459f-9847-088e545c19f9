"""
对手采样器模块

提供从不同来源采样对手策略的功能。
"""

import os
import random
import logging
import numpy as np
import torch
from typing import Dict, List, Tuple, Any, Optional, Union, Callable

from cardgame_ai.utils.model_saver import ModelSaver
from cardgame_ai.algorithms.opponent_generation.gan_policy_generator import GANPolicyGenerator
from cardgame_ai.algorithms.opponent_generation.self_play_evolution import SelfPlayEvolution
from cardgame_ai.algorithms.opponent_generation.human_style_generator import HumanStyleGenerator

# 配置日志
logger = logging.getLogger(__name__)


class OpponentSampler:
    """
    对手采样器

    从不同来源采样对手策略。
    """

    def __init__(
        self,
        checkpoint_dir: str = None,
        gan_model_path: str = None,
        human_style_model_path: str = None,
        sampling_strategy: str = 'mixed',
        sampling_weights: Dict[str, float] = None,
        model_class: Any = None,
        model_config: Dict[str, Any] = None,
        device: str = None
    ):
        """
        初始化对手采样器

        Args:
            checkpoint_dir: 检查点目录，用于自对弈历史采样
            gan_model_path: GAN模型路径，用于GAN生成对手
            human_style_model_path: 人类风格模型路径，用于生成人类风格对手
            sampling_strategy: 采样策略，可选值为'self_play'（自对弈历史）, 'gan'（GAN生成）,
                              'human_style'（人类风格）, 'mixed'（混合采样）
            sampling_weights: 混合采样权重，键为采样策略，值为权重
            model_class: 模型类，用于加载检查点
            model_config: 模型配置，用于创建模型实例
            device: 计算设备
        """
        # 设置设备
        self.device = device if device else ('cuda' if torch.cuda.is_available() else 'cpu')

        # 保存参数
        self.checkpoint_dir = checkpoint_dir
        self.gan_model_path = gan_model_path
        self.human_style_model_path = human_style_model_path
        self.sampling_strategy = sampling_strategy
        self.model_class = model_class
        self.model_config = model_config if model_config else {}

        # 设置采样权重
        if sampling_weights is None:
            self.sampling_weights = {
                'self_play': 0.4,
                'gan': 0.3,
                'human_style': 0.3
            }
        else:
            self.sampling_weights = sampling_weights

        # 初始化采样器
        self.self_play_sampler = None
        self.gan_generator = None
        self.human_style_generator = None

        # 初始化可用的采样策略
        self.available_strategies = []

        # 初始化自对弈采样器
        if checkpoint_dir and os.path.exists(checkpoint_dir):
            self.self_play_sampler = SelfPlayEvolution(
                checkpoint_dir=checkpoint_dir,
                sampling_strategy='latest_k',
                k=5,
                model_class=model_class,
                model_config=model_config,
                device=device
            )
            self.available_strategies.append('self_play')

        # 初始化GAN生成器
        if gan_model_path and os.path.exists(gan_model_path):
            try:
                # 加载GAN模型
                self.gan_generator = self._load_gan_model(gan_model_path)
                self.available_strategies.append('gan')
            except Exception as e:
                logger.error(f"加载GAN模型失败: {e}")

        # 初始化人类风格生成器
        if human_style_model_path and os.path.exists(human_style_model_path):
            try:
                # 加载人类风格模型
                self.human_style_generator = self._load_human_style_model(human_style_model_path)
                self.available_strategies.append('human_style')
            except Exception as e:
                logger.error(f"加载人类风格模型失败: {e}")

        # 如果没有可用的采样策略，使用随机策略
        if not self.available_strategies:
            logger.warning("没有可用的采样策略，将使用随机策略")
            self.sampling_strategy = 'random'

        # 初始化统计信息
        self.stats = {
            "sampling_history": [],
            "strategy_usage": {strategy: 0 for strategy in self.available_strategies}
        }

        logger.info(f"初始化对手采样器，可用策略: {self.available_strategies}")

    def _load_gan_model(self, model_path: str) -> GANPolicyGenerator:
        """
        加载GAN模型

        Args:
            model_path: 模型路径

        Returns:
            GAN策略生成器
        """
        # 加载模型
        checkpoint = torch.load(model_path, map_location=self.device)

        # 获取配置
        config = checkpoint['config']

        # 创建GAN生成器
        gan_generator = GANPolicyGenerator(
            policy_dim=config['policy_dim'],
            latent_dim=config['latent_dim'],
            device=self.device
        )

        # 加载状态
        gan_generator.generator.load_state_dict(checkpoint['generator_state_dict'])

        return gan_generator

    def _load_human_style_model(self, model_path: str) -> HumanStyleGenerator:
        """
        加载人类风格模型

        Args:
            model_path: 模型路径

        Returns:
            人类风格生成器
        """
        # 创建一个临时的AI策略
        if self.model_class is not None:
            ai_policy = self.model_class(**self.model_config)
        else:
            # 创建一个简单的随机策略
            def random_policy(state):
                return np.random.random(self.model_config.get('policy_dim', 100))
            ai_policy = random_policy

        # 创建人类风格生成器
        human_style_generator = HumanStyleGenerator(
            ai_policy=ai_policy,
            human_data_path="",  # 不需要加载人类数据
            policy_dim=self.model_config.get('policy_dim', 100),
            device=self.device
        )

        # 加载模型
        human_style_generator.load(model_path)

        return human_style_generator

    def sample_opponent_policy(self, state: Optional[Any] = None) -> Any:
        """
        采样对手策略

        Args:
            state: 游戏状态，用于生成策略

        Returns:
            采样的对手策略
        """
        # 如果没有可用的采样策略，返回None
        if not self.available_strategies:
            logger.warning("没有可用的采样策略")
            return None

        # 选择采样策略
        if self.sampling_strategy == 'mixed':
            # 计算可用策略的权重
            available_weights = {
                strategy: self.sampling_weights[strategy]
                for strategy in self.available_strategies
            }

            # 归一化权重
            total_weight = sum(available_weights.values())
            normalized_weights = {
                strategy: weight / total_weight
                for strategy, weight in available_weights.items()
            }

            # 按权重选择策略
            strategies = list(normalized_weights.keys())
            weights = list(normalized_weights.values())
            strategy = np.random.choice(strategies, p=weights)
        else:
            # 使用指定的策略
            strategy = self.sampling_strategy

            # 如果指定的策略不可用，随机选择一个可用的策略
            if strategy not in self.available_strategies:
                strategy = random.choice(self.available_strategies)

        # 记录策略使用
        self.stats["strategy_usage"][strategy] += 1

        # 根据策略采样对手
        if strategy == 'self_play':
            # 从自对弈历史采样
            return self.self_play_sampler.sample_opponent_policy()

        elif strategy == 'gan':
            # 从GAN生成
            style = random.choice(['random', 'diverse', 'extreme'])
            return self.gan_generator.generate_opponent_policy(style=style)

        elif strategy == 'human_style':
            # 从人类风格生成
            style = random.choice(['random', 'conservative', 'aggressive'])
            return self.human_style_generator.generate_opponent_policy(state, style=style)

        else:
            logger.warning(f"不支持的采样策略: {strategy}")
            return None

    def refresh_self_play_checkpoints(self) -> None:
        """
        刷新自对弈检查点
        """
        if self.self_play_sampler is not None:
            self.self_play_sampler.refresh_checkpoints()

    def get_diverse_opponents(self, num_opponents: int = 3) -> List[Any]:
        """
        获取多样化的对手

        Args:
            num_opponents: 对手数量

        Returns:
            多样化的对手列表
        """
        opponents = []

        # 计算每种策略的对手数量
        if len(self.available_strategies) == 0:
            return []

        opponents_per_strategy = num_opponents // len(self.available_strategies)
        remaining = num_opponents % len(self.available_strategies)

        # 从每种策略采样对手
        for strategy in self.available_strategies:
            # 计算当前策略的对手数量
            strategy_opponents = opponents_per_strategy
            if remaining > 0:
                strategy_opponents += 1
                remaining -= 1

            # 采样对手
            for _ in range(strategy_opponents):
                if strategy == 'self_play' and self.self_play_sampler is not None:
                    # 从自对弈历史采样
                    opponent = self.self_play_sampler.sample_opponent_policy()
                    opponents.append(opponent)

                elif strategy == 'gan' and self.gan_generator is not None:
                    # 从GAN生成
                    style = random.choice(['random', 'diverse', 'extreme'])
                    opponent = self.gan_generator.generate_opponent_policy(style=style)
                    opponents.append(opponent)

                elif strategy == 'human_style' and self.human_style_generator is not None:
                    # 从人类风格生成
                    style = random.choice(['random', 'conservative', 'aggressive'])
                    opponent = self.human_style_generator.generate_opponent_policy(None, style=style)
                    opponents.append(opponent)

        return opponents

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            统计信息
        """
        stats = {
            "available_strategies": self.available_strategies,
            "sampling_strategy": self.sampling_strategy,
            "sampling_weights": self.sampling_weights,
            "strategy_usage": self.stats["strategy_usage"]
        }

        # 添加自对弈采样器统计信息
        if self.self_play_sampler is not None:
            stats["self_play"] = self.self_play_sampler.get_stats()

        # 添加GAN生成器统计信息
        if self.gan_generator is not None:
            stats["gan"] = {
                "latent_dim": self.gan_generator.latent_dim,
                "policy_dim": self.gan_generator.policy_dim
            }

        # 添加人类风格生成器统计信息
        if self.human_style_generator is not None:
            stats["human_style"] = self.human_style_generator.get_stats()

        return stats
