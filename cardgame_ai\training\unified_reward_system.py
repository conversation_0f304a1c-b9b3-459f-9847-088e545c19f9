"""
统一奖励系统

提供统一的奖励计算、配置管理和动态调整功能。
"""

import json
import logging
from typing import Dict, Any, Optional, List, Union, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import numpy as np

logger = logging.getLogger(__name__)


class GamePhase(Enum):
    """游戏阶段枚举"""
    BIDDING = "bidding"      # 叫地主阶段
    PLAYING = "playing"      # 出牌阶段
    ENDGAME = "endgame"      # 残局阶段


class PlayerRole(Enum):
    """玩家角色枚举"""
    LANDLORD = "landlord"    # 地主
    FARMER = "farmer"        # 农民


@dataclass
class RewardConfig:
    """奖励配置类"""
    # 基础奖励
    win_reward: float = 1.0
    lose_reward: float = -1.0
    
    # 炸弹奖励
    bomb_base_reward: float = 0.05
    bomb_win_multiplier: float = 2.0
    bomb_lose_penalty: float = 2.5
    rocket_multiplier: float = 1.5
    
    # 角色特定奖励权重
    landlord_role_factor: float = 1.0
    farmer_role_factor: float = 0.9
    
    # 协作奖励
    farmer_cooperation_weight: float = 0.7
    team_reward_weight: float = 0.8
    
    # 叫地主阶段奖励
    bid_reward_weight: float = 0.2
    grab_reward_weight: float = 0.3
    landlord_bid_threshold: float = 0.6
    farmer_bid_threshold: float = 0.4
    
    # 出牌奖励
    combo_reward: float = 0.05
    progress_reward_factor: float = 1.5
    type_reward_factor: float = 1.2
    
    # 动态调整参数
    enable_dynamic_adjustment: bool = True
    performance_window: int = 100
    adjustment_rate: float = 0.01
    
    # 情境感知参数
    enable_context_aware: bool = True
    endgame_threshold: int = 5
    critical_moment_multiplier: float = 2.0


class UnifiedRewardSystem:
    """统一奖励系统"""
    
    def __init__(self, config: Optional[RewardConfig] = None):
        """
        初始化奖励系统
        
        Args:
            config: 奖励配置，如果为None则使用默认配置
        """
        self.config = config or RewardConfig()
        self.performance_history = []
        self.reward_stats = {
            'total_rewards': 0,
            'bomb_rewards': 0,
            'cooperation_rewards': 0,
            'bid_rewards': 0
        }
        
    def calculate_terminal_reward(
        self,
        winner_id: int,
        player_id: int,
        player_role: PlayerRole,
        bomb_used: bool = False,
        bomb_count: int = 0
    ) -> float:
        """
        计算终局奖励
        
        Args:
            winner_id: 获胜玩家ID
            player_id: 当前玩家ID
            player_role: 玩家角色
            bomb_used: 是否使用了炸弹
            bomb_count: 炸弹使用次数
            
        Returns:
            终局奖励值
        """
        is_winner = (winner_id == player_id)
        
        # 基础奖励
        if is_winner:
            base_reward = self.config.win_reward
        else:
            base_reward = self.config.lose_reward
            
        # 炸弹调整
        if bomb_used:
            if is_winner:
                bomb_bonus = self.config.bomb_base_reward * self.config.bomb_win_multiplier * bomb_count
                base_reward += bomb_bonus
            else:
                bomb_penalty = self.config.bomb_base_reward * self.config.bomb_lose_penalty * bomb_count
                base_reward -= bomb_penalty
                
        # 角色调整
        if player_role == PlayerRole.LANDLORD:
            base_reward *= self.config.landlord_role_factor
        else:
            base_reward *= self.config.farmer_role_factor
            
        return base_reward
    
    def calculate_action_reward(
        self,
        action_type: str,
        player_role: PlayerRole,
        game_phase: GamePhase,
        context: Dict[str, Any]
    ) -> Dict[str, float]:
        """
        计算动作奖励
        
        Args:
            action_type: 动作类型
            player_role: 玩家角色
            game_phase: 游戏阶段
            context: 上下文信息
            
        Returns:
            奖励组件字典
        """
        reward_components = {}
        
        if game_phase == GamePhase.BIDDING:
            reward_components.update(self._calculate_bidding_reward(
                action_type, player_role, context
            ))
        elif game_phase == GamePhase.PLAYING:
            reward_components.update(self._calculate_playing_reward(
                action_type, player_role, context
            ))
            
        # 应用情境感知调整
        if self.config.enable_context_aware:
            reward_components = self._apply_context_adjustment(
                reward_components, context
            )
            
        return reward_components
    
    def _calculate_bidding_reward(
        self,
        action_type: str,
        player_role: PlayerRole,
        context: Dict[str, Any]
    ) -> Dict[str, float]:
        """计算叫地主阶段奖励"""
        reward_components = {}
        
        hand_strength = context.get('hand_strength', 0.5)
        
        if action_type in ['bid', 'grab']:
            # 根据手牌强度和角色计算奖励
            if player_role == PlayerRole.LANDLORD:
                threshold = self.config.landlord_bid_threshold
                weight = self.config.grab_reward_weight if action_type == 'grab' else self.config.bid_reward_weight
            else:
                threshold = self.config.farmer_bid_threshold
                weight = self.config.grab_reward_weight * 0.5 if action_type == 'grab' else self.config.bid_reward_weight * 0.5
                
            if hand_strength > threshold:
                reward_components['bid_reward'] = weight
            else:
                reward_components['bid_reward'] = -weight * 0.5
                
        elif action_type == 'pass':
            # 不叫的奖励
            if hand_strength < self.config.farmer_bid_threshold:
                reward_components['bid_reward'] = self.config.bid_reward_weight * 0.3
            else:
                reward_components['bid_reward'] = -self.config.bid_reward_weight * 0.2
                
        return reward_components
    
    def _calculate_playing_reward(
        self,
        action_type: str,
        player_role: PlayerRole,
        context: Dict[str, Any]
    ) -> Dict[str, float]:
        """计算出牌阶段奖励"""
        reward_components = {}
        
        # 基础出牌奖励
        if action_type != 'pass':
            # 牌型奖励
            card_type = context.get('card_type', 'single')
            type_reward = self._get_type_reward(card_type)
            reward_components['type_reward'] = type_reward * self.config.type_reward_factor
            
            # 进度奖励
            cards_played = context.get('cards_played', 1)
            total_cards = context.get('total_cards', 17)
            progress = cards_played / total_cards
            reward_components['progress_reward'] = progress * self.config.progress_reward_factor
            
            # 炸弹奖励
            if card_type in ['bomb', 'rocket']:
                bomb_reward = self.config.bomb_base_reward
                if card_type == 'rocket':
                    bomb_reward *= self.config.rocket_multiplier
                reward_components['bomb_reward'] = bomb_reward
                
            # 连击奖励
            if context.get('can_beat_last', False):
                reward_components['combo_reward'] = self.config.combo_reward
                
        # 农民协作奖励
        if player_role == PlayerRole.FARMER:
            cooperation_reward = self._calculate_cooperation_reward(context)
            if cooperation_reward > 0:
                reward_components['cooperation_reward'] = cooperation_reward
                
        return reward_components
    
    def _get_type_reward(self, card_type: str) -> float:
        """获取牌型基础奖励"""
        type_rewards = {
            'single': 0.01,
            'pair': 0.02,
            'triple': 0.03,
            'straight': 0.05,
            'bomb': 0.1,
            'rocket': 0.15
        }
        return type_rewards.get(card_type, 0.01)
    
    def _calculate_cooperation_reward(self, context: Dict[str, Any]) -> float:
        """计算农民协作奖励"""
        cooperation_score = context.get('cooperation_score', 0.0)
        return cooperation_score * self.config.farmer_cooperation_weight
    
    def _apply_context_adjustment(
        self,
        reward_components: Dict[str, float],
        context: Dict[str, Any]
    ) -> Dict[str, float]:
        """应用情境感知调整"""
        adjusted_components = reward_components.copy()
        
        # 残局阶段调整
        cards_left = context.get('cards_left', 10)
        if cards_left <= self.config.endgame_threshold:
            multiplier = self.config.critical_moment_multiplier
            for key in adjusted_components:
                adjusted_components[key] *= multiplier
                
        return adjusted_components
    
    def calculate_total_reward(self, reward_components: Dict[str, float]) -> float:
        """计算总奖励"""
        return sum(reward_components.values())
    
    def update_performance_history(self, episode_reward: float):
        """更新性能历史"""
        self.performance_history.append(episode_reward)
        
        # 保持窗口大小
        if len(self.performance_history) > self.config.performance_window:
            self.performance_history.pop(0)
            
        # 动态调整
        if self.config.enable_dynamic_adjustment:
            self._dynamic_adjustment()
    
    def _dynamic_adjustment(self):
        """动态调整奖励参数"""
        if len(self.performance_history) < self.config.performance_window:
            return
            
        # 计算最近性能
        recent_performance = np.mean(self.performance_history[-50:])
        overall_performance = np.mean(self.performance_history)
        
        # 如果最近性能下降，增加奖励强度
        if recent_performance < overall_performance * 0.9:
            self.config.bomb_base_reward *= (1 + self.config.adjustment_rate)
            self.config.combo_reward *= (1 + self.config.adjustment_rate)
            logger.info(f"动态调整：增加奖励强度 - 炸弹奖励: {self.config.bomb_base_reward:.4f}")
    
    def get_reward_statistics(self) -> Dict[str, Any]:
        """获取奖励统计信息"""
        stats = self.reward_stats.copy()
        
        if self.performance_history:
            stats.update({
                'avg_episode_reward': np.mean(self.performance_history),
                'reward_std': np.std(self.performance_history),
                'recent_trend': np.mean(self.performance_history[-10:]) if len(self.performance_history) >= 10 else 0
            })
            
        return stats
    
    def save_config(self, filepath: str):
        """保存奖励配置"""
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(asdict(self.config), f, indent=2, ensure_ascii=False)
            
    def load_config(self, filepath: str):
        """加载奖励配置"""
        with open(filepath, 'r', encoding='utf-8') as f:
            config_dict = json.load(f)
            self.config = RewardConfig(**config_dict)
    
    def reset_statistics(self):
        """重置统计信息"""
        self.performance_history.clear()
        self.reward_stats = {
            'total_rewards': 0,
            'bomb_rewards': 0,
            'cooperation_rewards': 0,
            'bid_rewards': 0
        }
