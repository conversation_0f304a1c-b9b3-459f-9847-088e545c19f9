"""
游戏描述器模块

提供游戏描述器类，用于描述游戏的规则和特性。
"""
from typing import Dict, Any, List, Tuple, Optional, Union
import json
import yaml


class GameDescriptor:
    """
    游戏描述器类
    
    用于描述游戏的规则和特性，便于框架理解和处理不同的游戏。
    """
    
    def __init__(
        self,
        name: str,
        num_players: int,
        is_zero_sum: bool,
        is_perfect_information: bool,
        is_deterministic: bool,
        rules: Dict[str, Any],
        state_shape: Tuple[int, ...],
        action_shape: Tuple[int, ...],
        description: Optional[str] = None
    ):
        """
        初始化游戏描述器
        
        Args:
            name (str): 游戏名称
            num_players (int): 玩家数量
            is_zero_sum (bool): 是否为零和游戏
            is_perfect_information (bool): 是否为完全信息游戏
            is_deterministic (bool): 是否为确定性游戏
            rules (Dict[str, Any]): 游戏规则
            state_shape (<PERSON><PERSON>[int, ...]): 状态的形状
            action_shape (Tuple[int, ...]): 动作的形状
            description (Optional[str], optional): 游戏描述. Defaults to None.
        """
        self.name = name
        self.num_players = num_players
        self.is_zero_sum = is_zero_sum
        self.is_perfect_information = is_perfect_information
        self.is_deterministic = is_deterministic
        self.rules = rules
        self.state_shape = state_shape
        self.action_shape = action_shape
        self.description = description
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典表示
        
        Returns:
            Dict[str, Any]: 字典表示
        """
        return {
            'name': self.name,
            'num_players': self.num_players,
            'is_zero_sum': self.is_zero_sum,
            'is_perfect_information': self.is_perfect_information,
            'is_deterministic': self.is_deterministic,
            'rules': self.rules,
            'state_shape': self.state_shape,
            'action_shape': self.action_shape,
            'description': self.description
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'GameDescriptor':
        """
        从字典创建游戏描述器
        
        Args:
            data (Dict[str, Any]): 字典表示
            
        Returns:
            GameDescriptor: 游戏描述器对象
        """
        return cls(
            name=data['name'],
            num_players=data['num_players'],
            is_zero_sum=data['is_zero_sum'],
            is_perfect_information=data['is_perfect_information'],
            is_deterministic=data['is_deterministic'],
            rules=data['rules'],
            state_shape=tuple(data['state_shape']),
            action_shape=tuple(data['action_shape']),
            description=data.get('description')
        )
    
    def to_json(self) -> str:
        """
        转换为JSON字符串
        
        Returns:
            str: JSON字符串
        """
        return json.dumps(self.to_dict(), indent=2, ensure_ascii=False)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'GameDescriptor':
        """
        从JSON字符串创建游戏描述器
        
        Args:
            json_str (str): JSON字符串
            
        Returns:
            GameDescriptor: 游戏描述器对象
        """
        data = json.loads(json_str)
        return cls.from_dict(data)
    
    def to_yaml(self) -> str:
        """
        转换为YAML字符串
        
        Returns:
            str: YAML字符串
        """
        return yaml.dump(self.to_dict(), default_flow_style=False)
    
    @classmethod
    def from_yaml(cls, yaml_str: str) -> 'GameDescriptor':
        """
        从YAML字符串创建游戏描述器
        
        Args:
            yaml_str (str): YAML字符串
            
        Returns:
            GameDescriptor: 游戏描述器对象
        """
        data = yaml.safe_load(yaml_str)
        return cls.from_dict(data)
    
    def save(self, path: str) -> None:
        """
        保存到文件
        
        Args:
            path (str): 文件路径
        """
        if path.endswith('.json'):
            with open(path, 'w', encoding='utf-8') as f:
                f.write(self.to_json())
        elif path.endswith('.yaml') or path.endswith('.yml'):
            with open(path, 'w', encoding='utf-8') as f:
                f.write(self.to_yaml())
        else:
            raise ValueError(f"不支持的文件格式: {path}")
    
    @classmethod
    def load(cls, path: str) -> 'GameDescriptor':
        """
        从文件加载
        
        Args:
            path (str): 文件路径
            
        Returns:
            GameDescriptor: 游戏描述器对象
        """
        with open(path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if path.endswith('.json'):
            return cls.from_json(content)
        elif path.endswith('.yaml') or path.endswith('.yml'):
            return cls.from_yaml(content)
        else:
            raise ValueError(f"不支持的文件格式: {path}")
    
    def __str__(self) -> str:
        """
        转换为字符串表示
        
        Returns:
            str: 字符串表示
        """
        return f"GameDescriptor(name={self.name}, num_players={self.num_players})"
    
    def __repr__(self) -> str:
        """
        转换为详细字符串表示
        
        Returns:
            str: 详细字符串表示
        """
        return f"GameDescriptor({self.to_dict()})"
