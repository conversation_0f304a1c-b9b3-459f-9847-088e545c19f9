#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
修复版斗地主游戏自我对弈模块

修复了DouDizhuSelfPlay类中的save_experiences方法不存在的问题。
"""

import os
import time
import logging
import pickle
from typing import Dict, Any, List, Optional

from cardgame_ai.core.agent import Agent
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.training.doudizhu_self_play import play_doudizhu_game
from cardgame_ai.training.bidding_phase_handler import BiddingPhaseHandler
from cardgame_ai.training.self_play import SelfPlay


class FixedDouDizhuSelfPlay(SelfPlay):
    """修复版斗地主游戏自我对弈类"""

    def save_experiences(self, experiences: List[Dict[str, Any]], save_file: str):
        """
        保存经验数据

        Args:
            experiences (List[Dict[str, Any]]): 经验数据列表
            save_file (str): 保存文件路径
        """
        # 创建保存目录
        os.makedirs(os.path.dirname(save_file), exist_ok=True)
        
        # 保存经验数据
        with open(save_file, 'wb') as f:
            pickle.dump(experiences, f)
        
        self.logger.info(f"保存经验数据: {save_file}")

    def generate_experience(self, env: DouDizhuEnvironment, agent: Agent, num_games: int,
                           temperature: float = 1.0, save: bool = True,
                           parallel: bool = False, disable_tqdm: bool = False,
                           bidding_handler: Optional[BiddingPhaseHandler] = None) -> List[Dict[str, Any]]:
        """
        生成斗地主自我对弈经验数据

        Args:
            env (DouDizhuEnvironment): 斗地主游戏环境
            agent (Agent): 用于自我对弈的代理
            num_games (int): 对弈的游戏数
            temperature (float, optional): 温度参数，控制动作采样的随机性. Defaults to 1.0.
            save (bool, optional): 是否保存经验数据. Defaults to True.
            parallel (bool, optional): 是否并行生成. Defaults to False.
            disable_tqdm (bool, optional): 是否禁用进度条. Defaults to False.
            bidding_handler (Optional[BiddingPhaseHandler], optional): 叫地主/抢地主阶段处理器. Defaults to None.

        Returns:
            List[Dict[str, Any]]: 生成的经验数据
        """
        start_time = time.time()
        all_experiences = []

        # 顺序生成游戏（暂不支持并行，因为需要处理完整的游戏流程）
        self.logger.info(f"开始生成斗地主自我对弈数据，游戏数: {num_games}")

        for i in range(num_games):
            game_experiences = play_doudizhu_game(env, agent, temperature, bidding_handler)
            all_experiences.extend(game_experiences)

            if (i + 1) % 10 == 0 or (i + 1) == num_games:
                elapsed_time = time.time() - start_time
                self.logger.info(
                    f"已完成 {i + 1}/{num_games} 局游戏 | "
                    f"时间: {elapsed_time:.2f}s | "
                    f"平均每局时间: {elapsed_time / (i + 1):.2f}s"
                )

        # 保存经验数据
        if save and self.save_path:
            save_file = os.path.join(self.save_path, f"experiences_{int(time.time())}.pkl")
            self.save_experiences(all_experiences, save_file)

        return all_experiences
