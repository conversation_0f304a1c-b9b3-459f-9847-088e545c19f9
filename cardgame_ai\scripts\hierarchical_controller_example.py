#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
层次控制器示例脚本

展示如何使用层次控制器动态选择执行高层策略还是低层策略。
"""

import os
import sys
import argparse
import logging
import numpy as np
import torch
from typing import Dict, List, Any, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from cardgame_ai.algorithms.hrl.high_level_policy import HighLevelPolicy
from cardgame_ai.algorithms.hrl.low_level_policy import LowLevelPolicy
from cardgame_ai.algorithms.hrl.hierarchical_controller import HierarchicalController
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.games.doudizhu.state import DouDizhuState
from cardgame_ai.games.doudizhu.card_group import CardGroupType

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='层次控制器示例')
    
    parser.add_argument('--high_level_model', type=str, default='models/hrl/high_level_policy.pt',
                        help='高层策略模型路径')
    parser.add_argument('--low_level_model', type=str, default='models/hrl/low_level_policy.pt',
                        help='低层策略模型路径')
    parser.add_argument('--complexity_threshold', type=float, default=0.6,
                        help='复杂度阈值，高于此值使用高层策略')
    parser.add_argument('--confidence_threshold', type=float, default=0.8,
                        help='置信度阈值，高于此值直接使用低层策略')
    parser.add_argument('--dynamic_scheduling', action='store_true',
                        help='是否使用动态调度')
    parser.add_argument('--use_history', action='store_true',
                        help='是否使用历史信息')
    parser.add_argument('--history_window', type=int, default=5,
                        help='历史窗口大小')
    parser.add_argument('--num_games', type=int, default=5,
                        help='模拟游戏数量')
    parser.add_argument('--seed', type=int, default=None,
                        help='随机种子')
    
    return parser.parse_args()


def simulate_game(controller: HierarchicalController, env: DouDizhuEnvironment):
    """
    模拟游戏
    
    Args:
        controller: 层次控制器
        env: 游戏环境
    """
    # 重置环境
    state = env.reset()
    done = False
    
    # 游戏循环
    while not done:
        # 获取当前玩家
        current_player = state.current_player
        
        # 获取合法动作
        legal_actions = env.get_legal_actions(state)
        
        # 如果是玩家0（我们的AI）
        if current_player == 0:
            # 使用层次控制器做出决策
            action, decision_info = controller.decide(state, legal_actions)
            
            # 打印决策信息
            logger.info(f"决策模式: {decision_info['mode']}, 复杂度: {decision_info['complexity']:.2f}, 置信度: {decision_info['confidence']:.2f}")
        else:
            # 其他玩家随机选择动作
            action = np.random.choice(legal_actions) if legal_actions else None
        
        # 执行动作
        next_state, reward, done, info = env.step(action)
        
        # 更新状态
        state = next_state
        
        # 如果游戏结束，打印结果
        if done:
            winner = info.get('winner', -1)
            logger.info(f"游戏结束，获胜玩家: {winner}")


def main():
    """主函数"""
    args = parse_args()
    
    # 设置随机种子
    if args.seed is not None:
        np.random.seed(args.seed)
        torch.manual_seed(args.seed)
    
    # 创建环境
    env = DouDizhuEnvironment()
    
    # 获取观察和动作空间
    observation_shape = env.observation_space.shape
    action_shape = (env.action_space.n,)
    
    # 创建高层策略
    high_level_policy = HighLevelPolicy(
        state_dim=observation_shape[0],
        hidden_dims=[256, 128],
        dropout=0.1
    )
    
    # 创建低层策略
    low_level_policy = LowLevelPolicy(
        state_dim=observation_shape[0],
        low_level_action_dim=env.action_space.n,
        hidden_dims=[256, 128],
        dropout=0.1
    )
    
    # 加载模型（如果存在）
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    if os.path.exists(args.high_level_model):
        high_level_policy.load_state_dict(torch.load(args.high_level_model, map_location=device))
        logger.info(f"已加载高层策略模型: {args.high_level_model}")
    
    if os.path.exists(args.low_level_model):
        low_level_policy.load_state_dict(torch.load(args.low_level_model, map_location=device))
        logger.info(f"已加载低层策略模型: {args.low_level_model}")
    
    # 创建层次控制器
    controller = HierarchicalController(
        high_level_policy=high_level_policy,
        low_level_policy=low_level_policy,
        complexity_threshold=args.complexity_threshold,
        confidence_threshold=args.confidence_threshold,
        dynamic_scheduling=args.dynamic_scheduling,
        use_history=args.use_history,
        history_window=args.history_window
    )
    
    # 模拟游戏
    for i in range(args.num_games):
        logger.info(f"开始游戏 {i+1}/{args.num_games}")
        simulate_game(controller, env)
    
    # 打印统计信息
    stats = controller.get_stats()
    logger.info(f"总决策次数: {stats['total_calls']}")
    logger.info(f"高层策略调用次数: {stats['high_level_calls']} ({stats['high_level_ratio']:.2%})")
    logger.info(f"低层策略调用次数: {stats['low_level_calls']} ({stats['low_level_ratio']:.2%})")
    logger.info(f"直接策略调用次数: {stats['direct_calls']} ({stats['direct_ratio']:.2%})")
    logger.info(f"平均复杂度: {stats['avg_complexity']:.2f}")
    logger.info(f"平均置信度: {stats['avg_confidence']:.2f}")
    
    return 0


if __name__ == "__main__":
    main()
