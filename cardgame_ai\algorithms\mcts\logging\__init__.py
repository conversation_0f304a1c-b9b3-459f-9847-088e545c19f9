"""
MCTS日志增强模块

本模块为MCTS算法提供详细的调试日志功能，支持：
- UCB计算过程追踪
- 节点扩展详情记录  
- 搜索路径完整追踪
- 性能统计监控
- 可配置的日志级别和输出格式

主要组件：
- MCTSLogger: 主日志器类，提供所有日志记录功能
- MCTSFormatter: 日志格式化器，支持JSON和文本格式
- LogConfig: 日志配置管理器
- PerformanceMonitor: 性能监控器

使用示例：
    from cardgame_ai.algorithms.mcts.logging import MCTSLogger
    
    logger = MCTSLogger(level='DEBUG', enable_debug=True)
    logger.log_ucb_calculation(parent_node, children_scores, selected_action)
"""

from .mcts_logger import MCTSLogger
from .formatters import MCTSFormatter, JSONFormatter, TextFormatter
from .config import LogConfig
from .utils import generate_session_id, format_timestamp
from .performance_monitor import PerformanceMonitor

__all__ = [
    'MCTSLogger',
    'MCTSFormatter', 
    'JSONFormatter',
    'TextFormatter',
    'LogConfig',
    'generate_session_id',
    'format_timestamp',
    'PerformanceMonitor'
]

# 版本信息
__version__ = '1.0.0'
__author__ = 'MCTS日志增强团队'
__description__ = 'MCTS算法详细调试日志系统'
