# 智能工具选择规则 (ITSR)
## Intelligent Tool Selection Rules

### 🎯 规则目标
- 提高AI工具选择的透明度和质量
- 避免机械化、死板的工具调用
- 让用户参与工具选择过程
- 确保每次工具调用都有明确价值

### 📋 强制执行流程

#### 阶段1：问题评估 (必须执行)
```
## 🔍 问题分析
**复杂度评估：**
- [ ] 简单 (直接回答即可)
- [ ] 中等 (需要1-2个工具)  
- [ ] 复杂 (需要多工具协作)

**问题类型：**
- [ ] 技术调试
- [ ] 概念分析
- [ ] 决策制定
- [ ] 创意设计
- [ ] 系统优化
- [ ] 其他：_______

**用户期望深度：**
- [ ] 快速回答
- [ ] 标准分析
- [ ] 深度探索
- [ ] 最高质量 (不考虑成本)
```

#### 阶段2：工具候选分析 (复杂度≥中等时必须执行)
```
## 🔧 候选工具评估

**可用工具列表：**
1. **sequentialthinking** - 适用性：_/10
   - 优势：多步骤逻辑分析，支持复杂推理
   - 局限：可能过于详细，耗时较长
   - 适用场景：复杂问题分解、多步骤分析

2. **mentalmodel** - 适用性：_/10  
   - 优势：6种经典思维框架，结构化分析
   - 局限：可能过于理论化
   - 适用场景：根本原因分析、权衡决策

3. **debuggingapproach** - 适用性：_/10
   - 优势：6种系统化调试方法
   - 局限：主要针对技术问题
   - 适用场景：错误诊断、性能优化

4. **collaborativereasoning** - 适用性：_/10
   - 优势：多角度专家协作分析
   - 局限：可能产生冗余观点
   - 适用场景：复杂决策、多方利益考量

5. **decisionframework** - 适用性：_/10
   - 优势：系统化决策分析框架
   - 局限：可能过于正式化
   - 适用场景：重要决策、方案比较

6. **scientificmethod** - 适用性：_/10
   - 优势：严格的假设验证流程
   - 局限：可能过于学术化
   - 适用场景：理论验证、实验设计

7. **metacognitivemonitoring** - 适用性：_/10
   - 优势：自我认知监控，质量保证
   - 局限：主要用于元分析
   - 适用场景：知识边界评估、质量检查

8. **structuredargumentation** - 适用性：_/10
   - 优势：严格的论证分析框架
   - 局限：可能过于学术化
   - 适用场景：论证分析、观点辩论

9. **visualreasoning** - 适用性：_/10
   - 优势：图形化思维，直观展示
   - 局限：需要可视化需求
   - 适用场景：架构设计、流程分析
```

#### 阶段3：选择决策 (必须执行)
```
## 🎯 推荐方案

**主要工具：** [工具名]
**选择理由：** [详细说明为什么这个工具最适合]

**辅助工具：** [工具名] (如需要)
**协作理由：** [说明如何与主工具协作]

**备选方案：** [工具名]
**备选理由：** [在什么情况下使用备选方案]

**预期价值：** [这个工具组合能为用户提供什么价值]
```

#### 阶段4：用户确认 (推荐执行)
```
## 🤝 用户确认

**方案展示：**
基于以上分析，我推荐使用 [工具组合] 来解决您的问题。

**确认问题：**
- 您是否同意这个工具选择？
- 您有其他偏好或要求吗？
- 您希望调整分析深度吗？

**快速选项：**
- ✅ 同意推荐方案
- 🔄 使用备选方案  
- 🎯 指定特定工具
- 💬 直接回答，不使用工具
```

### 🚫 禁止行为
1. **禁止盲目调用** - 不经过评估直接调用工具
2. **禁止过度工具化** - 简单问题强制使用复杂工具
3. **禁止机械选择** - 基于关键词匹配选择工具
4. **禁止忽略用户偏好** - 不考虑用户的时间和期望

### ✅ 鼓励行为
1. **智能判断** - 根据实际需求选择工具
2. **透明决策** - 解释工具选择理由
3. **用户导向** - 优先考虑用户价值
4. **灵活调整** - 根据反馈调整策略

### 📊 质量检查清单
- [ ] 是否评估了问题复杂度？
- [ ] 是否列出了候选工具？
- [ ] 是否解释了选择理由？
- [ ] 是否考虑了用户期望？
- [ ] 是否提供了备选方案？

### 🔄 持续改进
- 记录用户偏好和反馈
- 分析工具选择的成功率
- 优化评估标准和流程
- 更新工具适用性评分
