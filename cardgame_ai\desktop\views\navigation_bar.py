#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
导航栏组件

实现美观的侧边导航栏，包含应用程序logo和名称，以及导航项（图标和文字）。
"""

import os
import logging
from typing import List, Dict, Optional, Callable

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QPushButton, QLabel, 
    QSpacerItem, QSizePolicy, QHBoxLayout
)
from PySide6.QtCore import Qt, Signal, QSize
from PySide6.QtGui import QIcon, QPixmap, QFont

from cardgame_ai.desktop.utils.theme_manager import theme_manager

logger = logging.getLogger(__name__)


class NavButton(QPushButton):
    """导航按钮类"""
    
    def __init__(
        self, 
        text: str, 
        icon_path: Optional[str] = None, 
        parent: Optional[QWidget] = None
    ):
        """
        初始化导航按钮
        
        Args:
            text (str): 按钮文本
            icon_path (Optional[str], optional): 图标路径. Defaults to None.
            parent (Optional[QWidget], optional): 父组件. Defaults to None.
        """
        super().__init__(parent)
        self.setText(text)
        self.setCheckable(True)
        self.setFlat(True)
        self.setProperty("class", "NavButton")
        
        # 设置字体
        font = self.font()
        font.setPointSize(13)
        self.setFont(font)
        
        # 设置图标
        if icon_path and os.path.exists(icon_path):
            self.setIcon(QIcon(icon_path))
            self.setIconSize(QSize(24, 24))
        
        # 设置样式
        self.setMinimumHeight(48)
        self.setCursor(Qt.PointingHandCursor)


class NavigationBar(QWidget):
    """导航栏组件类"""
    
    # 导航信号，发送导航项名称
    navigation_changed = Signal(str)
    
    def __init__(self, parent: Optional[QWidget] = None):
        """
        初始化导航栏组件
        
        Args:
            parent (Optional[QWidget], optional): 父组件. Defaults to None.
        """
        super().__init__(parent)
        
        # 设置固定宽度
        self.setFixedWidth(250)
        self.setObjectName("navigationBar")
        
        # 设置背景属性，确保能显示背景色
        self.setAttribute(Qt.WA_StyledBackground, True)
        
        # 设置深色背景 - 使用样式表和setAttribute组合
        self.setStyleSheet("""
            QWidget#navigationBar {
                background-color: #1a2533;
                border-right: 1px solid #0f1823;
            }
            
            #appLogo {
                color: #85c1e9;
                font-weight: bold;
            }
            
            .NavButton {
                color: #85c1e9;
                background-color: transparent;
                border: none;
                padding: 12px 15px;
                text-align: left;
            }
            
            .NavButton:hover {
                background-color: #2c3e50;
                color: #aed6f1;
            }
            
            .NavButton:checked {
                background-color: #34495e;
                color: #ffffff;
                border-left: 3px solid #3498db;
                padding-left: 12px;
            }
        """)
        
        # 创建布局
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 10, 0, 0)
        self.layout.setSpacing(5)
        
        # 添加应用程序logo和名称
        self.setup_logo()
        
        # 添加导航项
        self.nav_buttons = {}
        self.setup_navigation_items()
        
        # 添加底部空白
        self.layout.addSpacerItem(
            QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding)
        )
        
        # 连接主题变更信号
        theme_manager.theme_changed.connect(self.update_theme)
        
        logger.info("导航栏组件初始化完成")
    
    def setup_logo(self):
        """设置应用程序logo和名称"""
        logo_container = QWidget()
        logo_layout = QHBoxLayout(logo_container)
        logo_layout.setContentsMargins(15, 40, 15, 20)
        
        # 创建logo标签
        logo_label = QLabel("AI棋牌")
        logo_label.setObjectName("appLogo")
        logo_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        
        # 设置字体
        font = QFont()
        font.setPointSize(16)
        font.setBold(True)
        logo_label.setFont(font)
        
        # 添加到布局
        logo_layout.addWidget(logo_label)
        self.layout.addWidget(logo_container)
    
    def setup_navigation_items(self):
        """设置导航项"""
        # 定义导航项
        nav_items = [
            {"name": "训练", "icon": "desktop/resources/icons/training.png"},
            {"name": "推理", "icon": "desktop/resources/icons/inference.png"},
            {"name": "对战", "icon": "desktop/resources/icons/battle.png"},
            {"name": "设置", "icon": "desktop/resources/icons/settings.png"}
        ]
        
        # 添加导航项
        for item in nav_items:
            self.add_navigation_item(item["name"], item["icon"])
        
        # 默认选中第一个导航项
        if self.nav_buttons:
            first_button = next(iter(self.nav_buttons.values()))
            first_button.setChecked(True)
    
    def add_navigation_item(self, name: str, icon_path: str):
        """
        添加导航项
        
        Args:
            name (str): 导航项名称
            icon_path (str): 图标路径
        """
        # 创建导航按钮
        nav_button = NavButton(name, icon_path, self)
        
        # 连接点击信号
        nav_button.clicked.connect(lambda checked, n=name: self.on_navigation_clicked(n))
        
        # 添加到布局
        self.layout.addWidget(nav_button)
        
        # 保存按钮引用
        self.nav_buttons[name] = nav_button
    
    def on_navigation_clicked(self, name: str):
        """
        导航项点击处理
        
        Args:
            name (str): 导航项名称
        """
        # 更新按钮状态
        for button_name, button in self.nav_buttons.items():
            button.setChecked(button_name == name)
        
        # 发送导航信号
        self.navigation_changed.emit(name)
        logger.info(f"导航到：{name}")
    
    def update_theme(self, theme_name: str):
        """
        更新主题
        
        Args:
            theme_name (str): 主题名称
        """
        # 主题变更时可以在这里进行特定的样式调整
        logger.info(f"导航栏更新主题：{theme_name}")
    
    def select_navigation_item(self, name: str):
        """
        选择导航项
        
        Args:
            name (str): 导航项名称
        """
        if name in self.nav_buttons:
            self.nav_buttons[name].click()
        else:
            logger.warning(f"导航项不存在：{name}")
