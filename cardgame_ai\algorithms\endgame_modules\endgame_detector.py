"""
残局检测器模块

提供检测游戏是否处于残局状态的功能，并识别残局类型。
"""

from enum import Enum, auto
from typing import Dict, List, Optional, Tuple, Set

from cardgame_ai.core.base import State
from cardgame_ai.games.doudizhu.state import DouDizhuState
from cardgame_ai.games.doudizhu.card import Card, CardRank
from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType


class EndgameType(Enum):
    """残局类型枚举"""
    KING_BOMB = auto()           # 王炸残局
    SINGLE_CARD_CONTROL = auto()  # 单张控制残局
    BOMB_ENDGAME = auto()         # 炸弹残局
    STRAIGHT_CONTROL = auto()     # 顺子控制残局
    PAIR_CONTROL = auto()         # 对子控制残局
    TRIO_CONTROL = auto()         # 三张控制残局
    GENERAL_ENDGAME = auto()      # 一般残局


def is_endgame(state: State) -> bool:
    """
    检测游戏是否处于残局状态
    
    残局通常是指游戏接近结束，玩家手牌较少的状态。
    
    Args:
        state: 游戏状态
        
    Returns:
        bool: 是否处于残局状态
    """
    if not isinstance(state, DouDizhuState):
        return False
    
    # 检查是否有玩家手牌数量较少（小于等于5张）
    for hand in state.hands:
        if 0 < len(hand) <= 5:
            return True
    
    return False


def get_endgame_type(state: State) -> Optional[EndgameType]:
    """
    获取残局类型
    
    Args:
        state: 游戏状态
        
    Returns:
        Optional[EndgameType]: 残局类型，如果不是残局则返回None
    """
    if not is_endgame(state):
        return None
    
    if not isinstance(state, DouDizhuState):
        return None
    
    # 检查是否是王炸残局
    from cardgame_ai.algorithms.endgame_modules.king_bomb_handler import is_king_bomb_scenario
    if is_king_bomb_scenario(state):
        return EndgameType.KING_BOMB
    
    # 检查是否是单张控制残局
    from cardgame_ai.algorithms.endgame_modules.single_card_control import is_single_card_control_scenario
    if is_single_card_control_scenario(state):
        return EndgameType.SINGLE_CARD_CONTROL
    
    # 检查是否是炸弹残局
    if _is_bomb_endgame(state):
        return EndgameType.BOMB_ENDGAME
    
    # 检查是否是顺子控制残局
    if _is_straight_control(state):
        return EndgameType.STRAIGHT_CONTROL
    
    # 检查是否是对子控制残局
    if _is_pair_control(state):
        return EndgameType.PAIR_CONTROL
    
    # 检查是否是三张控制残局
    if _is_trio_control(state):
        return EndgameType.TRIO_CONTROL
    
    # 默认为一般残局
    return EndgameType.GENERAL_ENDGAME


def _is_bomb_endgame(state: DouDizhuState) -> bool:
    """
    检查是否是炸弹残局
    
    炸弹残局是指有玩家手中有炸弹，且其他玩家手牌较少的情况。
    
    Args:
        state: 游戏状态
        
    Returns:
        bool: 是否是炸弹残局
    """
    # 检查每个玩家的手牌
    for i, hand in enumerate(state.hands):
        # 统计手牌中的炸弹数量
        bomb_count = 0
        rank_count = {}
        
        for card in hand:
            if card.rank not in rank_count:
                rank_count[card.rank] = 0
            rank_count[card.rank] += 1
        
        for rank, count in rank_count.items():
            if count == 4:
                bomb_count += 1
        
        # 如果有炸弹，且其他玩家手牌较少，则认为是炸弹残局
        if bomb_count > 0:
            other_players = [j for j in range(len(state.hands)) if j != i]
            for other_player in other_players:
                if len(state.hands[other_player]) <= 4:
                    return True
    
    return False


def _is_straight_control(state: DouDizhuState) -> bool:
    """
    检查是否是顺子控制残局
    
    顺子控制残局是指有玩家手中有顺子，且其他玩家手牌较少的情况。
    
    Args:
        state: 游戏状态
        
    Returns:
        bool: 是否是顺子控制残局
    """
    # 检查每个玩家的手牌
    for i, hand in enumerate(state.hands):
        # 如果手牌数量大于等于5，可能有顺子
        if len(hand) >= 5:
            # 统计手牌中的点数
            ranks = [card.rank for card in hand]
            ranks.sort()
            
            # 检查是否有连续的5个或更多点数
            consecutive_count = 1
            for j in range(1, len(ranks)):
                if ranks[j].value == ranks[j-1].value + 1 and ranks[j].value <= CardRank.ACE.value:
                    consecutive_count += 1
                else:
                    consecutive_count = 1
                
                if consecutive_count >= 5:
                    # 如果有顺子，且其他玩家手牌较少，则认为是顺子控制残局
                    other_players = [k for k in range(len(state.hands)) if k != i]
                    for other_player in other_players:
                        if len(state.hands[other_player]) <= 5:
                            return True
    
    return False


def _is_pair_control(state: DouDizhuState) -> bool:
    """
    检查是否是对子控制残局
    
    对子控制残局是指有玩家手中有多个对子，且其他玩家手牌较少的情况。
    
    Args:
        state: 游戏状态
        
    Returns:
        bool: 是否是对子控制残局
    """
    # 检查每个玩家的手牌
    for i, hand in enumerate(state.hands):
        # 统计手牌中的对子数量
        rank_count = {}
        for card in hand:
            if card.rank not in rank_count:
                rank_count[card.rank] = 0
            rank_count[card.rank] += 1
        
        pair_count = sum(1 for count in rank_count.values() if count == 2)
        
        # 如果有多个对子，且其他玩家手牌较少，则认为是对子控制残局
        if pair_count >= 2:
            other_players = [j for j in range(len(state.hands)) if j != i]
            for other_player in other_players:
                if len(state.hands[other_player]) <= 4:
                    return True
    
    return False


def _is_trio_control(state: DouDizhuState) -> bool:
    """
    检查是否是三张控制残局
    
    三张控制残局是指有玩家手中有三张，且其他玩家手牌较少的情况。
    
    Args:
        state: 游戏状态
        
    Returns:
        bool: 是否是三张控制残局
    """
    # 检查每个玩家的手牌
    for i, hand in enumerate(state.hands):
        # 统计手牌中的三张数量
        rank_count = {}
        for card in hand:
            if card.rank not in rank_count:
                rank_count[card.rank] = 0
            rank_count[card.rank] += 1
        
        trio_count = sum(1 for count in rank_count.values() if count == 3)
        
        # 如果有三张，且其他玩家手牌较少，则认为是三张控制残局
        if trio_count > 0:
            other_players = [j for j in range(len(state.hands)) if j != i]
            for other_player in other_players:
                if len(state.hands[other_player]) <= 3:
                    return True
    
    return False
