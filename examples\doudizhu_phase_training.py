#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
斗地主游戏阶段训练脚本

这个脚本用于训练斗地主游戏的完整流程，包括发牌、叫地主、抢地主和出牌阶段。
同时收集各阶段的统计数据，用于分析和改进。
"""

import os
import time
import logging
import argparse
import numpy as np
from collections import defaultdict
from typing import Dict, List, Any, Tuple

import torch

from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.games.doudizhu.state import GamePhase
from cardgame_ai.games.doudizhu.action import BidAction, GrabAction
from cardgame_ai.agents.dqn_agent import DQNAgent
from cardgame_ai.training.doudizhu_self_play import DouDizhuSelfPlay
from cardgame_ai.training.trainer import AdvancedTrainer
from cardgame_ai.utils.logger import setup_logger

# 设置日志
logger = setup_logger('doudizhu_phase_training', level=logging.INFO)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='斗地主游戏阶段训练')

    # 训练参数
    parser.add_argument('--num_epochs', type=int, default=100, help='训练轮数')
    parser.add_argument('--games_per_epoch', type=int, default=100, help='每轮游戏数')
    parser.add_argument('--batch_size', type=int, default=128, help='批大小')
    parser.add_argument('--learning_rate', type=float, default=0.001, help='学习率')
    parser.add_argument('--gamma', type=float, default=0.99, help='折扣因子')
    parser.add_argument('--temperature', type=float, default=1.0, help='温度参数')

    # 模型参数
    parser.add_argument('--hidden_dim', type=int, default=256, help='隐藏层维度')
    parser.add_argument('--num_layers', type=int, default=2, help='网络层数')

    # 保存和加载
    parser.add_argument('--save_dir', type=str, default='models/doudizhu_phase', help='模型保存目录')
    parser.add_argument('--load_model', type=str, default=None, help='加载模型路径')
    parser.add_argument('--save_interval', type=int, default=10, help='模型保存间隔')

    # 其他参数
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    parser.add_argument('--device', type=str, default='cuda' if torch.cuda.is_available() else 'cpu', help='设备')
    parser.add_argument('--disable_progress_bar', action='store_true', help='禁用进度条')

    return parser.parse_args()

def collect_phase_statistics(experiences: List[Dict[str, Any]]) -> Dict[str, Any]:
    """收集各阶段的统计数据

    Args:
        experiences: 经验数据列表

    Returns:
        各阶段的统计数据
    """
    stats = {
        'phase_counts': defaultdict(int),  # 各阶段的次数
        'bid_actions': defaultdict(int),   # 叫地主动作的次数
        'grab_actions': defaultdict(int),  # 抢地主动作的次数
        'bid_rewards': defaultdict(list),  # 叫地主动作的奖励
        'grab_rewards': defaultdict(list), # 抢地主动作的奖励
        'phase_transitions': defaultdict(int),  # 阶段转换的次数
        'landlord_win_rate': 0.0,          # 地主胜率
        'avg_game_length': 0.0,            # 平均游戏长度
    }

    # 游戏计数和长度
    game_count = 0
    game_lengths = []
    current_game_length = 0
    landlord_wins = 0

    # 上一个阶段
    last_phase = None

    for exp in experiences:
        state = exp['state']
        action = exp['action']
        reward = exp['reward']
        next_state = exp['next_state']
        done = exp['done']

        # 记录阶段
        current_phase = state.game_phase
        stats['phase_counts'][current_phase.name] += 1

        # 记录阶段转换
        if last_phase is not None and last_phase != current_phase:
            transition = f"{last_phase.name}_to_{current_phase.name}"
            stats['phase_transitions'][transition] += 1

        last_phase = current_phase

        # 记录动作和奖励
        if current_phase == GamePhase.BIDDING and isinstance(action, BidAction):
            stats['bid_actions'][action.name] += 1
            stats['bid_rewards'][action.name].append(reward)

        elif current_phase == GamePhase.GRABBING and isinstance(action, GrabAction):
            stats['grab_actions'][action.name] += 1
            stats['grab_rewards'][action.name].append(reward)

        # 记录游戏长度
        current_game_length += 1

        # 如果游戏结束，更新统计数据
        if done:
            game_count += 1
            game_lengths.append(current_game_length)
            current_game_length = 0

            # 记录地主胜负
            if next_state.landlord is not None and next_state.get_payoffs()[next_state.landlord] > 0:
                landlord_wins += 1

    # 计算平均值
    if game_count > 0:
        stats['landlord_win_rate'] = landlord_wins / game_count
        stats['avg_game_length'] = sum(game_lengths) / game_count

    # 计算平均奖励
    for action, rewards in stats['bid_rewards'].items():
        if rewards:
            stats['bid_rewards'][action] = sum(rewards) / len(rewards)

    for action, rewards in stats['grab_rewards'].items():
        if rewards:
            stats['grab_rewards'][action] = sum(rewards) / len(rewards)

    return stats

def print_statistics(stats: Dict[str, Any], epoch: int):
    """打印统计数据

    Args:
        stats: 统计数据
        epoch: 当前轮数
    """
    logger.info(f"第 {epoch} 轮统计数据:")

    # 打印阶段计数
    logger.info("阶段计数:")
    for phase, count in stats['phase_counts'].items():
        logger.info(f"  {phase}: {count}")

    # 打印叫地主动作
    logger.info("叫地主动作:")
    for action, count in stats['bid_actions'].items():
        avg_reward = stats['bid_rewards'].get(action, 0.0)
        logger.info(f"  {action}: {count} 次, 平均奖励: {avg_reward:.4f}")

    # 打印抢地主动作
    logger.info("抢地主动作:")
    for action, count in stats['grab_actions'].items():
        avg_reward = stats['grab_rewards'].get(action, 0.0)
        logger.info(f"  {action}: {count} 次, 平均奖励: {avg_reward:.4f}")

    # 打印阶段转换
    logger.info("阶段转换:")
    for transition, count in stats['phase_transitions'].items():
        logger.info(f"  {transition}: {count} 次")

    # 打印其他统计数据
    logger.info(f"地主胜率: {stats['landlord_win_rate']:.4f}")
    logger.info(f"平均游戏长度: {stats['avg_game_length']:.2f}")

def main():
    """主函数"""
    args = parse_args()

    # 设置随机种子
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)

    # 创建保存目录
    if not os.path.exists(args.save_dir):
        os.makedirs(args.save_dir)

    # 创建环境
    env = DouDizhuEnvironment()

    # 创建智能体
    agent = DQNAgent(
        state_dim=env.observation_space.shape[0],
        action_dim=env.action_space.n,
        hidden_dim=args.hidden_dim,
        num_layers=args.num_layers,
        learning_rate=args.learning_rate,
        gamma=args.gamma,
        device=args.device
    )

    # 加载模型（如果指定）
    if args.load_model and os.path.exists(args.load_model):
        agent.load(args.load_model)
        logger.info(f"加载模型: {args.load_model}")

    # 创建自我对弈和训练器
    self_play = DouDizhuSelfPlay(save_path=os.path.join(args.save_dir, 'experiences'))
    trainer = AdvancedTrainer(save_path=args.save_dir)

    # 训练循环
    for epoch in range(1, args.num_epochs + 1):
        logger.info(f"开始第 {epoch}/{args.num_epochs} 轮训练")

        # 自我对弈生成经验
        start_time = time.time()
        logger.info("开始自我对弈...")

        experiences = self_play.generate_experience(
            env=env,
            agent=agent,
            num_games=args.games_per_epoch,
            temperature=args.temperature,
            save=True,
            parallel=True
        )

        logger.info(f"自我对弈完成，生成 {len(experiences)} 个经验，耗时 {time.time() - start_time:.2f}s")

        # 收集统计数据
        stats = collect_phase_statistics(experiences)
        print_statistics(stats, epoch)

        # 训练智能体
        start_time = time.time()
        logger.info("开始训练智能体...")

        train_metrics = trainer.train_with_experiences(
            agent=agent,
            experiences=experiences,
            batch_size=min(args.batch_size, len(experiences)),
            num_epochs=5,
            shuffle=True
        )

        logger.info(f"训练完成，耗时 {time.time() - start_time:.2f}s")
        logger.info(f"训练指标: {train_metrics}")

        # 保存模型
        if epoch % args.save_interval == 0:
            save_path = os.path.join(args.save_dir, f"agent_epoch_{epoch}.pt")
            agent.save(save_path)
            logger.info(f"保存模型: {save_path}")

    # 保存最终模型
    final_save_path = os.path.join(args.save_dir, "agent_final.pt")
    agent.save(final_save_path)
    logger.info(f"保存最终模型: {final_save_path}")

if __name__ == "__main__":
    main()
