"""
基于规则的AI代理模块

实现斗地主游戏的规则型AI代理，使用启发式规则进行决策。
"""
import os
import pickle
import random
from typing import Dict, Any, List, Tuple, Optional, Union
import numpy as np

from cardgame_ai.core.agent import Agent
from cardgame_ai.core.base import State, Action, Experience, Batch
from cardgame_ai.games.doudizhu.card import Card
from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType
from cardgame_ai.games.doudizhu.state import DouDizhuState, GamePhase
from cardgame_ai.games.doudizhu.action import BidAction, GrabAction


class RuleBasedAgent(Agent):
    """
    基于规则的代理
    
    使用人工定义的规则和启发式方法来进行斗地主游戏的决策。
    比随机代理更有策略性，但不如深度学习模型精确。
    """
    
    def __init__(self, difficulty: str = 'medium', seed: Optional[int] = None):
        """
        初始化基于规则的代理
        
        Args:
            difficulty (str, optional): 难度级别，可选 'easy', 'medium', 'hard'. Defaults to 'medium'.
            seed (Optional[int], optional): 随机种子. Defaults to None.
        """
        self.rng = random.Random(seed)
        
        # 设置不同难度级别的参数
        # 这些参数影响代理的行为和决策
        if difficulty == 'easy':
            self.bid_aggressiveness = 0.3      # 叫分激进程度
            self.grab_probability = 0.2        # 抢地主概率
            self.play_strongest_prob = 0.3     # 出最强牌组的概率
            self.strategic_playing = False     # 是否使用策略性出牌
        elif difficulty == 'hard':
            self.bid_aggressiveness = 0.8
            self.grab_probability = 0.7
            self.play_strongest_prob = 0.8
            self.strategic_playing = True
        else:  # medium (default)
            self.bid_aggressiveness = 0.5
            self.grab_probability = 0.5
            self.play_strongest_prob = 0.6
            self.strategic_playing = True
    
    def act(self, state: Union[State, np.ndarray], legal_actions: List[Action], is_training: bool = False) -> Action:
        """
        根据规则选择动作
        
        Args:
            state (Union[State, np.ndarray]): 游戏状态或观察
            legal_actions (List[Action]): 合法动作列表
            is_training (bool, optional): 是否为训练模式. Defaults to False.
            
        Returns:
            Action: 选择的动作
        """
        # 如果没有合法动作，返回None（通常表示"不出"）
        if not legal_actions:
            return None
        
        # 处理不同类型的状态输入
        if isinstance(state, np.ndarray):
            # 如果输入是观察向量，则无法应用规则
            # 退化为随机代理
            return self.rng.choice(legal_actions)
        
        # 确保状态是斗地主状态
        if not isinstance(state, DouDizhuState):
            raise ValueError("状态必须是DouDizhuState类型")
        
        # 根据游戏阶段选择相应的决策方法
        if state.game_phase == GamePhase.BIDDING:
            return self._bid_action(state, legal_actions)
        elif state.game_phase == GamePhase.GRABBING:
            return self._grab_action(state, legal_actions)
        elif state.game_phase == GamePhase.PLAYING:
            return self._play_action(state, legal_actions)
        else:
            # 其他阶段，随机选择
            return self.rng.choice(legal_actions)
    
    def _bid_action(self, state: DouDizhuState, legal_actions: List[Action]) -> Action:
        """
        叫地主阶段的决策
        
        Args:
            state (DouDizhuState): 当前游戏状态
            legal_actions (List[Action]): 合法动作列表
            
        Returns:
            Action: 叫分动作
        """
        # 获取手牌
        my_cards = state.hands[state.current_player]
        
        # 评估手牌强度
        hand_strength = self._evaluate_hand_strength(my_cards)
        
        # 根据手牌强度和激进程度决定叫分
        if hand_strength > 0.7 and self.rng.random() < self.bid_aggressiveness:
            # 强手牌，高叫分
            for action in reversed(legal_actions):
                if action == BidAction.BID_3:
                    return action
            
        elif hand_strength > 0.5 and self.rng.random() < self.bid_aggressiveness:
            # 中等手牌，中等叫分
            for action in reversed(legal_actions):
                if action == BidAction.BID_2:
                    return action
            
        elif hand_strength > 0.3 and self.rng.random() < self.bid_aggressiveness:
            # 弱手牌，低叫分
            for action in reversed(legal_actions):
                if action == BidAction.BID_1:
                    return action
        
        # 默认不叫
        for action in legal_actions:
            if action == BidAction.PASS:
                return action
        
        # 如果没有找到合适的动作，随机选择
        return self.rng.choice(legal_actions)
    
    def _grab_action(self, state: DouDizhuState, legal_actions: List[Action]) -> Action:
        """
        抢地主阶段的决策
        
        Args:
            state (DouDizhuState): 当前游戏状态
            legal_actions (List[Action]): 合法动作列表
            
        Returns:
            Action: 抢地主动作
        """
        # 获取手牌
        my_cards = state.hands[state.current_player]
        
        # 评估手牌强度
        hand_strength = self._evaluate_hand_strength(my_cards)
        
        # 根据手牌强度和激进程度决定是否抢地主
        if hand_strength > 0.6 and self.rng.random() < self.grab_probability:
            # 强手牌，抢地主
            for action in legal_actions:
                if action == GrabAction.GRAB:
                    return action
        
        # 默认不抢
        for action in legal_actions:
            if action == GrabAction.PASS:
                return action
        
        # 如果没有找到合适的动作，随机选择
        return self.rng.choice(legal_actions)
    
    def _play_action(self, state: DouDizhuState, legal_actions: List[Action]) -> Action:
        """
        出牌阶段的决策
        
        Args:
            state (DouDizhuState): 当前游戏状态
            legal_actions (List[Action]): 合法动作列表
            
        Returns:
            Action: 出牌动作
        """
        # 首先处理"不出"选项
        if len(legal_actions) == 1 and (legal_actions[0] is None or legal_actions[0] == "pass"):
            return legal_actions[0]
        
        # 过滤掉None和"不出"选项
        card_actions = [action for action in legal_actions if action is not None and action != "pass"]
        if not card_actions:
            # 如果只有"不出"是合法的，选择"不出"
            return None
        
        # 如果是首次出牌，使用更激进的策略
        is_first_play = state.num_passes == 0 and state.played_cards == {}
        
        if is_first_play:
            return self._first_play_strategy(state, card_actions)
        else:
            return self._follow_play_strategy(state, card_actions)
    
    def _first_play_strategy(self, state: DouDizhuState, card_actions: List[CardGroup]) -> CardGroup:
        """
        首次出牌策略
        
        Args:
            state (DouDizhuState): 当前游戏状态
            card_actions (List[CardGroup]): 合法的出牌动作
            
        Returns:
            CardGroup: 选择的出牌
        """
        # 分类牌组
        singles = []
        pairs = []
        trips = []
        bombs = []
        chains = []
        
        for action in card_actions:
            if isinstance(action, CardGroup):
                if action.type == CardGroupType.SINGLE:
                    singles.append(action)
                elif action.type == CardGroupType.PAIR:
                    pairs.append(action)
                elif action.type == CardGroupType.TRIPLE:
                    trips.append(action)
                elif action.type == CardGroupType.BOMB or action.type == CardGroupType.ROCKET:
                    bombs.append(action)
                elif action.type in [CardGroupType.SINGLE_CHAIN, CardGroupType.PAIR_CHAIN, CardGroupType.TRIPLE_CHAIN]:
                    chains.append(action)
        
        # 优先出单张
        if singles and self.rng.random() < 0.7:
            # 从小到大排序，优先出小牌
            sorted_singles = sorted(singles, key=lambda x: x.value)
            return sorted_singles[0]
        
        # 其次考虑对子
        if pairs and self.rng.random() < 0.6:
            sorted_pairs = sorted(pairs, key=lambda x: x.value)
            return sorted_pairs[0]
        
        # 再考虑三张
        if trips and self.rng.random() < 0.5:
            sorted_trips = sorted(trips, key=lambda x: x.value)
            return sorted_trips[0]
        
        # 考虑顺子
        if chains and self.rng.random() < 0.8:
            # 优先出长的顺子
            sorted_chains = sorted(chains, key=lambda x: len(x.cards), reverse=True)
            return sorted_chains[0]
        
        # 炸弹作为最后的选择
        if bombs and self.rng.random() < 0.2:
            sorted_bombs = sorted(bombs, key=lambda x: x.value)
            return sorted_bombs[0]
        
        # 如果没有特别的策略，选择随机动作
        return self.rng.choice(card_actions)
    
    def _follow_play_strategy(self, state: DouDizhuState, card_actions: List[CardGroup]) -> Union[CardGroup, None]:
        """
        跟牌策略
        
        Args:
            state (DouDizhuState): 当前游戏状态
            card_actions (List[CardGroup]): 合法的出牌动作
            
        Returns:
            Union[CardGroup, None]: 选择的出牌或者"不出"
        """
        # 获取上家出的牌
        last_move = None
        for player, move in state.last_move.items():
            if move is not None and player != state.current_player:
                last_move = move
                break
        
        if last_move is None:
            # 没有上家出牌记录，使用首次出牌策略
            return self._first_play_strategy(state, card_actions)
        
        # 找出能压过上家的最小牌
        min_action = None
        min_value = float('inf')
        
        for action in card_actions:
            if isinstance(action, CardGroup) and action.value > last_move.value:
                if action.value < min_value:
                    min_action = action
                    min_value = action.value
        
        # 如果没有能压过上家的牌，选择"不出"
        if min_action is None:
            return None
        
        # 判断是否是炸弹
        is_bomb = (isinstance(min_action, CardGroup) and 
                   (min_action.type == CardGroupType.BOMB or min_action.type == CardGroupType.ROCKET))
        
        # 对手剩余的牌数
        opponent_cards_count = 0
        for player, cards in state.hands.items():
            if player != state.current_player:
                opponent_cards_count += len(cards)
        
        # 如果对手牌数很少，尽量出牌
        if opponent_cards_count < 5:
            return min_action
        
        # 如果是炸弹，根据概率决定是否使用
        if is_bomb:
            if self.rng.random() < 0.3:  # 保留炸弹的概率较高
                return None
        
        # 根据策略性出牌参数决定是否出最小的牌
        if self.strategic_playing and self.rng.random() > self.play_strongest_prob:
            return min_action
        
        # 默认行为：随机选择一个能压过上家的牌
        valid_actions = [action for action in card_actions 
                         if isinstance(action, CardGroup) and action.value > last_move.value]
        
        if valid_actions:
            return self.rng.choice(valid_actions)
        
        # 如果没有合适的选择，选择"不出"
        return None
    
    def _evaluate_hand_strength(self, cards: List[Card]) -> float:
        """
        评估手牌强度
        
        Args:
            cards (List[Card]): 手牌列表
            
        Returns:
            float: 手牌强度评分 (0.0-1.0)
        """
        if not cards:
            return 0.0
        
        score = 0.0
        total_cards = len(cards)
        
        # 统计牌值分布
        rank_count = {}
        for card in cards:
            rank = card.rank
            if rank not in rank_count:
                rank_count[rank] = 0
            rank_count[rank] += 1
        
        # 计算分数
        
        # 1. 基于牌点数的分数
        for card in cards:
            # 2分值5分
            if card.rank == "2":
                score += 5
            # A值4分
            elif card.rank == "A":
                score += 4
            # K值3分
            elif card.rank == "K":
                score += 3
            # Q值2分
            elif card.rank == "Q":
                score += 2
            # J值1分
            elif card.rank == "J":
                score += 1
            # 大小王特别值高
            elif card.is_joker:
                if card.color == "RED":  # 大王
                    score += 20
                else:  # 小王
                    score += 15
        
        # 2. 基于牌型的额外分数
        
        # 炸弹 (四张相同点数)
        for rank, count in rank_count.items():
            if count == 4:
                score += 25
            # 三张
            elif count == 3:
                score += 5
            # 对子
            elif count == 2:
                score += 1
        
        # 3. 检查是否有火箭 (大小王)
        has_red_joker = any(card.is_joker and card.color == "RED" for card in cards)
        has_black_joker = any(card.is_joker and card.color == "BLACK" for card in cards)
        if has_red_joker and has_black_joker:
            score += 40
        
        # 4. 检查是否有顺子潜力
        consecutive_ranks = self._count_consecutive_ranks(cards)
        score += consecutive_ranks * 2
        
        # 归一化分数 (0.0-1.0)
        # 假设理论最高分是每张牌都是炸弹+火箭 = 总牌数 * 25 + 40
        max_theoretical_score = total_cards * 5 + 40
        normalized_score = min(1.0, score / max_theoretical_score)
        
        return normalized_score
    
    def _count_consecutive_ranks(self, cards: List[Card]) -> int:
        """
        计算有多少连续的牌
        
        Args:
            cards (List[Card]): 手牌列表
            
        Returns:
            int: 连续牌的数量
        """
        # 斗地主的牌值顺序: 3, 4, 5, 6, 7, 8, 9, 10, J, Q, K, A, 2, Joker
        rank_order = {"3": 0, "4": 1, "5": 2, "6": 3, "7": 4, "8": 5, "9": 6, "10": 7, 
                      "J": 8, "Q": 9, "K": 10, "A": 11, "2": 12}
        
        # 去除大小王和2
        filtered_cards = [card for card in cards if not card.is_joker and card.rank != "2"]
        
        # 获取所有不同的牌值
        ranks = set(card.rank for card in filtered_cards)
        
        # 转换为数字并排序
        rank_values = sorted([rank_order[rank] for rank in ranks])
        
        if not rank_values:
            return 0
        
        # 计算最长的连续序列
        max_consecutive = 1
        current_consecutive = 1
        
        for i in range(1, len(rank_values)):
            if rank_values[i] == rank_values[i-1] + 1:
                current_consecutive += 1
            else:
                max_consecutive = max(max_consecutive, current_consecutive)
                current_consecutive = 1
        
        # 检查最后一个序列
        max_consecutive = max(max_consecutive, current_consecutive)
        
        return max_consecutive
    
    def train(self, experience: Union[Experience, Batch]) -> Dict[str, float]:
        """
        基于规则的代理不需要训练
        
        Args:
            experience (Union[Experience, Batch]): 经验数据
            
        Returns:
            Dict[str, float]: 空字典
        """
        return {}
    
    def save(self, path: str) -> None:
        """
        保存代理配置
        
        Args:
            path (str): 保存路径
        """
        config = {
            'bid_aggressiveness': self.bid_aggressiveness,
            'grab_probability': self.grab_probability,
            'play_strongest_prob': self.play_strongest_prob,
            'strategic_playing': self.strategic_playing,
            'rng_state': self.rng.getstate()
        }
        
        # 确保目录存在
        os.makedirs(os.path.dirname(path), exist_ok=True)
        
        with open(path, 'wb') as f:
            pickle.dump(config, f)
    
    def load(self, path: str) -> None:
        """
        加载代理配置
        
        Args:
            path (str): 加载路径
        """
        with open(path, 'rb') as f:
            config = pickle.load(f)
        
        self.bid_aggressiveness = config.get('bid_aggressiveness', 0.5)
        self.grab_probability = config.get('grab_probability', 0.5)
        self.play_strongest_prob = config.get('play_strongest_prob', 0.6)
        self.strategic_playing = config.get('strategic_playing', True)
        
        # 恢复随机状态
        rng_state = config.get('rng_state')
        if rng_state:
            self.rng.setstate(rng_state) 