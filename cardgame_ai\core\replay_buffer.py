"""
经验回放缓冲区

提供存储和采样训练经验的能力，用于强化学习算法。
"""
from typing import List, Dict, Any, Optional, Tuple, Union
import random
import numpy as np


class ReplayBuffer:
    """
    经验回放缓冲区类
    
    存储并管理强化学习算法的训练经验，支持随机采样。
    """
    
    def __init__(self, capacity: int = 10000):
        """
        初始化经验回放缓冲区
        
        Args:
            capacity (int, optional): 缓冲区容量. Defaults to 10000.
        """
        self.capacity = capacity
        self.buffer = []
        self.position = 0
    
    def add(self, experience: Dict[str, Any]) -> None:
        """
        添加单条经验
        
        Args:
            experience (Dict[str, Any]): 经验数据，通常包含状态、动作、奖励、下一状态等信息
        """
        if len(self.buffer) < self.capacity:
            self.buffer.append(experience)
        else:
            self.buffer[self.position] = experience
        
        self.position = (self.position + 1) % self.capacity
    
    def add_experiences(self, experiences: List[Dict[str, Any]]) -> None:
        """
        批量添加经验
        
        Args:
            experiences (List[Dict[str, Any]]): 经验数据列表
        """
        for experience in experiences:
            self.add(experience)
    
    def sample(self, batch_size: int) -> List[Dict[str, Any]]:
        """
        随机采样经验
        
        Args:
            batch_size (int): 批次大小
            
        Returns:
            List[Dict[str, Any]]: 采样的经验数据列表
            
        Raises:
            ValueError: 当缓冲区为空或批次大小大于缓冲区大小时抛出
        """
        if len(self.buffer) == 0:
            raise ValueError("经验回放缓冲区为空")
        
        batch_size = min(batch_size, len(self.buffer))
        return random.sample(self.buffer, batch_size)
    
    def clear(self) -> None:
        """
        清空缓冲区
        """
        self.buffer = []
        self.position = 0
    
    def __len__(self) -> int:
        """
        获取缓冲区大小
        
        Returns:
            int: 缓冲区中的经验数量
        """
        return len(self.buffer)
    
    def __getitem__(self, index: int) -> Dict[str, Any]:
        """
        通过索引获取经验
        
        Args:
            index (int): 索引
            
        Returns:
            Dict[str, Any]: 经验数据
            
        Raises:
            IndexError: 当索引超出范围时抛出
        """
        if index < 0 or index >= len(self.buffer):
            raise IndexError("索引超出范围")
        
        return self.buffer[index]
    
    def __iter__(self):
        """
        迭代器
        
        Returns:
            Iterator: 缓冲区迭代器
        """
        return iter(self.buffer)
    
    def __str__(self) -> str:
        """
        字符串表示
        
        Returns:
            str: 缓冲区的字符串表示
        """
        return f"ReplayBuffer(size={len(self.buffer)}, capacity={self.capacity})" 