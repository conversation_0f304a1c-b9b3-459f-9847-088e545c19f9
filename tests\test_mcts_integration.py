#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
MCTS集成测试

测试auto_deploy.py修复后是否能正确调用MCTS
"""

import sys
import unittest
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from cardgame_ai.zhuchengxu.main_training import OptimizedTrainingSystem
    from cardgame_ai.algorithms.mcts import MCTS
    INTEGRATION_AVAILABLE = True
except ImportError as e:
    INTEGRATION_AVAILABLE = False
    print(f"集成测试模块导入失败: {e}")


@unittest.skipUnless(INTEGRATION_AVAILABLE, "集成测试模块不可用")
class TestMCTSIntegration(unittest.TestCase):
    """测试MCTS集成"""
    
    def setUp(self):
        """设置测试环境"""
        self.test_config = {
            'mcts': {
                'num_simulations': 50,
                'c_puct': 1.25,
                'dirichlet_alpha': 0.3,
                'exploration_fraction': 0.25,
                'discount': 0.997
            },
            'training': {
                'batch_size': 128,
                'learning_rate': 0.001,
                'num_workers': 4
            },
            'device': {
                'type': 'cpu'
            },
            'monitoring': {
                'enabled': True
            }
        }
    
    def test_mcts_initialization(self):
        """测试MCTS初始化"""
        # 创建训练系统
        training_system = OptimizedTrainingSystem()

        # 初始化logger
        import logging
        training_system.logger = logging.getLogger(__name__)

        # 初始化优化组件
        training_system._initialize_optimization_components(self.test_config, 'cpu')
        
        # 验证MCTS已正确初始化
        self.assertIsNotNone(training_system.mcts)
        self.assertIsInstance(training_system.mcts, MCTS)
        
        # 验证MCTS配置
        self.assertEqual(training_system.mcts.num_simulations, 50)
        self.assertEqual(training_system.mcts.pb_c_init, 1.25)
        self.assertEqual(training_system.mcts.dirichlet_alpha, 0.3)
    
    def test_mcts_config_compatibility(self):
        """测试MCTS配置兼容性"""
        # 测试不同的配置参数
        test_configs = [
            {'num_simulations': 100},
            {'c_puct': 2.0},
            {'dirichlet_alpha': 0.5},
            {}  # 空配置，应使用默认值
        ]
        
        for mcts_config in test_configs:
            with self.subTest(config=mcts_config):
                config = self.test_config.copy()
                config['mcts'] = mcts_config
                
                training_system = OptimizedTrainingSystem()

                # 初始化logger
                import logging
                training_system.logger = logging.getLogger(__name__)

                training_system._initialize_optimization_components(config, 'cpu')
                
                # 验证MCTS初始化成功
                self.assertIsNotNone(training_system.mcts)
                self.assertIsInstance(training_system.mcts, MCTS)
    
    def test_training_config_generation(self):
        """测试训练配置生成"""
        training_system = OptimizedTrainingSystem()

        # 初始化logger
        import logging
        training_system.logger = logging.getLogger(__name__)

        training_system._initialize_optimization_components(self.test_config, 'cpu')
        
        # 模拟训练配置生成
        train_config = self.test_config.copy()
        train_config['optimization'] = {
            'use_mcts': True,
            'use_optimized_dataloader': True,
            'use_performance_monitor': True,
            'mcts_instance': training_system.mcts,
            'data_config': {}
        }
        
        # 验证配置结构
        self.assertTrue(train_config['optimization']['use_mcts'])
        self.assertIsInstance(train_config['optimization']['mcts_instance'], MCTS)
    
    def test_mcts_logging_integration(self):
        """测试MCTS日志集成"""
        training_system = OptimizedTrainingSystem()

        # 初始化logger
        import logging
        training_system.logger = logging.getLogger(__name__)

        training_system._initialize_optimization_components(self.test_config, 'cpu')
        
        # 验证MCTS具有日志功能
        mcts = training_system.mcts
        
        # 检查MCTS是否有日志相关属性
        # 注意：这里我们只检查MCTS对象是否正确创建
        # 实际的日志功能测试在专门的MCTS日志测试中进行
        self.assertTrue(hasattr(mcts, 'run'))
        self.assertTrue(hasattr(mcts, 'num_simulations'))


class TestMCTSStandalone(unittest.TestCase):
    """测试MCTS独立功能"""
    
    def test_mcts_creation(self):
        """测试MCTS创建"""
        mcts = MCTS(
            num_simulations=50,
            pb_c_init=1.25,
            dirichlet_alpha=0.3
        )
        
        self.assertEqual(mcts.num_simulations, 50)
        self.assertEqual(mcts.pb_c_init, 1.25)
        self.assertEqual(mcts.dirichlet_alpha, 0.3)
    
    def test_mcts_default_parameters(self):
        """测试MCTS默认参数"""
        mcts = MCTS()
        
        # 验证默认参数
        self.assertEqual(mcts.num_simulations, 50)
        self.assertEqual(mcts.discount, 0.997)
        self.assertEqual(mcts.dirichlet_alpha, 0.25)
        self.assertEqual(mcts.exploration_fraction, 0.25)


def run_integration_test():
    """运行集成测试"""
    print("=" * 60)
    print("MCTS集成测试")
    print("=" * 60)
    
    if not INTEGRATION_AVAILABLE:
        print("集成测试模块不可用，跳过测试")
        return False
    
    # 运行测试
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加测试用例
    suite.addTests(loader.loadTestsFromTestCase(TestMCTSIntegration))
    suite.addTests(loader.loadTestsFromTestCase(TestMCTSStandalone))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 显示结果
    if result.wasSuccessful():
        print("\n所有MCTS集成测试通过！")
        print("auto_deploy.py现在可以正确调用MCTS了")
        return True
    else:
        print(f"\n测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
        return False


if __name__ == "__main__":
    success = run_integration_test()
    sys.exit(0 if success else 1)
