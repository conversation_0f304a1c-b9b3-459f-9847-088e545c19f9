"""
特征提取器模块

提供从游戏状态中提取特征的接口和实现，供学习算法使用。
"""
from typing import Dict, Any, List, Tuple, Optional, Union, Type
import numpy as np
import abc

from cardgame_ai.core.base import State
from cardgame_ai.games.common.game_descriptor import GameDescriptor


class FeatureExtractor(abc.ABC):
    """
    特征提取器基类
    
    用于从游戏状态中提取特征，供学习算法使用。
    """
    
    def __init__(self, game_descriptor: GameDescriptor):
        """
        初始化特征提取器
        
        Args:
            game_descriptor (GameDescriptor): 游戏描述器
        """
        self.game_descriptor = game_descriptor
        self.feature_dim = None
    
    @abc.abstractmethod
    def extract_features(self, state: State) -> np.ndarray:
        """
        从游戏状态中提取特征
        
        Args:
            state (State): 游戏状态
        
        Returns:
            np.ndarray: 特征向量或矩阵
        """
        pass
    
    @abc.abstractmethod
    def get_feature_dim(self) -> <PERSON><PERSON>[int, ...]:
        """
        获取特征维度
        
        Returns:
            Tuple[int, ...]: 特征维度
        """
        pass
    
    @abc.abstractmethod
    def preprocess_batch(self, states: List[State]) -> np.ndarray:
        """
        预处理一批游戏状态
        
        Args:
            states (List[State]): 游戏状态列表
        
        Returns:
            np.ndarray: 特征批次
        """
        pass


class DefaultFeatureExtractor(FeatureExtractor):
    """
    默认特征提取器
    
    使用游戏状态的默认特征表示方法。
    """
    
    def __init__(self, game_descriptor: GameDescriptor):
        """
        初始化默认特征提取器
        
        Args:
            game_descriptor (GameDescriptor): 游戏描述器
        """
        super().__init__(game_descriptor)
        self.feature_dim = game_descriptor.state_shape
    
    def extract_features(self, state: State) -> np.ndarray:
        """
        从游戏状态中提取特征
        
        使用状态的to_numpy方法获取特征表示。
        
        Args:
            state (State): 游戏状态
        
        Returns:
            np.ndarray: 特征向量或矩阵
        """
        return state.to_numpy()
    
    def get_feature_dim(self) -> Tuple[int, ...]:
        """
        获取特征维度
        
        Returns:
            Tuple[int, ...]: 特征维度
        """
        return self.feature_dim
    
    def preprocess_batch(self, states: List[State]) -> np.ndarray:
        """
        预处理一批游戏状态
        
        Args:
            states (List[State]): 游戏状态列表
        
        Returns:
            np.ndarray: 特征批次
        """
        return np.stack([self.extract_features(state) for state in states])


class CardGameFeatureExtractor(FeatureExtractor):
    """
    卡牌游戏特征提取器
    
    为卡牌游戏设计的特征提取器，提供更多卡牌游戏特定的特征。
    """
    
    def __init__(
        self, 
        game_descriptor: GameDescriptor,
        include_history: bool = True,
        include_legal_actions: bool = True,
        include_player_features: bool = True,
        normalize: bool = True
    ):
        """
        初始化卡牌游戏特征提取器
        
        Args:
            game_descriptor (GameDescriptor): 游戏描述器
            include_history (bool, optional): 是否包含历史信息. Defaults to True.
            include_legal_actions (bool, optional): 是否包含合法动作信息. Defaults to True.
            include_player_features (bool, optional): 是否包含玩家特定特征. Defaults to True.
            normalize (bool, optional): 是否归一化特征. Defaults to True.
        """
        super().__init__(game_descriptor)
        self.include_history = include_history
        self.include_legal_actions = include_legal_actions
        self.include_player_features = include_player_features
        self.normalize = normalize
        
        # 计算特征维度
        base_dim = np.prod(game_descriptor.state_shape)
        history_dim = np.prod(game_descriptor.state_shape) * 3 if include_history else 0
        action_dim = np.prod(game_descriptor.action_shape) if include_legal_actions else 0
        player_dim = game_descriptor.num_players * 5 if include_player_features else 0
        
        self.feature_dim = (int(base_dim + history_dim + action_dim + player_dim),)
    
    def extract_features(self, state: State) -> np.ndarray:
        """
        从游戏状态中提取特征
        
        Args:
            state (State): 游戏状态
        
        Returns:
            np.ndarray: 特征向量或矩阵
        """
        # 基础特征
        features = state.to_numpy().flatten()
        
        try:
            # 历史信息
            if self.include_history and hasattr(state, 'get_last_moves'):
                last_moves = state.get_last_moves(3)  # 获取最近3个动作
                history_features = np.concatenate([move.to_numpy().flatten() for move in last_moves]) if last_moves else np.zeros(0)
                features = np.concatenate([features, history_features])
            
            # 合法动作
            if self.include_legal_actions and hasattr(state, 'get_legal_actions'):
                legal_actions = state.get_legal_actions()
                action_features = np.zeros(np.prod(self.game_descriptor.action_shape))
                for action in legal_actions:
                    action_idx = action.to_index()
                    if action_idx < len(action_features):
                        action_features[action_idx] = 1
                features = np.concatenate([features, action_features])
            
            # 玩家特定特征
            if self.include_player_features and hasattr(state, 'get_player_features'):
                player_features = state.get_player_features()
                features = np.concatenate([features, player_features])
        except (AttributeError, NotImplementedError) as e:
            # 如果缺少某些方法，则忽略相应的特征
            pass
        
        # 填充或裁剪到正确的维度
        if len(features) < self.feature_dim[0]:
            features = np.pad(features, (0, self.feature_dim[0] - len(features)))
        elif len(features) > self.feature_dim[0]:
            features = features[:self.feature_dim[0]]
        
        # 归一化
        if self.normalize:
            features = self._normalize_features(features)
        
        return features
    
    def get_feature_dim(self) -> Tuple[int, ...]:
        """
        获取特征维度
        
        Returns:
            Tuple[int, ...]: 特征维度
        """
        return self.feature_dim
    
    def preprocess_batch(self, states: List[State]) -> np.ndarray:
        """
        预处理一批游戏状态
        
        Args:
            states (List[State]): 游戏状态列表
        
        Returns:
            np.ndarray: 特征批次
        """
        return np.stack([self.extract_features(state) for state in states])
    
    def _normalize_features(self, features: np.ndarray) -> np.ndarray:
        """
        归一化特征
        
        Args:
            features (np.ndarray): 特征向量
        
        Returns:
            np.ndarray: 归一化后的特征向量
        """
        # 简单归一化到[-1, 1]
        max_abs = np.max(np.abs(features)) or 1.0
        return features / max_abs


class FeatureExtractorRegistry:
    """
    特征提取器注册表
    
    用于注册和获取特征提取器。
    """
    
    _registry: Dict[str, Type[FeatureExtractor]] = {}
    
    @classmethod
    def register(cls, name: str, extractor_cls: Type[FeatureExtractor]):
        """
        注册特征提取器
        
        Args:
            name (str): 特征提取器名称
            extractor_cls (Type[FeatureExtractor]): 特征提取器类
        """
        if name in cls._registry:
            print(f"警告: 特征提取器 '{name}' 已存在，正在覆盖")
        cls._registry[name] = extractor_cls
    
    @classmethod
    def get(cls, name: str) -> Type[FeatureExtractor]:
        """
        获取特征提取器
        
        Args:
            name (str): 特征提取器名称
        
        Returns:
            Type[FeatureExtractor]: 特征提取器类
        
        Raises:
            KeyError: 如果特征提取器不存在
        """
        if name not in cls._registry:
            raise KeyError(f"特征提取器 '{name}' 不存在")
        return cls._registry[name]
    
    @classmethod
    def list_extractors(cls) -> List[str]:
        """
        列出所有可用的特征提取器
        
        Returns:
            List[str]: 特征提取器名称列表
        """
        return list(cls._registry.keys())


# 注册默认特征提取器
FeatureExtractorRegistry.register('default', DefaultFeatureExtractor)
FeatureExtractorRegistry.register('card_game', CardGameFeatureExtractor) 