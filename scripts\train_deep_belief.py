#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
训练 DeepBeliefNetwork 脚本

此脚本用于训练 DeepBeliefNetwork 模型，目标是预测对手手牌概率分布。
支持从游戏记录中生成训练数据，或直接加载已处理的数据集。
"""
import os
import time
import argparse
import logging
import random
from typing import Dict, List, Tuple, Optional, Any, Union
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, random_split
from torch.utils.tensorboard import SummaryWriter

from cardgame_ai.algorithms.belief_tracking.deep_belief import DeepBeliefNetwork
from cardgame_ai.games.common.belief_state import BeliefState, BeliefSource
from cardgame_ai.games.doudizhu.card import Card, CardRank, CardSuit
from cardgame_ai.core.base import State

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class HandBeliefDataset(Dataset):
    """
    手牌信念数据集

    用于训练和评估 DeepBeliefNetwork，每个样本包含历史动作序列、公共状态和目标手牌概率分布。
    """

    def __init__(
        self,
        data_path: str,
        max_seq_length: int = 50,
        split: str = "train",
        generate_synthetic: bool = False,
        card_vocab_size: int = 54
    ):
        """
        初始化数据集

        Args:
            data_path (str): 数据路径，可以是游戏记录目录或预处理后的数据文件
            max_seq_length (int, optional): 最大序列长度. Defaults to 50.
            split (str, optional): 数据集分割，可选 "train", "val", "test". Defaults to "train".
            generate_synthetic (bool, optional): 是否生成合成数据. Defaults to False.
            card_vocab_size (int, optional): 牌词汇表大小. Defaults to 54.
        """
        self.data_path = data_path
        self.max_seq_length = max_seq_length
        self.split = split
        self.generate_synthetic = generate_synthetic
        self.card_vocab_size = card_vocab_size

        # 数据列表，每个元素是 (动作历史, 公共状态, 目标概率分布) 的元组
        self.data = []

        # 所有可能的牌
        self.all_cards = self._generate_all_cards()

        # 加载数据
        self._load_data()

    def __len__(self) -> int:
        """返回数据集大小"""
        return len(self.data)

    def __getitem__(self, idx: int) -> Tuple[Dict[str, torch.Tensor], torch.Tensor]:
        """
        获取数据集中的一个样本

        Args:
            idx (int): 样本索引

        Returns:
            Tuple[Dict[str, torch.Tensor], torch.Tensor]: (输入, 目标) 元组
                输入字典包含 'action_history' 和 'state'
                目标是手牌概率分布
        """
        action_history, state, target_probs = self.data[idx]

        # 将动作历史转换为模型输入格式
        action_history_tensor = torch.zeros(self.max_seq_length, 128)  # 假设动作编码为128维
        seq_length = min(len(action_history), self.max_seq_length)
        
        for i in range(seq_length):
            # 这里假设已经对动作进行了编码，实际应根据DeepBeliefNetwork的输入格式调整
            action_history_tensor[i] = torch.tensor(action_history[i]["encoded_action"])
        
        # 将状态转换为模型输入格式
        state_tensor = torch.tensor(state, dtype=torch.float32)
        
        # 将目标概率转换为张量
        target_tensor = torch.tensor(target_probs, dtype=torch.float32)
        
        return {
            "action_history": action_history_tensor,
            "state": state_tensor,
            "action_lengths": torch.tensor(seq_length, dtype=torch.long)
        }, target_tensor

    def _load_data(self) -> None:
        """
        加载数据集

        会根据 data_path 判断是直接加载预处理好的数据，还是从游戏记录生成数据。
        如果 generate_synthetic 为 True，则生成合成数据用于测试和开发。
        """
        if self.generate_synthetic:
            logger.info("生成合成数据集")
            self._generate_synthetic_data()
            return

        if os.path.isfile(self.data_path) and self.data_path.endswith(".npz"):
            logger.info(f"加载预处理数据: {self.data_path}")
            self._load_preprocessed_data()
        elif os.path.isdir(self.data_path):
            logger.info(f"从游戏记录生成数据: {self.data_path}")
            self._generate_data_from_game_logs()
        else:
            raise ValueError(f"无效的数据路径: {self.data_path}")

    def _load_preprocessed_data(self) -> None:
        """加载预处理好的数据"""
        data = np.load(self.data_path, allow_pickle=True)
        
        action_histories = data["action_histories"]
        states = data["states"]
        target_probs = data["target_probs"]
        
        # 根据split选择数据
        # 通常预处理的数据已经分割好train/val/test
        if f"{self.split}_indices" in data:
            indices = data[f"{self.split}_indices"]
            action_histories = action_histories[indices]
            states = states[indices]
            target_probs = target_probs[indices]
        
        for i in range(len(action_histories)):
            self.data.append((action_histories[i], states[i], target_probs[i]))
        
        logger.info(f"加载了 {len(self.data)} 个{self.split}样本")

    def _generate_data_from_game_logs(self) -> None:
        """从游戏记录生成数据"""
        # 实际项目中，这个方法会比较复杂，需要解析游戏记录，提取动作序列、状态和手牌信息
        # 这里仅提供示例框架

        log_files = [f for f in os.listdir(self.data_path) if f.endswith(".json")]
        logger.info(f"发现 {len(log_files)} 个游戏记录文件")
        
        for log_file in log_files:
            file_path = os.path.join(self.data_path, log_file)
            try:
                # 解析游戏记录
                game_data = self._parse_game_log(file_path)
                self.data.extend(game_data)
            except Exception as e:
                logger.error(f"处理文件 {log_file} 时出错: {e}")
        
        logger.info(f"从游戏记录生成了 {len(self.data)} 个样本")
        
        # 根据split分割数据
        random.shuffle(self.data)
        if self.split != "train":
            # 简单起见，取前20%为val，前20-30%为test
            total = len(self.data)
            if self.split == "val":
                self.data = self.data[:int(total * 0.2)]
            elif self.split == "test":
                self.data = self.data[int(total * 0.2):int(total * 0.3)]

    def _parse_game_log(self, log_file: str) -> List[Tuple]:
        """
        解析单个游戏记录文件

        Args:
            log_file (str): 游戏记录文件路径

        Returns:
            List[Tuple]: 生成的数据样本列表
        """
        # 这个方法需要根据实际的游戏记录格式进行实现
        # 以下仅为示例框架
        import json
        
        with open(log_file, 'r', encoding='utf-8') as f:
            game_record = json.load(f)
        
        samples = []
        # 解析游戏记录，提取动作序列、状态和手牌信息
        # 示例：
        # for turn in game_record["turns"]:
        #     action_history = turn["action_history"]
        #     state = turn["state"]
        #     hands = turn["hands"]
        #     
        #     # 计算目标概率分布
        #     target_probs = self._calculate_target_probs(hands)
        #     
        #     samples.append((action_history, state, target_probs))
        
        return samples

    def _generate_synthetic_data(self) -> None:
        """生成合成数据用于测试和开发"""
        sample_count = 1000  # 生成样本数量
        logger.info(f"生成 {sample_count} 个合成样本")
        
        for _ in range(sample_count):
            # 生成随机动作历史
            seq_length = random.randint(5, self.max_seq_length)
            action_history = []
            
            for _ in range(seq_length):
                # 生成随机动作
                action_type = random.choice(["play", "pass"])
                if action_type == "play":
                    # 随机选择1-3张牌
                    card_count = random.randint(1, 3)
                    cards = random.sample(self.all_cards, card_count)
                    encoded_action = np.random.rand(128)  # 假设动作编码为128维
                    action = {
                        "type": "play",
                        "cards": cards,
                        "encoded_action": encoded_action
                    }
                else:
                    encoded_action = np.random.rand(128)  # 假设动作编码为128维
                    action = {
                        "type": "pass",
                        "cards": [],
                        "encoded_action": encoded_action
                    }
                action_history.append(action)
            
            # 生成随机状态
            state = np.random.rand(256)  # 假设状态编码为256维
            
            # 生成随机目标概率分布
            target_probs = np.random.dirichlet(np.ones(len(self.all_cards)))
            
            self.data.append((action_history, state, target_probs))

    def _generate_all_cards(self) -> List[str]:
        """
        生成所有可能的牌列表

        Returns:
            List[str]: 所有牌的字符串表示列表
        """
        cards = []
        # 生成普通牌
        for rank in CardRank:
            if rank < CardRank.SMALL_JOKER:
                for suit in CardSuit:
                    card = f"{suit.to_char()}{rank.to_char()}"
                    cards.append(card)
            elif rank == CardRank.SMALL_JOKER:
                cards.append("BJ")  # 小王
            elif rank == CardRank.BIG_JOKER:
                cards.append("RJ")  # 大王
        return cards


def train(args: argparse.Namespace) -> None:
    """
    训练模型

    Args:
        args (argparse.Namespace): 命令行参数
    """
    # 设置随机种子
    random.seed(args.seed)
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(args.seed)

    # 设置设备
    device = torch.device(args.device)
    logger.info(f"使用设备: {device}")

    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 创建TensorBoard日志
    writer = SummaryWriter(log_dir=os.path.join(args.output_dir, "tensorboard"))

    # 加载数据集
    logger.info("加载训练集...")
    train_dataset = HandBeliefDataset(
        data_path=args.data_path,
        max_seq_length=args.max_seq_length,
        split="train",
        generate_synthetic=args.use_synthetic_data,
        card_vocab_size=args.card_vocab_size
    )
    
    logger.info("加载验证集...")
    val_dataset = HandBeliefDataset(
        data_path=args.data_path,
        max_seq_length=args.max_seq_length,
        split="val",
        generate_synthetic=args.use_synthetic_data,
        card_vocab_size=args.card_vocab_size
    )
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=args.batch_size,
        shuffle=True,
        num_workers=args.num_workers,
        pin_memory=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=args.num_workers,
        pin_memory=True
    )
    
    # 初始化模型
    logger.info("初始化模型...")
    model = DeepBeliefNetwork(
        card_vocab_size=args.card_vocab_size,
        action_embedding_dim=args.action_embedding_dim,
        history_hidden_dim=args.history_hidden_dim,
        state_input_dim=args.state_input_dim,
        state_hidden_dim=args.state_hidden_dim,
        output_dim=args.output_dim,
        history_encoder_type=args.history_encoder_type,
        history_num_layers=args.history_num_layers,
        state_num_layers=args.state_num_layers,
        dropout=args.dropout
    )
    model = model.to(device)
    
    # 定义损失函数
    criterion = nn.KLDivLoss(reduction='batchmean')
    
    # 定义优化器
    optimizer = optim.Adam(model.parameters(), lr=args.learning_rate, weight_decay=args.weight_decay)
    
    # 学习率调度器
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.5, patience=3, verbose=True
    )
    
    # 训练循环
    logger.info("开始训练...")
    best_val_loss = float('inf')
    
    for epoch in range(args.epochs):
        logger.info(f"Epoch {epoch+1}/{args.epochs}")
        
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_kl_div = 0.0
        train_samples = 0
        
        start_time = time.time()
        
        for batch_idx, (inputs, targets) in enumerate(train_loader):
            # 将数据移到设备
            action_history = inputs["action_history"].to(device)
            state = inputs["state"].to(device)
            action_lengths = inputs["action_lengths"].to(device)
            targets = targets.to(device)
            
            # 前向传播
            outputs = model(action_history, state, action_lengths)
            
            # 应用log_softmax，因为KLDivLoss期望输入为对数概率
            log_outputs = torch.log_softmax(outputs, dim=1)
            
            # 计算损失
            loss = criterion(log_outputs, targets)
            
            # 反向传播和优化
            optimizer.zero_grad()
            loss.backward()
            
            # 梯度裁剪
            if args.clip_grad_norm > 0:
                torch.nn.utils.clip_grad_norm_(model.parameters(), args.clip_grad_norm)
            
            optimizer.step()
            
            # 累积统计信息
            batch_size = targets.size(0)
            train_loss += loss.item() * batch_size
            train_kl_div += loss.item() * batch_size
            train_samples += batch_size
            
            # 打印进度
            if (batch_idx + 1) % args.log_interval == 0:
                logger.info(f"Train Epoch: {epoch+1} [{batch_idx+1}/{len(train_loader)}] "
                           f"Loss: {loss.item():.6f}")
        
        # 计算平均训练损失
        avg_train_loss = train_loss / train_samples
        avg_train_kl_div = train_kl_div / train_samples
        
        # 记录训练指标
        writer.add_scalar("Loss/train", avg_train_loss, epoch)
        writer.add_scalar("KL_Divergence/train", avg_train_kl_div, epoch)
        
        # 计算训练时间
        epoch_time = time.time() - start_time
        logger.info(f"Train Epoch: {epoch+1} completed in {epoch_time:.2f}s. "
                   f"Avg loss: {avg_train_loss:.6f}, Avg KL div: {avg_train_kl_div:.6f}")
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_kl_div = 0.0
        val_samples = 0
        
        with torch.no_grad():
            for inputs, targets in val_loader:
                # 将数据移到设备
                action_history = inputs["action_history"].to(device)
                state = inputs["state"].to(device)
                action_lengths = inputs["action_lengths"].to(device)
                targets = targets.to(device)
                
                # 前向传播
                outputs = model(action_history, state, action_lengths)
                
                # 应用log_softmax
                log_outputs = torch.log_softmax(outputs, dim=1)
                
                # 计算损失
                loss = criterion(log_outputs, targets)
                
                # 累积统计信息
                batch_size = targets.size(0)
                val_loss += loss.item() * batch_size
                val_kl_div += loss.item() * batch_size
                val_samples += batch_size
        
        # 计算平均验证损失
        avg_val_loss = val_loss / val_samples
        avg_val_kl_div = val_kl_div / val_samples
        
        # 记录验证指标
        writer.add_scalar("Loss/val", avg_val_loss, epoch)
        writer.add_scalar("KL_Divergence/val", avg_val_kl_div, epoch)
        
        logger.info(f"Val Epoch: {epoch+1} Avg loss: {avg_val_loss:.6f}, Avg KL div: {avg_val_kl_div:.6f}")
        
        # 更新学习率
        scheduler.step(avg_val_loss)
        
        # 保存最佳模型
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            best_model_path = os.path.join(args.output_dir, "best_model.pt")
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_loss': best_val_loss,
                'args': vars(args)
            }, best_model_path)
            logger.info(f"保存最佳模型到 {best_model_path}")
        
        # 定期保存检查点
        if (epoch + 1) % args.save_interval == 0:
            checkpoint_path = os.path.join(args.output_dir, f"checkpoint_epoch_{epoch+1}.pt")
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_loss': avg_val_loss,
                'args': vars(args)
            }, checkpoint_path)
            logger.info(f"保存检查点到 {checkpoint_path}")
    
    # 保存最终模型
    final_model_path = os.path.join(args.output_dir, "final_model.pt")
    torch.save({
        'epoch': args.epochs,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'val_loss': avg_val_loss,
        'args': vars(args)
    }, final_model_path)
    logger.info(f"保存最终模型到 {final_model_path}")
    
    # 关闭TensorBoard写入器
    writer.close()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="训练 DeepBeliefNetwork 模型")
    
    # 数据参数
    parser.add_argument("--data_path", type=str, required=True,
                        help="数据路径，可以是游戏记录目录或预处理后的数据文件")
    parser.add_argument("--output_dir", type=str, default="./models/deep_belief",
                        help="模型输出目录")
    parser.add_argument("--use_synthetic_data", action="store_true",
                        help="使用合成数据进行测试和开发")
    parser.add_argument("--max_seq_length", type=int, default=50,
                        help="最大序列长度")
    
    # 模型参数
    parser.add_argument("--card_vocab_size", type=int, default=54,
                        help="牌词汇表大小")
    parser.add_argument("--action_embedding_dim", type=int, default=128,
                        help="动作嵌入维度")
    parser.add_argument("--history_hidden_dim", type=int, default=256,
                        help="历史编码器隐藏层维度")
    parser.add_argument("--state_input_dim", type=int, default=256,
                        help="状态输入维度")
    parser.add_argument("--state_hidden_dim", type=int, default=128,
                        help="状态编码器隐藏层维度")
    parser.add_argument("--output_dim", type=int, default=54,
                        help="输出维度，等于牌的数量")
    parser.add_argument("--history_encoder_type", type=str, default="lstm",
                        choices=["lstm", "gru", "transformer"],
                        help="历史编码器类型")
    parser.add_argument("--history_num_layers", type=int, default=2,
                        help="历史编码器层数")
    parser.add_argument("--state_num_layers", type=int, default=2,
                        help="状态编码器层数")
    parser.add_argument("--dropout", type=float, default=0.1,
                        help="Dropout比率")
    
    # 训练参数
    parser.add_argument("--batch_size", type=int, default=32,
                        help="批量大小")
    parser.add_argument("--epochs", type=int, default=50,
                        help="训练轮数")
    parser.add_argument("--learning_rate", type=float, default=0.001,
                        help="学习率")
    parser.add_argument("--weight_decay", type=float, default=1e-4,
                        help="权重衰减")
    parser.add_argument("--clip_grad_norm", type=float, default=1.0,
                        help="梯度裁剪范数，设为0禁用")
    parser.add_argument("--seed", type=int, default=42,
                        help="随机种子")
    parser.add_argument("--device", type=str, default="cuda" if torch.cuda.is_available() else "cpu",
                        help="训练设备")
    
    # 其他参数
    parser.add_argument("--num_workers", type=int, default=4,
                        help="数据加载器工作进程数")
    parser.add_argument("--log_interval", type=int, default=10,
                        help="日志打印间隔（批次）")
    parser.add_argument("--save_interval", type=int, default=5,
                        help="检查点保存间隔（轮数）")
    
    args = parser.parse_args()
    
    # 打印参数
    logger.info("训练参数:")
    for k, v in vars(args).items():
        logger.info(f"  {k}: {v}")
    
    # 开始训练
    train(args)


if __name__ == "__main__":
    main() 