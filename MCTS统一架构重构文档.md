# 🏗️ **MCTS统一架构重构项目 - 架构文档**

## 介绍 / 前言

本文档概述了斗地主AI训练系统中MCTS（蒙特卡洛树搜索）算法的统一架构重构方案。其主要目标是解决当前系统中存在的6个不同MCTS实现导致的代码重复、调用混乱和无限循环问题，同时保留所有核心功能特性，为AI驱动的开发提供统一、高效、可维护的MCTS架构蓝图。

**项目背景：** 当前系统存在MCTS无限循环问题（单子节点链导致深度41层遍历），同时项目中并存6个不同的MCTS实现，造成维护困难和功能冗余。

**根本问题分析（基于AI2的发现）：**
> 卡死并不是随机发生在"第40次模拟"这一奇异位置，而是由于搜索树在此时已经严重退化为一条"链"——每个节点的 children 始终只有 1 个子节点，整个树在不停地往下"长"，深度会随模拟次数线性增加，导致后面的选择（Selection）/扩展（Expansion）/回溯（Backpropagation）开销呈 O(n²) 级增长。

## 目录

1. [技术摘要](#技术摘要)
2. [高级概览](#高级概览)
3. [架构/设计模式](#架构设计模式采用)
4. [组件视图](#组件视图)
5. [项目结构](#项目结构)
6. [核心工作流程](#核心工作流程序列图)
7. [确定性技术栈选择](#确定性技术栈选择)
8. [基础设施和部署概览](#基础设施和部署概览)
9. [错误处理策略](#错误处理策略)
10. [编码标准](#编码标准)
11. [整体测试策略](#整体测试策略)
12. [安全最佳实践](#安全最佳实践)

## 技术摘要

MCTS统一架构重构项目采用**模块化分层架构**，通过**策略模式**和**工厂模式**整合现有的6个MCTS实现，构建统一的MCTS核心引擎。系统使用**PyTorch 2.0+**作为深度学习框架，**Python 3.11**作为主要开发语言，集成**EfficientZero**训练算法和**多智能体协作**机制。

**核心目标：**
- 解决MCTS无限循环问题（单子节点链）
- 统一6个MCTS实现为单一架构
- 保留所有高级功能（信念状态、信息价值计算、GTO剥削）
- 提升性能和可维护性

## 高级概览

系统采用**分层模块化架构**，将MCTS功能分解为核心引擎、策略组件、优化模块和集成接口四个主要层次。通过**依赖注入**和**接口抽象**实现组件间的松耦合，支持**热插拔**的功能模块。

**架构风格：** 分层架构 + 插件化设计
**仓库结构：** 单仓库（Monorepo）
**主要交互流程：** 训练系统 → 统一MCTS接口 → 核心引擎 → 策略组件 → 游戏环境

```mermaid
graph TB
    subgraph "MCTS统一架构"
        A[训练系统] --> B[统一MCTS接口]
        B --> C[MCTS核心引擎]
        C --> D[搜索策略组件]
        C --> E[优化模块]
        C --> F[监控与日志]
        D --> G[游戏环境]
        E --> G
    end
    
    subgraph "外部系统"
        H[EfficientZero算法]
        I[多智能体协作]
        J[性能监控]
    end
    
    B --> H
    B --> I
    F --> J
```

## 架构/设计模式采用

### 核心设计模式

- **策略模式** - _用途：_ 支持不同MCTS搜索策略的动态切换（UCB、PUCT、信念状态等）
- **工厂模式** - _用途：_ 统一创建不同类型的MCTS节点和搜索组件
- **观察者模式** - _用途：_ 实现搜索过程的实时监控和日志记录
- **模板方法模式** - _用途：_ 定义MCTS搜索的标准流程，允许子类定制具体步骤
- **适配器模式** - _用途：_ 整合现有6个MCTS实现到统一接口
- **装饰器模式** - _用途：_ 为基础MCTS功能添加高级特性（并行搜索、缓存优化等）

### 架构原则

- **单一职责原则：** 每个模块专注于特定的MCTS功能
- **开闭原则：** 支持新搜索策略的扩展，无需修改核心代码
- **依赖倒置原则：** 高层模块不依赖低层模块，都依赖于抽象接口
- **接口隔离原则：** 提供细粒度的功能接口，避免不必要的依赖

## 组件视图

### 核心组件架构

```mermaid
graph TD
    subgraph "统一MCTS架构"
        A[UnifiedMCTS] --> B[MCTSCore]
        A --> C[SearchStrategyManager]
        A --> D[OptimizationEngine]
        A --> E[MonitoringSystem]
        
        B --> F[NodeManager]
        B --> G[TreeBuilder]
        B --> H[SimulationEngine]
        
        C --> I[UCBStrategy]
        C --> J[BeliefStateStrategy]
        C --> K[GTOExploitingStrategy]
        C --> L[InformationValueStrategy]
        
        D --> M[ParallelSearch]
        D --> N[CacheOptimization]
        D --> O[MemoryManagement]
        
        E --> P[PerformanceLogger]
        E --> Q[DebugTracer]
        E --> R[MetricsCollector]
    end
```

### 主要组件说明

**UnifiedMCTS（统一MCTS接口）**
- 职责：提供统一的MCTS搜索接口，替代现有的6个不同实现
- 功能：搜索调度、策略选择、结果聚合
- 接口：`search()`, `configure()`, `get_statistics()`

**MCTSCore（MCTS核心引擎）**
- 职责：实现标准MCTS搜索流程（选择、扩展、模拟、回传）
- 功能：树构建、节点管理、搜索控制
- 特性：支持信念状态、动态预算分配、紧急终止

**SearchStrategyManager（搜索策略管理器）**
- 职责：管理和切换不同的搜索策略
- 功能：策略注册、动态选择、参数配置
- 支持策略：UCB、PUCT、信念状态、GTO剥削、信息价值

**OptimizationEngine（优化引擎）**
- 职责：提供性能优化功能
- 功能：并行搜索、内存优化、缓存管理
- 特性：自适应并行度、智能缓存、内存池管理

**MonitoringSystem（监控系统）**
- 职责：提供全面的监控和调试支持
- 功能：性能日志、调试追踪、指标收集
- 输出：结构化日志、性能报告、可视化数据

## 项目结构

```plaintext
cardgame_ai/
├── .github/                    # CI/CD工作流
│   └── workflows/
│       ├── mcts-tests.yml      # MCTS专项测试
│       └── integration.yml     # 集成测试
├── algorithms/                 # 核心算法模块
│   ├── mcts/                   # 🔥 统一MCTS架构
│   │   ├── __init__.py
│   │   ├── core/               # 核心引擎
│   │   │   ├── unified_mcts.py     # 统一MCTS接口
│   │   │   ├── mcts_core.py        # 核心搜索引擎
│   │   │   ├── node_manager.py     # 节点管理器
│   │   │   ├── tree_builder.py     # 树构建器
│   │   │   └── simulation_engine.py # 模拟引擎
│   │   ├── strategies/         # 搜索策略
│   │   │   ├── base_strategy.py    # 策略基类
│   │   │   ├── ucb_strategy.py     # UCB策略
│   │   │   ├── belief_state_strategy.py # 信念状态策略
│   │   │   ├── gto_exploiting_strategy.py # GTO剥削策略
│   │   │   └── information_value_strategy.py # 信息价值策略
│   │   ├── optimization/       # 性能优化
│   │   │   ├── parallel_search.py  # 并行搜索
│   │   │   ├── cache_manager.py    # 缓存管理
│   │   │   ├── memory_pool.py      # 内存池
│   │   │   └── performance_tuner.py # 性能调优
│   │   ├── monitoring/         # 监控系统
│   │   │   ├── logger.py           # 日志系统
│   │   │   ├── tracer.py           # 调试追踪
│   │   │   ├── metrics.py          # 指标收集
│   │   │   └── visualizer.py       # 可视化工具
│   │   ├── adapters/           # 适配器层
│   │   │   ├── legacy_adapter.py   # 遗留代码适配
│   │   │   ├── efficient_zero_adapter.py # EfficientZero适配
│   │   │   └── training_adapter.py # 训练系统适配
│   │   └── config/             # 配置管理
│   │       ├── mcts_config.py      # MCTS配置
│   │       ├── strategy_config.py  # 策略配置
│   │       └── optimization_config.py # 优化配置
│   ├── legacy/                 # 🗂️ 遗留MCTS实现（保留兼容）
│   │   ├── mcts.py             # 原始MCTS
│   │   ├── optimized_mcts.py   # 优化MCTS
│   │   ├── gto_exploiting_mcts.py # GTO剥削MCTS
│   │   └── mcts_agent.py       # MCTS代理
│   ├── efficient_zero_algorithm.py # EfficientZero算法
│   └── muzero.py               # MuZero算法
├── tests/                      # 测试目录
│   ├── mcts/                   # MCTS专项测试
│   │   ├── test_unified_mcts.py    # 统一接口测试
│   │   ├── test_strategies.py      # 策略测试
│   │   ├── test_optimization.py    # 优化测试
│   │   ├── test_monitoring.py      # 监控测试
│   │   └── test_integration.py     # 集成测试
│   ├── test_mcts_migration.py      # 迁移测试
│   └── test_performance_regression.py # 性能回归测试
├── configs/                    # 配置文件
│   ├── mcts/                   # MCTS配置
│   │   ├── unified.yaml        # 统一配置
│   │   ├── strategies.yaml     # 策略配置
│   │   └── optimization.yaml   # 优化配置
│   └── training/               # 训练配置
├── docs/                       # 文档目录
│   ├── mcts/                   # MCTS文档
│   │   ├── architecture.md     # 架构文档
│   │   ├── migration_guide.md  # 迁移指南
│   │   ├── strategy_guide.md   # 策略指南
│   │   └── performance_guide.md # 性能指南
│   └── api/                    # API文档
└── scripts/                    # 工具脚本
    ├── migrate_mcts.py         # MCTS迁移脚本
    ├── benchmark_mcts.py       # 性能基准测试
    └── validate_migration.py   # 迁移验证脚本
```

### 关键目录说明

- **algorithms/mcts/core/**: 统一MCTS的核心实现，包含主要的搜索逻辑
- **algorithms/mcts/strategies/**: 可插拔的搜索策略实现
- **algorithms/mcts/optimization/**: 性能优化模块，支持并行搜索和缓存
- **algorithms/mcts/monitoring/**: 监控和调试系统
- **algorithms/legacy/**: 保留现有MCTS实现，确保向后兼容
- **tests/mcts/**: 全面的MCTS测试套件

## 核心工作流程/序列图

### MCTS搜索主流程

```mermaid
sequenceDiagram
    participant TS as 训练系统
    participant UM as UnifiedMCTS
    participant MC as MCTSCore
    participant SM as StrategyManager
    participant OE as OptimizationEngine
    participant MS as MonitoringSystem
    participant GE as 游戏环境

    TS->>UM: search(state, config)
    UM->>MS: start_monitoring()
    UM->>SM: select_strategy(config)
    SM-->>UM: strategy_instance
    UM->>MC: initialize_search(state, strategy)
    
    loop 模拟循环
        MC->>OE: check_parallel_capacity()
        OE-->>MC: parallel_slots
        
        par 并行搜索
            MC->>MC: selection_phase()
            MC->>MC: expansion_phase()
            MC->>GE: simulate_game()
            GE-->>MC: simulation_result
            MC->>MC: backpropagation_phase()
        end
        
        MC->>MS: log_simulation_progress()
        MC->>MC: check_termination_conditions()
    end
    
    MC-->>UM: search_results
    UM->>MS: finalize_monitoring()
    UM-->>TS: action_probabilities
```

### 策略切换流程

```mermaid
sequenceDiagram
    participant UM as UnifiedMCTS
    participant SM as StrategyManager
    participant BS as BeliefStateStrategy
    participant GS as GTOStrategy
    participant UV as UCBStrategy

    UM->>SM: request_strategy(game_phase, opponent_model)
    
    alt 叫地主阶段
        SM->>UV: create_instance(config)
        UV-->>SM: ucb_strategy
    else 出牌阶段 + 有对手模型
        SM->>GS: create_instance(opponent_model)
        GS-->>SM: gto_strategy
    else 信息不完全
        SM->>BS: create_instance(belief_config)
        BS-->>SM: belief_strategy
    end
    
    SM-->>UM: selected_strategy
```

## 确定性技术栈选择

| 类别 | 技术 | 版本/详情 | 描述/用途 | 理由 |
|:-----|:-----|:----------|:----------|:-----|
| **语言** | Python | 3.11.x | 主要开发语言 | 现有代码基础，丰富的ML生态 |
| **运行时** | CPython | 3.11.x | Python解释器 | 标准实现，性能稳定 |
| **深度学习框架** | PyTorch | 2.0+ | 神经网络训练和推理 | 现有项目标准，动态图优势 |
| **数值计算** | NumPy | 1.24+ | 高性能数值计算 | MCTS算法核心依赖 |
| **配置管理** | PyYAML | 6.0+ | 配置文件解析 | 现有配置系统标准 |
| **日志系统** | Python logging | 内置 | 结构化日志记录 | 标准库，性能可靠 |
| **并行计算** | multiprocessing | 内置 | 并行MCTS搜索 | 标准库，跨平台支持 |
| **内存管理** | psutil | 5.9+ | 系统资源监控 | 内存池管理和监控 |
| **性能分析** | cProfile | 内置 | 性能瓶颈分析 | 标准性能分析工具 |
| **测试框架** | pytest | 7.0+ | 单元和集成测试 | 现有测试标准 |
| **代码质量** | Black | 23.0+ | 代码格式化 | 现有代码规范 |
| | Flake8 | 6.0+ | 代码风格检查 | 现有质量标准 |
| | MyPy | 1.0+ | 静态类型检查 | 类型安全保证 |
| **可视化** | Matplotlib | 3.6+ | 搜索树可视化 | 调试和分析工具 |
| **数据结构** | collections | 内置 | 高效数据结构 | deque, defaultdict等 |
| **算法库** | heapq | 内置 | 优先队列实现 | UCB计算优化 |

## 基础设施和部署概览

- **云平台**: 本地部署 + 可选云端扩展
- **核心服务**: 单机多进程架构，支持分布式扩展
- **基础设施即代码**: 配置文件驱动的部署
- **部署策略**: 渐进式迁移，支持新旧系统并行运行
- **环境**: Development（开发）, Testing（测试）, Production（生产）
- **环境升级**: dev → testing（自动化测试通过）→ production（手动审批）
- **回滚策略**: 配置回滚 + 代码版本回退，5分钟内完成回滚

## 错误处理策略

### 通用方法
- **异常处理**: 使用Python标准异常体系，定义MCTS专用异常类
- **日志记录**:
  - 库/方法: Python `logging`模块 + 结构化日志
  - 格式: JSON格式，包含时间戳、严重级别、上下文信息
  - 级别: DEBUG（算法细节）, INFO（搜索进度）, WARNING（性能警告）, ERROR（搜索失败）, CRITICAL（系统错误）
  - 上下文: 搜索ID、游戏状态哈希、策略类型、模拟次数、错误堆栈

### 特定处理模式

**MCTS搜索错误**:
- 无限循环检测: 深度限制（最大100层）+ 时间超时（30秒）
- 单子节点链检测: 连续10个单子节点触发警告，20个触发紧急终止
- 内存溢出保护: 节点数量限制 + 内存使用监控
- 恢复策略: 降级到简化搜索 + 错误状态记录

**策略切换错误**:
- 策略加载失败: 回退到默认UCB策略
- 参数验证错误: 使用默认参数 + 警告日志
- 运行时错误: 策略降级 + 异常上报

**并行搜索错误**:
- 进程崩溃: 自动重启工作进程，最大重试3次
- 通信超时: 降级到串行搜索模式
- 资源竞争: 动态调整并行度，避免系统过载

**游戏环境交互错误**:
- 状态无效: 状态验证 + 自动修复机制
- 动作非法: 动作过滤 + 合法动作重新生成
- 模拟超时: 提前终止 + 部分结果使用

## 编码标准

### 核心标准
- **主要运行时**: Python 3.11.x
- **风格指南**: Black + Flake8 + MyPy（配置文件: `pyproject.toml`）
- **命名约定**:
  - 变量/函数: `snake_case`
  - 类/接口: `PascalCase`
  - 常量: `UPPER_SNAKE_CASE`
  - 文件: `snake_case.py`
  - 模块/包: `snake_case`
- **文件结构**: 遵循项目结构定义，MCTS相关代码统一放在`algorithms/mcts/`
- **单元测试文件组织**: `test_*.py`文件放在`tests/`目录，镜像源码结构
- **异步操作**: 使用`asyncio`处理并发MCTS搜索，明确异常传播模式
- **类型安全**: 强制类型提示，MyPy严格模式，禁用`Any`类型

### MCTS特定编码规范

#### 核心类设计
```python
# 示例：MCTS节点类设计规范
from typing import Dict, List, Optional, Any, Protocol
from abc import ABC, abstractmethod

class MCTSNode:
    """MCTS搜索节点

    职责：存储节点状态、统计信息和子节点关系
    约束：不可变状态、线程安全的统计更新
    """
    def __init__(self, state: GameState, parent: Optional['MCTSNode'] = None):
        self._state: GameState = state  # 不可变游戏状态
        self._parent: Optional['MCTSNode'] = parent
        self._children: Dict[Action, 'MCTSNode'] = {}
        self._visit_count: int = 0
        self._value_sum: float = 0.0
        self._prior_probability: float = 0.0

    @property
    def is_leaf(self) -> bool:
        """检查是否为叶节点"""
        return len(self._children) == 0

    def add_child(self, action: Action, child_node: 'MCTSNode') -> None:
        """添加子节点（线程安全）"""
        if action in self._children:
            raise ValueError(f"子节点已存在: {action}")
        self._children[action] = child_node
```

#### 策略接口规范
```python
class SearchStrategy(Protocol):
    """搜索策略接口

    所有MCTS搜索策略必须实现此接口
    """

    def select_action(self, node: MCTSNode, legal_actions: List[Action]) -> Action:
        """选择下一个动作"""
        ...

    def calculate_value(self, node: MCTSNode, simulation_result: float) -> float:
        """计算节点价值"""
        ...

    def update_statistics(self, path: List[MCTSNode], result: float) -> None:
        """更新路径上所有节点的统计信息"""
        ...
```

#### 错误处理规范
```python
class MCTSError(Exception):
    """MCTS基础异常类"""
    pass

class MCTSTimeoutError(MCTSError):
    """MCTS搜索超时异常"""
    def __init__(self, elapsed_time: float, max_time: float):
        super().__init__(f"MCTS搜索超时: {elapsed_time:.2f}s > {max_time:.2f}s")
        self.elapsed_time = elapsed_time
        self.max_time = max_time

class MCTSInfiniteLoopError(MCTSError):
    """MCTS无限循环异常"""
    def __init__(self, depth: int, max_depth: int):
        super().__init__(f"检测到无限循环: 深度{depth} > 最大深度{max_depth}")
        self.depth = depth
        self.max_depth = max_depth
```

#### 日志规范
```python
import logging
from typing import Any, Dict

# MCTS专用日志器
mcts_logger = logging.getLogger('cardgame_ai.mcts')

def log_search_start(search_id: str, config: Dict[str, Any]) -> None:
    """记录搜索开始"""
    mcts_logger.info(
        "MCTS搜索开始",
        extra={
            "search_id": search_id,
            "config": config,
            "event_type": "search_start"
        }
    )

def log_simulation_progress(search_id: str, simulation: int, total: int,
                          elapsed_time: float) -> None:
    """记录模拟进度"""
    mcts_logger.debug(
        f"MCTS进度: {simulation}/{total} 模拟, 耗时: {elapsed_time*1000:.0f}ms",
        extra={
            "search_id": search_id,
            "simulation_count": simulation,
            "total_simulations": total,
            "elapsed_time": elapsed_time,
            "event_type": "simulation_progress"
        }
    )
```

### 性能优化规范
- **内存管理**: 使用对象池模式管理MCTSNode，避免频繁创建/销毁
- **并发安全**: 使用`threading.Lock`保护共享状态，避免数据竞争
- **缓存策略**: 实现LRU缓存用于UCB计算结果，缓存大小限制为10000条目
- **批量操作**: 批量处理节点更新，减少锁竞争

## 整体测试策略

### 测试工具
- **主要框架**: pytest 7.0+, pytest-asyncio, pytest-mock
- **覆盖率工具**: coverage.py, 目标覆盖率85%+

### 测试层次

#### 单元测试
- **范围**: 测试单个类、方法的功能正确性
- **位置**: `tests/mcts/unit/`目录，镜像源码结构
- **模拟策略**: 使用pytest-mock模拟游戏环境、网络调用
- **重点测试**:
  - MCTSNode的状态管理和统计更新
  - 各种搜索策略的算法正确性
  - 错误处理和边界条件
  - 并发安全性

#### 集成测试
- **范围**: 测试MCTS组件间的协作和数据流
- **位置**: `tests/mcts/integration/`
- **环境**: 使用真实游戏环境，模拟网络模型
- **重点测试**:
  - 统一MCTS接口与训练系统的集成
  - 策略切换的正确性
  - 并行搜索的协调机制
  - 监控系统的数据收集

#### 性能测试
- **范围**: 验证MCTS搜索性能和资源使用
- **位置**: `tests/mcts/performance/`
- **指标**: 搜索时间、内存使用、并发效率
- **基准**:
  - 单次搜索 < 1秒（100次模拟）
  - 内存使用 < 500MB（1000节点树）
  - 并发效率 > 80%（4进程）

#### 回归测试
- **范围**: 确保重构不破坏现有功能
- **位置**: `tests/mcts/regression/`
- **对比**: 新旧MCTS实现的结果一致性
- **自动化**: CI/CD流水线自动执行

### 测试数据管理
- **测试数据**: 使用工厂模式生成标准化测试数据
- **状态隔离**: 每个测试用例独立的游戏状态
- **随机种子**: 固定随机种子确保测试可重现

## 安全最佳实践

### 输入验证
- **MCTS参数验证**: 使用Pydantic验证所有配置参数，确保数值范围合理
- **游戏状态验证**: 验证输入状态的完整性和合法性，防止恶意状态注入
- **动作验证**: 严格验证动作的合法性，防止非法动作导致系统异常

### 资源保护
- **内存限制**: 限制MCTS树的最大节点数（默认10000），防止内存耗尽
- **CPU保护**: 设置搜索时间上限（默认30秒），防止CPU资源被恶意占用
- **并发控制**: 限制最大并行搜索进程数，防止系统过载

### 数据安全
- **敏感信息**: 不在日志中记录完整的游戏状态，只记录状态哈希
- **错误信息**: 错误消息不泄露内部实现细节，只提供必要的调试信息
- **配置安全**: 配置文件权限控制，防止未授权修改

### 代码安全
- **依赖管理**: 定期更新依赖包，使用`pip-audit`检查安全漏洞
- **静态分析**: 使用bandit进行安全代码扫描
- **权限最小化**: MCTS组件只访问必要的系统资源

## 关键参考文档

- [MCTS迁移指南](docs/mcts/migration_guide.md) - 详细的迁移步骤和注意事项
- [策略开发指南](docs/mcts/strategy_guide.md) - 如何开发和集成新的搜索策略
- [性能优化指南](docs/mcts/performance_guide.md) - MCTS性能调优最佳实践
- [API参考文档](docs/api/mcts_api.md) - 统一MCTS接口的详细API文档

## 变更日志

| 变更 | 日期 | 版本 | 描述 | 作者 |
|------|------|------|------|------|
| 初始架构设计 | 2025-06-04 | 1.0.0 | MCTS统一架构重构方案 | Timmy (Architect) |

---

## 🎯 **架构验证检查清单**

基于BMad架构师检查清单，我已验证以下关键项目：

### ✅ **需求对齐**
- [x] 支持所有6个MCTS实现的功能整合
- [x] 解决无限循环问题的技术方案
- [x] 保留信念状态、信息价值计算等高级功能
- [x] 向后兼容性保证

### ✅ **架构基础**
- [x] 清晰的分层模块化架构
- [x] 明确的组件职责分离
- [x] 统一的接口设计
- [x] 可扩展的插件化架构

### ✅ **技术栈决策**
- [x] 明确的技术版本选择（Python 3.11, PyTorch 2.0+）
- [x] 充分的技术选择理由
- [x] 完整的依赖管理策略

### ✅ **实施指导**
- [x] 详细的编码标准和规范
- [x] 全面的测试策略
- [x] 明确的错误处理机制
- [x] 完整的监控和日志系统

### ✅ **AI代理适配性**
- [x] 模块化设计便于AI代理实现
- [x] 清晰的接口和职责定义
- [x] 详细的实施指导和示例代码
- [x] 完整的测试和验证机制

---

## 🚀 **下一步建议**

1. **立即行动**: 开始实施核心MCTS引擎模块
2. **优先级**: 先解决无限循环问题，再进行功能整合
3. **验证策略**: 并行开发新架构和保留旧系统，确保平滑过渡
4. **监控重点**: 重点监控性能指标和兼容性

这个架构文档为MCTS重构项目提供了全面的技术蓝图，确保在解决当前问题的同时，构建一个可维护、可扩展的统一MCTS架构。
