{"policy": {"type": "transformer", "hidden_dim": 256, "num_heads": 4, "num_layers": 4, "ff_dim": 512, "seq_len": 100, "dropout": 0.1, "use_mixed_precision": true, "use_model_quantization": false, "use_rlhf": false}, "training": {"learning_rate": 0.0001, "gamma": 0.99, "gae_lambda": 0.95, "clip_ratio": 0.2, "value_coef": 0.5, "entropy_coef": 0.01, "max_grad_norm": 0.5, "update_epochs": 4, "batch_size": 64, "num_episodes": 1000, "max_steps_per_episode": 1000, "save_interval": 100, "eval_interval": 50, "eval_episodes": 20}, "environment": {"name": "<PERSON><PERSON><PERSON><PERSON>", "num_players": 3, "observation_type": "dict"}, "evaluation": {"baseline_models": ["mlp", "rule_based"], "metrics": ["win_rate", "avg_reward", "avg_game_length"], "num_games": 100, "save_games": true, "visualize": true}}