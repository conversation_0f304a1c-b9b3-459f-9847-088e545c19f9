"""
深度信念追踪器模块

利用神经网络（如RNN、LSTM、Transformer）处理历史出牌序列和场面信息，
生成比基础贝叶斯方法更精确的手牌概率分布。
"""
from typing import Dict, List, Set, Optional, Any, Tuple, Union
import time
import logging
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F

from cardgame_ai.core.base import State
from cardgame_ai.games.common.belief_state import BeliefState, BeliefSource
from cardgame_ai.games.doudizhu.card import Card, CardRank, CardSuit
from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType, get_card_group_type
from cardgame_ai.algorithms.information_value import InformationValueCalculator, calculate_information_value, calculate_all_cards_information_value
from cardgame_ai.algorithms.belief_tracking.online_updater import OnlineBeliefUpdater

# 配置日志
logger = logging.getLogger(__name__)


class HistoryEncoder(nn.Module):
    """
    历史动作编码器

    将历史动作序列编码为固定长度的向量表示。
    可以使用RNN、LSTM或Transformer实现。
    """

    def __init__(
        self,
        input_dim: int,
        hidden_dim: int,
        num_layers: int = 2,
        dropout: float = 0.1,
        encoder_type: str = "lstm"
    ):
        """
        初始化历史编码器

        Args:
            input_dim (int): 输入维度（动作表示的维度）
            hidden_dim (int): 隐藏层维度
            num_layers (int, optional): 层数. Defaults to 2.
            dropout (float, optional): Dropout比率. Defaults to 0.1.
            encoder_type (str, optional): 编码器类型，可选"lstm"、"gru"或"transformer". Defaults to "lstm".
        """
        super().__init__()
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        self.encoder_type = encoder_type.lower()

        # 根据编码器类型创建相应的网络
        if self.encoder_type == "lstm":
            self.encoder = nn.LSTM(
                input_size=input_dim,
                hidden_size=hidden_dim,
                num_layers=num_layers,
                batch_first=True,
                dropout=dropout if num_layers > 1 else 0
            )
        elif self.encoder_type == "gru":
            self.encoder = nn.GRU(
                input_size=input_dim,
                hidden_size=hidden_dim,
                num_layers=num_layers,
                batch_first=True,
                dropout=dropout if num_layers > 1 else 0
            )
        elif self.encoder_type == "transformer":
            encoder_layer = nn.TransformerEncoderLayer(
                d_model=input_dim,
                nhead=4,  # 多头注意力的头数
                dim_feedforward=hidden_dim,
                dropout=dropout,
                batch_first=True
            )
            self.encoder = nn.TransformerEncoder(
                encoder_layer=encoder_layer,
                num_layers=num_layers
            )
            # Transformer需要位置编码
            self.position_encoder = nn.Embedding(100, input_dim)  # 假设最大序列长度为100
        else:
            raise ValueError(f"不支持的编码器类型: {encoder_type}")

    def forward(self, x: torch.Tensor, lengths: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        前向传播

        Args:
            x (torch.Tensor): 输入序列，形状为[batch_size, seq_len, input_dim]
            lengths (Optional[torch.Tensor], optional): 序列长度. Defaults to None.

        Returns:
            torch.Tensor: 编码后的表示，形状为[batch_size, hidden_dim]
        """
        batch_size, seq_len = x.size(0), x.size(1)

        if self.encoder_type in ["lstm", "gru"]:
            # 对于RNN类型的编码器，处理变长序列
            if lengths is not None:
                # 打包变长序列
                packed_x = nn.utils.rnn.pack_padded_sequence(
                    x, lengths.cpu(), batch_first=True, enforce_sorted=False
                )
                # 编码
                if self.encoder_type == "lstm":
                    output, (hidden, _) = self.encoder(packed_x)
                else:  # GRU
                    output, hidden = self.encoder(packed_x)
                # 解包
                output, _ = nn.utils.rnn.pad_packed_sequence(output, batch_first=True)
            else:
                # 固定长度序列
                if self.encoder_type == "lstm":
                    output, (hidden, _) = self.encoder(x)
                else:  # GRU
                    output, hidden = self.encoder(x)

            # 使用最后一个时间步的隐藏状态作为序列表示
            # 对于多层RNN，取最后一层的隐藏状态
            last_hidden = hidden[-1]  # [batch_size, hidden_dim]
            return last_hidden

        elif self.encoder_type == "transformer":
            # 为输入添加位置编码
            positions = torch.arange(seq_len, device=x.device).unsqueeze(0).expand(batch_size, -1)
            position_embeddings = self.position_encoder(positions)
            x = x + position_embeddings

            # 创建注意力掩码（可选）
            if lengths is not None:
                mask = torch.arange(seq_len, device=x.device).expand(batch_size, seq_len) >= lengths.unsqueeze(1)
            else:
                mask = None

            # 编码
            output = self.encoder(x, src_key_padding_mask=mask if mask is not None else None)

            # 使用序列的平均值作为表示
            if lengths is not None:
                # 创建掩码来计算有效元素的平均值
                mask = torch.arange(seq_len, device=x.device).expand(batch_size, seq_len) < lengths.unsqueeze(1)
                mask = mask.float().unsqueeze(-1)  # [batch_size, seq_len, 1]
                # 计算平均值
                sum_embeddings = torch.sum(output * mask, dim=1)  # [batch_size, hidden_dim]
                sum_mask = torch.sum(mask, dim=1)  # [batch_size, 1]
                return sum_embeddings / sum_mask
            else:
                # 简单平均
                return torch.mean(output, dim=1)  # [batch_size, hidden_dim]


class ActionEncoder(nn.Module):
    """
    动作编码器

    将单个动作（出牌或不出）编码为固定长度的向量表示。
    """

    def __init__(
        self,
        card_vocab_size: int,
        embedding_dim: int,
        max_cards_per_action: int = 20
    ):
        """
        初始化动作编码器

        Args:
            card_vocab_size (int): 牌词汇表大小
            embedding_dim (int): 嵌入维度
            max_cards_per_action (int, optional): 每个动作最大的牌数. Defaults to 20.
        """
        super().__init__()
        self.card_vocab_size = card_vocab_size
        self.embedding_dim = embedding_dim
        self.max_cards_per_action = max_cards_per_action

        # 牌嵌入层
        self.card_embedding = nn.Embedding(card_vocab_size, embedding_dim)

        # 特殊的"不出"动作嵌入
        self.pass_embedding = nn.Parameter(torch.randn(embedding_dim))

        # 牌型嵌入层（单张、对子、三张等）
        self.card_type_embedding = nn.Embedding(len(CardGroupType), embedding_dim)

        # 组合网络
        self.combiner = nn.Sequential(
            nn.Linear(embedding_dim * 2, embedding_dim),
            nn.ReLU(),
            nn.Linear(embedding_dim, embedding_dim)
        )

    def forward(
        self,
        cards: Optional[List[str]] = None,
        card_type: Optional[CardGroupType] = None,
        is_pass: bool = False
    ) -> torch.Tensor:
        """
        编码单个动作

        Args:
            cards (Optional[List[str]], optional): 动作中的牌列表. Defaults to None.
            card_type (Optional[CardGroupType], optional): 牌型. Defaults to None.
            is_pass (bool, optional): 是否为"不出"动作. Defaults to False.

        Returns:
            torch.Tensor: 动作的向量表示，形状为[embedding_dim]
        """
        if is_pass:
            # "不出"动作
            action_embedding = self.pass_embedding
        else:
            # 编码牌
            if cards is None or len(cards) == 0:
                raise ValueError("cards不能为空，除非is_pass为True")

            # 将牌转换为索引
            card_indices = [self._card_to_index(card) for card in cards]
            card_indices_tensor = torch.tensor(card_indices, device=self.pass_embedding.device)

            # 获取牌嵌入
            card_embeddings = self.card_embedding(card_indices_tensor)  # [num_cards, embedding_dim]

            # 平均池化得到动作嵌入
            action_embedding = torch.mean(card_embeddings, dim=0)  # [embedding_dim]

        # 如果提供了牌型，则组合牌型嵌入
        if card_type is not None:
            card_type_idx = torch.tensor(card_type.value - 1, device=self.pass_embedding.device)  # 减1是因为枚举通常从1开始
            card_type_embedding = self.card_type_embedding(card_type_idx)  # [embedding_dim]

            # 组合动作嵌入和牌型嵌入
            combined = torch.cat([action_embedding, card_type_embedding], dim=0)  # [embedding_dim*2]
            action_embedding = self.combiner(combined)  # [embedding_dim]

        return action_embedding

    def _card_to_index(self, card: str) -> int:
        """
        将牌字符串转换为索引

        Args:
            card (str): 牌字符串，例如"♥3"

        Returns:
            int: 牌的索引
        """
        # 这里需要根据实际的牌表示方式实现
        # 简单示例：假设牌的格式为"花色+点数"，如"♥3"
        # 可以根据项目中的Card类实现更精确的转换

        # 简单哈希函数，实际应用中应该使用更好的映射
        return hash(card) % self.card_vocab_size


class StateEncoder(nn.Module):
    """
    状态编码器

    将当前游戏状态编码为固定长度的向量表示。
    """

    def __init__(
        self,
        input_dim: int,
        hidden_dim: int,
        num_layers: int = 2
    ):
        """
        初始化状态编码器

        Args:
            input_dim (int): 输入维度（状态表示的维度）
            hidden_dim (int): 隐藏层维度
            num_layers (int, optional): 层数. Defaults to 2.
        """
        super().__init__()
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim

        # 多层感知机
        layers = []
        layers.append(nn.Linear(input_dim, hidden_dim))
        layers.append(nn.ReLU())

        for _ in range(num_layers - 1):
            layers.append(nn.Linear(hidden_dim, hidden_dim))
            layers.append(nn.ReLU())

        self.encoder = nn.Sequential(*layers)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播

        Args:
            x (torch.Tensor): 输入状态，形状为[batch_size, input_dim]

        Returns:
            torch.Tensor: 编码后的表示，形状为[batch_size, hidden_dim]
        """
        return self.encoder(x)


class DeepBeliefNetwork(nn.Module):
    """
    深度信念网络

    结合历史动作序列和当前状态，预测对手手牌的概率分布。
    """

    def __init__(
        self,
        card_vocab_size: int,
        action_embedding_dim: int,
        history_hidden_dim: int,
        state_input_dim: int,
        state_hidden_dim: int,
        output_dim: int,
        history_encoder_type: str = "lstm",
        history_num_layers: int = 2,
        state_num_layers: int = 2,
        dropout: float = 0.1
    ):
        """
        初始化深度信念网络

        Args:
            card_vocab_size (int): 牌词汇表大小
            action_embedding_dim (int): 动作嵌入维度
            history_hidden_dim (int): 历史编码器隐藏层维度
            state_input_dim (int): 状态输入维度
            state_hidden_dim (int): 状态编码器隐藏层维度
            output_dim (int): 输出维度（通常等于牌的数量）
            history_encoder_type (str, optional): 历史编码器类型. Defaults to "lstm".
            history_num_layers (int, optional): 历史编码器层数. Defaults to 2.
            state_num_layers (int, optional): 状态编码器层数. Defaults to 2.
            dropout (float, optional): Dropout比率. Defaults to 0.1.
        """
        super().__init__()
        self.card_vocab_size = card_vocab_size
        self.output_dim = output_dim

        # 动作编码器
        self.action_encoder = ActionEncoder(
            card_vocab_size=card_vocab_size,
            embedding_dim=action_embedding_dim
        )

        # 历史编码器
        self.history_encoder = HistoryEncoder(
            input_dim=action_embedding_dim,
            hidden_dim=history_hidden_dim,
            num_layers=history_num_layers,
            dropout=dropout,
            encoder_type=history_encoder_type
        )

        # 状态编码器
        self.state_encoder = StateEncoder(
            input_dim=state_input_dim,
            hidden_dim=state_hidden_dim,
            num_layers=state_num_layers
        )

        # 组合网络
        self.combiner = nn.Sequential(
            nn.Linear(history_hidden_dim + state_hidden_dim, history_hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(history_hidden_dim, history_hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout)
        )

        # 输出层
        self.output_layer = nn.Linear(history_hidden_dim // 2, output_dim)

        # 初始化权重
        self._init_weights()

    def _init_weights(self):
        """初始化网络权重"""
        for name, param in self.named_parameters():
            if 'weight' in name:
                nn.init.xavier_uniform_(param)
            elif 'bias' in name:
                nn.init.zeros_(param)

    def forward(
        self,
        action_history: List[Dict[str, Any]],
        state: torch.Tensor,
        action_lengths: Optional[torch.Tensor] = None
    ) -> torch.Tensor:
        """
        前向传播

        Args:
            action_history (List[Dict[str, Any]]): 动作历史，每个元素是一个包含动作信息的字典
            state (torch.Tensor): 当前状态，形状为[batch_size, state_input_dim]
            action_lengths (Optional[torch.Tensor], optional): 动作序列长度. Defaults to None.

        Returns:
            torch.Tensor: 预测的手牌概率分布，形状为[batch_size, output_dim]
        """
        batch_size = state.size(0)

        # 编码动作历史
        action_embeddings = []
        for batch_idx in range(batch_size):
            # 获取当前批次的动作历史
            batch_actions = action_history[batch_idx]
            seq_len = len(batch_actions)

            # 编码每个动作
            batch_embeddings = []
            for i in range(seq_len):
                action = batch_actions[i]
                cards = action.get('cards')
                card_type = action.get('card_type')
                is_pass = action.get('is_pass', False)

                # 编码单个动作
                action_embedding = self.action_encoder(cards, card_type, is_pass)
                batch_embeddings.append(action_embedding)

            # 如果序列为空，添加一个零向量
            if not batch_embeddings:
                batch_embeddings.append(torch.zeros(self.action_encoder.embedding_dim, device=state.device))

            # 堆叠当前批次的动作嵌入
            batch_embeddings = torch.stack(batch_embeddings)  # [seq_len, embedding_dim]
            action_embeddings.append(batch_embeddings)

        # 填充序列到相同长度
        max_seq_len = max(emb.size(0) for emb in action_embeddings)
        padded_embeddings = []
        for emb in action_embeddings:
            seq_len = emb.size(0)
            if seq_len < max_seq_len:
                padding = torch.zeros(max_seq_len - seq_len, emb.size(1), device=emb.device)
                padded_emb = torch.cat([emb, padding], dim=0)
            else:
                padded_emb = emb
            padded_embeddings.append(padded_emb)

        # 堆叠所有批次的动作嵌入
        padded_embeddings = torch.stack(padded_embeddings)  # [batch_size, max_seq_len, embedding_dim]

        # 编码历史
        if action_lengths is None:
            # 如果没有提供序列长度，假设所有序列都是满长度
            history_encoding = self.history_encoder(padded_embeddings)  # [batch_size, history_hidden_dim]
        else:
            history_encoding = self.history_encoder(padded_embeddings, action_lengths)  # [batch_size, history_hidden_dim]

        # 编码状态
        state_encoding = self.state_encoder(state)  # [batch_size, state_hidden_dim]

        # 组合历史和状态编码
        combined = torch.cat([history_encoding, state_encoding], dim=1)  # [batch_size, history_hidden_dim + state_hidden_dim]
        combined_encoding = self.combiner(combined)  # [batch_size, history_hidden_dim // 2]

        # 预测手牌概率分布
        logits = self.output_layer(combined_encoding)  # [batch_size, output_dim]
        probabilities = torch.sigmoid(logits)  # 使用sigmoid得到独立概率

        return probabilities


class DeepBeliefTracker:
    """
    深度信念追踪器

    利用神经网络预测对手手牌分布，基于历史动作和当前状态。
    """

    def __init__(self, player_id, *args, **kwargs):
        """
        初始化深度信念追踪器

        Args:
            player_id (str): 玩家ID
        """
        # 解析关键参数
        self.player_id = player_id
        self.model = kwargs.get('model', None)
        self.card_mapping = kwargs.get('card_mapping', None)
        self.device = kwargs.get('device', 'cpu')
        initial_belief = kwargs.get('initial_belief', None)
        initial_hand_size = kwargs.get('initial_hand_size', None)
        online_updater_params = kwargs.get('online_updater_params', None)

        # 如果提供了模型，则设置为评估模式
        if self.model is not None:
            self.model.to(self.device)
            self.model.eval()

        # 初始化信念状态
        if initial_belief is not None:
            self.belief = initial_belief
        elif self.card_mapping:
            # 根据 card_mapping 初始化默认信念状态
            self.belief = BeliefState(
                player_id=self.player_id,
                card_probabilities={card: 1.0 / len(self.card_mapping) for card in self.card_mapping},
                estimated_hand_length=initial_hand_size,
                source=BeliefSource.INITIAL,
                confidence=0.5
            )
        else:
            # 无初始信念或 card_mapping，暂不初始化
            self.belief = None

        # 初始化动作历史
        self.action_history = []
        self.max_history_length = 20  # 最大历史长度

        # 初始化在线更新器
        self.online_updater = None
        if online_updater_params is not None:
            self.online_updater = OnlineBeliefUpdater(**online_updater_params)
            logger.info("在线信念更新器已初始化")

    def update(
        self,
        opponent_action: Optional[List[Card]],
        public_knowledge: Dict[str, Any],
        current_state: Optional[np.ndarray] = None
    ) -> None:
        """
        根据对手的动作和公共信息更新信念状态

        Args:
            opponent_action (Optional[List[Card]]): 对手的出牌动作，如果是None表示对手选择不出牌
            public_knowledge (Dict[str, Any]): 公共信息，包括已知的公共牌、历史出牌等
            current_state (Optional[np.ndarray], optional): 当前游戏状态的向量表示. Defaults to None.
        """
        current_time = time.time()

        # 记录动作历史
        action_info = self._process_action(opponent_action, public_knowledge)
        self.action_history.append(action_info)

        # 如果对手出牌了，更新手牌数量估计
        if opponent_action is not None and len(opponent_action) > 0:
            if self.belief.estimated_hand_length is not None:
                self.belief.estimated_hand_length -= len(opponent_action)

        # 使用神经网络更新信念状态
        if current_state is not None:
            # 准备输入数据
            state_tensor = torch.tensor(current_state, dtype=torch.float32).unsqueeze(0).to(self.device)
            action_history_batch = [self.action_history]

            # 使用模型预测
            with torch.no_grad():
                probabilities = self.model(action_history_batch, state_tensor)
                probabilities = probabilities.squeeze(0).cpu().numpy()

            # 更新信念状态
            new_probs = {}
            for i, card in enumerate(self.card_mapping):
                if i < len(probabilities):
                    new_probs[card] = float(probabilities[i])

            # 应用概率更新
            self.belief.update_probabilities(new_probs)

            # 更新元数据
            self.belief.source = BeliefSource.NEURAL
            self.belief.confidence = 0.8  # 神经网络预测的置信度较高
        else:
            # 如果没有提供当前状态，使用基础贝叶斯方法更新
            self._update_with_basic_bayesian(opponent_action, public_knowledge)

        # 归一化概率
        self.belief.normalize_probabilities()

        # 更新元数据
        self.belief.last_updated = current_time
        self.belief.version += 1

    def _process_action(
        self,
        opponent_action: Optional[List[Card]],
        public_knowledge: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        处理对手动作，转换为模型可用的格式

        Args:
            opponent_action (Optional[List[Card]]): 对手的出牌动作
            public_knowledge (Dict[str, Any]): 公共信息

        Returns:
            Dict[str, Any]: 处理后的动作信息
        """
        action_info = {}

        if opponent_action is None:
            # 不出牌
            action_info['is_pass'] = True
            action_info['cards'] = None
            action_info['card_type'] = None
        else:
            # 出牌
            action_info['is_pass'] = False
            action_info['cards'] = [str(card) for card in opponent_action]

            # 获取牌型
            try:
                card_group = CardGroup(opponent_action)
                card_type = get_card_group_type(card_group)
                action_info['card_type'] = card_type
            except:
                action_info['card_type'] = None

        # 添加公共信息
        action_info['public_knowledge'] = public_knowledge
        action_info['time'] = time.time()

        return action_info

    def _update_with_basic_bayesian(
        self,
        opponent_action: Optional[List[Card]],
        public_knowledge: Dict[str, Any]
    ) -> None:
        """
        使用基础贝叶斯方法更新信念状态

        Args:
            opponent_action (Optional[List[Card]]): 对手的出牌动作
            public_knowledge (Dict[str, Any]): 公共信息
        """
        new_probs = self.belief.card_probabilities.copy()

        # 如果对手出牌了，更新相应的概率
        if opponent_action is not None and len(opponent_action) > 0:
            # 对手出的牌，概率为0（已经打出）
            action_cards = [str(card) for card in opponent_action]
            for card in action_cards:
                new_probs[card] = 0.0

        # 更新公共信息中的已知牌
        if 'known_cards' in public_knowledge:
            known_cards = public_knowledge['known_cards']
            if isinstance(known_cards, list) and len(known_cards) > 0:
                known_cards_str = [str(card) if not isinstance(card, str) else card
                                  for card in known_cards]
                for card in known_cards_str:
                    new_probs[card] = 0.0  # 已知的公共牌，对手不可能持有

        # 应用概率更新
        self.belief.update_probabilities(new_probs)
        self.belief.source = BeliefSource.INFERENCE

    def get_belief_state(self) -> BeliefState:
        """
        获取当前信念状态

        Returns:
            BeliefState: 信念状态对象
        """
        return self.belief

    def predict(self, state: State, history: Optional[List[State]] = None) -> BeliefState:
        """
        预测当前状态下的信念状态

        Args:
            state: 当前游戏状态
            history: 历史状态列表（可选）

        Returns:
            BeliefState: 预测的信念状态
        """
        # 如果状态可以转换为向量表示
        if hasattr(state, 'to_vector') or hasattr(state, 'to_numpy'):
            # 获取状态的向量表示
            current_state = state.to_vector() if hasattr(state, 'to_vector') else state.to_numpy()

            # 准备输入数据
            state_tensor = torch.tensor(current_state, dtype=torch.float32).unsqueeze(0).to(self.device)
            action_history_batch = [self.action_history]

            # 使用模型预测
            with torch.no_grad():
                probabilities = self.model(action_history_batch, state_tensor)
                probabilities = probabilities.squeeze(0).cpu().numpy()

            # 更新临时信念状态（不修改内部状态）
            temp_belief = self.belief.copy() if hasattr(self.belief, 'copy') else BeliefState(
                player_id=self.player_id,
                card_probabilities=self.belief.card_probabilities.copy(),
                estimated_hand_length=self.belief.estimated_hand_length,
                source=self.belief.source,
                confidence=self.belief.confidence
            )

            # 更新概率
            new_probs = {}
            for i, card in enumerate(self.card_mapping):
                if i < len(probabilities):
                    new_probs[card] = float(probabilities[i])

            # 应用概率更新
            temp_belief.update_probabilities(new_probs)
            temp_belief.normalize_probabilities()

            # 设置元数据
            temp_belief.source = BeliefSource.NEURAL
            temp_belief.confidence = 0.8  # 神经网络预测的置信度较高

            # 应用在线更新（如果有）
            if self.online_updater is not None and history is not None and len(history) > 0:
                # 获取最近的对手动作
                last_opponent_action = None
                public_info = {}
                previous_actions = []

                # 处理历史状态，获取对手动作和公共信息
                for h_state in history:
                    if hasattr(h_state, 'last_move') and h_state.last_move is not None:
                        # 只处理对手的动作，不处理玩家自己的动作
                        if h_state.current_player != self.player_id:
                            if h_state.last_move.cards:
                                last_opponent_action = h_state.last_move.cards

                            # 将动作添加到历史中
                            action_data = self._process_action(last_opponent_action, {})
                            if action_data:
                                previous_actions.append(action_data)

                    # 收集公共信息
                    if hasattr(h_state, 'known_cards'):
                        public_info['known_cards'] = h_state.known_cards

                # 使用在线更新器更新信念
                if last_opponent_action is not None or public_info:
                    updated_belief = self.online_updater.update(
                        temp_belief,
                        last_opponent_action,
                        public_info,
                        previous_actions
                    )
                    logger.debug("应用在线信念更新")
                    return updated_belief

            return temp_belief

        # 如果无法获取状态向量表示，返回当前信念状态
        return self.belief

    def save_model(self, path: str) -> None:
        """
        保存模型

        Args:
            path (str): 保存路径
        """
        torch.save(self.model.state_dict(), path)

    def load_model(self, path: str) -> None:
        """
        加载模型

        Args:
            path (str): 模型路径
        """
        self.model.load_state_dict(torch.load(path, map_location=self.device))
        self.model.eval()

    def calculate_information_value(
        self,
        card: str,
        current_state: Optional[np.ndarray] = None,
        policy_network: Optional[Any] = None,
        method: str = 'combined'
    ) -> float:
        """
        计算获取特定牌信息的价值

        Args:
            card (str): 牌的表示
            current_state (Optional[np.ndarray], optional): 当前游戏状态的向量表示. Defaults to None.
            policy_network (Optional[Any], optional): 策略网络，用于评估动作分布变化. Defaults to None.
            method (str, optional): 计算方法，可选'basic'、'entropy'、'action'或'combined'. Defaults to 'combined'.

        Returns:
            float: 信息价值，越高表示获取该信息越有价值
        """
        # 如果提供了策略网络，创建策略函数
        policy_function = None
        if policy_network is not None and current_state is not None:
            policy_function = lambda belief, state: self._get_action_distribution(belief, state, policy_network)

        # 使用信息价值计算器计算信息价值
        return calculate_information_value(
            self.belief, card, current_state, policy_function, method
        )

    def calculate_information_values(
        self,
        current_state: Optional[np.ndarray] = None,
        policy_network: Optional[Any] = None,
        method: str = 'combined',
        top_n: Optional[int] = None
    ) -> Dict[str, float]:
        """
        计算所有牌的信息价值

        Args:
            current_state (Optional[np.ndarray], optional): 当前游戏状态的向量表示. Defaults to None.
            policy_network (Optional[Any], optional): 策略网络，用于评估动作分布变化. Defaults to None.
            method (str, optional): 计算方法，可选'basic'、'entropy'、'action'或'combined'. Defaults to 'combined'.
            top_n (Optional[int], optional): 返回信息价值最高的前N张牌. Defaults to None.

        Returns:
            Dict[str, float]: 牌到信息价值的映射
        """
        # 如果提供了策略网络，创建策略函数
        policy_function = None
        if policy_network is not None and current_state is not None:
            policy_function = lambda belief, state: self._get_action_distribution(belief, state, policy_network)

        # 使用信息价值计算器计算所有牌的信息价值
        return calculate_all_cards_information_value(
            self.belief, self.card_mapping, current_state, policy_function, method, top_n
        )

    def _get_action_distribution(
        self,
        belief: BeliefState,
        current_state: np.ndarray,
        policy_network: Any
    ) -> np.ndarray:
        """
        获取在给定信念状态和当前游戏状态下的动作分布

        Args:
            belief (BeliefState): 信念状态
            current_state (np.ndarray): 当前游戏状态的向量表示
            policy_network (Any): 策略网络

        Returns:
            np.ndarray: 动作概率分布
        """
        # 将信念状态转换为向量表示
        belief_vector = belief.to_numpy()

        # 将当前状态和信念状态组合
        combined_state = np.concatenate([current_state, belief_vector])

        # 使用策略网络预测动作分布
        try:
            # 转换为张量
            state_tensor = torch.tensor(combined_state, dtype=torch.float32).unsqueeze(0).to(self.device)

            # 预测动作分布
            with torch.no_grad():
                action_probs = policy_network(state_tensor)

            # 转换为NumPy数组
            return action_probs.squeeze(0).cpu().numpy()
        except Exception as e:
            logger.warning(f"获取动作分布时出错: {e}")
            # 返回均匀分布
            return np.ones(policy_network.output_dim) / policy_network.output_dim
