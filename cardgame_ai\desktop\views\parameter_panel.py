#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
参数配置组件

支持配置训练参数，根据选择的游戏动态加载相应的参数配置选项。
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QComboBox,
    QPushButton, QSpinBox, QDoubleSpinBox, QCheckBox,
    QGroupBox, QFormLayout, QScrollArea, QFrame,
    QSpacerItem, QSizePolicy, QTabWidget, QLineEdit
)
from PySide6.QtCore import Qt, Signal, Slot, QSize
from PySide6.QtGui import QFont

logger = logging.getLogger(__name__)


class ParameterPanel(QWidget):
    """参数配置组件类"""

    # 参数变化信号
    parameter_changed = Signal(str, object)

    def __init__(self, config, parent=None):
        """
        初始化参数配置组件

        Args:
            config: 客户端配置
            parent: 父部件
        """
        super().__init__(parent)

        # 保存配置
        self.config = config

        # 设置对象名称
        self.setObjectName("parameterPanel")

        # 参数控件字典
        self.parameter_widgets = {}

        # 当前游戏ID
        self.current_game_id = ""

        # 当前算法
        self.current_algorithm = ""

        # 初始化UI
        self.setup_ui()

        logger.info("参数配置组件初始化完成")

    def setup_ui(self):
        """设置UI布局"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(10)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)

        # 创建滚动内容部件
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setContentsMargins(0, 0, 0, 0)
        scroll_layout.setSpacing(10)

        # 创建模型选择区
        self.model_group = QGroupBox("模型选择")
        model_layout = QFormLayout(self.model_group)

        # 创建算法选择下拉框
        self.algorithm_combo = QComboBox()
        self.algorithm_combo.addItem("DQN")
        self.algorithm_combo.addItem("PPO")
        self.algorithm_combo.addItem("MuZero")
        self.algorithm_combo.addItem("EfficientZero")
        self.algorithm_combo.currentTextChanged.connect(self.on_algorithm_changed)
        model_layout.addRow("算法:", self.algorithm_combo)
        self.parameter_widgets["algorithm"] = self.algorithm_combo

        # 模型大小已移除，使用默认中型模型

        # 添加模型选择区到滚动布局
        scroll_layout.addWidget(self.model_group)

        # 创建训练参数区
        self.params_group = QGroupBox("训练参数")
        params_layout = QFormLayout(self.params_group)

        # 创建学习率下拉框
        self.learning_rate_combo = QComboBox()
        self.learning_rate_combo.addItem("0.001")
        self.learning_rate_combo.addItem("0.0005")
        self.learning_rate_combo.addItem("0.0001")
        self.learning_rate_combo.currentTextChanged.connect(
            lambda text: self.parameter_changed.emit("learning_rate", float(text))
        )
        params_layout.addRow("学习率:", self.learning_rate_combo)
        self.parameter_widgets["learning_rate"] = self.learning_rate_combo

        # 创建批量大小下拉框
        self.batch_size_combo = QComboBox()
        self.batch_size_combo.addItem("32")
        self.batch_size_combo.addItem("64")
        self.batch_size_combo.addItem("128")
        self.batch_size_combo.addItem("256")
        self.batch_size_combo.currentTextChanged.connect(
            lambda text: self.parameter_changed.emit("batch_size", int(text))
        )
        params_layout.addRow("批量大小:", self.batch_size_combo)
        self.parameter_widgets["batch_size"] = self.batch_size_combo

        # 创建训练轮数下拉框
        self.epochs_combo = QComboBox()
        self.epochs_combo.addItem("10")
        self.epochs_combo.addItem("50")
        self.epochs_combo.addItem("100")
        self.epochs_combo.addItem("500")
        self.epochs_combo.addItem("1000")
        self.epochs_combo.currentTextChanged.connect(
            lambda text: self.parameter_changed.emit("epochs", int(text))
        )
        params_layout.addRow("训练轮数:", self.epochs_combo)
        self.parameter_widgets["epochs"] = self.epochs_combo

        # 添加训练参数区到滚动布局
        scroll_layout.addWidget(self.params_group)

        # 创建高级参数区
        self.advanced_group = QGroupBox("高级参数")
        advanced_layout = QFormLayout(self.advanced_group)

        # 创建折扣因子下拉框
        self.gamma_combo = QComboBox()
        self.gamma_combo.addItem("0.99")
        self.gamma_combo.addItem("0.95")
        self.gamma_combo.addItem("0.9")
        self.gamma_combo.currentTextChanged.connect(
            lambda text: self.parameter_changed.emit("gamma", float(text))
        )
        advanced_layout.addRow("折扣因子:", self.gamma_combo)
        self.parameter_widgets["gamma"] = self.gamma_combo

        # 创建探索率下拉框
        self.epsilon_combo = QComboBox()
        self.epsilon_combo.addItem("0.1")
        self.epsilon_combo.addItem("0.05")
        self.epsilon_combo.addItem("0.01")
        self.epsilon_combo.currentTextChanged.connect(
            lambda text: self.parameter_changed.emit("epsilon", float(text))
        )
        advanced_layout.addRow("探索率:", self.epsilon_combo)
        self.parameter_widgets["epsilon"] = self.epsilon_combo

        # 创建目标网络更新频率
        self.target_update_spin = QSpinBox()
        self.target_update_spin.setRange(10, 10000)
        self.target_update_spin.setSingleStep(10)
        self.target_update_spin.setValue(100)
        self.target_update_spin.valueChanged.connect(
            lambda value: self.parameter_changed.emit("target_update", value)
        )
        advanced_layout.addRow("目标网络更新频率:", self.target_update_spin)
        self.parameter_widgets["target_update"] = self.target_update_spin

        # 添加高级参数区到滚动布局
        scroll_layout.addWidget(self.advanced_group)

        # 创建游戏特定参数区
        self.game_params_group = QGroupBox("游戏特定参数")
        self.game_params_layout = QFormLayout(self.game_params_group)

        # 游戏特定参数将在设置游戏时动态添加

        # 添加游戏特定参数区到滚动布局
        scroll_layout.addWidget(self.game_params_group)

        # 添加弹性空间
        scroll_layout.addSpacerItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))

        # 设置滚动区域的部件
        scroll_area.setWidget(scroll_content)

        # 添加滚动区域到主布局
        main_layout.addWidget(scroll_area)

        # 创建按钮区域
        button_layout = QHBoxLayout()

        # 创建重置按钮
        reset_button = QPushButton("重置参数")
        reset_button.clicked.connect(self.reset_parameters)
        button_layout.addWidget(reset_button)

        # 创建保存按钮
        save_button = QPushButton("保存配置")
        save_button.clicked.connect(self.save_parameters)
        button_layout.addWidget(save_button)

        # 添加按钮区域到主布局
        main_layout.addLayout(button_layout)

        logger.info("参数配置组件UI布局设置完成")

    @Slot(str)
    def on_algorithm_changed(self, algorithm):
        """
        算法变化处理

        Args:
            algorithm (str): 算法名称
        """
        self.current_algorithm = algorithm

        # 发送参数变化信号
        self.parameter_changed.emit("algorithm", algorithm)

        # 根据算法更新参数
        self.update_parameters_for_algorithm(algorithm)

        logger.info(f"选择算法：{algorithm}")

    def update_parameters_for_algorithm(self, algorithm):
        """
        更新算法特定参数

        Args:
            algorithm (str): 算法名称
        """
        # 根据算法更新参数
        if algorithm == "DQN":
            # DQN特定参数
            self.gamma_combo.setCurrentText("0.99")
            self.epsilon_combo.setCurrentText("0.1")
            self.target_update_spin.setValue(100)
        elif algorithm == "PPO":
            # PPO特定参数
            self.gamma_combo.setCurrentText("0.99")
            self.epsilon_combo.setCurrentText("0.05")
            self.target_update_spin.setValue(10)
        elif algorithm == "MuZero":
            # MuZero特定参数
            self.gamma_combo.setCurrentText("0.95")
            self.epsilon_combo.setCurrentText("0.05")
            self.target_update_spin.setValue(50)
        elif algorithm == "EfficientZero":
            # EfficientZero特定参数
            self.gamma_combo.setCurrentText("0.95")
            self.epsilon_combo.setCurrentText("0.01")
            self.target_update_spin.setValue(50)

        logger.info(f"更新算法参数：{algorithm}")

    def set_game(self, game_id, game_info):
        """
        设置游戏

        Args:
            game_id (str): 游戏ID
            game_info (Dict[str, Any]): 游戏信息
        """
        # 保存当前游戏ID
        self.current_game_id = game_id

        # 清空游戏特定参数
        self.clear_game_parameters()

        # 添加游戏特定参数
        self.add_game_parameters(game_info)

        logger.info(f"设置游戏参数：{game_id}")

    def clear_game_parameters(self):
        """清空游戏特定参数"""
        # 清空游戏特定参数布局
        while self.game_params_layout.count() > 0:
            item = self.game_params_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()

        # 移除游戏特定参数控件
        for key in list(self.parameter_widgets.keys()):
            if key.startswith("game_"):
                self.parameter_widgets.pop(key)

        logger.info("清空游戏特定参数")

    def add_game_parameters(self, game_info):
        """
        添加游戏特定参数

        Args:
            game_info (Dict[str, Any]): 游戏信息
        """
        # 获取游戏参数
        params = game_info.get("parameters", {})

        # 添加游戏特定参数
        for key, value in params.items():
            param_key = f"game_{key}"

            if isinstance(value, int):
                # 整数参数
                spin = QSpinBox()
                spin.setRange(0, 10000)
                spin.setValue(value)
                spin.valueChanged.connect(
                    lambda v, k=param_key: self.parameter_changed.emit(k, v)
                )
                self.game_params_layout.addRow(f"{key}:", spin)
                self.parameter_widgets[param_key] = spin
            elif isinstance(value, float):
                # 浮点数参数
                spin = QDoubleSpinBox()
                spin.setRange(0.0, 1.0)
                spin.setDecimals(4)
                spin.setSingleStep(0.01)
                spin.setValue(value)
                spin.valueChanged.connect(
                    lambda v, k=param_key: self.parameter_changed.emit(k, v)
                )
                self.game_params_layout.addRow(f"{key}:", spin)
                self.parameter_widgets[param_key] = spin
            elif isinstance(value, bool):
                # 布尔参数
                check = QCheckBox()
                check.setChecked(value)
                check.stateChanged.connect(
                    lambda v, k=param_key: self.parameter_changed.emit(k, bool(v))
                )
                self.game_params_layout.addRow(f"{key}:", check)
                self.parameter_widgets[param_key] = check
            elif isinstance(value, list):
                # 列表参数
                if len(value) == 2 and all(isinstance(v, int) for v in value):
                    # 二维大小参数
                    layout = QHBoxLayout()

                    spin1 = QSpinBox()
                    spin1.setRange(1, 100)
                    spin1.setValue(value[0])
                    layout.addWidget(spin1)

                    layout.addWidget(QLabel("x"))

                    spin2 = QSpinBox()
                    spin2.setRange(1, 100)
                    spin2.setValue(value[1])
                    layout.addWidget(spin2)

                    self.game_params_layout.addRow(f"{key}:", layout)
                    self.parameter_widgets[f"{param_key}_0"] = spin1
                    self.parameter_widgets[f"{param_key}_1"] = spin2

        logger.info(f"添加游戏特定参数：{len(params)}个")

    def reset_parameters(self):
        """重置参数"""
        # 重置算法
        self.algorithm_combo.setCurrentIndex(0)

        # 模型大小已移除，使用默认中型模型

        # 重置学习率
        self.learning_rate_combo.setCurrentIndex(0)  # 0.001

        # 重置批量大小
        self.batch_size_combo.setCurrentIndex(1)  # 64

        # 重置训练轮数
        self.epochs_combo.setCurrentIndex(2)  # 100

        # 重置折扣因子
        self.gamma_combo.setCurrentIndex(0)  # 0.99

        # 重置探索率
        self.epsilon_combo.setCurrentIndex(0)  # 0.1

        # 重置目标网络更新频率
        self.target_update_spin.setValue(100)

        # 重置游戏特定参数
        if self.current_game_id:
            # 获取游戏信息
            game_info = self.get_game_info(self.current_game_id)
            if game_info:
                # 重新设置游戏参数
                self.set_game(self.current_game_id, game_info)

        logger.info("重置参数")

    def save_parameters(self):
        """保存参数配置"""
        try:
            # 获取参数
            params = self.get_parameters()

            # 保存参数
            params_file = os.path.join(
                self.config.get("paths.configs", "configs"),
                f"params_{self.current_game_id}_{self.current_algorithm}.json"
            )

            # 确保目录存在
            os.makedirs(os.path.dirname(params_file), exist_ok=True)

            # 保存参数
            with open(params_file, "w", encoding="utf-8") as f:
                json.dump(params, f, indent=4, ensure_ascii=False)

            logger.info(f"保存参数配置：{params_file}")
        except Exception as e:
            logger.error(f"保存参数配置失败：{e}")

    def get_parameters(self) -> Dict[str, Any]:
        """
        获取参数

        Returns:
            Dict[str, Any]: 参数字典
        """
        # 参数字典
        params = {}

        # 获取基本参数
        params["algorithm"] = self.algorithm_combo.currentText()
        params["model_size"] = "中型"  # 默认使用中型模型
        params["learning_rate"] = float(self.learning_rate_combo.currentText())
        params["batch_size"] = int(self.batch_size_combo.currentText())
        params["epochs"] = int(self.epochs_combo.currentText())
        params["gamma"] = float(self.gamma_combo.currentText())
        params["epsilon"] = float(self.epsilon_combo.currentText())
        params["target_update"] = self.target_update_spin.value()

        # 获取游戏特定参数
        for key, widget in self.parameter_widgets.items():
            if key.startswith("game_"):
                if isinstance(widget, QSpinBox):
                    params[key] = widget.value()
                elif isinstance(widget, QDoubleSpinBox):
                    params[key] = widget.value()
                elif isinstance(widget, QCheckBox):
                    params[key] = widget.isChecked()

        return params

    def get_game_info(self, game_id: str) -> Dict[str, Any]:
        """
        获取游戏信息

        Args:
            game_id (str): 游戏ID

        Returns:
            Dict[str, Any]: 游戏信息
        """
        # 游戏配置文件路径
        games_config_path = os.path.join(
            self.config.get("paths.configs", "configs"),
            "games.json"
        )

        # 检查配置文件是否存在
        if not os.path.exists(games_config_path):
            logger.warning(f"游戏配置文件不存在：{games_config_path}")
            return {}

        try:
            # 加载配置文件
            with open(games_config_path, "r", encoding="utf-8") as f:
                games_config = json.load(f)

            # 查找游戏
            for game in games_config:
                if game.get("id") == game_id:
                    return game

            logger.warning(f"游戏不存在：{game_id}")
            return {}
        except Exception as e:
            logger.error(f"加载游戏配置文件失败：{e}")
            return {}
