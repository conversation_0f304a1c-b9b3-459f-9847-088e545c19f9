"""
可视化组件模块

提供用于可视化AI决策过程的UI组件。
"""

import logging
from typing import Dict, List, Any, Optional, Union, Tuple
import numpy as np
import time

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QProgressBar,
    QGroupBox, QSizePolicy, QFrame, QScrollArea
)
from PySide6.QtCore import Qt, Signal, Slot, QSize
from PySide6.QtGui import QFont, QColor, QPainter, QPen, QBrush, QLinearGradient, QGradient

# 尝试导入图表库
try:
    from PySide6.QtCharts import QChart, QChartView, QLineSeries, QPieSeries, QValueAxis, QBarSeries, QBarSet
    QTCHARTS_AVAILABLE = True
except ImportError:
    QTCHARTS_AVAILABLE = False

    # 尝试导入matplotlib作为备选
    try:
        import matplotlib
        matplotlib.use('Qt5Agg')
        from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
        from matplotlib.figure import Figure
        import matplotlib.pyplot as plt
        MATPLOTLIB_AVAILABLE = True
    except ImportError:
        MATPLOTLIB_AVAILABLE = False

logger = logging.getLogger(__name__)


class ComplexityScoreWidget(QWidget):
    """复杂度评分组件"""

    def __init__(self, parent=None):
        """初始化复杂度评分组件"""
        super().__init__(parent)

        # 设置对象名称
        self.setObjectName("complexityScoreWidget")

        # 当前复杂度评分
        self.complexity_score = 0.0

        # 设置最小大小
        self.setMinimumSize(200, 100)

        # 初始化UI
        self.setup_ui()

    def setup_ui(self):
        """设置UI"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(5)

        # 创建标题标签
        title_label = QLabel("复杂度评分")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-weight: bold;")
        main_layout.addWidget(title_label)

        # 创建进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("%p%")
        self.progress_bar.setMinimumHeight(20)

        # 设置进度条样式
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #ccc;
                border-radius: 5px;
                text-align: center;
                background-color: #f5f5f5;
            }
            QProgressBar::chunk {
                background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #5DADE2, stop:1 #3498DB);
                border-radius: 5px;
            }
        """)

        main_layout.addWidget(self.progress_bar)

        # 创建描述标签
        self.description_label = QLabel("低复杂度")
        self.description_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(self.description_label)

    def set_complexity_score(self, score: float):
        """
        设置复杂度评分

        Args:
            score: 复杂度评分 (0.0-1.0)
        """
        # 保存评分
        self.complexity_score = score

        # 更新进度条
        value = int(score * 100)
        self.progress_bar.setValue(value)

        # 更新描述
        if score < 0.3:
            description = "低复杂度"
            self.progress_bar.setStyleSheet("""
                QProgressBar {
                    border: 1px solid #ccc;
                    border-radius: 5px;
                    text-align: center;
                    background-color: #f5f5f5;
                }
                QProgressBar::chunk {
                    background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #2ECC71, stop:1 #27AE60);
                    border-radius: 5px;
                }
            """)
        elif score < 0.7:
            description = "中复杂度"
            self.progress_bar.setStyleSheet("""
                QProgressBar {
                    border: 1px solid #ccc;
                    border-radius: 5px;
                    text-align: center;
                    background-color: #f5f5f5;
                }
                QProgressBar::chunk {
                    background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #F4D03F, stop:1 #F39C12);
                    border-radius: 5px;
                }
            """)
        else:
            description = "高复杂度"
            self.progress_bar.setStyleSheet("""
                QProgressBar {
                    border: 1px solid #ccc;
                    border-radius: 5px;
                    text-align: center;
                    background-color: #f5f5f5;
                }
                QProgressBar::chunk {
                    background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #E74C3C, stop:1 #C0392B);
                    border-radius: 5px;
                }
            """)

        self.description_label.setText(description)


class ComponentUsageWidget(QWidget):
    """组件调用统计组件"""

    def __init__(self, parent=None):
        """初始化组件调用统计组件"""
        super().__init__(parent)

        # 设置对象名称
        self.setObjectName("componentUsageWidget")

        # 组件调用统计
        self.component_calls = {
            "mcts": 0,
            "network": 0,
            "rule": 0,
            "hybrid": 0
        }

        # 设置最小大小
        self.setMinimumSize(200, 200)

        # 初始化UI
        self.setup_ui()

    def setup_ui(self):
        """设置UI"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(5)

        # 创建标题标签
        title_label = QLabel("组件调用统计")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-weight: bold;")
        main_layout.addWidget(title_label)

        # 创建图表区域
        if QTCHARTS_AVAILABLE:
            # 使用QtCharts创建饼图
            self.chart_view = self.create_qtcharts_view()
            main_layout.addWidget(self.chart_view)
        else:
            # 创建简单文本显示
            self.stats_label = QLabel()
            self.stats_label.setAlignment(Qt.AlignCenter)
            self.stats_label.setWordWrap(True)
            self.stats_label.setText("MCTS: 0\nNetwork: 0\nRule: 0\nHybrid: 0")
            main_layout.addWidget(self.stats_label)

    def create_qtcharts_view(self):
        """创建QtCharts图表视图"""
        # 创建饼图系列
        self.pie_series = QPieSeries()
        self.pie_series.setHoleSize(0.35)  # 设置中心空洞大小

        # 添加初始数据
        self.mcts_slice = self.pie_series.append("MCTS", 0)
        self.mcts_slice.setBrush(QColor("#3498DB"))

        self.network_slice = self.pie_series.append("Network", 0)
        self.network_slice.setBrush(QColor("#2ECC71"))

        self.rule_slice = self.pie_series.append("Rule", 0)
        self.rule_slice.setBrush(QColor("#F39C12"))

        self.hybrid_slice = self.pie_series.append("Hybrid", 0)
        self.hybrid_slice.setBrush(QColor("#9B59B6"))

        # 创建图表
        chart = QChart()
        chart.addSeries(self.pie_series)
        chart.setTitle("组件调用分布")
        chart.legend().setVisible(True)
        chart.legend().setAlignment(Qt.AlignBottom)

        # 设置图表字体
        chart_font = QFont()
        chart_font.setPointSize(9)
        chart.setTitleFont(chart_font)

        # 创建图表视图
        chart_view = QChartView(chart)
        chart_view.setRenderHint(QPainter.Antialiasing)

        return chart_view

    def update_component_usage(self, component_calls: Dict[str, int]):
        """
        更新组件调用统计

        Args:
            component_calls: 组件调用统计
        """
        # 保存组件调用统计
        self.component_calls = component_calls

        # 计算总调用次数
        total_calls = sum(component_calls.values())

        if QTCHARTS_AVAILABLE:
            # 更新饼图数据
            if total_calls > 0:
                # 有调用数据时更新饼图
                mcts_value = component_calls.get("mcts", 0)
                network_value = component_calls.get("network", 0)
                rule_value = component_calls.get("rule", 0)
                hybrid_value = component_calls.get("hybrid", 0)

                self.mcts_slice.setValue(mcts_value)
                self.network_slice.setValue(network_value)
                self.rule_slice.setValue(rule_value)
                self.hybrid_slice.setValue(hybrid_value)

                # 更新标签
                self.mcts_slice.setLabel(f"MCTS: {mcts_value} ({mcts_value/total_calls:.1%})")
                self.network_slice.setLabel(f"Network: {network_value} ({network_value/total_calls:.1%})")
                self.rule_slice.setLabel(f"Rule: {rule_value} ({rule_value/total_calls:.1%})")
                self.hybrid_slice.setLabel(f"Hybrid: {hybrid_value} ({hybrid_value/total_calls:.1%})")
            else:
                # 无调用数据时重置饼图
                self.mcts_slice.setValue(1)
                self.network_slice.setValue(1)
                self.rule_slice.setValue(1)
                self.hybrid_slice.setValue(1)

                # 更新标签
                self.mcts_slice.setLabel("MCTS: 0")
                self.network_slice.setLabel("Network: 0")
                self.rule_slice.setLabel("Rule: 0")
                self.hybrid_slice.setLabel("Hybrid: 0")
        else:
            # 更新文本显示
            mcts_value = component_calls.get("mcts", 0)
            network_value = component_calls.get("network", 0)
            rule_value = component_calls.get("rule", 0)
            hybrid_value = component_calls.get("hybrid", 0)

            if total_calls > 0:
                text = (
                    f"MCTS: {mcts_value} ({mcts_value/total_calls:.1%})\n"
                    f"Network: {network_value} ({network_value/total_calls:.1%})\n"
                    f"Rule: {rule_value} ({rule_value/total_calls:.1%})\n"
                    f"Hybrid: {hybrid_value} ({hybrid_value/total_calls:.1%})"
                )
            else:
                text = "MCTS: 0\nNetwork: 0\nRule: 0\nHybrid: 0"

            self.stats_label.setText(text)


class ValueChangeWidget(QWidget):
    """价值变化组件"""

    def __init__(self, parent=None):
        """初始化价值变化组件"""
        super().__init__(parent)

        # 设置对象名称
        self.setObjectName("valueChangeWidget")

        # 价值历史
        self.value_history = []
        self.max_history_size = 100

        # 设置最小大小
        self.setMinimumSize(200, 150)

        # 初始化UI
        self.setup_ui()

    def setup_ui(self):
        """设置UI"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(5)

        # 创建标题标签
        title_label = QLabel("价值变化")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-weight: bold;")
        main_layout.addWidget(title_label)

        # 创建图表区域
        if QTCHARTS_AVAILABLE:
            # 使用QtCharts创建折线图
            self.chart_view = self.create_qtcharts_view()
            main_layout.addWidget(self.chart_view)
        else:
            # 创建简单文本显示
            self.value_label = QLabel("当前价值: 0.0")
            self.value_label.setAlignment(Qt.AlignCenter)
            main_layout.addWidget(self.value_label)

            # 创建简单图形显示
            self.graph_widget = QWidget()
            self.graph_widget.setMinimumHeight(80)
            self.graph_widget.paintEvent = self.paint_simple_graph
            main_layout.addWidget(self.graph_widget)

    def create_qtcharts_view(self):
        """创建QtCharts图表视图"""
        # 创建折线系列
        self.line_series = QLineSeries()
        self.line_series.setName("价值")

        # 创建图表
        chart = QChart()
        chart.addSeries(self.line_series)
        chart.setTitle("价值变化趋势")

        # 创建坐标轴
        self.axis_x = QValueAxis()
        self.axis_x.setTitleText("决策序号")
        self.axis_x.setRange(0, 10)
        self.axis_x.setTickCount(6)
        self.axis_x.setLabelFormat("%d")

        self.axis_y = QValueAxis()
        self.axis_y.setTitleText("价值")
        self.axis_y.setRange(-1, 1)
        self.axis_y.setTickCount(5)
        self.axis_y.setLabelFormat("%.1f")

        # 添加坐标轴到图表
        chart.addAxis(self.axis_x, Qt.AlignBottom)
        chart.addAxis(self.axis_y, Qt.AlignLeft)

        # 将系列附加到坐标轴
        self.line_series.attachAxis(self.axis_x)
        self.line_series.attachAxis(self.axis_y)

        # 设置图表字体
        chart_font = QFont()
        chart_font.setPointSize(9)
        chart.setTitleFont(chart_font)

        # 创建图表视图
        chart_view = QChartView(chart)
        chart_view.setRenderHint(QPainter.Antialiasing)

        return chart_view

    def paint_simple_graph(self, event):
        """绘制简单图形"""
        if not self.value_history:
            return

        # 创建画家
        painter = QPainter(self.graph_widget)
        painter.setRenderHint(QPainter.Antialiasing)

        # 获取绘制区域
        rect = self.graph_widget.rect()

        # 设置画笔
        pen = QPen(QColor("#3498DB"))
        pen.setWidth(2)
        painter.setPen(pen)

        # 计算坐标
        width = rect.width()
        height = rect.height()

        # 绘制中心线
        center_y = height / 2
        painter.drawLine(0, center_y, width, center_y)

        # 绘制价值曲线
        if len(self.value_history) > 1:
            # 计算点坐标
            points = []
            for i, value in enumerate(self.value_history):
                x = i * width / (len(self.value_history) - 1)
                # 将价值从[-1, 1]映射到[height, 0]
                y = center_y - value * center_y
                points.append((x, y))

            # 绘制线段
            for i in range(len(points) - 1):
                x1, y1 = points[i]
                x2, y2 = points[i + 1]
                painter.drawLine(int(x1), int(y1), int(x2), int(y2))

    def update_value_history(self, value_history: List[float]):
        """
        更新价值历史

        Args:
            value_history: 价值历史
        """
        # 保存价值历史
        self.value_history = value_history[-self.max_history_size:] if len(value_history) > self.max_history_size else value_history

        if QTCHARTS_AVAILABLE:
            # 更新折线图数据
            self.line_series.clear()

            if self.value_history:
                # 添加数据点
                for i, value in enumerate(self.value_history):
                    self.line_series.append(i, value)

                # 更新X轴范围
                self.axis_x.setRange(0, len(self.value_history) - 1)

                # 更新Y轴范围
                min_value = min(self.value_history)
                max_value = max(self.value_history)
                range_value = max_value - min_value

                if range_value > 0:
                    self.axis_y.setRange(
                        min_value - range_value * 0.1,
                        max_value + range_value * 0.1
                    )
                else:
                    # 如果所有值相同，设置一个合理的范围
                    self.axis_y.setRange(-1, 1)
        else:
            # 更新文本显示
            if self.value_history:
                current_value = self.value_history[-1]
                self.value_label.setText(f"当前价值: {current_value:.2f}")
            else:
                self.value_label.setText("当前价值: 0.0")

            # 更新图形
            self.graph_widget.update()


class BeliefDistributionWidget(QWidget):
    """
    信念分布可视化组件
    
    用于可视化对手手牌的概率分布。
    """
    
    def __init__(self, parent=None):
        """初始化信念分布可视化组件"""
        super().__init__(parent)
        
        # 设置对象名称
        self.setObjectName("beliefDistributionWidget")
        
        # 信念分布数据
        self.belief_data = {}  # 格式: {牌ID: 概率}
        self.card_names = {}   # 格式: {牌ID: 牌名}
        self.highest_belief_cards = []  # 概率最高的几张牌
        
        # 设置最小大小
        self.setMinimumSize(300, 200)
        
        # 初始化UI
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 创建标题标签
        title_label = QLabel("对手手牌信念分布")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        main_layout.addWidget(title_label)
        
        # 创建分割线
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        main_layout.addWidget(line)
        
        # 创建图表区域
        if QTCHARTS_AVAILABLE:
            # 使用QtCharts创建条形图
            self.chart_view = self.create_qtcharts_view()
            main_layout.addWidget(self.chart_view)
        else:
            # 创建滚动区域来显示信念分布
            scroll_area = QScrollArea()
            scroll_area.setWidgetResizable(True)
            
            # 创建容器部件
            content_widget = QWidget()
            self.belief_layout = QVBoxLayout(content_widget)
            self.belief_layout.setContentsMargins(5, 5, 5, 5)
            self.belief_layout.setSpacing(5)
            
            # 创建空白占位符
            self.placeholder_label = QLabel("暂无信念分布数据")
            self.placeholder_label.setAlignment(Qt.AlignCenter)
            self.belief_layout.addWidget(self.placeholder_label)
            
            scroll_area.setWidget(content_widget)
            main_layout.addWidget(scroll_area)
            
        # 创建最高概率区域
        high_prob_group = QGroupBox("最可能的手牌")
        high_prob_layout = QVBoxLayout(high_prob_group)
        high_prob_layout.setContentsMargins(5, 5, 5, 5)
        high_prob_layout.setSpacing(5)
        
        self.high_prob_label = QLabel("暂无数据")
        self.high_prob_label.setAlignment(Qt.AlignCenter)
        self.high_prob_label.setWordWrap(True)
        high_prob_layout.addWidget(self.high_prob_label)
        
        main_layout.addWidget(high_prob_group)
    
    def create_qtcharts_view(self):
        """创建QtCharts图表视图"""
        # 创建条形图系列
        self.bar_series = QBarSeries()
        
        # 创建空的数据集
        self.bar_set = QBarSet("持有概率")
        self.bar_series.append(self.bar_set)
        
        # 创建图表
        chart = QChart()
        chart.addSeries(self.bar_series)
        chart.setTitle("对手手牌概率分布")
        chart.setAnimationOptions(QChart.SeriesAnimations)
        
        # 创建X轴 (牌名)
        self.axis_x = QValueAxis()
        self.axis_x.setTickCount(1)  # 暂时不显示刻度
        chart.addAxis(self.axis_x, Qt.AlignBottom)
        self.bar_series.attachAxis(self.axis_x)
        
        # 创建Y轴 (概率)
        self.axis_y = QValueAxis()
        self.axis_y.setRange(0, 1)
        self.axis_y.setLabelFormat("%.2f")
        self.axis_y.setTickCount(6)
        self.axis_y.setTitleText("概率")
        chart.addAxis(self.axis_y, Qt.AlignLeft)
        self.bar_series.attachAxis(self.axis_y)
        
        # 设置图表字体
        chart_font = QFont()
        chart_font.setPointSize(9)
        chart.setTitleFont(chart_font)
        
        # 创建图表视图
        chart_view = QChartView(chart)
        chart_view.setRenderHint(QPainter.Antialiasing)
        
        return chart_view
    
    def update_belief_distribution(self, belief_distribution: Dict[str, float], card_names: Dict[str, str] = None):
        """
        更新信念分布
        
        Args:
            belief_distribution: 信念分布数据 {牌ID: 概率}
            card_names: 牌ID到牌名的映射 {牌ID: 牌名}
        """
        # 保存信念分布数据
        self.belief_data = belief_distribution
        
        # 保存牌名映射
        if card_names:
            self.card_names = card_names
        
        # 如果没有提供牌名映射，则使用牌ID作为牌名
        displayed_card_names = {card_id: self.card_names.get(card_id, str(card_id)) 
                               for card_id in belief_distribution.keys()}
        
        # 获取概率最高的N张牌
        top_n = 10
        sorted_cards = sorted(belief_distribution.items(), key=lambda x: x[1], reverse=True)
        self.highest_belief_cards = sorted_cards[:top_n]
        
        if QTCHARTS_AVAILABLE:
            # 使用QtCharts更新条形图
            self.bar_set.remove(0, self.bar_set.count())  # 清除现有数据
            
            # 添加新数据，只显示概率最高的N张牌
            for card_id, prob in self.highest_belief_cards:
                self.bar_set.append(prob)
            
            # 更新X轴
            self.axis_x.setTickCount(len(self.highest_belief_cards) + 1)
            
            # 设置标签
            chart = self.chart_view.chart()
            if hasattr(chart, 'axisX') and chart.axisX():
                chart.axisX().setLabels([displayed_card_names.get(card_id, str(card_id)) 
                                         for card_id, _ in self.highest_belief_cards])
        else:
            # 清除现有布局
            while self.belief_layout.count():
                item = self.belief_layout.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()
            
            # 如果没有数据，显示占位符
            if not belief_distribution:
                self.placeholder_label = QLabel("暂无信念分布数据")
                self.placeholder_label.setAlignment(Qt.AlignCenter)
                self.belief_layout.addWidget(self.placeholder_label)
            else:
                # 显示概率最高的N张牌
                for card_id, prob in self.highest_belief_cards:
                    # 创建单行布局
                    row_widget = QWidget()
                    row_layout = QHBoxLayout(row_widget)
                    row_layout.setContentsMargins(0, 0, 0, 0)
                    row_layout.setSpacing(5)
                    
                    # 添加牌名标签
                    card_label = QLabel(displayed_card_names.get(card_id, str(card_id)))
                    card_label.setMinimumWidth(80)
                    row_layout.addWidget(card_label)
                    
                    # 添加概率条
                    prob_bar = QProgressBar()
                    prob_bar.setRange(0, 100)
                    prob_bar.setValue(int(prob * 100))
                    prob_bar.setTextVisible(True)
                    prob_bar.setFormat("%.2f" % prob)
                    row_layout.addWidget(prob_bar)
                    
                    self.belief_layout.addWidget(row_widget)
                
                # 添加弹性空间
                self.belief_layout.addStretch(1)
        
        # 更新最高概率区域
        if self.highest_belief_cards:
            high_prob_text = "最可能的手牌:\n"
            for card_id, prob in self.highest_belief_cards[:5]:  # 只显示前5张
                card_name = displayed_card_names.get(card_id, str(card_id))
                high_prob_text += f"{card_name}: {prob:.2f}  "
            self.high_prob_label.setText(high_prob_text)
        else:
            self.high_prob_label.setText("暂无数据")

    def clear_belief_distribution(self):
        """清除信念分布数据"""
        # 清空数据
        self.belief_data = {}
        self.highest_belief_cards = []
        
        if QTCHARTS_AVAILABLE:
            # 清除条形图数据
            self.bar_set.remove(0, self.bar_set.count())
            
            # 重置X轴
            self.axis_x.setTickCount(1)
        else:
            # 清除现有布局
            while self.belief_layout.count():
                item = self.belief_layout.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()
            
            # 添加占位符
            self.placeholder_label = QLabel("暂无信念分布数据")
            self.placeholder_label.setAlignment(Qt.AlignCenter)
            self.belief_layout.addWidget(self.placeholder_label)
        
        # 重置最高概率区域
        self.high_prob_label.setText("暂无数据")
