"""
模型迁移学习机制模块

提供游戏间知识迁移的功能，允许在不同棋牌游戏之间共享和迁移模型知识。
"""
from typing import Dict, Any, List, Tuple, Optional, Union, Type
import numpy as np
import torch
import torch.nn as nn
import os
import json
from abc import ABC, abstractmethod

from cardgame_ai.core.base import State, Action
from cardgame_ai.games.common.game_descriptor import GameDescriptor
from cardgame_ai.games.common.feature_extractor import FeatureExtractor, FeatureExtractorRegistry


class FeatureMapper(ABC):
    """
    特征映射器基类
    
    用于将一个游戏的特征映射到另一个游戏的特征空间。
    """
    
    def __init__(
        self, 
        source_game_descriptor: GameDescriptor, 
        target_game_descriptor: GameDescriptor
    ):
        """
        初始化特征映射器
        
        Args:
            source_game_descriptor (GameDescriptor): 源游戏描述器
            target_game_descriptor (GameDescriptor): 目标游戏描述器
        """
        self.source_game_descriptor = source_game_descriptor
        self.target_game_descriptor = target_game_descriptor
        
        # 创建特征提取器
        self.source_extractor = self._create_feature_extractor(source_game_descriptor)
        self.target_extractor = self._create_feature_extractor(target_game_descriptor)
    
    def _create_feature_extractor(self, game_descriptor: GameDescriptor) -> FeatureExtractor:
        """
        创建特征提取器
        
        根据游戏类型选择合适的特征提取器。
        
        Args:
            game_descriptor (GameDescriptor): 游戏描述器
            
        Returns:
            FeatureExtractor: 特征提取器
        """
        # 根据游戏类型选择特征提取器
        game_name = game_descriptor.name.lower()
        
        if '扑克' in game_name or 'poker' in game_name or '斗地主' in game_name:
            extractor_cls = FeatureExtractorRegistry.get('card_game')
        else:
            extractor_cls = FeatureExtractorRegistry.get('default')
        
        return extractor_cls(game_descriptor)
    
    @abstractmethod
    def map_features(self, source_features: np.ndarray) -> np.ndarray:
        """
        映射特征
        
        将源游戏的特征映射到目标游戏的特征空间。
        
        Args:
            source_features (np.ndarray): 源游戏特征
            
        Returns:
            np.ndarray: 映射后的特征
        """
        pass
    
    @abstractmethod
    def map_actions(self, source_action: Action, source_state: State) -> Action:
        """
        映射动作
        
        将源游戏的动作映射到目标游戏的动作空间。
        
        Args:
            source_action (Action): 源游戏动作
            source_state (State): 源游戏状态
            
        Returns:
            Action: 映射后的动作
        """
        pass


class DirectFeatureMapper(FeatureMapper):
    """
    直接特征映射器
    
    通过零填充或裁剪将源游戏特征直接映射到目标游戏特征空间。
    适用于特征结构相似的游戏。
    """
    
    def __init__(
        self, 
        source_game_descriptor: GameDescriptor, 
        target_game_descriptor: GameDescriptor
    ):
        """
        初始化直接特征映射器
        
        Args:
            source_game_descriptor (GameDescriptor): 源游戏描述器
            target_game_descriptor (GameDescriptor): 目标游戏描述器
        """
        super().__init__(source_game_descriptor, target_game_descriptor)
        
        # 计算源和目标特征的维度
        self.source_dim = self.source_extractor.get_feature_dim()
        self.target_dim = self.target_extractor.get_feature_dim()
        
        # 确定是填充还是裁剪
        if np.prod(self.source_dim) <= np.prod(self.target_dim):
            self.need_padding = True
            self.need_cropping = False
        else:
            self.need_padding = False
            self.need_cropping = True
    
    def map_features(self, source_features: np.ndarray) -> np.ndarray:
        """
        映射特征
        
        通过零填充或裁剪将源特征映射到目标特征空间。
        
        Args:
            source_features (np.ndarray): 源游戏特征
            
        Returns:
            np.ndarray: 映射后的特征
        """
        # 确保源特征是一维的
        source_features = source_features.flatten()
        
        # 计算目标特征维度
        target_size = np.prod(self.target_dim)
        
        if self.need_padding:
            # 如果需要填充，则在末尾添加零
            padding_size = target_size - len(source_features)
            return np.pad(source_features, (0, padding_size))
        elif self.need_cropping:
            # 如果需要裁剪，则只保留前面部分
            return source_features[:target_size]
        else:
            # 如果维度相同，则直接返回
            return source_features
    
    def map_actions(self, source_action: Action, source_state: State) -> Optional[Action]:
        """
        映射动作
        
        对于直接特征映射器，动作映射通常不适用，返回None表示不支持。
        实际应用中应使用更复杂的动作映射策略。
        
        Args:
            source_action (Action): 源游戏动作
            source_state (State): 源游戏状态
            
        Returns:
            Optional[Action]: 映射后的动作，如果不支持则返回None
        """
        # 动作映射通常需要游戏特定的知识，这里简单返回None
        return None


class NeuralFeatureMapper(FeatureMapper):
    """
    神经网络特征映射器
    
    使用神经网络学习源游戏特征到目标游戏特征的映射关系。
    """
    
    def __init__(
        self, 
        source_game_descriptor: GameDescriptor, 
        target_game_descriptor: GameDescriptor,
        hidden_dim: int = 256,
        num_layers: int = 3
    ):
        """
        初始化神经网络特征映射器
        
        Args:
            source_game_descriptor (GameDescriptor): 源游戏描述器
            target_game_descriptor (GameDescriptor): 目标游戏描述器
            hidden_dim (int, optional): 隐藏层维度. Defaults to 256.
            num_layers (int, optional): 网络层数. Defaults to 3.
        """
        super().__init__(source_game_descriptor, target_game_descriptor)
        
        # 计算源和目标特征的维度
        self.source_dim = int(np.prod(self.source_extractor.get_feature_dim()))
        self.target_dim = int(np.prod(self.target_extractor.get_feature_dim()))
        
        # 创建神经网络
        self.network = self._create_network(self.source_dim, self.target_dim, hidden_dim, num_layers)
        
        # 设置设备
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.network.to(self.device)
        
        # 训练状态
        self.is_trained = False
    
    def _create_network(self, input_dim: int, output_dim: int, hidden_dim: int, num_layers: int) -> nn.Module:
        """
        创建神经网络
        
        Args:
            input_dim (int): 输入维度
            output_dim (int): 输出维度
            hidden_dim (int): 隐藏层维度
            num_layers (int): 网络层数
            
        Returns:
            nn.Module: 神经网络
        """
        layers = []
        
        # 输入层
        layers.append(nn.Linear(input_dim, hidden_dim))
        layers.append(nn.ReLU())
        
        # 隐藏层
        for _ in range(num_layers - 2):
            layers.append(nn.Linear(hidden_dim, hidden_dim))
            layers.append(nn.ReLU())
        
        # 输出层
        layers.append(nn.Linear(hidden_dim, output_dim))
        
        return nn.Sequential(*layers)
    
    def map_features(self, source_features: np.ndarray) -> np.ndarray:
        """
        映射特征
        
        使用神经网络将源特征映射到目标特征空间。
        
        Args:
            source_features (np.ndarray): 源游戏特征
            
        Returns:
            np.ndarray: 映射后的特征
        """
        if not self.is_trained:
            # 如果网络未训练，则使用零填充或裁剪
            return DirectFeatureMapper(
                self.source_game_descriptor, 
                self.target_game_descriptor
            ).map_features(source_features)
        
        # 确保源特征是一维的
        source_features = source_features.flatten()
        
        # 转换为张量
        source_tensor = torch.FloatTensor(source_features).to(self.device)
        
        # 使用网络进行映射
        with torch.no_grad():
            target_tensor = self.network(source_tensor)
        
        # 转换回NumPy数组
        return target_tensor.cpu().numpy()
    
    def map_actions(self, source_action: Action, source_state: State) -> Optional[Action]:
        """
        映射动作
        
        对于神经网络特征映射器，动作映射通常不适用，返回None表示不支持。
        实际应用中应使用更复杂的动作映射策略。
        
        Args:
            source_action (Action): 源游戏动作
            source_state (State): 源游戏状态
            
        Returns:
            Optional[Action]: 映射后的动作，如果不支持则返回None
        """
        # 动作映射通常需要游戏特定的知识，这里简单返回None
        return None
    
    def train(
        self, 
        source_features: List[np.ndarray], 
        target_features: List[np.ndarray], 
        epochs: int = 100, 
        batch_size: int = 32, 
        learning_rate: float = 1e-3
    ) -> Dict[str, List[float]]:
        """
        训练神经网络
        
        Args:
            source_features (List[np.ndarray]): 源游戏特征列表
            target_features (List[np.ndarray]): 目标游戏特征列表
            epochs (int, optional): 训练轮数. Defaults to 100.
            batch_size (int, optional): 批次大小. Defaults to 32.
            learning_rate (float, optional): 学习率. Defaults to 1e-3.
            
        Returns:
            Dict[str, List[float]]: 训练历史，包含损失值
        """
        # 转换为张量
        source_tensors = torch.FloatTensor(np.stack([f.flatten() for f in source_features])).to(self.device)
        target_tensors = torch.FloatTensor(np.stack([f.flatten() for f in target_features])).to(self.device)
        
        # 创建数据集
        dataset = torch.utils.data.TensorDataset(source_tensors, target_tensors)
        dataloader = torch.utils.data.DataLoader(dataset, batch_size=batch_size, shuffle=True)
        
        # 创建优化器
        optimizer = torch.optim.Adam(self.network.parameters(), lr=learning_rate)
        
        # 创建损失函数
        criterion = nn.MSELoss()
        
        # 训练历史
        history = {'loss': []}
        
        # 训练循环
        for epoch in range(epochs):
            epoch_loss = 0.0
            
            for source_batch, target_batch in dataloader:
                # 前向传播
                output = self.network(source_batch)
                loss = criterion(output, target_batch)
                
                # 反向传播
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
                
                epoch_loss += loss.item()
            
            # 记录平均损失
            avg_loss = epoch_loss / len(dataloader)
            history['loss'].append(avg_loss)
            
            print(f"Epoch {epoch+1}/{epochs}, Loss: {avg_loss:.6f}")
        
        # 设置训练状态
        self.is_trained = True
        
        return history
    
    def save(self, path: str) -> None:
        """
        保存模型
        
        Args:
            path (str): 保存路径
        """
        # 创建目录
        os.makedirs(os.path.dirname(os.path.abspath(path)), exist_ok=True)
        
        # 保存模型
        torch.save({
            'network_state_dict': self.network.state_dict(),
            'source_game': self.source_game_descriptor.to_dict(),
            'target_game': self.target_game_descriptor.to_dict(),
            'is_trained': self.is_trained
        }, path)
    
    @classmethod
    def load(cls, path: str) -> 'NeuralFeatureMapper':
        """
        加载模型
        
        Args:
            path (str): 模型路径
            
        Returns:
            NeuralFeatureMapper: 加载的模型
        """
        # 加载模型
        checkpoint = torch.load(path)
        
        # 创建游戏描述器
        source_game = GameDescriptor.from_dict(checkpoint['source_game'])
        target_game = GameDescriptor.from_dict(checkpoint['target_game'])
        
        # 创建映射器
        mapper = cls(source_game, target_game)
        
        # 加载网络参数
        mapper.network.load_state_dict(checkpoint['network_state_dict'])
        
        # 设置训练状态
        mapper.is_trained = checkpoint['is_trained']
        
        return mapper


class ModelTransfer:
    """
    模型迁移类
    
    用于在不同游戏之间迁移模型知识。
    """
    
    def __init__(
        self, 
        source_game_descriptor: GameDescriptor, 
        target_game_descriptor: GameDescriptor,
        feature_mapper: Optional[FeatureMapper] = None
    ):
        """
        初始化模型迁移
        
        Args:
            source_game_descriptor (GameDescriptor): 源游戏描述器
            target_game_descriptor (GameDescriptor): 目标游戏描述器
            feature_mapper (Optional[FeatureMapper], optional): 特征映射器. Defaults to None.
        """
        self.source_game_descriptor = source_game_descriptor
        self.target_game_descriptor = target_game_descriptor
        
        # 创建特征映射器
        if feature_mapper is None:
            self.feature_mapper = NeuralFeatureMapper(source_game_descriptor, target_game_descriptor)
        else:
            self.feature_mapper = feature_mapper
        
        # 创建特征提取器
        self.source_extractor = self.feature_mapper.source_extractor
        self.target_extractor = self.feature_mapper.target_extractor
    
    def transfer_knowledge(
        self, 
        source_model_path: str, 
        target_model_path: str, 
        transfer_config: Dict[str, Any]
    ) -> None:
        """
        迁移知识
        
        将源模型的知识迁移到目标模型。
        
        Args:
            source_model_path (str): 源模型路径
            target_model_path (str): 目标模型路径
            transfer_config (Dict[str, Any]): 迁移配置
        """
        # 加载源模型
        source_model = torch.load(source_model_path)
        
        # 创建目标模型（通常是源模型的拷贝，但网络结构可能不同）
        target_model = self._create_target_model(source_model, transfer_config)
        
        # 保存目标模型
        torch.save(target_model, target_model_path)
    
    def _create_target_model(self, source_model: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建目标模型
        
        根据源模型和配置创建目标模型。
        
        Args:
            source_model (Dict[str, Any]): 源模型
            config (Dict[str, Any]): 迁移配置
            
        Returns:
            Dict[str, Any]: 目标模型
        """
        # 创建目标模型
        target_model = {}
        
        # 复制元数据
        for key in ['model_type', 'version', 'created_at', 'hyperparameters']:
            if key in source_model:
                target_model[key] = source_model[key]
        
        # 设置目标游戏信息
        target_model['game'] = self.target_game_descriptor.to_dict()
        
        # 记录迁移信息
        target_model['transfer_info'] = {
            'source_game': self.source_game_descriptor.name,
            'target_game': self.target_game_descriptor.name,
            'transfer_config': config
        }
        
        # 处理网络参数
        if 'state_dict' in source_model:
            # 如果源模型和目标模型的网络结构相同，则可以直接复制参数
            if config.get('direct_copy', False):
                target_model['state_dict'] = source_model['state_dict']
            else:
                # 否则需要进行参数映射
                target_model['state_dict'] = self._map_network_parameters(
                    source_model['state_dict'], 
                    config.get('layer_mapping', {})
                )
        
        return target_model
    
    def _map_network_parameters(
        self, 
        source_state_dict: Dict[str, torch.Tensor], 
        layer_mapping: Dict[str, str]
    ) -> Dict[str, torch.Tensor]:
        """
        映射网络参数
        
        将源网络的参数映射到目标网络。
        
        Args:
            source_state_dict (Dict[str, torch.Tensor]): 源网络参数
            layer_mapping (Dict[str, str]): 层映射关系
            
        Returns:
            Dict[str, torch.Tensor]: 映射后的网络参数
        """
        target_state_dict = {}
        
        # 映射层参数
        for source_key, target_key in layer_mapping.items():
            if source_key in source_state_dict:
                target_state_dict[target_key] = source_state_dict[source_key]
        
        return target_state_dict


class ModelTransferRegistry:
    """
    模型迁移注册表
    
    用于注册和管理模型迁移。
    """
    
    _registry: Dict[Tuple[str, str], ModelTransfer] = {}
    
    @classmethod
    def register(cls, source_game: str, target_game: str, transfer: ModelTransfer) -> None:
        """
        注册模型迁移
        
        Args:
            source_game (str): 源游戏名称
            target_game (str): 目标游戏名称
            transfer (ModelTransfer): 模型迁移对象
        """
        cls._registry[(source_game, target_game)] = transfer
    
    @classmethod
    def get(cls, source_game: str, target_game: str) -> Optional[ModelTransfer]:
        """
        获取模型迁移
        
        Args:
            source_game (str): 源游戏名称
            target_game (str): 目标游戏名称
            
        Returns:
            Optional[ModelTransfer]: 模型迁移对象，如果不存在则返回None
        """
        return cls._registry.get((source_game, target_game))
    
    @classmethod
    def list_transfers(cls) -> List[Tuple[str, str]]:
        """
        列出所有可用的模型迁移
        
        Returns:
            List[Tuple[str, str]]: 模型迁移名称列表，每个元素为(源游戏,目标游戏)
        """
        return list(cls._registry.keys()) 