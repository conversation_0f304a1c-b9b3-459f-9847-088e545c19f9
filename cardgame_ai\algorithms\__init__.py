"""
算法模块

实现各种强化学习算法，包括基础算法（DQN、PPO等）和高级算法（MuZero等）。
"""

# 导出算法组件
from cardgame_ai.algorithms.replay_buffer import ReplayBuffer, PrioritizedReplayBuffer
from cardgame_ai.algorithms.parallel_replay_buffer import ParallelPrioritizedReplayBuffer, SumTree, ShardedSumTree
from cardgame_ai.algorithms.experience_filter import (
    ExperienceQualityMetric, TDErrorMetric, RewardMetric, RarityMetric,
    DiversityMetric, CompositeMetric, ExperienceFilter
)
from cardgame_ai.algorithms.experience_distillation import (
    ModelRegistry, ExperienceDistillation, DistillationTrainer
)
from cardgame_ai.algorithms.exploration import Epsilon<PERSON>reedy, UCB, ThompsonSampling
from cardgame_ai.algorithms.dqn import DQN, DoubleDQN, DuelingDQN
from cardgame_ai.algorithms.ppo import PPO
from cardgame_ai.algorithms.scheduler import (
    LRScheduler, ConstantLR, ExponentialLR, StepLR,
    CosineAnnealingLR, ReduceLROnPlateau
)
from cardgame_ai.algorithms.mcts import MCTS, Node
from cardgame_ai.algorithms.muzero import MuZero, MuZeroModel
from cardgame_ai.algorithms.transformer_policy import TransformerPolicy, TransformerPolicyNetwork, StateEncoder
from cardgame_ai.algorithms.muzero_transformer import MuZeroTransformer, MuZeroTransformerModel
from cardgame_ai.algorithms.efficient_zero import EfficientZero, EfficientZeroModel
from cardgame_ai.algorithms.hybrid_decision_system import (
    DecisionComponent, NeuralNetworkComponent,
    SearchComponent, RuleComponent, HRLComponent
)
from cardgame_ai.algorithms.adaptive_neural_architecture import (
    ArchitectureSearchSpace, NeuralArchitectureSearch,
    DynamicNetworkExtension, ConditionalComputationPath,
    ModularNetworkDesign
)
from cardgame_ai.algorithms.risk_sensitive_rl import (
    CVaRCalculator, cvar_policy_loss, cvar_value_loss
)
