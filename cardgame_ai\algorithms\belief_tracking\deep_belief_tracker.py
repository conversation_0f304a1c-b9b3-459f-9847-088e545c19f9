"""
新版深度信念追踪器模块

使用Transformer架构处理游戏历史序列和场面信息，
生成对手手牌概率分布的精确预测。包含自注意力机制以捕捉
长期依赖关系和复杂模式。
"""
from typing import Dict, List, Optional, Any, Tuple, Union
import time
import logging
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F

from cardgame_ai.core.base import State
from cardgame_ai.games.common.belief_state import BeliefState, BeliefSource
from cardgame_ai.games.doudizhu.card import Card, CardRank, CardSuit
from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType, get_card_group_type

# 配置日志
logger = logging.getLogger(__name__)


class PositionalEncoding(nn.Module):
    """
    位置编码模块
    
    为Transformer提供序列位置信息。
    """
    
    def __init__(self, d_model: int, max_seq_length: int = 200, dropout: float = 0.1):
        """
        初始化位置编码
        
        Args:
            d_model (int): 模型维度
            max_seq_length (int, optional): 最大序列长度. Defaults to 200.
            dropout (float, optional): Dropout比率. Defaults to 0.1.
        """
        super().__init__()
        self.dropout = nn.Dropout(p=dropout)
        
        # 创建位置编码矩阵
        pe = torch.zeros(max_seq_length, d_model)
        position = torch.arange(0, max_seq_length, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(
            torch.arange(0, d_model, 2).float() * (-np.log(10000.0) / d_model)
        )
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0)  # [1, max_seq_length, d_model]
        
        # 注册为缓冲区，不作为模型参数
        self.register_buffer('pe', pe)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        添加位置编码
        
        Args:
            x (torch.Tensor): 输入张量 [batch_size, seq_length, d_model]
            
        Returns:
            torch.Tensor: 添加位置编码后的张量
        """
        # 为输入添加位置编码
        x = x + self.pe[:, :x.size(1), :]
        return self.dropout(x)


class ActionEmbedding(nn.Module):
    """
    动作嵌入模块
    
    将游戏动作转换为稠密向量表示。
    """
    
    def __init__(
        self, 
        vocab_size: int, 
        embedding_dim: int, 
        num_card_types: int = len(CardGroupType)
    ):
        """
        初始化动作嵌入
        
        Args:
            vocab_size (int): 词汇表大小（牌的数量）
            embedding_dim (int): 嵌入维度
            num_card_types (int, optional): 牌型数量. Defaults to len(CardGroupType).
        """
        super().__init__()
        self.card_embedding = nn.Embedding(vocab_size, embedding_dim)
        self.card_type_embedding = nn.Embedding(num_card_types, embedding_dim)
        self.pass_embedding = nn.Parameter(torch.randn(embedding_dim))
        
        # 组合层
        self.combiner = nn.Sequential(
            nn.Linear(embedding_dim * 2, embedding_dim),
            nn.LayerNorm(embedding_dim),
            nn.ReLU(),
            nn.Linear(embedding_dim, embedding_dim)
        )
    
    def forward(
        self, 
        cards: Optional[List[int]] = None, 
        card_type: Optional[int] = None, 
        is_pass: bool = False
    ) -> torch.Tensor:
        """
        计算动作嵌入
        
        Args:
            cards (Optional[List[int]], optional): 牌的索引列表. Defaults to None.
            card_type (Optional[int], optional): 牌型索引. Defaults to None.
            is_pass (bool, optional): 是否不出. Defaults to False.
            
        Returns:
            torch.Tensor: 动作嵌入向量
        """
        if is_pass:
            # 不出牌
            return self.pass_embedding.unsqueeze(0)  # [1, embedding_dim]
        
        # 嵌入牌
        card_embeds = self.card_embedding(torch.tensor(cards, device=self.pass_embedding.device))
        # 取平均
        card_embed = torch.mean(card_embeds, dim=0)  # [embedding_dim]
        
        # 嵌入牌型
        type_embed = self.card_type_embedding(torch.tensor(card_type, device=self.pass_embedding.device))
        
        # 组合嵌入
        combined = torch.cat([card_embed, type_embed], dim=0)  # [embedding_dim*2]
        return self.combiner(combined.unsqueeze(0))  # [1, embedding_dim]


class TransformerBeliefTracker(nn.Module):
    """
    基于Transformer的信念追踪网络
    
    使用Transformer架构处理游戏历史和当前状态，预测对手手牌概率分布。
    """
    
    def __init__(
        self,
        input_dim: int,
        hidden_dim: int,
        output_dim: int,
        num_heads: int = 8,
        num_layers: int = 6,
        dropout: float = 0.1,
        max_seq_length: int = 200,
        vocab_size: int = 54  # 牌的数量
    ):
        """
        初始化Transformer信念追踪器
        
        Args:
            input_dim (int): 输入特征维度
            hidden_dim (int): 隐藏层维度
            output_dim (int): 输出维度(通常是牌的数量)
            num_heads (int, optional): 注意力头数. Defaults to 8.
            num_layers (int, optional): Transformer层数. Defaults to 6.
            dropout (float, optional): Dropout比率. Defaults to 0.1.
            max_seq_length (int, optional): 最大序列长度. Defaults to 200.
            vocab_size (int, optional): 词汇表大小. Defaults to 54.
        """
        super().__init__()
        
        # 动作嵌入
        self.action_embedding = ActionEmbedding(vocab_size, input_dim)
        
        # 状态嵌入
        self.state_embedding = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 位置编码
        self.positional_encoding = PositionalEncoding(
            d_model=hidden_dim,
            max_seq_length=max_seq_length,
            dropout=dropout
        )
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=hidden_dim,
            nhead=num_heads,
            dim_feedforward=hidden_dim * 4,
            dropout=dropout,
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(
            encoder_layer=encoder_layer,
            num_layers=num_layers
        )
        
        # 特征融合层
        self.fusion_layer = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 输出层
        self.output_layer = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.LayerNorm(hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, output_dim)
        )
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        """
        初始化模型权重
        """
        # 使用Xavier初始化线性层
        for name, param in self.named_parameters():
            if 'weight' in name and 'norm' not in name.lower() and 'embedding' not in name.lower():
                nn.init.xavier_uniform_(param)
    
    def forward(
        self, 
        action_history: List[Dict[str, Any]],
        current_state: torch.Tensor,
        mask: Optional[torch.Tensor] = None
    ) -> torch.Tensor:
        """
        前向传播
        
        Args:
            action_history (List[Dict[str, Any]]): 动作历史序列
            current_state (torch.Tensor): 当前游戏状态
            mask (Optional[torch.Tensor], optional): 注意力掩码. Defaults to None.
            
        Returns:
            torch.Tensor: 预测的概率分布 [batch_size, output_dim]
        """
        batch_size = current_state.size(0)
        device = current_state.device
        
        # 处理动作历史
        history_embeds = []
        for batch_idx in range(batch_size):
            batch_history = action_history[batch_idx]
            seq_embeds = []
            
            for action in batch_history:
                # 提取动作信息
                is_pass = action.get('is_pass', False)
                cards = action.get('cards_idx', None)
                card_type = action.get('card_type_idx', None)
                
                # 获取动作嵌入
                action_embed = self.action_embedding(cards, card_type, is_pass)
                seq_embeds.append(action_embed)
            
            # 将序列堆叠起来
            if seq_embeds:
                batch_seq_embeds = torch.cat(seq_embeds, dim=0)  # [seq_len, hidden_dim]
            else:
                # 如果没有历史，创建一个空的嵌入
                batch_seq_embeds = torch.zeros((1, self.action_embedding.pass_embedding.size(0)), 
                                              device=device)
            
            history_embeds.append(batch_seq_embeds)
        
        # 填充序列到相同长度
        max_seq_len = max(embed.size(0) for embed in history_embeds)
        padded_history = []
        history_lengths = []
        
        for embed in history_embeds:
            seq_len = embed.size(0)
            history_lengths.append(seq_len)
            
            # 填充
            if seq_len < max_seq_len:
                padding = torch.zeros((max_seq_len - seq_len, embed.size(1)), device=device)
                padded_embed = torch.cat([embed, padding], dim=0)
            else:
                padded_embed = embed
            
            padded_history.append(padded_embed)
        
        # 堆叠批次
        history_tensor = torch.stack(padded_history)  # [batch_size, max_seq_len, hidden_dim]
        
        # 添加位置编码
        history_tensor = self.positional_encoding(history_tensor)
        
        # 创建填充掩码
        padding_mask = None
        if history_lengths[0] < max_seq_len:  # 至少有一个序列需要填充
            padding_mask = torch.zeros((batch_size, max_seq_len), device=device, dtype=torch.bool)
            for i, length in enumerate(history_lengths):
                padding_mask[i, length:] = True
        
        # 通过Transformer编码器
        history_encoded = self.transformer_encoder(
            history_tensor, 
            src_key_padding_mask=padding_mask
        )
        
        # 获取有效输出的表示
        history_features = []
        for i, length in enumerate(history_lengths):
            # 使用最后一个非填充位置的输出
            if length > 0:
                history_features.append(history_encoded[i, length-1])
            else:
                # 如果序列为空，使用第一个位置
                history_features.append(history_encoded[i, 0])
        
        history_features = torch.stack(history_features)  # [batch_size, hidden_dim]
        
        # 处理当前状态
        state_features = self.state_embedding(current_state)  # [batch_size, hidden_dim]
        
        # 融合特征
        combined_features = torch.cat([history_features, state_features], dim=1)  # [batch_size, hidden_dim*2]
        fused_features = self.fusion_layer(combined_features)  # [batch_size, hidden_dim]
        
        # 生成输出
        logits = self.output_layer(fused_features)  # [batch_size, output_dim]
        
        # 应用softmax获取概率分布
        probabilities = F.softmax(logits, dim=1)
        
        return probabilities


class DeepBeliefTracker:
    """
    高级深度信念追踪器
    
    利用Transformer模型预测对手手牌分布，实现高精度的信念追踪。
    """
    
    def __init__(self, player_id: str, *args, **kwargs):
        """
        初始化深度信念追踪器
        
        Args:
            player_id (str): 玩家ID
        """
        # 基础属性
        self.player_id = player_id
        self.device = kwargs.get('device', 'cpu')
        
        # 模型与映射
        self.model = kwargs.get('model', None)
        self.card_mapping = kwargs.get('card_mapping', None)
        self.card_to_idx = kwargs.get('card_to_idx', {})
        self.idx_to_card = kwargs.get('idx_to_card', {})
        self.card_type_to_idx = kwargs.get('card_type_to_idx', {})
        
        # 配置
        self.max_history_length = kwargs.get('max_history_length', 50)
        initial_belief = kwargs.get('initial_belief', None)
        initial_hand_size = kwargs.get('initial_hand_size', None)
        
        # 如果提供了模型，设置为评估模式
        if self.model is not None:
            self.model.to(self.device)
            self.model.eval()
        
        # 初始化卡牌映射（如果未提供）
        if not self.card_mapping and not self.card_to_idx:
            self._initialize_card_mappings()
        
        # 初始化信念状态
        if initial_belief is not None:
            self.belief = initial_belief
        else:
            # 均匀分布初始化
            card_probabilities = {}
            for card in self.card_to_idx.keys():
                card_probabilities[card] = 1.0 / len(self.card_to_idx)
            
            self.belief = BeliefState(
                player_id=self.player_id,
                card_probabilities=card_probabilities,
                estimated_hand_length=initial_hand_size,
                source=BeliefSource.INITIAL,
                confidence=0.5
            )
        
        # 初始化动作历史
        self.action_history = []
        self.last_update_time = time.time()
        
        logger.info(f"高级深度信念追踪器已初始化，玩家ID: {player_id}")
    
    def _initialize_card_mappings(self):
        """
        初始化卡牌映射
        """
        # 创建卡牌到索引的映射
        self.card_to_idx = {}
        self.idx_to_card = {}
        
        # 初始化标准扑克牌
        idx = 0
        for rank in CardRank:
            for suit in CardSuit:
                # 跳过大小王的花色变体
                if (rank == CardRank.SMALL_JOKER or rank == CardRank.BIG_JOKER) and suit != CardSuit.SPADE:
                    continue
                
                card = Card(rank, suit)
                card_str = str(card)
                self.card_to_idx[card_str] = idx
                self.idx_to_card[idx] = card_str
                idx += 1
        
        # 为牌型创建映射
        self.card_type_to_idx = {card_type: i for i, card_type in enumerate(CardGroupType)}
        
        logger.info(f"已创建卡牌映射，共{len(self.card_to_idx)}张牌")
    
    def update(
        self,
        opponent_action: Optional[List[Card]],
        public_knowledge: Dict[str, Any],
        current_state: Optional[np.ndarray] = None
    ) -> None:
        """
        根据对手的动作和公共信息更新信念状态
        
        Args:
            opponent_action (Optional[List[Card]]): 对手的出牌动作，如果是None表示对手选择不出牌
            public_knowledge (Dict[str, Any]): 公共信息，包括已知的公共牌、历史出牌等
            current_state (Optional[np.ndarray], optional): 当前游戏状态的向量表示. Defaults to None.
        """
        current_time = time.time()
        
        # 处理动作，添加到历史
        action_info = self._process_action(opponent_action, public_knowledge)
        self.action_history.append(action_info)
        
        # 限制历史长度
        if len(self.action_history) > self.max_history_length:
            self.action_history = self.action_history[-self.max_history_length:]
        
        # 如果对手出牌了，更新手牌数量估计
        if opponent_action is not None and len(opponent_action) > 0:
            if self.belief.estimated_hand_length is not None:
                self.belief.estimated_hand_length -= len(opponent_action)
        
        # 使用神经网络更新信念状态（如果提供了当前状态和模型）
        if current_state is not None and self.model is not None:
            self._update_with_neural_network(current_state)
        else:
            # 否则使用基础规则更新
            self._update_with_basic_rules(opponent_action, public_knowledge)
        
        # 归一化概率
        self.belief.normalize_probabilities()
        
        # 更新元数据
        self.belief.last_updated = current_time
        self.belief.version += 1
        self.last_update_time = current_time
    
    def _process_action(
        self,
        opponent_action: Optional[List[Card]],
        public_knowledge: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        处理对手动作，转换为模型可用的格式
        
        Args:
            opponent_action (Optional[List[Card]]): 对手的出牌动作
            public_knowledge (Dict[str, Any]): 公共信息
            
        Returns:
            Dict[str, Any]: 处理后的动作信息
        """
        action_info = {}
        
        if opponent_action is None:
            # 不出牌
            action_info['is_pass'] = True
            action_info['cards'] = None
            action_info['cards_idx'] = None
            action_info['card_type'] = None
            action_info['card_type_idx'] = None
        else:
            # 出牌
            action_info['is_pass'] = False
            action_info['cards'] = [str(card) for card in opponent_action]
            
            # 获取牌型
            try:
                card_group = CardGroup(opponent_action)
                card_type = get_card_group_type(card_group)
                action_info['card_type'] = card_type
                action_info['card_type_idx'] = self.card_type_to_idx.get(card_type, 0)
            except Exception as e:
                logger.warning(f"获取牌型失败: {e}")
                action_info['card_type'] = None
                action_info['card_type_idx'] = 0
            
            # 转换为索引
            try:
                action_info['cards_idx'] = [self.card_to_idx.get(str(card), 0) for card in opponent_action]
            except Exception as e:
                logger.warning(f"转换卡牌索引失败: {e}")
                action_info['cards_idx'] = None
        
        # 添加公共信息
        action_info['public_knowledge'] = public_knowledge
        action_info['time'] = time.time()
        
        return action_info
    
    def _update_with_neural_network(self, current_state: np.ndarray) -> None:
        """
        使用神经网络更新信念状态
        
        Args:
            current_state (np.ndarray): 当前游戏状态
        """
        try:
            # 将状态转换为张量
            state_tensor = torch.tensor(current_state, dtype=torch.float32).unsqueeze(0).to(self.device)
            
            # 准备动作历史批次
            action_history_batch = [self.action_history]
            
            # 使用模型预测
            with torch.no_grad():
                probabilities = self.model(action_history_batch, state_tensor)
                probabilities = probabilities.squeeze(0).cpu().numpy()
            
            # 更新信念状态
            new_probs = {}
            for i, card in enumerate(self.idx_to_card.values()):
                if i < len(probabilities):
                    new_probs[card] = float(probabilities[i])
            
            # 应用概率更新
            self.belief.update_probabilities(new_probs)
            
            # 更新元数据
            self.belief.source = BeliefSource.NEURAL
            self.belief.confidence = 0.9  # 神经网络预测的置信度高
            
            logger.debug("使用神经网络更新信念状态成功")
        except Exception as e:
            logger.error(f"使用神经网络更新信念状态失败: {e}")
            # 如果神经网络更新失败，回退到基础规则
            self._update_with_basic_rules(None, {})
    
    def _update_with_basic_rules(
        self,
        opponent_action: Optional[List[Card]],
        public_knowledge: Dict[str, Any]
    ) -> None:
        """
        使用基础规则更新信念状态
        
        Args:
            opponent_action (Optional[List[Card]]): 对手的出牌动作
            public_knowledge (Dict[str, Any]): 公共信息
        """
        new_probs = self.belief.card_probabilities.copy()
        
        # 如果对手出牌了，更新相应的概率
        if opponent_action is not None and len(opponent_action) > 0:
            # 对手出的牌，概率为0（已经打出）
            action_cards = [str(card) for card in opponent_action]
            for card in action_cards:
                new_probs[card] = 0.0
        
        # 更新公共信息中的已知牌
        if 'known_cards' in public_knowledge:
            known_cards = public_knowledge['known_cards']
            if isinstance(known_cards, list) and len(known_cards) > 0:
                known_cards_str = [str(card) if not isinstance(card, str) else card
                                  for card in known_cards]
                for card in known_cards_str:
                    new_probs[card] = 0.0  # 已知的公共牌，对手不可能持有
        
        # 应用概率更新
        self.belief.update_probabilities(new_probs)
        self.belief.source = BeliefSource.INFERENCE
        self.belief.confidence = 0.7
    
    def get_belief_state(self) -> BeliefState:
        """
        获取当前信念状态
        
        Returns:
            BeliefState: 信念状态对象
        """
        return self.belief
    
    def predict(self, state: State, history: Optional[List[State]] = None) -> BeliefState:
        """
        预测给定状态的信念分布
        
        Args:
            state (State): 当前游戏状态
            history (Optional[List[State]], optional): 历史状态列表. Defaults to None.
            
        Returns:
            BeliefState: 预测的信念状态
        """
        # 转换状态为模型可接受的格式
        state_vector = self._state_to_vector(state)
        
        # 处理历史（如果提供）
        if history:
            # 构建历史动作序列
            processed_history = []
            for h_state in history:
                action_info = self._extract_action_from_state(h_state)
                if action_info:
                    processed_history.append(action_info)
            
            # 预测信念
            with torch.no_grad():
                state_tensor = torch.tensor(state_vector, dtype=torch.float32).unsqueeze(0).to(self.device)
                action_history_batch = [processed_history]
                probabilities = self.model(action_history_batch, state_tensor)
                probabilities = probabilities.squeeze(0).cpu().numpy()
                
                # 构建信念状态
                card_probs = {}
                for i, card in enumerate(self.idx_to_card.values()):
                    if i < len(probabilities):
                        card_probs[card] = float(probabilities[i])
                
                predicted_belief = BeliefState(
                    player_id=self.player_id,
                    card_probabilities=card_probs,
                    source=BeliefSource.NEURAL,
                    confidence=0.9
                )
                
                return predicted_belief
        
        # 如果没有历史，返回当前信念状态
        return self.belief
    
    def _state_to_vector(self, state: State) -> np.ndarray:
        """
        将游戏状态转换为向量表示
        
        Args:
            state (State): 游戏状态
            
        Returns:
            np.ndarray: 状态向量
        """
        # 这里需要根据具体游戏实现状态向量化
        # 如果状态已经是向量，直接返回
        if hasattr(state, 'to_vector'):
            return state.to_vector()
        return np.array([])
    
    def _extract_action_from_state(self, state: State) -> Optional[Dict[str, Any]]:
        """
        从状态中提取动作信息
        
        Args:
            state (State): 游戏状态
            
        Returns:
            Optional[Dict[str, Any]]: 提取的动作信息
        """
        # 需要根据具体游戏实现
        # 示例实现
        try:
            if hasattr(state, 'last_action') and hasattr(state, 'last_player'):
                if state.last_player != self.player_id:  # 是对手的动作
                    action = state.last_action
                    if action is None:
                        return {
                            'is_pass': True,
                            'cards': None,
                            'cards_idx': None,
                            'card_type': None,
                            'card_type_idx': None,
                            'time': time.time()
                        }
                    else:
                        # 假设action是Card对象列表
                        return self._process_action(action, {})
            return None
        except Exception as e:
            logger.error(f"从状态提取动作失败: {e}")
            return None
    
    def save_model(self, path: str) -> None:
        """
        保存模型
        
        Args:
            path (str): 保存路径
        """
        if self.model is not None:
            try:
                # 保存模型状态
                torch.save({
                    'model_state_dict': self.model.state_dict(),
                    'card_to_idx': self.card_to_idx,
                    'idx_to_card': self.idx_to_card,
                    'card_type_to_idx': self.card_type_to_idx
                }, path)
                logger.info(f"模型已保存到 {path}")
            except Exception as e:
                logger.error(f"保存模型失败: {e}")
    
    def load_model(self, path: str) -> None:
        """
        加载模型
        
        Args:
            path (str): 模型路径
        """
        try:
            # 加载模型
            checkpoint = torch.load(path, map_location=self.device)
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.model.eval()
            
            # 加载映射（如果存在）
            if 'card_to_idx' in checkpoint:
                self.card_to_idx = checkpoint['card_to_idx']
            if 'idx_to_card' in checkpoint:
                self.idx_to_card = checkpoint['idx_to_card']
            if 'card_type_to_idx' in checkpoint:
                self.card_type_to_idx = checkpoint['card_type_to_idx']
                
            logger.info(f"模型已从 {path} 加载")
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
    
    def reset(self, initial_belief: Optional[BeliefState] = None) -> None:
        """
        重置信念追踪器
        
        Args:
            initial_belief (Optional[BeliefState], optional): 初始信念. Defaults to None.
        """
        # 清空历史
        self.action_history = []
        
        # 重置信念
        if initial_belief is not None:
            self.belief = initial_belief
        else:
            # 默认均匀分布
            card_probabilities = {}
            for card in self.card_to_idx.keys():
                card_probabilities[card] = 1.0 / len(self.card_to_idx)
            
            self.belief = BeliefState(
                player_id=self.player_id,
                card_probabilities=card_probabilities,
                source=BeliefSource.INITIAL,
                confidence=0.5
            ) 