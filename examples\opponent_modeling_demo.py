"""
在线对手建模与决策系统集成演示

演示如何使用在线对手建模器生成对手表示，并将其集成到决策系统中以提升决策针对性。
"""

import torch
import numpy as np
from typing import List, Dict, Any
import time

from cardgame_ai.algorithms.opponent_modeling.online_modeler import OnlineOpponentModeler
from cardgame_ai.algorithms.transformer_policy import TransformerPolicy
from cardgame_ai.algorithms.hrl.hierarchical_controller import HierarchicalController
from cardgame_ai.algorithms.hrl.high_level_policy import HighLevelPolicy
from cardgame_ai.algorithms.hrl.low_level_policy import LowLevelPolicy

# 创建简单的游戏状态表示
class GameState:
    def __init__(self, player_id: str, hand_cards: List[int], history: List[tuple] = None):
        self.player_id = player_id
        self.hand_cards = hand_cards
        self.history = history or []
        
    def to_tensor(self) -> torch.Tensor:
        # 简单起见，将手牌转换为一维向量
        state = torch.zeros(54)  # 54张牌
        for card in self.hand_cards:
            if 0 <= card < 54:
                state[card] = 1
        return state

# 创建简单的高层策略
class SimpleHighLevelPolicy(HighLevelPolicy):
    def __init__(self):
        self.opponent_representation = None
    
    def set_goal(self, state_tensor):
        # 简单目标：根据状态，选择1-3中的一个
        if self.opponent_representation is not None:
            # 使用对手表示调整目标选择
            if self.opponent_representation.get('type') == 'aggressive':
                return 1  # 对激进对手使用目标1
            elif self.opponent_representation.get('type') == 'conservative':
                return 2  # 对保守对手使用目标2
            else:
                return 3  # 对其他类型对手使用目标3
        else:
            # 没有对手表示，随机选择
            return np.random.choice([1, 2, 3])
    
    def set_opponent_representation(self, representation):
        self.opponent_representation = representation

# 创建简单的低层策略
class SimpleLowLevelPolicy(LowLevelPolicy):
    def __init__(self):
        self.opponent_representation = None
    
    def execute(self, state_tensor, legal_actions, context=None):
        # 简单执行：选择第一个合法动作
        return legal_actions[0] if legal_actions else 0
    
    def execute_with_goal(self, state_tensor, goal, legal_actions):
        # 根据目标选择不同的动作
        if goal == 1:
            # 目标1：选择最小的合法动作
            return min(legal_actions) if legal_actions else 0
        elif goal == 2:
            # 目标2：选择最大的合法动作
            return max(legal_actions) if legal_actions else 0
        else:
            # 目标3：选择中间的合法动作
            if legal_actions:
                return legal_actions[len(legal_actions) // 2]
            return 0
    
    def decide_directly(self, state_tensor, legal_actions):
        # 简单直接决策：随机选择
        return np.random.choice(legal_actions) if legal_actions else 0
    
    def set_opponent_representation(self, representation):
        self.opponent_representation = representation
    
    def estimate_confidence(self, state_tensor, legal_actions):
        # 简单置信度估计
        if self.opponent_representation is not None:
            # 使用对手表示调整置信度
            if self.opponent_representation.get('type') == 'conservative':
                return 0.8  # 对保守对手更有信心
            elif self.opponent_representation.get('type') == 'unpredictable':
                return 0.3  # 对不可预测对手信心低
            else:
                return 0.6  # 对其他类型适中
        else:
            return 0.5  # 默认置信度

def simulate_game_with_opponent_modeling():
    """
    模拟一个使用对手建模的游戏场景
    """
    print("=== 在线对手建模与决策系统集成演示 ===\n")
    
    # 创建在线对手建模器
    print("创建在线对手建模器...")
    modeler = OnlineOpponentModeler(window_size=10, representation_dim=32)
    
    # 创建决策系统
    print("创建决策系统...")
    
    # 创建层次控制器
    high_level_policy = SimpleHighLevelPolicy()
    low_level_policy = SimpleLowLevelPolicy()
    hrl_controller = HierarchicalController(
        high_level_policy=high_level_policy,
        low_level_policy=low_level_policy,
        complexity_threshold=0.6,
        confidence_threshold=0.7,
        dynamic_scheduling=True,
        use_opponent_modeling=True
    )
    
    # 创建TransformerPolicy
    transformer_policy = TransformerPolicy(
        state_shape=(54,),
        action_shape=(10,),
        hidden_dim=64,
        num_heads=2,
        num_layers=2,
        ff_dim=128,
        use_opponent_modeling=True,
        opponent_dim=32
    )
    
    # 模拟对手行为轨迹
    opponent_id = "player_1"
    
    # 模拟不同阶段的对手行为
    print("\n=== 阶段1: 初识对手 ===")
    
    # 一开始的随机行为
    print("对手行为尚不明确，进行初步观察...")
    initial_actions = [3, 5, 2, 4, 1]
    for action in initial_actions:
        modeler.update(opponent_id, action)
        print(f"观察到对手行为: {action}")
    
    # 获取初步对手表示
    initial_representation = modeler.get_model(opponent_id, include_labels=True)
    print(f"\n初步对手分析:")
    print(f"- 类型: {initial_representation['type']}")
    print(f"- 跟踪动作数: {initial_representation['tracking_time']}")
    
    # 设置对手表示到决策系统
    hrl_controller.set_opponent_representation(initial_representation)
    transformer_policy.set_opponent_representation(initial_representation)
    
    # 模拟决策
    game_state = GameState(
        player_id="self", 
        hand_cards=[1, 5, 8, 12, 15, 20, 30], 
        history=[(opponent_id, [3]), ("self", [5])]
    )
    legal_actions = [1, 5, 8, 12, 15, 20, 30]
    
    # HRL决策
    print("\n使用层次控制器进行决策...")
    start_time = time.time()
    action, decision_info = hrl_controller.decide(game_state.to_tensor(), legal_actions)
    end_time = time.time()
    
    print(f"- 选择动作: {action}")
    print(f"- 决策模式: {decision_info['mode']}")
    print(f"- 复杂度评分: {decision_info['complexity']:.2f}")
    print(f"- 置信度评分: {decision_info['confidence']:.2f}")
    print(f"- 决策时间: {(end_time - start_time) * 1000:.2f}ms")
    
    # TransformerPolicy决策
    print("\n使用Transformer策略进行决策...")
    start_time = time.time()
    policy, value = transformer_policy.predict(game_state.to_tensor())
    end_time = time.time()
    
    # 找出最高概率的动作
    max_prob_idx = np.argmax(policy[:len(legal_actions)])
    
    print(f"- 最高概率动作: {legal_actions[max_prob_idx]}")
    print(f"- 概率: {policy[max_prob_idx]:.2f}")
    print(f"- 状态价值: {value:.2f}")
    print(f"- 决策时间: {(end_time - start_time) * 1000:.2f}ms")
    
    print("\n=== 阶段2: 对手模式明显 ===")
    
    # 模拟更多对手行为，显示出特定模式
    print("对手展现出明显的行为模式...")
    pattern_actions = [1, 1, 1, 2, 1, 1, 3, 1, 1]
    for action in pattern_actions:
        modeler.update(opponent_id, action)
        print(f"观察到对手行为: {action}")
    
    # 获取更新后的对手表示
    updated_representation = modeler.get_model(opponent_id, include_labels=True)
    print(f"\n更新后的对手分析:")
    print(f"- 类型: {updated_representation['type']}")
    print(f"- 跟踪动作数: {updated_representation['tracking_time']}")
    
    # 更新决策系统中的对手表示
    hrl_controller.set_opponent_representation(updated_representation)
    transformer_policy.set_opponent_representation(updated_representation)
    
    # 再次模拟决策
    game_state = GameState(
        player_id="self", 
        hand_cards=[1, 5, 8, 12, 15, 20, 30], 
        history=[(opponent_id, [1]), ("self", [8]), (opponent_id, [1])]
    )
    
    # HRL决策
    print("\n使用层次控制器进行决策...")
    start_time = time.time()
    action, decision_info = hrl_controller.decide(game_state.to_tensor(), legal_actions)
    end_time = time.time()
    
    print(f"- 选择动作: {action}")
    print(f"- 决策模式: {decision_info['mode']}")
    print(f"- 复杂度评分: {decision_info['complexity']:.2f}")
    print(f"- 置信度评分: {decision_info['confidence']:.2f}")
    print(f"- 决策时间: {(end_time - start_time) * 1000:.2f}ms")
    
    # TransformerPolicy决策
    print("\n使用Transformer策略进行决策...")
    start_time = time.time()
    policy, value, explanation = transformer_policy.predict(game_state.to_tensor(), explain=True)
    end_time = time.time()
    
    # 找出最高概率的动作
    max_prob_idx = np.argmax(policy[:len(legal_actions)])
    
    print(f"- 最高概率动作: {legal_actions[max_prob_idx]}")
    print(f"- 概率: {policy[max_prob_idx]:.2f}")
    print(f"- 状态价值: {value:.2f}")
    print(f"- 决策时间: {(end_time - start_time) * 1000:.2f}ms")
    
    # 输出对手影响信息（如果有）
    if 'opponent_influence' in explanation:
        print(f"- 对手信息影响: 使用了对手表示，表示范数: {explanation['opponent_influence']['representation_norm']:.2f}")
    
    print("\n=== 阶段3: 上下文相关行为 ===")
    
    # 模拟特定上下文下的行为
    context_key = "winning"
    print(f"在'{context_key}'上下文中观察对手...")
    context_actions = [5, 5, 4, 5, 5]
    for action in context_actions:
        modeler.context_aware_update(opponent_id, action, context_key)
        print(f"观察到对手在'{context_key}'情境下的行为: {action}")
    
    # 获取上下文相关的对手表示
    context_representation = modeler.get_model(opponent_id, context_key=context_key, include_labels=True)
    print(f"\n上下文相关的对手分析:")
    print(f"- 类型: {context_representation['type']}")
    print(f"- 跟踪动作数: {context_representation['tracking_time']}")
    
    # 更新决策系统中的对手表示
    hrl_controller.set_opponent_representation(context_representation)
    transformer_policy.set_opponent_representation(context_representation)
    
    # 在上下文情境下决策
    game_state = GameState(
        player_id="self", 
        hand_cards=[1, 5, 8, 12, 15, 20, 30], 
        history=[(opponent_id, [5]), ("self", [20]), (opponent_id, [5])]
    )
    
    # HRL决策
    print("\n使用层次控制器在特定上下文进行决策...")
    start_time = time.time()
    action, decision_info = hrl_controller.decide(game_state.to_tensor(), legal_actions)
    end_time = time.time()
    
    print(f"- 选择动作: {action}")
    print(f"- 决策模式: {decision_info['mode']}")
    print(f"- 复杂度评分: {decision_info['complexity']:.2f}")
    print(f"- 置信度评分: {decision_info['confidence']:.2f}")
    print(f"- 决策时间: {(end_time - start_time) * 1000:.2f}ms")
    
    # 获取HRL统计信息
    stats = hrl_controller.get_stats()
    print("\n层次控制器统计信息:")
    print(f"- 总调用次数: {stats['calls']['total']}")
    print(f"- 高层策略使用率: {stats['ratios']['high_level']:.2f}")
    print(f"- 低层策略使用率: {stats['ratios']['low_level']:.2f}")
    print(f"- 直接决策使用率: {stats['ratios']['direct']:.2f}")
    print(f"- 当前对手类型: {stats['opponent_modeling']['current_opponent_type']}")
    
    print("\n演示完成!")

if __name__ == "__main__":
    simulate_game_with_opponent_modeling() 