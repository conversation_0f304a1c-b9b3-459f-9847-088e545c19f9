"""
牌型模块

定义斗地主游戏中的牌型及其比较规则。
"""
from enum import Enum, auto
from typing import List, Dict, Any, Optional, Tuple, Set
from collections import Counter

from cardgame_ai.games.doudizhu.card import Card, CardRank
from cardgame_ai.games.doudizhu.action import BidAction, GrabAction


class CardGroupType(Enum):
    """
    牌型枚举

    定义斗地主游戏中的所有牌型。
    """
    PASS = auto()             # 不出
    SINGLE = auto()           # 单张
    PAIR = auto()             # 对子
    TRIO = auto()             # 三张
    TRIO_WITH_SINGLE = auto() # 三带一
    TRIO_WITH_PAIR = auto()   # 三带二
    STRAIGHT = auto()         # 顺子（五张或更多的连续单牌）
    STRAIGHT_PAIR = auto()    # 连对（三对或更多的连续对牌）
    AIRPLANE = auto()         # 飞机（两个或更多的连续三张）
    AIRPLANE_WITH_SINGLE = auto()  # 飞机带单牌
    AIRPLANE_WITH_PAIR = auto()    # 飞机带对子
    FOUR_WITH_TWO_SINGLE = auto()  # 四带二单
    FOUR_WITH_TWO_PAIR = auto()    # 四带二对
    BOMB = auto()             # 炸弹
    ROCKET = auto()           # 火箭（双王）


class CardGroup:
    """
    牌型类

    表示一组牌及其牌型。
    """

    def __init__(self, cards: List[Card], card_type: Optional[CardGroupType] = None):
        """
        初始化牌型

        Args:
            cards (List[Card]): 牌列表
            card_type (Optional[CardGroupType], optional): 牌型. Defaults to None.
        """
        self.cards = sorted(cards) if cards else []
        self.card_type = card_type if card_type else self._get_type()
        self.main_rank = self._get_main_rank()

    def _get_type(self) -> CardGroupType:
        """
        获取牌型

        Returns:
            CardGroupType: 牌型
        """
        # 空牌，表示不出
        if not self.cards:
            return CardGroupType.PASS

        # 获取每个点数的数量
        counter = Counter([card.rank for card in self.cards])
        rank_count = sorted(counter.items(), key=lambda x: (x[1], x[0]), reverse=True)

        # 单张
        if len(self.cards) == 1:
            return CardGroupType.SINGLE

        # 对子
        if len(self.cards) == 2 and rank_count[0][1] == 2:
            return CardGroupType.PAIR

        # 三张
        if len(self.cards) == 3 and rank_count[0][1] == 3:
            return CardGroupType.TRIO

        # 三带一
        if len(self.cards) == 4 and rank_count[0][1] == 3:
            return CardGroupType.TRIO_WITH_SINGLE

        # 三带二
        if len(self.cards) == 5 and rank_count[0][1] == 3 and rank_count[1][1] == 2:
            return CardGroupType.TRIO_WITH_PAIR

        # 炸弹
        if len(self.cards) == 4 and rank_count[0][1] == 4:
            return CardGroupType.BOMB

        # 火箭
        if len(self.cards) == 2 and {card.rank for card in self.cards} == {CardRank.SMALL_JOKER, CardRank.BIG_JOKER}:
            return CardGroupType.ROCKET

        # 四带二单
        if len(self.cards) == 6 and rank_count[0][1] == 4:
            return CardGroupType.FOUR_WITH_TWO_SINGLE

        # 四带二对
        if len(self.cards) == 8 and rank_count[0][1] == 4 and rank_count[1][1] == 2 and rank_count[2][1] == 2:
            return CardGroupType.FOUR_WITH_TWO_PAIR

        # 顺子（五张或更多的连续单牌）
        if self._is_straight():
            return CardGroupType.STRAIGHT

        # 连对（三对或更多的连续对牌）
        if self._is_straight_pair():
            return CardGroupType.STRAIGHT_PAIR

        # 飞机（两个或更多的连续三张）
        if self._is_airplane():
            return CardGroupType.AIRPLANE

        # 飞机带单牌
        if self._is_airplane_with_single():
            return CardGroupType.AIRPLANE_WITH_SINGLE

        # 飞机带对子
        if self._is_airplane_with_pair():
            return CardGroupType.AIRPLANE_WITH_PAIR

        # 无效牌型
        return CardGroupType.PASS

    def _is_straight(self) -> bool:
        """
        判断是否为顺子

        Returns:
            bool: 是否为顺子
        """
        # 顺子至少5张牌
        if len(self.cards) < 5:
            return False

        # 获取点数集合
        ranks = {card.rank for card in self.cards}

        # 顺子不能包含2和王
        if any(rank >= CardRank.TWO for rank in ranks):
            return False

        # 检查是否有重复点数
        if len(ranks) != len(self.cards):
            return False

        # 检查是否连续
        min_rank = min(ranks)
        max_rank = max(ranks)
        return max_rank - min_rank + 1 == len(ranks) and len(ranks) == len(self.cards)

    def _is_straight_pair(self) -> bool:
        """
        判断是否为连对

        Returns:
            bool: 是否为连对
        """
        # 连对至少3对，即6张牌
        if len(self.cards) < 6 or len(self.cards) % 2 != 0:
            return False

        # 获取每个点数的数量
        counter = Counter([card.rank for card in self.cards])

        # 检查是否每个点数都有2张牌
        if not all(count == 2 for count in counter.values()):
            return False

        # 获取点数集合
        ranks = list(counter.keys())

        # 连对不能包含2和王
        if any(rank >= CardRank.TWO for rank in ranks):
            return False

        # 检查是否连续
        ranks.sort()
        for i in range(1, len(ranks)):
            if ranks[i] - ranks[i-1] != 1:
                return False

        return True

    def _is_airplane(self) -> bool:
        """
        判断是否为飞机（不带牌）

        Returns:
            bool: 是否为飞机
        """
        # 飞机至少2个连续三张，即6张牌
        if len(self.cards) < 6 or len(self.cards) % 3 != 0:
            return False

        # 获取每个点数的数量
        counter = Counter([card.rank for card in self.cards])

        # 检查是否每个点数都有3张牌
        if not all(count == 3 for count in counter.values()):
            return False

        # 获取点数集合
        ranks = list(counter.keys())

        # 飞机不能包含2和王
        if any(rank >= CardRank.TWO for rank in ranks):
            return False

        # 检查是否连续
        ranks.sort()
        for i in range(1, len(ranks)):
            if ranks[i] - ranks[i-1] != 1:
                return False

        return True

    def _is_airplane_with_single(self) -> bool:
        """
        判断是否为飞机带单牌

        Returns:
            bool: 是否为飞机带单牌
        """
        # 获取每个点数的数量
        counter = Counter([card.rank for card in self.cards])

        # 获取三张的点数
        trio_ranks = [rank for rank, count in counter.items() if count == 3]

        # 飞机至少2个连续三张
        if len(trio_ranks) < 2:
            return False

        # 检查三张的点数是否连续
        trio_ranks.sort()
        for i in range(1, len(trio_ranks)):
            if trio_ranks[i] - trio_ranks[i-1] != 1:
                return False

        # 飞机不能包含2和王
        if any(rank >= CardRank.TWO for rank in trio_ranks):
            return False

        # 检查单牌数量是否等于三张数量
        single_count = sum(1 for count in counter.values() if count == 1)
        trio_count = len(trio_ranks)

        return single_count == trio_count and len(self.cards) == trio_count * 4

    def _is_airplane_with_pair(self) -> bool:
        """
        判断是否为飞机带对子

        Returns:
            bool: 是否为飞机带对子
        """
        # 获取每个点数的数量
        counter = Counter([card.rank for card in self.cards])

        # 获取三张的点数
        trio_ranks = [rank for rank, count in counter.items() if count == 3]

        # 飞机至少2个连续三张
        if len(trio_ranks) < 2:
            return False

        # 检查三张的点数是否连续
        trio_ranks.sort()
        for i in range(1, len(trio_ranks)):
            if trio_ranks[i] - trio_ranks[i-1] != 1:
                return False

        # 飞机不能包含2和王
        if any(rank >= CardRank.TWO for rank in trio_ranks):
            return False

        # 检查对子数量是否等于三张数量
        pair_count = sum(1 for count in counter.values() if count == 2)
        trio_count = len(trio_ranks)

        return pair_count == trio_count and len(self.cards) == trio_count * 5

    def _get_main_rank(self) -> Optional[CardRank]:
        """
        获取主要点数

        对于大部分牌型，主要点数是出现次数最多的点数。
        对于顺子和连对，主要点数是最大的点数。

        Returns:
            Optional[CardRank]: 主要点数
        """
        if not self.cards:
            return None

        if self.card_type == CardGroupType.PASS:
            return None

        if self.card_type in [CardGroupType.STRAIGHT, CardGroupType.STRAIGHT_PAIR]:
            # 顺子和连对的主要点数是最大的点数
            return max(card.rank for card in self.cards)

        if self.card_type in [CardGroupType.AIRPLANE, CardGroupType.AIRPLANE_WITH_SINGLE, CardGroupType.AIRPLANE_WITH_PAIR]:
            # 飞机的主要点数是最大的三张点数
            counter = Counter([card.rank for card in self.cards])
            trio_ranks = [rank for rank, count in counter.items() if count == 3]
            return max(trio_ranks)

        # 其他牌型，主要点数是出现次数最多的点数
        counter = Counter([card.rank for card in self.cards])
        return max(counter.items(), key=lambda x: (x[1], x[0]))[0]

    def is_valid(self) -> bool:
        """
        判断牌型是否有效

        Returns:
            bool: 是否有效
        """
        return self.card_type != CardGroupType.PASS or not self.cards

    def can_beat(self, other: 'CardGroup') -> bool:
        """
        判断是否能够打过另一个牌型

        Args:
            other (CardGroup): 另一个牌型

        Returns:
            bool: 是否能够打过
        """
        # 如果对方不出，任何牌都可以打出
        if other is None or not other.cards or other.card_type == CardGroupType.PASS:
            return True

        # 火箭能打过任何牌
        if self.card_type == CardGroupType.ROCKET:
            return True

        # 炸弹能打过除火箭外的任何牌
        if self.card_type == CardGroupType.BOMB and other.card_type != CardGroupType.ROCKET:
            # 炸弹之间比较大小
            if other.card_type == CardGroupType.BOMB:
                return self.main_rank > other.main_rank
            return True

        # 其他牌型只能打过相同牌型且点数更大的牌
        if self.card_type == other.card_type:
            # 对于相同牌型，比较主要点数
            return self.main_rank > other.main_rank

        return False

    def __eq__(self, other: 'CardGroup') -> bool:
        """
        比较两个牌型是否相等

        Args:
            other (CardGroup): 另一个牌型

        Returns:
            bool: 是否相等
        """
        if not isinstance(other, CardGroup):
            return False

        return (self.card_type == other.card_type and
                self.main_rank == other.main_rank and
                len(self.cards) == len(other.cards))

    def __hash__(self) -> int:
        """
        获取哈希值，使CardGroup可以作为字典键使用

        该方法实现与__eq__方法一致的哈希逻辑，确保相等的对象具有相同的哈希值

        Returns:
            int: 哈希值
        """
        return hash((self.card_type, self.main_rank, len(self.cards)))

    def __str__(self) -> str:
        """
        转换为字符串表示

        Returns:
            str: 字符串表示
        """
        if not self.cards:
            return "不出"

        return f"{self.card_type.name}: {' '.join(str(card) for card in self.cards)}"

    def __repr__(self) -> str:
        """
        转换为详细字符串表示

        Returns:
            str: 详细字符串表示
        """
        return f"CardGroup(type={self.card_type.name}, main_rank={self.main_rank}, cards={self.cards})"

    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典表示

        Returns:
            Dict[str, Any]: 字典表示
        """
        return {
            'cards': [card.to_dict() for card in self.cards],
            'card_type': self.card_type.name if self.card_type else None,
            'main_rank': int(self.main_rank) if self.main_rank is not None else None
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CardGroup':
        """
        从字典创建牌型

        Args:
            data (Dict[str, Any]): 字典表示

        Returns:
            CardGroup: 牌型对象
        """
        cards = [Card.from_dict(card_data) for card_data in data['cards']]
        card_type = CardGroupType[data['card_type']] if data['card_type'] else None
        group = cls(cards, card_type)
        if data['main_rank'] is not None:
            group.main_rank = CardRank(data['main_rank'])
        return group

    @classmethod
    def from_cards(cls, cards: List[Card]) -> 'CardGroup':
        """
        从牌列表创建牌型

        Args:
            cards (List[Card]): 牌列表

        Returns:
            CardGroup: 牌型对象
        """
        return cls(cards)

    @classmethod
    def from_string(cls, card_str: str) -> 'CardGroup':
        """
        从字符串创建牌型

        Args:
            card_str (str): 字符串表示，如"H3 H4 H5 H6 H7"表示红桃3到7的顺子

        Returns:
            CardGroup: 牌型对象

        Raises:
            ValueError: 无效的牌字符串
        """
        if not card_str or card_str.lower() == 'pass':
            return cls([])

        cards = [Card.from_string(s) for s in card_str.split()]
        return cls(cards)

    def to_index(self) -> int:
        """
        将CardGroup转换为唯一的动作索引

        这个方法为每个不同的出牌组合生成唯一的索引，用于EfficientZero的动作映射。
        使用分层编码策略：基础偏移 + 牌型特定编码

        Returns:
            int: 唯一的动作索引
        """
        # 牌型基础偏移值（确保不同牌型有不同的索引范围）
        TYPE_OFFSETS = {
            CardGroupType.PASS: 0,
            CardGroupType.SINGLE: 100,
            CardGroupType.PAIR: 200,
            CardGroupType.TRIO: 300,
            CardGroupType.TRIO_WITH_SINGLE: 400,
            CardGroupType.TRIO_WITH_PAIR: 500,
            CardGroupType.STRAIGHT: 600,
            CardGroupType.STRAIGHT_PAIR: 700,
            CardGroupType.AIRPLANE: 800,
            CardGroupType.AIRPLANE_WITH_SINGLE: 900,
            CardGroupType.AIRPLANE_WITH_PAIR: 1000,
            CardGroupType.FOUR_WITH_TWO_SINGLE: 1100,
            CardGroupType.FOUR_WITH_TWO_PAIR: 1200,
            CardGroupType.BOMB: 1300,
            CardGroupType.ROCKET: 1400,
        }

        base_offset = TYPE_OFFSETS.get(self.card_type, 0)

        # 对于PASS动作，直接返回基础偏移
        if self.card_type == CardGroupType.PASS:
            return base_offset

        # 对于其他牌型，生成基于牌组合的唯一标识
        if not self.cards:
            return base_offset

        # 使用主要点数作为主要标识符
        main_rank_value = self.main_rank.value if self.main_rank else 0

        # 对于简单牌型（单张、对子、三张），主要点数足够区分
        if self.card_type in [CardGroupType.SINGLE, CardGroupType.PAIR, CardGroupType.TRIO]:
            return base_offset + main_rank_value

        # 对于复杂牌型，使用牌组合的哈希值作为额外标识
        cards_signature = hash(tuple(sorted([card.rank.value * 10 + card.suit.value for card in self.cards])))
        # 将哈希值限制在合理范围内，避免索引过大
        hash_component = abs(cards_signature) % 50

        return base_offset + main_rank_value + hash_component


def get_card_group_type(card_group: CardGroup) -> CardGroupType:
    """
    获取牌组类型

    Args:
        card_group: 牌组

    Returns:
        牌型
    """
    return card_group.card_type
