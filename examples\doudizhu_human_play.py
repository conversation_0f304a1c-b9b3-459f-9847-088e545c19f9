"""
斗地主人机对战示例

演示如何使用斗地主游戏环境进行人机对战。
"""
import random
from typing import List, Optional

from cardgame_ai.core.agent import RandomAgent, HumanAgent
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.games.doudizhu.card import Card
from cardgame_ai.games.doudizhu.card_group import CardGroup


class DouDizhuHumanAgent(HumanAgent):
    """
    斗地主人类代理
    
    扩展HumanAgent，提供更友好的交互界面。
    """
    
    def act(self, state, legal_actions, is_training=False):
        """
        从用户输入获取动作
        
        Args:
            state: 游戏状态或观察
            legal_actions: 合法动作列表
            is_training: 是否为训练模式
            
        Returns:
            动作
        """
        # 如果state是观察（numpy数组），则从环境中获取状态
        if hasattr(state, 'shape'):
            state = self.env.state
        
        # 显示当前状态
        print(f"\n当前玩家: 玩家{state.current_player}({['农民', '地主'][state.current_player == state.landlord]})")
        print(f"您的手牌: {' '.join(str(card) for card in state.hands[state.current_player])}")
        
        if state.last_move and state.last_move.cards:
            print(f"上一手牌: {state.last_move} (玩家{state.last_player})")
        
        # 显示合法动作
        print("\n合法动作:")
        for i, action in enumerate(legal_actions):
            print(f"{i}: {action}")
        
        # 获取用户输入
        while True:
            try:
                choice = input("\n请选择动作 (输入序号或牌，如'H3 H4 H5'，输入'pass'不出): ")
                
                # 如果输入序号
                if choice.isdigit():
                    idx = int(choice)
                    if 0 <= idx < len(legal_actions):
                        return legal_actions[idx]
                    else:
                        print(f"无效的选择，请输入0-{len(legal_actions)-1}之间的数字")
                # 如果输入牌
                else:
                    # 不出
                    if choice.lower() == 'pass':
                        for action in legal_actions:
                            if not action.cards:
                                return action
                        print("当前不能不出牌")
                    # 输入具体的牌
                    else:
                        try:
                            # 解析输入的牌
                            cards_str = choice.split()
                            cards = []
                            for card_str in cards_str:
                                # 查找手牌中匹配的牌
                                found = False
                                for card in state.hands[state.current_player]:
                                    if str(card) == card_str:
                                        cards.append(card)
                                        found = True
                                        break
                                if not found:
                                    raise ValueError(f"您的手牌中没有{card_str}")
                            
                            # 创建牌组
                            action = CardGroup(cards)
                            
                            # 检查是否是合法动作
                            for legal_action in legal_actions:
                                if action == legal_action:
                                    return legal_action
                            
                            print("不是合法的动作")
                        except Exception as e:
                            print(f"输入错误: {e}")
            except ValueError:
                print("请输入有效的数字或牌")


def play_game(env: DouDizhuEnvironment, human_player_idx: Optional[int] = None):
    """
    进行一局游戏
    
    Args:
        env (DouDizhuEnvironment): 游戏环境
        human_player_idx (Optional[int], optional): 人类玩家索引. Defaults to None.
    """
    # 重置环境
    state = env.reset()
    
    # 如果没有指定人类玩家索引，随机选择
    if human_player_idx is None:
        human_player_idx = random.randint(0, 2)
    
    # 创建代理
    agents = [RandomAgent(seed=i) for i in range(3)]
    human_agent = DouDizhuHumanAgent()
    human_agent.env = env  # 设置环境，便于访问状态
    agents[human_player_idx] = human_agent
    
    # 显示初始状态
    env.render()
    print(f"您是玩家{human_player_idx}({['农民', '地主'][human_player_idx == state.landlord]})")
    print("-" * 50)
    
    # 游戏循环
    done = False
    while not done:
        # 获取当前玩家
        current_player = state.current_player
        
        # 获取合法动作
        legal_actions = env.get_legal_actions(state)
        
        # 获取观察
        observation = env.get_observation(state)
        
        # 选择动作
        action = agents[current_player].act(state if current_player == human_player_idx else observation, legal_actions)
        
        if current_player != human_player_idx:
            print(f"\n玩家{current_player}({['农民', '地主'][current_player == state.landlord]})出牌: {action}")
        
        # 执行动作
        state, reward, done, info = env.step(action)
        
        if current_player != human_player_idx:
            env.render()
            print("-" * 50)
    
    # 显示结果
    winner = next(i for i, hand in enumerate(state.hands) if not hand)
    print(f"\n游戏结束，玩家{winner}({['农民', '地主'][winner == state.landlord]})获胜!")
    
    payoffs = env.get_payoffs(state)
    print(f"收益: {payoffs}")
    
    if winner == human_player_idx:
        print("恭喜，您赢了!")
    else:
        print("很遗憾，您输了!")


def main():
    """
    主函数
    """
    # 创建环境
    env = DouDizhuEnvironment()
    
    # 询问玩家选择角色
    while True:
        try:
            choice = input("请选择角色 (0: 随机, 1: 地主, 2: 农民): ")
            if choice == '0':
                human_player_idx = None
                break
            elif choice == '1':
                human_player_idx = 0  # 假设地主总是玩家0
                break
            elif choice == '2':
                human_player_idx = 1  # 假设农民是玩家1和2
                break
            else:
                print("无效的选择，请重新输入")
        except ValueError:
            print("请输入有效的数字")
    
    # 进行游戏
    play_game(env, human_player_idx)
    
    # 询问是否再玩一局
    while True:
        try:
            choice = input("\n是否再玩一局? (y/n): ")
            if choice.lower() == 'y':
                main()
                break
            elif choice.lower() == 'n':
                print("谢谢游玩!")
                break
            else:
                print("无效的选择，请重新输入")
        except ValueError:
            print("请输入有效的字符")


if __name__ == "__main__":
    main()
