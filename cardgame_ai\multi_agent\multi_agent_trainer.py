"""
多智能体训练器

实现多智能体协作训练和竞争训练机制，扩展基础训练器功能，
支持角色分配、合作机制、对抗策略的训练优化。
"""

import os
import time
import logging
import numpy as np
from typing import Dict, List, Tuple, Any, Optional, Union, Set

from cardgame_ai.core.base import Experience, Batch
from cardgame_ai.core.agent import Agent
from cardgame_ai.core.environment import MultiAgentEnvironment
from cardgame_ai.core.policy import Policy
from cardgame_ai.training.trainer import AdvancedTrainer
from cardgame_ai.training.self_play import MultiAgentSelfPlay
from cardgame_ai.multi_agent.multi_agent_framework import (
    RoleManager, CooperativeMechanism, AdversarialMechanism,
    FarmerCooperation, LandlordStrategy
)


class MultiAgentTrainer:
    """多智能体训练器

    扩展基本训练器，支持多智能体协作和竞争训练，特别适用于斗地主等多人游戏。
    """

    def __init__(
        self,
        env: MultiAgentEnvironment,
        base_trainer: AdvancedTrainer = None,
        cooperative_mechanism: CooperativeMechanism = None,
        adversarial_mechanism: AdversarialMechanism = None,
        log_level: int = logging.INFO
    ):
        """初始化多智能体训练器

        Args:
            env: 多智能体环境
            base_trainer: 基础训练器，用于单智能体训练部分
            cooperative_mechanism: 合作机制，默认为None
            adversarial_mechanism: 对抗机制，默认为None
            log_level: 日志级别
        """
        self.env = env
        self.base_trainer = base_trainer or AdvancedTrainer()

        # 设置日志
        self.logger = logging.getLogger("MultiAgentTrainer")
        self.logger.setLevel(log_level)
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)

        # 角色管理器
        self.role_manager = RoleManager(env)

        # 合作与对抗机制
        self.cooperative_mechanism = cooperative_mechanism or FarmerCooperation(self.role_manager)
        self.adversarial_mechanism = adversarial_mechanism or LandlordStrategy(self.role_manager)

        # 自我对弈系统
        self.self_play = MultiAgentSelfPlay()

    def train_cooperative(
        self,
        agents: Dict[str, Agent],
        num_episodes: int,
        save_dir: str = None,
        eval_interval: int = 100,
        team_reward: bool = True,
        share_experience: bool = True,
        cooperation_weight: float = 0.5,
        **kwargs
    ) -> Dict[str, Any]:
        """协作训练方法

        训练同一队伍的智能体协同工作，如斗地主中的农民合作对抗地主。

        Args:
            agents: 智能体字典，键为智能体ID (如 "0", "1", "2")
            num_episodes: 训练回合数
            save_dir: 模型和经验保存目录
            eval_interval: 评估间隔回合数
            team_reward: 是否使用团队奖励
            share_experience: 是否共享经验数据
            cooperation_weight: 合作奖励权重
            **kwargs: 其他参数

        Returns:
            训练统计数据
        """
        start_time = time.time()
        self.logger.info(f"开始协作训练，回合数：{num_episodes}")

        # 确保保存目录存在
        if save_dir and not os.path.exists(save_dir):
            os.makedirs(save_dir)

        # 训练统计数据
        stats = {
            "rewards": [],
            "win_rates": {},
            "team_rewards": [],
            "cooperation_metrics": []
        }

        # 协作训练主循环
        for episode in range(num_episodes):
            # 游戏初始化
            state = self.env.reset()
            game_over = False
            episode_rewards = {agent_id: 0.0 for agent_id in agents}
            episode_cooperative_actions = {agent_id: 0 for agent_id in agents}
            episode_history = []

            # 分配角色
            game_state = {"state": state}  # 这里可能需要根据实际环境补充游戏状态信息
            roles = self.role_manager.assign_roles(game_state)

            # 创建队伍信息
            teams = {}
            for agent_id, role in roles.items():
                if role not in teams:
                    teams[role] = []
                teams[role].append(agent_id)

            # 游戏循环
            while not game_over:
                player_id = str(state.get_player_id())

                if player_id in agents:
                    agent = agents[player_id]
                    legal_actions = self.env.get_legal_actions(state)
                    observation = self.env.get_observation(state)

                    # 获取所有智能体的观察
                    all_observations = {}
                    all_legal_actions = {}
                    for agent_id, ag in agents.items():
                        all_observations[agent_id] = self.env.get_observation(state, int(agent_id))
                        all_legal_actions[agent_id] = self.env.get_legal_actions(state, int(agent_id))

                    # 确定是否需要协作决策
                    role = self.role_manager.get_role(player_id)
                    action = None

                    if role == "farmer":  # 农民协作
                        # 尝试协调行动
                        suggested_action = self.cooperative_mechanism.coordinate_actions(
                            player_id, all_observations, all_legal_actions
                        )

                        if suggested_action is not None:
                            action = suggested_action
                            episode_cooperative_actions[player_id] += 1

                    # 如果没有协作行动，使用智能体自己的策略
                    if action is None:
                        action = agent.act(observation, legal_actions, is_training=True)

                    # 执行动作
                    next_state, reward, game_over, info = self.env.step(action)

                    # 创建经验
                    experience = Experience(state, action, reward, next_state, game_over, info)

                    # 更新训练数据
                    agent.train(experience)
                    episode_rewards[player_id] += reward

                    # 记录历史
                    episode_history.append({
                        'player_id': player_id,
                        'state': state,
                        'action': action,
                        'reward': reward,
                        'next_state': next_state,
                        'done': game_over,
                        'info': info
                    })

                    # 共享信息（可选）
                    if share_experience and role == "farmer":
                        shared_info = self.cooperative_mechanism.share_information(
                            player_id, all_observations
                        )

                        # 与队友共享经验
                        teammates = self.role_manager.get_teammates(player_id)
                        for teammate_id in teammates:
                            if teammate_id in agents:
                                # 这里可以实现复杂的经验共享机制
                                teammate_agent = agents[teammate_id]
                                # 简单的经验共享示例
                                teammate_agent.train(experience)

                    state = next_state
                else:
                    # 如果当前玩家不是我们训练的智能体，则使用随机策略
                    legal_actions = self.env.get_legal_actions(state)
                    action = np.random.choice(legal_actions)
                    next_state, reward, game_over, info = self.env.step(action)
                    state = next_state

            # 游戏结束后的处理
            # 获取团队奖励
            payoffs = self.env.get_payoffs(state)

            if team_reward:
                # 应用团队奖励
                for agent_id, role in roles.items():
                    if agent_id in agents:
                        team_members = teams[role]
                        team_reward = sum(payoffs[int(member)] for member in team_members) / len(team_members)

                        # 混合个人奖励和团队奖励
                        mixed_reward = (1 - cooperation_weight) * episode_rewards[agent_id] + \
                                      cooperation_weight * team_reward

                        # 更新统计数据
                        episode_rewards[agent_id] = mixed_reward

            # 记录统计数据
            stats["rewards"].append(episode_rewards)

            # 计算合作指标
            cooperation_metric = sum(episode_cooperative_actions.values()) / len(episode_cooperative_actions) \
                if episode_cooperative_actions else 0
            stats["cooperation_metrics"].append(cooperation_metric)

            # 记录团队奖励
            team_rewards = {}
            for role, team_members in teams.items():
                team_rewards[role] = sum(payoffs[int(member)] for member in team_members if member in agents) / \
                                    len([m for m in team_members if m in agents]) if team_members else 0
            stats["team_rewards"].append(team_rewards)

            # 定期评估和保存
            if (episode + 1) % eval_interval == 0 or episode == num_episodes - 1:
                # 评估性能
                eval_stats = self.evaluate(agents, 50)

                for role in teams:
                    if role not in stats["win_rates"]:
                        stats["win_rates"][role] = []
                    stats["win_rates"][role].append(eval_stats["win_rates"].get(role, 0))

                self.logger.info(
                    f"回合: {episode + 1}/{num_episodes} | "
                    f"平均奖励: {sum(episode_rewards.values()) / len(episode_rewards):.4f} | "
                    f"合作行动比例: {cooperation_metric:.4f} | "
                    f"农民胜率: {eval_stats['win_rates'].get('farmer', 0):.4f} | "
                    f"地主胜率: {eval_stats['win_rates'].get('landlord', 0):.4f}"
                )

                # 保存模型
                if save_dir:
                    for agent_id, agent in agents.items():
                        agent.save(os.path.join(save_dir, f"agent_{agent_id}_{episode + 1}"))

        total_time = time.time() - start_time
        self.logger.info(
            f"协作训练完成 | 总回合数: {num_episodes} | "
            f"总用时: {total_time:.2f}s | "
            f"平均每回合时间: {total_time / num_episodes:.2f}s"
        )

        return stats

    def train_competitive(
        self,
        agents: Dict[str, Agent],
        num_episodes: int,
        save_dir: str = None,
        eval_interval: int = 100,
        adaptive_difficulty: bool = True,
        opponent_modeling: bool = True,
        **kwargs
    ) -> Dict[str, Any]:
        """竞争训练方法

        训练对立的智能体相互竞争，如斗地主中地主对抗农民。

        Args:
            agents: 智能体字典，键为智能体ID
            num_episodes: 训练回合数
            save_dir: 模型和经验保存目录
            eval_interval: 评估间隔回合数
            adaptive_difficulty: 是否使用自适应难度
            opponent_modeling: 是否使用对手建模
            **kwargs: 其他参数

        Returns:
            训练统计数据
        """
        start_time = time.time()
        self.logger.info(f"开始竞争训练，回合数：{num_episodes}")

        # 确保保存目录存在
        if save_dir and not os.path.exists(save_dir):
            os.makedirs(save_dir)

        # 训练统计数据
        stats = {
            "rewards": [],
            "win_rates": {},
            "exploitation_metrics": []
        }

        # 对手行为历史记录
        opponent_history = {agent_id: [] for agent_id in agents}

        # 竞争训练主循环
        for episode in range(num_episodes):
            # 游戏初始化
            state = self.env.reset()
            game_over = False
            episode_rewards = {agent_id: 0.0 for agent_id in agents}
            episode_exploitation_actions = {agent_id: 0 for agent_id in agents}
            episode_history = []

            # 分配角色
            game_state = {"state": state}
            roles = self.role_manager.assign_roles(game_state)

            # 创建队伍信息
            teams = {}
            for agent_id, role in roles.items():
                if role not in teams:
                    teams[role] = []
                teams[role].append(agent_id)

            # 游戏循环
            while not game_over:
                player_id = str(state.get_player_id())

                if player_id in agents:
                    agent = agents[player_id]
                    legal_actions = self.env.get_legal_actions(state)
                    observation = self.env.get_observation(state)

                    # 获取所有智能体的观察
                    all_observations = {}
                    all_legal_actions = {}
                    for agent_id, ag in agents.items():
                        all_observations[agent_id] = self.env.get_observation(state, int(agent_id))
                        all_legal_actions[agent_id] = self.env.get_legal_actions(state, int(agent_id))

                    # 确定是否需要对抗决策
                    role = self.role_manager.get_role(player_id)
                    action = None

                    if role == "landlord" and opponent_modeling:  # 地主对抗策略
                        # 获取对手历史行为
                        opponents = self.role_manager.get_opponents(player_id)
                        opponents_history = {
                            opp: opponent_history.get(opp, []) for opp in opponents if opp in agents
                        }

                        # 尝试使用对抗策略
                        suggested_action = self.adversarial_mechanism.counter_strategy(
                            player_id, all_observations, all_legal_actions, opponents_history
                        )

                        if suggested_action is not None:
                            action = suggested_action
                            episode_exploitation_actions[player_id] += 1
                        else:
                            # 尝试弱点利用
                            opponent_model = {}  # 这里可以实现对手建模
                            exploited_action = self.adversarial_mechanism.exploit_weakness(
                                player_id, all_observations, opponent_model
                            )

                            if exploited_action is not None:
                                action = exploited_action
                                episode_exploitation_actions[player_id] += 1

                    # 如果没有对抗行动，使用智能体自己的策略
                    if action is None:
                        action = agent.act(observation, legal_actions, is_training=True)

                    # 执行动作
                    next_state, reward, game_over, info = self.env.step(action)

                    # 更新对手历史
                    if opponent_modeling:
                        opponent_history[player_id].append((action, reward))
                        # 限制历史长度，避免内存问题
                        if len(opponent_history[player_id]) > 1000:
                            opponent_history[player_id] = opponent_history[player_id][-1000:]

                    # 创建经验
                    experience = Experience(state, action, reward, next_state, game_over, info)

                    # 更新训练数据
                    agent.train(experience)
                    episode_rewards[player_id] += reward

                    # 记录历史
                    episode_history.append({
                        'player_id': player_id,
                        'state': state,
                        'action': action,
                        'reward': reward,
                        'next_state': next_state,
                        'done': game_over,
                        'info': info
                    })

                    state = next_state
                else:
                    # 如果当前玩家不是我们训练的智能体，则使用随机策略
                    legal_actions = self.env.get_legal_actions(state)
                    action = np.random.choice(legal_actions)
                    next_state, reward, game_over, info = self.env.step(action)
                    state = next_state

            # 游戏结束后的处理
            payoffs = self.env.get_payoffs(state)

            # 记录统计数据
            stats["rewards"].append(episode_rewards)

            # 计算对抗指标
            exploitation_metric = sum(episode_exploitation_actions.values()) / len(episode_exploitation_actions) \
                if episode_exploitation_actions else 0
            stats["exploitation_metrics"].append(exploitation_metric)

            # 自适应难度调整（可选）
            if adaptive_difficulty and episode > 0 and episode % 10 == 0:
                # 实现难度自适应逻辑
                # 例如：如果某角色胜率过高，可以增加对抗强度
                pass

            # 定期评估和保存
            if (episode + 1) % eval_interval == 0 or episode == num_episodes - 1:
                # 评估性能
                eval_stats = self.evaluate(agents, 50)

                for role in teams:
                    if role not in stats["win_rates"]:
                        stats["win_rates"][role] = []
                    stats["win_rates"][role].append(eval_stats["win_rates"].get(role, 0))

                self.logger.info(
                    f"回合: {episode + 1}/{num_episodes} | "
                    f"平均奖励: {sum(episode_rewards.values()) / len(episode_rewards):.4f} | "
                    f"对抗行动比例: {exploitation_metric:.4f} | "
                    f"农民胜率: {eval_stats['win_rates'].get('farmer', 0):.4f} | "
                    f"地主胜率: {eval_stats['win_rates'].get('landlord', 0):.4f}"
                )

                # 保存模型
                if save_dir:
                    for agent_id, agent in agents.items():
                        agent.save(os.path.join(save_dir, f"agent_{agent_id}_{episode + 1}"))

        total_time = time.time() - start_time
        self.logger.info(
            f"竞争训练完成 | 总回合数: {num_episodes} | "
            f"总用时: {total_time:.2f}s | "
            f"平均每回合时间: {total_time / num_episodes:.2f}s"
        )

        return stats

    def evaluate(
        self,
        agents: Dict[str, Agent],
        num_episodes: int
    ) -> Dict[str, Any]:
        """评估智能体性能

        Args:
            agents: 智能体字典
            num_episodes: 评估回合数

        Returns:
            评估统计数据
        """
        self.logger.info(f"开始评估，回合数：{num_episodes}")

        # 统计数据
        stats = {
            "rewards": [],
            "win_rates": {}
        }

        wins = {}
        total_rewards = {agent_id: 0.0 for agent_id in agents}

        for episode in range(num_episodes):
            state = self.env.reset()
            game_over = False
            episode_rewards = {agent_id: 0.0 for agent_id in agents}

            # 分配角色
            game_state = {"state": state}
            roles = self.role_manager.assign_roles(game_state)

            # 记录角色并初始化胜率统计
            for role in set(roles.values()):
                if role not in wins:
                    wins[role] = 0

            while not game_over:
                player_id = str(state.get_player_id())

                if player_id in agents:
                    agent = agents[player_id]
                    legal_actions = self.env.get_legal_actions(state)
                    observation = self.env.get_observation(state)

                    # 评估模式，无需探索
                    action = agent.act(observation, legal_actions, is_training=False)
                    next_state, reward, game_over, info = self.env.step(action)

                    episode_rewards[player_id] += reward
                    state = next_state
                else:
                    # 对于不在评估范围内的智能体，使用随机策略
                    legal_actions = self.env.get_legal_actions(state)
                    action = np.random.choice(legal_actions)
                    next_state, reward, game_over, info = self.env.step(action)
                    state = next_state

            # 游戏结束后
            payoffs = self.env.get_payoffs(state)

            # 更新奖励统计
            for agent_id in agents:
                total_rewards[agent_id] += episode_rewards[agent_id]

            # 确定获胜方
            winner_role = None
            max_payoff = float('-inf')

            for agent_id, role in roles.items():
                if agent_id in agents:
                    player_payoff = payoffs[int(agent_id)]
                    if player_payoff > max_payoff:
                        max_payoff = player_payoff
                        winner_role = role

            if winner_role:
                wins[winner_role] = wins.get(winner_role, 0) + 1

        # 计算胜率
        for role, win_count in wins.items():
            stats["win_rates"][role] = win_count / num_episodes

        # 计算平均奖励
        for agent_id in agents:
            total_rewards[agent_id] /= num_episodes

        stats["rewards"] = total_rewards

        self.logger.info(
            f"评估完成 | 回合数: {num_episodes} | "
            f"平均奖励: {sum(total_rewards.values()) / len(total_rewards):.4f} | "
            + " | ".join([f"{role}胜率: {wr:.4f}" for role, wr in stats["win_rates"].items()])
        )

        return stats