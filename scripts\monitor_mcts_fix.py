#!/usr/bin/env python3
"""
MCTS修复效果监控脚本

监控MCTS训练过程，确保修复后不再出现无限循环问题。
实时显示关键指标，包括模拟时间、节点数量、动作多样性等。
"""

import sys
import os
import time
import logging
import threading
from collections import defaultdict, deque
from typing import Dict, List, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.algorithms.efficient_zero_algorithm import EfficientZero

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class MCTSMonitor:
    """MCTS修复效果监控器"""
    
    def __init__(self, max_history=100):
        """
        初始化监控器
        
        Args:
            max_history: 保留的历史记录数量
        """
        self.max_history = max_history
        self.simulation_times = deque(maxlen=max_history)
        self.action_counts = deque(maxlen=max_history)
        self.unique_actions = deque(maxlen=max_history)
        self.error_count = 0
        self.timeout_count = 0
        self.total_simulations = 0
        
        # 动作类型统计
        self.action_type_stats = defaultdict(int)
        
        # 性能指标
        self.performance_metrics = {
            'avg_simulation_time': 0.0,
            'max_simulation_time': 0.0,
            'min_simulation_time': float('inf'),
            'avg_action_count': 0.0,
            'action_diversity': 0.0,
            'success_rate': 0.0
        }
        
        self.env = DouDizhuEnvironment()
        self.algorithm = EfficientZero()
        
    def monitor_single_simulation(self, timeout_seconds=30):
        """
        监控单次MCTS模拟
        
        Args:
            timeout_seconds: 超时时间（秒）
            
        Returns:
            Dict: 监控结果
        """
        result = {
            'success': False,
            'simulation_time': 0.0,
            'action_count': 0,
            'unique_actions': 0,
            'action_types': {},
            'error': None
        }
        
        try:
            # 重置环境
            state = self.env.reset()
            
            # 获取合法动作
            legal_actions = state.get_legal_actions()
            result['action_count'] = len(legal_actions)
            
            # 统计动作类型
            action_types = defaultdict(int)
            unique_action_ids = set()
            
            for action in legal_actions:
                action_id = self.algorithm._get_action_id(action)
                unique_action_ids.add(action_id)
                
                # 统计动作类型
                if hasattr(action, 'card_type'):
                    action_types[f"CardGroup_{action.card_type.name}"] += 1
                elif hasattr(action, 'value'):
                    action_types[f"{type(action).__name__}_{action.name}"] += 1
                else:
                    action_types[f"{type(action).__name__}"] += 1
            
            result['unique_actions'] = len(unique_action_ids)
            result['action_types'] = dict(action_types)
            
            # 检查是否存在单一动作问题
            if len(unique_action_ids) <= 1:
                result['error'] = f"检测到单一动作问题: 只有{len(unique_action_ids)}个唯一动作ID"
                return result
            
            # 执行MCTS模拟
            start_time = time.time()
            
            # 使用线程执行，以便设置超时
            simulation_result = {'action': None, 'error': None}
            
            def run_simulation():
                try:
                    action, _, _ = self.algorithm.act(state, explain=True)
                    simulation_result['action'] = action
                except Exception as e:
                    simulation_result['error'] = str(e)
            
            simulation_thread = threading.Thread(target=run_simulation)
            simulation_thread.daemon = True
            simulation_thread.start()
            simulation_thread.join(timeout=timeout_seconds)
            
            elapsed_time = time.time() - start_time
            result['simulation_time'] = elapsed_time
            
            if simulation_thread.is_alive():
                result['error'] = f"模拟超时: {elapsed_time:.2f}秒"
                return result
            
            if simulation_result['error']:
                result['error'] = simulation_result['error']
                return result
            
            if simulation_result['action'] is None:
                result['error'] = "模拟返回空动作"
                return result
            
            result['success'] = True
            
        except Exception as e:
            result['error'] = str(e)
        
        return result
    
    def update_statistics(self, result: Dict):
        """更新统计信息"""
        if result['success']:
            self.simulation_times.append(result['simulation_time'])
            self.action_counts.append(result['action_count'])
            self.unique_actions.append(result['unique_actions'])
            
            # 更新动作类型统计
            for action_type, count in result['action_types'].items():
                self.action_type_stats[action_type] += count
        else:
            self.error_count += 1
            if 'timeout' in result.get('error', '').lower():
                self.timeout_count += 1
        
        self.total_simulations += 1
        
        # 更新性能指标
        if self.simulation_times:
            self.performance_metrics['avg_simulation_time'] = sum(self.simulation_times) / len(self.simulation_times)
            self.performance_metrics['max_simulation_time'] = max(self.simulation_times)
            self.performance_metrics['min_simulation_time'] = min(self.simulation_times)
        
        if self.action_counts:
            self.performance_metrics['avg_action_count'] = sum(self.action_counts) / len(self.action_counts)
        
        if self.unique_actions:
            self.performance_metrics['action_diversity'] = sum(self.unique_actions) / len(self.unique_actions)
        
        self.performance_metrics['success_rate'] = (self.total_simulations - self.error_count) / self.total_simulations * 100
    
    def print_status(self):
        """打印当前状态"""
        print(f"\n{'='*60}")
        print(f"MCTS修复效果监控 - 总模拟次数: {self.total_simulations}")
        print(f"{'='*60}")
        
        # 基础统计
        print(f"成功率: {self.performance_metrics['success_rate']:.1f}%")
        print(f"错误次数: {self.error_count}")
        print(f"超时次数: {self.timeout_count}")
        
        # 性能指标
        if self.simulation_times:
            print(f"\n性能指标:")
            print(f"  平均模拟时间: {self.performance_metrics['avg_simulation_time']:.3f}秒")
            print(f"  最大模拟时间: {self.performance_metrics['max_simulation_time']:.3f}秒")
            print(f"  最小模拟时间: {self.performance_metrics['min_simulation_time']:.3f}秒")
        
        # 动作统计
        if self.action_counts:
            print(f"\n动作统计:")
            print(f"  平均动作数量: {self.performance_metrics['avg_action_count']:.1f}")
            print(f"  平均唯一动作: {self.performance_metrics['action_diversity']:.1f}")
        
        # 动作类型分布（显示前5个）
        if self.action_type_stats:
            print(f"\n动作类型分布 (前5个):")
            sorted_types = sorted(self.action_type_stats.items(), key=lambda x: x[1], reverse=True)
            for action_type, count in sorted_types[:5]:
                print(f"  {action_type}: {count}")
        
        print(f"{'='*60}")
    
    def run_continuous_monitoring(self, duration_minutes=10, interval_seconds=5):
        """
        运行连续监控
        
        Args:
            duration_minutes: 监控持续时间（分钟）
            interval_seconds: 监控间隔（秒）
        """
        logger.info(f"开始连续监控 - 持续{duration_minutes}分钟，间隔{interval_seconds}秒")
        
        start_time = time.time()
        end_time = start_time + duration_minutes * 60
        
        while time.time() < end_time:
            # 执行单次监控
            result = self.monitor_single_simulation()
            self.update_statistics(result)
            
            # 打印状态
            self.print_status()
            
            # 检查是否有严重问题
            if result.get('error') and 'timeout' in result['error'].lower():
                logger.warning(f"检测到超时问题: {result['error']}")
            elif not result['success']:
                logger.warning(f"检测到错误: {result.get('error', 'Unknown error')}")
            
            # 等待下一次监控
            time.sleep(interval_seconds)
        
        # 最终报告
        self.print_final_report()
    
    def print_final_report(self):
        """打印最终报告"""
        print(f"\n{'='*60}")
        print(f"MCTS修复效果监控 - 最终报告")
        print(f"{'='*60}")
        
        print(f"总模拟次数: {self.total_simulations}")
        print(f"成功率: {self.performance_metrics['success_rate']:.1f}%")
        print(f"错误次数: {self.error_count}")
        print(f"超时次数: {self.timeout_count}")
        
        if self.performance_metrics['success_rate'] >= 95:
            print("🎉 修复效果良好！成功率超过95%")
        elif self.performance_metrics['success_rate'] >= 80:
            print("⚠️ 修复效果一般，成功率在80-95%之间")
        else:
            print("❌ 修复效果不佳，成功率低于80%")
        
        if self.timeout_count == 0:
            print("✅ 未检测到超时问题，MCTS无限循环已修复")
        else:
            print(f"⚠️ 仍有{self.timeout_count}次超时，可能需要进一步优化")


if __name__ == "__main__":
    monitor = MCTSMonitor()
    
    # 运行连续监控
    try:
        monitor.run_continuous_monitoring(duration_minutes=5, interval_seconds=10)
    except KeyboardInterrupt:
        print("\n监控被用户中断")
        monitor.print_final_report()
    except Exception as e:
        logger.error(f"监控过程中发生错误: {e}")
        monitor.print_final_report()
