"""
监控服务接口定义

该接口定义了监控服务的标准契约，用于解耦zhuchengxu模块与具体监控实现的依赖关系。
通过该接口，zhuchengxu模块可以与任何实现了MonitoringInterface的监控服务进行交互。

设计目标:
- 解耦zhuchengxu与performance_monitor的直接依赖
- 提供标准化的监控服务接口
- 支持多种监控指标和可视化
- 实现实时监控和历史数据查询

作者: Architect Timmy
版本: v1.0
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Union, Callable
from dataclasses import dataclass
from enum import Enum
from datetime import datetime


class MetricType(Enum):
    """指标类型枚举"""
    COUNTER = "counter"        # 计数器
    GAUGE = "gauge"           # 仪表盘
    HISTOGRAM = "histogram"    # 直方图
    TIMER = "timer"           # 计时器
    RATE = "rate"             # 速率


class AggregationType(Enum):
    """聚合类型枚举"""
    SUM = "sum"               # 求和
    AVERAGE = "average"       # 平均值
    MIN = "min"               # 最小值
    MAX = "max"               # 最大值
    COUNT = "count"           # 计数
    PERCENTILE = "percentile" # 百分位数


@dataclass
class MetricData:
    """监控指标数据类"""
    name: str                              # 指标名称
    value: Union[int, float]               # 指标值
    timestamp: datetime                    # 时间戳
    type: MetricType                       # 指标类型
    tags: Optional[Dict[str, str]] = None  # 标签
    unit: Optional[str] = None             # 单位
    description: Optional[str] = None      # 描述


@dataclass
class AlertRule:
    """告警规则数据类"""
    name: str                              # 规则名称
    metric_name: str                       # 监控指标名称
    condition: str                         # 告警条件 (如: "> 0.8", "< 100")
    threshold: Union[int, float]           # 阈值
    duration: int                          # 持续时间(秒)
    severity: str                          # 严重程度 ("low", "medium", "high", "critical")
    enabled: bool = True                   # 是否启用
    description: Optional[str] = None      # 描述


@dataclass
class Alert:
    """告警数据类"""
    rule_name: str                         # 规则名称
    metric_name: str                       # 指标名称
    current_value: Union[int, float]       # 当前值
    threshold: Union[int, float]           # 阈值
    severity: str                          # 严重程度
    triggered_at: datetime                 # 触发时间
    resolved_at: Optional[datetime] = None # 解决时间
    message: Optional[str] = None          # 告警消息


class MonitoringInterface(ABC):
    """监控服务接口
    
    定义了监控服务必须实现的标准方法，用于解耦zhuchengxu模块与具体监控实现。
    
    实现该接口的类必须提供:
    1. 指标收集和存储功能
    2. 实时监控和告警功能
    3. 数据查询和聚合功能
    4. 可视化和报告功能
    
    注意:
        所有方法都必须是线程安全的，支持并发调用。
        监控数据收集不应该影响主程序的性能。
    """
    
    @abstractmethod
    def start_monitoring(self) -> bool:
        """启动监控服务
        
        Returns:
            bool: 是否成功启动
        """
        pass
    
    @abstractmethod
    def stop_monitoring(self) -> bool:
        """停止监控服务
        
        Returns:
            bool: 是否成功停止
        """
        pass
    
    @abstractmethod
    def is_monitoring(self) -> bool:
        """检查监控服务是否运行
        
        Returns:
            bool: 是否正在监控
        """
        pass
    
    @abstractmethod
    def record_metric(self, metric: MetricData) -> bool:
        """记录监控指标
        
        Args:
            metric: 指标数据
            
        Returns:
            bool: 是否成功记录
        """
        pass
    
    @abstractmethod
    def record_counter(self, name: str, value: Union[int, float] = 1,
                      tags: Optional[Dict[str, str]] = None) -> bool:
        """记录计数器指标
        
        Args:
            name: 指标名称
            value: 计数值
            tags: 标签
            
        Returns:
            bool: 是否成功记录
        """
        pass
    
    @abstractmethod
    def record_gauge(self, name: str, value: Union[int, float],
                    tags: Optional[Dict[str, str]] = None) -> bool:
        """记录仪表盘指标
        
        Args:
            name: 指标名称
            value: 指标值
            tags: 标签
            
        Returns:
            bool: 是否成功记录
        """
        pass
    
    @abstractmethod
    def record_timer(self, name: str, duration: float,
                    tags: Optional[Dict[str, str]] = None) -> bool:
        """记录计时器指标
        
        Args:
            name: 指标名称
            duration: 持续时间(秒)
            tags: 标签
            
        Returns:
            bool: 是否成功记录
        """
        pass
    
    @abstractmethod
    def start_timer(self, name: str, tags: Optional[Dict[str, str]] = None) -> str:
        """启动计时器
        
        Args:
            name: 计时器名称
            tags: 标签
            
        Returns:
            str: 计时器ID
        """
        pass
    
    @abstractmethod
    def stop_timer(self, timer_id: str) -> Optional[float]:
        """停止计时器
        
        Args:
            timer_id: 计时器ID
            
        Returns:
            Optional[float]: 计时时长(秒)，失败返回None
        """
        pass
    
    @abstractmethod
    def get_metric(self, name: str, 
                  start_time: Optional[datetime] = None,
                  end_time: Optional[datetime] = None,
                  tags: Optional[Dict[str, str]] = None) -> List[MetricData]:
        """获取指标数据
        
        Args:
            name: 指标名称
            start_time: 开始时间
            end_time: 结束时间
            tags: 标签过滤
            
        Returns:
            List[MetricData]: 指标数据列表
        """
        pass
    
    @abstractmethod
    def aggregate_metric(self, name: str, aggregation: AggregationType,
                        start_time: Optional[datetime] = None,
                        end_time: Optional[datetime] = None,
                        tags: Optional[Dict[str, str]] = None) -> Optional[float]:
        """聚合指标数据
        
        Args:
            name: 指标名称
            aggregation: 聚合类型
            start_time: 开始时间
            end_time: 结束时间
            tags: 标签过滤
            
        Returns:
            Optional[float]: 聚合结果，失败返回None
        """
        pass
    
    @abstractmethod
    def list_metrics(self) -> List[str]:
        """列出所有指标名称
        
        Returns:
            List[str]: 指标名称列表
        """
        pass
    
    @abstractmethod
    def add_alert_rule(self, rule: AlertRule) -> bool:
        """添加告警规则
        
        Args:
            rule: 告警规则
            
        Returns:
            bool: 是否成功添加
        """
        pass
    
    @abstractmethod
    def remove_alert_rule(self, rule_name: str) -> bool:
        """移除告警规则
        
        Args:
            rule_name: 规则名称
            
        Returns:
            bool: 是否成功移除
        """
        pass
    
    @abstractmethod
    def get_alert_rules(self) -> List[AlertRule]:
        """获取所有告警规则
        
        Returns:
            List[AlertRule]: 告警规则列表
        """
        pass
    
    @abstractmethod
    def get_active_alerts(self) -> List[Alert]:
        """获取活跃告警
        
        Returns:
            List[Alert]: 活跃告警列表
        """
        pass
    
    @abstractmethod
    def get_alert_history(self, start_time: Optional[datetime] = None,
                         end_time: Optional[datetime] = None) -> List[Alert]:
        """获取告警历史
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            List[Alert]: 告警历史列表
        """
        pass
    
    @abstractmethod
    def register_alert_callback(self, callback: Callable[[Alert], None]) -> str:
        """注册告警回调
        
        Args:
            callback: 告警回调函数
            
        Returns:
            str: 回调ID
        """
        pass
    
    @abstractmethod
    def unregister_alert_callback(self, callback_id: str) -> bool:
        """注销告警回调
        
        Args:
            callback_id: 回调ID
            
        Returns:
            bool: 是否成功注销
        """
        pass
    
    @abstractmethod
    def export_metrics(self, output_path: str, format: str = "json",
                      start_time: Optional[datetime] = None,
                      end_time: Optional[datetime] = None) -> bool:
        """导出监控数据
        
        Args:
            output_path: 输出路径
            format: 导出格式 ("json", "csv", "prometheus")
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            bool: 是否成功导出
        """
        pass
    
    @abstractmethod
    def get_dashboard_data(self, dashboard_name: str) -> Dict[str, Any]:
        """获取仪表板数据
        
        Args:
            dashboard_name: 仪表板名称
            
        Returns:
            Dict[str, Any]: 仪表板数据
        """
        pass
    
    @abstractmethod
    def create_dashboard(self, name: str, config: Dict[str, Any]) -> bool:
        """创建仪表板
        
        Args:
            name: 仪表板名称
            config: 仪表板配置
            
        Returns:
            bool: 是否成功创建
        """
        pass
