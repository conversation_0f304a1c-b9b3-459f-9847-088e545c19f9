"""
团队决策机制模块

实现农民之间的团队决策机制，提高多智能体协作效果。包括分层决策架构、
团队价值分解和角色特化机制等技术，使农民智能体能够更有效地协作。
"""
import os
import time
import logging
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from typing import Dict, List, Tuple, Any, Optional, Union
from collections import defaultdict, deque, Counter

from cardgame_ai.multi_agent.multi_agent_framework import FarmerCooperation, RoleManager
from cardgame_ai.multi_agent.implicit_communication import ImplicitCommunicationMechanism
from cardgame_ai.multi_agent.cooperative_strategy import JointPolicyOptimizer, RoleAwareCritic, CollaborativeExploration
from cardgame_ai.core.agent import Agent
from cardgame_ai.core.environment import Environment
from cardgame_ai.algorithms.mappo import MAPPO, MAPPONetwork


class HierarchicalDecisionArchitecture:
    """
    分层决策架构

    实现农民智能体之间的分层决策架构，包括战略层和战术层。
    战略层负责制定长期目标和协作计划，战术层负责具体动作的执行。
    """

    def __init__(
        self,
        agents: Dict[str, Agent],
        role_manager: RoleManager,
        strategic_update_freq: int = 5,
        tactical_update_freq: int = 1,
        use_attention_mechanism: bool = True,
        use_communication_channel: bool = True,
        human_player_ids: List[str] = None
    ):
        """
        初始化分层决策架构

        Args:
            agents: 智能体字典，键为智能体ID
            role_manager: 角色管理器
            strategic_update_freq: 战略层更新频率
            tactical_update_freq: 战术层更新频率
            use_attention_mechanism: 是否使用注意力机制
            use_communication_channel: 是否使用通信通道
            human_player_ids: 真人玩家ID列表，默认为None
        """
        self.agents = agents
        self.role_manager = role_manager
        self.strategic_update_freq = strategic_update_freq
        self.tactical_update_freq = tactical_update_freq
        self.use_attention_mechanism = use_attention_mechanism
        self.use_communication_channel = use_communication_channel
        self.human_player_ids = human_player_ids or []

        # 获取农民智能体
        self.farmer_agents = {}
        for agent_id, agent in agents.items():
            if role_manager.get_role(agent_id) == "farmer":
                self.farmer_agents[agent_id] = agent

        # 初始化人类玩家建模模块
        self.human_modeling_module = HumanPlayerModelingModule()

        # 初始化隐式通信解释器
        self.implicit_communication_interpreter = ImplicitCommunicationInterpreter(
            self.human_modeling_module
        )

        # 初始化战略层
        self.strategic_layer = StrategicLayer(
            agents=self.farmer_agents,
            role_manager=role_manager,
            use_attention_mechanism=use_attention_mechanism
        )

        # 替换角色分配器为自适应角色分配器
        self.strategic_layer.role_specializer = AdaptiveRoleSpecializer(
            agents=self.farmer_agents,
            role_manager=role_manager,
            human_modeling_module=self.human_modeling_module
        )

        # 初始化战术层
        self.tactical_layer = TacticalLayer(
            agents=self.farmer_agents,
            role_manager=role_manager,
            use_communication_channel=use_communication_channel
        )

        # 如果使用通信通道，添加隐式通信解释器
        if use_communication_channel and self.tactical_layer.communication_channel:
            self.tactical_layer.communication_channel.implicit_communication_interpreter = self.implicit_communication_interpreter

        # 初始化层间通信通道
        self.layer_communication = LayerCommunication()

        # 初始化统计信息
        self.stats = {
            "strategic_updates": 0,
            "tactical_updates": 0,
            "layer_communications": 0,
            "human_interactions": 0
        }

        # 初始化全局步数
        self.global_step = 0

    def is_human_player(self, player_id: str) -> bool:
        """
        判断玩家是否为真人

        Args:
            player_id: 玩家ID

        Returns:
            是否为真人玩家
        """
        return player_id in self.human_player_ids

    def make_decision(
        self,
        agent_id: str,
        observations: Dict[str, np.ndarray],
        legal_actions: Dict[str, List[int]],
        game_state: Dict[str, Any] = None
    ) -> Optional[int]:
        """
        做出团队决策

        基于分层决策架构，协调农民智能体的决策。
        支持与真人玩家协作。

        Args:
            agent_id: 当前智能体ID
            observations: 所有智能体的观察，键为智能体ID
            legal_actions: 所有智能体的合法动作，键为智能体ID
            game_state: 游戏状态，默认为None

        Returns:
            建议的决策动作，如果不需要协调则返回None
        """
        # 检查当前智能体是否为农民
        if self.role_manager.get_role(agent_id) != "farmer":
            return None

        # 更新全局步数
        self.global_step += 1

        # 检查是否有真人玩家参与
        human_farmers = [
            player_id for player_id in observations.keys()
            if self.is_human_player(player_id) and self.role_manager.get_role(player_id) == "farmer"
        ]

        # 如果有真人玩家参与，更新人类玩家建模模块
        for human_id in human_farmers:
            # 如果有游戏状态和真人玩家的上一个动作，更新人类玩家建模模块
            if game_state and "last_actions" in game_state and human_id in game_state["last_actions"]:
                self.human_modeling_module.update(
                    human_id,
                    game_state["last_actions"][human_id],
                    game_state,
                    observations
                )

                # 解释真人玩家的隐式通信
                self.implicit_communication_interpreter.interpret_human_action(
                    human_id,
                    game_state["last_actions"][human_id],
                    game_state,
                    observations
                )

                # 更新统计信息
                self.stats["human_interactions"] += 1

        # 更新战略层
        if self.global_step % self.strategic_update_freq == 0:
            # 如果有真人玩家参与，使用带有人类玩家信息的更新方法
            if human_farmers:
                strategic_info = self.strategic_layer.update_with_human(
                    agent_id, human_farmers[0], observations, legal_actions
                )
            else:
                strategic_info = self.strategic_layer.update(
                    agent_id, observations, legal_actions
                )

            self.stats["strategic_updates"] += 1

            # 层间通信
            self.layer_communication.strategic_to_tactical(
                strategic_info, self.tactical_layer
            )
            self.stats["layer_communications"] += 1

        # 更新战术层
        if self.global_step % self.tactical_update_freq == 0:
            # 如果有真人玩家参与，传入人类玩家的行为模型
            if human_farmers:
                human_behavior = {
                    human_id: {
                        "style": self.human_modeling_module.get_play_style(human_id),
                        "intention": self.human_modeling_module.infer_intention(
                            human_id, game_state or {}, observations
                        )
                    }
                    for human_id in human_farmers
                }

                self.tactical_layer.update_with_human(
                    agent_id, observations, legal_actions, human_behavior
                )
            else:
                self.tactical_layer.update(
                    agent_id, observations, legal_actions
                )

            self.stats["tactical_updates"] += 1

        # 从战术层获取决策
        action = self.tactical_layer.get_action(
            agent_id, observations, legal_actions
        )

        return action

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            统计信息
        """
        stats = self.stats.copy()

        # 添加战略层和战术层的统计信息
        stats.update({
            "strategic_layer": self.strategic_layer.get_stats(),
            "tactical_layer": self.tactical_layer.get_stats()
        })

        return stats


class StrategicLayer:
    """
    战略层

    负责制定长期目标和协作计划，如牌型分配、角色分工和整体战略。
    """

    def __init__(
        self,
        agents: Dict[str, Agent],
        role_manager: RoleManager,
        use_attention_mechanism: bool = True,
        planning_horizon: int = 5
    ):
        """
        初始化战略层

        Args:
            agents: 智能体字典，键为智能体ID
            role_manager: 角色管理器
            use_attention_mechanism: 是否使用注意力机制
            planning_horizon: 规划视野
        """
        self.agents = agents
        self.role_manager = role_manager
        self.use_attention_mechanism = use_attention_mechanism
        self.planning_horizon = planning_horizon

        # 初始化战略规划器
        self.strategic_planner = StrategicPlanner(
            planning_horizon=planning_horizon
        )

        # 初始化角色分工器
        self.role_specializer = RoleSpecializer(
            agents=agents,
            role_manager=role_manager
        )

        # 初始化注意力机制
        self.attention_mechanism = None
        if use_attention_mechanism:
            self.attention_mechanism = StrategicAttentionMechanism()

        # 初始化当前战略
        self.current_strategy = {}

        # 初始化统计信息
        self.stats = {
            "strategy_updates": 0,
            "role_assignments": 0
        }

    def update(
        self,
        agent_id: str,
        observations: Dict[str, np.ndarray],
        legal_actions: Dict[str, List[int]]
    ) -> Dict[str, Any]:
        """
        更新战略层

        基于当前观察和合法动作，更新战略层的规划和分工。

        Args:
            agent_id: 当前智能体ID
            observations: 所有智能体的观察，键为智能体ID
            legal_actions: 所有智能体的合法动作，键为智能体ID

        Returns:
            战略信息
        """
        # 获取农民队友
        teammates = self.role_manager.get_teammates(agent_id)
        if not teammates:
            return {}

        # 收集农民观察和合法动作
        farmer_observations = {}
        farmer_legal_actions = {}

        for farmer_id in [agent_id] + teammates:
            if farmer_id in observations:
                farmer_observations[farmer_id] = observations[farmer_id]
            if farmer_id in legal_actions:
                farmer_legal_actions[farmer_id] = legal_actions[farmer_id]

        # 使用注意力机制增强观察
        enhanced_observations = farmer_observations
        if self.use_attention_mechanism and self.attention_mechanism is not None:
            enhanced_observations = self.attention_mechanism.enhance_observations(
                farmer_observations
            )

        # 更新战略规划
        strategy = self.strategic_planner.plan(
            agent_id, enhanced_observations, farmer_legal_actions
        )
        self.stats["strategy_updates"] += 1

        # 更新角色分工
        role_assignments = self.role_specializer.assign_roles(
            agent_id, enhanced_observations, strategy
        )
        self.stats["role_assignments"] += 1

        # 更新当前战略
        self.current_strategy = {
            "strategy": strategy,
            "role_assignments": role_assignments
        }

        return self.current_strategy

    def update_with_human(
        self,
        agent_id: str,
        human_id: str,
        observations: Dict[str, np.ndarray],
        legal_actions: Dict[str, List[int]]
    ) -> Dict[str, Any]:
        """
        考虑真人玩家的行为，更新战略层

        Args:
            agent_id: 当前智能体ID
            human_id: 真人玩家ID
            observations: 所有智能体的观察，键为智能体ID
            legal_actions: 所有智能体的合法动作，键为智能体ID

        Returns:
            战略信息
        """
        # 获取农民队友
        teammates = self.role_manager.get_teammates(agent_id)
        if not teammates:
            return {}

        # 收集农民观察和合法动作
        farmer_observations = {}
        farmer_legal_actions = {}

        for farmer_id in [agent_id] + teammates:
            if farmer_id in observations:
                farmer_observations[farmer_id] = observations[farmer_id]
            if farmer_id in legal_actions:
                farmer_legal_actions[farmer_id] = legal_actions[farmer_id]

        # 使用注意力机制增强观察
        enhanced_observations = farmer_observations
        if self.use_attention_mechanism and self.attention_mechanism is not None:
            enhanced_observations = self.attention_mechanism.enhance_observations(
                farmer_observations
            )

        # 更新战略规划
        strategy = self.strategic_planner.plan(
            agent_id, enhanced_observations, farmer_legal_actions
        )
        self.stats["strategy_updates"] += 1

        # 使用自适应角色分配器分配角色
        role_assignments = self.role_specializer.assign_roles_with_human(
            agent_id, human_id, enhanced_observations, strategy
        )
        self.stats["role_assignments"] += 1

        # 更新当前战略
        self.current_strategy = {
            "strategy": strategy,
            "role_assignments": role_assignments
        }

        return self.current_strategy

    def get_current_strategy(self) -> Dict[str, Any]:
        """
        获取当前战略

        Returns:
            当前战略
        """
        return self.current_strategy

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            统计信息
        """
        return self.stats


class StrategicPlanner:
    """
    战略规划器

    负责制定长期目标和协作计划，如牌型分配和整体战略。
    """

    def __init__(self, planning_horizon: int = 5):
        """
        初始化战略规划器

        Args:
            planning_horizon: 规划视野
        """
        self.planning_horizon = planning_horizon

        # 初始化战略模板
        self.strategy_templates = {
            "aggressive": {
                "description": "进攻性战略，主动出牌，快速减少手牌",
                "card_allocation": {
                    "big_cards": "balanced",
                    "bombs": "save",
                    "small_cards": "aggressive"
                },
                "priority": ["control", "speed", "value"]
            },
            "defensive": {
                "description": "防守性战略，保存大牌，等待机会",
                "card_allocation": {
                    "big_cards": "save",
                    "bombs": "save",
                    "small_cards": "aggressive"
                },
                "priority": ["value", "control", "speed"]
            },
            "balanced": {
                "description": "平衡战略，灵活应对不同情况",
                "card_allocation": {
                    "big_cards": "balanced",
                    "bombs": "balanced",
                    "small_cards": "balanced"
                },
                "priority": ["control", "value", "speed"]
            },
            "cooperative": {
                "description": "协作战略，专注于配合队友",
                "card_allocation": {
                    "big_cards": "share",
                    "bombs": "share",
                    "small_cards": "balanced"
                },
                "priority": ["cooperation", "control", "value"]
            }
        }

        # 初始化当前战略
        self.current_strategy = self.strategy_templates["balanced"]

    def plan(
        self,
        agent_id: str,
        observations: Dict[str, np.ndarray],
        legal_actions: Dict[str, List[int]]
    ) -> Dict[str, Any]:
        """
        制定战略规划

        基于当前观察和合法动作，制定战略规划。

        Args:
            agent_id: 当前智能体ID
            observations: 农民智能体的观察，键为智能体ID
            legal_actions: 农民智能体的合法动作，键为智能体ID

        Returns:
            战略规划
        """
        # 分析当前局势
        situation = self._analyze_situation(observations, legal_actions)

        # 选择最适合的战略模板
        strategy_name = self._select_strategy_template(situation)
        strategy = self.strategy_templates[strategy_name].copy()

        # 根据局势调整战略
        adjusted_strategy = self._adjust_strategy(strategy, situation)

        # 更新当前战略
        self.current_strategy = adjusted_strategy

        return adjusted_strategy

    def _analyze_situation(self, observations: Dict[str, np.ndarray], legal_actions: Dict[str, List[int]]) -> Dict[str, Any]:
        """
        分析当前局势

        Args:
            observations: 农民智能体的观察，键为智能体ID
            legal_actions: 农民智能体的合法动作，键为智能体ID

        Returns:
            局势分析结果
        """
        # 简化的局势分析，实际实现中可以更复杂
        situation = {
            "game_stage": "early",  # early, middle, late
            "landlord_strength": "medium",  # weak, medium, strong
            "farmer_strength": "medium",  # weak, medium, strong
            "control": "balanced",  # landlord, farmers, balanced
            "card_distribution": {
                "big_cards": "balanced",  # landlord, farmers, balanced
                "bombs": "balanced",  # landlord, farmers, balanced
                "small_cards": "balanced"  # landlord, farmers, balanced
            }
        }

        # 分析游戏阶段
        # 简化实现，实际中可以基于手牌数量、已出牌数量等判断
        total_cards = sum(len(obs) for obs in observations.values() if isinstance(obs, (list, np.ndarray)))
        if total_cards > 40:
            situation["game_stage"] = "early"
        elif total_cards > 20:
            situation["game_stage"] = "middle"
        else:
            situation["game_stage"] = "late"

        # 分析地主和农民的强度
        # 简化实现，实际中可以基于手牌价值、牌型分布等判断
        situation["landlord_strength"] = "medium"
        situation["farmer_strength"] = "medium"

        return situation

    def _select_strategy_template(self, situation: Dict[str, Any]) -> str:
        """
        选择战略模板

        Args:
            situation: 局势分析结果

        Returns:
            战略模板名称
        """
        # 简化的战略选择逻辑，实际实现中可以更复杂

        # 如果农民强度高，采用进攻性战略
        if situation["farmer_strength"] == "strong":
            return "aggressive"

        # 如果地主强度高，采用防守性战略
        if situation["landlord_strength"] == "strong":
            return "defensive"

        # 如果在游戏前期，采用平衡战略
        if situation["game_stage"] == "early":
            return "balanced"

        # 如果在游戏中期，采用协作战略
        if situation["game_stage"] == "middle":
            return "cooperative"

        # 如果在游戏后期，采用进攻性战略
        if situation["game_stage"] == "late":
            return "aggressive"

        # 默认采用平衡战略
        return "balanced"

    def _adjust_strategy(self, strategy: Dict[str, Any], situation: Dict[str, Any]) -> Dict[str, Any]:
        """
        调整战略

        Args:
            strategy: 原始战略
            situation: 局势分析结果

        Returns:
            调整后的战略
        """
        # 创建战略副本
        adjusted_strategy = strategy.copy()

        # 根据局势调整牌型分配
        if situation["game_stage"] == "late":
            # 在游戏后期，更积极地使用大牌
            adjusted_strategy["card_allocation"]["big_cards"] = "aggressive"

        # 根据控制权调整优先级
        if situation["control"] == "landlord":
            # 如果地主控制局面，控制权优先级提高
            if "control" in adjusted_strategy["priority"]:
                adjusted_strategy["priority"].remove("control")
                adjusted_strategy["priority"].insert(0, "control")

        # 添加局势分析结果
        adjusted_strategy["situation"] = situation

        return adjusted_strategy


class RoleSpecializer:
    """
    角色分工器

    负责为农民智能体分配专门的角色和职责，使它们能够形成互补的策略。
    """

    def __init__(self, agents: Dict[str, Agent], role_manager: RoleManager):
        """
        初始化角色分工器

        Args:
            agents: 智能体字典，键为智能体ID
            role_manager: 角色管理器
        """
        self.agents = agents
        self.role_manager = role_manager

        # 初始化角色模板
        self.role_templates = {
            "controller": {
                "description": "控制者，负责控制局面和出牌节奏",
                "card_preference": ["single_big", "pair_big", "straight", "pair_straight"],
                "action_preference": ["control", "speed", "value"]
            },
            "attacker": {
                "description": "攻击者，负责主动出牌和减少手牌",
                "card_preference": ["bomb", "rocket", "trio", "trio_with_single", "trio_with_pair"],
                "action_preference": ["speed", "value", "control"]
            },
            "supporter": {
                "description": "支持者，负责配合队友和保存关键牌型",
                "card_preference": ["single_small", "pair_small", "trio_with_single", "trio_with_pair"],
                "action_preference": ["cooperation", "value", "control"]
            }
        }

        # 初始化当前角色分工
        self.current_assignments = {}

    def assign_roles(
        self,
        agent_id: str,
        observations: Dict[str, np.ndarray],
        strategy: Dict[str, Any]
    ) -> Dict[str, Dict[str, Any]]:
        """
        分配角色

        基于当前观察和战略，为农民智能体分配角色。

        Args:
            agent_id: 当前智能体ID
            observations: 农民智能体的观察，键为智能体ID
            strategy: 当前战略

        Returns:
            角色分工，键为智能体ID，值为角色信息
        """
        # 获取农民智能体ID
        farmer_ids = list(observations.keys())

        # 如果没有农民智能体，返回空字典
        if not farmer_ids:
            return {}

        # 分析农民智能体的手牌特点
        farmer_card_features = self._analyze_card_features(observations)

        # 基于手牌特点和战略分配角色
        role_assignments = {}

        # 如果有两个农民，分配不同的角色
        if len(farmer_ids) == 2:
            # 判断哪个农民更适合控制者角色
            controller_scores = {}
            for farmer_id in farmer_ids:
                controller_scores[farmer_id] = self._calculate_role_suitability(
                    farmer_card_features[farmer_id], "controller"
                )

            # 选择控制者和进攻者
            controller_id = max(controller_scores.items(), key=lambda x: x[1])[0]
            attacker_id = [fid for fid in farmer_ids if fid != controller_id][0]

            # 分配角色
            role_assignments[controller_id] = self.role_templates["controller"].copy()
            role_assignments[attacker_id] = self.role_templates["attacker"].copy()

        # 如果只有一个农民，分配平衡的角色
        elif len(farmer_ids) == 1:
            # 根据战略选择角色
            if strategy.get("description", "").startswith("进攻性"):
                role_assignments[farmer_ids[0]] = self.role_templates["attacker"].copy()
            elif strategy.get("description", "").startswith("防守性"):
                role_assignments[farmer_ids[0]] = self.role_templates["supporter"].copy()
            else:
                role_assignments[farmer_ids[0]] = self.role_templates["controller"].copy()

        # 更新当前角色分工
        self.current_assignments = role_assignments

        return role_assignments

    def _analyze_card_features(self, observations: Dict[str, np.ndarray]) -> Dict[str, Dict[str, float]]:
        """
        分析手牌特点

        Args:
            observations: 农民智能体的观察，键为智能体ID

        Returns:
            手牌特点，键为智能体ID，值为特点字典
        """
        # 简化的手牌特点分析，实际实现中可以更复杂
        card_features = {}

        for agent_id, observation in observations.items():
            # 简化的特点提取，实际中可以基于具体的观察结构
            card_features[agent_id] = {
                "big_cards_ratio": 0.3,  # 大牌比例
                "bomb_count": 1,  # 炸弹数量
                "straight_potential": 0.5,  # 顺子潜力
                "pair_count": 3,  # 对子数量
                "single_count": 5,  # 单牌数量
                "trio_count": 2  # 三张数量
            }

        return card_features

    def _calculate_role_suitability(self, card_features: Dict[str, float], role: str) -> float:
        """
        计算角色适合度

        Args:
            card_features: 手牌特点
            role: 角色名称

        Returns:
            适合度分数
        """
        # 简化的适合度计算，实际实现中可以更复杂
        if role == "controller":
            # 控制者适合有大牌和顺子
            return card_features["big_cards_ratio"] * 0.5 + card_features["straight_potential"] * 0.3 + card_features["pair_count"] * 0.2
        elif role == "attacker":
            # 进攻者适合有炸弹和三张
            return card_features["bomb_count"] * 0.4 + card_features["trio_count"] * 0.4 + card_features["big_cards_ratio"] * 0.2
        elif role == "supporter":
            # 支持者适合有小牌和对子
            return card_features["single_count"] * 0.4 + card_features["pair_count"] * 0.4 + (1 - card_features["big_cards_ratio"]) * 0.2

        return 0.0


class TacticalLayer:
    """
    战术层

    负责具体动作的执行，如出牌决策和即时反应。
    """

    def __init__(
        self,
        agents: Dict[str, Agent],
        role_manager: RoleManager,
        use_communication_channel: bool = True
    ):
        """
        初始化战术层

        Args:
            agents: 智能体字典，键为智能体ID
            role_manager: 角色管理器
            use_communication_channel: 是否使用通信通道
        """
        self.agents = agents
        self.role_manager = role_manager
        self.use_communication_channel = use_communication_channel

        # 初始化战术决策器
        self.tactical_decision_maker = TacticalDecisionMaker()

        # 初始化通信通道
        self.communication_channel = None
        if use_communication_channel:
            self.communication_channel = CommunicationChannel()

        # 初始化战略信息
        self.strategic_info = {}

        # 初始化统计信息
        self.stats = {
            "tactical_decisions": 0,
            "communications": 0
        }

    def update(
        self,
        agent_id: str,
        observations: Dict[str, np.ndarray],
        legal_actions: Dict[str, List[int]]
    ) -> None:
        """
        更新战术层

        基于当前观察和合法动作，更新战术层的决策和通信。

        Args:
            agent_id: 当前智能体ID
            observations: 所有智能体的观察，键为智能体ID
            legal_actions: 所有智能体的合法动作，键为智能体ID
        """
        # 获取农民队友
        teammates = self.role_manager.get_teammates(agent_id)
        if not teammates:
            return

        # 收集农民观察和合法动作
        farmer_observations = {}
        farmer_legal_actions = {}

        for farmer_id in [agent_id] + teammates:
            if farmer_id in observations:
                farmer_observations[farmer_id] = observations[farmer_id]
            if farmer_id in legal_actions:
                farmer_legal_actions[farmer_id] = legal_actions[farmer_id]

        # 更新战术决策
        self.tactical_decision_maker.update(
            agent_id, farmer_observations, farmer_legal_actions, self.strategic_info
        )
        self.stats["tactical_decisions"] += 1

        # 更新通信通道
        if self.use_communication_channel and self.communication_channel is not None:
            self.communication_channel.update(
                agent_id, farmer_observations, farmer_legal_actions, self.strategic_info
            )
            self.stats["communications"] += 1

    def update_with_human(
        self,
        agent_id: str,
        observations: Dict[str, np.ndarray],
        legal_actions: Dict[str, List[int]],
        human_behavior: Dict[str, Dict[str, Any]]
    ) -> None:
        """
        考虑真人玩家的行为，更新战术层

        Args:
            agent_id: 当前智能体ID
            observations: 所有智能体的观察，键为智能体ID
            legal_actions: 所有智能体的合法动作，键为智能体ID
            human_behavior: 真人玩家的行为模型，键为真人玩家ID，值为行为信息
        """
        # TODO: 根据需要实现 update_with_human，当前为占位实现
        pass

    def get_action(
        self,
        agent_id: str,
        observations: Dict[str, np.ndarray],
        legal_actions: Dict[str, List[int]]
    ) -> Optional[int]:
        # 获取动作：基于当前观察和合法动作，获取战术层的决策动作
        # 检查当前智能体是否为农民
        if self.role_manager.get_role(agent_id) != "farmer":
            return None

        # 获取当前智能体的合法动作
        if agent_id not in legal_actions or not legal_actions[agent_id]:
            return None

        # 从战术决策器获取动作
        action = self.tactical_decision_maker.decide(
            agent_id, observations, legal_actions[agent_id], self.strategic_info
        )

        # 如果有通信通道，可能会覆盖决策
        if self.use_communication_channel and self.communication_channel is not None:
            communication_action = self.communication_channel.get_action(
                agent_id, observations, legal_actions[agent_id], self.strategic_info
            )
            if communication_action is not None:
                action = communication_action

        return action

    def update_strategic_info(self, strategic_info: Dict[str, Any]) -> None:
        """
        更新战略信息

        Args:
            strategic_info: 战略信息
        """
        self.strategic_info = strategic_info

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            统计信息
        """
        stats = self.stats.copy()

        # 添加战术决策器的统计信息
        stats["tactical_decision_maker"] = self.tactical_decision_maker.get_stats()

        # 添加通信通道的统计信息
        if self.use_communication_channel and self.communication_channel is not None:
            stats["communication_channel"] = self.communication_channel.get_stats()

        return stats


class TacticalDecisionMaker:
    """
    战术决策器

    负责制定具体的出牌决策，如选择哪个牌型出牌。
    """

    def __init__(self):
        """
        初始化战术决策器
        """
        # 初始化决策策略
        self.decision_strategies = {
            "control": self._control_strategy,
            "speed": self._speed_strategy,
            "value": self._value_strategy,
            "cooperation": self._cooperation_strategy
        }

        # 初始化当前状态
        self.current_state = {}

        # 初始化统计信息
        self.stats = {
            "decisions": 0,
            "strategy_usage": {
                "control": 0,
                "speed": 0,
                "value": 0,
                "cooperation": 0
            }
        }

    def update(
        self,
        agent_id: str,
        observations: Dict[str, np.ndarray],
        legal_actions: Dict[str, List[int]],
        strategic_info: Dict[str, Any]
    ) -> None:
        """
        更新战术决策器

        Args:
            agent_id: 当前智能体ID
            observations: 农民智能体的观察，键为智能体ID
            legal_actions: 农民智能体的合法动作，键为智能体ID
            strategic_info: 战略信息
        """
        # 更新当前状态
        self.current_state = {
            "agent_id": agent_id,
            "observations": observations,
            "legal_actions": legal_actions,
            "strategic_info": strategic_info
        }

    def decide(
        self,
        agent_id: str,
        observations: Dict[str, np.ndarray],
        legal_actions: List[int],
        strategic_info: Dict[str, Any]
    ) -> Optional[int]:
        """
        决策动作

        Args:
            agent_id: 当前智能体ID
            observations: 所有智能体的观察，键为智能体ID
            legal_actions: 当前智能体的合法动作
            strategic_info: 战略信息

        Returns:
            决策动作，如果不需要决策则返回None
        """
        # 如果没有合法动作，返回None
        if not legal_actions:
            return None

        # 获取战略优先级
        priority = ["control", "value", "speed"]  # 默认优先级
        if "strategy" in strategic_info and "priority" in strategic_info["strategy"]:
            priority = strategic_info["strategy"]["priority"]

        # 获取角色分工
        role_assignment = None
        if "role_assignments" in strategic_info and agent_id in strategic_info["role_assignments"]:
            role_assignment = strategic_info["role_assignments"][agent_id]

        # 根据优先级应用决策策略
        for strategy_name in priority:
            if strategy_name in self.decision_strategies:
                action = self.decision_strategies[strategy_name](
                    agent_id, observations, legal_actions, strategic_info, role_assignment
                )
                if action is not None:
                    # 更新统计信息
                    self.stats["decisions"] += 1
                    self.stats["strategy_usage"][strategy_name] += 1
                    return action

        # 如果所有策略都没有返回动作，随机选择一个合法动作
        action = np.random.choice(legal_actions)
        self.stats["decisions"] += 1
        return action

    def _control_strategy(
        self,
        agent_id: str,
        observations: Dict[str, np.ndarray],
        legal_actions: List[int],
        strategic_info: Dict[str, Any],
        role_assignment: Optional[Dict[str, Any]]
    ) -> Optional[int]:
        """
        控制策略

        优先选择能够控制局面的动作，如大牌和炸弹。

        Args:
            agent_id: 当前智能体ID
            observations: 所有智能体的观察，键为智能体ID
            legal_actions: 当前智能体的合法动作
            strategic_info: 战略信息
            role_assignment: 角色分工

        Returns:
            控制策略动作，如果没有合适的动作则返回None
        """
        # 简化的控制策略，实际实现中可以更复杂

        # 如果是控制者角色，优先选择大牌
        if role_assignment and "description" in role_assignment and "controller" in role_assignment["description"]:
            # 假设动作编码中包含牌值信息，选择最大的牌
            return max(legal_actions) if legal_actions else None

        # 如果局势分析显示地主控制局面，选择炸弹或大牌
        if "strategy" in strategic_info and "situation" in strategic_info["strategy"]:
            situation = strategic_info["strategy"]["situation"]
            if situation.get("control") == "landlord":
                # 假设动作编码中包含牌型信息，选择炸弹或大牌
                for action in legal_actions:
                    # 简化判断，实际中需要根据具体的动作编码规则
                    if action > 600:  # 假设600以上是炸弹
                        return action

        return None

    def _speed_strategy(
        self,
        agent_id: str,
        observations: Dict[str, np.ndarray],
        legal_actions: List[int],
        strategic_info: Dict[str, Any],
        role_assignment: Optional[Dict[str, Any]]
    ) -> Optional[int]:
        """
        速度策略

        优先选择能够快速减少手牌数量的动作，如顺子和连对。

        Args:
            agent_id: 当前智能体ID
            observations: 所有智能体的观察，键为智能体ID
            legal_actions: 当前智能体的合法动作
            strategic_info: 战略信息
            role_assignment: 角色分工

        Returns:
            速度策略动作，如果没有合适的动作则返回None
        """
        # 简化的速度策略，实际实现中可以更复杂

        # 如果是进攻者角色，优先选择能够出牌数量最多的动作
        if role_assignment and "description" in role_assignment and "attacker" in role_assignment["description"]:
            # 假设动作编码中包含牌型信息，选择顺子或连对
            for action in legal_actions:
                # 简化判断，实际中需要根据具体的动作编码规则
                if 400 <= action < 500:  # 假设400-499是顺子
                    return action
                elif 500 <= action < 600:  # 假设500-599是连对
                    return action

        # 如果在游戏后期，优先选择能够快速出牌的动作
        if "strategy" in strategic_info and "situation" in strategic_info["strategy"]:
            situation = strategic_info["strategy"]["situation"]
            if situation.get("game_stage") == "late":
                # 选择能够出牌数量最多的动作
                # 简化实现，实际中需要根据具体的动作编码规则
                for action_type in [400, 500, 300]:  # 优先顺子、连对、三张
                    for action in legal_actions:
                        if action_type <= action < action_type + 100:
                            return action

        return None

    def _value_strategy(
        self,
        agent_id: str,
        observations: Dict[str, np.ndarray],
        legal_actions: List[int],
        strategic_info: Dict[str, Any],
        role_assignment: Optional[Dict[str, Any]]
    ) -> Optional[int]:
        """
        价值策略

        优先选择能够保存高价值牌型的动作，如保存炸弹和大牌。

        Args:
            agent_id: 当前智能体ID
            observations: 所有智能体的观察，键为智能体ID
            legal_actions: 当前智能体的合法动作
            strategic_info: 战略信息
            role_assignment: 角色分工

        Returns:
            价值策略动作，如果没有合适的动作则返回None
        """
        # 简化的价值策略，实际实现中可以更复杂

        # 如果是支持者角色，优先选择小牌，保存大牌
        if role_assignment and "description" in role_assignment and "supporter" in role_assignment["description"]:
            # 假设动作编码中包含牌值信息，选择最小的牌
            return min(legal_actions) if legal_actions else None

        # 如果战略是防守性的，保存炸弹和大牌
        if "strategy" in strategic_info and "description" in strategic_info["strategy"]:
            if "defensive" in strategic_info["strategy"]["description"]:
                # 选择小牌，避开炸弹和大牌
                # 简化实现，实际中需要根据具体的动作编码规则
                small_actions = [action for action in legal_actions if action < 300]  # 假设300以下是小牌
                if small_actions:
                    return min(small_actions)

        return None

    def _cooperation_strategy(
        self,
        agent_id: str,
        observations: Dict[str, np.ndarray],
        legal_actions: List[int],
        strategic_info: Dict[str, Any],
        role_assignment: Optional[Dict[str, Any]]
    ) -> Optional[int]:
        """
        协作策略

        优先选择能够配合队友的动作，如配合队友的牌型和节奏。

        Args:
            agent_id: 当前智能体ID
            observations: 所有智能体的观察，键为智能体ID
            legal_actions: 当前智能体的合法动作
            strategic_info: 战略信息
            role_assignment: 角色分工

        Returns:
            协作策略动作，如果没有合适的动作则返回None
        """
        # 简化的协作策略，实际实现中可以更复杂

        # 获取队友
        teammates = []
        for other_id in observations.keys():
            if other_id != agent_id and other_id in strategic_info.get("role_assignments", {}):
                teammates.append(other_id)

        if not teammates:
            return None

        # 如果战略是协作性的，配合队友的角色
        if "strategy" in strategic_info and "description" in strategic_info["strategy"]:
            if "cooperative" in strategic_info["strategy"]["description"]:
                # 获取队友的角色
                teammate_roles = {}
                for teammate_id in teammates:
                    if teammate_id in strategic_info.get("role_assignments", {}):
                        teammate_role = strategic_info["role_assignments"][teammate_id]
                        if "description" in teammate_role:
                            teammate_roles[teammate_id] = teammate_role["description"]

                # 如果队友是控制者，选择能够配合控制的动作
                for teammate_id, role_desc in teammate_roles.items():
                    if "controller" in role_desc:
                        # 选择小牌，避开大牌
                        small_actions = [action for action in legal_actions if action < 300]  # 假设300以下是小牌
                        if small_actions:
                            return min(small_actions)

                # 如果队友是进攻者，选择能够配合进攻的动作
                for teammate_id, role_desc in teammate_roles.items():
                    if "attacker" in role_desc:
                        # 选择三张牌型，配合进攻
                        for action in legal_actions:
                            if 300 <= action < 400:  # 假设300-399是三张
                                return action

        return None

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            统计信息
        """
        return self.stats


class LayerCommunication:
    """
    层间通信

    负责战略层和战术层之间的通信，传递战略信息和反馈。
    """

    def __init__(self):
        """
        初始化层间通信
        """
        # 初始化战略信息缓存
        self.strategic_info_cache = {}

        # 初始化战术反馈缓存
        self.tactical_feedback_cache = {}

        # 初始化统计信息
        self.stats = {
            "strategic_to_tactical": 0,
            "tactical_to_strategic": 0
        }

    def strategic_to_tactical(
        self,
        strategic_info: Dict[str, Any],
        tactical_layer: Any
    ) -> None:
        """
        战略层到战术层的通信

        Args:
            strategic_info: 战略信息
            tactical_layer: 战术层
        """
        # 更新战略信息缓存
        self.strategic_info_cache = strategic_info

        # 更新战术层的战略信息
        tactical_layer.update_strategic_info(strategic_info)

        # 更新统计信息
        self.stats["strategic_to_tactical"] += 1

    def tactical_to_strategic(
        self,
        tactical_feedback: Dict[str, Any],
        strategic_layer: Any
    ) -> None:
        """
        战术层到战略层的通信

        Args:
            tactical_feedback: 战术反馈
            strategic_layer: 战略层
        """
        # 更新战术反馈缓存
        self.tactical_feedback_cache = tactical_feedback

        # 更新统计信息
        self.stats["tactical_to_strategic"] += 1

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            统计信息
        """
        return self.stats


class CommunicationChannel:
    """
    通信通道

    负责农民智能体之间的通信，传递意图和协调信息。
    """

    def __init__(self):
        """
        初始化通信通道
        """
        # 初始化意图缓存
        self.intention_cache = {}

        # 初始化协调信息缓存
        self.coordination_cache = {}

        # 初始化统计信息
        self.stats = {
            "messages_sent": 0,
            "messages_received": 0
        }

    def update(
        self,
        agent_id: str,
        observations: Dict[str, np.ndarray],
        legal_actions: Dict[str, List[int]],
        strategic_info: Dict[str, Any]
    ) -> None:
        """
        更新通信通道

        Args:
            agent_id: 当前智能体ID
            observations: 农民智能体的观察，键为智能体ID
            legal_actions: 农民智能体的合法动作，键为智能体ID
            strategic_info: 战略信息
        """
        # 获取农民队友
        teammates = []
        for other_id in observations.keys():
            if other_id != agent_id:
                teammates.append(other_id)

        if not teammates:
            return

        # 更新意图缓存
        self.intention_cache[agent_id] = self._extract_intention(agent_id, observations, strategic_info)

        # 更新协调信息缓存
        self.coordination_cache[agent_id] = self._generate_coordination_info(
            agent_id, teammates, observations, legal_actions, strategic_info
        )

        # 更新统计信息
        self.stats["messages_sent"] += 1

    def get_action(
        self,
        agent_id: str,
        observations: Dict[str, np.ndarray],
        legal_actions: List[int],
        strategic_info: Dict[str, Any]
    ) -> Optional[int]:
        """
        获取协调动作

        Args:
            agent_id: 当前智能体ID
            observations: 所有智能体的观察，键为智能体ID
            legal_actions: 当前智能体的合法动作
            strategic_info: 战略信息

        Returns:
            协调动作，如果不需要协调则返回None
        """
        # 获取农民队友
        teammates = []
        for other_id in observations.keys():
            if other_id != agent_id:
                teammates.append(other_id)

        if not teammates:
            return None

        # 获取队友的意图
        teammate_intentions = {}
        for teammate_id in teammates:
            if teammate_id in self.intention_cache:
                teammate_intentions[teammate_id] = self.intention_cache[teammate_id]

        # 获取队友的协调信息
        teammate_coordinations = {}
        for teammate_id in teammates:
            if teammate_id in self.coordination_cache:
                teammate_coordinations[teammate_id] = self.coordination_cache[teammate_id]

        # 如果没有队友的意图和协调信息，返回None
        if not teammate_intentions and not teammate_coordinations:
            return None

        # 根据队友的意图和协调信息选择动作
        action = self._select_action_based_on_communication(
            agent_id, legal_actions, teammate_intentions, teammate_coordinations, strategic_info
        )

        # 更新统计信息
        if action is not None:
            self.stats["messages_received"] += 1

        return action

    def _extract_intention(self, agent_id: str, observations: Dict[str, np.ndarray], strategic_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取意图

        Args:
            agent_id: 当前智能体ID
            observations: 农民智能体的观察，键为智能体ID
            strategic_info: 战略信息

        Returns:
            意图信息
        """
        # 简化的意图提取，实际实现中可以更复杂
        intention = {
            "has_big_cards": False,
            "has_bomb": False,
            "has_many_pairs": False,
            "has_many_singles": False,
            "need_control": False
        }

        # 根据角色分工提取意图
        if "role_assignments" in strategic_info and agent_id in strategic_info["role_assignments"]:
            role_assignment = strategic_info["role_assignments"][agent_id]
            if "description" in role_assignment:
                if "controller" in role_assignment["description"]:
                    intention["has_big_cards"] = True
                    intention["need_control"] = True
                elif "attacker" in role_assignment["description"]:
                    intention["has_bomb"] = True
                    intention["has_many_pairs"] = True
                elif "supporter" in role_assignment["description"]:
                    intention["has_many_singles"] = True

        return intention

    def _generate_coordination_info(
        self,
        agent_id: str,
        teammates: List[str],
        observations: Dict[str, np.ndarray],
        legal_actions: Dict[str, List[int]],
        strategic_info: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        生成协调信息

        Args:
            agent_id: 当前智能体ID
            teammates: 队友ID列表
            observations: 农民智能体的观察，键为智能体ID
            legal_actions: 农民智能体的合法动作，键为智能体ID
            strategic_info: 战略信息

        Returns:
            协调信息
        """
        # 简化的协调信息生成，实际实现中可以更复杂
        coordination_info = {
            "preferred_actions": [],
            "avoided_actions": [],
            "priority": []
        }

        # 根据角色分工生成协调信息
        if "role_assignments" in strategic_info and agent_id in strategic_info["role_assignments"]:
            role_assignment = strategic_info["role_assignments"][agent_id]
            if "description" in role_assignment:
                if "controller" in role_assignment["description"]:
                    # 控制者偏好大牌和顺子
                    coordination_info["preferred_actions"] = ["single_big", "pair_big", "straight", "pair_straight"]
                    coordination_info["avoided_actions"] = ["single_small", "pair_small"]
                    coordination_info["priority"] = ["control", "value", "speed"]
                elif "attacker" in role_assignment["description"]:
                    # 进攻者偏好炸弹和三张
                    coordination_info["preferred_actions"] = ["bomb", "rocket", "trio", "trio_with_single"]
                    coordination_info["avoided_actions"] = ["pass"]
                    coordination_info["priority"] = ["speed", "value", "control"]
                elif "supporter" in role_assignment["description"]:
                    # 支持者偏好小牌和对子
                    coordination_info["preferred_actions"] = ["single_small", "pair_small", "trio_with_single"]
                    coordination_info["avoided_actions"] = ["bomb", "rocket"]
                    coordination_info["priority"] = ["cooperation", "value", "control"]

        return coordination_info

    def _select_action_based_on_communication(
        self,
        agent_id: str,
        legal_actions: List[int],
        teammate_intentions: Dict[str, Dict[str, Any]],
        teammate_coordinations: Dict[str, Dict[str, Any]],
        strategic_info: Dict[str, Any]
    ) -> Optional[int]:
        """
        基于通信选择动作

        Args:
            agent_id: 当前智能体ID
            legal_actions: 当前智能体的合法动作
            teammate_intentions: 队友的意图，键为队友ID
            teammate_coordinations: 队友的协调信息，键为队友ID
            strategic_info: 战略信息

        Returns:
            协调动作，如果不需要协调则返回None
        """
        # 简化的动作选择，实际实现中可以更复杂

        # 如果没有合法动作，返回None
        if not legal_actions:
            return None

        # 如果队友需要控制权
        for teammate_id, intention in teammate_intentions.items():
            if intention.get("need_control", False):
                # 选择小牌，避开大牌
                small_actions = [action for action in legal_actions if action < 300]  # 假设300以下是小牌
                if small_actions:
                    return min(small_actions)

        # 如果队友有炸弹
        for teammate_id, intention in teammate_intentions.items():
            if intention.get("has_bomb", False):
                # 选择不是炸弹的动作
                non_bomb_actions = [action for action in legal_actions if action < 600]  # 假设600以上是炸弹
                if non_bomb_actions:
                    return max(non_bomb_actions)

        # 如果队友偏好特定动作
        preferred_action_types = set()
        for teammate_id, coordination in teammate_coordinations.items():
            preferred_actions = coordination.get("preferred_actions", [])
            for action_type in preferred_actions:
                preferred_action_types.add(action_type)

        # 如果有偏好的动作类型，选择与之互补的动作
        if preferred_action_types:
            # 简化实现，实际中需要根据具体的动作编码规则
            if "single_big" in preferred_action_types or "pair_big" in preferred_action_types:
                # 队友偏好大牌，选择小牌
                small_actions = [action for action in legal_actions if action < 300]  # 假设300以下是小牌
                if small_actions:
                    return min(small_actions)
            elif "single_small" in preferred_action_types or "pair_small" in preferred_action_types:
                # 队友偏好小牌，选择大牌
                big_actions = [action for action in legal_actions if action >= 300]  # 假设300以上是大牌
                if big_actions:
                    return max(big_actions)

        # 如果没有特定的协调需求，返回None
        return None

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            统计信息
        """
        return self.stats


class StrategicAttentionMechanism:
    """
    战略注意力机制

    使用注意力机制增强农民智能体的观察，关注重要的信息。
    """

    def __init__(self):
        """
        初始化战略注意力机制
        """
        # 初始化注意力权重
        self.attention_weights = {
            "hand_cards": 0.5,
            "played_cards": 0.3,
            "remaining_cards": 0.2
        }

        # 初始化统计信息
        self.stats = {
            "attention_calls": 0
        }

    def enhance_observations(
        self,
        observations: Dict[str, np.ndarray]
    ) -> Dict[str, np.ndarray]:
        """
        增强观察

        Args:
            observations: 农民智能体的观察，键为智能体ID

        Returns:
            增强后的观察
        """
        # 简化的观察增强，实际实现中可以更复杂
        enhanced_observations = {}

        for agent_id, observation in observations.items():
            # 直接复制原始观察
            enhanced_observations[agent_id] = observation

        # 更新统计信息
        self.stats["attention_calls"] += 1

        return enhanced_observations

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            统计信息
        """
        return self.stats


def test_team_decision_mechanism():
    """
    测试团队决策机制
    """
    print("\n=== 测试团队决策机制 ===")

    # 创建角色管理器
    from cardgame_ai.multi_agent.multi_agent_framework import RoleManager
    role_manager = RoleManager(None)

    # 模拟角色管理器的方法
    role_manager.get_role = lambda agent_id: "farmer" if agent_id in ["0", "1"] else "landlord"
    role_manager.get_teammates = lambda agent_id: ["1"] if agent_id == "0" else ["0"] if agent_id == "1" else []

    # 创建模拟智能体
    from cardgame_ai.algorithms.mappo import MAPPO
    from cardgame_ai.core.agent import Agent

    agents = {}
    for agent_id in ["0", "1", "2"]:
        # 创建简化的MAPPO算法
        mappo = MAPPO(
            obs_dim=200,
            act_dim=100,
            hidden_dims=[128, 64],
            learning_rate=0.0003
        )

        # 创建智能体
        agents[agent_id] = Agent(algorithm=mappo)

    # 创建分层决策架构
    hierarchical_decision = HierarchicalDecisionArchitecture(
        agents=agents,
        role_manager=role_manager,
        strategic_update_freq=5,
        tactical_update_freq=1,
        use_attention_mechanism=True,
        use_communication_channel=True
    )

    # 测试决策
    print("\n测试团队决策:")

    # 模拟观察和合法动作
    import numpy as np
    observations = {"0": np.random.rand(200), "1": np.random.rand(200), "2": np.random.rand(200)}
    legal_actions = {"0": [0, 1, 2, 3, 4], "1": [0, 1, 2, 3], "2": [0, 1, 2]}

    # 做出决策
    action = hierarchical_decision.make_decision("0", observations, legal_actions)
    print(f"  决策动作: {action}")

    # 测试战略层
    print("\n测试战略层:")
    strategic_info = hierarchical_decision.strategic_layer.update(
        "0", observations, legal_actions
    )
    print(f"  战略信息: {strategic_info.keys()}")

    # 测试战术层
    print("\n测试战术层:")
    hierarchical_decision.tactical_layer.update(
        "0", observations, legal_actions
    )
    action = hierarchical_decision.tactical_layer.get_action(
        "0", observations, legal_actions
    )
    print(f"  战术动作: {action}")

    # 测试层间通信
    print("\n测试层间通信:")
    hierarchical_decision.layer_communication.strategic_to_tactical(
        strategic_info, hierarchical_decision.tactical_layer
    )
    print(f"  层间通信统计: {hierarchical_decision.layer_communication.get_stats()}")

    # 测试通信通道
    print("\n测试通信通道:")
    hierarchical_decision.tactical_layer.communication_channel.update(
        "0", observations, legal_actions, strategic_info
    )
    action = hierarchical_decision.tactical_layer.communication_channel.get_action(
        "0", observations, legal_actions["0"], strategic_info
    )
    print(f"  通信动作: {action}")

    # 测试注意力机制
    print("\n测试注意力机制:")
    enhanced_observations = hierarchical_decision.strategic_layer.attention_mechanism.enhance_observations(
        observations
    )
    print(f"  增强观察数量: {len(enhanced_observations)}")

    return {
        "hierarchical_decision": hierarchical_decision,
        "strategic_info": strategic_info
    }


class HumanPlayerModelingModule:
    """
    真人玩家行为建模模块

    分析和建模真人玩家的行为模式，包括出牌偏好、风格特征和意图推断。
    """

    def __init__(self, history_length: int = 50):
        """
        初始化真人玩家行为建模模块

        Args:
            history_length: 历史记录长度
        """
        # 出牌历史记录，键为玩家ID，值为最近的出牌记录
        self.play_history = defaultdict(lambda: deque(maxlen=history_length))

        # 风格特征，键为玩家ID，值为风格特征字典
        self.style_profile = defaultdict(dict)

        # 意图模型，键为玩家ID，值为意图概率分布
        self.intention_model = defaultdict(dict)

        # 统计信息
        self.stats = {
            "updates": 0,
            "style_inferences": 0,
            "intention_inferences": 0
        }

    def update(
        self,
        player_id: str,
        action: int,
        game_state: Dict[str, Any],
        observations: Dict[str, np.ndarray]
    ) -> None:
        """
        更新真人玩家的行为模型

        Args:
            player_id: 玩家ID
            action: 玩家动作
            game_state: 游戏状态
            observations: 观察
        """
        # 更新出牌历史
        self.play_history[player_id].append((action, game_state))

        # 更新风格特征
        self._update_style_profile(player_id)

        # 更新意图模型
        self._update_intention_model(player_id, observations)

        # 更新统计信息
        self.stats["updates"] += 1

    def get_play_style(self, player_id: str) -> Dict[str, float]:
        """
        获取真人玩家的出牌风格

        Args:
            player_id: 玩家ID

        Returns:
            风格特征字典，包括激进度、保守度、合作度等
        """
        # 如果没有该玩家的风格特征，返回默认值
        if player_id not in self.style_profile:
            return {
                "aggressive": 0.33,
                "conservative": 0.33,
                "cooperative": 0.33
            }

        # 更新统计信息
        self.stats["style_inferences"] += 1

        return self.style_profile[player_id]

    def infer_intention(
        self,
        player_id: str,
        game_state: Dict[str, Any],
        observations: Dict[str, np.ndarray]
    ) -> Dict[str, float]:
        """
        推断真人玩家的意图

        Args:
            player_id: 玩家ID
            game_state: 游戏状态
            observations: 观察

        Returns:
            意图概率分布，键为意图类型，值为概率
        """
        # 如果没有该玩家的意图模型，返回默认值
        if player_id not in self.intention_model:
            return {
                "win_quickly": 0.33,
                "control_game": 0.33,
                "support_teammate": 0.33
            }

        # 更新统计信息
        self.stats["intention_inferences"] += 1

        return self.intention_model[player_id]

    def predict_action(
        self,
        player_id: str,
        game_state: Dict[str, Any],
        legal_actions: List[int]
    ) -> Optional[int]:
        """
        预测真人玩家的下一步行动

        Args:
            player_id: 玩家ID
            game_state: 游戏状态
            legal_actions: 合法动作列表

        Returns:
            预测的动作，如果无法预测则返回None
        """
        # 如果没有该玩家的历史记录，无法预测
        if player_id not in self.play_history or not self.play_history[player_id]:
            return None

        # 简单实现：根据历史记录中最常见的动作预测
        action_counts = Counter([action for action, _ in self.play_history[player_id]])
        most_common_actions = action_counts.most_common()

        # 找到最常见的合法动作
        for action, _ in most_common_actions:
            if action in legal_actions:
                return action

        return None

    def _update_style_profile(self, player_id: str) -> None:
        """
        更新风格特征

        Args:
            player_id: 玩家ID
        """
        # 如果没有足够的历史记录，不更新
        if len(self.play_history[player_id]) < 10:
            return

        # 计算风格特征
        aggressive_score = 0.0
        conservative_score = 0.0
        cooperative_score = 0.0

        # 简化实现：根据历史记录中的动作类型计算风格特征
        for action, _ in self.play_history[player_id]:
            # 这里需要根据具体的游戏规则和动作编码进行实现
            # 简单示例：
            if action > 600:  # 假设600以上是炸弹
                aggressive_score += 1.0
            elif action < 300:  # 假设300以下是小牌
                conservative_score += 1.0
            elif 300 <= action < 400:  # 假设300-399是三张
                cooperative_score += 1.0

        # 归一化
        total = aggressive_score + conservative_score + cooperative_score
        if total > 0:
            aggressive_score /= total
            conservative_score /= total
            cooperative_score /= total
        else:
            aggressive_score = 0.33
            conservative_score = 0.33
            cooperative_score = 0.33

        # 更新风格特征
        self.style_profile[player_id] = {
            "aggressive": aggressive_score,
            "conservative": conservative_score,
            "cooperative": cooperative_score
        }

    def _update_intention_model(
        self,
        player_id: str,
        observations: Dict[str, np.ndarray]
    ) -> None:
        """
        更新意图模型

        Args:
            player_id: 玩家ID
            observations: 观察
        """
        # 如果没有足够的历史记录，不更新
        if len(self.play_history[player_id]) < 10:
            return

        # 简化实现：根据风格特征和当前观察推断意图
        style = self.get_play_style(player_id)

        # 根据风格特征设置初始意图概率
        win_quickly_prob = style["aggressive"]
        control_game_prob = style["conservative"]
        support_teammate_prob = style["cooperative"]

        # 根据当前观察调整意图概率
        # 这里需要根据具体的观察结构进行实现
        # 简单示例：
        if player_id in observations:
            # 假设观察中包含手牌信息，可以根据手牌情况调整意图概率
            pass

        # 更新意图模型
        self.intention_model[player_id] = {
            "win_quickly": win_quickly_prob,
            "control_game": control_game_prob,
            "support_teammate": support_teammate_prob
        }

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            统计信息
        """
        return self.stats


class ImplicitCommunicationInterpreter:
    """
    隐式通信解释器

    解释真人玩家通过出牌行为传递的隐含信息。
    """

    def __init__(self, human_modeling_module: HumanPlayerModelingModule):
        """
        初始化隐式通信解释器

        Args:
            human_modeling_module: 真人玩家行为建模模块
        """
        self.human_modeling_module = human_modeling_module

        # 通信模式库，键为模式名称，值为模式描述
        self.communication_patterns = {
            "signal_big_cards": {
                "description": "暗示有大牌",
                "actions": [0],  # 不出牌可能暗示有大牌
                "conditions": ["late_game"]
            },
            "signal_no_cards": {
                "description": "暗示没有某种牌型",
                "actions": [0],  # 不出牌可能暗示没有某种牌型
                "conditions": ["teammate_played"]
            },
            "signal_cooperation": {
                "description": "暗示愿意配合",
                "actions": list(range(1, 300)),  # 出小牌可能暗示愿意配合
                "conditions": ["early_game"]
            }
        }

        # 信号历史，键为玩家ID，值为最近的信号记录
        self.signal_history = defaultdict(list)

        # 统计信息
        self.stats = {
            "interpretations": 0,
            "signals_detected": 0
        }

    def interpret_human_action(
        self,
        human_id: str,
        action: int,
        game_state: Dict[str, Any],
        observations: Dict[str, np.ndarray]
    ) -> Dict[str, float]:
        """
        解释真人玩家行动中的隐含信息

        Args:
            human_id: 真人玩家ID
            action: 玩家动作
            game_state: 游戏状态
            observations: 观察

        Returns:
            通信信号概率分布，键为信号类型，值为概率
        """
        # 更新统计信息
        self.stats["interpretations"] += 1

        # 获取游戏阶段和上下文
        game_stage = self._get_game_stage(game_state)
        context = self._get_context(human_id, game_state)

        # 初始化信号概率
        signal_probs = {
            "signal_big_cards": 0.0,
            "signal_no_cards": 0.0,
            "signal_cooperation": 0.0,
            "no_signal": 1.0  # 默认为无信号
        }

        # 检查每种通信模式
        for pattern_name, pattern in self.communication_patterns.items():
            if action in pattern["actions"]:
                # 检查条件
                if all(self._check_condition(cond, game_stage, context) for cond in pattern["conditions"]):
                    # 增加该信号的概率
                    signal_probs[pattern_name] = 0.7
                    signal_probs["no_signal"] -= 0.7

                    # 更新统计信息
                    self.stats["signals_detected"] += 1

        # 确保概率和为1
        total_prob = sum(signal_probs.values())
        if total_prob > 0:
            for signal in signal_probs:
                signal_probs[signal] /= total_prob

        # 更新信号历史
        self.signal_history[human_id].append((action, signal_probs, game_state))

        return signal_probs

    def extract_communication_signal(
        self,
        human_id: str,
        action_history: List[Tuple[int, Dict[str, Any]]],
        game_state: Dict[str, Any]
    ) -> Dict[str, float]:
        """
        从真人玩家的行动历史中提取通信信号

        Args:
            human_id: 真人玩家ID
            action_history: 行动历史，每个元素为(动作, 游戏状态)
            game_state: 当前游戏状态

        Returns:
            通信信号概率分布，键为信号类型，值为概率
        """
        # 如果没有行动历史，返回默认值
        if not action_history:
            return {"no_signal": 1.0}

        # 初始化信号概率
        signal_probs = {
            "signal_big_cards": 0.0,
            "signal_no_cards": 0.0,
            "signal_cooperation": 0.0,
            "no_signal": 1.0
        }

        # 分析最近的行动
        recent_actions = action_history[-min(3, len(action_history)):]

        # 检查是否有连续的不出牌
        pass_count = sum(1 for action, _ in recent_actions if action == 0)
        if pass_count >= 2:
            # 连续不出牌可能暗示有大牌或没有某种牌型
            signal_probs["signal_big_cards"] = 0.4
            signal_probs["signal_no_cards"] = 0.4
            signal_probs["no_signal"] = 0.2

        # 检查是否有连续出小牌
        small_card_count = sum(1 for action, _ in recent_actions if 0 < action < 300)
        if small_card_count >= 2:
            # 连续出小牌可能暗示愿意配合
            signal_probs["signal_cooperation"] = 0.6
            signal_probs["no_signal"] = 0.4

        return signal_probs

    def _get_game_stage(self, game_state: Dict[str, Any]) -> str:
        """
        获取游戏阶段

        Args:
            game_state: 游戏状态

        Returns:
            游戏阶段：early_game, middle_game, late_game
        """
        # 简化实现：根据剩余牌数判断游戏阶段
        # 这里需要根据具体的游戏状态结构进行实现
        # 简单示例：
        remaining_cards = game_state.get("remaining_cards", 54)
        if remaining_cards > 40:
            return "early_game"
        elif remaining_cards > 20:
            return "middle_game"
        else:
            return "late_game"

    def _get_context(self, human_id: str, game_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取上下文信息

        Args:
            human_id: 真人玩家ID
            game_state: 游戏状态

        Returns:
            上下文信息
        """
        # 简化实现：提取上下文信息
        # 这里需要根据具体的游戏状态结构进行实现
        # 简单示例：
        context = {
            "teammate_played": game_state.get("last_player", "") != human_id,
            "landlord_played": game_state.get("last_player_role", "") == "landlord"
        }

        return context

    def _check_condition(self, condition: str, game_stage: str, context: Dict[str, Any]) -> bool:
        """
        检查条件是否满足

        Args:
            condition: 条件名称
            game_stage: 游戏阶段
            context: 上下文信息

        Returns:
            条件是否满足
        """
        if condition == "early_game":
            return game_stage == "early_game"
        elif condition == "middle_game":
            return game_stage == "middle_game"
        elif condition == "late_game":
            return game_stage == "late_game"
        elif condition == "teammate_played":
            return context.get("teammate_played", False)
        elif condition == "landlord_played":
            return context.get("landlord_played", False)

        return False

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            统计信息
        """
        return self.stats


class AdaptiveRoleSpecializer(RoleSpecializer):
    """
    自适应角色分配器

    扩展现有的RoleSpecializer，使其能够根据真人玩家的行为动态调整AI的角色。
    """

    def __init__(
        self,
        agents: Dict[str, Agent],
        role_manager: RoleManager,
        human_modeling_module: HumanPlayerModelingModule
    ):
        """
        初始化自适应角色分配器

        Args:
            agents: 智能体字典，键为智能体ID
            role_manager: 角色管理器
            human_modeling_module: 真人玩家行为建模模块
        """
        super().__init__(agents, role_manager)
        self.human_modeling_module = human_modeling_module

        # 人类角色缓存，键为玩家ID，值为推断的角色
        self.human_role_cache = {}

        # 角色互补映射表
        self.complementary_role_map = {
            "controller": "supporter",
            "attacker": "controller",
            "supporter": "attacker"
        }

        # 统计信息
        self.stats.update({
            "human_role_inferences": 0,
            "adaptive_assignments": 0
        })

    def assign_roles_with_human(
        self,
        agent_id: str,
        human_id: str,
        observations: Dict[str, np.ndarray],
        strategy: Dict[str, Any]
    ) -> Dict[str, Dict[str, Any]]:
        """
        考虑真人玩家的行为，为AI分配互补角色

        Args:
            agent_id: AI智能体ID
            human_id: 真人玩家ID
            observations: 观察，键为智能体ID
            strategy: 当前战略

        Returns:
            角色分配结果，键为智能体ID，值为角色信息
        """
        # 推断真人玩家的角色
        human_role = self.infer_human_role(human_id, observations)

        # 更新统计信息
        self.stats["human_role_inferences"] += 1

        # 为AI分配互补角色
        ai_role = self.complementary_role_map.get(human_role, "controller")

        # 创建角色分配结果
        role_assignments = {}

        # 分配AI角色
        role_assignments[agent_id] = self.role_templates[ai_role].copy()

        # 记录真人玩家的角色（仅用于内部记录，不会返回给真人玩家）
        self.human_role_cache[human_id] = human_role

        # 更新统计信息
        self.stats["adaptive_assignments"] += 1
        self.stats["role_assignments"] += 1

        return role_assignments

    def infer_human_role(
        self,
        human_id: str,
        observations: Dict[str, np.ndarray]
    ) -> str:
        """
        推断真人玩家的隐含角色

        Args:
            human_id: 真人玩家ID
            observations: 观察，键为智能体ID

        Returns:
            推断的角色：controller, attacker, supporter
        """
        # 如果有缓存的角色，直接返回
        if human_id in self.human_role_cache:
            return self.human_role_cache[human_id]

        # 获取真人玩家的风格特征
        style = self.human_modeling_module.get_play_style(human_id)

        # 根据风格特征推断角色
        if style["aggressive"] > max(style["conservative"], style["cooperative"]):
            return "attacker"
        elif style["conservative"] > max(style["aggressive"], style["cooperative"]):
            return "controller"
        else:
            return "supporter"

    def assign_roles(
        self,
        agent_id: str,
        observations: Dict[str, np.ndarray],
        strategy: Dict[str, Any]
    ) -> Dict[str, Dict[str, Any]]:
        """
        重写基类方法，为智能体分配角色

        Args:
            agent_id: 当前智能体ID
            observations: 农民智能体的观察，键为智能体ID
            strategy: 当前战略

        Returns:
            角色分配结果，键为智能体ID，值为角色信息
        """
        # 检查是否有真人玩家
        human_ids = [player_id for player_id in observations.keys() if player_id in self.human_role_cache]

        # 如果有真人玩家，使用自适应角色分配
        if human_ids:
            return self.assign_roles_with_human(agent_id, human_ids[0], observations, strategy)

        # 否则使用基类的角色分配方法
        return super().assign_roles(agent_id, observations, strategy)


if __name__ == "__main__":
    # 如果直接运行这个文件，则执行测试
    test_team_decision_mechanism()