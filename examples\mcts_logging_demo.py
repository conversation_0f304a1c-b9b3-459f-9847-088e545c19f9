"""
MCTS日志增强功能演示脚本

此脚本演示如何使用MCTS日志增强功能，包括：
1. 基本日志配置和使用
2. 不同格式化器的效果
3. 性能监控功能
4. 与MCTS算法的集成
5. 日志分析和可视化
"""

import os
import sys
import time
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from cardgame_ai.algorithms.mcts_logging import (
        MCTSLogger, LogConfig, JSONFormatter, TextFormatter,
        PerformanceMonitor, generate_session_id
    )
    MCTS_LOGGING_AVAILABLE = True
except ImportError as e:
    print(f"MCTS日志模块导入失败: {e}")
    print("请确保已正确安装所有依赖")
    MCTS_LOGGING_AVAILABLE = False


def demo_basic_logging():
    """演示基本日志功能"""
    print("=== MCTS日志基本功能演示 ===")
    
    if not MCTS_LOGGING_AVAILABLE:
        print("MCTS日志模块不可用，跳过演示")
        return
    
    # 创建日志配置
    config = LogConfig(
        enabled=True,
        level="DEBUG",
        output_format="json",
        log_to_file=True,
        log_to_console=True,
        log_file_path="logs/demo_mcts.log",
        async_logging=False  # 演示使用同步日志
    )
    
    print(f"创建日志配置: {config.to_dict()}")
    
    # 创建日志器
    with MCTSLogger(config=config) as logger:
        print(f"日志器会话ID: {logger.session_id}")
        
        # 演示UCB计算日志
        print("\n1. 演示UCB计算日志")
        mock_node = type('MockNode', (), {
            'visit_count': 10,
            'player_id_to_act': 0,
            'state': {'current_player': 0, 'cards': [1, 2, 3]}
        })()
        
        children_scores = [
            {'action': 0, 'visits': 5, 'value': 0.6, 'prior': 0.3, 'ucb_score': 0.8},
            {'action': 1, 'visits': 3, 'value': 0.4, 'prior': 0.4, 'ucb_score': 0.7},
            {'action': 2, 'visits': 2, 'value': 0.5, 'prior': 0.3, 'ucb_score': 0.6}
        ]
        
        logger.log_ucb_calculation(
            parent_node=mock_node,
            children_scores=children_scores,
            selected_action=0,
            game_context={'player_id': 0, 'num_children': 3}
        )
        
        # 演示节点扩展日志
        print("2. 演示节点扩展日志")
        policy_output = {
            'policy_entropy': 2.1,
            'max_policy_prob': 0.4,
            'num_legal_actions': 5
        }
        
        logger.log_node_expansion(
            node=mock_node,
            policy_output=policy_output,
            expansion_time=0.002,
            num_children=5
        )
        
        # 演示搜索路径日志
        print("3. 演示搜索路径日志")
        mock_path = []
        for i in range(3):
            value = 0.5 + i * 0.1  # 捕获当前的i值
            node = type('MockNode', (), {
                'visit_count': i + 1,
                'value': value,
                'state': {'depth': i, 'player': i % 2}
            })()
            mock_path.append(node)
        
        logger.log_search_path(
            path=mock_path,
            path_value=0.7,
            depth=3
        )
        
        # 演示性能统计日志
        print("4. 演示性能统计日志")
        logger.log_performance_stats({
            'demo_metric': 42,
            'search_efficiency': 0.85
        })
        
        # 显示会话统计
        stats = logger.get_session_stats()
        print(f"\n会话统计: {json.dumps(stats, indent=2, ensure_ascii=False)}")


def demo_formatters():
    """演示不同格式化器的效果"""
    print("\n=== 格式化器演示 ===")
    
    if not MCTS_LOGGING_AVAILABLE:
        print("MCTS日志模块不可用，跳过演示")
        return
    
    # 测试数据
    timestamp = time.time()
    session_id = generate_session_id()
    test_data = {
        'parent_visits': 15,
        'children_scores': [
            {'action': 0, 'ucb_score': 0.85, 'visits': 8},
            {'action': 1, 'ucb_score': 0.72, 'visits': 5},
            {'action': 2, 'ucb_score': 0.68, 'visits': 2}
        ],
        'selected_action': 0,
        'game_context': {'player_id': 1, 'turn': 5}
    }
    
    # JSON格式化器
    print("1. JSON格式化器输出:")
    json_formatter = JSONFormatter(indent=2)
    json_output = json_formatter.format_log(timestamp, session_id, 'ucb_calculation', test_data)
    print(json_output)

    # 文本格式化器
    print("\n2. 文本格式化器输出:")
    text_formatter = TextFormatter()
    text_output = text_formatter.format_log(timestamp, session_id, 'ucb_calculation', test_data)
    print(text_output)


def demo_performance_monitoring():
    """演示性能监控功能"""
    print("\n=== 性能监控演示 ===")
    
    if not MCTS_LOGGING_AVAILABLE:
        print("MCTS日志模块不可用，跳过演示")
        return
    
    # 创建性能监控器
    monitor = PerformanceMonitor(enable_memory_monitoring=True)
    
    print("开始性能监控演示...")
    
    # 模拟搜索过程
    search_id = "demo_search"
    monitor.start_search_timing(search_id)
    
    # 模拟多次UCB计算
    for i in range(5):
        ucb_id = f"ucb_{i}"
        monitor.start_ucb_timing(ucb_id)
        time.sleep(0.001)  # 模拟UCB计算时间
        monitor.end_ucb_timing(ucb_id)
    
    # 模拟节点扩展
    for i in range(3):
        expansion_id = f"expansion_{i}"
        monitor.start_expansion_timing(expansion_id)
        time.sleep(0.002)  # 模拟扩展时间
        monitor.end_expansion_timing(expansion_id, num_children=4)
    
    # 记录树深度
    monitor.record_tree_depth(10)
    
    # 结束搜索
    time.sleep(0.01)  # 模拟总搜索时间
    search_time = monitor.end_search_timing(search_id, num_simulations=50)
    
    # 生成性能报告
    report = monitor.generate_performance_report()
    
    print(f"搜索总时间: {search_time:.4f}秒")
    print(f"性能报告:")
    print(json.dumps(report, indent=2, ensure_ascii=False))


def demo_config_management():
    """演示配置管理功能"""
    print("\n=== 配置管理演示 ===")
    
    if not MCTS_LOGGING_AVAILABLE:
        print("MCTS日志模块不可用，跳过演示")
        return
    
    # 从文件加载配置
    config_file = project_root / "config" / "mcts_logging_config.yaml"
    if config_file.exists():
        print(f"从配置文件加载: {config_file}")
        # 由于LogConfig没有from_file方法，我们使用默认配置
        config = LogConfig()
        print(f"使用默认配置: {config.to_dict()}")
    else:
        print("配置文件不存在，使用默认配置")
        config = LogConfig()

    # 验证配置
    is_valid = config.validate()
    print(f"配置验证结果: {'有效' if is_valid else '无效'}")

    # 演示配置修改
    print("\n演示配置修改:")
    config.level = 'DEBUG'
    config.output_format = 'text'
    print(f"修改后配置: level={config.level}, format={config.output_format}")


def demo_log_analysis():
    """演示日志分析功能"""
    print("\n=== 日志分析演示 ===")
    
    log_file = "logs/demo_mcts.log"
    if not os.path.exists(log_file):
        print("日志文件不存在，请先运行基本日志演示")
        return
    
    print(f"分析日志文件: {log_file}")
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"日志总行数: {len(lines)}")
        
        # 统计不同类型的日志
        log_types = {}
        for line in lines:
            try:
                log_entry = json.loads(line.strip())
                log_type = log_entry.get('type', 'unknown')
                log_types[log_type] = log_types.get(log_type, 0) + 1
            except json.JSONDecodeError:
                continue
        
        print("日志类型统计:")
        for log_type, count in log_types.items():
            print(f"  {log_type}: {count}")
        
        # 显示最后几条日志
        print("\n最后3条日志:")
        for line in lines[-3:]:
            try:
                log_entry = json.loads(line.strip())
                print(f"  [{log_entry.get('type', 'unknown')}] {log_entry.get('timestamp', 'no-time')}")
            except json.JSONDecodeError:
                print(f"  [解析失败] {line.strip()[:50]}...")
    
    except Exception as e:
        print(f"日志分析失败: {e}")


def main():
    """主演示函数"""
    print("MCTS日志增强功能演示")
    print("=" * 50)
    
    # 确保日志目录存在
    os.makedirs("logs", exist_ok=True)
    
    # 运行各个演示
    demo_basic_logging()
    demo_formatters()
    demo_performance_monitoring()
    demo_config_management()
    demo_log_analysis()
    
    print("\n演示完成！")
    print("查看生成的日志文件: logs/demo_mcts.log")


if __name__ == "__main__":
    main()
