# 自动生成的训练配置模板
# 基于硬件检测结果自动优化参数
# 生成时间: {{ "now" | strftime("%Y-%m-%d %H:%M:%S") }}
# 硬件等级: {{ performance_tier }}
# GPU数量: {{ gpu_count }}
# 总显存: {{ "%.1f" | format(total_memory_gb) }}GB

# 项目基础信息
project:
  name: "斗地主AI训练"
  version: "2.0"
  description: "基于{{ performance_tier }}级硬件的自动优化配置"

# 设备配置
device:
  {% if gpu_count > 0 %}
  type: "cuda"
  ids: {{ list(range(gpu_count)) }}
  mixed_precision: true
  benchmark: true
  deterministic: false
  {% else %}
  type: "cpu"
  mixed_precision: false
  {% endif %}

# 训练配置
training:
  # 自动优化的批次大小
  batch_size: {{ optimal.batch_size }}
  
  # 自动调整的学习率
  learning_rate: {{ "%.6f" | format(optimal.learning_rate) }}
  
  # 训练轮数
  epochs: 1000
  
  # 梯度配置
  gradient:
    clip_norm: 5.0
    accumulation_steps: {% if gpu_count > 1 %}{{ gpu_count }}{% else %}1{% endif %}
  
  # 优化器配置
  optimizer:
    name: "AdamW"
    weight_decay: 0.01
    betas: [0.9, 0.999]
    eps: 1e-8
  
  # 学习率调度
  scheduler:
    name: "CosineAnnealingWarmRestarts"
    T_0: 100
    T_mult: 2
    eta_min: {{ "%.8f" | format(optimal.learning_rate * 0.01) }}

# 数据配置
data:
  # 自动优化的工作线程数
  num_workers: {{ optimal.num_workers }}
  
  # 自动调整的预取因子
  prefetch_factor: {{ optimal.prefetch_factor }}
  
  # 其他数据配置
  pin_memory: {% if gpu_count > 0 %}true{% else %}false{% endif %}
  persistent_workers: true
  
  # 缓存配置（基于总内存）
  cache_size_gb: {% if hardware.cpu.memory_total > 32 %}8{% elif hardware.cpu.memory_total > 16 %}4{% else %}2{% endif %}
  
  # 数据增强
  augmentation:
    enabled: true
    probability: 0.3

# MCTS配置
mcts:
  # 自动优化的模拟次数
  num_simulations: {{ optimal.mcts_simulations }}
  
  # 并行线程数（基于GPU数量）
  parallel_threads: {% if gpu_count > 0 %}{{ min(8, gpu_count * 2) }}{% else %}2{% endif %}
  
  # 批量推理大小
  batch_size_inference: {% if gpu_count > 0 %}{{ min(32, optimal.batch_size // 8) }}{% else %}8{% endif %}
  
  # 探索参数
  c_puct: 1.25
  dirichlet_alpha: 0.3
  exploration_fraction: 0.25

# 分布式训练配置
distributed:
  enabled: {% if gpu_count > 1 %}true{% else %}false{% endif %}
  {% if gpu_count > 1 %}
  backend: "nccl"
  init_method: "env://"
  world_size: {{ gpu_count }}
  sync_frequency: 10
  bucket_size_mb: 25
  {% endif %}

# 内存管理
memory:
  # 最大GPU内存使用率
  max_memory_usage: {% if performance_tier == "ultra" %}0.95{% elif performance_tier == "high" %}0.90{% else %}0.85{% endif %}
  
  # 梯度检查点（大模型时启用）
  gradient_checkpointing: {% if total_memory_gb < 20 %}true{% else %}false{% endif %}
  
  # 内存清理频率
  empty_cache_frequency: {% if performance_tier in ["low", "medium"] %}500{% else %}1000{% endif %}
  gc_frequency: {% if performance_tier in ["low", "medium"] %}1000{% else %}2000{% endif %}

# 日志和监控
logging:
  level: "INFO"
  save_frequency: 100
  tensorboard:
    enabled: true
    log_dir: "logs/tensorboard"
    update_frequency: 50
  
  # 性能监控
  performance:
    enabled: true
    gpu_monitoring: {% if gpu_count > 0 %}true{% else %}false{% endif %}
    memory_monitoring: true
    log_frequency: 200

# 模型保存
checkpoint:
  save_frequency: 1000
  max_keep: 5
  save_best: true
  save_optimizer: true
  
  # 自动压缩（低性能硬件）
  compression: {% if performance_tier in ["low", "medium"] %}true{% else %}false{% endif %}

# 验证和测试
evaluation:
  frequency: 500
  num_games: {% if performance_tier == "ultra" %}1000{% elif performance_tier == "high" %}500{% else %}200{% endif %}
  
  # 对手配置
  opponents:
    - type: "random"
      weight: 0.3
    - type: "rule_based"
      weight: 0.4
    - type: "previous_model"
      weight: 0.3

# 算法特定配置
algorithm:
  name: "EfficientZero"
  
  # 网络架构
  network:
    hidden_size: {% if performance_tier == "ultra" %}512{% elif performance_tier == "high" %}384{% else %}256{% endif %}
    num_layers: {% if performance_tier == "ultra" %}8{% elif performance_tier == "high" %}6{% else %}4{% endif %}
    attention_heads: {% if performance_tier == "ultra" %}8{% elif performance_tier == "high" %}6{% else %}4{% endif %}
  
  # 训练参数
  unroll_steps: 5
  td_steps: 5
  discount: 0.997
  
  # 优先级经验回放
  replay_buffer:
    size: {% if performance_tier == "ultra" %}1000000{% elif performance_tier == "high" %}500000{% else %}200000{% endif %}
    alpha: 0.6
    beta: 0.4
    beta_increment: 0.001

# 游戏环境配置
environment:
  name: "doudizhu"
  
  # 奖励配置
  rewards:
    win: 1.0
    lose: -1.0
    
    # 农民协作奖励
    farmer_cooperation: 0.8
    team_reward_weight: 0.9
    
    # 动作奖励
    pass_action: 0.0
    bomb_play: 0.01
  
  # 游戏规则
  rules:
    enable_jokers: true
    enable_bombs: true
    max_game_length: 1000

# 硬件信息（仅供参考）
_hardware_info:
  performance_tier: "{{ performance_tier }}"
  gpu_count: {{ gpu_count }}
  gpu_models: [{% for gpu in hardware.gpus %}"{{ gpu.name }}"{% if not loop.last %}, {% endif %}{% endfor %}]
  total_memory_gb: {{ "%.1f" | format(total_memory_gb) }}
  cpu_cores: {{ hardware.cpu.cores_logical }}
  system_info: "{{ hardware.system.os_name }} {{ hardware.system.os_version }}"
  generated_at: "{{ "now" | strftime("%Y-%m-%d %H:%M:%S") }}"
