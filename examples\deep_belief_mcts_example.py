"""
DeepBeliefTracker与MCTS集成示例

展示如何加载和使用DeepBeliefTracker与MCTS进行游戏。
"""

import os
import sys
import argparse
import torch
import numpy as np
import logging
from typing import Dict, Any

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cardgame_ai.algorithms.mcts_agent import MCTSAgent
from cardgame_ai.algorithms.belief_tracking.deep_belief_tracker import DeepBeliefTracker, TransformerBeliefTracker
from cardgame_ai.config.mcts_config import get_mcts_config
from cardgame_ai.models.value_policy_net import ValuePolicyNetwork
from cardgame_ai.games.doudizhu.game import <PERSON><PERSON><PERSON><PERSON><PERSON>ame
from cardgame_ai.games.doudizhu.state import <PERSON><PERSON>zhuState
from cardgame_ai.games.doudizhu.card import Card
from cardgame_ai.utils.logger import setup_logger

# 配置日志
setup_logger()
logger = logging.getLogger(__name__)

def load_belief_tracker_model(model_path: str, device: str = 'cpu') -> TransformerBeliefTracker:
    """
    加载信念追踪器模型
    
    Args:
        model_path (str): 模型路径
        device (str, optional): 设备. Defaults to 'cpu'.
        
    Returns:
        TransformerBeliefTracker: 信念追踪器模型
    """
    try:
        # 加载模型配置
        model_config = {
            'input_dim': 256,  # 输入维度
            'hidden_dim': 512,  # 隐藏层维度
            'output_dim': 54,  # 输出维度（牌的数量）
            'num_heads': 8,  # 多头注意力头数
            'num_layers': 6,  # Transformer层数
            'dropout': 0.1,  # Dropout比率
            'max_seq_length': 200  # 最大序列长度
        }
        
        # 创建模型
        model = TransformerBeliefTracker(**model_config).to(device)
        
        # 加载权重
        if os.path.exists(model_path):
            model.load_state_dict(torch.load(model_path, map_location=device))
            logger.info(f"成功加载信念追踪器模型: {model_path}")
        else:
            logger.warning(f"模型文件不存在: {model_path}，使用随机初始化的模型")
        
        return model
    except Exception as e:
        logger.error(f"加载信念追踪器模型失败: {e}")
        return None

def create_deep_belief_tracker(model: TransformerBeliefTracker, player_id: str, device: str = 'cpu') -> DeepBeliefTracker:
    """
    创建深度信念追踪器
    
    Args:
        model (TransformerBeliefTracker): 信念追踪器模型
        player_id (str): 玩家ID
        device (str, optional): 设备. Defaults to 'cpu'.
        
    Returns:
        DeepBeliefTracker: 深度信念追踪器
    """
    # 创建斗地主牌的映射
    card_mapping = {}
    for suit in ['H', 'S', 'D', 'C']:  # 红桃、黑桃、方块、梅花
        for rank in range(3, 16):  # 3-A
            card_str = f"{suit}{rank}"
            card_mapping[card_str] = 1.0 / 54  # 初始均匀分布
    
    # 添加大小王
    card_mapping['BR'] = 1.0 / 54  # 大王
    card_mapping['SR'] = 1.0 / 54  # 小王
    
    # 创建追踪器
    tracker = DeepBeliefTracker(
        player_id=player_id,
        model=model,
        card_mapping=card_mapping,
        device=device,
        max_history_length=100,
        initial_hand_size=17  # 斗地主初始手牌数量
    )
    
    return tracker

def run_game_with_deep_belief_mcts():
    """运行使用DeepBeliefTracker的MCTS游戏示例"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='运行集成DeepBeliefTracker的MCTS游戏示例')
    parser.add_argument('--config', type=str, default='deep_belief', help='MCTS配置名称')
    parser.add_argument('--model_path', type=str, default='models/belief_tracker/transformer_belief_model.pt', 
                        help='DeepBeliefTracker模型路径')
    parser.add_argument('--device', type=str, default='cuda' if torch.cuda.is_available() else 'cpu', 
                        help='运行设备')
    parser.add_argument('--num_games', type=int, default=1, help='游戏次数')
    args = parser.parse_args()
    
    # 加载MCTS配置
    mcts_config = get_mcts_config(args.config)
    logger.info(f"使用MCTS配置: {args.config}")
    
    # 加载信念追踪器模型
    tracker_model = load_belief_tracker_model(args.model_path, args.device)
    
    # 创建游戏和智能体
    game = DoudizhuGame()
    
    # 创建策略网络模型（简化示例，实际应该加载训练好的模型）
    value_policy_model = ValuePolicyNetwork(
        input_dim=game.get_state_size(),
        action_dim=game.get_action_size(),
        hidden_dim=512
    ).to(args.device)
    
    # 创建MCTS代理
    mcts_agent = MCTSAgent(
        model=value_policy_model,
        num_simulations=mcts_config['num_simulations'],
        discount=mcts_config['discount'],
        dirichlet_alpha=mcts_config['dirichlet_alpha'],
        exploration_fraction=mcts_config['exploration_fraction'],
        pb_c_base=mcts_config['pb_c_base'],
        pb_c_init=mcts_config['pb_c_init'],
        use_belief_state=mcts_config['use_belief_state'],
        use_information_value=mcts_config['use_information_value'],
        information_value_weight=mcts_config['information_value_weight'],
        information_value_method=mcts_config['information_value_method'],
        use_deep_belief_tracker=mcts_config['use_deep_belief_tracker'],
        deep_belief_weight=mcts_config['deep_belief_weight'],
        use_opponent_model_prior=mcts_config['use_opponent_model_prior'],
        opponent_model_prior_weight=mcts_config['opponent_model_prior_weight']
    )
    
    # 创建并注册深度信念追踪器（为每个玩家）
    player_ids = ['landlord', 'farmer1', 'farmer2']
    for player_id in player_ids:
        # 创建深度信念追踪器
        deep_tracker = create_deep_belief_tracker(tracker_model, player_id, args.device)
        
        # 注册到MCTS代理
        mcts_agent.register_belief_tracker(player_id, deep_tracker)
    
    # 使用MCTS代理运行游戏
    for game_idx in range(args.num_games):
        logger.info(f"开始游戏 {game_idx + 1}/{args.num_games}")
        
        # 初始化游戏
        state = game.reset()
        done = False
        
        # 游戏循环
        while not done:
            # 获取当前玩家ID
            current_player_id = state.current_player
            
            # 获取合法动作
            legal_actions = game.get_legal_actions(state)
            
            # 使用MCTS代理做决策
            action = mcts_agent.act(state, legal_actions)
            
            # 执行动作
            next_state, reward, done, info = game.step(state, action)
            
            # 更新深度信念追踪器
            for player_id in player_ids:
                if player_id in mcts_agent.deep_belief_trackers:
                    tracker = mcts_agent.deep_belief_trackers[player_id]
                    
                    # 提取已知信息
                    public_knowledge = {
                        'played_cards': info.get('played_cards', {}),
                        'hand_sizes': info.get('hand_sizes', {})
                    }
                    
                    # 更新追踪器
                    tracker.update(
                        opponent_action=info.get('last_action', []),
                        public_knowledge=public_knowledge,
                        current_state=np.array(state.to_numpy())
                    )
            
            # 更新状态
            state = next_state
            
            # 输出游戏进度
            if info.get('event') == 'card_played':
                logger.info(f"玩家 {current_player_id} 出牌: {action}")
        
        # 输出游戏结果
        logger.info(f"游戏 {game_idx + 1} 结束!")
        for player_id, player_reward in reward.items():
            logger.info(f"玩家 {player_id} 得分: {player_reward}")
    
    logger.info("所有游戏结束!")

if __name__ == "__main__":
    run_game_with_deep_belief_mcts() 