"""
MAML元学习算法框架

实现Model-Agnostic Meta-Learning (MAML)算法，用于快速适应新对手。
提供了一个通用框架，可以应用于各种基础模型（如TransformerPolicy）。
"""

import os
import time
import logging
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Any, Optional, Union, Callable
from collections import defaultdict, deque

from cardgame_ai.core.base import Experience, Batch
from cardgame_ai.core.agent import Agent
from cardgame_ai.core.environment import Environment

# 尝试导入higher库，用于实现MAML
try:
    import higher
    HIGHER_AVAILABLE = True
except ImportError:
    HIGHER_AVAILABLE = False
    logging.warning("higher库不可用，MAML算法将不可用。请使用pip install higher安装。")

# 配置日志
logger = logging.getLogger(__name__)


class MetaTask:
    """
    元任务类
    
    表示一个元学习任务，包含支持集和查询集数据。
    在卡牌游戏中，一个任务通常表示一个特定类型的对手。
    """
    
    def __init__(self, task_id: str, task_config: Dict[str, Any] = None):
        """
        初始化元任务
        
        Args:
            task_id: 任务ID
            task_config: 任务配置，可包含对手类型、难度等信息
        """
        self.task_id = task_id
        self.config = task_config or {}
        self.support_data = []  # 支持集（用于内循环适应）
        self.query_data = []    # 查询集（用于外循环评估）
    
    def add_support_data(self, data: Any) -> None:
        """添加支持集数据"""
        if isinstance(data, list):
            self.support_data.extend(data)
        else:
            self.support_data.append(data)
    
    def add_query_data(self, data: Any) -> None:
        """添加查询集数据"""
        if isinstance(data, list):
            self.query_data.extend(data)
        else:
            self.query_data.append(data)
    
    def clear_data(self) -> None:
        """清除所有数据"""
        self.support_data.clear()
        self.query_data.clear()
    
    def __str__(self) -> str:
        return f"MetaTask(id={self.task_id}, config={self.config}, " \
               f"support_size={len(self.support_data)}, query_size={len(self.query_data)})"


class MetaTaskSampler:
    """
    元任务采样器
    
    负责生成和管理不同类型的元任务，如不同风格的对手。
    """
    
    def __init__(self):
        """初始化元任务采样器"""
        self.tasks = {}  # 任务字典，键为任务ID，值为MetaTask对象
    
    def generate_opponent_tasks(self, 
                               num_tasks: int = 5, 
                               difficulty_range: Tuple[float, float] = (0.1, 1.0),
                               style_options: List[str] = None) -> List[MetaTask]:
        """
        生成对手任务
        
        Args:
            num_tasks: 任务数量
            difficulty_range: 难度范围，元组(最小难度, 最大难度)
            style_options: 对手风格选项，如["aggressive", "defensive", "balanced"]
            
        Returns:
            生成的任务列表
        """
        if style_options is None:
            style_options = ["aggressive", "defensive", "balanced", "random"]
        
        tasks = []
        
        for i in range(num_tasks):
            # 随机选择对手风格
            style = np.random.choice(style_options)
            
            # 随机生成难度
            difficulty = np.random.uniform(*difficulty_range)
            
            # 创建任务配置
            task_config = {
                "style": style,
                "difficulty": difficulty,
                "created_at": time.time()
            }
            
            # 创建任务ID
            task_id = f"opponent_{style}_{difficulty:.2f}_{i}"
            
            # 创建任务
            task = MetaTask(task_id, task_config)
            
            # 添加到任务字典和列表
            self.tasks[task_id] = task
            tasks.append(task)
        
        return tasks
    
    def add_task(self, task: MetaTask) -> None:
        """添加任务"""
        self.tasks[task.task_id] = task
    
    def get_task(self, task_id: str) -> Optional[MetaTask]:
        """获取任务"""
        return self.tasks.get(task_id)
    
    def sample_tasks(self, num_tasks: int = 5) -> List[MetaTask]:
        """随机采样任务"""
        if not self.tasks:
            return []
        
        task_ids = list(self.tasks.keys())
        selected_ids = np.random.choice(task_ids, 
                                       size=min(num_tasks, len(task_ids)), 
                                       replace=False)
        
        return [self.tasks[task_id] for task_id in selected_ids]
    
    def sample_data(self, task: Union[str, MetaTask], data_type: str = 'support', 
                   batch_size: int = None) -> Any:
        """
        从任务中采样数据
        
        Args:
            task: 任务对象或任务ID
            data_type: 数据类型，'support'或'query'
            batch_size: 批次大小，如果为None则返回所有数据
            
        Returns:
            采样的数据
        """
        # 获取任务对象
        if isinstance(task, str):
            task = self.get_task(task)
            if task is None:
                return None
        
        # 获取数据
        data = task.support_data if data_type == 'support' else task.query_data
        
        if not data:
            return None
        
        # 如果指定了批次大小，则随机采样
        if batch_size is not None and batch_size < len(data):
            indices = np.random.choice(len(data), size=batch_size, replace=False)
            return [data[i] for i in indices]
        
        return data


class MAMLMetaLearner:
    """
    Model-Agnostic Meta-Learning (MAML)元学习器
    
    实现MAML算法，用于快速适应新对手。
    """
    
    def __init__(
        self,
        base_model: nn.Module,
        meta_optimizer: torch.optim.Optimizer,
        task_sampler: MetaTaskSampler,
        inner_lr: float = 0.01,
        num_inner_steps: int = 5,
        first_order: bool = False,
        device: str = 'cuda' if torch.cuda.is_available() else 'cpu'
    ):
        """
        初始化MAML元学习器
        
        Args:
            base_model: 基础模型
            meta_optimizer: 元优化器
            task_sampler: 任务采样器
            inner_lr: 内循环学习率
            num_inner_steps: 内循环步数
            first_order: 是否使用一阶近似（忽略高阶导数）
            device: 设备
        """
        self.base_model = base_model
        self.meta_optimizer = meta_optimizer
        self.task_sampler = task_sampler
        self.inner_lr = inner_lr
        self.num_inner_steps = num_inner_steps
        self.first_order = first_order
        self.device = device
        
        # 检查higher库是否可用
        if not HIGHER_AVAILABLE:
            raise ImportError("MAML算法需要higher库。请使用pip install higher安装。")
        
        # 统计信息
        self.stats = {
            "meta_updates": 0,
            "inner_updates": 0,
            "meta_losses": [],
            "adaptation_times": [],
            "meta_update_times": []
        }
    
    def _process_batch(self, batch: Any) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        处理批次数据
        
        Args:
            batch: 批次数据，可以是Experience、Batch、字典或其他格式
            
        Returns:
            处理后的状态和动作张量
        """
        # 根据数据类型进行处理
        if isinstance(batch, Experience):
            # 处理单个Experience
            states = torch.tensor([batch.state], device=self.device)
            actions = torch.tensor([batch.action], device=self.device)
        elif isinstance(batch, Batch):
            # 处理Batch
            states = torch.tensor(batch.states, device=self.device)
            actions = torch.tensor(batch.actions, device=self.device)
        elif isinstance(batch, dict):
            # 处理字典
            states = batch.get("states") or batch.get("observations")
            actions = batch.get("actions")
            
            if states is None or actions is None:
                raise ValueError("批次字典必须包含'states'/'observations'和'actions'键")
            
            if not isinstance(states, torch.Tensor):
                states = torch.tensor(states, device=self.device)
            if not isinstance(actions, torch.Tensor):
                actions = torch.tensor(actions, device=self.device)
        elif isinstance(batch, list):
            # 处理列表
            if not batch:
                raise ValueError("批次列表不能为空")
            
            if isinstance(batch[0], dict):
                # 列表中的元素是字典
                states = []
                actions = []
                
                for item in batch:
                    state = item.get("state") or item.get("observation")
                    action = item.get("action")
                    
                    if state is not None and action is not None:
                        states.append(state)
                        actions.append(action)
                
                states = torch.tensor(states, device=self.device)
                actions = torch.tensor(actions, device=self.device)
            elif isinstance(batch[0], Experience):
                # 列表中的元素是Experience
                states = torch.tensor([exp.state for exp in batch], device=self.device)
                actions = torch.tensor([exp.action for exp in batch], device=self.device)
            else:
                raise ValueError(f"不支持的批次列表元素类型: {type(batch[0])}")
        else:
            raise ValueError(f"不支持的批次数据类型: {type(batch)}")
        
        return states, actions
    
    def _calculate_loss(self, model: nn.Module, batch: Any) -> torch.Tensor:
        """
        计算损失
        
        Args:
            model: 模型
            batch: 批次数据
            
        Returns:
            损失
        """
        try:
            # 处理批次数据
            states, actions = self._process_batch(batch)
            
            # 前向传播
            logits = model(states)
            
            # 计算交叉熵损失
            loss = F.cross_entropy(logits, actions)
            
            return loss
        except Exception as e:
            logger.error(f"计算损失时发生错误: {e}")
            # 返回零损失，避免训练中断
            return torch.tensor(0.0, device=self.device)
    
    def meta_train_step(self, num_tasks: int = 5) -> float:
        """
        执行一步元训练
        
        Args:
            num_tasks: 任务数量
            
        Returns:
            元损失
        """
        self.meta_optimizer.zero_grad()
        meta_update_start_time = time.time()
        
        # 采样任务
        tasks = self.task_sampler.sample_tasks(num_tasks)
        if not tasks:
            logger.warning("没有可用的任务")
            return 0.0
        
        total_meta_loss = 0.0
        
        for task in tasks:
            # 创建内循环优化器
            inner_optimizer = torch.optim.SGD(
                self.base_model.parameters(),
                lr=self.inner_lr
            )
            
            # 使用higher库创建可微分优化循环
            with higher.innerloop_ctx(
                self.base_model, 
                inner_optimizer, 
                copy_initial_weights=False, 
                track_higher_grads=not self.first_order
            ) as (fmodel, diffopt):
                # 内循环：适应任务
                for _ in range(self.num_inner_steps):
                    # 采样支持集数据
                    support_batch = self.task_sampler.sample_data(task, 'support')
                    if not support_batch:
                        continue
                    
                    # 计算支持集损失
                    support_loss = self._calculate_loss(fmodel, support_batch)
                    
                    # 更新模型
                    diffopt.step(support_loss)
                    
                    # 更新内循环统计
                    self.stats["inner_updates"] += 1
                
                # 采样查询集数据
                query_batch = self.task_sampler.sample_data(task, 'query')
                if not query_batch:
                    continue
                
                # 计算查询集损失（元损失）
                query_loss = self._calculate_loss(fmodel, query_batch)
                
                # 累加元损失
                total_meta_loss += query_loss
        
        # 计算平均元损失
        meta_loss = total_meta_loss / len(tasks)
        
        # 反向传播
        meta_loss.backward()
        
        # 更新元优化器
        self.meta_optimizer.step()
        
        # 更新元训练统计
        self.stats["meta_updates"] += 1
        self.stats["meta_losses"].append(meta_loss.item())
        self.stats["meta_update_times"].append(time.time() - meta_update_start_time)
        
        return meta_loss.item()
    
    def adapt(self, task: Union[str, MetaTask], num_steps: int = None,
             learning_rate: float = None) -> nn.Module:
        """
        快速适应到特定任务
        
        Args:
            task: 任务对象或任务ID
            num_steps: 适应步数，如果为None则使用初始设置的步数
            learning_rate: 学习率，如果为None则使用初始设置的学习率
            
        Returns:
            适应后的模型
        """
        adaptation_start_time = time.time()
        
        # 获取任务对象
        if isinstance(task, str):
            task = self.task_sampler.get_task(task)
            if task is None:
                logger.error(f"找不到任务: {task}")
                return self.base_model
        
        # 设置适应参数
        num_steps = num_steps if num_steps is not None else self.num_inner_steps
        learning_rate = learning_rate if learning_rate is not None else self.inner_lr
        
        # 创建适应模型的副本
        adapted_model = type(self.base_model)(*self.base_model.__init_args__)
        adapted_model.load_state_dict(self.base_model.state_dict())
        adapted_model.to(self.device)
        
        # 创建优化器
        optimizer = torch.optim.SGD(adapted_model.parameters(), lr=learning_rate)
        
        # 执行适应步骤
        for _ in range(num_steps):
            # 采样支持集数据
            support_batch = self.task_sampler.sample_data(task, 'support')
            if not support_batch:
                continue
            
            # 计算损失
            loss = self._calculate_loss(adapted_model, support_batch)
            
            # 更新模型
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
        
        # 更新适应统计
        self.stats["adaptation_times"].append(time.time() - adaptation_start_time)
        
        return adapted_model
    
    def evaluate(self, task: Union[str, MetaTask], model: Optional[nn.Module] = None) -> Dict[str, float]:
        """
        评估模型在特定任务上的性能
        
        Args:
            task: 任务对象或任务ID
            model: 要评估的模型，如果为None则使用基础模型
            
        Returns:
            评估结果字典
        """
        # 获取任务对象
        if isinstance(task, str):
            task = self.task_sampler.get_task(task)
            if task is None:
                logger.error(f"找不到任务: {task}")
                return {"loss": float('inf'), "accuracy": 0.0}
        
        # 使用指定模型或基础模型
        model = model if model is not None else self.base_model
        
        # 采样查询集数据
        query_batch = self.task_sampler.sample_data(task, 'query')
        if not query_batch:
            logger.warning(f"任务 {task.task_id} 没有查询集数据")
            return {"loss": float('inf'), "accuracy": 0.0}
        
        # 处理批次数据
        states, actions = self._process_batch(query_batch)
        
        # 前向传播
        with torch.no_grad():
            logits = model(states)
        
        # 计算损失
        loss = F.cross_entropy(logits, actions).item()
        
        # 计算准确率
        predictions = torch.argmax(logits, dim=1)
        accuracy = (predictions == actions).float().mean().item()
        
        return {"loss": loss, "accuracy": accuracy}
    
    def save(self, path: str) -> None:
        """
        保存元学习器
        
        Args:
            path: 保存路径
        """
        os.makedirs(os.path.dirname(path), exist_ok=True)
        
        # 准备要保存的数据
        save_data = {
            "base_model": self.base_model.state_dict(),
            "meta_optimizer": self.meta_optimizer.state_dict(),
            "inner_lr": self.inner_lr,
            "num_inner_steps": self.num_inner_steps,
            "first_order": self.first_order,
            "stats": self.stats
        }
        
        # 保存数据
        torch.save(save_data, path)
        logger.info(f"元学习器已保存到 {path}")
    
    def load(self, path: str) -> None:
        """
        加载元学习器
        
        Args:
            path: 加载路径
        """
        if not os.path.exists(path):
            logger.error(f"找不到保存的元学习器: {path}")
            return
        
        # 加载数据
        data = torch.load(path, map_location=self.device)
        
        # 加载模型参数
        self.base_model.load_state_dict(data["base_model"])
        
        # 加载优化器参数
        self.meta_optimizer.load_state_dict(data["meta_optimizer"])
        
        # 加载其他参数
        self.inner_lr = data.get("inner_lr", self.inner_lr)
        self.num_inner_steps = data.get("num_inner_steps", self.num_inner_steps)
        self.first_order = data.get("first_order", self.first_order)
        self.stats = data.get("stats", self.stats)
        
        logger.info(f"元学习器已从 {path} 加载")


# 示例：如何使用MAML元学习器
def example_usage():
    """元学习器使用示例"""
    import torch.optim as optim
    from cardgame_ai.algorithms.transformer_policy import TransformerPolicy
    
    # 创建基础模型
    base_model = TransformerPolicy(
        state_shape=(100, 64),  # 示例尺寸
        action_shape=(1000,),   # 示例尺寸
        hidden_dim=256,
        num_heads=4,
        num_layers=4
    )
    
    # 创建元优化器
    meta_optimizer = optim.Adam(base_model.parameters(), lr=0.001)
    
    # 创建任务采样器
    task_sampler = MetaTaskSampler()
    
    # 生成任务
    tasks = task_sampler.generate_opponent_tasks(
        num_tasks=5,
        difficulty_range=(0.1, 1.0),
        style_options=["aggressive", "defensive", "balanced"]
    )
    
    # 创建元学习器
    maml = MAMLMetaLearner(
        base_model=base_model,
        meta_optimizer=meta_optimizer,
        task_sampler=task_sampler,
        inner_lr=0.01,
        num_inner_steps=5,
        first_order=False,
        device='cuda' if torch.cuda.is_available() else 'cpu'
    )
    
    # 元训练
    for step in range(100):
        meta_loss = maml.meta_train_step(num_tasks=5)
        print(f"步骤 {step + 1}, 元损失: {meta_loss:.4f}")
    
    # 选择一个任务进行适应
    task = tasks[0]
    
    # 快速适应
    adapted_model = maml.adapt(task, num_steps=5)
    
    # 评估基础模型
    base_results = maml.evaluate(task)
    print(f"基础模型性能: 损失={base_results['loss']:.4f}, 准确率={base_results['accuracy']:.4f}")
    
    # 评估适应后的模型
    adapted_results = maml.evaluate(task, adapted_model)
    print(f"适应后模型性能: 损失={adapted_results['loss']:.4f}, 准确率={adapted_results['accuracy']:.4f}") 