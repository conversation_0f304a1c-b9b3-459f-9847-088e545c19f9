"""
手牌信息价值评估示例脚本

演示如何使用手牌信息价值评估器。
"""

import os
import sys
import argparse
import logging
import numpy as np
import torch
from typing import Dict, List, Any, Optional

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cardgame_ai.algorithms.card_information_value import CardInformationValueEstimator
from cardgame_ai.games.common.belief_state import BeliefState
from cardgame_ai.games.doudizhu.card import Card
from cardgame_ai.games.doudizhu.game import DouDizhuGame
from cardgame_ai.games.doudizhu.state import DouDizhuState
from cardgame_ai.algorithms.efficient_zero import EfficientZeroModel

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def parse_args():
    """
    解析命令行参数
    
    Returns:
        argparse.Namespace: 解析后的参数
    """
    parser = argparse.ArgumentParser(description='手牌信息价值评估示例')
    parser.add_argument('--model_path', type=str, default='models/efficient_zero_model.pth', help='模型路径')
    parser.add_argument('--method', type=str, default='combined', choices=['basic', 'entropy', 'action', 'combined'], help='评估方法')
    parser.add_argument('--context_aware', action='store_true', help='是否考虑上下文信息')
    parser.add_argument('--use_decision_impact', action='store_true', help='是否考虑对决策的影响')
    parser.add_argument('--top_n', type=int, default=5, help='返回信息价值最高的前N张牌')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    
    return parser.parse_args()


def create_test_state() -> DouDizhuState:
    """
    创建测试状态
    
    Returns:
        DouDizhuState: 测试状态
    """
    # 创建游戏
    game = DouDizhuGame()
    
    # 初始化状态
    state = game.get_init_state()
    
    # 模拟发牌
    landlord_cards = [
        Card.from_string('3'), Card.from_string('3'), Card.from_string('4'), Card.from_string('4'),
        Card.from_string('5'), Card.from_string('6'), Card.from_string('7'), Card.from_string('8'),
        Card.from_string('9'), Card.from_string('10'), Card.from_string('J'), Card.from_string('Q'),
        Card.from_string('K'), Card.from_string('A'), Card.from_string('2'), Card.from_string('2'),
        Card.from_string('JOKER')
    ]
    
    farmer1_cards = [
        Card.from_string('3'), Card.from_string('4'), Card.from_string('5'), Card.from_string('5'),
        Card.from_string('6'), Card.from_string('6'), Card.from_string('7'), Card.from_string('8'),
        Card.from_string('9'), Card.from_string('10'), Card.from_string('J'), Card.from_string('Q'),
        Card.from_string('K'), Card.from_string('A'), Card.from_string('A'), Card.from_string('JOKER')
    ]
    
    farmer2_cards = [
        Card.from_string('3'), Card.from_string('4'), Card.from_string('5'), Card.from_string('6'),
        Card.from_string('7'), Card.from_string('7'), Card.from_string('8'), Card.from_string('8'),
        Card.from_string('9'), Card.from_string('9'), Card.from_string('10'), Card.from_string('10'),
        Card.from_string('J'), Card.from_string('Q'), Card.from_string('K'), Card.from_string('K')
    ]
    
    # 设置手牌
    state.player_cards = {
        0: landlord_cards,
        1: farmer1_cards,
        2: farmer2_cards
    }
    
    # 设置地主
    state.landlord = 0
    
    # 设置当前玩家
    state.current_player = 0
    
    # 设置历史动作
    state.history = []
    
    return state


def create_belief_state(known_cards: List[Card], all_cards: List[Card]) -> BeliefState:
    """
    创建信念状态
    
    Args:
        known_cards: 已知的牌
        all_cards: 所有牌
        
    Returns:
        BeliefState: 信念状态
    """
    # 创建信念状态
    belief_state = BeliefState()
    
    # 设置已知牌的概率为0
    for card in known_cards:
        belief_state.set_probability(str(card), 0.0)
    
    # 设置其他牌的概率
    unknown_cards = [card for card in all_cards if card not in known_cards]
    for card in unknown_cards:
        belief_state.set_probability(str(card), 1.0 / len(unknown_cards))
    
    return belief_state


def load_model(model_path: str) -> EfficientZeroModel:
    """
    加载模型
    
    Args:
        model_path: 模型路径
        
    Returns:
        EfficientZeroModel: 加载的模型
    """
    # 检查模型文件是否存在
    if not os.path.exists(model_path):
        logger.warning(f"模型文件不存在: {model_path}，使用随机初始化的模型")
        # 创建随机初始化的模型
        model = EfficientZeroModel(
            observation_shape=(54 * 4 + 54 * 3,),
            action_shape=(54 * 3,),
            hidden_dim=128,
            state_dim=64
        )
        return model
    
    # 加载模型
    try:
        checkpoint = torch.load(model_path, map_location='cpu')
        model = EfficientZeroModel(
            observation_shape=(54 * 4 + 54 * 3,),
            action_shape=(54 * 3,),
            hidden_dim=128,
            state_dim=64
        )
        model.load_state_dict(checkpoint['model_state_dict'])
        logger.info(f"成功加载模型: {model_path}")
        return model
    except Exception as e:
        logger.error(f"加载模型失败: {e}")
        # 创建随机初始化的模型
        model = EfficientZeroModel(
            observation_shape=(54 * 4 + 54 * 3,),
            action_shape=(54 * 3,),
            hidden_dim=128,
            state_dim=64
        )
        return model


def policy_function(belief_state: BeliefState, state: np.ndarray) -> np.ndarray:
    """
    策略函数
    
    Args:
        belief_state: 信念状态
        state: 游戏状态
        
    Returns:
        np.ndarray: 动作概率分布
    """
    # 这里使用随机策略作为示例
    # 在实际应用中，应该使用训练好的策略网络
    return np.random.random(54 * 3)


def main():
    """
    主函数
    """
    # 解析命令行参数
    args = parse_args()
    
    # 设置随机种子
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    
    # 创建测试状态
    state = create_test_state()
    
    # 创建所有牌的列表
    all_cards = []
    for rank in ['3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A', '2']:
        for _ in range(4):
            all_cards.append(Card.from_string(rank))
    all_cards.append(Card.from_string('JOKER'))
    all_cards.append(Card.from_string('JOKER'))
    
    # 创建已知牌的列表（当前玩家的手牌）
    known_cards = state.player_cards[state.current_player]
    
    # 创建信念状态
    belief_state = create_belief_state(known_cards, all_cards)
    
    # 创建手牌信息价值评估器
    estimator = CardInformationValueEstimator(
        method=args.method,
        context_aware=args.context_aware,
        use_decision_impact=args.use_decision_impact
    )
    
    # 创建游戏上下文
    game_context = {
        'game_stage': 'mid',
        'remaining_cards': {
            'player': len(state.player_cards[state.current_player]),
            'opponent': len(state.player_cards[(state.current_player + 1) % 3])
        },
        'history': state.history
    }
    
    # 创建状态向量
    state_vector = np.random.random(54 * 4 + 54 * 3)  # 示例状态向量
    
    # 评估所有未知牌的信息价值
    unknown_cards = [card for card in all_cards if card not in known_cards]
    
    logger.info(f"评估 {len(unknown_cards)} 张未知牌的信息价值...")
    info_values = estimator.estimate_all_cards(
        belief_state=belief_state,
        cards=unknown_cards,
        current_state=state_vector,
        policy_function=policy_function,
        game_context=game_context,
        top_n=args.top_n
    )
    
    # 打印信息价值最高的牌
    logger.info(f"信息价值最高的 {len(info_values)} 张牌:")
    for card, value in sorted(info_values.items(), key=lambda x: x[1], reverse=True):
        logger.info(f"  {card}: {value:.4f}")


if __name__ == '__main__':
    main()
