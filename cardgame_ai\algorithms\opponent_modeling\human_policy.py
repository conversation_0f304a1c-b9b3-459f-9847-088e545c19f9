"""
人类策略网络模块

实现基于监督学习/模仿学习的人类策略网络，能够模仿人类玩家的行为模式。
用于模拟人类玩家的决策过程或作为AI训练的基线模型。
"""

import os
import logging
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from typing import Dict, List, Any, Tuple, Optional, Union
import random
import json
import time
from datetime import datetime

from cardgame_ai.games.doudizhu.state import DouDizhuState
from cardgame_ai.games.doudizhu.card_group import CardGroup
from cardgame_ai.utils.data_collection import InteractionDataCollector

# 配置日志
logger = logging.getLogger(__name__)


class DouDizhuDataset(Dataset):
    """
    斗地主人类数据集

    用于将(state, action)对转换为PyTorch模型可训练的数据集
    """
    
    def __init__(self, states: List[Any], actions: List[Any], transform=None):
        """
        初始化数据集
        
        Args:
            states: 游戏状态列表
            actions: 对应的动作列表
            transform: 数据转换函数（可选）
        """
        assert len(states) == len(actions), "状态和动作数量必须相同"
        self.states = states
        self.actions = actions
        self.transform = transform
        
    def __len__(self):
        return len(self.states)
        
    def __getitem__(self, idx):
        state = self.states[idx]
        action = self.actions[idx]
        
        if self.transform:
            state, action = self.transform(state, action)
            
        # 确保返回的是float32类型的tensor
        if isinstance(state, np.ndarray):
            state = torch.tensor(state, dtype=torch.float32)
        elif isinstance(state, torch.Tensor):
            state = state.float()
            
        # 确保返回的是long类型的tensor
        if isinstance(action, np.ndarray):
            action = torch.tensor(action, dtype=torch.long)
        elif isinstance(action, (int, np.integer)):
            action = torch.tensor(action, dtype=torch.long)
        elif isinstance(action, torch.Tensor):
            action = action.long()
            
        return state, action


class HumanPolicyNetwork(nn.Module):
    """
    人类策略网络
    
    使用神经网络模仿人类玩家的决策行为
    """
    
    def __init__(
        self, 
        input_dim: int = 627,  # 默认为斗地主观察空间维度
        hidden_dims: List[int] = [256, 128],
        action_dim: int = 1,  # 动作空间维度
        dropout_rate: float = 0.2,
        network_type: str = "mlp",
    ):
        """
        初始化人类策略网络
        
        Args:
            input_dim: 输入维度（状态表示）
            hidden_dims: 隐藏层维度列表
            action_dim: 动作维度
            dropout_rate: Dropout比率
            network_type: 网络类型 ("mlp", "cnn", "transformer")
        """
        super(HumanPolicyNetwork, self).__init__()
        
        self.input_dim = input_dim
        self.hidden_dims = hidden_dims
        self.action_dim = action_dim
        self.dropout_rate = dropout_rate
        self.network_type = network_type
        
        # 构建网络结构
        if network_type == "mlp":
            layers = []
            prev_dim = input_dim
            
            for dim in hidden_dims:
                layers.append(nn.Linear(prev_dim, dim))
                layers.append(nn.ReLU())
                layers.append(nn.Dropout(dropout_rate))
                prev_dim = dim
                
            layers.append(nn.Linear(prev_dim, action_dim))
            
            self.model = nn.Sequential(*layers)
            
        elif network_type == "cnn":
            # 假设输入是2D特征图，可以根据实际情况调整
            self.conv_layers = nn.Sequential(
                nn.Conv1d(1, 32, kernel_size=3, padding=1),
                nn.ReLU(),
                nn.MaxPool1d(kernel_size=2),
                nn.Conv1d(32, 64, kernel_size=3, padding=1),
                nn.ReLU(),
                nn.MaxPool1d(kernel_size=2)
            )
            
            # 计算卷积层输出大小（这里需要根据实际输入尺寸调整）
            conv_output_size = 64 * (input_dim // 4)
            
            self.fc_layers = nn.Sequential(
                nn.Linear(conv_output_size, hidden_dims[0]),
                nn.ReLU(),
                nn.Dropout(dropout_rate),
                nn.Linear(hidden_dims[0], action_dim)
            )
            
        elif network_type == "transformer":
            # 简化版Transformer编码器
            self.embedding = nn.Linear(input_dim, hidden_dims[0])
            encoder_layer = nn.TransformerEncoderLayer(
                d_model=hidden_dims[0],
                nhead=4,
                dim_feedforward=hidden_dims[0] * 4,
                dropout=dropout_rate
            )
            self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=2)
            self.fc_out = nn.Linear(hidden_dims[0], action_dim)
        
        else:
            raise ValueError(f"不支持的网络类型: {network_type}")
        
    def forward(self, x):
        """
        前向传播
        
        Args:
            x: 输入状态
            
        Returns:
            动作预测
        """
        # 确保输入是float32类型
        if x.dtype != torch.float32:
            x = x.float()
            
        if self.network_type == "mlp":
            return self.model(x)
            
        elif self.network_type == "cnn":
            # 调整输入形状以适合卷积层
            x = x.unsqueeze(1)  # 添加通道维度
            x = self.conv_layers(x)
            x = x.view(x.size(0), -1)  # 展平
            return self.fc_layers(x)
            
        elif self.network_type == "transformer":
            x = self.embedding(x)
            # 调整形状以适应Transformer（[seq_len, batch_size, embedding_dim]）
            x = x.unsqueeze(0)  # 添加序列长度维度（此处序列长度为1）
            x = self.transformer_encoder(x)
            x = x.squeeze(0)  # 移除序列长度维度
            return self.fc_out(x)


class HumanPolicyTrainer:
    """
    人类策略网络训练器
    
    用于训练人类策略网络模型
    """
    
    def __init__(
        self,
        model: HumanPolicyNetwork,
        learning_rate: float = 0.001,
        device: str = None,
        checkpoint_dir: str = "models/human_policy",
        log_dir: str = "logs/human_policy"
    ):
        """
        初始化训练器
        
        Args:
            model: 人类策略网络模型
            learning_rate: 学习率
            device: 计算设备 ('cpu' 或 'cuda')
            checkpoint_dir: 检查点保存目录
            log_dir: 日志保存目录
        """
        # 设置设备
        self.device = device if device else ('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = model.to(self.device)
        
        # 优化器和损失函数
        self.optimizer = optim.Adam(model.parameters(), lr=learning_rate)
        self.criterion = nn.CrossEntropyLoss()
        
        # 创建目录
        os.makedirs(checkpoint_dir, exist_ok=True)
        os.makedirs(log_dir, exist_ok=True)
        
        self.checkpoint_dir = checkpoint_dir
        self.log_dir = log_dir
        
        # 训练统计
        self.stats = {
            "train_loss": [],
            "val_loss": [],
            "train_accuracy": [],
            "val_accuracy": []
        }
        
    def train(
        self,
        train_dataloader: DataLoader,
        val_dataloader: Optional[DataLoader] = None,
        epochs: int = 10,
        log_interval: int = 100,
        save_interval: int = 1,
        verbose: bool = True
    ):
        """
        训练模型
        
        Args:
            train_dataloader: 训练数据加载器
            val_dataloader: 验证数据加载器（可选）
            epochs: 训练轮数
            log_interval: 日志记录间隔（步数）
            save_interval: 模型保存间隔（轮数）
            verbose: 是否打印详细信息
        
        Returns:
            训练统计信息
        """
        logger.info(f"开始训练人类策略网络，设备: {self.device}, 轮数: {epochs}")
        
        start_time = time.time()
        global_step = 0
        
        for epoch in range(epochs):
            # 训练模式
            self.model.train()
            train_loss = 0.0
            train_correct = 0
            train_total = 0
            
            # 训练循环
            for batch_idx, (states, actions) in enumerate(train_dataloader):
                # 确保数据类型正确
                states = states.float().to(self.device)
                actions = actions.long().to(self.device)
                
                # 梯度清零
                self.optimizer.zero_grad()
                
                # 前向传播
                outputs = self.model(states)
                
                # 计算损失
                loss = self.criterion(outputs, actions)
                
                # 反向传播
                loss.backward()
                
                # 更新参数
                self.optimizer.step()
                
                # 统计
                train_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                train_total += actions.size(0)
                train_correct += (predicted == actions).sum().item()
                
                global_step += 1
                
                # 记录日志
                if verbose and global_step % log_interval == 0:
                    logger.info(f"Epoch {epoch+1}/{epochs} [{batch_idx+1}/{len(train_dataloader)}] "
                               f"Loss: {loss.item():.4f}, "
                               f"Accuracy: {100. * train_correct / train_total:.2f}%")
            
            # 计算训练集指标
            epoch_train_loss = train_loss / len(train_dataloader)
            epoch_train_acc = 100. * train_correct / train_total
            
            self.stats["train_loss"].append(epoch_train_loss)
            self.stats["train_accuracy"].append(epoch_train_acc)
            
            # 验证
            if val_dataloader:
                val_loss, val_acc = self._validate(val_dataloader)
                self.stats["val_loss"].append(val_loss)
                self.stats["val_accuracy"].append(val_acc)
                
                if verbose:
                    logger.info(f"Epoch {epoch+1}/{epochs} "
                               f"Train Loss: {epoch_train_loss:.4f}, "
                               f"Train Acc: {epoch_train_acc:.2f}%, "
                               f"Val Loss: {val_loss:.4f}, "
                               f"Val Acc: {val_acc:.2f}%")
            else:
                if verbose:
                    logger.info(f"Epoch {epoch+1}/{epochs} "
                               f"Train Loss: {epoch_train_loss:.4f}, "
                               f"Train Acc: {epoch_train_acc:.2f}%")
            
            # 保存模型
            if (epoch + 1) % save_interval == 0:
                self.save_checkpoint(f"epoch_{epoch+1}")
        
        # 保存最终模型
        self.save_checkpoint("final")
        
        # 训练完成
        total_time = time.time() - start_time
        logger.info(f"训练完成，总用时: {total_time:.2f}秒")
        
        return self.stats
    
    def _validate(self, dataloader: DataLoader) -> Tuple[float, float]:
        """
        验证模型
        
        Args:
            dataloader: 数据加载器
        
        Returns:
            (loss, accuracy): 平均损失和准确率
        """
        self.model.eval()
        val_loss = 0.0
        correct = 0
        total = 0
        
        with torch.no_grad():
            for states, actions in dataloader:
                # 确保数据类型正确
                states = states.float().to(self.device)
                actions = actions.long().to(self.device)
                
                outputs = self.model(states)
                loss = self.criterion(outputs, actions)
                
                val_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                total += actions.size(0)
                correct += (predicted == actions).sum().item()
        
        return val_loss / len(dataloader), 100. * correct / total
    
    def save_checkpoint(self, tag: str = "latest"):
        """
        保存模型检查点
        
        Args:
            tag: 检查点标签
        """
        checkpoint_path = os.path.join(self.checkpoint_dir, f"human_policy_{tag}.pt")
        
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'stats': self.stats,
            'model_config': {
                'input_dim': self.model.input_dim,
                'hidden_dims': self.model.hidden_dims,
                'action_dim': self.model.action_dim,
                'dropout_rate': self.model.dropout_rate,
                'network_type': self.model.network_type
            }
        }, checkpoint_path)
        
        logger.info(f"模型检查点已保存至: {checkpoint_path}")
    
    def load_checkpoint(self, checkpoint_path: str) -> bool:
        """
        加载模型检查点
        
        Args:
            checkpoint_path: 检查点路径
            
        Returns:
            是否成功加载
        """
        if not os.path.exists(checkpoint_path):
            logger.warning(f"检查点不存在: {checkpoint_path}")
            return False
        
        try:
            checkpoint = torch.load(checkpoint_path, map_location=self.device)
            
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            self.stats = checkpoint['stats']
            
            logger.info(f"检查点加载成功: {checkpoint_path}")
            return True
            
        except Exception as e:
            logger.error(f"加载检查点失败: {e}")
            return False


class HumanPolicyAgent:
    """
    基于人类策略网络的代理
    
    使用训练好的人类策略网络模型来模拟人类玩家的行为
    """
    
    def __init__(
        self,
        model_path: str,
        device: str = None,
        exploration_rate: float = 0.1,
        temperature: float = 1.0,
        verbose: bool = False
    ):
        """
        初始化代理
        
        Args:
            model_path: 模型路径
            device: 计算设备
            exploration_rate: 探索率 (随机动作的概率)
            temperature: 温度参数 (控制分布的平滑程度)
            verbose: 是否打印详细信息
        """
        # 设置设备
        self.device = device if device else ('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 加载模型
        self.model_path = model_path
        self.model = self._load_model(model_path)
        
        # 设置参数
        self.exploration_rate = exploration_rate
        self.temperature = temperature
        self.verbose = verbose
        
    def _load_model(self, model_path: str) -> Optional[HumanPolicyNetwork]:
        """
        加载模型
        
        Args:
            model_path: 模型路径
            
        Returns:
            加载的模型或None（如果加载失败）
        """
        if not os.path.exists(model_path):
            logger.error(f"模型文件不存在: {model_path}")
            return None
        
        try:
            checkpoint = torch.load(model_path, map_location=self.device)
            model_config = checkpoint['model_config']
            
            model = HumanPolicyNetwork(
                input_dim=model_config['input_dim'],
                hidden_dims=model_config['hidden_dims'],
                action_dim=model_config['action_dim'],
                dropout_rate=model_config['dropout_rate'],
                network_type=model_config['network_type']
            ).to(self.device)
            
            model.load_state_dict(checkpoint['model_state_dict'])
            model.eval()  # 设置为评估模式
            
            logger.info(f"成功加载人类策略模型: {model_path}")
            return model
            
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            return None
    
    def act(self, state: Any, legal_actions: List[Any]) -> Any:
        """
        根据状态选择动作
        
        Args:
            state: 游戏状态
            legal_actions: 合法动作列表
            
        Returns:
            选择的动作
        """
        # 如果没有合法动作或者模型加载失败，随机选择
        if not legal_actions or self.model is None:
            return random.choice(legal_actions)
        
        # 随机探索
        if random.random() < self.exploration_rate:
            action = random.choice(legal_actions)
            if self.verbose:
                logger.info("随机探索动作")
            return action
        
        # 将状态转换为模型输入
        with torch.no_grad():
            state_tensor = self._preprocess_state(state)
            logits = self.model(state_tensor)
            
            # 应用温度
            logits = logits / self.temperature
            
            # 获取动作概率分布
            probs = torch.softmax(logits, dim=1).cpu().numpy()[0]
            
            # 选择合法动作中概率最高的
            legal_action_indices = [self._action_to_index(action) for action in legal_actions]
            legal_probs = [probs[i] if i < len(probs) else 0 for i in legal_action_indices]
            
            # 归一化概率
            sum_probs = sum(legal_probs)
            if sum_probs > 0:
                legal_probs = [p / sum_probs for p in legal_probs]
            else:
                legal_probs = [1.0 / len(legal_actions) for _ in legal_actions]
            
            # 根据概率选择动作
            try:
                action_idx = np.random.choice(len(legal_actions), p=legal_probs)
                return legal_actions[action_idx]
            except Exception as e:
                logger.error(f"选择动作时出错: {e}")
                # 出现错误时直接随机选择
                return random.choice(legal_actions)
    
    def _preprocess_state(self, state: Any) -> torch.Tensor:
        """
        预处理状态为模型输入
        
        Args:
            state: 游戏状态
            
        Returns:
            处理后的状态张量
        """
        # 如果state是numpy数组，直接转换
        if isinstance(state, np.ndarray):
            return torch.FloatTensor(state).unsqueeze(0).to(self.device)
        
        # 如果state是DouDizhuState，转换为观察
        if isinstance(state, DouDizhuState):
            observation = state.get_observation()
            return torch.FloatTensor(observation).unsqueeze(0).to(self.device)
        
        # 其他情况，尝试转换为numpy数组
        try:
            state_array = np.array(state, dtype=np.float32)
            return torch.FloatTensor(state_array).unsqueeze(0).to(self.device)
        except:
            logger.error(f"无法处理的状态类型: {type(state)}")
            # 返回零张量作为后备
            return torch.zeros(1, self.model.input_dim).to(self.device)
    
    def _action_to_index(self, action: Any) -> int:
        """
        将动作转换为索引
        
        Args:
            action: 动作
            
        Returns:
            动作索引
        """
        # 如果action是CardGroup，返回其类型的索引
        if isinstance(action, CardGroup):
            if not action.cards:  # 不出牌
                return 0
            return action.type.value + 1  # +1 因为0留给不出牌
        
        # 如果action已经是整数，直接返回
        if isinstance(action, (int, np.integer)):
            return int(action)
        
        # 如果是其他类型，返回其哈希值的模
        return hash(str(action)) % self.model.action_dim


def load_human_data(data_path: str, max_samples: int = None) -> Tuple[List, List]:
    """
    加载人类游戏数据
    
    Args:
        data_path: 数据路径
        max_samples: 最大样本数（可选）
        
    Returns:
        (states, actions): 状态和动作列表
    """
    # 检查路径是否存在
    if not os.path.exists(data_path):
        logger.error(f"数据路径不存在: {data_path}")
        return [], []
    
    states = []
    actions = []
    
    try:
        # 尝试加载npy/npz格式
        if data_path.endswith('.npy') or data_path.endswith('.npz'):
            data = np.load(data_path, allow_pickle=True)
            
            if isinstance(data, np.ndarray):
                # 单个npy文件
                if data.shape[1] >= 2:  # 确保每行至少有state和action两列
                    states = data[:, 0]
                    actions = data[:, 1]
            else:
                # npz文件（字典格式）
                if 'states' in data and 'actions' in data:
                    states = data['states']
                    actions = data['actions']
        
        # 尝试加载json/jsonl格式
        elif data_path.endswith('.json'):
            with open(data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
                if isinstance(data, list) and data:
                    for item in data:
                        if 'state' in item and 'action' in item:
                            states.append(item['state'])
                            actions.append(item['action'])
        
        # 尝试加载jsonl格式
        elif data_path.endswith('.jsonl'):
            with open(data_path, 'r', encoding='utf-8') as f:
                for line in f:
                    item = json.loads(line.strip())
                    if 'state' in item and 'action' in item:
                        states.append(item['state'])
                        actions.append(item['action'])
        
        # 限制样本数量
        if max_samples and len(states) > max_samples:
            # 随机采样
            indices = np.random.choice(len(states), max_samples, replace=False)
            states = [states[i] for i in indices]
            actions = [actions[i] for i in indices]
        
        logger.info(f"成功加载{len(states)}个样本")
        return states, actions
        
    except Exception as e:
        logger.error(f"加载数据失败: {e}")
        return [], []


def generate_synthetic_data(num_samples: int = 1000) -> Tuple[List[np.ndarray], List[int]]:
    """
    生成合成训练数据
    
    当没有真实人类数据时，可以生成合成数据进行模型训练和测试
    
    Args:
        num_samples: 样本数量
        
    Returns:
        (states, actions): 状态和动作列表
    """
    logger.info(f"生成{num_samples}个合成训练样本")
    
    # 假设state是627维的向量，action是0-14之间的整数
    states = []
    actions = []
    
    for _ in range(num_samples):
        # 生成随机状态向量
        state = np.random.rand(627)
        
        # 生成随机动作（0-14之间的整数，对应15种斗地主牌型）
        action = np.random.randint(0, 15)
        
        states.append(state)
        actions.append(action)
    
    logger.info(f"已生成{len(states)}个合成样本")
    return states, actions


def prepare_data_loaders(
    states: List[Any],
    actions: List[Any],
    val_ratio: float = 0.2,
    batch_size: int = 32,
    transform=None
) -> Tuple[DataLoader, DataLoader]:
    """
    准备数据加载器
    
    Args:
        states: 状态列表
        actions: 动作列表
        val_ratio: 验证集比例
        batch_size: 批次大小
        transform: 数据转换函数
        
    Returns:
        (train_loader, val_loader): 训练集和验证集的数据加载器
    """
    # 将状态和动作转换为numpy数组
    if not isinstance(states, np.ndarray):
        states = np.array(states)
    
    if not isinstance(actions, np.ndarray):
        actions = np.array(actions)
    
    # 划分训练集和验证集
    num_samples = len(states)
    indices = np.random.permutation(num_samples)
    val_size = int(num_samples * val_ratio)
    
    train_indices = indices[val_size:]
    val_indices = indices[:val_size]
    
    train_states = states[train_indices]
    train_actions = actions[train_indices]
    
    val_states = states[val_indices]
    val_actions = actions[val_indices]
    
    # 创建数据集
    train_dataset = DouDizhuDataset(train_states, train_actions, transform)
    val_dataset = DouDizhuDataset(val_states, val_actions, transform)
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    
    logger.info(f"数据加载器准备完成: 训练集{len(train_dataset)}个样本, 验证集{len(val_dataset)}个样本")
    return train_loader, val_loader


def train_human_policy(
    data_path: str = None,
    model_save_dir: str = "models/human_policy",
    epochs: int = 10,
    batch_size: int = 32,
    learning_rate: float = 0.001,
    network_type: str = "mlp",
    hidden_dims: List[int] = [256, 128],
    synthetic_data: bool = False,
    num_synthetic_samples: int = 1000,
    device: str = None,
    verbose: bool = True
):
    """
    训练人类策略网络
    
    Args:
        data_path: 训练数据路径 (如不提供则使用合成数据)
        model_save_dir: 模型保存目录
        epochs: 训练轮数
        batch_size: 批次大小
        learning_rate: 学习率
        network_type: 网络类型 (mlp, cnn, transformer)
        hidden_dims: 隐藏层维度
        synthetic_data: 是否使用合成数据
        num_synthetic_samples: 合成样本数量
        device: 计算设备
        verbose: 是否显示详细信息
        
    Returns:
        训练统计信息
    """
    # 加载/生成数据
    if synthetic_data or data_path is None:
        logger.info("使用合成数据进行训练")
        states, actions = generate_synthetic_data(num_synthetic_samples)
    else:
        logger.info(f"从{data_path}加载训练数据")
        states, actions = load_human_data(data_path)
        
        if not states or not actions:
            logger.warning("没有找到有效数据，将使用合成数据")
            states, actions = generate_synthetic_data(num_synthetic_samples)
    
    # 准备数据加载器
    train_loader, val_loader = prepare_data_loaders(
        states, actions, batch_size=batch_size
    )
    
    # 创建模型
    model = HumanPolicyNetwork(
        input_dim=627,  # 斗地主观察空间维度
        hidden_dims=hidden_dims,
        action_dim=15,  # 15种斗地主牌型 + 不出
        network_type=network_type
    )
    
    # 创建训练器
    trainer = HumanPolicyTrainer(
        model=model,
        learning_rate=learning_rate,
        device=device,
        checkpoint_dir=model_save_dir,
        log_dir=os.path.join(model_save_dir, "logs")
    )
    
    # 训练模型
    stats = trainer.train(
        train_dataloader=train_loader,
        val_dataloader=val_loader,
        epochs=epochs,
        verbose=verbose
    )
    
    # 保存最终模型
    trainer.save_checkpoint("final")
    
    return stats


if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
    )
    
    # 使用合成数据训练模型
    stats = train_human_policy(
        synthetic_data=True,
        num_synthetic_samples=2000,
        epochs=5,
        batch_size=32,
        network_type="mlp",
        verbose=True
    )
    
    print(f"训练完成，最终训练集准确率: {stats['train_accuracy'][-1]:.2f}%")
    if stats['val_accuracy']:
        print(f"最终验证集准确率: {stats['val_accuracy'][-1]:.2f}%") 