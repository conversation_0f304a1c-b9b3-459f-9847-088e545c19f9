"""
叫地主/抢地主阶段处理模块

实现叫地主和抢地主阶段的处理机制，确保每个角色模型都能学习到适合其角色的叫地主/抢地主策略。
"""
import numpy as np
import sys
from typing import Dict, List, Tuple, Any, Optional, Union

from cardgame_ai.games.doudizhu.card import Card, CardRank
from cardgame_ai.games.doudizhu.state import GamePhase
from cardgame_ai.games.doudizhu.action import BidAction, GrabAction


class BiddingPhaseHandler:
    """
    叫地主/抢地主阶段处理器

    处理叫地主和抢地主阶段，确保每个角色模型都能学习到适合其角色的策略。
    """

    def __init__(self, bid_reward_weight: float = 0.1, grab_reward_weight: float = 0.2,
                 landlord_bid_threshold: float = 0.6, farmer_bid_threshold: float = 0.4,
                 consider_landlord_cards: bool = True):
        """
        初始化叫地主/抢地主阶段处理器

        Args:
            bid_reward_weight: 叫地主奖励权重
            grab_reward_weight: 抢地主奖励权重
            landlord_bid_threshold: 地主叫地主的手牌强度阈值
            farmer_bid_threshold: 农民叫地主的手牌强度阈值
            consider_landlord_cards: 是否考虑底牌的影响
        """
        self.bid_reward_weight = bid_reward_weight
        self.grab_reward_weight = grab_reward_weight
        self.landlord_bid_threshold = landlord_bid_threshold
        self.farmer_bid_threshold = farmer_bid_threshold
        self.consider_landlord_cards = consider_landlord_cards

        # 角色特定的叫地主/抢地主策略
        self.role_strategies = {
            "landlord": {
                "bid_threshold": landlord_bid_threshold,  # 地主叫地主的手牌强度阈值
                "grab_threshold": landlord_bid_threshold + 0.1,  # 地主抢地主的手牌强度阈值
                "bid_reward_factor": 0.1,  # 地主叫地主的奖励因子
                "grab_reward_factor": 0.2,  # 地主抢地主的奖励因子
                "aggressive": True  # 地主策略是否激进
            },
            "farmer": {
                "bid_threshold": farmer_bid_threshold,  # 农民叫地主的手牌强度阈值
                "grab_threshold": farmer_bid_threshold + 0.1,  # 农民抢地主的手牌强度阈值
                "bid_reward_factor": 0.05,  # 农民叫地主的奖励因子
                "grab_reward_factor": 0.1,  # 农民抢地主的奖励因子
                "aggressive": False  # 农民策略是否激进
            }
        }

        # 统计数据
        self.bid_stats = {
            "total_bids": 0,
            "landlord_bids": 0,
            "farmer_bids": 0,
            "strong_hand_bids": 0,
            "weak_hand_bids": 0
        }

        self.grab_stats = {
            "total_grabs": 0,
            "landlord_grabs": 0,
            "farmer_grabs": 0,
            "strong_hand_grabs": 0,
            "weak_hand_grabs": 0
        }

    def evaluate_hand_strength(self, cards: List[Card]) -> float:
        """
        评估手牌强度

        Args:
            cards: 手牌列表

        Returns:
            手牌强度，0到1之间的值
        """
        if not cards:
            return 0.0

        # 计算大牌数量（A、2、大小王）
        big_cards = sum(1 for card in cards if card.rank >= CardRank.ACE or
                      card.rank in [CardRank.SMALL_JOKER, CardRank.BIG_JOKER])

        # 计算炸弹数量
        bomb_count = self._count_bombs(cards)

        # 检查是否有火箭（大小王）
        has_rocket = self._has_rocket(cards)

        # 计算顺子潜力
        straight_potential = self._calculate_straight_potential(cards)

        # 计算对子数量
        pair_count = self._count_pairs(cards)

        # 计算三张数量
        triple_count = self._count_triples(cards)

        # 计算飞机潜力
        airplane_potential = self._calculate_airplane_potential(cards)

        # 计算控制牌数量（单张、2、大小王）
        control_cards = sum(1 for card in cards if card.rank >= CardRank.TWO or
                           card.rank in [CardRank.SMALL_JOKER, CardRank.BIG_JOKER] or
                           (card.rank >= CardRank.ACE and
                            sum(1 for c in cards if c.rank == card.rank) == 1))

        # 综合评分
        strength = (
            0.25 * big_cards / len(cards) +       # 大牌比例
            0.25 * bomb_count +                  # 炸弹数量
            0.15 * (1.0 if has_rocket else 0.0) + # 火箭
            0.10 * straight_potential +          # 顺子潜力
            0.05 * pair_count / (len(cards)/2) + # 对子比例
            0.10 * triple_count / (len(cards)/3) + # 三张比例
            0.05 * airplane_potential +          # 飞机潜力
            0.05 * control_cards / len(cards)    # 控制牌比例
        )

        return min(1.0, strength)

    def _count_bombs(self, cards: List[Card]) -> int:
        """
        计算炸弹数量

        Args:
            cards: 手牌列表

        Returns:
            炸弹数量
        """
        # 统计每个点数的牌数
        rank_counts = {}
        for card in cards:
            rank = card.rank
            rank_counts[rank] = rank_counts.get(rank, 0) + 1

        # 计算炸弹数量（四张相同点数的牌）
        bomb_count = sum(1 for count in rank_counts.values() if count >= 4)

        return bomb_count

    def _has_rocket(self, cards: List[Card]) -> bool:
        """
        检查是否有火箭（大小王）

        Args:
            cards: 手牌列表

        Returns:
            是否有火箭
        """
        # 检查是否同时有大王和小王
        has_small_joker = any(card.rank == CardRank.SMALL_JOKER for card in cards)
        has_big_joker = any(card.rank == CardRank.BIG_JOKER for card in cards)

        return has_small_joker and has_big_joker

    def _calculate_straight_potential(self, cards: List[Card]) -> float:
        """
        计算顺子潜力

        Args:
            cards: 手牌列表

        Returns:
            顺子潜力，0到1之间的值
        """
        # 统计每个点数的牌数（不包括2和王）
        rank_counts = {}
        for card in cards:
            if card.rank < CardRank.TWO:  # 不包括2和王
                rank = card.rank
                rank_counts[rank] = rank_counts.get(rank, 0) + 1

        # 计算连续的点数
        consecutive_count = 0
        max_consecutive = 0

        for rank in range(CardRank.THREE, CardRank.ACE + 1):
            if rank in rank_counts:
                consecutive_count += 1
                max_consecutive = max(max_consecutive, consecutive_count)
            else:
                consecutive_count = 0

        # 顺子至少需要5张牌
        if max_consecutive >= 5:
            return max_consecutive / 12  # 最长可能的顺子是12张（3到A）
        else:
            return 0.0

    def _count_pairs(self, cards: List[Card]) -> int:
        """
        计算对子数量

        Args:
            cards: 手牌列表

        Returns:
            对子数量
        """
        # 统计每个点数的牌数
        rank_counts = {}
        for card in cards:
            rank = card.rank
            rank_counts[rank] = rank_counts.get(rank, 0) + 1

        # 计算对子数量（两张相同点数的牌）
        pair_count = sum(1 for count in rank_counts.values() if count >= 2)

        return pair_count

    def _count_triples(self, cards: List[Card]) -> int:
        """
        计算三张数量

        Args:
            cards: 手牌列表

        Returns:
            三张数量
        """
        # 统计每个点数的牌数
        rank_counts = {}
        for card in cards:
            rank = card.rank
            rank_counts[rank] = rank_counts.get(rank, 0) + 1

        # 计算三张数量（三张相同点数的牌）
        triple_count = sum(1 for count in rank_counts.values() if count >= 3)

        return triple_count

    def _calculate_airplane_potential(self, cards: List[Card]) -> float:
        """
        计算飞机潜力

        Args:
            cards: 手牌列表

        Returns:
            飞机潜力，0到1之间的值
        """
        # 统计每个点数的牌数（不包括2和王）
        rank_counts = {}
        for card in cards:
            if card.rank < CardRank.TWO:  # 不包括2和王
                rank = card.rank
                rank_counts[rank] = rank_counts.get(rank, 0) + 1

        # 找出所有三张的点数
        triple_ranks = [rank for rank, count in rank_counts.items() if count >= 3]
        triple_ranks.sort()

        # 计算连续的三张
        consecutive_count = 0
        max_consecutive = 0

        for i in range(1, len(triple_ranks)):
            if triple_ranks[i] == triple_ranks[i-1] + 1:
                consecutive_count += 1
                max_consecutive = max(max_consecutive, consecutive_count)
            else:
                consecutive_count = 0

        # 飞机至少需要2个连续的三张
        if max_consecutive >= 1:  # 注意：这里是连续数，实际上是2个三张
            return (max_consecutive + 1) / 10  # 最长可能的飞机是10个三张（3到Q）
        else:
            return 0.0

    def calculate_bid_reward(
        self,
        action: Union[BidAction, int, str],
        hand_cards: List[Card],
        role: str,
        landlord_cards: Optional[List[Card]] = None
    ) -> float:
        """
        计算叫地主奖励

        Args:
            action: 叫地主动作
            hand_cards: 手牌
            role: 角色，'landlord'或'farmer'
            landlord_cards: 底牌，可选

        Returns:
            叫地主奖励
        """
        # 更新统计
        self.bid_stats["total_bids"] += 1

        # 评估手牌强度
        hand_strength = self.evaluate_hand_strength(hand_cards)

        # 如果考虑底牌影响且提供了底牌
        if self.consider_landlord_cards and landlord_cards:
            # 评估底牌强度
            landlord_cards_strength = self.evaluate_hand_strength(landlord_cards)
            # 调整手牌强度，考虑底牌的影响
            hand_strength = 0.8 * hand_strength + 0.2 * landlord_cards_strength

        # 判断是否叫地主
        is_bid = False
        bid_value = 0

        if isinstance(action, BidAction):
            is_bid = action.is_bid
            bid_value = action.value if is_bid else 0
        elif isinstance(action, int):
            is_bid = action > 0
            bid_value = action if is_bid else 0
        elif isinstance(action, str):
            is_bid = action.lower() not in ["pass", "不叫", "不出"]
            # 尝试从字符串中提取叫分
            try:
                bid_value = int(action) if is_bid else 0
            except ValueError:
                bid_value = 1 if is_bid else 0  # 默认为1分

        # 更新统计
        if role == "landlord":
            self.bid_stats["landlord_bids"] += 1
        else:
            self.bid_stats["farmer_bids"] += 1

        if hand_strength > self.role_strategies[role]["bid_threshold"]:
            self.bid_stats["strong_hand_bids"] += 1
        else:
            self.bid_stats["weak_hand_bids"] += 1

        # 获取角色策略
        strategy = self.role_strategies.get(role, self.role_strategies["farmer"])
        bid_threshold = strategy["bid_threshold"]
        bid_reward_factor = strategy["bid_reward_factor"]
        aggressive = strategy["aggressive"]

        # 计算奖励
        reward = 0.0

        # 调试信息
        print(f"Role: {role}, Hand strength: {hand_strength}, Bid threshold: {bid_threshold}")
        print(f"Is bid: {is_bid}, Bid value: {bid_value}, Aggressive: {aggressive}")
        sys.stdout.flush()

        if aggressive:  # 激进策略（地主）
            if hand_strength > bid_threshold:
                # 强牌应该叫地主，叫分越高奖励越高
                reward = bid_value * bid_reward_factor if is_bid else -bid_reward_factor
                print(f"Strong hand landlord bid reward: {reward}")
            else:
                # 弱牌不应该叫地主
                reward = -0.5 * bid_reward_factor * bid_value if is_bid else 0.5 * bid_reward_factor
                print(f"Weak hand landlord bid reward: {reward}")
        else:  # 保守策略（农民）
            if hand_strength < bid_threshold:
                # 弱牌不应该叫地主
                reward = -bid_reward_factor * bid_value if is_bid else bid_reward_factor
                print(f"Weak hand farmer bid reward: {reward}")
            else:
                # 强牌可以叫地主，但奖励较小
                reward = 0.5 * bid_value * bid_reward_factor if is_bid else -0.5 * bid_reward_factor
                print(f"Strong hand farmer bid reward: {reward}")

        print(f"Final reward: {reward * self.bid_reward_weight}")

        return reward * self.bid_reward_weight

    def calculate_grab_reward(
        self,
        action: Union[GrabAction, bool, str],
        hand_cards: List[Card],
        role: str,
        landlord_cards: Optional[List[Card]] = None
    ) -> float:
        """
        计算抢地主奖励

        Args:
            action: 抢地主动作
            hand_cards: 手牌
            role: 角色，'landlord'或'farmer'
            landlord_cards: 底牌，可选

        Returns:
            抢地主奖励
        """
        # 更新统计
        self.grab_stats["total_grabs"] += 1

        # 评估手牌强度
        hand_strength = self.evaluate_hand_strength(hand_cards)

        # 如果考虑底牌影响且提供了底牌
        if self.consider_landlord_cards and landlord_cards:
            # 评估底牌强度
            landlord_cards_strength = self.evaluate_hand_strength(landlord_cards)
            # 调整手牌强度，考虑底牌的影响
            # 抢地主阶段底牌影响更大
            hand_strength = 0.7 * hand_strength + 0.3 * landlord_cards_strength

        # 判断是否抢地主
        is_grab = False

        if isinstance(action, GrabAction):
            is_grab = action.is_grab
        elif isinstance(action, bool):
            is_grab = action
        elif isinstance(action, str):
            is_grab = action.lower() in ["grab", "抢地主", "抢"]

        # 更新统计
        if role == "landlord":
            self.grab_stats["landlord_grabs"] += 1
        else:
            self.grab_stats["farmer_grabs"] += 1

        if hand_strength > self.role_strategies[role]["grab_threshold"]:
            self.grab_stats["strong_hand_grabs"] += 1
        else:
            self.grab_stats["weak_hand_grabs"] += 1

        # 获取角色策略
        strategy = self.role_strategies.get(role, self.role_strategies["farmer"])
        grab_threshold = strategy["grab_threshold"]
        grab_reward_factor = strategy["grab_reward_factor"]
        aggressive = strategy["aggressive"]

        # 计算奖励
        reward = 0.0

        if aggressive:  # 激进策略（地主）
            if hand_strength > grab_threshold:
                # 强牌应该抢地主
                reward = grab_reward_factor if is_grab else -grab_reward_factor
            else:
                # 弱牌不应该抢地主
                reward = -0.5 * grab_reward_factor if is_grab else 0.5 * grab_reward_factor
        else:  # 保守策略（农民）
            if hand_strength < grab_threshold:
                # 弱牌不应该抢地主
                reward = -grab_reward_factor if is_grab else grab_reward_factor
            else:
                # 强牌可以抢地主，但奖励较小
                reward = 0.5 * grab_reward_factor if is_grab else -0.5 * grab_reward_factor

        return reward * self.grab_reward_weight

    def collect_bidding_experience(
        self,
        state: Any,
        action: Any,
        reward: float,
        next_state: Any,
        done: bool,
        role: str
    ) -> Dict[str, Any]:
        """
        收集叫地主阶段的经验

        Args:
            state: 当前状态
            action: 动作
            reward: 奖励
            next_state: 下一个状态
            done: 是否结束
            role: 角色

        Returns:
            经验字典
        """
        # 获取手牌
        hand_cards = self._extract_hand_cards(state)

        # 获取底牌（如果有）
        landlord_cards = self._extract_landlord_cards(state)

        # 评估手牌强度
        hand_strength = self.evaluate_hand_strength(hand_cards)

        # 计算叫地主奖励
        bid_reward = self.calculate_bid_reward(action, hand_cards, role, landlord_cards)

        # 创建经验字典
        experience = {
            "state": state,
            "action": action,
            "reward": reward + bid_reward,  # 添加叫地主奖励
            "next_state": next_state,
            "done": done,
            "hand_strength": hand_strength,
            "phase": "bidding",
            "role": role
        }

        # 如果有底牌，添加底牌信息
        if landlord_cards:
            experience["landlord_cards"] = landlord_cards
            experience["landlord_cards_strength"] = self.evaluate_hand_strength(landlord_cards)

        return experience

    def collect_grabbing_experience(
        self,
        state: Any,
        action: Any,
        reward: float,
        next_state: Any,
        done: bool,
        role: str
    ) -> Dict[str, Any]:
        """
        收集抢地主阶段的经验

        Args:
            state: 当前状态
            action: 动作
            reward: 奖励
            next_state: 下一个状态
            done: 是否结束
            role: 角色

        Returns:
            经验字典
        """
        # 获取手牌
        hand_cards = self._extract_hand_cards(state)

        # 获取底牌（如果有）
        landlord_cards = self._extract_landlord_cards(state)

        # 评估手牌强度
        hand_strength = self.evaluate_hand_strength(hand_cards)

        # 计算抢地主奖励
        grab_reward = self.calculate_grab_reward(action, hand_cards, role, landlord_cards)

        # 创建经验字典
        experience = {
            "state": state,
            "action": action,
            "reward": reward + grab_reward,  # 添加抢地主奖励
            "next_state": next_state,
            "done": done,
            "hand_strength": hand_strength,
            "phase": "grabbing",
            "role": role
        }

        # 如果有底牌，添加底牌信息
        if landlord_cards:
            experience["landlord_cards"] = landlord_cards
            experience["landlord_cards_strength"] = self.evaluate_hand_strength(landlord_cards)

        return experience

    def _extract_landlord_cards(self, state: Any) -> Optional[List[Card]]:
        """
        从状态中提取底牌

        Args:
            state: 游戏状态

        Returns:
            底牌列表，如果没有底牌则返回None
        """
        # 根据具体实现提取底牌
        if hasattr(state, 'landlord_cards') and state.landlord_cards:
            return state.landlord_cards

        # 如果无法提取，返回None
        return None

    def _extract_hand_cards(self, state: Any) -> List[Card]:
        """
        从状态中提取手牌

        Args:
            state: 游戏状态

        Returns:
            手牌列表
        """
        # 根据具体实现提取手牌
        if hasattr(state, 'hands') and hasattr(state, 'current_player'):
            return state.hands[state.current_player]

        # 如果无法提取，返回空列表
        return []

    def get_bid_stats(self) -> Dict[str, Any]:
        """
        获取叫地主统计

        Returns:
            叫地主统计字典
        """
        stats = self.bid_stats.copy()

        # 计算比例
        total = stats["total_bids"]
        if total > 0:
            stats["landlord_bid_rate"] = stats["landlord_bids"] / total
            stats["farmer_bid_rate"] = stats["farmer_bids"] / total
            stats["strong_hand_bid_rate"] = stats["strong_hand_bids"] / total
            stats["weak_hand_bid_rate"] = stats["weak_hand_bids"] / total

        return stats

    def get_grab_stats(self) -> Dict[str, Any]:
        """
        获取抢地主统计

        Returns:
            抢地主统计字典
        """
        stats = self.grab_stats.copy()

        # 计算比例
        total = stats["total_grabs"]
        if total > 0:
            stats["landlord_grab_rate"] = stats["landlord_grabs"] / total
            stats["farmer_grab_rate"] = stats["farmer_grabs"] / total
            stats["strong_hand_grab_rate"] = stats["strong_hand_grabs"] / total
            stats["weak_hand_grab_rate"] = stats["weak_hand_grabs"] / total

        return stats

    def reset(self) -> None:
        """重置统计数据和策略"""
        # 重置统计数据
        self.bid_stats = {
            "total_bids": 0,
            "landlord_bids": 0,
            "farmer_bids": 0,
            "strong_hand_bids": 0,
            "weak_hand_bids": 0
        }

        self.grab_stats = {
            "total_grabs": 0,
            "landlord_grabs": 0,
            "farmer_grabs": 0,
            "strong_hand_grabs": 0,
            "weak_hand_grabs": 0
        }

        # 重置角色策略
        self.role_strategies = {
            "landlord": {
                "bid_threshold": self.landlord_bid_threshold,
                "grab_threshold": self.landlord_bid_threshold + 0.1,
                "bid_reward_factor": 0.1,
                "grab_reward_factor": 0.2,
                "aggressive": True
            },
            "farmer": {
                "bid_threshold": self.farmer_bid_threshold,
                "grab_threshold": self.farmer_bid_threshold + 0.1,
                "bid_reward_factor": 0.05,
                "grab_reward_factor": 0.1,
                "aggressive": False
            }
        }
