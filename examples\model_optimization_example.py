#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
模型优化示例

展示如何使用模型量化、ONNX转换和TensorRT优化功能加速神经网络的推理速度。
"""

import os
import sys
import argparse
import logging
import torch
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Union

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cardgame_ai.algorithms.efficient_zero import EfficientZero, EfficientZeroModel
from cardgame_ai.deployment.optimize_model import (
    quantize_model_static, 
    quantize_model_dynamic, 
    export_to_onnx, 
    create_onnx_inference_session, 
    onnx_inference, 
    convert_onnx_to_tensorrt, 
    benchmark_inference
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def create_dummy_model(device: str = 'cuda') -> EfficientZeroModel:
    """
    创建一个用于测试的EfficientZero模型
    
    Args:
        device: 计算设备
        
    Returns:
        EfficientZero模型
    """
    # 创建模型
    model = EfficientZeroModel(
        observation_shape=(656,),
        action_shape=(1,),
        hidden_dim=256,
        state_dim=64,
        use_resnet=True,
        device=device
    )
    
    # 设置为评估模式
    model.eval()
    
    return model


def create_calibration_dataset(num_samples: int = 100) -> torch.utils.data.DataLoader:
    """
    创建一个用于校准的数据集
    
    Args:
        num_samples: 样本数量
        
    Returns:
        数据加载器
    """
    # 创建随机数据
    data = torch.randn(num_samples, 656)
    
    # 创建数据集
    dataset = torch.utils.data.TensorDataset(data)
    
    # 创建数据加载器
    data_loader = torch.utils.data.DataLoader(
        dataset,
        batch_size=8,
        shuffle=True
    )
    
    return data_loader


def main():
    """
    主函数
    """
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='模型优化示例')
    parser.add_argument('--model_path', type=str, default=None, help='模型路径，如果不指定则创建一个测试模型')
    parser.add_argument('--output_dir', type=str, default='optimized_models', help='优化模型保存目录')
    parser.add_argument('--device', type=str, default='cuda' if torch.cuda.is_available() else 'cpu', help='计算设备')
    parser.add_argument('--quantize', action='store_true', help='是否进行量化')
    parser.add_argument('--export_onnx', action='store_true', help='是否导出ONNX模型')
    parser.add_argument('--convert_tensorrt', action='store_true', help='是否转换为TensorRT模型')
    parser.add_argument('--benchmark', action='store_true', help='是否进行性能基准测试')
    parser.add_argument('--precision', type=str, default='fp16', choices=['fp32', 'fp16', 'int8'], help='TensorRT精度模式')
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 加载或创建模型
    if args.model_path:
        logger.info(f"加载模型: {args.model_path}")
        model = torch.load(args.model_path)
    else:
        logger.info("创建测试模型")
        model = create_dummy_model(args.device)
    
    # 设置为评估模式
    model.eval()
    
    # 创建校准数据集
    calibration_loader = create_calibration_dataset()
    
    # 准备示例输入
    dummy_input = torch.randn(1, 656, device=args.device)
    
    # 模型优化路径
    pytorch_path = os.path.join(args.output_dir, 'model_pytorch.pt')
    static_quantized_path = os.path.join(args.output_dir, 'model_static_quantized.pt')
    dynamic_quantized_path = os.path.join(args.output_dir, 'model_dynamic_quantized.pt')
    onnx_path = os.path.join(args.output_dir, 'model.onnx')
    tensorrt_path = os.path.join(args.output_dir, 'model.trt')
    
    # 保存原始PyTorch模型
    torch.save(model, pytorch_path)
    logger.info(f"原始PyTorch模型已保存到: {pytorch_path}")
    
    # 量化模型
    if args.quantize:
        # 静态量化
        logger.info("开始静态量化...")
        static_quantized_model = quantize_model_static(
            model=model.cpu(),  # 量化需要在CPU上进行
            calibration_data_loader=calibration_loader,
            save_path=static_quantized_path,
            backend='fbgemm'  # 使用x86 CPU后端
        )
        
        # 动态量化
        logger.info("开始动态量化...")
        dynamic_quantized_model = quantize_model_dynamic(
            model=model.cpu(),  # 量化需要在CPU上进行
            save_path=dynamic_quantized_path
        )
    
    # 导出ONNX模型
    if args.export_onnx:
        logger.info("开始导出ONNX模型...")
        export_to_onnx(
            model=model.to(args.device),
            dummy_input=dummy_input,
            save_path=onnx_path,
            input_names=['input'],
            output_names=['policy_logits', 'value'],
            dynamic_axes={
                'input': {0: 'batch_size'},
                'policy_logits': {0: 'batch_size'},
                'value': {0: 'batch_size'}
            }
        )
        
        # 创建ONNX推理会话
        logger.info("创建ONNX推理会话...")
        providers = ['CUDAExecutionProvider'] if args.device == 'cuda' else ['CPUExecutionProvider']
        onnx_session = create_onnx_inference_session(onnx_path, providers=providers)
        
        # 测试ONNX推理
        logger.info("测试ONNX推理...")
        onnx_inputs = {'input': dummy_input.cpu().numpy()}
        onnx_outputs = onnx_inference(onnx_session, onnx_inputs)
        logger.info(f"ONNX推理输出形状: {[output.shape for output in onnx_outputs.values()]}")
    
    # 转换为TensorRT模型
    if args.convert_tensorrt and args.export_onnx:
        try:
            logger.info("开始转换为TensorRT模型...")
            convert_onnx_to_tensorrt(
                onnx_path=onnx_path,
                save_path=tensorrt_path,
                precision=args.precision
            )
        except ImportError:
            logger.warning("TensorRT相关库未安装，无法转换为TensorRT模型")
    
    # 性能基准测试
    if args.benchmark:
        logger.info("开始性能基准测试...")
        
        # 测试原始PyTorch模型
        pytorch_metrics = benchmark_inference(
            model_path=pytorch_path,
            model_type='pytorch',
            input_shape=(1, 656),
            num_iterations=100,
            device=args.device
        )
        
        # 测试静态量化模型
        if args.quantize:
            static_quantized_metrics = benchmark_inference(
                model_path=static_quantized_path,
                model_type='pytorch_quantized',
                input_shape=(1, 656),
                num_iterations=100,
                device='cpu'  # 量化模型在CPU上运行
            )
            
            # 测试动态量化模型
            dynamic_quantized_metrics = benchmark_inference(
                model_path=dynamic_quantized_path,
                model_type='pytorch_quantized',
                input_shape=(1, 656),
                num_iterations=100,
                device='cpu'  # 量化模型在CPU上运行
            )
        
        # 测试ONNX模型
        if args.export_onnx:
            onnx_metrics = benchmark_inference(
                model_path=onnx_path,
                model_type='onnx',
                input_shape=(1, 656),
                num_iterations=100,
                device=args.device
            )
        
        # 测试TensorRT模型
        if args.convert_tensorrt and args.export_onnx:
            try:
                tensorrt_metrics = benchmark_inference(
                    model_path=tensorrt_path,
                    model_type='tensorrt',
                    input_shape=(1, 656),
                    num_iterations=100,
                    device='cuda'  # TensorRT只能在CUDA上运行
                )
            except ImportError:
                logger.warning("TensorRT相关库未安装，无法进行TensorRT推理")
        
        # 打印性能比较
        logger.info("\n性能比较:")
        logger.info(f"原始PyTorch模型: {pytorch_metrics['avg_inference_time'] * 1000:.4f} ms, {pytorch_metrics['fps']:.2f} FPS")
        
        if args.quantize:
            speedup_static = pytorch_metrics['avg_inference_time'] / static_quantized_metrics['avg_inference_time']
            speedup_dynamic = pytorch_metrics['avg_inference_time'] / dynamic_quantized_metrics['avg_inference_time']
            
            logger.info(f"静态量化模型: {static_quantized_metrics['avg_inference_time'] * 1000:.4f} ms, {static_quantized_metrics['fps']:.2f} FPS (加速比: {speedup_static:.2f}x)")
            logger.info(f"动态量化模型: {dynamic_quantized_metrics['avg_inference_time'] * 1000:.4f} ms, {dynamic_quantized_metrics['fps']:.2f} FPS (加速比: {speedup_dynamic:.2f}x)")
        
        if args.export_onnx:
            speedup_onnx = pytorch_metrics['avg_inference_time'] / onnx_metrics['avg_inference_time']
            logger.info(f"ONNX模型: {onnx_metrics['avg_inference_time'] * 1000:.4f} ms, {onnx_metrics['fps']:.2f} FPS (加速比: {speedup_onnx:.2f}x)")
        
        if args.convert_tensorrt and args.export_onnx and 'tensorrt_metrics' in locals():
            speedup_tensorrt = pytorch_metrics['avg_inference_time'] / tensorrt_metrics['avg_inference_time']
            logger.info(f"TensorRT模型 ({args.precision}): {tensorrt_metrics['avg_inference_time'] * 1000:.4f} ms, {tensorrt_metrics['fps']:.2f} FPS (加速比: {speedup_tensorrt:.2f}x)")
    
    logger.info("模型优化示例完成")


if __name__ == "__main__":
    main()
