---
description: 
globs: 
alwaysApply: false
---
## Description
When a user requests to start a new Climb(feature, bug, task, or exploration), you will start a 2-phase process: Step 1 is to define a Product Requirements Document (PRD) via conversation with the user; Step 2 is to build a task list (a.k.a. "Moves") from that PRD.

Once alerted, you will then begin working on the Climb, move-by-move, soliciting user feedback and approval.  Moves can have "rest: true" on it, at which point you WILL ALWAYS stop after completion to check with the user on status.  They can also have the status "skip" on them, in which case you will skip  it and ASK the user at the end if they want to return to it.  Once you reach the end of the Moves list and they've opted out of skipping, you will mark the Climb as complete in both the PRD and the Moves list, and move both files to the .bivvy/complete/ directory.

IMPORTANT RULES:
1. YOU MUST STOP AND GET USER APPROVAL after:
   - Creating the initial PRD draft
   - Making any significant changes to the PRD
   - Completing any move marked with "rest: true"
2. NEVER work on tasks marked as "skip" unless explicitly requested by the user
3. NEVER work ahead of the current move in the task list
4. ALWAYS follow the moves in order, one at a time

## File locations
- Active PRDs are found in .bivvy/[id]-climb.md (PRD) and .bivvy/[id]-moves.json (i.e. the task list).
- Completed PRDs are moved to .bivvy/complete/[id]-climb.md and .bivvy/complete/[id]-moves.json
- The [id] is a 4-character string where each character can be [A-z0-9], e.g. "02b7" or "xK4p" (enforce randomness here, also check that the id doesn't already exist in the .bivvy/complete/ directory)

## Climb
IMPORTANT: When collecting information, we need to know if this is a feature, bug, task, or exploration.  The top of the PRD should always be:
**STARTFILE [id]-climb.md**
<Climb>
  <header>
    <id>[id]</id>
    <type>[feature|bug|task|exploration]</type>
    <description>(description)</description>
  <newDependencies>(Make sure to ask the user if there will be any new dependencies)</newDependencies>
  <prerequisitChanges>(You should think through this carefully)</prerequisitChanges>
  <relevantFiles>(Please do an initial grep based on the information you gather / ask the user for relevant files that might not be obvious)</relevantFiles>
  <everythingElse>(See below, there is a lot that could go here)</everythingElse>
</Climb>
**ENDFILE**

Note: no tasks / moves...just everything needed to carry them out

The PRD will differ with every Climb, but here are some guidelines:
Key Components to Include
Feature Overview

Feature Name and ID: Clear, unique identifier for the feature
Purpose Statement: Concise explanation of what the feature is and why it's valuable
Problem Being Solved: Specific user pain points or business needs addressed
Success Metrics: Measurable outcomes that indicate feature success

Requirements

Functional Requirements: Specific capabilities the feature must provide
Technical Requirements: Performance, security, and reliability expectations
User Requirements: How the feature should work from the user's perspective
Constraints: Technical limitations, business rules, or regulatory considerations

Design and Implementation

User Flow: Step-by-step journey through the feature
Architecture Overview: How this feature integrates with existing systems
Dependent Components: Other systems or features this feature relies on
API Specifications: Required endpoints, payloads, and responses
Data Models: Key data structures and relationships

Development Details

Relevant Files: Specific files or components that will be affected
Implementation Considerations: Technical approach and potential challenges
Dependencies: External services, libraries, or APIs required
Security Considerations: Authentication, authorization, data protection needs

Testing Approach

Test Cases: Critical scenarios to validate
Acceptance Criteria: Conditions that must be met for feature approval
Edge Cases: Unusual or boundary conditions that need special handling
Performance Requirements: Specific benchmarks for speed and reliability

Design Assets

Mockups/Wireframes: Visual representations of the UI (references or links)
User Interface Guidelines: Styling, interaction patterns, and accessibility requirements
Content Guidelines: Copy samples, terminology standards, messaging approach

Future Considerations

Scalability Plans: How the feature should evolve as usage grows
Enhancement Ideas: Potential future improvements outside current scope
Known Limitations: Acknowledged constraints in the current implementation

Formatting Best Practices

Be Specific: Avoid vague language; use precise descriptions
Use Clear Structure: Organize with consistent headers and formatting
Include Examples: Provide concrete examples when explaining complex functionality
Prioritize Requirements: Indicate which requirements are essential vs. nice-to-have
Link to Resources: Reference existing documentation, designs, or research
Keep It Concise: Focus on what's necessary; avoid unnecessary detail
Use Visual Aids: Include diagrams, flowcharts, or mockups when helpful
Define Technical Terms: Include a glossary if specialized terminology is used

What to Avoid

Prescribing Implementation Details: Focus on what, not how (unless necessary)
Including Task Lists: Leave specific tasks for project management tools
Rigid Timelines: PRDs describe requirements, not project schedules
Vague Goals: Ensure all success metrics are measurable
Overspecification: Allow room for engineering creativity in solutions
Ignoring Constraints: Acknowledge technical and business limitations
Excessive Jargon: Write for clarity across different team roles

By following this framework, you'll create feature PRDs that provide clear direction while maintaining flexibility for implementation approaches, ultimately leading to better features and more efficient development.

## Moves
Once the Climb is generated and approved by the user, generate the Moves list.
You should carefully consider the ORDER in which these tasks need to be completed.
The size of every move should be something an AI agent can carry out in 2-3 code changes.
Make sure to add reasonable {rest: true} along the way.
Moves have the statuses: todo|climbing|skip|complete

Here is a sample moves file:
**STARTFILE [id]-moves.json**
{
  "Climb": "abcd",
  "moves": [
    {
      "status": "complete",
      "description": "install the dependencies",
      "details": "install the deps listed as New Dependencies"
    }, {
      "status": "skip",
      "description": "Write tests"
    }, {
      "status": "climbing",
      "description": "Build the first part of the feature",
      "rest": "true"
    }, {
      "status": "todo",
      "description": "Build the last part of the feature",
      "details": "After this, you'd ask the user if they want to return to write tests"
    }
  ]
}
**ENDFILE**

## Running
Creating the PRD:
- THIS NEEDS TO BE ITERATIVE!
- If you need to, ask the user for clarifying questions before starting
- CRITICAL: YOU MUST STOP after your first draft of the PRD and wait for user approval
- CRITICAL: YOU MUST STOP after any significant changes to the PRD
- The PRD must be approved before moving on to creating the moves list

Creating and Managing Moves:
- After PRD approval, create the moves list
- CRITICAL: YOU MUST STOP after creating the initial moves list for user approval
- Moves marked as "skip" MUST NOT be worked on unless explicitly requested by the user
- NEVER work ahead or complete tasks out of order
- Each move should be completed and approved before moving to the next one
- If a move depends on a skipped move, YOU MUST ASK the user if they want to return to the skipped move first

Updating [id]-moves.json:
- After EVERY code approval, you should update the moves.json file
- Move through the moves array until you hit a todo item, then move on to it
- NEVER work on moves marked as "skip"
- NEVER work ahead of the current move
- It is okay to check with the user if they want to move forward, but trust the process
- Make sure to update the statuses within the moves.json file

Keeping track of the Climb
- IMPORTANT: EVERY TIME YOU USE THIS RULE, THE LAST LINE OF YOUR OUPUT SHOULD BE: "/|\ Bivvy Climb [id]"
- CRITICAL: Unless they are closing the Climb (see below) then do NOT keep track of the Climb.

## Closing (or canceling) a Climb
- If the user asks to close a Climb, ask them if they want to "delete" it or "complete" it
- They can also do either without asking to "close" first
- If they delete a Climb, delete both the Climb and moves file by id
- If they close a Climb, move it to complete
- CRITICAL: STOP ADDING THE Climb-TRACKING TEXT TO RESPONSES
- CRITICAL: STOP USING THIS RULE UNTIL A NEW Climb IS STARTED