#!/usr/bin/env python3
"""
MCTS动作映射修复验证测试

这个测试脚本用于验证MCTS无限循环问题的修复效果，
确保动作映射机制正常工作，避免单子节点链问题。
"""

import sys
import os
import logging
import time
from typing import List, Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cardgame_ai.games.doudizhu.action import BidAction, GrabAction
from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType
from cardgame_ai.games.doudizhu.card import Card, CardRank, CardSuit
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.algorithms.efficient_zero_algorithm import EfficientZero

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ActionMappingTester:
    """动作映射测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.env = DouDizhuEnvironment()
        self.efficient_zero = EfficientZero()
        
    def test_cardgroup_to_index(self):
        """测试CardGroup的to_index方法"""
        logger.info("🧪 测试CardGroup.to_index方法...")
        
        test_cases = [
            # PASS动作
            CardGroup([]),
            
            # 单张牌
            CardGroup([Card(CardRank.THREE, CardSuit.HEARTS)]),
            CardGroup([Card(CardRank.ACE, CardSuit.SPADES)]),
            
            # 对子
            CardGroup([
                Card(CardRank.FIVE, CardSuit.HEARTS),
                Card(CardRank.FIVE, CardSuit.SPADES)
            ]),
            
            # 三张
            CardGroup([
                Card(CardRank.SEVEN, CardSuit.HEARTS),
                Card(CardRank.SEVEN, CardSuit.SPADES),
                Card(CardRank.SEVEN, CardSuit.CLUBS)
            ]),
            
            # 炸弹
            CardGroup([
                Card(CardRank.KING, CardSuit.HEARTS),
                Card(CardRank.KING, CardSuit.SPADES),
                Card(CardRank.KING, CardSuit.CLUBS),
                Card(CardRank.KING, CardSuit.DIAMONDS)
            ]),
        ]
        
        action_ids = set()
        for i, card_group in enumerate(test_cases):
            action_id = card_group.to_index()
            logger.info(f"  测试用例{i+1}: {card_group.card_type.name} -> ID={action_id}")
            
            # 检查ID唯一性
            if action_id in action_ids:
                logger.error(f"❌ 发现重复的动作ID: {action_id}")
                return False
            action_ids.add(action_id)
        
        logger.info("✅ CardGroup.to_index测试通过")
        return True
    
    def test_efficient_zero_action_mapping(self):
        """测试EfficientZero的动作映射"""
        logger.info("🧪 测试EfficientZero动作映射...")
        
        test_actions = [
            # BidAction测试
            BidAction.PASS,
            BidAction.BID_1,
            BidAction.BID_2,
            BidAction.BID_3,
            
            # GrabAction测试
            GrabAction.PASS,
            GrabAction.GRAB,
            
            # CardGroup测试
            CardGroup([]),  # PASS
            CardGroup([Card(CardRank.THREE, CardSuit.HEARTS)]),  # 单张
            CardGroup([Card(CardRank.FIVE, CardSuit.HEARTS), Card(CardRank.FIVE, CardSuit.SPADES)]),  # 对子
        ]
        
        action_ids = set()
        for action in test_actions:
            action_id = self.efficient_zero._get_action_id(action)
            logger.info(f"  动作: {action} -> ID={action_id}")
            
            # 检查ID唯一性
            if action_id in action_ids:
                logger.error(f"❌ 发现重复的动作ID: {action_id}")
                return False
            action_ids.add(action_id)
        
        logger.info("✅ EfficientZero动作映射测试通过")
        return True
    
    def test_action_mask_generation(self):
        """测试动作掩码生成"""
        logger.info("🧪 测试动作掩码生成...")
        
        # 重置环境获取初始状态
        state = self.env.reset()
        
        # 获取合法动作
        legal_actions = state.get_legal_actions()
        logger.info(f"  合法动作数量: {len(legal_actions)}")
        
        if len(legal_actions) <= 1:
            logger.error(f"❌ 合法动作数量过少: {len(legal_actions)}")
            return False
        
        # 创建动作掩码
        try:
            max_action_id = max([self.efficient_zero._get_action_id(a) for a in legal_actions] + [0])
            action_mask = [0] * (max_action_id + 1)
            
            valid_action_count = 0
            for action in legal_actions:
                action_id = self.efficient_zero._get_action_id(action)
                action_mask[action_id] = 1
                valid_action_count += 1
            
            logger.info(f"  有效动作数量: {valid_action_count}")
            logger.info(f"  动作掩码长度: {len(action_mask)}")
            logger.info(f"  掩码中1的数量: {sum(action_mask)}")
            
            if sum(action_mask) != valid_action_count:
                logger.error(f"❌ 动作掩码不一致: 期望{valid_action_count}, 实际{sum(action_mask)}")
                return False
            
            if sum(action_mask) <= 1:
                logger.error(f"❌ 动作掩码中有效动作过少: {sum(action_mask)}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 动作掩码生成失败: {e}")
            return False
        
        logger.info("✅ 动作掩码生成测试通过")
        return True
    
    def test_mcts_simulation_basic(self):
        """测试基础MCTS模拟（不会无限循环）"""
        logger.info("🧪 测试基础MCTS模拟...")
        
        try:
            # 重置环境
            state = self.env.reset()
            
            # 设置较短的超时时间
            start_time = time.time()
            timeout_seconds = 10
            
            # 尝试执行动作选择
            action, action_probs, _ = self.efficient_zero.act(
                state, 
                explain=True
            )
            
            elapsed_time = time.time() - start_time
            logger.info(f"  MCTS执行时间: {elapsed_time:.2f}秒")
            
            if elapsed_time > timeout_seconds:
                logger.error(f"❌ MCTS执行超时: {elapsed_time:.2f}秒 > {timeout_seconds}秒")
                return False
            
            if action is None:
                logger.error("❌ MCTS返回空动作")
                return False
            
            logger.info(f"  选择的动作: {action}")
            logger.info(f"  动作概率数量: {len(action_probs) if action_probs else 0}")
            
        except Exception as e:
            logger.error(f"❌ MCTS模拟失败: {e}")
            return False
        
        logger.info("✅ 基础MCTS模拟测试通过")
        return True
    
    def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始MCTS动作映射修复验证测试...")
        
        tests = [
            ("CardGroup.to_index方法", self.test_cardgroup_to_index),
            ("EfficientZero动作映射", self.test_efficient_zero_action_mapping),
            ("动作掩码生成", self.test_action_mask_generation),
            ("基础MCTS模拟", self.test_mcts_simulation_basic),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"\n{'='*50}")
            logger.info(f"执行测试: {test_name}")
            logger.info(f"{'='*50}")
            
            try:
                if test_func():
                    passed += 1
                    logger.info(f"✅ {test_name} - 通过")
                else:
                    logger.error(f"❌ {test_name} - 失败")
            except Exception as e:
                logger.error(f"❌ {test_name} - 异常: {e}")
        
        logger.info(f"\n{'='*50}")
        logger.info(f"测试结果: {passed}/{total} 通过")
        logger.info(f"{'='*50}")
        
        if passed == total:
            logger.info("🎉 所有测试通过！MCTS动作映射修复成功！")
            return True
        else:
            logger.error("💥 部分测试失败，需要进一步修复")
            return False


if __name__ == "__main__":
    tester = ActionMappingTester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
