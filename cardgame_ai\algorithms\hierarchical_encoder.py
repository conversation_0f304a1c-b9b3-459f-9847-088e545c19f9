"""
层次化编码器模块

实现高级层次化编码器，使AI能够从低级特征（单牌）到高级特征（牌型、策略）逐步构建对游戏状态的理解。
包括牌型特定的编码器和全局上下文编码，显著提高AI对斗地主牌型和策略的理解能力。
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Union, Set

from cardgame_ai.games.doudizhu.card import Card, CardRank
from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType
from cardgame_ai.algorithms.advanced_representation import HierarchicalEncoder


class PatternSpecificEncoder(nn.Module):
    """
    牌型特定编码器

    为不同类型的牌组合（如顺子、炸弹等）设计专门的编码器，
    提高对特定牌型的理解能力。
    """

    def __init__(
        self,
        input_dim: int,
        hidden_dim: int = 128,
        output_dim: int = 64,
        num_card_types: int = 15,  # 3-17（A-2和大小王）
        num_card_patterns: int = 8,  # 单牌、对子、三张、顺子、连对、飞机、炸弹、火箭
        use_pattern_embedding: bool = True
    ):
        """
        初始化牌型特定编码器

        Args:
            input_dim: 输入维度
            hidden_dim: 隐藏层维度
            output_dim: 输出维度
            num_card_types: 牌的类型数量
            num_card_patterns: 牌型的类型数量
            use_pattern_embedding: 是否使用牌型嵌入
        """
        super(PatternSpecificEncoder, self).__init__()

        self.input_dim = input_dim
        self.output_dim = output_dim
        self.use_pattern_embedding = use_pattern_embedding

        # 共享特征提取层
        self.shared_encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim)
        )

        # 牌型嵌入层
        if use_pattern_embedding:
            self.pattern_embedding = nn.Embedding(num_card_patterns, hidden_dim)

        # 单牌编码器
        self.single_encoder = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, output_dim)
        )

        # 对子编码器
        self.pair_encoder = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, output_dim)
        )

        # 三张编码器
        self.trio_encoder = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, output_dim)
        )

        # 顺子编码器
        self.straight_encoder = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, output_dim)
        )

        # 连对编码器
        self.straight_pair_encoder = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, output_dim)
        )

        # 飞机编码器
        self.airplane_encoder = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, output_dim)
        )

        # 炸弹编码器
        self.bomb_encoder = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, output_dim)
        )

        # 火箭编码器
        self.rocket_encoder = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, output_dim)
        )

        # 特殊牌型编码器（三带一、三带二、四带二等）
        self.special_encoder = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, output_dim)
        )

        # 牌型分类器
        self.pattern_classifier = nn.Sequential(
            nn.Linear(hidden_dim, num_card_patterns),
            nn.Softmax(dim=1)
        )

    def forward(self, x: torch.Tensor, pattern_ids: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        前向传播

        Args:
            x: 输入特征
            pattern_ids: 牌型的ID，如果提供，将使用嵌入层

        Returns:
            包含各牌型编码结果的字典
        """
        # 共享特征提取
        shared_features = self.shared_encoder(x)

        # 如果提供了牌型的ID，使用嵌入层
        if self.use_pattern_embedding and pattern_ids is not None:
            pattern_embeddings = self.pattern_embedding(pattern_ids)
            # 结合特征和嵌入
            enhanced_features = shared_features + pattern_embeddings
        else:
            enhanced_features = shared_features

        # 牌型分类
        pattern_probs = self.pattern_classifier(enhanced_features)

        # 各牌型特定编码
        single_features = self.single_encoder(enhanced_features)
        pair_features = self.pair_encoder(enhanced_features)
        trio_features = self.trio_encoder(enhanced_features)
        straight_features = self.straight_encoder(enhanced_features)
        straight_pair_features = self.straight_pair_encoder(enhanced_features)
        airplane_features = self.airplane_encoder(enhanced_features)
        bomb_features = self.bomb_encoder(enhanced_features)
        rocket_features = self.rocket_encoder(enhanced_features)
        special_features = self.special_encoder(enhanced_features)

        # 如果提供了牌型的ID，使用对应的编码器
        if pattern_ids is not None:
            # 创建空的输出张量
            output_features = torch.zeros_like(single_features)

            # 根据牌型选择相应的编码器输出
            for i, pattern_id in enumerate(pattern_ids):
                if pattern_id == 0:  # 单牌
                    output_features[i] = single_features[i]
                elif pattern_id == 1:  # 对子
                    output_features[i] = pair_features[i]
                elif pattern_id == 2:  # 三张
                    output_features[i] = trio_features[i]
                elif pattern_id == 3:  # 顺子
                    output_features[i] = straight_features[i]
                elif pattern_id == 4:  # 连对
                    output_features[i] = straight_pair_features[i]
                elif pattern_id == 5:  # 飞机
                    output_features[i] = airplane_features[i]
                elif pattern_id == 6:  # 炸弹
                    output_features[i] = bomb_features[i]
                elif pattern_id == 7:  # 火箭
                    output_features[i] = rocket_features[i]
                else:  # 特殊牌型
                    output_features[i] = special_features[i]
        else:
            # 如果没有提供牌型的ID，使用牌型概率加权平均
            output_features = (
                pattern_probs[:, 0:1] * single_features +
                pattern_probs[:, 1:2] * pair_features +
                pattern_probs[:, 2:3] * trio_features +
                pattern_probs[:, 3:4] * straight_features +
                pattern_probs[:, 4:5] * straight_pair_features +
                pattern_probs[:, 5:6] * airplane_features +
                pattern_probs[:, 6:7] * bomb_features +
                pattern_probs[:, 7:8] * rocket_features
            )

        # 返回各牌型编码结果
        return {
            'shared_features': shared_features,
            'enhanced_features': enhanced_features,
            'pattern_probs': pattern_probs,
            'single_features': single_features,
            'pair_features': pair_features,
            'trio_features': trio_features,
            'straight_features': straight_features,
            'straight_pair_features': straight_pair_features,
            'airplane_features': airplane_features,
            'bomb_features': bomb_features,
            'rocket_features': rocket_features,
            'special_features': special_features,
            'output_features': output_features
        }

    def get_config(self) -> Dict[str, Any]:
        """
        获取配置信息

        Returns:
            配置字典
        """
        return {
            'input_dim': self.input_dim,
            'hidden_dim': self.shared_encoder[0].out_features,
            'output_dim': self.output_dim,
            'num_card_patterns': self.pattern_classifier[0].out_features if self.use_pattern_embedding else None,
            'use_pattern_embedding': self.use_pattern_embedding
        }


class GlobalContextEncoder(nn.Module):
    """
    全局上下文编码器

    编码游戏的全局上下文信息，包括当前局势、历史出牌和对手信息等，
    提高AI对整体局势的理解能力。
    """

    def __init__(
        self,
        hand_cards_dim: int,
        history_dim: int,
        public_info_dim: int,
        output_dim: int = 256,
        hidden_dim: int = 128,
        num_heads: int = 4,
        use_self_attention: bool = True
    ):
        """
        初始化全局上下文编码器

        Args:
            hand_cards_dim: 手牌特征维度
            history_dim: 历史出牌特征维度
            public_info_dim: 公开信息特征维度
            output_dim: 输出维度
            hidden_dim: 隐藏层维度
            num_heads: 注意力头数
            use_self_attention: 是否使用自注意力机制
        """
        super(GlobalContextEncoder, self).__init__()

        self.output_dim = output_dim
        self.use_self_attention = use_self_attention

        # 手牌编码器
        self.hand_cards_encoder = nn.Sequential(
            nn.Linear(hand_cards_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim)
        )

        # 历史出牌编码器
        self.history_encoder = nn.Sequential(
            nn.Linear(history_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim)
        )

        # 公开信息编码器
        self.public_info_encoder = nn.Sequential(
            nn.Linear(public_info_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim)
        )

        # 全局上下文融合层
        self.context_fusion = nn.Sequential(
            nn.Linear(hidden_dim * 3, hidden_dim * 2),
            nn.ReLU(),
            nn.Linear(hidden_dim * 2, output_dim)
        )

        # 自注意力机制（可选）
        if use_self_attention:
            self.self_attention = nn.MultiheadAttention(
                embed_dim=hidden_dim,
                num_heads=num_heads,
                dropout=0.1
            )

            # 层正规化
            self.layer_norm = nn.LayerNorm(hidden_dim)

            # 注意力后的融合层
            self.attention_fusion = nn.Sequential(
                nn.Linear(hidden_dim * 3, hidden_dim * 2),
                nn.ReLU(),
                nn.Linear(hidden_dim * 2, output_dim)
            )

    def forward(
        self,
        hand_cards: torch.Tensor,
        history: torch.Tensor,
        public_info: torch.Tensor
    ) -> Dict[str, torch.Tensor]:
        """
        前向传播

        Args:
            hand_cards: 手牌特征
            history: 历史出牌特征
            public_info: 公开信息特征

        Returns:
            包含各类编码结果的字典
        """
        # 编码各类信息
        hand_cards_features = self.hand_cards_encoder(hand_cards)
        history_features = self.history_encoder(history)
        public_info_features = self.public_info_encoder(public_info)

        # 如果使用自注意力机制
        if self.use_self_attention:
            # 将特征调整为注意力机制所需的形状
            # 从 [batch_size, hidden_dim] 变为 [seq_len, batch_size, hidden_dim]
            # 这里将三种特征视为序列的三个元素
            features_seq = torch.stack([hand_cards_features, history_features, public_info_features], dim=0)

            # 应用自注意力
            attn_output, _ = self.self_attention(
                features_seq, features_seq, features_seq
            )

            # 应用层正规化和残差连接
            attn_output = self.layer_norm(features_seq + attn_output)

            # 调整回原始形状
            hand_cards_attn = attn_output[0]
            history_attn = attn_output[1]
            public_info_attn = attn_output[2]

            # 连接注意力后的特征
            combined_features = torch.cat([hand_cards_attn, history_attn, public_info_attn], dim=1)
            context_features = self.attention_fusion(combined_features)
        else:
            # 直接连接特征
            combined_features = torch.cat([hand_cards_features, history_features, public_info_features], dim=1)
            context_features = self.context_fusion(combined_features)

        # 返回各类编码结果
        return {
            'hand_cards_features': hand_cards_features,
            'history_features': history_features,
            'public_info_features': public_info_features,
            'combined_features': combined_features,
            'context_features': context_features
        }

    def get_config(self) -> Dict[str, Any]:
        """
        获取配置信息

        Returns:
            配置字典
        """
        return {
            'hand_cards_dim': self.hand_cards_encoder[0].in_features,
            'history_dim': self.history_encoder[0].in_features,
            'public_info_dim': self.public_info_encoder[0].in_features,
            'output_dim': self.output_dim,
            'hidden_dim': self.hand_cards_encoder[0].out_features,
            'num_heads': self.self_attention.num_heads if self.use_self_attention else None,
            'use_self_attention': self.use_self_attention
        }


class EnhancedHierarchicalEncoder(nn.Module):
    """
    增强版层次化编码器

    整合牌型特定编码器和全局上下文编码器，使模型能够从低级特征（单牌）
    到高级特征（牌型、策略）逐步构建对游戏状态的理解，显著提高AI对斗地主牌型和策略的理解能力。
    """

    def __init__(
        self,
        hand_cards_dim: int = 54,
        history_dim: int = 100,
        public_info_dim: int = 20,
        pattern_hidden_dim: int = 128,
        context_hidden_dim: int = 128,
        output_dim: int = 256,
        num_card_types: int = 15,  # 3-17（A-2和大小王）
        num_card_patterns: int = 8,  # 单牌、对子、三张、顺子、连对、飞机、炸弹、火箭
        num_attention_heads: int = 4,
        use_pattern_embedding: bool = True,
        use_self_attention: bool = True,
        use_cross_attention: bool = True,
        use_residual: bool = True
    ):
        """
        初始化增强版层次化编码器

        Args:
            hand_cards_dim: 手牌特征维度
            history_dim: 历史出牌特征维度
            public_info_dim: 公开信息特征维度
            pattern_hidden_dim: 牌型编码器隐藏层维度
            context_hidden_dim: 上下文编码器隐藏层维度
            output_dim: 输出维度
            num_card_types: 牌的类型数量
            num_card_patterns: 牌型的类型数量
            num_attention_heads: 注意力头数
            use_pattern_embedding: 是否使用牌型嵌入
            use_self_attention: 是否使用自注意力机制
            use_cross_attention: 是否使用跨模态注意力
            use_residual: 是否使用残差连接
        """
        super(EnhancedHierarchicalEncoder, self).__init__()

        self.output_dim = output_dim
        self.use_cross_attention = use_cross_attention
        self.use_residual = use_residual

        # 牌型特定编码器
        self.pattern_encoder = PatternSpecificEncoder(
            input_dim=hand_cards_dim,
            hidden_dim=pattern_hidden_dim,
            output_dim=output_dim // 2,  # 将输出维度减半，以便与上下文编码器的输出进行融合
            num_card_types=num_card_types,
            num_card_patterns=num_card_patterns,
            use_pattern_embedding=use_pattern_embedding
        )

        # 全局上下文编码器
        self.context_encoder = GlobalContextEncoder(
            hand_cards_dim=hand_cards_dim,
            history_dim=history_dim,
            public_info_dim=public_info_dim,
            output_dim=output_dim // 2,  # 将输出维度减半，以便与牌型编码器的输出进行融合
            hidden_dim=context_hidden_dim,
            num_heads=num_attention_heads,
            use_self_attention=use_self_attention
        )

        # 跨模态注意力机制（可选）
        if use_cross_attention:
            self.cross_attention = nn.MultiheadAttention(
                embed_dim=output_dim // 2,
                num_heads=num_attention_heads,
                dropout=0.1
            )

            # 层正规化
            self.layer_norm = nn.LayerNorm(output_dim // 2)

        # 最终融合层
        self.fusion_layer = nn.Sequential(
            nn.Linear(output_dim, output_dim),
            nn.ReLU(),
            nn.Linear(output_dim, output_dim)
        )

    def forward(
        self,
        hand_cards: torch.Tensor,
        history: torch.Tensor,
        public_info: torch.Tensor,
        pattern_ids: Optional[torch.Tensor] = None
    ) -> Dict[str, torch.Tensor]:
        """
        前向传播

        Args:
            hand_cards: 手牌特征
            history: 历史出牌特征
            public_info: 公开信息特征
            pattern_ids: 牌型的ID，如果提供，将使用嵌入层

        Returns:
            包含各层编码结果的字典
        """
        # 牌型特定编码
        pattern_features = self.pattern_encoder(hand_cards, pattern_ids)
        pattern_output = pattern_features['output_features']

        # 全局上下文编码
        context_features = self.context_encoder(hand_cards, history, public_info)
        context_output = context_features['context_features']

        # 如果使用跨模态注意力
        if self.use_cross_attention:
            # 将特征调整为注意力机制所需的形状
            # 从 [batch_size, output_dim//2] 变为 [seq_len, batch_size, output_dim//2]
            # 这里将牌型特征和上下文特征视为序列的两个元素
            features_seq = torch.stack([pattern_output, context_output], dim=0)

            # 应用跨模态注意力
            attn_output, _ = self.cross_attention(
                features_seq, features_seq, features_seq
            )

            # 应用层正规化和残差连接
            attn_output = self.layer_norm(features_seq + attn_output)

            # 调整回原始形状
            pattern_output_attn = attn_output[0]
            context_output_attn = attn_output[1]

            # 连接注意力后的特征
            combined_features = torch.cat([pattern_output_attn, context_output_attn], dim=1)
        else:
            # 直接连接特征
            combined_features = torch.cat([pattern_output, context_output], dim=1)

        # 最终融合
        output_features = self.fusion_layer(combined_features)

        # 残差连接（如果启用）
        if self.use_residual:
            output_features = output_features + combined_features

        # 返回各层编码结果
        return {
            'pattern_features': pattern_features,
            'context_features': context_features,
            'combined_features': combined_features,
            'output_features': output_features
        }

    def get_config(self) -> Dict[str, Any]:
        """
        获取配置信息

        Returns:
            配置字典
        """
        pattern_config = self.pattern_encoder.get_config()
        context_config = self.context_encoder.get_config()

        return {
            'hand_cards_dim': pattern_config['input_dim'],
            'history_dim': context_config['history_dim'],
            'public_info_dim': context_config['public_info_dim'],
            'pattern_hidden_dim': pattern_config['hidden_dim'],
            'context_hidden_dim': context_config['hidden_dim'],
            'output_dim': self.output_dim,
            'num_card_patterns': pattern_config['num_card_patterns'],
            'num_attention_heads': self.cross_attention.num_heads if self.use_cross_attention else None,
            'use_pattern_embedding': pattern_config['use_pattern_embedding'],
            'use_self_attention': context_config['use_self_attention'],
            'use_cross_attention': self.use_cross_attention,
            'use_residual': self.use_residual
        }


def test_hierarchical_encoder():
    """
    测试层次化编码器

    这个函数用于测试牌型特定编码器、全局上下文编码器和增强版层次化编码器的性能。
    """
    import time
    import torch
    import numpy as np
    from cardgame_ai.games.doudizhu.state import DouDizhuState
    from cardgame_ai.games.doudizhu.card import Card
    from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType

    # 设置随机种子以确保可重现性
    torch.manual_seed(42)
    np.random.seed(42)

    # 测试牌型特定编码器
    print("\n=== 测试牌型特定编码器 ===")

    # 创建牌型特定编码器
    pattern_encoder = PatternSpecificEncoder(
        input_dim=54,
        hidden_dim=128,
        output_dim=64,
        num_card_patterns=8,
        use_pattern_embedding=True
    )

    # 生成测试数据
    batch_size = 16
    hand_cards = torch.randn(batch_size, 54)  # 模拟手牌特征
    pattern_ids = torch.randint(0, 8, (batch_size,))  # 模拟牌型的ID

    # 前向传播
    start_time = time.time()
    pattern_features = pattern_encoder(hand_cards, pattern_ids)
    forward_time = time.time() - start_time

    print(f"\u8f93入形状: {hand_cards.shape}")
    print(f"\u724c型特定编码器输出形状: {pattern_features['output_features'].shape}")
    print(f"\u524d向传播时间: {forward_time:.4f} 秒")

    # 测试全局上下文编码器
    print("\n=== 测试全局上下文编码器 ===")

    # 创建全局上下文编码器
    context_encoder = GlobalContextEncoder(
        hand_cards_dim=54,
        history_dim=100,
        public_info_dim=20,
        output_dim=64,
        hidden_dim=128,
        num_heads=4,
        use_self_attention=True
    )

    # 生成测试数据
    history = torch.randn(batch_size, 100)  # 模拟历史出牌特征
    public_info = torch.randn(batch_size, 20)  # 模拟公开信息特征

    # 前向传播
    start_time = time.time()
    context_features = context_encoder(hand_cards, history, public_info)
    forward_time = time.time() - start_time

    print(f"\u8f93入形状: {hand_cards.shape}, {history.shape}, {public_info.shape}")
    print(f"\u5168局上下文编码器输出形状: {context_features['context_features'].shape}")
    print(f"\u524d向传播时间: {forward_time:.4f} 秒")

    # 测试增强版层次化编码器
    print("\n=== 测试增强版层次化编码器 ===")

    # 创建增强版层次化编码器
    enhanced_encoder = EnhancedHierarchicalEncoder(
        hand_cards_dim=54,
        history_dim=100,
        public_info_dim=20,
        pattern_hidden_dim=128,
        context_hidden_dim=128,
        output_dim=256,
        num_card_patterns=8,
        num_attention_heads=4,
        use_pattern_embedding=True,
        use_self_attention=True,
        use_cross_attention=True,
        use_residual=True
    )

    # 前向传播
    start_time = time.time()
    enhanced_features = enhanced_encoder(hand_cards, history, public_info, pattern_ids)
    forward_time = time.time() - start_time

    print(f"\u8f93入形状: {hand_cards.shape}, {history.shape}, {public_info.shape}")
    print(f"\u589e强版层次化编码器输出形状: {enhanced_features['output_features'].shape}")
    print(f"\u524d向传播时间: {forward_time:.4f} 秒")

    # 测试与原始层次化编码器的比较
    print("\n=== 与原始层次化编码器的比较 ===")

    # 创建原始层次化编码器
    original_encoder = HierarchicalEncoder(
        input_dim=54,
        card_embedding_dim=32,
        pattern_embedding_dim=64,
        strategy_embedding_dim=128,
        output_dim=256,
        num_card_types=15,
        num_card_patterns=8,
        use_residual=True
    )

    # 生成测试数据
    card_ids = torch.randint(0, 15, (batch_size,))  # 模拟牌的ID

    # 前向传播
    start_time = time.time()
    original_features = original_encoder(hand_cards, card_ids, pattern_ids)
    forward_time = time.time() - start_time

    print(f"\u8f93入形状: {hand_cards.shape}")
    print(f"\u539f始层次化编码器输出形状: {original_features['output_features'].shape}")
    print(f"\u524d向传播时间: {forward_time:.4f} 秒")

    # 比较性能
    print("\n=== 性能比较 ===")
    print(f"\u539f始层次化编码器参数数量: {sum(p.numel() for p in original_encoder.parameters())}")
    print(f"\u589e强版层次化编码器参数数量: {sum(p.numel() for p in enhanced_encoder.parameters())}")

    return {
        'pattern_encoder_output_shape': pattern_features['output_features'].shape,
        'context_encoder_output_shape': context_features['context_features'].shape,
        'enhanced_encoder_output_shape': enhanced_features['output_features'].shape,
        'original_encoder_output_shape': original_features['output_features'].shape
    }


if __name__ == "__main__":
    # 如果直接运行这个文件，则执行测试
    test_hierarchical_encoder()
