#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
大规模分布式训练示例

展示如何使用分布式训练系统进行大规模训练。
支持数十到数百个worker的分布式自对弈数据产生和经验收集。
"""

import os
import sys
import argparse
import logging
import numpy as np
import torch
import random
from typing import Dict, Any

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cardgame_ai.distributed.coordinator import DistributedTrainingCoordinator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def parse_args():
    """
    解析命令行参数
    
    Returns:
        argparse.Namespace: 命令行参数
    """
    parser = argparse.ArgumentParser(description='大规模分布式训练示例')
    
    # 训练参数
    parser.add_argument('--num_iterations', type=int, default=1000, help='训练迭代次数')
    parser.add_argument('--episodes_per_iteration', type=int, default=32, help='每次迭代收集的回合数')
    parser.add_argument('--train_steps_per_iteration', type=int, default=1, help='每次迭代的训练步数')
    parser.add_argument('--batch_size', type=int, default=256, help='批次大小')
    parser.add_argument('--num_unroll_steps', type=int, default=5, help='展开步数')
    parser.add_argument('--td_steps', type=int, default=10, help='时序差分步数')
    parser.add_argument('--min_replay_size', type=int, default=10000, help='最小经验回放大小')
    parser.add_argument('--save_interval', type=int, default=100, help='保存间隔（迭代次数）')
    
    # 分布式参数
    parser.add_argument('--num_actors', type=int, default=16, help='Actor数量')
    parser.add_argument('--num_learners', type=int, default=1, help='Learner数量')
    parser.add_argument('--actor_device', type=str, default='cpu', help='Actor设备')
    parser.add_argument('--learner_device', type=str, default='cuda', help='Learner设备')
    
    # 模型参数
    parser.add_argument('--hidden_dim', type=int, default=256, help='隐藏层维度')
    parser.add_argument('--state_dim', type=int, default=64, help='状态维度')
    parser.add_argument('--use_resnet', action='store_true', help='是否使用ResNet')
    parser.add_argument('--projection_dim', type=int, default=256, help='投影维度')
    parser.add_argument('--prediction_dim', type=int, default=128, help='预测维度')
    parser.add_argument('--value_prefix_length', type=int, default=5, help='值前缀长度')
    
    # MCTS参数
    parser.add_argument('--num_simulations', type=int, default=50, help='MCTS模拟次数')
    parser.add_argument('--discount', type=float, default=0.997, help='折扣因子')
    parser.add_argument('--dirichlet_alpha', type=float, default=0.3, help='狄利克雷噪声参数')
    parser.add_argument('--exploration_fraction', type=float, default=0.25, help='探索比例')
    
    # 优化器参数
    parser.add_argument('--learning_rate', type=float, default=0.001, help='学习率')
    parser.add_argument('--weight_decay', type=float, default=1e-4, help='权重衰减')
    parser.add_argument('--optimizer_type', type=str, default='Adam', choices=['Adam', 'SGD'], help='优化器类型')
    parser.add_argument('--scheduler_type', type=str, default=None, choices=[None, 'StepLR', 'CosineAnnealingLR'], help='学习率调度器类型')
    
    # 经验回放缓冲区参数
    parser.add_argument('--replay_buffer_capacity', type=int, default=1000000, help='经验回放缓冲区容量')
    parser.add_argument('--num_shards', type=int, default=8, help='分片数量')
    parser.add_argument('--alpha', type=float, default=0.6, help='优先级指数')
    parser.add_argument('--beta', type=float, default=0.4, help='重要性采样指数')
    
    # 其他参数
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    parser.add_argument('--save_dir', type=str, default='models/distributed', help='保存目录')
    parser.add_argument('--resume', action='store_true', help='是否从检查点恢复训练')
    parser.add_argument('--model_path', type=str, default=None, help='模型路径，用于恢复训练')
    
    return parser.parse_args()


def main():
    """
    主函数
    """
    # 解析命令行参数
    args = parse_args()
    
    # 设置随机种子
    if args.seed is not None:
        np.random.seed(args.seed)
        torch.manual_seed(args.seed)
        random.seed(args.seed)
    
    # 创建保存目录
    os.makedirs(args.save_dir, exist_ok=True)
    
    # 创建模型配置
    model_config = {
        "observation_shape": (656,),
        "action_shape": (1000,),
        "hidden_dim": args.hidden_dim,
        "state_dim": args.state_dim,
        "use_resnet": args.use_resnet,
        "projection_dim": args.projection_dim,
        "prediction_dim": args.prediction_dim,
        "value_prefix_length": args.value_prefix_length
    }
    
    # 创建MCTS配置
    mcts_config = {
        "num_simulations": args.num_simulations,
        "discount": args.discount,
        "dirichlet_alpha": args.dirichlet_alpha,
        "exploration_fraction": args.exploration_fraction
    }
    
    # 创建优化器配置
    optimizer_config = {
        "type": args.optimizer_type,
        "learning_rate": args.learning_rate,
        "weight_decay": args.weight_decay,
        "scheduler_type": args.scheduler_type
    }
    
    # 创建经验回放缓冲区配置
    replay_buffer_config = {
        "total_capacity": args.replay_buffer_capacity,
        "num_shards": args.num_shards,
        "alpha": args.alpha,
        "beta": args.beta
    }
    
    # 创建分布式训练协调器
    coordinator = DistributedTrainingCoordinator(
        model_config=model_config,
        mcts_config=mcts_config,
        optimizer_config=optimizer_config,
        replay_buffer_config=replay_buffer_config,
        num_actors=args.num_actors,
        num_learners=args.num_learners,
        actor_device=args.actor_device,
        learner_device=args.learner_device,
        save_dir=args.save_dir,
        seed=args.seed
    )
    
    # 从检查点恢复训练
    if args.resume and args.model_path is not None:
        coordinator.load_model(args.model_path)
        logger.info(f"从检查点恢复训练: {args.model_path}")
    
    # 训练模型
    try:
        coordinator.train(
            num_iterations=args.num_iterations,
            episodes_per_iteration=args.episodes_per_iteration,
            train_steps_per_iteration=args.train_steps_per_iteration,
            batch_size=args.batch_size,
            num_unroll_steps=args.num_unroll_steps,
            td_steps=args.td_steps,
            min_replay_size=args.min_replay_size,
            save_interval=args.save_interval
        )
    except KeyboardInterrupt:
        logger.info("训练被中断")
    finally:
        # 保存最终模型
        coordinator.save_model()
        
        # 关闭协调器
        coordinator.shutdown()
    
    logger.info("训练完成")


if __name__ == "__main__":
    main()
