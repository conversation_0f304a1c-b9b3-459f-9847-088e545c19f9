"""
弹性权重固化(Elastic Weight Consolidation, EWC)模块

实现EWC算法，用于防止在线学习过程中的灾难性遗忘。
基于Kirkpatrick等人的论文 "Overcoming catastrophic forgetting in neural networks"。
"""

import copy
import logging
from typing import Dict, Any, List, Optional

import torch
import torch.nn as nn
import torch.nn.functional as F

logger = logging.getLogger(__name__)


class EWCPenalty:
    """
    弹性权重固化(EWC)惩罚计算类
    
    用于计算模型参数相对于先前任务参数的EWC惩罚项，以防止灾难性遗忘。
    支持对角Fisher近似和全Fisher矩阵两种方式。
    """
    
    def __init__(
        self,
        model: nn.Module,
        device: str = 'cuda' if torch.cuda.is_available() else 'cpu',
        fisher_estimation_samples: int = 200,
        diagonal_fisher: bool = True
    ):
        """
        初始化EWC惩罚计算器
        
        Args:
            model: 要保护的模型
            device: 计算设备
            fisher_estimation_samples: 用于估计Fisher信息矩阵的样本数量
            diagonal_fisher: 是否使用对角近似的Fisher矩阵
        """
        self.model = model
        self.device = device
        self.fisher_estimation_samples = fisher_estimation_samples
        self.diagonal_fisher = diagonal_fisher
        
        # 存储任务参数和Fisher矩阵
        self.task_parameters: Dict[str, Dict[str, torch.Tensor]] = {}
        self.fisher_matrices: Dict[str, Dict[str, torch.Tensor]] = {}
        
        # 任务计数
        self.task_count = 0
        
        logger.info(f"初始化EWC惩罚计算器: diagonal_fisher={diagonal_fisher}, "
                   f"fisher_estimation_samples={fisher_estimation_samples}")
    
    def compute_fisher(
        self,
        data_loader: torch.utils.data.DataLoader,
        log_likelihood_fn: callable
    ) -> Dict[str, torch.Tensor]:
        """
        计算Fisher信息矩阵
        
        Args:
            data_loader: 数据加载器，用于提供估计Fisher矩阵的样本
            log_likelihood_fn: 计算对数似然的函数，接收模型和数据批次，返回对数似然

        Returns:
            包含每个参数Fisher矩阵的字典
        """
        logger.info(f"开始计算Fisher信息矩阵，使用{self.fisher_estimation_samples}个样本")
        
        fisher_matrices = {}
        
        # 获取模型参数名称和形状
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                # 初始化对应参数的Fisher矩阵
                if self.diagonal_fisher:
                    # 对角近似时，Fisher矩阵与参数形状相同
                    fisher_matrices[name] = torch.zeros_like(param.data)
                else:
                    # 全Fisher矩阵，形状为 param_size x param_size
                    param_size = param.numel()
                    fisher_matrices[name] = torch.zeros((param_size, param_size), device=self.device)
                    
        # 设置模型为评估模式
        self.model.eval()
        
        # 计算样本数上限
        samples_seen = 0
        
        # 遍历数据集计算Fisher矩阵
        for batch in data_loader:
            # 如果已经处理了足够的样本，则停止
            if samples_seen >= self.fisher_estimation_samples:
                break
                
            # 使用提供的函数计算对数似然
            log_likelihood = log_likelihood_fn(self.model, batch)
            
            # 更新已处理样本数
            batch_size = self._get_batch_size(batch)
            samples_seen += batch_size
            
            # 计算梯度
            self.model.zero_grad()
            log_likelihood.backward()
            
            # 更新Fisher矩阵
            for name, param in self.model.named_parameters():
                if param.requires_grad and param.grad is not None:
                    if self.diagonal_fisher:
                        # 对角Fisher矩阵：梯度平方
                        fisher_matrices[name] += (param.grad ** 2) * batch_size
                    else:
                        # 全Fisher矩阵：外积
                        grad_flat = param.grad.view(-1)
                        fisher_matrices[name] += torch.ger(grad_flat, grad_flat) * batch_size
        
        # 归一化Fisher矩阵
        if samples_seen > 0:
            for name in fisher_matrices:
                fisher_matrices[name] /= samples_seen
                
        logger.info(f"Fisher信息矩阵计算完成，处理了{samples_seen}个样本")
        
        return fisher_matrices
    
    def _get_batch_size(self, batch: Any) -> int:
        """
        从批次数据中获取批次大小
        
        Args:
            batch: 批次数据，可能是张量、字典或其他格式

        Returns:
            批次大小
        """
        if isinstance(batch, torch.Tensor):
            return batch.shape[0]
        elif isinstance(batch, dict) and 'states' in batch and isinstance(batch['states'], list):
            return len(batch['states'])
        elif isinstance(batch, dict) and any(isinstance(batch[k], torch.Tensor) for k in batch):
            for k in batch:
                if isinstance(batch[k], torch.Tensor):
                    return batch[k].shape[0]
        elif isinstance(batch, list):
            return len(batch)
        
        # 默认批次大小
        return 1
    
    def register_task(
        self,
        task_id: Optional[str] = None,
        fisher_matrices: Optional[Dict[str, torch.Tensor]] = None,
        data_loader: Optional[torch.utils.data.DataLoader] = None,
        log_likelihood_fn: Optional[callable] = None
    ) -> str:
        """
        注册任务，保存当前模型参数和Fisher矩阵
        
        Args:
            task_id: 任务ID，如果为None则自动生成
            fisher_matrices: 预计算的Fisher矩阵，如果为None则使用data_loader计算
            data_loader: 数据加载器，用于计算Fisher矩阵
            log_likelihood_fn: 对数似然函数，用于计算Fisher矩阵

        Returns:
            任务ID
        """
        # 如果未提供任务ID，则使用自增计数
        if task_id is None:
            task_id = f"task_{self.task_count}"
            self.task_count += 1
            
        logger.info(f"注册任务: {task_id}")
        
        # 保存当前模型参数的副本
        parameters = {}
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                parameters[name] = param.data.clone().detach()
        
        self.task_parameters[task_id] = parameters
        
        # 如果提供了Fisher矩阵，则直接使用
        if fisher_matrices is not None:
            self.fisher_matrices[task_id] = fisher_matrices
        # 否则，如果提供了数据加载器和对数似然函数，则计算Fisher矩阵
        elif data_loader is not None and log_likelihood_fn is not None:
            self.fisher_matrices[task_id] = self.compute_fisher(data_loader, log_likelihood_fn)
        else:
            raise ValueError("必须提供预计算的Fisher矩阵或用于计算Fisher矩阵的数据加载器和对数似然函数")
            
        logger.info(f"任务 {task_id} 注册完成")
        
        return task_id
    
    def calculate(
        self,
        task_ids: Optional[List[str]] = None,
        importance: Optional[Dict[str, float]] = None
    ) -> torch.Tensor:
        """
        计算EWC惩罚项
        
        Args:
            task_ids: 要考虑的任务ID列表，如果为None则考虑所有已注册任务
            importance: 每个任务的重要性权重字典，默认所有任务权重相等

        Returns:
            EWC惩罚项，用于添加到损失函数中
        """
        # 如果没有注册任务，返回零惩罚
        if not self.task_parameters:
            return torch.tensor(0.0, device=self.device)
            
        # 如果未指定任务ID，则使用所有已注册任务
        if task_ids is None:
            task_ids = list(self.task_parameters.keys())
            
        # 如果未指定重要性权重，则所有任务权重相等
        if importance is None:
            importance = {task_id: 1.0 for task_id in task_ids}
        
        # 初始化总惩罚
        total_penalty = torch.tensor(0.0, device=self.device)
        
        # 对每个任务计算惩罚
        for task_id in task_ids:
            if task_id not in self.task_parameters or task_id not in self.fisher_matrices:
                logger.warning(f"任务 {task_id} 未注册，跳过")
                continue
                
            task_importance = importance.get(task_id, 1.0)
            
            # 对每个参数计算惩罚
            for name, param in self.model.named_parameters():
                if not param.requires_grad:
                    continue
                    
                if name not in self.task_parameters[task_id] or name not in self.fisher_matrices[task_id]:
                    continue
                
                # 获取参数和Fisher矩阵
                old_param = self.task_parameters[task_id][name]
                fisher = self.fisher_matrices[task_id][name]
                
                # 计算参数差异
                param_diff = param.data - old_param
                
                # 根据Fisher矩阵类型计算惩罚
                if self.diagonal_fisher:
                    # 对角Fisher矩阵：逐元素加权平方差
                    penalty = torch.sum(fisher * (param_diff ** 2))
                else:
                    # 全Fisher矩阵：二次型
                    param_diff_flat = param_diff.view(-1)
                    penalty = param_diff_flat @ fisher @ param_diff_flat
                
                # 添加到总惩罚
                total_penalty += task_importance * penalty
        
        return 0.5 * total_penalty
    
    def clear_tasks(self):
        """清除所有已注册任务的数据"""
        self.task_parameters.clear()
        self.fisher_matrices.clear()
        self.task_count = 0
        logger.info("已清除所有任务数据")
    
    def remove_task(self, task_id: str):
        """
        移除特定任务
        
        Args:
            task_id: 要移除的任务ID
        """
        if task_id in self.task_parameters:
            del self.task_parameters[task_id]
        
        if task_id in self.fisher_matrices:
            del self.fisher_matrices[task_id]
            
        logger.info(f"已移除任务: {task_id}")
        
    def save(self, path: str):
        """
        保存EWC状态到文件
        
        Args:
            path: 保存路径
        """
        state = {
            'task_parameters': self.task_parameters,
            'fisher_matrices': self.fisher_matrices,
            'task_count': self.task_count,
            'diagonal_fisher': self.diagonal_fisher,
            'fisher_estimation_samples': self.fisher_estimation_samples
        }
        
        torch.save(state, path)
        logger.info(f"EWC状态已保存到: {path}")
        
    def load(self, path: str):
        """
        从文件加载EWC状态
        
        Args:
            path: 加载路径
        """
        state = torch.load(path, map_location=self.device)
        
        self.task_parameters = state['task_parameters']
        self.fisher_matrices = state['fisher_matrices']
        self.task_count = state['task_count']
        self.diagonal_fisher = state.get('diagonal_fisher', True)
        self.fisher_estimation_samples = state.get('fisher_estimation_samples', 200)
        
        logger.info(f"已从 {path} 加载EWC状态，包含 {len(self.task_parameters)} 个任务") 