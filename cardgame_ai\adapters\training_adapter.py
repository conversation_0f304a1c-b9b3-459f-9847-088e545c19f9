"""
训练服务适配器

该适配器将现有的train_efficient_zero函数适配为TrainingInterface接口，
实现zhuchengxu模块与训练服务的解耦。

设计目标:
- 适配现有训练函数为标准接口
- 保持完全的功能兼容性
- 提供异步训练控制能力
- 实现fail-fast原则

作者: Full Stack Dev James
版本: v1.0
"""

import threading
import time
import traceback
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime
from dataclasses import asdict

from cardgame_ai.interfaces.training_interface import (
    TrainingInterface, TrainingConfig, TrainingResult, TrainingStatus
)


class EfficientZeroTrainingAdapter(TrainingInterface):
    """EfficientZero训练适配器
    
    将现有的train_efficient_zero函数适配为TrainingInterface接口。
    提供标准化的训练服务，支持异步训练控制和状态监控。
    
    注意:
        该适配器严格遵循代码技术要求规则，不使用任何模拟或降级处理。
        所有错误都会快速失败，确保问题能够及时发现和解决。
    """
    
    def __init__(self):
        """初始化训练适配器
        
        注意:
            初始化时会验证训练模块的可用性，如果无法导入则立即失败。
        """
        self._status = TrainingStatus.IDLE
        self._current_config = None
        self._training_thread = None
        self._result = None
        self._callbacks = {}
        self._start_time = None
        self._stop_event = threading.Event()
        
        # 导入训练函数 - 必须成功，否则立即失败
        try:
            from cardgame_ai.training.train_efficient_zero import train_efficient_zero
            self._train_func = train_efficient_zero
        except ImportError as e:
            raise ImportError(f"无法导入训练模块: {e}") from e
    
    def validate_config(self, config: TrainingConfig) -> bool:
        """验证训练配置
        
        Args:
            config: 训练配置对象
            
        Returns:
            bool: 配置是否有效
            
        注意:
            严格验证所有配置参数，任何无效配置都会导致验证失败。
        """
        try:
            # 验证基础配置
            if not config.game or config.game not in ["doudizhu"]:
                return False
            
            if not config.algorithm or config.algorithm not in ["efficient_zero"]:
                return False
            
            # 验证训练参数
            if config.epochs <= 0 or config.batch_size <= 0:
                return False
            
            if config.learning_rate <= 0 or config.learning_rate >= 1:
                return False
            
            if config.num_simulations <= 0:
                return False
            
            # 验证频率参数
            if (config.save_frequency <= 0 or 
                config.eval_frequency <= 0 or 
                config.log_frequency <= 0):
                return False
            
            # 验证权重参数
            if (config.cooperation_weight < 0 or config.cooperation_weight > 1 or
                config.team_reward_weight < 0 or config.team_reward_weight > 1):
                return False
            
            return True
            
        except Exception:
            # 验证过程中的任何异常都视为配置无效
            return False
    
    def start_training(self, config: TrainingConfig) -> bool:
        """启动训练
        
        Args:
            config: 训练配置对象
            
        Returns:
            bool: 是否成功启动
            
        注意:
            训练在后台线程中异步执行，此方法立即返回。
            如果配置无效或系统状态不允许启动，则快速失败。
        """
        # 验证当前状态
        if self._status != TrainingStatus.IDLE:
            return False
        
        # 验证配置
        if not self.validate_config(config):
            return False
        
        try:
            # 保存配置和状态
            self._current_config = config
            self._status = TrainingStatus.INITIALIZING
            self._result = None
            self._start_time = datetime.now()
            self._stop_event.clear()
            
            # 启动训练线程
            self._training_thread = threading.Thread(
                target=self._run_training,
                daemon=False
            )
            self._training_thread.start()
            
            # 等待初始化完成
            time.sleep(0.1)
            
            return True
            
        except Exception as e:
            self._status = TrainingStatus.FAILED
            self._result = TrainingResult(
                status=TrainingStatus.FAILED,
                exit_code=1,
                message=f"启动训练失败: {e}"
            )
            # 实现fail-fast原则，不允许降级处理
            raise RuntimeError(f"训练启动失败: {e}") from e
    
    def stop_training(self) -> bool:
        """停止训练
        
        Returns:
            bool: 是否成功停止
        """
        if self._status not in [TrainingStatus.TRAINING, TrainingStatus.PAUSED]:
            return False
        
        try:
            self._stop_event.set()
            
            # 等待训练线程结束
            if self._training_thread and self._training_thread.is_alive():
                self._training_thread.join(timeout=30)
            
            self._status = TrainingStatus.CANCELLED
            return True
            
        except Exception:
            return False
    
    def pause_training(self) -> bool:
        """暂停训练
        
        Returns:
            bool: 是否成功暂停
            
        注意:
            当前实现不支持暂停功能，返回False。
        """
        # EfficientZero训练暂停功能需要在训练函数中实现
        return False
    
    def resume_training(self) -> bool:
        """恢复训练
        
        Returns:
            bool: 是否成功恢复
            
        注意:
            当前实现不支持恢复功能，返回False。
        """
        # EfficientZero训练恢复功能需要在训练函数中实现
        return False
    
    def get_status(self) -> TrainingStatus:
        """获取训练状态
        
        Returns:
            TrainingStatus: 当前训练状态
        """
        return self._status
    
    def get_progress(self) -> Dict[str, Any]:
        """获取训练进度
        
        Returns:
            Dict[str, Any]: 训练进度信息
        """
        if not self._current_config:
            return {}
        
        progress = {
            "current_epoch": 0,  # 需要从训练函数获取实际进度
            "total_epochs": self._current_config.epochs,
            "progress_percent": 0.0,
            "estimated_time_remaining": None,
            "current_metrics": {}
        }
        
        # 计算运行时间
        if self._start_time:
            elapsed = (datetime.now() - self._start_time).total_seconds()
            progress["elapsed_time"] = elapsed
        
        return progress
    
    def get_result(self) -> Optional[TrainingResult]:
        """获取训练结果
        
        Returns:
            Optional[TrainingResult]: 训练结果，如果训练未完成返回None
        """
        return self._result
    
    def wait_for_completion(self, timeout: Optional[float] = None) -> TrainingResult:
        """等待训练完成
        
        Args:
            timeout: 超时时间(秒)，None表示无限等待
            
        Returns:
            TrainingResult: 训练结果
            
        Raises:
            TimeoutError: 超时异常
        """
        if self._training_thread:
            self._training_thread.join(timeout=timeout)
            
            if self._training_thread.is_alive():
                raise TimeoutError("等待训练完成超时")
        
        return self._result or TrainingResult(
            status=TrainingStatus.FAILED,
            exit_code=1,
            message="训练结果不可用"
        )
    
    def register_callback(self, event: str, callback: Callable) -> bool:
        """注册事件回调
        
        Args:
            event: 事件名称
            callback: 回调函数
            
        Returns:
            bool: 是否成功注册
        """
        try:
            if event not in self._callbacks:
                self._callbacks[event] = []
            self._callbacks[event].append(callback)
            return True
        except Exception:
            return False
    
    def get_supported_algorithms(self) -> List[str]:
        """获取支持的算法列表
        
        Returns:
            List[str]: 支持的算法名称列表
        """
        return ["efficient_zero"]
    
    def get_default_config(self, algorithm: str) -> TrainingConfig:
        """获取算法的默认配置
        
        Args:
            algorithm: 算法名称
            
        Returns:
            TrainingConfig: 默认配置
        """
        if algorithm == "efficient_zero":
            return TrainingConfig(
                game="doudizhu",
                algorithm="efficient_zero",
                device="auto",
                epochs=1000,
                batch_size=256,
                learning_rate=0.0005,
                num_simulations=100,
                farmer_cooperation_enabled=True,
                cooperation_weight=0.8,
                team_reward_weight=0.9
            )
        else:
            raise ValueError(f"不支持的算法: {algorithm}")
    
    def _run_training(self):
        """运行训练的内部方法
        
        在后台线程中执行实际的训练过程。
        
        注意:
            该方法严格遵循fail-fast原则，任何错误都会立即失败。
        """
        try:
            self._status = TrainingStatus.TRAINING
            self._trigger_callback("training_started", self._current_config)
            
            # 转换配置为训练函数需要的格式
            config_dict = self._convert_config_to_dict(self._current_config)
            
            # 调用实际的训练函数
            exit_code = self._train_func(self._current_config.game, config_dict)
            
            # 检查是否被停止
            if self._stop_event.is_set():
                self._status = TrainingStatus.CANCELLED
                self._result = TrainingResult(
                    status=TrainingStatus.CANCELLED,
                    exit_code=1,
                    message="训练被用户取消"
                )
            elif exit_code == 0:
                self._status = TrainingStatus.COMPLETED
                self._result = TrainingResult(
                    status=TrainingStatus.COMPLETED,
                    exit_code=0,
                    message="训练成功完成",
                    total_epochs=self._current_config.epochs,
                    completed_epochs=self._current_config.epochs,
                    training_time=(datetime.now() - self._start_time).total_seconds()
                )
            else:
                self._status = TrainingStatus.FAILED
                self._result = TrainingResult(
                    status=TrainingStatus.FAILED,
                    exit_code=exit_code,
                    message=f"训练失败，退出码: {exit_code}"
                )
            
            self._trigger_callback("training_completed", self._result)
            
        except Exception as e:
            self._status = TrainingStatus.FAILED
            self._result = TrainingResult(
                status=TrainingStatus.FAILED,
                exit_code=1,
                message=f"训练异常: {e}",
                training_time=(datetime.now() - self._start_time).total_seconds() if self._start_time else 0
            )
            self._trigger_callback("training_error", e)
    
    def _convert_config_to_dict(self, config: TrainingConfig) -> Dict[str, Any]:
        """将TrainingConfig转换为字典格式
        
        Args:
            config: 训练配置对象
            
        Returns:
            Dict[str, Any]: 配置字典
        """
        config_dict = asdict(config)
        
        # 移除自定义配置字段，因为训练函数不需要
        if 'custom_config' in config_dict:
            custom = config_dict.pop('custom_config')
            if custom:
                config_dict.update(custom)
        
        return config_dict
    
    def _trigger_callback(self, event: str, data: Any):
        """触发事件回调
        
        Args:
            event: 事件名称
            data: 事件数据
        """
        if event in self._callbacks:
            for callback in self._callbacks[event]:
                try:
                    callback(data)
                except Exception:
                    # 回调异常不应该影响主流程
                    pass
