#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
训练控制组件

包含开始、暂停、停止训练等控制按钮，以及训练进度和状态显示。
"""

import os
import time
import logging
from enum import Enum
from typing import Dict, Any, Optional

from PySide6.QtWidgets import (
    QWidget, QHBoxLayout, QVBoxLayout, QPushButton, QLabel,
    QProgressBar, QSpacerItem, QSizePolicy, QFrame, QGroupBox,
    QLineEdit, QCheckBox, QFileDialog
)
from PySide6.QtCore import Qt, Signal, Slot, QTimer, QSize
from PySide6.QtGui import QFont, QIcon

logger = logging.getLogger(__name__)


class TrainingStatus(Enum):
    """训练状态枚举"""
    READY = 0      # 就绪
    RUNNING = 1    # 运行中
    PAUSED = 2     # 暂停
    STOPPED = 3    # 停止
    COMPLETED = 4  # 完成
    ERROR = 5      # 错误


class TrainingControlPanel(QWidget):
    """训练控制组件类"""

    # 训练控制信号
    training_started = Signal(Dict[str, Any])  # 开始训练信号，发送训练参数
    training_paused = Signal()                 # 暂停训练信号
    training_resumed = Signal()                # 恢复训练信号
    training_stopped = Signal()                # 停止训练信号

    # 模型路径信号
    model_path_changed = Signal(str)           # 模型路径变化信号
    auto_save_changed = Signal(bool)           # 自动保存变化信号

    def __init__(self, config, parent=None):
        """
        初始化训练控制组件

        Args:
            config: 客户端配置
            parent: 父部件
        """
        super().__init__(parent)

        # 保存配置
        self.config = config

        # 设置对象名称
        self.setObjectName("trainingControlPanel")

        # 训练状态
        self.status = TrainingStatus.READY

        # 训练参数
        self.training_params = {}

        # 训练计时器
        self.timer = QTimer(self)
        self.timer.setInterval(1000)  # 1秒更新一次
        self.timer.timeout.connect(self.update_training_time)

        # 训练开始时间
        self.start_time = 0

        # 训练暂停时间
        self.pause_time = 0

        # 训练总时间（秒）
        self.total_time = 0

        # 模型路径
        self.model_path = ""

        # 自动保存模型
        self.auto_save = True

        # 初始化UI
        self.setup_ui()

        logger.info("训练控制组件初始化完成")

    def setup_ui(self):
        """设置UI布局"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)  # 减少边距
        main_layout.setSpacing(5)  # 减少间距

        # 创建状态和进度区域
        status_layout = QHBoxLayout()
        status_layout.setContentsMargins(0, 0, 0, 0)  # 减少边距
        status_layout.setSpacing(5)  # 减少间距

        # 创建状态标签
        self.status_label = QLabel("就绪")
        self.status_label.setFont(QFont("Microsoft YaHei", 10))
        status_layout.addWidget(self.status_label)

        # 添加间隔
        status_layout.addSpacerItem(QSpacerItem(20, 10, QSizePolicy.Expanding, QSizePolicy.Minimum))

        # 创建时间标签
        self.time_label = QLabel("00:00:00")
        self.time_label.setFont(QFont("Microsoft YaHei", 10))
        status_layout.addWidget(self.time_label)

        # 添加状态和进度区域到主布局
        main_layout.addLayout(status_layout)

        # 创建进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("%p% (%v/%m)")
        self.progress_bar.setMinimumHeight(20)  # 减小高度
        main_layout.addWidget(self.progress_bar)

        # 创建模型设置区域
        model_group = QGroupBox("模型设置")
        model_group.setContentsMargins(5, 5, 5, 5)  # 减少边距
        model_layout = QVBoxLayout(model_group)
        model_layout.setContentsMargins(5, 5, 5, 5)  # 减少边距
        model_layout.setSpacing(5)  # 减少间距

        # 创建模型路径区域
        path_layout = QHBoxLayout()
        path_layout.setContentsMargins(0, 0, 0, 0)  # 减少边距
        path_layout.setSpacing(5)  # 减少间距

        path_label = QLabel("模型路径:")
        path_layout.addWidget(path_label)

        self.path_edit = QLineEdit()
        self.path_edit.setPlaceholderText("输入继续训练的模型路径")
        self.path_edit.textChanged.connect(self.on_model_path_changed)
        path_layout.addWidget(self.path_edit)

        self.browse_button = QPushButton("浏览...")
        self.browse_button.setMaximumWidth(60)  # 减小宽度
        self.browse_button.clicked.connect(self.browse_model_path)
        path_layout.addWidget(self.browse_button)

        model_layout.addLayout(path_layout)

        # 创建自动保存选项
        self.auto_save_check = QCheckBox("自动保存模型")
        self.auto_save_check.setChecked(self.auto_save)
        self.auto_save_check.stateChanged.connect(self.on_auto_save_changed)
        model_layout.addWidget(self.auto_save_check)

        # 添加模型设置区域到主布局
        main_layout.addWidget(model_group)

        # 创建按钮区域
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 0, 0, 0)  # 减少边距
        button_layout.setSpacing(5)  # 减少间距

        # 创建开始按钮
        self.start_button = QPushButton("开始训练")
        self.start_button.setMinimumWidth(100)  # 减小宽度
        self.start_button.setMinimumHeight(28)  # 减小高度
        self.start_button.clicked.connect(self.start_training)
        button_layout.addWidget(self.start_button)

        # 创建暂停按钮
        self.pause_button = QPushButton("暂停训练")
        self.pause_button.setMinimumWidth(100)  # 减小宽度
        self.pause_button.setMinimumHeight(28)  # 减小高度
        self.pause_button.clicked.connect(self.pause_training)
        self.pause_button.setEnabled(False)
        button_layout.addWidget(self.pause_button)

        # 创建停止按钮
        self.stop_button = QPushButton("停止训练")
        self.stop_button.setMinimumWidth(100)  # 减小宽度
        self.stop_button.setMinimumHeight(28)  # 减小高度
        self.stop_button.clicked.connect(self.stop_training)
        self.stop_button.setEnabled(False)
        button_layout.addWidget(self.stop_button)

        # 添加按钮区域到主布局
        main_layout.addLayout(button_layout)

        logger.info("训练控制组件UI布局设置完成")

    def start_training(self):
        """开始训练"""
        if self.status == TrainingStatus.READY or self.status == TrainingStatus.STOPPED or self.status == TrainingStatus.COMPLETED:
            # 设置状态为运行中
            self.set_status(TrainingStatus.RUNNING)

            # 记录开始时间
            self.start_time = time.time()
            self.total_time = 0

            # 启动计时器
            self.timer.start()

            # 发送开始训练信号
            self.training_started.emit(self.training_params)

            logger.info("开始训练")
        elif self.status == TrainingStatus.PAUSED:
            # 恢复训练
            self.resume_training()

    def pause_training(self):
        """暂停训练"""
        if self.status == TrainingStatus.RUNNING:
            # 设置状态为暂停
            self.set_status(TrainingStatus.PAUSED)

            # 记录暂停时间
            self.pause_time = time.time()

            # 停止计时器
            self.timer.stop()

            # 发送暂停训练信号
            self.training_paused.emit()

            logger.info("暂停训练")

    def resume_training(self):
        """恢复训练"""
        if self.status == TrainingStatus.PAUSED:
            # 设置状态为运行中
            self.set_status(TrainingStatus.RUNNING)

            # 计算暂停时间
            pause_duration = time.time() - self.pause_time

            # 调整开始时间
            self.start_time += pause_duration

            # 启动计时器
            self.timer.start()

            # 发送恢复训练信号
            self.training_resumed.emit()

            logger.info("恢复训练")

    def stop_training(self):
        """停止训练"""
        if self.status == TrainingStatus.RUNNING or self.status == TrainingStatus.PAUSED:
            # 设置状态为停止
            self.set_status(TrainingStatus.STOPPED)

            # 停止计时器
            self.timer.stop()

            # 发送停止训练信号
            self.training_stopped.emit()

            logger.info("停止训练")

    def set_status(self, status: TrainingStatus):
        """
        设置训练状态

        Args:
            status (TrainingStatus): 训练状态
        """
        # 保存状态
        self.status = status

        # 更新UI
        if status == TrainingStatus.READY:
            self.status_label.setText("就绪")
            self.start_button.setText("开始训练")
            self.start_button.setEnabled(True)
            self.pause_button.setEnabled(False)
            self.stop_button.setEnabled(False)
        elif status == TrainingStatus.RUNNING:
            self.status_label.setText("训练中")
            self.start_button.setText("开始训练")
            self.start_button.setEnabled(False)
            self.pause_button.setText("暂停训练")
            self.pause_button.setEnabled(True)
            self.stop_button.setEnabled(True)
        elif status == TrainingStatus.PAUSED:
            self.status_label.setText("已暂停")
            self.start_button.setText("继续训练")
            self.start_button.setEnabled(True)
            self.pause_button.setText("暂停训练")
            self.pause_button.setEnabled(False)
            self.stop_button.setEnabled(True)
        elif status == TrainingStatus.STOPPED:
            self.status_label.setText("已停止")
            self.start_button.setText("开始训练")
            self.start_button.setEnabled(True)
            self.pause_button.setEnabled(False)
            self.stop_button.setEnabled(False)
        elif status == TrainingStatus.COMPLETED:
            self.status_label.setText("已完成")
            self.start_button.setText("开始训练")
            self.start_button.setEnabled(True)
            self.pause_button.setEnabled(False)
            self.stop_button.setEnabled(False)
        elif status == TrainingStatus.ERROR:
            self.status_label.setText("错误")
            self.start_button.setText("开始训练")
            self.start_button.setEnabled(True)
            self.pause_button.setEnabled(False)
            self.stop_button.setEnabled(False)

        logger.info(f"设置训练状态：{status.name}")

    def update_training_time(self):
        """更新训练时间"""
        if self.status == TrainingStatus.RUNNING:
            # 计算训练时间
            self.total_time = int(time.time() - self.start_time)

            # 更新时间标签
            self.time_label.setText(self.format_time(self.total_time))

    def format_time(self, seconds: int) -> str:
        """
        格式化时间

        Args:
            seconds (int): 秒数

        Returns:
            str: 格式化后的时间字符串（HH:MM:SS）
        """
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60

        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"

    def set_progress(self, current: int, total: int):
        """
        设置训练进度

        Args:
            current (int): 当前进度
            total (int): 总进度
        """
        # 设置进度条范围
        self.progress_bar.setRange(0, total)

        # 设置进度条值
        self.progress_bar.setValue(current)

        # 如果训练完成
        if current >= total and self.status == TrainingStatus.RUNNING:
            # 设置状态为完成
            self.set_status(TrainingStatus.COMPLETED)

            # 停止计时器
            self.timer.stop()

            logger.info("训练完成")

    def set_training_params(self, params: Dict[str, Any]):
        """
        设置训练参数

        Args:
            params (Dict[str, Any]): 训练参数
        """
        # 保存训练参数
        self.training_params = params

        # 添加模型路径和自动保存选项
        self.training_params["model_path"] = self.model_path
        self.training_params["auto_save"] = self.auto_save

        logger.info("设置训练参数")

    def get_training_params(self) -> Dict[str, Any]:
        """
        获取训练参数

        Returns:
            Dict[str, Any]: 训练参数
        """
        # 确保参数包含最新的模型路径和自动保存选项
        self.training_params["model_path"] = self.model_path
        self.training_params["auto_save"] = self.auto_save

        return self.training_params

    def reset(self):
        """重置训练控制组件"""
        # 设置状态为就绪
        self.set_status(TrainingStatus.READY)

        # 重置进度条
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)

        # 重置时间
        self.time_label.setText("00:00:00")
        self.total_time = 0

        # 停止计时器
        self.timer.stop()

        logger.info("重置训练控制组件")

    def on_training_error(self, error_message: str):
        """
        训练错误处理

        Args:
            error_message (str): 错误信息
        """
        # 设置状态为错误
        self.set_status(TrainingStatus.ERROR)

        # 停止计时器
        self.timer.stop()

        # 更新状态标签
        self.status_label.setText(f"错误: {error_message}")

        logger.error(f"训练错误: {error_message}")

    def on_model_path_changed(self, path):
        """
        模型路径变化处理

        Args:
            path (str): 模型路径
        """
        self.model_path = path
        self.model_path_changed.emit(path)
        logger.info(f"模型路径变化: {path}")

    def browse_model_path(self):
        """浏览模型路径"""
        # 获取模型目录
        models_dir = self.config.get("paths.models", "models")

        # 打开文件对话框
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择模型文件",
            models_dir,
            "模型文件 (*.pt *.pth *.h5 *.model);;所有文件 (*.*)"
        )

        if file_path:
            self.path_edit.setText(file_path)

    def on_auto_save_changed(self, state):
        """
        自动保存变化处理

        Args:
            state (int): 复选框状态
        """
        self.auto_save = (state == Qt.Checked)
        self.auto_save_changed.emit(self.auto_save)
        logger.info(f"自动保存变化: {self.auto_save}")

    def get_model_path(self):
        """
        获取模型路径

        Returns:
            str: 模型路径
        """
        return self.model_path

    def is_auto_save_enabled(self):
        """
        是否启用自动保存

        Returns:
            bool: 是否启用自动保存
        """
        return self.auto_save
