#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
优化效果测试脚本

测试各项优化组件的性能和功能：
- 数据加载器性能测试
- MCTS搜索效率测试
- 动态批次大小测试
- 性能监控测试
"""

import os
import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到路径
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(script_dir, '..'))
sys.path.insert(0, project_root)

import numpy as np
import torch
from torch.utils.data import TensorDataset

# 导入优化组件
from cardgame_ai.utils.optimized_data_loader import OptimizedDataLoader, StreamingDataset
from cardgame_ai.training.dynamic_optimizer import DynamicBatchSizeManager, AdaptiveLearningRateScheduler
from cardgame_ai.mcts.optimized_mcts import OptimizedMCTS, NodePool
from cardgame_ai.utils.performance_monitor import PerformanceMonitor

logger = logging.getLogger(__name__)


def setup_test_logging():
    """设置测试日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler()]
    )


def test_data_loader_performance():
    """测试数据加载器性能"""
    print("=" * 60)
    print("测试数据加载器性能")
    print("=" * 60)
    
    # 创建测试数据
    batch_size = 256
    num_samples = 10000
    feature_dim = 656
    
    # 生成随机数据
    features = torch.randn(num_samples, feature_dim)
    labels = torch.randint(0, 2, (num_samples,))
    dataset = TensorDataset(features, labels)
    
    # 测试标准数据加载器
    print("测试标准PyTorch DataLoader...")
    start_time = time.time()
    
    from torch.utils.data import DataLoader
    standard_loader = DataLoader(
        dataset, batch_size=batch_size, shuffle=True, num_workers=4
    )
    
    standard_batches = 0
    for batch in standard_loader:
        standard_batches += 1
        if standard_batches >= 20:  # 只测试20个批次
            break
    
    standard_time = time.time() - start_time
    print(f"标准DataLoader: {standard_batches}批次, 用时: {standard_time:.3f}秒")
    
    # 测试优化数据加载器
    print("测试优化DataLoader...")
    start_time = time.time()
    
    optimized_loader = OptimizedDataLoader(
        dataset=dataset,
        batch_size=batch_size,
        num_workers=8,  # 更多worker
        pin_memory=True,
        prefetch_factor=6,
        persistent_workers=True
    )
    
    optimized_loader.start_prefetch()
    
    optimized_batches = 0
    for batch in optimized_loader:
        optimized_batches += 1
        if optimized_batches >= 20:
            break
    
    optimized_loader.stop_prefetch_thread()
    optimized_time = time.time() - start_time
    print(f"优化DataLoader: {optimized_batches}批次, 用时: {optimized_time:.3f}秒")
    
    # 计算性能提升
    if standard_time > 0:
        improvement = (standard_time - optimized_time) / standard_time * 100
        print(f"性能提升: {improvement:.1f}%")
    
    print("数据加载器测试完成\n")


def test_dynamic_batch_size():
    """测试动态批次大小管理"""
    print("=" * 60)
    print("测试动态批次大小管理")
    print("=" * 60)
    
    manager = DynamicBatchSizeManager(
        base_batch_size=256,
        min_batch_size=128,
        max_batch_size=384,
        memory_threshold=0.8
    )
    
    print(f"初始批次大小: {manager.get_optimal_batch_size()}")
    
    # 模拟不同内存使用情况
    memory_scenarios = [0.5, 0.7, 0.85, 0.9, 0.6, 0.75]
    
    for i, memory_usage in enumerate(memory_scenarios):
        # 模拟内存使用
        if torch.cuda.is_available():
            # 实际分配一些GPU内存来模拟
            dummy_tensor = torch.randn(1000, 1000).cuda()
            
        manager.memory_history.append(memory_usage)
        optimal_size = manager.get_optimal_batch_size()
        
        print(f"场景 {i+1}: 内存使用 {memory_usage:.1%} -> 批次大小 {optimal_size}")
        
        # 清理内存
        if torch.cuda.is_available():
            del dummy_tensor
            torch.cuda.empty_cache()
    
    # 显示统计信息
    stats = manager.get_statistics()
    print(f"统计信息: {stats}")
    
    print("动态批次大小测试完成\n")


def test_mcts_optimization():
    """测试MCTS搜索优化"""
    print("=" * 60)
    print("测试MCTS搜索优化")
    print("=" * 60)
    
    # MCTS配置
    config = {
        'num_simulations': 100,
        'c_puct': 1.4,
        'parallel_threads': 4,
        'batch_size_inference': 16,
        'dynamic_budget': True,
        'node_pool_size': 5000
    }
    
    mcts = OptimizedMCTS(config)
    
    # 测试节点池
    print("测试节点池...")
    node_pool = NodePool(size=1000)
    
    # 获取和归还节点
    nodes = []
    start_time = time.time()
    
    for i in range(500):
        node = node_pool.get_node()
        nodes.append(node)
    
    get_time = time.time() - start_time
    print(f"获取500个节点用时: {get_time:.4f}秒")
    
    start_time = time.time()
    for node in nodes:
        node_pool.return_node(node)
    
    return_time = time.time() - start_time
    print(f"归还500个节点用时: {return_time:.4f}秒")
    
    # 测试动态预算分配
    print("测试动态预算分配...")
    
    class MockState:
        def __init__(self, phase=1, hands=None):
            self.game_phase = MockPhase(phase)
            self.hands = hands or [list(range(10)), list(range(8)), list(range(5))]
    
    class MockPhase:
        def __init__(self, value):
            self.value = value
    
    # 测试不同游戏阶段的预算分配
    states = [
        MockState(0),  # 早期
        MockState(1),  # 中期
        MockState(2),  # 后期
        MockState(1, [list(range(2)), list(range(8)), list(range(5))]),  # 关键时刻
    ]
    
    for i, state in enumerate(states):
        budget = mcts._get_dynamic_budget(state)
        is_critical = mcts._is_critical_moment(state)
        print(f"状态 {i+1}: 预算 {budget}, 关键时刻: {is_critical}")
    
    print("MCTS优化测试完成\n")


def test_performance_monitor():
    """测试性能监控"""
    print("=" * 60)
    print("测试性能监控")
    print("=" * 60)
    
    monitor = PerformanceMonitor(monitor_interval=0.1, history_size=100)
    
    # 启动监控
    monitor.start_monitoring()
    print("性能监控已启动")
    
    # 模拟训练步骤
    for step in range(10):
        # 模拟训练指标
        metrics = {
            'loss': np.random.uniform(0.1, 1.0),
            'accuracy': np.random.uniform(0.7, 0.95),
            'learning_rate': 0.001 * (0.99 ** step)
        }
        
        monitor.log_training_step(metrics)
        time.sleep(0.1)  # 模拟训练时间
        
        if step % 3 == 0:
            print(f"步骤 {step}: 损失 {metrics['loss']:.3f}")
    
    # 模拟epoch完成
    epoch_metrics = {
        'epoch_loss': 0.25,
        'epoch_accuracy': 0.85,
        'epoch_time': 120.5
    }
    monitor.log_epoch_completion(epoch_metrics)
    
    # 等待一段时间收集系统指标
    time.sleep(1.0)
    
    # 停止监控
    monitor.stop_monitoring()
    print("性能监控已停止")
    
    # 获取性能摘要
    summary = monitor.get_performance_summary()
    print(f"性能摘要:")
    print(f"  监控时长: {summary['monitoring_duration']:.1f}秒")
    print(f"  总步数: {summary['total_steps']}")
    print(f"  平均吞吐量: {summary['avg_throughput']:.2f} 步/秒")
    
    # 生成报告
    report = monitor.generate_performance_report()
    print("性能报告:")
    print(report[:500] + "..." if len(report) > 500 else report)
    
    print("性能监控测试完成\n")


def test_integration():
    """集成测试"""
    print("=" * 60)
    print("集成测试")
    print("=" * 60)
    
    # 测试所有组件是否能正常协作
    print("测试组件集成...")
    
    # 创建配置
    config = {
        'mcts': {
            'num_simulations': 50,
            'parallel_threads': 2,
            'batch_size_inference': 8
        },
        'data': {
            'num_workers': 4,
            'batch_size': 128,
            'prefetch_factor': 4
        },
        'training': {
            'learning_rate': 0.001,
            'batch_size': {'base': 128}
        }
    }
    
    # 初始化组件
    mcts = OptimizedMCTS(config['mcts'])
    batch_manager = DynamicBatchSizeManager(base_batch_size=128)
    monitor = PerformanceMonitor(monitor_interval=0.5)
    
    print("所有组件初始化成功")
    
    # 启动监控
    monitor.start_monitoring()
    
    # 模拟短期训练
    for step in range(5):
        # 获取最优批次大小
        optimal_batch_size = batch_manager.get_optimal_batch_size()
        
        # 记录指标
        metrics = {
            'step': step,
            'batch_size': optimal_batch_size,
            'loss': np.random.uniform(0.2, 0.8)
        }
        monitor.log_training_step(metrics)
        
        print(f"步骤 {step}: 批次大小 {optimal_batch_size}, 损失 {metrics['loss']:.3f}")
        time.sleep(0.2)
    
    # 停止监控
    monitor.stop_monitoring()
    
    print("集成测试完成")


def main():
    """主测试函数"""
    setup_test_logging()
    
    print("🧪 开始优化组件测试")
    print("=" * 80)
    
    try:
        # 运行各项测试
        test_data_loader_performance()
        test_dynamic_batch_size()
        test_mcts_optimization()
        test_performance_monitor()
        test_integration()
        
        print("=" * 80)
        print("✅ 所有测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
