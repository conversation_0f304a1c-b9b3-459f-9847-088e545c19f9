# 操作指南

## 概述

本文档定义了斗地主AI优化项目的操作标准，包括编码规范、测试策略、错误处理和安全最佳实践。

## 编码标准

### Python代码规范

#### 基本规范
- **Python版本**: 3.8-3.11
- **编码格式**: UTF-8
- **行长度**: 最大88字符 (Black默认)
- **缩进**: 4个空格，不使用Tab

#### 代码质量工具
```bash
# 代码格式化
black .

# 代码检查
flake8 .

# 类型检查
mypy .

# 导入排序
isort .
```

#### 命名约定
- **变量**: `snake_case`
- **函数**: `snake_case`
- **类**: `PascalCase`
- **常量**: `UPPER_SNAKE_CASE`
- **私有成员**: `_leading_underscore`
- **特殊方法**: `__double_underscore__`

#### 类型提示
```python
from typing import List, Dict, Optional, Union, Tuple

def train_model(
    data: List[Dict[str, Any]], 
    epochs: int = 100,
    learning_rate: float = 0.001
) -> Tuple[float, Dict[str, float]]:
    """训练模型函数示例"""
    pass
```

#### 文档字符串
```python
def efficient_zero_train(
    config: TrainingConfig,
    model: EfficientZeroModel,
    data_loader: DataLoader
) -> TrainingResult:
    """
    使用EfficientZero算法训练模型
    
    Args:
        config: 训练配置参数
        model: EfficientZero模型实例
        data_loader: 训练数据加载器
        
    Returns:
        TrainingResult: 包含训练指标和模型状态的结果对象
        
    Raises:
        TrainingError: 当训练过程中出现不可恢复错误时
        ConfigError: 当配置参数无效时
        
    Example:
        >>> config = TrainingConfig(epochs=100, lr=0.001)
        >>> model = EfficientZeroModel()
        >>> result = efficient_zero_train(config, model, data_loader)
    """
    pass
```

### 错误处理规范

#### 异常层次结构
```python
class CardGameAIError(Exception):
    """项目基础异常类"""
    pass

class TrainingError(CardGameAIError):
    """训练相关错误"""
    pass

class ModelError(CardGameAIError):
    """模型相关错误"""
    pass

class ConfigError(CardGameAIError):
    """配置相关错误"""
    pass
```

#### 错误处理模式
```python
import logging
from typing import Optional

logger = logging.getLogger(__name__)

def safe_operation(data: Any) -> Optional[Result]:
    """安全操作示例"""
    try:
        result = risky_operation(data)
        return result
    except SpecificError as e:
        logger.error(f"特定错误: {e}", exc_info=True)
        return None
    except Exception as e:
        logger.critical(f"未预期错误: {e}", exc_info=True)
        raise TrainingError(f"操作失败: {e}") from e
```

## 测试策略

### 测试类型和覆盖率

#### 单元测试
- **位置**: `tests/unit/`
- **命名**: `test_*.py`
- **覆盖率目标**: 80%+
- **工具**: pytest, pytest-cov

```python
import pytest
from unittest.mock import Mock, patch
from cardgame_ai.algorithms.efficient_zero import EfficientZero

class TestEfficientZero:
    """EfficientZero算法单元测试"""
    
    def setup_method(self):
        """测试前置设置"""
        self.config = Mock()
        self.model = EfficientZero(self.config)
    
    def test_initialization(self):
        """测试初始化"""
        assert self.model.config == self.config
        assert self.model.is_initialized
    
    @patch('cardgame_ai.algorithms.efficient_zero.torch')
    def test_forward_pass(self, mock_torch):
        """测试前向传播"""
        mock_input = Mock()
        result = self.model.forward(mock_input)
        assert result is not None
        mock_torch.assert_called()
```

#### 集成测试
- **位置**: `tests/integration/`
- **范围**: 组件间交互
- **环境**: 测试数据库/容器

```python
import pytest
from cardgame_ai.training.distributed import DistributedTrainer
from cardgame_ai.algorithms.efficient_zero import EfficientZero

class TestTrainingPipeline:
    """训练流水线集成测试"""
    
    @pytest.fixture
    def trainer(self):
        """训练器fixture"""
        config = self.load_test_config()
        return DistributedTrainer(config)
    
    def test_full_training_cycle(self, trainer):
        """测试完整训练周期"""
        # 准备测试数据
        test_data = self.generate_test_data()
        
        # 执行训练
        result = trainer.train(test_data, epochs=1)
        
        # 验证结果
        assert result.success
        assert result.metrics['loss'] < 1.0
```

#### 性能测试
- **位置**: `tests/performance/`
- **指标**: 训练速度、推理延迟、内存使用

```python
import time
import pytest
from cardgame_ai.algorithms.efficient_zero import EfficientZero

class TestPerformance:
    """性能测试"""
    
    def test_inference_speed(self):
        """测试推理速度"""
        model = EfficientZero.load_pretrained()
        test_input = self.generate_test_input()
        
        start_time = time.time()
        for _ in range(100):
            result = model.predict(test_input)
        end_time = time.time()
        
        avg_time = (end_time - start_time) / 100
        assert avg_time < 0.01  # 小于10ms
```

### 测试配置

#### pytest配置 (pyproject.toml)
```toml
[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--cov=cardgame_ai",
    "--cov-report=html",
    "--cov-report=term-missing",
    "--cov-fail-under=80",
    "--strict-markers",
    "--disable-warnings"
]
markers = [
    "slow: marks tests as slow",
    "integration: marks tests as integration tests",
    "performance: marks tests as performance tests"
]
```

## 日志记录规范

### 日志配置
```python
import logging
import structlog
from pathlib import Path

def setup_logging(log_level: str = "INFO", log_dir: str = "logs"):
    """设置结构化日志"""
    log_path = Path(log_dir)
    log_path.mkdir(exist_ok=True)
    
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
```

### 日志使用示例
```python
import structlog

logger = structlog.get_logger(__name__)

def train_epoch(epoch: int, model: Model, data_loader: DataLoader):
    """训练一个epoch"""
    logger.info(
        "开始训练epoch",
        epoch=epoch,
        model_type=type(model).__name__,
        batch_size=data_loader.batch_size
    )
    
    try:
        for batch_idx, batch in enumerate(data_loader):
            loss = model.train_step(batch)
            
            if batch_idx % 100 == 0:
                logger.debug(
                    "训练进度",
                    epoch=epoch,
                    batch=batch_idx,
                    loss=float(loss),
                    gpu_memory=get_gpu_memory_usage()
                )
                
    except Exception as e:
        logger.error(
            "训练失败",
            epoch=epoch,
            error=str(e),
            exc_info=True
        )
        raise
```

## 安全最佳实践

### 输入验证
```python
from pydantic import BaseModel, validator
from typing import List, Optional

class TrainingConfig(BaseModel):
    """训练配置验证"""
    epochs: int
    learning_rate: float
    batch_size: int
    model_path: Optional[str] = None
    
    @validator('epochs')
    def validate_epochs(cls, v):
        if v <= 0:
            raise ValueError('epochs必须大于0')
        if v > 10000:
            raise ValueError('epochs不能超过10000')
        return v
    
    @validator('learning_rate')
    def validate_learning_rate(cls, v):
        if not 0 < v < 1:
            raise ValueError('learning_rate必须在0和1之间')
        return v
```

### 密钥管理
```python
import os
from pathlib import Path
from typing import Optional

class SecretManager:
    """密钥管理器"""
    
    @staticmethod
    def get_secret(key: str) -> Optional[str]:
        """安全获取密钥"""
        # 优先从环境变量获取
        value = os.getenv(key)
        if value:
            return value
            
        # 从安全文件获取
        secret_file = Path(f"/etc/secrets/{key}")
        if secret_file.exists():
            return secret_file.read_text().strip()
            
        return None
    
    @staticmethod
    def validate_api_key(api_key: str) -> bool:
        """验证API密钥格式"""
        if not api_key:
            return False
        if len(api_key) < 32:
            return False
        # 添加其他验证逻辑
        return True
```

### 资源管理
```python
import contextlib
import torch
from typing import Generator

@contextlib.contextmanager
def gpu_memory_manager() -> Generator[None, None, None]:
    """GPU内存管理上下文"""
    try:
        # 清理GPU缓存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        yield
    finally:
        # 确保释放GPU内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

# 使用示例
def train_with_memory_management():
    with gpu_memory_manager():
        model = load_large_model()
        train_model(model)
```

## 配置管理

### Hydra配置示例
```yaml
# configs/base.yaml
defaults:
  - training: efficient_zero
  - evaluation: benchmark
  - _self_

project:
  name: "doudizhu_ai_optimization"
  version: "1.0.0"
  
logging:
  level: INFO
  dir: logs
  
device:
  type: cuda
  ids: [0, 1, 2, 3]
```

### 配置加载
```python
import hydra
from omegaconf import DictConfig, OmegaConf

@hydra.main(version_base=None, config_path="configs", config_name="base")
def main(cfg: DictConfig) -> None:
    """主函数"""
    # 验证配置
    OmegaConf.set_struct(cfg, True)
    
    # 设置日志
    setup_logging(cfg.logging.level, cfg.logging.dir)
    
    # 初始化训练
    trainer = create_trainer(cfg)
    trainer.train()

if __name__ == "__main__":
    main()
```
