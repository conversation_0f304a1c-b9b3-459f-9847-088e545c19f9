"""
基础数据结构模块

定义框架的基础数据结构，包括状态、动作和经验等。
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Tuple, Optional, Union
import numpy as np


class State(ABC):
    """
    状态基类
    
    表示游戏的状态，包括游戏中的所有信息。
    """
    
    @abstractmethod
    def to_dict(self) -> Dict[str, Any]:
        """
        将状态转换为字典表示
        
        Returns:
            Dict[str, Any]: 状态的字典表示
        """
        pass
    
    @classmethod
    @abstractmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'State':
        """
        从字典创建状态
        
        Args:
            data (Dict[str, Any]): 状态的字典表示
            
        Returns:
            State: 创建的状态对象
        """
        pass
    
    @abstractmethod
    def get_player_id(self) -> int:
        """
        获取当前玩家ID
        
        Returns:
            int: 当前玩家ID
        """
        pass


class Action(ABC):
    """
    动作基类
    
    表示游戏中的动作，如出牌、选择等。
    """
    
    @abstractmethod
    def to_dict(self) -> Dict[str, Any]:
        """
        将动作转换为字典表示
        
        Returns:
            Dict[str, Any]: 动作的字典表示
        """
        pass
    
    @classmethod
    @abstractmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Action':
        """
        从字典创建动作
        
        Args:
            data (Dict[str, Any]): 动作的字典表示
            
        Returns:
            Action: 创建的动作对象
        """
        pass
    
    @abstractmethod
    def is_valid(self, state: State) -> bool:
        """
        检查动作在给定状态下是否有效
        
        Args:
            state (State): 游戏状态
            
        Returns:
            bool: 动作是否有效
        """
        pass


class Game(ABC):
    """
    游戏基类
    
    定义游戏的核心接口，包括状态转换、动作执行等。
    """
    
    @abstractmethod
    def reset(self, **kwargs) -> State:
        """
        重置游戏状态
        
        Args:
            **kwargs: 重置参数
            
        Returns:
            State: 初始状态
        """
        pass
    
    @abstractmethod
    def step(self, action: Action) -> Tuple[State, float, bool, Dict[str, Any]]:
        """
        执行一步动作
        
        Args:
            action (Action): 要执行的动作
            
        Returns:
            Tuple[State, float, bool, Dict[str, Any]]: 
                新状态、奖励、是否结束、额外信息
        """
        pass
    
    @abstractmethod
    def get_legal_actions(self, player_id: Optional[int] = None) -> List[Action]:
        """
        获取当前状态下的合法动作列表
        
        Args:
            player_id (Optional[int]): 玩家ID，默认为当前玩家
            
        Returns:
            List[Action]: 合法动作列表
        """
        pass
    
    @abstractmethod
    def get_observation(self, player_id: int) -> Dict[str, Any]:
        """
        获取指定玩家的观察
        
        Args:
            player_id (int): 玩家ID
            
        Returns:
            Dict[str, Any]: 玩家的观察
        """
        pass
    
    def is_terminal(self) -> bool:
        """
        检查当前状态是否为终止状态
        
        Returns:
            bool: 是否为终止状态
        """
        # 默认实现，子类可以覆盖
        _, _, done, _ = self.step(None)
        return done


class Experience:
    """
    经验类
    
    表示强化学习中的经验，包括状态、动作、奖励、下一状态等。
    """
    
    def __init__(
        self,
        state: State,
        action: Action,
        reward: float,
        next_state: State,
        done: bool,
        info: Optional[Dict[str, Any]] = None
    ):
        """
        初始化经验对象
        
        Args:
            state (State): 当前状态
            action (Action): 执行的动作
            reward (float): 获得的奖励
            next_state (State): 下一状态
            done (bool): 是否结束
            info (Optional[Dict[str, Any]], optional): 额外信息. Defaults to None.
        """
        self.state = state
        self.action = action
        self.reward = reward
        self.next_state = next_state
        self.done = done
        self.info = info if info is not None else {}
    
    def to_dict(self) -> Dict[str, Any]:
        """
        将经验转换为字典表示
        
        Returns:
            Dict[str, Any]: 经验的字典表示
        """
        return {
            'state': self.state.to_dict(),
            'action': self.action.to_dict(),
            'reward': self.reward,
            'next_state': self.next_state.to_dict(),
            'done': self.done,
            'info': self.info
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any], state_cls, action_cls) -> 'Experience':
        """
        从字典创建经验
        
        Args:
            data (Dict[str, Any]): 经验的字典表示
            state_cls: 状态类
            action_cls: 动作类
            
        Returns:
            Experience: 创建的经验对象
        """
        return cls(
            state=state_cls.from_dict(data['state']),
            action=action_cls.from_dict(data['action']),
            reward=data['reward'],
            next_state=state_cls.from_dict(data['next_state']),
            done=data['done'],
            info=data.get('info', {})
        )


class Batch:
    """
    批次类
    
    表示一批经验数据，用于批量训练。
    """
    
    def __init__(self, experiences: List[Experience]):
        """
        初始化批次对象
        
        Args:
            experiences (List[Experience]): 经验列表
        """
        self.experiences = experiences
        
    def __len__(self) -> int:
        """
        获取批次大小
        
        Returns:
            int: 批次大小
        """
        return len(self.experiences)
    
    def __getitem__(self, idx: int) -> Experience:
        """
        获取指定索引的经验
        
        Args:
            idx (int): 索引
            
        Returns:
            Experience: 经验对象
        """
        return self.experiences[idx]
    
    def to_arrays(self) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """
        将批次转换为NumPy数组
        
        Returns:
            Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray]: 
                状态数组、动作数组、奖励数组、下一状态数组、结束标志数组
        """
        # 这里假设状态和动作可以转换为NumPy数组
        # 实际实现可能需要根据具体的状态和动作类型进行调整
        states = np.array([exp.state for exp in self.experiences])
        actions = np.array([exp.action for exp in self.experiences])
        rewards = np.array([exp.reward for exp in self.experiences])
        next_states = np.array([exp.next_state for exp in self.experiences])
        dones = np.array([exp.done for exp in self.experiences])
        
        return states, actions, rewards, next_states, dones


class Space:
    """
    空间类，模拟OpenAI Gym的spaces
    """
    def __init__(self, shape=None, n=None):
        """
        初始化空间
        
        Args:
            shape (Tuple[int, ...]): 观察空间的形状
            n (int): 动作空间的大小
        """
        self.shape = shape
        self.n = n
    
    def sample(self):
        """
        随机采样一个值
        
        Returns:
            int 或 ndarray: 根据空间类型返回随机值
        """
        if self.n is not None:
            # 如果定义了n，表示离散动作空间，返回一个随机整数
            return np.random.randint(0, self.n)
        elif self.shape is not None:
            # 如果定义了shape，表示连续观察空间，返回随机数组
            return np.random.random(self.shape)
        else:
            # 默认情况
            return 0
