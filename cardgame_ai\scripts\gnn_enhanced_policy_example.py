#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
GNN增强版价值策略网络示例脚本

展示如何使用GNN增强版价值策略网络进行训练和推理。
"""

import os
import sys
import argparse
import logging
import numpy as np
import torch
from typing import Dict, List, Any, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from cardgame_ai.models.enhanced_value_policy_net import GNNEnhancedValuePolicyNet
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.games.doudizhu.state import DouDizhuState
from cardgame_ai.games.common.belief_state import BeliefState

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='GNN增强版价值策略网络示例')

    parser.add_argument('--state_dim', type=int, default=512,
                        help='状态维度')
    parser.add_argument('--belief_dim', type=int, default=54,
                        help='信念状态维度')
    parser.add_argument('--action_dim', type=int, default=309,
                        help='动作维度')
    parser.add_argument('--node_feature_dim', type=int, default=19,
                        help='节点特征维度')
    parser.add_argument('--gnn_hidden_dim', type=int, default=128,
                        help='GNN隐藏层维度')
    parser.add_argument('--gnn_output_dim', type=int, default=64,
                        help='GNN输出维度')
    parser.add_argument('--gnn_type', type=str, default='gcn',
                        help='GNN类型，可选值为gcn, gat, sage')
    parser.add_argument('--gnn_layers', type=int, default=2,
                        help='GNN层数')
    parser.add_argument('--use_belief_state', action='store_true',
                        help='是否使用信念状态')
    parser.add_argument('--seed', type=int, default=42,
                        help='随机种子')

    return parser.parse_args()


def create_dummy_state(state_dim: int) -> torch.Tensor:
    """创建虚拟状态张量"""
    return torch.randn(1, state_dim)


def create_dummy_belief_state(belief_dim: int) -> BeliefState:
    """创建虚拟信念状态"""
    # 创建随机概率分布
    card_probabilities = {}
    for i in range(belief_dim):
        card_probabilities[f'card_{i}'] = np.random.random()

    # 归一化概率
    total = sum(card_probabilities.values())
    for card in card_probabilities:
        card_probabilities[card] /= total

    # 创建信念状态
    belief_state = BeliefState(player_id=0)
    belief_state.card_probabilities = card_probabilities

    return belief_state


def main():
    """主函数"""
    args = parse_args()

    # 设置随机种子
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)

    # 创建环境
    env = DouDizhuEnvironment()

    # 创建GNN增强版价值策略网络
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = GNNEnhancedValuePolicyNet(
        state_dim=args.state_dim,
        belief_dim=args.belief_dim,
        action_dim=args.action_dim,
        node_feature_dim=args.node_feature_dim,
        gnn_hidden_dim=args.gnn_hidden_dim,
        gnn_output_dim=args.gnn_output_dim,
        gnn_type=args.gnn_type,
        gnn_layers=args.gnn_layers,
        use_belief_state=args.use_belief_state
    ).to(device)

    logger.info(f"创建GNN增强版价值策略网络: {model.__class__.__name__}")
    logger.info(f"模型参数: state_dim={args.state_dim}, belief_dim={args.belief_dim}, action_dim={args.action_dim}")
    logger.info(f"GNN参数: node_feature_dim={args.node_feature_dim}, gnn_hidden_dim={args.gnn_hidden_dim}, gnn_output_dim={args.gnn_output_dim}")
    logger.info(f"GNN类型: {args.gnn_type}, GNN层数: {args.gnn_layers}")
    logger.info(f"使用信念状态: {args.use_belief_state}")

    # 重置环境
    state = env.reset()

    # 创建虚拟状态和信念状态
    dummy_state = create_dummy_state(args.state_dim).to(device)
    dummy_belief = create_dummy_belief_state(args.belief_dim)

    # 前向传播（不使用解释）
    logger.info("执行前向传播（不使用解释）...")
    policy_logits, value = model(
        state_input=dummy_state,
        game_state=state,
        player_id=0,
        belief_input=dummy_belief if args.use_belief_state else None
    )

    logger.info(f"策略输出形状: {policy_logits.shape}")
    logger.info(f"价值输出形状: {value.shape}")

    # 前向传播（使用解释）
    logger.info("执行前向传播（使用解释）...")
    policy_logits, value, explanation = model(
        state_input=dummy_state,
        game_state=state,
        player_id=0,
        belief_input=dummy_belief if args.use_belief_state else None,
        explain=True
    )

    logger.info(f"策略输出形状: {policy_logits.shape}")
    logger.info(f"价值输出形状: {value.shape}")

    # 打印解释数据
    logger.info("解释数据:")
    for key, value in explanation.items():
        if isinstance(value, dict):
            logger.info(f"  {key}:")
            for subkey, subvalue in value.items():
                logger.info(f"    {subkey}: {subvalue}")
        else:
            logger.info(f"  {key}: {value}")

    # 计算模型参数数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)

    logger.info(f"模型总参数数量: {total_params:,}")
    logger.info(f"可训练参数数量: {trainable_params:,}")

    return 0


if __name__ == "__main__":
    main()
