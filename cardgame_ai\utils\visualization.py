"""
可视化工具模块

提供各种可视化功能，用于展示训练和评估结果。
"""
import os
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Any, Optional


def plot_comparison(data: Dict[str, float], title: str = "Comparison", 
                   xlabel: str = "Models", ylabel: str = "Value", 
                   save_path: Optional[str] = None):
    """
    绘制对比图
    
    Args:
        data: 数据字典，键为模型名称，值为对应的指标值
        title: 图表标题
        xlabel: x轴标签
        ylabel: y轴标签
        save_path: 保存路径，如果为None则显示图表
    """
    # 设置样式
    sns.set_style("whitegrid")
    plt.figure(figsize=(10, 6))
    
    # 绘制条形图
    bars = plt.bar(data.keys(), data.values(), color=sns.color_palette("muted"))
    
    # 添加数值标签
    for bar in bars:
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{height:.4f}', ha='center', va='bottom')
    
    # 设置标题和标签
    plt.title(title, fontsize=16)
    plt.xlabel(xlabel, fontsize=12)
    plt.ylabel(ylabel, fontsize=12)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存或显示
    if save_path:
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
    else:
        plt.show()


def plot_training_history(history: Dict[str, List[float]], title: str = "Training History",
                         xlabel: str = "Episodes", save_path: Optional[str] = None):
    """
    绘制训练历史
    
    Args:
        history: 历史数据字典，键为指标名称，值为对应的历史数据列表
        title: 图表标题
        xlabel: x轴标签
        save_path: 保存路径，如果为None则显示图表
    """
    # 设置样式
    sns.set_style("whitegrid")
    plt.figure(figsize=(12, 8))
    
    # 绘制每个指标
    for metric, values in history.items():
        plt.plot(values, label=metric)
    
    # 设置标题和标签
    plt.title(title, fontsize=16)
    plt.xlabel(xlabel, fontsize=12)
    plt.legend(fontsize=12)
    plt.grid(True)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存或显示
    if save_path:
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
    else:
        plt.show()
