"""
动态计算预算分配模块

在识别出的关键决策点自动分配10-100倍的计算资源，进行更深入的搜索和推理。
"""

import logging
import numpy as np
from typing import Dict, List, Any, Optional, Union, Tuple

# 更新导入路径，使用组件目录下的 KeyMomentDetector
from cardgame_ai.algorithms.components.key_moment_detector import KeyMomentDetector
from cardgame_ai.core.base import State

# 配置日志
logger = logging.getLogger(__name__)


class DynamicBudgetAllocator:
    """
    动态计算预算分配器
    
    在识别出的关键决策点自动分配10-100倍的计算资源，进行更深入的搜索和推理。
    """
    
    def __init__(
        self,
        key_moment_detector: KeyMomentDetector,
        base_budget: int = 50,
        max_budget: int = 5000,
        amplification_factor: int = 10,
        min_amplification: float = 1.0,
        max_amplification: float = 100.0,
        critical_threshold: float = 0.5,
        adaptive_scaling: bool = True,
        resource_constraint: Optional[Dict[str, Any]] = None
    ):
        """
        初始化动态计算预算分配器
        
        Args:
            key_moment_detector: 关键决策点检测器
            base_budget: 基础计算预算（如MCTS模拟次数）
            max_budget: 最大计算预算
            amplification_factor: 放大因子，决定关键决策点的预算增加倍数
            min_amplification: 最小放大倍数
            max_amplification: 最大放大倍数
            critical_threshold: 关键决策点阈值，分数高于此值被视为关键决策点
            adaptive_scaling: 是否使用自适应缩放（根据关键程度动态调整放大倍数）
            resource_constraint: 资源约束，如{"max_time_ms": 1000}
        """
        self.key_moment_detector = key_moment_detector
        self.base_budget = base_budget
        self.max_budget = max_budget
        self.amplification_factor = amplification_factor
        self.min_amplification = min_amplification
        self.max_amplification = max_amplification
        self.critical_threshold = critical_threshold
        self.adaptive_scaling = adaptive_scaling
        self.resource_constraint = resource_constraint or {}
        
        # 统计信息
        self.stats = {
            "total_allocations": 0,
            "critical_allocations": 0,
            "avg_budget": base_budget,
            "max_budget_used": base_budget,
            "total_budget_used": 0,
            "critical_ratio": 0.0
        }
        
        logger.info(f"初始化动态计算预算分配器: base_budget={base_budget}, max_budget={max_budget}, amplification_factor={amplification_factor}")
    
    def allocate_budget(
        self,
        state: Union[State, np.ndarray],
        state_history: Optional[List[Union[State, np.ndarray]]] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        为当前状态分配计算预算
        
        Args:
            state: 当前状态
            state_history: 历史状态列表，可选
            context: 上下文信息，可选
            
        Returns:
            Dict[str, Any]: 计算预算配置，包括模拟次数、时间限制等
        """
        # 检测是否为关键决策点
        is_key_moment = self.key_moment_detector.is_key_moment(state)
        
        # 如果支持解释模式，尝试获取详细信息
        try:
            result, explanation = self.key_moment_detector.decide(state, [], explain=True)
            score = explanation.get("importance_score", 0.5 if is_key_moment else 0.0)
        except:
            # 如果不支持解释模式，使用默认分数
            score = 0.5 if is_key_moment else 0.0
        
        # 更新统计信息
        self.stats["total_allocations"] += 1
        
        # 初始化预算配置
        budget_config = {
            "num_simulations": self.base_budget,
            "is_critical": is_key_moment,
            "criticality_score": score,
            "max_time_ms": self.resource_constraint.get("max_time_ms", None)
        }
        
        if is_key_moment:
            # 更新关键决策点统计
            self.stats["critical_allocations"] += 1
            self.stats["critical_ratio"] = self.stats["critical_allocations"] / self.stats["total_allocations"]
            
            # 计算放大倍数
            if self.adaptive_scaling:
                # 根据关键程度动态调整放大倍数
                # score范围为[0, 1]，越接近1表示越关键
                amplification = self.min_amplification + (self.max_amplification - self.min_amplification) * score
            else:
                # 使用固定放大倍数
                amplification = self.amplification_factor
            
            # 计算预算
            budget = int(self.base_budget * amplification)
            
            # 确保不超过最大预算
            budget = min(budget, self.max_budget)
            
            # 更新预算配置
            budget_config["num_simulations"] = budget
            
            # 更新统计信息
            self.stats["total_budget_used"] += budget
            self.stats["avg_budget"] = self.stats["total_budget_used"] / self.stats["total_allocations"]
            self.stats["max_budget_used"] = max(self.stats["max_budget_used"], budget)
            
            logger.info(f"检测到关键决策点! 关键程度: {score:.4f}, 分配计算预算: {budget}")
        else:
            # 非关键决策点使用基础预算
            self.stats["total_budget_used"] += self.base_budget
            self.stats["avg_budget"] = self.stats["total_budget_used"] / self.stats["total_allocations"]
        
        return budget_config
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return self.stats.copy()
    
    def reset_stats(self) -> None:
        """
        重置统计信息
        """
        self.stats = {
            "total_allocations": 0,
            "critical_allocations": 0,
            "avg_budget": self.base_budget,
            "max_budget_used": self.base_budget,
            "total_budget_used": 0,
            "critical_ratio": 0.0
        }
        
        logger.info("重置动态计算预算分配器统计信息")
