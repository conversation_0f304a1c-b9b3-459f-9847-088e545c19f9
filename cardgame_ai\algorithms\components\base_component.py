"""
决策组件基类模块

定义所有决策组件的基类，提供统一的接口和功能。
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Union
import time
import logging
import random

from cardgame_ai.core.base import State, Action


class DecisionComponent(ABC):
    """
    决策组件基类

    所有决策组件的抽象基类，定义了统一的接口。
    """

    def __init__(self, name: str):
        """
        初始化决策组件

        Args:
            name: 组件名称
        """
        self.name = name
        self.stats = {
            "calls": 0,
            "time_spent": 0.0,
            "success_rate": 0.0
        }

    @abstractmethod
    def decide(self, state: State, legal_actions: List[Action], explain: bool = False) -> Union[Action, tuple]:
        """
        做出决策

        Args:
            state: 当前状态
            legal_actions: 合法动作列表
            explain: 是否启用解释模式

        Returns:
            如果explain=False，返回选择的动作
            如果explain=True，返回(选择的动作, 解释数据)元组
        """
        pass

    def update_stats(self, time_spent: float, success: bool = True):
        """
        更新统计信息

        Args:
            time_spent: 决策耗时
            success: 是否成功
        """
        self.stats["calls"] += 1
        self.stats["time_spent"] += time_spent

        # 更新成功率
        if "successes" not in self.stats:
            self.stats["successes"] = 0

        if success:
            self.stats["successes"] += 1

        self.stats["success_rate"] = self.stats["successes"] / self.stats["calls"]

    def get_explanation_data(self) -> Dict[str, Any]:
        """
        获取基础解释数据

        Returns:
            包含基础解释数据的字典
        """
        return {
            "component_name": self.name,
            "component_stats": {
                "calls": self.stats["calls"],
                "avg_time": self.stats["time_spent"] / max(1, self.stats["calls"]),
                "success_rate": self.stats["success_rate"]
            }
        }
