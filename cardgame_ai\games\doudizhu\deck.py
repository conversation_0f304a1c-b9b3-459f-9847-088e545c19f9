"""
牌组模块

定义牌组的基本属性和方法。
"""
import random
from typing import List, Dict, Any, Optional, Tuple

from cardgame_ai.games.doudizhu.card import Card, CardSuit, CardRank


class Deck:
    """
    牌组类
    
    表示一副扑克牌，包含54张牌（52张普通牌和2张王牌）。
    """
    
    def __init__(self, seed: Optional[int] = None):
        """
        初始化牌组
        
        Args:
            seed (Optional[int], optional): 随机种子. Defaults to None.
        """
        self.rng = random.Random(seed)
        self.cards = self._create_cards()
    
    def _create_cards(self) -> List[Card]:
        """
        创建一副完整的扑克牌
        
        Returns:
            List[Card]: 扑克牌列表
        """
        cards = []
        
        # 创建普通牌（3-A, 2）
        for rank in range(CardRank.THREE, CardRank.TWO + 1):
            for suit in range(CardSuit.CLUB, CardSuit.SPADE + 1):
                cards.append(Card(CardRank(rank), CardSuit(suit)))
        
        # 创建王牌
        cards.append(Card(CardRank.SMALL_JOKER))
        cards.append(Card(CardRank.BIG_JOKER))
        
        return cards
    
    def shuffle(self) -> None:
        """
        洗牌
        """
        self.rng.shuffle(self.cards)
    
    def deal(self, num_players: int = 3) -> Tuple[List[List[Card]], List[Card]]:
        """
        发牌
        
        按照斗地主规则，将牌发给指定数量的玩家，并留下底牌。
        
        Args:
            num_players (int, optional): 玩家数量. Defaults to 3.
            
        Returns:
            Tuple[List[List[Card]], List[Card]]: 玩家手牌和底牌
        """
        if num_players <= 0:
            raise ValueError("玩家数量必须大于0")
        
        # 洗牌
        self.shuffle()
        
        # 复制牌组
        cards = self.cards.copy()
        
        # 计算发牌数量，除去3张底牌
        total_player_cards = len(cards) - 3
        
        # 计算基本每个玩家的牌数和余数
        base_cards_per_player = total_player_cards // num_players
        remainder = total_player_cards % num_players
        
        # 发牌
        hands = [[] for _ in range(num_players)]
        card_index = 0
        
        # 先分配基本牌数
        for i in range(num_players):
            for _ in range(base_cards_per_player):
                hands[i].append(cards[card_index])
                card_index += 1
        
        # 处理余数，从索引1开始的玩家分配额外的牌
        for i in range(1, remainder + 1):
            hands[i % num_players].append(cards[card_index])
            card_index += 1
        
        # 底牌
        landlord_cards = cards[card_index:]
        
        # 排序手牌
        for hand in hands:
            hand.sort()
        
        return hands, landlord_cards
    
    def __len__(self) -> int:
        """
        获取牌组大小
        
        Returns:
            int: 牌组大小
        """
        return len(self.cards)
    
    def __str__(self) -> str:
        """
        转换为字符串表示
        
        Returns:
            str: 字符串表示
        """
        return f"Deck({len(self.cards)} cards)"
    
    def __repr__(self) -> str:
        """
        转换为详细字符串表示
        
        Returns:
            str: 详细字符串表示
        """
        return f"Deck(cards={self.cards})"
