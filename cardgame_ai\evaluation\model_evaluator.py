#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
模型评估器

提供完整的斗地主AI模型测试和评估功能，包括：
- 模型加载和验证
- 对战测试（AI vs AI, AI vs Random）
- 性能指标统计
- 可视化分析
- 人机对战接口

使用方法:
    基础评估:
    python model_evaluator.py --model models/muzero_doudizhu/muzero_epoch_2/model.pt

    详细评估:
    python model_evaluator.py --model models/muzero_doudizhu/muzero_epoch_2/model.pt --games 100 --detailed

    人机对战:
    python model_evaluator.py --model models/muzero_doudizhu/muzero_epoch_2/model.pt --human-play
"""

import os
import sys
import time
import json
import logging
import argparse
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict, Counter
from datetime import datetime

# 添加项目根目录到路径
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(script_dir, '../..'))
sys.path.insert(0, project_root)


class ModelEvaluator:
    """模型评估器"""

    def __init__(self, model_path: str, device: str = "auto"):
        """
        初始化评估器

        Args:
            model_path: 模型文件路径
            device: 计算设备
        """
        self.model_path = model_path
        self.device = self._detect_device(device)
        self.model = None
        self.game_env = None

        # 评估统计
        self.evaluation_stats = {
            'total_games': 0,
            'wins': {'P0': 0, 'P1': 0, 'P2': 0},
            'landlord_wins': 0,
            'farmer_wins': 0,
            'avg_game_length': 0.0,
            'avg_decision_time': 0.0,
            'action_distribution': defaultdict(int),
            'phase_statistics': defaultdict(dict),
            'error_count': 0
        }

        self._setup_logging()

    def _detect_device(self, device: str) -> str:
        """检测可用设备"""
        if device == "auto":
            try:
                import torch
                if torch.cuda.is_available():
                    return "cuda:0"
                else:
                    return "cpu"
            except ImportError:
                return "cpu"
        return device

    def _setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(f'logs/evaluation_{int(time.time())}.log')
            ]
        )
        self.logger = logging.getLogger(__name__)

    def load_model(self) -> bool:
        """
        加载模型

        Returns:
            bool: 加载是否成功
        """
        try:
            self.logger.info(f"加载模型: {self.model_path}")

            if not os.path.exists(self.model_path):
                self.logger.error(f"模型文件不存在: {self.model_path}")
                return False

            # 根据模型类型选择加载方法
            if "muzero" in self.model_path.lower() or "efficient_zero" in self.model_path.lower():
                from cardgame_ai.algorithms.efficient_zero import EfficientZero

                # 创建模型配置
                model_config = {
                    'state_shape': (1, 54),  # 斗地主状态空间
                    'action_shape': (1,),    # 动作空间
                    'device': self.device
                }

                self.model = EfficientZero(**model_config)
                self.model.load(self.model_path)

            elif "dqn" in self.model_path.lower():
                from cardgame_ai.algorithms.dqn import DQN
                self.model = DQN(device=self.device)
                self.model.load(self.model_path)

            else:
                self.logger.warning("未知模型类型，尝试通用加载")
                import torch
                self.model = torch.load(self.model_path, map_location=self.device)

            self.logger.info(f"模型加载成功，设备: {self.device}")
            return True

        except Exception as e:
            self.logger.error(f"模型加载失败: {e}")
            return False

    def setup_environment(self) -> bool:
        """
        设置游戏环境

        Returns:
            bool: 设置是否成功
        """
        try:
            from cardgame_ai.games.doudizhu import DouDizhuEnvironment
            self.game_env = DouDizhuEnvironment()
            self.logger.info("游戏环境设置成功")
            return True
        except Exception as e:
            self.logger.error(f"游戏环境设置失败: {e}")
            return False

    def evaluate_vs_random(self, num_games: int = 50) -> Dict[str, Any]:
        """
        与随机策略对战评估

        Args:
            num_games: 游戏局数

        Returns:
            Dict[str, Any]: 评估结果
        """
        self.logger.info(f"开始与随机策略对战评估 ({num_games}局)")

        results = {
            'total_games': num_games,
            'ai_wins': 0,
            'random_wins': 0,
            'ai_as_landlord_wins': 0,
            'ai_as_farmer_wins': 0,
            'game_lengths': [],
            'decision_times': []
        }

        for game_idx in range(num_games):
            try:
                game_result = self._play_vs_random_game()

                # 更新统计
                results['game_lengths'].append(game_result['game_length'])
                results['decision_times'].extend(game_result['decision_times'])

                if game_result['ai_won']:
                    results['ai_wins'] += 1
                    if game_result['ai_role'] == 'landlord':
                        results['ai_as_landlord_wins'] += 1
                    else:
                        results['ai_as_farmer_wins'] += 1
                else:
                    results['random_wins'] += 1

                # 进度显示
                if (game_idx + 1) % 10 == 0:
                    win_rate = results['ai_wins'] / (game_idx + 1) * 100
                    self.logger.info(f"进度: {game_idx + 1}/{num_games}, AI胜率: {win_rate:.1f}%")

            except Exception as e:
                self.logger.error(f"游戏 {game_idx + 1} 执行失败: {e}")
                continue

        # 计算最终统计
        results['ai_win_rate'] = results['ai_wins'] / num_games * 100
        results['avg_game_length'] = np.mean(results['game_lengths'])
        results['avg_decision_time'] = np.mean(results['decision_times'])

        self.logger.info(f"评估完成 - AI胜率: {results['ai_win_rate']:.1f}%")
        return results

    def _play_vs_random_game(self) -> Dict[str, Any]:
        """
        执行一局与随机策略的对战

        Returns:
            Dict[str, Any]: 游戏结果
        """
        state = self.game_env.reset()
        done = False
        step_count = 0
        decision_times = []

        # 随机分配AI角色
        ai_player = np.random.randint(0, 3)

        while not done and step_count < 200:  # 防止无限循环
            current_player = state.current_player

            if current_player == ai_player:
                # AI决策
                start_time = time.time()
                state_array = self.game_env.get_observation(state)
                action_idx = self.model.act(state_array, temperature=0.1)[0]
                decision_time = time.time() - start_time
                decision_times.append(decision_time)
            else:
                # 随机决策
                legal_actions = self.game_env.get_legal_actions(state)
                action_idx = np.random.randint(len(legal_actions))

            # 执行动作
            legal_actions = self.game_env.get_legal_actions(state)
            action = legal_actions[action_idx]
            state, reward, done, info = self.game_env.step(action)
            step_count += 1

        # 判断AI是否获胜
        payoffs = state.get_payoffs()
        ai_won = payoffs[ai_player] > 0

        # 判断AI角色
        ai_role = 'landlord' if state.landlord == ai_player else 'farmer'

        return {
            'ai_won': ai_won,
            'ai_role': ai_role,
            'game_length': step_count,
            'decision_times': decision_times
        }

    def evaluate_self_play(self, num_games: int = 30) -> Dict[str, Any]:
        """
        自我对战评估

        Args:
            num_games: 游戏局数

        Returns:
            Dict[str, Any]: 评估结果
        """
        self.logger.info(f"开始自我对战评估 ({num_games}局)")

        results = {
            'total_games': num_games,
            'landlord_wins': 0,
            'farmer_wins': 0,
            'game_lengths': [],
            'decision_times': [],
            'action_distributions': defaultdict(int)
        }

        for game_idx in range(num_games):
            try:
                game_result = self._play_self_play_game()

                # 更新统计
                results['game_lengths'].append(game_result['game_length'])
                results['decision_times'].extend(game_result['decision_times'])

                if game_result['landlord_won']:
                    results['landlord_wins'] += 1
                else:
                    results['farmer_wins'] += 1

                # 更新动作分布
                for action_type, count in game_result['action_distribution'].items():
                    results['action_distributions'][action_type] += count

                # 进度显示
                if (game_idx + 1) % 10 == 0:
                    landlord_rate = results['landlord_wins'] / (game_idx + 1) * 100
                    self.logger.info(f"进度: {game_idx + 1}/{num_games}, 地主胜率: {landlord_rate:.1f}%")

            except Exception as e:
                self.logger.error(f"游戏 {game_idx + 1} 执行失败: {e}")
                continue

        # 计算最终统计
        results['landlord_win_rate'] = results['landlord_wins'] / num_games * 100
        results['farmer_win_rate'] = results['farmer_wins'] / num_games * 100
        results['avg_game_length'] = np.mean(results['game_lengths'])
        results['avg_decision_time'] = np.mean(results['decision_times'])

        self.logger.info(f"自我对战完成 - 地主胜率: {results['landlord_win_rate']:.1f}%")
        return results

    def _play_self_play_game(self) -> Dict[str, Any]:
        """
        执行一局自我对战

        Returns:
            Dict[str, Any]: 游戏结果
        """
        state = self.game_env.reset()
        done = False
        step_count = 0
        decision_times = []
        action_distribution = defaultdict(int)

        while not done and step_count < 200:
            current_player = state.current_player

            # AI决策
            start_time = time.time()
            state_array = self.game_env.get_observation(state)
            action_idx = self.model.act(state_array, temperature=0.3)[0]
            decision_time = time.time() - start_time
            decision_times.append(decision_time)

            # 执行动作
            legal_actions = self.game_env.get_legal_actions(state)
            action = legal_actions[action_idx]

            # 统计动作类型
            if hasattr(action, 'cards') and action.cards:
                action_distribution['play_cards'] += 1
            else:
                action_distribution['pass'] += 1

            state, reward, done, info = self.game_env.step(action)
            step_count += 1

        # 判断地主是否获胜
        payoffs = state.get_payoffs()
        landlord_won = payoffs[state.landlord] > 0 if state.landlord is not None else False

        return {
            'landlord_won': landlord_won,
            'game_length': step_count,
            'decision_times': decision_times,
            'action_distribution': action_distribution
        }

    def generate_report(self, results: Dict[str, Any], output_file: str = None) -> str:
        """
        生成评估报告

        Args:
            results: 评估结果
            output_file: 输出文件路径

        Returns:
            str: 报告内容
        """
        report = []
        report.append("=" * 60)
        report.append("🎮 斗地主AI模型评估报告")
        report.append("=" * 60)
        report.append(f"📅 评估时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"🤖 模型路径: {self.model_path}")
        report.append(f"💻 计算设备: {self.device}")
        report.append("")

        # 基础统计
        if 'ai_win_rate' in results:
            report.append("📊 与随机策略对战结果:")
            report.append(f"   总游戏数: {results['total_games']}")
            report.append(f"   AI胜率: {results['ai_win_rate']:.1f}%")
            report.append(f"   AI作为地主胜利: {results['ai_as_landlord_wins']}")
            report.append(f"   AI作为农民胜利: {results['ai_as_farmer_wins']}")
            report.append(f"   平均游戏长度: {results['avg_game_length']:.1f}步")
            report.append(f"   平均决策时间: {results['avg_decision_time']:.3f}秒")

        if 'landlord_win_rate' in results:
            report.append("")
            report.append("🎯 自我对战结果:")
            report.append(f"   总游戏数: {results['total_games']}")
            report.append(f"   地主胜率: {results['landlord_win_rate']:.1f}%")
            report.append(f"   农民胜率: {results['farmer_win_rate']:.1f}%")
            report.append(f"   平均游戏长度: {results['avg_game_length']:.1f}步")
            report.append(f"   平均决策时间: {results['avg_decision_time']:.3f}秒")

        report.append("")
        report.append("=" * 60)

        report_text = "\n".join(report)

        # 保存到文件
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_text)
            self.logger.info(f"报告已保存到: {output_file}")

        return report_text

    def interactive_play(self):
        """人机交互对战"""
        self.logger.info("启动人机交互对战模式")
        print("\n🎮 欢迎来到斗地主人机对战！")
        print("=" * 50)
        print("操作说明:")
        print("- 输入数字选择动作")
        print("- 输入 'q' 退出游戏")
        print("- 输入 'h' 查看帮助")
        print("=" * 50)

        while True:
            try:
                result = self._play_interactive_game()

                print(f"\n🎉 游戏结束！")
                if result['human_won']:
                    print("🏆 恭喜您获胜！")
                else:
                    print("🤖 AI获胜，继续努力！")

                play_again = input("\n是否再来一局？(y/n): ").lower()
                if play_again != 'y':
                    break

            except KeyboardInterrupt:
                print("\n👋 游戏结束，感谢游玩！")
                break
            except Exception as e:
                self.logger.error(f"交互游戏出错: {e}")
                break

    def _play_interactive_game(self) -> Dict[str, Any]:
        """执行一局人机交互游戏"""
        state = self.game_env.reset()
        done = False
        step_count = 0

        # 人类玩家固定为P0
        human_player = 0

        print(f"\n🎲 新游戏开始！您是玩家P{human_player}")

        while not done and step_count < 200:
            current_player = state.current_player

            if current_player == human_player:
                # 人类玩家回合
                action_idx = self._get_human_action(state)
                if action_idx == -1:  # 退出游戏
                    return {'human_won': False, 'game_length': step_count}
            else:
                # AI玩家回合
                print(f"\n🤖 AI玩家P{current_player}思考中...")
                state_array = self.game_env.get_observation(state)
                action_idx = self.model.act(state_array, temperature=0.1)[0]

            # 执行动作
            legal_actions = self.game_env.get_legal_actions(state)
            action = legal_actions[action_idx]

            # 显示动作
            if current_player != human_player:
                action_str = self._format_action(action)
                print(f"   P{current_player}的动作: {action_str}")

            state, reward, done, info = self.game_env.step(action)
            step_count += 1

            # 显示当前状态
            self._display_game_state(state, human_player)

        # 判断人类是否获胜
        payoffs = state.get_payoffs()
        human_won = payoffs[human_player] > 0

        return {'human_won': human_won, 'game_length': step_count}

    def _get_human_action(self, state) -> int:
        """获取人类玩家的动作选择"""
        legal_actions = self.game_env.get_legal_actions(state)

        print(f"\n🎯 轮到您行动了！")
        print("可选动作:")
        for i, action in enumerate(legal_actions):
            action_str = self._format_action(action)
            print(f"  {i}: {action_str}")

        while True:
            try:
                user_input = input("\n请选择动作编号 (或输入 'q' 退出): ").strip()

                if user_input.lower() == 'q':
                    return -1

                action_idx = int(user_input)
                if 0 <= action_idx < len(legal_actions):
                    return action_idx
                else:
                    print(f"❌ 无效选择，请输入0-{len(legal_actions)-1}之间的数字")

            except ValueError:
                print("❌ 请输入有效的数字")
            except KeyboardInterrupt:
                return -1

    def _format_action(self, action) -> str:
        """格式化动作显示"""
        if hasattr(action, 'cards') and action.cards:
            # 出牌动作
            cards_str = ', '.join([str(card) for card in action.cards])
            return f"出牌: {cards_str}"
        elif hasattr(action, 'name'):
            return action.name
        else:
            return "不出"

    def _display_game_state(self, state, human_player: int):
        """显示游戏状态"""
        if hasattr(state, 'hands') and state.hands:
            print(f"\n📋 您的手牌: {', '.join([str(card) for card in state.hands[human_player]])}")

        if hasattr(state, 'landlord') and state.landlord is not None:
            role = "地主" if state.landlord == human_player else "农民"
            print(f"🎭 您的角色: {role}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='斗地主AI模型评估器')

    parser.add_argument('--model', type=str, required=True,
                       help='模型文件路径')
    parser.add_argument('--device', type=str, default='auto',
                       help='计算设备 (auto/cpu/cuda:0)')
    parser.add_argument('--games', type=int, default=50,
                       help='评估游戏数量')
    parser.add_argument('--vs-random', action='store_true',
                       help='与随机策略对战')
    parser.add_argument('--self-play', action='store_true',
                       help='自我对战评估')
    parser.add_argument('--human-play', action='store_true',
                       help='人机交互对战')
    parser.add_argument('--output', type=str,
                       help='报告输出文件路径')
    parser.add_argument('--detailed', action='store_true',
                       help='详细评估模式')

    args = parser.parse_args()

    # 创建评估器
    evaluator = ModelEvaluator(args.model, args.device)

    # 加载模型
    if not evaluator.load_model():
        print("❌ 模型加载失败")
        return 1

    # 设置环境
    if not evaluator.setup_environment():
        print("❌ 环境设置失败")
        return 1

    print("✅ 模型和环境准备完成")

    # 执行评估
    if args.human_play:
        evaluator.interactive_play()
        return 0

    results = {}

    if args.vs_random or (not args.self_play and not args.human_play):
        print(f"\n🎯 开始与随机策略对战评估...")
        vs_random_results = evaluator.evaluate_vs_random(args.games)
        results.update(vs_random_results)

    if args.self_play:
        print(f"\n🤖 开始自我对战评估...")
        self_play_results = evaluator.evaluate_self_play(args.games)
        results.update(self_play_results)

    # 生成报告
    if results:
        output_file = args.output or f"evaluation_report_{int(time.time())}.txt"
        report = evaluator.generate_report(results, output_file)
        print("\n" + report)

    return 0


if __name__ == "__main__":
    sys.exit(main())
