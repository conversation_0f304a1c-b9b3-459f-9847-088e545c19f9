"""
混合决策系统模块

实现一个混合决策系统，结合人类决策和AI决策，根据信心评分动态调整介入程度。
"""

import os
import logging
import numpy as np
from typing import Dict, List, Any, Optional, Tuple, Union, Callable

from cardgame_ai.core.base import State, Action
from cardgame_ai.core.meta_controller import MetaController

# 配置日志
logger = logging.getLogger(__name__)


class HybridDecisionSystem:
    """
    混合决策系统
    
    结合人类决策和AI决策，根据信心评分动态调整介入程度。
    """
    
    def __init__(
        self,
        ai_agent: Any,
        initial_confidence: float = 0.8,
        learning_rate: float = 0.1,
        history_window: int = 10,
        intervention_threshold: float = 0.5,
        use_dynamic_intervention: bool = True
    ):
        """
        初始化混合决策系统
        
        Args:
            ai_agent: AI代理
            initial_confidence: 初始信心评分
            learning_rate: 学习率
            history_window: 历史记录窗口大小
            intervention_threshold: 介入阈值
            use_dynamic_intervention: 是否使用动态介入
        """
        self.ai_agent = ai_agent
        self.meta_controller = MetaController(
            initial_confidence=initial_confidence,
            learning_rate=learning_rate,
            history_window=history_window
        )
        self.intervention_threshold = intervention_threshold
        self.use_dynamic_intervention = use_dynamic_intervention
        
        # 如果AI代理有价值函数，设置给元控制器
        if hasattr(ai_agent, 'evaluate_action') and callable(getattr(ai_agent, 'evaluate_action')):
            self.meta_controller.set_value_function(ai_agent.evaluate_action)
        
        # 历史记录
        self.decision_history = []
        
    def decide(self, state: State, human_action: Optional[Action] = None) -> Action:
        """
        做出决策
        
        结合人类决策和AI决策，根据信心评分动态调整介入程度。
        
        Args:
            state: 当前游戏状态
            human_action: 人类动作（可选）
            
        Returns:
            Action: 最终决策动作
        """
        # 获取AI推荐动作
        ai_action = self.ai_agent.act(state)
        
        # 如果没有人类动作，直接返回AI动作
        if human_action is None:
            return ai_action
            
        # 更新信心评分
        self.meta_controller.update_confidence(human_action, ai_action, state)
        
        # 决定介入程度
        intervention_level = self.meta_controller.decide_intervention_level()
        
        # 记录决策
        self.decision_history.append({
            'human_action': human_action,
            'ai_action': ai_action,
            'confidence': self.meta_controller.get_confidence_score(),
            'intervention_level': intervention_level
        })
        
        # 如果不使用动态介入，直接返回人类动作
        if not self.use_dynamic_intervention:
            return human_action
            
        # 根据介入程度决定最终动作
        if intervention_level >= self.intervention_threshold:
            # AI介入
            logger.info(f"AI介入 (介入程度: {intervention_level:.2f})")
            return ai_action
        else:
            # 采纳人类决策
            logger.info(f"采纳人类决策 (介入程度: {intervention_level:.2f})")
            return human_action
            
    def get_confidence_score(self) -> float:
        """
        获取当前信心评分
        
        Returns:
            float: 当前信心评分
        """
        return self.meta_controller.get_confidence_score()
        
    def get_intervention_level(self) -> float:
        """
        获取当前介入程度
        
        Returns:
            float: 当前介入程度
        """
        return self.meta_controller.decide_intervention_level()
        
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            Dict[str, Any]: 统计信息字典
        """
        stats = self.meta_controller.get_stats()
        stats.update({
            'decision_history': self.decision_history[-self.meta_controller.history_window:],
            'intervention_threshold': self.intervention_threshold,
            'use_dynamic_intervention': self.use_dynamic_intervention
        })
        return stats
