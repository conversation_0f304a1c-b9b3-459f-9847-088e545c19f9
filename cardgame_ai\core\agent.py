"""
代理接口模块

定义代理的接口和抽象类，是框架的核心组件之一。
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Tuple, Optional, Union
import numpy as np
import os
import pickle

from cardgame_ai.core.base import State, Action, Experience, Batch


class Agent(ABC):
    """
    代理接口
    
    定义代理的标准接口，包括选择动作、训练、保存和加载模型等方法。
    所有具体代理都应该实现这个接口。
    """
    
    @abstractmethod
    def act(self, state: Union[State, np.ndarray], legal_actions: List[Action], is_training: bool = False) -> Action:
        """
        根据状态和合法动作选择一个动作
        
        Args:
            state (Union[State, np.ndarray]): 游戏状态或观察
            legal_actions (List[Action]): 合法动作列表
            is_training (bool, optional): 是否为训练模式. Defaults to False.
            
        Returns:
            Action: 选择的动作
        """
        pass
    
    @abstractmethod
    def train(self, experience: Union[Experience, Batch]) -> Dict[str, float]:
        """
        使用经验数据训练代理
        
        Args:
            experience (Union[Experience, Batch]): 单个经验或经验批次
            
        Returns:
            Dict[str, float]: 训练指标，如损失值等
        """
        pass
    
    @abstractmethod
    def save(self, path: str) -> None:
        """
        保存代理模型
        
        Args:
            path (str): 保存路径
        """
        pass
    
    @abstractmethod
    def load(self, path: str) -> None:
        """
        加载代理模型
        
        Args:
            path (str): 加载路径
        """
        pass
    
    def observe(self, observation: Any, reward: float, done: bool, info: Dict[str, Any]) -> None:
        """
        观察环境信息和对手动作，用于在线学习和对手建模
        
        默认实现为空，子类可以根据需要重写
        
        Args:
            observation (Any): 当前观察
            reward (float): 获得的奖励
            done (bool): 是否结束
            info (Dict[str, Any]): 额外信息，包括对手动作等
        """
        pass


class BaseAgent(Agent):
    """
    基础代理实现
    
    提供了基本功能的实现，包括对手建模支持。
    具体代理可以继承这个类获得通用功能。
    """
    
    def __init__(self, use_online_modeler: bool = False):
        """
        初始化基础代理
        
        Args:
            use_online_modeler (bool, optional): 是否启用在线对手建模. Defaults to False.
        """
        self.use_online_modeler = use_online_modeler
        self.online_modeler = None
        
        # 如果启用了在线对手建模，则创建建模器
        if self.use_online_modeler:
            try:
                from cardgame_ai.algorithms.opponent_modeling.online_modeler import OnlineOpponentModeler
                self.online_modeler = OnlineOpponentModeler()
            except ImportError:
                print("警告：无法导入OnlineOpponentModeler，对手建模功能将被禁用")
                self.use_online_modeler = False
    
    def observe(self, observation: Any, reward: float, done: bool, info: Dict[str, Any]) -> None:
        """
        观察环境信息和对手动作，并更新对手模型
        
        Args:
            observation (Any): 当前观察
            reward (float): 获得的奖励
            done (bool): 是否结束
            info (Dict[str, Any]): 额外信息，包括对手动作等
        """
        # 如果启用了在线对手建模且有对手动作信息，则更新模型
        if self.use_online_modeler and self.online_modeler:
            if 'opponent_action' in info and 'opponent_id' in info:
                # 基本更新
                self.online_modeler.update(
                    player_id=info['opponent_id'],
                    action=info['opponent_action'],
                    game_state=observation.game_state if hasattr(observation, 'game_state') else None
                )
                
                # 如果提供了上下文信息，则进行上下文相关的更新
                if 'context_key' in info:
                    self.online_modeler.context_aware_update(
                        player_id=info['opponent_id'],
                        action=info['opponent_action'],
                        context_key=info['context_key'],
                        game_state=observation.game_state if hasattr(observation, 'game_state') else None
                    )
    
    def act(self, state: Union[State, np.ndarray], legal_actions: List[Action], is_training: bool = False) -> Action:
        """
        根据状态和合法动作选择一个动作
        
        这是一个基本实现，需要在子类中重写
        
        Args:
            state (Union[State, np.ndarray]): 游戏状态或观察
            legal_actions (List[Action]): 合法动作列表
            is_training (bool, optional): 是否为训练模式. Defaults to False.
            
        Returns:
            Action: 选择的动作
        """
        # 必须在子类中实现
        raise NotImplementedError("act方法必须在子类中实现")
    
    def train(self, experience: Union[Experience, Batch]) -> Dict[str, float]:
        """
        使用经验数据训练代理
        
        这是一个基本实现，需要在子类中重写
        
        Args:
            experience (Union[Experience, Batch]): 单个经验或经验批次
            
        Returns:
            Dict[str, float]: 训练指标，如损失值等
        """
        # 必须在子类中实现
        raise NotImplementedError("train方法必须在子类中实现")
    
    def save(self, path: str) -> None:
        """
        保存代理模型
        
        这是一个基本实现，需要在子类中重写
        
        Args:
            path (str): 保存路径
        """
        # 必须在子类中实现
        raise NotImplementedError("save方法必须在子类中实现")
    
    def load(self, path: str) -> None:
        """
        加载代理模型
        
        这是一个基本实现，需要在子类中重写
        
        Args:
            path (str): 加载路径
        """
        # 必须在子类中实现
        raise NotImplementedError("load方法必须在子类中实现")


class RandomAgent(Agent):
    """
    随机代理
    
    一个简单的随机代理，用于基准测试和调试。
    """
    
    def __init__(self, seed: Optional[int] = None):
        """
        初始化随机代理
        
        Args:
            seed (Optional[int], optional): 随机种子. Defaults to None.
        """
        self.rng = np.random.RandomState(seed)
    
    def act(self, state: Union[State, np.ndarray], legal_actions: List[Action], is_training: bool = False) -> Action:
        """
        随机选择一个合法动作
        
        Args:
            state (Union[State, np.ndarray]): 游戏状态或观察
            legal_actions (List[Action]): 合法动作列表
            is_training (bool, optional): 是否为训练模式. Defaults to False.
            
        Returns:
            Action: 随机选择的动作
        """
        return self.rng.choice(legal_actions)
    
    def train(self, experience: Union[Experience, Batch]) -> Dict[str, float]:
        """
        随机代理不需要训练
        
        Args:
            experience (Union[Experience, Batch]): 单个经验或经验批次
            
        Returns:
            Dict[str, float]: 空字典
        """
        return {}
    
    def save(self, path: str) -> None:
        """
        保存随机代理
        
        Args:
            path (str): 保存路径
        """
        os.makedirs(os.path.dirname(path), exist_ok=True)
        with open(path, 'wb') as f:
            pickle.dump(self.rng, f)
    
    def load(self, path: str) -> None:
        """
        加载随机代理
        
        Args:
            path (str): 加载路径
        """
        with open(path, 'rb') as f:
            self.rng = pickle.load(f)


class HumanAgent(Agent):
    """
    人类代理
    
    用于人机交互，从用户输入获取动作。
    """
    
    def act(self, state: Union[State, np.ndarray], legal_actions: List[Action], is_training: bool = False) -> Action:
        """
        从用户输入获取动作
        
        Args:
            state (Union[State, np.ndarray]): 游戏状态或观察
            legal_actions (List[Action]): 合法动作列表
            is_training (bool, optional): 是否为训练模式. Defaults to False.
            
        Returns:
            Action: 用户选择的动作
        """
        # 显示当前状态和合法动作
        print("当前状态:")
        print(state)
        print("合法动作:")
        for i, action in enumerate(legal_actions):
            print(f"{i}: {action}")
        
        # 获取用户输入
        while True:
            try:
                choice = int(input("请选择动作 (输入序号): "))
                if 0 <= choice < len(legal_actions):
                    return legal_actions[choice]
                else:
                    print(f"无效的选择，请输入0-{len(legal_actions)-1}之间的数字")
            except ValueError:
                print("请输入有效的数字")
    
    def train(self, experience: Union[Experience, Batch]) -> Dict[str, float]:
        """
        人类代理不需要训练
        
        Args:
            experience (Union[Experience, Batch]): 单个经验或经验批次
            
        Returns:
            Dict[str, float]: 空字典
        """
        return {}
    
    def save(self, path: str) -> None:
        """
        人类代理不需要保存
        
        Args:
            path (str): 保存路径
        """
        pass
    
    def load(self, path: str) -> None:
        """
        人类代理不需要加载
        
        Args:
            path (str): 加载路径
        """
        pass
