"""
评估器接口模块

定义评估器的接口和抽象类，是框架的核心组件之一。
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Tuple, Optional, Union
import numpy as np
import os
import time
import logging
try:
    import matplotlib.pyplot as plt
    HAS_MATPLOTLIB = True
except ImportError:
    HAS_MATPLOTLIB = False
    print("警告：matplotlib未安装，可视化功能将不可用")
from collections import defaultdict

from cardgame_ai.core.base import State, Action
from cardgame_ai.core.environment import Environment
from cardgame_ai.core.agent import Agent


class Evaluator(ABC):
    """
    评估器接口

    定义评估器的标准接口，包括评估代理性能、可视化评估结果等方法。
    所有具体评估器都应该实现这个接口。
    """

    @abstractmethod
    def evaluate(self, env: Environment, agents: Union[Agent, List[Agent]], num_games: int, **kwargs) -> Dict[str, Any]:
        """
        评估代理性能

        Args:
            env (Environment): 游戏环境
            agents (Union[Agent, List[Agent]]): 要评估的代理或代理列表
            num_games (int): 评估的游戏数
            **kwargs: 其他参数

        Returns:
            Dict[str, Any]: 评估结果，如胜率、平均奖励等
        """
        pass

    @abstractmethod
    def visualize(self, metrics: Dict[str, Any], **kwargs) -> None:
        """
        可视化评估结果

        Args:
            metrics (Dict[str, Any]): 评估指标
            **kwargs: 其他参数
        """
        pass


class BaseEvaluator(Evaluator):
    """
    基础评估器抽象类

    为评估器提供一些通用实现。
    """

    def __init__(self, save_path: str = 'results'):
        """
        初始化基础评估器

        Args:
            save_path (str, optional): 结果保存路径. Defaults to 'results'.
        """
        self.save_path = save_path
        self.logger = logging.getLogger(self.__class__.__name__)

        # 创建保存目录
        os.makedirs(save_path, exist_ok=True)

    def evaluate(self, env: Environment, agents: Union[Agent, List[Agent]], num_games: int, **kwargs) -> Dict[str, Any]:
        """
        评估代理性能

        Args:
            env (Environment): 游戏环境
            agents (Union[Agent, List[Agent]]): 要评估的代理或代理列表
            num_games (int): 评估的游戏数
            **kwargs: 其他参数

        Returns:
            Dict[str, Any]: 评估结果，如胜率、平均奖励等
        """
        # 如果agents是单个代理，则创建代理列表
        if not isinstance(agents, list):
            agents = [agents] * env.num_players

        # 确保代理数量与玩家数量一致
        assert len(agents) == env.num_players, f"代理数量({len(agents)})与玩家数量({env.num_players})不一致"

        start_time = time.time()
        metrics = defaultdict(list)

        for game in range(num_games):
            # 重置环境
            state = env.reset()
            done = False
            game_rewards = [0] * env.num_players
            game_length = 0

            # 运行一局游戏
            while not done:
                player_id = state.get_player_id()
                legal_actions = env.get_legal_actions(state)
                observation = env.get_observation(state)

                # 获取当前玩家的动作
                action = agents[player_id].act(observation, legal_actions, is_training=False)

                # 执行动作
                next_state, reward, done, info = env.step(action)

                # 更新状态和奖励
                state = next_state
                game_rewards[player_id] += reward
                game_length += 1

            # 获取游戏结果
            payoffs = env.get_payoffs(state)

            # 记录指标
            metrics['game_rewards'].append(game_rewards)
            metrics['game_lengths'].append(game_length)
            metrics['payoffs'].append(payoffs)

            # 记录胜负
            winner = np.argmax(payoffs)
            for i in range(env.num_players):
                metrics[f'player_{i}_win'].append(1 if i == winner else 0)

            # 日志
            if (game + 1) % 10 == 0:
                elapsed_time = time.time() - start_time
                self.logger.info(
                    f"Evaluation: {game + 1}/{num_games} games | "
                    f"Time: {elapsed_time:.2f}s"
                )

        # 计算汇总指标
        result = {}

        # 平均奖励
        result['mean_rewards'] = np.mean(metrics['game_rewards'], axis=0).tolist()

        # 平均游戏长度
        result['mean_game_length'] = np.mean(metrics['game_lengths'])

        # 胜率
        for i in range(env.num_players):
            result[f'player_{i}_win_rate'] = np.mean(metrics[f'player_{i}_win'])

        # 平均收益
        result['mean_payoffs'] = np.mean(metrics['payoffs'], axis=0).tolist()

        # 计算总评估时间
        total_time = time.time() - start_time
        result['total_time'] = total_time

        return result

    def visualize(self, metrics: Dict[str, Any], **kwargs) -> None:
        """
        可视化评估结果

        Args:
            metrics (Dict[str, Any]): 评估指标
            **kwargs: 其他参数
        """
        if not HAS_MATPLOTLIB:
            self.logger.warning("无法可视化结果：matplotlib未安装")
            return

        # 创建图形
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))

        # 绘制胜率
        win_rates = []
        labels = []
        for key, value in metrics.items():
            if key.endswith('_win_rate'):
                win_rates.append(value)
                labels.append(key.replace('_win_rate', ''))

        axes[0, 0].bar(labels, win_rates)
        axes[0, 0].set_title('Win Rate')
        axes[0, 0].set_ylim(0, 1)
        axes[0, 0].set_ylabel('Win Rate')

        # 绘制平均奖励
        if 'mean_rewards' in metrics:
            axes[0, 1].bar(labels, metrics['mean_rewards'])
            axes[0, 1].set_title('Mean Rewards')
            axes[0, 1].set_ylabel('Reward')

        # 绘制平均收益
        if 'mean_payoffs' in metrics:
            axes[1, 0].bar(labels, metrics['mean_payoffs'])
            axes[1, 0].set_title('Mean Payoffs')
            axes[1, 0].set_ylabel('Payoff')

        # 绘制平均游戏长度
        if 'mean_game_length' in metrics:
            axes[1, 1].bar(['Game Length'], [metrics['mean_game_length']])
            axes[1, 1].set_title('Mean Game Length')
            axes[1, 1].set_ylabel('Steps')

        # 调整布局
        plt.tight_layout()

        # 保存图形
        save_path = os.path.join(self.save_path, 'evaluation_results.png')
        plt.savefig(save_path)
        self.logger.info(f"Evaluation results saved to {save_path}")

        # 显示图形
        plt.show()


class TournamentEvaluator(BaseEvaluator):
    """
    锦标赛评估器

    用于评估多个代理之间的相对性能。
    """

    def evaluate_tournament(self, env: Environment, agents: List[Agent], num_games: int, **kwargs) -> Dict[str, Any]:
        """
        评估锦标赛性能

        Args:
            env (Environment): 游戏环境
            agents (List[Agent]): 要评估的代理列表
            num_games (int): 每对代理之间的游戏数
            **kwargs: 其他参数

        Returns:
            Dict[str, Any]: 评估结果，如胜率矩阵等
        """
        num_agents = len(agents)
        win_matrix = np.zeros((num_agents, num_agents))
        payoff_matrix = np.zeros((num_agents, num_agents))

        # 对每对代理进行评估
        for i in range(num_agents):
            for j in range(i + 1, num_agents):
                # 创建代理列表
                tournament_agents = [agents[i], agents[j]]

                # 评估
                result = self.evaluate(env, tournament_agents, num_games, **kwargs)

                # 记录胜率
                win_matrix[i, j] = result['player_0_win_rate']
                win_matrix[j, i] = result['player_1_win_rate']

                # 记录收益
                payoff_matrix[i, j] = result['mean_payoffs'][0]
                payoff_matrix[j, i] = result['mean_payoffs'][1]

        # 计算总胜率
        total_win_rates = win_matrix.sum(axis=1) / (num_agents - 1)

        # 计算总收益
        total_payoffs = payoff_matrix.sum(axis=1) / (num_agents - 1)

        # 返回结果
        return {
            'win_matrix': win_matrix.tolist(),
            'payoff_matrix': payoff_matrix.tolist(),
            'total_win_rates': total_win_rates.tolist(),
            'total_payoffs': total_payoffs.tolist()
        }

    def visualize_tournament(self, metrics: Dict[str, Any], agent_names: Optional[List[str]] = None, **kwargs) -> None:
        """
        可视化锦标赛结果

        Args:
            metrics (Dict[str, Any]): 评估指标
            agent_names (Optional[List[str]], optional): 代理名称列表. Defaults to None.
            **kwargs: 其他参数
        """
        if not HAS_MATPLOTLIB:
            self.logger.warning("无法可视化结果：matplotlib未安装")
            return

        win_matrix = np.array(metrics['win_matrix'])
        payoff_matrix = np.array(metrics['payoff_matrix'])
        total_win_rates = np.array(metrics['total_win_rates'])
        total_payoffs = np.array(metrics['total_payoffs'])

        num_agents = len(total_win_rates)

        # 如果没有提供代理名称，则使用默认名称
        if agent_names is None:
            agent_names = [f'Agent {i}' for i in range(num_agents)]

        # 创建图形
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # 绘制胜率矩阵
        im0 = axes[0, 0].imshow(win_matrix, cmap='viridis')
        axes[0, 0].set_title('Win Rate Matrix')
        axes[0, 0].set_xticks(np.arange(num_agents))
        axes[0, 0].set_yticks(np.arange(num_agents))
        axes[0, 0].set_xticklabels(agent_names)
        axes[0, 0].set_yticklabels(agent_names)
        plt.colorbar(im0, ax=axes[0, 0])

        # 绘制收益矩阵
        im1 = axes[0, 1].imshow(payoff_matrix, cmap='viridis')
        axes[0, 1].set_title('Payoff Matrix')
        axes[0, 1].set_xticks(np.arange(num_agents))
        axes[0, 1].set_yticks(np.arange(num_agents))
        axes[0, 1].set_xticklabels(agent_names)
        axes[0, 1].set_yticklabels(agent_names)
        plt.colorbar(im1, ax=axes[0, 1])

        # 绘制总胜率
        axes[1, 0].bar(agent_names, total_win_rates)
        axes[1, 0].set_title('Total Win Rates')
        axes[1, 0].set_ylim(0, 1)
        axes[1, 0].set_ylabel('Win Rate')

        # 绘制总收益
        axes[1, 1].bar(agent_names, total_payoffs)
        axes[1, 1].set_title('Total Payoffs')
        axes[1, 1].set_ylabel('Payoff')

        # 调整布局
        plt.tight_layout()

        # 保存图形
        save_path = os.path.join(self.save_path, 'tournament_results.png')
        plt.savefig(save_path)
        self.logger.info(f"Tournament results saved to {save_path}")

        # 显示图形
        plt.show()
