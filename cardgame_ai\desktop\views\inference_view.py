#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
推理视图

用于AI模型推理的视图，包括模型选择、参数配置、推理控制和结果展示。
"""

import os
import logging
from typing import Dict, Any, Optional, List

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QGroupBox, QFormLayout, QComboBox, QSpinBox, QDoubleSpinBox,
    QTabWidget, QSplitter, QScrollArea, QFrame
)
from PySide6.QtCore import Qt, Signal, Slot, QSize
from PySide6.QtGui import QFont, QColor

# 导入自定义组件
from ..utils.theme_manager import theme_manager
from ..config import ClientConfig
from cardgame_ai.ui.trust_visualization import TrustVisualizationWidget
from cardgame_ai.ui.visualization_components import BeliefDistributionWidget
from cardgame_ai.interface.api import BeliefAPI

logger = logging.getLogger(__name__)


class InferenceView(QWidget):
    """推理视图类"""

    def __init__(self, config: ClientConfig):
        """
        初始化推理视图

        Args:
            config: 客户端配置
        """
        super().__init__()

        # 保存配置
        self.config = config

        # 设置对象名称
        self.setObjectName("inferenceView")

        # 初始化UI组件
        self.ui_components = {}

        # 初始化UI
        self.setup_ui()

        logger.info("推理视图初始化完成")

    def setup_ui(self):
        """设置UI布局"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # 创建游戏选择区（顶部）
        game_selection_widget = self.create_game_selection_widget()
        main_layout.addWidget(game_selection_widget)

        # 创建中央区域
        central_widget = QWidget()
        central_layout = QHBoxLayout(central_widget)
        central_layout.setContentsMargins(0, 0, 0, 0)
        central_layout.setSpacing(10)

        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)

        # 创建左侧面板（参数配置区和模型选择区）
        left_panel = self.create_left_panel()
        splitter.addWidget(left_panel)

        # 创建中央区域（推理结果区）
        result_widget = self.create_result_widget()
        splitter.addWidget(result_widget)

        # 创建右侧面板（信任可视化区）
        right_panel = self.create_trust_visualization_widget()
        splitter.addWidget(right_panel)

        # 设置分割器初始大小
        splitter.setSizes([300, 500, 300])

        # 添加分割器到中央布局
        central_layout.addWidget(splitter)

        # 添加中央区域到主布局
        main_layout.addWidget(central_widget)

        # 创建底部控制区
        control_widget = self.create_control_widget()
        main_layout.addWidget(control_widget)

    def create_game_selection_widget(self):
        """
        创建游戏选择区

        Returns:
            QWidget: 游戏选择区部件
        """
        # 创建游戏选择组
        group_box = QGroupBox("游戏选择")
        group_box.setMaximumHeight(80)  # 限制高度

        # 创建布局
        layout = QHBoxLayout(group_box)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # 创建游戏选择下拉框
        game_label = QLabel("选择游戏:")
        layout.addWidget(game_label)

        game_combo = QComboBox()
        game_combo.addItem("斗地主(经典版)", "doudizhu_classic")
        game_combo.addItem("斗地主(二打一版)", "doudizhu_2v1")
        layout.addWidget(game_combo)

        # 保存组件引用
        self.ui_components["game_combo"] = game_combo

        # 添加模型选择下拉框
        model_label = QLabel("选择模型:")
        layout.addWidget(model_label)

        model_combo = QComboBox()
        model_combo.addItem("选择模型...", "")
        layout.addWidget(model_combo)

        # 保存组件引用
        self.ui_components["model_combo"] = model_combo

        # 添加加载按钮
        load_button = QPushButton("加载模型")
        layout.addWidget(load_button)

        # 保存组件引用
        self.ui_components["load_button"] = load_button

        # 添加弹性空间
        layout.addStretch(1)

        return group_box

    def create_left_panel(self):
        """
        创建左侧面板

        Returns:
            QWidget: 左侧面板部件
        """
        # 创建左侧面板
        left_panel = QWidget()
        left_panel.setMinimumWidth(250)
        left_panel.setMaximumWidth(350)

        # 创建布局
        layout = QVBoxLayout(left_panel)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(10)

        # 创建参数配置区
        params_group = QGroupBox("参数配置")
        params_layout = QFormLayout(params_group)
        params_layout.setContentsMargins(10, 10, 10, 10)
        params_layout.setSpacing(10)

        # 添加温度参数
        temperature_label = QLabel("温度:")
        temperature_spin = QDoubleSpinBox()
        temperature_spin.setRange(0.1, 2.0)
        temperature_spin.setSingleStep(0.1)
        temperature_spin.setValue(1.0)
        params_layout.addRow(temperature_label, temperature_spin)

        # 保存组件引用
        self.ui_components["temperature_spin"] = temperature_spin

        # 添加搜索深度参数
        search_depth_label = QLabel("搜索深度:")
        search_depth_spin = QSpinBox()
        search_depth_spin.setRange(1, 100)
        search_depth_spin.setSingleStep(1)
        search_depth_spin.setValue(10)
        params_layout.addRow(search_depth_label, search_depth_spin)

        # 保存组件引用
        self.ui_components["search_depth_spin"] = search_depth_spin

        # 添加组件权重区域
        weights_label = QLabel("组件权重:")
        params_layout.addRow(weights_label)

        # 添加神经网络权重
        nn_weight_label = QLabel("神经网络权重:")
        nn_weight_spin = QDoubleSpinBox()
        nn_weight_spin.setRange(0.0, 1.0)
        nn_weight_spin.setSingleStep(0.1)
        nn_weight_spin.setValue(0.5)
        params_layout.addRow(nn_weight_label, nn_weight_spin)

        # 保存组件引用
        self.ui_components["nn_weight_spin"] = nn_weight_spin

        # 添加搜索权重
        search_weight_label = QLabel("搜索权重:")
        search_weight_spin = QDoubleSpinBox()
        search_weight_spin.setRange(0.0, 1.0)
        search_weight_spin.setSingleStep(0.1)
        search_weight_spin.setValue(0.3)
        params_layout.addRow(search_weight_label, search_weight_spin)

        # 保存组件引用
        self.ui_components["search_weight_spin"] = search_weight_spin

        # 添加规则权重
        rule_weight_label = QLabel("规则权重:")
        rule_weight_spin = QDoubleSpinBox()
        rule_weight_spin.setRange(0.0, 1.0)
        rule_weight_spin.setSingleStep(0.1)
        rule_weight_spin.setValue(0.2)
        params_layout.addRow(rule_weight_label, rule_weight_spin)

        # 保存组件引用
        self.ui_components["rule_weight_spin"] = rule_weight_spin

        # 添加解释模式选项
        explanation_label = QLabel("解释模式:")
        explanation_combo = QComboBox()
        explanation_combo.addItem("无", "none")
        explanation_combo.addItem("简单", "simple")
        explanation_combo.addItem("详细", "detailed")
        params_layout.addRow(explanation_label, explanation_combo)

        # 保存组件引用
        self.ui_components["explanation_combo"] = explanation_combo

        # 添加参数配置区到左侧面板
        layout.addWidget(params_group)

        # 添加弹性空间
        layout.addStretch(1)

        return left_panel

    def create_result_widget(self):
        """
        创建推理结果区

        Returns:
            QWidget: 推理结果区部件
        """
        # 创建推理结果区
        result_widget = QTabWidget()
        result_widget.setMinimumWidth(400)

        # 创建结果面板
        result_tab = QWidget()
        result_layout = QVBoxLayout(result_tab)
        result_layout.setContentsMargins(10, 10, 10, 10)
        result_layout.setSpacing(10)

        # 添加状态信息区
        status_group = QGroupBox("状态信息")
        status_layout = QVBoxLayout(status_group)
        status_layout.setContentsMargins(10, 10, 10, 10)
        status_layout.setSpacing(10)

        self.status_label = QLabel("模型未加载")
        self.status_label.setWordWrap(True)
        status_layout.addWidget(self.status_label)

        result_layout.addWidget(status_group)

        # 添加推理结果区
        output_group = QGroupBox("推理结果")
        output_layout = QVBoxLayout(output_group)
        output_layout.setContentsMargins(10, 10, 10, 10)
        output_layout.setSpacing(10)

        self.output_label = QLabel("暂无推理结果")
        self.output_label.setWordWrap(True)
        self.output_label.setAlignment(Qt.AlignTop | Qt.AlignLeft)
        self.output_label.setMinimumHeight(150)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setWidget(self.output_label)

        output_layout.addWidget(scroll_area)

        result_layout.addWidget(output_group)

        # 添加结果指标区
        metrics_group = QGroupBox("结果指标")
        metrics_layout = QVBoxLayout(metrics_group)
        metrics_layout.setContentsMargins(10, 10, 10, 10)
        metrics_layout.setSpacing(10)

        self.metrics_label = QLabel("暂无指标数据")
        self.metrics_label.setWordWrap(True)
        metrics_layout.addWidget(self.metrics_label)

        result_layout.addWidget(metrics_group)

        # 添加结果面板到标签页
        result_widget.addTab(result_tab, "推理结果")

        # 添加信念分布标签页
        belief_tab = QWidget()
        belief_layout = QVBoxLayout(belief_tab)
        belief_layout.setContentsMargins(10, 10, 10, 10)
        belief_layout.setSpacing(10)

        # 添加信念分布可视化组件
        self.belief_distribution_widget = BeliefDistributionWidget()
        belief_layout.addWidget(self.belief_distribution_widget)

        # 添加信念分布标签页到标签页
        result_widget.addTab(belief_tab, "信念分布")

        # 保存组件引用
        self.ui_components["result_widget"] = result_widget
        self.ui_components["belief_distribution_widget"] = self.belief_distribution_widget

        return result_widget

    def create_trust_visualization_widget(self):
        """
        创建信任可视化组件

        Returns:
            QWidget: 信任可视化组件
        """
        # 创建信任可视化容器
        trust_container = QWidget()
        trust_container.setMinimumWidth(250)
        trust_container.setMaximumWidth(350)

        # 创建布局
        layout = QVBoxLayout(trust_container)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(10)

        # 创建信任可视化组件
        self.trust_visualization = TrustVisualizationWidget()

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setWidget(self.trust_visualization)

        # 添加滚动区域到布局
        layout.addWidget(scroll_area)

        # 添加标题
        title_label = QLabel("AI信任可视化")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        layout.insertWidget(0, title_label)

        # 添加说明
        description_label = QLabel(
            "此区域显示AI决策过程的可视化信息，"
            "帮助您理解AI的决策依据和可信度。"
        )
        description_label.setWordWrap(True)
        description_label.setAlignment(Qt.AlignCenter)
        layout.insertWidget(1, description_label)

        return trust_container

    def create_control_widget(self):
        """
        创建控制区域

        Returns:
            QWidget: 控制区域部件
        """
        # 创建控制区域
        control_widget = QWidget()
        control_widget.setMaximumHeight(80)

        # 创建布局
        layout = QHBoxLayout(control_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(20)

        # 添加运行推理按钮
        run_button = QPushButton("运行推理")
        run_button.setMinimumWidth(120)
        layout.addWidget(run_button)

        # 保存组件引用
        self.ui_components["run_button"] = run_button

        # 添加批量推理按钮
        batch_button = QPushButton("批量推理")
        batch_button.setMinimumWidth(120)
        layout.addWidget(batch_button)

        # 保存组件引用
        self.ui_components["batch_button"] = batch_button

        # 添加导出结果按钮
        export_button = QPushButton("导出结果")
        export_button.setMinimumWidth(120)
        layout.addWidget(export_button)

        # 保存组件引用
        self.ui_components["export_button"] = export_button

        # 添加清除按钮
        clear_button = QPushButton("清除")
        clear_button.setMinimumWidth(120)
        layout.addWidget(clear_button)

        # 保存组件引用
        self.ui_components["clear_button"] = clear_button

        # 连接信号
        run_button.clicked.connect(self.on_run_inference)
        batch_button.clicked.connect(self.on_batch_inference)
        export_button.clicked.connect(self.on_export_results)
        clear_button.clicked.connect(self.on_clear)

        return control_widget

    @Slot()
    def on_run_inference(self):
        """运行推理"""
        logger.info("开始运行推理")
        
        # 更新状态
        self.status_label.setText("正在推理...")
        
        # 运行推理 (模拟演示)
        self.simulate_inference()

    @Slot()
    def on_batch_inference(self):
        """批量推理"""
        logger.info("批量推理")

    @Slot()
    def on_export_results(self):
        """导出结果"""
        logger.info("导出结果")

    @Slot()
    def on_clear(self):
        """清除结果"""
        logger.info("清除结果")

        # 重置UI
        self.status_label.setText("模型未加载")
        self.output_label.setText("暂无推理结果")
        self.metrics_label.setText("暂无指标数据")
        self.belief_distribution_widget.clear_belief_distribution()

        # 清除信任可视化
        self.trust_visualization.clear_visualization()

    def simulate_inference(self):
        """模拟推理过程 (演示用)"""
        import random
        import time
        
        # 模拟推理延迟
        time.sleep(0.5)
        
        # 更新状态
        self.status_label.setText("推理完成")
        
        # 生成随机推理结果
        actions = ["出牌: A", "出牌: K", "出牌: Q", "出牌: J", "不出"]
        confidence = random.uniform(0.7, 0.98)
        
        result_text = f"推荐动作: {random.choice(actions)}\n"
        result_text += f"置信度: {confidence:.2f}\n\n"
        result_text += "决策理由:\n"
        result_text += "1. 基于当前牌局状态评估\n"
        result_text += "2. 考虑对手可能持有的牌\n"
        result_text += "3. 最大化获胜概率的策略\n"
        
        self.output_label.setText(result_text)
        
        # 生成随机指标数据
        metrics_text = f"决策时间: {random.uniform(0.1, 0.5):.3f} 秒\n"
        metrics_text += f"搜索深度: {random.randint(3, 10)}\n"
        metrics_text += f"节点数: {random.randint(100, 5000)}\n"
        metrics_text += f"剪枝率: {random.uniform(0.3, 0.8):.2f}\n"
        
        self.metrics_label.setText(metrics_text)
        
        # 更新信念分布数据 - 使用模拟数据
        self.update_belief_distribution()
        
    def update_belief_distribution(self, belief_tracker=None, game_state=None):
        """
        更新信念分布
        
        Args:
            belief_tracker: 信念追踪器实例
            game_state: 游戏状态
        """
        # 如果提供了实际的belief_tracker，则使用实际数据
        if belief_tracker is not None:
            try:
                # 使用API获取信念分布数据
                belief_data = BeliefAPI.get_belief_distribution(
                    game_state=game_state, 
                    belief_tracker=belief_tracker
                )
                
                # 更新可视化组件
                self.belief_distribution_widget.update_belief_distribution(
                    belief_data['distribution'], 
                    belief_data['card_names']
                )
                
                logger.info("使用实际数据更新信念分布")
                return
                
            except Exception as e:
                logger.error(f"更新信念分布时出错: {e}")
                # 如果出错，回退到模拟数据
        
        # 使用模拟数据（当没有实际数据或者出错时）
        self.simulate_belief_distribution()
        
    def simulate_belief_distribution(self):
        """模拟信念分布数据 (演示用)"""
        import random
        
        # 创建模拟的牌映射
        card_names = {
            "h1": "红桃A", "h2": "红桃2", "h3": "红桃3", "h4": "红桃4", "h5": "红桃5",
            "s1": "黑桃A", "s2": "黑桃2", "s3": "黑桃3", "s4": "黑桃4", "s5": "黑桃5",
            "d1": "方块A", "d2": "方块2", "d3": "方块3", "d4": "方块4", "d5": "方块5",
            "c1": "梅花A", "c2": "梅花2", "c3": "梅花3", "c4": "梅花4", "c5": "梅花5"
        }
        
        # 生成随机概率分布
        belief_distribution = {}
        for card_id in card_names:
            # 生成随机概率，总和趋近于1
            belief_distribution[card_id] = random.uniform(0, 0.6)
        
        # 归一化概率，确保总和为1
        total_prob = sum(belief_distribution.values())
        for card_id in belief_distribution:
            belief_distribution[card_id] /= total_prob
        
        # 更新信念分布组件
        self.belief_distribution_widget.update_belief_distribution(belief_distribution, card_names)
        logger.info("使用模拟数据更新信念分布")
