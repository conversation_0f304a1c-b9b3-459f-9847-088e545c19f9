#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
重构后系统快速验证脚本

快速验证重构后的斗地主AI训练系统核心功能是否正常工作。
这个脚本会进行基础的功能测试，确保重构没有破坏核心功能。
"""

import sys
import os
import tempfile
import yaml
import traceback
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

def test_imports():
    """测试核心模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        # 测试核心训练脚本导入
        from cardgame_ai.zhuchengxu.optimized_training_integrated import OptimizedTrainingSystem
        print("  ✅ 核心训练脚本导入成功")
        
        # 测试决策系统导入
        from cardgame_ai.core.optimized_integrated_system import OptimizedIntegratedSystem
        print("  ✅ 集成决策系统导入成功")
        
        # 测试混合决策系统导入
        try:
            from cardgame_ai.algorithms.hybrid_decision_system import NeuralNetworkComponent
            print("  ✅ 混合决策系统导入成功")
        except ImportError as e:
            print(f"  ⚠️ 混合决策系统导入失败（可能缺少依赖）: {e}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 模块导入失败: {e}")
        traceback.print_exc()
        return False

def test_training_system_basic():
    """测试训练系统基础功能"""
    print("\n🔍 测试训练系统基础功能...")
    
    try:
        from cardgame_ai.zhuchengxu.optimized_training_integrated import OptimizedTrainingSystem
        
        # 测试初始化
        system = OptimizedTrainingSystem()
        print("  ✅ 训练系统初始化成功")
        
        # 测试日志设置
        with tempfile.TemporaryDirectory() as temp_dir:
            log_dir = os.path.join(temp_dir, "logs")
            system.setup_logging("INFO", log_dir)
            print("  ✅ 日志系统设置成功")
            
            # 测试设备检测
            device = system.detect_device()
            print(f"  ✅ 设备检测成功: {device}")
            
            # 测试配置加载（使用临时配置）
            config_path = os.path.join(temp_dir, "test_config.yaml")
            test_config = {
                'game': 'doudizhu',
                'algorithm': 'efficient_zero',
                'device': 'cpu',
                'training': {
                    'epochs': 1,
                    'episodes_per_epoch': 1,
                    'updates_per_epoch': 1
                }
            }
            
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(test_config, f)
            
            config = system.load_config(config_path)
            print("  ✅ 配置文件加载成功")
            
            return True
            
    except Exception as e:
        print(f"  ❌ 训练系统测试失败: {e}")
        traceback.print_exc()
        return False

def test_fail_fast_behavior():
    """测试fail-fast行为"""
    print("\n🔍 测试fail-fast行为...")
    
    try:
        from cardgame_ai.zhuchengxu.optimized_training_integrated import OptimizedTrainingSystem
        
        system = OptimizedTrainingSystem()
        system.setup_logging()
        
        # 测试配置文件不存在时的fail-fast
        try:
            system.load_config("non_existent_config.yaml")
            print("  ❌ 配置文件不存在时应该抛出异常")
            return False
        except FileNotFoundError:
            print("  ✅ 配置文件不存在时正确抛出FileNotFoundError")
        
        # 测试不支持的配置文件格式
        with tempfile.TemporaryDirectory() as temp_dir:
            json_config = os.path.join(temp_dir, "config.json")
            with open(json_config, 'w') as f:
                f.write('{"test": "value"}')
            
            try:
                system.load_config(json_config)
                print("  ❌ 不支持的配置格式应该抛出异常")
                return False
            except ValueError as e:
                if "只支持YAML格式" in str(e):
                    print("  ✅ 不支持的配置格式正确抛出ValueError")
                else:
                    print(f"  ❌ 错误信息不正确: {e}")
                    return False
        
        # 测试无效YAML文件
        with tempfile.TemporaryDirectory() as temp_dir:
            invalid_yaml = os.path.join(temp_dir, "invalid.yaml")
            with open(invalid_yaml, 'w') as f:
                f.write("invalid: yaml: content: [")
            
            try:
                system.load_config(invalid_yaml)
                print("  ❌ 无效YAML文件应该抛出异常")
                return False
            except RuntimeError as e:
                if "配置文件加载失败" in str(e):
                    print("  ✅ 无效YAML文件正确抛出RuntimeError")
                else:
                    print(f"  ❌ 错误信息不正确: {e}")
                    return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ fail-fast行为测试失败: {e}")
        traceback.print_exc()
        return False

def test_no_fallback_mechanisms():
    """测试确保没有备用机制"""
    print("\n🔍 测试确保没有备用机制...")
    
    try:
        from cardgame_ai.zhuchengxu.optimized_training_integrated import OptimizedTrainingSystem
        
        system = OptimizedTrainingSystem()
        
        # 检查训练系统中不存在备用相关的属性或方法
        fallback_attributes = [
            'use_unified_system', 'use_optimization', '_fallback_training',
            '_simulation_mode', '_mock_training', 'get_default_config'
        ]
        
        for attr in fallback_attributes:
            if hasattr(system, attr):
                print(f"  ❌ 发现备用机制属性: {attr}")
                return False
        
        print("  ✅ 训练系统中没有发现备用机制属性")
        
        # 检查模块级别不存在模拟相关的类或函数
        import cardgame_ai.zhuchengxu.optimized_training_integrated as module
        
        simulation_classes = [
            'NumpyFallback', 'MockTrainer', 'SimulationMode',
            'HAS_NUMPY', 'HAS_YAML', 'HAS_UNIFIED_SYSTEM', 
            'HAS_TRAINING_MODULE', 'HAS_OPTIMIZATION_COMPONENTS'
        ]
        
        for cls in simulation_classes:
            if hasattr(module, cls):
                print(f"  ❌ 发现模拟组件: {cls}")
                return False
        
        print("  ✅ 模块中没有发现模拟组件")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 备用机制检查失败: {e}")
        traceback.print_exc()
        return False

def test_decision_system_basic():
    """测试决策系统基础功能"""
    print("\n🔍 测试决策系统基础功能...")
    
    try:
        # 由于决策系统可能依赖复杂的组件，我们只测试基本的导入和初始化
        from cardgame_ai.core.optimized_integrated_system import OptimizedIntegratedSystem
        
        # 测试类定义存在
        print("  ✅ 集成决策系统类定义正确")
        
        # 检查没有备用决策方法
        if hasattr(OptimizedIntegratedSystem, '_fallback_decision'):
            print("  ❌ 发现备用决策方法 _fallback_decision")
            return False
        
        print("  ✅ 没有发现备用决策方法")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 决策系统测试失败: {e}")
        traceback.print_exc()
        return False

def test_error_message_quality():
    """测试错误信息质量"""
    print("\n🔍 测试错误信息质量...")
    
    try:
        from cardgame_ai.zhuchengxu.optimized_training_integrated import OptimizedTrainingSystem
        
        system = OptimizedTrainingSystem()
        system.setup_logging()
        
        # 测试配置文件不存在的错误信息
        try:
            system.load_config("test_non_existent.yaml")
        except FileNotFoundError as e:
            error_msg = str(e)
            required_info = ["配置文件不存在", "无法继续训练", "test_non_existent.yaml"]
            
            for info in required_info:
                if info not in error_msg:
                    print(f"  ❌ 错误信息缺少必要信息: {info}")
                    return False
            
            print("  ✅ 错误信息包含所有必要信息")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 错误信息质量测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始重构后系统快速验证...")
    print("=" * 60)
    
    tests = [
        ("模块导入", test_imports),
        ("训练系统基础功能", test_training_system_basic),
        ("fail-fast行为", test_fail_fast_behavior),
        ("备用机制检查", test_no_fallback_mechanisms),
        ("决策系统基础功能", test_decision_system_basic),
        ("错误信息质量", test_error_message_quality),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"💥 {test_name} - 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！重构后的系统功能正常。")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
