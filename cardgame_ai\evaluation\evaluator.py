"""
评估系统模块

实现评估系统，包括多种评估指标、对抗评估、自我评估和结果分析。
"""
import os
import time
import json
import logging
import datetime
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, Any, List, Tuple, Optional, Union, Callable
from collections import defaultdict

from cardgame_ai.core.base import State, Action
from cardgame_ai.core.environment import Environment
from cardgame_ai.core.agent import Agent
from cardgame_ai.core.evaluator import BaseEvaluator, TournamentEvaluator


class AdvancedEvaluator(BaseEvaluator):
    """
    高级评估器
    
    实现更完整的评估功能，包括多种评估指标、历史跟踪和可视化。
    """
    
    def __init__(self, save_path: str = 'results', history_size: int = 10):
        """
        初始化高级评估器
        
        Args:
            save_path (str, optional): 结果保存路径. Defaults to 'results'.
            history_size (int, optional): 保存的历史评估数量. Defaults to 10.
        """
        super().__init__(save_path)
        
        self.history_size = history_size
        self.history = []
        
        # 创建目录
        self.plots_dir = os.path.join(save_path, 'plots')
        self.data_dir = os.path.join(save_path, 'data')
        os.makedirs(self.plots_dir, exist_ok=True)
        os.makedirs(self.data_dir, exist_ok=True)
        
        # 评估历史记录文件
        self.history_file = os.path.join(save_path, 'evaluation_history.jsonl')
        
        # 协作指标
        self.cooperation_metrics = {
            "coordination_attempts": 0,
            "coordination_success": 0,
            "coordination_rate": 0.0,
            "joint_play_success": 0,
            "joint_play_attempts": 0,
            "joint_success_rate": 0.0,
            "redundant_play_count": 0
        }
    
    def evaluate(self, env: Environment, agents: Union[Agent, List[Agent]], num_games: int,
                reference_agents: Optional[Union[Agent, List[Agent]]] = None,
                agent_names: Optional[List[str]] = None,
                save_games: bool = False,
                evaluate_cooperation: bool = False,
                **kwargs) -> Dict[str, Any]:
        """
        评估代理性能
        
        Args:
            env (Environment): 游戏环境
            agents (Union[Agent, List[Agent]]): 要评估的代理或代理列表
            num_games (int): 评估的游戏数
            reference_agents (Optional[Union[Agent, List[Agent]]], optional): 参考代理. Defaults to None.
            agent_names (Optional[List[str]], optional): 代理名称. Defaults to None.
            save_games (bool, optional): 是否保存游戏记录. Defaults to False.
            evaluate_cooperation (bool, optional): 是否评估协作能力. Defaults to False.
            **kwargs: 其他参数
            
        Returns:
            Dict[str, Any]: 评估结果，如胜率、平均奖励等
        """
        # 基础评估
        result = super().evaluate(env, agents, num_games, **kwargs)
        
        # 添加额外指标
        if save_games and hasattr(env, 'get_game_record'):
            result['game_records'] = env.get_game_record()
        
        # 添加时间戳和评估元数据
        result['timestamp'] = datetime.datetime.now().isoformat()
        result['num_games'] = num_games
        
        if agent_names:
            result['agent_names'] = agent_names
        
        # 评估协作能力
        if evaluate_cooperation and isinstance(agents, list) and len(agents) > 1:
            cooperation_result = self._evaluate_cooperation(agents)
            result['cooperation_metrics'] = cooperation_result
        
        # 添加到历史记录
        self.history.append(result)
        while len(self.history) > self.history_size:
            self.history.pop(0)
        
        # 保存评估结果
        self._save_result(result)
        
        return result
    
    def _evaluate_cooperation(self, agents: List[Agent]) -> Dict[str, Any]:
        """
        评估智能体之间的协作能力
        
        Args:
            agents (List[Agent]): 要评估的智能体列表
            
        Returns:
            Dict[str, Any]: 协作评估结果
        """
        # 重置协作指标
        self.cooperation_metrics = {
            "coordination_attempts": 0,
            "coordination_success": 0,
            "coordination_rate": 0.0,
            "joint_play_success": 0,
            "joint_play_attempts": 0,
            "joint_success_rate": 0.0,
            "redundant_play_count": 0
        }
        
        # 收集协作统计数据
        for agent in agents:
            if hasattr(agent, 'cooperation_stats'):
                # 累加协作统计
                for key in self.cooperation_metrics:
                    if key in agent.cooperation_stats:
                        self.cooperation_metrics[key] += agent.cooperation_stats[key]
        
        # 计算协作成功率
        if self.cooperation_metrics["coordination_attempts"] > 0:
            self.cooperation_metrics["coordination_rate"] = (
                self.cooperation_metrics["coordination_success"] / 
                self.cooperation_metrics["coordination_attempts"]
            )
        
        # 计算联合出牌成功率
        if self.cooperation_metrics["joint_play_attempts"] > 0:
            self.cooperation_metrics["joint_success_rate"] = (
                self.cooperation_metrics["joint_play_success"] / 
                self.cooperation_metrics["joint_play_attempts"]
            )
        
        return self.cooperation_metrics
    
    def _save_result(self, result: Dict[str, Any]) -> None:
        """
        保存评估结果
        
        Args:
            result (Dict[str, Any]): 评估结果
        """
        # 保存为JSON
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        json_file = os.path.join(self.data_dir, f"evaluation_{timestamp}.json")
        with open(json_file, 'w') as f:
            json.dump(result, f, indent=2)
        
        # 追加到历史记录
        with open(self.history_file, 'a') as f:
            f.write(json.dumps(result) + '\n')
        
        self.logger.info(f"评估结果已保存至 {json_file}")
    
    def evaluate_against_baseline(self, env: Environment, agent: Agent, baseline_agents: List[Agent],
                               baseline_names: List[str], num_games: int, **kwargs) -> Dict[str, Any]:
        """
        与基准代理对比评估
        
        Args:
            env (Environment): 游戏环境
            agent (Agent): 要评估的代理
            baseline_agents (List[Agent]): 基准代理列表
            baseline_names (List[str]): 基准代理名称
            num_games (int): 每个基准的评估游戏数
            **kwargs: 其他参数
            
        Returns:
            Dict[str, Any]: 评估结果
        """
        start_time = time.time()
        results = {}
        
        self.logger.info(f"开始与 {len(baseline_agents)} 个基准代理进行对比评估")
        
        for i, (baseline, name) in enumerate(zip(baseline_agents, baseline_names)):
            self.logger.info(f"评估对比 [{i+1}/{len(baseline_agents)}]: {name}")
            
            # 创建代理列表
            if env.num_players == 2:
                # 二人游戏，直接对抗
                eval_agents = [agent, baseline]
                agent_names = ["被评估代理", name]
            else:
                # 多人游戏，将基准代理放在其他位置
                eval_agents = [agent] * env.num_players
                eval_agents[1] = baseline  # 假设第二个位置放基准代理
                agent_names = ["被评估代理"] * env.num_players
                agent_names[1] = name
            
            # 评估
            baseline_result = self.evaluate(
                env=env,
                agents=eval_agents,
                num_games=num_games,
                agent_names=agent_names,
                **kwargs
            )
            
            # 标记基准名称
            baseline_result['baseline_name'] = name
            
            # 添加到结果
            results[name] = baseline_result
        
        # 汇总结果
        summary = {
            'baselines': baseline_names,
            'num_games_per_baseline': num_games,
            'total_games': num_games * len(baseline_agents),
            'results': results,
            'timestamp': datetime.datetime.now().isoformat(),
            'total_time': time.time() - start_time
        }
        
        # 计算胜率汇总
        win_rates = {}
        for name, result in results.items():
            # 假设第一个代理是被评估的代理
            win_rates[name] = result['player_0_win_rate']
        
        summary['win_rates'] = win_rates
        summary['average_win_rate'] = np.mean(list(win_rates.values()))
        
        # 保存汇总结果
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        json_file = os.path.join(self.data_dir, f"baseline_comparison_{timestamp}.json")
        with open(json_file, 'w') as f:
            json.dump(summary, f, indent=2)
        
        self.logger.info(
            f"基准对比评估完成 | "
            f"平均胜率: {summary['average_win_rate']:.4f} | "
            f"总时间: {summary['total_time']:.2f}s"
        )
        
        return summary
    
    def evaluate_self_play(self, env: Environment, agent: Agent, num_games: int,
                        agent_positions: Optional[List[int]] = None, **kwargs) -> Dict[str, Any]:
        """
        自我对弈评估
        
        Args:
            env (Environment): 游戏环境
            agent (Agent): 要评估的代理
            num_games (int): 评估的游戏数
            agent_positions (Optional[List[int]], optional): 代理在哪些位置. Defaults to None.
            **kwargs: 其他参数
            
        Returns:
            Dict[str, Any]: 评估结果
        """
        # 创建代理列表
        agents = [agent] * env.num_players
        
        # 如果指定了位置，则只在特定位置使用被评估代理
        if agent_positions:
            # 创建随机代理
            from cardgame_ai.core.agent import RandomAgent
            random_agents = [RandomAgent() for _ in range(env.num_players)]
            
            # 在指定位置放置被评估代理
            for pos in agent_positions:
                if 0 <= pos < env.num_players:
                    random_agents[pos] = agent
            
            agents = random_agents
        
        # 评估
        result = self.evaluate(
            env=env,
            agents=agents,
            num_games=num_games,
            **kwargs
        )
        
        # 添加自我对弈标记
        result['evaluation_type'] = 'self_play'
        if agent_positions:
            result['agent_positions'] = agent_positions
        
        # 计算额外指标
        if env.num_players > 2:
            # 多人游戏，计算代理之间的胜率分布
            win_distribution = {}
            for i in range(env.num_players):
                win_key = f'player_{i}_win_rate'
                if win_key in result:
                    win_distribution[i] = result[win_key]
            
            result['win_distribution'] = win_distribution
            
            # 计算胜率偏差（理想情况下应该均匀分布）
            ideal_win_rate = 1.0 / env.num_players
            win_rate_deviation = np.std(list(win_distribution.values()))
            result['win_rate_deviation'] = win_rate_deviation
            result['normalized_deviation'] = win_rate_deviation / ideal_win_rate
        
        return result
    
    def evaluate_historical(self, env: Environment, agent: Agent, historical_agents: List[Agent],
                         version_names: List[str], num_games: int, **kwargs) -> Dict[str, Any]:
        """
        与历史版本对比评估
        
        Args:
            env (Environment): 游戏环境
            agent (Agent): 要评估的当前代理
            historical_agents (List[Agent]): 历史版本代理列表
            version_names (List[str]): 历史版本名称
            num_games (int): 每个历史版本的评估游戏数
            **kwargs: 其他参数
            
        Returns:
            Dict[str, Any]: 评估结果
        """
        # 实现与与基准对比评估类似，但强调这是历史版本比较
        results = self.evaluate_against_baseline(
            env=env,
            agent=agent,
            baseline_agents=historical_agents,
            baseline_names=version_names,
            num_games=num_games,
            **kwargs
        )
        
        # 修改结果类型标记
        results['evaluation_type'] = 'historical_comparison'
        
        # 计算改进率
        if len(historical_agents) > 0:
            current_win_rates = results['win_rates']
            
            # 与最近的历史版本比较
            latest_version = version_names[-1]
            latest_win_rate = current_win_rates[latest_version]
            
            # 与最早的历史版本比较
            earliest_version = version_names[0]
            earliest_win_rate = current_win_rates[earliest_version]
            
            # 计算提升百分比
            results['improvement_over_latest'] = (latest_win_rate - 0.5) * 2  # 转换为-1到1范围
            results['improvement_over_earliest'] = (earliest_win_rate - 0.5) * 2
        
        return results
    
    def visualize(self, metrics: Dict[str, Any], **kwargs) -> None:
        """
        可视化评估结果
        
        Args:
            metrics (Dict[str, Any]): 评估指标
            **kwargs: 其他参数
        """
        # 调用基类的可视化方法
        super().visualize(metrics, **kwargs)
        
        # 添加更多可视化
        self._visualize_detailed_metrics(metrics, **kwargs)
    
    def _visualize_detailed_metrics(self, metrics: Dict[str, Any], **kwargs) -> None:
        """
        可视化详细指标
        
        Args:
            metrics (Dict[str, Any]): 评估指标
            **kwargs: 其他参数
        """
        # 创建图形
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 游戏长度分布（如果有）
        if 'game_lengths' in metrics:
            plt.figure(figsize=(10, 6))
            sns.histplot(metrics['game_lengths'], kde=True)
            plt.title('Game Length Distribution')
            plt.xlabel('Game Length (steps)')
            plt.ylabel('Frequency')
            plt.grid(True, alpha=0.3)
            save_path = os.path.join(self.plots_dir, f"game_length_dist_{timestamp}.png")
            plt.savefig(save_path)
            plt.close()
        
        # 奖励分布（如果有）
        if 'game_rewards' in metrics:
            plt.figure(figsize=(12, 8))
            rewards_array = np.array(metrics['game_rewards'])
            
            # 为每个玩家绘制奖励分布
            for i in range(rewards_array.shape[1]):
                sns.kdeplot(rewards_array[:, i], label=f'Player {i}')
            
            plt.title('Reward Distribution by Player')
            plt.xlabel('Reward')
            plt.ylabel('Density')
            plt.legend()
            plt.grid(True, alpha=0.3)
            save_path = os.path.join(self.plots_dir, f"reward_dist_{timestamp}.png")
            plt.savefig(save_path)
            plt.close()
        
        # 如果是历史比较，绘制历史趋势
        if len(self.history) > 1:
            self._visualize_historical_trends()
    
    def _visualize_historical_trends(self) -> None:
        """
        可视化历史评估趋势
        """
        # 确保有足够的历史数据
        if len(self.history) < 2:
            return
        
        # 提取时间和指标
        timestamps = []
        win_rates = []
        mean_rewards = []
        
        for entry in self.history:
            # 尝试解析时间戳
            try:
                timestamp = datetime.datetime.fromisoformat(entry['timestamp'])
                timestamps.append(timestamp)
            except (KeyError, ValueError):
                # 如果没有时间戳或格式不正确，则使用当前时间
                timestamps.append(datetime.datetime.now())
            
            # 尝试提取胜率（假设是第一个玩家）
            try:
                win_rates.append(entry['player_0_win_rate'])
            except KeyError:
                win_rates.append(None)
            
            # 尝试提取平均奖励
            try:
                mean_rewards.append(entry['mean_rewards'][0])
            except (KeyError, IndexError):
                mean_rewards.append(None)
        
        # 创建图形
        plt.figure(figsize=(12, 8))
        
        # 绘制胜率趋势
        if all(wr is not None for wr in win_rates):
            plt.plot(timestamps, win_rates, 'o-', label='Win Rate (Player 0)')
        
        # 绘制平均奖励趋势
        if all(mr is not None for mr in mean_rewards):
            plt.plot(timestamps, mean_rewards, 's-', label='Mean Reward (Player 0)')
        
        plt.title('Performance Trends Over Time')
        plt.xlabel('Evaluation Time')
        plt.ylabel('Metric Value')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 格式化x轴日期
        plt.gcf().autofmt_xdate()
        
        # 保存图形
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        save_path = os.path.join(self.plots_dir, f"performance_trend_{timestamp}.png")
        plt.savefig(save_path)
        plt.close()
        
        self.logger.info(f"历史趋势图已保存至 {save_path}")
    
    def visualize_tournament(self, metrics: Dict[str, Any], agent_names: Optional[List[str]] = None, **kwargs) -> None:
        """
        可视化锦标赛结果
        
        Args:
            metrics (Dict[str, Any]): 锦标赛指标
            agent_names (Optional[List[str]], optional): 代理名称. Defaults to None.
            **kwargs: 其他参数
        """
        if 'win_matrix' not in metrics or 'payoff_matrix' not in metrics:
            self.logger.warning("锦标赛结果中缺少胜率矩阵或收益矩阵，无法可视化")
            return
        
        win_matrix = metrics['win_matrix']
        payoff_matrix = metrics['payoff_matrix']
        
        # 确定代理名称
        if agent_names is None:
            num_agents = win_matrix.shape[0]
            agent_names = [f"Agent {i}" for i in range(num_agents)]
        
        # 创建热力图
        fig, axes = plt.subplots(1, 2, figsize=(16, 7))
        
        # 胜率矩阵热力图
        sns.heatmap(win_matrix, annot=True, fmt=".2f", cmap="YlGnBu", 
                   xticklabels=agent_names, yticklabels=agent_names, ax=axes[0])
        axes[0].set_title("Win Rate Matrix")
        axes[0].set_xlabel("Opponent")
        axes[0].set_ylabel("Agent")
        
        # 收益矩阵热力图
        sns.heatmap(payoff_matrix, annot=True, fmt=".2f", cmap="RdYlGn", 
                   xticklabels=agent_names, yticklabels=agent_names, ax=axes[1])
        axes[1].set_title("Payoff Matrix")
        axes[1].set_xlabel("Opponent")
        axes[1].set_ylabel("Agent")
        
        plt.tight_layout()
        
        # 保存图形
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        save_path = os.path.join(self.plots_dir, f"tournament_results_{timestamp}.png")
        plt.savefig(save_path)
        plt.close()
        
        self.logger.info(f"锦标赛结果图已保存至 {save_path}")
        
        # 创建排名条形图
        plt.figure(figsize=(10, 6))
        
        # 计算总胜率
        win_rates = win_matrix.mean(axis=1)
        ranking = np.argsort(win_rates)[::-1]  # 从高到低排序
        
        plt.bar(range(len(agent_names)), win_rates[ranking])
        plt.xticks(range(len(agent_names)), [agent_names[i] for i in ranking], rotation=45)
        plt.title("Agent Ranking by Average Win Rate")
        plt.xlabel("Agent")
        plt.ylabel("Average Win Rate")
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        # 保存排名图
        save_path = os.path.join(self.plots_dir, f"tournament_ranking_{timestamp}.png")
        plt.savefig(save_path)
        plt.close()
        
        self.logger.info(f"锦标赛排名图已保存至 {save_path}")


class GameAnalyzer:
    """
    游戏分析器
    
    分析游戏记录，提取关键决策点、动作分布等信息。
    """
    
    def __init__(self, save_path: str = 'analysis'):
        """
        初始化游戏分析器
        
        Args:
            save_path (str, optional): 分析结果保存路径. Defaults to 'analysis'.
        """
        self.save_path = save_path
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 创建保存目录
        os.makedirs(save_path, exist_ok=True)
    
    def analyze_game_records(self, game_records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        分析游戏记录
        
        Args:
            game_records (List[Dict[str, Any]]): 游戏记录列表
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        if not game_records:
            self.logger.warning("没有游戏记录可分析")
            return {}
        
        result = {
            'num_games': len(game_records),
            'game_lengths': [],
            'winner_distribution': defaultdict(int),
            'action_distribution': defaultdict(int),
            'action_distribution_by_player': {},
            'decision_times': [],
            'decision_times_by_player': {}
        }
        
        # 遍历游戏记录
        for game_idx, record in enumerate(game_records):
            # 游戏长度
            if 'actions' in record:
                result['game_lengths'].append(len(record['actions']))
            
            # 获胜者分布
            if 'winner' in record:
                result['winner_distribution'][record['winner']] += 1
            
            # 动作分布
            if 'actions' in record:
                for action_entry in record['actions']:
                    if 'action' in action_entry:
                        action = action_entry['action']
                        result['action_distribution'][action] += 1
                        
                        # 按玩家统计动作分布
                        if 'player' in action_entry:
                            player = action_entry['player']
                            if player not in result['action_distribution_by_player']:
                                result['action_distribution_by_player'][player] = defaultdict(int)
                            result['action_distribution_by_player'][player][action] += 1
            
            # 决策时间
            if 'actions' in record and all('decision_time' in entry for entry in record['actions']):
                decision_times = [entry['decision_time'] for entry in record['actions'] if 'decision_time' in entry]
                result['decision_times'].extend(decision_times)
                
                # 按玩家统计决策时间
                for action_entry in record['actions']:
                    if 'player' in action_entry and 'decision_time' in action_entry:
                        player = action_entry['player']
                        if player not in result['decision_times_by_player']:
                            result['decision_times_by_player'][player] = []
                        result['decision_times_by_player'][player].append(action_entry['decision_time'])
        
        # 计算汇总统计
        if result['game_lengths']:
            result['mean_game_length'] = np.mean(result['game_lengths'])
            result['std_game_length'] = np.std(result['game_lengths'])
            result['min_game_length'] = min(result['game_lengths'])
            result['max_game_length'] = max(result['game_lengths'])
        
        if result['winner_distribution']:
            total_games = sum(result['winner_distribution'].values())
            result['winner_distribution_percentage'] = {
                player: count / total_games for player, count in result['winner_distribution'].items()
            }
        
        if result['decision_times']:
            result['mean_decision_time'] = np.mean(result['decision_times'])
            result['std_decision_time'] = np.std(result['decision_times'])
            
            # 按玩家计算平均决策时间
            result['mean_decision_time_by_player'] = {
                player: np.mean(times) for player, times in result['decision_times_by_player'].items()
            }
        
        # 保存分析结果
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        json_file = os.path.join(self.save_path, f"game_analysis_{timestamp}.json")
        
        with open(json_file, 'w') as f:
            json.dump(result, f, indent=2)
        
        self.logger.info(f"游戏分析结果已保存至 {json_file}")
        
        return result
    
    def visualize_analysis(self, analysis: Dict[str, Any]) -> None:
        """
        可视化分析结果
        
        Args:
            analysis (Dict[str, Any]): 分析结果
        """
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建图形
        plt.figure(figsize=(16, 12))
        plt.suptitle('Game Analysis Results', fontsize=16)
        
        # 绘制游戏长度分布
        if 'game_lengths' in analysis and analysis['game_lengths']:
            plt.subplot(2, 2, 1)
            sns.histplot(analysis['game_lengths'], kde=True)
            plt.title('Game Length Distribution')
            plt.xlabel('Game Length (steps)')
            plt.ylabel('Frequency')
            plt.grid(True, alpha=0.3)
        
        # 绘制获胜者分布
        if 'winner_distribution' in analysis and analysis['winner_distribution']:
            plt.subplot(2, 2, 2)
            players = list(analysis['winner_distribution'].keys())
            wins = list(analysis['winner_distribution'].values())
            plt.bar(players, wins)
            plt.title('Winner Distribution')
            plt.xlabel('Player')
            plt.ylabel('Number of Wins')
            plt.grid(True, alpha=0.3)
        
        # 绘制动作分布(前10种最常用的动作)
        if 'action_distribution' in analysis and analysis['action_distribution']:
            plt.subplot(2, 2, 3)
            actions = list(analysis['action_distribution'].keys())
            counts = list(analysis['action_distribution'].values())
            
            # 按频率排序并取前10
            sorted_indices = np.argsort(counts)[::-1][:10]
            top_actions = [actions[i] for i in sorted_indices]
            top_counts = [counts[i] for i in sorted_indices]
            
            plt.bar(range(len(top_actions)), top_counts)
            plt.xticks(range(len(top_actions)), top_actions, rotation=45)
            plt.title('Top 10 Action Distribution')
            plt.xlabel('Action')
            plt.ylabel('Frequency')
            plt.grid(True, alpha=0.3)
        
        # 绘制决策时间分布
        if 'decision_times' in analysis and analysis['decision_times']:
            plt.subplot(2, 2, 4)
            sns.histplot(analysis['decision_times'], kde=True)
            plt.title('Decision Time Distribution')
            plt.xlabel('Decision Time (s)')
            plt.ylabel('Frequency')
            plt.grid(True, alpha=0.3)
        
        plt.tight_layout(rect=[0, 0, 1, 0.96])
        
        # 保存图形
        save_path = os.path.join(self.save_path, f"game_analysis_viz_{timestamp}.png")
        plt.savefig(save_path)
        plt.close()
        
        # 如果有按玩家统计的数据，创建单独的图表
        if 'mean_decision_time_by_player' in analysis and analysis['mean_decision_time_by_player']:
            plt.figure(figsize=(12, 6))
            players = list(analysis['mean_decision_time_by_player'].keys())
            times = list(analysis['mean_decision_time_by_player'].values())
            plt.bar(players, times)
            plt.title('Mean Decision Time by Player')
            plt.xlabel('Player')
            plt.ylabel('Mean Decision Time (s)')
            plt.grid(True, alpha=0.3)
            
            save_path = os.path.join(self.save_path, f"decision_time_by_player_{timestamp}.png")
            plt.savefig(save_path)
            plt.close()
        
        self.logger.info(f"分析可视化结果已保存至 {self.save_path}") 