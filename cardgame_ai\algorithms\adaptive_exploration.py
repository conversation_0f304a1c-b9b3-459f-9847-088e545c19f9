"""
自适应探索模块

实现自适应探索策略，根据当前状态和历史表现动态调整探索行为，提高样本效率。
"""
import os
import logging
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Any, Optional, Union, Callable
from collections import defaultdict

from cardgame_ai.core.base import State, Action
from cardgame_ai.utils.logger import get_logger

logger = logging.getLogger(__name__)


class AdaptiveExploration:
    """
    自适应探索
    
    根据当前游戏状态、不确定性和历史表现动态调整探索策略，平衡探索和利用。
    """
    
    def __init__(
        self,
        exploration_network: Optional[nn.Module] = None,
        initial_temperature: float = 1.0,
        min_temperature: float = 0.1,
        max_temperature: float = 5.0,
        decay_rate: float = 0.99,
        uncertainty_weight: float = 0.5,
        novelty_weight: float = 0.3,
        value_weight: float = 0.2,
        device: str = 'cuda' if torch.cuda.is_available() else 'cpu'
    ):
        """
        初始化自适应探索
        
        Args:
            exploration_network: 用于预测探索参数的网络
            initial_temperature: 初始温度参数
            min_temperature: 最小温度
            max_temperature: 最大温度
            decay_rate: 温度衰减率
            uncertainty_weight: 不确定性权重
            novelty_weight: 新颖性权重
            value_weight: 价值权重
            device: 计算设备
        """
        self.exploration_network = exploration_network
        if exploration_network is not None:
            self.exploration_network = exploration_network.to(device)
            self.optimizer = torch.optim.Adam(self.exploration_network.parameters(), lr=0.001)
        
        self.temperature = initial_temperature
        self.min_temperature = min_temperature
        self.max_temperature = max_temperature
        self.decay_rate = decay_rate
        self.uncertainty_weight = uncertainty_weight
        self.novelty_weight = novelty_weight
        self.value_weight = value_weight
        self.device = device
        
        # 状态访问计数
        self.state_visit_counts = defaultdict(int)
        
        # 记忆缓冲区，用于记录状态嵌入
        self.state_buffer = []
        self.buffer_capacity = 1000
        
        # 统计信息
        self.stats = {
            "temperature_history": [initial_temperature],
            "exploration_vs_exploitation": [],  # 1表示探索，0表示利用
            "exploration_rewards": [],
            "exploitation_rewards": [],
            "updates": 0
        }
        
        logger.info(f"自适应探索初始化完成，初始温度: {initial_temperature}")
    
    def compute_exploration_factor(
        self,
        state: torch.Tensor,
        logits: torch.Tensor,
        embedding: Optional[torch.Tensor] = None
    ) -> Tuple[float, Dict[str, float]]:
        """
        计算探索因子
        
        Args:
            state: 状态张量
            logits: 策略logits
            embedding: 状态嵌入，用于计算新颖性
            
        Returns:
            (探索因子, 详细指标)
        """
        # 计算策略不确定性（熵）
        probs = F.softmax(logits, dim=-1)
        entropy = -torch.sum(probs * torch.log(probs + 1e-10), dim=-1)
        uncertainty = entropy.mean().item()
        
        # 计算新颖性
        novelty = 0.0
        if embedding is not None:
            novelty = self._compute_novelty(embedding)
        
        # 计算价值
        value = self._get_state_value(state)
        
        # 综合计算探索因子
        exploration_factor = (
            self.uncertainty_weight * uncertainty +
            self.novelty_weight * novelty +
            self.value_weight * (1.0 - value)  # 低价值状态更需要探索
        )
        
        return exploration_factor, {
            "uncertainty": uncertainty,
            "novelty": novelty,
            "value": value
        }
    
    def _compute_novelty(self, embedding: torch.Tensor) -> float:
        """
        计算状态新颖性
        
        Args:
            embedding: 状态嵌入
            
        Returns:
            新颖性得分
        """
        if not self.state_buffer:
            # 缓冲区为空，认为是完全新颖的状态
            return 1.0
        
        # 计算与缓冲区中所有状态的平均相似度
        similarities = []
        for stored_embedding in self.state_buffer:
            cos_sim = F.cosine_similarity(embedding, stored_embedding, dim=0)
            similarities.append(cos_sim.item())
        
        # 相似度越低，新颖性越高
        avg_similarity = np.mean(similarities)
        novelty = 1.0 - avg_similarity
        
        # 更新状态缓冲区
        self._update_state_buffer(embedding)
        
        return novelty
    
    def _update_state_buffer(self, embedding: torch.Tensor) -> None:
        """
        更新状态缓冲区
        
        Args:
            embedding: 状态嵌入
        """
        if len(self.state_buffer) >= self.buffer_capacity:
            # 随机移除一个状态
            idx = np.random.randint(0, len(self.state_buffer))
            self.state_buffer.pop(idx)
        
        # 添加新状态
        self.state_buffer.append(embedding.detach().cpu())
    
    def _get_state_value(self, state: torch.Tensor) -> float:
        """
        获取状态价值估计
        
        Args:
            state: 状态张量
            
        Returns:
            状态价值估计
        """
        # 如果使用探索网络，获取价值预测
        if self.exploration_network is not None:
            with torch.no_grad():
                value = self.exploration_network(state).item()
            return value
        
        # 默认返回中等价值
        return 0.5
    
    def get_temperature(self, state: torch.Tensor, logits: torch.Tensor, embedding: Optional[torch.Tensor] = None) -> float:
        """
        获取当前温度参数
        
        Args:
            state: 状态张量
            logits: 策略logits
            embedding: 状态嵌入
            
        Returns:
            温度参数
        """
        # 如果使用探索网络，直接预测温度
        if self.exploration_network is not None:
            with torch.no_grad():
                # 假设网络输出温度在[0,1]范围内
                raw_temp = self.exploration_network(state).item()
                # 映射到温度范围
                temp = self.min_temperature + raw_temp * (self.max_temperature - self.min_temperature)
                return temp
        
        # 计算探索因子
        exploration_factor, _ = self.compute_exploration_factor(state, logits, embedding)
        
        # 根据探索因子调整温度
        adjusted_temp = self.temperature * (1.0 + exploration_factor)
        
        # 限制在合理范围内
        adjusted_temp = max(self.min_temperature, min(self.max_temperature, adjusted_temp))
        
        return adjusted_temp
    
    def select_action(
        self,
        state: torch.Tensor,
        logits: torch.Tensor,
        embedding: Optional[torch.Tensor] = None,
        is_training: bool = True
    ) -> Tuple[int, bool]:
        """
        选择动作
        
        Args:
            state: 状态张量
            logits: 策略logits
            embedding: 状态嵌入
            is_training: 是否处于训练模式
            
        Returns:
            (选择的动作索引, 是否为探索)
        """
        # 确保输入在正确的设备上
        state = state.to(self.device)
        logits = logits.to(self.device)
        if embedding is not None:
            embedding = embedding.to(self.device)
        
        # 获取当前温度
        if is_training:
            temperature = self.get_temperature(state, logits, embedding)
        else:
            # 测试模式使用最小温度
            temperature = self.min_temperature
        
        # 记录温度历史
        self.stats["temperature_history"].append(temperature)
        
        # 使用温度参数调整logits
        scaled_logits = logits / temperature
        
        # 计算动作概率
        probs = F.softmax(scaled_logits, dim=-1)
        
        # 选择动作
        if is_training:
            # 训练模式：采样动作
            action_dist = torch.distributions.Categorical(probs)
            action = action_dist.sample().item()
            
            # 确定是探索还是利用
            greedy_action = torch.argmax(logits).item()
            is_exploration = (action != greedy_action)
            
            # 更新统计信息
            self.stats["exploration_vs_exploitation"].append(int(is_exploration))
            
            # 更新状态访问计数
            state_key = self._get_state_key(state)
            self.state_visit_counts[state_key] += 1
        else:
            # 测试模式：贪婪选择
            action = torch.argmax(logits).item()
            is_exploration = False
        
        return action, is_exploration
    
    def update(
        self,
        states: torch.Tensor,
        actions: torch.Tensor,
        rewards: torch.Tensor,
        is_explorations: List[bool]
    ) -> Dict[str, float]:
        """
        更新探索网络
        
        Args:
            states: 状态张量
            actions: 动作张量
            rewards: 奖励张量
            is_explorations: 是否为探索的标记列表
            
        Returns:
            更新指标
        """
        if self.exploration_network is None:
            # 没有探索网络，只进行温度衰减
            self._decay_temperature()
            
            # 记录探索和利用的奖励
            for i, is_exploration in enumerate(is_explorations):
                if is_exploration:
                    self.stats["exploration_rewards"].append(rewards[i].item())
                else:
                    self.stats["exploitation_rewards"].append(rewards[i].item())
            
            return {"exploration_loss": 0.0}
        
        # 确保输入在正确的设备上
        states = states.to(self.device)
        actions = actions.to(self.device)
        rewards = rewards.to(self.device)
        is_explorations = torch.tensor(is_explorations, dtype=torch.float32, device=self.device)
        
        # 网络预测的温度参数
        predicted_temps = self.exploration_network(states)
        
        # 计算损失
        # 探索获得高奖励时，增加温度；利用获得高奖励时，降低温度
        exploration_rewards = rewards * is_explorations
        exploitation_rewards = rewards * (1 - is_explorations)
        
        # 目标温度：基于奖励的加权平均
        target_temps = (
            self.min_temperature + 
            (self.max_temperature - self.min_temperature) * 
            (exploration_rewards - exploitation_rewards + 1) / 2  # 映射到[0,1]
        )
        
        # 均方误差损失
        loss = F.mse_loss(predicted_temps.squeeze(), target_temps)
        
        # 更新网络
        self.optimizer.zero_grad()
        loss.backward()
        self.optimizer.step()
        
        # 记录探索和利用的奖励
        for i, is_exploration in enumerate(is_explorations):
            if is_exploration:
                self.stats["exploration_rewards"].append(rewards[i].item())
            else:
                self.stats["exploitation_rewards"].append(rewards[i].item())
        
        # 更新统计信息
        self.stats["updates"] += 1
        
        return {"exploration_loss": loss.item()}
    
    def _decay_temperature(self) -> None:
        """
        衰减温度参数
        """
        self.temperature = max(self.min_temperature, self.temperature * self.decay_rate)
    
    def _get_state_key(self, state: torch.Tensor) -> str:
        """
        获取状态的唯一标识
        
        Args:
            state: 状态张量
            
        Returns:
            状态标识
        """
        # 简单哈希方法，实际应用中可能需要更复杂的状态表示
        return str(hash(state.cpu().numpy().tobytes()))
    
    def save(self, path: str) -> None:
        """
        保存模型
        
        Args:
            path: 保存路径
        """
        state_dict = {
            "temperature": self.temperature,
            "min_temperature": self.min_temperature,
            "max_temperature": self.max_temperature,
            "decay_rate": self.decay_rate,
            "uncertainty_weight": self.uncertainty_weight,
            "novelty_weight": self.novelty_weight,
            "value_weight": self.value_weight,
            "state_visit_counts": dict(self.state_visit_counts),
            "state_buffer": self.state_buffer,
            "stats": self.stats
        }
        
        if self.exploration_network is not None:
            state_dict["exploration_network"] = self.exploration_network.state_dict()
            state_dict["optimizer"] = self.optimizer.state_dict()
        
        torch.save(state_dict, path)
        logger.info(f"自适应探索模型已保存到 {path}")
    
    def load(self, path: str) -> None:
        """
        加载模型
        
        Args:
            path: 加载路径
        """
        if not os.path.exists(path):
            logger.warning(f"模型文件不存在: {path}")
            return
            
        try:
            state_dict = torch.load(path, map_location=self.device)
            
            self.temperature = state_dict["temperature"]
            self.min_temperature = state_dict["min_temperature"]
            self.max_temperature = state_dict["max_temperature"]
            self.decay_rate = state_dict["decay_rate"]
            self.uncertainty_weight = state_dict["uncertainty_weight"]
            self.novelty_weight = state_dict["novelty_weight"]
            self.value_weight = state_dict["value_weight"]
            self.state_visit_counts = defaultdict(int, state_dict["state_visit_counts"])
            self.state_buffer = state_dict["state_buffer"]
            self.stats = state_dict["stats"]
            
            if "exploration_network" in state_dict and self.exploration_network is not None:
                self.exploration_network.load_state_dict(state_dict["exploration_network"])
                self.optimizer.load_state_dict(state_dict["optimizer"])
            
            logger.info(f"自适应探索模型已从 {path} 加载")
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            统计信息字典
        """
        # 计算最近温度的平均值
        avg_temperature = np.mean(self.stats["temperature_history"][-100:])
        
        # 计算探索率
        exploration_rate = np.mean(self.stats["exploration_vs_exploitation"][-100:]) if self.stats["exploration_vs_exploitation"] else 0.0
        
        # 计算平均奖励
        avg_exploration_reward = np.mean(self.stats["exploration_rewards"][-100:]) if self.stats["exploration_rewards"] else 0.0
        avg_exploitation_reward = np.mean(self.stats["exploitation_rewards"][-100:]) if self.stats["exploitation_rewards"] else 0.0
        
        return {
            "avg_temperature": avg_temperature,
            "exploration_rate": exploration_rate,
            "avg_exploration_reward": avg_exploration_reward,
            "avg_exploitation_reward": avg_exploitation_reward,
            "updates": self.stats["updates"]
        }