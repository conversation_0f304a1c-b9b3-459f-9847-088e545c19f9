"""
DQN算法示例：CartPole环境

使用DQN算法在CartPole环境中训练智能体，展示DQN算法的基本用法。
"""
import gym
import numpy as np
import matplotlib.pyplot as plt
from typing import List, Tuple, Dict
import torch
import os
import time

from cardgame_ai.algorithms.dqn import DQN
from cardgame_ai.core.base import Experience
from cardgame_ai.algorithms.exploration import EpsilonGreedy


def test_dqn_cartpole():
    """
    在CartPole环境中测试DQN算法
    """
    # 创建环境
    env = gym.make('CartPole-v1')
    
    # 获取状态和动作空间
    state_shape = env.observation_space.shape
    action_shape = (env.action_space.n,)
    
    # 创建DQN算法
    dqn = DQN(
        state_shape=state_shape,
        action_shape=action_shape,
        hidden_dims=[128, 128],
        learning_rate=0.001,
        buffer_size=10000,
        batch_size=64,
        gamma=0.99,
        tau=0.005,
        update_frequency=1,
        target_update_frequency=100
    )
    
    # 创建探索策略
    exploration = EpsilonGreedy(epsilon=1.0, epsilon_decay=0.995, epsilon_min=0.01)
    
    # 训练参数
    num_episodes = 500
    max_steps = 500
    
    # 记录奖励
    rewards_history = []
    avg_rewards_history = []
    
    # 训练循环
    for episode in range(num_episodes):
        # 重置环境
        state = env.reset()
        total_reward = 0
        
        # 每个回合的步骤
        for step in range(max_steps):
            # 预测动作概率
            action_probs, _ = dqn.predict(state)
            
            # 使用探索策略选择动作
            action = exploration.select_action(np.array(action_probs))
            
            # 执行动作
            next_state, reward, done, _ = env.step(action)
            
            # 存储经验
            experience = Experience(
                state=state,
                action=action,
                reward=reward,
                next_state=next_state,
                done=done
            )
            
            # 更新模型
            dqn.update(experience)
            
            # 更新状态和奖励
            state = next_state
            total_reward += reward
            
            # 如果回合结束，则跳出循环
            if done:
                break
        
        # 更新探索策略
        exploration.update()
        
        # 记录奖励
        rewards_history.append(total_reward)
        avg_reward = np.mean(rewards_history[-100:])
        avg_rewards_history.append(avg_reward)
        
        # 打印进度
        if (episode + 1) % 10 == 0:
            print(f"Episode {episode + 1}/{num_episodes}, Reward: {total_reward:.2f}, Avg Reward: {avg_reward:.2f}, Epsilon: {exploration.epsilon:.4f}")
        
        # 如果平均奖励达到目标，则提前结束
        if avg_reward >= 475.0:
            print(f"环境已解决！在第 {episode + 1} 回合达到平均奖励 {avg_reward:.2f}")
            break
    
    # 关闭环境
    env.close()
    
    # 绘制奖励曲线
    plt.figure(figsize=(10, 5))
    plt.plot(rewards_history, label='奖励')
    plt.plot(avg_rewards_history, label='平均奖励', color='red')
    plt.xlabel('回合')
    plt.ylabel('奖励')
    plt.title('DQN在CartPole环境中的训练奖励')
    plt.legend()
    plt.grid()
    
    # 创建保存目录
    os.makedirs('results', exist_ok=True)
    plt.savefig('results/dqn_cartpole_rewards.png')
    plt.show()
    
    # 测试训练好的模型
    test_model(env, dqn)


def test_model(env: gym.Env, dqn: DQN, num_episodes: int = 5):
    """
    测试训练好的模型
    
    Args:
        env (gym.Env): 环境
        dqn (DQN): DQN算法
        num_episodes (int, optional): 测试回合数. Defaults to 5.
    """
    print("\n正在测试训练好的模型...")
    
    for episode in range(num_episodes):
        state = env.reset()
        total_reward = 0
        done = False
        
        while not done:
            # 渲染环境
            env.render()
            
            # 使用模型选择动作
            action_probs, _ = dqn.predict(state)
            action = np.argmax(action_probs)
            
            # 执行动作
            state, reward, done, _ = env.step(action)
            total_reward += reward
            
            # 减慢速度，便于观察
            time.sleep(0.01)
        
        print(f"测试回合 {episode + 1}/{num_episodes}, 奖励: {total_reward}")
    
    env.close()


if __name__ == "__main__":
    test_dqn_cartpole() 