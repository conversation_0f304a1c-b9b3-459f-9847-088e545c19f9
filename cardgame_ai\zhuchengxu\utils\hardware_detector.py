"""
硬件检测器模块

自动检测服务器硬件配置，包括GPU、CPU、内存等信息，
为自动化配置管理提供基础数据。

主要功能:
- GPU检测：数量、型号、显存大小、计算能力
- CPU检测：核心数、线程数、内存大小
- 系统检测：操作系统、CUDA版本、驱动版本
- 性能评估：硬件性能等级评估
"""

import torch
import psutil
import platform
import subprocess
import json
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict


@dataclass
class GPUInfo:
    """GPU信息数据类"""
    index: int
    name: str
    memory_total: int  # MB
    memory_free: int   # MB
    compute_capability: Tuple[int, int]
    driver_version: str
    cuda_version: str


@dataclass
class CPUInfo:
    """CPU信息数据类"""
    cores_physical: int
    cores_logical: int
    frequency_max: float  # GHz
    memory_total: int     # GB
    memory_available: int # GB


@dataclass
class SystemInfo:
    """系统信息数据类"""
    os_name: str
    os_version: str
    python_version: str
    pytorch_version: str
    cuda_available: bool
    cuda_version: Optional[str]


@dataclass
class HardwareProfile:
    """完整硬件配置文件"""
    gpus: List[GPUInfo]
    cpu: CPUInfo
    system: SystemInfo
    performance_tier: str  # "low", "medium", "high", "ultra"
    recommended_config: Dict


class HardwareDetector:
    """硬件检测器主类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def detect_gpus(self) -> List[GPUInfo]:
        """检测GPU信息"""
        gpus = []
        
        if not torch.cuda.is_available():
            self.logger.warning("CUDA不可用，未检测到GPU")
            return gpus
            
        try:
            for i in range(torch.cuda.device_count()):
                # 获取GPU基本信息
                props = torch.cuda.get_device_properties(i)
                
                # 获取显存信息
                torch.cuda.set_device(i)
                memory_total = torch.cuda.get_device_properties(i).total_memory // (1024**2)
                memory_free = torch.cuda.memory_reserved(i) // (1024**2)
                
                # 获取CUDA和驱动版本
                cuda_version = torch.version.cuda
                driver_version = self._get_nvidia_driver_version()
                
                gpu_info = GPUInfo(
                    index=i,
                    name=props.name,
                    memory_total=memory_total,
                    memory_free=memory_free,
                    compute_capability=(props.major, props.minor),
                    driver_version=driver_version,
                    cuda_version=cuda_version
                )
                gpus.append(gpu_info)
                
        except Exception as e:
            self.logger.error(f"GPU检测失败: {e}")
            
        return gpus
    
    def detect_cpu(self) -> CPUInfo:
        """检测CPU信息"""
        try:
            # CPU核心信息
            cores_physical = psutil.cpu_count(logical=False)
            cores_logical = psutil.cpu_count(logical=True)
            
            # CPU频率信息
            freq_info = psutil.cpu_freq()
            frequency_max = freq_info.max / 1000 if freq_info else 0  # 转换为GHz
            
            # 内存信息
            memory = psutil.virtual_memory()
            memory_total = memory.total // (1024**3)  # 转换为GB
            memory_available = memory.available // (1024**3)
            
            return CPUInfo(
                cores_physical=cores_physical,
                cores_logical=cores_logical,
                frequency_max=frequency_max,
                memory_total=memory_total,
                memory_available=memory_available
            )
            
        except Exception as e:
            self.logger.error(f"CPU检测失败: {e}")
            return CPUInfo(0, 0, 0.0, 0, 0)
    
    def detect_system(self) -> SystemInfo:
        """检测系统信息"""
        try:
            return SystemInfo(
                os_name=platform.system(),
                os_version=platform.version(),
                python_version=platform.python_version(),
                pytorch_version=torch.__version__,
                cuda_available=torch.cuda.is_available(),
                cuda_version=torch.version.cuda if torch.cuda.is_available() else None
            )
        except Exception as e:
            self.logger.error(f"系统检测失败: {e}")
            return SystemInfo("Unknown", "Unknown", "Unknown", "Unknown", False, None)
    
    def _get_nvidia_driver_version(self) -> str:
        """获取NVIDIA驱动版本"""
        try:
            result = subprocess.run(['nvidia-smi', '--query-gpu=driver_version', 
                                   '--format=csv,noheader,nounits'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                return result.stdout.strip().split('\n')[0]
        except Exception:
            pass
        return "Unknown"
    
    def evaluate_performance_tier(self, gpus: List[GPUInfo], cpu: CPUInfo) -> str:
        """评估硬件性能等级"""
        if not gpus:
            return "cpu_only"
            
        # 计算GPU性能分数
        gpu_score = 0
        for gpu in gpus:
            # 基于显存大小和GPU型号评分
            memory_score = gpu.memory_total / 1000  # 每GB显存1分
            
            # GPU型号加权
            if "A100" in gpu.name:
                model_score = 10
            elif "4090" in gpu.name:
                model_score = 8
            elif "3080" in gpu.name:
                model_score = 6
            elif "V100" in gpu.name:
                model_score = 7
            else:
                model_score = 3
                
            gpu_score += memory_score + model_score
        
        # 根据分数确定性能等级
        if gpu_score >= 50:  # 4x A100级别
            return "ultra"
        elif gpu_score >= 25:  # 2x 4090级别
            return "high"
        elif gpu_score >= 10:  # 1x 3080级别
            return "medium"
        else:
            return "low"
    
    def generate_hardware_profile(self) -> HardwareProfile:
        """生成完整的硬件配置文件"""
        gpus = self.detect_gpus()
        cpu = self.detect_cpu()
        system = self.detect_system()
        performance_tier = self.evaluate_performance_tier(gpus, cpu)
        
        # 生成推荐配置
        recommended_config = self._generate_recommended_config(gpus, cpu, performance_tier)
        
        return HardwareProfile(
            gpus=gpus,
            cpu=cpu,
            system=system,
            performance_tier=performance_tier,
            recommended_config=recommended_config
        )
    
    def _generate_recommended_config(self, gpus: List[GPUInfo], cpu: CPUInfo, tier: str) -> Dict:
        """根据硬件生成推荐配置"""
        config = {}
        
        if not gpus:
            # CPU-only配置
            config = {
                "device": {"type": "cpu"},
                "training": {"batch_size": 32, "num_workers": min(4, cpu.cores_logical)},
                "mcts": {"num_simulations": 25, "parallel_threads": 2}
            }
        else:
            # GPU配置
            total_memory = sum(gpu.memory_total for gpu in gpus)
            gpu_count = len(gpus)
            
            # 基于显存自动计算batch_size
            if total_memory >= 80000:  # 80GB+
                batch_size = 512
            elif total_memory >= 40000:  # 40GB+
                batch_size = 384
            elif total_memory >= 20000:  # 20GB+
                batch_size = 256
            else:
                batch_size = 128
                
            # 基于GPU数量调整MCTS参数
            mcts_sims = min(200, 50 + gpu_count * 25)
            parallel_threads = min(8, gpu_count * 2)
            
            # 基于CPU核心数调整数据加载
            num_workers = min(16, cpu.cores_logical)
            
            config = {
                "device": {
                    "type": "cuda",
                    "ids": list(range(gpu_count)),
                    "mixed_precision": True
                },
                "training": {
                    "batch_size": batch_size,
                    "num_workers": num_workers
                },
                "mcts": {
                    "num_simulations": mcts_sims,
                    "parallel_threads": parallel_threads
                },
                "distributed": {
                    "enabled": gpu_count > 1
                }
            }
        
        return config
    
    def save_profile(self, profile: HardwareProfile, filepath: str):
        """保存硬件配置文件"""
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(asdict(profile), f, indent=2, ensure_ascii=False)
            self.logger.info(f"硬件配置文件已保存: {filepath}")
        except Exception as e:
            self.logger.error(f"保存硬件配置文件失败: {e}")
    
    def load_profile(self, filepath: str) -> Optional[HardwareProfile]:
        """加载硬件配置文件"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 重构数据类对象
            gpus = [GPUInfo(**gpu) for gpu in data['gpus']]
            cpu = CPUInfo(**data['cpu'])
            system = SystemInfo(**data['system'])
            
            return HardwareProfile(
                gpus=gpus,
                cpu=cpu,
                system=system,
                performance_tier=data['performance_tier'],
                recommended_config=data['recommended_config']
            )
        except Exception as e:
            self.logger.error(f"加载硬件配置文件失败: {e}")
            return None


def main():
    """测试硬件检测功能"""
    detector = HardwareDetector()
    profile = detector.generate_hardware_profile()
    
    print("=== 硬件检测结果 ===")
    print(f"性能等级: {profile.performance_tier}")
    print(f"GPU数量: {len(profile.gpus)}")
    for gpu in profile.gpus:
        print(f"  - {gpu.name}: {gpu.memory_total}MB")
    print(f"CPU: {profile.cpu.cores_physical}核/{profile.cpu.cores_logical}线程")
    print(f"内存: {profile.cpu.memory_total}GB")
    print("\n=== 推荐配置 ===")
    print(json.dumps(profile.recommended_config, indent=2, ensure_ascii=False))


if __name__ == "__main__":
    main()
