#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
斗地主游戏多局训练测试脚本

这个脚本用于测试斗地主游戏的完整流程，包括发牌、叫地主、抢地主和出牌阶段。
使用DouDizhuSelfPlay类生成多局游戏的经验数据。
"""

import os
import sys
import time
import logging
import numpy as np
from collections import defaultdict

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.games.doudizhu.state import GamePhase
from cardgame_ai.training.doudizhu_self_play import DouDizhuSelfPlay


# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('doudizhu_multi_games')


class RandomAgent:
    """随机代理，用于测试"""
    
    def __init__(self, seed=None):
        """初始化随机代理"""
        self.rng = np.random.RandomState(seed)
    
    def act(self, observation, legal_actions, is_training=False, temperature=1.0):
        """随机选择一个合法动作"""
        return self.rng.choice(legal_actions)


def collect_statistics(experiences):
    """收集统计数据
    
    Args:
        experiences: 经验数据列表
        
    Returns:
        统计数据
    """
    stats = {
        'phase_counts': defaultdict(int),  # 各阶段的次数
        'bid_actions': defaultdict(int),   # 叫地主动作的次数
        'grab_actions': defaultdict(int),  # 抢地主动作的次数
        'bid_rewards': defaultdict(list),  # 叫地主动作的奖励
        'grab_rewards': defaultdict(list), # 抢地主动作的奖励
        'phase_transitions': defaultdict(int),  # 阶段转换的次数
        'landlord_win_rate': 0.0,          # 地主胜率
        'avg_game_length': 0.0,            # 平均游戏长度
    }
    
    # 游戏计数和长度
    game_count = 0
    game_lengths = []
    current_game_length = 0
    landlord_wins = 0
    
    # 上一个阶段
    last_phase = None
    
    for exp in experiences:
        state = exp['state']
        action = exp['action']
        reward = exp['reward']
        next_state = exp['next_state']
        done = exp['done']
        
        # 记录阶段
        current_phase = state.game_phase
        stats['phase_counts'][current_phase.name] += 1
        
        # 记录阶段转换
        if last_phase is not None and last_phase != current_phase:
            transition = f"{last_phase.name}_to_{current_phase.name}"
            stats['phase_transitions'][transition] += 1
        
        last_phase = current_phase
        
        # 记录动作和奖励
        if current_phase == GamePhase.BIDDING:
            action_name = action.name if hasattr(action, 'name') else str(action)
            stats['bid_actions'][action_name] += 1
            stats['bid_rewards'][action_name].append(reward)
        
        elif current_phase == GamePhase.GRABBING:
            action_name = action.name if hasattr(action, 'name') else str(action)
            stats['grab_actions'][action_name] += 1
            stats['grab_rewards'][action_name].append(reward)
        
        # 记录游戏长度
        current_game_length += 1
        
        # 如果游戏结束，更新统计数据
        if done:
            game_count += 1
            game_lengths.append(current_game_length)
            current_game_length = 0
            
            # 记录地主胜负
            if next_state.landlord is not None and next_state.get_payoffs()[next_state.landlord] > 0:
                landlord_wins += 1
    
    # 计算平均值
    if game_count > 0:
        stats['landlord_win_rate'] = landlord_wins / game_count
        stats['avg_game_length'] = sum(game_lengths) / game_count
    
    # 计算平均奖励
    for action, rewards in stats['bid_rewards'].items():
        if rewards:
            stats['bid_rewards'][action] = sum(rewards) / len(rewards)
    
    for action, rewards in stats['grab_rewards'].items():
        if rewards:
            stats['grab_rewards'][action] = sum(rewards) / len(rewards)
    
    return stats


def print_statistics(stats):
    """打印统计数据
    
    Args:
        stats: 统计数据
    """
    logger.info("游戏统计数据:")
    
    # 打印阶段计数
    logger.info("阶段计数:")
    for phase, count in stats['phase_counts'].items():
        logger.info(f"  {phase}: {count}")
    
    # 打印叫地主动作
    logger.info("叫地主动作:")
    for action, count in stats['bid_actions'].items():
        avg_reward = stats['bid_rewards'].get(action, 0.0)
        logger.info(f"  {action}: {count} 次, 平均奖励: {avg_reward:.4f}")
    
    # 打印抢地主动作
    logger.info("抢地主动作:")
    for action, count in stats['grab_actions'].items():
        avg_reward = stats['grab_rewards'].get(action, 0.0)
        logger.info(f"  {action}: {count} 次, 平均奖励: {avg_reward:.4f}")
    
    # 打印阶段转换
    logger.info("阶段转换:")
    for transition, count in stats['phase_transitions'].items():
        logger.info(f"  {transition}: {count} 次")
    
    # 打印其他统计数据
    logger.info(f"地主胜率: {stats['landlord_win_rate']:.4f}")
    logger.info(f"平均游戏长度: {stats['avg_game_length']:.2f}")


def main():
    """主函数"""
    # 创建环境
    env = DouDizhuEnvironment(seed=42)
    
    # 创建随机代理
    agent = RandomAgent(seed=42)
    
    # 创建自我对弈
    self_play = DouDizhuSelfPlay(save_path='models/doudizhu_multi')
    
    # 生成经验
    num_games = 5
    logger.info(f"开始生成{num_games}局游戏的经验...")
    start_time = time.time()
    
    experiences = self_play.generate_experience(
        env=env,
        agent=agent,
        num_games=num_games,
        temperature=1.0,
        save=False,
        parallel=False
    )
    
    logger.info(f"生成经验完成，耗时 {time.time() - start_time:.2f}s")
    logger.info(f"生成 {len(experiences)} 个经验")
    
    # 收集统计数据
    stats = collect_statistics(experiences)
    
    # 打印统计数据
    print_statistics(stats)


if __name__ == "__main__":
    main()
