"""
动作模块

定义斗地主游戏中的各种动作类型。
"""
from enum import Enum
from typing import Union, TYPE_CHECKING

if TYPE_CHECKING:
    from cardgame_ai.games.doudizhu.card_group import CardGroup


class BidAction(Enum):
    """
    叫地主动作枚举

    表示叫地主阶段的动作选择。
    """
    PASS = 0    # 不叫
    BID_1 = 1   # 叫1分
    BID_2 = 2   # 叫2分
    BID_3 = 3   # 叫3分

    def __str__(self) -> str:
        """
        转换为字符串表示

        Returns:
            str: 字符串表示
        """
        if self == BidAction.PASS:
            return "不叫"
        else:
            return f"叫{self.value}分"

    def __repr__(self) -> str:
        """
        转换为详细字符串表示

        Returns:
            str: 详细字符串表示
        """
        return f"BidAction.{self.name}"

    @property
    def is_bid(self) -> bool:
        """
        判断是否为实际叫分动作（非PASS）

        Returns:
            bool: 如果不是PASS则返回True，否则返回False
        """
        return self != BidAction.PASS


class GrabAction(Enum):
    """
    抢地主动作枚举

    表示抢地主阶段的动作选择。
    """
    PASS = 0    # 不抢
    GRAB = 1    # 抢地主

    def __str__(self) -> str:
        """
        转换为字符串表示

        Returns:
            str: 字符串表示
        """
        if self == GrabAction.PASS:
            return "不抢"
        else:
            return "抢地主"

    def __repr__(self) -> str:
        """
        转换为详细字符串表示

        Returns:
            str: 详细字符串表示
        """
        return f"GrabAction.{self.name}"

    @property
    def is_grab(self) -> bool:
        """
        判断是否为抢地主动作

        Returns:
            bool: 如果是GRAB则返回True，否则返回False
        """
        return self == GrabAction.GRAB


# 统一动作类型
DouDizhuAction = Union['CardGroup', BidAction, GrabAction]
