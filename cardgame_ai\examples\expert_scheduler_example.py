#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
专家调度器示例脚本

展示如何使用专家调度器动态选择最合适的专家策略。
"""

import os
import sys
import argparse
import logging
import numpy as np
import torch
import random
from typing import Dict, List, Any, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from cardgame_ai.core.expert_pool import ExpertPolicyPool
from cardgame_ai.core.expert_scheduler import RuleBasedScheduler, NeuralScheduler, UCBScheduler
from cardgame_ai.algorithms.hybrid_decision_system import HybridDecisionSystem
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.games.doudizhu.state import DouDizhuState
from cardgame_ai.algorithms.key_moment_detector import KeyMomentDetector

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='专家调度器示例')
    
    parser.add_argument('--scheduler_type', type=str, default='rule',
                        choices=['rule', 'neural', 'ucb'],
                        help='调度器类型')
    parser.add_argument('--expert_config', type=str, default='configs/expert_pool.json',
                        help='专家池配置文件路径')
    parser.add_argument('--state_dim', type=int, default=64,
                        help='状态特征维度')
    parser.add_argument('--hidden_dims', type=str, default='128,64',
                        help='隐藏层维度，用逗号分隔')
    parser.add_argument('--num_games', type=int, default=10,
                        help='游戏局数')
    parser.add_argument('--seed', type=int, default=42,
                        help='随机种子')
    
    return parser.parse_args()


def create_scheduler(args, expert_pool):
    """创建调度器"""
    if args.scheduler_type == 'rule':
        # 创建基于规则的调度器
        scheduler = RuleBasedScheduler(expert_pool)
        logger.info("已创建基于规则的调度器")
    elif args.scheduler_type == 'neural':
        # 解析隐藏层维度
        hidden_dims = [int(dim) for dim in args.hidden_dims.split(',')]
        
        # 创建神经网络调度器
        scheduler = NeuralScheduler(
            expert_pool=expert_pool,
            state_feature_dim=args.state_dim,
            hidden_dims=hidden_dims
        )
        logger.info(f"已创建神经网络调度器，特征维度: {args.state_dim}, 隐藏层: {hidden_dims}")
    elif args.scheduler_type == 'ucb':
        # 创建基于UCB的调度器
        scheduler = UCBScheduler(
            expert_pool=expert_pool,
            c=2.0,
            initial_value=0.0
        )
        logger.info("已创建基于UCB的调度器")
    else:
        raise ValueError(f"不支持的调度器类型: {args.scheduler_type}")
    
    return scheduler


def simulate_game(hybrid_system, env):
    """模拟游戏"""
    # 重置环境
    state = env.reset()
    done = False
    total_reward = 0.0
    
    # 记录专家使用情况
    expert_usage = {}
    
    while not done:
        # 获取合法动作
        legal_actions = env.get_legal_actions(state)
        
        # 使用混合决策系统选择动作
        action = hybrid_system.act(state, legal_actions)
        
        # 执行动作
        next_state, reward, done, info = env.step(action)
        
        # 更新状态和奖励
        state = next_state
        total_reward += reward
    
    # 更新混合决策系统
    hybrid_system.train({"reward": total_reward})
    
    return total_reward, expert_usage


def main():
    """主函数"""
    args = parse_args()
    
    # 设置随机种子
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    random.seed(args.seed)
    
    # 创建专家池
    expert_pool = ExpertPolicyPool(config_path=args.expert_config)
    
    # 创建调度器
    scheduler = create_scheduler(args, expert_pool)
    
    # 创建关键决策点检测器
    key_moment_detector = KeyMomentDetector()
    
    # 创建混合决策系统
    hybrid_system = HybridDecisionSystem(
        expert_pool=expert_pool,
        expert_scheduler=scheduler,
        key_moment_detector=key_moment_detector,
        use_scheduler=True
    )
    
    # 创建环境
    env = DouDizhuEnvironment()
    
    # 模拟游戏
    total_rewards = []
    
    for i in range(args.num_games):
        logger.info(f"开始游戏 {i+1}/{args.num_games}")
        reward, _ = simulate_game(hybrid_system, env)
        total_rewards.append(reward)
        logger.info(f"游戏 {i+1} 结束，奖励: {reward:.4f}")
    
    # 打印统计信息
    avg_reward = sum(total_rewards) / len(total_rewards)
    logger.info(f"平均奖励: {avg_reward:.4f}")
    
    stats = hybrid_system.get_stats()
    logger.info(f"专家使用情况: {stats['expert_usage']}")
    
    if 'expert_usage_ratio' in stats:
        logger.info(f"专家使用比例: {stats['expert_usage_ratio']}")
    
    if 'scheduler' in stats:
        scheduler_stats = stats['scheduler']
        logger.info(f"调度器统计信息: {scheduler_stats}")
    
    return 0


if __name__ == "__main__":
    main()
