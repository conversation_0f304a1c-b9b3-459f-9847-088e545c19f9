#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
桌面客户端主程序入口

启动桌面客户端应用程序，初始化主窗口和各个模块。
支持Windows和Linux（特别是Ubuntu）系统。
"""

import sys
import os
import logging
import platform
import tempfile
import datetime

# 将项目根目录添加到Python路径中
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
root_dir = os.path.dirname(parent_dir)
if root_dir not in sys.path:
    sys.path.insert(0, root_dir)
    print(f"已添加项目根目录到Python路径: {root_dir}")

# 导入跨平台工具
from cardgame_ai.desktop.utils.platform_utils import PlatformConfig

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt

from cardgame_ai.desktop.views.main_window import MainWindow
from cardgame_ai.desktop.utils.db_manager import DBManager
from cardgame_ai.desktop.config import ClientConfig


def setup_logging():
    """设置日志系统"""
    # 使用跨平台配置类获取日志目录
    log_dir = PlatformConfig.get_logs_dir()
    os.makedirs(log_dir, exist_ok=True)

    log_file = os.path.join(log_dir, 'client.log')

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

    logger = logging.getLogger('desktop_client')
    logger.info(f"当前操作系统: {PlatformConfig.get_system_name()}")
    return logger


def main():
    """主函数"""
    # 设置环境
    PlatformConfig.setup_environment()

    # 设置日志
    logger = setup_logging()
    logger.info("启动桌面客户端")

    # 初始化数据库
    db_manager = DBManager()
    db_manager.init_db()

    # 加载配置
    config = ClientConfig()

    # 检查是否需要恢复配置
    try:
        # 获取备份目录
        backup_dir = os.path.join(tempfile.gettempdir(), "cardgame_ai_backups")
        if os.path.exists(backup_dir):
            # 获取最新的备份文件
            backup_files = [os.path.join(backup_dir, f) for f in os.listdir(backup_dir)
                           if f.startswith("config_backup_") and f.endswith(".json")]

            if backup_files:
                # 按修改时间排序
                backup_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
                latest_backup = backup_files[0]

                # 检查备份文件的时间
                backup_time = os.path.getmtime(latest_backup)
                config_time = os.path.getmtime(config.config_file) if os.path.exists(config.config_file) else 0

                # 如果备份文件比配置文件新，则提示恢复
                if backup_time > config_time:
                    logger.info(f"发现更新的备份文件: {latest_backup}")

                    # 在主窗口创建后再提示恢复，这里只记录日志
                    config.latest_backup = latest_backup
    except Exception as e:
        logger.error(f"检查配置备份失败: {e}")

    # 创建应用程序
    app = QApplication(sys.argv)
    app.setApplicationName("AI棋牌强化学习框架")
    app.setOrganizationName("CardGameAI")

    # 检测PySide6版本以应用合适的DPI设置
    from PySide6 import __version__ as pyside_version
    logger.info(f"PySide6版本: {pyside_version}")

    # Qt6.0后，AA_UseHighDpiPixmaps和AA_EnableHighDpiScaling已弃用
    if int(pyside_version.split('.')[0]) >= 6:
        # Qt6新版本 - 使用新的API
        if hasattr(Qt.ApplicationAttribute, 'UseHighDpiPixmaps'):
            app.setAttribute(Qt.ApplicationAttribute.UseHighDpiPixmaps)
            logger.info("应用高DPI像素图: Qt6 API")

        if PlatformConfig.is_windows():
            if hasattr(Qt.ApplicationAttribute, 'HighDpiScaleFactorRoundingPolicy'):
                app.setAttribute(Qt.ApplicationAttribute.HighDpiScaleFactorRoundingPolicy,
                               Qt.HighDpiScaleFactorRoundingPolicy.PassThrough)
                logger.info("应用高DPI缩放: Qt6 API")
    else:
        # 兼容Qt5老版本
        if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
            app.setAttribute(Qt.AA_UseHighDpiPixmaps)
            logger.info("应用高DPI像素图: Qt5 API")

        if PlatformConfig.is_windows() and hasattr(Qt, 'AA_EnableHighDpiScaling'):
            app.setAttribute(Qt.AA_EnableHighDpiScaling)
            logger.info("应用高DPI缩放: Qt5 API")

    # Linux特定设置
    if PlatformConfig.is_linux():
        if hasattr(Qt.ApplicationAttribute, 'UseDesktopOpenGL'):
            app.setAttribute(Qt.ApplicationAttribute.UseDesktopOpenGL)
        elif hasattr(Qt, 'AA_UseDesktopOpenGL'):
            app.setAttribute(Qt.AA_UseDesktopOpenGL)
        logger.info("Linux: 启用桌面OpenGL")

    # 翻译功能已移除

    # 设置辅助功能
    from cardgame_ai.desktop.utils.accessibility import get_accessibility_manager
    accessibility_manager = get_accessibility_manager()

    # 加载辅助功能配置
    accessibility_config = {
        "keyboard_navigation": config.get('accessibility.keyboard_navigation', True),
        "screen_reader": config.get('accessibility.screen_reader', False),
        "high_contrast": config.get('accessibility.high_contrast', False),
        "color_blind_mode": config.get('accessibility.color_blind_mode', 'none'),
        "font_scale": config.get('accessibility.font_scale', 1.0),
        "reduced_motion": config.get('accessibility.reduced_motion', False)
    }
    accessibility_manager.load_config(accessibility_config)

    # 应用辅助功能设置
    accessibility_manager.apply_all(app)

    # 创建主窗口
    print("正在创建主窗口...")
    main_window = MainWindow(config)
    print("主窗口创建成功")

    # 显示主窗口
    print("显示主窗口...")
    main_window.show()
    print("主窗口已显示")

    # 记录启动时间
    logger.info(f"程序启动完成，启动时间：{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
