{"experts": {"random": {"type": "random", "description": "完全随机的策略，用于基准测试", "tags": ["baseline", "random"], "performance": {"win_rate": 0.0}}, "rule_based": {"type": "rule_based", "description": "基于人工规则的策略，具有基本的游戏策略", "tags": ["heuristic", "rule_based"], "performance": {"win_rate": 0.3}}, "mcts_basic": {"type": "mcts", "description": "基础MCTS策略，使用50次模拟", "params": {"num_simulations": 50}, "tags": ["search", "mcts", "basic"], "performance": {"win_rate": 0.5}}, "mcts_advanced": {"type": "mcts", "description": "高级MCTS策略，使用200次模拟", "params": {"num_simulations": 200, "c_puct": 2.0, "dirichlet_alpha": 0.3, "dirichlet_epsilon": 0.25}, "tags": ["search", "mcts", "advanced"], "performance": {"win_rate": 0.7}}, "dqn_model": {"type": "dqn", "path": "models/dqn/doudizhu_v1", "description": "基于DQN的策略，在斗地主游戏上训练", "params": {"hidden_dim": 256, "learning_rate": 0.001}, "tags": ["rl", "dqn", "pretrained"], "performance": {"win_rate": 0.6}, "created_at": "2023-05-15", "updated_at": "2023-06-20"}, "muzero_model": {"type": "muzero", "path": "models/muzero/doudizhu_v1", "description": "基于MuZero的策略，在斗地主游戏上训练", "params": {"hidden_dim": 256, "state_dim": 64, "num_simulations": 50}, "tags": ["rl", "muzero", "pretrained"], "performance": {"win_rate": 0.8}, "created_at": "2023-07-10", "updated_at": "2023-08-15"}, "efficient_zero_model": {"type": "efficient_zero", "path": "models/efficient_zero/doudizhu_v1", "description": "基于EfficientZero的策略，在斗地主游戏上训练", "params": {"hidden_dim": 256, "state_dim": 64, "num_simulations": 50, "use_self_supervised": true}, "tags": ["rl", "efficient_zero", "pretrained"], "performance": {"win_rate": 0.85}, "created_at": "2023-09-01", "updated_at": "2023-10-05"}, "transformer_model": {"type": "transformer", "path": "models/transformer/doudizhu_v1", "description": "基于Transformer的策略，在斗地主游戏上训练", "params": {"hidden_dim": 256, "num_layers": 6, "num_heads": 8}, "tags": ["rl", "transformer", "pretrained"], "performance": {"win_rate": 0.75}, "created_at": "2023-08-20", "updated_at": "2023-09-25"}, "ensemble_model": {"type": "ensemble", "description": "集成多个模型的策略", "params": {"models": ["dqn_model", "muzero_model", "efficient_zero_model"], "weights": [0.2, 0.3, 0.5]}, "tags": ["ensemble", "advanced"], "performance": {"win_rate": 0.9}, "created_at": "2023-10-10", "updated_at": "2023-10-10"}}}