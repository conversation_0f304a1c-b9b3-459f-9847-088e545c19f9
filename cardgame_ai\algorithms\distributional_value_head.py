"""
分布式价值头模块

实现分布式价值头，用于预测价值分布而不是单一价值，
支持条件风险价值（CVaR）计算，使AI在决策时能够考虑风险。
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Tuple, Optional, Union, List


class DistributionalValueHead(nn.Module):
    """
    分布式价值头

    预测价值分布而不是单一价值，支持条件风险价值（CVaR）计算。
    """

    def __init__(
        self,
        input_dim: int,
        hidden_dim: int = 128,
        value_support_size: int = 601,
        value_min: float = -300,
        value_max: float = 300,
        device: str = None
    ):
        """
        初始化分布式价值头

        Args:
            input_dim: 输入特征维度
            hidden_dim: 隐藏层维度
            value_support_size: 价值分布支持大小
            value_min: 价值范围最小值
            value_max: 价值范围最大值
            device: 计算设备
        """
        super(DistributionalValueHead, self).__init__()

        self.value_support_size = value_support_size
        self.value_min = value_min
        self.value_max = value_max
        self.device = device if device is not None else ('cuda' if torch.cuda.is_available() else 'cpu')

        # 创建价值支持
        self.register_buffer(
            'value_support',
            torch.linspace(value_min, value_max, value_support_size)
        )

        # 价值头网络
        self.value_head = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, value_support_size)
        )

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播

        Args:
            x: 输入特征

        Returns:
            价值分布logits
        """
        return self.value_head(x)

    def compute_expected_value(self, logits: torch.Tensor) -> torch.Tensor:
        """
        计算期望价值

        Args:
            logits: 价值分布logits

        Returns:
            期望价值
        """
        # 计算概率分布
        probs = F.softmax(logits, dim=-1)
        
        # 计算期望值
        expected_value = torch.sum(probs * self.value_support.unsqueeze(0), dim=-1)
        
        return expected_value

    def compute_cvar(
        self,
        logits: torch.Tensor,
        alpha: float = 0.05,
        return_var: bool = False
    ) -> Union[torch.Tensor, Tuple[torch.Tensor, torch.Tensor]]:
        """
        计算条件风险价值（CVaR）

        Args:
            logits: 价值分布logits
            alpha: CVaR的置信水平，表示关注分布最低alpha比例的样本
            return_var: 是否同时返回风险价值（VaR）

        Returns:
            CVaR值，如果return_var为True，则同时返回(CVaR, VaR)
        """
        # 计算概率分布
        probs = F.softmax(logits, dim=-1)
        
        # 获取排序后的价值支持和对应概率
        sorted_support, _ = torch.sort(self.value_support)
        sorted_probs = probs.gather(-1, torch.argsort(self.value_support).unsqueeze(0).expand(probs.size(0), -1))
        
        # 计算累积概率
        cumulative_probs = torch.cumsum(sorted_probs, dim=-1)
        
        # 找到VaR（风险价值）的索引
        var_indices = torch.sum(cumulative_probs < alpha, dim=-1)
        
        # 确保索引在有效范围内
        var_indices = torch.clamp(var_indices, 0, self.value_support_size - 1)
        
        # 获取VaR值
        var_values = sorted_support[var_indices]
        
        # 计算CVaR
        # 创建掩码，标记小于等于VaR的元素
        mask = sorted_support.unsqueeze(0) <= var_values.unsqueeze(-1)
        
        # 计算CVaR（在掩码区域内的加权平均值）
        masked_values = sorted_support.unsqueeze(0) * sorted_probs * mask.float()
        masked_probs = sorted_probs * mask.float()
        
        # 计算总概率（避免除以零）
        total_probs = torch.sum(masked_probs, dim=-1).clamp(min=1e-10)
        
        # 计算CVaR
        cvar = torch.sum(masked_values, dim=-1) / total_probs
        
        if return_var:
            return cvar, var_values
        else:
            return cvar

    def compute_risk_sensitive_value(
        self,
        logits: torch.Tensor,
        alpha: float = 0.05,
        beta: float = 0.1
    ) -> torch.Tensor:
        """
        计算风险敏感价值

        结合期望价值和CVaR，根据风险厌恶系数beta进行加权。
        风险敏感价值 = (1-β) * E[V] + β * CVaR_α(V)

        Args:
            logits: 价值分布logits
            alpha: CVaR的置信水平
            beta: 风险厌恶系数，控制风险敏感度

        Returns:
            风险敏感价值
        """
        # 计算期望价值
        expected_value = self.compute_expected_value(logits)
        
        # 计算CVaR
        cvar = self.compute_cvar(logits, alpha)
        
        # 计算风险敏感价值
        risk_sensitive_value = (1 - beta) * expected_value + beta * cvar
        
        return risk_sensitive_value

    def compute_distributional_loss(
        self,
        predicted_logits: torch.Tensor,
        target_values: torch.Tensor,
        reduction: str = 'mean'
    ) -> torch.Tensor:
        """
        计算分布式价值损失

        将目标值转换为分布，然后计算与预测分布的交叉熵损失。

        Args:
            predicted_logits: 预测的价值分布logits
            target_values: 目标价值（标量）
            reduction: 损失归约方式，可选'none', 'mean', 'sum'

        Returns:
            分布式价值损失
        """
        # 将目标值转换为分布
        target_distributions = self._scalar_to_distribution(target_values)
        
        # 计算交叉熵损失
        loss = F.cross_entropy(
            predicted_logits,
            target_distributions,
            reduction=reduction
        )
        
        return loss

    def _scalar_to_distribution(self, values: torch.Tensor) -> torch.Tensor:
        """
        将标量值转换为分布（one-hot或平滑分布）

        Args:
            values: 标量值

        Returns:
            分布索引（用于交叉熵损失）
        """
        # 将值限制在支持范围内
        values = torch.clamp(values, self.value_min, self.value_max)
        
        # 计算最接近的索引
        indices = torch.bucketize(values, self.value_support) - 1
        indices = torch.clamp(indices, 0, self.value_support_size - 1).long()
        
        return indices
