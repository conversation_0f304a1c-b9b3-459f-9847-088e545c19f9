"""
决策解释模式示例脚本

演示如何使用决策解释模式。
"""

import os
import sys
import argparse
import logging
import json
import numpy as np
import torch
from typing import Dict, List, Any, Optional

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cardgame_ai.algorithms.explanation_manager import ExplanationManager
from cardgame_ai.algorithms.mcts import MCTS
from cardgame_ai.algorithms.efficient_zero import EfficientZeroModel
from cardgame_ai.games.doudizhu.game import DouDizhuGame
from cardgame_ai.games.doudizhu.state import DouDizhuState
from cardgame_ai.games.doudizhu.card import Card

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def parse_args():
    """
    解析命令行参数
    
    Returns:
        argparse.Namespace: 解析后的参数
    """
    parser = argparse.ArgumentParser(description='决策解释模式示例')
    parser.add_argument('--model_path', type=str, default='models/efficient_zero_model.pth', help='模型路径')
    parser.add_argument('--detail_level', type=str, default='medium', choices=['low', 'medium', 'high'], help='解释详细程度')
    parser.add_argument('--visualization', action='store_true', help='是否启用可视化')
    parser.add_argument('--save_explanations', action='store_true', help='是否保存解释结果')
    parser.add_argument('--save_dir', type=str, default='explanations', help='解释结果保存目录')
    parser.add_argument('--num_simulations', type=int, default=100, help='MCTS模拟次数')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    
    return parser.parse_args()


def create_test_state() -> DouDizhuState:
    """
    创建测试状态
    
    Returns:
        DouDizhuState: 测试状态
    """
    # 创建游戏
    game = DouDizhuGame()
    
    # 初始化状态
    state = game.get_init_state()
    
    # 模拟发牌
    landlord_cards = [
        Card.from_string('3'), Card.from_string('3'), Card.from_string('4'), Card.from_string('4'),
        Card.from_string('5'), Card.from_string('6'), Card.from_string('7'), Card.from_string('8'),
        Card.from_string('9'), Card.from_string('10'), Card.from_string('J'), Card.from_string('Q'),
        Card.from_string('K'), Card.from_string('A'), Card.from_string('2'), Card.from_string('2'),
        Card.from_string('JOKER')
    ]
    
    farmer1_cards = [
        Card.from_string('3'), Card.from_string('4'), Card.from_string('5'), Card.from_string('5'),
        Card.from_string('6'), Card.from_string('6'), Card.from_string('7'), Card.from_string('8'),
        Card.from_string('9'), Card.from_string('10'), Card.from_string('J'), Card.from_string('Q'),
        Card.from_string('K'), Card.from_string('A'), Card.from_string('A'), Card.from_string('JOKER')
    ]
    
    farmer2_cards = [
        Card.from_string('3'), Card.from_string('4'), Card.from_string('5'), Card.from_string('6'),
        Card.from_string('7'), Card.from_string('7'), Card.from_string('8'), Card.from_string('8'),
        Card.from_string('9'), Card.from_string('9'), Card.from_string('10'), Card.from_string('10'),
        Card.from_string('J'), Card.from_string('Q'), Card.from_string('K'), Card.from_string('K')
    ]
    
    # 设置手牌
    state.player_cards = {
        0: landlord_cards,
        1: farmer1_cards,
        2: farmer2_cards
    }
    
    # 设置地主
    state.landlord = 0
    
    # 设置当前玩家
    state.current_player = 0
    
    # 设置历史动作
    state.history = []
    
    return state


def load_model(model_path: str) -> EfficientZeroModel:
    """
    加载模型
    
    Args:
        model_path: 模型路径
        
    Returns:
        EfficientZeroModel: 加载的模型
    """
    # 检查模型文件是否存在
    if not os.path.exists(model_path):
        logger.warning(f"模型文件不存在: {model_path}，使用随机初始化的模型")
        # 创建随机初始化的模型
        model = EfficientZeroModel(
            observation_shape=(54 * 4 + 54 * 3,),
            action_shape=(54 * 3,),
            hidden_dim=128,
            state_dim=64
        )
        return model
    
    # 加载模型
    try:
        checkpoint = torch.load(model_path, map_location='cpu')
        model = EfficientZeroModel(
            observation_shape=(54 * 4 + 54 * 3,),
            action_shape=(54 * 3,),
            hidden_dim=128,
            state_dim=64
        )
        model.load_state_dict(checkpoint['model_state_dict'])
        logger.info(f"成功加载模型: {model_path}")
        return model
    except Exception as e:
        logger.error(f"加载模型失败: {e}")
        # 创建随机初始化的模型
        model = EfficientZeroModel(
            observation_shape=(54 * 4 + 54 * 3,),
            action_shape=(54 * 3,),
            hidden_dim=128,
            state_dim=64
        )
        return model


def main():
    """
    主函数
    """
    # 解析命令行参数
    args = parse_args()
    
    # 设置随机种子
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    
    # 创建解释管理器
    explanation_manager = ExplanationManager(
        enabled=True,
        detail_level=args.detail_level,
        visualization_enabled=args.visualization,
        save_explanations=args.save_explanations,
        save_dir=args.save_dir
    )
    
    # 加载模型
    model = load_model(args.model_path)
    
    # 创建MCTS
    mcts = MCTS(
        num_simulations=args.num_simulations,
        discount=0.99,
        dirichlet_alpha=0.3,
        exploration_fraction=0.25
    )
    
    # 创建测试状态
    state = create_test_state()
    
    # 获取合法动作
    legal_actions = state.get_legal_actions()
    
    # 创建动作掩码
    actions_mask = [i in legal_actions for i in range(54 * 3)]
    
    # 使用MCTS进行搜索，启用解释模式
    logger.info("使用MCTS进行搜索...")
    visit_counts, pi, explanation_data = mcts.run(
        root_state=state,
        model=model,
        temperature=1.0,
        actions_mask=actions_mask,
        explain=True
    )
    
    # 获取最佳动作
    best_action = max(visit_counts.items(), key=lambda x: x[1])[0]
    logger.info(f"最佳动作: {best_action}")
    
    # 解释决策
    logger.info("解释决策...")
    explanation = explanation_manager.explain_decision(
        decision_data=explanation_data,
        decision_type="mcts",
        context={
            "state": str(state),
            "legal_actions": legal_actions,
            "best_action": best_action
        }
    )
    
    # 打印解释结果
    logger.info("解释结果:")
    logger.info(f"摘要: {explanation['summary']}")
    logger.info(f"置信度: {explanation['confidence']}")
    logger.info("主要动作:")
    for action in explanation['top_actions']:
        logger.info(f"  {action}")
    
    # 如果启用了可视化，打印可视化URL
    if args.visualization:
        logger.info("可视化:")
        for name, url in explanation['visualizations'].items():
            logger.info(f"  {name}: {url[:50]}...")
    
    # 打印统计信息
    logger.info("统计信息:")
    for key, value in explanation_manager.get_stats().items():
        logger.info(f"  {key}: {value}")


if __name__ == '__main__':
    main()
