"""
指标收集模块

用于收集和处理AI决策过程中的各种指标，为可视化提供数据支持。
"""

import logging
from typing import Dict, List, Any, Optional, Union, Tuple
from collections import defaultdict, deque
import numpy as np
import time

logger = logging.getLogger(__name__)


class MetricsCollector:
    """
    指标收集器

    收集和处理AI决策过程中的各种指标，为可视化提供数据支持。
    """

    def __init__(
        self,
        max_history_size: int = 100,
        enabled: bool = True
    ):
        """
        初始化指标收集器

        Args:
            max_history_size: 历史记录最大长度
            enabled: 是否启用指标收集
        """
        self.max_history_size = max_history_size
        self.enabled = enabled

        # 复杂度评分历史
        self.complexity_scores = deque(maxlen=max_history_size)
        
        # 组件调用统计
        self.component_calls = defaultdict(int)
        self.component_calls_history = {
            "mcts": deque(maxlen=max_history_size),
            "network": deque(maxlen=max_history_size),
            "rule": deque(maxlen=max_history_size),
            "hybrid": deque(maxlen=max_history_size)
        }
        
        # 价值变化历史
        self.value_history = deque(maxlen=max_history_size)
        
        # 决策时间统计
        self.decision_times = deque(maxlen=max_history_size)
        
        # 置信度历史
        self.confidence_history = deque(maxlen=max_history_size)
        
        # 时间戳记录
        self.timestamps = deque(maxlen=max_history_size)
        
        logger.info(f"初始化指标收集器，历史记录最大长度: {max_history_size}")

    def collect_metrics(self, explanation_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        收集指标

        Args:
            explanation_data: 解释数据

        Returns:
            Dict[str, Any]: 处理后的指标数据
        """
        if not self.enabled:
            return {}
            
        # 记录时间戳
        current_time = time.time()
        self.timestamps.append(current_time)
        
        # 提取决策类型
        decision_type = explanation_data.get("decision_type", "unknown")
        
        # 更新组件调用统计
        self.component_calls[decision_type] += 1
        for component in self.component_calls_history:
            value = 1 if component == decision_type else 0
            self.component_calls_history[component].append(value)
        
        # 提取复杂度评分
        complexity_score = self._calculate_complexity_score(explanation_data)
        self.complexity_scores.append(complexity_score)
        
        # 提取价值
        value = self._extract_value(explanation_data)
        self.value_history.append(value)
        
        # 提取置信度
        confidence = self._extract_confidence(explanation_data)
        self.confidence_history.append(confidence)
        
        # 提取决策时间
        decision_time = explanation_data.get("decision_time", 0.0)
        self.decision_times.append(decision_time)
        
        # 构建指标数据
        metrics_data = {
            "complexity_score": complexity_score,
            "component_calls": dict(self.component_calls),
            "component_distribution": {
                component: list(history) 
                for component, history in self.component_calls_history.items()
            },
            "value_history": list(self.value_history),
            "confidence_history": list(self.confidence_history),
            "decision_times": list(self.decision_times),
            "timestamps": list(self.timestamps)
        }
        
        return metrics_data

    def _calculate_complexity_score(self, explanation_data: Dict[str, Any]) -> float:
        """
        计算复杂度评分

        Args:
            explanation_data: 解释数据

        Returns:
            float: 复杂度评分 (0.0-1.0)
        """
        # 默认复杂度
        default_score = 0.5
        
        # 根据决策类型计算复杂度
        decision_type = explanation_data.get("decision_type", "unknown")
        
        if decision_type == "mcts":
            # MCTS复杂度基于搜索深度和模拟次数
            root_info = explanation_data.get("root_info", {})
            total_simulations = explanation_data.get("total_simulations", 0)
            
            # 搜索深度
            max_depth = root_info.get("max_depth", 0)
            # 归一化深度 (假设最大深度为20)
            depth_score = min(1.0, max_depth / 20.0)
            
            # 模拟次数
            simulations_score = min(1.0, total_simulations / 1000.0)
            
            # 组合得分
            score = 0.5 * depth_score + 0.5 * simulations_score
            
        elif decision_type == "network":
            # 神经网络复杂度基于策略熵和特征维度
            network_output = explanation_data.get("network_output", {})
            feature_dims = explanation_data.get("feature_dims", {})
            
            # 策略熵
            policy_entropy = network_output.get("policy_entropy", 0.0)
            # 归一化熵 (假设最大熵为5.0)
            entropy_score = min(1.0, policy_entropy / 5.0)
            
            # 特征维度
            feature_count = len(feature_dims)
            # 归一化特征数 (假设最大特征数为100)
            feature_score = min(1.0, feature_count / 100.0)
            
            # 组合得分
            score = 0.3 * entropy_score + 0.7 * feature_score
            
        elif decision_type == "rule":
            # 规则复杂度基于规则数量和条件数量
            rule_details = explanation_data.get("rule_details", {})
            conditions = explanation_data.get("conditions", [])
            
            # 规则优先级
            priority = rule_details.get("priority", 0)
            # 归一化优先级 (假设最高优先级为10)
            priority_score = min(1.0, priority / 10.0)
            
            # 条件数量
            condition_count = len(conditions)
            # 归一化条件数 (假设最大条件数为10)
            condition_score = min(1.0, condition_count / 10.0)
            
            # 组合得分
            score = 0.4 * priority_score + 0.6 * condition_score
            
        elif decision_type == "hybrid":
            # 混合决策复杂度基于组件数量和权重分布
            components = explanation_data.get("components", [])
            weights = explanation_data.get("weights", {})
            
            # 组件数量
            component_count = len(components)
            # 归一化组件数 (假设最大组件数为4)
            component_score = min(1.0, component_count / 4.0)
            
            # 权重分布
            if weights:
                weight_values = list(weights.values())
                # 计算权重熵
                weight_entropy = -sum(w * np.log2(w) if w > 0 else 0 for w in weight_values) / np.log2(len(weight_values))
                # 归一化权重熵
                weight_score = min(1.0, weight_entropy)
            else:
                weight_score = 0.5
            
            # 组合得分
            score = 0.3 * component_score + 0.7 * weight_score
            
        else:
            # 未知决策类型
            score = default_score
        
        return score

    def _extract_value(self, explanation_data: Dict[str, Any]) -> float:
        """
        提取价值

        Args:
            explanation_data: 解释数据

        Returns:
            float: 价值 (-1.0 到 1.0)
        """
        # 默认价值
        default_value = 0.0
        
        # 根据决策类型提取价值
        decision_type = explanation_data.get("decision_type", "unknown")
        
        if decision_type == "mcts":
            # MCTS价值来自根节点
            root_info = explanation_data.get("root_info", {})
            value = root_info.get("value", default_value)
            
        elif decision_type == "network":
            # 神经网络价值来自网络输出
            value = explanation_data.get("value_estimate", default_value)
            
        elif decision_type == "rule":
            # 规则价值是固定的
            value = 0.5  # 规则通常没有明确的价值估计
            
        elif decision_type == "hybrid":
            # 混合决策价值是组件价值的加权平均
            components = explanation_data.get("components", [])
            weights = explanation_data.get("weights", {})
            
            if components and weights:
                # 计算加权平均价值
                total_value = 0.0
                total_weight = 0.0
                
                for component in components:
                    component_type = component.get("type", "unknown")
                    component_data = component.get("data", {})
                    component_weight = weights.get(component_type, 0.0)
                    
                    if component_type == "mcts":
                        component_value = component_data.get("root_info", {}).get("value", 0.0)
                    elif component_type == "network":
                        component_value = component_data.get("value_estimate", 0.0)
                    else:
                        component_value = 0.0
                    
                    total_value += component_value * component_weight
                    total_weight += component_weight
                
                if total_weight > 0:
                    value = total_value / total_weight
                else:
                    value = default_value
            else:
                value = default_value
            
        else:
            # 未知决策类型
            value = default_value
        
        return value

    def _extract_confidence(self, explanation_data: Dict[str, Any]) -> float:
        """
        提取置信度

        Args:
            explanation_data: 解释数据

        Returns:
            float: 置信度 (0.0-1.0)
        """
        # 默认置信度
        default_confidence = 0.5
        
        # 根据决策类型提取置信度
        decision_type = explanation_data.get("decision_type", "unknown")
        
        if decision_type == "mcts":
            # MCTS置信度基于访问次数分布
            visit_counts = explanation_data.get("visit_counts", {})
            
            if visit_counts:
                # 转换为列表
                counts = list(visit_counts.values())
                total_counts = sum(counts)
                
                if total_counts > 0:
                    # 计算最大访问次数占比
                    max_count = max(counts)
                    confidence = max_count / total_counts
                else:
                    confidence = default_confidence
            else:
                confidence = default_confidence
            
        elif decision_type == "network":
            # 神经网络置信度来自策略分布
            network_output = explanation_data.get("network_output", {})
            top_actions = network_output.get("top_actions", [])
            
            if top_actions and len(top_actions) > 0:
                # 获取最高概率
                confidence = top_actions[0].get("probability", default_confidence)
            else:
                confidence = default_confidence
            
        elif decision_type == "rule":
            # 规则置信度是固定的
            confidence = 0.9  # 规则通常具有高置信度
            
        elif decision_type == "hybrid":
            # 混合决策置信度是组件置信度的加权平均
            final_decision = explanation_data.get("final_decision", {})
            confidence = final_decision.get("confidence", default_confidence)
            
        else:
            # 未知决策类型
            confidence = default_confidence
        
        return confidence

    def clear_history(self):
        """清除历史记录"""
        self.complexity_scores.clear()
        self.component_calls.clear()
        for component in self.component_calls_history:
            self.component_calls_history[component].clear()
        self.value_history.clear()
        self.decision_times.clear()
        self.confidence_history.clear()
        self.timestamps.clear()
        
        logger.info("已清除指标历史记录")

    def enable(self):
        """启用指标收集"""
        self.enabled = True
        logger.info("已启用指标收集")

    def disable(self):
        """禁用指标收集"""
        self.enabled = False
        logger.info("已禁用指标收集")

    def get_summary_metrics(self) -> Dict[str, Any]:
        """
        获取汇总指标

        Returns:
            Dict[str, Any]: 汇总指标数据
        """
        if not self.enabled or not self.complexity_scores:
            return {}
            
        # 计算平均复杂度
        avg_complexity = sum(self.complexity_scores) / len(self.complexity_scores)
        
        # 计算组件调用分布
        total_calls = sum(self.component_calls.values())
        component_distribution = {
            component: count / total_calls if total_calls > 0 else 0
            for component, count in self.component_calls.items()
        }
        
        # 计算平均价值
        avg_value = sum(self.value_history) / len(self.value_history) if self.value_history else 0.0
        
        # 计算平均置信度
        avg_confidence = sum(self.confidence_history) / len(self.confidence_history) if self.confidence_history else 0.0
        
        # 计算平均决策时间
        avg_decision_time = sum(self.decision_times) / len(self.decision_times) if self.decision_times else 0.0
        
        # 构建汇总指标
        summary = {
            "avg_complexity": avg_complexity,
            "component_distribution": component_distribution,
            "avg_value": avg_value,
            "avg_confidence": avg_confidence,
            "avg_decision_time": avg_decision_time,
            "total_decisions": total_calls
        }
        
        return summary
