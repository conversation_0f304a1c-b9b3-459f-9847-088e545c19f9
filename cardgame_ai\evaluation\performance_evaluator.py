#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
性能评估器

实现AI性能的全面评估，包括：
- 胜率评估
- 策略多样性分析
- 协作效率评估
- 与基准AI对比
"""

import torch
import numpy as np
import logging
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict

logger = logging.getLogger(__name__)


@dataclass
class EvaluationResult:
    """评估结果数据结构"""
    win_rate: float
    average_score: float
    game_length: float
    cooperation_efficiency: float
    strategy_diversity: float
    evaluation_time: float
    games_evaluated: int


class PerformanceEvaluator:
    """性能评估器"""

    def __init__(
        self,
        algorithm,
        environment,
        config: Dict[str, Any]
    ):
        """
        初始化性能评估器

        Args:
            algorithm: 待评估的算法
            environment: 游戏环境
            config: 评估配置
        """
        self.algorithm = algorithm
        self.environment = environment
        self.config = config

        # 评估参数
        self.eval_episodes = config.get('eval_episodes', 100)
        self.opponents = config.get('opponents', [])

        logger.info(f"性能评估器初始化完成，评估局数: {self.eval_episodes}")

    def evaluate(self) -> Dict[str, float]:
        """
        执行完整的性能评估

        Returns:
            Dict[str, float]: 评估指标字典
        """
        logger.info("开始性能评估")
        start_time = time.time()

        # 初始化评估指标
        metrics = {
            'win_rate': 0.0,
            'average_score': 0.0,
            'game_length': 0.0,
            'cooperation_efficiency': 0.0,
            'strategy_diversity': 0.0
        }

        # 对每种对手类型进行评估
        all_results = []

        for opponent_config in self.opponents:
            opponent_type = opponent_config.get('type', 'random')
            weight = opponent_config.get('weight', 1.0)

            # 评估对特定对手的性能
            results = self._evaluate_against_opponent(opponent_type)

            # 加权累积结果
            for key, value in results.items():
                if key in metrics:
                    metrics[key] += value * weight

            all_results.append(results)

        # 归一化指标
        total_weight = sum(config.get('weight', 1.0) for config in self.opponents)
        if total_weight > 0:
            for key in metrics:
                metrics[key] /= total_weight

        # 计算策略多样性
        metrics['strategy_diversity'] = self._calculate_strategy_diversity(all_results)

        # 记录评估时间
        evaluation_time = time.time() - start_time
        metrics['evaluation_time'] = evaluation_time

        logger.info(f"性能评估完成，耗时: {evaluation_time:.2f}s")
        logger.info(f"胜率: {metrics['win_rate']:.3f}, 平均得分: {metrics['average_score']:.2f}")

        return metrics

    def _evaluate_against_opponent(self, opponent_type: str) -> Dict[str, float]:
        """
        对特定对手类型进行评估

        Args:
            opponent_type: 对手类型

        Returns:
            Dict[str, float]: 评估结果
        """
        wins = 0
        total_score = 0.0
        total_game_length = 0
        cooperation_scores = []

        for episode in range(self.eval_episodes):
            # 重置环境
            state = self.environment.reset()

            # 游戏循环
            game_length = 0
            episode_score = 0
            cooperation_actions = []

            done = False
            while not done:
                # AI决策
                action = self.algorithm.act(state, training=False)

                # 执行动作
                next_state, reward, done, info = self.environment.step(action)

                # 记录信息
                episode_score += reward
                game_length += 1

                # 记录协作相关动作
                if self._is_cooperation_action(action, state):
                    cooperation_actions.append(action)

                state = next_state

            # 统计结果
            if episode_score > 0:
                wins += 1

            total_score += episode_score
            total_game_length += game_length

            # 计算协作效率
            cooperation_efficiency = len(cooperation_actions) / max(game_length, 1)
            cooperation_scores.append(cooperation_efficiency)

        # 计算平均指标
        win_rate = wins / self.eval_episodes
        avg_score = total_score / self.eval_episodes
        avg_game_length = total_game_length / self.eval_episodes
        avg_cooperation = np.mean(cooperation_scores) if cooperation_scores else 0.0

        return {
            'win_rate': win_rate,
            'average_score': avg_score,
            'game_length': avg_game_length,
            'cooperation_efficiency': avg_cooperation
        }

    def _is_cooperation_action(self, action: Any, state: Any) -> bool:
        """
        判断是否为协作动作

        Args:
            action: 动作
            state: 游戏状态

        Returns:
            bool: 是否为协作动作
        """
        # 简化的协作动作判断逻辑
        # 实际实现中应该基于具体的游戏规则
        return False  # 占位符

    def _calculate_strategy_diversity(self, results: List[Dict[str, float]]) -> float:
        """
        计算策略多样性

        Args:
            results: 评估结果列表

        Returns:
            float: 策略多样性得分
        """
        if not results:
            return 0.0

        # 简化的策略多样性计算
        # 基于不同对手的胜率差异
        win_rates = [result.get('win_rate', 0.0) for result in results]

        if len(win_rates) <= 1:
            return 0.0

        # 计算胜率的标准差作为多样性指标
        diversity = np.std(win_rates)

        return min(diversity, 1.0)  # 限制在0-1范围内
