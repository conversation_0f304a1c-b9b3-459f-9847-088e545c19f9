# 单GPU硬件配置
# 适用于单张GPU的训练配置

device:
  type: "cuda"
  ids: [0]
  mixed_precision: true
  benchmark: true
  deterministic: false

# 内存配置
memory:
  max_memory_usage: 0.85
  gradient_checkpointing: false
  empty_cache_frequency: 1000
  gc_frequency: 2000

# 数据加载配置
data:
  num_workers: 8
  pin_memory: true
  prefetch_factor: 4
  persistent_workers: true
  cache_size_gb: 4

# 训练配置
training:
  batch_size:
    base: 256
    min: 128
    max: 384
    auto_scale: true
    memory_threshold: 0.8

# MCTS配置
mcts:
  num_simulations: 100
  parallel_threads: 4
  batch_size_inference: 16

# 分布式配置
distributed:
  enabled: false
