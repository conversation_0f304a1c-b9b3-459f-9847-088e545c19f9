"""
测试EfficientZero算法中target_policies/target_values张量化问题的修复

该测试验证三个CodeRabbit发现的问题：
1. target_policies/target_values from dict batches are never tensorised
2. Guard against NumPy → Tensor dtype/shape corner-cases in _ensure_tensor
3. 改进的_ensure_tensor方法处理边界情况
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import numpy as np
from cardgame_ai.algorithms.efficient_zero_algorithm import EfficientZ<PERSON>


def test_target_policies_values_tensorization():
    """测试target_policies和target_values的张量化"""
    print("=== 测试target_policies和target_values的张量化 ===\n")
    
    # 创建EfficientZero实例
    algorithm = EfficientZero(
        state_shape=(4,),
        action_shape=(3,),
        hidden_dim=32,
        state_dim=16,
        batch_size=2,
        device='cpu'
    )
    
    # 测试1: 列表类型的target_policies和target_values
    print("测试1: 列表类型的target_policies和target_values")
    try:
        # 创建包含列表类型target数据的字典batch
        dict_batch = {
            'observations': [[1.0, 2.0, 3.0, 4.0], [5.0, 6.0, 7.0, 8.0]],
            'actions': [1, 2],
            'rewards': [1.0, -1.0],
            'next_states': [[2.0, 3.0, 4.0, 5.0], [6.0, 7.0, 8.0, 9.0]],
            'dones': [False, True],
            'target_policies': [[[0.1, 0.8, 0.1], [0.2, 0.6, 0.2]], [[0.3, 0.4, 0.3], [0.1, 0.7, 0.2]]],  # 列表格式
            'target_values': [[1.0, 0.5], [-1.0, -0.5]]  # 列表格式
        }
        
        # 这应该不会抛出AttributeError
        result = algorithm.train(dict_batch)
        assert isinstance(result, dict)
        assert 'total_loss' in result
        print("✓ 列表类型target数据测试通过")
        
    except AttributeError as e:
        if "'list' object has no attribute" in str(e):
            print(f"✗ 列表类型target数据测试失败: 仍然存在AttributeError: {e}")
        else:
            print(f"✗ 列表类型target数据测试失败: 其他AttributeError: {e}")
    except Exception as e:
        # 其他错误是可以接受的，只要不是AttributeError
        print(f"✓ 列表类型target数据测试通过（没有AttributeError）: {type(e).__name__}")
    
    # 测试2: NumPy数组类型的target_policies和target_values
    print("\n测试2: NumPy数组类型的target_policies和target_values")
    try:
        # 创建包含NumPy数组类型target数据的字典batch
        dict_batch = {
            'observations': np.array([[1.0, 2.0, 3.0, 4.0], [5.0, 6.0, 7.0, 8.0]]),
            'actions': np.array([1, 2]),
            'rewards': np.array([1.0, -1.0]),
            'next_states': np.array([[2.0, 3.0, 4.0, 5.0], [6.0, 7.0, 8.0, 9.0]]),
            'dones': np.array([False, True]),
            'target_policies': np.array([[[0.1, 0.8, 0.1], [0.2, 0.6, 0.2]], [[0.3, 0.4, 0.3], [0.1, 0.7, 0.2]]]),  # NumPy数组
            'target_values': np.array([[1.0, 0.5], [-1.0, -0.5]])  # NumPy数组
        }
        
        # 这应该不会抛出AttributeError
        result = algorithm.train(dict_batch)
        assert isinstance(result, dict)
        assert 'total_loss' in result
        print("✓ NumPy数组类型target数据测试通过")
        
    except AttributeError as e:
        if "'numpy.ndarray' object has no attribute" in str(e):
            print(f"✗ NumPy数组类型target数据测试失败: 仍然存在AttributeError: {e}")
        else:
            print(f"✗ NumPy数组类型target数据测试失败: 其他AttributeError: {e}")
    except Exception as e:
        # 其他错误是可以接受的，只要不是AttributeError
        print(f"✓ NumPy数组类型target数据测试通过（没有AttributeError）: {type(e).__name__}")


def test_ensure_tensor_improvements():
    """测试改进的_ensure_tensor方法"""
    print("\n=== 测试改进的_ensure_tensor方法 ===\n")
    
    algorithm = EfficientZero(
        state_shape=(4,),
        action_shape=(3,),
        hidden_dim=32,
        state_dim=16,
        batch_size=2,
        device='cpu'
    )
    
    # 测试1: object dtype NumPy数组（应该失败）
    print("测试1: object dtype NumPy数组（应该fail fast）")
    try:
        # 创建object dtype的NumPy数组（ragged数据）
        object_array = np.array([[1, 2, 3], [4, 5]], dtype=object)
        result = algorithm._ensure_tensor(object_array, torch.float32)
        print("✗ object dtype数组测试失败: 应该抛出错误但没有")
    except ValueError as e:
        if "object-dtype NumPy 数组" in str(e):
            print(f"✓ object dtype数组测试通过: {e}")
        else:
            print(f"✗ 抛出了ValueError但错误消息不正确: {e}")
    except Exception as e:
        print(f"✗ 抛出了错误的异常类型: {type(e).__name__}: {e}")
    
    # 测试2: 正常NumPy数组
    print("\n测试2: 正常NumPy数组")
    try:
        normal_array = np.array([[1.0, 2.0], [3.0, 4.0]], dtype=np.float32)
        result = algorithm._ensure_tensor(normal_array, torch.float32)
        assert isinstance(result, torch.Tensor)
        assert result.dtype == torch.float32
        assert result.shape == (2, 2)
        print(f"✓ 正常NumPy数组测试通过: {result.shape}, {result.dtype}")
    except Exception as e:
        print(f"✗ 正常NumPy数组测试失败: {e}")
    
    # 测试3: 标量值
    print("\n测试3: 标量值")
    try:
        scalar_value = 5.0
        result = algorithm._ensure_tensor(scalar_value, torch.float32)
        assert isinstance(result, torch.Tensor)
        assert result.dtype == torch.float32
        assert result.shape == ()  # 标量张量
        print(f"✓ 标量值测试通过: {result.shape}, {result.dtype}")
    except Exception as e:
        print(f"✗ 标量值测试失败: {e}")
    
    # 测试4: 已有张量
    print("\n测试4: 已有张量")
    try:
        existing_tensor = torch.tensor([[1, 2], [3, 4]], dtype=torch.long)
        result = algorithm._ensure_tensor(existing_tensor, torch.float32)
        assert isinstance(result, torch.Tensor)
        assert result.dtype == torch.float32
        assert result.shape == (2, 2)
        print(f"✓ 已有张量测试通过: {result.shape}, {result.dtype}")
    except Exception as e:
        print(f"✗ 已有张量测试失败: {e}")


def test_cross_entropy_compatibility():
    """测试与F.cross_entropy的兼容性"""
    print("\n=== 测试与F.cross_entropy的兼容性 ===\n")
    
    algorithm = EfficientZero(
        state_shape=(4,),
        action_shape=(3,),
        hidden_dim=32,
        state_dim=16,
        batch_size=2,
        device='cpu'
    )
    
    # 测试1: 确保target_policies能够与torch.argmax和F.cross_entropy兼容
    print("测试1: target_policies与F.cross_entropy兼容性")
    try:
        # 创建模拟的target_policies（列表格式）
        target_policies_list = [[[0.1, 0.8, 0.1], [0.2, 0.6, 0.2]], [[0.3, 0.4, 0.3], [0.1, 0.7, 0.2]]]
        
        # 使用_ensure_tensor转换
        target_policies_tensor = algorithm._ensure_tensor(target_policies_list, torch.float32)
        
        # 验证可以与torch.argmax一起使用
        argmax_result = torch.argmax(target_policies_tensor[:, 0], dim=1)
        assert isinstance(argmax_result, torch.Tensor)
        assert argmax_result.dtype == torch.long
        
        # 创建模拟的policy_logits
        policy_logits = torch.randn(2, 3)  # batch_size=2, action_dim=3
        
        # 验证可以与F.cross_entropy一起使用
        import torch.nn.functional as F
        loss = F.cross_entropy(policy_logits, argmax_result, reduction='mean')
        assert isinstance(loss, torch.Tensor)
        assert loss.dim() == 0  # 标量损失
        
        print(f"✓ target_policies兼容性测试通过: argmax shape={argmax_result.shape}, loss={loss.item():.4f}")
        
    except Exception as e:
        print(f"✗ target_policies兼容性测试失败: {e}")
    
    # 测试2: 确保target_values能够与F.mse_loss兼容
    print("\n测试2: target_values与F.mse_loss兼容性")
    try:
        # 创建模拟的target_values（列表格式）
        target_values_list = [[1.0, 0.5], [-1.0, -0.5]]
        
        # 使用_ensure_tensor转换
        target_values_tensor = algorithm._ensure_tensor(target_values_list, torch.float32)
        
        # 创建模拟的predicted values
        predicted_values = torch.randn(2)  # batch_size=2
        
        # 验证可以与F.mse_loss一起使用
        import torch.nn.functional as F
        loss = F.mse_loss(predicted_values, target_values_tensor[:, 0], reduction='mean')
        assert isinstance(loss, torch.Tensor)
        assert loss.dim() == 0  # 标量损失
        
        print(f"✓ target_values兼容性测试通过: target shape={target_values_tensor.shape}, loss={loss.item():.4f}")
        
    except Exception as e:
        print(f"✗ target_values兼容性测试失败: {e}")


if __name__ == "__main__":
    print("=== 测试EfficientZero target_policies/target_values修复 ===\n")
    
    try:
        test_target_policies_values_tensorization()
        test_ensure_tensor_improvements()
        test_cross_entropy_compatibility()
        
        print("\n=== 所有测试完成 ===")
        print("\n修复总结:")
        print("- 修复了字典batch中target_policies和target_values的张量化问题")
        print("- 改进了_ensure_tensor方法，处理NumPy数组边界情况")
        print("- 添加了object dtype NumPy数组的fail-fast检查")
        print("- 确保与F.cross_entropy和F.mse_loss的兼容性")
        print("- 消除了'list' object has no attribute 'shape'错误")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
