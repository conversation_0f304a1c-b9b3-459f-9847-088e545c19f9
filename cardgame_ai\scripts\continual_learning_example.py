#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
连续学习示例脚本

展示如何使用弹性权重固化 (EWC) 和 L2 正则化防止灾难性遗忘。
"""

import os
import sys
import argparse
import logging
import time
import torch
import numpy as np
from typing import Dict, List, Any, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from cardgame_ai.training.online_updater import OnlineUpdater
from cardgame_ai.algorithms.efficient_zero import EfficientZero
from cardgame_ai.algorithms.continual_learning import EWC, L2Regularization
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.utils.data_loader import load_trajectory_data

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('continual_learning_example.log')
    ]
)
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='连续学习示例')
    
    parser.add_argument('--model_path', type=str, default=None,
                        help='模型路径')
    parser.add_argument('--old_task_data_dir', type=str, default='data/old_task_data',
                        help='旧任务数据目录')
    parser.add_argument('--new_task_data_dir', type=str, default='data/new_task_data',
                        help='新任务数据目录')
    parser.add_argument('--use_ewc', action='store_true',
                        help='是否使用弹性权重固化 (EWC)')
    parser.add_argument('--fisher_importance', type=float, default=1000.0,
                        help='Fisher重要性系数')
    parser.add_argument('--use_l2_reg', action='store_true',
                        help='是否使用L2正则化')
    parser.add_argument('--weight_decay', type=float, default=0.01,
                        help='权重衰减系数')
    parser.add_argument('--batch_size', type=int, default=32,
                        help='批次大小')
    parser.add_argument('--updates_per_trajectory', type=int, default=5,
                        help='每个轨迹的更新次数')
    parser.add_argument('--max_runtime', type=int, default=3600,
                        help='最大运行时间（秒）')
    
    return parser.parse_args()


def create_model(args, env):
    """创建模型"""
    # 获取观察和动作空间
    observation_shape = env.observation_space.shape
    action_shape = (env.action_space.n,)
    
    # 创建EfficientZero模型
    model = EfficientZero(
        state_shape=observation_shape,
        action_shape=action_shape,
        hidden_dim=128,
        state_dim=64,
        use_resnet=False
    )
    
    # 如果有预训练模型，加载参数
    if args.model_path and os.path.exists(args.model_path):
        model.load(args.model_path)
        logger.info(f"已加载预训练模型: {args.model_path}")
    
    return model


def load_old_task_data(args):
    """加载旧任务数据"""
    if not os.path.exists(args.old_task_data_dir):
        logger.warning(f"旧任务数据目录不存在: {args.old_task_data_dir}")
        return None
    
    # 获取所有轨迹文件
    old_files = []
    for root, _, files in os.walk(args.old_task_data_dir):
        for file in files:
            if file.endswith('.json'):
                old_files.append(os.path.join(root, file))
    
    if not old_files:
        logger.warning(f"旧任务数据目录中没有找到文件: {args.old_task_data_dir}")
        return None
    
    # 加载所有轨迹数据
    old_batches = []
    for filepath in old_files[:10]:  # 限制文件数量，避免内存溢出
        batch = load_trajectory_data(filepath)
        if batch and "observations" in batch and len(batch["observations"]) > 0:
            old_batches.append(batch)
    
    if old_batches:
        logger.info(f"成功加载旧任务数据: {len(old_batches)}个批次")
        return old_batches
    else:
        logger.warning("未能加载任何有效的旧任务数据")
        return None


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 创建环境
    env = DouDizhuEnvironment()
    
    # 创建模型
    model = create_model(args, env)
    
    # 加载旧任务数据
    old_task_data = load_old_task_data(args)
    
    # 初始化连续学习组件
    ewc = None
    l2_reg = None
    
    if args.use_ewc and old_task_data:
        logger.info(f"初始化EWC，Fisher重要性系数: {args.fisher_importance}")
        try:
            ewc = EWC(model, old_task_data, args.fisher_importance)
            logger.info("EWC初始化成功")
        except Exception as e:
            logger.error(f"EWC初始化失败: {e}")
            args.use_ewc = False
    
    if args.use_l2_reg:
        logger.info(f"初始化L2正则化，权重衰减系数: {args.weight_decay}")
        l2_reg = L2Regularization(model, args.weight_decay)
        logger.info("L2正则化初始化成功")
    
    # 创建在线更新器
    updater = OnlineUpdater(
        model=model,
        trajectory_dir=args.new_task_data_dir,
        update_interval=1,  # 每秒检查一次
        batch_size=args.batch_size,
        updates_per_trajectory=args.updates_per_trajectory,
        max_trajectories_per_update=10,
        use_experience_format=False,
        file_pattern="*.json",
        use_ewc=args.use_ewc,
        old_task_dataloader=old_task_data,
        fisher_importance=args.fisher_importance,
        use_l2_reg=args.use_l2_reg,
        weight_decay=args.weight_decay,
        old_task_data_dir=args.old_task_data_dir
    )
    
    # 记录开始时间
    start_time = time.time()
    
    logger.info(f"开始运行在线更新器: {args.new_task_data_dir}")
    logger.info(f"使用EWC: {args.use_ewc}")
    logger.info(f"使用L2正则化: {args.use_l2_reg}")
    logger.info(f"最大运行时间: {args.max_runtime}秒")
    
    try:
        while True:
            # 检查是否达到最大运行时间
            if args.max_runtime is not None and time.time() - start_time > args.max_runtime:
                logger.info(f"达到最大运行时间: {args.max_runtime}秒")
                break
            
            # 检查并更新模型
            stats = updater.check_and_update()
            
            # 输出统计信息
            if stats["total_updates"] > 0:
                logger.info(f"更新统计: 总更新次数={stats['total_updates']}, "
                           f"总轨迹数={stats['total_trajectories']}, "
                           f"总步骤数={stats['total_steps']}")
                
                # 输出正则化损失
                if "regularization_loss" in stats:
                    logger.info(f"正则化损失: {stats['regularization_loss']:.4f}")
            
            # 等待下一次更新
            time.sleep(1)
    
    except KeyboardInterrupt:
        logger.info("用户中断")
    
    except Exception as e:
        logger.error(f"运行在线更新器时出错: {e}")
    
    finally:
        # 输出最终统计信息
        stats = updater.stats
        elapsed_time = time.time() - start_time
        
        logger.info(f"在线更新器运行结束")
        logger.info(f"运行时间: {elapsed_time:.2f}秒")
        logger.info(f"总更新次数: {stats['total_updates']}")
        logger.info(f"总轨迹数: {stats['total_trajectories']}")
        logger.info(f"总步骤数: {stats['total_steps']}")
        
        if elapsed_time > 0:
            logger.info(f"更新速率: {stats['total_updates'] / elapsed_time:.2f}次/秒")
            logger.info(f"轨迹处理速率: {stats['total_trajectories'] / elapsed_time:.2f}个/秒")
        
        # 保存更新后的模型
        if args.model_path:
            save_path = args.model_path + ".updated"
        else:
            save_path = f"models/efficient_zero_updated.pt"
        
        # 确保目录存在
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        
        # 保存模型
        model.save(save_path)
        logger.info(f"已保存更新后的模型: {save_path}")


if __name__ == "__main__":
    main()
