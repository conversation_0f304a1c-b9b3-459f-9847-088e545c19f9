"""
超参数配置模块

定义常用超参数配置和优化策略，为不同算法和任务提供默认配置。
"""
import os
import json
from typing import Dict, Any, List, Tuple, Optional, Union


# DQN超参数范围
DQN_PARAM_RANGES = {
    "learning_rate": (1e-5, 1e-2),
    "discount_factor": (0.9, 0.999),
    "epsilon_start": (0.9, 1.0),
    "epsilon_end": (0.01, 0.1),
    "epsilon_decay": (0.995, 0.9999),
    "batch_size": [32, 64, 128, 256],
    "target_update_interval": [100, 200, 500, 1000],
    "memory_size": [10000, 50000, 100000],
    "hidden_size": [64, 128, 256, 512]
}

# PPO超参数范围
PPO_PARAM_RANGES = {
    "learning_rate": (1e-5, 1e-3),
    "discount_factor": (0.9, 0.999),
    "gae_lambda": (0.9, 0.99),
    "clip_ratio": (0.1, 0.3),
    "value_coef": (0.5, 1.0),
    "entropy_coef": (0.0, 0.01),
    "max_grad_norm": (0.1, 1.0),
    "batch_size": [32, 64, 128, 256],
    "epochs_per_update": [3, 5, 10],
    "hidden_size": [64, 128, 256, 512]
}

# MuZero超参数范围 (预留)
MUZERO_PARAM_RANGES = {
    "learning_rate": (1e-5, 1e-3),
    "discount_factor": (0.9, 0.999),
    "num_unroll_steps": [3, 5, 8],
    "td_steps": [5, 10, 20],
    "num_simulations": [50, 100, 200],
    "dirichlet_alpha": (0.1, 0.5),
    "exploration_fraction": (0.1, 0.3),
    "batch_size": [16, 32, 64],
    "memory_size": [10000, 50000, 100000],
    "hidden_size": [64, 128, 256, 512]
}

# 自我对弈超参数范围
SELF_PLAY_PARAM_RANGES = {
    "temperature": (0.5, 2.0),
    "games_per_episode": [10, 20, 50, 100],
    "max_buffer_size": [10000, 50000, 100000],
    "epochs_per_episode": [1, 3, 5, 10]
}

# 训练与评估超参数范围
TRAINING_PARAM_RANGES = {
    "num_episodes": [100, 200, 500, 1000],
    "batch_size": [32, 64, 128, 256],
    "eval_interval": [10, 20, 50],
    "save_interval": [10, 20, 50, 100],
    "patience": [5, 10, 20]
}


class HyperparameterConfig:
    """
    超参数配置管理
    
    管理和加载超参数配置，支持从文件加载和保存配置。
    """
    
    @staticmethod
    def load_config(file_path: str) -> Dict[str, Any]:
        """
        从文件加载超参数配置
        
        Args:
            file_path (str): 配置文件路径
            
        Returns:
            Dict[str, Any]: 超参数配置
        """
        with open(file_path, 'r') as f:
            config = json.load(f)
        return config
    
    @staticmethod
    def save_config(config: Dict[str, Any], file_path: str) -> None:
        """
        保存超参数配置到文件
        
        Args:
            config (Dict[str, Any]): 超参数配置
            file_path (str): 配置文件路径
        """
        with open(file_path, 'w') as f:
            json.dump(config, f, indent=2)
    
    @classmethod
    def get_dqn_default(cls) -> Dict[str, Any]:
        """
        获取DQN默认超参数配置
        
        Returns:
            Dict[str, Any]: DQN默认超参数
        """
        return {
            "learning_rate": 0.001,
            "discount_factor": 0.99,
            "epsilon_start": 1.0,
            "epsilon_end": 0.05,
            "epsilon_decay": 0.998,
            "batch_size": 64,
            "target_update_interval": 500,
            "memory_size": 50000,
            "hidden_size": 128,
            "double_dqn": True,
            "dueling_dqn": True,
            "prioritized_replay": True,
            "alpha": 0.6,  # 优先经验回放的alpha参数
            "beta": 0.4,   # 优先经验回放的beta参数
        }
    
    @classmethod
    def get_ppo_default(cls) -> Dict[str, Any]:
        """
        获取PPO默认超参数配置
        
        Returns:
            Dict[str, Any]: PPO默认超参数
        """
        return {
            "learning_rate": 0.0003,
            "discount_factor": 0.99,
            "gae_lambda": 0.95,
            "clip_ratio": 0.2,
            "value_coef": 0.5,
            "entropy_coef": 0.01,
            "max_grad_norm": 0.5,
            "batch_size": 64,
            "epochs_per_update": 5,
            "hidden_size": 128,
            "use_critic_normalization": True,
            "use_reward_scaling": True,
            "reward_scaling_factor": 0.01,
        }
    
    @classmethod
    def get_muzero_default(cls) -> Dict[str, Any]:
        """
        获取MuZero默认超参数配置
        
        Returns:
            Dict[str, Any]: MuZero默认超参数
        """
        return {
            "learning_rate": 0.0002,
            "discount_factor": 0.997,
            "num_unroll_steps": 5,
            "td_steps": 10,
            "num_simulations": 100,
            "dirichlet_alpha": 0.3,
            "exploration_fraction": 0.25,
            "batch_size": 32,
            "memory_size": 50000,
            "hidden_size": 128,
            "value_loss_weight": 1.0,
            "policy_loss_weight": 1.0,
            "reward_loss_weight": 1.0,
            "consistency_loss_weight": 0.1,
        }
    
    @classmethod
    def get_self_play_default(cls) -> Dict[str, Any]:
        """
        获取自我对弈默认配置
        
        Returns:
            Dict[str, Any]: 自我对弈默认配置
        """
        return {
            "temperature": 1.0,
            "games_per_episode": 20,
            "max_buffer_size": 50000,
            "epochs_per_episode": 3,
            "save_experiences": True,
            "parallel": True,
            "use_multiprocessing": False,
            "max_workers": None,  # 自动使用CPU核心数
        }
    
    @classmethod
    def get_training_default(cls) -> Dict[str, Any]:
        """
        获取训练与评估默认配置
        
        Returns:
            Dict[str, Any]: 训练与评估默认配置
        """
        return {
            "num_episodes": 500,
            "batch_size": 64,
            "eval_interval": 20,
            "eval_games": 10,
            "save_interval": 50,
            "log_interval": 10,
            "checkpoint_interval": 100,
            "max_checkpoints": 5,
            "patience": 10,
            "min_delta": 0.001,
            "early_stop_metric": "eval_win_rate",
        }
    
    @classmethod
    def merge_configs(cls, base_config: Dict[str, Any], override_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        合并配置，用override_config覆盖base_config中的同名项
        
        Args:
            base_config (Dict[str, Any]): 基础配置
            override_config (Dict[str, Any]): 覆盖配置
            
        Returns:
            Dict[str, Any]: 合并后的配置
        """
        merged = base_config.copy()
        for key, value in override_config.items():
            if isinstance(value, dict) and key in merged and isinstance(merged[key], dict):
                # 递归合并嵌套字典
                merged[key] = cls.merge_configs(merged[key], value)
            else:
                # 直接覆盖
                merged[key] = value
        return merged


# 预定义的超参数优化方案

# DQN网格搜索配置
DQN_GRID_SEARCH_CONFIG = {
    "learning_rate": [0.0001, 0.0005, 0.001],
    "batch_size": [32, 64, 128],
    "hidden_size": [64, 128, 256],
    "double_dqn": [True, False],
    "dueling_dqn": [True, False],
}

# PPO随机搜索配置范围
PPO_RANDOM_SEARCH_CONFIG = {
    "learning_rate": (0.0001, 0.001),
    "clip_ratio": (0.1, 0.3),
    "value_coef": (0.25, 1.0),
    "entropy_coef": (0.0, 0.02),
    "gae_lambda": (0.9, 0.99),
    "batch_size": [32, 64, 128],
    "epochs_per_update": [3, 5, 10],
}

# 创建PBT初始种群的辅助函数
def create_pbt_population(base_config: Dict[str, Any], population_size: int, 
                        mutation_ranges: Dict[str, Tuple]) -> List[Dict[str, Any]]:
    """
    创建PBT初始种群
    
    基于基础配置，创建带有随机变异的初始种群
    
    Args:
        base_config (Dict[str, Any]): 基础配置
        population_size (int): 种群大小
        mutation_ranges (Dict[str, Tuple]): 变异范围
        
    Returns:
        List[Dict[str, Any]]: 初始种群
    """
    population = []
    
    for _ in range(population_size):
        # 复制基础配置
        config = base_config.copy()
        
        # 随机变异
        for param_name, param_range in mutation_ranges.items():
            if param_name not in config:
                continue
                
            if isinstance(param_range, tuple) and len(param_range) == 2:
                # 范围参数
                min_val, max_val = param_range
                if isinstance(config[param_name], int) and isinstance(min_val, int) and isinstance(max_val, int):
                    # 整数参数
                    config[param_name] = random.randint(min_val, max_val)
                elif isinstance(config[param_name], float):
                    # 浮点参数
                    config[param_name] = random.uniform(min_val, max_val)
            elif isinstance(param_range, list):
                # 列表选项
                config[param_name] = random.choice(param_range)
        
        population.append(config)
    
    return population


# DQN PBT初始种群变异范围
DQN_PBT_MUTATION_RANGES = {
    "learning_rate": (0.0001, 0.002),
    "epsilon_decay": (0.99, 0.9999),
    "batch_size": [32, 64, 128],
    "hidden_size": [64, 128, 256],
}

# PPO PBT初始种群变异范围
PPO_PBT_MUTATION_RANGES = {
    "learning_rate": (0.0001, 0.001),
    "clip_ratio": (0.1, 0.3),
    "value_coef": (0.25, 1.0),
    "entropy_coef": (0.0, 0.02),
    "batch_size": [32, 64, 128],
} 