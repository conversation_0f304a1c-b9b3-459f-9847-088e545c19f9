"""
训练可视化模块

实现训练过程和结果的可视化，包括训练曲线、性能指标和对比分析。
"""
import os
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, Any, List, Tuple, Optional, Union
from matplotlib.figure import Figure
from matplotlib.axes import Axes


class TrainingVisualizer:
    """
    训练可视化器
    
    可视化训练过程和结果，包括训练曲线、性能指标和对比分析。
    """
    
    def __init__(self, save_dir: str = 'visualizations'):
        """
        初始化训练可视化器
        
        Args:
            save_dir (str, optional): 可视化结果保存目录. Defaults to 'visualizations'.
        """
        self.save_dir = save_dir
        os.makedirs(save_dir, exist_ok=True)
        
        # 设置可视化样式
        sns.set_theme(style="whitegrid")
        plt.rcParams.update({
            'font.size': 12,
            'axes.titlesize': 14,
            'axes.labelsize': 12,
            'xtick.labelsize': 10,
            'ytick.labelsize': 10,
            'legend.fontsize': 10,
            'figure.titlesize': 16
        })
    
    def plot_training_curve(self, log_file: str, metrics: List[str] = None, 
                          window_size: int = 10, figsize: Tuple[int, int] = (12, 8),
                          save_as: str = None) -> Figure:
        """
        绘制训练曲线
        
        Args:
            log_file (str): 训练日志文件路径
            metrics (List[str], optional): 要绘制的指标. Defaults to None.
            window_size (int, optional): 移动平均窗口大小. Defaults to 10.
            figsize (Tuple[int, int], optional): 图形大小. Defaults to (12, 8).
            save_as (str, optional): 保存文件名. Defaults to None.
            
        Returns:
            Figure: matplotlib图形对象
        """
        # 读取日志文件
        data = []
        with open(log_file, 'r') as f:
            for line in f:
                try:
                    entry = json.loads(line.strip())
                    data.append(entry)
                except json.JSONDecodeError:
                    continue
        
        if not data:
            print(f"警告: 日志文件 {log_file} 中没有找到有效数据")
            return None
        
        # 转换为DataFrame
        df = pd.DataFrame(data)
        
        # 确定要绘制的指标
        if metrics is None:
            # 自动选择常见的指标
            possible_metrics = [
                'episode_reward', 'mean_reward', 'loss', 'value_loss', 'policy_loss',
                'entropy', 'win_rate', 'episode_length'
            ]
            metrics = [col for col in df.columns if any(metric in col for metric in possible_metrics)]
        
        # 如果losses是嵌套结构，展开它
        if 'losses' in df.columns and isinstance(df['losses'].iloc[0], dict):
            for loss_key in df['losses'].iloc[0].keys():
                df[f'loss_{loss_key}'] = df['losses'].apply(lambda x: x.get(loss_key, np.nan))
            
            # 更新指标列表
            loss_columns = [f'loss_{key}' for key in df['losses'].iloc[0].keys()]
            metrics = [m for m in metrics if m != 'losses'] + loss_columns
        
        # 应用移动平均
        smoothed_df = df.copy()
        for metric in metrics:
            if metric in df.columns:
                smoothed_df[f'{metric}_smooth'] = df[metric].rolling(window=window_size, min_periods=1).mean()
        
        # 创建图形
        fig, axes = plt.subplots(len(metrics), 1, figsize=figsize, sharex=True)
        if len(metrics) == 1:
            axes = [axes]
        
        # 绘制每个指标
        for i, metric in enumerate(metrics):
            if metric in df.columns:
                ax = axes[i]
                
                # 绘制原始数据（半透明）
                ax.plot(df['episode'], df[metric], 'o-', alpha=0.3, label='原始数据')
                
                # 绘制平滑后的数据
                ax.plot(df['episode'], smoothed_df[f'{metric}_smooth'], 
                      'r-', linewidth=2, label=f'移动平均 (窗口={window_size})')
                
                ax.set_title(f'{metric} vs Episode')
                ax.set_ylabel(metric)
                ax.legend()
                ax.grid(True, alpha=0.3)
        
        # 设置x轴标签
        axes[-1].set_xlabel('Episode')
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图形
        if save_as:
            save_path = os.path.join(self.save_dir, save_as)
            plt.savefig(save_path)
            print(f"训练曲线已保存至 {save_path}")
        
        return fig
    
    def plot_reward_distribution(self, eval_results_file: str, figsize: Tuple[int, int] = (10, 6),
                                save_as: str = None) -> Figure:
        """
        绘制奖励分布
        
        Args:
            eval_results_file (str): 评估结果文件路径
            figsize (Tuple[int, int], optional): 图形大小. Defaults to (10, 6).
            save_as (str, optional): 保存文件名. Defaults to None.
            
        Returns:
            Figure: matplotlib图形对象
        """
        # 读取评估结果
        with open(eval_results_file, 'r') as f:
            eval_results = json.load(f)
        
        # 检查是否包含游戏奖励
        if 'game_rewards' not in eval_results:
            print(f"警告: 评估结果 {eval_results_file} 中没有找到游戏奖励数据")
            return None
        
        game_rewards = eval_results['game_rewards']
        
        # 转换为numpy数组
        rewards_array = np.array(game_rewards)
        
        # 创建图形
        fig, ax = plt.subplots(figsize=figsize)
        
        # 为每个玩家绘制奖励分布
        num_players = rewards_array.shape[1]
        for i in range(num_players):
            sns.kdeplot(rewards_array[:, i], ax=ax, label=f'玩家 {i}')
        
        ax.set_title('游戏奖励分布')
        ax.set_xlabel('奖励')
        ax.set_ylabel('密度')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 保存图形
        if save_as:
            save_path = os.path.join(self.save_dir, save_as)
            plt.savefig(save_path)
            print(f"奖励分布图已保存至 {save_path}")
        
        return fig
    
    def plot_win_rates(self, eval_results_files: List[str], labels: List[str] = None,
                     figsize: Tuple[int, int] = (10, 6), save_as: str = None) -> Figure:
        """
        绘制胜率对比
        
        Args:
            eval_results_files (List[str]): 评估结果文件路径列表
            labels (List[str], optional): 标签列表. Defaults to None.
            figsize (Tuple[int, int], optional): 图形大小. Defaults to (10, 6).
            save_as (str, optional): 保存文件名. Defaults to None.
            
        Returns:
            Figure: matplotlib图形对象
        """
        if not labels:
            labels = [f"模型 {i+1}" for i in range(len(eval_results_files))]
        
        # 读取评估结果
        win_rates = []
        for file_path in eval_results_files:
            with open(file_path, 'r') as f:
                eval_results = json.load(f)
            
            # 收集胜率数据
            player_win_rates = []
            for i in range(10):  # 尝试最多10个玩家
                win_rate_key = f'player_{i}_win_rate'
                if win_rate_key in eval_results:
                    player_win_rates.append(eval_results[win_rate_key])
            
            win_rates.append(player_win_rates)
        
        # 创建图形
        fig, ax = plt.subplots(figsize=figsize)
        
        # 设置柱状图的宽度和位置
        num_models = len(win_rates)
        num_players = max(len(rates) for rates in win_rates)
        width = 0.8 / num_models
        
        # 绘制柱状图
        for i, (rates, label) in enumerate(zip(win_rates, labels)):
            x = np.arange(len(rates))
            offset = (i - num_models / 2 + 0.5) * width
            ax.bar(x + offset, rates, width, label=label)
        
        ax.set_title('代理胜率对比')
        ax.set_xlabel('玩家')
        ax.set_ylabel('胜率')
        ax.set_xticks(np.arange(num_players))
        ax.set_xticklabels([f'玩家 {i}' for i in range(num_players)])
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 保存图形
        if save_as:
            save_path = os.path.join(self.save_dir, save_as)
            plt.savefig(save_path)
            print(f"胜率对比图已保存至 {save_path}")
        
        return fig
    
    def plot_hyperparameter_importance(self, results_file: str, top_n: int = 5,
                                     figsize: Tuple[int, int] = (12, 8),
                                     save_as: str = None) -> Figure:
        """
        绘制超参数重要性
        
        Args:
            results_file (str): 超参数优化结果文件路径
            top_n (int, optional): 显示前N个最重要的超参数. Defaults to 5.
            figsize (Tuple[int, int], optional): 图形大小. Defaults to (12, 8).
            save_as (str, optional): 保存文件名. Defaults to None.
            
        Returns:
            Figure: matplotlib图形对象
        """
        # 读取优化结果
        data = []
        with open(results_file, 'r') as f:
            for line in f:
                try:
                    entry = json.loads(line.strip())
                    data.append(entry)
                except json.JSONDecodeError:
                    continue
        
        if not data:
            print(f"警告: 结果文件 {results_file} 中没有找到有效数据")
            return None
        
        # 转换为DataFrame
        df = pd.DataFrame(data)
        
        # 展开参数
        params_df = pd.json_normalize(df['params'].apply(lambda x: x if isinstance(x, dict) else {}))
        df = pd.concat([df.drop('params', axis=1), params_df], axis=1)
        
        # 确定是否是最大化目标
        maximize = True
        if 'maximize' in df.columns:
            maximize = df['maximize'].iloc[0]
        
        # 获取超参数列
        param_cols = params_df.columns.tolist()
        if not param_cols:
            print(f"警告: 结果文件 {results_file} 中没有找到超参数")
            return None
        
        # 计算每个超参数与目标值的相关性
        correlations = {}
        for param in param_cols:
            if df[param].dtype in (np.float64, np.int64):
                corr = df[param].corr(df['objective'])
                if not np.isnan(corr):
                    correlations[param] = corr
        
        # 获取前N个最重要的超参数
        if maximize:
            top_params = sorted(correlations.items(), key=lambda x: abs(x[1]), reverse=True)[:top_n]
        else:
            top_params = sorted(correlations.items(), key=lambda x: abs(x[1]), reverse=True)[:top_n]
        
        # 创建图形
        fig, axes = plt.subplots(len(top_params), 1, figsize=figsize)
        if len(top_params) == 1:
            axes = [axes]
        
        # 绘制每个重要超参数与目标值的关系
        for i, (param, corr) in enumerate(top_params):
            ax = axes[i]
            
            # 绘制散点图
            ax.scatter(df[param], df['objective'], alpha=0.7)
            
            # 添加趋势线
            z = np.polyfit(df[param], df['objective'], 1)
            p = np.poly1d(z)
            ax.plot(sorted(df[param]), p(sorted(df[param])), "r--", alpha=0.7)
            
            ax.set_title(f'{param} vs Objective (相关性: {corr:.4f})')
            ax.set_xlabel(param)
            ax.set_ylabel('Objective Value')
            ax.grid(True, alpha=0.3)
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图形
        if save_as:
            save_path = os.path.join(self.save_dir, save_as)
            plt.savefig(save_path)
            print(f"超参数重要性图已保存至 {save_path}")
        
        return fig
    
    def plot_training_dashboard(self, log_file: str, eval_file: str = None,
                              figsize: Tuple[int, int] = (18, 10),
                              save_as: str = None) -> Figure:
        """
        绘制训练仪表板
        
        Args:
            log_file (str): 训练日志文件路径
            eval_file (str, optional): 评估结果文件路径. Defaults to None.
            figsize (Tuple[int, int], optional): 图形大小. Defaults to (18, 10).
            save_as (str, optional): 保存文件名. Defaults to None.
            
        Returns:
            Figure: matplotlib图形对象
        """
        # 读取训练日志
        train_data = []
        with open(log_file, 'r') as f:
            for line in f:
                try:
                    entry = json.loads(line.strip())
                    train_data.append(entry)
                except json.JSONDecodeError:
                    continue
        
        if not train_data:
            print(f"警告: 日志文件 {log_file} 中没有找到有效数据")
            return None
        
        # 转换为DataFrame
        train_df = pd.DataFrame(train_data)
        
        # 创建仪表板
        fig = plt.figure(figsize=figsize)
        fig.suptitle('训练与评估仪表板', fontsize=16)
        
        # 定义网格布局
        gs = fig.add_gridspec(3, 3)
        
        # 绘制训练时间
        if 'self_play_time' in train_df.columns and 'training_time' in train_df.columns:
            ax_time = fig.add_subplot(gs[0, 0])
            train_df.plot(x='episode', y=['self_play_time', 'training_time'], 
                        kind='line', ax=ax_time, marker='o')
            ax_time.set_title('训练时间')
            ax_time.set_xlabel('Episode')
            ax_time.set_ylabel('时间 (秒)')
            ax_time.grid(True, alpha=0.3)
            ax_time.legend(['自我对弈时间', '训练时间'])
        
        # 绘制经验缓冲区大小
        if 'experience_buffer_size' in train_df.columns:
            ax_buffer = fig.add_subplot(gs[0, 1])
            train_df.plot(x='episode', y='experience_buffer_size', 
                        kind='line', ax=ax_buffer, marker='o', color='green')
            ax_buffer.set_title('经验缓冲区大小')
            ax_buffer.set_xlabel('Episode')
            ax_buffer.set_ylabel('缓冲区大小')
            ax_buffer.grid(True, alpha=0.3)
        
        # 绘制损失
        ax_loss = fig.add_subplot(gs[0, 2])
        loss_columns = []
        
        # 检查是否有嵌套的损失
        if 'losses' in train_df.columns and isinstance(train_df['losses'].iloc[0], dict):
            for loss_key in train_df['losses'].iloc[0].keys():
                col_name = f'loss_{loss_key}'
                train_df[col_name] = train_df['losses'].apply(lambda x: x.get(loss_key, np.nan))
                loss_columns.append(col_name)
        
        # 检查是否有其他损失列
        for col in train_df.columns:
            if 'loss' in col.lower() and col not in loss_columns:
                loss_columns.append(col)
        
        if loss_columns:
            train_df.plot(x='episode', y=loss_columns, kind='line', ax=ax_loss, marker='o')
            ax_loss.set_title('训练损失')
            ax_loss.set_xlabel('Episode')
            ax_loss.set_ylabel('损失')
            ax_loss.grid(True, alpha=0.3)
            if len(loss_columns) > 1:
                ax_loss.legend(loss_columns)
        
        # 如果有评估文件，添加评估指标
        if eval_file and os.path.exists(eval_file):
            eval_data = None
            try:
                with open(eval_file, 'r') as f:
                    eval_data = json.load(f)
            except json.JSONDecodeError:
                print(f"警告: 评估文件 {eval_file} 格式不正确")
            
            if eval_data:
                # 绘制胜率
                win_rates = {}
                for key, value in eval_data.items():
                    if 'win_rate' in key:
                        win_rates[key] = value
                
                if win_rates:
                    ax_win = fig.add_subplot(gs[1, 0])
                    ax_win.bar(win_rates.keys(), win_rates.values())
                    ax_win.set_title('胜率')
                    ax_win.set_xlabel('玩家')
                    ax_win.set_ylabel('胜率')
                    ax_win.set_ylim(0, 1)
                    plt.setp(ax_win.get_xticklabels(), rotation=45, ha='right')
                    ax_win.grid(True, alpha=0.3)
                
                # 绘制平均奖励
                if 'mean_rewards' in eval_data:
                    ax_reward = fig.add_subplot(gs[1, 1])
                    ax_reward.bar(range(len(eval_data['mean_rewards'])), 
                               eval_data['mean_rewards'])
                    ax_reward.set_title('平均奖励')
                    ax_reward.set_xlabel('玩家')
                    ax_reward.set_ylabel('平均奖励')
                    ax_reward.set_xticks(range(len(eval_data['mean_rewards'])))
                    ax_reward.set_xticklabels([f'玩家 {i}' for i in range(len(eval_data['mean_rewards']))])
                    ax_reward.grid(True, alpha=0.3)
                
                # 绘制平均游戏长度
                if 'mean_game_length' in eval_data:
                    ax_length = fig.add_subplot(gs[1, 2])
                    ax_length.bar(['平均游戏长度'], [eval_data['mean_game_length']], color='purple')
                    ax_length.set_title('平均游戏长度')
                    ax_length.set_ylabel('步数')
                    ax_length.grid(True, alpha=0.3)
                
                # 如果有游戏记录，添加更多指标
                if 'game_records' in eval_data:
                    # TODO: 添加游戏记录分析
                    pass
        
        # 添加更多训练指标
        # 如果有eval_*指标在训练日志中
        eval_cols = [col for col in train_df.columns if col.startswith('eval_')]
        if eval_cols:
            ax_eval = fig.add_subplot(gs[2, :])
            train_df.plot(x='episode', y=eval_cols, kind='line', ax=ax_eval, marker='o')
            ax_eval.set_title('评估指标')
            ax_eval.set_xlabel('Episode')
            ax_eval.set_ylabel('指标值')
            ax_eval.grid(True, alpha=0.3)
            if len(eval_cols) > 1:
                ax_eval.legend(eval_cols)
        
        # 调整布局
        plt.tight_layout(rect=[0, 0, 1, 0.97])
        
        # 保存图形
        if save_as:
            save_path = os.path.join(self.save_dir, save_as)
            plt.savefig(save_path, dpi=150)
            print(f"训练仪表板已保存至 {save_path}")
        
        return fig
    
    def compare_agents(self, eval_results_files: List[str], agent_names: List[str] = None,
                      metrics: List[str] = None, figsize: Tuple[int, int] = (15, 10),
                      save_as: str = None) -> Figure:
        """
        比较多个代理的性能
        
        Args:
            eval_results_files (List[str]): 评估结果文件路径列表
            agent_names (List[str], optional): 代理名称列表. Defaults to None.
            metrics (List[str], optional): 要比较的指标列表. Defaults to None.
            figsize (Tuple[int, int], optional): 图形大小. Defaults to (15, 10).
            save_as (str, optional): 保存文件名. Defaults to None.
            
        Returns:
            Figure: matplotlib图形对象
        """
        if not agent_names:
            agent_names = [f"代理 {i+1}" for i in range(len(eval_results_files))]
        
        # 读取评估结果
        eval_results = []
        for file_path in eval_results_files:
            with open(file_path, 'r') as f:
                eval_results.append(json.load(f))
        
        # 确定要比较的指标
        if not metrics:
            # 常见指标列表
            common_metrics = [
                'player_0_win_rate', 'mean_game_length', 'mean_rewards',
                'mean_payoffs', 'total_time'
            ]
            
            # 找出所有结果中共有的指标
            metrics = []
            for metric in common_metrics:
                if all(metric in result for result in eval_results):
                    metrics.append(metric)
        
        # 创建图形
        num_metrics = len(metrics)
        nrows = (num_metrics + 1) // 2  # 向上取整
        fig, axes = plt.subplots(nrows, 2, figsize=figsize)
        axes = axes.flatten()
        
        # 绘制每个指标
        for i, metric in enumerate(metrics):
            if i < len(axes):
                ax = axes[i]
                
                # 收集数据
                values = []
                for result in eval_results:
                    if metric in result:
                        if isinstance(result[metric], list):
                            # 如果是列表（如mean_rewards），取第一个玩家的值
                            values.append(result[metric][0])
                        else:
                            values.append(result[metric])
                    else:
                        values.append(np.nan)
                
                # 绘制柱状图
                ax.bar(agent_names, values)
                ax.set_title(metric)
                plt.setp(ax.get_xticklabels(), rotation=45, ha='right')
                ax.grid(True, alpha=0.3)
        
        # 隐藏未使用的子图
        for i in range(num_metrics, len(axes)):
            axes[i].set_visible(False)
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图形
        if save_as:
            save_path = os.path.join(self.save_dir, save_as)
            plt.savefig(save_path)
            print(f"代理比较图已保存至 {save_path}")
        
        return fig 