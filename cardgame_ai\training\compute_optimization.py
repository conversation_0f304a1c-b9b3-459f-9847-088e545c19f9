"""
计算优化模块

提升算法的计算效率，加速训练过程并优化资源利用。包括实现分布式训练、
优化内存使用和添加并行化支持等技术，使算法能够更高效地运行。
"""
import os
import time
import logging
import threading
import torch
import torch.nn as nn
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel as DDP
from torch.cuda.amp import autocast, GradScaler
from typing import Dict, List, Tuple, Any, Optional, Union, Callable

from cardgame_ai.core.agent import Agent
from cardgame_ai.core.environment import Environment


class GradientCheckpointing:
    """
    梯度检查点类

    实现梯度检查点技术，通过在前向传播过程中仅保存部分中间激活值，
    并在反向传播时重新计算其他激活值，从而减少内存使用。
    """

    @staticmethod
    def apply(model: nn.Module, checkpoint_segments: int = 3) -> nn.Module:
        """
        应用梯度检查点技术到模型

        Args:
            model: 要应用梯度检查点的模型
            checkpoint_segments: 检查点分段数，越多内存越少，但计算量越大

        Returns:
            应用了梯度检查点的模型
        """
        # 检查模型是否支持梯度检查点
        if not hasattr(model, 'forward'):
            raise ValueError("模型必须有forward方法")

        # 如果模型是序列模型，应用梯度检查点
        if isinstance(model, nn.Sequential):
            return GradientCheckpointing._apply_to_sequential(model, checkpoint_segments)

        # 如果模型是变换器，应用梯度检查点到每个层
        if hasattr(model, 'transformer') or hasattr(model, 'encoder') or hasattr(model, 'decoder'):
            return GradientCheckpointing._apply_to_transformer(model)

        # 其他类型的模型，应用通用梯度检查点
        return GradientCheckpointing._apply_general_checkpointing(model)

    @staticmethod
    def _apply_to_sequential(model: nn.Sequential, checkpoint_segments: int) -> nn.Module:
        """
        应用梯度检查点到序列模型

        Args:
            model: 序列模型
            checkpoint_segments: 检查点分段数

        Returns:
            应用了梯度检查点的模型
        """
        # 如果模型层数小于检查点分段数，调整分段数
        num_layers = len(model)
        if num_layers < checkpoint_segments:
            checkpoint_segments = num_layers

        # 计算每个分段的层数
        layers_per_segment = num_layers // checkpoint_segments

        # 创建新的序列模型
        checkpointed_model = nn.Sequential()

        # 将原模型分成多个分段，并应用梯度检查点
        for i in range(checkpoint_segments):
            start_idx = i * layers_per_segment
            end_idx = (i + 1) * layers_per_segment if i < checkpoint_segments - 1 else num_layers

            # 创建分段
            segment = nn.Sequential(*list(model[start_idx:end_idx]))

            # 应用梯度检查点
            checkpointed_segment = torch.utils.checkpoint.checkpoint_sequential(segment, 2, segment)

            # 添加到新模型
            checkpointed_model.add_module(f'segment_{i}', checkpointed_segment)

        return checkpointed_model

    @staticmethod
    def _apply_to_transformer(model: nn.Module) -> nn.Module:
        """
        应用梯度检查点到变换器模型

        Args:
            model: 变换器模型

        Returns:
            应用了梯度检查点的模型
        """
        # 如果模型有transformer属性
        if hasattr(model, 'transformer'):
            # 获取原始前向传播函数
            original_forward = model.transformer.forward

            # 定义新的前向传播函数，使用梯度检查点
            def checkpointed_forward(*args, **kwargs):
                return torch.utils.checkpoint.checkpoint(original_forward, *args, **kwargs)

            # 替换前向传播函数
            model.transformer.forward = checkpointed_forward

        # 如果模型有encoder属性
        if hasattr(model, 'encoder') and hasattr(model.encoder, 'forward'):
            # 获取原始前向传播函数
            original_forward = model.encoder.forward

            # 定义新的前向传播函数，使用梯度检查点
            def checkpointed_forward(*args, **kwargs):
                return torch.utils.checkpoint.checkpoint(original_forward, *args, **kwargs)

            # 替换前向传播函数
            model.encoder.forward = checkpointed_forward

        # 如果模型有decoder属性
        if hasattr(model, 'decoder') and hasattr(model.decoder, 'forward'):
            # 获取原始前向传播函数
            original_forward = model.decoder.forward

            # 定义新的前向传播函数，使用梯度检查点
            def checkpointed_forward(*args, **kwargs):
                return torch.utils.checkpoint.checkpoint(original_forward, *args, **kwargs)

            # 替换前向传播函数
            model.decoder.forward = checkpointed_forward

        return model

    @staticmethod
    def _apply_general_checkpointing(model: nn.Module) -> nn.Module:
        """
        应用通用梯度检查点

        Args:
            model: 模型

        Returns:
            应用了梯度检查点的模型
        """
        # 获取原始前向传播函数
        original_forward = model.forward

        # 定义新的前向传播函数，使用梯度检查点
        def checkpointed_forward(*args, **kwargs):
            return torch.utils.checkpoint.checkpoint(original_forward, *args, **kwargs)

        # 替换前向传播函数
        model.forward = checkpointed_forward

        return model


class ActivationRecomputation:
    """
    激活值重计算类

    实现激活值重计算技术，通过在前向传播过程中不保存中间激活值，
    而是在反向传播时重新计算这些激活值，从而减少内存使用。
    """

    @staticmethod
    def apply(model: nn.Module, recompute_layers: Optional[List[str]] = None) -> nn.Module:
        """
        应用激活值重计算技术到模型

        Args:
            model: 要应用激活值重计算的模型
            recompute_layers: 需要重计算的层名称列表，如果为None，则应用到所有层

        Returns:
            应用了激活值重计算的模型
        """
        # 如果没有指定重计算层，则应用到所有层
        if recompute_layers is None:
            # 对所有子模块应用激活值重计算
            for name, module in model.named_children():
                if isinstance(module, (nn.ReLU, nn.LeakyReLU, nn.GELU, nn.Sigmoid, nn.Tanh)):
                    # 对激活函数应用重计算
                    ActivationRecomputation._apply_to_activation(model, name, module)
                elif len(list(module.children())) > 0:
                    # 递归应用到子模块
                    ActivationRecomputation.apply(module)
        else:
            # 只对指定层应用激活值重计算
            for name in recompute_layers:
                if '.' in name:
                    # 处理嵌套层
                    parts = name.split('.')
                    parent_name = '.'.join(parts[:-1])
                    child_name = parts[-1]

                    # 获取父模块
                    parent_module = model
                    for part in parts[:-1]:
                        if hasattr(parent_module, part):
                            parent_module = getattr(parent_module, part)
                        else:
                            break

                    # 获取子模块
                    if hasattr(parent_module, child_name):
                        child_module = getattr(parent_module, child_name)
                        ActivationRecomputation._apply_to_activation(parent_module, child_name, child_module)
                else:
                    # 处理非嵌套层
                    if hasattr(model, name):
                        module = getattr(model, name)
                        ActivationRecomputation._apply_to_activation(model, name, module)

        return model

    @staticmethod
    def _apply_to_activation(parent_module: nn.Module, name: str, module: nn.Module) -> None:
        """
        应用激活值重计算到激活函数

        Args:
            parent_module: 父模块
            name: 模块名称
            module: 要应用重计算的模块
        """
        # 获取原始前向传播函数
        original_forward = module.forward

        # 定义新的前向传播函数，使用重计算
        def recompute_forward(x):
            # 在前向传播时不保存激活值
            with torch.no_grad():
                y = original_forward(x)

            # 在反向传播时重新计算激活值
            if x.requires_grad:
                y = original_forward(x)

            return y

        # 替换前向传播函数
        setattr(parent_module, name, type(module)(recompute_forward))


class MixedPrecisionTraining:
    """
    混合精度训练类

    实现混合精度训练，使用FP16进行前向传播和反向传播，
    使用FP32进行参数更新，从而减少内存使用并加速训练。
    """

    def __init__(self, enabled: bool = True, dtype: torch.dtype = torch.float16):
        """
        初始化混合精度训练

        Args:
            enabled: 是否启用混合精度训练
            dtype: 混合精度类型，默认为float16
        """
        self.enabled = enabled
        self.dtype = dtype
        # 使用新的GradScaler API
        self.scaler = GradScaler('cuda', enabled=enabled)

    def train_step(self, model: nn.Module, optimizer: torch.optim.Optimizer, loss_fn: Callable, *args, **kwargs) -> torch.Tensor:
        """
        混合精度训练步骤

        Args:
            model: 模型
            optimizer: 优化器
            loss_fn: 损失函数
            *args: 传递给损失函数的位置参数
            **kwargs: 传递给损失函数的关键字参数

        Returns:
            损失值
        """
        # 清空梯度
        optimizer.zero_grad()

        # 使用混合精度进行前向传播
        with autocast(device_type='cuda', dtype=self.dtype, enabled=self.enabled):
            # 计算损失
            loss = loss_fn(*args, **kwargs)

        # 使用梯度缩放器进行反向传播
        self.scaler.scale(loss).backward()

        # 使用梯度缩放器更新参数
        self.scaler.step(optimizer)

        # 更新缩放器
        self.scaler.update()

        return loss

    def inference(self, model: nn.Module, *args, **kwargs) -> Any:
        """
        混合精度推理

        Args:
            model: 模型
            *args: 传递给模型的位置参数
            **kwargs: 传递给模型的关键字参数

        Returns:
            模型输出
        """
        # 使用混合精度进行推理
        with torch.no_grad():
            with autocast(device_type='cuda', dtype=self.dtype, enabled=self.enabled):
                return model(*args, **kwargs)


class DistributedTrainingWrapper:
    """
    分布式训练包装器

    实现分布式训练，包装模型为分布式数据并行模型，
    并提供分布式训练的工具函数。
    """

    def __init__(
        self,
        backend: str = 'nccl',
        init_method: str = 'env://',
        world_size: Optional[int] = None,
        rank: Optional[int] = None,
        local_rank: Optional[int] = None,
        find_unused_parameters: bool = False
    ):
        """
        初始化分布式训练包装器

        Args:
            backend: 通信后端，默认为nccl（GPU）
            init_method: 初始化方法，默认为环境变量
            world_size: 总进程数，如果为None则从环境变量中获取
            rank: 当前进程的全局排名，如果为None则从环境变量中获取
            local_rank: 当前进程的本地排名，如果为None则从环境变量中获取
            find_unused_parameters: 是否查找未使用的参数，如果模型中有一些参数在前向传播中未使用，需要设置为True
        """
        self.backend = backend
        self.init_method = init_method
        self.find_unused_parameters = find_unused_parameters

        # 获取分布式训练参数
        self.world_size = world_size if world_size is not None else int(os.environ.get('WORLD_SIZE', 1))
        self.rank = rank if rank is not None else int(os.environ.get('RANK', 0))
        self.local_rank = local_rank if local_rank is not None else int(os.environ.get('LOCAL_RANK', 0))

        # 初始化分布式环境
        self.initialized = False

        # 设置日志
        self.logger = logging.getLogger(__name__)

    def init_process_group(self) -> None:
        """
        初始化进程组
        """
        if not self.initialized:
            # 设置当前设备
            torch.cuda.set_device(self.local_rank)

            # 初始化进程组
            dist.init_process_group(
                backend=self.backend,
                init_method=self.init_method,
                world_size=self.world_size,
                rank=self.rank
            )

            self.initialized = True
            self.logger.info(
                f"分布式训练已初始化 | "
                f"世界大小: {dist.get_world_size()} | "
                f"排名: {dist.get_rank()} | "
                f"本地排名: {self.local_rank}"
            )

    def cleanup(self) -> None:
        """
        清理分布式环境
        """
        if self.initialized and dist.is_initialized():
            dist.destroy_process_group()
            self.initialized = False
            self.logger.info("分布式训练环境已清理")

    def is_master(self) -> bool:
        """
        检查当前进程是否为主进程

        Returns:
            是否为主进程
        """
        if not self.initialized:
            return True

        return dist.get_rank() == 0

    def wrap_model(self, model: nn.Module) -> nn.Module:
        """
        将模型包装为分布式数据并行模型

        Args:
            model: 要包装的模型

        Returns:
            包装后的模型
        """
        if not self.initialized:
            self.init_process_group()

        # 将模型移动到当前设备
        device = torch.device(f'cuda:{self.local_rank}')
        model = model.to(device)

        # 包装为DDP模型
        ddp_model = DDP(
            model,
            device_ids=[self.local_rank],
            output_device=self.local_rank,
            find_unused_parameters=self.find_unused_parameters
        )

        self.logger.info(
            f"模型已包装为DDP | "
            f"设备: {device} | "
            f"排名: {dist.get_rank()}"
        )

        return ddp_model

    def all_reduce(self, tensor: torch.Tensor, op: str = 'sum') -> torch.Tensor:
        """
        在所有进程中汇总张量

        Args:
            tensor: 要汇总的张量
            op: 汇总操作，支持'sum'、'avg'、'max'、'min'

        Returns:
            汇总后的张量
        """
        if not self.initialized:
            return tensor

        # 确保张量在GPU上
        if not tensor.is_cuda:
            tensor = tensor.to(f'cuda:{self.local_rank}')

        # 选择操作
        if op == 'sum':
            reduce_op = dist.ReduceOp.SUM
        elif op == 'avg':
            reduce_op = dist.ReduceOp.SUM
        elif op == 'max':
            reduce_op = dist.ReduceOp.MAX
        elif op == 'min':
            reduce_op = dist.ReduceOp.MIN
        else:
            raise ValueError(f"不支持的操作: {op}")

        # 汇总张量
        dist.all_reduce(tensor, reduce_op)

        # 如果是平均操作，需要除以进程数
        if op == 'avg':
            tensor /= dist.get_world_size()

        return tensor

    def broadcast(self, tensor: torch.Tensor, src: int = 0) -> torch.Tensor:
        """
        从源进程广播张量到所有进程

        Args:
            tensor: 要广播的张量
            src: 源进程的排名

        Returns:
            广播后的张量
        """
        if not self.initialized:
            return tensor

        # 确保张量在GPU上
        if not tensor.is_cuda:
            tensor = tensor.to(f'cuda:{self.local_rank}')

        # 广播张量
        dist.broadcast(tensor, src)

        return tensor

    def barrier(self) -> None:
        """
        同步所有进程
        """
        if self.initialized:
            dist.barrier()

    def model_parallel_shard(self, model: nn.Module, num_gpus: int) -> List[nn.Module]:
        """
        将模型分片到多个GPU上进行模型并行

        Args:
            model: 要分片的模型
            num_gpus: GPU数量

        Returns:
            分片后的模型列表
        """
        if not isinstance(model, nn.Sequential):
            raise ValueError("只支持对Sequential模型进行模型并行分片")

        # 确保可用的GPU数量
        available_gpus = min(torch.cuda.device_count(), num_gpus)
        if available_gpus < 2:
            self.logger.warning(f"可用GPU数量不足两个，无法进行模型并行。当前可用: {available_gpus}")
            return [model.to(f'cuda:0')]

        # 计算每个GPU上的层数
        num_layers = len(model)
        layers_per_gpu = num_layers // available_gpus

        # 创建分片模型列表
        sharded_models = []

        for i in range(available_gpus):
            # 计算当前GPU的层范围
            start_idx = i * layers_per_gpu
            end_idx = (i + 1) * layers_per_gpu if i < available_gpus - 1 else num_layers

            # 创建分片模型
            shard = nn.Sequential(*list(model[start_idx:end_idx]))

            # 移动到对应的GPU
            device = f'cuda:{i}'
            shard = shard.to(device)

            sharded_models.append(shard)

            self.logger.info(f"模型分片 {i+1}/{available_gpus} | 层: {start_idx}-{end_idx-1} | 设备: {device}")

        return sharded_models


def test_compute_optimization():
    """
    测试计算优化功能
    """
    import torch.optim as optim
    import time
    import numpy as np

    # 设置随机种子以确保可重现性
    torch.manual_seed(42)
    np.random.seed(42)

    # 测试梯度检查点
    print("\n=== 测试梯度检查点 ===")

    # 创建一个简单的模型
    model = nn.Sequential(
        nn.Linear(100, 50),
        nn.ReLU(),
        nn.Linear(50, 25),
        nn.ReLU(),
        nn.Linear(25, 10)
    )

    # 应用梯度检查点
    checkpointed_model = GradientCheckpointing.apply(model, checkpoint_segments=2)

    # 测试模型
    input_data = torch.randn(32, 100)
    output = checkpointed_model(input_data)

    print(f"原始模型类型: {type(model).__name__}")
    print(f"检查点模型类型: {type(checkpointed_model).__name__}")
    print(f"输出形状: {output.shape}")

    # 测试激活值重计算
    print("\n=== 测试激活值重计算 ===")

    # 创建一个新模型
    model = nn.Sequential(
        nn.Linear(100, 50),
        nn.ReLU(),
        nn.Linear(50, 25),
        nn.Sigmoid(),
        nn.Linear(25, 10),
        nn.Tanh()
    )

    # 应用激活值重计算
    recomputed_model = ActivationRecomputation.apply(model)

    # 测试模型
    input_data = torch.randn(32, 100)
    output = recomputed_model(input_data)

    print(f"原始模型类型: {type(model).__name__}")
    print(f"重计算模型类型: {type(recomputed_model).__name__}")
    print(f"输出形状: {output.shape}")

    # 测试混合精度训练
    print("\n=== 测试混合精度训练 ===")

    # 创建一个新模型
    model = nn.Sequential(
        nn.Linear(100, 50),
        nn.ReLU(),
        nn.Linear(50, 10)
    )

    # 如果有GPU，将模型移动到GPU
    device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)

    # 创建优化器
    optimizer = optim.Adam(model.parameters(), lr=0.001)

    # 创建混合精度训练器
    mixed_precision = MixedPrecisionTraining(enabled=torch.cuda.is_available())

    # 定义损失函数
    def loss_fn(model, x, y):
        return torch.nn.functional.mse_loss(model(x), y)

    # 生成测试数据
    x = torch.randn(32, 100, device=device)
    y = torch.randn(32, 10, device=device)

    # 测试混合精度训练
    start_time = time.time()
    loss = mixed_precision.train_step(model, optimizer, loss_fn, model, x, y)
    training_time = time.time() - start_time

    print(f"混合精度训练时间: {training_time:.4f} 秒")
    print(f"损失值: {loss.item():.4f}")

    # 测试混合精度推理
    start_time = time.time()
    output = mixed_precision.inference(model, x)
    inference_time = time.time() - start_time

    print(f"混合精度推理时间: {inference_time:.4f} 秒")
    print(f"输出形状: {output.shape}")

    # 测试分布式训练包装器
    print("\n=== 测试分布式训练包装器 ===")

    # 创建分布式训练包装器
    distributed = DistributedTrainingWrapper()

    # 检查是否为主进程
    is_master = distributed.is_master()
    print(f"是否为主进程: {is_master}")

    # 测试模型并行
    if torch.cuda.device_count() >= 2:
        # 创建一个大模型
        big_model = nn.Sequential(
            nn.Linear(100, 80),
            nn.ReLU(),
            nn.Linear(80, 60),
            nn.ReLU(),
            nn.Linear(60, 40),
            nn.ReLU(),
            nn.Linear(40, 20),
            nn.ReLU(),
            nn.Linear(20, 10)
        )

        # 分片模型
        sharded_models = distributed.model_parallel_shard(big_model, torch.cuda.device_count())

        print(f"模型分片数量: {len(sharded_models)}")
        for i, shard in enumerate(sharded_models):
            print(f"分片 {i+1} 设备: {next(shard.parameters()).device}")
    else:
        print("可用GPU数量不足，跳过模型并行测试")

    # 测试张量操作
    if torch.cuda.is_available():
        # 创建一个张量
        tensor = torch.tensor([1.0, 2.0, 3.0], device='cuda:0')

        # 测试all_reduce
        reduced_tensor = distributed.all_reduce(tensor, op='sum')
        print(f"All-reduce结果: {reduced_tensor}")

        # 测试broadcast
        broadcasted_tensor = distributed.broadcast(tensor)
        print(f"Broadcast结果: {broadcasted_tensor}")
    else:
        print("无可用GPU，跳过张量操作测试")

    return {
        'gradient_checkpointing': GradientCheckpointing,
        'activation_recomputation': ActivationRecomputation,
        'mixed_precision': mixed_precision,
        'distributed': distributed
    }


if __name__ == "__main__":
    # 如果直接运行这个文件，则执行测试
    test_compute_optimization()