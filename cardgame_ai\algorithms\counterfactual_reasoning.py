"""
反事实推理模块

实现在规划阶段（如MCTS模拟或树搜索的特定分支）进行显式的反事实思考。
例如，评估"如果对手没有那张牌会怎样"或"如果我走了另一条路会怎样"，
以改进当前决策。
"""

import copy
import logging
from typing import Dict, List, Tuple, Any, Optional, Union

import numpy as np

from cardgame_ai.algorithms.mcts import Node, MCTS
from cardgame_ai.games.common.belief_state import BeliefState

# 配置日志
logger = logging.getLogger(__name__)


class CounterfactualAssumption:
    """
    反事实假设类

    表示一个反事实假设，如"对手没有某张牌"或"我选择了不同的动作"。
    用于在规划阶段进行反事实推理，评估不同假设下的决策结果。
    """

    def __init__(self, assumption_type: str, **kwargs):
        """
        初始化反事实假设

        Args:
            assumption_type: 假设类型，如"card_absence"、"action_change"等
            **kwargs: 与假设类型相关的参数
        """
        self.type = assumption_type
        self.params = kwargs

    def __str__(self) -> str:
        """返回假设的字符串表示"""
        if self.type == "card_absence":
            return f"假设玩家 {self.params['player_id']} 没有牌 {self.params['card']}"
        elif self.type == "card_presence":
            return f"假设玩家 {self.params['player_id']} 有牌 {self.params['card']}"
        elif self.type == "action_change":
            return f"假设选择了动作 {self.params['action']} 而不是 {self.params['original_action']}"
        else:
            return f"假设类型 {self.type} 参数 {self.params}"

    @classmethod
    def card_absence(cls, player_id: str, card: str) -> 'CounterfactualAssumption':
        """
        创建"对手没有某张牌"的假设

        Args:
            player_id: 玩家ID
            card: 牌的表示

        Returns:
            CounterfactualAssumption: 创建的假设
        """
        return cls("card_absence", player_id=player_id, card=card)

    @classmethod
    def card_presence(cls, player_id: str, card: str) -> 'CounterfactualAssumption':
        """
        创建"对手有某张牌"的假设

        Args:
            player_id: 玩家ID
            card: 牌的表示

        Returns:
            CounterfactualAssumption: 创建的假设
        """
        return cls("card_presence", player_id=player_id, card=card)

    @classmethod
    def action_change(cls, action: int, original_action: int) -> 'CounterfactualAssumption':
        """
        创建"选择了不同动作"的假设

        Args:
            action: 假设选择的动作
            original_action: 原始选择的动作

        Returns:
            CounterfactualAssumption: 创建的假设
        """
        return cls("action_change", action=action, original_action=original_action)


class BeliefStateModifier:
    """
    信念状态修改器

    根据反事实假设修改信念状态，用于反事实推理。
    """

    @staticmethod
    def modify_belief_state(
        belief_state: BeliefState,
        assumption: CounterfactualAssumption
    ) -> BeliefState:
        """
        根据假设修改信念状态

        Args:
            belief_state: 原始信念状态
            assumption: 反事实假设

        Returns:
            BeliefState: 修改后的信念状态
        """
        # 创建信念状态的深拷贝
        modified_belief = copy.deepcopy(belief_state)

        # 根据假设类型修改信念状态
        if assumption.type == "card_absence":
            # 设置特定牌的概率为0
            player_id = assumption.params["player_id"]
            card = assumption.params["card"]

            # 修改信念状态中的牌概率
            if hasattr(modified_belief, "card_probabilities") and player_id in modified_belief.card_probabilities:
                if card in modified_belief.card_probabilities[player_id]:
                    modified_belief.card_probabilities[player_id][card] = 0.0

                    # 重新归一化概率
                    total_prob = sum(modified_belief.card_probabilities[player_id].values())
                    if total_prob > 0:
                        for c in modified_belief.card_probabilities[player_id]:
                            modified_belief.card_probabilities[player_id][c] /= total_prob

        elif assumption.type == "card_presence":
            # 设置特定牌的概率为1，其他牌为0
            player_id = assumption.params["player_id"]
            card = assumption.params["card"]

            # 修改信念状态中的牌概率
            if hasattr(modified_belief, "card_probabilities") and player_id in modified_belief.card_probabilities:
                for c in modified_belief.card_probabilities[player_id]:
                    if c == card:
                        modified_belief.card_probabilities[player_id][c] = 1.0
                    else:
                        modified_belief.card_probabilities[player_id][c] = 0.0

        # 标记为反事实信念状态
        modified_belief.is_counterfactual = True
        modified_belief.assumption = str(assumption)

        return modified_belief


class CounterfactualMCTS(MCTS):
    """
    反事实蒙特卡洛树搜索

    扩展标准MCTS，支持反事实推理，评估不同假设下的决策结果。
    """

    def __init__(
        self,
        num_simulations: int = 50,
        discount: float = 0.997,
        dirichlet_alpha: float = 0.25,
        exploration_fraction: float = 0.25,
        pb_c_base: int = 19652,
        pb_c_init: float = 1.25,
        use_belief_state: bool = True,
        use_information_value: bool = True,
        information_value_weight: float = 0.5,
        counterfactual_weight: float = 0.3,
        max_counterfactual_assumptions: int = 3,
        counterfactual_threshold: float = 0.1
    ):
        """
        初始化反事实MCTS

        Args:
            num_simulations: 每次决策的模拟次数
            discount: 折扣因子
            dirichlet_alpha: Dirichlet噪声参数
            exploration_fraction: 探索噪声比例
            pb_c_base: PUCT公式的基础参数
            pb_c_init: PUCT公式的初始化参数
            use_belief_state: 是否使用信念状态
            use_information_value: 是否考虑信息价值
            information_value_weight: 信息价值权重
            counterfactual_weight: 反事实推理权重
            max_counterfactual_assumptions: 最大反事实假设数量
            counterfactual_threshold: 反事实假设阈值
        """
        super().__init__(
            num_simulations=num_simulations,
            discount=discount,
            dirichlet_alpha=dirichlet_alpha,
            exploration_fraction=exploration_fraction,
            pb_c_base=pb_c_base,
            pb_c_init=pb_c_init,
            use_belief_state=use_belief_state,
            use_information_value=use_information_value,
            information_value_weight=information_value_weight
        )

        # 反事实推理参数
        self.counterfactual_weight = counterfactual_weight
        self.max_counterfactual_assumptions = max_counterfactual_assumptions
        self.counterfactual_threshold = counterfactual_threshold

        # 存储反事实假设及其结果
        self.counterfactual_results = {}

    def run(
        self,
        root_state: Any,
        model: Any,
        temperature: float = 1.0,
        actions_mask: Optional[List[int]] = None,
        belief_trackers: Optional[Dict[str, Any]] = None,
        current_player_id: Optional[str] = None,
        explain: bool = False
    ) -> Union[Tuple[Dict[int, int], Dict[int, float]], Tuple[Dict[int, int], Dict[int, float], Dict[str, Any]]]:
        """
        执行反事实MCTS搜索

        Args:
            root_state: 根状态
            model: 模型
            temperature: 温度参数
            actions_mask: 合法动作掩码
            belief_trackers: 信念追踪器字典
            current_player_id: 当前玩家ID
            explain: 是否生成解释

        Returns:
            访问计数、策略和可选的解释数据
        """
        # 首先执行标准MCTS搜索
        result = super().run(
            root_state=root_state,
            model=model,
            temperature=temperature,
            actions_mask=actions_mask,
            belief_trackers=belief_trackers,
            current_player_id=current_player_id,
            explain=explain
        )

        # 清除之前的反事实结果
        self.counterfactual_results = {}

        # 如果启用了信念状态且有信念追踪器，执行反事实推理
        if self.use_belief_state and belief_trackers and current_player_id:
            # 生成反事实假设
            assumptions = self._generate_counterfactual_assumptions(
                belief_trackers, current_player_id, actions_mask
            )

            # 对每个假设执行反事实搜索
            for assumption in assumptions:
                # 修改信念状态
                modified_belief_trackers = self._modify_belief_trackers(
                    belief_trackers, assumption
                )

                # 执行反事实搜索
                cf_result = self._run_counterfactual_search(
                    root_state=root_state,
                    model=model,
                    temperature=temperature,
                    actions_mask=actions_mask,
                    belief_trackers=modified_belief_trackers,
                    current_player_id=current_player_id
                )

                # 存储反事实结果
                self.counterfactual_results[str(assumption)] = cf_result

            # 整合反事实结果到访问计数和策略中
            if explain:
                visit_counts, pi, explanation_data = result
                # 整合反事实结果
                visit_counts, pi = self.integrate_counterfactual_results(visit_counts, pi)
                # 添加反事实结果到解释数据
                explanation_data["counterfactual_results"] = self.counterfactual_results
                return visit_counts, pi, explanation_data
            else:
                visit_counts, pi = result
                # 整合反事实结果
                visit_counts, pi = self.integrate_counterfactual_results(visit_counts, pi)
                return visit_counts, pi

        return result

    def _generate_counterfactual_assumptions(
        self,
        belief_trackers: Dict[str, Any],
        current_player_id: str,
        actions_mask: Optional[List[int]] = None
    ) -> List[CounterfactualAssumption]:
        """
        生成反事实假设

        Args:
            belief_trackers: 信念追踪器字典
            current_player_id: 当前玩家ID
            actions_mask: 合法动作掩码，用于生成"选择了不同动作"的假设

        Returns:
            List[CounterfactualAssumption]: 生成的反事实假设列表
        """
        assumptions = []

        # 获取当前玩家的信念追踪器
        current_tracker = belief_trackers.get(current_player_id)
        if not current_tracker:
            return assumptions

        # 获取信念状态
        belief_state = current_tracker.get_belief_state()
        if not belief_state:
            return assumptions

        # 1. 生成"对手没有某张牌"的假设
        if hasattr(belief_state, "card_probabilities"):
            # 遍历所有玩家
            for player_id, card_probs in belief_state.card_probabilities.items():
                # 跳过当前玩家
                if player_id == current_player_id:
                    continue

                # 找出概率较高的牌
                high_prob_cards = []
                for card, prob in card_probs.items():
                    if prob > self.counterfactual_threshold:
                        high_prob_cards.append((card, prob))

                # 按概率排序，选择概率最高的几张牌
                high_prob_cards.sort(key=lambda x: x[1], reverse=True)
                for card, prob in high_prob_cards[:self.max_counterfactual_assumptions]:
                    assumptions.append(CounterfactualAssumption.card_absence(player_id, card))

        # 2. 生成"对手有某张牌"的假设
        # 类似上面的逻辑，但选择概率中等的牌
        if hasattr(belief_state, "card_probabilities"):
            for player_id, card_probs in belief_state.card_probabilities.items():
                if player_id == current_player_id:
                    continue

                # 找出概率中等的牌
                mid_prob_cards = []
                for card, prob in card_probs.items():
                    if 0.3 < prob < 0.7:  # 中等概率范围
                        mid_prob_cards.append((card, prob))

                # 选择几张牌
                mid_prob_cards.sort(key=lambda x: abs(x[1] - 0.5))  # 按接近0.5排序
                for card, prob in mid_prob_cards[:self.max_counterfactual_assumptions // 2]:
                    assumptions.append(CounterfactualAssumption.card_presence(player_id, card))

        # 3. 生成"选择了不同动作"的假设
        # 这需要访问MCTS的根节点，获取访问次数第二高的动作
        if hasattr(self, "last_root") and self.last_root and self.last_root.children:
            # 获取访问次数排序
            actions_by_visits = sorted(
                [(action, child.visit_count) for action, child in self.last_root.children.items()],
                key=lambda x: x[1],
                reverse=True
            )

            # 如果有多个动作，考虑访问次数第二高的动作
            if len(actions_by_visits) > 1:
                best_action = actions_by_visits[0][0]
                second_best_action = actions_by_visits[1][0]

                # 创建"选择了不同动作"的假设
                assumptions.append(
                    CounterfactualAssumption.action_change(
                        action=second_best_action,
                        original_action=best_action
                    )
                )

        # 4. 如果提供了动作掩码，考虑其他合法动作
        if actions_mask is not None:
            # 获取所有合法动作
            legal_actions = [i for i, mask in enumerate(actions_mask) if mask]

            # 如果有足够多的合法动作，随机选择一些进行反事实推理
            if len(legal_actions) > 2:
                # 如果有last_root，排除已经考虑过的动作
                excluded_actions = []
                if hasattr(self, "last_root") and self.last_root and self.last_root.children:
                    # 获取访问次数最高的几个动作
                    top_actions = [action for action, _ in sorted(
                        [(action, child.visit_count) for action, child in self.last_root.children.items()],
                        key=lambda x: x[1],
                        reverse=True
                    )[:2]]  # 排除前两个动作
                    excluded_actions.extend(top_actions)

                # 过滤掉已考虑的动作
                candidate_actions = [a for a in legal_actions if a not in excluded_actions]

                # 随机选择一些动作
                if candidate_actions:
                    num_to_select = min(2, len(candidate_actions))
                    selected_actions = np.random.choice(
                        candidate_actions,
                        size=num_to_select,
                        replace=False
                    )

                    # 为每个选择的动作创建假设
                    for action in selected_actions:
                        # 如果有last_root，使用最佳动作作为对比
                        if hasattr(self, "last_root") and self.last_root and self.last_root.children:
                            best_action = max(
                                self.last_root.children.items(),
                                key=lambda x: x[1].visit_count
                            )[0]

                            assumptions.append(
                                CounterfactualAssumption.action_change(
                                    action=action,
                                    original_action=best_action
                                )
                            )

        # 限制假设数量
        if len(assumptions) > self.max_counterfactual_assumptions:
            assumptions = assumptions[:self.max_counterfactual_assumptions]

        return assumptions

    def _modify_belief_trackers(
        self,
        belief_trackers: Dict[str, Any],
        assumption: CounterfactualAssumption
    ) -> Dict[str, Any]:
        """
        根据假设修改信念追踪器

        Args:
            belief_trackers: 原始信念追踪器字典
            assumption: 反事实假设

        Returns:
            Dict[str, Any]: 修改后的信念追踪器字典
        """
        # 创建信念追踪器的深拷贝
        modified_trackers = copy.deepcopy(belief_trackers)

        # 根据假设类型修改信念状态
        if assumption.type in ["card_absence", "card_presence"]:
            player_id = assumption.params["player_id"]

            # 获取玩家的信念追踪器
            if player_id in modified_trackers:
                # 获取信念状态
                belief_state = modified_trackers[player_id].get_belief_state()
                if belief_state:
                    # 修改信念状态
                    modified_belief = BeliefStateModifier.modify_belief_state(
                        belief_state, assumption
                    )

                    # 更新信念追踪器
                    modified_trackers[player_id].set_belief_state(modified_belief)

        return modified_trackers

    def _run_counterfactual_search(
        self,
        root_state: Any,
        model: Any,
        temperature: float = 1.0,
        actions_mask: Optional[List[int]] = None,
        belief_trackers: Optional[Dict[str, Any]] = None,
        current_player_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        执行反事实搜索

        Args:
            root_state: 根状态
            model: 模型
            temperature: 温度参数
            actions_mask: 合法动作掩码
            belief_trackers: 修改后的信念追踪器字典
            current_player_id: 当前玩家ID

        Returns:
            Dict[str, Any]: 反事实搜索结果
        """
        # 创建一个新的MCTS实例，避免修改当前实例的状态
        cf_mcts = MCTS(
            num_simulations=self.num_simulations // 2,  # 减少模拟次数
            discount=self.discount,
            dirichlet_alpha=self.dirichlet_alpha,
            exploration_fraction=self.exploration_fraction,
            pb_c_base=self.pb_c_base,
            pb_c_init=self.pb_c_init,
            use_belief_state=self.use_belief_state,
            use_information_value=self.use_information_value,
            information_value_weight=self.information_value_weight
        )

        # 执行搜索
        visit_counts, pi = cf_mcts.run(
            root_state=root_state,
            model=model,
            temperature=temperature,
            actions_mask=actions_mask,
            belief_trackers=belief_trackers,
            current_player_id=current_player_id
        )

        # 返回结果
        return {
            "visit_counts": visit_counts,
            "pi": pi,
            "best_action": max(visit_counts.items(), key=lambda x: x[1])[0] if visit_counts else None
        }

    def _select_child(self, node: Node, belief_trackers: Optional[Dict[str, Any]] = None) -> Tuple[int, Node]:
        """
        使用PUCT公式选择子节点，并考虑反事实推理结果

        重写父类方法，在选择子节点时考虑反事实推理的结果。

        Args:
            node (Node): 当前节点
            belief_trackers (Optional[Dict[str, Any]], optional): 信念追踪器字典，键为玩家ID. Defaults to None.

        Returns:
            Tuple[int, Node]: 选择的动作和对应的子节点
        """
        # 首先使用父类方法选择子节点
        action, child = super()._select_child(node, belief_trackers)

        # 如果有反事实结果，考虑它们的影响
        if hasattr(self, "counterfactual_results") and self.counterfactual_results:
            # 检查是否是根节点
            if node == self.last_root:
                # 获取反事实结果中的最佳动作
                cf_best_actions = {}
                for assumption, result in self.counterfactual_results.items():
                    if "best_action" in result and result["best_action"] is not None:
                        cf_action = result["best_action"]
                        if cf_action not in cf_best_actions:
                            cf_best_actions[cf_action] = 0
                        cf_best_actions[cf_action] += 1

                # 如果有多个反事实结果指向同一个动作，考虑选择它
                if cf_best_actions:
                    most_common_cf_action = max(cf_best_actions.items(), key=lambda x: x[1])[0]

                    # 如果反事实最佳动作与标准选择不同，且有足够的支持
                    if most_common_cf_action != action and cf_best_actions[most_common_cf_action] >= len(self.counterfactual_results) / 2:
                        # 获取对应的子节点
                        if most_common_cf_action in node.children:
                            # 根据权重决定是否采用反事实结果
                            if np.random.random() < self.counterfactual_weight:
                                return most_common_cf_action, node.children[most_common_cf_action]

        return action, child

    def integrate_counterfactual_results(self, visit_counts: Dict[int, int], pi: Dict[int, float]) -> Tuple[Dict[int, int], Dict[int, float]]:
        """
        整合反事实推理结果

        将反事实推理的结果整合到最终的访问计数和策略中。

        Args:
            visit_counts: 原始访问计数
            pi: 原始策略

        Returns:
            Tuple[Dict[int, int], Dict[int, float]]: 整合后的访问计数和策略
        """
        # 如果没有反事实结果，直接返回原始结果
        if not self.counterfactual_results:
            return visit_counts, pi

        # 创建新的访问计数和策略
        new_visit_counts = visit_counts.copy()
        new_pi = pi.copy()

        # 统计反事实结果中的最佳动作
        cf_action_counts = {}
        for assumption, result in self.counterfactual_results.items():
            if "best_action" in result and result["best_action"] is not None:
                cf_action = result["best_action"]
                if cf_action not in cf_action_counts:
                    cf_action_counts[cf_action] = 0
                cf_action_counts[cf_action] += 1

        # 如果有多个反事实结果指向同一个动作，增加其访问计数
        if cf_action_counts:
            for action, count in cf_action_counts.items():
                if action in new_visit_counts:
                    # 根据反事实权重和支持度增加访问计数
                    additional_visits = int(
                        self.counterfactual_weight * count * self.num_simulations / len(self.counterfactual_results)
                    )
                    new_visit_counts[action] += additional_visits

            # 重新计算策略
            if sum(new_visit_counts.values()) > 0:
                for action in new_pi:
                    new_pi[action] = new_visit_counts[action] / sum(new_visit_counts.values())

        return new_visit_counts, new_pi
