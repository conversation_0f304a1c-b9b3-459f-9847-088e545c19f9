# -*- mode: python ; coding: utf-8 -*-

import os
import sys
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# 添加项目根目录到路径
# 处理脚本直接运行时的情况
try:
    script_dir = os.path.dirname(__file__)
except NameError:  # __file__ 不存在时
    script_dir = os.path.abspath('cardgame_ai/scripts')

project_root = os.path.abspath(os.path.join(script_dir, '..'))
sys.path.insert(0, project_root)

# 定义版本号
__version__ = "0.1.0"

# 定义应用程序名称
app_name = f"CardGameAI-{__version__}"

# 定义入口点
entry_point = 'G:/kaifa/xiangmu/hid5/bqq/cardgame_ai/desktop/main.py'

# 收集数据文件
datas = []
# 添加配置文件
datas += [(os.path.join(project_root, 'config'), 'config')]
# 添加资源文件
datas += [(os.path.join(project_root, 'desktop', 'resources'), 'desktop/resources')]
# 添加翻译文件
datas += [(os.path.join(project_root, 'desktop', 'translations'), 'desktop/translations')]

# 收集隐式导入的模块
hiddenimports = []
hiddenimports += collect_submodules('cardgame_ai')
hiddenimports += collect_submodules('PySide6')

# 定义分析对象
a = Analysis(
    [entry_point],
    pathex=[project_root],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# 定义PYZ对象
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# 定义可执行文件
exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name=app_name,
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # 设置为False以隐藏控制台窗口
    icon=os.path.join(project_root, 'desktop', 'resources', 'icons', 'app_icon.ico'),
)

# 定义收集对象
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name=app_name,
)

# 为macOS创建应用程序包
if sys.platform == 'darwin':
    app = BUNDLE(
        coll,
        name=f"{app_name}.app",
        icon=os.path.join(project_root, 'desktop', 'resources', 'icons', 'app_icon.icns'),
        bundle_identifier='com.example.cardgameai',
    )
