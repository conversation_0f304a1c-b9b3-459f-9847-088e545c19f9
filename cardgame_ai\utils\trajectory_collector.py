"""
轨迹收集器模块

提供实时完整对局轨迹收集功能，支持不同格式的轨迹数据存储和加载。
"""
import os
import json
import pickle
import time
import datetime
import logging
from typing import Dict, List, Any, Optional, Union, Tuple

from cardgame_ai.core.base import State, Action


class TrajectoryCollector:
    """
    轨迹收集器类

    用于实时收集完整的对局轨迹数据，支持不同格式的存储和加载。
    """

    def __init__(
        self,
        save_dir: str = "data/trajectories",
        format: str = "json",
        auto_save: bool = True,
        save_interval: int = 1,
        compress: bool = False
    ):
        """
        初始化轨迹收集器

        Args:
            save_dir: 轨迹数据保存目录
            format: 轨迹数据保存格式，支持 'json' 和 'pickle'
            auto_save: 是否自动保存轨迹数据
            save_interval: 自动保存的间隔（步数）
            compress: 是否压缩轨迹数据
        """
        self.save_dir = save_dir
        self.format = format
        self.auto_save = auto_save
        self.save_interval = save_interval
        self.compress = compress

        # 创建保存目录
        os.makedirs(save_dir, exist_ok=True)

        # 初始化轨迹数据
        self.clear()

        # 配置日志
        self.logger = logging.getLogger(__name__)

    def clear(self):
        """
        清空轨迹数据
        """
        # 游戏信息
        self.game_info = {
            "id": None,
            "type": None,
            "player_role": None,
            "ai_model": None,
            "start_time": time.time(),
            "end_time": None
        }

        # 轨迹数据
        self.trajectory = []

        # 元数据
        self.metadata = {
            "version": "1.0",
            "format": self.format,
            "created_at": datetime.datetime.now().isoformat()
        }

        # 步数计数器
        self.step_counter = 0

        # 最后保存时间
        self.last_save_time = time.time()

    def add_game_info(self, game_id: str, game_type: str, player_role: str, ai_model: str):
        """
        添加游戏信息

        Args:
            game_id: 游戏ID
            game_type: 游戏类型
            player_role: 玩家角色
            ai_model: AI模型
        """
        self.game_info["id"] = game_id
        self.game_info["type"] = game_type
        self.game_info["player_role"] = player_role
        self.game_info["ai_model"] = ai_model
        self.game_info["start_time"] = time.time()

    def add_step(
        self,
        state: Union[State, Dict[str, Any]],
        action: Union[Action, Dict[str, Any]],
        reward: float,
        next_state: Union[State, Dict[str, Any]],
        done: bool,
        info: Optional[Dict[str, Any]] = None
    ):
        """
        添加一步轨迹数据

        Args:
            state: 当前状态
            action: 执行的动作
            reward: 获得的奖励
            next_state: 下一状态
            done: 是否结束
            info: 额外信息
        """
        # 转换状态和动作为字典
        state_dict = state.to_dict() if hasattr(state, 'to_dict') else state
        action_dict = action.to_dict() if hasattr(action, 'to_dict') else action
        next_state_dict = next_state.to_dict() if hasattr(next_state, 'to_dict') else next_state

        # 创建步骤数据
        step_data = {
            "step": self.step_counter,
            "state": state_dict,
            "action": action_dict,
            "reward": reward,
            "next_state": next_state_dict,
            "done": done,
            "info": info or {},
            "timestamp": time.time()
        }

        # 添加到轨迹
        self.trajectory.append(step_data)

        # 更新步数计数器
        self.step_counter += 1

        # 如果启用自动保存且达到保存间隔，则保存轨迹
        if self.auto_save and self.step_counter % self.save_interval == 0:
            self.save()

    def end_game(self):
        """
        结束游戏，记录结束时间
        """
        self.game_info["end_time"] = time.time()

        # 保存最终轨迹
        if self.auto_save:
            self.save()

    def save(self, file_path: Optional[str] = None) -> str:
        """
        保存轨迹数据

        Args:
            file_path: 保存路径，如果为None则自动生成

        Returns:
            str: 保存的文件路径
        """
        # 如果未指定文件路径，则自动生成
        if file_path is None:
            game_id = self.game_info["id"] or f"game_{int(time.time())}"
            file_name = f"{game_id}_trajectory.{self.format}"
            file_path = os.path.join(self.save_dir, file_name)

        # 准备完整的轨迹数据
        trajectory_data = self.get_trajectory()

        # 根据格式保存
        if self.format == "json":
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(trajectory_data, f, indent=2, ensure_ascii=False)
        elif self.format == "pickle":
            with open(file_path, 'wb') as f:
                pickle.dump(trajectory_data, f)
        else:
            raise ValueError(f"不支持的格式: {self.format}")

        self.logger.info(f"轨迹数据已保存到: {file_path}")
        self.last_save_time = time.time()

        return file_path

    def load(self, file_path: str) -> Dict[str, Any]:
        """
        加载轨迹数据

        Args:
            file_path: 文件路径

        Returns:
            Dict[str, Any]: 轨迹数据
        """
        # 根据文件扩展名确定格式
        file_ext = os.path.splitext(file_path)[1].lower()

        if file_ext == ".json":
            with open(file_path, 'r', encoding='utf-8') as f:
                trajectory_data = json.load(f)
        elif file_ext in [".pickle", ".pkl"]:
            with open(file_path, 'rb') as f:
                trajectory_data = pickle.load(f)
        else:
            raise ValueError(f"不支持的文件格式: {file_ext}")

        # 更新当前轨迹数据
        self.game_info = trajectory_data.get("game_info", {})
        self.trajectory = trajectory_data.get("trajectory", [])
        self.metadata = trajectory_data.get("metadata", {})
        self.step_counter = len(self.trajectory)

        self.logger.info(f"已加载轨迹数据: {file_path}")

        return trajectory_data

    def get_trajectory(self) -> Dict[str, Any]:
        """
        获取完整轨迹数据

        Returns:
            Dict[str, Any]: 完整轨迹数据
        """
        return {
            "game_info": self.game_info,
            "trajectory": self.trajectory,
            "metadata": self.metadata
        }

    def analyze(self) -> Dict[str, Any]:
        """
        分析轨迹数据

        Returns:
            Dict[str, Any]: 分析结果
        """
        if not self.trajectory:
            return {"error": "轨迹为空"}

        # 基本统计
        total_steps = len(self.trajectory)
        total_reward = sum(step["reward"] for step in self.trajectory)
        avg_reward = total_reward / total_steps if total_steps > 0 else 0

        # 游戏时长
        start_time = self.game_info.get("start_time")
        end_time = self.game_info.get("end_time") or time.time()
        duration = end_time - start_time if start_time else 0

        # 动作统计
        action_counts = {}
        for step in self.trajectory:
            action = step.get("action", {})
            action_type = action.get("type", "unknown")
            if action_type not in action_counts:
                action_counts[action_type] = 0
            action_counts[action_type] += 1

        # 奖励统计
        rewards = [step["reward"] for step in self.trajectory]
        min_reward = min(rewards) if rewards else 0
        max_reward = max(rewards) if rewards else 0

        return {
            "total_steps": total_steps,
            "total_reward": total_reward,
            "avg_reward": avg_reward,
            "min_reward": min_reward,
            "max_reward": max_reward,
            "duration": duration,
            "action_counts": action_counts,
            "game_info": self.game_info
        }

    def to_experiences(self, state_cls=None, action_cls=None) -> List[Dict[str, Any]]:
        """
        将轨迹转换为经验列表

        Args:
            state_cls: 状态类，用于反序列化状态
            action_cls: 动作类，用于反序列化动作

        Returns:
            List[Dict[str, Any]]: 经验列表
        """
        experiences = []

        for i in range(len(self.trajectory) - 1):
            step = self.trajectory[i]
            next_step = self.trajectory[i + 1]

            # 创建经验
            experience = {
                "state": step["state"],
                "action": step["action"],
                "reward": step["reward"],
                "next_state": next_step["state"],
                "done": step["done"],
                "info": step["info"]
            }

            experiences.append(experience)

        # 处理最后一步
        if self.trajectory:
            last_step = self.trajectory[-1]
            experience = {
                "state": last_step["state"],
                "action": last_step["action"],
                "reward": last_step["reward"],
                "next_state": last_step["next_state"],
                "done": last_step["done"],
                "info": last_step["info"]
            }
            experiences.append(experience)

        return experiences
