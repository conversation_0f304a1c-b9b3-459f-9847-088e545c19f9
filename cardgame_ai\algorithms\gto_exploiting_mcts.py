"""
GTO 偏离剥削增强型 MCTS

实现一个扩展 MCTS 算法，在搜索过程中直接融入对 GTO 偏离的剥削能力，
通过在节点选择和价值回传过程中考虑对手策略偏离信息。
"""

import math
import logging
import torch
import numpy as np
from typing import Dict, List, Optional, Tuple, Union, Any

# 导入原始 MCTS 类
from cardgame_ai.algorithms.mcts import MCTS, Node
# 导入偏离检测器
from cardgame_ai.algorithms.opponent_modeling.deviation_detector import DeviationDetector, DeviationToExploitMapper

# 配置日志
logger = logging.getLogger(__name__)


class ExploitNode(Node):
    """
    增强型 MCTS 节点，支持 GTO 偏离剥削
    
    扩展标准 MCTS 节点，增加存储对手偏离信息的功能。
    """
    
    def __init__(self, prior: float = 0.0, player_id_to_act: str = None, use_value_distribution: bool = False, value_support_size: int = 601):
        """
        初始化带有剥削能力的 MCTS 节点
        
        Args:
            prior: 从策略网络获得的先验概率
            player_id_to_act: 当前节点轮到行动的玩家ID
            use_value_distribution: 是否使用值分布
            value_support_size: 值分布的支持大小
        """
        super().__init__(prior, player_id_to_act, use_value_distribution, value_support_size)
        
        # 偏离信息
        self.deviation_score = 0.0        # 偏离分数
        self.deviation_pattern = None     # 偏离模式
        self.deviation_confidence = 0.0   # 偏离置信度
        self.exploitation_factor = 1.0    # 剥削因子
        
        # 剥削特定信息
        self.exploitation_bonus = 0.0     # 剥削奖励
        self.exploitation_update_weight = 1.0  # 剥削更新权重
        
        # 对手偏好信息
        self.opponent_action_prefs = {}   # 对手动作偏好 {action_type: preference_score}
        
    def get_explanation_data(self) -> Dict[str, Any]:
        """
        获取节点的解释数据
        
        Returns:
            Dict[str, Any]: 包含节点统计信息的字典，用于解释决策过程
        """
        explanation_data = super().get_explanation_data()
        
        # 添加剥削相关信息
        if self.deviation_score > 0:
            explanation_data['deviation_score'] = self.deviation_score
            
        if self.deviation_pattern:
            explanation_data['deviation_pattern'] = self.deviation_pattern
            explanation_data['deviation_confidence'] = self.deviation_confidence
            
        if self.exploitation_bonus != 0.0:
            explanation_data['exploitation_bonus'] = self.exploitation_bonus
            
        return explanation_data


class GTOExploitingMCTS(MCTS):
    """
    GTO 偏离剥削增强型 MCTS
    
    扩展标准 MCTS 算法，在搜索过程中直接融入对 GTO 偏离的剥削能力。
    通过修改节点选择 (UCB 计算) 和价值回传过程，基于对手的 GTO 偏离程度
    调整搜索方向和价值估计，以实现更有效的对手剥削。
    """
    
    def __init__(
        self,
        num_simulations: int = 50,
        discount: float = 0.997,
        dirichlet_alpha: float = 0.25,
        exploration_fraction: float = 0.25,
        pb_c_base: int = 19652,
        pb_c_init: float = 1.25,
        root_exploration_noise: bool = True,
        use_belief_state: bool = False,
        # GTO 偏离剥削相关参数
        deviation_detector: Optional[DeviationDetector] = None,
        exploit_mapper: Optional[DeviationToExploitMapper] = None,
        use_gto_exploitation: bool = True,
        exploitation_strength: float = 1.0,
        exploitation_threshold: float = 0.3,
        dynamic_exploitation: bool = True,
        backprop_exploitation_weight: float = 0.5,
        exploitation_memory_size: int = 20,
        exploit_known_patterns: bool = True,
        pattern_bonus_factor: float = 1.2,
        **kwargs  # 支持传递额外参数给父类
    ):
        """
        初始化 GTO 偏离剥削增强型 MCTS
        
        Args:
            num_simulations: 每次行动前的模拟次数
            discount: 折扣因子
            dirichlet_alpha: Dirichlet噪声参数
            exploration_fraction: 根节点探索噪声比例
            pb_c_base: PUCT公式基础常数
            pb_c_init: PUCT公式初始常数
            root_exploration_noise: 是否在根节点添加探索噪声
            use_belief_state: 是否使用信念状态
            deviation_detector: 偏离检测器实例
            exploit_mapper: 偏离剥削映射器实例
            use_gto_exploitation: 是否启用 GTO 偏离剥削
            exploitation_strength: 剥削强度，控制剥削调整的幅度
            exploitation_threshold: 剥削阈值，偏离分数低于此值不进行剥削
            dynamic_exploitation: 是否启用动态剥削强度
            backprop_exploitation_weight: 反向传播阶段剥削权重
            exploitation_memory_size: 剥削记忆大小，保存的历史偏离记录数量
            exploit_known_patterns: 是否利用已知的偏离模式
            pattern_bonus_factor: 模式奖励因子，控制模式识别的剥削强度
            **kwargs: 传递给父类的额外参数
        """
        super().__init__(
            num_simulations=num_simulations,
            discount=discount, 
            dirichlet_alpha=dirichlet_alpha,
            exploration_fraction=exploration_fraction,
            pb_c_base=pb_c_base,
            pb_c_init=pb_c_init,
            root_exploration_noise=root_exploration_noise,
            use_belief_state=use_belief_state,
            **kwargs
        )
        
        # 设置 GTO 偏离剥削相关参数
        self.deviation_detector = deviation_detector
        self.exploit_mapper = exploit_mapper
        self.use_gto_exploitation = use_gto_exploitation
        self.exploitation_strength = exploitation_strength
        self.exploitation_threshold = exploitation_threshold
        self.dynamic_exploitation = dynamic_exploitation
        self.backprop_exploitation_weight = backprop_exploitation_weight
        self.exploitation_memory_size = exploitation_memory_size
        self.exploit_known_patterns = exploit_known_patterns
        self.pattern_bonus_factor = pattern_bonus_factor
        
        # 初始化剥削记忆
        self.exploitation_memory = []
        
        # 检查是否提供了偏离检测器和剥削映射器
        if self.use_gto_exploitation and (self.deviation_detector is None or self.exploit_mapper is None):
            logger.warning("启用 GTO 偏离剥削功能但未提供偏离检测器或剥削映射器，将尝试创建默认实例")
    
    def run(
        self,
        root_state: Any,
        model: Any,
        temperature: float = 1.0,
        actions_mask: Optional[List[int]] = None,
        deviation_info: Optional[Dict[str, Any]] = None,
        **kwargs  # 支持传递额外参数给父类
    ) -> Union[Tuple[Dict[int, int], Dict[int, float]], Tuple[Dict[int, int], Dict[int, float], Dict[str, Any]]]:
        """
        运行 MCTS 搜索
        
        Args:
            root_state: 根状态
            model: 用于预测策略和价值的模型
            temperature: 温度参数，控制搜索结果的确定性
            actions_mask: 合法动作掩码
            deviation_info: 对手偏离信息，可选
            **kwargs: 传递给父类的额外参数
        
        Returns:
            visit_counts: 动作访问计数
            policy: 搜索产生的策略
            explanation: 解释数据 (如果 explain=True)
        """
        # 如果提供了偏离信息，更新剥削记忆
        if deviation_info and self.use_gto_exploitation:
            self._update_exploitation_memory(deviation_info)
        
        # 调用父类的 run 方法
        return super().run(
            root_state=root_state,
            model=model,
            temperature=temperature,
            actions_mask=actions_mask,
            **kwargs
        )
    
    def _update_exploitation_memory(self, deviation_info: Dict[str, Any]) -> None:
        """
        更新剥削记忆
        
        Args:
            deviation_info: 对手偏离信息
        """
        # 将当前偏离信息添加到记忆中
        self.exploitation_memory.append(deviation_info)
        
        # 保持记忆大小不超过最大限制
        if len(self.exploitation_memory) > self.exploitation_memory_size:
            self.exploitation_memory.pop(0)
    
    def _select_child(self, node: Node, belief_trackers: Optional[Dict[str, Any]] = None) -> Tuple[int, Node]:
        """
        使用增强的 UCB 公式选择子节点，考虑 GTO 偏离剥削

        Args:
            node: 当前节点
            belief_trackers: 信念追踪器字典，键为玩家ID

        Returns:
            选择的动作和对应的子节点
        """
        max_ucb = float('-inf')
        max_action = -1
        max_child = None
        
        # 如果启用了 GTO 偏离剥削并且有剥削记忆
        exploitation_active = (self.use_gto_exploitation and 
                              self.exploitation_memory and 
                              isinstance(node, ExploitNode))
        
        # 获取当前对手的偏离模式和置信度
        deviation_pattern = None
        deviation_confidence = 0.0
        if exploitation_active:
            # 使用最近的偏离信息
            latest_deviation = self.exploitation_memory[-1]
            deviation_score = latest_deviation.get("deviation_score", 0.0)
            deviation_pattern = latest_deviation.get("deviation_pattern", None)
            deviation_confidence = latest_deviation.get("confidence", 0.0)
            
            # 存储偏离信息到节点
            node.deviation_score = deviation_score
            node.deviation_pattern = deviation_pattern
            node.deviation_confidence = deviation_confidence
        
        # 获取当前对手动作偏好
        action_prefs = {}
        if exploitation_active and self.exploit_known_patterns:
            # 分析历史偏离记录，提取动作偏好
            action_prefs = self._extract_action_preferences()
            node.opponent_action_prefs = action_prefs
        
        # 基础 UCB 公式构成部分
        total_visits_sqrt = math.sqrt(node.visit_count) if node.visit_count > 0 else 1.0
        
        # UCB 得分 = Q(s,a) + P(s,a) * sqrt(sum_b(N(s,b))) / (1 + N(s,a))
        for action, child in node.children.items():
            # 探索项
            pb_c = math.log((node.visit_count + self.pb_c_base + 1) / self.pb_c_base) + self.pb_c_init
            pb_c *= total_visits_sqrt / (child.visit_count + 1)
            
            # 处理数组类型的 prior
            prior = child.prior
            if isinstance(prior, (np.ndarray, torch.Tensor)):
                if isinstance(prior, torch.Tensor):
                    prior = prior.mean().item()
                else:
                    prior = float(np.mean(prior))
            
            # 获取节点值
            value = child.value()
            
            # ------------ GTO 偏离剥削增强 (选择阶段) ------------
            if exploitation_active and deviation_confidence >= self.exploitation_threshold:
                try:
                    # 获取动作类型（如果可用）
                    action_type = self._get_action_type(action, node)
                    
                    # 1. 基于偏离模式的剥削调整
                    if deviation_pattern and self.exploit_known_patterns:
                        exploitation_bonus = self._calculate_pattern_exploitation_bonus(
                            deviation_pattern, action_type, action, deviation_confidence
                        )
                        
                        if isinstance(child, ExploitNode):
                            child.exploitation_bonus = exploitation_bonus
                        
                        # 应用剥削奖励
                        if exploitation_bonus > 0:
                            # 根据偏离置信度和剥削强度调整剥削奖励
                            adjusted_bonus = exploitation_bonus * self.exploitation_strength * deviation_confidence
                            value += adjusted_bonus
                    
                    # 2. 基于对手动作偏好的调整
                    if action_prefs and action_type in action_prefs:
                        pref_score = action_prefs[action_type]
                        # 根据偏好分数调整 UCB
                        if pref_score > 0:  # 对手偏好这种动作
                            # 提高对应反制动作的分数
                            counter_bonus = self._get_counter_action_bonus(action_type, pref_score)
                            value += counter_bonus * self.exploitation_strength
                            
                        elif pref_score < 0:  # 对手避免这种动作
                            # 提高这类动作的分数 (对手可能不擅长应对)
                            avoidance_bonus = abs(pref_score) * 0.5 * self.exploitation_strength
                            value += avoidance_bonus
                    
                    # 3. 动态探索-利用平衡调整
                    if self.dynamic_exploitation:
                        # 高置信度时减少探索，增加利用
                        if deviation_confidence > 0.7:
                            # 减少探索项权重
                            pb_c *= (1.0 - 0.3 * deviation_confidence)
                            # 增加利用项权重
                            value *= (1.0 + 0.2 * deviation_confidence)
                
                except Exception as e:
                    logger.debug(f"计算剥削奖励出错: {e}")
            
            # 基础 UCB 得分
            ucb_score = value + prior * pb_c
            
            # 更新最大值
            if ucb_score > max_ucb:
                max_ucb = ucb_score
                max_action = action
                max_child = child
        
        return max_action, max_child
    
    def _backpropagate(self, search_path: List[Node], value: Union[float, torch.Tensor, np.ndarray], discount: float, belief_trackers: Optional[Dict[str, Any]] = None) -> float:
        """
        带有 GTO 偏离剥削能力的反向传播
        
        Args:
            search_path: 搜索路径中的节点
            value: 初始值估计，可以是标量或分布
            discount: 折扣因子
            belief_trackers: 信念追踪器字典，键为玩家ID
            
        Returns:
            根节点的更新值
        """
        # 检查值是否为分布
        is_distribution = isinstance(value, (torch.Tensor, np.ndarray))
        
        # 如果是分布，确保它是 numpy 数组并计算标量值
        if is_distribution:
            if isinstance(value, torch.Tensor):
                value_distribution = value.detach().cpu().numpy()
            else:
                value_distribution = value
            scalar_value = float(np.mean(value_distribution))
        else:
            scalar_value = value
        
        # 反向传播
        for node in reversed(search_path):
            # 基础更新权重
            update_weight = 1.0
            
            # ------------ GTO 偏离剥削增强 (反向传播阶段) ------------
            if self.use_gto_exploitation and isinstance(node, ExploitNode):
                # 获取节点的偏离信息
                deviation_score = getattr(node, 'deviation_score', 0.0)
                deviation_confidence = getattr(node, 'deviation_confidence', 0.0)
                
                # 如果偏离分数超过阈值，调整更新权重
                if deviation_score > self.exploitation_threshold and deviation_confidence > 0.0:
                    # 获取剥削奖励（如果有）
                    exploitation_bonus = getattr(node, 'exploitation_bonus', 0.0)
                    
                    if exploitation_bonus > 0:
                        # 根据剥削奖励和置信度调整更新权重
                        exploitation_factor = 1.0 + (
                            exploitation_bonus * 
                            self.backprop_exploitation_weight * 
                            deviation_confidence
                        )
                        update_weight *= exploitation_factor
                        
                        # 存储剥削更新权重
                        node.exploitation_update_weight = exploitation_factor
            
            # 更新节点统计信息
            if hasattr(node, 'use_value_distribution') and node.use_value_distribution and is_distribution:
                # 如果节点使用值分布，并且值是分布，则更新分布
                if isinstance(node.value_sum, np.ndarray) and node.value_sum.shape == value_distribution.shape:
                    # 更新值分布 (应用更新权重)
                    node.value_sum += value_distribution * update_weight
                else:
                    # 如果形状不匹配，使用标量更新
                    node.value_sum += scalar_value * update_weight
            else:
                # 使用标量更新 (应用更新权重)
                node.value_sum += scalar_value * update_weight
            
            # 更新访问次数
            node.visit_count += 1
            
            # 根据折扣更新值
            scalar_value = node.reward + discount * scalar_value
        
        return scalar_value
    
    def _get_action_type(self, action: int, node: Node) -> str:
        """
        获取动作类型
        
        Args:
            action: 动作索引
            node: 当前节点
            
        Returns:
            动作类型字符串
        """
        # 尝试从节点中获取动作映射信息
        if hasattr(node, 'action_types') and action in node.action_types:
            return node.action_types[action]
        
        # 如果有剥削映射器，尝试使用它获取动作类型
        if self.exploit_mapper:
            try:
                # 使用剥削映射器的方法映射动作类型
                state = getattr(node, 'state', None)
                if state:
                    action_types = self.exploit_mapper._map_actions_to_types([action], state)
                    if action_types and len(action_types) > 0:
                        return action_types[0]
            except Exception:
                pass
        
        # 默认类型
        return "UNKNOWN"
    
    def _calculate_pattern_exploitation_bonus(self, pattern: str, action_type: str, action: int, confidence: float) -> float:
        """
        计算基于偏离模式的剥削奖励
        
        Args:
            pattern: 偏离模式
            action_type: 动作类型
            action: 动作索引
            confidence: 置信度
            
        Returns:
            剥削奖励
        """
        bonus = 0.0
        
        # 根据不同的偏离模式提供不同的剥削奖励
        if pattern == "risk_averse":
            # 对风险规避型对手，增强激进动作
            if action_type in ["BOMB", "ROCKET", "STRAIGHT", "AIRPLANE"]:
                bonus = 0.2 * self.pattern_bonus_factor
        
        elif pattern == "risk_seeking":
            # 对风险追求型对手，增强稳健动作
            if action_type in ["SINGLE", "PAIR", "TRIO"]:
                bonus = 0.15 * self.pattern_bonus_factor
        
        elif pattern == "pattern_player":
            # 对模式化玩家，增强非常规动作
            # 需要更多上下文信息来确定，使用一个较小的通用奖励
            bonus = 0.1 * self.pattern_bonus_factor
            
        elif pattern == "passive":
            # 对被动型玩家，增强控场动作
            if action_type in ["STRAIGHT", "STRAIGHT_PAIR", "AIRPLANE"]:
                bonus = 0.2 * self.pattern_bonus_factor
            
        elif pattern == "aggressive":
            # 对激进型玩家，增强防守和反击动作
            if action_type in ["BOMB", "ROCKET", "PASS"]:
                bonus = 0.25 * self.pattern_bonus_factor
            
        elif pattern == "card_hoarding":
            # 对囤牌型玩家，增强逼牌动作
            if action_type in ["SINGLE", "PAIR"]:
                bonus = 0.15 * self.pattern_bonus_factor
        
        elif pattern == "high_deviation":
            # 高偏离但模式不明确，给予小幅奖励
            bonus = 0.05 * self.pattern_bonus_factor
        
        # 根据置信度调整最终奖励值
        return bonus * confidence
    
    def _extract_action_preferences(self) -> Dict[str, float]:
        """
        从剥削记忆中提取对手动作偏好
        
        Returns:
            动作类型到偏好分数的映射
        """
        action_prefs = {}
        
        if not self.exploitation_memory:
            return action_prefs
        
        # 统计各种动作类型的使用频率
        action_counts = {}
        total_actions = 0
        
        for deviation_info in self.exploitation_memory:
            action_type = deviation_info.get("action_type", "UNKNOWN")
            if action_type != "UNKNOWN":
                action_counts[action_type] = action_counts.get(action_type, 0) + 1
                total_actions += 1
        
        if total_actions == 0:
            return action_prefs
        
        # 计算动作偏好分数 (相对于均匀分布的偏差)
        expected_freq = 1.0 / len(action_counts) if action_counts else 0
        
        for action_type, count in action_counts.items():
            actual_freq = count / total_actions
            # 偏好分数范围：[-1, 1]，正值表示偏好，负值表示避免
            pref_score = (actual_freq - expected_freq) / expected_freq
            # 归一化到 [-1, 1] 范围
            pref_score = max(-1.0, min(1.0, pref_score))
            action_prefs[action_type] = pref_score
        
        return action_prefs
    
    def _get_counter_action_bonus(self, action_type: str, pref_score: float) -> float:
        """
        获取针对特定动作类型的反制动作奖励
        
        Args:
            action_type: 目标动作类型
            pref_score: 偏好分数
            
        Returns:
            反制动作奖励
        """
        # 基础奖励值
        base_bonus = 0.1 * abs(pref_score)
        
        # 根据不同的动作类型提供不同的反制奖励
        if action_type == "PASS":
            # 对手经常 PASS，增强出小牌的奖励
            return base_bonus * 1.5
        
        elif action_type in ["BOMB", "ROCKET"]:
            # 对手经常出炸弹，增强防守或更大炸弹的奖励
            return base_bonus * 2.0
        
        elif "STRAIGHT" in action_type:
            # 对手经常出顺子，增强炸弹和大单牌的奖励
            return base_bonus * 1.2
        
        elif action_type in ["SINGLE", "PAIR"]:
            # 对手经常出单牌/对子，增强连牌的奖励
            return base_bonus * 1.1
        
        # 默认奖励
        return base_bonus 