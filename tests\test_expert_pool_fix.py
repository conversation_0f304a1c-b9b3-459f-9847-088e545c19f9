#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
专家池修复验证测试

验证RuleBasedAgent和MCTS策略加载的修复效果
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_expert_pool_fix():
    """测试专家池修复"""
    print("测试专家池修复...")
    
    try:
        from cardgame_ai.core.expert_pool import ExpertPolicyPool
        
        # 创建专家池实例
        expert_pool = ExpertPolicyPool()
        print("✅ ExpertPolicyPool实例化成功")
        
        # 检查加载的专家策略
        experts = expert_pool.list_experts()
        print(f"✅ 已加载专家策略: {experts}")
        
        # 检查是否有rule_based策略
        if 'rule_based' in experts:
            print("✅ rule_based策略加载成功")
        else:
            print("⚠️ rule_based策略未加载")
            
        # 检查是否有mcts_basic策略
        if 'mcts_basic' in experts:
            print("✅ mcts_basic策略加载成功")
        else:
            print("⚠️ mcts_basic策略未加载（预期行为）")
            
        return True
        
    except Exception as e:
        print(f"❌ 专家池测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("专家池修复验证测试")
    print("=" * 50)
    
    # 测试专家池修复
    test_result = test_expert_pool_fix()
    
    print("\n" + "=" * 50)
    print("测试结果:")
    print(f"  专家池修复: {'通过' if test_result else '失败'}")
    
    if test_result:
        print("\n🎉 专家池修复验证成功！")
        return True
    else:
        print("\n❌ 专家池修复失败")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
