"""
自对弈模块

提供基于智能体的自对弈功能，用于生成训练数据。
"""
from typing import List, Dict, Any, Optional, Tuple, Union
import numpy as np
import random
import time

from cardgame_ai.core.base import State, Action


class SelfPlay:
    """
    自对弈类
    
    使用智能体进行自对弈，生成训练数据。
    """
    
    def __init__(self, game, agent):
        """
        初始化自对弈环境
        
        Args:
            game: 游戏环境，必须实现reset和step等方法
            agent: 智能体，必须实现predict_action方法
        """
        self.game = game
        self.agent = agent
    
    def play_game(self, temperature: float = 1.0) -> List[Dict[str, Any]]:
        """
        进行一局自对弈游戏，并返回产生的经验数据
        
        Args:
            temperature (float, optional): 温度参数，控制探索程度. Defaults to 1.0.
            
        Returns:
            List[Dict[str, Any]]: 经验数据列表
        """
        # 重置游戏环境
        state = self.game.reset()
        done = False
        
        # 存储经验数据
        experiences = []
        
        # 记录每个状态的策略和估值
        state_history = []
        policy_history = []
        value_estimates = []
        
        # 游戏循环
        while not done:
            # 获取当前玩家
            current_player = state.current_player
            
            # 获取动作和策略分布
            action, policy, value = self.agent.predict_action(state, temperature)
            
            # 存储当前状态和策略
            state_history.append(state)
            policy_history.append(policy)
            value_estimates.append(value)
            
            # 执行动作
            next_state, reward, done, info = self.game.step(state, action)
            
            # 更新状态
            state = next_state
        
        # 获取最终状态的奖励（游戏结果）
        final_rewards = self.game.get_payoffs(state)
        
        # 生成经验数据
        for i, (state, policy) in enumerate(zip(state_history, policy_history)):
            player = state.current_player
            value_target = final_rewards[player]
            
            # 创建经验数据
            experience = {
                'state': state,
                'policy': policy,
                'value': value_target,
                'player': player
            }
            
            experiences.append(experience)
        
        return experiences
    
    def play_games(self, num_games: int, temperature: float = 1.0) -> List[Dict[str, Any]]:
        """
        连续进行多局自对弈游戏
        
        Args:
            num_games (int): 游戏局数
            temperature (float, optional): 温度参数. Defaults to 1.0.
            
        Returns:
            List[Dict[str, Any]]: 所有游戏的经验数据列表
        """
        all_experiences = []
        
        for _ in range(num_games):
            experiences = self.play_game(temperature)
            all_experiences.extend(experiences)
        
        return all_experiences
    
    def evaluate(self, num_games: int = 10, render: bool = False) -> float:
        """
        评估智能体的表现
        
        Args:
            num_games (int, optional): 评估游戏局数. Defaults to 10.
            render (bool, optional): 是否渲染游戏. Defaults to False.
            
        Returns:
            float: 智能体的平均收益
        """
        total_reward = 0.0
        
        for _ in range(num_games):
            # 重置游戏环境
            state = self.game.reset()
            done = False
            
            # 渲染初始状态
            if render:
                self.game.render(state)
            
            # 游戏循环
            while not done:
                # 获取动作
                action, _, _ = self.agent.predict_action(state, temperature=0.1, deterministic=True)
                
                # 执行动作
                next_state, reward, done, info = self.game.step(state, action)
                
                # 渲染状态
                if render:
                    self.game.render(next_state)
                
                # 更新状态
                state = next_state
            
            # 获取最终奖励
            payoffs = self.game.get_payoffs(state)
            total_reward += sum(payoffs) / len(payoffs)  # 简单平均所有玩家的收益
        
        return total_reward / num_games
    
    def __str__(self) -> str:
        """
        字符串表示
        
        Returns:
            str: 自对弈环境的字符串表示
        """
        return f"SelfPlay(game={self.game}, agent={self.agent})" 