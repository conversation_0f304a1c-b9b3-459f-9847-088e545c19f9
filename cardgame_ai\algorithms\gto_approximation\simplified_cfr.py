"""
简化CFR模块

提供用于计算近似GTO策略的简化CFR算法。
"""

import numpy as np
import logging
from typing import Dict, Any, Optional, List, Tuple, Union, Callable

from .gto_policy import GTOPolicy

# 配置日志
logger = logging.getLogger(__name__)

class SimplifiedCFR:
    """
    简化CFR类
    
    使用简化的反事实遗憾最小化(CFR)算法计算近似GTO策略。
    """
    
    def __init__(
        self,
        num_actions: int,
        utility_function: Callable[[str, int], float],
        is_terminal: Callable[[str], bool],
        get_legal_actions: Callable[[str], List[int]],
        get_next_state: Callable[[str, int], str],
        max_iterations: int = 1000
    ):
        """
        初始化简化CFR
        
        Args:
            num_actions: 动作空间大小
            utility_function: 效用函数，接收状态和动作，返回效用值
            is_terminal: 判断状态是否为终止状态的函数
            get_legal_actions: 获取合法动作的函数
            get_next_state: 获取下一个状态的函数
            max_iterations: 最大迭代次数
        """
        self.num_actions = num_actions
        self.utility_function = utility_function
        self.is_terminal = is_terminal
        self.get_legal_actions = get_legal_actions
        self.get_next_state = get_next_state
        self.max_iterations = max_iterations
        
        # 累积遗憾和策略
        self.cumulative_regrets = {}
        self.cumulative_strategy = {}
        
        # 当前策略
        self.current_strategy = {}
        
        logger.info(f"初始化简化CFR，动作空间大小: {num_actions}，最大迭代次数: {max_iterations}")
    
    def _get_strategy(self, state_key: str) -> np.ndarray:
        """
        根据累积遗憾计算当前策略
        
        Args:
            state_key: 状态的唯一标识符
            
        Returns:
            当前策略分布
        """
        # 如果状态不存在，初始化
        if state_key not in self.cumulative_regrets:
            self.cumulative_regrets[state_key] = np.zeros(self.num_actions)
            self.cumulative_strategy[state_key] = np.zeros(self.num_actions)
        
        # 获取合法动作
        legal_actions = self.get_legal_actions(state_key)
        
        # 计算正遗憾和
        regrets = np.maximum(0, self.cumulative_regrets[state_key])
        regret_sum = np.sum(regrets[legal_actions])
        
        # 计算策略
        strategy = np.zeros(self.num_actions)
        if regret_sum > 0:
            for action in legal_actions:
                strategy[action] = regrets[action] / regret_sum
        else:
            # 如果没有正遗憾，使用均匀分布
            for action in legal_actions:
                strategy[action] = 1.0 / len(legal_actions)
        
        return strategy
    
    def _cfr(self, state_key: str, reach_probability: float) -> float:
        """
        执行CFR递归
        
        Args:
            state_key: 状态的唯一标识符
            reach_probability: 到达概率
            
        Returns:
            状态的期望效用
        """
        # 如果是终止状态，返回效用
        if self.is_terminal(state_key):
            return 0.0
        
        # 获取合法动作
        legal_actions = self.get_legal_actions(state_key)
        
        # 获取当前策略
        strategy = self._get_strategy(state_key)
        
        # 更新累积策略
        self.cumulative_strategy[state_key] += reach_probability * strategy
        
        # 计算每个动作的反事实值
        action_values = np.zeros(self.num_actions)
        for action in legal_actions:
            # 获取下一个状态
            next_state = self.get_next_state(state_key, action)
            
            # 递归计算反事实值
            action_values[action] = self._cfr(next_state, reach_probability * strategy[action])
        
        # 计算期望值
        expected_value = np.sum(strategy * action_values)
        
        # 更新累积遗憾
        for action in legal_actions:
            self.cumulative_regrets[state_key][action] += action_values[action] - expected_value
        
        return expected_value
    
    def train(self, initial_state: str) -> GTOPolicy:
        """
        训练CFR算法
        
        Args:
            initial_state: 初始状态的唯一标识符
            
        Returns:
            训练得到的GTO策略
        """
        logger.info(f"开始训练CFR算法，初始状态: {initial_state}")
        
        # 执行CFR迭代
        for i in range(self.max_iterations):
            if (i + 1) % 100 == 0:
                logger.info(f"CFR迭代: {i + 1}/{self.max_iterations}")
            self._cfr(initial_state, 1.0)
        
        # 创建GTO策略
        gto_policy = GTOPolicy()
        
        # 计算平均策略
        for state_key, cumulative_strategy in self.cumulative_strategy.items():
            # 获取合法动作
            legal_actions = self.get_legal_actions(state_key)
            
            # 计算策略
            strategy_sum = np.sum(cumulative_strategy[legal_actions])
            if strategy_sum > 0:
                normalized_strategy = np.zeros(self.num_actions)
                for action in legal_actions:
                    normalized_strategy[action] = cumulative_strategy[action] / strategy_sum
                gto_policy.set_policy(state_key, normalized_strategy)
        
        logger.info(f"CFR训练完成，生成GTO策略，包含 {len(gto_policy.policy)} 个状态")
        
        return gto_policy
    
    def prune_game_tree(self, max_depth: int = 3) -> None:
        """
        剪枝游戏树，减少状态空间
        
        Args:
            max_depth: 最大深度
        """
        logger.info(f"剪枝游戏树，最大深度: {max_depth}")
        
        # 实现游戏树剪枝逻辑
        # 这里只是一个示例，实际实现可能需要更复杂的逻辑
        pass
