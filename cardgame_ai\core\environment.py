"""
游戏环境接口模块

定义游戏环境的接口和抽象类，是框架的核心组件之一。
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Tuple, Optional, Union
import numpy as np

from cardgame_ai.core.base import State, Action


class Environment(ABC):
    """
    游戏环境接口
    
    定义游戏环境的标准接口，包括重置环境、执行动作、获取合法动作等方法。
    所有具体游戏环境都应该实现这个接口。
    """
    
    @abstractmethod
    def reset(self) -> State:
        """
        重置游戏环境，返回初始状态
        
        Returns:
            State: 初始状态
        """
        pass
    
    @abstractmethod
    def step(self, action: Action) -> Tuple[State, float, bool, Dict[str, Any]]:
        """
        执行动作，返回新状态、奖励、是否结束和额外信息
        
        Args:
            action (Action): 要执行的动作
            
        Returns:
            Tuple[State, float, bool, Dict[str, Any]]: 
                新状态、奖励、是否结束、额外信息
        """
        pass
    
    @abstractmethod
    def get_legal_actions(self, state: State) -> List[Action]:
        """
        获取在给定状态下的合法动作列表
        
        Args:
            state (State): 游戏状态
            
        Returns:
            List[Action]: 合法动作列表
        """
        pass
    
    @abstractmethod
    def get_observation(self, state: State) -> np.ndarray:
        """
        获取状态的观察表示，通常是神经网络的输入
        
        Args:
            state (State): 游戏状态
            
        Returns:
            np.ndarray: 观察表示
        """
        pass
    
    @abstractmethod
    def is_terminal(self, state: State) -> bool:
        """
        判断状态是否为终止状态
        
        Args:
            state (State): 游戏状态
            
        Returns:
            bool: 是否为终止状态
        """
        pass
    
    @abstractmethod
    def get_payoffs(self, state: State) -> List[float]:
        """
        获取终止状态下各玩家的收益
        
        Args:
            state (State): 游戏状态
            
        Returns:
            List[float]: 各玩家的收益
        """
        pass
    
    @property
    @abstractmethod
    def num_players(self) -> int:
        """
        获取游戏的玩家数量
        
        Returns:
            int: 玩家数量
        """
        pass
    
    @property
    @abstractmethod
    def state_shape(self) -> Tuple[int, ...]:
        """
        获取状态的形状
        
        Returns:
            Tuple[int, ...]: 状态的形状
        """
        pass
    
    @property
    @abstractmethod
    def action_shape(self) -> Tuple[int, ...]:
        """
        获取动作的形状
        
        Returns:
            Tuple[int, ...]: 动作的形状
        """
        pass


class MultiAgentEnvironment(Environment):
    """
    多智能体环境抽象类
    
    为多智能体环境提供一些通用实现。
    """
    
    def __init__(self, num_players: int):
        """
        初始化多智能体环境
        
        Args:
            num_players (int): 玩家数量
        """
        self._num_players = num_players
    
    @property
    def num_players(self) -> int:
        """
        获取游戏的玩家数量
        
        Returns:
            int: 玩家数量
        """
        return self._num_players
    
    def run_game(self, agents: List[Any], is_training: bool = False) -> Tuple[List[float], List[Dict[str, Any]]]:
        """
        运行一局游戏
        
        Args:
            agents (List[Any]): 智能体列表
            is_training (bool, optional): 是否为训练模式. Defaults to False.
            
        Returns:
            Tuple[List[float], List[Dict[str, Any]]]: 
                各玩家的收益、游戏历史记录
        """
        state = self.reset()
        done = False
        history = []
        
        while not done:
            player_id = state.get_player_id()
            legal_actions = self.get_legal_actions(state)
            observation = self.get_observation(state)
            
            # 获取当前玩家的动作
            action = agents[player_id].act(observation, legal_actions, is_training)
            
            # 执行动作
            next_state, reward, done, info = self.step(action)
            
            # 记录历史
            history.append({
                'player_id': player_id,
                'state': state,
                'action': action,
                'reward': reward,
                'next_state': next_state,
                'done': done,
                'info': info
            })
            
            state = next_state
        
        # 获取各玩家的收益
        payoffs = self.get_payoffs(state)
        
        return payoffs, history
