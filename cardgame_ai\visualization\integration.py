"""
可视化集成模块

将训练、评估和可视化系统集成起来，提供统一的接口。
"""
import os
import logging
from typing import Dict, Any, List, Tuple, Optional, Union, Callable
import matplotlib.pyplot as plt
import numpy as np

from cardgame_ai.core.agent import Agent
from cardgame_ai.core.environment import Environment
from cardgame_ai.core.evaluator import Evaluator
from cardgame_ai.visualization.viz_config import VisualizationConfig
from cardgame_ai.visualization.training_viz import TrainingVisualizer
from cardgame_ai.visualization.game_viz import GameVisualizer


class VisualizationIntegrator:
    """
    可视化集成器
    
    集成训练、评估和可视化系统，提供统一的可视化接口。
    """
    
    def __init__(self, config: Optional[VisualizationConfig] = None, 
                save_dir: str = 'visualizations'):
        """
        初始化可视化集成器
        
        Args:
            config (Optional[VisualizationConfig], optional): 可视化配置. Defaults to None.
            save_dir (str, optional): 保存目录. Defaults to 'visualizations'.
        """
        # 创建或使用可视化配置
        self.config = config if config else VisualizationConfig()
        
        # 如果提供了保存目录，更新配置
        if save_dir != 'visualizations':
            self.config.update_config({'save_dir': save_dir})
        
        # 创建训练可视化器
        self.training_viz = TrainingVisualizer(save_dir=os.path.join(save_dir, 'training'))
        
        # 创建游戏可视化器
        self.game_viz = GameVisualizer(save_dir=os.path.join(save_dir, 'games'))
        
        # 设置日志
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def visualize_training_process(self, log_file: str, eval_file: Optional[str] = None,
                                 metrics: Optional[List[str]] = None,
                                 save_dashboard: bool = True) -> Dict[str, plt.Figure]:
        """
        可视化训练过程
        
        Args:
            log_file (str): 训练日志文件
            eval_file (Optional[str], optional): 评估结果文件. Defaults to None.
            metrics (Optional[List[str]], optional): 要可视化的指标. Defaults to None.
            save_dashboard (bool, optional): 是否保存训练面板. Defaults to True.
            
        Returns:
            Dict[str, plt.Figure]: 图表字典
        """
        self.logger.info(f"开始可视化训练过程，使用日志文件：{log_file}")
        
        figures = {}
        
        # 可视化训练曲线
        if metrics:
            window_size = 5  # 移动平均窗口大小
            fig = self.training_viz.plot_training_curve(
                log_file=log_file,
                metrics=metrics,
                window_size=window_size,
                figsize=self.config.get_figure_size('large'),
                save_as='training_curves.png'
            )
            figures['training_curves'] = fig
        
        # 如果提供了评估结果，可视化奖励分布
        if eval_file:
            fig = self.training_viz.plot_reward_distribution(
                eval_results_file=eval_file,
                figsize=self.config.get_figure_size('medium'),
                save_as='reward_distribution.png'
            )
            figures['reward_distribution'] = fig
        
        # 创建训练面板
        if save_dashboard and eval_file:
            fig = self.training_viz.plot_training_dashboard(
                log_file=log_file,
                eval_file=eval_file,
                figsize=self.config.get_figure_size('dashboard'),
                save_as='training_dashboard.png'
            )
            figures['training_dashboard'] = fig
        
        self.logger.info(f"训练过程可视化完成，生成了 {len(figures)} 张图表")
        return figures
    
    def visualize_hyperparameter_tuning(self, results_file: str, top_n: int = 10) -> plt.Figure:
        """
        可视化超参数调优结果
        
        Args:
            results_file (str): 超参数调优结果文件
            top_n (int, optional): 显示前N个重要超参数. Defaults to 10.
            
        Returns:
            plt.Figure: 图形对象
        """
        self.logger.info(f"开始可视化超参数调优结果，使用文件：{results_file}")
        
        fig = self.training_viz.plot_hyperparameter_importance(
            results_file=results_file,
            top_n=top_n,
            figsize=self.config.get_figure_size('large'),
            save_as='hyperparameter_importance.png'
        )
        
        self.logger.info("超参数调优结果可视化完成")
        return fig
    
    def visualize_agent_comparison(self, eval_results_files: List[str], 
                                 agent_names: List[str],
                                 metrics: Optional[List[str]] = None) -> plt.Figure:
        """
        可视化智能体比较
        
        Args:
            eval_results_files (List[str]): 评估结果文件列表
            agent_names (List[str]): 智能体名称列表
            metrics (Optional[List[str]], optional): 比较指标. Defaults to None.
            
        Returns:
            plt.Figure: 图形对象
        """
        self.logger.info(f"开始比较 {len(agent_names)} 个智能体的性能")
        
        fig = self.training_viz.compare_agents(
            eval_results_files=eval_results_files,
            agent_names=agent_names,
            metrics=metrics,
            figsize=self.config.get_figure_size('large'),
            save_as='agent_comparison.png'
        )
        
        self.logger.info("智能体比较可视化完成")
        return fig
    
    def visualize_win_rates(self, eval_results_files: List[str], 
                          labels: Optional[List[str]] = None) -> plt.Figure:
        """
        可视化胜率
        
        Args:
            eval_results_files (List[str]): 评估结果文件列表
            labels (Optional[List[str]], optional): 标签列表. Defaults to None.
            
        Returns:
            plt.Figure: 图形对象
        """
        self.logger.info(f"开始可视化胜率，使用 {len(eval_results_files)} 个评估结果文件")
        
        fig = self.training_viz.plot_win_rates(
            eval_results_files=eval_results_files,
            labels=labels,
            figsize=self.config.get_figure_size('medium'),
            save_as='win_rates.png'
        )
        
        self.logger.info("胜率可视化完成")
        return fig
    
    def visualize_game_record(self, game_record: Dict[str, Any], 
                            save_as: str = 'game_record.png') -> plt.Figure:
        """
        可视化游戏记录
        
        Args:
            game_record (Dict[str, Any]): 游戏记录
            save_as (str, optional): 保存文件名. Defaults to 'game_record.png'.
            
        Returns:
            plt.Figure: 图形对象
        """
        self.logger.info("开始可视化游戏记录")
        
        fig = self.game_viz.visualize_game_record(
            game_record=game_record,
            figsize=self.config.get_figure_size('large'),
            save_as=save_as
        )
        
        self.logger.info(f"游戏记录可视化完成，保存为 {save_as}")
        return fig
    
    def visualize_decision_process(self, decision_data: Dict[str, Any], 
                                 save_as: str = 'decision.png') -> plt.Figure:
        """
        可视化决策过程
        
        Args:
            decision_data (Dict[str, Any]): 决策数据
            save_as (str, optional): 保存文件名. Defaults to 'decision.png'.
            
        Returns:
            plt.Figure: 图形对象
        """
        self.logger.info("开始可视化决策过程")
        
        fig = self.game_viz.visualize_decision_process(
            decision_data=decision_data,
            figsize=self.config.get_figure_size('medium'),
            save_as=save_as
        )
        
        self.logger.info(f"决策过程可视化完成，保存为 {save_as}")
        return fig
    
    def visualize_game_states(self, game_states: List[Dict[str, Any]], 
                            render_func: Callable,
                            num_states: int = 4,
                            save_as: str = 'game_states.png') -> plt.Figure:
        """
        可视化游戏状态序列
        
        Args:
            game_states (List[Dict[str, Any]]): 游戏状态列表
            render_func (Callable): 渲染函数
            num_states (int, optional): 要显示的状态数量. Defaults to 4.
            save_as (str, optional): 保存文件名. Defaults to 'game_states.png'.
            
        Returns:
            plt.Figure: 图形对象
        """
        self.logger.info(f"开始可视化 {len(game_states)} 个游戏状态，显示 {num_states} 个")
        
        fig = self.game_viz.visualize_game_states(
            game_states=game_states,
            render_func=render_func,
            num_states=num_states,
            figsize=self.config.get_figure_size('large'),
            save_as=save_as
        )
        
        self.logger.info(f"游戏状态可视化完成，保存为 {save_as}")
        return fig
    
    def visualize_value_heatmap(self, state_values: Dict[str, float], 
                              state_names: Optional[List[str]] = None,
                              save_as: str = 'value_heatmap.png') -> plt.Figure:
        """
        可视化状态价值热力图
        
        Args:
            state_values (Dict[str, float]): 状态价值字典
            state_names (Optional[List[str]], optional): 状态名称列表. Defaults to None.
            save_as (str, optional): 保存文件名. Defaults to 'value_heatmap.png'.
            
        Returns:
            plt.Figure: 图形对象
        """
        self.logger.info(f"开始可视化 {len(state_values)} 个状态的价值热力图")
        
        fig = self.game_viz.visualize_value_heatmap(
            state_values=state_values,
            state_names=state_names,
            figsize=self.config.get_figure_size('large'),
            save_as=save_as
        )
        
        self.logger.info(f"状态价值热力图可视化完成，保存为 {save_as}")
        return fig
    
    def create_animated_game(self, game_states: List[Dict[str, Any]], 
                           render_func: Callable,
                           save_as: str = 'game_animation.gif',
                           interval: int = 500) -> Tuple[plt.Figure, Any]:
        """
        创建游戏动画
        
        Args:
            game_states (List[Dict[str, Any]]): 游戏状态列表
            render_func (Callable): 渲染函数
            save_as (str, optional): 保存文件名. Defaults to 'game_animation.gif'.
            interval (int, optional): 帧间隔（毫秒）. Defaults to 500.
            
        Returns:
            Tuple[plt.Figure, Any]: 图形对象和动画对象
        """
        self.logger.info(f"开始创建 {len(game_states)} 帧的游戏动画")
        
        result = self.game_viz.animate_game(
            game_states=game_states,
            render_func=render_func,
            interval=interval,
            figsize=self.config.get_figure_size('square'),
            save_as=save_as
        )
        
        self.logger.info(f"游戏动画创建完成，保存为 {save_as}")
        return result
    
    def visualize_evaluation_results(self, evaluator: Evaluator, env: Environment, 
                                   agents: List[Agent], agent_names: List[str],
                                   num_games: int = 100,
                                   save_dir: str = 'evaluation') -> Dict[str, Any]:
        """
        执行评估并可视化结果
        
        Args:
            evaluator (Evaluator): 评估器
            env (Environment): 环境
            agents (List[Agent]): 智能体列表
            agent_names (List[str]): 智能体名称列表
            num_games (int, optional): 游戏数量. Defaults to 100.
            save_dir (str, optional): 保存目录. Defaults to 'evaluation'.
            
        Returns:
            Dict[str, Any]: 评估结果和图形字典
        """
        self.logger.info(f"开始对 {len(agents)} 个智能体进行评估和可视化")
        
        # 创建保存目录
        full_save_dir = os.path.join(self.config.config['save_dir'], save_dir)
        os.makedirs(full_save_dir, exist_ok=True)
        
        # 执行评估
        results = evaluator.evaluate(
            env=env,
            agents=agents,
            num_games=num_games,
            agent_names=agent_names,
            save_games=True
        )
        
        # 保存评估结果
        results_file = os.path.join(full_save_dir, 'evaluation_results.json')
        evaluator._save_result(results)
        
        # 可视化结果
        viz_results = {}
        
        # 胜率柱状图
        if 'win_rates' in results:
            fig, ax = self.config.create_figure('medium')
            players = list(results['win_rates'].keys())
            win_rates = [results['win_rates'][p] for p in players]
            
            bars = ax.bar(players, win_rates, alpha=0.7)
            
            # 添加数据标签
            for i, bar in enumerate(bars):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                       f'{win_rates[i]:.1%}',
                       ha='center', va='bottom', fontsize=10)
            
            ax.set_ylim(0, 1.0)
            ax.set_ylabel('胜率')
            ax.set_title('智能体胜率比较')
            ax.grid(True, alpha=0.3)
            
            self.config.save_figure(fig, 'win_rates.png', subdir=save_dir)
            viz_results['win_rates'] = fig
        
        # 平均奖励柱状图
        if 'mean_rewards' in results:
            fig, ax = self.config.create_figure('medium')
            players = list(results['mean_rewards'].keys())
            rewards = [results['mean_rewards'][p] for p in players]
            
            bars = ax.bar(players, rewards, alpha=0.7)
            
            # 添加数据标签
            for i, bar in enumerate(bars):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                       f'{rewards[i]:.2f}',
                       ha='center', va='bottom', fontsize=10)
            
            ax.set_ylabel('平均奖励')
            ax.set_title('智能体平均奖励比较')
            ax.grid(True, alpha=0.3)
            
            self.config.save_figure(fig, 'mean_rewards.png', subdir=save_dir)
            viz_results['mean_rewards'] = fig
        
        # 决策时间柱状图
        if 'mean_decision_times' in results:
            fig, ax = self.config.create_figure('medium')
            players = list(results['mean_decision_times'].keys())
            times = [results['mean_decision_times'][p] for p in players]
            
            bars = ax.bar(players, times, alpha=0.7)
            
            # 添加数据标签
            for i, bar in enumerate(bars):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                       f'{times[i]:.4f}s',
                       ha='center', va='bottom', fontsize=10)
            
            ax.set_ylabel('平均决策时间 (秒)')
            ax.set_title('智能体决策时间比较')
            ax.grid(True, alpha=0.3)
            
            self.config.save_figure(fig, 'decision_times.png', subdir=save_dir)
            viz_results['decision_times'] = fig
        
        # 评估摘要面板
        fig, axes = plt.subplots(2, 2, figsize=self.config.get_figure_size('dashboard'))
        
        # 胜率饼图
        ax = axes[0, 0]
        if 'win_counts' in results:
            labels = list(results['win_counts'].keys())
            sizes = [results['win_counts'][p] for p in labels]
            
            ax.pie(sizes, labels=labels, autopct='%1.1f%%', startangle=90, explode=[0.05]*len(labels))
            ax.set_title('胜利分布')
        else:
            ax.set_visible(False)
        
        # 游戏长度直方图
        ax = axes[0, 1]
        if 'game_lengths' in results:
            game_lengths = results['game_lengths']
            ax.hist(game_lengths, bins=20, alpha=0.7)
            ax.axvline(np.mean(game_lengths), color='r', linestyle='--', 
                      label=f'平均: {np.mean(game_lengths):.1f}步')
            ax.set_xlabel('游戏长度（步数）')
            ax.set_ylabel('频率')
            ax.set_title('游戏长度分布')
            ax.legend()
        else:
            ax.set_visible(False)
        
        # 评估摘要表格
        ax = axes[1, 0]
        ax.axis('off')
        
        summary_text = "评估摘要\n\n"
        summary_text += f"总游戏数: {results.get('num_games', 'N/A')}\n"
        summary_text += f"平均游戏长度: {np.mean(results.get('game_lengths', [0])):.1f}步\n\n"
        
        # 添加胜率
        if 'win_rates' in results:
            summary_text += "胜率:\n"
            for player, rate in results['win_rates'].items():
                summary_text += f"  {player}: {rate:.1%}\n"
        
        # 添加平均奖励
        if 'mean_rewards' in results:
            summary_text += "\n平均奖励:\n"
            for player, reward in results['mean_rewards'].items():
                summary_text += f"  {player}: {reward:.2f}\n"
        
        ax.text(0.05, 0.95, summary_text, transform=ax.transAxes, fontsize=12,
               verticalalignment='top', bbox=dict(boxstyle='round', alpha=0.1))
        
        # 决策时间箱型图
        ax = axes[1, 1]
        if 'decision_times' in results:
            decision_times = results['decision_times']
            
            # 整理数据
            data = []
            labels = []
            for player, times in decision_times.items():
                if times:  # 只有非空列表才添加
                    data.append(times)
                    labels.append(player)
            
            if data:
                ax.boxplot(data, labels=labels, showfliers=False)
                ax.set_ylabel('决策时间 (秒)')
                ax.set_title('决策时间分布')
                ax.grid(True, alpha=0.3)
            else:
                ax.set_visible(False)
        else:
            ax.set_visible(False)
        
        plt.tight_layout()
        self.config.save_figure(fig, 'evaluation_summary.png', subdir=save_dir)
        viz_results['summary'] = fig
        
        self.logger.info(f"评估和可视化完成，生成了 {len(viz_results)} 张图表")
        
        return {
            'evaluation_results': results,
            'visualizations': viz_results
        } 