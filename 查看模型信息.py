#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速查看训练模型信息

直接运行查看所有模型的参数量和基本信息：
    python 查看模型信息.py
"""

import os
import sys
import torch
from pathlib import Path
from datetime import datetime

def format_params(param_count: int) -> str:
    """格式化参数量"""
    if param_count >= 1_000_000_000:
        return f"{param_count / 1_000_000_000:.1f}B"
    elif param_count >= 1_000_000:
        return f"{param_count / 1_000_000:.1f}M"
    elif param_count >= 1_000:
        return f"{param_count / 1_000:.1f}K"
    else:
        return str(param_count)

def format_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes >= 1024**3:
        return f"{size_bytes / (1024**3):.1f} GB"
    elif size_bytes >= 1024**2:
        return f"{size_bytes / (1024**2):.1f} MB"
    elif size_bytes >= 1024:
        return f"{size_bytes / 1024:.1f} KB"
    else:
        return f"{size_bytes} B"

def get_model_params(model_path: str) -> int:
    """获取模型参数量"""
    try:
        data = torch.load(model_path, map_location='cpu')
        
        # 尝试不同的参数获取方式
        if isinstance(data, dict):
            # 方式1：从parameter_stats获取
            if 'parameter_stats' in data:
                return data['parameter_stats'].get('total_parameters', 0)
            
            # 方式2：从model_state_dict计算
            elif 'model_state_dict' in data:
                return sum(tensor.numel() for tensor in data['model_state_dict'].values())
            
            # 方式3：直接从state_dict计算
            elif any(key.endswith('.weight') or key.endswith('.bias') for key in data.keys()):
                return sum(tensor.numel() for tensor in data.values() if hasattr(tensor, 'numel'))
        
        return 0
    except:
        return 0

def find_models():
    """查找所有模型文件"""
    # 搜索目录
    search_dirs = [
        "models",
        "cardgame_ai/zhuchengxu/models", 
        "checkpoints",
        "saved_models",
        "results"
    ]
    
    models = []
    for dir_name in search_dirs:
        dir_path = Path(dir_name)
        if dir_path.exists():
            # 查找模型文件
            for pattern in ['**/*.pt', '**/*.pth', '**/*.ckpt']:
                models.extend(dir_path.glob(pattern))
    
    # 按修改时间排序（最新的在前）
    models = list(set(models))
    models.sort(key=lambda x: x.stat().st_mtime, reverse=True)
    
    return models

def main():
    """主函数"""
    print("🔍 搜索训练模型文件...")
    print("=" * 50)
    
    models = find_models()
    
    if not models:
        print("❌ 未找到任何模型文件")
        print("💡 请检查以下目录是否存在模型文件：")
        print("   - models/")
        print("   - cardgame_ai/zhuchengxu/models/")
        print("   - checkpoints/")
        return
    
    print(f"🎯 找到 {len(models)} 个模型文件\n")
    
    total_params = 0
    
    for i, model_path in enumerate(models, 1):
        # 基本信息
        file_size = model_path.stat().st_size
        mod_time = datetime.fromtimestamp(model_path.stat().st_mtime)
        
        # 参数量
        param_count = get_model_params(str(model_path))
        
        # 显示信息
        print(f"[{i}] 📄 {model_path.name}")
        print(f"    📊 参数量: {format_params(param_count)} ({param_count:,})")
        print(f"    💾 大小: {format_size(file_size)}")
        print(f"    📁 路径: {model_path.parent}")
        print(f"    ⏰ 时间: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        total_params += param_count
    
    # 统计
    print("📊 统计摘要:")
    print(f"   模型总数: {len(models)}")
    print(f"   总参数量: {format_params(total_params)} ({total_params:,})")
    print("\n✅ 完成！")

if __name__ == "__main__":
    main()
