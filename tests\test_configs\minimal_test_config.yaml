# 最小化测试配置文件
# 用于验证重构后的训练系统基础功能

game: doudizhu
algorithm: efficient_zero
device: cpu

# 训练配置 - 最小化设置用于快速测试
training:
  epochs: 2                    # 只训练2个轮次
  episodes_per_epoch: 2        # 每轮只收集2局游戏
  updates_per_epoch: 2         # 每轮只更新2次
  batch_size: 32               # 小批次大小
  learning_rate: 0.001
  save_interval: 1             # 每轮都保存
  eval_interval: 1             # 每轮都评估
  log_interval: 1              # 每轮都记录日志
  save_dir: "tests/temp_models"

# 模型配置 - 简化模型用于测试
model:
  hidden_size: 64              # 小的隐藏层大小
  num_layers: 2                # 少的层数
  action_space_size: 100       # 动作空间大小
  observation_space_size: 200  # 观察空间大小

# MCTS配置 - 减少搜索次数加快测试
mcts:
  num_simulations: 10          # 少的模拟次数
  discount: 0.99
  dirichlet_alpha: 0.25
  exploration_fraction: 0.25
  pb_c_base: 19652
  pb_c_init: 1.25

# 环境配置
environment:
  max_episode_steps: 100       # 限制最大步数
  
# 监控配置 - 简化监控
monitoring:
  enabled: true
  tensorboard:
    enabled: false             # 关闭TensorBoard以简化测试
  wandb:
    enabled: false             # 关闭WandB以简化测试

# 多智能体配置
multi_agent:
  farmer_cooperation:
    enabled: true
    cooperation_weight: 0.8
    team_reward_weight: 0.9

# 分布式训练配置 - 关闭以简化测试
distributed:
  enabled: false
  num_workers: 1

# 其他配置
seed: 42                       # 固定随机种子
resume: false                  # 不恢复训练
