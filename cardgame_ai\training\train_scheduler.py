#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
专家调度器训练脚本

训练专家调度器，使其能够根据当前局面特征选择最合适的专家策略。
"""

import os
import sys
import argparse
import logging
import numpy as np
import torch
import random
import time
from typing import Dict, List, Any, Optional, Tuple

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from cardgame_ai.core.expert_pool import ExpertPolicyPool
from cardgame_ai.core.expert_scheduler import NeuralScheduler, RuleBasedScheduler, UCBScheduler
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.games.doudizhu.state import DouDizhuState
from cardgame_ai.utils.model_saver import ModelSaver

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='专家调度器训练')
    
    parser.add_argument('--scheduler_type', type=str, default='neural',
                        choices=['neural', 'rule', 'ucb'],
                        help='调度器类型')
    parser.add_argument('--expert_config', type=str, default='configs/expert_pool.json',
                        help='专家池配置文件路径')
    parser.add_argument('--state_dim', type=int, default=64,
                        help='状态特征维度')
    parser.add_argument('--hidden_dims', type=str, default='128,64',
                        help='隐藏层维度，用逗号分隔')
    parser.add_argument('--learning_rate', type=float, default=0.001,
                        help='学习率')
    parser.add_argument('--batch_size', type=int, default=32,
                        help='批次大小')
    parser.add_argument('--num_episodes', type=int, default=1000,
                        help='训练轮数')
    parser.add_argument('--train_interval', type=int, default=100,
                        help='训练间隔')
    parser.add_argument('--save_interval', type=int, default=200,
                        help='保存间隔')
    parser.add_argument('--save_path', type=str, default='models/scheduler',
                        help='保存路径')
    parser.add_argument('--load_path', type=str, default=None,
                        help='加载路径')
    parser.add_argument('--seed', type=int, default=42,
                        help='随机种子')
    
    return parser.parse_args()


def create_scheduler(args, expert_pool):
    """创建调度器"""
    if args.scheduler_type == 'neural':
        # 解析隐藏层维度
        hidden_dims = [int(dim) for dim in args.hidden_dims.split(',')]
        
        # 创建神经网络调度器
        scheduler = NeuralScheduler(
            expert_pool=expert_pool,
            state_feature_dim=args.state_dim,
            hidden_dims=hidden_dims,
            learning_rate=args.learning_rate
        )
    elif args.scheduler_type == 'rule':
        # 创建基于规则的调度器
        scheduler = RuleBasedScheduler(
            expert_pool=expert_pool
        )
    elif args.scheduler_type == 'ucb':
        # 创建基于UCB的调度器
        scheduler = UCBScheduler(
            expert_pool=expert_pool,
            c=2.0,
            initial_value=0.0
        )
    else:
        raise ValueError(f"不支持的调度器类型: {args.scheduler_type}")
    
    # 加载模型
    if args.load_path and os.path.exists(args.load_path):
        scheduler.load(args.load_path)
        logger.info(f"已加载调度器: {args.load_path}")
    
    return scheduler


def evaluate_scheduler(scheduler, env, num_episodes=100):
    """评估调度器"""
    total_reward = 0.0
    expert_usage = {name: 0 for name in scheduler.expert_names}
    
    for _ in range(num_episodes):
        # 重置环境
        state = env.reset()
        done = False
        episode_reward = 0.0
        
        while not done:
            # 获取合法动作
            legal_actions = env.get_legal_actions(state)
            
            # 选择专家
            expert_name = scheduler.select_expert(state, legal_actions)
            expert = scheduler.expert_pool.get_expert(expert_name)
            
            # 更新专家使用统计
            expert_usage[expert_name] += 1
            
            # 使用专家选择动作
            action = expert.act(state, legal_actions)
            
            # 执行动作
            next_state, reward, done, _ = env.step(action)
            
            # 更新状态和奖励
            state = next_state
            episode_reward += reward
        
        # 更新调度器奖励
        scheduler.update_reward(expert_name, episode_reward)
        
        # 累加总奖励
        total_reward += episode_reward
    
    # 计算平均奖励
    avg_reward = total_reward / num_episodes
    
    # 计算专家使用率
    total_usage = sum(expert_usage.values())
    expert_usage_ratio = {name: count / total_usage for name, count in expert_usage.items()}
    
    return avg_reward, expert_usage_ratio


def train_scheduler(args):
    """训练调度器"""
    # 设置随机种子
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    random.seed(args.seed)
    
    # 创建专家池
    expert_pool = ExpertPolicyPool(config_path=args.expert_config)
    
    # 创建调度器
    scheduler = create_scheduler(args, expert_pool)
    
    # 创建环境
    env = DouDizhuEnvironment()
    
    # 训练循环
    best_reward = float('-inf')
    
    for episode in range(args.num_episodes):
        # 重置环境
        state = env.reset()
        done = False
        episode_reward = 0.0
        
        # 记录专家使用情况
        expert_usage = {name: 0 for name in scheduler.expert_names}
        
        while not done:
            # 获取合法动作
            legal_actions = env.get_legal_actions(state)
            
            # 选择专家
            expert_name = scheduler.select_expert(state, legal_actions)
            expert = expert_pool.get_expert(expert_name)
            
            # 更新专家使用统计
            expert_usage[expert_name] += 1
            
            # 使用专家选择动作
            action = expert.act(state, legal_actions)
            
            # 执行动作
            next_state, reward, done, _ = env.step(action)
            
            # 更新状态和奖励
            state = next_state
            episode_reward += reward
        
        # 更新调度器奖励
        scheduler.update_reward(expert_name, episode_reward)
        
        # 训练调度器
        if args.scheduler_type == 'neural' and (episode + 1) % args.train_interval == 0:
            train_stats = scheduler.train(batch_size=args.batch_size, epochs=5)
            logger.info(f"训练统计: {train_stats}")
        
        # 评估调度器
        if (episode + 1) % args.save_interval == 0:
            avg_reward, expert_usage_ratio = evaluate_scheduler(scheduler, env, num_episodes=20)
            
            logger.info(f"评估结果 - 平均奖励: {avg_reward:.4f}")
            logger.info(f"专家使用率: {expert_usage_ratio}")
            
            # 保存最佳模型
            if avg_reward > best_reward:
                best_reward = avg_reward
                
                # 创建保存目录
                os.makedirs(args.save_path, exist_ok=True)
                
                # 保存模型
                save_path = os.path.join(args.save_path, f"{args.scheduler_type}_scheduler_best.json")
                scheduler.save(save_path)
                
                logger.info(f"保存最佳模型: {save_path}")
        
        # 打印训练信息
        if (episode + 1) % 10 == 0:
            # 计算专家使用率
            total_usage = sum(expert_usage.values())
            expert_usage_ratio = {name: count / total_usage for name, count in expert_usage.items()}
            
            logger.info(f"Episode {episode+1}/{args.num_episodes} - 奖励: {episode_reward:.4f}")
            logger.info(f"专家使用率: {expert_usage_ratio}")
    
    # 保存最终模型
    os.makedirs(args.save_path, exist_ok=True)
    save_path = os.path.join(args.save_path, f"{args.scheduler_type}_scheduler_final.json")
    scheduler.save(save_path)
    
    logger.info(f"保存最终模型: {save_path}")
    
    return scheduler


def main():
    """主函数"""
    args = parse_args()
    
    # 训练调度器
    scheduler = train_scheduler(args)
    
    # 打印统计信息
    stats = scheduler.get_stats()
    logger.info(f"总决策次数: {stats['total_decisions']}")
    logger.info(f"专家使用率: {stats['expert_usage_ratio']}")
    
    return 0


if __name__ == "__main__":
    main()
