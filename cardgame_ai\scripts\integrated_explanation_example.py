"""
集成决策解释模式示例脚本

演示如何将决策解释模式集成到现有系统中。
"""

import os
import sys
import argparse
import logging
import json
import numpy as np
import torch
from typing import Dict, List, Any, Optional

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cardgame_ai.algorithms.explanation_manager import ExplanationManager
from cardgame_ai.algorithms.integrated_ai_system import IntegratedAISystem
from cardgame_ai.games.doudizhu.game import DouDizhuGame
from cardgame_ai.games.doudizhu.state import DouDizhuState
from cardgame_ai.games.doudizhu.card import Card
from cardgame_ai.algorithms.belief_tracking.belief_tracker import BeliefTracker

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def parse_args():
    """
    解析命令行参数
    
    Returns:
        argparse.Namespace: 解析后的参数
    """
    parser = argparse.ArgumentParser(description='集成决策解释模式示例')
    parser.add_argument('--model_path', type=str, default='models/integrated_ai_system.pth', help='模型路径')
    parser.add_argument('--detail_level', type=str, default='medium', choices=['low', 'medium', 'high'], help='解释详细程度')
    parser.add_argument('--visualization', action='store_true', help='是否启用可视化')
    parser.add_argument('--save_explanations', action='store_true', help='是否保存解释结果')
    parser.add_argument('--save_dir', type=str, default='explanations', help='解释结果保存目录')
    parser.add_argument('--num_simulations', type=int, default=100, help='MCTS模拟次数')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    
    return parser.parse_args()


def create_test_state() -> DouDizhuState:
    """
    创建测试状态
    
    Returns:
        DouDizhuState: 测试状态
    """
    # 创建游戏
    game = DouDizhuGame()
    
    # 初始化状态
    state = game.get_init_state()
    
    # 模拟发牌
    landlord_cards = [
        Card.from_string('3'), Card.from_string('3'), Card.from_string('4'), Card.from_string('4'),
        Card.from_string('5'), Card.from_string('6'), Card.from_string('7'), Card.from_string('8'),
        Card.from_string('9'), Card.from_string('10'), Card.from_string('J'), Card.from_string('Q'),
        Card.from_string('K'), Card.from_string('A'), Card.from_string('2'), Card.from_string('2'),
        Card.from_string('JOKER')
    ]
    
    farmer1_cards = [
        Card.from_string('3'), Card.from_string('4'), Card.from_string('5'), Card.from_string('5'),
        Card.from_string('6'), Card.from_string('6'), Card.from_string('7'), Card.from_string('8'),
        Card.from_string('9'), Card.from_string('10'), Card.from_string('J'), Card.from_string('Q'),
        Card.from_string('K'), Card.from_string('A'), Card.from_string('A'), Card.from_string('JOKER')
    ]
    
    farmer2_cards = [
        Card.from_string('3'), Card.from_string('4'), Card.from_string('5'), Card.from_string('6'),
        Card.from_string('7'), Card.from_string('7'), Card.from_string('8'), Card.from_string('8'),
        Card.from_string('9'), Card.from_string('9'), Card.from_string('10'), Card.from_string('10'),
        Card.from_string('J'), Card.from_string('Q'), Card.from_string('K'), Card.from_string('K')
    ]
    
    # 设置手牌
    state.player_cards = {
        0: landlord_cards,
        1: farmer1_cards,
        2: farmer2_cards
    }
    
    # 设置地主
    state.landlord = 0
    
    # 设置当前玩家
    state.current_player = 0
    
    # 设置历史动作
    state.history = []
    
    return state


def load_integrated_ai_system(model_path: str, explanation_manager: ExplanationManager) -> IntegratedAISystem:
    """
    加载集成AI系统
    
    Args:
        model_path: 模型路径
        explanation_manager: 解释管理器
        
    Returns:
        IntegratedAISystem: 加载的集成AI系统
    """
    # 检查模型文件是否存在
    if not os.path.exists(model_path):
        logger.warning(f"模型文件不存在: {model_path}，使用随机初始化的模型")
        # 创建随机初始化的模型
        system = IntegratedAISystem(
            observation_shape=(54 * 4 + 54 * 3,),
            action_shape=(54 * 3,),
            hidden_dim=128,
            state_dim=64,
            num_simulations=100
        )
    else:
        # 加载模型
        try:
            checkpoint = torch.load(model_path, map_location='cpu')
            system = IntegratedAISystem(
                observation_shape=(54 * 4 + 54 * 3,),
                action_shape=(54 * 3,),
                hidden_dim=128,
                state_dim=64,
                num_simulations=100
            )
            system.load_state_dict(checkpoint['model_state_dict'])
            logger.info(f"成功加载模型: {model_path}")
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            # 创建随机初始化的模型
            system = IntegratedAISystem(
                observation_shape=(54 * 4 + 54 * 3,),
                action_shape=(54 * 3,),
                hidden_dim=128,
                state_dim=64,
                num_simulations=100
            )
    
    # 设置解释管理器
    system.explanation_manager = explanation_manager
    
    return system


def main():
    """
    主函数
    """
    # 解析命令行参数
    args = parse_args()
    
    # 设置随机种子
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    
    # 创建解释管理器
    explanation_manager = ExplanationManager(
        enabled=True,
        detail_level=args.detail_level,
        visualization_enabled=args.visualization,
        save_explanations=args.save_explanations,
        save_dir=args.save_dir
    )
    
    # 加载集成AI系统
    system = load_integrated_ai_system(args.model_path, explanation_manager)
    
    # 创建测试状态
    state = create_test_state()
    
    # 获取合法动作
    legal_actions = state.get_legal_actions()
    
    # 创建信念追踪器
    belief_trackers = {
        0: BeliefTracker(player_id=0, num_cards=54),
        1: BeliefTracker(player_id=1, num_cards=54),
        2: BeliefTracker(player_id=2, num_cards=54)
    }
    
    # 使用集成AI系统选择动作，启用解释模式
    logger.info("使用集成AI系统选择动作...")
    action, explanation = system.act(
        state=state,
        legal_actions=legal_actions,
        temperature=1.0,
        deterministic=False,
        belief_trackers=belief_trackers,
        current_player_id=0,
        explain=True
    )
    
    # 打印选择的动作
    logger.info(f"选择的动作: {action}")
    
    # 打印解释结果
    if explanation:
        logger.info("解释结果:")
        logger.info(f"摘要: {explanation.get('summary', '无摘要')}")
        logger.info(f"置信度: {explanation.get('confidence', 0.0)}")
        logger.info("主要动作:")
        for action_info in explanation.get('top_actions', []):
            logger.info(f"  {action_info}")
        
        # 如果启用了可视化，打印可视化URL
        if args.visualization and 'visualizations' in explanation:
            logger.info("可视化:")
            for name, url in explanation['visualizations'].items():
                logger.info(f"  {name}: {url[:50]}...")
    else:
        logger.warning("未获取到解释结果")
    
    # 打印统计信息
    logger.info("统计信息:")
    for key, value in explanation_manager.get_stats().items():
        logger.info(f"  {key}: {value}")


if __name__ == '__main__':
    main()
