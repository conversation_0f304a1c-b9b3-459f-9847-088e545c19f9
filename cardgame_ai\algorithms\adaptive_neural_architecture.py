"""
自适应神经架构模块

实现自适应神经架构，使模型能够自动调整结构以适应不同的任务和挑战。
包括神经架构搜索、动态网络扩展、条件计算路径和模块化网络设计等技术。
"""
import os
import math
import copy
import logging
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional, Any, Union, Callable
from abc import ABC, abstractmethod

from cardgame_ai.algorithms.transformer_network import (
    TransformerEncoder,
    PositionalEncoding,
    MultiHeadAttention
)
from cardgame_ai.algorithms.advanced_transformer import (
    CrossRoundAttention,
    AdvancedTransformerEncoderLayer,
    AdvancedTransformerEncoder,
    AdvancedRelativePositionalEncoding
)


logger = logging.getLogger(__name__)


class ArchitectureSearchSpace:
    """
    神经架构搜索空间

    定义神经架构搜索的参数空间，包括网络深度、宽度、激活函数等。
    """

    def __init__(self):
        """
        初始化架构搜索空间
        """
        # 网络深度范围
        self.depth_range = {
            "min": 2,
            "max": 24,
            "step": 2
        }

        # 隐藏层宽度范围
        self.width_range = {
            "min": 64,
            "max": 512,
            "step": 64
        }

        # 注意力头数范围
        self.num_heads_range = {
            "min": 4,
            "max": 16,
            "step": 4
        }

        # 前馈网络维度范围
        self.ff_dim_range = {
            "min": 128,
            "max": 1024,
            "step": 128
        }

        # 激活函数选项
        self.activation_functions = [
            nn.ReLU,
            nn.GELU,
            nn.SiLU,
            nn.Mish
        ]

        # 归一化层选项
        self.normalization_layers = [
            nn.LayerNorm,
            nn.BatchNorm1d
        ]

        # 注意力机制选项
        self.attention_mechanisms = [
            "standard",
            "relative",
            "cross_round"
        ]

        # 位置编码选项
        self.positional_encodings = [
            "standard",
            "relative",
            "learned"
        ]

        # 残差连接选项
        self.residual_options = [
            "standard",
            "dense",
            "highway"
        ]

        # 正则化选项
        self.regularization_options = {
            "dropout": [0.0, 0.1, 0.2, 0.3],
            "weight_decay": [0.0, 1e-5, 1e-4, 1e-3]
        }

    def sample_architecture(self) -> Dict[str, Any]:
        """
        随机采样一个架构配置

        Returns:
            Dict[str, Any]: 架构配置
        """
        config = {}

        # 采样网络深度
        config["depth"] = np.random.randint(
            self.depth_range["min"],
            self.depth_range["max"] + 1,
            size=1
        )[0]

        # 采样隐藏层宽度
        config["width"] = np.random.randint(
            self.width_range["min"],
            self.width_range["max"] + 1,
            size=1
        )[0]

        # 采样注意力头数
        config["num_heads"] = np.random.randint(
            self.num_heads_range["min"],
            self.num_heads_range["max"] + 1,
            size=1
        )[0]

        # 采样前馈网络维度
        config["ff_dim"] = np.random.randint(
            self.ff_dim_range["min"],
            self.ff_dim_range["max"] + 1,
            size=1
        )[0]

        # 采样激活函数
        config["activation"] = np.random.choice(self.activation_functions)

        # 采样归一化层
        config["normalization"] = np.random.choice(self.normalization_layers)

        # 采样注意力机制
        config["attention_mechanism"] = np.random.choice(self.attention_mechanisms)

        # 采样位置编码
        config["positional_encoding"] = np.random.choice(self.positional_encodings)

        # 采样残差连接
        config["residual"] = np.random.choice(self.residual_options)

        # 采样正则化选项
        config["dropout"] = np.random.choice(self.regularization_options["dropout"])
        config["weight_decay"] = np.random.choice(self.regularization_options["weight_decay"])

        return config

    def mutate_architecture(self, config: Dict[str, Any], mutation_rate: float = 0.2) -> Dict[str, Any]:
        """
        变异架构配置

        Args:
            config (Dict[str, Any]): 原始架构配置
            mutation_rate (float, optional): 变异率. Defaults to 0.2.

        Returns:
            Dict[str, Any]: 变异后的架构配置
        """
        new_config = copy.deepcopy(config)

        # 变异网络深度
        if np.random.random() < mutation_rate:
            new_depth = new_config["depth"] + np.random.choice([-2, 2])
            new_depth = max(self.depth_range["min"], min(self.depth_range["max"], new_depth))
            new_config["depth"] = new_depth

        # 变异隐藏层宽度
        if np.random.random() < mutation_rate:
            new_width = new_config["width"] + np.random.choice([-64, 64])
            new_width = max(self.width_range["min"], min(self.width_range["max"], new_width))
            new_config["width"] = new_width

        # 变异注意力头数
        if np.random.random() < mutation_rate:
            new_num_heads = new_config["num_heads"] + np.random.choice([-4, 4])
            new_num_heads = max(self.num_heads_range["min"], min(self.num_heads_range["max"], new_num_heads))
            new_config["num_heads"] = new_num_heads

        # 变异前馈网络维度
        if np.random.random() < mutation_rate:
            new_ff_dim = new_config["ff_dim"] + np.random.choice([-128, 128])
            new_ff_dim = max(self.ff_dim_range["min"], min(self.ff_dim_range["max"], new_ff_dim))
            new_config["ff_dim"] = new_ff_dim

        # 变异激活函数
        if np.random.random() < mutation_rate:
            new_config["activation"] = np.random.choice(self.activation_functions)

        # 变异归一化层
        if np.random.random() < mutation_rate:
            new_config["normalization"] = np.random.choice(self.normalization_layers)

        # 变异注意力机制
        if np.random.random() < mutation_rate:
            new_config["attention_mechanism"] = np.random.choice(self.attention_mechanisms)

        # 变异位置编码
        if np.random.random() < mutation_rate:
            new_config["positional_encoding"] = np.random.choice(self.positional_encodings)

        # 变异残差连接
        if np.random.random() < mutation_rate:
            new_config["residual"] = np.random.choice(self.residual_options)

        # 变异正则化选项
        if np.random.random() < mutation_rate:
            new_config["dropout"] = np.random.choice(self.regularization_options["dropout"])

        if np.random.random() < mutation_rate:
            new_config["weight_decay"] = np.random.choice(self.regularization_options["weight_decay"])

        return new_config

    def crossover_architectures(self, config1: Dict[str, Any], config2: Dict[str, Any]) -> Dict[str, Any]:
        """
        交叉两个架构配置

        Args:
            config1 (Dict[str, Any]): 第一个架构配置
            config2 (Dict[str, Any]): 第二个架构配置

        Returns:
            Dict[str, Any]: 交叉后的架构配置
        """
        new_config = {}

        # 随机选择每个参数
        for key in config1.keys():
            if np.random.random() < 0.5:
                new_config[key] = config1[key]
            else:
                new_config[key] = config2[key]

        return new_config


class NeuralArchitectureSearch:
    """
    神经架构搜索

    实现神经架构搜索算法，包括进化算法和贝叶斯优化。
    """

    def __init__(
        self,
        search_space: ArchitectureSearchSpace,
        evaluation_function: Callable[[Dict[str, Any]], float],
        population_size: int = 20,
        num_generations: int = 10,
        mutation_rate: float = 0.2,
        crossover_rate: float = 0.5,
        tournament_size: int = 3,
        elite_size: int = 2
    ):
        """
        初始化神经架构搜索

        Args:
            search_space: 架构搜索空间
            evaluation_function: 架构评估函数，返回评分（越高越好）
            population_size: 种群大小
            num_generations: 代数
            mutation_rate: 变异率
            crossover_rate: 交叉率
            tournament_size: 选择赛大小
            elite_size: 精英数量
        """
        self.search_space = search_space
        self.evaluation_function = evaluation_function
        self.population_size = population_size
        self.num_generations = num_generations
        self.mutation_rate = mutation_rate
        self.crossover_rate = crossover_rate
        self.tournament_size = tournament_size
        self.elite_size = elite_size

        # 初始化种群
        self.population = []
        self.fitness = []

        # 记录最佳架构
        self.best_architecture = None
        self.best_fitness = float('-inf')

        # 记录搜索历史
        self.history = {
            "best_fitness": [],
            "avg_fitness": [],
            "best_architecture": []
        }

    def initialize_population(self):
        """
        初始化种群
        """
        self.population = []
        self.fitness = []

        for _ in range(self.population_size):
            # 随机采样架构
            architecture = self.search_space.sample_architecture()
            self.population.append(architecture)

            # 评估架构
            fitness = self.evaluation_function(architecture)
            self.fitness.append(fitness)

            # 更新最佳架构
            if fitness > self.best_fitness:
                self.best_fitness = fitness
                self.best_architecture = architecture

        # 记录初始化结果
        self.history["best_fitness"].append(self.best_fitness)
        self.history["avg_fitness"].append(sum(self.fitness) / len(self.fitness))
        self.history["best_architecture"].append(self.best_architecture)

    def tournament_selection(self) -> Dict[str, Any]:
        """
        选择赛选择

        Returns:
            选中的架构
        """
        # 随机选择参赛者
        tournament_indices = np.random.choice(
            len(self.population),
            size=self.tournament_size,
            replace=False
        )

        # 选择最佳参赛者
        best_idx = tournament_indices[0]
        best_fitness = self.fitness[best_idx]

        for idx in tournament_indices[1:]:
            if self.fitness[idx] > best_fitness:
                best_idx = idx
                best_fitness = self.fitness[idx]

        return self.population[best_idx]

    def evolve_population(self):
        """
        进化种群
        """
        # 保存精英
        elite_indices = np.argsort(self.fitness)[-self.elite_size:]
        new_population = [self.population[i] for i in elite_indices]

        # 生成新种群
        while len(new_population) < self.population_size:
            # 选择父代
            parent1 = self.tournament_selection()

            # 决定是否交叉
            if np.random.random() < self.crossover_rate:
                parent2 = self.tournament_selection()
                offspring = self.search_space.crossover_architectures(parent1, parent2)
            else:
                offspring = copy.deepcopy(parent1)

            # 变异
            offspring = self.search_space.mutate_architecture(offspring, self.mutation_rate)

            # 添加到新种群
            new_population.append(offspring)

        # 更新种群
        self.population = new_population

        # 评估新种群
        self.fitness = [self.evaluation_function(arch) for arch in self.population]

        # 更新最佳架构
        current_best_idx = np.argmax(self.fitness)
        current_best_fitness = self.fitness[current_best_idx]

        if current_best_fitness > self.best_fitness:
            self.best_fitness = current_best_fitness
            self.best_architecture = self.population[current_best_idx]

        # 记录进化结果
        self.history["best_fitness"].append(self.best_fitness)
        self.history["avg_fitness"].append(sum(self.fitness) / len(self.fitness))
        self.history["best_architecture"].append(self.best_architecture)

    def search(self) -> Dict[str, Any]:
        """
        执行神经架构搜索

        Returns:
            最佳架构
        """
        # 初始化种群
        self.initialize_population()

        # 进化多代
        for generation in range(self.num_generations):
            logger.info(f"Generation {generation+1}/{self.num_generations}")
            logger.info(f"Best fitness: {self.best_fitness:.4f}")
            logger.info(f"Average fitness: {self.history['avg_fitness'][-1]:.4f}")

            # 进化种群
            self.evolve_population()

        logger.info("Neural architecture search completed")
        logger.info(f"Best fitness: {self.best_fitness:.4f}")
        logger.info(f"Best architecture: {self.best_architecture}")

        return self.best_architecture


class DynamicNetworkExtension(nn.Module):
    """
    动态网络扩展

    实现动态网络扩展机制，允许模型在训练过程中自动扩展网络结构。
    """

    def __init__(
        self,
        input_dim: int,
        output_dim: int,
        hidden_dims: List[int] = [128, 128],
        max_hidden_dims: List[int] = [256, 256],
        growth_threshold: float = 0.01,
        growth_factor: float = 1.5,
        min_growth: int = 16,
        activation: nn.Module = nn.ReLU,
        dropout: float = 0.1
    ):
        """
        初始化动态网络扩展

        Args:
            input_dim: 输入维度
            output_dim: 输出维度
            hidden_dims: 初始隐藏层维度
            max_hidden_dims: 最大隐藏层维度
            growth_threshold: 增长阈值，当损失下降小于该值时触发扩展
            growth_factor: 增长因子，每次扩展的倍数
            min_growth: 最小增长单元数
            activation: 激活函数
            dropout: Dropout比率
        """
        super(DynamicNetworkExtension, self).__init__()

        self.input_dim = input_dim
        self.output_dim = output_dim
        self.hidden_dims = hidden_dims.copy()
        self.max_hidden_dims = max_hidden_dims.copy()
        self.growth_threshold = growth_threshold
        self.growth_factor = growth_factor
        self.min_growth = min_growth
        self.activation_class = activation
        self.dropout_rate = dropout

        # 创建初始网络
        self.layers = nn.ModuleList()
        self._build_network()

        # 记录扩展历史
        self.extension_history = []

        # 记录损失历史
        self.loss_history = []
        self.plateau_counter = 0

    def _build_network(self):
        """
        构建网络
        """
        # 清除现有层
        self.layers = nn.ModuleList()

        # 构建输入层
        dims = [self.input_dim] + self.hidden_dims + [self.output_dim]

        # 构建各层
        for i in range(len(dims) - 1):
            # 添加线性层
            self.layers.append(nn.Linear(dims[i], dims[i+1]))

            # 如果不是最后一层，添加激活函数和Dropout
            if i < len(dims) - 2:
                self.layers.append(self.activation_class())
                self.layers.append(nn.Dropout(self.dropout_rate))

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播

        Args:
            x: 输入张量

        Returns:
            输出张量
        """
        for layer in self.layers:
            x = layer(x)
        return x

    def extend_network(self, loss: float) -> bool:
        """
        扩展网络

        Args:
            loss: 当前损失值

        Returns:
            是否扩展了网络
        """
        # 记录损失
        self.loss_history.append(loss)

        # 如果损失历史不足，不扩展
        if len(self.loss_history) < 5:
            return False

        # 计算损失下降率
        recent_losses = self.loss_history[-5:]
        loss_decrease = (recent_losses[0] - recent_losses[-1]) / recent_losses[0]

        # 如果损失下降率小于阈值，考虑扩展
        if loss_decrease < self.growth_threshold:
            self.plateau_counter += 1
        else:
            self.plateau_counter = 0

        # 如果连续3次处于平台期，扩展网络
        if self.plateau_counter >= 3:
            # 重置计数器
            self.plateau_counter = 0

            # 扩展网络
            return self._grow_network()

        return False

    def __init__(
        self,
        input_dim: int,
        output_dim: int,
        hidden_dims: List[int] = [128, 128],
        max_hidden_dims: List[int] = [256, 256],
        growth_threshold: float = 0.01,
        growth_factor: float = 1.5,
        min_growth: int = 16,
        activation: nn.Module = nn.ReLU,
        dropout: float = 0.1
    ):
        """
        初始化动态网络扩展

        Args:
            input_dim: 输入维度
            output_dim: 输出维度
            hidden_dims: 初始隐藏层维度
            max_hidden_dims: 最大隐藏层维度
            growth_threshold: 增长阈值，当损失下降小于该值时触发扩展
            growth_factor: 增长因子，每次扩展的倍数
            min_growth: 最小增长单元数
            activation: 激活函数
            dropout: Dropout比率
        """
        super(DynamicNetworkExtension, self).__init__()

        self.input_dim = input_dim
        self.output_dim = output_dim
        self.hidden_dims = hidden_dims.copy()
        self.max_hidden_dims = max_hidden_dims.copy()
        self.growth_threshold = growth_threshold
        self.growth_factor = growth_factor
        self.min_growth = min_growth
        self.activation_class = activation
        self.dropout_rate = dropout

        # 创建初始网络
        self.layers = nn.ModuleList()
        self._build_network()

        # 记录扩展历史
        self.extension_history = []

        # 记录损失历史
        self.loss_history = []
        self.plateau_counter = 0

    def _build_network(self):
        """
        构建网络
        """
        # 清除现有层
        self.layers = nn.ModuleList()

        # 构建输入层
        dims = [self.input_dim] + self.hidden_dims + [self.output_dim]

        # 构建各层
        for i in range(len(dims) - 1):
            # 添加线性层
            self.layers.append(nn.Linear(dims[i], dims[i+1]))

            # 如果不是最后一层，添加激活函数和Dropout
            if i < len(dims) - 2:
                self.layers.append(self.activation_class())
                self.layers.append(nn.Dropout(self.dropout_rate))

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播

        Args:
            x: 输入张量

        Returns:
            输出张量
        """
        for layer in self.layers:
            x = layer(x)
        return x

    def extend_network(self, loss: float) -> bool:
        """
        扩展网络

        Args:
            loss: 当前损失值

        Returns:
            是否扩展了网络
        """
        # 记录损失
        self.loss_history.append(loss)

        # 如果损失历史不足，不扩展
        if len(self.loss_history) < 5:
            return False

        # 计算损失下降率
        recent_losses = self.loss_history[-5:]
        loss_decrease = (recent_losses[0] - recent_losses[-1]) / recent_losses[0]

        # 如果损失下降率小于阈值，考虑扩展
        if loss_decrease < self.growth_threshold:
            self.plateau_counter += 1
        else:
            self.plateau_counter = 0

        # 如果连续3次处于平台期，扩展网络
        if self.plateau_counter >= 3:
            # 重置计数器
            self.plateau_counter = 0

            # 扩展网络
            return self._grow_network()

        return False

    def _grow_network(self) -> bool:
        """
        增长网络

        Returns:
            是否成功增长
        """
        # 检查是否所有层都达到最大尺寸
        all_maxed = True
        for i, dim in enumerate(self.hidden_dims):
            if dim < self.max_hidden_dims[i]:
                all_maxed = False
                break

        # 如果所有层都达到最大尺寸，不再增长
        if all_maxed:
            logger.info("All layers have reached maximum size, no further growth")
            return False

        # 记录原始尺寸
        old_dims = self.hidden_dims.copy()

        # 增长各层
        for i in range(len(self.hidden_dims)):
            if self.hidden_dims[i] < self.max_hidden_dims[i]:
                # 计算增长量
                growth = max(
                    self.min_growth,
                    int(self.hidden_dims[i] * (self.growth_factor - 1))
                )

                # 确保不超过最大尺寸
                self.hidden_dims[i] = min(
                    self.max_hidden_dims[i],
                    self.hidden_dims[i] + growth
                )

        # 重建网络
        old_state_dict = self.state_dict()
        self._build_network()

        # 运行参数迁移
        self._migrate_parameters(old_state_dict)

        # 记录扩展历史
        self.extension_history.append({
            "old_dims": old_dims,
            "new_dims": self.hidden_dims.copy(),
            "loss": self.loss_history[-1] if self.loss_history else None
        })

        logger.info(f"Network extended: {old_dims} -> {self.hidden_dims}")
        return True

    def _migrate_parameters(self, old_state_dict: Dict[str, torch.Tensor]):
        """
        迁移参数

        Args:
            old_state_dict: 旧的状态字典
        """
        new_state_dict = self.state_dict()

        # 遍历新的状态字典
        for name, param in new_state_dict.items():
            if name in old_state_dict:
                old_param = old_state_dict[name]

                # 如果尺寸相同，直接复制
                if param.shape == old_param.shape:
                    param.copy_(old_param)
                # 如果是权重矩阵，复制可复制的部分
                elif len(param.shape) == 2 and len(old_param.shape) == 2:
                    # 复制可复制的部分
                    min_rows = min(param.shape[0], old_param.shape[0])
                    min_cols = min(param.shape[1], old_param.shape[1])
                    param[:min_rows, :min_cols].copy_(old_param[:min_rows, :min_cols])

                    # 初始化新参数
                    if param.shape[0] > old_param.shape[0] or param.shape[1] > old_param.shape[1]:
                        # 使用高斯分布初始化新参数
                        if param.shape[0] > old_param.shape[0]:
                            nn.init.normal_(param[old_param.shape[0]:, :], std=0.01)
                        if param.shape[1] > old_param.shape[1]:
                            nn.init.normal_(param[:, old_param.shape[1]:], std=0.01)
                # 如果是偏置向量，复制可复制的部分
                elif len(param.shape) == 1 and len(old_param.shape) == 1:
                    min_size = min(param.shape[0], old_param.shape[0])
                    param[:min_size].copy_(old_param[:min_size])

                    # 初始化新参数
                    if param.shape[0] > old_param.shape[0]:
                        nn.init.zeros_(param[old_param.shape[0]:])

        # 加载新的状态字典
        self.load_state_dict(new_state_dict)


class ConditionalComputationPath(nn.Module):
    """
    条件计算路径

    实现条件计算路径机制，允许模型根据输入动态选择不同的计算路径。
    """

    def __init__(
        self,
        input_dim: int,
        output_dim: int,
        num_experts: int = 4,
        expert_hidden_dims: List[int] = [128, 128],
        gate_hidden_dims: List[int] = [64],
        activation: nn.Module = nn.ReLU,
        dropout: float = 0.1,
        sparsity_threshold: float = 0.2,
        capacity_factor: float = 1.5
    ):
        """
        初始化条件计算路径

        Args:
            input_dim: 输入维度
            output_dim: 输出维度
            num_experts: 专家数量
            expert_hidden_dims: 专家网络的隐藏层维度
            gate_hidden_dims: 门控网络的隐藏层维度
            activation: 激活函数
            dropout: Dropout比率
            sparsity_threshold: 稀疏性阈值，用于控制每个输入使用的专家数量
            capacity_factor: 容量因子，用于控制每个专家处理的输入数量
        """
        super(ConditionalComputationPath, self).__init__()

        self.input_dim = input_dim
        self.output_dim = output_dim
        self.num_experts = num_experts
        self.expert_hidden_dims = expert_hidden_dims
        self.gate_hidden_dims = gate_hidden_dims
        self.activation_class = activation
        self.dropout_rate = dropout
        self.sparsity_threshold = sparsity_threshold
        self.capacity_factor = capacity_factor

        # 创建专家网络
        self.experts = nn.ModuleList([
            self._create_expert() for _ in range(num_experts)
        ])

        # 创建门控网络
        self.gate = self._create_gate()

        # 记录专家使用统计
        self.expert_usage = torch.zeros(num_experts)

    def _create_expert(self) -> nn.Sequential:
        """
        创建专家网络

        Returns:
            专家网络
        """
        layers = []
        dims = [self.input_dim] + self.expert_hidden_dims + [self.output_dim]

        for i in range(len(dims) - 1):
            # 添加线性层
            layers.append(nn.Linear(dims[i], dims[i+1]))

            # 如果不是最后一层，添加激活函数和Dropout
            if i < len(dims) - 2:
                layers.append(self.activation_class())
                layers.append(nn.Dropout(self.dropout_rate))

        return nn.Sequential(*layers)

    def _create_gate(self) -> nn.Sequential:
        """
        创建门控网络

        Returns:
            门控网络
        """
        layers = []
        dims = [self.input_dim] + self.gate_hidden_dims + [self.num_experts]

        for i in range(len(dims) - 1):
            # 添加线性层
            layers.append(nn.Linear(dims[i], dims[i+1]))

            # 如果不是最后一层，添加激活函数和Dropout
            if i < len(dims) - 2:
                layers.append(self.activation_class())
                layers.append(nn.Dropout(self.dropout_rate))

        return nn.Sequential(*layers)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播

        Args:
            x: 输入张量，形状为[batch_size, input_dim]

        Returns:
            输出张量，形状为[batch_size, output_dim]
        """
        batch_size = x.shape[0]

        # 计算门控权重
        gate_logits = self.gate(x)  # [batch_size, num_experts]
        gate_probs = F.softmax(gate_logits, dim=-1)

        # 应用稀疏性阈值
        # 将小于阈值的概率设为0
        mask = gate_probs >= self.sparsity_threshold
        gate_probs = gate_probs * mask.float()

        # 重新归一化
        gate_probs = gate_probs / (gate_probs.sum(dim=-1, keepdim=True) + 1e-10)

        # 计算每个专家的负载
        # 每个专家的容量 = batch_size * capacity_factor / num_experts
        capacity = int(batch_size * self.capacity_factor / self.num_experts)
        expert_load = torch.sum(gate_probs > 0, dim=0)  # [num_experts]

        # 更新专家使用统计
        self.expert_usage += expert_load.detach().cpu()

        # 初始化输出
        outputs = torch.zeros(batch_size, self.output_dim, device=x.device)

        # 对每个专家进行计算
        for expert_idx in range(self.num_experts):
            # 选择使用该专家的样本
            expert_mask = gate_probs[:, expert_idx] > 0
            expert_count = expert_mask.sum().item()

            # 如果没有样本使用该专家，跳过
            if expert_count == 0:
                continue

            # 如果样本数量超过容量，选择概率最高的样本
            if expert_count > capacity:
                # 选择概率最高的capacity个样本
                _, top_indices = torch.topk(gate_probs[:, expert_idx], capacity)
                new_mask = torch.zeros_like(expert_mask)
                new_mask[top_indices] = True
                expert_mask = new_mask
                expert_count = capacity

            # 提取使用该专家的样本
            expert_inputs = x[expert_mask]
            expert_weights = gate_probs[expert_mask, expert_idx].unsqueeze(1)  # [expert_count, 1]

            # 计算专家输出
            expert_outputs = self.experts[expert_idx](expert_inputs)  # [expert_count, output_dim]

            # 加权平均
            weighted_outputs = expert_outputs * expert_weights

            # 将输出添加到最终结果
            outputs[expert_mask] += weighted_outputs

        return outputs

    def get_expert_usage_stats(self) -> Dict[str, float]:
        """
        获取专家使用统计

        Returns:
            专家使用统计
        """
        total_usage = self.expert_usage.sum().item()
        if total_usage == 0:
            return {f"expert_{i}_usage": 0.0 for i in range(self.num_experts)}

        # 计算每个专家的使用比例
        usage_ratios = self.expert_usage / total_usage

        # 计算使用均衡性
        # 完全均衡时，每个专家的使用比例应为1/num_experts
        expected_ratio = 1.0 / self.num_experts
        balance_score = 1.0 - torch.mean(torch.abs(usage_ratios - expected_ratio)).item() * self.num_experts

        # 返回统计信息
        stats = {
            f"expert_{i}_usage": usage_ratios[i].item() for i in range(self.num_experts)
        }
        stats["balance_score"] = balance_score

        return stats


class ModularNetworkDesign(nn.Module):
    """
    模块化网络设计

    实现模块化网络设计，允许模型由多个可插拔的模块组成。
    """

    def __init__(
        self,
        input_dim: int,
        output_dim: int,
        module_configs: List[Dict[str, Any]],
        dropout: float = 0.1
    ):
        """
        初始化模块化网络设计

        Args:
            input_dim: 输入维度
            output_dim: 输出维度
            module_configs: 模块配置列表，每个配置包含模块类型、参数等
            dropout: Dropout比率
        """
        super(ModularNetworkDesign, self).__init__()

        self.input_dim = input_dim
        self.output_dim = output_dim
        self.module_configs = module_configs
        self.dropout_rate = dropout

        # 创建模块
        self.modules_list = nn.ModuleList()
        self._build_modules()

        # 创建输出层
        last_dim = self._get_last_dim()
        self.output_layer = nn.Linear(last_dim, output_dim)

        # 记录模块使用统计
        self.module_usage = {i: 0 for i in range(len(self.modules_list))}
        self.forward_count = 0

    def _build_modules(self):
        """
        构建模块
        """
        current_dim = self.input_dim

        for config in self.module_configs:
            module_type = config.get("type", "linear")

            if module_type == "linear":
                hidden_dim = config.get("hidden_dim", 128)
                activation = config.get("activation", nn.ReLU)

                module = nn.Sequential(
                    nn.Linear(current_dim, hidden_dim),
                    activation(),
                    nn.Dropout(self.dropout_rate)
                )

                current_dim = hidden_dim

            elif module_type == "transformer":
                hidden_dim = config.get("hidden_dim", 128)
                num_heads = config.get("num_heads", 4)
                ff_dim = config.get("ff_dim", hidden_dim * 4)
                num_layers = config.get("num_layers", 2)

                # 创建变换器模块
                module = TransformerEncoder(
                    d_model=hidden_dim,
                    nhead=num_heads,
                    dim_feedforward=ff_dim,
                    num_layers=num_layers,
                    dropout=self.dropout_rate
                )

                # 如果输入维度不等于隐藏层维度，添加映射层
                if current_dim != hidden_dim:
                    projection = nn.Linear(current_dim, hidden_dim)
                    module = nn.Sequential(projection, module)

                current_dim = hidden_dim

            elif module_type == "advanced_transformer":
                hidden_dim = config.get("hidden_dim", 128)
                num_heads = config.get("num_heads", 4)
                ff_dim = config.get("ff_dim", hidden_dim * 4)
                num_layers = config.get("num_layers", 2)

                # 创建高级变换器模块
                module = AdvancedTransformerEncoder(
                    d_model=hidden_dim,
                    nhead=num_heads,
                    dim_feedforward=ff_dim,
                    num_layers=num_layers,
                    dropout=self.dropout_rate
                )

                # 如果输入维度不等于隐藏层维度，添加映射层
                if current_dim != hidden_dim:
                    projection = nn.Linear(current_dim, hidden_dim)
                    module = nn.Sequential(projection, module)

                current_dim = hidden_dim

            elif module_type == "conditional":
                # 条件计算路径模块
                num_experts = config.get("num_experts", 4)
                expert_hidden_dims = config.get("expert_hidden_dims", [128, 128])
                gate_hidden_dims = config.get("gate_hidden_dims", [64])
                sparsity_threshold = config.get("sparsity_threshold", 0.2)

                module = ConditionalComputationPath(
                    input_dim=current_dim,
                    output_dim=expert_hidden_dims[-1],
                    num_experts=num_experts,
                    expert_hidden_dims=expert_hidden_dims,
                    gate_hidden_dims=gate_hidden_dims,
                    activation=nn.ReLU,
                    dropout=self.dropout_rate,
                    sparsity_threshold=sparsity_threshold
                )

                current_dim = expert_hidden_dims[-1]

            elif module_type == "dynamic":
                # 动态网络扩展模块
                hidden_dims = config.get("hidden_dims", [128, 128])
                max_hidden_dims = config.get("max_hidden_dims", [256, 256])
                growth_threshold = config.get("growth_threshold", 0.01)

                module = DynamicNetworkExtension(
                    input_dim=current_dim,
                    output_dim=hidden_dims[-1],
                    hidden_dims=hidden_dims[:-1],
                    max_hidden_dims=max_hidden_dims[:-1],
                    growth_threshold=growth_threshold,
                    activation=nn.ReLU,
                    dropout=self.dropout_rate
                )

                current_dim = hidden_dims[-1]

            else:
                raise ValueError(f"Unknown module type: {module_type}")

            # 添加模块到列表
            self.modules_list.append(module)

    def _get_last_dim(self) -> int:
        """
        获取最后一个模块的输出维度

        Returns:
            最后一个模块的输出维度
        """
        current_dim = self.input_dim

        for config in self.module_configs:
            module_type = config.get("type", "linear")

            if module_type == "linear":
                current_dim = config.get("hidden_dim", 128)

            elif module_type == "transformer" or module_type == "advanced_transformer":
                current_dim = config.get("hidden_dim", 128)

            elif module_type == "conditional":
                current_dim = config.get("expert_hidden_dims", [128, 128])[-1]

            elif module_type == "dynamic":
                current_dim = config.get("hidden_dims", [128, 128])[-1]

        return current_dim

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播

        Args:
            x: 输入张量，形状为[batch_size, input_dim]

        Returns:
            输出张量，形状为[batch_size, output_dim]
        """
        # 更新统计
        self.forward_count += 1

        # 传递输入通过每个模块
        for i, module in enumerate(self.modules_list):
            x = module(x)
            self.module_usage[i] += 1

        # 通过输出层
        x = self.output_layer(x)

        return x

    def get_module_usage_stats(self) -> Dict[str, float]:
        """
        获取模块使用统计

        Returns:
            模块使用统计
        """
        if self.forward_count == 0:
            return {f"module_{i}_usage": 0.0 for i in range(len(self.modules_list))}

        # 计算每个模块的使用率
        stats = {
            f"module_{i}_usage": self.module_usage[i] / self.forward_count
            for i in range(len(self.modules_list))
        }

        # 添加模块类型信息
        for i, config in enumerate(self.module_configs):
            module_type = config.get("type", "linear")
            stats[f"module_{i}_type"] = module_type

        return stats