"""
人类策略网络示例

展示如何训练和使用人类策略网络，包括基础版和增强版。
"""

import os
import torch
import numpy as np
import argparse
import logging
from typing import Dict, List, Any

from cardgame_ai.algorithms.human_policy_network import (
    HumanPolicyNetwork, 
    EnhancedHumanPolicyNetwork,
    train_human_policy,
    train_enhanced_human_policy
)
from cardgame_ai.utils.data_loader import load_human_log_data

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='人类策略网络示例')
    
    # 数据和模型路径
    parser.add_argument('--data_path', type=str, default='data/human_logs',
                        help='人类对局日志数据路径')
    parser.add_argument('--model_path', type=str, default='models/human_policy',
                        help='模型保存路径')
    
    # 模型参数
    parser.add_argument('--state_dim', type=int, default=256,
                        help='状态维度')
    parser.add_argument('--action_dim', type=int, default=309,
                        help='动作维度')
    parser.add_argument('--hidden_dims', type=int, nargs='+', default=[256, 128],
                        help='隐藏层维度列表')
    
    # 增强版模型参数
    parser.add_argument('--num_styles', type=int, default=3,
                        help='风格数量')
    parser.add_argument('--style_dim', type=int, default=32,
                        help='风格嵌入维度')
    parser.add_argument('--use_attention', action='store_true',
                        help='是否使用注意力机制')
    
    # 训练参数
    parser.add_argument('--learning_rate', type=float, default=0.001,
                        help='学习率')
    parser.add_argument('--batch_size', type=int, default=32,
                        help='批次大小')
    parser.add_argument('--num_epochs', type=int, default=50,
                        help='训练轮数')
    parser.add_argument('--patience', type=int, default=5,
                        help='早停耐心值')
    
    # 运行模式
    parser.add_argument('--mode', type=str, choices=['train', 'test', 'both'], default='both',
                        help='运行模式：训练、测试或两者都执行')
    parser.add_argument('--model_type', type=str, choices=['basic', 'enhanced', 'both'], default='both',
                        help='模型类型：基础版、增强版或两者都执行')
    
    # 设备
    parser.add_argument('--device', type=str, default=None,
                        help='计算设备，如果为None则自动选择')
    
    return parser.parse_args()


def train_models(args):
    """训练模型"""
    # 设置设备
    device = args.device if args.device else ('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 创建保存目录
    os.makedirs(os.path.dirname(args.model_path), exist_ok=True)
    
    # 训练基础版人类策略网络
    if args.model_type in ['basic', 'both']:
        logger.info("开始训练基础版人类策略网络...")
        basic_model_path = os.path.join(args.model_path, 'basic_human_policy.pt')
        
        basic_model = train_human_policy(
            log_data_path=args.data_path,
            model_save_path=basic_model_path,
            state_dim=args.state_dim,
            action_dim=args.action_dim,
            hidden_dims=args.hidden_dims,
            learning_rate=args.learning_rate,
            batch_size=args.batch_size,
            num_epochs=args.num_epochs,
            patience=args.patience,
            device=device
        )
        
        logger.info(f"基础版人类策略网络训练完成，已保存到: {basic_model_path}")
    
    # 训练增强版人类策略网络
    if args.model_type in ['enhanced', 'both']:
        logger.info("开始训练增强版人类策略网络...")
        enhanced_model_path = os.path.join(args.model_path, 'enhanced_human_policy.pt')
        
        enhanced_model = train_enhanced_human_policy(
            log_data_path=args.data_path,
            model_save_path=enhanced_model_path,
            state_dim=args.state_dim,
            action_dim=args.action_dim,
            hidden_dims=args.hidden_dims,
            num_styles=args.num_styles,
            style_dim=args.style_dim,
            use_attention=args.use_attention,
            learning_rate=args.learning_rate,
            batch_size=args.batch_size,
            num_epochs=args.num_epochs,
            patience=args.patience,
            device=device
        )
        
        logger.info(f"增强版人类策略网络训练完成，已保存到: {enhanced_model_path}")


def test_models(args):
    """测试模型"""
    # 设置设备
    device = args.device if args.device else ('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 加载测试数据
    test_loader, _ = load_human_log_data(
        args.data_path,
        batch_size=args.batch_size,
        train_ratio=0.0  # 全部用于测试
    )
    
    # 测试基础版人类策略网络
    if args.model_type in ['basic', 'both']:
        basic_model_path = os.path.join(args.model_path, 'basic_human_policy.pt')
        
        if os.path.exists(basic_model_path):
            logger.info(f"加载基础版人类策略网络: {basic_model_path}")
            
            # 加载模型
            checkpoint = torch.load(basic_model_path, map_location=device)
            basic_model = HumanPolicyNetwork(
                state_dim=args.state_dim,
                action_dim=args.action_dim,
                hidden_dims=args.hidden_dims
            ).to(device)
            basic_model.load_state_dict(checkpoint['model_state_dict'])
            
            # 测试模型
            basic_model.eval()
            test_correct = 0
            test_total = 0
            
            with torch.no_grad():
                for states, actions in test_loader:
                    # 移动数据到设备
                    states = states.to(device)
                    actions = actions.to(device)
                    
                    # 前向传播
                    logits = basic_model(states)
                    
                    # 统计
                    _, predicted = torch.max(logits, 1)
                    test_total += actions.size(0)
                    test_correct += (predicted == actions).sum().item()
            
            # 计算测试准确率
            test_acc = test_correct / test_total if test_total > 0 else 0
            logger.info(f"基础版人类策略网络测试准确率: {test_acc:.4f}")
            
            # 示例：使用模型进行预测
            if test_total > 0:
                # 获取一个示例状态
                example_state = states[0].unsqueeze(0)
                
                # 预测动作概率
                action_probs = basic_model.predict(example_state)
                
                # 获取最可能的动作
                top_actions = torch.topk(action_probs[0], min(5, args.action_dim))
                
                logger.info("基础版人类策略网络预测示例:")
                for i, (action_idx, prob) in enumerate(zip(top_actions.indices, top_actions.values)):
                    logger.info(f"  Top {i+1}: 动作 {action_idx.item()}, 概率 {prob.item():.4f}")
        else:
            logger.warning(f"基础版人类策略网络模型不存在: {basic_model_path}")
    
    # 测试增强版人类策略网络
    if args.model_type in ['enhanced', 'both']:
        enhanced_model_path = os.path.join(args.model_path, 'enhanced_human_policy.pt')
        
        if os.path.exists(enhanced_model_path):
            logger.info(f"加载增强版人类策略网络: {enhanced_model_path}")
            
            # 加载模型
            enhanced_model = EnhancedHumanPolicyNetwork.load(enhanced_model_path, device)
            
            # 测试模型
            enhanced_model.eval()
            
            # 对每种风格进行测试
            for style_id in range(args.num_styles):
                style_name = enhanced_model.get_style_description(style_id)
                logger.info(f"测试风格: {style_name} (ID: {style_id})")
                
                test_correct = 0
                test_total = 0
                
                with torch.no_grad():
                    for states, actions in test_loader:
                        # 移动数据到设备
                        states = states.to(device)
                        actions = actions.to(device)
                        
                        # 前向传播（对每个样本使用相同的风格）
                        batch_correct = 0
                        for i, (state, action) in enumerate(zip(states, actions)):
                            # 使用特定风格进行前向传播
                            logits = enhanced_model(state.unsqueeze(0), style_id)
                            
                            # 统计
                            _, predicted = torch.max(logits, 1)
                            batch_correct += (predicted == action).sum().item()
                        
                        # 统计
                        test_total += len(states)
                        test_correct += batch_correct
                
                # 计算测试准确率
                test_acc = test_correct / test_total if test_total > 0 else 0
                logger.info(f"  风格 '{style_name}' 测试准确率: {test_acc:.4f}")
            
            # 示例：使用模型进行预测
            if test_total > 0:
                # 获取一个示例状态
                example_state = states[0].unsqueeze(0)
                
                logger.info("增强版人类策略网络预测示例:")
                
                # 对每种风格进行预测
                for style_id in range(args.num_styles):
                    style_name = enhanced_model.get_style_description(style_id)
                    
                    # 预测动作概率
                    action_probs = enhanced_model.predict(example_state, style_id)
                    
                    # 获取最可能的动作
                    top_actions = torch.topk(action_probs[0], min(5, args.action_dim))
                    
                    logger.info(f"  风格 '{style_name}' 预测:")
                    for i, (action_idx, prob) in enumerate(zip(top_actions.indices, top_actions.values)):
                        logger.info(f"    Top {i+1}: 动作 {action_idx.item()}, 概率 {prob.item():.4f}")
            
            # 显示风格使用统计
            style_stats = enhanced_model.get_stats()
            logger.info("风格使用统计:")
            for style_id, count in style_stats['style_usage'].items():
                style_name = enhanced_model.get_style_description(style_id)
                logger.info(f"  风格 '{style_name}': 使用 {count} 次")
        else:
            logger.warning(f"增强版人类策略网络模型不存在: {enhanced_model_path}")


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 训练模型
    if args.mode in ['train', 'both']:
        train_models(args)
    
    # 测试模型
    if args.mode in ['test', 'both']:
        test_models(args)


if __name__ == '__main__':
    main()
