#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
辅助功能模块

提供界面的可访问性支持，包括键盘导航支持、屏幕阅读器支持、颜色对比度优化和字体大小调整等功能。
"""

import os
import logging
import json
from typing import Dict, Any, Optional, List, Tuple

from PySide6.QtCore import QObject, Signal, QSettings, Qt
from PySide6.QtWidgets import QApplication, QWidget
from PySide6.QtGui import QFont, QColor, QPalette

from cardgame_ai.desktop.utils.platform_utils import PlatformConfig

logger = logging.getLogger(__name__)


class Accessibility(QObject):
    """辅助功能类，管理应用程序的可访问性功能"""

    # 辅助功能变更信号
    accessibility_changed = Signal(str)

    def __init__(self, parent=None):
        """
        初始化辅助功能管理器

        Args:
            parent: 父对象
        """
        super().__init__(parent)

        # 默认配置
        self._default_config = {
            "keyboard_navigation": True,
            "screen_reader": False,
            "high_contrast": False,
            "font_scale": 1.0,
            "color_blind_mode": "none",  # none, protanopia, deuteranopia, tritanopia
            "reduced_motion": False,
            "text_to_speech": False,
            "focus_highlight": True,
            "tab_focus_mode": True
        }

        # 当前配置
        self._config = self._default_config.copy()

        # 颜色对比度调整方案
        self._color_schemes = {
            "default": {
                "background": "#f5f5f5",
                "foreground": "#333333",
                "primary": "#3498db",
                "secondary": "#2ecc71",
                "accent": "#e74c3c",
                "border": "#d0d0d0"
            },
            "high_contrast": {
                "background": "#000000",
                "foreground": "#ffffff",
                "primary": "#00aaff",
                "secondary": "#00ff00",
                "accent": "#ff0000",
                "border": "#ffffff"
            },
            "protanopia": {  # 红色盲
                "background": "#f5f5f5",
                "foreground": "#333333",
                "primary": "#0070bb",
                "secondary": "#ffc20e",
                "accent": "#925fa3",
                "border": "#d0d0d0"
            },
            "deuteranopia": {  # 绿色盲
                "background": "#f5f5f5",
                "foreground": "#333333",
                "primary": "#0070bb",
                "secondary": "#ffc20e",
                "accent": "#d11141",
                "border": "#d0d0d0"
            },
            "tritanopia": {  # 蓝色盲
                "background": "#f5f5f5",
                "foreground": "#333333",
                "primary": "#d11141",
                "secondary": "#ffc20e",
                "accent": "#0070bb",
                "border": "#d0d0d0"
            }
        }

        logger.info("辅助功能管理器初始化完成")

    def load_config(self, config: Dict[str, Any]) -> None:
        """
        加载辅助功能配置

        Args:
            config: 配置字典
        """
        if not config:
            return

        # 更新配置
        for key, value in config.items():
            if key in self._config:
                self._config[key] = value

        logger.info("已加载辅助功能配置")

    def get_config(self) -> Dict[str, Any]:
        """
        获取当前辅助功能配置

        Returns:
            当前配置字典
        """
        return self._config.copy()

    def set_config(self, key: str, value: Any) -> None:
        """
        设置辅助功能配置

        Args:
            key: 配置项名称
            value: 配置项值
        """
        if key not in self._config:
            logger.warning(f"未知的辅助功能配置项: {key}")
            return

        # 更新配置
        self._config[key] = value

        # 发送变更信号
        self.accessibility_changed.emit(key)

        logger.info(f"已设置辅助功能配置: {key} = {value}")

    def reset_config(self) -> None:
        """重置辅助功能配置为默认值"""
        self._config = self._default_config.copy()
        self.accessibility_changed.emit("all")
        logger.info("已重置辅助功能配置为默认值")

    def apply_font_scale(self, app: QApplication) -> None:
        """
        应用字体缩放

        Args:
            app: QApplication实例
        """
        font_scale = self._config.get("font_scale", 1.0)
        if font_scale <= 0:
            font_scale = 1.0

        # 获取当前字体
        font = app.font()
        default_size = PlatformConfig.get_default_font_size()

        # 设置新字体大小
        font.setPointSize(int(default_size * font_scale))
        app.setFont(font)

        logger.info(f"已应用字体缩放: {font_scale}")

    def apply_color_scheme(self, app: QApplication) -> None:
        """
        应用颜色方案

        Args:
            app: QApplication实例
        """
        # 获取颜色方案
        scheme_name = "default"
        if self._config.get("high_contrast", False):
            scheme_name = "high_contrast"
        elif self._config.get("color_blind_mode", "none") != "none":
            scheme_name = self._config.get("color_blind_mode", "none")

        scheme = self._color_schemes.get(scheme_name, self._color_schemes["default"])

        # 创建调色板
        palette = QPalette()

        # 设置颜色
        palette.setColor(QPalette.Window, QColor(scheme["background"]))
        palette.setColor(QPalette.WindowText, QColor(scheme["foreground"]))
        palette.setColor(QPalette.Base, QColor(scheme["background"]))
        palette.setColor(QPalette.AlternateBase, QColor(scheme["background"]).lighter(110))
        palette.setColor(QPalette.Text, QColor(scheme["foreground"]))
        palette.setColor(QPalette.Button, QColor(scheme["background"]))
        palette.setColor(QPalette.ButtonText, QColor(scheme["foreground"]))
        palette.setColor(QPalette.BrightText, QColor(scheme["accent"]))
        palette.setColor(QPalette.Link, QColor(scheme["primary"]))
        palette.setColor(QPalette.Highlight, QColor(scheme["primary"]))
        palette.setColor(QPalette.HighlightedText, QColor("#ffffff"))

        # 应用调色板
        app.setPalette(palette)

        logger.info(f"已应用颜色方案: {scheme_name}")

    def setup_keyboard_navigation(self, widget: QWidget) -> None:
        """
        设置键盘导航

        Args:
            widget: 要设置键盘导航的组件
        """
        if not self._config.get("keyboard_navigation", True):
            return

        # 设置焦点策略
        widget.setFocusPolicy(Qt.StrongFocus)

        # 设置焦点高亮
        if self._config.get("focus_highlight", True):
            widget.setStyleSheet("""
                *:focus {
                    border: 2px solid #3498db;
                    outline: none;
                }
            """)

        # 设置Tab焦点模式
        if self._config.get("tab_focus_mode", True):
            # 兼容不同版本的PySide6
            if hasattr(widget, 'setTabKeyNavigation'):
                widget.setTabKeyNavigation(True)
            else:
                # 备选方法，使用setFocusPolicy
                widget.setFocusPolicy(Qt.StrongFocus)

        logger.info(f"已设置键盘导航: {widget.objectName()}")

    def setup_screen_reader(self, widget: QWidget) -> None:
        """
        设置屏幕阅读器支持

        Args:
            widget: 要设置屏幕阅读器支持的组件
        """
        if not self._config.get("screen_reader", False):
            return

        # 设置辅助文本
        if not widget.accessibleName() and widget.objectName():
            widget.setAccessibleName(widget.objectName())

        # 设置辅助描述
        if not widget.accessibleDescription() and widget.toolTip():
            widget.setAccessibleDescription(widget.toolTip())

        # 设置可访问性标志
        # 兼容不同版本的PySide6
        if hasattr(widget, 'setAccessibleFlag'):
            widget.setAccessibleFlag(QWidget.AccessibleFlag.Accessible, True)
        elif hasattr(widget, 'setAccessibleName'):
            # 确保设置了AccessibleName
            if not widget.accessibleName():
                widget.setAccessibleName(widget.objectName() or "Accessible Widget")

        logger.info(f"已设置屏幕阅读器支持: {widget.objectName()}")

    def apply_reduced_motion(self, app: QApplication) -> None:
        """
        应用减少动画效果

        Args:
            app: QApplication实例
        """
        reduced_motion = self._config.get("reduced_motion", False)

        # 设置全局属性
        app.setProperty("reduced_motion", reduced_motion)

        logger.info(f"已应用减少动画效果: {reduced_motion}")

    def apply_all(self, app: QApplication) -> None:
        """
        应用所有辅助功能设置

        Args:
            app: QApplication实例
        """
        # 应用字体缩放
        self.apply_font_scale(app)

        # 应用颜色方案
        self.apply_color_scheme(app)

        # 应用减少动画效果
        self.apply_reduced_motion(app)

        # 注意：不要在这里调用setup_keyboard_navigation，因为它需要QWidget参数而不是QApplication

        logger.info("已应用所有辅助功能设置")


# 全局辅助功能管理器实例
_accessibility_manager = None


def get_accessibility_manager() -> Accessibility:
    """
    获取全局辅助功能管理器实例

    Returns:
        辅助功能管理器实例
    """
    global _accessibility_manager
    if _accessibility_manager is None:
        _accessibility_manager = Accessibility()
    return _accessibility_manager


def apply_accessibility(widget: QWidget) -> None:
    """
    应用辅助功能到组件

    Args:
        widget: 要应用辅助功能的组件
    """
    manager = get_accessibility_manager()
    manager.setup_keyboard_navigation(widget)
    manager.setup_screen_reader(widget)
    logger.info(f"已应用辅助功能到组件: {widget.objectName()}")
