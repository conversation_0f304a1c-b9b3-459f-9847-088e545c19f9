"""
EfficientZero核心算法模块

该模块实现了EfficientZero算法的核心逻辑，包括：
- EfficientZero类：主要算法实现
- MCTS搜索与动作选择
- 训练和更新逻辑
- 重要性加权和自适应数据重用

主要功能：
- 基于MuZero的改进算法
- 自监督表示学习
- 值前缀预测
- 一致性损失计算
- 动态预算分配
"""

import copy
import torch
import torch.nn.functional as F
import numpy as np
import logging
import time
from typing import Tuple, Dict, Any, Union, List, Optional

from .muzero import MuZero
from .efficient_zero_model import EfficientZeroModel
from .continual_learning import EWC
from cardgame_ai.core.base import State, Action, Experience, Batch
from cardgame_ai.utils.opponent_distribution_switcher import OpponentDistributionSwitcher
from cardgame_ai.algorithms.components.key_moment_detector import KeyMomentDetector
from cardgame_ai.algorithms.components.dynamic_budget_allocator import DynamicBudgetAllocator

# 配置日志
logger = logging.getLogger(__name__)


class EfficientZero(MuZero):
    """
    EfficientZero算法
    
    基于MuZero的改进算法，通过自监督表示学习、值前缀预测和
    自适应数据重用等技术，显著提高样本效率。
    
    主要特性：
    - 自监督表示学习：提高状态表示质量
    - 值前缀预测：更准确的奖励预测
    - 一致性损失：增强模型稳定性
    - 重要性加权：优化数据利用效率
    """

    def __init__(
        self,
        state_shape: Tuple[int, ...],
        action_shape: Tuple[int, ...],
        hidden_dim: int = 256,
        state_dim: int = 64,
        use_resnet: bool = True,
        projection_dim: int = 128,
        prediction_dim: int = 64,
        value_prefix_length: int = 5,
        use_distributional_value: bool = False,
        value_support_size: int = 601,
        value_min: float = -300,
        value_max: float = 300,
        risk_alpha: float = 0.05,
        risk_beta: float = 0.1,
        use_belief_state: bool = False,
        belief_dim: int = 128,
        use_belief_attention: bool = True,
        belief_attention_heads: int = 4,
        use_residual_belief: bool = True,
        use_gating_mechanism: bool = True,
        num_simulations: int = 50,
        c_puct: float = 1.25,
        dirichlet_alpha: float = 0.3,
        exploration_fraction: float = 0.25,
        pb_c_base: float = 19652,
        pb_c_init: float = 1.25,
        replay_buffer_size: int = 100000,
        batch_size: int = 256,
        num_unroll_steps: int = 5,
        td_steps: int = 5,
        value_loss_weight: float = 0.25,
        policy_loss_weight: float = 1.0,
        consistency_loss_weight: float = 2.0,
        self_supervised_loss_weight: float = 2.0,
        use_ewc: bool = False,
        ewc_lambda: float = 100.0,
        ewc_state_path: Optional[str] = None,
        learning_rate: float = 0.001,
        weight_decay: float = 1e-4,
        lr_scheduler: str = 'step',
        device: str = None
    ):
        """
        初始化EfficientZero算法
        
        Args:
            state_shape: 状态空间形状
            action_shape: 动作空间形状
            hidden_dim: 隐藏层维度
            state_dim: 状态表示维度
            use_resnet: 是否使用ResNet架构
            projection_dim: 自监督学习投影维度
            prediction_dim: 自监督学习预测维度
            value_prefix_length: 值前缀长度
            use_distributional_value: 是否使用分布式价值头
            value_support_size: 分布式价值支持大小
            value_min: 价值范围最小值
            value_max: 价值范围最大值
            risk_alpha: CVaR的置信水平
            risk_beta: 风险厌恶系数
            use_belief_state: 是否使用信念状态
            belief_dim: 信念状态维度
            use_belief_attention: 是否使用注意力机制处理信念
            belief_attention_heads: 注意力头数
            use_residual_belief: 是否使用残差连接
            use_gating_mechanism: 是否使用门控机制
            num_simulations: MCTS模拟次数
            c_puct: UCB公式中的探索常数
            dirichlet_alpha: Dirichlet噪声参数
            exploration_fraction: 探索比例
            pb_c_base: Progressive bias基础值
            pb_c_init: Progressive bias初始值
            replay_buffer_size: 回放缓冲区大小
            batch_size: 批次大小
            num_unroll_steps: 展开步数
            td_steps: TD学习步数
            value_loss_weight: 价值损失权重
            policy_loss_weight: 策略损失权重
            consistency_loss_weight: 一致性损失权重
            self_supervised_loss_weight: 自监督损失权重
            use_ewc: 是否使用EWC算法
            ewc_lambda: EWC正则化系数
            ewc_state_path: EWC状态保存路径
            learning_rate: 学习率
            weight_decay: 权重衰减
            lr_scheduler: 学习率调度器类型
            device: 设备类型
        """
        # 创建EfficientZero模型
        model = EfficientZeroModel(
            observation_shape=state_shape,
            action_shape=action_shape,
            hidden_dim=hidden_dim,
            state_dim=state_dim,
            use_resnet=use_resnet,
            projection_dim=projection_dim,
            prediction_dim=prediction_dim,
            value_prefix_length=value_prefix_length,
            use_distributional_value=use_distributional_value,
            value_support_size=value_support_size,
            value_min=value_min,
            value_max=value_max,
            risk_alpha=risk_alpha,
            risk_beta=risk_beta,
            use_belief_state=use_belief_state,
            belief_dim=belief_dim,
            use_belief_attention=use_belief_attention,
            belief_attention_heads=belief_attention_heads,
            use_residual_belief=use_residual_belief,
            use_gating_mechanism=use_gating_mechanism,
            device=device
        )

        # 调用父类初始化（MuZero不接受model参数）
        super().__init__(
            state_shape=state_shape,
            action_shape=action_shape,
            hidden_dim=hidden_dim,
            state_dim=state_dim,
            use_resnet=use_resnet,
            num_simulations=num_simulations,
            discount=0.997,  # 使用默认折扣因子
            dirichlet_alpha=dirichlet_alpha,
            exploration_fraction=exploration_fraction,
            pb_c_base=pb_c_base,
            pb_c_init=pb_c_init,
            replay_buffer_size=replay_buffer_size,
            batch_size=batch_size,
            num_unroll_steps=num_unroll_steps,
            td_steps=td_steps,
            value_loss_weight=value_loss_weight,
            policy_loss_weight=policy_loss_weight,
            consistency_loss_weight=consistency_loss_weight,
            learning_rate=learning_rate,
            weight_decay=weight_decay,
            lr_scheduler=lr_scheduler,
            device=device
        )

        # 替换父类创建的模型为EfficientZero模型
        self.model = model

        # 保存MCTS参数（父类没有保存这个）
        self.num_simulations = num_simulations

        # 保存EfficientZero特有的参数
        self.projection_dim = projection_dim
        self.prediction_dim = prediction_dim
        self.value_prefix_length = value_prefix_length
        self.use_distributional_value = use_distributional_value
        self.value_support_size = value_support_size
        self.value_min = value_min
        self.value_max = value_max
        self.risk_alpha = risk_alpha
        self.risk_beta = risk_beta
        self.use_belief_state = use_belief_state
        self.belief_dim = belief_dim

        # 保存损失权重参数
        self.self_supervised_loss_weight = self_supervised_loss_weight
        self.use_ewc = use_ewc
        self.ewc_lambda = ewc_lambda

        # 初始化组件
        self._initialize_components()

    def _initialize_components(self):
        """初始化EfficientZero特有的组件"""
        try:
            # 初始化对手分布切换器，提供默认的专家池
            from cardgame_ai.core.expert_pool import ExpertPolicyPool

            # 创建默认专家池
            expert_pool = ExpertPolicyPool()

            # 使用专家池初始化对手分布切换器
            self.opponent_switcher = OpponentDistributionSwitcher(
                expert_pool=expert_pool,
                switch_strategy='periodic',
                switch_interval=1000
            )
            logger.info("对手分布切换器初始化成功，使用默认专家池")
        except Exception as e:
            logger.warning(f"对手分布切换器初始化失败: {e}，使用空实现")
            self.opponent_switcher = None

        try:
            # 初始化关键时刻检测器
            self.key_moment_detector = KeyMomentDetector()
        except Exception as e:
            logger.warning(f"关键时刻检测器初始化失败: {e}，使用空实现")
            self.key_moment_detector = None

        try:
            # 初始化动态预算分配器
            self.dynamic_budget_allocator = DynamicBudgetAllocator()
        except Exception as e:
            logger.warning(f"动态预算分配器初始化失败: {e}，使用空实现")
            self.dynamic_budget_allocator = None

        # 初始化EWC（如果启用）
        if hasattr(self, 'use_ewc') and self.use_ewc:
            self.ewc = None  # 将在训练开始时初始化

        logger.info("EfficientZero组件初始化完成")

    def _ensure_tensor(self, data: Any, dtype: torch.dtype = torch.float32) -> torch.Tensor:
        """
        确保数据是张量类型，处理列表、NumPy数组和已有张量

        Args:
            data: 要转换的数据（可能是列表、NumPy数组或张量）
            dtype: 目标张量类型

        Returns:
            torch.Tensor: 转换后的张量
        """
        if isinstance(data, torch.Tensor):
            return data.to(dtype)
        elif isinstance(data, list):
            return self._safe_tensor_conversion(data, dtype)

        # NumPy 数组或可转为 NumPy 的对象
        if isinstance(data, np.ndarray):
            if data.dtype == np.object_:
                raise ValueError("无法从 object-dtype NumPy 数组安全创建张量，请先进行填充/转换")
            return torch.as_tensor(data, dtype=dtype)

        # 其余情况（单个标量、Tensor 子类等）
        return torch.as_tensor(data, dtype=dtype)

    def _safe_tensor_conversion(self, data_list: List[Any], dtype: torch.dtype = torch.float32) -> torch.Tensor:
        """
        安全地将数据列表转换为张量，处理可变长度数据和ragged tensor问题

        Args:
            data_list: 要转换的数据列表
            dtype: 目标张量类型

        Returns:
            torch.Tensor: 转换后的张量

        Raises:
            ValueError: 当数据无法安全转换为张量时
        """
        try:
            # 对所有类型都使用numpy stack确保形状一致，避免ragged tensor问题
            if dtype == torch.float32:
                stacked_array = np.stack(data_list).astype(np.float32)
            elif dtype == torch.long:
                stacked_array = np.stack(data_list).astype(np.int64)
            elif dtype == torch.bool:
                stacked_array = np.stack(data_list).astype(np.bool_)
            else:
                # 其他类型，先stack再转换
                stacked_array = np.stack(data_list)

            return torch.as_tensor(stacked_array, dtype=dtype)
        except ValueError as e:
            # 如果stack失败，说明存在形状不匹配的问题
            error_msg = str(e).lower()
            if ("could not broadcast" in error_msg or
                "inconsistent" in error_msg or
                "same shape" in error_msg or
                "shape" in error_msg):
                # 检查数据形状
                shapes = [np.array(item).shape for item in data_list]
                unique_shapes = list(set(shapes))
                raise ValueError(
                    f"检测到ragged tensor：数据形状不一致。"
                    f"发现的形状: {unique_shapes[:5]}{'...' if len(unique_shapes) > 5 else ''}。"
                    f"请确保所有观察数据具有相同的形状，或使用适当的填充。"
                ) from e
            else:
                raise ValueError(f"张量转换失败: {e}") from e

    def _extract_batch_data(self, experiences: List[Any]) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        从经验列表中提取批次数据的辅助函数

        Args:
            experiences: 经验数据列表

        Returns:
            Tuple[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor]:
            observations, actions, rewards, next_states, dones张量
        """
        observations = []
        actions = []
        rewards = []
        next_states = []
        dones = []

        for exp in experiences:
            # 获取状态的观察
            if hasattr(exp.state, 'get_observation'):
                obs = exp.state.get_observation()
            else:
                obs = exp.state
            observations.append(obs)

            # 获取动作索引
            if hasattr(exp.action, 'to_index'):
                action_idx = exp.action.to_index()
            elif hasattr(exp.action, 'action_type'):
                # 对于复合动作，使用动作类型作为索引
                action_idx = exp.action.action_type.value if hasattr(exp.action.action_type, 'value') else 0
            else:
                action_idx = 0  # 默认动作
            actions.append(action_idx)

            rewards.append(exp.reward)

            # 获取下一状态的观察
            if hasattr(exp.next_state, 'get_observation'):
                next_obs = exp.next_state.get_observation()
            else:
                next_obs = exp.next_state
            next_states.append(next_obs)

            dones.append(exp.done)

        # 安全地转换为张量
        observations_tensor = self._safe_tensor_conversion(observations, torch.float32)
        actions_tensor = self._safe_tensor_conversion(actions, torch.long)
        rewards_tensor = self._safe_tensor_conversion(rewards, torch.float32)
        next_states_tensor = self._safe_tensor_conversion(next_states, torch.float32)
        dones_tensor = self._safe_tensor_conversion(dones, torch.float32)

        return observations_tensor, actions_tensor, rewards_tensor, next_states_tensor, dones_tensor

    def train(self, batch: Dict[str, Any], human_feedback_batch: Optional[Dict[str, Any]] = None) -> Dict[str, float]:
        """
        训练模型（支持重要性加权和EfficientZero特有的一致性损失）

        Args:
            batch: 从回放缓冲区采样的批次数据
            human_feedback_batch: 人类反馈数据（可选）

        Returns:
            Dict[str, float]: 包含各种损失值的字典
        """
        # 初始化优化器
        self.optimizer.zero_grad()

        # 提取批次数据 - 修复：处理Batch对象、字典类型和tuple类型
        # 初始化重要性采样权重
        importance_weights = None

        if isinstance(batch, Batch):
            # 如果是Batch对象，提取经验数据
            experiences = batch.experiences

            # 使用辅助函数提取批次数据
            observations, actions, rewards, next_states, dones = self._extract_batch_data(experiences)

            target_policies = None
            target_values = None

        elif isinstance(batch, tuple):
            # 如果是tuple类型，可能是PrioritizedReplayBuffer返回的(batch, indices, weights)
            # 或者是HRL ReplayBuffer返回的(states, high_actions, low_actions, rewards, next_states, dones)
            if len(batch) == 3 and hasattr(batch[0], 'experiences'):
                # PrioritizedReplayBuffer: (Batch, indices, weights)
                batch_obj, indices, weights = batch
                experiences = batch_obj.experiences

                # 存储重要性采样权重
                importance_weights = self._ensure_tensor(weights, torch.float32)

                # 使用辅助函数提取批次数据
                observations, actions, rewards, next_states, dones = self._extract_batch_data(experiences)

            elif len(batch) == 6:
                # HRL ReplayBuffer: (states, high_actions, low_actions, rewards, next_states, dones)
                states, high_actions, low_actions, rewards, next_states, dones = batch

                # 安全地转换为张量（HRL数据可能是列表或NumPy数组）
                observations = self._ensure_tensor(states, torch.float32)
                actions = self._ensure_tensor(low_actions, torch.long)  # 使用低层动作
                rewards = self._ensure_tensor(rewards, torch.float32)
                next_states = self._ensure_tensor(next_states, torch.float32)
                dones = self._ensure_tensor(dones, torch.float32)

            else:
                raise ValueError(f"不支持的tuple格式，长度: {len(batch)}")

            target_policies = None
            target_values = None

        elif isinstance(batch, dict):
            # 如果是字典类型，安全地转换为张量
            if 'observations' in batch:
                observations = self._ensure_tensor(batch['observations'], torch.float32)
            elif 'states' in batch:
                observations = self._ensure_tensor(batch['states'], torch.float32)  # 使用states作为observations
            else:
                raise KeyError("批次数据中既没有'observations'也没有'states'键")

            actions = self._ensure_tensor(batch['actions'], torch.long)
            rewards = self._ensure_tensor(batch['rewards'], torch.float32)
            target_policies = batch.get('target_policies', None)
            if target_policies is not None and not isinstance(target_policies, torch.Tensor):
                target_policies = self._ensure_tensor(target_policies, torch.float32)

            target_values = batch.get('target_values', None)
            if target_values is not None and not isinstance(target_values, torch.Tensor):
                target_values = self._ensure_tensor(target_values, torch.float32)
            next_states = self._ensure_tensor(batch['next_states'], torch.float32) if batch.get('next_states') is not None else None
            dones = self._ensure_tensor(batch['dones'], torch.float32) if batch.get('dones') is not None else None
        else:
            # 如果batch不是支持的类型，抛出错误
            raise TypeError(f"不支持的batch类型: {type(batch)}")

        # 确保所有张量都在正确的设备上
        if hasattr(self, 'device'):
            observations = observations.to(self.device)
            actions = actions.to(self.device)
            rewards = rewards.to(self.device)
            if next_states is not None:
                next_states = next_states.to(self.device)
            if dones is not None:
                dones = dones.to(self.device)
            if importance_weights is not None:
                importance_weights = importance_weights.to(self.device)

        # 如果没有目标策略，使用MCTS生成
        if target_policies is None:
            target_policies = self._compute_target_policies(observations)

        # 如果没有目标价值，计算n步回报
        if target_values is None:
            if next_states is None:
                raise ValueError("next_states required to compute target values")
            target_values = self._compute_target_values(rewards, dones, next_states)

        # 获取初始隐藏状态
        current_states = self.model.represent(observations)

        # 初始化损失
        losses = {
            'total_loss': 0.0,
            'value_loss': 0.0,
            'policy_loss': 0.0,
            'reward_loss': 0.0,
            'consistency_loss': 0.0,
            'self_supervised_loss': 0.0
        }

        # 计算初始状态的损失
        policy_logits, values = self.model.predict(current_states)

        # 策略损失
        policy_loss = F.cross_entropy(
            policy_logits,
            torch.argmax(target_policies[:, 0], dim=1),
            reduction='mean'
        )
        losses['policy_loss'] += policy_loss * self.policy_loss_weight

        # 价值损失
        if self.use_distributional_value:
            value_loss = self._compute_distributional_value_loss(values, target_values[:, 0])
        else:
            value_loss = F.mse_loss(values.squeeze(), target_values[:, 0], reduction='mean')
        losses['value_loss'] += value_loss * self.value_loss_weight

        # 展开步骤计算损失
        reward_loss = 0.0
        consistency_loss = 0.0
        self_supervised_loss = 0.0

        batch_size = current_states.shape[0]

        # 展开步骤
        for step in range(self.num_unroll_steps):
            # 获取当前动作
            if step == 0:
                current_actions = actions
            else:
                # 使用目标策略中概率最高的动作
                current_actions = torch.argmax(target_policies[:, step - 1], dim=1)

            # 使用动态网络预测下一个状态和值前缀
            next_states_pred, value_prefix_pred = self.model.dynamics_network(current_states, current_actions)

            # 使用预测网络预测策略和价值
            policy_logits_pred, values_pred = self.model.prediction_network(next_states_pred)

            # 计算奖励损失 - 使用值前缀的第一个元素作为即时奖励预测
            if step == 0:
                # 第一步使用真实奖励，值前缀的第一个元素应该预测即时奖励
                immediate_reward_pred = value_prefix_pred[:, 0]  # 取值前缀的第一个元素
                reward_loss += F.mse_loss(immediate_reward_pred, rewards, reduction='mean')

            # 计算策略损失 (交叉熵)
            policy_loss += F.cross_entropy(
                policy_logits_pred,
                torch.argmax(target_policies[:, step], dim=1),
                reduction='mean'
            )

            # 计算价值损失
            if self.use_distributional_value:
                value_loss += self._compute_distributional_value_loss(values_pred, target_values[:, step])
            else:
                value_loss += F.mse_loss(values_pred.squeeze(), target_values[:, step], reduction='mean')

            # 计算一致性损失（如果有目标网络）
            if hasattr(self, 'target_model') and self.consistency_loss_weight > 0:
                with torch.no_grad():
                    # 对齐 MuZero 论文做法：目标网络对当前隐藏状态+动作的预测
                    target_next_states, _ = self.target_model.dynamics_network(current_states, current_actions)
                consistency_loss += F.mse_loss(next_states_pred, target_next_states, reduction='mean')

            # 计算自监督损失
            if hasattr(self.model, 'self_supervised_module') and self.self_supervised_loss_weight > 0:
                # 使用当前状态和下一个状态计算自监督损失
                if step < self.num_unroll_steps - 1:
                    self_supervised_loss += self.model.self_supervised_loss(current_states, next_states_pred)

            # 更新当前状态
            current_states = next_states_pred

        # 添加损失到总损失（修复：统一权重应用策略）
        losses['reward_loss'] = reward_loss
        losses['consistency_loss'] = consistency_loss * self.consistency_loss_weight
        losses['self_supervised_loss'] = self_supervised_loss  # 不在这里应用权重

        # 计算总损失（修复：在这里应用自监督损失权重）
        total_loss = (
            losses['value_loss'] +
            losses['policy_loss'] +
            losses['reward_loss'] +
            losses['consistency_loss'] +
            losses['self_supervised_loss'] * self.self_supervised_loss_weight
        )

        # 应用重要性采样权重（如果有的话）
        if importance_weights is not None:
            # 将重要性权重应用到总损失
            total_loss = (total_loss * importance_weights).mean()

        losses['total_loss'] = total_loss

        # 添加正则化损失（如EWC或L2正则化）
        if hasattr(self, 'regularization_loss') and self.regularization_loss.item() > 0:
            losses['regularization_loss'] = self.regularization_loss.item()
            losses['total_loss'] += self.regularization_loss
        else:
            losses['regularization_loss'] = 0.0

        # 计算EWC损失（防止灾难性遗忘）
        if self.use_ewc and self.ewc is not None:
            try:
                # 计算EWC惩罚项
                ewc_loss = self.ewc.penalty(self.model)
                losses['ewc_loss'] = ewc_loss.item()
                losses['total_loss'] += ewc_loss
            except Exception as e:
                logger.warning(f"计算EWC损失时出错: {e}")
                losses['ewc_loss'] = 0.0
        else:
            losses['ewc_loss'] = 0.0

        # 反向传播
        losses['total_loss'].backward()

        # 梯度裁剪
        if hasattr(self, 'gradient_clip_norm') and self.gradient_clip_norm > 0:
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.gradient_clip_norm)

        # 更新参数
        self.optimizer.step()

        # 更新学习率
        if hasattr(self, 'lr_scheduler') and self.lr_scheduler is not None:
            self.lr_scheduler.step()

        # 更新训练步数
        if hasattr(self, 'train_steps'):
            self.train_steps += 1

        # 转换损失为标量值
        scalar_losses = {}
        for key, value in losses.items():
            if isinstance(value, torch.Tensor):
                scalar_losses[key] = value.item()
            else:
                scalar_losses[key] = value

        return scalar_losses

    def act(self, state, belief_state=None, opponent_model_priors=None, dynamic_budget=None, explain=False, force_exploration=False):
        """
        使用MCTS选择动作

        Args:
            state: 当前游戏状态
            belief_state: 信念状态（可选）
            opponent_model_priors: 对手模型先验（可选）
            dynamic_budget: 动态预算分配（可选）
            explain: 是否返回解释信息
            force_exploration: 是否强制探索

        Returns:
            action: 选择的动作
            action_probs: 动作概率分布
            explain_info: 解释信息（如果explain=True）
        """
        # 修复：直接使用MCTS实例而不是调用不存在的父类方法
        try:
            # 获取观察数据
            if hasattr(state, 'get_observation'):
                observation = state.get_observation()
            else:
                observation = state

            # 转换为numpy数组（如果需要）
            if not isinstance(observation, np.ndarray):
                observation = np.array(observation, dtype=np.float32)

            # 使用动态预算分配器调整MCTS模拟次数
            original_simulations = None
            if dynamic_budget and hasattr(self, 'dynamic_budget_allocator') and self.dynamic_budget_allocator:
                try:
                    adjusted_simulations = self.dynamic_budget_allocator.allocate_budget(
                        state, self.num_simulations, dynamic_budget
                    )
                    # 临时保存原始模拟次数
                    original_simulations = self.mcts.num_simulations
                    self.mcts.num_simulations = adjusted_simulations
                except Exception as e:
                    logger.warning(f"动态预算分配失败: {e}")

            # 创建动作掩码（如果状态支持合法动作）
            action_mask = None
            if hasattr(state, 'get_legal_actions'):
                try:
                    legal_actions = state.get_legal_actions()
                    if legal_actions is not None and len(legal_actions) > 0:
                        # 创建动作掩码
                        max_action_id = max([self._get_action_id(a) for a in legal_actions] + [0])
                        action_mask = [0] * (max_action_id + 1)
                        for action in legal_actions:
                            action_id = self._get_action_id(action)
                            action_mask[action_id] = 1
                except Exception as e:
                    logger.warning(f"创建动作掩码失败: {e}")
                    action_mask = None

            # 设置MCTS参数
            temperature = 1.0 if force_exploration else 0.1

            # 直接调用MCTS运行
            mcts_result = self.mcts.run(
                observation,
                self.model,
                temperature=temperature,
                actions_mask=action_mask,
                belief_trackers=None,  # 暂时不使用信念追踪
                opponent_model_priors=opponent_model_priors,
                deepbelief_tracker=None,  # 暂时不使用深度信念追踪
                explain=explain,
                max_time_ms=None,
                use_act=None,
                dynamic_budget=dynamic_budget,
                force_exploration=force_exploration
            )

            # 处理MCTS返回值（根据explain参数决定返回值数量）
            if explain:
                visit_counts, action_probs, mcts_explanation = mcts_result
            else:
                visit_counts, action_probs = mcts_result

            # 恢复原始模拟次数
            if original_simulations is not None:
                self.mcts.num_simulations = original_simulations

            # 选择动作
            if len(action_probs) > 0:
                # 根据概率分布选择动作
                actions = list(action_probs.keys())
                probs = list(action_probs.values())

                if force_exploration or np.random.random() < 0.1:  # 10%的探索概率
                    action_idx = np.random.choice(actions, p=probs)
                else:
                    # 选择概率最高的动作
                    action_idx = actions[np.argmax(probs)]
            else:
                action_idx = 0
                action_probs = {0: 1.0}

            if explain:
                explain_info = {
                    'visit_counts': visit_counts,
                    'action_probs': action_probs,
                    'num_simulations': self.mcts.num_simulations,
                    'legal_actions_count': len(action_mask) if action_mask else 0,
                    'fallback_used': False,
                    'temperature': temperature
                }
                # 如果MCTS返回了解释信息，合并到explain_info中
                if 'mcts_explanation' in locals():
                    explain_info.update(mcts_explanation)
                return action_idx, action_probs, explain_info
            else:
                return action_idx, action_probs

        except Exception as e:
            logger.error(f"MCTS执行失败: {e}")
            logger.error(f"错误详情: {type(e).__name__}: {str(e)}")

            # 恢复原始模拟次数（如果有修改）
            if original_simulations is not None:
                self.mcts.num_simulations = original_simulations

            # 不使用简化策略回退，直接抛出异常
            # 用户明确表示不允许使用简化策略
            raise RuntimeError(f"MCTS执行失败，无法继续训练: {e}") from e

    def _get_action_id(self, action):
        """
        获取动作的ID，用于创建动作掩码

        修复：确保不同类型的动作有唯一的ID映射，避免所有动作都映射到ID=0

        Args:
            action: 动作对象

        Returns:
            int: 动作ID
        """
        # 导入必要的类型（避免循环导入）
        from cardgame_ai.games.doudizhu.action import BidAction, GrabAction
        from cardgame_ai.games.doudizhu.card_group import CardGroup

        # 1. 优先使用to_index方法（CardGroup现在有这个方法）
        if hasattr(action, 'to_index'):
            action_id = action.to_index()
            logger.debug(f"🎯 动作映射: {action} -> ID={action_id} (via to_index)")
            return action_id

        # 2. 处理BidAction枚举
        elif isinstance(action, BidAction):
            # BidAction: PASS=0, BID_1=1, BID_2=2, BID_3=3
            # 使用基础偏移2000避免与CardGroup冲突
            action_id = 2000 + action.value
            logger.debug(f"🎯 动作映射: {action} -> ID={action_id} (BidAction)")
            return action_id

        # 3. 处理GrabAction枚举
        elif isinstance(action, GrabAction):
            # GrabAction: PASS=0, GRAB=1
            # 使用基础偏移2100避免冲突
            action_id = 2100 + action.value
            logger.debug(f"🎯 动作映射: {action} -> ID={action_id} (GrabAction)")
            return action_id

        # 4. 处理CardGroup类型（修复原有的card_type逻辑）
        elif hasattr(action, 'card_type'):
            # 这是CardGroup但没有to_index方法的fallback
            if hasattr(action.card_type, 'value'):
                action_id = action.card_type.value
                logger.warning(f"⚠️ 动作映射fallback: {action} -> ID={action_id} (card_type.value)")
                return action_id

        # 5. 处理整数类型
        elif isinstance(action, int):
            logger.debug(f"🎯 动作映射: {action} -> ID={action} (int)")
            return action

        # 6. 最后的fallback（应该很少触发）
        else:
            logger.error(f"❌ 无法映射动作: {action} (type: {type(action)}) -> 使用默认ID=0")
            return 0
