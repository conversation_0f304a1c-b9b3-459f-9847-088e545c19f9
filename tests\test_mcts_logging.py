"""
MCTS日志增强功能测试

测试MCTS日志系统的各个组件，包括：
- 日志配置管理
- 格式化器功能
- 性能监控
- 主日志器功能
- 与MCTS算法的集成
"""

import unittest
import tempfile
import json
import time
import os
from pathlib import Path
from unittest.mock import Mock, patch

# 导入被测试的模块
try:
    from cardgame_ai.algorithms.mcts_logging import (
        MCTSLogger, LogConfig, JSONFormatter, TextFormatter,
        PerformanceMonitor, generate_session_id
    )
    MCTS_LOGGING_AVAILABLE = True
except ImportError as e:
    MCTS_LOGGING_AVAILABLE = False
    print(f"MCTS日志模块导入失败: {e}")


@unittest.skipUnless(MCTS_LOGGING_AVAILABLE, "MCTS日志模块不可用")
class TestLogConfig(unittest.TestCase):
    """测试日志配置类"""
    
    def test_default_config(self):
        """测试默认配置"""
        config = LogConfig()
        
        self.assertTrue(config.enabled)
        self.assertEqual(config.level, "INFO")
        self.assertEqual(config.output_format, "json")
        self.assertTrue(config.enable_ucb_logging)
        self.assertTrue(config.enable_expansion_logging)
        self.assertTrue(config.enable_path_logging)
        self.assertTrue(config.enable_performance_logging)
    
    def test_config_validation(self):
        """测试配置验证"""
        # 有效配置
        valid_config = LogConfig(level="DEBUG", output_format="json")
        self.assertTrue(valid_config.validate())
        
        # 无效日志级别
        invalid_config = LogConfig(level="INVALID")
        self.assertFalse(invalid_config.validate())
        
        # 无效输出格式
        invalid_config = LogConfig(output_format="invalid")
        self.assertFalse(invalid_config.validate())
    
    def test_config_from_env(self):
        """测试从环境变量加载配置"""
        with patch.dict(os.environ, {
            'MCTS_LOG_ENABLED': 'true',
            'MCTS_LOG_LEVEL': 'DEBUG',
            'MCTS_LOG_FORMAT': 'text'
        }):
            config = LogConfig.from_env()
            self.assertTrue(config.enabled)
            self.assertEqual(config.level, "DEBUG")
            self.assertEqual(config.output_format, "text")
    
    def test_config_to_dict(self):
        """测试配置转换为字典"""
        config = LogConfig(level="DEBUG", enable_ucb_logging=False)
        config_dict = config.to_dict()
        
        self.assertIsInstance(config_dict, dict)
        self.assertEqual(config_dict['level'], "DEBUG")
        self.assertFalse(config_dict['enable_ucb_logging'])


@unittest.skipUnless(MCTS_LOGGING_AVAILABLE, "MCTS日志模块不可用")
class TestFormatters(unittest.TestCase):
    """测试格式化器"""
    
    def setUp(self):
        self.json_formatter = JSONFormatter()
        self.text_formatter = TextFormatter()
        self.timestamp = time.time()
        self.session_id = "test_session"
        
    def test_json_formatter_ucb_log(self):
        """测试JSON格式化器的UCB日志"""
        data = {
            'parent_visits': 10,
            'children_scores': [
                {'action': 0, 'ucb_score': 0.8, 'visits': 5},
                {'action': 1, 'ucb_score': 0.6, 'visits': 3}
            ],
            'selected_action': 0
        }
        
        result = self.json_formatter.format_ucb_log(
            self.timestamp, self.session_id, data
        )
        
        # 验证是否为有效JSON
        parsed = json.loads(result)
        self.assertEqual(parsed['type'], 'ucb_calculation')
        self.assertEqual(parsed['session_id'], self.session_id)
        self.assertEqual(parsed['data']['selected_action'], 0)
    
    def test_text_formatter_expansion_log(self):
        """测试文本格式化器的扩展日志"""
        data = {
            'num_children_added': 5,
            'expansion_time': 0.001,
            'policy_output': {'entropy': 2.3}
        }
        
        result = self.text_formatter.format_expansion_log(
            self.timestamp, self.session_id, data
        )
        
        self.assertIn('NODE_EXPANSION', result)
        self.assertIn('num_children_added: 5', result)
        self.assertIn('expansion_time: 0.001', result)


@unittest.skipUnless(MCTS_LOGGING_AVAILABLE, "MCTS日志模块不可用")
class TestPerformanceMonitor(unittest.TestCase):
    """测试性能监控器"""
    
    def setUp(self):
        self.monitor = PerformanceMonitor(enable_memory_monitoring=False)
    
    def test_search_timing(self):
        """测试搜索计时"""
        search_id = "test_search"
        
        self.monitor.start_search_timing(search_id)
        time.sleep(0.01)  # 模拟搜索时间
        elapsed = self.monitor.end_search_timing(search_id, num_simulations=100)
        
        self.assertGreater(elapsed, 0.005)  # 至少5ms
        self.assertEqual(self.monitor.metrics.total_simulations, 100)
    
    def test_ucb_timing(self):
        """测试UCB计算计时"""
        ucb_id = "test_ucb"
        
        self.monitor.start_ucb_timing(ucb_id)
        time.sleep(0.001)  # 模拟UCB计算时间
        elapsed = self.monitor.end_ucb_timing(ucb_id)
        
        self.assertGreater(elapsed, 0.0005)  # 至少0.5ms
        self.assertEqual(self.monitor.metrics.total_ucb_calculations, 1)
    
    def test_expansion_timing(self):
        """测试节点扩展计时"""
        expansion_id = "test_expansion"
        
        self.monitor.start_expansion_timing(expansion_id)
        time.sleep(0.001)  # 模拟扩展时间
        elapsed = self.monitor.end_expansion_timing(expansion_id, num_children=5)
        
        self.assertGreater(elapsed, 0.0005)  # 至少0.5ms
        self.assertEqual(self.monitor.metrics.total_expansions, 1)
        self.assertEqual(self.monitor.metrics.total_nodes_created, 5)
    
    def test_performance_report(self):
        """测试性能报告生成"""
        # 模拟一些操作
        self.monitor.start_search_timing("search1")
        time.sleep(0.01)
        self.monitor.end_search_timing("search1", 50)
        
        report = self.monitor.generate_performance_report()
        
        self.assertIn('summary', report)
        self.assertIn('detailed_metrics', report)
        self.assertIn('efficiency_ratios', report)
        self.assertEqual(report['detailed_metrics']['total_simulations'], 50)


@unittest.skipUnless(MCTS_LOGGING_AVAILABLE, "MCTS日志模块不可用")
class TestMCTSLogger(unittest.TestCase):
    """测试主MCTS日志器"""
    
    def setUp(self):
        # 创建临时目录用于日志文件
        self.temp_dir = tempfile.mkdtemp()
        self.log_file = os.path.join(self.temp_dir, "test_mcts.log")
        
        # 创建测试配置
        self.config = LogConfig(
            enabled=True,
            level="DEBUG",
            output_format="json",
            log_to_file=True,
            log_to_console=False,
            log_file_path=self.log_file,
            async_logging=False  # 同步日志便于测试
        )
        
        self.logger = MCTSLogger(config=self.config)
    
    def tearDown(self):
        self.logger.close()
        # 清理临时文件
        if os.path.exists(self.log_file):
            os.remove(self.log_file)
        os.rmdir(self.temp_dir)
    
    def test_logger_initialization(self):
        """测试日志器初始化"""
        self.assertIsNotNone(self.logger.session_id)
        self.assertEqual(self.logger.config.level, "DEBUG")
        self.assertIsNotNone(self.logger.performance_monitor)
    
    def test_ucb_calculation_logging(self):
        """测试UCB计算日志"""
        # 创建模拟节点
        mock_node = Mock()
        mock_node.visit_count = 10
        mock_node.player_id_to_act = 0
        
        children_scores = [
            {'action': 0, 'ucb_score': 0.8, 'visits': 5},
            {'action': 1, 'ucb_score': 0.6, 'visits': 3}
        ]
        
        self.logger.log_ucb_calculation(
            parent_node=mock_node,
            children_scores=children_scores,
            selected_action=0,
            game_context={'test': 'context'}
        )
        
        # 验证统计计数
        self.assertEqual(self.logger.stats['ucb_logs'], 1)
    
    def test_node_expansion_logging(self):
        """测试节点扩展日志"""
        mock_node = Mock()
        mock_node.visit_count = 5
        
        policy_output = {
            'entropy': 2.3,
            'max_prob': 0.4
        }
        
        self.logger.log_node_expansion(
            node=mock_node,
            policy_output=policy_output,
            expansion_time=0.001,
            num_children=5
        )
        
        # 验证统计计数
        self.assertEqual(self.logger.stats['expansion_logs'], 1)
    
    def test_search_path_logging(self):
        """测试搜索路径日志"""
        # 创建模拟路径
        mock_nodes = []
        for i in range(3):
            node = Mock()
            node.visit_count = i + 1
            node.value = lambda: 0.5 + i * 0.1
            mock_nodes.append(node)
        
        self.logger.log_search_path(
            path=mock_nodes,
            path_value=0.7,
            depth=3
        )
        
        # 验证统计计数
        self.assertEqual(self.logger.stats['path_logs'], 1)
    
    def test_performance_stats_logging(self):
        """测试性能统计日志"""
        additional_stats = {
            'custom_metric': 42,
            'test_info': 'performance_test'
        }
        
        self.logger.log_performance_stats(additional_stats)
        
        # 验证统计计数
        self.assertEqual(self.logger.stats['performance_logs'], 1)
    
    def test_session_stats(self):
        """测试会话统计"""
        stats = self.logger.get_session_stats()
        
        self.assertIn('session_id', stats)
        self.assertIn('config', stats)
        self.assertIn('logging_stats', stats)
        self.assertIn('performance_metrics', stats)
        
        self.assertEqual(stats['session_id'], self.logger.session_id)
    
    def test_timing_methods(self):
        """测试计时方法"""
        # 测试搜索计时
        search_id = self.logger.start_search_timing()
        self.assertIsNotNone(search_id)
        
        time.sleep(0.01)
        elapsed = self.logger.end_search_timing(search_id, 100)
        self.assertGreater(elapsed, 0.005)
        
        # 测试UCB计时
        ucb_id = self.logger.start_ucb_timing()
        self.assertIsNotNone(ucb_id)
        
        # 测试扩展计时
        expansion_id = self.logger.start_expansion_timing()
        self.assertIsNotNone(expansion_id)


class TestUtilityFunctions(unittest.TestCase):
    """测试工具函数"""
    
    @unittest.skipUnless(MCTS_LOGGING_AVAILABLE, "MCTS日志模块不可用")
    def test_session_id_generation(self):
        """测试会话ID生成"""
        session_id1 = generate_session_id()
        session_id2 = generate_session_id()
        
        # 验证格式
        self.assertTrue(session_id1.startswith('mcts_'))
        self.assertTrue(session_id2.startswith('mcts_'))
        
        # 验证唯一性
        self.assertNotEqual(session_id1, session_id2)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
