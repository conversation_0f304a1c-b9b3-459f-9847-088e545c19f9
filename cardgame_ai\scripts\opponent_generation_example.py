#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
对手生成示例脚本

展示如何使用对抗式对手生成器生成多样化且具有挑战性的对手策略。
"""

import os
import sys
import argparse
import logging
import numpy as np
import torch
import random
from typing import Dict, List, Any, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from cardgame_ai.algorithms.opponent_generation.gan_policy_generator import GANPolicyGenerator
from cardgame_ai.algorithms.opponent_generation.self_play_evolution import SelfPlayEvolution, EvolutionaryPolicyGenerator
from cardgame_ai.algorithms.opponent_generation.human_style_generator import HumanStyleGenerator, StyleTransferNetwork
from cardgame_ai.utils.opponent_sampler import OpponentSampler
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.games.doudizhu.state import DouDizhuState
from cardgame_ai.algorithms.random_agent import RandomAgent

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='对手生成示例')
    
    parser.add_argument('--mode', type=str, default='gan',
                        choices=['gan', 'self_play', 'human_style', 'sampler'],
                        help='对手生成模式')
    parser.add_argument('--checkpoint_dir', type=str, default='models/checkpoints',
                        help='检查点目录，用于自对弈历史采样')
    parser.add_argument('--gan_model_path', type=str, default='models/gan/gan_model.pt',
                        help='GAN模型路径')
    parser.add_argument('--human_style_model_path', type=str, default='models/human_style/human_style_model.pt',
                        help='人类风格模型路径')
    parser.add_argument('--policy_dim', type=int, default=309,
                        help='策略维度')
    parser.add_argument('--latent_dim', type=int, default=64,
                        help='潜在空间维度')
    parser.add_argument('--num_opponents', type=int, default=5,
                        help='生成的对手数量')
    parser.add_argument('--seed', type=int, default=42,
                        help='随机种子')
    
    return parser.parse_args()


def test_gan_generator(args):
    """测试GAN策略生成器"""
    logger.info("=== 测试GAN策略生成器 ===")
    
    # 创建GAN策略生成器
    gan_generator = GANPolicyGenerator(
        policy_dim=args.policy_dim,
        latent_dim=args.latent_dim
    )
    
    # 如果模型路径存在，加载模型
    if os.path.exists(args.gan_model_path):
        gan_generator.load(args.gan_model_path)
        logger.info(f"已加载GAN模型: {args.gan_model_path}")
    else:
        logger.warning(f"GAN模型路径不存在: {args.gan_model_path}")
        
        # 创建一个简单的训练数据集
        logger.info("创建随机训练数据...")
        num_samples = 1000
        random_policies = torch.rand(num_samples, args.policy_dim)
        
        # 归一化策略
        random_policies = F.softmax(random_policies, dim=1)
        
        # 创建数据集
        class SimpleDataset(torch.utils.data.Dataset):
            def __init__(self, data):
                self.data = data
            
            def __len__(self):
                return len(self.data)
            
            def __getitem__(self, idx):
                return self.data[idx]
        
        dataset = SimpleDataset(random_policies)
        
        # 训练GAN
        logger.info("训练GAN...")
        gan_generator.train(
            real_policies_dataset=dataset,
            num_epochs=5,
            batch_size=32,
            save_interval=5,
            save_path=os.path.dirname(args.gan_model_path),
            verbose=True
        )
        
        # 保存模型
        os.makedirs(os.path.dirname(args.gan_model_path), exist_ok=True)
        gan_generator.save(args.gan_model_path)
        logger.info(f"已保存GAN模型: {args.gan_model_path}")
    
    # 生成对手策略
    logger.info(f"生成 {args.num_opponents} 个对手策略...")
    for i in range(args.num_opponents):
        # 随机选择风格
        style = random.choice(['random', 'diverse', 'extreme'])
        
        # 生成策略
        policy = gan_generator.generate_opponent_policy(style=style)
        
        # 打印策略信息
        logger.info(f"对手 {i+1}/{args.num_opponents}, 风格: {style}")
        logger.info(f"策略形状: {policy.shape}")
        logger.info(f"策略前5个值: {policy[:5]}")
        logger.info(f"策略和: {policy.sum()}")
        logger.info("")


def test_self_play_evolution(args):
    """测试自对弈演化"""
    logger.info("=== 测试自对弈演化 ===")
    
    # 检查检查点目录是否存在
    if not os.path.exists(args.checkpoint_dir):
        logger.warning(f"检查点目录不存在: {args.checkpoint_dir}")
        
        # 创建检查点目录
        os.makedirs(args.checkpoint_dir, exist_ok=True)
        
        # 创建一些模拟检查点
        logger.info("创建模拟检查点...")
        for i in range(5):
            # 创建随机模型
            model = torch.nn.Sequential(
                torch.nn.Linear(100, 100),
                torch.nn.ReLU(),
                torch.nn.Linear(100, args.policy_dim),
                torch.nn.Softmax(dim=1)
            )
            
            # 保存模型
            checkpoint_path = os.path.join(args.checkpoint_dir, f"model_{i}.pt")
            torch.save(model.state_dict(), checkpoint_path)
            logger.info(f"已创建模拟检查点: {checkpoint_path}")
    
    # 创建自对弈演化
    self_play_evolution = SelfPlayEvolution(
        checkpoint_dir=args.checkpoint_dir,
        sampling_strategy='latest_k',
        k=3
    )
    
    # 采样对手策略
    logger.info(f"采样 {args.num_opponents} 个对手策略...")
    for i in range(args.num_opponents):
        # 采样策略
        policy = self_play_evolution.sample_opponent_policy()
        
        # 打印策略信息
        logger.info(f"对手 {i+1}/{args.num_opponents}")
        logger.info(f"策略类型: {type(policy)}")
        logger.info("")
    
    # 获取统计信息
    stats = self_play_evolution.get_stats()
    logger.info(f"检查点数量: {stats['num_checkpoints']}")
    logger.info(f"采样策略: {stats['sampling_strategy']}")


def test_human_style_generator(args):
    """测试人类风格生成器"""
    logger.info("=== 测试人类风格生成器 ===")
    
    # 创建一个简单的AI策略
    def random_policy(state):
        return np.random.random(args.policy_dim)
    
    # 创建人类风格生成器
    human_style_generator = HumanStyleGenerator(
        ai_policy=random_policy,
        human_data_path="",  # 不需要加载人类数据
        policy_dim=args.policy_dim
    )
    
    # 如果模型路径存在，加载模型
    if os.path.exists(args.human_style_model_path):
        human_style_generator.load(args.human_style_model_path)
        logger.info(f"已加载人类风格模型: {args.human_style_model_path}")
    else:
        logger.warning(f"人类风格模型路径不存在: {args.human_style_model_path}")
        
        # 创建风格迁移网络
        style_transfer = StyleTransferNetwork(
            input_dim=args.policy_dim,
            output_dim=args.policy_dim,
            style_dim=32,
            num_styles=3
        )
        
        # 保存模型
        os.makedirs(os.path.dirname(args.human_style_model_path), exist_ok=True)
        torch.save({
            'style_transfer_state_dict': style_transfer.state_dict(),
            'optimizer_state_dict': torch.optim.Adam(style_transfer.parameters()).state_dict(),
            'stats': {
                "training_loss": [],
                "validation_loss": [],
                "style_usage": {i: 0 for i in range(3)}
            },
            'style_descriptions': {
                0: "保守型",
                1: "激进型",
                2: "随机型"
            }
        }, args.human_style_model_path)
        
        # 重新加载模型
        human_style_generator.load(args.human_style_model_path)
        logger.info(f"已创建并保存人类风格模型: {args.human_style_model_path}")
    
    # 生成对手策略
    logger.info(f"生成 {args.num_opponents} 个对手策略...")
    for i in range(args.num_opponents):
        # 随机选择风格
        style = random.choice(['random', 'conservative', 'aggressive'])
        
        # 创建随机状态
        state = np.random.random(100)
        
        # 生成策略
        policy = human_style_generator.generate_opponent_policy(state, style=style)
        
        # 打印策略信息
        logger.info(f"对手 {i+1}/{args.num_opponents}, 风格: {style}")
        logger.info(f"策略形状: {policy.shape}")
        logger.info(f"策略前5个值: {policy[:5]}")
        logger.info(f"策略和: {policy.sum()}")
        logger.info("")


def test_opponent_sampler(args):
    """测试对手采样器"""
    logger.info("=== 测试对手采样器 ===")
    
    # 创建对手采样器
    opponent_sampler = OpponentSampler(
        checkpoint_dir=args.checkpoint_dir,
        gan_model_path=args.gan_model_path if os.path.exists(args.gan_model_path) else None,
        human_style_model_path=args.human_style_model_path if os.path.exists(args.human_style_model_path) else None,
        sampling_strategy='mixed',
        model_config={'policy_dim': args.policy_dim}
    )
    
    # 采样对手策略
    logger.info(f"采样 {args.num_opponents} 个对手策略...")
    for i in range(args.num_opponents):
        # 创建随机状态
        state = np.random.random(100)
        
        # 采样策略
        policy = opponent_sampler.sample_opponent_policy(state)
        
        # 打印策略信息
        logger.info(f"对手 {i+1}/{args.num_opponents}")
        logger.info(f"策略类型: {type(policy)}")
        if isinstance(policy, torch.Tensor):
            logger.info(f"策略形状: {policy.shape}")
            logger.info(f"策略前5个值: {policy[:5]}")
            logger.info(f"策略和: {policy.sum()}")
        logger.info("")
    
    # 获取多样化对手
    logger.info(f"获取 {args.num_opponents} 个多样化对手...")
    diverse_opponents = opponent_sampler.get_diverse_opponents(args.num_opponents)
    logger.info(f"多样化对手数量: {len(diverse_opponents)}")
    
    # 获取统计信息
    stats = opponent_sampler.get_stats()
    logger.info(f"可用策略: {stats['available_strategies']}")
    logger.info(f"采样策略: {stats['sampling_strategy']}")
    logger.info(f"策略使用情况: {stats['strategy_usage']}")


def main():
    """主函数"""
    args = parse_args()
    
    # 设置随机种子
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    random.seed(args.seed)
    
    # 根据模式测试不同的对手生成方法
    if args.mode == 'gan':
        test_gan_generator(args)
    elif args.mode == 'self_play':
        test_self_play_evolution(args)
    elif args.mode == 'human_style':
        test_human_style_generator(args)
    elif args.mode == 'sampler':
        test_opponent_sampler(args)
    else:
        logger.error(f"不支持的模式: {args.mode}")
    
    return 0


if __name__ == "__main__":
    main()
