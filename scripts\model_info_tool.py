#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
模型信息查看工具

快速查看PyTorch模型文件的参数量和其他信息。

使用方法:
    # 查看单个模型
    python scripts/model_info_tool.py path/to/model.pt
    
    # 查看目录下所有模型
    python scripts/model_info_tool.py models/ --recursive
    
    # 详细信息
    python scripts/model_info_tool.py path/to/model.pt --detailed
    
    # 导出信息到JSON
    python scripts/model_info_tool.py models/ --output models_info.json
"""

import sys
import os
import argparse
import json
import torch
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

from cardgame_ai.utils.model_saver import ModelSaver


def find_model_files(path: str, recursive: bool = False) -> List[Path]:
    """
    查找模型文件
    
    Args:
        path: 文件或目录路径
        recursive: 是否递归搜索子目录
        
    Returns:
        模型文件路径列表
    """
    path_obj = Path(path)
    model_files = []
    
    if path_obj.is_file():
        if path_obj.suffix in ['.pt', '.pth', '.ckpt']:
            model_files.append(path_obj)
    elif path_obj.is_dir():
        if recursive:
            # 递归搜索
            for pattern in ['**/*.pt', '**/*.pth', '**/*.ckpt']:
                model_files.extend(path_obj.glob(pattern))
        else:
            # 只搜索当前目录
            for pattern in ['*.pt', '*.pth', '*.ckpt']:
                model_files.extend(path_obj.glob(pattern))
    
    return sorted(model_files)


def get_file_size(file_path: Path) -> str:
    """获取文件大小的可读格式"""
    size_bytes = file_path.stat().st_size
    
    if size_bytes >= 1024**3:
        return f"{size_bytes / (1024**3):.1f} GB"
    elif size_bytes >= 1024**2:
        return f"{size_bytes / (1024**2):.1f} MB"
    elif size_bytes >= 1024:
        return f"{size_bytes / 1024:.1f} KB"
    else:
        return f"{size_bytes} B"


def analyze_model_file(file_path: Path, detailed: bool = False) -> Dict[str, Any]:
    """
    分析单个模型文件
    
    Args:
        file_path: 模型文件路径
        detailed: 是否返回详细信息
        
    Returns:
        模型信息字典
    """
    try:
        # 基本文件信息
        file_info = {
            'file_path': str(file_path),
            'file_name': file_path.name,
            'file_size': get_file_size(file_path),
            'file_size_bytes': file_path.stat().st_size
        }
        
        # 尝试加载模型信息
        try:
            model_info = ModelSaver.get_model_info(str(file_path))
            
            if 'error' not in model_info:
                # 成功获取模型信息
                file_info.update({
                    'parameter_stats': model_info.get('parameter_stats', {}),
                    'formatted_params': model_info.get('formatted_params', 'Unknown'),
                    'save_timestamp': model_info.get('save_timestamp', 'Unknown'),
                    'epoch': model_info.get('epoch'),
                    'performance': model_info.get('performance'),
                    'tag': model_info.get('tag'),
                    'model_type': 'enhanced_format'  # 使用增强格式保存的模型
                })
            else:
                # 尝试直接加载PyTorch模型
                model_data = torch.load(str(file_path), map_location='cpu')
                
                if isinstance(model_data, dict):
                    # 检查是否包含state_dict
                    if 'model_state_dict' in model_data:
                        state_dict = model_data['model_state_dict']
                        file_info['model_type'] = 'checkpoint_format'
                    elif any(key.endswith('.weight') or key.endswith('.bias') for key in model_data.keys()):
                        state_dict = model_data
                        file_info['model_type'] = 'state_dict_format'
                    else:
                        state_dict = None
                        file_info['model_type'] = 'unknown_dict_format'
                    
                    # 计算参数量
                    if state_dict:
                        total_params = sum(tensor.numel() for tensor in state_dict.values())
                        file_info['parameter_stats'] = {'total_parameters': total_params}
                        file_info['formatted_params'] = ModelSaver.format_parameter_count(total_params)
                    
                    # 提取其他信息
                    file_info.update({
                        'epoch': model_data.get('epoch'),
                        'performance': model_data.get('performance'),
                        'save_timestamp': model_data.get('timestamp') or model_data.get('save_timestamp')
                    })
                else:
                    file_info['model_type'] = 'raw_model_format'
                    file_info['error'] = 'Cannot analyze raw model format'
        
        except Exception as e:
            file_info['error'] = str(e)
            file_info['model_type'] = 'load_error'
        
        # 详细信息
        if detailed and 'parameter_stats' in file_info:
            param_stats = file_info['parameter_stats']
            if isinstance(param_stats, dict) and 'layer_parameters' in param_stats:
                # 添加层级参数统计
                layer_params = param_stats['layer_parameters']
                file_info['layer_count'] = len(layer_params)
                file_info['largest_layer'] = max(layer_params.items(), key=lambda x: x[1]) if layer_params else None
        
        return file_info
        
    except Exception as e:
        return {
            'file_path': str(file_path),
            'file_name': file_path.name,
            'error': f"Failed to analyze file: {str(e)}"
        }


def print_model_info(model_info: Dict[str, Any], detailed: bool = False):
    """打印模型信息"""
    print(f"\n📄 {model_info['file_name']}")
    print("-" * 50)
    
    # 基本信息
    print(f"📁 路径: {model_info['file_path']}")
    print(f"💾 大小: {model_info['file_size']}")
    
    if 'error' in model_info:
        print(f"❌ 错误: {model_info['error']}")
        return
    
    # 模型类型
    model_type_names = {
        'enhanced_format': '增强格式 (ModelSaver)',
        'checkpoint_format': '检查点格式',
        'state_dict_format': '状态字典格式',
        'unknown_dict_format': '未知字典格式',
        'raw_model_format': '原始模型格式',
        'load_error': '加载错误'
    }
    model_type = model_info.get('model_type', 'unknown')
    print(f"🏷️ 类型: {model_type_names.get(model_type, model_type)}")
    
    # 参数量
    if 'formatted_params' in model_info:
        print(f"📊 参数量: {model_info['formatted_params']}")
        
        if detailed and 'parameter_stats' in model_info:
            param_stats = model_info['parameter_stats']
            if isinstance(param_stats, dict):
                total = param_stats.get('total_parameters', 0)
                trainable = param_stats.get('trainable_parameters', 0)
                non_trainable = param_stats.get('non_trainable_parameters', 0)
                
                print(f"   总参数: {total:,}")
                if trainable > 0:
                    print(f"   可训练: {trainable:,}")
                if non_trainable > 0:
                    print(f"   不可训练: {non_trainable:,}")
    
    # 训练信息
    if model_info.get('epoch'):
        print(f"🔄 轮次: {model_info['epoch']}")
    
    if model_info.get('performance'):
        print(f"📈 性能: {model_info['performance']:.4f}")
    
    if model_info.get('tag'):
        print(f"🏷️ 标签: {model_info['tag']}")
    
    if model_info.get('save_timestamp'):
        print(f"⏰ 保存时间: {model_info['save_timestamp']}")
    
    # 详细信息
    if detailed:
        if model_info.get('layer_count'):
            print(f"🔧 层数: {model_info['layer_count']}")
        
        if model_info.get('largest_layer'):
            layer_name, layer_params = model_info['largest_layer']
            formatted_layer_params = ModelSaver.format_parameter_count(layer_params)
            print(f"🎯 最大层: {layer_name} ({formatted_layer_params})")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="模型信息查看工具")
    parser.add_argument('path', help='模型文件或目录路径')
    parser.add_argument('-r', '--recursive', action='store_true',
                       help='递归搜索子目录')
    parser.add_argument('-d', '--detailed', action='store_true',
                       help='显示详细信息')
    parser.add_argument('-o', '--output', type=str,
                       help='输出JSON文件路径')
    parser.add_argument('--sort-by', type=str, default='name',
                       choices=['name', 'size', 'params'],
                       help='排序方式')
    
    args = parser.parse_args()
    
    # 查找模型文件
    model_files = find_model_files(args.path, args.recursive)
    
    if not model_files:
        print(f"❌ 在 {args.path} 中未找到模型文件")
        return 1
    
    print(f"🔍 找到 {len(model_files)} 个模型文件")
    
    # 分析所有模型
    all_model_info = []
    for file_path in model_files:
        model_info = analyze_model_file(file_path, args.detailed)
        all_model_info.append(model_info)
    
    # 排序
    if args.sort_by == 'size':
        all_model_info.sort(key=lambda x: x.get('file_size_bytes', 0), reverse=True)
    elif args.sort_by == 'params':
        all_model_info.sort(key=lambda x: x.get('parameter_stats', {}).get('total_parameters', 0), reverse=True)
    else:  # name
        all_model_info.sort(key=lambda x: x['file_name'])
    
    # 显示信息
    for model_info in all_model_info:
        print_model_info(model_info, args.detailed)
    
    # 统计摘要
    total_files = len(all_model_info)
    successful_analyses = len([info for info in all_model_info if 'error' not in info])
    total_params = sum(
        info.get('parameter_stats', {}).get('total_parameters', 0)
        for info in all_model_info
        if 'parameter_stats' in info
    )
    
    print(f"\n📊 统计摘要:")
    print(f"   总文件数: {total_files}")
    print(f"   成功分析: {successful_analyses}")
    if total_params > 0:
        print(f"   总参数量: {ModelSaver.format_parameter_count(total_params)} ({total_params:,})")
    
    # 输出JSON
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(all_model_info, f, indent=2, ensure_ascii=False, default=str)
        print(f"💾 信息已保存到: {args.output}")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
