﻿"""
EfficientZero算法实现 - 主入口模块

该模块是EfficientZero算法的主入口，负责导入和暴露所有相关组件。
EfficientZero是MuZero的改进版本，通过自监督表示学习、值前缀预测和
自适应数据重用等技术，显著提高了样本效率。

参考论文：
- "Mastering Atari Games with Limited Data" (Ye et al., 2021)

模块结构：
- efficient_zero_model.py: 模型定义
- efficient_zero_algorithm.py: 核心算法实现
- efficient_zero_amp.py: 混合精度支持
- efficient_zero_training.py: 训练函数
- efficient_zero_utils.py: 工具函数

技术债务修复说明：
- 原始文件过大(3667行)，已拆分为多个专门模块
- 每个模块职责单一，便于维护和测试
- 保持向后兼容性，所有公共接口保持不变
"""

import logging

# 导入所有组件
from .efficient_zero_model import EfficientZeroModel
from .efficient_zero_algorithm import EfficientZero
from .efficient_zero_amp import EfficientZeroAMP
from .efficient_zero_training import train_efficient_zero
from .efficient_zero_utils import (
    test_efficient_zero_amp,
    test_importance_weighted_training,
    monitor_training_performance,
    debug_model_gradients,
    analyze_replay_buffer
)

# 配置日志
logger = logging.getLogger(__name__)

# 导出所有公共接口
__all__ = [
    'EfficientZeroModel',
    'EfficientZero',
    'EfficientZeroAMP',
    'train_efficient_zero',
    'test_efficient_zero_amp',
    'test_importance_weighted_training',
    'monitor_training_performance',
    'debug_model_gradients',
    'analyze_replay_buffer'
]

# 记录模块加载信息
logger.info("EfficientZero模块已加载 - 技术债务修复版本")

# 版本信息
__version__ = "2.0.0"
__author__ = "EfficientZero Team"
__description__ = "EfficientZero算法实现 - 技术债务修复版本"

def get_version():
    """获取版本信息"""
    return __version__

def get_module_info():
    """获取模块信息"""
    return {
        'name': 'EfficientZero',
        'version': __version__,
        'author': __author__,
        'description': __description__,
        'components': __all__
    }

if __name__ == "__main__":
    # 如果直接运行这个文件，则显示模块信息
    print("=== EfficientZero模块信息 ===")
    info = get_module_info()
    for key, value in info.items():
        print(f"{key}: {value}")

    print("\n=== 可用组件 ===")
    for component in __all__:
        print(f"- {component}")

    print("\n注意：要运行测试，请使用以下命令：")
    print("python -m cardgame_ai.algorithms.efficient_zero_test")
