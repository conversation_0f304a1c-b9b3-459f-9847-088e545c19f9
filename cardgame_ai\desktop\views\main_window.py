#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
主窗口

应用程序的主窗口，负责管理所有视图和提供导航功能。
"""

import os
import logging
from typing import Dict, Any, Optional

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QHBoxLayout, QStackedWidget,
    QStatusBar, QToolBar, QMessageBox, QLabel, QPushButton
)
from PySide6.QtCore import Qt, Slot, QSize
from PySide6.QtGui import QPalette, QColor

# 使用相对导入
from ..utils.theme_manager import theme_manager
from .navigation_bar import NavigationBar
from .training_view import TrainingView
from .inference_view import InferenceView

logger = logging.getLogger(__name__)


class MainWindow(QMainWindow):
    """主窗口类"""

    def __init__(self, config):
        """
        初始化主窗口

        Args:
            config: 客户端配置
        """
        super().__init__()

        # 保存配置
        self.config = config

        # 初始化属性
        self.central_widget = None
        self.navigation_bar = None
        self.content_area = None
        self.views = {}

        # 设置窗口属性
        self.setWindowTitle("AI棋牌强化学习框架")
        self.resize(1200, 800)

        # 设置UI
        self.setup_ui()
        # 移除菜单栏设置
        # self.setup_menu()
        self.setup_toolbar()
        self.setup_statusbar()

        # 设置主题 - 固定使用浅色主题
        theme_manager.set_theme("light")

        # 获取QApplication实例
        from PySide6.QtWidgets import QApplication
        app = QApplication.instance()

        # 应用主题
        theme_manager.apply_theme(app)

        # 直接强制设置导航栏背景色 - 使用QPalette
        if self.navigation_bar:
            palette = self.navigation_bar.palette()
            palette.setColor(QPalette.Window, QColor("#1a2533"))  # 深色背景
            self.navigation_bar.setAutoFillBackground(True)  # 设置自动填充背景
            self.navigation_bar.setPalette(palette)  # 应用调色板

            # 为导航栏中的标签和按钮设置颜色
            for widget in self.navigation_bar.findChildren(QLabel):
                if widget.objectName() == "appLogo":
                    text_palette = widget.palette()
                    text_palette.setColor(QPalette.WindowText, QColor("#85c1e9"))  # 天蓝色文字
                    widget.setPalette(text_palette)

            # 为导航按钮设置颜色
            for button in self.navigation_bar.findChildren(QPushButton):
                button_palette = button.palette()
                button_palette.setColor(QPalette.ButtonText, QColor("#85c1e9"))  # 天蓝色文字
                button.setPalette(button_palette)

            print("已使用QPalette强制设置导航栏背景色")

        logger.info("主窗口初始化完成")

        # 默认显示训练视图
        self.navigate_to("训练")

    def setup_ui(self):
        """设置UI布局"""
        # 创建中央部件
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)

        # 创建主布局
        main_layout = QHBoxLayout(self.central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建导航栏
        self.navigation_bar = NavigationBar(self)
        main_layout.addWidget(self.navigation_bar)

        # 创建内容区域
        self.content_area = QStackedWidget()
        self.content_area.setObjectName("contentArea")

        # 明确设置内容区域为浅色背景
        content_palette = QPalette()
        content_palette.setColor(QPalette.Window, QColor("#F5F7FA"))
        content_palette.setColor(QPalette.WindowText, QColor("#2C3E50"))
        self.content_area.setPalette(content_palette)
        self.content_area.setAutoFillBackground(True)

        main_layout.addWidget(self.content_area)

        # 连接导航信号
        self.navigation_bar.navigation_changed.connect(self.navigate_to)

        logger.info("主窗口UI布局设置完成")

    def setup_menu(self):
        """设置菜单 - 已禁用"""
        # 菜单栏已被移除，此方法保留为空实现
        logger.info("主窗口菜单已禁用")

    def setup_toolbar(self):
        """设置工具栏"""
        # 创建工具栏
        tool_bar = QToolBar()
        tool_bar.setMovable(False)
        tool_bar.setIconSize(QSize(24, 24))
        self.addToolBar(tool_bar)

        # 不再添加导航选项，因为已经在侧边导航栏中提供

        logger.info("主窗口工具栏设置完成")

    def setup_statusbar(self):
        """设置状态栏"""
        # 创建状态栏
        status_bar = QStatusBar()
        self.setStatusBar(status_bar)

        # 设置初始状态信息
        status_bar.showMessage("就绪")

        logger.info("主窗口状态栏设置完成")

    @Slot(str)
    def navigate_to(self, view_name: str):
        """
        切换到指定视图

        Args:
            view_name (str): 视图名称
        """
        # 如果视图不存在，则创建
        if view_name not in self.views:
            self.create_view(view_name)

        # 获取视图
        view = self.views.get(view_name)

        if view:
            # 切换到指定视图
            self.content_area.setCurrentWidget(view)
            logger.info(f"切换到视图：{view_name}")
        else:
            logger.warning(f"视图不存在：{view_name}")

    def create_view(self, view_name: str):
        """
        创建视图

        Args:
            view_name (str): 视图名称
        """
        # 创建视图
        view = None

        if view_name == "训练":
            # 创建训练视图
            view = TrainingView(self.config)
            view.setObjectName("trainingView")
        elif view_name == "推理":
            # 创建推理视图
            view = InferenceView(self.config)
            view.setObjectName("inferenceView")
        elif view_name == "对战":
            # 创建对战视图
            view = QWidget()
            view.setObjectName("battleView")
            # 这里将在后续任务中实现对战视图
        elif view_name == "设置":
            # 创建设置视图
            view = QWidget()
            view.setObjectName("settingsView")
            # 这里将在后续任务中实现设置视图

        if view:
            # 添加到视图字典
            self.views[view_name] = view

            # 添加到内容区域
            self.content_area.addWidget(view)

            logger.info(f"创建视图：{view_name}")
        else:
            logger.warning(f"无法创建视图：{view_name}")

    def show_message(self, title: str, message: str, icon: Optional[QMessageBox.Icon] = None):
        """
        显示消息对话框

        Args:
            title (str): 标题
            message (str): 消息内容
            icon (Optional[QMessageBox.Icon], optional): 图标. Defaults to None.
        """
        # 创建消息框
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)

        # 设置图标
        if icon:
            msg_box.setIcon(icon)

        # 显示消息框
        msg_box.exec()

    def show_about(self):
        """显示关于对话框"""
        about_text = (
            "AI棋牌强化学习框架\n\n"
            "版本：1.0.0\n\n"
            "一个用于训练和测试AI棋牌游戏智能体的框架，"
            "支持斗地主等棋牌游戏。\n\n"
            "© 2023 AI棋牌强化学习框架团队"
        )

        self.show_message("关于", about_text, QMessageBox.Information)

    def exit_app(self):
        """退出应用程序"""
        # 保存配置
        self.config.save()

        # 关闭窗口
        self.close()

        logger.info("退出应用程序")
