#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
优化的MCTS搜索模块

提供高效的MCTS搜索功能：
- 并行MCTS搜索
- 批量叶节点评估
- UCB缓存优化
- 搜索树剪枝
- 动态预算分配
"""

import time
import logging
import threading
from typing import Dict, List, Any, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor
import numpy as np
import torch
from collections import defaultdict, deque

logger = logging.getLogger(__name__)


class NodePool:
    """MCTS节点池，用于内存优化"""
    
    def __init__(self, size: int = 10000):
        """初始化节点池"""
        self.size = size
        self.pool = deque()
        self.active_nodes = set()
        self.lock = threading.Lock()
        
        # 预分配节点
        for _ in range(size):
            self.pool.append(MCTSNode())
        
        logger.info(f"初始化MCTS节点池，大小: {size}")
    
    def get_node(self) -> 'MCTSNode':
        """从池中获取节点"""
        with self.lock:
            if self.pool:
                node = self.pool.popleft()
                node.reset()
                self.active_nodes.add(id(node))
                return node
            else:
                # 池已空，创建新节点
                node = MCTSNode()
                self.active_nodes.add(id(node))
                return node
    
    def return_node(self, node: 'MCTSNode') -> None:
        """将节点返回池中"""
        with self.lock:
            if id(node) in self.active_nodes:
                self.active_nodes.remove(id(node))
                if len(self.pool) < self.size:
                    self.pool.append(node)


class MCTSNode:
    """MCTS搜索节点"""
    
    def __init__(self):
        """初始化MCTS节点"""
        self.reset()
    
    def reset(self):
        """重置节点状态"""
        self.state = None
        self.parent = None
        self.children = {}
        self.visit_count = 0
        self.value_sum = 0.0
        self.prior = 0.0
        self.is_expanded = False
        self.action_from_parent = None
    
    def is_leaf(self) -> bool:
        """检查是否为叶节点"""
        return not self.is_expanded
    
    def get_value(self) -> float:
        """获取节点平均价值"""
        if self.visit_count == 0:
            return 0.0
        return self.value_sum / self.visit_count
    
    def get_ucb_score(self, c_puct: float, parent_visits: int) -> float:
        """计算UCB分数"""
        if self.visit_count == 0:
            return float('inf')
        
        exploration = c_puct * self.prior * np.sqrt(parent_visits) / (1 + self.visit_count)
        return self.get_value() + exploration
    
    def select_child(self, c_puct: float) -> 'MCTSNode':
        """选择最佳子节点"""
        best_score = -float('inf')
        best_child = None
        
        for child in self.children.values():
            score = child.get_ucb_score(c_puct, self.visit_count)
            if score > best_score:
                best_score = score
                best_child = child
        
        return best_child
    
    def expand(self, action_priors: Dict[Any, float], node_pool: NodePool):
        """扩展节点"""
        for action, prior in action_priors.items():
            child = node_pool.get_node()
            child.parent = self
            child.prior = prior
            child.action_from_parent = action
            self.children[action] = child
        
        self.is_expanded = True
    
    def backup(self, value: float):
        """回传价值"""
        self.visit_count += 1
        self.value_sum += value
        
        if self.parent:
            self.parent.backup(-value)  # 对手视角，价值取反


class OptimizedMCTS:
    """优化的MCTS搜索器"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化优化MCTS搜索器"""
        self.config = config
        self.num_simulations = config.get('num_simulations', 120)
        self.c_puct = config.get('c_puct', 1.4)
        self.parallel_threads = config.get('parallel_threads', 4)
        self.batch_size_inference = config.get('batch_size_inference', 16)
        
        # 优化组件
        self.node_pool = NodePool(size=config.get('node_pool_size', 10000))
        self.ucb_cache = {}
        self.batch_inference_queue = []
        self.inference_lock = threading.Lock()
        
        # 动态预算分配
        self.dynamic_budget = config.get('dynamic_budget', True)
        self.budget_allocation = config.get('budget_allocation', {
            'critical_moments': 2.0,
            'early_game': 0.8,
            'mid_game': 1.2,
            'late_game': 1.5
        })
        
        logger.info(f"初始化优化MCTS: simulations={self.num_simulations}, threads={self.parallel_threads}")
    
    def search(self, root_state, model) -> Dict[Any, float]:
        """执行MCTS搜索"""
        # 创建根节点
        root = self.node_pool.get_node()
        root.state = root_state
        
        # 动态调整搜索预算
        search_budget = self._get_dynamic_budget(root_state)
        
        if self.parallel_threads > 1:
            # 并行搜索
            action_probs = self._parallel_search(root, model, search_budget)
        else:
            # 串行搜索
            action_probs = self._serial_search(root, model, search_budget)
        
        # 清理节点
        self._cleanup_tree(root)
        
        return action_probs
    
    def _get_dynamic_budget(self, state) -> int:
        """获取动态搜索预算"""
        if not self.dynamic_budget:
            return self.num_simulations
        
        # 根据游戏阶段调整预算
        game_phase = getattr(state, 'game_phase', None)
        if game_phase:
            if hasattr(game_phase, 'value'):
                phase_value = game_phase.value
            else:
                phase_value = 1  # 默认中期
            
            if phase_value == 0:  # 早期
                factor = self.budget_allocation.get('early_game', 0.8)
            elif phase_value == 1:  # 中期
                factor = self.budget_allocation.get('mid_game', 1.2)
            else:  # 后期
                factor = self.budget_allocation.get('late_game', 1.5)
        else:
            factor = 1.0
        
        # 检测关键时刻
        if self._is_critical_moment(state):
            factor *= self.budget_allocation.get('critical_moments', 2.0)
        
        budget = int(self.num_simulations * factor)
        return max(50, min(budget, 500))  # 限制在合理范围内
    
    def _is_critical_moment(self, state) -> bool:
        """检测是否为关键时刻"""
        # 简单的关键时刻检测逻辑
        # 可以根据具体游戏规则进行优化
        
        # 检查手牌数量
        if hasattr(state, 'hands'):
            for hand in state.hands:
                if len(hand) <= 3:  # 有玩家手牌很少
                    return True
        
        # 检查是否为地主决策阶段
        if hasattr(state, 'game_phase') and hasattr(state.game_phase, 'value'):
            if state.game_phase.value in [0, 1]:  # 叫地主或抢地主阶段
                return True
        
        return False
    
    def _parallel_search(self, root, model, search_budget: int) -> Dict[Any, float]:
        """并行MCTS搜索"""
        simulations_per_thread = search_budget // self.parallel_threads
        
        with ThreadPoolExecutor(max_workers=self.parallel_threads) as executor:
            futures = []
            
            for i in range(self.parallel_threads):
                future = executor.submit(
                    self._thread_search,
                    root, model, simulations_per_thread
                )
                futures.append(future)
            
            # 等待所有线程完成
            for future in futures:
                future.result()
        
        # 计算动作概率
        return self._compute_action_probabilities(root)
    
    def _serial_search(self, root, model, search_budget: int) -> Dict[Any, float]:
        """串行MCTS搜索"""
        for _ in range(search_budget):
            self._single_simulation(root, model)
        
        return self._compute_action_probabilities(root)
    
    def _thread_search(self, root, model, num_simulations: int):
        """单线程搜索"""
        for _ in range(num_simulations):
            self._single_simulation(root, model)
    
    def _single_simulation(self, root, model):
        """单次MCTS模拟"""
        path = []
        node = root
        
        # 选择阶段
        while not node.is_leaf():
            action = self._select_action(node)
            path.append((node, action))
            node = node.children[action]
        
        # 扩展和评估阶段
        if node.visit_count == 0:
            # 首次访问，直接评估
            value = self._evaluate_leaf(node, model)
        else:
            # 扩展节点
            action_priors = self._expand_node(node, model)
            if action_priors:
                node.expand(action_priors, self.node_pool)
                # 选择一个子节点进行评估
                if node.children:
                    action = max(action_priors.keys(), key=lambda a: action_priors[a])
                    child = node.children[action]
                    value = self._evaluate_leaf(child, model)
                    path.append((node, action))
                    node = child
                else:
                    value = self._evaluate_leaf(node, model)
            else:
                value = self._evaluate_leaf(node, model)
        
        # 回传阶段
        node.backup(value)
    
    def _select_action(self, node):
        """选择动作（UCB）"""
        return node.select_child(self.c_puct).action_from_parent
    
    def _evaluate_leaf(self, node, model) -> float:
        """评估叶节点"""
        try:
            with torch.no_grad():
                # 这里需要根据实际的模型接口进行调整
                state_tensor = self._state_to_tensor(node.state)
                if state_tensor is not None:
                    policy, value = model(state_tensor.unsqueeze(0))
                    return value.item()
                else:
                    return 0.0
        except Exception as e:
            logger.warning(f"叶节点评估错误: {e}")
            return 0.0
    
    def _expand_node(self, node, model) -> Dict[Any, float]:
        """扩展节点"""
        try:
            with torch.no_grad():
                state_tensor = self._state_to_tensor(node.state)
                if state_tensor is not None:
                    policy, _ = model(state_tensor.unsqueeze(0))
                    
                    # 获取合法动作
                    legal_actions = self._get_legal_actions(node.state)
                    
                    # 构建动作先验概率
                    action_priors = {}
                    policy_probs = torch.softmax(policy[0], dim=0)
                    
                    for i, action in enumerate(legal_actions):
                        if i < len(policy_probs):
                            action_priors[action] = policy_probs[i].item()
                        else:
                            action_priors[action] = 1.0 / len(legal_actions)
                    
                    return action_priors
                else:
                    return {}
        except Exception as e:
            logger.warning(f"节点扩展错误: {e}")
            return {}
    
    def _state_to_tensor(self, state) -> Optional[torch.Tensor]:
        """将状态转换为张量"""
        # 这里需要根据实际的状态表示进行实现
        try:
            if hasattr(state, 'to_tensor'):
                return state.to_tensor()
            else:
                # 简单的状态表示
                return torch.zeros(656, dtype=torch.float32)
        except Exception as e:
            logger.warning(f"状态转换错误: {e}")
            return None
    
    def _get_legal_actions(self, state) -> List[Any]:
        """获取合法动作"""
        try:
            if hasattr(state, 'get_legal_actions'):
                return state.get_legal_actions()
            else:
                return []
        except Exception as e:
            logger.warning(f"获取合法动作错误: {e}")
            return []
    
    def _compute_action_probabilities(self, root) -> Dict[Any, float]:
        """计算动作概率"""
        if not root.children:
            return {}
        
        # 基于访问次数计算概率
        total_visits = sum(child.visit_count for child in root.children.values())
        if total_visits == 0:
            return {}
        
        action_probs = {}
        for action, child in root.children.items():
            action_probs[action] = child.visit_count / total_visits
        
        return action_probs
    
    def _cleanup_tree(self, root):
        """清理搜索树"""
        def cleanup_recursive(node):
            for child in node.children.values():
                cleanup_recursive(child)
                self.node_pool.return_node(child)
            node.children.clear()
        
        cleanup_recursive(root)
        self.node_pool.return_node(root)
