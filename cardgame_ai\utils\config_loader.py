"""
配置加载工具模块

提供全局配置加载和管理功能。
"""
import os
import logging
from typing import Dict, Any, Optional, Union

from cardgame_ai.utils.config import ConfigManager

logger = logging.getLogger(__name__)

def load_global_config(config_path: Optional[str] = None, config_dict: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    加载全局配置

    优先从 config_dict 直接加载配置，如果为 None 则尝试从 config_path 加载。
    如果两者都为 None，则返回默认配置。

    Args:
        config_path (Optional[str], optional): 配置文件路径. Defaults to None.
        config_dict (Optional[Dict[str, Any]], optional): 配置字典. Defaults to None.

    Returns:
        Dict[str, Any]: 加载的配置

    Raises:
        FileNotFoundError: 如果配置文件不存在
        ValueError: 如果配置文件格式不支持
    """
    # 默认配置
    default_config = {
        "logging": {
            "level": "INFO",
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            "file": "logs/cardgame_ai.log",
            "console": True
        },
        "environment": {
            "type": "doudizhu",
            "render_mode": None
        },
        "agent": {
            "type": "dqn",
            "model_path": None
        },
        "training": {
            "epochs": 1000,
            "batch_size": 64,
            "checkpoint_interval": 100,
            "checkpoint_dir": "models"
        },
        "inference": {
            "episodes": 100,
            "render": False
        },
        "evaluation": {
            "episodes": 100,
            "metrics": ["win_rate", "average_reward"]
        }
    }

    # 如果提供了配置字典，直接使用
    if config_dict is not None:
        logger.info("使用提供的配置字典")
        # 合并默认配置和提供的配置
        merged_config = _deep_merge(default_config, config_dict)
        return merged_config

    # 如果提供了配置文件路径，从文件加载
    if config_path is not None:
        try:
            logger.info(f"从文件加载配置: {config_path}")
            config_manager = ConfigManager(config_path)
            # 合并默认配置和加载的配置
            merged_config = _deep_merge(default_config, config_manager.config)
            return merged_config
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            logger.warning("使用默认配置")
            return default_config

    # 如果没有提供配置，使用默认配置
    logger.info("未提供配置，使用默认配置")
    return default_config

def _deep_merge(dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
    """
    深度合并两个字典
    
    dict2 的值将覆盖 dict1 的值
    
    Args:
        dict1 (Dict[str, Any]): 基础字典
        dict2 (Dict[str, Any]): 覆盖字典
        
    Returns:
        Dict[str, Any]: 合并后的字典
    """
    result = dict1.copy()
    
    for key, value in dict2.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            # 递归合并嵌套字典
            result[key] = _deep_merge(result[key], value)
        else:
            # 直接覆盖值
            result[key] = value
            
    return result 