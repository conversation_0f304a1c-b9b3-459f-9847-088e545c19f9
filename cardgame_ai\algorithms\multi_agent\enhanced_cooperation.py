#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
增强的多智能体协作模块

实现基于MAPPO算法的农民协作机制，包含：
- 智能体间通信协议
- 角色专门化系统
- 团队奖励机制
- 协作策略优化

基于架构文档和PRD需求设计。
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import logging
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class AgentRole(Enum):
    """智能体角色枚举"""
    LANDLORD = "landlord"
    FARMER_1 = "farmer_1"
    FARMER_2 = "farmer_2"


@dataclass
class CommunicationMessage:
    """通信消息数据结构"""
    sender_id: int
    receiver_id: int
    message_type: str
    content: torch.Tensor
    timestamp: float
    priority: float = 1.0


class CommunicationChannel:
    """
    智能体间通信信道

    实现智能体之间的信息传递和协调机制
    """

    def __init__(
        self,
        message_dim: int = 64,
        max_messages: int = 3,
        attention_heads: int = 4,
        device: str = "cpu"
    ):
        """
        初始化通信信道

        Args:
            message_dim: 消息向量维度
            max_messages: 最大消息数量
            attention_heads: 注意力头数
            device: 计算设备
        """
        self.message_dim = message_dim
        self.max_messages = max_messages
        self.device = device

        # 消息编码器
        self.message_encoder = nn.Sequential(
            nn.Linear(message_dim, message_dim),
            nn.ReLU(),
            nn.Linear(message_dim, message_dim)
        ).to(device)

        # 消息解码器
        self.message_decoder = nn.Sequential(
            nn.Linear(message_dim, message_dim),
            nn.ReLU(),
            nn.Linear(message_dim, message_dim)
        ).to(device)

        # 注意力机制
        self.attention = nn.MultiheadAttention(
            embed_dim=message_dim,
            num_heads=attention_heads,
            batch_first=True
        ).to(device)

        # 消息缓冲区
        self.message_buffer: List[CommunicationMessage] = []

        logger.info(f"通信信道初始化完成，消息维度: {message_dim}")

    def send_message(
        self,
        sender_id: int,
        receiver_id: int,
        message_content: torch.Tensor,
        message_type: str = "cooperation",
        priority: float = 1.0
    ) -> bool:
        """
        发送消息

        Args:
            sender_id: 发送者ID
            receiver_id: 接收者ID
            message_content: 消息内容
            message_type: 消息类型
            priority: 消息优先级

        Returns:
            bool: 是否发送成功
        """
        # 编码消息
        encoded_content = self.message_encoder(message_content)

        # 创建消息对象
        message = CommunicationMessage(
            sender_id=sender_id,
            receiver_id=receiver_id,
            message_type=message_type,
            content=encoded_content,
            timestamp=torch.cuda.Event().record() if torch.cuda.is_available() else 0.0,
            priority=priority
        )

        # 添加到缓冲区
        self.message_buffer.append(message)

        # 维护缓冲区大小
        if len(self.message_buffer) > self.max_messages:
            # 移除优先级最低的消息
            self.message_buffer.sort(key=lambda x: x.priority, reverse=True)
            self.message_buffer = self.message_buffer[:self.max_messages]

        return True

    def receive_messages(self, receiver_id: int) -> List[torch.Tensor]:
        """
        接收消息

        Args:
            receiver_id: 接收者ID

        Returns:
            List[torch.Tensor]: 接收到的消息列表
        """
        # 筛选目标消息
        target_messages = [
            msg for msg in self.message_buffer
            if msg.receiver_id == receiver_id or msg.receiver_id == -1  # -1表示广播
        ]

        if not target_messages:
            return []

        # 解码消息
        decoded_messages = []
        for msg in target_messages:
            decoded_content = self.message_decoder(msg.content)
            decoded_messages.append(decoded_content)

        return decoded_messages

    def process_messages(self, messages: List[torch.Tensor]) -> torch.Tensor:
        """
        处理接收到的消息

        Args:
            messages: 消息列表

        Returns:
            torch.Tensor: 处理后的消息表示
        """
        if not messages:
            return torch.zeros(1, self.message_dim, device=self.device)

        # 堆叠消息
        message_stack = torch.stack(messages, dim=0).unsqueeze(0)  # [1, num_messages, message_dim]

        # 使用注意力机制处理消息
        processed_messages, attention_weights = self.attention(
            message_stack, message_stack, message_stack
        )

        # 聚合消息
        aggregated_message = processed_messages.mean(dim=1)  # [1, message_dim]

        return aggregated_message.squeeze(0)  # [message_dim]

    def clear_buffer(self):
        """清空消息缓冲区"""
        self.message_buffer.clear()


class RoleSpecializer:
    """
    角色专门化系统

    为不同角色的智能体提供专门化的策略和价值函数
    """

    def __init__(
        self,
        state_dim: int,
        action_dim: int,
        hidden_dim: int = 256,
        device: str = "cpu"
    ):
        """
        初始化角色专门化系统

        Args:
            state_dim: 状态维度
            action_dim: 动作维度
            hidden_dim: 隐藏层维度
            device: 计算设备
        """
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.device = device

        # 为每个角色创建专门的网络
        self.role_networks = nn.ModuleDict({
            AgentRole.LANDLORD.value: self._create_role_network(hidden_dim),
            AgentRole.FARMER_1.value: self._create_role_network(hidden_dim),
            AgentRole.FARMER_2.value: self._create_role_network(hidden_dim)
        }).to(device)

        # 角色识别网络
        self.role_classifier = nn.Sequential(
            nn.Linear(state_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, len(AgentRole)),
            nn.Softmax(dim=-1)
        ).to(device)

        logger.info("角色专门化系统初始化完成")

    def _create_role_network(self, hidden_dim: int) -> nn.Module:
        """创建角色专门网络"""
        return nn.Sequential(
            nn.Linear(self.state_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, self.action_dim + 1)  # 动作概率 + 价值
        )

    def identify_role(self, state: torch.Tensor) -> AgentRole:
        """
        识别当前角色

        Args:
            state: 游戏状态

        Returns:
            AgentRole: 识别的角色
        """
        role_probs = self.role_classifier(state)
        role_idx = torch.argmax(role_probs, dim=-1).item()

        roles = list(AgentRole)
        return roles[role_idx]

    def get_specialized_output(
        self,
        state: torch.Tensor,
        role: AgentRole
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        获取角色专门化输出

        Args:
            state: 游戏状态
            role: 智能体角色

        Returns:
            Tuple[torch.Tensor, torch.Tensor]: (动作概率, 价值)
        """
        network = self.role_networks[role.value]
        output = network(state)

        action_probs = F.softmax(output[:-1], dim=-1)
        value = output[-1]

        return action_probs, value


class TeamRewardCalculator:
    """
    团队奖励计算器

    实现农民团队的协作奖励机制
    """

    def __init__(
        self,
        cooperation_weight: float = 0.8,
        team_reward_weight: float = 0.9,
        individual_weight: float = 0.2
    ):
        """
        初始化团队奖励计算器

        Args:
            cooperation_weight: 协作权重
            team_reward_weight: 团队奖励权重
            individual_weight: 个体奖励权重
        """
        self.cooperation_weight = cooperation_weight
        self.team_reward_weight = team_reward_weight
        self.individual_weight = individual_weight

        logger.info(f"团队奖励计算器初始化完成，协作权重: {cooperation_weight}")

    def calculate_team_reward(
        self,
        individual_rewards: Dict[int, float],
        team_outcome: float,
        cooperation_score: float
    ) -> Dict[int, float]:
        """
        计算团队奖励

        Args:
            individual_rewards: 个体奖励字典
            team_outcome: 团队结果 (1.0胜利, -1.0失败)
            cooperation_score: 协作得分 (0.0-1.0)

        Returns:
            Dict[int, float]: 调整后的奖励字典
        """
        adjusted_rewards = {}

        # 计算团队奖励
        team_reward = team_outcome * self.team_reward_weight
        cooperation_bonus = cooperation_score * self.cooperation_weight

        for agent_id, individual_reward in individual_rewards.items():
            # 组合个体奖励和团队奖励
            adjusted_reward = (
                individual_reward * self.individual_weight +
                team_reward +
                cooperation_bonus
            )
            adjusted_rewards[agent_id] = adjusted_reward

        return adjusted_rewards

    def calculate_cooperation_score(
        self,
        agent_actions: Dict[int, Any],
        game_state: Any
    ) -> float:
        """
        计算协作得分

        Args:
            agent_actions: 智能体动作字典
            game_state: 游戏状态

        Returns:
            float: 协作得分 (0.0-1.0)
        """
        # 简化的协作得分计算
        # 实际实现中可以基于具体的游戏逻辑

        cooperation_indicators = []

        # 检查是否有协调的出牌
        farmer_actions = [action for agent_id, action in agent_actions.items()
                         if agent_id != 0]  # 假设0是地主

        if len(farmer_actions) >= 2:
            # 检查农民是否有协调行为
            # 这里可以实现更复杂的协作检测逻辑
            cooperation_indicators.append(0.5)

        # 返回平均协作得分
        return np.mean(cooperation_indicators) if cooperation_indicators else 0.0


class EnhancedCooperationManager:
    """
    增强的协作管理器

    整合通信、角色专门化和团队奖励机制
    """

    def __init__(
        self,
        state_dim: int,
        action_dim: int,
        message_dim: int = 64,
        hidden_dim: int = 256,
        cooperation_weight: float = 0.8,
        team_reward_weight: float = 0.9,
        device: str = "cpu"
    ):
        """
        初始化协作管理器

        Args:
            state_dim: 状态维度
            action_dim: 动作维度
            message_dim: 消息维度
            hidden_dim: 隐藏层维度
            cooperation_weight: 协作权重
            team_reward_weight: 团队奖励权重
            device: 计算设备
        """
        self.device = device

        # 初始化子模块
        self.communication = CommunicationChannel(
            message_dim=message_dim,
            device=device
        )

        self.role_specializer = RoleSpecializer(
            state_dim=state_dim,
            action_dim=action_dim,
            hidden_dim=hidden_dim,
            device=device
        )

        self.reward_calculator = TeamRewardCalculator(
            cooperation_weight=cooperation_weight,
            team_reward_weight=team_reward_weight
        )

        logger.info("增强协作管理器初始化完成")

    def process_multi_agent_step(
        self,
        states: Dict[int, torch.Tensor],
        actions: Dict[int, Any],
        rewards: Dict[int, float],
        game_outcome: float
    ) -> Tuple[Dict[int, torch.Tensor], Dict[int, float]]:
        """
        处理多智能体步骤

        Args:
            states: 智能体状态字典
            actions: 智能体动作字典
            rewards: 智能体奖励字典
            game_outcome: 游戏结果

        Returns:
            Tuple[Dict[int, torch.Tensor], Dict[int, float]]: (增强状态, 调整奖励)
        """
        enhanced_states = {}

        # 处理每个智能体
        for agent_id, state in states.items():
            # 接收消息
            messages = self.communication.receive_messages(agent_id)

            # 处理消息
            if messages:
                processed_message = self.communication.process_messages(messages)
                # 将消息信息融合到状态中
                enhanced_state = torch.cat([state, processed_message], dim=-1)
            else:
                # 如果没有消息，添加零向量
                zero_message = torch.zeros(self.communication.message_dim, device=self.device)
                enhanced_state = torch.cat([state, zero_message], dim=-1)

            enhanced_states[agent_id] = enhanced_state

        # 计算协作得分
        cooperation_score = self.reward_calculator.calculate_cooperation_score(
            actions, None  # 这里可以传入更详细的游戏状态
        )

        # 调整奖励
        adjusted_rewards = self.reward_calculator.calculate_team_reward(
            rewards, game_outcome, cooperation_score
        )

        # 清空通信缓冲区（为下一步准备）
        self.communication.clear_buffer()

        return enhanced_states, adjusted_rewards

    def generate_cooperation_message(
        self,
        sender_id: int,
        state: torch.Tensor,
        intended_action: Any
    ) -> torch.Tensor:
        """
        生成协作消息

        Args:
            sender_id: 发送者ID
            state: 当前状态
            intended_action: 意图动作

        Returns:
            torch.Tensor: 生成的消息
        """
        # 简化的消息生成
        # 实际实现中可以基于更复杂的策略

        # 将状态和动作信息编码为消息
        action_tensor = torch.tensor([intended_action], dtype=torch.float32, device=self.device)

        # 组合状态和动作信息
        message_input = torch.cat([
            state[:self.communication.message_dim-1],  # 截取状态的一部分
            action_tensor
        ], dim=0)

        return message_input


def create_enhanced_cooperation_system(
    state_dim: int,
    action_dim: int,
    config: Dict[str, Any]
) -> EnhancedCooperationManager:
    """
    创建增强协作系统的工厂函数

    Args:
        state_dim: 状态维度
        action_dim: 动作维度
        config: 配置字典

    Returns:
        EnhancedCooperationManager: 协作管理器实例
    """
    return EnhancedCooperationManager(
        state_dim=state_dim,
        action_dim=action_dim,
        message_dim=config.get('message_dim', 64),
        hidden_dim=config.get('hidden_dim', 256),
        cooperation_weight=config.get('cooperation_weight', 0.8),
        team_reward_weight=config.get('team_reward_weight', 0.9),
        device=config.get('device', 'cpu')
    )


def create_enhanced_cooperation_system(
    state_dim: int,
    action_dim: int,
    config: Dict[str, Any]
) -> EnhancedCooperationManager:
    """
    创建增强协作系统的工厂函数

    Args:
        state_dim: 状态维度
        action_dim: 动作维度
        config: 配置字典

    Returns:
        EnhancedCooperationManager: 协作管理器实例
    """
    return EnhancedCooperationManager(
        state_dim=state_dim,
        action_dim=action_dim,
        message_dim=config.get('message_dim', 64),
        hidden_dim=config.get('hidden_dim', 256),
        cooperation_weight=config.get('cooperation_weight', 0.8),
        team_reward_weight=config.get('team_reward_weight', 0.9),
        device=config.get('device', 'cpu')
    )
