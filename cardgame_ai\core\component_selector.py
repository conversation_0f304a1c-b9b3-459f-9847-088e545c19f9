"""
组件选择器模块

提供智能组件选择功能，根据状态特征和历史性能动态选择最适合的算法组件，
提高决策质量和系统性能。
"""

import os
import time
import logging
import threading
import numpy as np
from typing import Dict, Any, List, Optional, Callable, Tuple, Union

# 设置日志
logger = logging.getLogger(__name__)


class ComponentPerformanceTracker:
    """
    组件性能跟踪器类

    跟踪各个组件在不同状态下的性能表现，为组件选择提供依据。
    """

    def __init__(self, decay_factor: float = 0.95, max_history: int = 1000):
        """
        初始化组件性能跟踪器

        Args:
            decay_factor: 历史性能衰减因子，用于降低旧数据的权重
            max_history: 每个组件保存的最大历史记录数
        """
        self.decay_factor = decay_factor
        self.max_history = max_history
        self.performance_data = {}  # 组件性能数据
        self.lock = threading.RLock()

        # 性能指标
        self.metrics = ["reward", "time", "complexity"]

    def record_performance(
        self,
        component_id: str,
        state_features: np.ndarray,
        performance: Dict[str, float]
    ) -> None:
        """
        记录组件性能

        Args:
            component_id: 组件ID
            state_features: 状态特征向量
            performance: 性能指标字典，包含reward、time、complexity等
        """
        with self.lock:
            # 确保组件存在于性能数据中
            if component_id not in self.performance_data:
                self.performance_data[component_id] = {
                    "state_features": [],
                    "performance": [],
                    "timestamp": []
                }

            # 添加新记录
            data = self.performance_data[component_id]
            data["state_features"].append(state_features)
            data["performance"].append(performance)
            data["timestamp"].append(time.time())

            # 如果超过最大历史记录数，删除最旧的记录
            if len(data["state_features"]) > self.max_history:
                data["state_features"].pop(0)
                data["performance"].pop(0)
                data["timestamp"].pop(0)

    def get_performance(
        self,
        component_id: str,
        state_features: np.ndarray,
        metric: str = "reward",
        k: int = 5
    ) -> float:
        """
        获取组件在特定状态下的性能

        使用k近邻加权平均计算性能

        Args:
            component_id: 组件ID
            state_features: 状态特征向量
            metric: 性能指标，如"reward"、"time"、"complexity"
            k: 近邻数量

        Returns:
            float: 预测的性能值，如果没有历史数据则返回默认值
        """
        with self.lock:
            # 检查组件是否存在
            if component_id not in self.performance_data:
                return self._get_default_performance(metric)

            data = self.performance_data[component_id]

            # 检查是否有足够的历史数据
            if not data["state_features"]:
                return self._get_default_performance(metric)

            # 计算与历史状态的相似度
            similarities = []
            for hist_features in data["state_features"]:
                similarity = self._compute_similarity(state_features, hist_features)
                similarities.append(similarity)

            # 找到k个最相似的状态
            if k > len(similarities):
                k = len(similarities)

            # 获取k个最相似状态的索引
            top_k_indices = np.argsort(similarities)[-k:]

            # 计算加权平均性能
            weighted_sum = 0.0
            weight_sum = 0.0

            for idx in top_k_indices:
                weight = similarities[idx]
                # 应用时间衰减
                time_diff = time.time() - data["timestamp"][idx]
                decay = self.decay_factor ** (time_diff / 3600.0)  # 每小时衰减

                # 获取性能值
                perf_value = data["performance"][idx].get(metric, self._get_default_performance(metric))

                weighted_sum += weight * decay * perf_value
                weight_sum += weight * decay

            # 返回加权平均值
            if weight_sum > 0:
                return weighted_sum / weight_sum
            else:
                return self._get_default_performance(metric)

    def _compute_similarity(self, features1: np.ndarray, features2: np.ndarray) -> float:
        """
        计算两个特征向量的相似度

        使用余弦相似度

        Args:
            features1: 第一个特征向量
            features2: 第二个特征向量

        Returns:
            float: 相似度，范围[0, 1]
        """
        # 检查输入
        if features1 is None or features2 is None:
            return 0.0

        if len(features1) != len(features2):
            return 0.0

        # 计算余弦相似度
        dot_product = np.sum(features1 * features2)
        norm1 = np.sqrt(np.sum(features1 * features1))
        norm2 = np.sqrt(np.sum(features2 * features2))

        if norm1 == 0 or norm2 == 0:
            return 0.0

        similarity = dot_product / (norm1 * norm2)

        # 确保结果在[0, 1]范围内
        return max(0.0, min(1.0, (similarity + 1) / 2))

    def _get_default_performance(self, metric: str) -> float:
        """
        获取默认性能值

        Args:
            metric: 性能指标

        Returns:
            float: 默认性能值
        """
        if metric == "reward":
            return 0.0  # 默认奖励为0
        elif metric == "time":
            return 1.0  # 默认时间为1秒
        elif metric == "complexity":
            return 0.5  # 默认复杂度为中等
        else:
            return 0.0  # 其他指标默认为0


class SmartComponentSelector:
    """
    智能组件选择器类

    根据状态特征和历史性能智能选择最适合的算法组件。
    """

    def __init__(
        self,
        components: List[str],
        feature_extractor: Optional[Callable[[Any], np.ndarray]] = None,
        exploration_factor: float = 0.1,
        performance_tracker: Optional[ComponentPerformanceTracker] = None
    ):
        """
        初始化智能组件选择器

        Args:
            components: 可选组件列表
            feature_extractor: 特征提取函数，将状态转换为特征向量
            exploration_factor: 探索因子，控制探索与利用的平衡
            performance_tracker: 性能跟踪器，如果为None则创建新的
        """
        self.components = components
        self.feature_extractor = feature_extractor or self._default_feature_extractor
        self.exploration_factor = exploration_factor
        self.performance_tracker = performance_tracker or ComponentPerformanceTracker()

        # 组件使用计数
        self.component_counts = {comp: 0 for comp in components}

        # 组件性能缓存
        self.performance_cache = {}

        # 锁
        self.lock = threading.RLock()

        logger.info(f"智能组件选择器已初始化，组件数量: {len(components)}")

    def _default_feature_extractor(self, state: Any) -> np.ndarray:
        """
        默认特征提取函数

        如果没有提供特征提取函数，使用此函数

        Args:
            state: 状态对象

        Returns:
            np.ndarray: 特征向量
        """
        # 尝试将状态转换为数组
        try:
            if hasattr(state, "to_feature_vector"):
                # 如果状态对象有to_feature_vector方法，使用它
                return state.to_feature_vector()
            elif hasattr(state, "__array__"):
                # 如果状态对象可以转换为数组，使用它
                return np.array(state)
            elif isinstance(state, dict):
                # 如果状态是字典，将值转换为数组
                return np.array(list(state.values()))
            elif isinstance(state, (list, tuple)):
                # 如果状态是列表或元组，直接转换为数组
                return np.array(state)
            else:
                # 其他情况，返回一个默认特征
                logger.warning(f"无法从类型 {type(state)} 提取特征，使用默认特征")
                return np.array([0.5])
        except Exception as e:
            logger.error(f"特征提取失败: {e}")
            return np.array([0.5])

    def select_component(self, state: Any) -> str:
        """
        选择最适合当前状态的组件

        使用UCB算法平衡探索与利用

        Args:
            state: 当前状态

        Returns:
            str: 选择的组件ID
        """
        with self.lock:
            # 提取特征
            features = self.feature_extractor(state)

            # 计算每个组件的UCB值
            total_count = sum(self.component_counts.values()) or 1
            ucb_values = {}

            for component in self.components:
                # 获取组件性能
                reward = self.performance_tracker.get_performance(
                    component_id=component,
                    state_features=features,
                    metric="reward"
                )

                # 计算UCB值
                count = self.component_counts[component]
                exploration_term = self.exploration_factor * np.sqrt(2 * np.log(total_count) / (count + 1))
                ucb_values[component] = reward + exploration_term

            # 选择UCB值最高的组件
            selected_component = max(ucb_values.items(), key=lambda x: x[1])[0]

            # 更新使用计数
            self.component_counts[selected_component] += 1

            return selected_component

    def update_performance(
        self,
        component_id: str,
        state: Any,
        performance: Dict[str, float]
    ) -> None:
        """
        更新组件性能

        Args:
            component_id: 组件ID
            state: 状态
            performance: 性能指标字典
        """
        with self.lock:
            # 提取特征
            features = self.feature_extractor(state)

            # 更新性能跟踪器
            self.performance_tracker.record_performance(
                component_id=component_id,
                state_features=features,
                performance=performance
            )

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            Dict[str, Any]: 统计信息字典
        """
        with self.lock:
            return {
                "components": self.components,
                "component_counts": self.component_counts,
                "total_selections": sum(self.component_counts.values()),
                "exploration_factor": self.exploration_factor
            }
