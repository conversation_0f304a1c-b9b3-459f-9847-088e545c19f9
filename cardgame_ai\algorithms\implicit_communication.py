"""
隐式通信模块

实现多智能体系统中的隐式通信机制，使智能体能够通过行为和状态表示进行协作，
而无需显式的信息交换。适用于合作型卡牌游戏中的队友协作。
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Any, Optional, Union, Callable
import logging

from cardgame_ai.core.base import State, Action

logger = logging.getLogger(__name__)


class ImplicitCommunicationChannel:
    """
    隐式通信通道
    
    通过智能体的行为和状态表示传递隐含信息，辅助智能体之间的协作。
    """
    
    def __init__(
        self,
        embedding_size: int = 128,
        num_agents: int = 2,
        message_size: int = 64,
        hidden_size: int = 256,
        device: str = 'cuda' if torch.cuda.is_available() else 'cpu'
    ):
        """
        初始化隐式通信通道
        
        Args:
            embedding_size: 状态嵌入的维度
            num_agents: 智能体数量
            message_size: 消息嵌入的维度
            hidden_size: 隐藏层大小
            device: 计算设备
        """
        self.embedding_size = embedding_size
        self.num_agents = num_agents
        self.message_size = message_size
        self.hidden_size = hidden_size
        self.device = device
        
        # 隐式消息编码器（从状态和动作提取隐式信号）
        self.encoder = nn.Sequential(
            nn.Linear(embedding_size + message_size, hidden_size),
            nn.ReLU(),
            nn.Linear(hidden_size, message_size)
        ).to(device)
        
        # 隐式消息解码器（解释其他智能体的信号）
        self.decoder = nn.Sequential(
            nn.Linear(message_size, hidden_size),
            nn.ReLU(),
            nn.Linear(hidden_size, embedding_size)
        ).to(device)
        
        # 状态和动作的历史记录（用于学习通信模式）
        self.state_history = [[] for _ in range(num_agents)]
        self.action_history = [[] for _ in range(num_agents)]
        self.message_history = [[] for _ in range(num_agents)]
        
        # 优化器
        self.optimizer = torch.optim.Adam(
            list(self.encoder.parameters()) + list(self.decoder.parameters()),
            lr=0.001
        )
        
        logger.info(f"初始化隐式通信通道，智能体数量: {num_agents}, 消息大小: {message_size}")
    
    def encode_message(
        self,
        state_embedding: torch.Tensor,
        prev_message: Optional[torch.Tensor] = None,
        agent_id: int = 0
    ) -> torch.Tensor:
        """
        从状态嵌入中编码隐式消息
        
        Args:
            state_embedding: 状态嵌入张量 [batch_size, embedding_size]
            prev_message: 上一步的消息 [batch_size, message_size]
            agent_id: 智能体ID
            
        Returns:
            隐式消息张量 [batch_size, message_size]
        """
        # 确保输入在正确的设备上
        state_embedding = state_embedding.to(self.device)
        
        # 如果没有上一步消息，创建零张量
        if prev_message is None:
            batch_size = state_embedding.shape[0]
            prev_message = torch.zeros(batch_size, self.message_size, device=self.device)
        else:
            prev_message = prev_message.to(self.device)
        
        # 连接状态嵌入和上一步消息
        combined = torch.cat([state_embedding, prev_message], dim=-1)
        
        # 编码消息
        message = self.encoder(combined)
        
        # 使用tanh激活函数将消息范围限制在[-1, 1]
        message = torch.tanh(message)
        
        # 记录消息历史
        if len(self.state_history[agent_id]) < 100:  # 限制历史长度
            self.state_history[agent_id].append(state_embedding.detach().cpu())
            self.message_history[agent_id].append(message.detach().cpu())
        
        return message
    
    def decode_message(self, message: torch.Tensor) -> torch.Tensor:
        """
        解码接收到的隐式消息
        
        Args:
            message: 消息张量 [batch_size, message_size]
            
        Returns:
            解码后的状态表示 [batch_size, embedding_size]
        """
        # 确保输入在正确的设备上
        message = message.to(self.device)
        
        # 解码消息
        decoded = self.decoder(message)
        
        return decoded
    
    def interpret_action(
        self,
        action: Union[Action, torch.Tensor, np.ndarray],
        state: Optional[Union[State, torch.Tensor, np.ndarray]] = None,
        agent_id: int = 0
    ) -> torch.Tensor:
        """
        解释其他智能体的动作，提取隐含的通信信号
        
        Args:
            action: 动作
            state: 动作执行时的状态
            agent_id: 执行动作的智能体ID
            
        Returns:
            隐式消息的解释 [batch_size, message_size]
        """
        # 将动作转换为张量
        if isinstance(action, Action):
            # 需要根据具体的Action类实现转换逻辑
            action_tensor = torch.FloatTensor([0.0] * self.message_size)  # 占位实现
        elif isinstance(action, np.ndarray):
            action_tensor = torch.from_numpy(action).float()
        else:
            action_tensor = action
        
        # 确保输入在正确的设备上
        action_tensor = action_tensor.to(self.device)
        
        # 记录动作历史
        if len(self.action_history[agent_id]) < 100:  # 限制历史长度
            self.action_history[agent_id].append(action_tensor.detach().cpu())
        
        # 简单的线性投影作为占位实现
        # 实际实现应当基于状态和动作的组合，或使用更复杂的模型
        message = action_tensor[:self.message_size] if action_tensor.numel() >= self.message_size else torch.zeros(self.message_size, device=self.device)
        message = torch.tanh(message)
        
        return message
    
    def update(
        self,
        state_embeddings: List[torch.Tensor],
        actions: List[torch.Tensor],
        rewards: torch.Tensor,
        teammates_rewards: Optional[torch.Tensor] = None
    ) -> Dict[str, float]:
        """
        更新通信通道模型
        
        Args:
            state_embeddings: 各智能体的状态嵌入列表
            actions: 各智能体的动作列表
            rewards: 智能体的奖励
            teammates_rewards: 队友的奖励（可选）
            
        Returns:
            包含各种损失的字典
        """
        # 确保输入在正确的设备上
        state_embeddings = [emb.to(self.device) for emb in state_embeddings]
        actions = [act.to(self.device) for act in actions]
        rewards = rewards.to(self.device)
        
        # 如果提供了队友奖励，使用它进行学习，否则使用自己的奖励
        if teammates_rewards is not None:
            teammates_rewards = teammates_rewards.to(self.device)
        else:
            teammates_rewards = rewards
        
        # 计算消息
        messages = []
        for i, state_emb in enumerate(state_embeddings):
            message = self.encode_message(state_emb, agent_id=i)
            messages.append(message)
        
        # 每个智能体解码其他智能体的消息
        decoded_messages = []
        for i in range(self.num_agents):
            for j in range(self.num_agents):
                if i != j:  # 不解码自己的消息
                    decoded = self.decode_message(messages[j])
                    decoded_messages.append(decoded)
        
        # 计算重建损失（自己的状态嵌入与解码后的消息之间的差异）
        reconstruction_loss = 0.0
        for i, state_emb in enumerate(state_embeddings):
            for decoded in decoded_messages:
                reconstruction_loss += F.mse_loss(decoded, state_emb)
        
        # 计算奖励相关损失（如果队友获得更高奖励，则增强通信）
        reward_loss = 0.0
        if teammates_rewards.numel() > 0:
            # 激励那些与高奖励相关的消息
            for i, message in enumerate(messages):
                reward_weight = torch.sigmoid(teammates_rewards)
                reward_loss -= torch.mean(message.abs() * reward_weight)
        
        # 计算稀疏性损失（鼓励消息更加简洁）
        sparsity_loss = sum(torch.mean(message.abs()) for message in messages)
        
        # 总损失
        total_loss = reconstruction_loss + 0.1 * reward_loss + 0.01 * sparsity_loss
        
        # 优化器更新
        self.optimizer.zero_grad()
        total_loss.backward()
        self.optimizer.step()
        
        return {
            "reconstruction_loss": reconstruction_loss.item(),
            "reward_loss": reward_loss.item(),
            "sparsity_loss": sparsity_loss.item(),
            "total_loss": total_loss.item()
        }
    
    def save(self, path: str) -> None:
        """
        保存通信通道模型
        
        Args:
            path: 保存路径
        """
        state_dict = {
            "encoder": self.encoder.state_dict(),
            "decoder": self.decoder.state_dict(),
            "optimizer": self.optimizer.state_dict(),
            "embedding_size": self.embedding_size,
            "num_agents": self.num_agents,
            "message_size": self.message_size,
            "hidden_size": self.hidden_size
        }
        torch.save(state_dict, path)
        logger.info(f"隐式通信通道模型已保存到 {path}")
    
    def load(self, path: str) -> None:
        """
        加载通信通道模型
        
        Args:
            path: 加载路径
        """
        try:
            state_dict = torch.load(path, map_location=self.device)
            
            # 检查配置是否匹配
            if (state_dict["embedding_size"] == self.embedding_size and
                state_dict["message_size"] == self.message_size and
                state_dict["hidden_size"] == self.hidden_size):
                
                self.encoder.load_state_dict(state_dict["encoder"])
                self.decoder.load_state_dict(state_dict["decoder"])
                self.optimizer.load_state_dict(state_dict["optimizer"])
                self.num_agents = state_dict["num_agents"]
                
                logger.info(f"隐式通信通道模型已从 {path} 加载")
            else:
                logger.warning(f"配置不匹配，无法加载模型: {path}")
        except Exception as e:
            logger.error(f"加载模型失败: {e}")


class MultiAgentImplicitCommunication:
    """
    多智能体隐式通信
    
    管理多个智能体之间的隐式通信，包括消息传递和解释。
    """
    
    def __init__(
        self,
        num_agents: int = 2,
        embedding_size: int = 128,
        message_size: int = 64,
        communication_type: str = "action_based",
        device: str = 'cuda' if torch.cuda.is_available() else 'cpu'
    ):
        """
        初始化多智能体隐式通信
        
        Args:
            num_agents: 智能体数量
            embedding_size: 状态嵌入的维度
            message_size: 消息嵌入的维度
            communication_type: 通信类型（"action_based"或"embedding_based"）
            device: 计算设备
        """
        self.num_agents = num_agents
        self.embedding_size = embedding_size
        self.message_size = message_size
        self.communication_type = communication_type
        self.device = device
        
        # 创建通信通道
        self.communication_channel = ImplicitCommunicationChannel(
            embedding_size=embedding_size,
            num_agents=num_agents,
            message_size=message_size,
            device=device
        )
        
        # 消息缓冲区（存储智能体之间最近的消息）
        self.message_buffer = [torch.zeros(message_size, device=device) for _ in range(num_agents)]
        
        logger.info(f"初始化多智能体隐式通信，智能体数量: {num_agents}, 通信类型: {communication_type}")
    
    def send_message(
        self,
        agent_id: int,
        state_embedding: torch.Tensor,
        action: Optional[torch.Tensor] = None
    ) -> torch.Tensor:
        """
        发送隐式消息
        
        Args:
            agent_id: 发送消息的智能体ID
            state_embedding: 状态嵌入
            action: 智能体执行的动作（可选）
            
        Returns:
            编码后的消息
        """
        message = None
        
        if self.communication_type == "embedding_based":
            # 基于状态嵌入的通信
            prev_message = self.message_buffer[agent_id]
            message = self.communication_channel.encode_message(
                state_embedding,
                prev_message,
                agent_id
            )
        elif self.communication_type == "action_based":
            # 基于动作的通信
            if action is not None:
                message = self.communication_channel.interpret_action(
                    action,
                    state_embedding,
                    agent_id
                )
            else:
                # 如果没有提供动作，则使用基于嵌入的方法
                prev_message = self.message_buffer[agent_id]
                message = self.communication_channel.encode_message(
                    state_embedding,
                    prev_message,
                    agent_id
                )
        
        # 更新消息缓冲区
        self.message_buffer[agent_id] = message
        
        return message
    
    def receive_message(self, agent_id: int) -> List[torch.Tensor]:
        """
        接收其他智能体的消息
        
        Args:
            agent_id: 接收消息的智能体ID
            
        Returns:
            其他智能体的消息列表
        """
        # 获取除了自己以外的所有智能体的消息
        messages = [self.message_buffer[i] for i in range(self.num_agents) if i != agent_id]
        return messages
    
    def decode_message(self, message: torch.Tensor) -> torch.Tensor:
        """
        解码接收到的消息
        
        Args:
            message: 接收到的消息
            
        Returns:
            解码后的状态表示
        """
        return self.communication_channel.decode_message(message)
    
    def integrate_messages(
        self,
        agent_id: int,
        state_embedding: torch.Tensor
    ) -> torch.Tensor:
        """
        整合接收到的消息到智能体的状态表示
        
        Args:
            agent_id: 智能体ID
            state_embedding: 原始状态嵌入
            
        Returns:
            整合了其他智能体消息的状态表示
        """
        # 确保输入在正确的设备上
        state_embedding = state_embedding.to(self.device)
        
        # 接收其他智能体的消息
        messages = self.receive_message(agent_id)
        
        if not messages:
            return state_embedding
        
        # 解码所有消息
        decoded_messages = [self.decode_message(msg) for msg in messages]
        
        # 计算解码消息的平均值
        avg_message = torch.mean(torch.stack(decoded_messages), dim=0)
        
        # 将原始状态嵌入与解码消息整合
        # 此处使用简单的加权和，可以替换为更复杂的融合机制
        alpha = 0.3  # 解码消息的权重
        integrated_embedding = (1 - alpha) * state_embedding + alpha * avg_message
        
        return integrated_embedding
    
    def update(
        self,
        state_embeddings: List[torch.Tensor],
        actions: List[torch.Tensor],
        rewards: torch.Tensor,
        teammates_rewards: Optional[torch.Tensor] = None
    ) -> Dict[str, float]:
        """
        更新隐式通信模型
        
        Args:
            state_embeddings: 各智能体的状态嵌入列表
            actions: 各智能体的动作列表
            rewards: 智能体的奖励
            teammates_rewards: 队友的奖励（可选）
            
        Returns:
            包含各种损失的字典
        """
        return self.communication_channel.update(
            state_embeddings,
            actions,
            rewards,
            teammates_rewards
        )
    
    def save(self, path: str) -> None:
        """
        保存隐式通信模型
        
        Args:
            path: 保存路径
        """
        self.communication_channel.save(path)
    
    def load(self, path: str) -> None:
        """
        加载隐式通信模型
        
        Args:
            path: 加载路径
        """
        self.communication_channel.load(path) 