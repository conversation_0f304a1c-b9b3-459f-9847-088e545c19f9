"""
测试DeploymentManager中command变量未定义问题的修复

该测试验证_launch_training方法能够正确设置result['command']，
使用实际执行的cmd_args而不是未定义的command变量。
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import tempfile
import shutil
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path


class MockUtils:
    """模拟工具类"""
    def __init__(self, is_windows=False):
        self._is_windows = is_windows
    
    def is_windows(self):
        return self._is_windows


class MockLogger:
    """模拟日志记录器"""
    def __init__(self):
        self.messages = []
    
    def info(self, msg):
        self.messages.append(('INFO', msg))
    
    def warning(self, msg):
        self.messages.append(('WARNING', msg))
    
    def error(self, msg):
        self.messages.append(('ERROR', msg))


def test_command_variable_fix():
    """测试command变量修复"""
    print("=== 测试DeploymentManager command变量修复 ===\n")
    
    # 导入DeploymentManager
    from cardgame_ai.zhuchengxu.auto_config.deployment_manager import DeploymentManager
    
    # 创建临时目录和文件
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建模拟的配置文件
        config_path = os.path.join(temp_dir, "test_config.yaml")
        with open(config_path, 'w', encoding='utf-8') as f:
            f.write("test: config")
        
        # 创建模拟的训练脚本
        script_path = os.path.join(temp_dir, "test_script.py")
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write("print('test script')")
        
        # 测试1: Windows环境
        print("测试1: Windows环境下的command设置")
        try:
            # 创建DeploymentManager实例
            manager = DeploymentManager()
            manager.utils = MockUtils(is_windows=True)
            manager.logger = MockLogger()
            
            # 模拟subprocess.Popen
            with patch('subprocess.Popen') as mock_popen:
                mock_process = Mock()
                mock_process.pid = 12345
                mock_popen.return_value = mock_process
                
                # 模拟open函数
                with patch('builtins.open', create=True) as mock_open:
                    mock_file = Mock()
                    mock_open.return_value.__enter__.return_value = mock_file
                    
                    # 调用_launch_training方法
                    result = manager._launch_training(config_path, script_path)
                    
                    # 验证结果
                    assert result['success'] == True, f"启动应该成功，但结果: {result}"
                    assert 'command' in result, "结果中应该包含command字段"
                    
                    # 验证command字段包含正确的内容
                    command = result['command']
                    assert sys.executable in command, f"command应该包含Python可执行文件路径: {command}"
                    assert script_path in command, f"command应该包含脚本路径: {command}"
                    assert config_path in command, f"command应该包含配置文件路径: {command}"
                    assert '--config' in command, f"command应该包含--config参数: {command}"
                    
                    print(f"✓ Windows测试通过，command: {command}")
        
        except Exception as e:
            print(f"✗ Windows测试失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 测试2: Linux环境
        print("\n测试2: Linux环境下的command设置")
        try:
            # 创建DeploymentManager实例
            manager = DeploymentManager()
            manager.utils = MockUtils(is_windows=False)
            manager.logger = MockLogger()
            
            # 模拟subprocess.Popen
            with patch('subprocess.Popen') as mock_popen:
                mock_process = Mock()
                mock_process.pid = 54321
                mock_popen.return_value = mock_process
                
                # 模拟open函数
                with patch('builtins.open', create=True) as mock_open:
                    mock_file = Mock()
                    mock_open.return_value.__enter__.return_value = mock_file
                    
                    # 调用_launch_training方法
                    result = manager._launch_training(config_path, script_path)
                    
                    # 验证结果
                    assert result['success'] == True, f"启动应该成功，但结果: {result}"
                    assert 'command' in result, "结果中应该包含command字段"
                    
                    # 验证command字段包含正确的内容
                    command = result['command']
                    assert sys.executable in command, f"command应该包含Python可执行文件路径: {command}"
                    assert script_path in command, f"command应该包含脚本路径: {command}"
                    assert config_path in command, f"command应该包含配置文件路径: {command}"
                    assert '--config' in command, f"command应该包含--config参数: {command}"
                    
                    print(f"✓ Linux测试通过，command: {command}")
        
        except Exception as e:
            print(f"✗ Linux测试失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 测试3: 验证command格式正确性
        print("\n测试3: 验证command格式正确性")
        try:
            manager = DeploymentManager()
            manager.utils = MockUtils(is_windows=True)
            manager.logger = MockLogger()
            
            with patch('subprocess.Popen') as mock_popen:
                mock_process = Mock()
                mock_process.pid = 99999
                mock_popen.return_value = mock_process
                
                with patch('builtins.open', create=True) as mock_open:
                    mock_file = Mock()
                    mock_open.return_value.__enter__.return_value = mock_file
                    
                    result = manager._launch_training(config_path, script_path)
                    
                    # 验证command可以被正确解析
                    command = result['command']
                    command_parts = command.split()
                    
                    # 应该至少包含4个部分：python, script, --config, config_path
                    assert len(command_parts) >= 4, f"command应该至少包含4个部分，实际: {command_parts}"
                    assert command_parts[-2] == '--config', f"倒数第二个参数应该是--config，实际: {command_parts[-2]}"
                    assert command_parts[-1] == config_path, f"最后一个参数应该是配置文件路径，实际: {command_parts[-1]}"
                    
                    print(f"✓ command格式验证通过: {command_parts}")
        
        except Exception as e:
            print(f"✗ command格式验证失败: {e}")
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    print("=== 测试DeploymentManager command变量修复 ===\n")
    
    try:
        test_command_variable_fix()
        
        print("\n=== 所有测试完成 ===")
        print("\n修复总结:")
        print("- 修复了第425行引用未定义command变量的问题")
        print("- 使用' '.join(cmd_args)替代未定义的command变量")
        print("- 删除了未使用的command变量定义（第358-361行）")
        print("- 确保result['command']包含实际执行的命令")
        print("- 符合fail-fast原则：使用实际执行的命令而不是构造的字符串")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
