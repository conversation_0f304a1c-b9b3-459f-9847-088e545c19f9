"""
关键决策点数据集模块

提供加载和处理关键决策点标注数据的功能，用于训练关键决策点检测器。
"""

import os
import json
import logging
import numpy as np
import torch
from typing import Dict, List, Tuple, Any, Optional, Union
from torch.utils.data import Dataset, DataLoader

from cardgame_ai.core.base import State
from cardgame_ai.games.doudizhu.state import DouDizhuState
from cardgame_ai.games.common.feature_extractor import FeatureExtractor, CardGameFeatureExtractor

# 配置日志
logger = logging.getLogger(__name__)


class KeyMomentDataset(Dataset):
    """
    关键决策点数据集

    加载和处理关键决策点标注数据，用于训练关键决策点检测器。
    """

    def __init__(
        self,
        data_dir: str,
        feature_extractor: Optional[FeatureExtractor] = None,
        use_history: bool = True,
        history_length: int = 5,
        transform=None
    ):
        """
        初始化数据集

        Args:
            data_dir: 数据目录路径
            feature_extractor: 特征提取器，可选
            use_history: 是否使用历史状态
            history_length: 历史状态长度
            transform: 数据转换函数
        """
