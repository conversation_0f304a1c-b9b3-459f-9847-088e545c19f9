"""
风险敏感决策示例

演示如何使用CVaR风险敏感决策进行训练和推理。
"""

import os
import torch
import numpy as np
import matplotlib.pyplot as plt
from cardgame_ai.algorithms.efficient_zero import EfficientZero
from cardgame_ai.games.doudizhu import DouDizhuEnvironment
from cardgame_ai.algorithms.risk_sensitive_rl import CVaRCalculator


def plot_value_distribution(value_distribution, title="价值分布"):
    """绘制价值分布"""
    plt.figure(figsize=(10, 6))
    plt.bar(range(len(value_distribution)), value_distribution)
    plt.title(title)
    plt.xlabel("价值")
    plt.ylabel("概率")
    plt.grid(True)
    plt.savefig(f"{title}.png")
    plt.close()


def compare_risk_profiles(model, state, risk_betas=[0.0, 0.1, 0.3, 0.5, 0.7, 0.9]):
    """比较不同风险厌恶系数下的决策"""
    # 转换状态为张量
    state_tensor = torch.FloatTensor(state).unsqueeze(0).to(model.device)
    
    # 获取隐藏状态
    hidden_state = model.model.representation_network(state_tensor)
    
    # 获取价值分布
    value_logits = model.model.distributional_value_head(hidden_state)
    
    # 计算概率分布
    probs = torch.softmax(value_logits, dim=-1)
    
    # 获取价值支持
    value_support = model.model.distributional_value_head.value_support
    
    # 绘制价值分布
    plot_value_distribution(
        probs.squeeze().detach().cpu().numpy(),
        title="价值分布"
    )
    
    # 比较不同风险厌恶系数下的决策
    results = []
    for beta in risk_betas:
        # 计算风险敏感价值
        risk_value = model.model.distributional_value_head.compute_risk_sensitive_value(
            value_logits,
            alpha=0.05,  # 固定alpha
            beta=beta
        ).item()
        
        # 计算CVaR
        cvar = model.model.distributional_value_head.compute_cvar(
            value_logits,
            alpha=0.05
        ).item()
        
        # 计算期望价值
        expected_value = model.model.distributional_value_head.compute_expected_value(
            value_logits
        ).item()
        
        # 记录结果
        results.append({
            'beta': beta,
            'risk_value': risk_value,
            'cvar': cvar,
            'expected_value': expected_value
        })
    
    # 打印结果
    print("\n不同风险厌恶系数下的决策比较:")
    print("-" * 60)
    print(f"{'风险厌恶系数(β)':<15}{'风险敏感价值':<15}{'CVaR(α=0.05)':<15}{'期望价值':<15}")
    print("-" * 60)
    for result in results:
        print(f"{result['beta']:<15.2f}{result['risk_value']:<15.2f}{result['cvar']:<15.2f}{result['expected_value']:<15.2f}")
    
    # 绘制风险敏感价值随beta变化的曲线
    plt.figure(figsize=(10, 6))
    betas = [result['beta'] for result in results]
    risk_values = [result['risk_value'] for result in results]
    plt.plot(betas, risk_values, 'o-', label='风险敏感价值')
    plt.axhline(y=results[0]['expected_value'], color='r', linestyle='--', label='期望价值')
    plt.axhline(y=results[0]['cvar'], color='g', linestyle='--', label='CVaR(α=0.05)')
    plt.title("风险敏感价值随风险厌恶系数变化")
    plt.xlabel("风险厌恶系数(β)")
    plt.ylabel("价值")
    plt.legend()
    plt.grid(True)
    plt.savefig("风险敏感价值随beta变化.png")
    plt.close()
    
    return results


def compare_mcts_decisions(model, state, risk_betas=[0.0, 0.5, 0.9]):
    """比较不同风险厌恶系数下的MCTS决策"""
    results = []
    
    for beta in risk_betas:
        # 更新模型的风险厌恶系数
        model.risk_beta = beta
        model.model.risk_beta = beta
        
        # 重新创建MCTS搜索器
        model.mcts = model._create_mcts(
            use_risk_sensitive_decision=True,
            risk_alpha=0.05,
            risk_beta=beta
        )
        
        # 运行MCTS
        visit_counts, action_probs = model.mcts.run(
            root_state=state,
            model=model.model,
            temperature=1.0
        )
        
        # 记录结果
        results.append({
            'beta': beta,
            'visit_counts': visit_counts,
            'action_probs': action_probs
        })
    
    # 打印结果
    print("\n不同风险厌恶系数下的MCTS决策比较:")
    print("-" * 60)
    print(f"{'风险厌恶系数(β)':<15}{'前3个动作及其访问次数':<45}")
    print("-" * 60)
    for result in results:
        # 获取访问次数最多的前3个动作
        top_actions = sorted(result['visit_counts'].items(), key=lambda x: x[1], reverse=True)[:3]
        action_str = ", ".join([f"动作{a}:{c}" for a, c in top_actions])
        print(f"{result['beta']:<15.2f}{action_str:<45}")
    
    return results


def main():
    """主函数"""
    # 创建环境
    env = DouDizhuEnvironment()
    
    # 获取状态和动作空间形状
    state_shape = env.observation_space.shape
    action_shape = (env.action_space.n,)
    
    # 创建EfficientZero模型
    model = EfficientZero(
        state_shape=state_shape,
        action_shape=action_shape,
        hidden_dim=128,
        state_dim=64,
        use_distributional_value=True,  # 启用分布式价值头
        value_support_size=51,
        value_min=-5.0,
        value_max=5.0,
        risk_alpha=0.05,
        risk_beta=0.3
    )
    
    # 重置环境
    state = env.reset()
    
    # 比较不同风险厌恶系数下的决策
    value_results = compare_risk_profiles(model, state)
    
    # 比较不同风险厌恶系数下的MCTS决策
    mcts_results = compare_mcts_decisions(model, state)
    
    print("\n风险敏感决策示例完成！")
    print("生成的图表已保存到当前目录。")


if __name__ == "__main__":
    main()
