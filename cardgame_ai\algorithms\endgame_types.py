"""
残局类型定义和识别模块

定义斗地主游戏中的各种残局类型，并提供识别这些残局类型的功能。
"""

from enum import Enum, auto
from typing import Dict, List, Optional, Tuple, Set
import logging

from cardgame_ai.core.base import State
from cardgame_ai.games.doudizhu.state import DouDizhuState
from cardgame_ai.games.doudizhu.card import Card, CardRank, CardSuit
from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType


class EndgameType(Enum):
    """残局类型枚举"""
    KING_BOMB = auto()           # 王炸残局
    SINGLE_CARD_CONTROL = auto()  # 单张控制残局
    BOMB_ENDGAME = auto()         # 炸弹残局
    STRAIGHT_CONTROL = auto()     # 顺子控制残局
    PAIR_CONTROL = auto()         # 对子控制残局
    TRIO_CONTROL = auto()         # 三张控制残局
    AIRPLANE_CONTROL = auto()     # 飞机控制残局
    FINAL_ATTACK = auto()         # 最后攻击残局（手牌数量≤3）
    GENERAL_ENDGAME = auto()      # 一般残局


def is_endgame(state: State) -> bool:
    """
    检测游戏是否处于残局状态
    
    残局通常是指游戏接近结束，玩家手牌较少的状态。
    
    Args:
        state: 游戏状态
        
    Returns:
        bool: 是否处于残局状态
    """
    if not isinstance(state, DouDizhuState):
        return False
    
    # 检查是否有玩家手牌数量较少（小于等于8张）
    for hand in state.hands:
        if 0 < len(hand) <= 8:
            return True
    
    return False


def get_endgame_type(state: State) -> Optional[EndgameType]:
    """
    获取残局类型
    
    Args:
        state: 游戏状态
        
    Returns:
        Optional[EndgameType]: 残局类型，如果不是残局则返回None
    """
    if not is_endgame(state):
        return None
    
    if not isinstance(state, DouDizhuState):
        return None
    
    # 检查是否是最后攻击残局（手牌数量≤3）
    if _is_final_attack(state):
        return EndgameType.FINAL_ATTACK
    
    # 检查是否是王炸残局
    if _is_king_bomb_scenario(state):
        return EndgameType.KING_BOMB
    
    # 检查是否是单张控制残局
    if _is_single_card_control(state):
        return EndgameType.SINGLE_CARD_CONTROL
    
    # 检查是否是炸弹残局
    if _is_bomb_endgame(state):
        return EndgameType.BOMB_ENDGAME
    
    # 检查是否是顺子控制残局
    if _is_straight_control(state):
        return EndgameType.STRAIGHT_CONTROL
    
    # 检查是否是对子控制残局
    if _is_pair_control(state):
        return EndgameType.PAIR_CONTROL
    
    # 检查是否是三张控制残局
    if _is_trio_control(state):
        return EndgameType.TRIO_CONTROL
    
    # 检查是否是飞机控制残局
    if _is_airplane_control(state):
        return EndgameType.AIRPLANE_CONTROL
    
    # 默认为一般残局
    return EndgameType.GENERAL_ENDGAME


def _is_king_bomb_scenario(state: DouDizhuState) -> bool:
    """
    检测是否是王炸残局场景
    
    王炸残局场景是指有玩家手中有大小王，且游戏处于残局状态。
    
    Args:
        state: 游戏状态
        
    Returns:
        bool: 是否是王炸残局场景
    """
    # 检查每个玩家的手牌
    for hand in state.hands:
        # 检查是否有大小王
        has_small_joker = False
        has_big_joker = False
        
        for card in hand:
            if card.rank == CardRank.SMALL_JOKER:
                has_small_joker = True
            elif card.rank == CardRank.BIG_JOKER:
                has_big_joker = True
        
        # 如果有大小王，则是王炸残局场景
        if has_small_joker and has_big_joker:
            return True
    
    return False


def _is_single_card_control(state: DouDizhuState) -> bool:
    """
    检测是否是单张控制残局
    
    单张控制残局是指游戏进入最后阶段，玩家手中主要是单张牌，且牌的数量较少。
    
    Args:
        state: 游戏状态
        
    Returns:
        bool: 是否是单张控制残局
    """
    # 检查每个玩家的手牌
    for hand in state.hands:
        if len(hand) <= 5:
            # 统计单张牌的数量
            rank_count = {}
            
            for card in hand:
                if card.rank not in rank_count:
                    rank_count[card.rank] = 0
                rank_count[card.rank] += 1
            
            # 计算单张牌的比例
            single_count = sum(1 for count in rank_count.values() if count == 1)
            single_ratio = single_count / len(hand) if hand else 0
            
            # 如果单张牌比例超过60%，则认为是单张控制残局
            if single_ratio >= 0.6:
                return True
    
    return False


def _is_bomb_endgame(state: DouDizhuState) -> bool:
    """
    检查是否是炸弹残局
    
    炸弹残局是指有玩家手中有炸弹，且其他玩家手牌较少的情况。
    
    Args:
        state: 游戏状态
        
    Returns:
        bool: 是否是炸弹残局
    """
    # 检查每个玩家的手牌
    for i, hand in enumerate(state.hands):
        # 统计手牌中的炸弹数量
        bomb_count = 0
        rank_count = {}
        
        for card in hand:
            if card.rank not in rank_count:
                rank_count[card.rank] = 0
            rank_count[card.rank] += 1
        
        for rank, count in rank_count.items():
            if count == 4:
                bomb_count += 1
        
        # 如果有炸弹，且其他玩家手牌较少，则认为是炸弹残局
        if bomb_count > 0:
            other_players = [j for j in range(len(state.hands)) if j != i]
            for other_player in other_players:
                if len(state.hands[other_player]) <= 6:
                    return True
    
    return False


def _is_straight_control(state: DouDizhuState) -> bool:
    """
    检查是否是顺子控制残局
    
    顺子控制残局是指有玩家手中有顺子，且游戏处于残局状态。
    
    Args:
        state: 游戏状态
        
    Returns:
        bool: 是否是顺子控制残局
    """
    # 检查每个玩家的手牌
    for hand in state.hands:
        if len(hand) >= 5:  # 顺子至少需要5张牌
            # 按点数排序
            sorted_hand = sorted(hand, key=lambda card: card.rank.value)
            
            # 检查是否有连续的5张或更多的牌
            consecutive_count = 1
            max_consecutive = 1
            
            for i in range(1, len(sorted_hand)):
                if sorted_hand[i].rank.value == sorted_hand[i-1].rank.value + 1:
                    consecutive_count += 1
                    max_consecutive = max(max_consecutive, consecutive_count)
                else:
                    consecutive_count = 1
            
            # 如果有连续的5张或更多的牌，则认为是顺子控制残局
            if max_consecutive >= 5:
                return True
    
    return False


def _is_pair_control(state: DouDizhuState) -> bool:
    """
    检查是否是对子控制残局
    
    对子控制残局是指游戏进入最后阶段，玩家手中主要是对子，且牌的数量较少。
    
    Args:
        state: 游戏状态
        
    Returns:
        bool: 是否是对子控制残局
    """
    # 检查每个玩家的手牌
    for hand in state.hands:
        if len(hand) <= 8 and len(hand) % 2 == 0:  # 对子控制残局通常手牌数量是偶数
            # 统计对子的数量
            rank_count = {}
            
            for card in hand:
                if card.rank not in rank_count:
                    rank_count[card.rank] = 0
                rank_count[card.rank] += 1
            
            # 计算对子的比例
            pair_count = sum(1 for count in rank_count.values() if count == 2)
            pair_ratio = (pair_count * 2) / len(hand) if hand else 0
            
            # 如果对子比例超过60%，则认为是对子控制残局
            if pair_ratio >= 0.6:
                return True
    
    return False


def _is_trio_control(state: DouDizhuState) -> bool:
    """
    检查是否是三张控制残局
    
    三张控制残局是指游戏进入最后阶段，玩家手中主要是三张，且牌的数量较少。
    
    Args:
        state: 游戏状态
        
    Returns:
        bool: 是否是三张控制残局
    """
    # 检查每个玩家的手牌
    for hand in state.hands:
        if len(hand) <= 9 and len(hand) % 3 == 0:  # 三张控制残局通常手牌数量是3的倍数
            # 统计三张的数量
            rank_count = {}
            
            for card in hand:
                if card.rank not in rank_count:
                    rank_count[card.rank] = 0
                rank_count[card.rank] += 1
            
            # 计算三张的比例
            trio_count = sum(1 for count in rank_count.values() if count == 3)
            trio_ratio = (trio_count * 3) / len(hand) if hand else 0
            
            # 如果三张比例超过60%，则认为是三张控制残局
            if trio_ratio >= 0.6:
                return True
    
    return False


def _is_airplane_control(state: DouDizhuState) -> bool:
    """
    检查是否是飞机控制残局
    
    飞机控制残局是指有玩家手中有飞机（连续的三张），且游戏处于残局状态。
    
    Args:
        state: 游戏状态
        
    Returns:
        bool: 是否是飞机控制残局
    """
    # 检查每个玩家的手牌
    for hand in state.hands:
        if len(hand) >= 6:  # 飞机至少需要6张牌（两个三张）
            # 统计每个点数的牌数
            rank_count = {}
            
            for card in hand:
                if card.rank not in rank_count:
                    rank_count[card.rank] = 0
                rank_count[card.rank] += 1
            
            # 检查是否有连续的三张
            trios = [rank for rank, count in rank_count.items() if count == 3]
            trios.sort(key=lambda r: r.value)
            
            # 检查是否有连续的三张
            consecutive_count = 1
            max_consecutive = 1
            
            for i in range(1, len(trios)):
                if trios[i].value == trios[i-1].value + 1:
                    consecutive_count += 1
                    max_consecutive = max(max_consecutive, consecutive_count)
                else:
                    consecutive_count = 1
            
            # 如果有连续的两个或更多的三张，则认为是飞机控制残局
            if max_consecutive >= 2:
                return True
    
    return False


def _is_final_attack(state: DouDizhuState) -> bool:
    """
    检查是否是最后攻击残局
    
    最后攻击残局是指有玩家手牌数量非常少（小于等于3张），即将获胜的情况。
    
    Args:
        state: 游戏状态
        
    Returns:
        bool: 是否是最后攻击残局
    """
    # 检查每个玩家的手牌
    for hand in state.hands:
        if 0 < len(hand) <= 3:
            return True
    
    return False
