"""
高级表示学习模块

实现高级表示学习方法，提高AI对斗地主游戏状态的理解能力。
包括对比学习方法、多模态表示和层次化编码器等技术。
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Union

from cardgame_ai.algorithms.simsiam_loss import SimSiamLoss
from cardgame_ai.games.doudizhu.state import DouDizhuState, GamePhase
from cardgame_ai.games.doudizhu.card import Card, CardRank, CardSuit
from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType


class ContrastiveLearning(nn.Module):
    """
    对比学习模块

    实现基于InfoNCE的对比学习方法，通过区分相似和不相似的游戏状态，
    提高模型对游戏状态的理解能力。
    """

    def __init__(
        self,
        encoder: nn.Module,
        projection_dim: int = 128,
        temperature: float = 0.07,
        use_momentum_encoder: bool = True,
        momentum: float = 0.999
    ):
        """
        初始化对比学习模块

        Args:
            encoder: 编码器网络，将游戏状态编码为向量表示
            projection_dim: 投影维度
            temperature: 温度参数，控制softmax的平滑度
            use_momentum_encoder: 是否使用动量编码器
            momentum: 动量编码器的动量参数
        """
        super(ContrastiveLearning, self).__init__()

        self.encoder = encoder
        self.temperature = temperature
        self.use_momentum_encoder = use_momentum_encoder
        self.momentum = momentum

        # 投影头，将编码器输出投影到低维空间
        self.projector = nn.Sequential(
            nn.Linear(encoder.output_dim, encoder.output_dim),
            nn.ReLU(),
            nn.Linear(encoder.output_dim, projection_dim)
        )

        # 动量编码器和投影头（可选）
        if use_momentum_encoder:
            self.momentum_encoder = self._create_momentum_encoder()
            self.momentum_projector = self._create_momentum_projector()

            # 初始化动量编码器和投影头
            self._initialize_momentum_network()

            # 关闭动量编码器和投影头的梯度更新
            for param in self.momentum_encoder.parameters():
                param.requires_grad = False
            for param in self.momentum_projector.parameters():
                param.requires_grad = False

    def _create_momentum_encoder(self) -> nn.Module:
        """
        创建动量编码器

        Returns:
            动量编码器网络
        """
        # 创建与原编码器相同结构的网络
        return type(self.encoder)(**self.encoder.get_config())

    def _create_momentum_projector(self) -> nn.Module:
        """
        创建动量投影头

        Returns:
            动量投影头网络
        """
        # 创建与原投影头相同结构的网络
        return nn.Sequential(
            nn.Linear(self.encoder.output_dim, self.encoder.output_dim),
            nn.ReLU(),
            nn.Linear(self.encoder.output_dim, self.projector[2].out_features)
        )

    def _initialize_momentum_network(self):
        """
        初始化动量网络
        """
        # 将原编码器和投影头的参数复制到动量编码器和投影头
        for param_q, param_k in zip(self.encoder.parameters(), self.momentum_encoder.parameters()):
            param_k.data.copy_(param_q.data)

        for param_q, param_k in zip(self.projector.parameters(), self.momentum_projector.parameters()):
            param_k.data.copy_(param_q.data)

    def _update_momentum_network(self):
        """
        更新动量网络
        """
        # 使用动量更新动量编码器和投影头的参数
        for param_q, param_k in zip(self.encoder.parameters(), self.momentum_encoder.parameters()):
            param_k.data = param_k.data * self.momentum + param_q.data * (1. - self.momentum)

        for param_q, param_k in zip(self.projector.parameters(), self.momentum_projector.parameters()):
            param_k.data = param_k.data * self.momentum + param_q.data * (1. - self.momentum)

    def forward(self, x: torch.Tensor, x_aug: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播

        Args:
            x: 原始游戏状态
            x_aug: 增强后的游戏状态（正样本）

        Returns:
            原始状态和增强状态的投影表示
        """
        # 编码原始状态
        q = self.encoder(x)
        q = self.projector(q)
        q = F.normalize(q, dim=1)

        # 使用动量编码器编码增强状态（如果启用）
        if self.use_momentum_encoder:
            with torch.no_grad():
                self._update_momentum_network()
                k = self.momentum_encoder(x_aug)
                k = self.momentum_projector(k)
                k = F.normalize(k, dim=1)
        else:
            # 使用原编码器编码增强状态
            k = self.encoder(x_aug)
            k = self.projector(k)
            k = F.normalize(k, dim=1)

        return q, k

    def compute_loss(self, q: torch.Tensor, k: torch.Tensor, batch_size: Optional[int] = None) -> torch.Tensor:
        """
        计算InfoNCE对比学习损失

        Args:
            q: 查询表示（原始状态的投影）
            k: 键表示（增强状态的投影）
            batch_size: 批次大小，默认为None（使用实际批次大小）

        Returns:
            InfoNCE损失值
        """
        # 如果没有指定批次大小，使用实际批次大小
        if batch_size is None:
            batch_size = q.shape[0]

        # 计算相似度矩阵
        # [batch_size, batch_size]
        sim_matrix = torch.mm(q, k.T) / self.temperature

        # 标签：对角线上的元素是正样本对
        labels = torch.arange(batch_size, device=sim_matrix.device)

        # 计算交叉熵损失
        loss = F.cross_entropy(sim_matrix, labels)

        return loss

    def augment_state(self, state: torch.Tensor, augmentation_type: str = 'random') -> torch.Tensor:
        """
        增强游戏状态

        Args:
            state: 原始游戏状态
            augmentation_type: 增强类型，可选'random'、'mask'、'permute'或'combine'

        Returns:
            增强后的游戏状态
        """
        if augmentation_type == 'random':
            # 随机选择一种增强方法
            aug_types = ['mask', 'permute', 'combine']
            augmentation_type = np.random.choice(aug_types)

        if augmentation_type == 'mask':
            # 随机遮罩一部分特征
            mask = torch.rand_like(state) > 0.2  # 80%的特征保持不变
            return state * mask

        elif augmentation_type == 'permute':
            # 随机打乱一部分特征的顺序
            if len(state.shape) > 1 and state.shape[1] > 10:
                # 对于序列数据，随机打乱一部分序列的顺序
                perm_idx = torch.randperm(state.shape[1])
                return state[:, perm_idx]
            else:
                # 对于非序列数据，使用遮罩增强
                return self.augment_state(state, 'mask')

        elif augmentation_type == 'combine':
            # 结合多种增强方法
            state = self.augment_state(state, 'mask')
            state = self.augment_state(state, 'permute')
            return state

        else:
            # 默认不做增强
            return state


class MultiModalRepresentation(nn.Module):
    """
    多模态表示模块

    实现多模态表示学习，分别编码不同类型的信息（手牌、历史动作、公开信息等），
    然后通过多模态融合机制将它们组合成统一的表示。
    """

    def __init__(
        self,
        hand_cards_dim: int,
        history_dim: int,
        public_info_dim: int,
        fusion_dim: int = 256,
        fusion_method: str = 'attention',
        use_cross_modal_attention: bool = True
    ):
        """
        初始化多模态表示模块

        Args:
            hand_cards_dim: 手牌特征维度
            history_dim: 历史动作特征维度
            public_info_dim: 公开信息特征维度
            fusion_dim: 融合后的特征维度
            fusion_method: 融合方法，可选'concat'、'attention'或'gated'
            use_cross_modal_attention: 是否使用跨模态注意力
        """
        super(MultiModalRepresentation, self).__init__()

        self.fusion_method = fusion_method
        self.use_cross_modal_attention = use_cross_modal_attention
        self.output_dim = fusion_dim

        # 手牌编码器
        self.hand_cards_encoder = nn.Sequential(
            nn.Linear(hand_cards_dim, 128),
            nn.ReLU(),
            nn.Linear(128, 128)
        )

        # 历史动作编码器
        self.history_encoder = nn.Sequential(
            nn.Linear(history_dim, 128),
            nn.ReLU(),
            nn.Linear(128, 128)
        )

        # 公开信息编码器
        self.public_info_encoder = nn.Sequential(
            nn.Linear(public_info_dim, 64),
            nn.ReLU(),
            nn.Linear(64, 64)
        )

        # 根据融合方法创建相应的融合模块
        if fusion_method == 'concat':
            # 简单连接各模态特征
            concat_dim = 128 + 128 + 64  # 手牌 + 历史 + 公开信息
            self.fusion_layer = nn.Sequential(
                nn.Linear(concat_dim, fusion_dim),
                nn.ReLU(),
                nn.Linear(fusion_dim, fusion_dim)
            )

        elif fusion_method == 'attention':
            # 使用注意力机制融合各模态
            self.attention_dim = 128  # 注意力维度

            # 手牌注意力层
            self.hand_cards_attention = nn.Sequential(
                nn.Linear(128, self.attention_dim),
                nn.Tanh()
            )

            # 历史注意力层
            self.history_attention = nn.Sequential(
                nn.Linear(128, self.attention_dim),
                nn.Tanh()
            )

            # 公开信息注意力层
            self.public_info_attention = nn.Sequential(
                nn.Linear(64, self.attention_dim),
                nn.Tanh()
            )

            # 注意力权重向量
            self.attention_weights = nn.Parameter(torch.randn(self.attention_dim))

            # 融合层
            self.fusion_layer = nn.Sequential(
                nn.Linear(128 + 128 + 64, fusion_dim),  # 手牌 + 历史 + 公开信息
                nn.ReLU(),
                nn.Linear(fusion_dim, fusion_dim)
            )

        elif fusion_method == 'gated':
            # 使用门控机制融合各模态
            self.hand_cards_gate = nn.Sequential(
                nn.Linear(128, 1),
                nn.Sigmoid()
            )

            self.history_gate = nn.Sequential(
                nn.Linear(128, 1),
                nn.Sigmoid()
            )

            self.public_info_gate = nn.Sequential(
                nn.Linear(64, 1),
                nn.Sigmoid()
            )

            # 融合层
            self.fusion_layer = nn.Sequential(
                nn.Linear(128 + 128 + 64, fusion_dim),  # 手牌 + 历史 + 公开信息
                nn.ReLU(),
                nn.Linear(fusion_dim, fusion_dim)
            )

        # 跨模态注意力机制（可选）
        if use_cross_modal_attention:
            # 跨模态注意力层
            self.cross_modal_attention = nn.MultiheadAttention(
                embed_dim=fusion_dim,
                num_heads=4,
                dropout=0.1
            )

            # 层正规化
            self.layer_norm = nn.LayerNorm(fusion_dim)

    def forward(
        self,
        hand_cards: torch.Tensor,
        history: torch.Tensor,
        public_info: torch.Tensor
    ) -> torch.Tensor:
        """
        前向传播

        Args:
            hand_cards: 手牌特征
            history: 历史动作特征
            public_info: 公开信息特征

        Returns:
            融合后的多模态表示
        """
        # 编码各模态
        hand_cards_features = self.hand_cards_encoder(hand_cards)
        history_features = self.history_encoder(history)
        public_info_features = self.public_info_encoder(public_info)

        # 根据融合方法融合各模态
        if self.fusion_method == 'concat':
            # 简单连接各模态特征
            combined_features = torch.cat([hand_cards_features, history_features, public_info_features], dim=1)
            fused_features = self.fusion_layer(combined_features)

        elif self.fusion_method == 'attention':
            # 使用注意力机制融合各模态
            hand_cards_attention = self.hand_cards_attention(hand_cards_features)
            history_attention = self.history_attention(history_features)
            public_info_attention = self.public_info_attention(public_info_features)

            # 计算注意力分数
            hand_cards_score = torch.sum(hand_cards_attention * self.attention_weights, dim=1, keepdim=True)
            history_score = torch.sum(history_attention * self.attention_weights, dim=1, keepdim=True)
            public_info_score = torch.sum(public_info_attention * self.attention_weights, dim=1, keepdim=True)

            # 计算注意力权重
            attention_scores = torch.cat([hand_cards_score, history_score, public_info_score], dim=1)
            attention_weights = F.softmax(attention_scores, dim=1)

            # 加权平均
            hand_cards_weighted = hand_cards_features * attention_weights[:, 0:1]
            history_weighted = history_features * attention_weights[:, 1:2]
            public_info_weighted = public_info_features * attention_weights[:, 2:3]

            # 连接加权特征
            combined_features = torch.cat([hand_cards_weighted, history_weighted, public_info_weighted], dim=1)
            fused_features = self.fusion_layer(combined_features)

        elif self.fusion_method == 'gated':
            # 使用门控机制融合各模态
            hand_cards_gate_value = self.hand_cards_gate(hand_cards_features)
            history_gate_value = self.history_gate(history_features)
            public_info_gate_value = self.public_info_gate(public_info_features)

            # 门控特征
            hand_cards_gated = hand_cards_features * hand_cards_gate_value
            history_gated = history_features * history_gate_value
            public_info_gated = public_info_features * public_info_gate_value

            # 连接门控特征
            combined_features = torch.cat([hand_cards_gated, history_gated, public_info_gated], dim=1)
            fused_features = self.fusion_layer(combined_features)

        else:
            # 默认使用连接
            combined_features = torch.cat([hand_cards_features, history_features, public_info_features], dim=1)
            fused_features = self.fusion_layer(combined_features)

        # 应用跨模态注意力（如果启用）
        if self.use_cross_modal_attention:
            # 调整形状以适应MultiheadAttention的输入要求
            # 从 [batch_size, fusion_dim] 变为 [seq_len, batch_size, fusion_dim]
            fused_features_reshaped = fused_features.unsqueeze(0)  # [1, batch_size, fusion_dim]

            # 应用跨模态注意力
            attn_output, _ = self.cross_modal_attention(
                fused_features_reshaped,
                fused_features_reshaped,
                fused_features_reshaped
            )

            # 调整形状回原始形状
            attn_output = attn_output.squeeze(0)  # [batch_size, fusion_dim]

            # 添加残差连接和层正规化
            fused_features = self.layer_norm(fused_features + attn_output)

        return fused_features

    def get_config(self) -> Dict[str, Any]:
        """
        获取配置信息

        Returns:
            配置字典
        """
        return {
            'hand_cards_dim': self.hand_cards_encoder[0].in_features,
            'history_dim': self.history_encoder[0].in_features,
            'public_info_dim': self.public_info_encoder[0].in_features,
            'fusion_dim': self.output_dim,
            'fusion_method': self.fusion_method,
            'use_cross_modal_attention': self.use_cross_modal_attention
        }


class HierarchicalEncoder(nn.Module):
    """
    层次化编码器

    实现层次化编码器，使模型能够从低级特征（单牌）到高级特征（牌型、策略）
    逐步构建对游戏状态的理解。
    """

    def __init__(
        self,
        input_dim: int,
        card_embedding_dim: int = 32,
        pattern_embedding_dim: int = 64,
        strategy_embedding_dim: int = 128,
        output_dim: int = 256,
        num_card_types: int = 15,  # 3-17（A-2和大小王）
        num_card_patterns: int = 8,  # 单牌、对子、三张、顺子、连对、飞机、炸弹、火箭
        use_residual: bool = True
    ):
        """
        初始化层次化编码器

        Args:
            input_dim: 输入维度
            card_embedding_dim: 单牌嵌入维度
            pattern_embedding_dim: 牌型嵌入维度
            strategy_embedding_dim: 策略嵌入维度
            output_dim: 输出维度
            num_card_types: 牌的类型数量
            num_card_patterns: 牌型的类型数量
            use_residual: 是否使用残差连接
        """
        super(HierarchicalEncoder, self).__init__()

        self.input_dim = input_dim
        self.output_dim = output_dim
        self.use_residual = use_residual

        # 单牌编码器（第一层）
        self.card_encoder = nn.Sequential(
            nn.Linear(input_dim, 128),
            nn.ReLU(),
            nn.Linear(128, card_embedding_dim)
        )

        # 单牌嵌入层
        self.card_embedding = nn.Embedding(num_card_types, card_embedding_dim)

        # 牌型编码器（第二层）
        self.pattern_encoder = nn.Sequential(
            nn.Linear(card_embedding_dim, 128),
            nn.ReLU(),
            nn.Linear(128, pattern_embedding_dim)
        )

        # 牌型嵌入层
        self.pattern_embedding = nn.Embedding(num_card_patterns, pattern_embedding_dim)

        # 策略编码器（第三层）
        self.strategy_encoder = nn.Sequential(
            nn.Linear(pattern_embedding_dim, 128),
            nn.ReLU(),
            nn.Linear(128, strategy_embedding_dim)
        )

        # 最终输出层
        self.output_layer = nn.Sequential(
            nn.Linear(strategy_embedding_dim, output_dim),
            nn.ReLU()
        )

        # 注意力机制，用于关注重要的牌型和策略
        self.pattern_attention = nn.Sequential(
            nn.Linear(pattern_embedding_dim, 1),
            nn.Sigmoid()
        )

        self.strategy_attention = nn.Sequential(
            nn.Linear(strategy_embedding_dim, 1),
            nn.Sigmoid()
        )

    def forward(self, x: torch.Tensor, card_ids: Optional[torch.Tensor] = None, pattern_ids: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        前向传播

        Args:
            x: 输入特征
            card_ids: 牌的ID，如果提供，将使用嵌入层
            pattern_ids: 牌型的ID，如果提供，将使用嵌入层

        Returns:
            包含各层编码结果的字典
        """
        # 第一层：单牌编码
        card_features = self.card_encoder(x)

        # 如果提供了牌的ID，使用嵌入层
        if card_ids is not None:
            card_embeddings = self.card_embedding(card_ids)
            # 结合编码器和嵌入层的结果
            card_features = card_features + card_embeddings

        # 第二层：牌型编码
        pattern_features = self.pattern_encoder(card_features)

        # 如果提供了牌型的ID，使用嵌入层
        if pattern_ids is not None:
            pattern_embeddings = self.pattern_embedding(pattern_ids)
            # 结合编码器和嵌入层的结果
            pattern_features = pattern_features + pattern_embeddings

        # 应用牌型注意力
        pattern_attention_weights = self.pattern_attention(pattern_features)
        pattern_features_attended = pattern_features * pattern_attention_weights

        # 第三层：策略编码
        strategy_features = self.strategy_encoder(pattern_features_attended)

        # 应用策略注意力
        strategy_attention_weights = self.strategy_attention(strategy_features)
        strategy_features_attended = strategy_features * strategy_attention_weights

        # 最终输出层
        output_features = self.output_layer(strategy_features_attended)

        # 残差连接（如果启用）
        if self.use_residual:
            # 将策略特征与输出特征相加
            output_features = output_features + F.linear(
                strategy_features_attended,
                torch.zeros(self.output_dim, strategy_features_attended.size(-1), device=x.device),
                torch.zeros(self.output_dim, device=x.device)
            )

        # 返回各层的编码结果
        return {
            'card_features': card_features,
            'pattern_features': pattern_features,
            'pattern_attention': pattern_attention_weights,
            'strategy_features': strategy_features,
            'strategy_attention': strategy_attention_weights,
            'output_features': output_features
        }

    def get_config(self) -> Dict[str, Any]:
        """
        获取配置信息

        Returns:
            配置字典
        """
        return {
            'input_dim': self.input_dim,
            'card_embedding_dim': self.card_embedding.embedding_dim,
            'pattern_embedding_dim': self.pattern_embedding.embedding_dim,
            'strategy_embedding_dim': self.strategy_encoder[2].out_features,
            'output_dim': self.output_dim,
            'num_card_types': self.card_embedding.num_embeddings,
            'num_card_patterns': self.pattern_embedding.num_embeddings,
            'use_residual': self.use_residual
        }


def test_advanced_representation():
    """
    测试高级表示学习方法

    这个函数用于测试对比学习、多模态表示和层次化编码器的性能。
    """
    import time
    import torch
    import numpy as np
    from cardgame_ai.games.doudizhu.state import DouDizhuState
    from cardgame_ai.games.doudizhu.card import Card
    from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType

    # 设置随机种子以确保可重现性
    torch.manual_seed(42)
    np.random.seed(42)

    # 测试对比学习
    print("\n=== 测试对比学习 ===")

    # 创建一个简单的编码器
    class SimpleEncoder(nn.Module):
        def __init__(self, input_dim=54, output_dim=64):
            super(SimpleEncoder, self).__init__()
            self.output_dim = output_dim
            self.net = nn.Sequential(
                nn.Linear(input_dim, 128),
                nn.ReLU(),
                nn.Linear(128, output_dim)
            )

        def forward(self, x):
            return self.net(x)

        def get_config(self):
            return {'input_dim': self.net[0].in_features, 'output_dim': self.output_dim}

    # 创建对比学习模块
    encoder = SimpleEncoder(input_dim=54, output_dim=64)
    contrastive_learning = ContrastiveLearning(encoder=encoder, projection_dim=32)

    # 生成测试数据
    batch_size = 16
    input_data = torch.randn(batch_size, 54)  # 模拟手牌特征

    # 生成增强数据
    augmented_data = contrastive_learning.augment_state(input_data)

    # 前向传播
    start_time = time.time()
    q, k = contrastive_learning(input_data, augmented_data)
    forward_time = time.time() - start_time

    # 计算损失
    loss = contrastive_learning.compute_loss(q, k)

    print(f"\u8f93入形状: {input_data.shape}")
    print(f"\u67e5询表示形状: {q.shape}")
    print(f"\u952e表示形状: {k.shape}")
    print(f"\u5bf9比学习损失: {loss.item():.4f}")
    print(f"\u524d向传播时间: {forward_time:.4f} 秒")

    # 测试多模态表示
    print("\n=== 测试多模态表示 ===")

    # 创建多模态表示模块
    multi_modal = MultiModalRepresentation(
        hand_cards_dim=54,
        history_dim=100,
        public_info_dim=20,
        fusion_dim=128,
        fusion_method='attention',
        use_cross_modal_attention=True
    )

    # 生成测试数据
    hand_cards = torch.randn(batch_size, 54)  # 模拟手牌特征
    history = torch.randn(batch_size, 100)    # 模拟历史动作特征
    public_info = torch.randn(batch_size, 20)  # 模拟公开信息特征

    # 前向传播
    start_time = time.time()
    fused_features = multi_modal(hand_cards, history, public_info)
    forward_time = time.time() - start_time

    print(f"\u624b牌特征形状: {hand_cards.shape}")
    print(f"\u5386史特征形状: {history.shape}")
    print(f"\u516c开信息特征形状: {public_info.shape}")
    print(f"\u878d合特征形状: {fused_features.shape}")
    print(f"\u524d向传播时间: {forward_time:.4f} 秒")

    # 测试层次化编码器
    print("\n=== 测试层次化编码器 ===")

    # 创建层次化编码器
    hierarchical_encoder = HierarchicalEncoder(
        input_dim=54,
        card_embedding_dim=32,
        pattern_embedding_dim=64,
        strategy_embedding_dim=128,
        output_dim=256
    )

    # 生成测试数据
    input_features = torch.randn(batch_size, 54)  # 模拟手牌特征
    card_ids = torch.randint(0, 15, (batch_size,))  # 模拟牌的ID
    pattern_ids = torch.randint(0, 8, (batch_size,))  # 模拟牌型的ID

    # 前向传播
    start_time = time.time()
    hierarchical_features = hierarchical_encoder(input_features, card_ids, pattern_ids)
    forward_time = time.time() - start_time

    print(f"\u8f93入特征形状: {input_features.shape}")
    print(f"\u5355牌特征形状: {hierarchical_features['card_features'].shape}")
    print(f"\u724c型特征形状: {hierarchical_features['pattern_features'].shape}")
    print(f"\u7b56略特征形状: {hierarchical_features['strategy_features'].shape}")
    print(f"\u8f93出特征形状: {hierarchical_features['output_features'].shape}")
    print(f"\u724c型注意力形状: {hierarchical_features['pattern_attention'].shape}")
    print(f"\u7b56略注意力形状: {hierarchical_features['strategy_attention'].shape}")
    print(f"\u524d向传播时间: {forward_time:.4f} 秒")

    return {
        'contrastive_learning_loss': loss.item(),
        'multi_modal_fusion_shape': fused_features.shape,
        'hierarchical_encoder_output_shape': hierarchical_features['output_features'].shape
    }


if __name__ == "__main__":
    # 如果直接运行这个文件，则执行测试
    test_advanced_representation()
