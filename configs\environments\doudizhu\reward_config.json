{"description": "斗地主统一奖励配置", "version": "1.0", "base_rewards": {"win_reward": 1.0, "lose_reward": -1.0, "step_penalty": -0.001, "illegal_action_penalty": -0.1}, "bomb_rewards": {"bomb_base_reward": 0.05, "bomb_win_multiplier": 2.0, "bomb_lose_penalty": 2.5, "rocket_multiplier": 1.5, "context_aware_scaling": true, "endgame_bomb_multiplier": 1.5}, "role_specific": {"landlord": {"role_factor": 1.0, "aggressive_bonus": 0.02, "control_bonus": 0.01}, "farmer": {"role_factor": 0.9, "cooperation_weight": 0.7, "team_reward_weight": 0.8, "defensive_bonus": 0.01}}, "bidding_phase": {"bid_reward_weight": 0.2, "grab_reward_weight": 0.3, "landlord_bid_threshold": 0.6, "farmer_bid_threshold": 0.4, "hand_strength_scaling": true, "risk_adjustment": true}, "playing_phase": {"type_rewards": {"single": 0.01, "pair": 0.02, "triple": 0.03, "triple_with_single": 0.04, "triple_with_pair": 0.05, "straight": 0.05, "pair_straight": 0.06, "triple_straight": 0.08, "bomb": 0.1, "rocket": 0.15}, "combo_reward": 0.05, "progress_reward_factor": 1.5, "type_reward_factor": 1.2, "strategic_pass_reward": 0.02}, "cooperation_rewards": {"farmer_cooperation_weight": 0.7, "team_reward_weight": 0.8, "assist_bonus": 0.03, "block_landlord_bonus": 0.04, "sacrifice_play_bonus": 0.02}, "dynamic_adjustment": {"enable_dynamic_adjustment": true, "performance_window": 100, "adjustment_rate": 0.01, "min_adjustment_threshold": 0.05, "max_adjustment_factor": 1.5}, "context_aware": {"enable_context_aware": true, "endgame_threshold": 5, "critical_moment_multiplier": 2.0, "game_stage_weights": {"early": 0.8, "mid": 1.0, "late": 1.2}, "position_weights": {"first_to_play": 1.1, "last_to_play": 0.9}}, "advanced_features": {"information_value_reward": {"enabled": true, "weight": 0.1, "exploration_bonus": 0.02}, "opponent_modeling_reward": {"enabled": true, "prediction_accuracy_bonus": 0.03, "adaptation_bonus": 0.02}, "risk_sensitive_adjustment": {"enabled": true, "risk_aversion_factor": 0.1, "uncertainty_penalty": 0.01}}, "normalization": {"enable_reward_normalization": true, "normalization_method": "z_score", "clip_rewards": true, "reward_clip_range": [-5.0, 5.0]}, "logging": {"log_reward_components": true, "log_statistics_interval": 100, "save_reward_history": true, "detailed_analysis": false}}