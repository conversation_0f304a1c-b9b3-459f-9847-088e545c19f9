# 多GPU硬件配置模板
device:
  type: "cuda"
  ids: {{ GPU_IDS | to_json }}
  mixed_precision: true
  benchmark: true

memory:
  pin_memory: true
  non_blocking: true
  persistent_workers: true
  cache_size_gb: {{ min(12, SYSTEM_MEMORY * 0.2) | round }}

training:
  batch_size: {{ BATCH_SIZE }}
  num_workers: {{ NUM_WORKERS }}
  prefetch_factor: {{ PREFETCH_FACTOR }}

distributed:
  enabled: true
  backend: "nccl"
  world_size: {{ GPU_COUNT }}
  find_unused_parameters: false
