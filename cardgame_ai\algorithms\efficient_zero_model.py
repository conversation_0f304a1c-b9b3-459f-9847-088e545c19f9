"""
EfficientZero模型定义模块

该模块定义了EfficientZero算法的神经网络模型，包括：
- EfficientZeroModel: 扩展MuZero模型，添加自监督学习和信念状态支持
- 信念状态处理网络
- 分布式价值头支持
- 注意力机制和门控机制

主要功能：
- 表示网络：将观察转换为隐藏状态
- 动态网络：预测下一个状态和值前缀
- 预测网络：预测策略和价值
- 信念状态融合：处理不完全信息游戏的信念状态
"""

import os
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import logging
import time
from typing import Tuple, Dict, Any, Union, List, Optional

from .muzero import MuZeroModel
from .simsiam_loss import SelfSupervisedModule
from .distributional_value_head import DistributionalValueHead
from cardgame_ai.algorithms.risk_sensitive_rl import CVaRCalculator

# 配置日志
logger = logging.getLogger(__name__)


class EfficientZeroModel(MuZeroModel):
    """
    EfficientZero模型
    
    扩展MuZero模型，添加自监督表示学习组件和信念状态支持。
    该模型是EfficientZero算法的核心，负责状态表示、动态预测和策略价值预测。
    
    主要特性：
    - 自监督表示学习：提高样本效率
    - 信念状态支持：处理不完全信息游戏
    - 分布式价值头：更准确的价值估计
    - 注意力机制：增强信念状态处理能力
    """

    def representation(self, observations: torch.Tensor) -> torch.Tensor:
        """
        将观察转换为隐藏状态

        Args:
            observations (torch.Tensor): 观察数据，形状为[batch_size, obs_dim]

        Returns:
            torch.Tensor: 隐藏状态表示，形状为[batch_size, state_dim]
        """
        # 修复: 直接调用父类的represent方法，避免无限递归
        return super().represent(observations)

    def predict_with_belief(self, hidden_state: torch.Tensor, belief_state: Any) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        使用信念状态进行预测
        
        该方法将隐藏状态与信念状态融合，提供更准确的策略和价值预测。
        适用于不完全信息游戏，如斗地主等卡牌游戏。
        
        Args:
            hidden_state (torch.Tensor): 隐藏状态，形状为[batch_size, state_dim]
            belief_state (Any): 信念状态，可以是BeliefState或JointBeliefState
            
        Returns:
            Tuple[torch.Tensor, torch.Tensor]: 策略对数和价值
        """
        # 如果有专门的信念处理网络，则使用它
        if hasattr(self, 'belief_processor'):
            # 将信念状态转换为张量
            belief_tensor = self._convert_belief_to_tensor(belief_state)

            # 使用注意力机制处理信念状态
            if hasattr(self, 'belief_attention'):
                # 准备query: 从隐藏状态投影
                query = self.belief_query_proj(hidden_state).unsqueeze(1)  # [B, 1, head_dim*num_heads]

                # 准备key和value: 从信念状态投影
                key = self.belief_key_proj(belief_tensor).unsqueeze(0).expand(hidden_state.size(0), -1, -1)  # [B, belief_len, head_dim*num_heads]
                value = self.belief_value_proj(belief_tensor).unsqueeze(0).expand(hidden_state.size(0), -1, -1)  # [B, belief_len, head_dim*num_heads]

                # 应用注意力
                processed_belief, attention_weights = self.belief_attention(
                    query, key, value
                )

                # 摊平注意力输出
                processed_belief = processed_belief.view(hidden_state.size(0), -1)

                # 可视化注意力权重（调试用）
                # self._visualize_attention(attention_weights, belief_state)
            else:
                # 使用标准信念处理器
                processed_belief = self.belief_processor(belief_tensor)

            # 使用融合门控机制
            if hasattr(self, 'fusion_gate'):
                # 计算门控值 (0-1之间的值，决定信念的影响程度)
                gate_value = torch.sigmoid(self.fusion_gate(torch.cat([hidden_state, processed_belief], dim=1)))

                # 应用门控
                processed_belief = processed_belief * gate_value

            # 将隐藏状态和处理后的信念状态连接起来
            combined_state = torch.cat([hidden_state, processed_belief], dim=1)

            # 使用组合处理器处理组合状态
            if hasattr(self, 'combined_processor'):
                if hasattr(self, 'residual_belief') and self.residual_belief:
                    # 使用残差连接
                    residual = hidden_state
                    combined_features = self.combined_processor(combined_state)
                    combined_features = combined_features + residual
                else:
                    combined_features = self.combined_processor(combined_state)
            else:
                combined_features = combined_state

            # 使用预测网络预测策略和价值
            if self.use_distributional_value:
                # 如果使用分布式价值头
                if hasattr(self, 'prediction_network_with_belief_policy') and hasattr(self, 'prediction_network_with_belief_value'):
                    # 使用分离的策略头和价值头
                    policy_logits = self.prediction_network_with_belief_policy(combined_features)
                    value_logits = self.prediction_network_with_belief_value(combined_features)

                    # 计算风险敏感价值
                    value = self.prediction_network_with_belief_value.compute_risk_sensitive_value(
                        value_logits,
                        alpha=self.risk_alpha,
                        beta=self.risk_beta
                    )

                    return policy_logits, value
                else:
                    # 如果没有分离的头，则使用标准预测
                    return self.predict(hidden_state)
            else:
                # 使用标准预测网络
                if hasattr(self, 'prediction_network_with_belief'):
                    return self.prediction_network_with_belief(combined_features)
                else:
                    return self.predict(hidden_state)
        else:
            # 如果没有专门的信念处理网络，则使用标准预测
            return self.predict(hidden_state)

    def _convert_belief_to_tensor(self, belief_state: Any) -> torch.Tensor:
        """
        将信念状态转换为张量
        
        该方法处理不同类型的信念状态对象，将其转换为神经网络可处理的张量格式。
        支持单个玩家信念状态和联合信念状态。
        
        Args:
            belief_state (Any): 信念状态对象，可以是BeliefState或JointBeliefState
            
        Returns:
            torch.Tensor: 信念状态张量，形状为[1, belief_dim]或[batch_size, belief_dim]
        """
        # 处理JointBeliefState
        if hasattr(belief_state, 'individual_beliefs') and hasattr(belief_state, 'joint_factors'):
            # 这是一个JointBeliefState对象
            player_beliefs = []

            # 获取每个玩家的信念状态
            for player_id in belief_state.player_ids:
                individual_belief = belief_state.get_individual_belief(player_id)
                if individual_belief:
                    # 获取概率分布
                    card_probs = list(individual_belief.card_probabilities.values())
                    player_beliefs.append(card_probs)

            # 如果没有信念状态，返回空张量
            if not player_beliefs:
                return torch.zeros(1, 1).to(self.device)

            # 连接所有玩家的信念状态
            all_beliefs = np.concatenate(player_beliefs)

            # 添加联合因子信息（可选，取平均值作为简化）
            joint_info = []
            for factor_key, factor_matrix in belief_state.joint_factors.items():
                # 简化：使用因子矩阵的平均值和最大值
                joint_info.extend([
                    np.mean(factor_matrix),
                    np.max(factor_matrix)
                ])

            # 将所有信息连接起来
            if joint_info:
                all_info = np.concatenate([all_beliefs, joint_info])
            else:
                all_info = all_beliefs

            # 转换为张量
            belief_tensor = torch.tensor(all_info, dtype=torch.float32).to(self.device)

            # 添加批次维度
            if belief_tensor.dim() == 1:
                belief_tensor = belief_tensor.unsqueeze(0)

            return belief_tensor

        # 处理普通BeliefState
        elif hasattr(belief_state, 'card_probabilities'):
            card_probs = list(belief_state.card_probabilities.values())

            # 添加信念状态的元信息（可选）
            if hasattr(belief_state, 'confidence') and hasattr(belief_state, 'source'):
                # 添加置信度和来源信息
                meta_info = [
                    belief_state.confidence,
                    float(belief_state.source.value) / 5.0  # 归一化来源枚举
                ]

                # 如果有估计手牌长度，也添加它
                if hasattr(belief_state, 'estimated_hand_length') and belief_state.estimated_hand_length is not None:
                    meta_info.append(belief_state.estimated_hand_length / 20.0)  # 假设最大手牌为20张

                # 连接概率和元信息
                all_info = np.concatenate([card_probs, meta_info])
                belief_tensor = torch.tensor(all_info, dtype=torch.float32).to(self.device)
            else:
                belief_tensor = torch.tensor(card_probs, dtype=torch.float32).to(self.device)

            # 添加批次维度
            if belief_tensor.dim() == 1:
                belief_tensor = belief_tensor.unsqueeze(0)

            return belief_tensor
        else:
            # 如果没有概率分布，则返回空张量
            return torch.zeros(1, 1).to(self.device)

    def __init__(
        self,
        observation_shape: Tuple[int, ...],
        action_shape: Tuple[int, ...],
        hidden_dim: int = 256,
        state_dim: int = 64,
        use_resnet: bool = True,
        projection_dim: int = 128,
        prediction_dim: int = 64,
        value_prefix_length: int = 5,
        use_distributional_value: bool = False,
        value_support_size: int = 601,
        value_min: float = -300,
        value_max: float = 300,
        risk_alpha: float = 0.05,
        risk_beta: float = 0.1,
        use_belief_attention: bool = True,
        belief_attention_heads: int = 4,
        use_residual_belief: bool = True,
        use_gating_mechanism: bool = True,
        use_belief_state: bool = False,
        belief_dim: int = 128,
        device: str = None
    ):
        """
        初始化EfficientZero模型

        Args:
            observation_shape: 观察空间形状
            action_shape: 动作空间形状
            hidden_dim: 隐藏层维度
            state_dim: 状态表示维度
            use_resnet: 是否使用ResNet架构
            projection_dim: 自监督学习投影维度
            prediction_dim: 自监督学习预测维度
            value_prefix_length: 值前缀长度
            use_distributional_value: 是否使用分布式价值头
            value_support_size: 分布式价值支持大小
            value_min: 价值范围最小值
            value_max: 价值范围最大值
            risk_alpha: CVaR的置信水平
            risk_beta: 风险厌恶系数
            use_belief_attention: 是否使用注意力机制处理信念
            belief_attention_heads: 注意力头数
            use_residual_belief: 是否使用残差连接
            use_gating_mechanism: 是否使用门控机制
            use_belief_state: 是否使用信念状态
            belief_dim: 信念状态维度
            device: 设备类型
        """
        # 调用父类初始化
        super().__init__(
            observation_shape=observation_shape,
            action_shape=action_shape,
            hidden_dim=hidden_dim,
            state_dim=state_dim,
            use_resnet=use_resnet,
            device=device
        )

        # 保存配置参数
        self.projection_dim = projection_dim
        self.prediction_dim = prediction_dim
        self.value_prefix_length = value_prefix_length
        self.use_distributional_value = use_distributional_value
        self.value_support_size = value_support_size
        self.value_min = value_min
        self.value_max = value_max
        self.risk_alpha = risk_alpha
        self.risk_beta = risk_beta
        self.use_belief_attention = use_belief_attention
        self.belief_attention_heads = belief_attention_heads
        self.use_residual_belief = use_residual_belief
        self.use_gating_mechanism = use_gating_mechanism
        self.use_belief_state = use_belief_state
        self.belief_dim = belief_dim

        # 添加自监督学习模块
        self.self_supervised_module = SelfSupervisedModule(
            state_dim=state_dim,
            projection_dim=projection_dim,
            prediction_dim=prediction_dim
        )

        # 设置设备
        if device is None:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)

        # 移动自监督模块到设备
        self.self_supervised_module = self.self_supervised_module.to(self.device)

        # 如果使用分布式价值头，则替换预测网络的价值头
        if use_distributional_value:
            # 创建分布式价值头
            self.distributional_value_head = DistributionalValueHead(
                input_dim=state_dim,
                hidden_dim=hidden_dim // 2,
                value_support_size=value_support_size,
                value_min=value_min,
                value_max=value_max,
                device=self.device
            )

            # 修改预测网络，分离策略头和价值头
            # 策略头保持不变
            self.policy_head = nn.Sequential(
                nn.Linear(state_dim, hidden_dim),
                nn.ReLU(),
                nn.Linear(hidden_dim, action_shape[0])
            ).to(self.device)

            # 创建CVaR计算器
            self.cvar_calculator = CVaRCalculator(
                alpha=risk_alpha,
                beta=risk_beta,
                adaptive_beta=True,
                device=self.device
            )

        # 如果使用信念状态，则添加信念处理网络
        if use_belief_state:
            # 信念状态处理器
            self.belief_processor = nn.Sequential(
                nn.Linear(belief_dim, hidden_dim // 2),
                nn.ReLU(),
                nn.Linear(hidden_dim // 2, hidden_dim // 4)
            ).to(self.device)

            # 组合处理器
            self.combined_processor = nn.Sequential(
                nn.Linear(state_dim + hidden_dim // 4, hidden_dim),
                nn.ReLU(),
                nn.Linear(hidden_dim, state_dim)
            ).to(self.device)

            # 修改预测网络以支持信念状态
            if use_distributional_value:
                # 如果同时使用信念状态和分布式价值头
                self.prediction_network_with_belief_policy = nn.Sequential(
                    nn.Linear(state_dim, hidden_dim),
                    nn.ReLU(),
                    nn.Linear(hidden_dim, action_shape[0])
                ).to(self.device)

                # 分布式价值头
                self.prediction_network_with_belief_value = DistributionalValueHead(
                    input_dim=state_dim,
                    hidden_dim=hidden_dim // 2,
                    value_support_size=value_support_size,
                    value_min=value_min,
                    value_max=value_max,
                    device=self.device
                ).to(self.device)
            else:
                # 使用标准预测网络
                self.prediction_network_with_belief = nn.Sequential(
                    nn.Linear(state_dim, hidden_dim),
                    nn.ReLU(),
                    nn.Linear(hidden_dim, action_shape[0] + 1)  # 输出动作概率和价值
                ).to(self.device)

        # 创建自定义的EfficientZero动态网络，支持两个参数的forward调用
        class EfficientZeroDynamicsNetwork(nn.Module):
            def __init__(self, state_dim, action_shape, hidden_dim, value_prefix_length, device):
                super().__init__()
                self.state_dim = state_dim
                self.value_prefix_length = value_prefix_length
                self.network = nn.Sequential(
                    nn.Linear(state_dim + np.prod(action_shape).item(), hidden_dim),
                    nn.ReLU(),
                    nn.Linear(hidden_dim, state_dim + value_prefix_length)
                )
                self.to(device)

            def forward(self, state, action):
                """
                动态网络前向传播

                Args:
                    state: 状态张量，形状为[batch_size, state_dim]
                    action: 动作张量，形状为[batch_size]或[batch_size, 1]

                Returns:
                    next_state: 下一个状态，形状为[batch_size, state_dim]
                    value_prefix: 值前缀，形状为[batch_size, value_prefix_length]
                """
                # 确保动作张量有正确的形状
                if action.dim() == 1:
                    action = action.unsqueeze(1)  # 将形状从[batch_size]变为[batch_size, 1]

                x = torch.cat([state, action], dim=1)
                dynamics_output = self.network(x)

                # 分离状态和值前缀
                next_state = dynamics_output[:, :self.state_dim]
                value_prefix = dynamics_output[:, self.state_dim:]

                return next_state, value_prefix

        # 创建动态网络实例
        self.dynamics_network = EfficientZeroDynamicsNetwork(
            state_dim=state_dim,
            action_shape=action_shape,
            hidden_dim=hidden_dim,
            value_prefix_length=value_prefix_length,
            device=self.device
        )

    def predict(self, hidden_state: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        预测策略和价值

        Args:
            hidden_state (torch.Tensor): 隐藏状态，形状为[batch_size, state_dim]

        Returns:
            Tuple[torch.Tensor, torch.Tensor]: 策略对数和价值
        """
        # 保存原始训练状态
        training = self.prediction_network.training
        self.prediction_network.eval()

        try:
            with torch.no_grad():
                if self.use_distributional_value:
                    # 如果使用分布式价值头
                    # 使用策略头获取策略对数
                    policy_logits = self.policy_head(hidden_state)

                    # 使用分布式价值头获取价值分布
                    value_logits = self.distributional_value_head(hidden_state)

                    # 计算期望价值
                    value = self.distributional_value_head.compute_expected_value(value_logits)

                    return policy_logits, value
                else:
                    # 使用标准预测网络
                    return self.prediction_network(hidden_state)
        except Exception as e:
            logger.error(f"预测过程中出错: {e}")
            # 返回默认值
            batch_size = hidden_state.size(0)
            action_dim = self.action_shape[0] if hasattr(self, 'action_shape') else 1
            policy_logits = torch.zeros(batch_size, action_dim).to(hidden_state.device)
            values = torch.zeros(batch_size, 1).to(hidden_state.device)
            return policy_logits, values
        finally:
            # 恢复原始训练状态
            self.prediction_network.train(training)

    def dynamics(self, state: torch.Tensor, action: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        动态函数：预测下一个状态和值前缀

        Args:
            state (torch.Tensor): 当前状态，形状为[batch_size, state_dim]
            action (torch.Tensor): 执行的动作，形状为[batch_size]或[batch_size, 1]

        Returns:
            Tuple[torch.Tensor, torch.Tensor]: 下一个状态和值前缀
        """
        # 直接调用dynamics_network的forward方法，传递两个参数
        return self.dynamics_network(state, action)

    def self_supervised_loss(self, state1: torch.Tensor, state2: torch.Tensor) -> torch.Tensor:
        """
        计算自监督损失

        Args:
            state1 (torch.Tensor): 第一个状态表示
            state2 (torch.Tensor): 第二个状态表示

        Returns:
            torch.Tensor: 自监督损失值
        """
        p1, p2, z1, z2 = self.self_supervised_module(state1, state2)
        return self.self_supervised_module.compute_loss(p1, p2, z1, z2)

    def consistency_loss(self, predicted_state: torch.Tensor, target_state: torch.Tensor) -> torch.Tensor:
        """
        计算一致性损失

        Args:
            predicted_state (torch.Tensor): 预测状态
            target_state (torch.Tensor): 目标状态

        Returns:
            torch.Tensor: 一致性损失值
        """
        # 使用均方误差计算一致性损失
        return F.mse_loss(predicted_state, target_state)
