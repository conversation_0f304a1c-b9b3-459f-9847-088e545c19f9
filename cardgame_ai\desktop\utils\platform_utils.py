#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
平台工具

处理不同操作系统的兼容性问题。
"""

import os
import sys
import platform
import logging
from typing import Optional

logger = logging.getLogger(__name__)


class PlatformConfig:
    """平台配置类"""

    @classmethod
    def setup_environment(cls) -> None:
        """设置环境变量"""
        # 设置环境变量
        os.environ["QT_AUTO_SCREEN_SCALE_FACTOR"] = "1"

        # Windows特定设置
        if cls.is_windows():
            # 设置Windows特定环境变量
            pass

        # Linux特定设置
        elif cls.is_linux():
            # 设置Linux特定环境变量
            pass

        # macOS特定设置
        elif cls.is_macos():
            # 设置macOS特定环境变量
            pass

        logger.info(f"环境设置完成，操作系统：{cls.get_system_name()}")

    @classmethod
    def get_base_dir(cls) -> str:
        """
        获取基础目录

        Returns:
            str: 基础目录路径
        """
        # 获取当前文件所在目录
        current_dir = os.path.dirname(os.path.abspath(__file__))

        # 获取项目根目录
        desktop_dir = os.path.dirname(current_dir)
        cardgame_dir = os.path.dirname(desktop_dir)
        base_dir = os.path.dirname(cardgame_dir)

        return base_dir

    @classmethod
    def get_config_dir(cls) -> str:
        """
        获取配置目录

        Returns:
            str: 配置目录路径
        """
        # 获取基础目录
        base_dir = cls.get_base_dir()

        # 获取配置目录
        config_dir = os.path.join(base_dir, "config")

        # 确保目录存在
        os.makedirs(config_dir, exist_ok=True)

        return config_dir

    @classmethod
    def get_data_dir(cls) -> str:
        """
        获取数据目录

        Returns:
            str: 数据目录路径
        """
        # 获取基础目录
        base_dir = cls.get_base_dir()

        # 获取数据目录
        data_dir = os.path.join(base_dir, "data")

        # 确保目录存在
        os.makedirs(data_dir, exist_ok=True)

        return data_dir

    @classmethod
    def get_models_dir(cls) -> str:
        """
        获取模型目录

        Returns:
            str: 模型目录路径
        """
        # 获取基础目录
        base_dir = cls.get_base_dir()

        # 获取模型目录
        models_dir = os.path.join(base_dir, "models")

        # 确保目录存在
        os.makedirs(models_dir, exist_ok=True)

        return models_dir

    @classmethod
    def get_logs_dir(cls) -> str:
        """
        获取日志目录

        Returns:
            str: 日志目录路径
        """
        # 获取基础目录
        base_dir = cls.get_base_dir()

        # 获取日志目录
        logs_dir = os.path.join(base_dir, "logs")

        # 确保目录存在
        os.makedirs(logs_dir, exist_ok=True)

        return logs_dir

    @classmethod
    def is_windows(cls) -> bool:
        """
        检查是否为Windows系统

        Returns:
            bool: 是否为Windows系统
        """
        return platform.system().lower() == "windows"

    @classmethod
    def is_linux(cls) -> bool:
        """
        检查是否为Linux系统

        Returns:
            bool: 是否为Linux系统
        """
        return platform.system().lower() == "linux"

    @classmethod
    def is_macos(cls) -> bool:
        """
        检查是否为macOS系统

        Returns:
            bool: 是否为macOS系统
        """
        return platform.system().lower() == "darwin"

    @classmethod
    def get_system_name(cls) -> str:
        """
        获取操作系统名称

        Returns:
            str: 操作系统名称
        """
        system = platform.system().lower()

        if system == "windows":
            return f"Windows {platform.release()}"
        elif system == "linux":
            return f"Linux {platform.release()}"
        elif system == "darwin":
            return f"macOS {platform.mac_ver()[0]}"
        else:
            return f"Unknown {system}"

    @classmethod
    def get_default_font_size(cls) -> int:
        """
        获取默认字体大小

        Returns:
            int: 默认字体大小
        """
        if cls.is_windows():
            return 9  # Windows默认字体大小
        elif cls.is_macos():
            return 13  # macOS默认字体大小
        else:  # Linux
            return 10  # Linux默认字体大小
