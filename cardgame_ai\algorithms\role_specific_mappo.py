"""
角色特定的多智能体近端策略优化算法模块

扩展MAPPO算法，实现角色特定的多智能体近端策略优化(Role-Specific MAPPO)算法。
为斗地主中的地主、农民1(地主下家)和农民2(地主上家)三个角色提供专门的模型。
支持农民智能体之间的隐式通信机制，提高协作效率。
"""
import os
import logging
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from typing import Dict, List, Tuple, Any, Optional, Union

from cardgame_ai.core.base import State, Action, Experience, Batch
from cardgame_ai.algorithms.mappo import MAPPO, MAPPONetwork

# 配置日志
logger = logging.getLogger(__name__)


class RoleSpecificMAPPONetwork(MAPPONetwork):
    """
    角色特定的多智能体PPO网络

    扩展MAPPONetwork，添加角色特定的功能，为不同角色提供专门的网络结构。
    支持农民智能体之间的隐式通信机制，通过共享信息提高协作效率。
    """

    def __init__(
        self,
        role: str,
        obs_dim: int,
        act_dim: int,
        global_state_dim: Optional[int] = None,
        hidden_dims: List[int] = [256, 128],
        shared_layers: bool = True,
        use_central_critic: bool = True,
        use_implicit_communication: bool = False,
        communication_dim: int = 32
    ):
        """
        初始化角色特定的多智能体PPO网络

        Args:
            role: 角色名称，'landlord'(地主)、'farmer1'(农民1)或'farmer2'(农民2)
            obs_dim: 局部观察维度
            act_dim: 动作维度
            global_state_dim: 全局状态维度，默认为None（使用局部观察）
            hidden_dims: 隐藏层维度
            shared_layers: 是否共享特征提取层
            use_central_critic: 是否使用中心化评论家（全局状态输入）
            use_implicit_communication: 是否使用隐式通信机制（仅对农民有效）
            communication_dim: 通信嵌入维度
        """
        # 添加角色编码到观察维度
        self.role = role
        self.role_encoding_dim = 3  # 三个角色的one-hot编码

        # 隐式通信相关参数
        self.use_implicit_communication = use_implicit_communication and (role == 'farmer1' or role == 'farmer2')
        self.communication_dim = communication_dim

        # 计算扩展后的观察维度
        extended_obs_dim = obs_dim + self.role_encoding_dim

        # 如果使用隐式通信，为农民角色添加通信嵌入维度
        if self.use_implicit_communication:
            extended_obs_dim += self.communication_dim
            logger.info(f"为{role}启用隐式通信机制，通信嵌入维度: {communication_dim}")

        # 调用父类初始化，使用扩展后的观察维度
        super().__init__(
            obs_dim=extended_obs_dim,
            act_dim=act_dim,
            global_state_dim=global_state_dim,
            hidden_dims=hidden_dims,
            shared_layers=shared_layers,
            use_central_critic=use_central_critic
        )

        # 为农民角色添加通信嵌入生成器
        if self.use_implicit_communication:
            self.communication_encoder = nn.Sequential(
                nn.Linear(hidden_dims[0], hidden_dims[0] // 2),
                nn.ReLU(),
                nn.Linear(hidden_dims[0] // 2, self.communication_dim),
                nn.Tanh()  # 使用Tanh激活函数，输出范围[-1, 1]
            )

        # 根据角色调整网络结构
        self._init_role_specific_network()

    def _init_role_specific_network(self):
        """
        初始化角色特定的网络结构

        根据角色调整网络结构，例如为地主增加更多的隐藏单元，
        或为农民添加协作相关的特征提取层。
        """
        # 根据角色调整网络结构
        if self.role == 'landlord':
            # 地主可能需要更强的表达能力
            if hasattr(self, 'actor_feature_layer') and isinstance(self.actor_feature_layer, nn.Sequential):
                # 为地主增加额外的注意力层或更多隐藏单元
                pass
        elif self.role == 'farmer1' or self.role == 'farmer2':
            # 农民需要更好的协作能力
            if hasattr(self, 'actor_feature_layer') and isinstance(self.actor_feature_layer, nn.Sequential):
                # 为农民添加协作相关的特征提取层
                if self.use_implicit_communication:
                    # 添加协作注意力层
                    hidden_dim = self.actor_feature_layer[-2].out_features
                    self.cooperation_attention = nn.Sequential(
                        nn.Linear(hidden_dim, hidden_dim),
                        nn.ReLU(),
                        nn.Linear(hidden_dim, hidden_dim),
                        nn.Sigmoid()  # 使用Sigmoid作为注意力权重
                    )

                    # 添加协作特征融合层
                    self.cooperation_fusion = nn.Sequential(
                        nn.Linear(hidden_dim * 2, hidden_dim),
                        nn.ReLU()
                    )

                    logger.info(f"为{self.role}添加协作注意力层和特征融合层")

    def _add_role_encoding(self, obs: torch.Tensor, communication_embedding: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        添加角色编码和通信嵌入到观察

        Args:
            obs: 原始观察张量，不包含角色编码和通信嵌入
            communication_embedding: 通信嵌入张量，默认为None

        Returns:
            添加了角色编码和通信嵌入的观察张量
        """
        batch_size = obs.shape[0]

        # 创建角色one-hot编码
        role_encoding = torch.zeros(batch_size, self.role_encoding_dim, device=obs.device)

        if self.role == 'landlord':
            role_encoding[:, 0] = 1.0
        elif self.role == 'farmer1':
            role_encoding[:, 1] = 1.0
        elif self.role == 'farmer2':
            role_encoding[:, 2] = 1.0

        # 连接原始观察和角色编码
        result = torch.cat([obs, role_encoding], dim=1)

        # 如果启用隐式通信且提供了通信嵌入，则添加到观察中
        if self.use_implicit_communication and communication_embedding is not None:
            # 确保通信嵌入的批次大小与观察一致
            if communication_embedding.shape[0] != batch_size:
                if communication_embedding.shape[0] == 1:
                    # 如果通信嵌入是单个样本，则扩展到批次大小
                    communication_embedding = communication_embedding.expand(batch_size, -1)
                else:
                    # 否则，截断或填充到批次大小
                    if communication_embedding.shape[0] > batch_size:
                        communication_embedding = communication_embedding[:batch_size]
                    else:
                        padding = torch.zeros(batch_size - communication_embedding.shape[0],
                                             self.communication_dim,
                                             device=obs.device)
                        communication_embedding = torch.cat([communication_embedding, padding], dim=0)

            # 连接通信嵌入
            result = torch.cat([result, communication_embedding], dim=1)
        elif self.use_implicit_communication:
            # 如果启用隐式通信但没有提供通信嵌入，则添加零向量
            zero_embedding = torch.zeros(batch_size, self.communication_dim, device=obs.device)
            result = torch.cat([result, zero_embedding], dim=1)

        return result

    def forward_actor(self, obs: torch.Tensor, communication_embedding: Optional[torch.Tensor] = None) -> Union[torch.Tensor, Tuple[torch.Tensor, torch.Tensor]]:
        """
        前向传播 - 策略网络

        Args:
            obs: 局部观察张量，不包含角色编码
            communication_embedding: 通信嵌入张量，默认为None

        Returns:
            如果不使用隐式通信：动作对数概率
            如果使用隐式通信：(动作对数概率, 通信嵌入)
        """
        # 添加角色编码和通信嵌入
        obs_with_role = self._add_role_encoding(obs, communication_embedding)

        # 如果不使用隐式通信或不是农民角色，直接调用父类方法
        if not self.use_implicit_communication or self.role == 'landlord':
            return super().forward_actor(obs_with_role)

        # 对于使用隐式通信的农民角色，生成通信嵌入
        if self.shared_layers:
            features = self.actor_feature_layer(obs_with_role)
            action_logits = self.policy_head(features)
        else:
            features = self.policy_net[:-1](obs_with_role)  # 除了最后一层外的所有层
            action_logits = self.policy_net[-1](features)   # 最后一层

        # 生成通信嵌入
        comm_embedding = self.communication_encoder(features)

        return action_logits, comm_embedding

    def forward_critic(self, state: torch.Tensor, communication_embedding: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        前向传播 - 价值网络

        Args:
            state: 状态张量（全局状态或局部观察），不包含角色编码
            communication_embedding: 通信嵌入张量，默认为None

        Returns:
            状态价值
        """
        # 如果使用局部观察作为评论家输入，则添加角色编码和通信嵌入
        if not self.use_central_critic or state.shape[1] == (self.obs_dim - self.role_encoding_dim - (self.communication_dim if self.use_implicit_communication else 0)):
            state = self._add_role_encoding(state, communication_embedding)

        # 调用父类方法
        return super().forward_critic(state)

    def process_farmer_cooperation(self, obs: torch.Tensor, teammate_embedding: torch.Tensor) -> Tuple[torch.Tensor, float]:
        """
        处理农民之间的协作

        使用队友的通信嵌入增强当前农民的决策，并计算协作奖励。

        Args:
            obs: 局部观察张量，不包含角色编码
            teammate_embedding: 队友的通信嵌入

        Returns:
            增强后的动作对数概率、协作奖励
        """
        if not self.use_implicit_communication or self.role not in ['farmer1', 'farmer2']:
            # 如果不使用隐式通信或不是农民角色，直接返回原始结果和零奖励
            logits = self.forward_actor(obs)
            return logits, 0.0

        # 前向传播，获取动作对数概率和通信嵌入
        action_logits, comm_embedding = self.forward_actor(obs)

        # 提取特征
        if self.shared_layers:
            features = self.actor_feature_layer(self._add_role_encoding(obs, comm_embedding))
        else:
            features = self.policy_net[:-1](self._add_role_encoding(obs, comm_embedding))

        # 计算注意力权重
        attention_weights = self.cooperation_attention(features)

        # 使用注意力权重融合自身特征和队友通信嵌入
        # 首先将队友通信嵌入映射到相同维度
        teammate_features = F.linear(
            teammate_embedding,
            weight=self.communication_encoder[-2].weight.t(),  # 使用通信编码器的倒数第二层权重的转置
            bias=None
        )
        teammate_features = F.relu(teammate_features)

        # 应用注意力权重
        attended_teammate = attention_weights * teammate_features

        # 融合特征
        fused_features = self.cooperation_fusion(torch.cat([features, attended_teammate], dim=1))

        # 生成增强后的动作对数概率
        if self.shared_layers:
            enhanced_logits = self.policy_head(fused_features)
        else:
            enhanced_logits = self.policy_net[-1](fused_features)

        # 计算协作奖励：基于自身通信嵌入和队友通信嵌入的相似度
        cooperation_reward = F.cosine_similarity(comm_embedding, teammate_embedding, dim=1).mean().item()

        return enhanced_logits, cooperation_reward


class RoleSpecificMAPPO(MAPPO):
    """
    角色特定的多智能体近端策略优化(Role-Specific MAPPO)算法

    扩展MAPPO算法，为不同角色提供专门的模型和训练策略。
    支持农民智能体之间的隐式通信机制，提高协作效率。
    """

    def __init__(
        self,
        role: str,
        obs_shape: Tuple[int, ...],
        act_shape: Tuple[int, ...],
        global_state_shape: Optional[Tuple[int, ...]] = None,
        hidden_dims: List[int] = [256, 128],
        learning_rate: float = 0.0001,
        gamma: float = 0.99,
        gae_lambda: float = 0.95,
        clip_ratio: float = 0.2,
        value_coef: float = 0.5,
        entropy_coef: float = 0.01,
        max_grad_norm: float = 0.5,
        update_epochs: int = 4,
        shared_network: bool = True,
        use_central_critic: bool = True,
        batch_size: int = 64,
        device: str = None,
        use_implicit_communication: bool = False,
        communication_dim: int = 32,
        cooperation_coef: float = 0.1,
        use_cooperative_strategy: bool = False
    ):
        """
        初始化角色特定的MAPPO算法

        Args:
            role: 角色名称，'landlord'(地主)、'farmer1'(农民1)或'farmer2'(农民2)
            obs_shape: 观察形状
            act_shape: 动作形状
            global_state_shape: 全局状态形状，默认为None
            hidden_dims: 隐藏层维度
            learning_rate: 学习率
            gamma: 折扣因子
            gae_lambda: GAE平滑参数
            clip_ratio: PPO裁剪比例
            value_coef: 价值损失系数
            entropy_coef: 熵正则化系数
            max_grad_norm: 梯度裁剪范数
            update_epochs: 每次更新的轮数
            shared_network: 是否使用共享网络
            use_central_critic: 是否使用中心化评论家
            batch_size: 批次大小
            device: 计算设备
            use_implicit_communication: 是否使用隐式通信机制（仅对农民有效）
            communication_dim: 通信嵌入维度
            cooperation_coef: 协作奖励系数
            use_cooperative_strategy: 是否使用协作策略（仅对农民有效）
        """
        # 保存隐式通信相关参数
        self.use_implicit_communication = use_implicit_communication and (role == 'farmer1' or role == 'farmer2')
        self.communication_dim = communication_dim
        self.cooperation_coef = cooperation_coef
        
        # 保存协作策略参数
        self.use_cooperative_strategy = use_cooperative_strategy and (role == 'farmer1' or role == 'farmer2')

        # 初始化父类
        super().__init__(
            obs_shape=obs_shape,
            act_shape=act_shape,
            global_state_shape=global_state_shape,
            hidden_dims=hidden_dims,
            learning_rate=learning_rate,
            gamma=gamma,
            gae_lambda=gae_lambda,
            clip_ratio=clip_ratio,
            value_coef=value_coef,
            entropy_coef=entropy_coef,
            max_grad_norm=max_grad_norm,
            update_epochs=update_epochs,
            shared_network=shared_network,
            use_central_critic=use_central_critic,
            batch_size=batch_size,
            device=device
        )

        # 保存角色信息
        self.role = role

        # 创建角色特定的网络，替换父类创建的网络
        self.network = RoleSpecificMAPPONetwork(
            role=role,
            obs_dim=self.obs_dim,
            act_dim=self.act_dim,
            global_state_dim=self.global_state_dim,
            hidden_dims=hidden_dims,
            shared_layers=shared_network,
            use_central_critic=use_central_critic,
            use_implicit_communication=self.use_implicit_communication,
            communication_dim=communication_dim
        ).to(self.device)

        # 创建优化器
        self.optimizer = optim.Adam(self.network.parameters(), lr=learning_rate)

        # 角色特定的超参数调整
        self._adjust_hyperparameters_for_role()

        # 通信嵌入缓存
        self.communication_embeddings = {}

        # 协作统计
        self.cooperation_stats = {
            "total_cooperation_rewards": 0.0,
            "cooperation_count": 0,
            "avg_cooperation_reward": 0.0,
            "joint_play_success": 0,
            "joint_play_attempts": 0,
            "redundant_play_count": 0
        }
        
        # 如果启用协作策略，创建CooperativeStrategy实例
        if self.use_cooperative_strategy:
            from cardgame_ai.multi_agent.cooperative_strategy import JointPolicyOptimizer
            self.coop_strategy = JointPolicyOptimizer(
                agents={role: self},
                role_manager=None,  # 将在使用时提供
                joint_loss_weight=0.7
            )
            
            # 添加协作策略统计
            self.cooperation_stats.update({
                "coordination_success": 0,
                "coordination_attempts": 0,
                "coordination_rate": 0.0,
                "strategy_conflicts": 0
            })

    def _adjust_hyperparameters_for_role(self):
        """
        根据角色调整超参数

        不同角色可能需要不同的超参数设置，例如：
        - 地主可能需要更高的熵正则化系数以鼓励探索
        - 农民可能需要更高的价值损失系数以更准确地评估状态
        """
        if self.role == 'landlord':
            # 地主可能需要更高的熵正则化系数以鼓励探索
            self.entropy_coef *= 1.2
        elif self.role == 'farmer1' or self.role == 'farmer2':
            # 农民可能需要更高的价值损失系数以更准确地评估状态
            self.value_coef *= 1.2

            # 如果使用隐式通信，调整协作奖励系数
            if self.use_implicit_communication:
                # 根据角色微调协作奖励系数
                if self.role == 'farmer1':
                    self.cooperation_coef *= 1.1  # 农民1可能需要更主动的协作
                else:
                    self.cooperation_coef *= 0.9  # 农民2可能需要更被动的协作

    def predict(self, obs: Union[State, np.ndarray], global_state: Optional[np.ndarray] = None,
               teammate_id: Optional[str] = None) -> Tuple[List[float], float]:
        """
        预测动作概率分布和价值

        Args:
            obs: 观察或状态
            global_state: 全局状态，用于中心化评论家
            teammate_id: 队友ID，用于获取队友的通信嵌入

        Returns:
            动作概率分布、状态价值
        """
        # 处理观察
        if isinstance(obs, State):
            obs_array = obs.to_dict().get('observation', np.zeros(self.obs_dim - 3))  # 减去角色编码维度
            obs_tensor = torch.FloatTensor(obs_array).to(self.device)
        else:
            obs_tensor = torch.FloatTensor(obs).to(self.device)

        # 重塑观察
        if obs_tensor.dim() == 1:
            obs_tensor = obs_tensor.unsqueeze(0)

        # 处理全局状态
        if self.use_central_critic and global_state is not None:
            state_tensor = torch.FloatTensor(global_state).to(self.device)
            if state_tensor.dim() == 1:
                state_tensor = state_tensor.unsqueeze(0)
        else:
            state_tensor = obs_tensor  # 使用局部观察作为状态输入

        # 预测动作概率和价值
        with torch.no_grad():
            # 如果是农民角色且使用隐式通信且提供了队友ID
            if self.use_implicit_communication and self.role in ['farmer1', 'farmer2'] and teammate_id is not None:
                # 获取队友的通信嵌入
                teammate_embedding = self.get_teammate_embedding(teammate_id)

                if teammate_embedding is not None:
                    # 使用队友的通信嵌入增强决策
                    action_logits, cooperation_reward = self.network.process_farmer_cooperation(
                        obs_tensor, teammate_embedding
                    )

                    # 更新协作统计
                    self.cooperation_stats["total_cooperation_rewards"] += cooperation_reward
                    self.cooperation_stats["cooperation_count"] += 1
                    self.cooperation_stats["avg_cooperation_reward"] = (
                        self.cooperation_stats["total_cooperation_rewards"] /
                        self.cooperation_stats["cooperation_count"]
                    )

                    # 转换为概率分布
                    action_probs = F.softmax(action_logits, dim=-1).cpu().numpy()[0]

                    # 生成并缓存自己的通信嵌入
                    _, comm_embedding = self.network.forward_actor(obs_tensor)
                    self.communication_embeddings[self.role] = comm_embedding.detach()
                else:
                    # 如果没有队友的通信嵌入，使用普通预测
                    action_logits, comm_embedding = self.network.forward_actor(obs_tensor)
                    action_probs = F.softmax(action_logits, dim=-1).cpu().numpy()[0]

                    # 缓存自己的通信嵌入
                    self.communication_embeddings[self.role] = comm_embedding.detach()
            else:
                # 对于地主或不使用隐式通信的情况，使用普通预测
                action_probs = self.network.get_action_probs(obs_tensor).cpu().numpy()[0]

                # 如果是农民角色且使用隐式通信，生成并缓存通信嵌入
                if self.use_implicit_communication and self.role in ['farmer1', 'farmer2']:
                    _, comm_embedding = self.network.forward_actor(obs_tensor)
                    self.communication_embeddings[self.role] = comm_embedding.detach()

            # 获取状态价值
            value = self.network.get_value(state_tensor).cpu().numpy()[0][0]
            
            # 如果启用了协作策略且为农民角色，使用协作策略优化动作
            if self.use_cooperative_strategy and self.role in ['farmer1', 'farmer2'] and teammate_id is not None:
                # 获取队友信息
                teammate_info = self.get_teammate_info(teammate_id)
                
                # 如果有队友信息，使用协作策略调整动作概率
                if teammate_info is not None and hasattr(self.coop_strategy, 'cooperative_decision'):
                    # 获取原始动作
                    original_action = np.argmax(action_probs)
                    legal_actions = None
                    
                    # 尝试获取合法动作
                    if isinstance(obs, State) and hasattr(obs, 'get_legal_actions'):
                        legal_actions = obs.get_legal_actions()
                    
                    # 使用协作策略调整动作
                    adjusted_action = self.coop_strategy.cooperative_decision(
                        self.role,
                        original_action,
                        teammate_info,
                        legal_actions
                    )
                    
                    # 如果调整后的动作不同，更新动作概率
                    if adjusted_action is not None and adjusted_action != original_action:
                        # 创建新的动作概率分布，增加调整后动作的概率
                        new_action_probs = action_probs.copy()
                        
                        # 增加调整后动作的概率，减少原始动作的概率
                        adjustment = 0.3  # 调整比例
                        new_action_probs[adjusted_action] += action_probs[original_action] * adjustment
                        new_action_probs[original_action] *= (1 - adjustment)
                        
                        # 归一化概率
                        new_action_probs = new_action_probs / np.sum(new_action_probs)
                        
                        # 更新协作统计
                        self.cooperation_stats["coordination_attempts"] += 1
                        self.cooperation_stats["coordination_success"] += 1
                        self.cooperation_stats["coordination_rate"] = (
                            self.cooperation_stats["coordination_success"] /
                            self.cooperation_stats["coordination_attempts"]
                        )
                        
                        # 使用调整后的动作概率
                        action_probs = new_action_probs
                    elif adjusted_action is not None:
                        # 动作没有改变，但尝试了协调
                        self.cooperation_stats["coordination_attempts"] += 1
                        self.cooperation_stats["coordination_rate"] = (
                            self.cooperation_stats["coordination_success"] /
                            self.cooperation_stats["coordination_attempts"]
                        )

        return list(action_probs), float(value)

    def get_teammate_embedding(self, teammate_id: str) -> Optional[torch.Tensor]:
        """
        获取队友的通信嵌入

        Args:
            teammate_id: 队友ID

        Returns:
            队友的通信嵌入，如果不存在则返回None
        """
        # 将队友ID转换为角色
        teammate_role = None
        if teammate_id == '1':
            teammate_role = 'farmer1'
        elif teammate_id == '2':
            teammate_role = 'farmer2'

        # 如果队友角色有效且存在通信嵌入，则返回
        if teammate_role and teammate_role in self.communication_embeddings:
            return self.communication_embeddings[teammate_role]

        return None

    def get_teammate_info(self, teammate_id: str) -> Optional[Dict[str, Any]]:
        """
        获取队友信息
        
        Args:
            teammate_id: 队友ID
            
        Returns:
            队友信息，如果不存在则返回None
        """
        # 将队友ID转换为角色
        teammate_role = None
        if teammate_id == '1':
            teammate_role = 'farmer1'
        elif teammate_id == '2':
            teammate_role = 'farmer2'
            
        # 如果队友角色有效，构建队友信息
        if teammate_role:
            teammate_info = {
                'role': teammate_role,
                'embedding': self.get_teammate_embedding(teammate_id)
            }
            return teammate_info
            
        return None

    def save(self, path: str) -> None:
        """
        保存模型

        Args:
            path: 保存路径
        """
        # 确保目录存在
        os.makedirs(os.path.dirname(path), exist_ok=True)

        # 保存模型
        torch.save({
            'role': self.role,
            'network_state_dict': self.network.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict()
        }, path)

    def load(self, path: str) -> None:
        """
        加载模型

        Args:
            path: 加载路径
        """
        # 加载模型
        checkpoint = torch.load(path, map_location=self.device)

        # 检查角色是否匹配
        loaded_role = checkpoint.get('role')
        if loaded_role and loaded_role != self.role:
            print(f"警告：加载的模型角色({loaded_role})与当前角色({self.role})不匹配")

        self.network.load_state_dict(checkpoint['network_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])

    def _update_policy(self) -> Dict[str, float]:
        """
        更新策略

        Returns:
            更新指标
        """
        # 计算优势和回报
        advantages, returns = self._compute_advantages_and_returns()

        # 转换为张量
        observations = torch.FloatTensor(self.observations).to(self.device)
        global_states = torch.FloatTensor(self.global_states).to(self.device) if self.use_central_critic else observations
        actions = torch.LongTensor(self.actions).to(self.device)
        old_action_log_probs = torch.FloatTensor(self.action_log_probs).to(self.device)
        returns = torch.FloatTensor(returns).to(self.device)
        advantages = torch.FloatTensor(advantages).to(self.device)

        # 归一化优势
        advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)

        # 多轮更新
        total_policy_loss = 0
        total_value_loss = 0
        total_entropy = 0
        total_cooperation_loss = 0
        total_coop_strategy_loss = 0  # 新增协作策略损失统计

        for _ in range(self.update_epochs):
            # 生成批次索引
            indices = torch.randperm(len(observations))

            # 按批次更新
            for start_idx in range(0, len(observations), self.batch_size):
                # 获取批次索引
                batch_indices = indices[start_idx:start_idx + self.batch_size]

                # 提取批次数据
                batch_observations = observations[batch_indices]
                batch_global_states = global_states[batch_indices]
                batch_actions = actions[batch_indices]
                batch_old_action_log_probs = old_action_log_probs[batch_indices]
                batch_returns = returns[batch_indices]
                batch_advantages = advantages[batch_indices]

                # 评估动作
                action_log_probs, values, entropy = self.network.evaluate_actions(
                    batch_observations, batch_global_states, batch_actions
                )

                # 计算比率和裁剪目标
                ratio = torch.exp(action_log_probs - batch_old_action_log_probs)
                surr1 = ratio * batch_advantages
                surr2 = torch.clamp(ratio, 1.0 - self.clip_ratio, 1.0 + self.clip_ratio) * batch_advantages

                # 计算策略损失
                policy_loss = -torch.min(surr1, surr2).mean()

                # 计算价值损失
                value_loss = F.mse_loss(values, batch_returns)

                # 计算协作损失（仅对农民角色）
                cooperation_loss = torch.tensor(0.0, device=self.device)
                if self.use_implicit_communication and self.role in ['farmer1', 'farmer2']:
                    # 生成通信嵌入
                    _, comm_embedding = self.network.forward_actor(batch_observations)

                    # 计算通信嵌入的多样性损失（鼓励不同状态产生不同的通信嵌入）
                    comm_norm = F.normalize(comm_embedding, p=2, dim=1)
                    similarity_matrix = torch.matmul(comm_norm, comm_norm.t())
                    mask = torch.eye(similarity_matrix.shape[0], device=self.device)
                    diversity_loss = (similarity_matrix * (1 - mask)).mean()

                    # 计算协作损失
                    cooperation_loss = diversity_loss
                
                # 计算协作策略损失（仅当启用协作策略且为农民角色时）
                coop_strategy_loss = torch.tensor(0.0, device=self.device)
                if self.use_cooperative_strategy and self.role in ['farmer1', 'farmer2']:
                    # 构建适合协作策略的批次数据
                    batch_data = {
                        'observations': batch_observations,
                        'actions': batch_actions,
                        'advantages': batch_advantages,
                        'returns': batch_returns,
                        'old_log_probs': batch_old_action_log_probs
                    }
                    
                    # 如果有队友数据，获取队友数据
                    teammate_data = None
                    if hasattr(self, '_get_teammate_data'):
                        teammate_data = self._get_teammate_data(batch_data)
                    
                    # 计算协作策略损失
                    if hasattr(self.coop_strategy, 'calculate_cooperation_loss'):
                        coop_strategy_loss = self.coop_strategy.calculate_cooperation_loss(
                            batch_data, teammate_data
                        )
                    elif hasattr(self.coop_strategy, '_compute_joint_loss'):
                        # 兼容JointPolicyOptimizer
                        if self.role == 'farmer1':
                            teammate_role = 'farmer2'
                        else:
                            teammate_role = 'farmer1'
                            
                        # 构建经验数据格式
                        experiences = {
                            self.role: [(
                                batch_observations[i].cpu().numpy(),
                                batch_actions[i].cpu().item(),
                                0.0,  # reward placeholder
                                batch_observations[i].cpu().numpy(),  # next_state placeholder
                                False,  # done placeholder
                                batch_old_action_log_probs[i].cpu().item(),
                                values[i].cpu().item()
                            ) for i in range(len(batch_observations))]
                        }
                        
                        # 计算联合损失
                        coop_strategy_loss = self.coop_strategy._compute_joint_loss(experiences)

                # 计算总损失
                loss = (
                    policy_loss +
                    self.value_coef * value_loss -
                    self.entropy_coef * entropy +
                    (self.cooperation_coef * cooperation_loss if self.use_implicit_communication else 0.0) +
                    (self.cooperation_coef * coop_strategy_loss if self.use_cooperative_strategy else 0.0)
                )

                # 更新网络
                self.optimizer.zero_grad()
                loss.backward()
                nn.utils.clip_grad_norm_(self.network.parameters(), self.max_grad_norm)
                self.optimizer.step()

                # 累加损失
                total_policy_loss += policy_loss.item()
                total_value_loss += value_loss.item()
                total_entropy += entropy.item()
                if self.use_implicit_communication:
                    total_cooperation_loss += cooperation_loss.item()
                if self.use_cooperative_strategy:
                    total_coop_strategy_loss += coop_strategy_loss.item()

        # 清空存储的数据
        self.observations = []
        self.global_states = []
        self.actions = []
        self.action_log_probs = []
        self.rewards = []
        self.values = []
        self.dones = []

        # 计算平均损失
        num_batches = (len(observations) // self.batch_size + 1) * self.update_epochs
        avg_policy_loss = total_policy_loss / num_batches
        avg_value_loss = total_value_loss / num_batches
        avg_entropy = total_entropy / num_batches

        # 返回更新指标
        result = {
            'policy_loss': avg_policy_loss,
            'value_loss': avg_value_loss,
            'entropy': avg_entropy
        }

        # 如果使用隐式通信，添加协作损失
        if self.use_implicit_communication:
            avg_cooperation_loss = total_cooperation_loss / num_batches
            result['cooperation_loss'] = avg_cooperation_loss
            
        # 如果使用协作策略，添加协作策略损失
        if self.use_cooperative_strategy:
            avg_coop_strategy_loss = total_coop_strategy_loss / num_batches
            result['coop_strategy_loss'] = avg_coop_strategy_loss

        return result

    def _store_experience(self, experience: Experience) -> None:
        """
        存储经验数据

        Args:
            experience: 经验数据
        """
        # 提取观察和动作
        if hasattr(experience, 'observation'):
            obs_array = experience.observation
        elif isinstance(experience.state, State):
            obs_array = experience.state.to_dict().get('observation', np.zeros(self.obs_dim - 3))  # 减去角色编码维度
        else:
            obs_array = np.zeros(self.obs_dim - 3)  # 减去角色编码维度

        # 提取全局状态
        if hasattr(experience, 'global_state'):
            global_state_array = experience.global_state
        else:
            global_state_array = obs_array  # 使用局部观察作为全局状态

        # 提取动作
        action = experience.action

        # 转换为张量并预测动作概率和价值
        obs_tensor = torch.FloatTensor(obs_array).unsqueeze(0).to(self.device)
        state_tensor = torch.FloatTensor(global_state_array).unsqueeze(0).to(self.device) if self.use_central_critic else obs_tensor

        with torch.no_grad():
            # 获取动作对数概率
            if self.use_implicit_communication and self.role in ['farmer1', 'farmer2']:
                # 如果使用隐式通信，获取动作对数概率和通信嵌入
                action_logits, _ = self.network.forward_actor(obs_tensor)
                action_log_probs = F.log_softmax(action_logits, dim=-1)
                action_log_prob = action_log_probs[0, action].cpu().item()
            else:
                # 否则，直接获取动作对数概率
                action_log_probs = self.network.get_action_log_probs(obs_tensor)
                action_log_prob = action_log_probs[0, action].cpu().item()

            # 获取状态价值
            value = self.network.get_value(state_tensor).cpu().item()

        # 存储数据
        self.observations.append(obs_array)
        self.global_states.append(global_state_array)
        self.actions.append(action)
        self.action_log_probs.append(action_log_prob)
        self.rewards.append(experience.reward)
        self.values.append(value)
        self.dones.append(float(experience.done))

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            统计信息
        """
        # 获取父类统计信息
        stats = super().get_stats()

        # 添加角色特定的统计信息
        stats['role'] = self.role

        # 添加隐式通信相关的统计信息
        if self.use_implicit_communication:
            stats['implicit_communication'] = {
                'enabled': True,
                'communication_dim': self.communication_dim,
                'cooperation_coef': self.cooperation_coef,
                'cooperation_stats': self.cooperation_stats
            }

        return stats

    def get_cooperation_stats(self) -> Dict[str, Any]:
        """
        获取协作统计信息

        Returns:
            协作统计信息
        """
        if not self.use_implicit_communication:
            return {'enabled': False}

        return {
            'enabled': True,
            'total_cooperation_rewards': self.cooperation_stats['total_cooperation_rewards'],
            'cooperation_count': self.cooperation_stats['cooperation_count'],
            'avg_cooperation_reward': self.cooperation_stats['avg_cooperation_reward'],
            'joint_play_success': self.cooperation_stats['joint_play_success'],
            'joint_play_attempts': self.cooperation_stats['joint_play_attempts'],
            'joint_play_success_rate': (
                self.cooperation_stats['joint_play_success'] /
                max(1, self.cooperation_stats['joint_play_attempts'])
            ),
            'redundant_play_count': self.cooperation_stats['redundant_play_count']
        }

    def update_cooperation_stats(self, joint_play_success: bool = False, redundant_play: bool = False) -> None:
        """
        更新协作统计信息

        Args:
            joint_play_success: 是否成功进行了联合出牌
            redundant_play: 是否进行了冗余出牌
        """
        if not self.use_implicit_communication:
            return

        # 更新联合出牌统计
        if joint_play_success:
            self.cooperation_stats['joint_play_success'] += 1
            self.cooperation_stats['joint_play_attempts'] += 1
        elif joint_play_success is False:  # 明确是失败的联合出牌尝试
            self.cooperation_stats['joint_play_attempts'] += 1

        # 更新冗余出牌统计
        if redundant_play:
            self.cooperation_stats['redundant_play_count'] += 1