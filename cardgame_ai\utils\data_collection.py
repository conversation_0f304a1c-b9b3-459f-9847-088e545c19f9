"""
人机交互数据收集模块

提供收集人机对战过程中的关键数据的功能，包括状态、AI建议、人类实际决策、
可选的人类反馈等，为后续RLHF和人类策略网络训练准备数据。
"""

import os
import json
import time
import logging
import datetime
from typing import Dict, Any, Optional
import numpy as np

# 配置日志
logger = logging.getLogger(__name__)


class InteractionDataCollector:
    """
    人机交互数据收集器

    收集人机对战过程中的关键数据，包括状态、AI建议、人类实际决策、
    可选的人类反馈等，为后续RLHF和人类策略网络训练准备数据。
    """

    def __init__(self, data_dir: str = "data/human_interaction",
                 enabled: bool = True,
                 format: str = "jsonl",
                 buffer_size: int = 100):
        """
        初始化数据收集器

        Args:
            data_dir (str): 数据保存目录
            enabled (bool): 是否启用数据收集
            format (str): 数据保存格式，支持 jsonl, csv
            buffer_size (int): 缓冲区大小，达到此大小时自动保存
        """
        self.data_dir = data_dir
        self.enabled = enabled
        self.format = format
        self.buffer_size = buffer_size

        # 创建数据目录
        if self.enabled:
            os.makedirs(self.data_dir, exist_ok=True)
            os.makedirs(os.path.join(self.data_dir, "games"), exist_ok=True)
            os.makedirs(os.path.join(self.data_dir, "feedback"), exist_ok=True)
            os.makedirs(os.path.join(self.data_dir, "interactions"), exist_ok=True)

        # 初始化数据缓冲区
        self.buffer = []

        # 当前会话ID
        self.session_id = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

        logger.info(f"初始化数据收集器: 目录={data_dir}, 启用={enabled}, 格式={format}")

    def log_interaction(self,
                       game_state: Any,
                       ai_suggestion: Any,
                       human_action: Any,
                       game_id: str = None,
                       human_feedback: Optional[Dict[str, Any]] = None) -> None:
        """
        记录人机交互数据

        Args:
            game_state: 游戏状态
            ai_suggestion: AI建议的动作
            human_action: 人类实际执行的动作
            game_id: 游戏ID，如果为None则使用时间戳
            human_feedback: 人类反馈，可选
        """
        if not self.enabled:
            return

        # 生成游戏ID（如果未提供）
        if game_id is None:
            game_id = f"game_{int(time.time())}"

        # 创建数据条目
        entry = {
            "timestamp": time.time(),
            "datetime": datetime.datetime.now().isoformat(),
            "game_id": game_id,
            "session_id": self.session_id,
            "game_state": self._serialize_state(game_state),
            "ai_suggestion": self._serialize_action(ai_suggestion),
            "human_action": self._serialize_action(human_action)
        }

        # 添加人类反馈（如果有）
        if human_feedback is not None:
            entry["human_feedback"] = human_feedback

        # 添加到缓冲区
        self.buffer.append(entry)

        # 如果缓冲区达到指定大小，自动保存
        if len(self.buffer) >= self.buffer_size:
            self.save_buffer()

        return entry

    def _serialize_state(self, state: Any) -> Dict[str, Any]:
        """
        序列化游戏状态

        Args:
            state: 游戏状态

        Returns:
            Dict[str, Any]: 序列化后的状态
        """
        # 如果状态有to_dict方法，使用该方法
        if hasattr(state, 'to_dict') and callable(getattr(state, 'to_dict')):
            return state.to_dict()

        # 如果状态有to_json方法，使用该方法
        if hasattr(state, 'to_json') and callable(getattr(state, 'to_json')):
            return json.loads(state.to_json())

        # 如果状态是字典，直接返回
        if isinstance(state, dict):
            return state

        # 如果状态是NumPy数组，转换为列表
        if isinstance(state, np.ndarray):
            return state.tolist()

        # 其他情况，尝试转换为字符串
        return {"raw_state": str(state)}

    def _serialize_action(self, action: Any) -> Dict[str, Any]:
        """
        序列化动作

        Args:
            action: 动作

        Returns:
            Dict[str, Any]: 序列化后的动作
        """
        # 如果动作是None，返回空动作
        if action is None:
            return {"action_type": "pass"}

        # 如果动作有to_dict方法，使用该方法
        if hasattr(action, 'to_dict') and callable(getattr(action, 'to_dict')):
            return action.to_dict()

        # 如果动作有to_json方法，使用该方法
        if hasattr(action, 'to_json') and callable(getattr(action, 'to_json')):
            return json.loads(action.to_json())

        # 如果动作是字典，直接返回
        if isinstance(action, dict):
            return action

        # 如果动作是列表或元组，转换为列表
        if isinstance(action, (list, tuple)):
            return {"action_list": list(action)}

        # 如果动作是整数或浮点数，转换为数值
        if isinstance(action, (int, float)):
            return {"action_value": action}

        # 其他情况，尝试转换为字符串
        return {"action_raw": str(action)}

    def save_buffer(self) -> None:
        """
        保存缓冲区数据到文件
        """
        if not self.enabled or not self.buffer:
            return

        # 生成文件名
        timestamp = int(time.time())
        filename = f"interactions_{self.session_id}_{timestamp}.{self.format}"
        filepath = os.path.join(self.data_dir, "interactions", filename)

        try:
            # 根据格式保存数据
            if self.format == "jsonl":
                with open(filepath, 'w', encoding='utf-8') as f:
                    for entry in self.buffer:
                        f.write(json.dumps(entry, ensure_ascii=False) + '\n')
            elif self.format == "csv":
                # CSV格式需要更复杂的处理，这里简化处理
                import csv
                with open(filepath, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    # 写入表头
                    if self.buffer:
                        writer.writerow(self.buffer[0].keys())
                        # 写入数据
                        for entry in self.buffer:
                            writer.writerow(entry.values())

            logger.info(f"保存交互数据: {filepath} ({len(self.buffer)}条记录)")

            # 清空缓冲区
            self.buffer = []
        except Exception as e:
            logger.error(f"保存交互数据失败: {e}")

    def log_feedback(self, game_id: str, feedback: Dict[str, Any]) -> None:
        """
        记录人类反馈

        Args:
            game_id: 游戏ID
            feedback: 反馈数据
        """
        if not self.enabled:
            return

        # 添加时间戳
        feedback_data = feedback.copy()
        feedback_data["timestamp"] = time.time()
        feedback_data["datetime"] = datetime.datetime.now().isoformat()
        feedback_data["game_id"] = game_id
        feedback_data["session_id"] = self.session_id

        # 保存反馈
        feedback_dir = os.path.join(self.data_dir, "feedback")
        os.makedirs(feedback_dir, exist_ok=True)

        # 生成文件名
        filename = f"{game_id}_feedback.json"
        filepath = os.path.join(feedback_dir, filename)

        try:
            # 如果文件已存在，读取现有数据
            existing_data = []
            if os.path.exists(filepath):
                with open(filepath, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)

            # 添加新数据
            if isinstance(existing_data, list):
                existing_data.append(feedback_data)
            else:
                existing_data = [feedback_data]

            # 保存数据
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(existing_data, f, indent=4, ensure_ascii=False)

            logger.info(f"保存反馈数据: {filepath}")
        except Exception as e:
            logger.error(f"保存反馈数据失败: {e}")

    def get_data_stats(self) -> Dict[str, Any]:
        """
        获取数据统计信息

        Returns:
            Dict[str, Any]: 数据统计信息
        """
        if not self.enabled:
            return {"enabled": False}

        stats = {
            "enabled": True,
            "session_id": self.session_id,
            "data_dir": self.data_dir,
            "buffer_size": len(self.buffer),
            "interactions": 0,
            "feedback": 0,
            "games": 0
        }

        # 统计交互数据
        interactions_dir = os.path.join(self.data_dir, "interactions")
        if os.path.exists(interactions_dir):
            interaction_files = [f for f in os.listdir(interactions_dir) if f.endswith(f".{self.format}")]
            stats["interactions"] = len(interaction_files)

        # 统计反馈数据
        feedback_dir = os.path.join(self.data_dir, "feedback")
        if os.path.exists(feedback_dir):
            feedback_files = [f for f in os.listdir(feedback_dir) if f.endswith(".json")]
            stats["feedback"] = len(feedback_files)

        # 统计游戏数据
        games_dir = os.path.join(self.data_dir, "games")
        if os.path.exists(games_dir):
            game_files = [f for f in os.listdir(games_dir) if f.endswith(".json")]
            stats["games"] = len(game_files)

        return stats

    def close(self) -> None:
        """
        关闭数据收集器，保存所有缓冲数据
        """
        if self.enabled and self.buffer:
            self.save_buffer()
            logger.info(f"关闭数据收集器: {self.session_id}")


# 辅助函数
def log_interaction_data(log_file_path: str, game_state, ai_suggestion, human_action, human_feedback=None):
    """
    记录交互数据的简化函数

    Args:
        log_file_path (str): 日志文件路径
        game_state: 游戏状态
        ai_suggestion: AI建议的动作
        human_action: 人类实际执行的动作
        human_feedback: 人类反馈，可选
    """
    # 创建数据条目
    data_entry = {
        'timestamp': time.time(),
        'datetime': datetime.datetime.now().isoformat(),
        'game_state': _serialize_object(game_state),
        'ai_suggestion': _serialize_object(ai_suggestion),
        'human_action': _serialize_object(human_action),
        'human_feedback': human_feedback
    }

    # 确保目录存在
    os.makedirs(os.path.dirname(log_file_path), exist_ok=True)

    # 写入数据
    with open(log_file_path, 'a', encoding='utf-8') as f:
        f.write(json.dumps(data_entry, ensure_ascii=False) + '\n')


def _serialize_object(obj):
    """
    序列化对象的辅助函数

    Args:
        obj: 要序列化的对象

    Returns:
        序列化后的对象
    """
    # 如果对象有to_dict方法，使用该方法
    if hasattr(obj, 'to_dict') and callable(getattr(obj, 'to_dict')):
        return obj.to_dict()

    # 如果对象有to_json方法，使用该方法
    if hasattr(obj, 'to_json') and callable(getattr(obj, 'to_json')):
        return json.loads(obj.to_json())

    # 如果对象是字典，直接返回
    if isinstance(obj, dict):
        return obj

    # 如果对象是列表或元组，转换为列表
    if isinstance(obj, (list, tuple)):
        return list(obj)

    # 如果对象是NumPy数组，转换为列表
    if isinstance(obj, np.ndarray):
        return obj.tolist()

    # 其他情况，尝试转换为字符串
    return str(obj)
