"""
游戏模块

提供各种卡牌游戏的实现，包括状态表示、规则和环境。
"""

# 导入游戏组件
from cardgame_ai.games.common.game_adapter import GameAdapter, IdentityAdapter
from cardgame_ai.games.common.game_descriptor import GameDescriptor

# 导入斗地主游戏
from cardgame_ai.games.doudizhu import Card, CardSuit, CardRank, Deck, CardGroup, CardGroupType, DouDizhuState, DouDizhuEnvironment

# 导出游戏
from cardgame_ai.games.doudizhu import Card, CardSuit, CardRank, Deck
from cardgame_ai.games.doudizhu import CardGroup, CardGroupType
from cardgame_ai.games.doudizhu import DouDizhuState, DouDizhuEnvironment

# 游戏类型别名定义
DoudizhuGame = DouDizhuEnvironment
DoudizhuAction = CardGroup

# 所有支持的游戏列表
SUPPORTED_GAMES = {
    'doudizhu_classic': Do<PERSON>zhuG<PERSON>,
    'doudizhu_2v1': Do<PERSON><PERSON><PERSON><PERSON><PERSON>
}

# 获取游戏
def get_game(game_name: str):
    """
    通过名称获取游戏类

    Args:
        game_name (str): 游戏名称

    Returns:
        游戏类

    Raises:
        ValueError: 当游戏名称不受支持时抛出
    """
    if game_name not in SUPPORTED_GAMES:
        raise ValueError(f"不支持的游戏: {game_name}，支持的游戏有: {list(SUPPORTED_GAMES.keys())}")

    return SUPPORTED_GAMES[game_name]
