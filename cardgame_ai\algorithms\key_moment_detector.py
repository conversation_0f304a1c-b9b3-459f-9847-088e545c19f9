"""
关键决策点检测器模块 - 兼容性重定向

这个模块是为了保持向后兼容性而存在的。
它重新导出 cardgame_ai.algorithms.components.key_moment_detector 模块中的 KeyMomentDetector 类。
"""

import logging
import warnings

# 配置日志
logger = logging.getLogger(__name__)

# 显示警告
warnings.warn(
    "从 cardgame_ai.algorithms.key_moment_detector 导入已过时，"
    "请改用 cardgame_ai.algorithms.components.key_moment_detector",
    DeprecationWarning,
    stacklevel=2
)

# 重新导出 KeyMomentDetector
from cardgame_ai.algorithms.components.key_moment_detector import KeyMomentDetector

# 记录一条日志
logger.info("重定向 cardgame_ai.algorithms.key_moment_detector 到 cardgame_ai.algorithms.components.key_moment_detector")
