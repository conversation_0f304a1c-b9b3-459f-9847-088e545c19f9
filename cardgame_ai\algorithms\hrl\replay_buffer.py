"""
经验回放缓冲区模块

实现层次化强化学习中的经验回放缓冲区，用于存储和采样经验。
"""

import random
import numpy as np
import torch
from typing import Dict, List, Tuple, Any, Optional, Union


class ReplayBuffer:
    """
    经验回放缓冲区
    
    用于存储和采样经验，支持层次化强化学习。
    """
    
    def __init__(self, capacity: int):
        """
        初始化经验回放缓冲区
        
        Args:
            capacity: 缓冲区容量
        """
        self.capacity = capacity
        self.buffer = []
        self.position = 0
    
    def add(
        self, 
        state: np.ndarray, 
        high_level_action: int, 
        low_level_action: int, 
        reward: float, 
        next_state: np.ndarray, 
        done: bool
    ) -> None:
        """
        添加经验到缓冲区
        
        Args:
            state: 状态
            high_level_action: 高层动作
            low_level_action: 低层动作
            reward: 奖励
            next_state: 下一个状态
            done: 是否结束
        """
        if len(self.buffer) < self.capacity:
            self.buffer.append(None)
        self.buffer[self.position] = (state, high_level_action, low_level_action, reward, next_state, done)
        self.position = (self.position + 1) % self.capacity
    
    def sample(self, batch_size: int) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        从缓冲区中采样一批经验
        
        Args:
            batch_size: 批次大小
            
        Returns:
            状态、高层动作、低层动作、奖励、下一个状态、是否结束
        """
        batch = random.sample(self.buffer, batch_size)
        states, high_level_actions, low_level_actions, rewards, next_states, dones = zip(*batch)
        
        # 转换为张量
        states = torch.FloatTensor(np.array(states))
        high_level_actions = torch.LongTensor(np.array(high_level_actions))
        low_level_actions = torch.LongTensor(np.array(low_level_actions))
        rewards = torch.FloatTensor(np.array(rewards))
        next_states = torch.FloatTensor(np.array(next_states))
        dones = torch.FloatTensor(np.array(dones))
        
        return states, high_level_actions, low_level_actions, rewards, next_states, dones
    
    def __len__(self) -> int:
        """
        返回缓冲区中的经验数量
        
        Returns:
            经验数量
        """
        return len(self.buffer)


class PrioritizedReplayBuffer(ReplayBuffer):
    """
    优先经验回放缓冲区
    
    基于TD误差的优先级采样，提高训练效率。
    """
    
    def __init__(self, capacity: int, alpha: float = 0.6, beta: float = 0.4, beta_increment: float = 0.001):
        """
        初始化优先经验回放缓冲区
        
        Args:
            capacity: 缓冲区容量
            alpha: 优先级指数，控制优先级的影响程度
            beta: 重要性采样指数，用于纠正优先级采样引入的偏差
            beta_increment: beta的增量，随着训练的进行，beta会逐渐增加到1
        """
        super().__init__(capacity)
        self.alpha = alpha
        self.beta = beta
        self.beta_increment = beta_increment
        self.priorities = np.zeros((capacity,), dtype=np.float32)
        self.max_priority = 1.0
    
    def add(
        self, 
        state: np.ndarray, 
        high_level_action: int, 
        low_level_action: int, 
        reward: float, 
        next_state: np.ndarray, 
        done: bool
    ) -> None:
        """
        添加经验到缓冲区
        
        Args:
            state: 状态
            high_level_action: 高层动作
            low_level_action: 低层动作
            reward: 奖励
            next_state: 下一个状态
            done: 是否结束
        """
        # 新经验的优先级设为最大优先级
        max_priority = self.max_priority if self.buffer else 1.0
        
        if len(self.buffer) < self.capacity:
            self.buffer.append(None)
            self.priorities = np.append(self.priorities, max_priority)
        
        self.buffer[self.position] = (state, high_level_action, low_level_action, reward, next_state, done)
        self.priorities[self.position] = max_priority
        self.position = (self.position + 1) % self.capacity
    
    def sample(self, batch_size: int) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, List[int]]:
        """
        从缓冲区中采样一批经验
        
        Args:
            batch_size: 批次大小
            
        Returns:
            状态、高层动作、低层动作、奖励、下一个状态、是否结束、重要性权重、索引
        """
        # 增加beta值
        self.beta = min(1.0, self.beta + self.beta_increment)
        
        # 计算采样概率
        priorities = self.priorities[:len(self.buffer)]
        probabilities = priorities ** self.alpha
        probabilities /= probabilities.sum()
        
        # 采样索引
        indices = np.random.choice(len(self.buffer), batch_size, p=probabilities)
        
        # 获取经验
        batch = [self.buffer[idx] for idx in indices]
        states, high_level_actions, low_level_actions, rewards, next_states, dones = zip(*batch)
        
        # 计算重要性权重
        weights = (len(self.buffer) * probabilities[indices]) ** (-self.beta)
        weights /= weights.max()
        
        # 转换为张量
        states = torch.FloatTensor(np.array(states))
        high_level_actions = torch.LongTensor(np.array(high_level_actions))
        low_level_actions = torch.LongTensor(np.array(low_level_actions))
        rewards = torch.FloatTensor(np.array(rewards))
        next_states = torch.FloatTensor(np.array(next_states))
        dones = torch.FloatTensor(np.array(dones))
        weights = torch.FloatTensor(weights)
        
        return states, high_level_actions, low_level_actions, rewards, next_states, dones, weights, indices
    
    def update_priorities(self, indices: List[int], priorities: np.ndarray) -> None:
        """
        更新经验的优先级
        
        Args:
            indices: 经验的索引
            priorities: 新的优先级
        """
        for idx, priority in zip(indices, priorities):
            self.priorities[idx] = priority
            self.max_priority = max(self.max_priority, priority)
