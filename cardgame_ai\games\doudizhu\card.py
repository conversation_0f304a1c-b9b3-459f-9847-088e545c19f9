"""
扑克牌模块

定义扑克牌的基本属性和方法。
"""
from enum import Enum, IntEnum
from typing import List, Dict, Any, Optional


class CardSuit(IntEnum):
    """
    扑克牌花色枚举
    
    按照从小到大的顺序：梅花、方块、红桃、黑桃
    """
    CLUB = 0       # 梅花 ♣
    DIAMOND = 1    # 方块 ♦
    HEART = 2      # 红桃 ♥
    SPADE = 3      # 黑桃 ♠
    
    @classmethod
    def from_char(cls, char: str) -> 'CardSuit':
        """
        从字符创建花色
        
        Args:
            char (str): 花色字符，'C'/'c'表示梅花，'D'/'d'表示方块，'H'/'h'表示红桃，'S'/'s'表示黑桃
            
        Returns:
            CardSuit: 花色枚举值
            
        Raises:
            ValueError: 无效的花色字符
        """
        char = char.upper()
        if char == 'C':
            return cls.CLUB
        elif char == 'D':
            return cls.DIAMOND
        elif char == 'H':
            return cls.HEART
        elif char == 'S':
            return cls.SPADE
        else:
            raise ValueError(f"无效的花色字符: {char}")
    
    def to_char(self) -> str:
        """
        转换为字符表示
        
        Returns:
            str: 花色字符，'C'表示梅花，'D'表示方块，'H'表示红桃，'S'表示黑桃
        """
        if self == CardSuit.CLUB:
            return 'C'
        elif self == CardSuit.DIAMOND:
            return 'D'
        elif self == CardSuit.HEART:
            return 'H'
        elif self == CardSuit.SPADE:
            return 'S'
    
    def to_unicode(self) -> str:
        """
        转换为Unicode字符表示
        
        Returns:
            str: Unicode花色字符，♣表示梅花，♦表示方块，♥表示红桃，♠表示黑桃
        """
        if self == CardSuit.CLUB:
            return '♣'
        elif self == CardSuit.DIAMOND:
            return '♦'
        elif self == CardSuit.HEART:
            return '♥'
        elif self == CardSuit.SPADE:
            return '♠'


class CardRank(IntEnum):
    """
    扑克牌点数枚举
    
    按照斗地主规则从小到大的顺序：3, 4, 5, 6, 7, 8, 9, 10, J, Q, K, A, 2, 小王, 大王
    """
    THREE = 0      # 3
    FOUR = 1       # 4
    FIVE = 2       # 5
    SIX = 3        # 6
    SEVEN = 4      # 7
    EIGHT = 5      # 8
    NINE = 6       # 9
    TEN = 7        # 10
    JACK = 8       # J
    QUEEN = 9      # Q
    KING = 10      # K
    ACE = 11       # A
    TWO = 12       # 2
    SMALL_JOKER = 13  # 小王
    BIG_JOKER = 14    # 大王
    
    @classmethod
    def from_char(cls, char: str) -> 'CardRank':
        """
        从字符创建点数
        
        Args:
            char (str): 点数字符，'3'-'10', 'J'/'j', 'Q'/'q', 'K'/'k', 'A'/'a', '2', 'X'/'x'(小王), 'D'/'d'(大王)
            
        Returns:
            CardRank: 点数枚举值
            
        Raises:
            ValueError: 无效的点数字符
        """
        char = char.upper()
        if char == '3':
            return cls.THREE
        elif char == '4':
            return cls.FOUR
        elif char == '5':
            return cls.FIVE
        elif char == '6':
            return cls.SIX
        elif char == '7':
            return cls.SEVEN
        elif char == '8':
            return cls.EIGHT
        elif char == '9':
            return cls.NINE
        elif char == '10' or char == 'T':
            return cls.TEN
        elif char == 'J':
            return cls.JACK
        elif char == 'Q':
            return cls.QUEEN
        elif char == 'K':
            return cls.KING
        elif char == 'A':
            return cls.ACE
        elif char == '2':
            return cls.TWO
        elif char == 'X':
            return cls.SMALL_JOKER
        elif char == 'D':
            return cls.BIG_JOKER
        else:
            raise ValueError(f"无效的点数字符: {char}")
    
    def to_char(self) -> str:
        """
        转换为字符表示
        
        Returns:
            str: 点数字符，'3'-'10', 'J', 'Q', 'K', 'A', '2', 'X'(小王), 'D'(大王)
        """
        if self == CardRank.THREE:
            return '3'
        elif self == CardRank.FOUR:
            return '4'
        elif self == CardRank.FIVE:
            return '5'
        elif self == CardRank.SIX:
            return '6'
        elif self == CardRank.SEVEN:
            return '7'
        elif self == CardRank.EIGHT:
            return '8'
        elif self == CardRank.NINE:
            return '9'
        elif self == CardRank.TEN:
            return '10'
        elif self == CardRank.JACK:
            return 'J'
        elif self == CardRank.QUEEN:
            return 'Q'
        elif self == CardRank.KING:
            return 'K'
        elif self == CardRank.ACE:
            return 'A'
        elif self == CardRank.TWO:
            return '2'
        elif self == CardRank.SMALL_JOKER:
            return 'X'
        elif self == CardRank.BIG_JOKER:
            return 'D'


class Card:
    """
    扑克牌类
    
    表示一张扑克牌，包含花色和点数。
    """
    
    def __init__(self, rank: CardRank, suit: Optional[CardSuit] = None):
        """
        初始化扑克牌
        
        Args:
            rank (CardRank): 点数
            suit (Optional[CardSuit], optional): 花色，对于王牌可以为None. Defaults to None.
        """
        self.rank = rank
        self.suit = suit
        
        # 王牌没有花色
        if rank in [CardRank.SMALL_JOKER, CardRank.BIG_JOKER]:
            self.suit = None
    
    def __eq__(self, other: 'Card') -> bool:
        """
        比较两张牌是否相等
        
        Args:
            other (Card): 另一张牌
            
        Returns:
            bool: 是否相等
        """
        if not isinstance(other, Card):
            return False
        
        # 王牌只比较点数
        if self.rank in [CardRank.SMALL_JOKER, CardRank.BIG_JOKER]:
            return self.rank == other.rank
        
        return self.rank == other.rank and self.suit == other.suit
    
    def __lt__(self, other: 'Card') -> bool:
        """
        比较两张牌的大小
        
        Args:
            other (Card): 另一张牌
            
        Returns:
            bool: 是否小于另一张牌
        """
        if not isinstance(other, Card):
            raise TypeError("只能与Card类型比较")
        
        # 先比较点数
        if self.rank != other.rank:
            return self.rank < other.rank
        
        # 点数相同，比较花色（王牌没有花色）
        if self.suit is not None and other.suit is not None:
            return self.suit < other.suit
        
        # 王牌或点数和花色都相同
        return False
    
    def __hash__(self) -> int:
        """
        计算哈希值
        
        Returns:
            int: 哈希值
        """
        return hash((self.rank, self.suit))
    
    def __str__(self) -> str:
        """
        转换为字符串表示
        
        Returns:
            str: 字符串表示，如"♥A"表示红桃A
        """
        if self.rank == CardRank.SMALL_JOKER:
            return "小王"
        elif self.rank == CardRank.BIG_JOKER:
            return "大王"
        else:
            return f"{self.suit.to_unicode()}{self.rank.to_char()}"
    
    def __repr__(self) -> str:
        """
        转换为详细字符串表示
        
        Returns:
            str: 详细字符串表示
        """
        if self.rank == CardRank.SMALL_JOKER:
            return "Card(SMALL_JOKER)"
        elif self.rank == CardRank.BIG_JOKER:
            return "Card(BIG_JOKER)"
        else:
            return f"Card({self.rank.name}, {self.suit.name})"
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典表示
        
        Returns:
            Dict[str, Any]: 字典表示
        """
        return {
            'rank': int(self.rank),
            'suit': None if self.suit is None else int(self.suit)
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Card':
        """
        从字典创建扑克牌
        
        Args:
            data (Dict[str, Any]): 字典表示
            
        Returns:
            Card: 扑克牌对象
        """
        rank = CardRank(data['rank'])
        suit = None if data['suit'] is None else CardSuit(data['suit'])
        return cls(rank, suit)
    
    @classmethod
    def from_string(cls, card_str: str) -> 'Card':
        """
        从字符串创建扑克牌
        
        Args:
            card_str (str): 字符串表示，如"H3"表示红桃3，"X"表示小王，"D"表示大王
            
        Returns:
            Card: 扑克牌对象
            
        Raises:
            ValueError: 无效的牌字符串
        """
        card_str = card_str.upper()
        
        # 处理王牌
        if card_str == 'X':
            return cls(CardRank.SMALL_JOKER)
        elif card_str == 'D':
            return cls(CardRank.BIG_JOKER)
        
        # 处理普通牌
        if len(card_str) < 2:
            raise ValueError(f"无效的牌字符串: {card_str}")
        
        # 处理10（两个字符）
        if card_str[0] == '1' and len(card_str) >= 3 and card_str[1] == '0':
            suit = CardSuit.from_char(card_str[2])
            rank = CardRank.TEN
        else:
            suit = CardSuit.from_char(card_str[0])
            rank = CardRank.from_char(card_str[1:])
        
        return cls(rank, suit)
    
    def to_onehot(self, with_suit: bool = False) -> List[int]:
        """
        转换为独热编码
        
        Args:
            with_suit (bool, optional): 是否包含花色信息. Defaults to False.
            
        Returns:
            List[int]: 独热编码列表
        """
        if with_suit:
            # 包含花色信息，共15*4+2=62维（王牌没有花色）
            encoding = [0] * 62
            if self.rank in [CardRank.SMALL_JOKER, CardRank.BIG_JOKER]:
                # 王牌
                if self.rank == CardRank.SMALL_JOKER:
                    encoding[60] = 1
                else:  # BIG_JOKER
                    encoding[61] = 1
            else:
                # 普通牌
                index = int(self.rank) * 4 + int(self.suit)
                encoding[index] = 1
        else:
            # 不包含花色信息，共15维
            encoding = [0] * 15
            encoding[int(self.rank)] = 1
        
        return encoding
