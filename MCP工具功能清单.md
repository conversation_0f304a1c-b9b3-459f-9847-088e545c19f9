# MCP工具功能清单
## Model Context Protocol (MCP) Tools Inventory

### 📅 更新时间
最后更新：2025年1月

### 🎯 概述
本文档列出了当前可用的所有MCP工具及其详细功能说明，为AI助手提供完整的工具调用参考。

---

## 🧠 思维推理工具 (Cognitive Reasoning Tools)

### 1. **sequentialthinking_clear-thought**
**功能：** 连续思考和多步骤分析
**适用场景：**
- 复杂问题分解
- 多步骤逻辑推理
- 决策过程分析
- 综合规划

**核心特性：**
- 支持思维修订和分支
- 进度跟踪和上下文维护
- 可调整思考步骤数量
- 支持不确定性表达

### 2. **mentalmodel_clear-thought**
**功能：** 结构化心智模型分析
**内置模式：**
- `first_principles` - 第一性原理思维
- `opportunity_cost` - 机会成本分析
- `error_propagation` - 错误传播理解
- `rubber_duck` - 橡皮鸭调试
- `pareto_principle` - 帕累托原则(80/20法则)
- `occams_razor` - 奥卡姆剃刀

**适用场景：**
- 根本原因分析
- 权衡决策
- 系统优化
- 问题澄清

### 3. **collaborativereasoning_clear-thought**
**功能：** 多专家角色协作分析
**协作阶段：**
- problem-definition (问题定义)
- ideation (构思)
- critique (批评)
- integration (整合)
- decision (决策)
- reflection (反思)

**适用场景：**
- 复杂决策制定
- 多角度问题分析
- 利益相关者考量
- 创新方案设计

### 4. **decisionframework_clear-thought**
**功能：** 系统化决策分析
**分析类型：**
- expected-utility (期望效用)
- multi-criteria (多标准决策)
- maximin (最大最小策略)
- minimax-regret (最小最大后悔)
- satisficing (满意化决策)

**适用场景：**
- 重要决策制定
- 方案比较评估
- 风险评估
- 资源分配

### 5. **metacognitivemonitoring_clear-thought**
**功能：** 元认知监控和自我评估
**监控阶段：**
- knowledge-assessment (知识评估)
- planning (规划)
- execution (执行)
- monitoring (监控)
- evaluation (评估)
- reflection (反思)

**适用场景：**
- 知识边界评估
- 推理质量监控
- 不确定性管理
- 学习过程优化

### 6. **scientificmethod_clear-thought**
**功能：** 科学方法验证和假设检验
**科学流程：**
- observation (观察)
- question (提问)
- hypothesis (假设)
- experiment (实验)
- analysis (分析)
- conclusion (结论)
- iteration (迭代)

**适用场景：**
- 理论验证
- 实验设计
- 因果关系分析
- 系统性研究

### 7. **structuredargumentation_clear-thought**
**功能：** 结构化论证和辩证分析
**论证类型：**
- thesis (论题)
- antithesis (反题)
- synthesis (综合)
- objection (反对)
- rebuttal (反驳)

**适用场景：**
- 论证分析
- 观点辩论
- 逻辑验证
- 批判性思维

### 8. **debuggingapproach_clear-thought**
**功能：** 系统化调试和问题诊断
**调试方法：**
- binary_search (二分查找)
- reverse_engineering (逆向工程)
- divide_conquer (分而治之)
- backtracking (回溯)
- cause_elimination (原因排除)
- program_slicing (程序切片)

**适用场景：**
- 技术问题诊断
- 性能优化
- 错误定位
- 系统分析

### 9. **visualreasoning_clear-thought**
**功能：** 可视化思维和图形分析
**图表类型：**
- graph (图形)
- flowchart (流程图)
- stateDiagram (状态图)
- conceptMap (概念图)
- treeDiagram (树形图)
- custom (自定义)

**操作类型：**
- create (创建)
- update (更新)
- delete (删除)
- transform (转换)
- observe (观察)

**适用场景：**
- 架构设计
- 流程分析
- 概念可视化
- 系统建模

---

## 📚 文档和代码工具 (Documentation & Code Tools)

### 10. **resolve-library-id_context7-mcp**
**功能：** 解析库/包名称为Context7兼容的库ID
**适用场景：**
- 技术文档查询准备
- 库依赖分析
- 包管理

### 11. **get-library-docs_context7-mcp**
**功能：** 获取库的最新文档
**特性：**
- 支持版本指定
- 主题聚焦查询
- 令牌数量控制
**适用场景：**
- API文档查询
- 技术实现参考
- 最佳实践学习

### 12. **str-replace-editor**
**功能：** 精确文件编辑
**特性：**
- 多处同时替换
- 行号精确定位
- 支持插入和替换
**适用场景：**
- 代码修改
- 配置文件更新
- 文档编辑

### 13. **save-file**
**功能：** 创建新文件
**限制：** 最多300行内容
**适用场景：**
- 新代码文件创建
- 文档生成
- 配置文件创建

### 14. **view**
**功能：** 文件和目录查看
**特性：**
- 正则表达式搜索
- 行范围指定
- 上下文行控制
**适用场景：**
- 代码审查
- 文件内容检查
- 目录结构查看

### 15. **remove-files**
**功能：** 安全文件删除
**特性：** 可撤销的删除操作
**适用场景：**
- 清理临时文件
- 重构代码
- 项目整理

### 16. **codebase-retrieval**
**功能：** Augment的代码库上下文检索引擎
**特性：**
- 自然语言查询
- 实时索引
- 跨语言检索
**适用场景：**
- 代码理解
- 相关代码查找
- 架构分析

### 17. **diagnostics**
**功能：** 获取IDE错误和警告信息
**适用场景：**
- 代码质量检查
- 错误诊断
- 编译问题解决

---

## 🌐 网络和浏览工具 (Web & Browser Tools)

### 18. **web-search**
**功能：** Google搜索API
**特性：**
- 可配置结果数量(1-10)
- Markdown格式返回
**适用场景：**
- 信息查询
- 技术资料搜索
- 最新动态了解

### 19. **web-fetch**
**功能：** 网页内容获取和Markdown转换
**适用场景：**
- 网页内容分析
- 文档资料获取
- 在线资源整理

### 20. **open-browser**
**功能：** 在默认浏览器中打开URL
**注意：** 避免重复打开同一URL
**适用场景：**
- 用户浏览引导
- 外部资源访问

---

## 💻 系统和进程工具 (System & Process Tools)

### 21. **launch-process**
**功能：** 启动shell命令和进程
**模式：**
- 等待模式 (wait=true)
- 后台模式 (wait=false)
**适用场景：**
- 编译构建
- 测试执行
- 系统命令运行

### 22. **kill-process**
**功能：** 终止指定进程
**适用场景：**
- 进程管理
- 资源清理
- 错误恢复

### 23. **read-process**
**功能：** 读取进程输出
**特性：**
- 支持等待完成
- 超时控制
**适用场景：**
- 命令结果获取
- 进程状态监控

### 24. **write-process**
**功能：** 向进程写入输入
**适用场景：**
- 交互式命令
- 自动化输入
- 进程控制

### 25. **list-processes**
**功能：** 列出所有已知进程及状态
**适用场景：**
- 进程监控
- 资源管理
- 状态检查

### 26. **read-terminal**
**功能：** 读取VSCode终端输出
**特性：**
- 支持选中文本读取
- 全屏内容获取
**适用场景：**
- 终端输出分析
- 命令结果查看

---

## 🎨 可视化和记忆工具 (Visualization & Memory Tools)

### 27. **render-mermaid**
**功能：** 渲染Mermaid图表
**特性：**
- 交互式图表
- 缩放和复制功能
**适用场景：**
- 流程图创建
- 架构图绘制
- 概念图表达

### 28. **remember**
**功能：** 创建长期记忆
**特性：** 简洁的一句话记忆
**适用场景：**
- 重要信息记录
- 用户偏好保存
- 项目上下文维护

---

## 🎯 工具选择指南

### **按问题类型选择：**

**技术调试：**
- 主要：`debuggingapproach` + `codebase-retrieval`
- 辅助：`scientificmethod`

**复杂分析：**
- 主要：`sequentialthinking` + `mentalmodel`
- 辅助：`metacognitivemonitoring`

**决策制定：**
- 主要：`decisionframework` + `collaborativereasoning`
- 辅助：`structuredargumentation`

**系统设计：**
- 主要：`visualreasoning` + `sequentialthinking`
- 辅助：`mentalmodel`

**文档查询：**
- 主要：`resolve-library-id` + `get-library-docs`
- 辅助：`web-search`

### **按复杂度选择：**

**简单问题：** 直接回答，无需工具
**中等问题：** 1-2个主要工具
**复杂问题：** 多工具协作，3-5个工具组合

---

## 📋 使用原则

1. **需求驱动** - 根据实际问题选择工具
2. **质量优先** - 追求最高分析质量
3. **用户导向** - 考虑用户期望和偏好
4. **透明决策** - 解释工具选择理由
5. **灵活调整** - 根据反馈优化策略

---

## 🔄 更新记录

- 2025年1月：初始版本，包含28个工具的完整功能清单
- 待更新：根据工具变化和用户反馈持续更新
