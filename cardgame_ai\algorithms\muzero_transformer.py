"""
基于Transformer的MuZero算法

结合Transformer架构与MuZero算法，增强模型处理卡牌游戏中的序列信息和长期依赖的能力。
"""
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional, Any, Union

from cardgame_ai.core.base import State, Action, Experience, Batch
from cardgame_ai.core.algorithm import ModelBasedAlgorithm
from cardgame_ai.algorithms.mcts import MCTS
from cardgame_ai.algorithms.transformer_network import (
    TransformerRepresentationNetwork,
    TransformerPredictionNetwork,
    TransformerDynamicsNetwork
)


class MuZeroTransformerModel(nn.Module):
    """
    结合Transformer架构的MuZero模型
    
    整合表示网络、动态网络和预测网络，其中关键组件使用Transformer架构实现。
    """
    
    def __init__(
        self,
        input_dim: int,
        action_dim: int,
        state_dim: int = 64,
        hidden_dim: int = 128,
        num_heads: int = 4,
        num_layers: int = 2,
        dropout: float = 0.1,
        seq_len: int = 1,
        device: torch.device = None
    ):
        """
        初始化MuZeroTransformer模型
        
        Args:
            input_dim (int): 输入维度
            action_dim (int): 动作维度
            state_dim (int, optional): 状态维度. Defaults to 64.
            hidden_dim (int, optional): 隐藏层维度. Defaults to 128.
            num_heads (int, optional): 注意力头数量. Defaults to 4.
            num_layers (int, optional): Transformer层数量. Defaults to 2.
            dropout (float, optional): Dropout比率. Defaults to 0.1.
            seq_len (int, optional): 序列长度. Defaults to 1.
            device (torch.device, optional): 计算设备. Defaults to None.
        """
        super(MuZeroTransformerModel, self).__init__()
        
        if device is None:
            self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        else:
            self.device = device
            
        self.state_dim = state_dim
        self.action_dim = action_dim
        
        # 表示网络：观察 -> 隐藏状态
        self.representation_network = TransformerRepresentationNetwork(
            input_dim=input_dim,
            hidden_dim=hidden_dim,
            state_dim=state_dim,
            seq_len=seq_len,
            num_heads=num_heads,
            num_layers=num_layers,
            dropout=dropout
        )
        
        # 动态网络：(隐藏状态, 动作) -> (下一个隐藏状态, 奖励)
        self.dynamics_network = TransformerDynamicsNetwork(
            state_dim=state_dim,
            action_dim=action_dim,
            hidden_dim=hidden_dim,
            num_heads=num_heads,
            num_layers=num_layers,
            dropout=dropout
        )
        
        # 预测网络：隐藏状态 -> (策略, 价值)
        self.prediction_network = TransformerPredictionNetwork(
            state_dim=state_dim,
            action_dim=action_dim,
            hidden_dim=hidden_dim,
            num_heads=num_heads,
            num_layers=num_layers,
            dropout=dropout
        )
    
    def representation(self, observation: torch.Tensor) -> torch.Tensor:
        """
        表示函数：将观察转换为初始隐藏状态
        
        Args:
            observation (torch.Tensor): 观察张量
            
        Returns:
            torch.Tensor: 隐藏状态
        """
        return self.representation_network(observation)
    
    def dynamics(self, state: torch.Tensor, action: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        动态函数：预测下一个隐藏状态和奖励
        
        Args:
            state (torch.Tensor): 当前隐藏状态
            action (torch.Tensor): 执行的动作
            
        Returns:
            Tuple[torch.Tensor, torch.Tensor]: 下一个隐藏状态和奖励
        """
        return self.dynamics_network(state, action)
    
    def prediction(self, state: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        预测函数：从隐藏状态预测策略和价值
        
        Args:
            state (torch.Tensor): 隐藏状态
            
        Returns:
            Tuple[torch.Tensor, torch.Tensor]: 策略对数和价值
        """
        return self.prediction_network(state)
    
    def initial_inference(self, observation: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        初始推理：处理根状态
        
        Args:
            observation (torch.Tensor): 观察张量
            
        Returns:
            Dict[str, torch.Tensor]: 包含隐藏状态、策略对数和价值的字典
        """
        state = self.representation(observation)
        policy_logits, value = self.prediction(state)
        
        return {
            "state": state,
            "policy_logits": policy_logits,
            "value": value
        }
    
    def recurrent_inference(self, state: torch.Tensor, action: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        递归推理：处理非根状态
        
        Args:
            state (torch.Tensor): 当前隐藏状态
            action (torch.Tensor): 执行的动作
            
        Returns:
            Dict[str, torch.Tensor]: 包含下一个隐藏状态、奖励、策略对数和价值的字典
        """
        next_state, reward = self.dynamics(state, action)
        policy_logits, value = self.prediction(next_state)
        
        return {
            "next_state": next_state,
            "reward": reward,
            "policy_logits": policy_logits,
            "value": value
        }
    
    def forward(self, observation: torch.Tensor, actions: Optional[List[torch.Tensor]] = None) -> Dict[str, Any]:
        """
        前向传播：用于训练
        
        Args:
            observation (torch.Tensor): 初始观察
            actions (Optional[List[torch.Tensor]], optional): 动作序列. Defaults to None.
            
        Returns:
            Dict[str, Any]: 包含预测的状态、奖励、策略和价值的字典
        """
        # 初始表示
        state = self.representation(observation)
        policy_logits, value = self.prediction(state)
        
        predictions = {
            "policy_logits": [policy_logits],
            "values": [value],
            "rewards": []
        }
        
        # 如果提供了动作序列，模拟未来步骤
        if actions is not None:
            for action in actions:
                state, reward = self.dynamics(state, action)
                policy_logits, value = self.prediction(state)
                
                predictions["policy_logits"].append(policy_logits)
                predictions["values"].append(value)
                predictions["rewards"].append(reward)
        
        return predictions


class MuZeroTransformer(ModelBasedAlgorithm):
    """
    基于Transformer的MuZero算法
    
    结合Transformer架构与MuZero算法的实现，增强模型处理卡牌游戏中的序列信息和长期依赖的能力。
    """
    
    def __init__(
        self,
        input_dim: int,
        action_dim: int,
        state_dim: int = 64,
        hidden_dim: int = 128,
        num_simulations: int = 50,
        discount: float = 0.99,
        dirichlet_alpha: float = 0.25,
        exploration_fraction: float = 0.25,
        pb_c_base: float = 19652,
        pb_c_init: float = 1.25,
        weight_decay: float = 1e-4,
        lr: float = 0.001,
        num_heads: int = 4,
        num_layers: int = 2,
        dropout: float = 0.1,
        seq_len: int = 1,
        device: torch.device = None
    ):
        """
        初始化MuZeroTransformer算法
        
        Args:
            input_dim (int): 输入维度
            action_dim (int): 动作维度
            state_dim (int, optional): 状态维度. Defaults to 64.
            hidden_dim (int, optional): 隐藏层维度. Defaults to 128.
            num_simulations (int, optional): MCTS模拟次数. Defaults to 50.
            discount (float, optional): 折扣因子. Defaults to 0.99.
            dirichlet_alpha (float, optional): Dirichlet噪声参数. Defaults to 0.25.
            exploration_fraction (float, optional): 探索比例. Defaults to 0.25.
            pb_c_base (float, optional): PUCT常数基数. Defaults to 19652.
            pb_c_init (float, optional): PUCT初始常数. Defaults to 1.25.
            weight_decay (float, optional): 权重衰减. Defaults to 1e-4.
            lr (float, optional): 学习率. Defaults to 0.001.
            num_heads (int, optional): 注意力头数量. Defaults to 4.
            num_layers (int, optional): Transformer层数量. Defaults to 2.
            dropout (float, optional): Dropout比率. Defaults to 0.1.
            seq_len (int, optional): 序列长度. Defaults to 1.
            device (torch.device, optional): 计算设备. Defaults to None.
        """
        if device is None:
            self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        else:
            self.device = device
            
        # 创建模型
        self.model = MuZeroTransformerModel(
            input_dim=input_dim,
            action_dim=action_dim,
            state_dim=state_dim,
            hidden_dim=hidden_dim,
            num_heads=num_heads,
            num_layers=num_layers,
            dropout=dropout,
            seq_len=seq_len,
            device=self.device
        ).to(self.device)
        
        # 优化器
        self.optimizer = torch.optim.Adam(
            self.model.parameters(),
            lr=lr,
            weight_decay=weight_decay
        )
        
        # MCTS参数
        self.num_simulations = num_simulations
        self.discount = discount
        self.dirichlet_alpha = dirichlet_alpha
        self.exploration_fraction = exploration_fraction
        self.pb_c_base = pb_c_base
        self.pb_c_init = pb_c_init
        
        # 损失权重
        self.value_loss_weight = 1.0
        self.reward_loss_weight = 1.0
        self.policy_loss_weight = 1.0
        
        # 创建MCTS实例
        self.mcts = MCTS(
            pb_c_base=pb_c_base,
            pb_c_init=pb_c_init,
            discount=discount
        )
    
    def predict_action(self, state: State, temperature: float = 1.0, deterministic: bool = False) -> Action:
        """
        预测动作
        
        使用MCTS进行搜索，然后根据访问计数选择动作。
        
        Args:
            state (State): 当前游戏状态
            temperature (float, optional): 温度参数，控制搜索策略的随机性. Defaults to 1.0.
            deterministic (bool, optional): 是否确定性选择动作. Defaults to False.
            
        Returns:
            Action: 选择的动作
        """
        # 转换状态为张量
        observation = torch.FloatTensor(state.to_array()).unsqueeze(0).to(self.device)
        valid_actions = state.get_valid_actions()
        
        # 创建动作掩码
        action_mask = torch.zeros(self.model.action_dim).to(self.device)
        for action in valid_actions:
            action_mask[action.id] = 1
        
        # 运行MCTS
        root = self.mcts.run(
            model=self.model,
            root_state=observation,
            actions_mask=action_mask,
            num_simulations=self.num_simulations,
            temperature=temperature,
            add_exploration_noise=not deterministic,
            dirichlet_alpha=self.dirichlet_alpha,
            exploration_fraction=self.exploration_fraction
        )
        
        # 获取访问计数
        visit_counts = torch.zeros(self.model.action_dim).to(self.device)
        for action_id, child in root.children.items():
            visit_counts[action_id] = child.visit_count
        
        # 掩盖无效动作
        visit_counts = visit_counts * action_mask
        
        # 根据温度参数调整分布
        if temperature == 0:  # 确定性选择
            action_id = visit_counts.argmax().item()
        else:
            # 应用温度参数
            visit_count_distribution = (visit_counts / visit_counts.sum()) ** (1 / temperature)
            visit_count_distribution = visit_count_distribution / visit_count_distribution.sum()
            
            # 根据分布采样
            if deterministic:
                action_id = visit_count_distribution.argmax().item()
            else:
                action_id = torch.multinomial(visit_count_distribution, 1).item()
        
        # 创建动作对象
        for action in valid_actions:
            if action.id == action_id:
                return action
        
        # 如果上面的逻辑有问题，则选择第一个有效动作
        return valid_actions[0]
    
    def predict_value(self, state: State) -> float:
        """
        预测状态的价值
        
        Args:
            state (State): 当前游戏状态
            
        Returns:
            float: 状态的预测价值
        """
        with torch.no_grad():
            observation = torch.FloatTensor(state.to_array()).unsqueeze(0).to(self.device)
            _, value = self.model.prediction(self.model.representation(observation))
            return value.item()
    
    def update(self, batch: Batch) -> Dict[str, float]:
        """
        使用经验批次更新模型
        
        Args:
            batch (Batch): 经验批次
            
        Returns:
            Dict[str, float]: 损失字典
        """
        # 准备训练数据
        observations = []
        actions = []
        targets_policy = []
        targets_value = []
        targets_reward = []
        
        for experience in batch.experiences:
            observations.append(torch.FloatTensor(experience.state.to_array()))
            
            # 目标策略 (MCTS搜索策略)
            policy = torch.zeros(self.model.action_dim)
            for action_id, prob in experience.search_policy.items():
                policy[action_id] = prob
            targets_policy.append(policy)
            
            # 目标值 (实际回报)
            targets_value.append(experience.value)
            
            # 目标奖励和动作
            if experience.next_state is not None:
                actions.append(torch.tensor(experience.action.id))
                targets_reward.append(experience.reward)
        
        # 转换为批次张量
        batch_size = len(observations)
        observations = torch.stack(observations).to(self.device)
        
        if actions:
            actions = torch.stack(actions).to(self.device)
            targets_reward = torch.tensor(targets_reward, dtype=torch.float32).to(self.device)
        
        targets_policy = torch.stack(targets_policy).to(self.device)
        targets_value = torch.tensor(targets_value, dtype=torch.float32).to(self.device)
        
        # 执行模型预测
        self.optimizer.zero_grad()
        
        # 初始推理
        initial_output = self.model.initial_inference(observations)
        predicted_policies = [initial_output["policy_logits"]]
        predicted_values = [initial_output["value"]]
        hidden_states = [initial_output["state"]]
        
        # 递归推理 (如果有动作)
        predicted_rewards = []
        if actions.shape[0] > 0:
            # 使用dynamics网络进行预测
            next_state, reward = self.model.dynamics(hidden_states[0], actions)
            predicted_rewards.append(reward)
            
            # 使用prediction网络预测策略和价值
            policy_logits, value = self.model.prediction(next_state)
            predicted_policies.append(policy_logits)
            predicted_values.append(value)
        
        # 计算损失
        policy_loss = 0
        value_loss = 0
        reward_loss = 0
        
        # 策略损失 (交叉熵)
        policy_logits = predicted_policies[0]
        log_probs = F.log_softmax(policy_logits, dim=1)
        policy_loss = -torch.mean(torch.sum(targets_policy * log_probs, dim=1))
        
        # 价值损失 (MSE)
        predicted_value = predicted_values[0].squeeze(-1)
        value_loss = F.mse_loss(predicted_value, targets_value)
        
        # 奖励损失 (MSE，如果有奖励)
        if predicted_rewards:
            predicted_reward = predicted_rewards[0].squeeze(-1)
            reward_loss = F.mse_loss(predicted_reward, targets_reward)
        
        # 总损失
        total_loss = (
            self.policy_loss_weight * policy_loss +
            self.value_loss_weight * value_loss +
            self.reward_loss_weight * reward_loss
        )
        
        # 反向传播和优化
        total_loss.backward()
        self.optimizer.step()
        
        # 返回损失信息
        return {
            "policy_loss": policy_loss.item(),
            "value_loss": value_loss.item(),
            "reward_loss": reward_loss.item() if predicted_rewards else 0,
            "total_loss": total_loss.item()
        }
    
    def save(self, path: str) -> None:
        """
        保存模型
        
        Args:
            path (str): 保存路径
        """
        state = {
            "model": self.model.state_dict(),
            "optimizer": self.optimizer.state_dict()
        }
        torch.save(state, path)
    
    def load(self, path: str) -> None:
        """
        加载模型
        
        Args:
            path (str): 加载路径
        """
        state = torch.load(path, map_location=self.device)
        self.model.load_state_dict(state["model"])
        self.optimizer.load_state_dict(state["optimizer"]) 