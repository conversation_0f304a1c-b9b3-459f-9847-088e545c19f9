"""
工厂类模块

提供创建游戏环境和智能体实例的工厂类。
"""
import logging
from typing import Dict, Any, Optional, Type, List, Union

from cardgame_ai.core.environment import Environment
from cardgame_ai.core.agent import Agent, RandomAgent
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment

logger = logging.getLogger(__name__)


class EnvironmentFactory:
    """
    环境工厂类
    
    根据配置动态创建游戏环境实例。
    """
    
    # 注册环境类型映射表
    _environment_registry = {
        'doudizhu': DouDizhuEnvironment,
        # 将来可以添加更多环境类型
    }
    
    def __init__(self, default_env_config: Optional[Dict[str, Any]] = None):
        """
        初始化环境工厂
        
        Args:
            default_env_config (Optional[Dict[str, Any]], optional): 默认环境配置. Defaults to None.
        """
        self.default_config = default_env_config or {}
    
    def create_environment(self, env_config: Optional[Dict[str, Any]] = None) -> Environment:
        """
        创建环境实例
        
        Args:
            env_config (Optional[Dict[str, Any]], optional): 环境配置. Defaults to None.
        
        Returns:
            Environment: 创建的环境实例
            
        Raises:
            ValueError: 如果环境类型不支持
        """
        # 合并配置
        config = {**self.default_config, **(env_config or {})}
        
        # 获取环境类型
        env_type = config.get('type', 'doudizhu')
        logger.info(f"创建环境: 类型 = {env_type}")
        
        # 从注册表中获取环境类
        if env_type not in self._environment_registry:
            raise ValueError(f"不支持的环境类型: {env_type}")
        
        environment_class = self._environment_registry[env_type]
        
        # 提取配置参数，移除类型键
        params = config.get('params', {})
        
        # 创建并返回环境实例
        try:
            return environment_class(**params)
        except Exception as e:
            logger.error(f"创建环境失败: {e}")
            raise
    
    @classmethod
    def register_environment(cls, env_type: str, environment_class: Type[Environment]) -> None:
        """
        注册新的环境类型
        
        Args:
            env_type (str): 环境类型名称
            environment_class (Type[Environment]): 环境类
        """
        cls._environment_registry[env_type] = environment_class
        logger.info(f"注册环境类型: {env_type}")


class AgentFactory:
    """
    智能体工厂类
    
    根据配置动态创建智能体实例。
    """
    
    # 注册智能体类型映射表
    _agent_registry = {
        'random': RandomAgent,
        # 'dqn': DQNAgent,  # 将在 DQNAgent 解耦后添加
        # 将来可以添加更多智能体类型
    }
    
    def __init__(self, default_agent_config: Optional[Dict[str, Any]] = None):
        """
        初始化智能体工厂
        
        Args:
            default_agent_config (Optional[Dict[str, Any]], optional): 默认智能体配置. Defaults to None.
        """
        self.default_config = default_agent_config or {}
    
    def create_agent(self, 
                    env: Environment, 
                    agent_config: Optional[Dict[str, Any]] = None, 
                    is_training: bool = True) -> Agent:
        """
        创建智能体实例
        
        Args:
            env (Environment): 游戏环境实例
            agent_config (Optional[Dict[str, Any]], optional): 智能体配置. Defaults to None.
            is_training (bool, optional): 是否为训练模式. Defaults to True.
        
        Returns:
            Agent: 创建的智能体实例
            
        Raises:
            ValueError: 如果智能体类型不支持
            NotImplementedError: 如果智能体类型尚未实现
        """
        # 合并配置
        config = {**self.default_config, **(agent_config or {})}
        
        # 获取智能体类型
        agent_type = config.get('type', 'random')
        logger.info(f"创建智能体: 类型 = {agent_type}, 训练模式 = {is_training}")
        
        # 检查是否支持该类型
        if agent_type not in self._agent_registry:
            # 处理 DQN 特殊情况（取决于解耦任务）
            if agent_type == 'dqn':
                logger.warning("DQN 智能体尚未解耦，请先完成解耦任务")
                raise NotImplementedError(f"智能体类型 '{agent_type}' 尚未解耦")
            else:
                raise ValueError(f"不支持的智能体类型: {agent_type}")
        
        agent_class = self._agent_registry[agent_type]
        
        # 提取配置参数，移除类型键
        params = config.get('params', {})
        
        # 随机智能体特殊处理
        if agent_type == 'random':
            return agent_class(**params)  # RandomAgent 不需要环境参数
        
        # 其他类型的智能体，视情况处理
        try:
            # 不同智能体可能有不同的构造函数签名
            if agent_type in ['dqn']:
                # DQN 智能体的特殊处理（将在解耦后完善）
                raise NotImplementedError(f"智能体类型 '{agent_type}' 尚未解耦")
            
            # 默认情况
            return agent_class(env, **params)
        except Exception as e:
            logger.error(f"创建智能体失败: {e}")
            raise
    
    @classmethod
    def register_agent(cls, agent_type: str, agent_class: Type[Agent]) -> None:
        """
        注册新的智能体类型
        
        Args:
            agent_type (str): 智能体类型名称
            agent_class (Type[Agent]): 智能体类
        """
        cls._agent_registry[agent_type] = agent_class
        logger.info(f"注册智能体类型: {agent_type}") 