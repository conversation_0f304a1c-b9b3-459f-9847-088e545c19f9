/* 
 * AI棋牌强化学习框架 - 游戏界面样式
 */

/* 游戏容器 */
.game-container {
    width: 100%;
    height: 100vh;
    max-height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: var(--light-color);
    position: relative;
    overflow: hidden;
}

/* 游戏头部 */
.game-header {
    padding: 1rem;
    background-color: var(--header-bg);
    border-bottom: 1px solid var(--border-color);
    z-index: 10;
}

.game-title {
    font-size: 1.5rem;
    font-weight: bold;
    margin: 0;
}

.game-status {
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    background-color: var(--card-bg);
    display: inline-block;
    font-weight: 500;
}

/* 游戏主体区域 */
.game-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
    padding: 1rem;
    gap: 1rem;
}

/* 玩家区域 */
.player-area {
    display: flex;
    flex-direction: column;
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: rgba(0, 0, 0, 0.05);
}

.ai-player {
    height: 15vh;
    margin-bottom: 0.5rem;
}

.human-player {
    height: 25vh;
    background-color: rgba(0, 123, 255, 0.1);
    margin-top: auto;
}

.player-info {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.player-name {
    font-weight: bold;
    margin-right: 1rem;
}

.player-role {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    margin-right: 1rem;
}

.landlord {
    background-color: var(--warning-color);
    color: #212529;
}

.farmer {
    background-color: var(--info-color);
    color: #fff;
}

.card-count {
    color: var(--secondary-color);
    font-size: 0.875rem;
}

/* 卡牌容器 */
.card-container {
    display: flex;
    flex-wrap: nowrap;
    gap: 0.25rem;
    min-height: 8rem;
    overflow-x: auto;
    padding: 0.5rem 0;
    align-items: center;
}

.human-cards {
    justify-content: center;
}

.ai-cards {
    justify-content: flex-start;
}

.hidden-cards .card-back {
    width: 3rem;
    height: 4.5rem;
    background-color: var(--primary-color);
    border: 1px solid var(--border-color);
    border-radius: 0.25rem;
    margin-right: -2.5rem;
    box-shadow: 0 0.1rem 0.25rem rgba(0, 0, 0, 0.1);
}

/* 出牌区域 */
.play-area {
    min-height: 5rem;
    margin-top: 0.5rem;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
}

/* 动作按钮 */
.action-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 1rem;
}

.action-btn {
    min-width: 6rem;
}

/* 中央区域 */
.central-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

/* 地主牌区域 */
.landlord-cards-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 2rem;
}

.landlord-cards {
    display: flex;
    gap: 0.5rem;
}

.landlord-label {
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: var(--secondary-color);
}

/* 游戏信息区域 */
.game-info-container {
    width: 100%;
    max-width: 600px;
    background-color: var(--card-bg);
    border-radius: 0.5rem;
    padding: 1rem;
    box-shadow: 0 0.25rem 0.5rem var(--shadow-color);
}

.game-info {
    margin-bottom: 1rem;
    font-weight: 500;
    text-align: center;
}

.play-history {
    height: 15vh;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 0.25rem;
    padding: 0.5rem;
}

/* 卡牌样式 */
.card {
    width: 3.5rem;
    height: 5rem;
    border-radius: 0.25rem;
    background-color: white;
    box-shadow: 0 0.1rem 0.25rem rgba(0, 0, 0, 0.1);
    margin-right: -1.75rem;
    cursor: pointer;
    transition: transform 0.2s ease, margin 0.2s ease;
    position: relative;
    user-select: none;
}

.card:last-child {
    margin-right: 0;
}

.card:hover {
    transform: translateY(-1rem);
    z-index: 2;
}

.card.selected {
    transform: translateY(-1.5rem);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.2);
    z-index: 3;
}

.card-face {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 0.25rem;
}

.card-top, .card-bottom {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.card-value {
    font-weight: bold;
    font-size: 1.25rem;
}

.card-suit {
    font-size: 1rem;
}

.card-suit.red {
    color: red;
}

.card-suit.black {
    color: black;
}

.card-center {
    font-size: 2rem;
    text-align: center;
}

/* 卡牌样式：简约 */
.card-style-simple .card {
    width: 2.5rem;
    height: 3.5rem;
    margin-right: -1.25rem;
}

.card-style-simple .card-value {
    font-size: 1rem;
}

.card-style-simple .card-suit {
    font-size: 0.75rem;
}

.card-style-simple .card-center {
    font-size: 1.5rem;
}

/* 动画速度 */
[data-animation-speed="fast"] .card {
    transition: transform 0.1s ease, margin 0.1s ease;
}

[data-animation-speed="normal"] .card {
    transition: transform 0.2s ease, margin 0.2s ease;
}

[data-animation-speed="slow"] .card {
    transition: transform 0.4s ease, margin 0.4s ease;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .game-main {
        padding: 0.5rem;
    }
    
    .card {
        width: 2.5rem;
        height: 3.5rem;
        margin-right: -1.25rem;
    }
    
    .card-value {
        font-size: 1rem;
    }
    
    .card-suit {
        font-size: 0.75rem;
    }
    
    .card-center {
        font-size: 1.5rem;
    }
    
    .action-btn {
        min-width: 4rem;
        font-size: 0.875rem;
    }
    
    .game-title {
        font-size: 1.25rem;
    }
} 