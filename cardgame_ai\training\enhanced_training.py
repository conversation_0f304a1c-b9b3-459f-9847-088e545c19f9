"""
增强训练方法模块

实现优化的训练方法，提高训练效率和模型性能。包括细化分阶段训练策略、
优化自我对弈机制和实现高级经验回放等技术，使AI能够更高效地学习斗地主策略。
"""
import os
import time
import logging
import random
import numpy as np
from typing import Dict, List, Tuple, Any, Optional, Union, Callable
from collections import deque

import torch

from cardgame_ai.core.agent import Agent
from cardgame_ai.core.environment import Environment
from cardgame_ai.core.base import Experience, Batch
from cardgame_ai.training.phased_training_strategy import PhasedTrainingStrategy, TrainingPhase
from cardgame_ai.core.self_play import SelfPlay


class TemperatureScheduler:
    """
    温度调度器

    控制自我对弈过程中的温度参数，实现从探索到利用的动态调整。
    """

    def __init__(
        self,
        initial_temp: float = 1.0,
        final_temp: float = 0.1,
        decay_factor: float = 0.9999,
        min_temp: float = 0.05,
        exploration_fraction: float = 0.8,
        schedule_type: str = 'exponential'
    ):
        """
        初始化温度调度器

        Args:
            initial_temp: 初始温度
            final_temp: 最终温度
            decay_factor: 衰减因子
            min_temp: 最小温度
            exploration_fraction: 探索阶段占比
            schedule_type: 调度类型，支持'exponential'、'linear'和'step'
        """
        self.initial_temp = initial_temp
        self.final_temp = final_temp
        self.decay_factor = decay_factor
        self.min_temp = min_temp
        self.exploration_fraction = exploration_fraction
        self.schedule_type = schedule_type

        self.current_temp = initial_temp
        self.step_count = 0

    def step(self, total_steps: Optional[int] = None) -> float:
        """
        更新温度参数

        Args:
            total_steps: 总步数，用于线性和阶梯式调度

        Returns:
            更新后的温度值
        """
        self.step_count += 1

        if self.schedule_type == 'exponential':
            # 指数衰减
            self.current_temp = max(self.final_temp, self.current_temp * self.decay_factor)
        elif self.schedule_type == 'linear' and total_steps is not None:
            # 线性衰减
            progress = min(1.0, self.step_count / (self.exploration_fraction * total_steps))
            self.current_temp = max(self.min_temp, self.initial_temp - progress * (self.initial_temp - self.final_temp))
        elif self.schedule_type == 'step':
            # 阶梯式衰减
            if total_steps is not None:
                step_size = total_steps * self.exploration_fraction / 3  # 分三个阶段
                if self.step_count < step_size:
                    self.current_temp = self.initial_temp
                elif self.step_count < 2 * step_size:
                    self.current_temp = self.initial_temp * 0.5
                else:
                    self.current_temp = self.final_temp

        # 确保温度不低于最小值
        self.current_temp = max(self.current_temp, self.min_temp)

        return self.current_temp

    def get_temperature(self) -> float:
        """
        获取当前温度值

        Returns:
            当前温度值
        """
        return self.current_temp


class HistoricalModelPool:
    """
    历史模型池

    管理模型的历史版本，用于自我对弈中的多样化对手选择。
    """

    def __init__(
        self,
        max_models: int = 10,
        update_frequency: int = 1000,
        sampling_strategy: str = 'uniform'
    ):
        """
        初始化历史模型池

        Args:
            max_models: 最大模型数量
            update_frequency: 更新频率，每多少步添加一个新模型
            sampling_strategy: 采样策略，支持'uniform'、'prioritized'和'latest_biased'
        """
        self.max_models = max_models
        self.update_frequency = update_frequency
        self.sampling_strategy = sampling_strategy

        # 存储模型的列表，每个元素是(model_state_dict, step_count)
        self.models = []

        # 当前步数
        self.step_count = 0

        # 模型优先级（用于优先级采样）
        self.model_priorities = []

        # 最新模型的权重（用于偏向最新的采样）
        self.latest_weight = 2.0

    def add_model(self, model: torch.nn.Module, priority: float = 1.0) -> None:
        """
        添加模型到池中

        Args:
            model: 要添加的模型
            priority: 模型的优先级（用于优先级采样）
        """
        self.step_count += 1

        # 检查是否应该添加新模型
        if self.step_count % self.update_frequency == 0:
            # 复制模型状态
            model_state = {k: v.cpu().clone() for k, v in model.state_dict().items()}

            # 添加模型和优先级
            self.models.append((model_state, self.step_count))
            self.model_priorities.append(priority)

            # 如果模型数量超过最大值，删除最旧的模型
            if len(self.models) > self.max_models:
                self.models.pop(0)
                self.model_priorities.pop(0)

    def sample_model(self) -> Optional[Dict[str, torch.Tensor]]:
        """
        从模型池中采样一个模型

        Returns:
            采样的模型状态字典，如果池为空则返回None
        """
        if not self.models:
            return None

        if self.sampling_strategy == 'uniform':
            # 均匀采样
            idx = random.randint(0, len(self.models) - 1)
            return self.models[idx][0]

        elif self.sampling_strategy == 'prioritized':
            # 优先级采样
            total_priority = sum(self.model_priorities)
            if total_priority <= 0:
                # 如果所有优先级都为0，则均匀采样
                idx = random.randint(0, len(self.models) - 1)
            else:
                # 根据优先级采样
                r = random.uniform(0, total_priority)
                cumulative = 0
                idx = 0
                for i, p in enumerate(self.model_priorities):
                    cumulative += p
                    if r <= cumulative:
                        idx = i
                        break

            return self.models[idx][0]

        elif self.sampling_strategy == 'latest_biased':
            # 偏向最新模型的采样
            weights = [1.0] * (len(self.models) - 1) + [self.latest_weight]  # 最新的模型权重更高
            total_weight = sum(weights)
            r = random.uniform(0, total_weight)
            cumulative = 0
            idx = 0
            for i, w in enumerate(weights):
                cumulative += w
                if r <= cumulative:
                    idx = i
                    break

            return self.models[idx][0]

        # 默认使用均匀采样
        idx = random.randint(0, len(self.models) - 1)
        return self.models[idx][0]

    def update_priority(self, idx: int, priority: float) -> None:
        """
        更新模型的优先级

        Args:
            idx: 模型索引
            priority: 新的优先级
        """
        if 0 <= idx < len(self.model_priorities):
            self.model_priorities[idx] = priority

    def get_model_count(self) -> int:
        """
        获取模型数量

        Returns:
            模型数量
        """
        return len(self.models)

    def load_model(self, model: torch.nn.Module, idx: int = -1) -> bool:
        """
        将指定索引的模型加载到给定的模型对象中

        Args:
            model: 要加载到的模型对象
            idx: 模型索引，默认为-1（最新的模型）

        Returns:
            是否成功加载
        """
        if not self.models:
            return False

        # 如果索引为负数，从后往前计数
        if idx < 0:
            idx = len(self.models) + idx

        # 检查索引是否有效
        if not (0 <= idx < len(self.models)):
            return False

        # 加载模型状态
        model.load_state_dict(self.models[idx][0])
        return True


class DiverseStateGenerator:
    """
    多样化初始状态生成器

    生成多样化的初始游戏状态，提高自我对弈的探索效果。
    """

    def __init__(
        self,
        env: Environment,
        diversity_types: List[str] = ['random', 'biased', 'fixed'],
        biased_prob: float = 0.7,
        fixed_states_path: Optional[str] = None,
        num_fixed_states: int = 100
    ):
        """
        初始化多样化初始状态生成器

        Args:
            env: 游戏环境
            diversity_types: 多样化类型列表，支持'random'、'biased'和'fixed'
            biased_prob: 偏置生成的概率参数
            fixed_states_path: 固定状态文件路径
            num_fixed_states: 固定状态数量
        """
        self.env = env
        self.diversity_types = diversity_types
        self.biased_prob = biased_prob
        self.fixed_states_path = fixed_states_path
        self.num_fixed_states = num_fixed_states

        # 固定状态列表
        self.fixed_states = []

        # 如果提供了固定状态路径，尝试加载
        if fixed_states_path and os.path.exists(fixed_states_path):
            self._load_fixed_states()

    def _load_fixed_states(self) -> None:
        """
        加载固定状态
        """
        try:
            import pickle
            with open(self.fixed_states_path, 'rb') as f:
                self.fixed_states = pickle.load(f)

            # 限制固定状态数量
            if len(self.fixed_states) > self.num_fixed_states:
                self.fixed_states = self.fixed_states[:self.num_fixed_states]

            logging.info(f"成功加载 {len(self.fixed_states)} 个固定状态")
        except Exception as e:
            logging.error(f"加载固定状态失败: {str(e)}")
            self.fixed_states = []

    def _save_fixed_states(self) -> None:
        """
        保存固定状态
        """
        if not self.fixed_states_path:
            return

        try:
            import pickle
            # 创建目录（如果不存在）
            os.makedirs(os.path.dirname(self.fixed_states_path), exist_ok=True)

            with open(self.fixed_states_path, 'wb') as f:
                pickle.dump(self.fixed_states, f)

            logging.info(f"成功保存 {len(self.fixed_states)} 个固定状态")
        except Exception as e:
            logging.error(f"保存固定状态失败: {str(e)}")

    def add_fixed_state(self, state: Any) -> None:
        """
        添加固定状态

        Args:
            state: 要添加的状态
        """
        # 添加状态
        self.fixed_states.append(state)

        # 限制固定状态数量
        if len(self.fixed_states) > self.num_fixed_states:
            self.fixed_states.pop(0)  # 移除最旧的状态

        # 保存固定状态
        self._save_fixed_states()

    def generate_state(self) -> Any:
        """
        生成多样化的初始状态

        Returns:
            生成的初始状态
        """
        # 随机选择一种多样化类型
        if not self.diversity_types:
            # 如果没有指定多样化类型，使用默认的随机生成
            return self.env.reset()

        diversity_type = random.choice(self.diversity_types)

        if diversity_type == 'random':
            # 完全随机生成
            return self.env.reset()

        elif diversity_type == 'biased':
            # 偏置生成，例如生成特定类型的牌局
            return self._generate_biased_state()

        elif diversity_type == 'fixed' and self.fixed_states:
            # 使用预定义的固定状态
            return random.choice(self.fixed_states)

        # 默认使用随机生成
        return self.env.reset()

    def _generate_biased_state(self) -> Any:
        """
        生成偏置的初始状态

        Returns:
            偏置生成的初始状态
        """
        # 首先生成一个随机状态
        state = self.env.reset()

        # 尝试修改状态以创建偏置的状态
        # 注意：具体的偏置生成需要根据环境的实际情况来实现
        # 这里提供一个示例实现

        # 判断是否应用偏置
        if random.random() < self.biased_prob:
            # 如果状态是字典类型，尝试修改其属性
            if isinstance(state, dict):
                # 例如，可以修改特定属性
                # 这里只是一个示例，实际实现需要根据环境的具体情况
                pass

            # 如果状态有特定的方法来修改其内部状态
            if hasattr(state, 'modify_for_bias'):
                state.modify_for_bias()

        return state


class EnhancedTrainingPhase(TrainingPhase):
    """
    增强版训练阶段

    扩展基本训练阶段，添加更多的配置选项和训练策略。
    """

    def __init__(
        self,
        name: str,
        num_episodes: int,
        eval_interval: int,
        success_threshold: float,
        config: Optional[Dict[str, Any]] = None,
        temperature_scheduler: Optional[TemperatureScheduler] = None,
        model_pool: Optional[HistoricalModelPool] = None,
        state_generator: Optional[DiverseStateGenerator] = None,
        curriculum_config: Optional[Dict[str, Any]] = None
    ):
        """
        初始化增强版训练阶段

        Args:
            name: 阶段名称
            num_episodes: 训练回合数
            eval_interval: 评估间隔回合数
            success_threshold: 成功阈值
            config: 配置字典
            temperature_scheduler: 温度调度器
            model_pool: 历史模型池
            state_generator: 多样化初始状态生成器
            curriculum_config: 课程学习配置
        """
        super().__init__(name, num_episodes, eval_interval, success_threshold)

        # 使用提供的配置或创建默认配置
        self.config = config or {}

        # 设置增强组件
        self.temperature_scheduler = temperature_scheduler
        self.model_pool = model_pool
        self.state_generator = state_generator

        # 课程学习配置
        self.curriculum_config = curriculum_config or {}

        # 课程学习阶段
        self.curriculum_stage = 0
        self.curriculum_stages = self.curriculum_config.get('stages', 3)
        self.curriculum_progress = 0.0

        # 记录当前温度
        self.current_temperature = 1.0

        # 记录当前使用的对手模型
        self.current_opponent_model = None

    def start(self) -> None:
        """
        开始训练阶段
        """
        super().start()

        # 重置课程学习状态
        self.curriculum_stage = 0
        self.curriculum_progress = 0.0

        # 记录日志
        logging.info(f"开始增强版训练阶段: {self.name}")

    def update(self, metrics: Dict[str, Any]) -> None:
        """
        更新训练阶段状态

        Args:
            metrics: 回合指标
        """
        # 调用父类的update方法
        super().update(metrics)

        # 更新课程学习进度
        self._update_curriculum_progress(metrics)

        # 更新温度
        if self.temperature_scheduler:
            self.current_temperature = self.temperature_scheduler.step(self.num_episodes)

        # 更新模型池优先级（如果需要）
        if self.model_pool and self.current_opponent_model is not None:
            # 根据指标更新模型优先级
            win_rate = metrics.get('win_rate', 0.5)
            model_idx = self.model_pool.get_model_count() - 1  # 最新模型的索引

            # 根据胜率调整优先级
            if win_rate > 0.7:  # 如果胜率过高，降低优先级
                self.model_pool.update_priority(model_idx, 0.5)
            elif win_rate < 0.3:  # 如果胜率过低，提高优先级
                self.model_pool.update_priority(model_idx, 2.0)

    def _update_curriculum_progress(self, metrics: Dict[str, Any]) -> None:
        """
        更新课程学习进度

        Args:
            metrics: 回合指标
        """
        if not self.curriculum_config:
            return

        # 计算课程学习进度
        progress_per_episode = 1.0 / self.num_episodes
        self.curriculum_progress += progress_per_episode

        # 限制进度范围
        self.curriculum_progress = min(1.0, max(0.0, self.curriculum_progress))

        # 检查是否需要进入下一个课程学习阶段
        stage_threshold = (self.curriculum_stage + 1) / self.curriculum_stages

        if self.curriculum_progress >= stage_threshold and self.curriculum_stage < self.curriculum_stages - 1:
            self.curriculum_stage += 1
            logging.info(f"进入课程学习阶段 {self.curriculum_stage + 1}/{self.curriculum_stages}")

            # 应用课程学习阶段特定的调整
            self._apply_curriculum_stage_adjustments()

    def _apply_curriculum_stage_adjustments(self) -> None:
        """
        应用课程学习阶段特定的调整
        """
        # 获取当前阶段的调整配置
        stage_adjustments = self.curriculum_config.get(f'stage_{self.curriculum_stage}', {})

        # 应用调整
        for key, value in stage_adjustments.items():
            if key in self.config:
                self.config[key] = value
                logging.info(f"课程学习调整: {key} = {value}")

    def get_temperature(self) -> float:
        """
        获取当前温度

        Returns:
            当前温度值
        """
        return self.current_temperature

    def sample_opponent_model(self) -> Optional[Dict[str, torch.Tensor]]:
        """
        采样对手模型

        Returns:
            对手模型状态字典，如果没有可用的模型则返回None
        """
        if not self.model_pool or self.model_pool.get_model_count() == 0:
            return None

        # 采样模型
        self.current_opponent_model = self.model_pool.sample_model()
        return self.current_opponent_model

    def get_initial_state(self) -> Any:
        """
        获取初始状态

        Returns:
            初始状态，如果没有状态生成器则返回None
        """
        if not self.state_generator:
            return None

        return self.state_generator.generate_state()

    def get_summary(self) -> Dict[str, Any]:
        """
        获取阶段摘要

        Returns:
            阶段摘要字典
        """
        # 获取基本摘要
        summary = super().get_summary()

        # 添加增强版特定的信息
        summary.update({
            'curriculum_stage': self.curriculum_stage,
            'curriculum_progress': self.curriculum_progress,
            'temperature': self.current_temperature,
            'config': self.config
        })

        return summary


class EnhancedPhasedTrainingStrategy(PhasedTrainingStrategy):
    """
    增强版分阶段训练策略

    扩展基本分阶段训练策略，添加更多的配置选项和训练策略。
    """

    def __init__(
        self,
        env: Optional[Environment] = None,
        agents: Optional[Dict[str, Agent]] = None,
        use_temperature_scheduler: bool = True,
        use_model_pool: bool = True,
        use_diverse_state_generator: bool = True,
        use_curriculum_learning: bool = True
    ):
        """
        初始化增强版分阶段训练策略

        Args:
            env: 游戏环境，可选
            agents: 智能体字典，可选
            use_temperature_scheduler: 是否使用温度调度器
            use_model_pool: 是否使用历史模型池
            use_diverse_state_generator: 是否使用多样化初始状态生成器
            use_curriculum_learning: 是否使用课程学习
        """
        # 调用父类的初始化方法
        super().__init__(env=env, agents=agents)

        # 增强组件配置
        self.use_temperature_scheduler = use_temperature_scheduler
        self.use_model_pool = use_model_pool
        self.use_diverse_state_generator = use_diverse_state_generator
        self.use_curriculum_learning = use_curriculum_learning

        # 初始化增强组件
        self.temperature_scheduler = None
        self.model_pool = None
        self.state_generator = None

        # 课程学习配置
        self.curriculum_config = {}

    def start(self) -> None:
        """
        开始训练
        """
        # 初始化增强组件
        self._initialize_enhanced_components()

        # 初始化课程学习配置
        self._initialize_curriculum_config()

        # 创建增强版训练阶段
        self._create_enhanced_phases()

        # 调用父类的start方法
        super().start()

    def _initialize_enhanced_components(self) -> None:
        """
        初始化增强组件
        """
        # 初始化温度调度器
        if self.use_temperature_scheduler:
            self.temperature_scheduler = TemperatureScheduler(
                initial_temp=1.0,
                final_temp=0.1,
                decay_factor=0.9999,
                min_temp=0.05,
                exploration_fraction=0.8,
                schedule_type='exponential'
            )

        # 初始化历史模型池
        if self.use_model_pool:
            self.model_pool = HistoricalModelPool(
                max_models=10,
                update_frequency=1000,
                sampling_strategy='latest_biased'
            )

        # 初始化多样化初始状态生成器
        if self.use_diverse_state_generator and self.env is not None:
            self.state_generator = DiverseStateGenerator(
                env=self.env,
                diversity_types=['random', 'biased'],
                biased_prob=0.7
            )

    def _initialize_curriculum_config(self) -> None:
        """
        初始化课程学习配置
        """
        if not self.use_curriculum_learning:
            return

        # 课程学习配置
        self.curriculum_config = {
            'stages': 3,  # 课程学习阶段数
            'stage_0': {  # 第一阶段：简单难度
                'learning_rate': 0.001,
                'entropy_coef': 0.01,
                'clip_ratio': 0.2
            },
            'stage_1': {  # 第二阶段：中等难度
                'learning_rate': 0.0005,
                'entropy_coef': 0.005,
                'clip_ratio': 0.15
            },
            'stage_2': {  # 第三阶段：高级难度
                'learning_rate': 0.0002,
                'entropy_coef': 0.002,
                'clip_ratio': 0.1
            }
        }

    def _create_enhanced_phases(self) -> None:
        """
        创建增强版训练阶段
        """
        # 创建基础训练阶段
        basic_phase = EnhancedTrainingPhase(
            name="基础训练",
            num_episodes=1000,
            eval_interval=100,
            success_threshold=0.5,
            config={
                "learning_rate": 0.001,
                "batch_size": 64,
                "update_epochs": 4,
                "clip_ratio": 0.2,
                "entropy_coef": 0.01
            },
            temperature_scheduler=self.temperature_scheduler,
            model_pool=self.model_pool,
            state_generator=self.state_generator,
            curriculum_config=self.curriculum_config if self.use_curriculum_learning else None
        )

        # 创建角色专一化训练阶段
        role_specific_phase = EnhancedTrainingPhase(
            name="角色专一化训练",
            num_episodes=2000,
            eval_interval=100,
            success_threshold=0.6,
            config={
                "learning_rate": 0.0005,
                "batch_size": 128,
                "update_epochs": 6,
                "clip_ratio": 0.15,
                "entropy_coef": 0.005,
                "role_control": True,
                "separate_buffers": True
            },
            temperature_scheduler=self.temperature_scheduler,
            model_pool=self.model_pool,
            state_generator=self.state_generator,
            curriculum_config=self.curriculum_config if self.use_curriculum_learning else None
        )

        # 创建农民协作训练阶段
        farmer_cooperation_phase = EnhancedTrainingPhase(
            name="农民协作训练",
            num_episodes=2000,
            eval_interval=100,
            success_threshold=0.65,
            config={
                "learning_rate": 0.0003,
                "batch_size": 128,
                "update_epochs": 8,
                "clip_ratio": 0.15,
                "entropy_coef": 0.003,
                "cooperation_weight": 0.7,
                "team_reward_weight": 0.8
            },
            temperature_scheduler=self.temperature_scheduler,
            model_pool=self.model_pool,
            state_generator=self.state_generator,
            curriculum_config=self.curriculum_config if self.use_curriculum_learning else None
        )

        # 创建对抗训练阶段
        adversarial_phase = EnhancedTrainingPhase(
            name="对抗训练",
            num_episodes=3000,
            eval_interval=100,
            success_threshold=0.7,
            config={
                "learning_rate": 0.0002,
                "batch_size": 256,
                "update_epochs": 10,
                "clip_ratio": 0.1,
                "entropy_coef": 0.002,
                "adaptive_difficulty": True,
                "opponent_modeling": True
            },
            temperature_scheduler=self.temperature_scheduler,
            model_pool=self.model_pool,
            state_generator=self.state_generator,
            curriculum_config=self.curriculum_config if self.use_curriculum_learning else None
        )

        # 创建微调阶段
        fine_tuning_phase = EnhancedTrainingPhase(
            name="微调阶段",
            num_episodes=1000,
            eval_interval=50,
            success_threshold=0.75,
            config={
                "learning_rate": 0.0001,
                "batch_size": 256,
                "update_epochs": 12,
                "clip_ratio": 0.05,
                "entropy_coef": 0.001
            },
            temperature_scheduler=self.temperature_scheduler,
            model_pool=self.model_pool,
            state_generator=self.state_generator,
            curriculum_config=self.curriculum_config if self.use_curriculum_learning else None
        )

        # 设置训练阶段
        self.phases = [
            basic_phase,
            role_specific_phase,
            farmer_cooperation_phase,
            adversarial_phase,
            fine_tuning_phase
        ]

    def update(self, episode_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新训练状态

        Args:
            episode_metrics: 回合指标

        Returns:
            更新后的状态信息
        """
        # 调用父类的update方法
        result = super().update(episode_metrics)

        # 获取当前阶段
        current_phase = self.get_current_phase()

        # 如果当前阶段是增强版训练阶段，获取当前温度
        if isinstance(current_phase, EnhancedTrainingPhase):
            result['temperature'] = current_phase.get_temperature()

            # 如果需要评估，尝试采样对手模型
            if result.get('should_evaluate', False):
                opponent_model = current_phase.sample_opponent_model()
                if opponent_model is not None:
                    result['opponent_model'] = opponent_model

                # 尝试获取初始状态
                initial_state = current_phase.get_initial_state()
                if initial_state is not None:
                    result['initial_state'] = initial_state

        return result


def test_enhanced_training():
    """
    测试增强版训练方法
    """
    # 创建温度调度器
    temperature_scheduler = TemperatureScheduler()

    # 测试温度调度
    print("\n=== 测试温度调度器 ===")
    temps = []
    for i in range(10):
        temp = temperature_scheduler.step(1000)
        temps.append(temp)
    print(f"\u6e29度变化: {temps}")

    # 创建历史模型池
    model_pool = HistoricalModelPool(max_models=5)

    # 测试模型池
    print("\n=== 测试历史模型池 ===")

    # 创建一个简单的模型
    model = torch.nn.Sequential(torch.nn.Linear(10, 5))

    # 添加模型
    for i in range(3):
        model_pool.add_model(model)
        print(f"\u6dfb加模型 {i+1}, 模型数量: {model_pool.get_model_count()}")

    # 采样模型
    sampled_model = model_pool.sample_model()
    print(f"\u91c7样模型: {sampled_model is not None}")

    # 测试课程学习
    print("\n=== 测试课程学习 ===")

    # 创建一个增强版训练阶段
    phase = EnhancedTrainingPhase(
        name="测试阶段",
        num_episodes=100,
        eval_interval=10,
        success_threshold=0.6,
        curriculum_config={
            'stages': 2,
            'stage_0': {'learning_rate': 0.001},
            'stage_1': {'learning_rate': 0.0005}
        }
    )

    # 模拟训练过程
    phase.start()
    for i in range(60):
        phase.update({'episode': i, 'win_rate': 0.5})
        if i % 10 == 0:
            print(f"\u56de合 {i}, 课程学习阶段: {phase.curriculum_stage}, 进度: {phase.curriculum_progress:.2f}")

    return {
        'temperature_scheduler': temperature_scheduler,
        'model_pool': model_pool,
        'phase': phase
    }


if __name__ == "__main__":
    # 如果直接运行这个文件，则执行测试
    test_enhanced_training()