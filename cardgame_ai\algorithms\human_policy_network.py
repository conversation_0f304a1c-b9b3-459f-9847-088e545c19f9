"""
人类策略网络模块

实现一个模仿人类出牌行为的策略网络，用于模拟、评估或作为混合策略的一部分。
支持多种风格的人类策略，包括保守型、激进型和随机型。
"""

import os
import torch
import torch.nn as nn
import torch.nn.functional as F
import logging
import numpy as np
from typing import Dict, List, Any, Optional

from cardgame_ai.utils.data_loader import load_human_log_data

# 配置日志
logger = logging.getLogger(__name__)


class HumanPolicyNetwork(nn.Module):
    """
    人类策略网络

    模仿人类出牌行为的策略网络，用于模拟、评估或作为混合策略的一部分。
    """

    def __init__(self, state_dim: int, action_dim: int,
                hidden_dims: List[int] = [256, 128],
                dropout_rate: float = 0.2):
        """
        初始化人类策略网络

        Args:
            state_dim: 状态维度
            action_dim: 动作维度
            hidden_dims: 隐藏层维度列表
            dropout_rate: Dropout比率
        """
        super(HumanPolicyNetwork, self).__init__()

        self.state_dim = state_dim
        self.action_dim = action_dim
        self.hidden_dims = hidden_dims
        self.dropout_rate = dropout_rate

        # 构建网络层
        layers = []
        input_dim = state_dim

        for hidden_dim in hidden_dims:
            layers.append(nn.Linear(input_dim, hidden_dim))
            layers.append(nn.ReLU())
            layers.append(nn.Dropout(dropout_rate))
            input_dim = hidden_dim

        # 输出层
        layers.append(nn.Linear(input_dim, action_dim))

        # 构建序列模型
        self.network = nn.Sequential(*layers)

    def forward(self, state: torch.Tensor) -> torch.Tensor:
        """
        前向传播

        Args:
            state: 状态张量

        Returns:
            动作概率对数
        """
        # 确保输入形状正确
        if state.dim() == 1:
            state = state.unsqueeze(0)  # 添加批次维度

        # 通过网络获取动作概率对数
        action_logits = self.network(state)

        return action_logits

    def predict(self, state: torch.Tensor, temperature: float = 1.0) -> torch.Tensor:
        """
        预测动作概率

        Args:
            state: 状态张量
            temperature: 温度参数，控制探索程度

        Returns:
            动作概率
        """
        action_logits = self.forward(state)

        # 应用温度缩放
        if temperature != 1.0:
            action_logits = action_logits / temperature

        # 转换为概率
        action_probs = F.softmax(action_logits, dim=-1)

        return action_probs

    def act(self, state: torch.Tensor, legal_actions: List[int] = None,
            deterministic: bool = False, temperature: float = 1.0) -> int:
        """
        选择动作

        Args:
            state: 状态张量
            legal_actions: 合法动作列表
            deterministic: 是否确定性选择
            temperature: 温度参数

        Returns:
            选择的动作ID
        """
        # 获取动作概率
        action_probs = self.predict(state, temperature)

        # 如果有合法动作限制，将非法动作的概率设为0
        if legal_actions is not None:
            # 创建掩码
            mask = torch.zeros_like(action_probs)
            mask[0, legal_actions] = 1.0

            # 应用掩码
            masked_probs = action_probs * mask

            # 重新归一化
            sum_probs = masked_probs.sum()
            if sum_probs > 0:
                masked_probs = masked_probs / sum_probs
            else:
                # 如果所有概率都为0，使用均匀分布
                masked_probs = mask / mask.sum()

            action_probs = masked_probs

        # 选择动作
        if deterministic:
            # 确定性选择：选择概率最高的动作
            action_id = torch.argmax(action_probs).item()
        else:
            # 随机选择：根据概率分布采样
            action_id = torch.multinomial(action_probs.view(-1), 1).item()

        return action_id


class EnhancedHumanPolicyNetwork(nn.Module):
    """
    增强版人类策略网络

    支持多种风格的人类策略，包括保守型、激进型和随机型。
    可以根据游戏状态和玩家角色动态调整策略。
    """

    def __init__(self, state_dim: int, action_dim: int,
                hidden_dims: List[int] = [256, 128],
                num_styles: int = 3,
                style_dim: int = 32,
                dropout_rate: float = 0.2,
                use_attention: bool = True):
        """
        初始化增强版人类策略网络

        Args:
            state_dim: 状态维度
            action_dim: 动作维度
            hidden_dims: 隐藏层维度列表
            num_styles: 风格数量
            style_dim: 风格嵌入维度
            dropout_rate: Dropout比率
            use_attention: 是否使用注意力机制
        """
        super(EnhancedHumanPolicyNetwork, self).__init__()

        self.state_dim = state_dim
        self.action_dim = action_dim
        self.hidden_dims = hidden_dims
        self.num_styles = num_styles
        self.style_dim = style_dim
        self.dropout_rate = dropout_rate
        self.use_attention = use_attention

        # 风格嵌入
        self.style_embeddings = nn.Embedding(num_styles, style_dim)

        # 状态编码器
        encoder_layers = []
        encoder_layers.append(nn.Linear(state_dim, hidden_dims[0]))
        encoder_layers.append(nn.ReLU())
        encoder_layers.append(nn.Dropout(dropout_rate))

        for i in range(len(hidden_dims) - 1):
            encoder_layers.append(nn.Linear(hidden_dims[i], hidden_dims[i + 1]))
            encoder_layers.append(nn.ReLU())
            encoder_layers.append(nn.Dropout(dropout_rate))

        self.encoder = nn.Sequential(*encoder_layers)

        # 注意力机制（如果启用）
        if use_attention:
            self.query_proj = nn.Linear(hidden_dims[-1], hidden_dims[-1])
            self.key_proj = nn.Linear(style_dim, hidden_dims[-1])
            self.value_proj = nn.Linear(style_dim, hidden_dims[-1])
            self.attention_scale = hidden_dims[-1] ** 0.5

        # 策略头
        policy_input_dim = hidden_dims[-1] + style_dim if not use_attention else hidden_dims[-1] * 2
        self.policy_head = nn.Sequential(
            nn.Linear(policy_input_dim, hidden_dims[-1]),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dims[-1], action_dim)
        )

        # 风格描述
        self.style_descriptions = {
            0: "保守型",  # 保守型玩家，倾向于保留强牌
            1: "激进型",  # 激进型玩家，倾向于快速出牌
            2: "随机型"   # 随机型玩家，行为不可预测
        }

        # 初始化统计信息
        self.stats = {
            "style_usage": {i: 0 for i in range(num_styles)}
        }

    def forward(self, state: torch.Tensor, style_id: int = None) -> torch.Tensor:
        """
        前向传播

        Args:
            state: 状态张量
            style_id: 风格ID，如果为None则随机选择

        Returns:
            动作概率对数
        """
        # 确保输入形状正确
        if state.dim() == 1:
            state = state.unsqueeze(0)  # 添加批次维度

        # 如果没有指定风格ID，随机选择
        if style_id is None:
            style_id = np.random.randint(0, self.num_styles)

        # 记录风格使用
        self.stats["style_usage"][style_id] += 1

        # 获取风格嵌入
        style_embedding = self.style_embeddings(torch.tensor(style_id, device=state.device))

        # 如果批次大小大于1，扩展风格嵌入
        if state.size(0) > 1:
            style_embedding = style_embedding.unsqueeze(0).expand(state.size(0), -1)

        # 编码状态
        encoded_state = self.encoder(state)

        # 使用注意力机制（如果启用）
        if self.use_attention:
            # 计算注意力
            query = self.query_proj(encoded_state)
            key = self.key_proj(style_embedding)
            value = self.value_proj(style_embedding)

            # 计算注意力分数
            attention_scores = torch.matmul(query, key.transpose(-2, -1)) / self.attention_scale
            attention_weights = F.softmax(attention_scores, dim=-1)

            # 应用注意力
            context = torch.matmul(attention_weights, value)

            # 连接编码状态和上下文
            combined = torch.cat((encoded_state, context), dim=-1)
        else:
            # 直接连接编码状态和风格嵌入
            combined = torch.cat((encoded_state, style_embedding), dim=-1)

        # 输出动作概率对数
        action_logits = self.policy_head(combined)

        return action_logits

    def predict(self, state: torch.Tensor, style_id: int = None, temperature: float = 1.0) -> torch.Tensor:
        """
        预测动作概率

        Args:
            state: 状态张量
            style_id: 风格ID，如果为None则随机选择
            temperature: 温度参数，控制探索程度

        Returns:
            动作概率
        """
        action_logits = self.forward(state, style_id)

        # 应用温度缩放
        if temperature != 1.0:
            action_logits = action_logits / temperature

        # 转换为概率
        action_probs = F.softmax(action_logits, dim=-1)

        return action_probs

    def act(self, state: torch.Tensor, style_id: int = None, legal_actions: List[int] = None,
            deterministic: bool = False, temperature: float = 1.0) -> int:
        """
        选择动作

        Args:
            state: 状态张量
            style_id: 风格ID，如果为None则随机选择
            legal_actions: 合法动作列表
            deterministic: 是否确定性选择
            temperature: 温度参数

        Returns:
            选择的动作ID
        """
        # 获取动作概率
        action_probs = self.predict(state, style_id, temperature)

        # 如果有合法动作限制，将非法动作的概率设为0
        if legal_actions is not None:
            # 创建掩码
            mask = torch.zeros_like(action_probs)
            mask[0, legal_actions] = 1.0

            # 应用掩码
            masked_probs = action_probs * mask

            # 重新归一化
            sum_probs = masked_probs.sum()
            if sum_probs > 0:
                masked_probs = masked_probs / sum_probs
            else:
                # 如果所有概率都为0，使用均匀分布
                masked_probs = mask / mask.sum()

            action_probs = masked_probs

        # 选择动作
        if deterministic:
            # 确定性选择：选择概率最高的动作
            action_id = torch.argmax(action_probs).item()
        else:
            # 随机选择：根据概率分布采样
            action_id = torch.multinomial(action_probs.view(-1), 1).item()

        return action_id

    def get_style_description(self, style_id: int) -> str:
        """
        获取风格描述

        Args:
            style_id: 风格ID

        Returns:
            风格描述
        """
        return self.style_descriptions.get(style_id, "未知风格")

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            统计信息
        """
        return {
            "style_usage": self.stats["style_usage"],
            "style_descriptions": self.style_descriptions
        }

    def save(self, path: str) -> None:
        """
        保存模型

        Args:
            path: 保存路径
        """
        # 创建目录
        os.makedirs(os.path.dirname(path), exist_ok=True)

        # 保存模型
        torch.save({
            'model_state_dict': self.state_dict(),
            'stats': self.stats,
            'style_descriptions': self.style_descriptions,
            'config': {
                'state_dim': self.state_dim,
                'action_dim': self.action_dim,
                'hidden_dims': self.hidden_dims,
                'num_styles': self.num_styles,
                'style_dim': self.style_dim,
                'dropout_rate': self.dropout_rate,
                'use_attention': self.use_attention
            }
        }, path)

        logger.info(f"模型已保存到: {path}")

    @classmethod
    def load(cls, path: str, device: str = None) -> 'EnhancedHumanPolicyNetwork':
        """
        加载模型

        Args:
            path: 加载路径
            device: 计算设备

        Returns:
            加载的模型
        """
        # 设置设备
        if device is None:
            device = 'cuda' if torch.cuda.is_available() else 'cpu'

        # 加载模型
        checkpoint = torch.load(path, map_location=device)

        # 获取配置
        config = checkpoint['config']

        # 创建模型
        model = cls(
            state_dim=config['state_dim'],
            action_dim=config['action_dim'],
            hidden_dims=config['hidden_dims'],
            num_styles=config['num_styles'],
            style_dim=config['style_dim'],
            dropout_rate=config['dropout_rate'],
            use_attention=config['use_attention']
        ).to(device)

        # 加载状态
        model.load_state_dict(checkpoint['model_state_dict'])
        model.stats = checkpoint['stats']
        model.style_descriptions = checkpoint['style_descriptions']

        logger.info(f"模型已加载: {path}")

        return model


def train_enhanced_human_policy(log_data_path: str, model_save_path: str,
                           state_dim: int, action_dim: int,
                           hidden_dims: List[int] = [256, 128],
                           num_styles: int = 3,
                           style_dim: int = 32,
                           dropout_rate: float = 0.2,
                           use_attention: bool = True,
                           learning_rate: float = 0.001,
                           batch_size: int = 32,
                           num_epochs: int = 50,
                           patience: int = 5,
                           device: str = None) -> EnhancedHumanPolicyNetwork:
    """
    训练增强版人类策略网络

    Args:
        log_data_path: 日志数据路径
        model_save_path: 模型保存路径
        state_dim: 状态维度
        action_dim: 动作维度
        hidden_dims: 隐藏层维度列表
        num_styles: 风格数量
        style_dim: 风格嵌入维度
        dropout_rate: Dropout比率
        use_attention: 是否使用注意力机制
        learning_rate: 学习率
        batch_size: 批次大小
        num_epochs: 训练轮数
        patience: 早停耐心值
        device: 计算设备

    Returns:
        训练好的增强版人类策略网络
    """
    # 设置设备
    if device is None:
        device = 'cuda' if torch.cuda.is_available() else 'cpu'

    # 创建保存目录
    os.makedirs(os.path.dirname(model_save_path), exist_ok=True)

    # 加载数据
    train_loader, val_loader = load_human_log_data(
        log_data_path,
        batch_size=batch_size
    )

    # 如果没有训练样本，则报错并退出
    if len(train_loader) == 0:
        logger.error(f"训练数据为空: {log_data_path}，请检查交互日志目录是否正确")
        raise RuntimeError(f"训练数据为空: {log_data_path}")

    # 创建模型
    model = EnhancedHumanPolicyNetwork(
        state_dim=state_dim,
        action_dim=action_dim,
        hidden_dims=hidden_dims,
        num_styles=num_styles,
        style_dim=style_dim,
        dropout_rate=dropout_rate,
        use_attention=use_attention
    ).to(device)

    # 创建优化器和损失函数
    optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)
    criterion = nn.CrossEntropyLoss()

    # 训练跟踪变量
    best_val_loss = float('inf')
    best_epoch = 0
    no_improve_count = 0

    # 训练循环
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0

        for states, actions in train_loader:
            # 移动数据到设备
            states = states.to(device)
            actions = actions.to(device)

            # 随机选择风格
            style_ids = torch.randint(0, num_styles, (len(states),)).to(device)

            # 前向传播（对每个样本使用不同的风格）
            batch_loss = 0.0
            batch_correct = 0
            for i, (state, action) in enumerate(zip(states, actions)):
                # 使用特定风格进行前向传播
                logits = model(state.unsqueeze(0), style_ids[i])
                loss = criterion(logits, action.unsqueeze(0))
                batch_loss += loss

                # 统计
                _, predicted = torch.max(logits, 1)
                batch_correct += (predicted == action).sum().item()

            # 平均损失
            batch_loss /= len(states)

            # 反向传播和优化
            optimizer.zero_grad()
            batch_loss.backward()
            optimizer.step()

            # 统计
            train_loss += batch_loss.item()
            train_total += len(states)
            train_correct += batch_correct

        # 计算训练指标
        train_loss = train_loss / len(train_loader)
        train_acc = train_correct / train_total

        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0

        with torch.no_grad():
            for states, actions in val_loader:
                # 移动数据到设备
                states = states.to(device)
                actions = actions.to(device)

                # 随机选择风格
                style_ids = torch.randint(0, num_styles, (len(states),)).to(device)

                # 前向传播（对每个样本使用不同的风格）
                batch_loss = 0.0
                batch_correct = 0
                for i, (state, action) in enumerate(zip(states, actions)):
                    # 使用特定风格进行前向传播
                    logits = model(state.unsqueeze(0), style_ids[i])
                    loss = criterion(logits, action.unsqueeze(0))
                    batch_loss += loss

                    # 统计
                    _, predicted = torch.max(logits, 1)
                    batch_correct += (predicted == action).sum().item()

                # 平均损失
                batch_loss /= len(states)

                # 统计
                val_loss += batch_loss.item()
                val_total += len(states)
                val_correct += batch_correct

        # 计算验证指标
        val_loss = val_loss / len(val_loader)
        val_acc = val_correct / val_total if val_total > 0 else 0

        # 打印训练信息
        logger.info(f"Epoch {epoch+1}/{num_epochs}, "
                   f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}, "
                   f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}")

        # 检查是否需要保存模型
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            best_epoch = epoch
            no_improve_count = 0

            # 保存最佳模型
            model.save(model_save_path)
            logger.info(f"保存模型到 {model_save_path}")
        else:
            no_improve_count += 1

        # 早停检查
        if no_improve_count >= patience:
            logger.info(f"早停: {patience} 轮未改善")
            break

    # 训练结束，加载最佳模型
    logger.info(f"训练完成. 最佳模型在第 {best_epoch+1} 轮, 验证损失: {best_val_loss:.4f}")

    # 加载最佳模型
    model = EnhancedHumanPolicyNetwork.load(model_save_path, device)

    return model


def train_human_policy(log_data_path: str, model_save_path: str,
                      state_dim: int, action_dim: int,
                      hidden_dims: List[int] = [256, 128],
                      learning_rate: float = 0.001,
                      batch_size: int = 32,
                      num_epochs: int = 50,
                      patience: int = 5,
                      device: str = None) -> HumanPolicyNetwork:
    """
    训练人类策略网络

    Args:
        log_data_path: 日志数据路径
        model_save_path: 模型保存路径
        state_dim: 状态维度
        action_dim: 动作维度
        hidden_dims: 隐藏层维度列表
        learning_rate: 学习率
        batch_size: 批次大小
        num_epochs: 训练轮数
        patience: 早停耐心值
        device: 计算设备

    Returns:
        训练好的人类策略网络
    """
    # 设置设备
    if device is None:
        device = 'cuda' if torch.cuda.is_available() else 'cpu'

    # 创建保存目录
    os.makedirs(os.path.dirname(model_save_path), exist_ok=True)

    # 加载数据
    train_loader, val_loader = load_human_log_data(
        log_data_path,
        batch_size=batch_size
    )

    # 如果没有训练样本，则报错并退出
    if len(train_loader) == 0:
        logger.error(f"训练数据为空: {log_data_path}，请检查交互日志目录是否正确")
        raise RuntimeError(f"训练数据为空: {log_data_path}")

    # 创建模型
    model = HumanPolicyNetwork(
        state_dim=state_dim,
        action_dim=action_dim,
        hidden_dims=hidden_dims
    ).to(device)

    # 创建优化器和损失函数
    optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)
    criterion = nn.CrossEntropyLoss()

    # 训练跟踪变量
    best_val_loss = float('inf')
    best_epoch = 0
    no_improve_count = 0

    # 训练循环
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0

        for states, actions in train_loader:
            # 移动数据到设备
            states = states.to(device)
            actions = actions.to(device)

            # 前向传播
            logits = model(states)
            loss = criterion(logits, actions)

            # 反向传播和优化
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()

            # 统计
            train_loss += loss.item()
            _, predicted = torch.max(logits, 1)
            train_total += actions.size(0)
            train_correct += (predicted == actions).sum().item()

        # 计算训练指标
        train_loss = train_loss / len(train_loader)
        train_acc = train_correct / train_total

        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0

        with torch.no_grad():
            for states, actions in val_loader:
                # 移动数据到设备
                states = states.to(device)
                actions = actions.to(device)

                # 前向传播
                logits = model(states)
                loss = criterion(logits, actions)

                # 统计
                val_loss += loss.item()
                _, predicted = torch.max(logits, 1)
                val_total += actions.size(0)
                val_correct += (predicted == actions).sum().item()

        # 计算验证指标
        val_loss = val_loss / len(val_loader)
        val_acc = val_correct / val_total if val_total > 0 else 0

        # 打印训练信息
        logger.info(f"Epoch {epoch+1}/{num_epochs}, "
                   f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}, "
                   f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}")

        # 检查是否需要保存模型
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            best_epoch = epoch
            no_improve_count = 0

            # 保存最佳模型
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'train_loss': train_loss,
                'val_loss': val_loss,
                'train_acc': train_acc,
                'val_acc': val_acc,
            }, model_save_path)

            logger.info(f"保存模型到 {model_save_path}")
        else:
            no_improve_count += 1

        # 早停检查
        if no_improve_count >= patience:
            logger.info(f"早停: {patience} 轮未改善")
            break

    # 训练结束，加载最佳模型
    logger.info(f"训练完成. 最佳模型在第 {best_epoch+1} 轮, 验证损失: {best_val_loss:.4f}")

    # 加载最佳模型
    checkpoint = torch.load(model_save_path)
    model.load_state_dict(checkpoint['model_state_dict'])

    return model
