"""
MCTS代理模块

实现基于蒙特卡洛树搜索的代理，支持解释功能。
"""

import time
import logging
import numpy as np
from typing import Dict, List, Tuple, Any, Optional, Union

from cardgame_ai.core.agent import BaseAgent
from cardgame_ai.core.base import State, Action, Experience, Batch
from cardgame_ai.algorithms.mcts import MCTS
from cardgame_ai.algorithms.opponent_modeling.online_modeler import OnlineOpponentModeler
from cardgame_ai.models.value_policy_net import ValuePolicyNet

class MCTSAgent(BaseAgent):
    """
    基于蒙特卡洛树搜索的代理

    使用MCTS算法进行决策，支持解释功能和在线对手建模。
    """

    def __init__(
        self,
        model: Any,
        num_simulations: int = 50,
        discount: float = 0.997,
        dirichlet_alpha: float = 0.25,
        exploration_fraction: float = 0.25,
        pb_c_base: int = 19652,
        pb_c_init: float = 1.25,
        use_belief_state: bool = False,
        use_information_value: bool = False,
        information_value_weight: float = 0.3,
        information_value_method: str = 'combined',
        use_deep_belief_tracker: bool = False,
        deep_belief_weight: float = 0.7,
        use_opponent_model_prior: bool = False,
        opponent_model_prior_weight: float = 0.5,
        opponent_model_window_size: int = 20,
        enable_logging: bool = False
    ):
        """
        初始化MCTS代理

        Args:
            model (Any): 用于预测的模型
            num_simulations (int, optional): 每次决策的模拟次数. Defaults to 50.
            discount (float, optional): 折扣因子. Defaults to 0.997.
            dirichlet_alpha (float, optional): Dirichlet噪声参数. Defaults to 0.25.
            exploration_fraction (float, optional): 探索噪声比例. Defaults to 0.25.
            pb_c_base (int, optional): PUCT公式基础常数. Defaults to 19652.
            pb_c_init (float, optional): PUCT公式初始常数. Defaults to 1.25.
            use_belief_state (bool, optional): 是否使用信念状态. Defaults to False.
            use_information_value (bool, optional): 是否使用信息价值. Defaults to False.
            information_value_weight (float, optional): 信息价值权重. Defaults to 0.3.
            information_value_method (str, optional): 信息价值计算方法. Defaults to 'combined'.
            use_deep_belief_tracker (bool, optional): 是否使用深度信念追踪器. Defaults to False.
            deep_belief_weight (float, optional): 深度信念权重. Defaults to 0.7.
            use_opponent_model_prior (bool, optional): 是否使用对手模型先验. Defaults to False.
            opponent_model_prior_weight (float, optional): 对手模型先验权重. Defaults to 0.5.
            opponent_model_window_size (int, optional): 对手模型窗口大小. Defaults to 20.
            enable_logging (bool, optional): 是否启用日志记录. Defaults to False.
        """
        # 初始化基类，启用在线对手建模
        super().__init__(use_online_modeler=use_opponent_model_prior)

        self.model = model
        self.logger = logging.getLogger(__name__)
        self.enable_logging = enable_logging

        # 创建MCTS搜索器
        self.mcts = MCTS(
            num_simulations=num_simulations,
            discount=discount,
            dirichlet_alpha=dirichlet_alpha,
            exploration_fraction=exploration_fraction,
            pb_c_base=pb_c_base,
            pb_c_init=pb_c_init,
            root_exploration_noise=True,
            use_belief_state=use_belief_state,
            use_information_value=use_information_value,
            information_value_weight=information_value_weight,
            information_value_method=information_value_method,
            use_deep_belief_tracker=use_deep_belief_tracker,
            deep_belief_weight=deep_belief_weight,
            use_opponent_model_prior=use_opponent_model_prior,
            opponent_model_prior_weight=opponent_model_prior_weight
        )

        # 存储追踪器
        self.belief_trackers = {}  # 普通信念追踪器
        self.deep_belief_trackers = {}  # 深度信念追踪器

        # 对手模型
        self.use_opponent_model_prior = use_opponent_model_prior
        self.opponent_model_prior_weight = opponent_model_prior_weight

        # 如果使用BaseAgent的在线对手建模，则关闭此处的对手建模器
        # 避免重复创建对手建模器
        if not use_opponent_model_prior or self.online_modeler:
            self.opponent_modeler = None
        else:
            self.opponent_modeler = OnlineOpponentModeler(
                window_size=opponent_model_window_size,
                enable_logging=enable_logging
            )

        # 记录当前玩家ID，用于跟踪对手
        self.player_id = None
        self.opponent_ids = set()

        # 统计信息
        self.stats = {
            "decisions_made": 0,
            "avg_decision_time": 0.0,
            "total_simulations": 0,
            "opponent_actions_observed": 0
        }

    def observe(self, observation: Any, reward: float, done: bool, info: Dict[str, Any]) -> None:
        """
        观察环境信息和对手动作，并更新对手模型

        Args:
            observation (Any): 当前观察
            reward (float): 获得的奖励
            done (bool): 是否结束
            info (Dict[str, Any]): 额外信息，包括对手动作等
        """
        # 调用基类的observe方法，更新在线对手建模器
        super().observe(observation, reward, done, info)

        # 如果自定义了对手建模器，也进行更新
        if self.opponent_modeler and 'opponent_action' in info and 'opponent_id' in info:
            # 获取对手ID和动作
            opponent_id = info['opponent_id']
            opponent_action = info['opponent_action']

            # 更新对手ID集合
            if opponent_id not in self.opponent_ids:
                self.opponent_ids.add(opponent_id)

            # 基本更新
            game_state = observation.game_state if hasattr(observation, 'game_state') else None
            self.opponent_modeler.update(
                player_id=opponent_id,
                action=opponent_action,
                game_state=game_state
            )

            # 如果提供了上下文信息，则进行上下文相关的更新
            if 'context_key' in info:
                self.opponent_modeler.context_aware_update(
                    player_id=opponent_id,
                    action=opponent_action,
                    context_key=info['context_key'],
                    game_state=game_state
                )

            # 更新统计信息
            self.stats["opponent_actions_observed"] += 1

            # 记录日志
            if self.enable_logging:
                self.logger.debug(f"观察到对手 {opponent_id} 的动作: {opponent_action}")

                # 如果有上下文，也记录
                if 'context_key' in info:
                    self.logger.debug(f"上下文: {info['context_key']}")

    def act(self, state: Union[State, np.ndarray], legal_actions: List[Action], is_training: bool = False,
             belief_trackers: Optional[Dict[str, Any]] = None,
             deep_belief_trackers: Optional[Dict[str, Any]] = None,
             opponent_model_priors: Optional[Dict[str, Dict[int, float]]] = None) -> Action:
        """
        选择动作

        Args:
            state (Union[State, np.ndarray]): 游戏状态或观察
            legal_actions (List[Action]): 合法动作列表
            is_training (bool, optional): 是否为训练模式. Defaults to False.
            belief_trackers (Optional[Dict[str, Any]], optional): 信念追踪器字典. Defaults to None.
            deep_belief_trackers (Optional[Dict[str, Any]], optional): 深度信念追踪器字典. Defaults to None.
            opponent_model_priors (Optional[Dict[str, Dict[int, float]]], optional): 对手模型先验. Defaults to None.

        Returns:
            Action: 选择的动作
        """
        # 记录开始时间
        start_time = time.time()

        # 使用传入的追踪器或已存储的追踪器
        belief_trackers = belief_trackers or self.belief_trackers
        deep_belief_trackers = deep_belief_trackers or self.deep_belief_trackers

        # 从状态中获取当前玩家ID
        current_player_id = self._extract_player_id(state)
        if current_player_id:
            self.player_id = current_player_id

        # 如果启用了对手建模，且没有提供对手先验，则使用在线对手模型生成先验
        if self.use_opponent_model_prior and not opponent_model_priors:
            # 尝试使用BaseAgent中的在线对手建模器
            if self.online_modeler:
                opponent_model_priors = self._generate_opponent_model_priors_from_online_modeler(state, legal_actions)
            # 回退到自定义对手建模器
            elif self.opponent_modeler:
                opponent_model_priors = self._generate_opponent_model_priors(state, legal_actions)

        # 创建动作掩码
        action_mask = None
        if legal_actions is not None:
            max_action_id = max([self._get_action_id(a) for a in legal_actions] + [0])
            action_mask = [0] * (max_action_id + 1)
            for action in legal_actions:
                action_id = self._get_action_id(action)
                action_mask[action_id] = 1

        # 使用MCTS进行搜索
        temperature = 1.0 if is_training else 0.0  # 训练时使用温度参数，推理时确定性选择
        visit_counts, action_probs = self.mcts.run(
            state,
            self.model,
            temperature=temperature,
            actions_mask=action_mask,
            belief_trackers=belief_trackers,
            deepbelief_tracker=deep_belief_trackers,
            opponent_model_priors=opponent_model_priors
        )

        # 选择动作
        if temperature == 0:
            # 确定性选择：选择访问次数最多的动作
            action_id = max(visit_counts.items(), key=lambda x: x[1])[0]
        else:
            # 随机选择：根据概率分布采样
            actions = list(action_probs.keys())
            probs = list(action_probs.values())
            action_id = np.random.choice(actions, p=probs)

        # 将动作ID转换为实际动作
        action = self._get_action_from_id(action_id, legal_actions)

        # 更新统计信息
        decision_time = time.time() - start_time
        self.stats["decisions_made"] += 1
        self.stats["avg_decision_time"] = (self.stats["avg_decision_time"] * (self.stats["decisions_made"] - 1) + decision_time) / self.stats["decisions_made"]
        self.stats["total_simulations"] += self.mcts.num_simulations

        return action

    def _extract_player_id(self, state: Union[State, np.ndarray]) -> Optional[str]:
        """
        从状态中提取当前玩家ID

        Args:
            state (Union[State, np.ndarray]): 游戏状态或观察

        Returns:
            Optional[str]: 玩家ID，如果无法提取则返回None
        """
        # 如果状态是numpy数组，无法提取玩家ID
        if isinstance(state, np.ndarray):
            return None

        # 尝试从状态对象中获取玩家ID
        if hasattr(state, 'current_player'):
            return state.current_player
        elif hasattr(state, 'to_play'):
            return state.to_play

        return None

    def _generate_opponent_model_priors_from_online_modeler(self, state: Any, legal_actions: List[Action]) -> Dict[str, Dict[int, float]]:
        """
        从BaseAgent的在线对手建模器生成先验概率

        Args:
            state (Any): 游戏状态
            legal_actions (List[Action]): 合法动作列表

        Returns:
            Dict[str, Dict[int, float]]: 对手模型先验，键为玩家ID，值为动作ID到概率的映射
        """
        if not self.online_modeler or not hasattr(self, 'opponent_ids') or not self.opponent_ids:
            return {}

        result = {}
        action_to_id = lambda a: self._get_action_id(a)

        # 为每个已知对手生成先验概率
        for opponent_id in self.opponent_ids:
            # 尝试提取该对手的合法动作（如果游戏状态提供）
            opponent_legal_actions = self._extract_opponent_legal_actions(state, opponent_id, legal_actions)

            if opponent_legal_actions:
                # 获取上下文键（如果可用）
                context_key = self._extract_game_context(state, opponent_id)

                # 使用在线对手建模器生成并转换为MCTS需要的格式
                id_priors = self.online_modeler.convert_to_mcts_priors(
                    opponent_id,
                    opponent_legal_actions,
                    action_to_id,
                    context_key
                )

                if id_priors:
                    result[opponent_id] = id_priors

                    # 记录日志
                    if self.enable_logging:
                        self.logger.debug(f"为对手 {opponent_id} 生成了先验概率: {id_priors}")

        return result

    def _generate_opponent_model_priors(self, state: Any, legal_actions: List[Action]) -> Dict[str, Dict[int, float]]:
        """
        根据对手模型生成先验概率

        Args:
            state (Any): 游戏状态
            legal_actions (List[Action]): 合法动作列表

        Returns:
            Dict[str, Dict[int, float]]: 对手模型先验，键为玩家ID，值为动作ID到概率的映射
        """
        if not self.opponent_modeler or not self.opponent_ids:
            return {}

        result = {}
        action_to_id = lambda a: self._get_action_id(a)

        # 为每个已知对手生成先验概率
        for opponent_id in self.opponent_ids:
            # 尝试提取该对手的合法动作（如果游戏状态提供）
            opponent_legal_actions = self._extract_opponent_legal_actions(state, opponent_id, legal_actions)

            if opponent_legal_actions:
                # 获取对手在当前游戏上下文下可能的先验概率
                context_key = self._extract_game_context(state, opponent_id)

                # 生成对手动作先验并转换为MCTS需要的格式
                id_priors = self.opponent_modeler.convert_to_mcts_priors(
                    opponent_id,
                    opponent_legal_actions,
                    action_to_id,
                    context_key
                )

                if id_priors:
                    result[opponent_id] = id_priors

                    # 记录日志
                    if self.enable_logging:
                        self.logger.debug(f"为对手 {opponent_id} 生成了先验概率: {id_priors}")

        return result

    def _extract_opponent_legal_actions(self, state: Any, opponent_id: str, default_actions: List[Action]) -> List[Action]:
        """
        从游戏状态中提取对手的合法动作

        Args:
            state (Any): 游戏状态
            opponent_id (str): 对手ID
            default_actions (List[Action]): 默认动作列表，当无法从状态中提取时使用

        Returns:
            List[Action]: 对手的合法动作列表
        """
        # 如果状态是numpy数组，无法提取合法动作
        if isinstance(state, np.ndarray):
            return default_actions

        # 尝试从状态对象中获取对手的合法动作
        if hasattr(state, 'get_legal_actions_for_player'):
            try:
                return state.get_legal_actions_for_player(opponent_id)
            except (AttributeError, TypeError):
                pass

        # 尝试从状态的游戏对象中获取
        if hasattr(state, 'game') and hasattr(state.game, 'get_legal_actions'):
            try:
                return state.game.get_legal_actions(opponent_id)
            except (AttributeError, TypeError):
                pass

        # 如果无法从状态中提取，则使用默认动作列表
        return default_actions

    def _extract_game_context(self, state: Any, player_id: str) -> Optional[str]:
        """
        从游戏状态中提取游戏上下文

        Args:
            state (Any): 游戏状态
            player_id (str): 玩家ID

        Returns:
            Optional[str]: 游戏上下文，如果无法提取则返回None
        """
        # 如果状态是numpy数组，无法提取上下文
        if isinstance(state, np.ndarray):
            return None

        context_parts = []

        # 尝试确定玩家角色（地主/农民）
        if hasattr(state, 'landlord') and state.landlord is not None:
            is_landlord = player_id == state.landlord
            context_parts.append("landlord" if is_landlord else "farmer")

        # 尝试获取剩余手牌数量
        if hasattr(state, 'player_hands') and player_id in state.player_hands:
            cards_left = len(state.player_hands[player_id])
            if cards_left <= 5:
                context_parts.append("few_cards")
            elif cards_left >= 12:
                context_parts.append("many_cards")

        # 如果无法提取上下文，则返回None
        if not context_parts:
            return None

        # 连接上下文部分
        return "_".join(context_parts)

    def _get_action_id(self, action: Action) -> int:
        """
        获取动作的ID

        Args:
            action (Action): 动作

        Returns:
            int: 动作ID
        """
        # 尝试使用动作的hash值作为ID
        if hasattr(action, '__hash__'):
            return hash(action) % 10000  # 使用模10000避免ID过大

        # 尝试使用动作的整数表示（如果有）
        if hasattr(action, 'to_id'):
            return action.to_id()

        # 回退到直接使用动作对象的字符串表示的哈希
        return hash(str(action)) % 10000

    def _get_action_from_id(self, action_id: int, legal_actions: List[Action]) -> Action:
        """
        根据动作ID获取实际动作

        Args:
            action_id (int): 动作ID
            legal_actions (List[Action]): 合法动作列表

        Returns:
            Action: 对应的实际动作
        """
        # 遍历合法动作列表，查找ID匹配的动作
        for action in legal_actions:
            if self._get_action_id(action) == action_id:
                return action

        # 如果找不到匹配的动作，返回列表中的第一个动作（或者根据应用场景选择更合适的回退策略）
        if legal_actions:
            return legal_actions[0]

        # 如果没有合法动作（这种情况不应该发生），抛出异常
        raise ValueError(f"无法找到ID为{action_id}的动作，且没有合法动作可供选择")

    def train(self, experience: Union[Experience, Batch]) -> Dict[str, float]:
        """
        训练代理

        MCTSAgent本身不支持训练，该方法仅用于满足接口要求

        Args:
            experience (Union[Experience, Batch]): 经验数据

        Returns:
            Dict[str, float]: 空字典
        """
        return {}

    def save(self, path: str) -> None:
        """
        保存代理

        Args:
            path (str): 保存路径
        """
        # 该方法需要由具体实现类实现
        pass

    def load(self, path: str) -> None:
        """
        加载代理

        Args:
            path (str): 加载路径
        """
        # 该方法需要由具体实现类实现
        pass
