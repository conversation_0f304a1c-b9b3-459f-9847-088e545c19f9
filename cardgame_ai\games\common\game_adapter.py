"""
游戏适配器模块

提供游戏适配器接口和实现，用于将不同游戏适配到框架的通用接口。
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Tuple, Optional, Union

from cardgame_ai.core.base import State, Action


class GameAdapter(ABC):
    """
    游戏适配器接口
    
    定义游戏适配器的标准接口，用于将不同游戏适配到框架的通用接口。
    """
    
    @abstractmethod
    def adapt_state(self, state: State) -> State:
        """
        适配状态
        
        Args:
            state (State): 原始状态
            
        Returns:
            State: 适配后的状态
        """
        pass
    
    @abstractmethod
    def adapt_action(self, action: Action) -> Action:
        """
        适配动作
        
        Args:
            action (Action): 原始动作
            
        Returns:
            Action: 适配后的动作
        """
        pass
    
    @abstractmethod
    def adapt_reward(self, reward: float) -> float:
        """
        适配奖励
        
        Args:
            reward (float): 原始奖励
            
        Returns:
            float: 适配后的奖励
        """
        pass


class IdentityAdapter(GameAdapter):
    """
    恒等适配器
    
    不进行任何适配，直接返回原始值。
    """
    
    def adapt_state(self, state: State) -> State:
        """
        适配状态
        
        Args:
            state (State): 原始状态
            
        Returns:
            State: 原始状态
        """
        return state
    
    def adapt_action(self, action: Action) -> Action:
        """
        适配动作
        
        Args:
            action (Action): 原始动作
            
        Returns:
            Action: 原始动作
        """
        return action
    
    def adapt_reward(self, reward: float) -> float:
        """
        适配奖励
        
        Args:
            reward (float): 原始奖励
            
        Returns:
            float: 原始奖励
        """
        return reward
