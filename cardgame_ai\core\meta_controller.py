"""
元控制器模块

实现动态角色分配与信任度估计，通过比较人类玩家最近决策与AI模拟最优决策的差异来估计信心评分。
此评分用于动态调整AI介入程度。
"""

import os
import time
import logging
import numpy as np
from typing import Dict, List, Any, Optional, Tuple, Union, Callable

from cardgame_ai.core.base import State, Action
from cardgame_ai.games.common.belief_state import BeliefState
from cardgame_ai.games.doudizhu.state import DouDizhuState
from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType

# 配置日志
logger = logging.getLogger(__name__)


class MetaController:
    """
    元控制器类

    负责动态角色分配与信任度估计，通过比较人类玩家最近决策与AI模拟最优决策的差异来估计信心评分。
    此评分用于动态调整AI介入程度。
    """

    def __init__(
        self,
        initial_confidence: float = 1.0,
        decay_rate: float = 0.95,
        learning_rate: float = 0.1,
        history_window: int = 10,
        min_confidence: float = 0.0,
        max_confidence: float = 1.0
    ):
        """
        初始化元控制器

        Args:
            initial_confidence: 初始信心评分，范围[0, 1]
            decay_rate: 信心评分衰减率
            learning_rate: 学习率，控制信心评分更新速度
            history_window: 历史记录窗口大小
            min_confidence: 最小信心评分
            max_confidence: 最大信心评分
        """
        self.human_confidence_score = initial_confidence
        self.confidence_history = []
        self.decay_rate = decay_rate
        self.learning_rate = learning_rate
        self.history_window = history_window
        self.min_confidence = min_confidence
        self.max_confidence = max_confidence

        # 记录最近的动作相似度
        self.recent_similarities = []

        # 统计信息
        self.stats = {
            "updates": 0,
            "avg_similarity": 0.0,
            "min_similarity": 1.0,
            "max_similarity": 0.0,
            "intervention_level": 0.0
        }

    def update_confidence(self, human_action: Action, ai_best_action: Action, game_state: State) -> float:
        """
        更新信心评分

        通过比较人类动作与AI最优动作的相似度来更新信心评分。

        Args:
            human_action: 人类玩家的动作
            ai_best_action: AI推荐的最优动作
            game_state: 当前游戏状态

        Returns:
            float: 更新后的信心评分
        """
        # 计算动作相似度
        similarity = self._calculate_action_similarity(human_action, ai_best_action, game_state)

        # 更新信心评分：如果相似度高，增加信心；如果相似度低，降低信心
        target_confidence = similarity  # 简化：相似度作为目标信心评分
        self.human_confidence_score = (1 - self.learning_rate) * self.human_confidence_score + self.learning_rate * target_confidence

        # 确保信心评分在有效范围内
        self.human_confidence_score = max(self.min_confidence, min(self.max_confidence, self.human_confidence_score))

        # 存储历史记录
        self.confidence_history.append(self.human_confidence_score)
        if len(self.confidence_history) > self.history_window:
            self.confidence_history = self.confidence_history[-self.history_window:]

        # 记录相似度
        self.recent_similarities.append(similarity)
        if len(self.recent_similarities) > self.history_window:
            self.recent_similarities = self.recent_similarities[-self.history_window:]

        # 更新统计信息
        self.stats["updates"] += 1
        self.stats["avg_similarity"] = sum(self.recent_similarities) / len(self.recent_similarities) if self.recent_similarities else 0.0
        self.stats["min_similarity"] = min(self.stats["min_similarity"], similarity)
        self.stats["max_similarity"] = max(self.stats["max_similarity"], similarity)

        # 应用衰减（可选）
        # self.human_confidence_score *= self.decay_rate

        logger.debug(f"更新信心评分: {self.human_confidence_score:.4f}, 动作相似度: {similarity:.4f}")

        return self.human_confidence_score

    def get_confidence_score(self) -> float:
        """
        获取当前信心评分

        Returns:
            float: 当前信心评分
        """
        return self.human_confidence_score

    def decide_intervention_level(self) -> float:
        """
        决定AI介入程度

        基于信心评分决定AI介入程度，范围[0, 1]
        0表示完全由人类决策，1表示完全由AI决策

        Returns:
            float: AI介入程度
        """
        # 简单的反比关系：信心越低，介入程度越高
        intervention = 1.0 - self.human_confidence_score

        # 确保介入程度在有效范围内
        intervention = max(0.0, min(1.0, intervention))

        # 更新统计信息
        self.stats["intervention_level"] = intervention

        return intervention

    def _calculate_action_similarity(self, action1: Action, action2: Action, state: State) -> float:
        """
        计算两个动作的相似度

        Args:
            action1: 第一个动作
            action2: 第二个动作
            state: 当前游戏状态

        Returns:
            float: 相似度，范围[0, 1]
        """
        # 如果动作完全相同，返回最高相似度
        if action1 == action2:
            return 1.0

        # 如果其中一个动作为None，返回最低相似度
        if action1 is None or action2 is None:
            return 0.0

        # 检查是否为斗地主游戏状态和动作
        if isinstance(state, DouDizhuState) and isinstance(action1, CardGroup) and isinstance(action2, CardGroup):
            return self._calculate_doudizhu_action_similarity(action1, action2)

        # 尝试使用动作的内部比较方法（如果有）
        if hasattr(action1, 'similarity_to') and callable(getattr(action1, 'similarity_to')):
            return action1.similarity_to(action2)

        # 尝试基于动作价值评估相似度
        try:
            return self._calculate_value_based_similarity(action1, action2, state)
        except Exception as e:
            logger.warning(f"计算价值相似度失败: {e}")

        # 如果无法计算价值相似度，尝试基于动作特征计算相似度
        try:
            return self._calculate_feature_based_similarity(action1, action2, state)
        except Exception as e:
            logger.warning(f"计算特征相似度失败: {e}")

        # 如果所有方法都失败，返回二元相似度（相同为1，不同为0）
        return 0.0

    def _calculate_value_based_similarity(self, action1: Action, action2: Action, state: State) -> float:
        """
        基于动作价值计算相似度

        使用价值函数评估两个动作的价值，然后基于价值差异计算相似度。

        Args:
            action1: 第一个动作
            action2: 第二个动作
            state: 当前游戏状态

        Returns:
            float: 相似度，范围[0, 1]
        """
        # 这里需要访问价值函数来评估动作
        # 在实际实现中，可能需要传入价值函数或模型

        # 假设我们有一个evaluate_action函数
        if hasattr(self, 'value_function') and callable(self.value_function):
            # 评估两个动作的价值
            value1 = self.value_function(state, action1)
            value2 = self.value_function(state, action2)

            # 计算价值差异
            value_diff = abs(value1 - value2)

            # 将差异转换为相似度
            # 使用指数衰减函数：similarity = exp(-k * diff)
            # k控制衰减速率，这里使用k=5作为示例
            similarity = np.exp(-5 * value_diff)

            return float(similarity)

        # 如果没有价值函数，抛出异常
        raise ValueError("没有可用的价值函数来评估动作")

    def _calculate_feature_based_similarity(self, action1: Action, action2: Action, state: State) -> float:
        """
        基于动作特征计算相似度

        提取动作的特征向量，然后计算特征向量之间的相似度。

        Args:
            action1: 第一个动作
            action2: 第二个动作
            state: 当前游戏状态

        Returns:
            float: 相似度，范围[0, 1]
        """
        # 尝试将动作转换为特征向量
        if hasattr(action1, 'to_feature') and callable(getattr(action1, 'to_feature')):
            # 提取特征向量
            feature1 = action1.to_feature()
            feature2 = action2.to_feature()

            # 计算余弦相似度
            if isinstance(feature1, np.ndarray) and isinstance(feature2, np.ndarray):
                # 确保特征向量非空
                if feature1.size > 0 and feature2.size > 0:
                    # 计算余弦相似度
                    dot_product = np.dot(feature1, feature2)
                    norm1 = np.linalg.norm(feature1)
                    norm2 = np.linalg.norm(feature2)

                    if norm1 > 0 and norm2 > 0:
                        cosine_similarity = dot_product / (norm1 * norm2)
                        # 确保相似度在[0, 1]范围内
                        return float(max(0.0, min(1.0, cosine_similarity)))

        # 如果无法提取特征或计算相似度，抛出异常
        raise ValueError("无法基于特征计算动作相似度")

    def set_value_function(self, value_function: Callable[[State, Action], float]) -> None:
        """
        设置价值函数

        Args:
            value_function: 价值函数，接受状态和动作，返回价值
        """
        self.value_function = value_function

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            Dict[str, Any]: 统计信息字典
        """
        # 更新一些统计信息
        self.stats["current_confidence"] = self.human_confidence_score
        self.stats["confidence_history"] = self.confidence_history.copy()
        self.stats["recent_similarities"] = self.recent_similarities.copy()

        return self.stats

    def _calculate_doudizhu_action_similarity(self, action1: CardGroup, action2: CardGroup) -> float:
        """
        计算斗地主游戏中两个动作的相似度

        Args:
            action1: 第一个动作（牌组）
            action2: 第二个动作（牌组）

        Returns:
            float: 相似度，范围[0, 1]
        """
        # 如果两个动作都是"不出"（PASS），则完全相似
        if action1.card_type == CardGroupType.PASS and action2.card_type == CardGroupType.PASS:
            return 1.0

        # 如果一个是"不出"而另一个不是，则完全不相似
        if action1.card_type == CardGroupType.PASS or action2.card_type == CardGroupType.PASS:
            return 0.0

        # 如果牌型不同，相似度较低
        if action1.card_type != action2.card_type:
            return 0.1

        # 如果牌型相同，根据牌值计算相似度
        # 牌值越接近，相似度越高
        if hasattr(action1, 'value') and hasattr(action2, 'value'):
            # 计算牌值差异
            value_diff = abs(action1.value - action2.value)
            max_value = 15  # 假设最大牌值差异为15

            # 将差异转换为相似度
            similarity = 1.0 - (value_diff / max_value)
            return max(0.1, similarity)  # 确保最小相似度为0.1

        # 如果无法比较牌值，则比较牌数
        if hasattr(action1, 'cards') and hasattr(action2, 'cards'):
            # 计算牌数差异
            len_diff = abs(len(action1.cards) - len(action2.cards))
            max_len = max(len(action1.cards), len(action2.cards))

            if max_len > 0:
                # 将差异转换为相似度
                similarity = 1.0 - (len_diff / max_len)
                return max(0.1, similarity)  # 确保最小相似度为0.1

        # 如果无法比较，返回默认相似度
        return 0.2
