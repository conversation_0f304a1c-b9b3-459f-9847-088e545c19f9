"""
资源分配器模块

提供动态资源分配功能，根据任务复杂度和优先级分配计算资源，
提高系统资源利用率和性能。
"""

import os
import time
import logging
import threading
import multiprocessing
import psutil
from typing import Dict, Any, Optional, List, Union

# 设置日志
logger = logging.getLogger(__name__)


class ResourceMonitor:
    """
    资源监控类
    
    监控系统资源使用情况，包括CPU、内存和GPU。
    """
    
    def __init__(self, update_interval: float = 1.0):
        """
        初始化资源监控器
        
        Args:
            update_interval: 更新间隔（秒）
        """
        self.update_interval = update_interval
        self.running = False
        self.thread = None
        self.lock = threading.RLock()
        
        # 资源使用情况
        self.cpu_usage = 0.0
        self.memory_usage = 0.0
        self.gpu_usage = 0.0
        self.gpu_memory_usage = 0.0
        
        # 资源限制
        self.cpu_limit = multiprocessing.cpu_count()
        self.memory_limit = psutil.virtual_memory().total
        self.gpu_limit = 0
        self.gpu_memory_limit = 0
        
        # 检查GPU可用性
        try:
            import torch
            if torch.cuda.is_available():
                self.gpu_limit = torch.cuda.device_count()
                self.gpu_memory_limit = torch.cuda.get_device_properties(0).total_memory
        except (ImportError, Exception) as e:
            logger.warning(f"无法检查GPU可用性: {e}")
    
    def start(self):
        """启动资源监控"""
        if self.running:
            return
            
        self.running = True
        self.thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.thread.start()
        logger.info("资源监控已启动")
        
    def stop(self):
        """停止资源监控"""
        self.running = False
        if self.thread:
            self.thread.join(timeout=2.0)
            self.thread = None
        logger.info("资源监控已停止")
        
    def _monitor_loop(self):
        """资源监控循环"""
        while self.running:
            try:
                self._update_resource_usage()
                time.sleep(self.update_interval)
            except Exception as e:
                logger.error(f"资源监控出错: {e}")
                time.sleep(self.update_interval)
                
    def _update_resource_usage(self):
        """更新资源使用情况"""
        with self.lock:
            # 更新CPU使用率
            self.cpu_usage = psutil.cpu_percent(interval=None) / 100.0
            
            # 更新内存使用率
            memory = psutil.virtual_memory()
            self.memory_usage = memory.used / memory.total
            
            # 更新GPU使用率
            try:
                import torch
                if torch.cuda.is_available():
                    # 获取GPU使用率需要安装pynvml
                    import pynvml
                    pynvml.nvmlInit()
                    handle = pynvml.nvmlDeviceGetHandleByIndex(0)
                    utilization = pynvml.nvmlDeviceGetUtilizationRates(handle)
                    self.gpu_usage = utilization.gpu / 100.0
                    
                    # 获取GPU内存使用率
                    memory_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
                    self.gpu_memory_usage = memory_info.used / memory_info.total
                    
                    pynvml.nvmlShutdown()
            except (ImportError, Exception) as e:
                # 忽略错误，可能是没有安装pynvml或没有GPU
                pass
                
    def get_resource_usage(self) -> Dict[str, float]:
        """
        获取资源使用情况
        
        Returns:
            Dict[str, float]: 资源使用情况字典
        """
        with self.lock:
            return {
                "cpu_usage": self.cpu_usage,
                "memory_usage": self.memory_usage,
                "gpu_usage": self.gpu_usage,
                "gpu_memory_usage": self.gpu_memory_usage
            }
            
    def get_resource_limits(self) -> Dict[str, Any]:
        """
        获取资源限制
        
        Returns:
            Dict[str, Any]: 资源限制字典
        """
        with self.lock:
            return {
                "cpu_limit": self.cpu_limit,
                "memory_limit": self.memory_limit,
                "gpu_limit": self.gpu_limit,
                "gpu_memory_limit": self.gpu_memory_limit
            }


class DynamicResourceAllocator:
    """
    动态资源分配器类
    
    根据任务复杂度和优先级动态分配计算资源。
    """
    
    def __init__(
        self,
        max_cpu: Optional[int] = None,
        max_memory: Optional[int] = None,
        max_gpu: Optional[int] = None,
        resource_monitor: Optional[ResourceMonitor] = None
    ):
        """
        初始化动态资源分配器
        
        Args:
            max_cpu: 最大CPU核心数，None表示使用系统可用核心数
            max_memory: 最大内存（字节），None表示使用系统可用内存
            max_gpu: 最大GPU数量，None表示使用系统可用GPU数量
            resource_monitor: 资源监控器，None表示创建新的监控器
        """
        # 初始化资源监控器
        self.resource_monitor = resource_monitor or ResourceMonitor()
        self.resource_monitor.start()
        
        # 设置资源限制
        resource_limits = self.resource_monitor.get_resource_limits()
        self.max_cpu = max_cpu or resource_limits["cpu_limit"]
        self.max_memory = max_memory or resource_limits["memory_limit"]
        self.max_gpu = max_gpu or resource_limits["gpu_limit"]
        
        # 初始化分配记录
        self.allocations = {}
        self.lock = threading.RLock()
        
        logger.info(f"动态资源分配器已初始化，最大CPU: {self.max_cpu}, 最大内存: {self.max_memory}, 最大GPU: {self.max_gpu}")
        
    def allocate_resources(
        self,
        task_id: str,
        task_complexity: float,
        priority: float = 1.0,
        min_cpu: int = 1,
        min_memory: int = 1024 * 1024 * 100,  # 100MB
        min_gpu: int = 0
    ) -> Dict[str, Any]:
        """
        分配资源
        
        Args:
            task_id: 任务ID
            task_complexity: 任务复杂度，范围[0, 1]
            priority: 任务优先级，范围[0, 1]
            min_cpu: 最小CPU核心数
            min_memory: 最小内存（字节）
            min_gpu: 最小GPU数量
            
        Returns:
            Dict[str, Any]: 资源分配结果
        """
        with self.lock:
            # 获取当前资源使用情况
            resource_usage = self.resource_monitor.get_resource_usage()
            
            # 计算可用资源
            available_cpu = max(0, self.max_cpu * (1 - resource_usage["cpu_usage"]))
            available_memory = max(0, self.max_memory * (1 - resource_usage["memory_usage"]))
            available_gpu = max(0, self.max_gpu * (1 - resource_usage["gpu_usage"]))
            
            # 根据任务复杂度和优先级计算资源分配
            if task_complexity > 0.7:  # 高复杂度任务
                cpu_allocation = max(min_cpu, int(available_cpu * 0.7 * priority))
                memory_allocation = max(min_memory, int(available_memory * 0.7 * priority))
                gpu_allocation = min_gpu if available_gpu < min_gpu else (
                    1 if available_gpu >= 1 and self.max_gpu > 0 else 0
                )
            elif task_complexity > 0.3:  # 中等复杂度任务
                cpu_allocation = max(min_cpu, int(available_cpu * 0.4 * priority))
                memory_allocation = max(min_memory, int(available_memory * 0.4 * priority))
                gpu_allocation = min_gpu if available_gpu < min_gpu else (
                    1 if available_gpu >= 1 and priority > 0.8 and self.max_gpu > 0 else 0
                )
            else:  # 低复杂度任务
                cpu_allocation = max(min_cpu, int(available_cpu * 0.2 * priority))
                memory_allocation = max(min_memory, int(available_memory * 0.2 * priority))
                gpu_allocation = min_gpu
                
            # 记录分配
            allocation = {
                "cpu": cpu_allocation,
                "memory": memory_allocation,
                "gpu": gpu_allocation,
                "task_complexity": task_complexity,
                "priority": priority,
                "timestamp": time.time()
            }
            
            self.allocations[task_id] = allocation
            
            logger.debug(f"为任务 {task_id} 分配资源: CPU={cpu_allocation}, 内存={memory_allocation}, GPU={gpu_allocation}")
            
            return allocation
            
    def release_resources(self, task_id: str) -> None:
        """
        释放资源
        
        Args:
            task_id: 任务ID
        """
        with self.lock:
            if task_id in self.allocations:
                del self.allocations[task_id]
                logger.debug(f"已释放任务 {task_id} 的资源")
                
    def get_allocation(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务的资源分配
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[Dict[str, Any]]: 资源分配结果，如果任务不存在则返回None
        """
        with self.lock:
            return self.allocations.get(task_id)
            
    def get_all_allocations(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有资源分配
        
        Returns:
            Dict[str, Dict[str, Any]]: 所有资源分配结果
        """
        with self.lock:
            return self.allocations.copy()
            
    def get_total_allocated_resources(self) -> Dict[str, Any]:
        """
        获取总分配资源
        
        Returns:
            Dict[str, Any]: 总分配资源
        """
        with self.lock:
            total_cpu = sum(alloc["cpu"] for alloc in self.allocations.values())
            total_memory = sum(alloc["memory"] for alloc in self.allocations.values())
            total_gpu = sum(alloc["gpu"] for alloc in self.allocations.values())
            
            return {
                "cpu": total_cpu,
                "memory": total_memory,
                "gpu": total_gpu
            }
            
    def __del__(self):
        """析构函数，停止资源监控"""
        if hasattr(self, 'resource_monitor') and self.resource_monitor:
            self.resource_monitor.stop()
