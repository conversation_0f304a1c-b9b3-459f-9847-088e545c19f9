"""
增强信任可视化模块

提供用于可视化AI决策过程的信任相关组件，包括复杂度评分、组件调用统计和因果分析可视化。
"""

import logging
from typing import Dict, List, Any, Optional
import time

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QTabWidget,
    QGroupBox, QSizePolicy, QScrollArea, QTextEdit
)
from PySide6.QtCore import Qt, Signal, Slot
from PySide6.QtGui import QFont

from cardgame_ai.data.metrics_collector import MetricsCollector
from cardgame_ai.ui.visualization_components import (
    ComplexityScoreWidget, ComponentUsageWidget, ValueChangeWidget
)

logger = logging.getLogger(__name__)


class CausalAnalysisWidget(QWidget):
    """因果分析可视化组件"""

    def __init__(self, parent=None):
        """初始化因果分析可视化组件"""
        super().__init__(parent)

        # 设置对象名称
        self.setObjectName("causalAnalysisWidget")

        # 初始化UI
        self.setup_ui()

        logger.info("初始化因果分析可视化组件")

    def setup_ui(self):
        """设置UI"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # 创建标题标签
        title_label = QLabel("因果分析")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        main_layout.addWidget(title_label)

        # 创建因果分析文本框
        self.causal_text = QTextEdit()
        self.causal_text.setReadOnly(True)
        self.causal_text.setMinimumHeight(150)
        main_layout.addWidget(self.causal_text)

    def update_causal_analysis(self, causal_data: Dict[str, Any]):
        """
        更新因果分析

        Args:
            causal_data: 因果分析数据
        """
        if not causal_data:
            self.causal_text.setText("暂无因果分析数据")
            return

        # 生成因果分析文本
        text = f"<h3>因果分析结果</h3>"
        text += f"<p><b>置信度:</b> {causal_data.get('confidence', 0.0):.2f}</p>"
        text += f"<p><b>摘要:</b> {causal_data.get('summary', '无摘要')}</p>"

        # 添加因果效应
        causal_effects = causal_data.get("causal_effects", [])
        if causal_effects:
            text += "<h4>因果效应:</h4>"
            text += "<ul>"
            for effect in causal_effects:
                text += f"<li><b>效应:</b> {effect.get('effect', '')}</li>"
                text += f"<li><b>备选动作:</b> {effect.get('alternative_action', '')}</li>"
                text += f"<li><b>置信度:</b> {effect.get('confidence', 0.0):.2f}</li>"
                text += "<br>"
            text += "</ul>"

        self.causal_text.setHtml(text)

    def clear_causal_analysis(self):
        """清除因果分析"""
        self.causal_text.setText("暂无因果分析数据")


class EnhancedTrustVisualizationWidget(QWidget):
    """增强信任可视化组件"""

    def __init__(self, parent=None):
        """初始化增强信任可视化组件"""
        super().__init__(parent)

        # 设置对象名称
        self.setObjectName("enhancedTrustVisualizationWidget")

        # 创建指标收集器
        self.metrics_collector = MetricsCollector()

        # 初始化UI
        self.setup_ui()

        logger.info("初始化增强信任可视化组件")

    def setup_ui(self):
        """设置UI"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # 创建标题标签
        title_label = QLabel("AI信任可视化")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        main_layout.addWidget(title_label)

        # 创建选项卡组件
        tab_widget = QTabWidget()
        main_layout.addWidget(tab_widget)

        # 创建基本信息选项卡
        basic_tab = QWidget()
        basic_layout = QVBoxLayout(basic_tab)

        # 创建顶部区域（复杂度评分和组件调用统计）
        top_layout = QHBoxLayout()
        top_layout.setSpacing(10)

        # 创建复杂度评分组件
        self.complexity_score_widget = ComplexityScoreWidget()
        top_layout.addWidget(self.complexity_score_widget)

        # 创建组件调用统计组件
        self.component_usage_widget = ComponentUsageWidget()
        top_layout.addWidget(self.component_usage_widget)

        basic_layout.addLayout(top_layout)

        # 创建价值变化组件
        self.value_change_widget = ValueChangeWidget()
        basic_layout.addWidget(self.value_change_widget)

        # 添加基本信息选项卡
        tab_widget.addTab(basic_tab, "基本信息")

        # 创建因果分析选项卡
        causal_tab = QWidget()
        causal_layout = QVBoxLayout(causal_tab)

        # 创建因果分析组件
        self.causal_analysis_widget = CausalAnalysisWidget()
        causal_layout.addWidget(self.causal_analysis_widget)

        # 添加因果分析选项卡
        tab_widget.addTab(causal_tab, "因果分析")

        # 创建搜索树选项卡
        search_tab = QWidget()
        search_layout = QVBoxLayout(search_tab)

        # 创建搜索树文本框
        self.search_tree_text = QTextEdit()
        self.search_tree_text.setReadOnly(True)
        search_layout.addWidget(self.search_tree_text)

        # 添加搜索树选项卡
        tab_widget.addTab(search_tab, "搜索树")

    def update_visualization(self, explanation_data: Dict[str, Any]):
        """
        更新可视化

        Args:
            explanation_data: 解释数据
        """
        # 收集指标
        metrics_data = self.metrics_collector.collect_metrics(explanation_data)

        # 更新复杂度评分
        complexity_score = metrics_data.get("complexity_score", 0.5)
        self.complexity_score_widget.set_complexity_score(complexity_score)

        # 更新组件调用统计
        component_calls = metrics_data.get("component_calls", {})
        self.component_usage_widget.update_component_usage(component_calls)

        # 更新价值变化
        value_history = metrics_data.get("value_history", [])
        self.value_change_widget.update_value_history(value_history)

        # 更新因果分析
        if "causal_analysis" in explanation_data:
            self.causal_analysis_widget.update_causal_analysis(explanation_data["causal_analysis"])

        # 更新搜索树信息
        if "mcts_data" in explanation_data:
            self.update_search_tree_info(explanation_data["mcts_data"])

        logger.debug("更新增强信任可视化")

    def update_search_tree_info(self, mcts_data: Dict[str, Any]):
        """
        更新搜索树信息

        Args:
            mcts_data: MCTS数据
        """
        if not mcts_data:
            self.search_tree_text.setText("暂无搜索树数据")
            return

        # 生成搜索树信息文本
        text = f"<h3>MCTS搜索树信息</h3>"

        # 添加根节点信息
        if "root_info" in mcts_data:
            root_info = mcts_data["root_info"]
            text += f"<p><b>根节点:</b> 访问次数 {root_info.get('visit_count', 0)}, "
            text += f"价值 {root_info.get('value', 0.0):.3f}</p>"

            # 添加信念状态信息（如果有）
            if "belief_confidence" in root_info:
                text += f"<p><b>信念置信度:</b> {root_info.get('belief_confidence', 0.5):.3f}</p>"

            if "belief_entropy" in root_info:
                text += f"<p><b>信念熵:</b> {root_info.get('belief_entropy', 0.0):.3f}</p>"

            if "info_value" in root_info:
                text += f"<p><b>信息价值:</b> {root_info.get('info_value', 0.0):.3f}</p>"

        # 添加模拟次数信息
        text += f"<p><b>总模拟次数:</b> {mcts_data.get('total_simulations', 0)}</p>"
        text += f"<p><b>实际模拟次数:</b> {mcts_data.get('actual_simulations', 0)}</p>"

        # 添加ACT信息
        if "act_info" in mcts_data:
            act_info = mcts_data["act_info"]
            text += f"<h4>自适应计算时间 (ACT)</h4>"
            text += f"<p><b>启用状态:</b> {'是' if act_info.get('enabled', False) else '否'}</p>"

            if act_info.get("enabled", False):
                text += f"<p><b>置信度:</b> {act_info.get('confidence', 0.0):.3f}</p>"
                text += f"<p><b>置信度阈值:</b> {act_info.get('confidence_threshold', 0.0):.3f}</p>"
                text += f"<p><b>提前停止:</b> {'是' if act_info.get('early_stopped', False) else '否'}</p>"

        # 添加主要变化路径
        if "principal_variation" in mcts_data and mcts_data["principal_variation"]:
            text += f"<h4>主要变化路径</h4>"
            text += "<ol>"

            for i, step in enumerate(mcts_data["principal_variation"]):
                text += f"<li><b>动作:</b> {step.get('action', '未知')}</li>"

                if "node_info" in step:
                    node_info = step["node_info"]
                    text += f"<ul>"
                    text += f"<li>访问次数: {node_info.get('visit_count', 0)}</li>"
                    text += f"<li>价值: {node_info.get('value', 0.0):.3f}</li>"
                    text += f"</ul>"

            text += "</ol>"

        # 添加顶级动作
        if "top_actions" in mcts_data and mcts_data["top_actions"]:
            text += f"<h4>顶级动作</h4>"
            text += "<ol>"

            for action_data in mcts_data["top_actions"]:
                text += f"<li><b>动作:</b> {action_data.get('action', '未知')}</li>"
                text += f"<ul>"
                text += f"<li>访问次数: {action_data.get('visit_count', 0)}</li>"
                text += f"<li>价值: {action_data.get('value', 0.0):.3f}</li>"
                text += f"<li>概率: {action_data.get('policy_prob', 0.0) * 100:.1f}%</li>"
                text += f"</ul>"

            text += "</ol>"

        self.search_tree_text.setHtml(text)

    def clear_visualization(self):
        """清除可视化"""
        # 清除指标收集器历史记录
        self.metrics_collector.clear_history()

        # 重置复杂度评分
        self.complexity_score_widget.set_complexity_score(0.0)

        # 重置组件调用统计
        self.component_usage_widget.update_component_usage({
            "mcts": 0,
            "network": 0,
            "rule": 0,
            "hybrid": 0
        })

        # 重置价值变化
        self.value_change_widget.update_value_history([])

        # 重置因果分析
        self.causal_analysis_widget.clear_causal_analysis()

        # 重置搜索树信息
        self.search_tree_text.setText("暂无搜索树数据")

        logger.info("清除增强信任可视化")
