"""
MCTS性能监控模块

提供详细的性能监控功能，包括：
- 搜索时间统计
- 内存使用监控
- UCB计算效率
- 节点扩展速度
- 实时性能指标
"""

import time
import threading
from typing import Dict, List, Any, Optional
from collections import defaultdict, deque
from dataclasses import dataclass, field

from .utils import get_memory_usage, format_memory_size, ThreadSafeCounter


@dataclass
class PerformanceMetrics:
    """
    性能指标数据类
    
    存储各种性能统计数据。
    """
    
    # 搜索统计
    total_search_time: float = 0.0
    total_simulations: int = 0
    average_simulation_time: float = 0.0
    
    # UCB计算统计
    total_ucb_calculations: int = 0
    total_ucb_time: float = 0.0
    average_ucb_time: float = 0.0
    
    # 节点扩展统计
    total_expansions: int = 0
    total_expansion_time: float = 0.0
    average_expansion_time: float = 0.0
    
    # 内存统计
    peak_memory_usage: int = 0
    current_memory_usage: int = 0
    memory_usage_history: List[int] = field(default_factory=list)
    
    # 搜索树统计
    max_tree_depth: int = 0
    total_nodes_created: int = 0
    total_nodes_visited: int = 0
    
    # 时间分布统计
    search_time_distribution: Dict[str, int] = field(default_factory=lambda: defaultdict(int))
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'total_search_time': self.total_search_time,
            'total_simulations': self.total_simulations,
            'average_simulation_time': self.average_simulation_time,
            'total_ucb_calculations': self.total_ucb_calculations,
            'total_ucb_time': self.total_ucb_time,
            'average_ucb_time': self.average_ucb_time,
            'total_expansions': self.total_expansions,
            'total_expansion_time': self.total_expansion_time,
            'average_expansion_time': self.average_expansion_time,
            'peak_memory_usage': self.peak_memory_usage,
            'peak_memory_formatted': format_memory_size(self.peak_memory_usage),
            'current_memory_usage': self.current_memory_usage,
            'current_memory_formatted': format_memory_size(self.current_memory_usage),
            'max_tree_depth': self.max_tree_depth,
            'total_nodes_created': self.total_nodes_created,
            'total_nodes_visited': self.total_nodes_visited,
            'search_time_distribution': dict(self.search_time_distribution),
            'memory_usage_samples': len(self.memory_usage_history)
        }


class PerformanceMonitor:
    """
    MCTS性能监控器
    
    实时监控MCTS搜索过程的各种性能指标，提供详细的统计信息。
    """
    
    def __init__(self, 
                 enable_memory_monitoring: bool = True,
                 memory_sample_interval: float = 0.1,
                 max_history_size: int = 1000):
        """
        初始化性能监控器
        
        Args:
            enable_memory_monitoring: 是否启用内存监控
            memory_sample_interval: 内存采样间隔（秒）
            max_history_size: 最大历史记录数量
        """
        self.enable_memory_monitoring = enable_memory_monitoring
        self.memory_sample_interval = memory_sample_interval
        self.max_history_size = max_history_size
        
        # 性能指标
        self.metrics = PerformanceMetrics()
        
        # 线程安全计数器
        self.simulation_counter = ThreadSafeCounter()
        self.ucb_counter = ThreadSafeCounter()
        self.expansion_counter = ThreadSafeCounter()
        self.node_counter = ThreadSafeCounter()
        
        # 时间记录
        self.search_start_times = {}  # 搜索开始时间
        self.ucb_start_times = {}     # UCB计算开始时间
        self.expansion_start_times = {}  # 扩展开始时间
        
        # 内存监控
        self.memory_monitoring_active = False
        self.memory_monitor_thread = None
        self.memory_history = deque(maxlen=max_history_size)
        
        # 线程锁
        self.lock = threading.Lock()
    
    def start_search_timing(self, search_id: str) -> None:
        """
        开始搜索计时
        
        Args:
            search_id: 搜索标识符
        """
        with self.lock:
            self.search_start_times[search_id] = time.perf_counter()
        
        # 启动内存监控
        if self.enable_memory_monitoring and not self.memory_monitoring_active:
            self._start_memory_monitoring()
    
    def end_search_timing(self, search_id: str, num_simulations: int) -> float:
        """
        结束搜索计时
        
        Args:
            search_id: 搜索标识符
            num_simulations: 模拟次数
            
        Returns:
            float: 搜索耗时（秒）
        """
        end_time = time.perf_counter()
        
        with self.lock:
            start_time = self.search_start_times.pop(search_id, end_time)
            search_time = end_time - start_time
            
            # 更新统计
            self.metrics.total_search_time += search_time
            self.metrics.total_simulations += num_simulations
            
            if self.metrics.total_simulations > 0:
                self.metrics.average_simulation_time = (
                    self.metrics.total_search_time / self.metrics.total_simulations
                )
            
            # 更新时间分布
            time_bucket = self._get_time_bucket(search_time)
            self.metrics.search_time_distribution[time_bucket] += 1
        
        # 停止内存监控
        if self.enable_memory_monitoring and not self.search_start_times:
            self._stop_memory_monitoring()
        
        return search_time
    
    def start_ucb_timing(self, ucb_id: str) -> None:
        """
        开始UCB计算计时
        
        Args:
            ucb_id: UCB计算标识符
        """
        with self.lock:
            self.ucb_start_times[ucb_id] = time.perf_counter()
    
    def end_ucb_timing(self, ucb_id: str) -> float:
        """
        结束UCB计算计时
        
        Args:
            ucb_id: UCB计算标识符
            
        Returns:
            float: UCB计算耗时（秒）
        """
        end_time = time.perf_counter()
        
        with self.lock:
            start_time = self.ucb_start_times.pop(ucb_id, end_time)
            ucb_time = end_time - start_time
            
            # 更新统计
            self.metrics.total_ucb_calculations += 1
            self.metrics.total_ucb_time += ucb_time
            self.metrics.average_ucb_time = (
                self.metrics.total_ucb_time / self.metrics.total_ucb_calculations
            )
        
        return ucb_time
    
    def start_expansion_timing(self, expansion_id: str) -> None:
        """
        开始节点扩展计时
        
        Args:
            expansion_id: 扩展标识符
        """
        with self.lock:
            self.expansion_start_times[expansion_id] = time.perf_counter()
    
    def end_expansion_timing(self, expansion_id: str, num_children: int) -> float:
        """
        结束节点扩展计时
        
        Args:
            expansion_id: 扩展标识符
            num_children: 新增子节点数量
            
        Returns:
            float: 扩展耗时（秒）
        """
        end_time = time.perf_counter()
        
        with self.lock:
            start_time = self.expansion_start_times.pop(expansion_id, end_time)
            expansion_time = end_time - start_time
            
            # 更新统计
            self.metrics.total_expansions += 1
            self.metrics.total_expansion_time += expansion_time
            self.metrics.average_expansion_time = (
                self.metrics.total_expansion_time / self.metrics.total_expansions
            )
            
            # 更新节点统计
            self.metrics.total_nodes_created += num_children
        
        return expansion_time
    
    def record_tree_depth(self, depth: int) -> None:
        """
        记录搜索树深度
        
        Args:
            depth: 当前深度
        """
        with self.lock:
            self.metrics.max_tree_depth = max(self.metrics.max_tree_depth, depth)
    
    def record_node_visit(self) -> None:
        """记录节点访问"""
        self.metrics.total_nodes_visited += 1
    
    def get_current_metrics(self) -> PerformanceMetrics:
        """
        获取当前性能指标
        
        Returns:
            PerformanceMetrics: 当前性能指标
        """
        with self.lock:
            # 更新当前内存使用
            memory_info = get_memory_usage()
            self.metrics.current_memory_usage = memory_info.get('rss', 0)
            
            return self.metrics
    
    def reset_metrics(self) -> None:
        """重置所有性能指标"""
        with self.lock:
            self.metrics = PerformanceMetrics()
            self.simulation_counter.reset()
            self.ucb_counter.reset()
            self.expansion_counter.reset()
            self.node_counter.reset()
            self.memory_history.clear()
    
    def _get_time_bucket(self, time_seconds: float) -> str:
        """
        获取时间分布桶
        
        Args:
            time_seconds: 时间（秒）
            
        Returns:
            str: 时间桶标识
        """
        if time_seconds < 0.001:
            return "<1ms"
        elif time_seconds < 0.01:
            return "1-10ms"
        elif time_seconds < 0.1:
            return "10-100ms"
        elif time_seconds < 1.0:
            return "100ms-1s"
        elif time_seconds < 10.0:
            return "1-10s"
        else:
            return ">10s"
    
    def _start_memory_monitoring(self) -> None:
        """启动内存监控线程"""
        if self.memory_monitoring_active:
            return
        
        self.memory_monitoring_active = True
        self.memory_monitor_thread = threading.Thread(
            target=self._memory_monitor_loop,
            daemon=True
        )
        self.memory_monitor_thread.start()
    
    def _stop_memory_monitoring(self) -> None:
        """停止内存监控线程"""
        self.memory_monitoring_active = False
        if self.memory_monitor_thread:
            self.memory_monitor_thread.join(timeout=1.0)
    
    def _memory_monitor_loop(self) -> None:
        """内存监控循环"""
        while self.memory_monitoring_active:
            try:
                memory_info = get_memory_usage()
                current_memory = memory_info.get('rss', 0)
                
                with self.lock:
                    # 更新峰值内存
                    self.metrics.peak_memory_usage = max(
                        self.metrics.peak_memory_usage, 
                        current_memory
                    )
                    
                    # 记录内存历史
                    self.memory_history.append(current_memory)
                    self.metrics.memory_usage_history = list(self.memory_history)
                
                time.sleep(self.memory_sample_interval)
                
            except Exception:
                # 忽略内存监控错误
                time.sleep(self.memory_sample_interval)
    
    def generate_performance_report(self) -> Dict[str, Any]:
        """
        生成详细的性能报告
        
        Returns:
            Dict[str, Any]: 性能报告
        """
        metrics = self.get_current_metrics()
        
        return {
            'summary': {
                'total_search_time': f"{metrics.total_search_time:.3f}s",
                'total_simulations': metrics.total_simulations,
                'average_simulation_time': f"{metrics.average_simulation_time*1000:.2f}ms",
                'peak_memory_usage': format_memory_size(metrics.peak_memory_usage),
                'max_tree_depth': metrics.max_tree_depth
            },
            'detailed_metrics': metrics.to_dict(),
            'efficiency_ratios': {
                'simulations_per_second': (
                    metrics.total_simulations / metrics.total_search_time 
                    if metrics.total_search_time > 0 else 0
                ),
                'ucb_calculations_per_simulation': (
                    metrics.total_ucb_calculations / metrics.total_simulations
                    if metrics.total_simulations > 0 else 0
                ),
                'expansions_per_simulation': (
                    metrics.total_expansions / metrics.total_simulations
                    if metrics.total_simulations > 0 else 0
                )
            }
        }
