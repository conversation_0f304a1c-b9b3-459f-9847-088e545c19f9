"""
扑克游戏插件

实现标准的扑克游戏规则，支持多种变体。

版本: 0.1.0
作者: Card Game AI 团队
网站: https://cardgame-ai.example.com
"""
from typing import Optional, List, Dict, Any, Tuple
import random
from enum import Enum

from cardgame_ai.games.common.game_config import GameConfig, ConfigurableGame
from cardgame_ai.games.common.game_expansion import GamePlugin


class PokerSuit(Enum):
    """扑克牌花色"""
    CLUBS = 0
    DIAMONDS = 1
    HEARTS = 2
    SPADES = 3
    
    def __str__(self) -> str:
        """返回花色符号"""
        symbols = {
            PokerSuit.CLUBS: "♣",
            PokerSuit.DIAMONDS: "♦",
            PokerSuit.HEARTS: "♥",
            PokerSuit.SPADES: "♠"
        }
        return symbols[self]


class PokerRank(Enum):
    """扑克牌点数"""
    TWO = 2
    THREE = 3
    FOUR = 4
    FIVE = 5
    SIX = 6
    SEVEN = 7
    EIGHT = 8
    NINE = 9
    TEN = 10
    JACK = 11
    QUEEN = 12
    KING = 13
    ACE = 14
    
    def __str__(self) -> str:
        """返回点数符号"""
        symbols = {
            PokerRank.TWO: "2",
            PokerRank.THREE: "3",
            PokerRank.FOUR: "4",
            PokerRank.FIVE: "5",
            PokerRank.SIX: "6",
            PokerRank.SEVEN: "7",
            PokerRank.EIGHT: "8",
            PokerRank.NINE: "9",
            PokerRank.TEN: "10",
            PokerRank.JACK: "J",
            PokerRank.QUEEN: "Q",
            PokerRank.KING: "K",
            PokerRank.ACE: "A"
        }
        return symbols[self]


class PokerCard:
    """扑克牌"""
    
    def __init__(self, suit: PokerSuit, rank: PokerRank):
        """
        初始化扑克牌
        
        Args:
            suit (PokerSuit): 花色
            rank (PokerRank): 点数
        """
        self.suit = suit
        self.rank = rank
    
    def __str__(self) -> str:
        """返回扑克牌字符串表示"""
        return f"{self.rank}{self.suit}"
    
    def __eq__(self, other) -> bool:
        """比较两张扑克牌是否相等"""
        if not isinstance(other, PokerCard):
            return False
        return self.suit == other.suit and self.rank == other.rank
    
    def __lt__(self, other) -> bool:
        """比较两张扑克牌大小"""
        if not isinstance(other, PokerCard):
            return NotImplemented
        if self.rank == other.rank:
            return self.suit.value < other.suit.value
        return self.rank.value < other.rank.value


class PokerHand(Enum):
    """扑克牌型"""
    HIGH_CARD = 0
    PAIR = 1
    TWO_PAIR = 2
    THREE_OF_A_KIND = 3
    STRAIGHT = 4
    FLUSH = 5
    FULL_HOUSE = 6
    FOUR_OF_A_KIND = 7
    STRAIGHT_FLUSH = 8
    ROYAL_FLUSH = 9


class PokerPlayer:
    """扑克玩家"""
    
    def __init__(self, name: str, chips: int = 1000):
        """
        初始化扑克玩家
        
        Args:
            name (str): 玩家名称
            chips (int, optional): 筹码数量. Defaults to 1000.
        """
        self.name = name
        self.chips = chips
        self.hand: List[PokerCard] = []
        self.bet = 0
        self.folded = False
    
    def reset(self):
        """重置玩家状态"""
        self.hand = []
        self.bet = 0
        self.folded = False
    
    def add_card(self, card: PokerCard):
        """
        添加卡牌到手牌
        
        Args:
            card (PokerCard): 扑克牌
        """
        self.hand.append(card)
    
    def place_bet(self, amount: int) -> int:
        """
        下注
        
        Args:
            amount (int): 下注金额
            
        Returns:
            int: 实际下注金额
        """
        if amount > self.chips:
            amount = self.chips
        
        self.bet += amount
        self.chips -= amount
        
        return amount
    
    def fold(self):
        """弃牌"""
        self.folded = True
    
    def __str__(self) -> str:
        """返回玩家字符串表示"""
        hand_str = ", ".join(str(card) for card in self.hand)
        return f"{self.name} (筹码: {self.chips}, 手牌: {hand_str})"


class PokerGame(ConfigurableGame):
    """
    扑克游戏实现
    """
    
    def __init__(self, config: Optional[GameConfig] = None):
        """
        初始化游戏
        
        Args:
            config (Optional[GameConfig], optional): 游戏配置. Defaults to None.
        """
        self._config = config or PokerPlugin().get_default_config()
        self._players: List[PokerPlayer] = []
        self._deck: List[PokerCard] = []
        self._community_cards: List[PokerCard] = []
        self._pot = 0
        self._current_player_index = 0
        self._dealer_index = 0
        self._small_blind_index = 0
        self._big_blind_index = 0
        self._min_bet = self._config.get("min_bet", 10)
        self._max_players = self._config.get("max_players", 9)
        self._starting_chips = self._config.get("starting_chips", 1000)
    
    def get_config(self) -> GameConfig:
        """
        获取游戏配置
        
        Returns:
            GameConfig: 游戏配置
        """
        return self._config
    
    def apply_config(self, config: GameConfig) -> None:
        """
        应用游戏配置
        
        Args:
            config (GameConfig): 游戏配置
        """
        if self.validate_config(config):
            self._config = config
            # 更新相关配置
            self._min_bet = self._config.get("min_bet", 10)
            self._max_players = self._config.get("max_players", 9)
            self._starting_chips = self._config.get("starting_chips", 1000)
    
    def validate_config(self, config: GameConfig) -> bool:
        """
        验证游戏配置是否有效
        
        Args:
            config (GameConfig): 游戏配置
            
        Returns:
            bool: 如果配置有效则返回True
        """
        # 检查必要的配置项
        required_keys = ["min_bet", "max_players", "starting_chips"]
        for key in required_keys:
            if key not in config:
                return False
        
        # 验证配置值合法性
        if config.get("min_bet", 0) <= 0:
            return False
        if config.get("max_players", 0) <= 1:
            return False
        if config.get("starting_chips", 0) <= 0:
            return False
        
        return True
    
    def add_player(self, name: str) -> bool:
        """
        添加玩家
        
        Args:
            name (str): 玩家名称
            
        Returns:
            bool: 添加是否成功
        """
        if len(self._players) >= self._max_players:
            return False
        
        self._players.append(PokerPlayer(name, self._starting_chips))
        return True
    
    def remove_player(self, name: str) -> bool:
        """
        移除玩家
        
        Args:
            name (str): 玩家名称
            
        Returns:
            bool: 移除是否成功
        """
        for i, player in enumerate(self._players):
            if player.name == name:
                self._players.pop(i)
                return True
        return False
    
    def reset_game(self):
        """重置游戏状态"""
        self._deck = []
        self._community_cards = []
        self._pot = 0
        
        for player in self._players:
            player.reset()
    
    def initialize_deck(self):
        """初始化牌组"""
        self._deck = [
            PokerCard(suit, rank)
            for suit in PokerSuit
            for rank in PokerRank
        ]
        random.shuffle(self._deck)
    
    def deal_cards(self):
        """发牌"""
        # 确保玩家数量足够
        if len(self._players) < 2:
            raise ValueError("至少需要2名玩家才能开始游戏")
        
        # 初始化牌组
        self.initialize_deck()
        
        # 初始化各位置索引
        player_count = len(self._players)
        self._dealer_index = (self._dealer_index + 1) % player_count
        self._small_blind_index = (self._dealer_index + 1) % player_count
        self._big_blind_index = (self._small_blind_index + 1) % player_count
        self._current_player_index = (self._big_blind_index + 1) % player_count
        
        # 收取盲注
        small_blind = self._min_bet // 2
        big_blind = self._min_bet
        
        self._pot += self._players[self._small_blind_index].place_bet(small_blind)
        self._pot += self._players[self._big_blind_index].place_bet(big_blind)
        
        # 发手牌
        for _ in range(2):
            for player in self._players:
                if len(self._deck) > 0:
                    player.add_card(self._deck.pop())
    
    def deal_community_cards(self, count: int = 1):
        """
        发公共牌
        
        Args:
            count (int, optional): 发牌数量. Defaults to 1.
        """
        for _ in range(count):
            if len(self._deck) > 0:
                self._community_cards.append(self._deck.pop())
    
    def evaluate_hand(self, player: PokerPlayer) -> Tuple[PokerHand, List[PokerCard]]:
        """
        评估玩家牌型
        
        Args:
            player (PokerPlayer): 玩家
            
        Returns:
            Tuple[PokerHand, List[PokerCard]]: 牌型和最佳牌组合
        """
        # 所有可用的牌
        all_cards = player.hand + self._community_cards
        all_cards.sort(reverse=True)
        
        # 检查各种牌型
        # (为简化示例，这里只实现了部分牌型检测)
        
        # 检查对子
        ranks = [card.rank for card in all_cards]
        pairs = []
        
        for rank in PokerRank:
            if ranks.count(rank) == 2:
                pairs.append(rank)
        
        if len(pairs) == 1:
            # 找出对子牌
            pair_cards = [card for card in all_cards if card.rank == pairs[0]]
            # 加上最高的三张牌
            kickers = [card for card in all_cards if card.rank != pairs[0]][:3]
            return PokerHand.PAIR, pair_cards + kickers
        
        # 如果没有任何组合，返回高牌
        return PokerHand.HIGH_CARD, all_cards[:5]
    
    def get_winner(self) -> List[PokerPlayer]:
        """
        获取获胜玩家
        
        Returns:
            List[PokerPlayer]: 获胜玩家列表
        """
        active_players = [p for p in self._players if not p.folded]
        if len(active_players) == 1:
            return active_players
        
        # 评估每个玩家的牌型
        evaluations = []
        for player in active_players:
            hand_type, best_cards = self.evaluate_hand(player)
            evaluations.append((player, hand_type, best_cards))
        
        # 根据牌型排序
        evaluations.sort(key=lambda x: (x[1].value, x[2]), reverse=True)
        
        # 找出最高牌型的玩家
        best_hand = evaluations[0][1]
        winners = [eval_tuple[0] for eval_tuple in evaluations if eval_tuple[1] == best_hand]
        
        return winners
    
    def play_round(self):
        """
        进行一轮游戏
        
        Returns:
            List[PokerPlayer]: 获胜玩家列表
        """
        # 重置游戏状态
        self.reset_game()
        
        # 发牌
        self.deal_cards()
        
        # 下注轮（简化版，实际游戏中需要更复杂的逻辑）
        self._betting_round()
        
        # 翻牌 (3张公共牌)
        self.deal_community_cards(3)
        self._betting_round()
        
        # 转牌 (1张公共牌)
        self.deal_community_cards(1)
        self._betting_round()
        
        # 河牌 (1张公共牌)
        self.deal_community_cards(1)
        self._betting_round()
        
        # 确定获胜者
        winners = self.get_winner()
        
        # 分发奖池 (平分)
        if winners:
            winnings_per_player = self._pot // len(winners)
            for winner in winners:
                winner.chips += winnings_per_player
        
        return winners
    
    def _betting_round(self):
        """进行一轮下注"""
        # 这里提供一个简化版的下注轮逻辑
        # 实际游戏中需要处理加注、跟注、弃牌等复杂逻辑
        
        # 在这个简化版中，所有玩家根据简单规则进行操作
        for player in self._players:
            if player.folded:
                continue
            
            # 简单的AI决策 (随机操作)
            if random.random() < 0.2:  # 20%概率弃牌
                player.fold()
            else:
                bet_amount = random.randint(self._min_bet, min(self._min_bet * 3, player.chips))
                self._pot += player.place_bet(bet_amount)


class PokerPlugin(GamePlugin):
    """
    扑克游戏插件实现
    """
    
    @property
    def name(self) -> str:
        """
        获取游戏名称
        
        Returns:
            str: 游戏名称
        """
        return "德州扑克"
    
    @property
    def version(self) -> str:
        """
        获取游戏版本
        
        Returns:
            str: 游戏版本
        """
        return "0.1.0"
    
    @property
    def description(self) -> str:
        """
        获取游戏描述
        
        Returns:
            str: 游戏描述
        """
        return "标准的德州扑克游戏，支持多人对战"
    
    def create_game(self, config: Optional[GameConfig] = None) -> ConfigurableGame:
        """
        创建游戏实例
        
        Args:
            config (Optional[GameConfig], optional): 游戏配置. Defaults to None.
            
        Returns:
            ConfigurableGame: 游戏实例
        """
        return PokerGame(config or self.get_default_config())
    
    def get_default_config(self) -> GameConfig:
        """
        获取默认配置
        
        Returns:
            GameConfig: 默认游戏配置
        """
        # 创建默认配置
        config = GameConfig("德州扑克")
        config.set("min_bet", 10)
        config.set("max_players", 9)
        config.set("starting_chips", 1000)
        config.set("small_blind", 5)
        config.set("big_blind", 10)
        return config 