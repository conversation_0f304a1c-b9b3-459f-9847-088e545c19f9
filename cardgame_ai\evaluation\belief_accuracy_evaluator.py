"""
信念准确性评估模块

实现信念追踪器准确性的评估方法，用于衡量信念追踪器的预测准确性。
"""
import os
import time
import logging
import numpy as np
from typing import Dict, List, Tuple, Any, Optional, Union

from cardgame_ai.core.base import State
from cardgame_ai.core.environment import Environment
from cardgame_ai.core.agent import Agent
from cardgame_ai.core.evaluator import BaseEvaluator
from cardgame_ai.algorithms.belief_tracking.deep_belief import DeepBeliefTracker
from cardgame_ai.games.common.belief_state import BeliefState
from cardgame_ai.algorithms.hybrid_decision_system import HybridDecisionSystem


class BeliefAccuracyEvaluator(BaseEvaluator):
    """
    信念准确性评估器
    
    评估信念追踪器的预测准确性，并计算相关指标。
    """
    
    def __init__(self, save_path: str = 'results/belief_accuracy'):
        """
        初始化信念准确性评估器
        
        Args:
            save_path: 结果保存路径
        """
        super().__init__(save_path)
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def evaluate(self, env: Environment, agents: Union[Agent, List[Agent]], num_games: int, 
                 ground_truth_available: bool = True, **kwargs) -> Dict[str, Any]:
        """
        评估信念追踪器的准确性
        
        Args:
            env: 游戏环境
            agents: 要评估的代理或代理列表
            num_games: 评估的游戏数
            ground_truth_available: 是否有真实手牌信息可用
            **kwargs: 其他参数
            
        Returns:
            评估结果，包含信念准确性指标
        """
        # 首先进行基础评估
        result = super().evaluate(env, agents, num_games, **kwargs)
        
        # 如果没有信念追踪器，直接返回基础评估结果
        belief_trackers = self._get_belief_trackers(agents)
        if not belief_trackers:
            self.logger.info("未找到信念追踪器，跳过信念准确性评估")
            return result
        
        # 信念准确性指标
        belief_metrics = {
            "belief_accuracy": [],
            "belief_confidence": [],
            "belief_usage_count": 0,
            "belief_impact_on_win_rate": 0.0
        }
        
        # 运行评估游戏
        for game in range(num_games):
            # 重置环境
            state = env.reset()
            done = False
            
            # 记录每局游戏的信念准确性
            game_belief_accuracy = []
            
            # 运行一局游戏
            while not done:
                player_id = state.get_player_id()
                agent = agents[player_id] if isinstance(agents, list) else agents
                
                # 获取当前玩家的信念追踪器
                belief_tracker = belief_trackers.get(player_id)
                
                # 如果当前玩家有信念追踪器
                if belief_tracker:
                    # 获取信念状态
                    belief_state = belief_tracker.get_belief_state()
                    
                    # 如果有真实手牌信息可用，计算准确性
                    if ground_truth_available and hasattr(state, 'hands'):
                        # 获取其他玩家的真实手牌
                        other_player_hands = {}
                        for pid, hand in enumerate(state.hands):
                            if pid != player_id:
                                other_player_hands[str(pid)] = hand
                        
                        # 计算信念准确性
                        accuracy = self._calculate_belief_accuracy(belief_state, other_player_hands)
                        game_belief_accuracy.append(accuracy)
                        
                        # 记录置信度
                        belief_metrics["belief_confidence"].append(belief_state.confidence)
                    
                    # 记录信念使用次数
                    belief_metrics["belief_usage_count"] += 1
                
                # 获取合法动作
                legal_actions = env.get_legal_actions(state)
                
                # 获取当前玩家的动作
                action = agent.act(state, legal_actions, is_training=False)
                
                # 执行动作
                next_state, reward, done, info = env.step(action)
                
                # 更新状态
                state = next_state
            
            # 计算本局游戏的平均信念准确性
            if game_belief_accuracy:
                avg_game_accuracy = np.mean(game_belief_accuracy)
                belief_metrics["belief_accuracy"].append(avg_game_accuracy)
        
        # 计算平均信念准确性
        if belief_metrics["belief_accuracy"]:
            result["mean_belief_accuracy"] = np.mean(belief_metrics["belief_accuracy"])
            result["std_belief_accuracy"] = np.std(belief_metrics["belief_accuracy"])
        else:
            result["mean_belief_accuracy"] = 0.0
            result["std_belief_accuracy"] = 0.0
        
        # 计算平均信念置信度
        if belief_metrics["belief_confidence"]:
            result["mean_belief_confidence"] = np.mean(belief_metrics["belief_confidence"])
        else:
            result["mean_belief_confidence"] = 0.0
        
        # 记录信念使用次数
        result["belief_usage_count"] = belief_metrics["belief_usage_count"]
        
        # 计算信念对胜率的影响（如果有多个代理）
        if isinstance(agents, list) and len(agents) > 1:
            # 假设第一个代理使用了信念追踪器
            if 0 in belief_trackers:
                result["belief_impact_on_win_rate"] = result.get("player_0_win_rate", 0.0)
        
        self.logger.info(f"信念准确性评估完成，平均准确性: {result['mean_belief_accuracy']:.4f}")
        
        return result
    
    def _get_belief_trackers(self, agents: Union[Agent, List[Agent]]) -> Dict[int, DeepBeliefTracker]:
        """
        从代理中获取信念追踪器
        
        Args:
            agents: 代理或代理列表
            
        Returns:
            玩家ID到信念追踪器的映射
        """
        belief_trackers = {}
        
        # 如果是单个代理
        if not isinstance(agents, list):
            # 检查是否是HybridDecisionSystem
            if isinstance(agents, HybridDecisionSystem) and agents.belief_tracker is not None:
                belief_trackers[0] = agents.belief_tracker
            return belief_trackers
        
        # 如果是代理列表
        for i, agent in enumerate(agents):
            # 检查是否是HybridDecisionSystem
            if isinstance(agent, HybridDecisionSystem) and agent.belief_tracker is not None:
                belief_trackers[i] = agent.belief_tracker
        
        return belief_trackers
    
    def _calculate_belief_accuracy(self, belief_state: BeliefState, ground_truth: Dict[str, List[str]]) -> float:
        """
        计算信念准确性
        
        Args:
            belief_state: 信念状态
            ground_truth: 真实手牌，玩家ID到手牌列表的映射
            
        Returns:
            准确性分数，范围[0, 1]
        """
        # 如果没有真实手牌信息，返回0
        if not ground_truth:
            return 0.0
        
        # 获取信念状态的玩家ID
        player_id = belief_state.player_id
        
        # 如果没有该玩家的真实手牌信息，返回0
        if player_id not in ground_truth:
            return 0.0
        
        # 获取真实手牌
        true_cards = set(ground_truth[player_id])
        
        # 获取信念状态中概率最高的牌
        card_probs = belief_state.card_probabilities
        sorted_cards = sorted(card_probs.items(), key=lambda x: x[1], reverse=True)
        
        # 取概率最高的N张牌，N等于真实手牌数量
        top_n_cards = set([card for card, _ in sorted_cards[:len(true_cards)]])
        
        # 计算准确性（交集大小除以真实手牌数量）
        intersection = len(true_cards.intersection(top_n_cards))
        accuracy = intersection / len(true_cards) if true_cards else 0.0
        
        return accuracy
