"""
斗地主AI系统接口定义模块

该模块定义了系统各组件之间的标准接口，用于解耦zhuchengxu模块与核心库的依赖关系。
通过接口抽象，实现模块间的松耦合，提高系统的可维护性和可测试性。

主要接口:
- TrainingInterface: 训练服务接口
- ConfigInterface: 配置管理接口  
- LoggingInterface: 日志服务接口
- MonitoringInterface: 监控服务接口
- DataInterface: 数据服务接口

设计原则:
- 接口隔离原则: 每个接口职责单一
- 依赖倒置原则: 高层模块不依赖低层模块
- 开闭原则: 对扩展开放，对修改关闭

作者: Architect Timmy + Full Stack Dev James
版本: v1.0
"""

from .training_interface import TrainingInterface, TrainingConfig, TrainingResult
from .config_interface import ConfigInterface, ConfigValidationResult
from .logging_interface import LoggingInterface, LogLevel
from .monitoring_interface import MonitoringInterface, MetricData
from .data_interface import DataInterface, DataLoadResult

__version__ = "1.0.0"
__author__ = "Architect Timmy + Full Stack Dev James"

__all__ = [
    # 训练接口
    "TrainingInterface",
    "TrainingConfig", 
    "TrainingResult",
    
    # 配置接口
    "ConfigInterface",
    "ConfigValidationResult",
    
    # 日志接口
    "LoggingInterface",
    "LogLevel",
    
    # 监控接口
    "MonitoringInterface",
    "MetricData",
    
    # 数据接口
    "DataInterface",
    "DataLoadResult"
]
