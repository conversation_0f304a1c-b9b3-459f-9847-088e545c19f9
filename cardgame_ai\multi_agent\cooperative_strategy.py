"""
协作策略优化模块

实现农民之间的协作策略优化，提高多智能体协作效果。包括联合策略优化、
角色感知批评家网络和协同探索机制等技术，使农民智能体能够更有效地协作。
"""
import os
import time
import logging
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from typing import Dict, List, Tuple, Any, Optional, Union
from collections import defaultdict, deque

from cardgame_ai.multi_agent.multi_agent_framework import FarmerCooperation, RoleManager
from cardgame_ai.multi_agent.implicit_communication import ImplicitCommunicationMechanism
from cardgame_ai.core.agent import Agent
from cardgame_ai.core.environment import Environment
from cardgame_ai.algorithms.mappo import MAPPO, MAPPONetwork


class JointPolicyOptimizer:
    """
    联合策略优化器

    实现农民智能体之间的联合策略优化，通过协调梯度更新和共享价值函数，
    使农民智能体能够学习到更好的协作策略。
    """

    def __init__(
        self,
        agents: Dict[str, Agent],
        role_manager: RoleManager,
        learning_rate: float = 0.0003,
        joint_update_freq: int = 5,
        gradient_clip: float = 0.5,
        joint_loss_weight: float = 0.7
    ):
        """
        初始化联合策略优化器

        Args:
            agents: 智能体字典，键为智能体ID
            role_manager: 角色管理器
            learning_rate: 学习率
            joint_update_freq: 联合更新频率
            gradient_clip: 梯度裁剪阈值
            joint_loss_weight: 联合损失权重
        """
        self.agents = agents
        self.role_manager = role_manager
        self.learning_rate = learning_rate
        self.joint_update_freq = joint_update_freq
        self.gradient_clip = gradient_clip
        self.joint_loss_weight = joint_loss_weight

        # 获取农民智能体
        self.farmer_agents = {}
        for agent_id, agent in agents.items():
            if role_manager.get_role(agent_id) == "farmer":
                self.farmer_agents[agent_id] = agent

        # 创建联合优化器
        self.joint_optimizer = self._create_joint_optimizer()

        # 训练统计
        self.stats = {
            "joint_updates": 0,
            "joint_loss": [],
            "individual_loss": []
        }

    def _create_joint_optimizer(self) -> Optional[torch.optim.Optimizer]:
        """
        创建联合优化器

        将所有农民智能体的策略网络参数合并到一个优化器中

        Returns:
            联合优化器
        """
        if not self.farmer_agents:
            return None

        # 收集所有农民智能体的策略网络参数
        parameters = []
        for agent in self.farmer_agents.values():
            if hasattr(agent.algorithm, 'network'):
                parameters.extend(agent.algorithm.network.parameters())

        # 创建联合优化器
        if parameters:
            return optim.Adam(parameters, lr=self.learning_rate)

        return None

    def optimize(
        self,
        experiences: Dict[str, List[Any]],
        global_step: int
    ) -> Dict[str, float]:
        """
        优化联合策略

        Args:
            experiences: 经验数据，键为智能体ID
            global_step: 全局步数

        Returns:
            训练统计
        """
        # 如果没有农民智能体或联合优化器，返回空统计
        if not self.farmer_agents or self.joint_optimizer is None:
            return {}

        # 检查是否需要进行联合更新
        if global_step % self.joint_update_freq != 0:
            return {}

        # 提取农民智能体的经验
        farmer_experiences = {
            agent_id: experiences[agent_id]
            for agent_id in self.farmer_agents.keys()
            if agent_id in experiences
        }

        # 如果没有足够的农民经验，返回空统计
        if len(farmer_experiences) < 2:
            return {}

        # 计算联合梯度
        joint_loss = self._compute_joint_loss(farmer_experiences)

        # 更新联合优化器
        self.joint_optimizer.zero_grad()
        joint_loss.backward()

        # 梯度裁剪
        for agent in self.farmer_agents.values():
            if hasattr(agent.algorithm, 'network'):
                nn.utils.clip_grad_norm_(
                    agent.algorithm.network.parameters(),
                    self.gradient_clip
                )

        # 应用梯度
        self.joint_optimizer.step()

        # 更新统计
        self.stats["joint_updates"] += 1
        self.stats["joint_loss"].append(joint_loss.item())

        return {
            "joint_loss": joint_loss.item(),
            "joint_updates": self.stats["joint_updates"]
        }

    def _compute_joint_loss(self, experiences: Dict[str, List[Any]]) -> torch.Tensor:
        """
        计算联合损失

        Args:
            experiences: 经验数据，键为智能体ID

        Returns:
            联合损失
        """
        # 初始化联合损失
        joint_loss = torch.tensor(0.0, requires_grad=True)

        # 计算每个农民智能体的个体损失
        individual_losses = {}
        for agent_id, agent_experiences in experiences.items():
            agent = self.farmer_agents[agent_id]

            # 如果智能体使用MAPPO算法
            if isinstance(agent.algorithm, MAPPO):
                # 提取经验数据
                states, actions, rewards, next_states, dones, log_probs, values = zip(*agent_experiences)

                # 转换为张量
                states_tensor = torch.FloatTensor(np.array(states))
                actions_tensor = torch.LongTensor(np.array(actions))
                rewards_tensor = torch.FloatTensor(np.array(rewards))
                next_states_tensor = torch.FloatTensor(np.array(next_states))
                dones_tensor = torch.FloatTensor(np.array(dones))
                old_log_probs_tensor = torch.FloatTensor(np.array(log_probs))
                old_values_tensor = torch.FloatTensor(np.array(values))

                # 计算优势和回报
                advantages, returns = agent.algorithm._compute_advantages_and_returns(
                    rewards_tensor, old_values_tensor, dones_tensor
                )

                # 创建批次
                batch = {
                    'states': states_tensor,
                    'actions': actions_tensor,
                    'old_log_probs': old_log_probs_tensor,
                    'advantages': advantages,
                    'returns': returns,
                    'old_values': old_values_tensor
                }

                # 计算个体损失
                policy_loss, value_loss, entropy = agent.algorithm._compute_losses(batch)
                individual_loss = policy_loss + agent.algorithm.value_coef * value_loss - agent.algorithm.entropy_coef * entropy
                individual_losses[agent_id] = individual_loss

        # 如果有多个农民智能体，计算联合损失
        if len(individual_losses) >= 2:
            # 计算个体损失的平均值
            avg_individual_loss = torch.mean(torch.stack(list(individual_losses.values())))

            # 计算协作损失（农民之间的策略差异）
            cooperation_loss = torch.tensor(0.0, requires_grad=True)
            farmer_ids = list(individual_losses.keys())
            for i in range(len(farmer_ids)):
                for j in range(i+1, len(farmer_ids)):
                    # 计算两个农民策略之间的KL散度
                    agent_i = self.farmer_agents[farmer_ids[i]]
                    agent_j = self.farmer_agents[farmer_ids[j]]

                    if (isinstance(agent_i.algorithm, MAPPO) and
                        isinstance(agent_j.algorithm, MAPPO) and
                        hasattr(agent_i.algorithm, 'network') and
                        hasattr(agent_j.algorithm, 'network')):
                        # 获取共享状态
                        shared_states = experiences[farmer_ids[i]][0]  # 使用第一个农民的状态
                        shared_states_tensor = torch.FloatTensor(np.array(shared_states))

                        # 计算两个农民的策略
                        with torch.no_grad():
                            policy_i, _ = agent_i.algorithm.network(shared_states_tensor)
                            policy_j, _ = agent_j.algorithm.network(shared_states_tensor)

                            # 转换为概率分布
                            probs_i = F.softmax(policy_i, dim=-1)
                            probs_j = F.softmax(policy_j, dim=-1)

                            # 计算KL散度
                            kl_div = F.kl_div(
                                torch.log(probs_i + 1e-10),
                                probs_j,
                                reduction='batchmean'
                            )

                            # 添加到协作损失
                            cooperation_loss = cooperation_loss + kl_div

            # 如果有协作损失，归一化
            if cooperation_loss.item() > 0:
                cooperation_loss = cooperation_loss / (len(farmer_ids) * (len(farmer_ids) - 1) / 2)

                # 计算联合损失（个体损失 + 协作损失）
                joint_loss = (1 - self.joint_loss_weight) * avg_individual_loss + self.joint_loss_weight * cooperation_loss
            else:
                # 如果没有协作损失，使用个体损失
                joint_loss = avg_individual_loss
        else:
            # 如果只有一个农民智能体，使用个体损失
            joint_loss = list(individual_losses.values())[0]

        return joint_loss


class RoleAwareCritic(nn.Module):
    """
    角色感知批评家网络

    实现角色感知的批评家网络，为不同角色提供专门的价值评估，
    并且能够考虑角色间的交互和协作关系。
    """

    def __init__(
        self,
        state_dim: int,
        hidden_dims: List[int] = [256, 128],
        num_roles: int = 3,
        use_role_embedding: bool = True,
        role_embedding_dim: int = 16,
        use_attention: bool = True,
        num_heads: int = 4
    ):
        """
        初始化角色感知批评家网络

        Args:
            state_dim: 状态维度
            hidden_dims: 隐藏层维度
            num_roles: 角色数量
            use_role_embedding: 是否使用角色嵌入
            role_embedding_dim: 角色嵌入维度
            use_attention: 是否使用注意力机制
            num_heads: 注意力头数
        """
        super(RoleAwareCritic, self).__init__()

        self.state_dim = state_dim
        self.hidden_dims = hidden_dims
        self.num_roles = num_roles
        self.use_role_embedding = use_role_embedding
        self.role_embedding_dim = role_embedding_dim
        self.use_attention = use_attention
        self.num_heads = num_heads

        # 计算输入维度
        input_dim = state_dim
        if use_role_embedding:
            input_dim += role_embedding_dim

        # 角色嵌入
        if use_role_embedding:
            self.role_embedding = nn.Embedding(num_roles, role_embedding_dim)

        # 特征提取器
        self.feature_extractor = self._build_feature_extractor(input_dim, hidden_dims)

        # 注意力机制
        if use_attention:
            self.attention = nn.MultiheadAttention(
                embed_dim=hidden_dims[-1],
                num_heads=num_heads,
                batch_first=True
            )

        # 角色特定的价值头
        self.role_value_heads = nn.ModuleList([
            nn.Linear(hidden_dims[-1], 1) for _ in range(num_roles)
        ])

        # 全局价值头
        self.global_value_head = nn.Linear(hidden_dims[-1], 1)

        # 协作价值头（专门评估农民协作）
        self.cooperation_value_head = nn.Linear(hidden_dims[-1], 1)

    def _build_feature_extractor(self, input_dim: int, hidden_dims: List[int]) -> nn.Sequential:
        """
        构建特征提取器

        Args:
            input_dim: 输入维度
            hidden_dims: 隐藏层维度

        Returns:
            特征提取器
        """
        layers = []
        prev_dim = input_dim

        for dim in hidden_dims:
            layers.append(nn.Linear(prev_dim, dim))
            layers.append(nn.ReLU())
            prev_dim = dim

        return nn.Sequential(*layers)

    def forward(
        self,
        state: torch.Tensor,
        role_ids: torch.Tensor,
        team_states: Optional[torch.Tensor] = None
    ) -> Tuple[torch.Tensor, Dict[str, torch.Tensor]]:
        """
        前向传播

        Args:
            state: 状态张量 [batch_size, state_dim]
            role_ids: 角色ID张量 [batch_size]
            team_states: 团队状态张量 [batch_size, num_agents, state_dim]，可选

        Returns:
            全局价值和各个头的价值字典
        """
        # 如果使用角色嵌入，将角色嵌入与状态连接
        if self.use_role_embedding:
            role_embeddings = self.role_embedding(role_ids)
            state = torch.cat([state, role_embeddings], dim=1)

        # 提取特征
        features = self.feature_extractor(state)

        # 如果使用注意力机制并提供了团队状态
        if self.use_attention and team_states is not None:
            # 将团队状态转换为特征
            team_features = []
            for i in range(team_states.shape[1]):
                agent_state = team_states[:, i, :]
                if self.use_role_embedding:
                    # 假设团队中的智能体角色是固定的，使用固定的角色ID
                    agent_role_id = torch.ones_like(role_ids) * i
                    agent_embedding = self.role_embedding(agent_role_id)
                    agent_state = torch.cat([agent_state, agent_embedding], dim=1)
                agent_features = self.feature_extractor(agent_state)
                team_features.append(agent_features)

            # 将团队特征堆叠为 [batch_size, num_agents, hidden_dim]
            team_features = torch.stack(team_features, dim=1)

            # 将当前智能体的特征作为查询，对团队特征进行注意力计算
            query = features.unsqueeze(1)  # [batch_size, 1, hidden_dim]
            attn_output, _ = self.attention(query, team_features, team_features)

            # 将注意力输出与原始特征组合
            features = features + attn_output.squeeze(1)  # [batch_size, hidden_dim]

        # 计算各个头的价值
        role_values = {}
        for i in range(self.num_roles):
            role_values[f'role_{i}'] = self.role_value_heads[i](features)

        # 计算全局价值
        global_value = self.global_value_head(features)

        # 计算协作价值
        cooperation_value = self.cooperation_value_head(features)
        role_values['cooperation'] = cooperation_value

        return global_value, role_values

    def get_role_value(self, state: torch.Tensor, role_id: int) -> torch.Tensor:
        """
        获取特定角色的价值

        Args:
            state: 状态张量 [batch_size, state_dim]
            role_id: 角色ID

        Returns:
            角色价值
        """
        role_ids = torch.ones(state.shape[0], dtype=torch.long) * role_id
        _, role_values = self.forward(state, role_ids)
        return role_values[f'role_{role_id}']

    def get_cooperation_value(self, state: torch.Tensor, role_ids: torch.Tensor) -> torch.Tensor:
        """
        获取协作价值

        Args:
            state: 状态张量 [batch_size, state_dim]
            role_ids: 角色ID张量 [batch_size]

        Returns:
            协作价值
        """
        _, role_values = self.forward(state, role_ids)
        return role_values['cooperation']


class CollaborativeExploration:
    """
    协同探索机制

    实现农民智能体之间的协同探索，通过协调探索策略和共享探索经验，
    使农民智能体能够更有效地探索状态空间。
    """

    def __init__(
        self,
        agents: Dict[str, Agent],
        role_manager: RoleManager,
        exploration_rate: float = 0.1,
        decay_factor: float = 0.995,
        min_exploration_rate: float = 0.01,
        use_common_exploration: bool = True,
        use_coordinated_exploration: bool = True,
        use_curiosity_driven: bool = False
    ):
        """
        初始化协同探索机制

        Args:
            agents: 智能体字典，键为智能体ID
            role_manager: 角色管理器
            exploration_rate: 探索率
            decay_factor: 衰减因子
            min_exploration_rate: 最小探索率
            use_common_exploration: 是否使用共同探索
            use_coordinated_exploration: 是否使用协调探索
            use_curiosity_driven: 是否使用好奇心驱动探索
        """
        self.agents = agents
        self.role_manager = role_manager
        self.exploration_rate = exploration_rate
        self.decay_factor = decay_factor
        self.min_exploration_rate = min_exploration_rate
        self.use_common_exploration = use_common_exploration
        self.use_coordinated_exploration = use_coordinated_exploration
        self.use_curiosity_driven = use_curiosity_driven

        # 获取农民智能体
        self.farmer_agents = {}
        for agent_id, agent in agents.items():
            if role_manager.get_role(agent_id) == "farmer":
                self.farmer_agents[agent_id] = agent

        # 初始化探索统计
        self.exploration_stats = {
            "total_explorations": 0,
            "coordinated_explorations": 0,
            "common_explorations": 0,
            "curiosity_driven_explorations": 0
        }

        # 初始化共同探索经验缓冲区
        self.common_exploration_buffer = deque(maxlen=1000)

        # 初始化好奇心驱动探索模型（如果使用）
        self.curiosity_model = None
        if use_curiosity_driven:
            self.curiosity_model = self._create_curiosity_model()

    def _create_curiosity_model(self) -> nn.Module:
        """
        创建好奇心驱动探索模型

        Returns:
            好奇心驱动探索模型
        """
        # 简化的好奇心驱动模型，实际实现中可以使用更复杂的模型
        class CuriosityModel(nn.Module):
            def __init__(self, state_dim: int = 200, hidden_dim: int = 128, action_dim: int = 100):
                super(CuriosityModel, self).__init__()

                # 前向模型（预测下一个状态）
                self.forward_model = nn.Sequential(
                    nn.Linear(state_dim + action_dim, hidden_dim),
                    nn.ReLU(),
                    nn.Linear(hidden_dim, hidden_dim),
                    nn.ReLU(),
                    nn.Linear(hidden_dim, state_dim)
                )

                # 逆向模型（预测动作）
                self.inverse_model = nn.Sequential(
                    nn.Linear(state_dim * 2, hidden_dim),
                    nn.ReLU(),
                    nn.Linear(hidden_dim, hidden_dim),
                    nn.ReLU(),
                    nn.Linear(hidden_dim, action_dim)
                )

                # 优化器
                self.optimizer = optim.Adam(self.parameters(), lr=0.001)

            def forward(self, state: torch.Tensor, action: torch.Tensor, next_state: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
                # 将状态和动作连接
                state_action = torch.cat([state, action], dim=1)

                # 前向模型预测下一个状态
                predicted_next_state = self.forward_model(state_action)

                # 逆向模型预测动作
                state_next_state = torch.cat([state, next_state], dim=1)
                predicted_action = self.inverse_model(state_next_state)

                return predicted_next_state, predicted_action

            def compute_intrinsic_reward(self, state: torch.Tensor, action: torch.Tensor, next_state: torch.Tensor) -> torch.Tensor:
                # 计算内在奖励（预测误差）
                with torch.no_grad():
                    predicted_next_state, _ = self.forward(state, action, next_state)
                    intrinsic_reward = F.mse_loss(predicted_next_state, next_state, reduction='none').mean(dim=1)

                return intrinsic_reward

            def update(self, state: torch.Tensor, action: torch.Tensor, next_state: torch.Tensor) -> Dict[str, float]:
                # 前向和逆向预测
                predicted_next_state, predicted_action = self.forward(state, action, next_state)

                # 计算前向和逆向损失
                forward_loss = F.mse_loss(predicted_next_state, next_state)
                inverse_loss = F.mse_loss(predicted_action, action)

                # 总损失
                total_loss = forward_loss + inverse_loss

                # 反向传播和优化
                self.optimizer.zero_grad()
                total_loss.backward()
                self.optimizer.step()

                return {
                    'forward_loss': forward_loss.item(),
                    'inverse_loss': inverse_loss.item(),
                    'total_loss': total_loss.item()
                }

        return CuriosityModel()

    def select_exploration_action(
        self,
        agent_id: str,
        state: Any,
        legal_actions: List[int],
        teammate_states: Optional[Dict[str, Any]] = None
    ) -> Optional[int]:
        """
        选择探索动作

        Args:
            agent_id: 智能体ID
            state: 当前状态
            legal_actions: 合法动作列表
            teammate_states: 队友状态字典，可选

        Returns:
            探索动作，如果不需要探索则返回None
        """
        # 检查是否为农民智能体
        if self.role_manager.get_role(agent_id) != "farmer":
            return None

        # 检查是否需要探索
        if np.random.random() > self.exploration_rate:
            return None

        # 更新统计
        self.exploration_stats["total_explorations"] += 1

        # 如果没有合法动作，返回None
        if not legal_actions:
            return None

        # 协调探索
        if self.use_coordinated_exploration and teammate_states:
            coordinated_action = self._select_coordinated_exploration_action(
                agent_id, state, legal_actions, teammate_states
            )
            if coordinated_action is not None:
                self.exploration_stats["coordinated_explorations"] += 1
                return coordinated_action

        # 共同探索
        if self.use_common_exploration and self.common_exploration_buffer:
            common_action = self._select_common_exploration_action(
                agent_id, state, legal_actions
            )
            if common_action is not None:
                self.exploration_stats["common_explorations"] += 1
                return common_action

        # 好奇心驱动探索
        if self.use_curiosity_driven and self.curiosity_model is not None:
            curiosity_action = self._select_curiosity_driven_action(
                agent_id, state, legal_actions
            )
            if curiosity_action is not None:
                self.exploration_stats["curiosity_driven_explorations"] += 1
                return curiosity_action

        # 随机探索
        return np.random.choice(legal_actions)

    def _select_coordinated_exploration_action(
        self,
        agent_id: str,
        state: Any,
        legal_actions: List[int],
        teammate_states: Dict[str, Any]
    ) -> Optional[int]:
        """
        选择协调探索动作

        协调探索是指农民智能体之间协调探索策略，以避免重复探索相同的状态空间。

        Args:
            agent_id: 智能体ID
            state: 当前状态
            legal_actions: 合法动作列表
            teammate_states: 队友状态字典

        Returns:
            协调探索动作，如果无法协调则返回None
        """
        # 获取队友
        teammates = []
        for other_id in teammate_states.keys():
            if self.role_manager.get_role(other_id) == "farmer" and other_id != agent_id:
                teammates.append(other_id)

        if not teammates:
            return None

        # 获取队友的动作价值
        teammate_action_values = {}
        for teammate_id in teammates:
            teammate_agent = self.agents.get(teammate_id)
            if teammate_agent and hasattr(teammate_agent.algorithm, 'network'):
                teammate_state = teammate_states[teammate_id]
                # 假设网络输出策略和价值
                with torch.no_grad():
                    state_tensor = torch.FloatTensor(np.array([teammate_state]))
                    policy_logits, _ = teammate_agent.algorithm.network(state_tensor)
                    policy = F.softmax(policy_logits, dim=-1).squeeze(0).numpy()

                    # 将策略转换为动作价值字典
                    for action in range(len(policy)):
                        if action not in teammate_action_values:
                            teammate_action_values[action] = 0.0
                        teammate_action_values[action] += policy[action]

        if not teammate_action_values:
            return None

        # 将队友的动作价值归一化
        total_value = sum(teammate_action_values.values())
        if total_value > 0:
            for action in teammate_action_values:
                teammate_action_values[action] /= total_value

        # 选择与队友互补的动作（低价值的动作）
        complementary_actions = []
        for action in legal_actions:
            if action in teammate_action_values and teammate_action_values[action] < 0.1:  # 低价值阈值
                complementary_actions.append(action)

        if complementary_actions:
            return np.random.choice(complementary_actions)

        return None

    def _select_common_exploration_action(
        self,
        agent_id: str,
        state: Any,
        legal_actions: List[int]
    ) -> Optional[int]:
        """
        选择共同探索动作

        共同探索是指农民智能体共享探索经验，以加速学习过程。

        Args:
            agent_id: 智能体ID
            state: 当前状态
            legal_actions: 合法动作列表

        Returns:
            共同探索动作，如果无法共享则返回None
        """
        if not self.common_exploration_buffer:
            return None

        # 从共同探索经验缓冲区中随机选择一个经验
        experience = np.random.choice(self.common_exploration_buffer)

        # 提取动作
        action = experience.get('action')

        # 检查动作是否合法
        if action in legal_actions:
            return action

        return None

    def _select_curiosity_driven_action(
        self,
        agent_id: str,
        state: Any,
        legal_actions: List[int]
    ) -> Optional[int]:
        """
        选择好奇心驱动探索动作

        好奇心驱动探索是指使用内在奖励机制，鼓励智能体探索未知的状态空间。

        Args:
            agent_id: 智能体ID
            state: 当前状态
            legal_actions: 合法动作列表

        Returns:
            好奇心驱动探索动作，如果无法驱动则返回None
        """
        if not self.curiosity_model or not legal_actions:
            return None

        # 计算每个动作的内在奖励
        intrinsic_rewards = {}

        # 将状态转换为张量
        state_tensor = torch.FloatTensor(np.array([state]))

        for action in legal_actions:
            # 将动作转换为独热编码
            action_tensor = torch.zeros(100)  # 假设动作空间大小为100
            if action < 100:
                action_tensor[action] = 1.0
            action_tensor = action_tensor.unsqueeze(0)

            # 使用前向模型预测下一个状态
            with torch.no_grad():
                # 简化的预测，实际实现中可能需要使用环境模型
                predicted_next_state, _ = self.curiosity_model.forward(state_tensor, action_tensor, state_tensor)

                # 计算预测误差作为内在奖励
                intrinsic_reward = F.mse_loss(predicted_next_state, state_tensor, reduction='none').mean().item()
                intrinsic_rewards[action] = intrinsic_reward

        if not intrinsic_rewards:
            return None

        # 选择内在奖励最高的动作
        best_action = max(intrinsic_rewards.items(), key=lambda x: x[1])[0]

        return best_action

    def update_exploration_rate(self) -> None:
        """
        更新探索率
        """
        # 衰减探索率
        self.exploration_rate = max(self.min_exploration_rate, self.exploration_rate * self.decay_factor)

    def add_to_common_buffer(self, experience: Dict[str, Any]) -> None:
        """
        添加到共同探索缓冲区

        Args:
            experience: 探索经验
        """
        self.common_exploration_buffer.append(experience)

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            统计信息
        """
        return self.exploration_stats


def optimize_cooperative_strategy(
    farmer_cooperation: FarmerCooperation,
    agents: Dict[str, Agent],
    role_manager: RoleManager
) -> None:
    """
    优化协作策略

    将联合策略优化、角色感知批评家网络和协同探索机制集成到现有的农民协作机制中。

    Args:
        farmer_cooperation: 现有的农民协作机制
        agents: 智能体字典，键为智能体ID
        role_manager: 角色管理器
    """
    # 创建联合策略优化器
    joint_policy_optimizer = JointPolicyOptimizer(
        agents=agents,
        role_manager=role_manager,
        joint_loss_weight=0.7
    )

    # 创建角色感知批评家网络
    role_aware_critic = RoleAwareCritic(
        state_dim=200,  # 根据实际状态维度调整
        hidden_dims=[256, 128],
        num_roles=3,  # 地主和两个农民
        use_role_embedding=True,
        use_attention=True
    )

    # 创建协同探索机制
    collaborative_exploration = CollaborativeExploration(
        agents=agents,
        role_manager=role_manager,
        exploration_rate=0.1,
        decay_factor=0.995,
        use_common_exploration=True,
        use_coordinated_exploration=True
    )

    # 将这些组件添加到农民协作机制中
    farmer_cooperation.joint_policy_optimizer = joint_policy_optimizer
    farmer_cooperation.role_aware_critic = role_aware_critic
    farmer_cooperation.collaborative_exploration = collaborative_exploration

    # 增强原有的coordinate_actions方法
    original_coordinate_actions = farmer_cooperation.coordinate_actions

    def enhanced_coordinate_actions(agent_id, observations, legal_actions):
        # 先尝试使用协同探索
        if hasattr(farmer_cooperation, 'collaborative_exploration'):
            exploration_action = farmer_cooperation.collaborative_exploration.select_exploration_action(
                agent_id, observations[agent_id], legal_actions[agent_id], observations
            )
            if exploration_action is not None:
                return exploration_action

        # 如果没有探索动作，使用原有的协调方法
        return original_coordinate_actions(agent_id, observations, legal_actions)

    # 替换原有的coordinate_actions方法
    farmer_cooperation.coordinate_actions = enhanced_coordinate_actions


def test_cooperative_strategy():
    """
    测试协作策略优化
    """
    print("\n=== 测试协作策略优化 ===")

    # 创建角色管理器
    from cardgame_ai.multi_agent.multi_agent_framework import RoleManager
    role_manager = RoleManager(None)

    # 模拟角色管理器的方法
    role_manager.get_role = lambda agent_id: "farmer" if agent_id in ["0", "1"] else "landlord"
    role_manager.get_teammates = lambda agent_id: ["1"] if agent_id == "0" else ["0"] if agent_id == "1" else []

    # 创建农民协作机制
    from cardgame_ai.multi_agent.multi_agent_framework import FarmerCooperation
    farmer_cooperation = FarmerCooperation(role_manager)

    # 创建模拟智能体
    from cardgame_ai.algorithms.mappo import MAPPO
    from cardgame_ai.core.agent import Agent

    agents = {}
    for agent_id in ["0", "1", "2"]:
        # 创建简化的MAPPO算法
        mappo = MAPPO(
            obs_dim=200,
            act_dim=100,
            hidden_dims=[128, 64],
            learning_rate=0.0003
        )

        # 创建智能体
        agents[agent_id] = Agent(algorithm=mappo)

    # 优化协作策略
    optimize_cooperative_strategy(farmer_cooperation, agents, role_manager)

    # 测试联合策略优化器
    print("\n测试联合策略优化器:")
    joint_policy_optimizer = farmer_cooperation.joint_policy_optimizer
    print(f"  农民智能体数量: {len(joint_policy_optimizer.farmer_agents)}")

    # 测试角色感知批评家网络
    print("\n测试角色感知批评家网络:")
    role_aware_critic = farmer_cooperation.role_aware_critic

    # 创建模拟状态
    import torch
    import numpy as np
    state = torch.rand(2, 200)  # 批大小为2的状态
    role_ids = torch.tensor([0, 1])  # 角色ID

    # 计算价值
    global_value, role_values = role_aware_critic(state, role_ids)
    print(f"  全局价值形状: {global_value.shape}")
    print(f"  角色价值数量: {len(role_values)}")

    # 测试协同探索机制
    print("\n测试协同探索机制:")
    collaborative_exploration = farmer_cooperation.collaborative_exploration

    # 模拟状态和合法动作
    state = np.random.rand(200)
    legal_actions = [0, 1, 2, 3, 4]

    # 选择探索动作
    exploration_action = collaborative_exploration.select_exploration_action(
        "0", state, legal_actions
    )
    print(f"  探索动作: {exploration_action}")

    # 测试增强的coordinate_actions方法
    print("\n测试增强的coordinate_actions方法:")

    # 模拟观察和合法动作
    observations = {"0": np.random.rand(200), "1": np.random.rand(200), "2": np.random.rand(200)}
    legal_actions = {"0": [0, 1, 2, 3, 4], "1": [0, 1, 2, 3], "2": [0, 1, 2]}

    # 协调动作
    action = farmer_cooperation.coordinate_actions("0", observations, legal_actions)
    print(f"  协调动作: {action}")

    return {
        "farmer_cooperation": farmer_cooperation,
        "joint_policy_optimizer": joint_policy_optimizer,
        "role_aware_critic": role_aware_critic,
        "collaborative_exploration": collaborative_exploration
    }


if __name__ == "__main__":
    # 如果直接运行这个文件，则执行测试
    test_cooperative_strategy()