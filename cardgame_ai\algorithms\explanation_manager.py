"""
决策解释管理器模块

管理决策解释模式，提供统一的接口。
"""

import logging
from typing import Dict, List, Any, Optional, Union, Tuple
from collections import defaultdict

from cardgame_ai.algorithms.decision_explanation import DecisionExplanationMode
from cardgame_ai.algorithms.causal_inference import CausalInferenceModule
from cardgame_ai.core.base import State, Action

# 配置日志
logger = logging.getLogger(__name__)


class ExplanationManager:
    """
    决策解释管理器

    管理决策解释模式，提供统一的接口。
    """

    def __init__(
        self,
        enabled: bool = False,
        detail_level: str = "medium",
        visualization_enabled: bool = True,
        save_explanations: bool = False,
        save_dir: str = "explanations",
        enable_causal_inference: bool = True,
        causal_confidence_threshold: float = 0.7
    ):
        """
        初始化决策解释管理器

        Args:
            enabled: 是否启用决策解释模式
            detail_level: 解释详细程度，可选值为"low"、"medium"、"high"
            visualization_enabled: 是否启用可视化
            save_explanations: 是否保存解释结果
            save_dir: 解释结果保存目录
            enable_causal_inference: 是否启用因果推断
            causal_confidence_threshold: 因果推断置信度阈值
        """
        self.enabled = enabled
        self.detail_level = detail_level
        self.visualization_enabled = visualization_enabled
        self.save_explanations = save_explanations
        self.save_dir = save_dir
        self.enable_causal_inference = enable_causal_inference
        self.causal_confidence_threshold = causal_confidence_threshold

        # 创建决策解释模式
        self.explanation_mode = None
        if self.enabled:
            self.enable_explanation_mode()

        # 创建因果推断模块
        self.causal_inference = None
        if self.enable_causal_inference:
            self.causal_inference = CausalInferenceModule(
                confidence_threshold=self.causal_confidence_threshold
            )
            logger.info("已启用因果推断模块")

        # 统计信息
        self.stats = defaultdict(int)

        logger.info(f"初始化决策解释管理器，启用状态: {enabled}，因果推断: {enable_causal_inference}")

    def enable_explanation_mode(self) -> None:
        """
        启用决策解释模式
        """
        if not self.enabled:
            self.enabled = True
            self.explanation_mode = DecisionExplanationMode(
                detail_level=self.detail_level,
                visualization_enabled=self.visualization_enabled,
                save_explanations=self.save_explanations,
                save_dir=self.save_dir
            )
            logger.info("已启用决策解释模式")
        else:
            logger.info("决策解释模式已经启用")

    def disable_explanation_mode(self) -> None:
        """
        禁用决策解释模式
        """
        if self.enabled:
            self.enabled = False
            self.explanation_mode = None
            logger.info("已禁用决策解释模式")
        else:
            logger.info("决策解释模式已经禁用")

    def explain_decision(
        self,
        decision_data: Dict[str, Any],
        decision_type: str = "mcts",
        context: Optional[Dict[str, Any]] = None
    ) -> Optional[Dict[str, Any]]:
        """
        解释决策

        Args:
            decision_data: 决策数据
            decision_type: 决策类型，可选值为"mcts"、"network"、"rule"、"hybrid"
            context: 决策上下文信息

        Returns:
            Optional[Dict[str, Any]]: 解释结果，如果未启用决策解释模式则返回None
        """
        # 记录统计信息
        self.stats["total_requests"] += 1
        self.stats[f"{decision_type}_requests"] += 1

        # 如果未启用决策解释模式，返回None
        if not self.enabled or self.explanation_mode is None:
            return None

        # 解释决策
        explanation = self.explanation_mode.explain_decision(
            decision_data=decision_data,
            decision_type=decision_type,
            context=context
        )

        return explanation

    def get_explanation_history(
        self,
        limit: Optional[int] = None,
        decision_type: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        获取解释历史

        Args:
            limit: 返回的历史记录数量限制
            decision_type: 决策类型过滤

        Returns:
            List[Dict[str, Any]]: 解释历史，如果未启用决策解释模式则返回空列表
        """
        if not self.enabled or self.explanation_mode is None:
            return []

        return self.explanation_mode.get_explanation_history(
            limit=limit,
            decision_type=decision_type
        )

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        stats = dict(self.stats)

        # 如果启用了决策解释模式，添加解释模式的统计信息
        if self.enabled and self.explanation_mode is not None:
            explanation_stats = self.explanation_mode.get_stats()
            for key, value in explanation_stats.items():
                stats[f"explanation_{key}"] = value

        # 如果启用了因果推断，添加因果推断模块的统计信息
        if self.enable_causal_inference and self.causal_inference is not None:
            causal_stats = self.causal_inference.get_stats()
            for key, value in causal_stats.items():
                stats[f"causal_{key}"] = value

        return stats

    def clear_history(self) -> None:
        """
        清除历史记录
        """
        if self.enabled and self.explanation_mode is not None:
            self.explanation_mode.clear_history()
            logger.info("已清除解释历史记录")

    def set_detail_level(self, detail_level: str) -> None:
        """
        设置解释详细程度

        Args:
            detail_level: 解释详细程度，可选值为"low"、"medium"、"high"
        """
        if detail_level not in ["low", "medium", "high"]:
            logger.warning(f"无效的解释详细程度: {detail_level}，使用默认值: medium")
            detail_level = "medium"

        self.detail_level = detail_level

        # 如果已启用决策解释模式，重新创建解释模式
        if self.enabled:
            self.explanation_mode = DecisionExplanationMode(
                detail_level=self.detail_level,
                visualization_enabled=self.visualization_enabled,
                save_explanations=self.save_explanations,
                save_dir=self.save_dir
            )

        logger.info(f"已设置解释详细程度: {detail_level}")

    def set_visualization_enabled(self, enabled: bool) -> None:
        """
        设置是否启用可视化

        Args:
            enabled: 是否启用可视化
        """
        self.visualization_enabled = enabled

        # 如果已启用决策解释模式，重新创建解释模式
        if self.enabled:
            self.explanation_mode = DecisionExplanationMode(
                detail_level=self.detail_level,
                visualization_enabled=self.visualization_enabled,
                save_explanations=self.save_explanations,
                save_dir=self.save_dir
            )

        logger.info(f"已设置可视化启用状态: {enabled}")

    def set_save_explanations(self, enabled: bool, save_dir: Optional[str] = None) -> None:
        """
        设置是否保存解释结果

        Args:
            enabled: 是否保存解释结果
            save_dir: 解释结果保存目录
        """
        self.save_explanations = enabled

        if save_dir is not None:
            self.save_dir = save_dir

        # 如果已启用决策解释模式，重新创建解释模式
        if self.enabled:
            self.explanation_mode = DecisionExplanationMode(
                detail_level=self.detail_level,
                visualization_enabled=self.visualization_enabled,
                save_explanations=self.save_explanations,
                save_dir=self.save_dir
            )

        logger.info(f"已设置解释结果保存状态: {enabled}，保存目录: {self.save_dir}")

    def analyze_causal_impact(
        self,
        state: State,
        action: Action,
        alternative_actions: List[Action],
        search_results: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        分析动作的因果影响

        Args:
            state: 当前游戏状态
            action: 选择的动作
            alternative_actions: 备选动作列表
            search_results: 搜索结果，包含访问计数、价值估计等

        Returns:
            Optional[Dict[str, Any]]: 因果分析结果，如果未启用因果推断则返回None
        """
        # 记录统计信息
        self.stats["causal_analysis_requests"] += 1

        # 如果未启用因果推断，返回None
        if not self.enable_causal_inference or self.causal_inference is None:
            return None

        # 进行因果分析
        analysis = self.causal_inference.analyze_action_impact(
            state=state,
            action=action,
            alternative_actions=alternative_actions,
            search_results=search_results
        )

        # 如果启用了决策解释模式，将因果分析结果添加到解释中
        if self.enabled and self.explanation_mode is not None and "causal_analysis" not in search_results:
            search_results["causal_analysis"] = analysis

        return analysis
