"""
模型优化模块

提供模型量化、ONNX转换和TensorRT优化功能，以加速神经网络的推理速度。
"""

import os
import time
import torch
import torch.nn as nn
import torch.quantization
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Union, Callable
import logging

# 配置日志
logger = logging.getLogger(__name__)

# 尝试导入ONNX相关库
try:
    import onnx
    import onnxruntime
    ONNX_AVAILABLE = True
except ImportError:
    logger.warning("ONNX相关库未安装，ONNX转换功能将不可用")
    ONNX_AVAILABLE = False

# 尝试导入TensorRT相关库
try:
    import tensorrt as trt
    import pycuda.driver as cuda
    import pycuda.autoinit
    TENSORRT_AVAILABLE = True
except ImportError:
    logger.warning("TensorRT相关库未安装，TensorRT优化功能将不可用")
    TENSORRT_AVAILABLE = False


def prepare_model_for_quantization(model: nn.Module) -> nn.Module:
    """
    准备模型进行量化，包括融合模块等操作

    Args:
        model: 原始模型

    Returns:
        准备好进行量化的模型
    """
    # 设置为评估模式
    model.eval()

    # 尝试融合常见的模块组合
    # 注意：实际的融合模块列表需要根据模型架构进行调整
    fusion_list = []

    # 查找可能的卷积-BN-ReLU模式
    for name, module in model.named_modules():
        if isinstance(module, nn.Sequential):
            if len(module) >= 3:
                if (isinstance(module[0], nn.Conv2d) and
                    isinstance(module[1], nn.BatchNorm2d) and
                    isinstance(module[2], nn.ReLU)):
                    fusion_list.append([f"{name}.0", f"{name}.1", f"{name}.2"])

    # 如果找到了可融合的模块，进行融合
    if fusion_list:
        logger.info(f"找到 {len(fusion_list)} 个可融合的模块组")
        model = torch.quantization.fuse_modules(model, fusion_list, inplace=False)
        logger.info("模块融合完成")
    else:
        logger.info("未找到可融合的模块组")

    return model


def quantize_model_static(
    model: nn.Module,
    calibration_data_loader: Any,
    save_path: str,
    backend: str = 'fbgemm',
    num_calibration_batches: int = 10
) -> nn.Module:
    """
    使用静态量化优化模型

    Args:
        model: 原始模型
        calibration_data_loader: 校准数据加载器
        save_path: 量化模型保存路径
        backend: 量化后端，可选值为'fbgemm'(x86 CPU)或'qnnpack'(ARM CPU)
        num_calibration_batches: 用于校准的批次数量

    Returns:
        量化后的模型
    """
    # 准备模型进行量化
    model = prepare_model_for_quantization(model)

    # 指定量化配置
    model.qconfig = torch.quantization.get_default_qconfig(backend)
    logger.info(f"使用 {backend} 后端进行量化")

    # 准备量化（插入观察器）
    model_prepared = torch.quantization.prepare(model)
    logger.info("模型准备完成，开始校准...")

    # 使用校准数据校准模型
    with torch.no_grad():
        for i, batch in enumerate(calibration_data_loader):
            if i >= num_calibration_batches:
                break

            # 获取输入数据
            if isinstance(batch, dict):
                inputs = batch['state']
            elif isinstance(batch, (list, tuple)):
                inputs = batch[0]
            else:
                inputs = batch

            # 前向传播
            model_prepared(inputs)

            if (i + 1) % 5 == 0:
                logger.info(f"已完成 {i + 1}/{num_calibration_batches} 批次的校准")

    logger.info("校准完成，转换模型...")

    # 转换为量化模型
    quantized_model = torch.quantization.convert(model_prepared)
    logger.info("模型量化完成")

    # 保存量化模型
    torch.save(quantized_model.state_dict(), save_path)
    logger.info(f"量化模型已保存到 {save_path}")

    return quantized_model


def quantize_model_dynamic(
    model: nn.Module,
    save_path: str,
    dtype: torch.dtype = torch.qint8
) -> nn.Module:
    """
    使用动态量化优化模型

    Args:
        model: 原始模型
        save_path: 量化模型保存路径
        dtype: 量化数据类型

    Returns:
        量化后的模型
    """
    # 设置为评估模式
    model.eval()

    # 动态量化
    quantized_model = torch.quantization.quantize_dynamic(
        model,
        {nn.Linear, nn.LSTM, nn.GRU},
        dtype=dtype
    )

    logger.info("动态量化完成")

    # 保存量化模型
    torch.save(quantized_model.state_dict(), save_path)
    logger.info(f"动态量化模型已保存到 {save_path}")

    return quantized_model


def export_to_onnx(
    model: nn.Module,
    dummy_input: torch.Tensor,
    save_path: str,
    input_names: List[str] = ['input'],
    output_names: List[str] = ['output'],
    dynamic_axes: Optional[Dict[str, Dict[int, str]]] = None,
    opset_version: int = 11
) -> str:
    """
    将模型导出为ONNX格式

    Args:
        model: PyTorch模型
        dummy_input: 示例输入
        save_path: ONNX模型保存路径
        input_names: 输入名称列表
        output_names: 输出名称列表
        dynamic_axes: 动态轴配置
        opset_version: ONNX操作集版本

    Returns:
        ONNX模型保存路径
    """
    if not ONNX_AVAILABLE:
        raise ImportError("ONNX相关库未安装，无法导出ONNX模型")

    # 设置为评估模式
    model.eval()

    # 导出ONNX模型
    torch.onnx.export(
        model,
        dummy_input,
        save_path,
        export_params=True,
        opset_version=opset_version,
        do_constant_folding=True,
        input_names=input_names,
        output_names=output_names,
        dynamic_axes=dynamic_axes
    )

    logger.info(f"模型已导出为ONNX格式: {save_path}")

    # 验证ONNX模型
    onnx_model = onnx.load(save_path)
    onnx.checker.check_model(onnx_model)
    logger.info("ONNX模型验证通过")

    return save_path


def create_onnx_inference_session(
    onnx_path: str,
    providers: Optional[List[str]] = None
) -> Any:
    """
    创建ONNX推理会话

    Args:
        onnx_path: ONNX模型路径
        providers: 计算提供者列表

    Returns:
        ONNX推理会话
    """
    if not ONNX_AVAILABLE:
        raise ImportError("ONNX相关库未安装，无法创建ONNX推理会话")

    # 如果未指定提供者，使用默认提供者
    if providers is None:
        providers = ['CUDAExecutionProvider', 'CPUExecutionProvider']

    # 创建推理会话
    session = onnxruntime.InferenceSession(onnx_path, providers=providers)
    logger.info(f"已创建ONNX推理会话，使用提供者: {session.get_providers()}")

    return session


def onnx_inference(
    session: Any,
    inputs: Dict[str, np.ndarray]
) -> Dict[str, np.ndarray]:
    """
    使用ONNX会话进行推理

    Args:
        session: ONNX推理会话
        inputs: 输入数据字典，键为输入名称，值为NumPy数组

    Returns:
        输出数据字典，键为输出名称，值为NumPy数组
    """
    if not ONNX_AVAILABLE:
        raise ImportError("ONNX相关库未安装，无法进行ONNX推理")

    # 获取输入名称
    input_names = [input.name for input in session.get_inputs()]

    # 确保所有必需的输入都已提供
    for name in input_names:
        if name not in inputs:
            raise ValueError(f"缺少必需的输入: {name}")

    # 进行推理
    outputs = session.run(None, inputs)

    # 获取输出名称
    output_names = [output.name for output in session.get_outputs()]

    # 构建输出字典
    return {name: output for name, output in zip(output_names, outputs)}


def convert_onnx_to_tensorrt(
    onnx_path: str,
    save_path: str,
    precision: str = 'fp16',
    max_workspace_size: int = 1 << 30,  # 1GB
    max_batch_size: int = 1
) -> str:
    """
    将ONNX模型转换为TensorRT格式

    Args:
        onnx_path: ONNX模型路径
        save_path: TensorRT模型保存路径
        precision: 精度模式，可选值为'fp32'、'fp16'、'int8'
        max_workspace_size: 最大工作空间大小（字节）
        max_batch_size: 最大批次大小

    Returns:
        TensorRT模型保存路径
    """
    if not TENSORRT_AVAILABLE:
        raise ImportError("TensorRT相关库未安装，无法转换为TensorRT模型")

    logger.info(f"开始将ONNX模型转换为TensorRT模型，精度模式: {precision}")

    # 创建TensorRT构建器和网络
    logger.info("创建TensorRT构建器和网络...")
    TRT_LOGGER = trt.Logger(trt.Logger.WARNING)
    builder = trt.Builder(TRT_LOGGER)
    network = builder.create_network(1 << int(trt.NetworkDefinitionCreationFlag.EXPLICIT_BATCH))
    config = builder.create_builder_config()
    config.max_workspace_size = max_workspace_size

    # 设置精度模式
    if precision == 'fp16' and builder.platform_has_fast_fp16:
        config.set_flag(trt.BuilderFlag.FP16)
        logger.info("启用FP16精度")
    elif precision == 'int8' and builder.platform_has_fast_int8:
        config.set_flag(trt.BuilderFlag.INT8)
        logger.info("启用INT8精度")
    else:
        logger.info("使用FP32精度")

    # 解析ONNX模型
    logger.info("解析ONNX模型...")
    parser = trt.OnnxParser(network, TRT_LOGGER)
    with open(onnx_path, 'rb') as model:
        if not parser.parse(model.read()):
            for error in range(parser.num_errors):
                logger.error(f"ONNX解析错误 {error}: {parser.get_error(error)}")
            raise RuntimeError("ONNX解析失败")

    # 构建引擎
    logger.info("构建TensorRT引擎...")
    engine = builder.build_engine(network, config)
    if engine is None:
        raise RuntimeError("TensorRT引擎构建失败")

    # 保存引擎
    logger.info(f"保存TensorRT引擎到 {save_path}...")
    with open(save_path, 'wb') as f:
        f.write(engine.serialize())

    logger.info("TensorRT模型转换完成")
    return save_path


def benchmark_inference(
    model_path: str,
    model_type: str,
    input_shape: Tuple[int, ...],
    num_iterations: int = 100,
    warmup_iterations: int = 10,
    device: str = 'cuda'
) -> Dict[str, float]:
    """
    对模型进行推理性能基准测试

    Args:
        model_path: 模型路径
        model_type: 模型类型，可选值为'pytorch'、'pytorch_quantized'、'onnx'、'tensorrt'
        input_shape: 输入形状
        num_iterations: 测试迭代次数
        warmup_iterations: 预热迭代次数
        device: 计算设备

    Returns:
        性能指标字典
    """
    logger.info(f"开始对 {model_type} 模型进行推理性能基准测试...")

    # 准备随机输入数据
    if model_type in ['pytorch', 'pytorch_quantized']:
        inputs = torch.randn(input_shape, device=device)
    else:
        inputs = np.random.randn(*input_shape).astype(np.float32)

    # 加载模型并准备推理
    if model_type == 'pytorch':
        # 加载PyTorch模型
        model = torch.load(model_path)
        model.eval()
        model.to(device)

        # 预热
        logger.info(f"预热 {warmup_iterations} 次迭代...")
        with torch.no_grad():
            for _ in range(warmup_iterations):
                _ = model(inputs)

        # 计时
        logger.info(f"开始 {num_iterations} 次计时迭代...")
        torch.cuda.synchronize() if device == 'cuda' else None
        start_time = time.time()

        with torch.no_grad():
            for _ in range(num_iterations):
                _ = model(inputs)

        torch.cuda.synchronize() if device == 'cuda' else None
        end_time = time.time()

    elif model_type == 'pytorch_quantized':
        # 加载量化PyTorch模型
        model = torch.load(model_path)
        model.eval()

        # 预热
        logger.info(f"预热 {warmup_iterations} 次迭代...")
        with torch.no_grad():
            for _ in range(warmup_iterations):
                _ = model(inputs.cpu())  # 量化模型通常在CPU上运行

        # 计时
        logger.info(f"开始 {num_iterations} 次计时迭代...")
        start_time = time.time()

        with torch.no_grad():
            for _ in range(num_iterations):
                _ = model(inputs.cpu())

        end_time = time.time()

    elif model_type == 'onnx':
        # 创建ONNX会话
        providers = ['CUDAExecutionProvider'] if device == 'cuda' else ['CPUExecutionProvider']
        session = create_onnx_inference_session(model_path, providers=providers)

        # 准备输入
        input_name = session.get_inputs()[0].name
        onnx_inputs = {input_name: inputs}

        # 预热
        logger.info(f"预热 {warmup_iterations} 次迭代...")
        for _ in range(warmup_iterations):
            _ = session.run(None, onnx_inputs)

        # 计时
        logger.info(f"开始 {num_iterations} 次计时迭代...")
        start_time = time.time()

        for _ in range(num_iterations):
            _ = session.run(None, onnx_inputs)

        end_time = time.time()

    elif model_type == 'tensorrt':
        if not TENSORRT_AVAILABLE:
            raise ImportError("TensorRT相关库未安装，无法进行TensorRT推理")

        # 加载TensorRT引擎
        logger.info("加载TensorRT引擎...")
        with open(model_path, 'rb') as f, trt.Runtime(trt.Logger(trt.Logger.WARNING)) as runtime:
            engine = runtime.deserialize_cuda_engine(f.read())

        # 创建执行上下文
        context = engine.create_execution_context()

        # 分配输入/输出缓冲区
        h_input = cuda.pagelocked_empty(trt.volume(engine.get_binding_shape(0)), dtype=np.float32)
        h_output = cuda.pagelocked_empty(trt.volume(engine.get_binding_shape(1)), dtype=np.float32)
        d_input = cuda.mem_alloc(h_input.nbytes)
        d_output = cuda.mem_alloc(h_output.nbytes)

        # 复制输入数据
        np.copyto(h_input, inputs.ravel())

        # 创建CUDA流
        stream = cuda.Stream()

        # 预热
        logger.info(f"预热 {warmup_iterations} 次迭代...")
        for _ in range(warmup_iterations):
            cuda.memcpy_htod_async(d_input, h_input, stream)
            context.execute_async_v2(bindings=[int(d_input), int(d_output)], stream_handle=stream.handle)
            cuda.memcpy_dtoh_async(h_output, d_output, stream)
            stream.synchronize()

        # 计时
        logger.info(f"开始 {num_iterations} 次计时迭代...")
        start_time = time.time()

        for _ in range(num_iterations):
            cuda.memcpy_htod_async(d_input, h_input, stream)
            context.execute_async_v2(bindings=[int(d_input), int(d_output)], stream_handle=stream.handle)
            cuda.memcpy_dtoh_async(h_output, d_output, stream)
            stream.synchronize()

        end_time = time.time()

    else:
        raise ValueError(f"不支持的模型类型: {model_type}")

    # 计算性能指标
    total_time = end_time - start_time
    avg_time = total_time / num_iterations
    fps = num_iterations / total_time

    logger.info(f"推理性能测试完成:")
    logger.info(f"  总时间: {total_time:.4f} 秒")
    logger.info(f"  平均推理时间: {avg_time * 1000:.4f} 毫秒")
    logger.info(f"  吞吐量: {fps:.2f} FPS")

    return {
        'total_time': total_time,
        'avg_inference_time': avg_time,
        'fps': fps,
        'model_type': model_type
    }
