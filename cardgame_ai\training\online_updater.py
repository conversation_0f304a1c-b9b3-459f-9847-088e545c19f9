"""
在线更新器模块

提供在线更新功能，使用实时收集的完整对局轨迹对模型进行更新，形成闭环。
"""

import os
import time
import logging
import glob
from typing import Dict, List, Any, Optional, Union, Set, Callable

from cardgame_ai.utils.data_loader import (
    load_trajectory_data,
    load_trajectory_to_batch,
    load_trajectory_to_experiences
)
from cardgame_ai.core.base import Experience, Batch
from cardgame_ai.algorithms.continual_learning import EWC, L2Regularization
from cardgame_ai.algorithms.policy_distillation import PolicyDistillation
import torch
import copy

# 配置日志
logger = logging.getLogger(__name__)


class OnlineUpdater:
    """
    在线更新器类

    扫描轨迹目录，发现新的轨迹文件，加载轨迹数据，并使用这些数据更新模型。
    """

    def __init__(
        self,
        model: Any,
        trajectory_dir: str = "data/trajectories",
        update_interval: int = 60,  # 秒
        batch_size: int = 32,
        updates_per_trajectory: int = 5,
        max_trajectories_per_update: int = 10,
        use_experience_format: bool = False,
        file_pattern: str = "*.json",
        use_ewc: bool = False,
        old_task_dataloader: Any = None,
        fisher_importance: float = 1000.0,
        use_l2_reg: bool = False,
        weight_decay: float = 0.01,
        old_task_data_dir: str = "data/old_task_data",
        use_policy_distillation: bool = False,
        policy_distillation_config: Dict[str, Any] = None
    ):
        """
        初始化在线更新器

        Args:
            model: 要更新的模型，必须有train或update方法
            trajectory_dir: 轨迹目录路径
            update_interval: 更新间隔（秒）
            batch_size: 批次大小
            updates_per_trajectory: 每个轨迹的更新次数
            max_trajectories_per_update: 每次更新的最大轨迹数
            use_experience_format: 是否使用Experience格式
            file_pattern: 轨迹文件模式
            use_ewc: 是否使用弹性权重固化 (EWC)
            old_task_dataloader: 旧任务数据加载器，用于EWC
            fisher_importance: Fisher重要性系数，控制EWC惩罚强度
            use_l2_reg: 是否使用L2正则化
            weight_decay: 权重衰减系数，控制L2正则化强度
            old_task_data_dir: 旧任务数据目录，如果old_task_dataloader为None，则从此目录加载数据
            use_policy_distillation: 是否使用策略蒸馏
            policy_distillation_config: 策略蒸馏配置
        """
        self.model = model
        self.trajectory_dir = trajectory_dir
        self.update_interval = update_interval
        self.batch_size = batch_size
        self.updates_per_trajectory = updates_per_trajectory
        self.max_trajectories_per_update = max_trajectories_per_update
        self.use_experience_format = use_experience_format
        self.file_pattern = file_pattern

        # 连续学习相关参数
        self.use_ewc = use_ewc
        self.old_task_dataloader = old_task_dataloader
        self.fisher_importance = fisher_importance
        self.use_l2_reg = use_l2_reg
        self.weight_decay = weight_decay
        self.old_task_data_dir = old_task_data_dir

        # 策略蒸馏相关参数
        self.use_policy_distillation = use_policy_distillation
        self.policy_distillation = None
        if self.use_policy_distillation:
            # 创建教师模型快照
            self.teacher_model = copy.deepcopy(self.model)
            # 初始化策略蒸馏
            pd_config = policy_distillation_config or {}
            temperature = pd_config.get("temperature", 2.0)
            alpha = pd_config.get("alpha", 0.5)
            try:
                self.policy_distillation = PolicyDistillation(
                    student_model=self.model,
                    teacher_model=self.teacher_model,
                    temperature=temperature,
                    alpha=alpha,
                    device=pd_config.get("device", None)
                )
                logger.info(f"策略蒸馏初始化成功，temperature: {temperature}, alpha: {alpha}")
            except Exception as e:
                logger.warning(f"策略蒸馏初始化失败: {e}")
                self.use_policy_distillation = False

        # 初始化连续学习组件
        self.ewc = None
        self.l2_reg = None

        if self.use_ewc:
            # 如果没有提供旧任务数据加载器，尝试从目录加载
            if self.old_task_dataloader is None:
                self._load_old_task_data()

            if self.old_task_dataloader is not None:
                logger.info(f"初始化EWC，Fisher重要性系数: {fisher_importance}")
                try:
                    self.ewc = EWC(model, self.old_task_dataloader, fisher_importance)
                    logger.info("EWC初始化成功")
                except Exception as e:
                    logger.error(f"EWC初始化失败: {e}")
                    self.use_ewc = False
            else:
                logger.warning("未提供旧任务数据，无法初始化EWC")
                self.use_ewc = False

        if self.use_l2_reg:
            logger.info(f"初始化L2正则化，权重衰减系数: {weight_decay}")
            self.l2_reg = L2Regularization(model, weight_decay)
            logger.info("L2正则化初始化成功")

        # 已处理文件集合
        self.processed_files: Set[str] = set()

        # 上次更新时间
        self.last_update_time = 0

        # 更新统计信息
        self.stats = {
            "total_updates": 0,
            "total_trajectories": 0,
            "total_steps": 0,
            "start_time": time.time(),
            "ewc_loss": 0.0,
            "l2_loss": 0.0,
            "distillation_loss": 0.0
        }

        # 确保轨迹目录存在
        os.makedirs(trajectory_dir, exist_ok=True)

        logger.info(f"初始化在线更新器: {trajectory_dir}")

    def _load_old_task_data(self) -> None:
        """
        从目录加载旧任务数据
        """
        if not os.path.exists(self.old_task_data_dir):
            logger.warning(f"旧任务数据目录不存在: {self.old_task_data_dir}")
            return

        try:
            # 获取所有轨迹文件
            pattern = os.path.join(self.old_task_data_dir, self.file_pattern)
            old_files = glob.glob(pattern)

            if not old_files:
                logger.warning(f"旧任务数据目录中没有找到文件: {self.old_task_data_dir}")
                return

            # 加载所有轨迹数据
            old_batches = []
            for filepath in old_files[:10]:  # 限制文件数量，避免内存溢出
                if self.use_experience_format:
                    batch = load_trajectory_to_batch(filepath)
                    if batch is not None and len(batch.experiences) > 0:
                        old_batches.append(batch)
                else:
                    batch = load_trajectory_data(filepath)
                    if batch and "observations" in batch and len(batch["observations"]) > 0:
                        old_batches.append(batch)

            if old_batches:
                # 创建数据加载器
                self.old_task_dataloader = old_batches
                logger.info(f"成功加载旧任务数据: {len(old_batches)}个批次")
            else:
                logger.warning("未能加载任何有效的旧任务数据")
        except Exception as e:
            logger.error(f"加载旧任务数据时出错: {e}")

    def check_and_update(self) -> Dict[str, Any]:
        """
        检查并更新模型

        如果距离上次更新已经过去了足够的时间，则扫描轨迹目录，
        发现新的轨迹文件，加载轨迹数据，并使用这些数据更新模型。

        Returns:
            Dict[str, Any]: 更新统计信息
        """
        current_time = time.time()

        # 检查是否需要更新
        if current_time - self.last_update_time < self.update_interval:
            return self.stats

        # 更新上次更新时间
        self.last_update_time = current_time

        # 扫描轨迹目录
        new_files = self._find_new_trajectories()

        if not new_files:
            logger.info("没有新的轨迹文件")
            return self.stats

        # 限制每次更新的轨迹数
        if len(new_files) > self.max_trajectories_per_update:
            logger.info(f"限制轨迹数量: {len(new_files)} -> {self.max_trajectories_per_update}")
            new_files = new_files[:self.max_trajectories_per_update]

        # 处理新的轨迹文件
        for filepath in new_files:
            logger.info(f"处理轨迹: {filepath}")

            # 加载轨迹数据
            if self.use_experience_format:
                # 使用Experience格式
                batch = load_trajectory_to_batch(filepath)
                if batch is None or len(batch.experiences) == 0:
                    logger.warning(f"无法加载轨迹或轨迹为空: {filepath}")
                    self.processed_files.add(filepath)
                    continue

                # 更新统计信息
                self.stats["total_trajectories"] += 1
                self.stats["total_steps"] += len(batch.experiences)

                # 更新模型
                for _ in range(self.updates_per_trajectory):
                    self._update_model(batch)
                    self.stats["total_updates"] += 1
            else:
                # 使用字典格式
                batch = load_trajectory_data(filepath)
                if not batch or "observations" not in batch or len(batch["observations"]) == 0:
                    logger.warning(f"无法加载轨迹或轨迹为空: {filepath}")
                    self.processed_files.add(filepath)
                    continue

                # 更新统计信息
                self.stats["total_trajectories"] += 1
                self.stats["total_steps"] += len(batch["observations"])

                # 更新模型
                for _ in range(self.updates_per_trajectory):
                    self._update_model(batch)
                    self.stats["total_updates"] += 1

            # 标记为已处理
            self.processed_files.add(filepath)
            logger.info(f"已处理轨迹: {filepath}")

        # 计算更新速率
        elapsed_time = time.time() - self.stats["start_time"]
        if elapsed_time > 0:
            self.stats["updates_per_second"] = self.stats["total_updates"] / elapsed_time
            self.stats["trajectories_per_second"] = self.stats["total_trajectories"] / elapsed_time

        return self.stats

    def _find_new_trajectories(self) -> List[str]:
        """
        查找新的轨迹文件

        Returns:
            List[str]: 新的轨迹文件路径列表
        """
        # 获取所有轨迹文件
        pattern = os.path.join(self.trajectory_dir, self.file_pattern)
        all_files = glob.glob(pattern)

        # 过滤出未处理的文件
        new_files = [f for f in all_files if f not in self.processed_files]

        # 按修改时间排序
        new_files.sort(key=os.path.getmtime)

        return new_files

    def _update_model(self, batch: Union[Dict[str, Any], Batch]) -> None:
        """
        更新模型

        Args:
            batch: 训练批次
        """
        try:
            # 计算EWC惩罚项
            ewc_loss = 0.0
            if self.use_ewc and self.ewc is not None:
                try:
                    ewc_loss = self.ewc.penalty(self.model)
                    self.stats["ewc_loss"] = ewc_loss.item()
                    logger.debug(f"EWC惩罚项: {ewc_loss.item()}")
                except Exception as e:
                    logger.warning(f"计算EWC惩罚项时出错: {e}")
                    ewc_loss = 0.0

            # 计算L2正则化惩罚项
            l2_loss = 0.0
            if self.use_l2_reg and self.l2_reg is not None:
                try:
                    l2_loss = self.l2_reg.penalty_with_reference(self.model)
                    self.stats["l2_loss"] = l2_loss.item()
                    logger.debug(f"L2正则化惩罚项: {l2_loss.item()}")
                except Exception as e:
                    logger.warning(f"计算L2正则化惩罚项时出错: {e}")
                    l2_loss = 0.0

            # 检查模型是否有train方法
            if hasattr(self.model, 'train') and callable(self.model.train):
                # 如果batch是Batch对象，则直接传递
                if isinstance(batch, Batch):
                    # 提取人类反馈（如果有）
                    human_feedback_batch = self._extract_human_feedback(batch)

                    # 更新模型
                    if human_feedback_batch:
                        # 添加EWC和L2正则化惩罚项
                        if hasattr(self.model, 'add_regularization_loss'):
                            self.model.add_regularization_loss(ewc_loss + l2_loss)
                            self.model.train(batch, human_feedback_batch=human_feedback_batch)
                        else:
                            # 如果模型没有add_regularization_loss方法，则直接传递
                            self.model.train(batch, human_feedback_batch=human_feedback_batch)
                    else:
                        # 添加EWC和L2正则化惩罚项
                        if hasattr(self.model, 'add_regularization_loss'):
                            self.model.add_regularization_loss(ewc_loss + l2_loss)
                            self.model.train(batch)
                        else:
                            # 如果模型没有add_regularization_loss方法，则直接传递
                            self.model.train(batch)
                else:
                    # 如果batch是字典，则提取人类反馈（如果有）
                    human_feedback_batch = batch.get("human_feedback")

                    # 更新模型
                    if human_feedback_batch:
                        # 添加EWC和L2正则化惩罚项
                        if hasattr(self.model, 'add_regularization_loss'):
                            self.model.add_regularization_loss(ewc_loss + l2_loss)
                            self.model.train(batch, human_feedback_batch=human_feedback_batch)
                        else:
                            # 如果模型没有add_regularization_loss方法，则直接传递
                            self.model.train(batch, human_feedback_batch=human_feedback_batch)
                    else:
                        # 添加EWC和L2正则化惩罚项
                        if hasattr(self.model, 'add_regularization_loss'):
                            self.model.add_regularization_loss(ewc_loss + l2_loss)
                            self.model.train(batch)
                        else:
                            # 如果模型没有add_regularization_loss方法，则直接传递
                            self.model.train(batch)

            # 检查模型是否有update方法
            elif hasattr(self.model, 'update') and callable(self.model.update):
                # 如果batch是Batch对象，则直接传递
                if isinstance(batch, Batch):
                    # 提取人类反馈（如果有）
                    human_feedback_batch = self._extract_human_feedback(batch)

                    # 更新模型
                    if human_feedback_batch:
                        # 添加EWC和L2正则化惩罚项
                        if hasattr(self.model, 'add_regularization_loss'):
                            self.model.add_regularization_loss(ewc_loss + l2_loss)
                            self.model.update(batch, human_feedback_samples=human_feedback_batch)
                        else:
                            # 如果模型没有add_regularization_loss方法，则直接传递
                            self.model.update(batch, human_feedback_samples=human_feedback_batch)
                    else:
                        # 添加EWC和L2正则化惩罚项
                        if hasattr(self.model, 'add_regularization_loss'):
                            self.model.add_regularization_loss(ewc_loss + l2_loss)
                            self.model.update(batch)
                        else:
                            # 如果模型没有add_regularization_loss方法，则直接传递
                            self.model.update(batch)
                else:
                    # 如果batch是字典，则需要转换为Experience或Batch对象
                    experiences = self._convert_batch_to_experiences(batch)
                    batch_obj = Batch(experiences)

                    # 提取人类反馈（如果有）
                    human_feedback_batch = batch.get("human_feedback")

                    # 更新模型
                    if human_feedback_batch:
                        # 添加EWC和L2正则化惩罚项
                        if hasattr(self.model, 'add_regularization_loss'):
                            self.model.add_regularization_loss(ewc_loss + l2_loss)
                            self.model.update(batch_obj, human_feedback_samples=human_feedback_batch)
                        else:
                            # 如果模型没有add_regularization_loss方法，则直接传递
                            self.model.update(batch_obj, human_feedback_samples=human_feedback_batch)
                    else:
                        # 添加EWC和L2正则化惩罚项
                        if hasattr(self.model, 'add_regularization_loss'):
                            self.model.add_regularization_loss(ewc_loss + l2_loss)
                            self.model.update(batch_obj)
                        else:
                            # 如果模型没有add_regularization_loss方法，则直接传递
                            self.model.update(batch_obj)
            else:
                logger.warning(f"模型没有train或update方法: {type(self.model)}")

            # 策略蒸馏更新
            if self.use_policy_distillation and self.policy_distillation is not None:
                try:
                    # 构建状态张量
                    if isinstance(batch, Batch):
                        states = torch.stack([exp.state for exp in batch.experiences]).to(self.model.device)
                    elif isinstance(batch, dict) and "states" in batch:
                        states = torch.tensor(batch["states"], device=self.model.device)
                    else:
                        states = None
                    if states is not None:
                        distill_stats = self.policy_distillation.update(states)
                        self.stats["distillation_loss"] = distill_stats.get("distillation_loss", 0.0)
                        logger.debug(f"策略蒸馏损失: {distill_stats.get('distillation_loss')}")
                except Exception as e:
                    logger.warning(f"策略蒸馏更新失败: {e}")

        except Exception as e:
            logger.error(f"更新模型时出错: {e}")

    def _extract_human_feedback(self, batch: Batch) -> Optional[Dict[str, Any]]:
        """
        从Batch对象中提取人类反馈

        Args:
            batch: Batch对象

        Returns:
            Optional[Dict[str, Any]]: 人类反馈数据，如果没有则返回None
        """
        # 检查是否有人类反馈
        has_feedback = False
        for exp in batch.experiences:
            if exp.info and "human_feedback" in exp.info:
                has_feedback = True
                break

        if not has_feedback:
            return None

        # 提取人类反馈
        feedback_batch = {
            "states": [],
            "actions": [],
            "feedback_scores": [],
            "preferences": []
        }

        for exp in batch.experiences:
            if exp.info and "human_feedback" in exp.info:
                feedback = exp.info["human_feedback"]

                # 添加状态和动作
                feedback_batch["states"].append(exp.state)
                feedback_batch["actions"].append(exp.action)

                # 添加反馈评分（如果有）
                if "score" in feedback:
                    feedback_batch["feedback_scores"].append(feedback["score"])

                # 添加偏好（如果有）
                if "preference" in feedback:
                    feedback_batch["preferences"].append(feedback["preference"])

        return feedback_batch

    def _convert_batch_to_experiences(self, batch: Dict[str, Any]) -> List[Experience]:
        """
        将批次数据转换为Experience对象列表

        Args:
            batch: 批次数据

        Returns:
            List[Experience]: Experience对象列表
        """
        experiences = []

        observations = batch.get("observations", [])
        actions = batch.get("actions", [])
        rewards = batch.get("rewards", [])
        next_observations = batch.get("next_observations", [])
        dones = batch.get("dones", [])
        infos = batch.get("infos", [])

        # 确保所有列表长度一致
        min_length = min(
            len(observations),
            len(actions),
            len(rewards),
            len(next_observations),
            len(dones),
            len(infos)
        )

        for i in range(min_length):
            exp = Experience(
                state=observations[i],
                action=actions[i],
                reward=rewards[i],
                next_state=next_observations[i],
                done=dones[i],
                info=infos[i]
            )
            experiences.append(exp)

        return experiences


def run_online_updater(
    model: Any,
    trajectory_dir: str = "data/trajectories",
    update_interval: int = 60,
    max_runtime: Optional[int] = None,
    stop_condition: Optional[Callable[[], bool]] = None
) -> None:
    """
    运行在线更新器

    Args:
        model: 要更新的模型
        trajectory_dir: 轨迹目录路径
        update_interval: 更新间隔（秒）
        max_runtime: 最大运行时间（秒），如果为None则一直运行
        stop_condition: 停止条件，如果为None则使用max_runtime
    """
    # 创建在线更新器
    updater = OnlineUpdater(
        model=model,
        trajectory_dir=trajectory_dir,
        update_interval=update_interval
    )

    # 记录开始时间
    start_time = time.time()

    logger.info(f"开始运行在线更新器: {trajectory_dir}")

    try:
        while True:
            # 检查是否达到最大运行时间
            if max_runtime is not None and time.time() - start_time > max_runtime:
                logger.info(f"达到最大运行时间: {max_runtime}秒")
                break

            # 检查是否满足停止条件
            if stop_condition is not None and stop_condition():
                logger.info("满足停止条件")
                break

            # 检查并更新模型
            stats = updater.check_and_update()

            # 输出统计信息
            if stats["total_updates"] > 0:
                logger.info(f"更新统计: 总更新次数={stats['total_updates']}, "
                           f"总轨迹数={stats['total_trajectories']}, "
                           f"总步骤数={stats['total_steps']}")

            # 等待下一次更新
            time.sleep(1)

    except KeyboardInterrupt:
        logger.info("用户中断")

    except Exception as e:
        logger.error(f"运行在线更新器时出错: {e}")

    finally:
        # 输出最终统计信息
        stats = updater.stats
        elapsed_time = time.time() - start_time

        logger.info(f"在线更新器运行结束")
        logger.info(f"运行时间: {elapsed_time:.2f}秒")
        logger.info(f"总更新次数: {stats['total_updates']}")
        logger.info(f"总轨迹数: {stats['total_trajectories']}")
        logger.info(f"总步骤数: {stats['total_steps']}")

        if elapsed_time > 0:
            logger.info(f"更新速率: {stats['total_updates'] / elapsed_time:.2f}次/秒")
            logger.info(f"轨迹处理速率: {stats['total_trajectories'] / elapsed_time:.2f}个/秒")
