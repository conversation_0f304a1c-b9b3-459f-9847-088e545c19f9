<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI棋牌强化学习框架 - 斗地主</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light" data-theme="{{ theme }}">
    <div class="container py-5">
        <header class="text-center mb-5">
            <h1 class="display-4 fw-bold">AI棋牌强化学习框架</h1>
            <p class="lead">与先进的AI代理进行斗地主对战</p>
        </header>

        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h3 class="card-title mb-0">开始新游戏</h3>
                    </div>
                    <div class="card-body">
                        <form id="game-form">
                            <div class="mb-4">
                                <label for="game-type" class="form-label">游戏类型</label>
                                <select class="form-select" id="game-type" name="game_type">
                                    <option value="doudizhu_classic" selected>斗地主经典版</option>
                                    <option value="doudizhu_2v1">斗地主二打一版</option>
                                </select>
                            </div>

                            <div class="mb-4">
                                <label for="player-role" class="form-label">你想扮演的角色</label>
                                <select class="form-select" id="player-role" name="player_role">
                                    <option value="random" selected>随机</option>
                                    <option value="landlord">地主</option>
                                    <option value="farmer">农民</option>
                                </select>
                            </div>

                            <div class="mb-4">
                                <label for="ai-model" class="form-label">AI模型</label>
                                <select class="form-select" id="ai-model" name="ai_model">
                                    {% for model in available_models %}
                                    <option value="{{ model }}" {% if model == "random" %}selected{% endif %}>
                                        {{ model|capitalize }}
                                    </option>
                                    {% endfor %}
                                </select>
                                <div class="form-text">
                                    <ul class="mt-2 ps-3">
                                        <li><strong>Random</strong>: 随机选择合法动作</li>
                                        <li><strong>DQN</strong>: 深度Q网络，基础强化学习算法</li>
                                        <li><strong>PPO</strong>: 近端策略优化，稳定的策略梯度方法</li>
                                        <li><strong>MuZero</strong>: 先进的模型驱动强化学习算法</li>
                                        <li><strong>Transformer-RL</strong>: 基于Transformer的强化学习算法，善于处理长期依赖关系</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">开始游戏</button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card shadow mt-4">
                    <div class="card-header bg-secondary text-white">
                        <h3 class="card-title mb-0">界面设置</h3>
                    </div>
                    <div class="card-body">
                        <form id="ui-config-form">
                            <div class="mb-3">
                                <label for="theme" class="form-label">主题</label>
                                <select class="form-select" id="theme" name="theme">
                                    <option value="default" {% if theme == "default" %}selected{% endif %}>默认</option>
                                    <option value="dark" {% if theme == "dark" %}selected{% endif %}>暗黑</option>
                                    <option value="light" {% if theme == "light" %}selected{% endif %}>明亮</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="card-style" class="form-label">卡牌样式</label>
                                <select class="form-select" id="card-style" name="card_style">
                                    <option value="standard">标准</option>
                                    <option value="simple">简约</option>
                                    <option value="classic">经典</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="animation-speed" class="form-label">动画速度</label>
                                <select class="form-select" id="animation-speed" name="animation_speed">
                                    <option value="fast">快速</option>
                                    <option value="normal" selected>正常</option>
                                    <option value="slow">慢速</option>
                                </select>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="sound-enabled" name="sound_enabled" checked>
                                <label class="form-check-label" for="sound-enabled">启用声音</label>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-secondary">保存设置</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <footer class="mt-5 text-center text-muted">
            <p>AI棋牌强化学习框架 &copy; 2025</p>
        </footer>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/index.js') }}"></script>
</body>
</html>