/**
 * AI棋牌强化学习框架 - 决策解释可视化
 * 
 * 提供信任度相关的可视化功能，展示AI决策过程的解释信息。
 */

// 全局变量，存储最新的解释数据
let lastExplanationData = null;

/**
 * 初始化可视化组件
 */
function initVisualization() {
    // 创建可视化容器（如果不存在）
    if (!document.getElementById('ai-explanation-panel')) {
        createVisualizationPanel();
    }
    
    // 绑定事件
    bindVisualizationEvents();
}

/**
 * 创建可视化面板
 */
function createVisualizationPanel() {
    const panel = document.createElement('div');
    panel.id = 'ai-explanation-panel';
    panel.className = 'explanation-panel';
    
    // 面板标题
    panel.innerHTML = `
        <div class="explanation-header">
            <h5>AI决策解释</h5>
            <button id="toggle-explanation-btn" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-chevron-up"></i>
            </button>
        </div>
        <div class="explanation-content">
            <div class="row">
                <div class="col-md-6">
                    <div class="card mb-2">
                        <div class="card-header">
                            <h6 class="mb-0">AI信心评分</h6>
                        </div>
                        <div class="card-body">
                            <div id="confidence-score" class="confidence-score">
                                <div class="progress">
                                    <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                                </div>
                                <div class="score-value">0%</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card mb-2">
                        <div class="card-header">
                            <h6 class="mb-0">决策组件</h6>
                        </div>
                        <div class="card-body">
                            <div id="component-usage" class="component-usage">
                                <span class="badge bg-primary">未知</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-2">
                <div class="card-header">
                    <h6 class="mb-0">MCTS搜索路径</h6>
                </div>
                <div class="card-body">
                    <div id="mcts-explanation" class="mcts-explanation">
                        <p class="text-muted">暂无数据</p>
                    </div>
                </div>
            </div>
            
            <div class="card mb-2">
                <div class="card-header">
                    <h6 class="mb-0">神经网络输出</h6>
                </div>
                <div class="card-body">
                    <div id="network-output" class="network-output">
                        <p class="text-muted">暂无数据</p>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // 添加到页面
    document.body.appendChild(panel);
    
    // 添加样式
    addVisualizationStyles();
}

/**
 * 添加可视化样式
 */
function addVisualizationStyles() {
    const style = document.createElement('style');
    style.textContent = `
        .explanation-panel {
            position: fixed;
            bottom: 0;
            right: 20px;
            width: 400px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 5px 5px 0 0;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
            transition: height 0.3s ease;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .explanation-header {
            padding: 10px 15px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
        }
        
        .explanation-content {
            padding: 15px;
            overflow-y: auto;
            max-height: calc(80vh - 50px);
        }
        
        .explanation-panel.collapsed .explanation-content {
            display: none;
        }
        
        .confidence-score {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .score-value {
            font-weight: bold;
            text-align: center;
        }
        
        .mcts-explanation {
            font-size: 0.9rem;
        }
        
        .network-output {
            font-size: 0.9rem;
        }
        
        .component-usage {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }
        
        /* 信心评分颜色 */
        .confidence-low { color: #dc3545; }
        .confidence-medium { color: #ffc107; }
        .confidence-high { color: #28a745; }
    `;
    
    document.head.appendChild(style);
}

/**
 * 绑定可视化事件
 */
function bindVisualizationEvents() {
    // 切换面板展开/折叠
    document.addEventListener('click', function(e) {
        if (e.target.id === 'toggle-explanation-btn' || e.target.closest('#toggle-explanation-btn')) {
            toggleExplanationPanel();
        } else if (e.target.closest('.explanation-header')) {
            toggleExplanationPanel();
        }
    });
}

/**
 * 切换解释面板的展开/折叠状态
 */
function toggleExplanationPanel() {
    const panel = document.getElementById('ai-explanation-panel');
    panel.classList.toggle('collapsed');
    
    const btn = document.getElementById('toggle-explanation-btn');
    if (panel.classList.contains('collapsed')) {
        btn.innerHTML = '<i class="fas fa-chevron-down"></i>';
    } else {
        btn.innerHTML = '<i class="fas fa-chevron-up"></i>';
    }
}

/**
 * 更新可视化数据
 * @param {Object} explanationData - 解释数据
 */
function updateVisualization(explanationData) {
    if (!explanationData) return;
    
    // 保存最新的解释数据
    lastExplanationData = explanationData;
    
    // 更新各个可视化组件
    updateConfidenceScore(explanationData.confidence || 0.5);
    updateComponentUsage(explanationData.component_used || 'Unknown');
    
    // 更新MCTS解释（如果有）
    if (explanationData.mcts) {
        displayMctsExplanation(explanationData.mcts);
    }
    
    // 更新神经网络输出（如果有）
    if (explanationData.network) {
        displayNetworkOutput(explanationData.network);
    }
}

/**
 * 显示MCTS搜索路径解释
 * @param {Object} mctsData - MCTS解释数据
 */
function displayMctsExplanation(mctsData) {
    const container = document.getElementById('mcts-explanation');
    if (!container) return;
    
    container.innerHTML = ''; // 清空之前的内容
    
    if (!mctsData || !mctsData.root_info) {
        container.innerHTML = '<p class="text-muted">暂无MCTS数据</p>';
        return;
    }
    
    // 显示根节点信息
    let html = `<div class="root-info">
        <strong>根节点:</strong> 访问次数 ${mctsData.root_info.visit_count}, 
        价值 ${mctsData.root_info.value.toFixed(3)}
    </div>`;
    
    // 显示主要变化路径
    if (mctsData.principal_variation && mctsData.principal_variation.length > 0) {
        html += '<div class="principal-variation mt-2">';
        html += '<strong>主要变化路径:</strong>';
        
        mctsData.principal_variation.forEach((step, index) => {
            html += `<div class="variation-step ms-2">
                <strong>步骤 ${index + 1}:</strong> 
                动作 ${step.action || '(根节点)'}
            </div>`;
            
            // 显示子节点信息
            if (step.node_info) {
                html += `<div class="node-info ms-3">
                    访问次数: ${step.node_info.visit_count}, 
                    价值: ${step.node_info.value.toFixed(3)}
                </div>`;
            }
        });
        
        html += '</div>';
    }
    
    // 显示顶级动作
    if (mctsData.top_actions && mctsData.top_actions.length > 0) {
        html += '<div class="top-actions mt-2">';
        html += '<strong>顶级动作:</strong>';
        
        mctsData.top_actions.forEach((action, index) => {
            html += `<div class="action-info ms-2">
                <strong>${index + 1}.</strong> 
                动作 ${action.action}, 
                访问次数: ${action.visit_count}, 
                价值: ${action.value.toFixed(3)}, 
                概率: ${(action.policy_prob * 100).toFixed(1)}%
            </div>`;
        });
        
        html += '</div>';
    }
    
    container.innerHTML = html;
}

/**
 * 显示神经网络输出
 * @param {Object} networkData - 神经网络输出数据
 */
function displayNetworkOutput(networkData) {
    const container = document.getElementById('network-output');
    if (!container) return;
    
    container.innerHTML = ''; // 清空之前的内容
    
    if (!networkData || !networkData.network_output) {
        container.innerHTML = '<p class="text-muted">暂无神经网络数据</p>';
        return;
    }
    
    const output = networkData.network_output;
    
    // 显示价值预测
    let html = `<div>
        <strong>状态价值:</strong> ${output.value.toFixed(3)}
    </div>`;
    
    // 显示顶级动作
    if (output.top_actions && output.top_actions.length > 0) {
        html += '<div class="mt-2">';
        html += '<strong>顶级动作预测:</strong>';
        
        output.top_actions.forEach((action, index) => {
            html += `<div class="ms-2">
                <strong>${index + 1}.</strong> 
                动作 ${action.action}, 
                概率: ${(action.probability * 100).toFixed(1)}%
            </div>`;
        });
        
        html += '</div>';
    }
    
    // 显示特征维度（如果有）
    if (networkData.feature_dims) {
        html += '<div class="mt-2 small text-muted">';
        html += '<strong>特征维度:</strong> ';
        
        for (const [key, value] of Object.entries(networkData.feature_dims)) {
            html += `${key}: ${JSON.stringify(value)}, `;
        }
        
        html = html.slice(0, -2); // 移除最后的逗号和空格
        html += '</div>';
    }
    
    container.innerHTML = html;
}

/**
 * 更新信心评分
 * @param {number} score - 信心评分 (0-1)
 */
function updateConfidenceScore(score) {
    const element = document.getElementById('confidence-score');
    if (!element) return;
    
    // 更新进度条
    const progressBar = element.querySelector('.progress-bar');
    const scoreValue = element.querySelector('.score-value');
    
    const percentage = Math.round(score * 100);
    progressBar.style.width = `${percentage}%`;
    scoreValue.textContent = `${percentage}%`;
    
    // 根据分数设置颜色
    progressBar.className = 'progress-bar';
    if (score < 0.4) {
        progressBar.classList.add('bg-danger');
        scoreValue.className = 'score-value confidence-low';
    } else if (score < 0.7) {
        progressBar.classList.add('bg-warning');
        scoreValue.className = 'score-value confidence-medium';
    } else {
        progressBar.classList.add('bg-success');
        scoreValue.className = 'score-value confidence-high';
    }
}

/**
 * 更新组件使用情况
 * @param {string} componentName - 组件名称
 */
function updateComponentUsage(componentName) {
    const element = document.getElementById('component-usage');
    if (!element) return;
    
    // 清空之前的内容
    element.innerHTML = '';
    
    // 创建徽章
    let badgeClass = 'bg-primary';
    
    // 根据组件类型设置不同的颜色
    if (componentName.toLowerCase().includes('mcts')) {
        badgeClass = 'bg-success';
    } else if (componentName.toLowerCase().includes('network') || 
               componentName.toLowerCase().includes('neural')) {
        badgeClass = 'bg-info';
    } else if (componentName.toLowerCase().includes('rule')) {
        badgeClass = 'bg-warning';
    } else if (componentName.toLowerCase().includes('hybrid')) {
        badgeClass = 'bg-purple';
    }
    
    element.innerHTML = `<span class="badge ${badgeClass}">${componentName}</span>`;
}

// 导出函数
window.AIVisualization = {
    init: initVisualization,
    update: updateVisualization,
    displayMctsExplanation: displayMctsExplanation,
    displayNetworkOutput: displayNetworkOutput,
    updateConfidenceScore: updateConfidenceScore,
    updateComponentUsage: updateComponentUsage
};
