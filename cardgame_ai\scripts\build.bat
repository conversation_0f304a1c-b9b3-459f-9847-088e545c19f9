@echo off
REM 打包脚本（Windows版）

REM 激活虚拟环境
call venv\Scripts\activate

REM 设置环境变量
set PYTHONPATH=%PYTHONPATH%;%CD%

REM 解析命令行参数
set VERSION=
set CLEAN=
set NO_BUILD=
set NO_PACKAGE=
set RELEASE_NOTES=

:parse_args
if "%~1"=="" goto run_build
if /i "%~1"=="--version" (
    set VERSION=--version %~2
    shift
    shift
    goto parse_args
)
if /i "%~1"=="--clean" (
    set CLEAN=--clean
    shift
    goto parse_args
)
if /i "%~1"=="--no-build" (
    set NO_BUILD=--no-build
    shift
    goto parse_args
)
if /i "%~1"=="--no-package" (
    set NO_PACKAGE=--no-package
    shift
    goto parse_args
)
if /i "%~1"=="--release-notes" (
    set RELEASE_NOTES=--release-notes
    shift
    goto parse_args
)
shift
goto parse_args

:run_build
echo 运行打包脚本...

REM 运行打包脚本
python scripts/build.py %VERSION% %CLEAN% %NO_BUILD% %NO_PACKAGE% %RELEASE_NOTES%

REM 检查打包结果
if %ERRORLEVEL% EQU 0 (
    echo 打包成功完成！
) else (
    echo 打包失败，错误代码: %ERRORLEVEL%
)

REM 退出虚拟环境
call deactivate

exit /b %ERRORLEVEL%
