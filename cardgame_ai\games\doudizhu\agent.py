#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
斗地主代理模块

定义斗地主游戏的代理类。
"""

from typing import List, Optional, Dict, Any, Union
import random
import numpy as np

from cardgame_ai.core.agent import Agent
from cardgame_ai.games.doudizhu.card import Card
from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType

class DouDizhuAgent(Agent):
    """斗地主代理基类"""
    
    def __init__(self, name: str):
        """初始化代理
        
        Args:
            name: 代理名称
        """
        self.name = name
    
    def act(self, state: Dict[str, Any], valid_actions: Optional[List[Any]] = None) -> Any:
        """选择动作
        
        Args:
            state: 游戏状态
            valid_actions: 有效动作列表
            
        Returns:
            选择的动作
        """
        raise NotImplementedError("子类必须实现act方法")
    
    def observe(self, state: Dict[str, Any], action: Any, reward: float, next_state: Dict[str, Any], done: bool) -> None:
        """观察环境变化
        
        Args:
            state: 当前状态
            action: 执行的动作
            reward: 获得的奖励
            next_state: 下一个状态
            done: 是否结束
        """
        pass
    
    def save(self, path: str) -> None:
        """保存代理
        
        Args:
            path: 保存路径
        """
        pass
    
    def load(self, path: str) -> None:
        """加载代理
        
        Args:
            path: 加载路径
        """
        pass

class DouDizhuRandomAgent(DouDizhuAgent):
    """斗地主随机代理"""
    
    def act(self, state: Dict[str, Any], valid_actions: Optional[List[Any]] = None) -> Any:
        """随机选择动作
        
        Args:
            state: 游戏状态
            valid_actions: 有效动作列表
            
        Returns:
            选择的动作
        """
        if not valid_actions:
            return None
        
        return random.choice(valid_actions)
