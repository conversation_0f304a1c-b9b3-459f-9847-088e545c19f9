"""
界面配置模块

提供人机交互界面的配置管理功能。
"""
import os
import json
import yaml
from typing import Dict, Any, Optional


class InterfaceConfig:
    """
    界面配置类

    管理人机交互界面的配置参数。
    """

    def __init__(self, config_data: Optional[Dict[str, Any]] = None):
        """
        初始化界面配置

        Args:
            config_data (Optional[Dict[str, Any]], optional): 初始配置数据. Defaults to None.
        """
        # 默认配置
        self._default_config = {
            "server": {
                "host": "0.0.0.0",
                "port": 5000,
                "debug": False,
                "secret_key": "cardgame_ai_secret",
                "session_lifetime": 3600  # 会话生存时间（秒）
            },
            "game": {
                "max_players": 3,
                "ai_thinking_time": 1.0,  # AI思考时间（秒）
                "human_timeout": 60.0,    # 人类玩家超时时间（秒）
                "default_ai_model": "random",  # 默认AI模型
                "available_models": ["random", "dqn", "ppo", "muzero", "efficientzero", "transformer-rl", "mcts"]  # 可用的AI模型
            },
            "ui": {
                "theme": "default",
                "card_style": "standard",
                "animation_speed": "normal",
                "sound_enabled": True,
                "language": "zh_CN"
            },
            "data_collection": {
                "enabled": True,
                "save_games": True,
                "save_feedback": True,
                "data_dir": "data/human_games",
                "trajectory": {
                    "enabled": True,
                    "format": "json",
                    "auto_save": True,
                    "save_interval": 5,
                    "compress": False,
                    "save_dir": "data/trajectories"
                }
            }
        }

        # 当前配置
        self._config = self._default_config.copy()

        # 如果提供了配置数据，则更新配置
        if config_data:
            self.update_config(config_data)

    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置项

        Args:
            key (str): 配置项的键，支持点分隔的路径
            default (Any, optional): 默认值. Defaults to None.

        Returns:
            Any: 配置项的值，如果不存在则返回默认值
        """
        keys = key.split('.')
        config = self._config

        for k in keys[:-1]:
            if k not in config:
                return default
            config = config[k]

        if keys[-1] not in config:
            return default

        return config[keys[-1]]

    def set(self, key: str, value: Any) -> None:
        """
        设置配置项

        Args:
            key (str): 配置项的键，支持点分隔的路径
            value (Any): 配置项的值
        """
        keys = key.split('.')
        config = self._config

        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]

        config[keys[-1]] = value

    def update_config(self, config_data: Dict[str, Any]) -> None:
        """
        更新配置

        Args:
            config_data (Dict[str, Any]): 新的配置数据
        """
        self._recursive_update(self._config, config_data)

    def _recursive_update(self, d1: Dict[str, Any], d2: Dict[str, Any]) -> None:
        """
        递归更新字典

        Args:
            d1 (Dict[str, Any]): 待更新的字典
            d2 (Dict[str, Any]): 新的数据
        """
        for k, v in d2.items():
            if k in d1 and isinstance(d1[k], dict) and isinstance(v, dict):
                self._recursive_update(d1[k], v)
            else:
                d1[k] = v

    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典

        Returns:
            Dict[str, Any]: 配置字典
        """
        return self._config.copy()

    def save(self, path: str, format: str = 'json') -> None:
        """
        保存配置到文件

        Args:
            path (str): 文件路径
            format (str, optional): 文件格式，支持'json'和'yaml'. Defaults to 'json'.

        Raises:
            ValueError: 如果文件格式不支持
        """
        # 创建目录
        os.makedirs(os.path.dirname(os.path.abspath(path)), exist_ok=True)

        # 保存配置
        if format.lower() == 'json':
            with open(path, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=4, ensure_ascii=False)
        elif format.lower() == 'yaml':
            with open(path, 'w', encoding='utf-8') as f:
                yaml.dump(self._config, f, default_flow_style=False, allow_unicode=True)
        else:
            raise ValueError(f"不支持的文件格式：{format}")

    @classmethod
    def load(cls, path: str) -> 'InterfaceConfig':
        """
        从文件加载配置

        Args:
            path (str): 文件路径

        Returns:
            InterfaceConfig: 界面配置对象

        Raises:
            FileNotFoundError: 如果文件不存在
            ValueError: 如果文件格式不支持
        """
        if not os.path.exists(path):
            raise FileNotFoundError(f"配置文件不存在：{path}")

        ext = os.path.splitext(path)[1].lower()

        if ext == '.json':
            with open(path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
        elif ext in ['.yml', '.yaml']:
            with open(path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
        else:
            raise ValueError(f"不支持的文件格式：{ext}")

        return cls(config_data)
