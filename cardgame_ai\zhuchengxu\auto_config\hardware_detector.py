"""
硬件检测模块

支持Windows和Ubuntu系统的硬件自动检测，
包括GPU、CPU、内存等关键硬件信息。
"""

import json
import re
import psutil
from typing import Dict, List, Optional, Any
from .utils import CrossPlatformUtils

class HardwareDetector:
    """硬件检测器"""
    
    def __init__(self):
        self.logger = CrossPlatformUtils.setup_logging()
        self.utils = CrossPlatformUtils()
    
    def detect_all_hardware(self) -> Dict[str, Any]:
        """
        检测所有硬件信息
        
        Returns:
            包含GPU、CPU、内存、存储等信息的字典
        """
        self.logger.info("开始硬件检测...")
        
        hardware_info = {
            'system': self.utils.get_system_info(),
            'gpu': self.detect_gpu_specs(),
            'cpu': self.detect_cpu_specs(),
            'memory': self.detect_memory_specs(),
            'storage': self.detect_storage_specs(),
            'network': self.detect_network_specs()
        }
        
        self.logger.info("硬件检测完成")
        return hardware_info
    
    def detect_gpu_specs(self) -> List[Dict[str, Any]]:
        """
        检测GPU规格信息
        
        Returns:
            GPU信息列表，每个GPU包含型号、显存等信息
        """
        gpu_specs = []
        
        try:
            # 首先尝试使用nvidia-ml-py (推荐方式)
            gpu_specs = self._detect_gpu_with_nvml()
            if gpu_specs:
                self.logger.info(f"通过NVML检测到 {len(gpu_specs)} 个GPU")
                return gpu_specs
        except Exception as e:
            self.logger.warning(f"NVML检测失败: {e}")
        
        try:
            # 备用方案：使用nvidia-smi命令
            gpu_specs = self._detect_gpu_with_nvidia_smi()
            if gpu_specs:
                self.logger.info(f"通过nvidia-smi检测到 {len(gpu_specs)} 个GPU")
                return gpu_specs
        except Exception as e:
            self.logger.warning(f"nvidia-smi检测失败: {e}")
        
        try:
            # 最后尝试PyTorch检测
            gpu_specs = self._detect_gpu_with_pytorch()
            if gpu_specs:
                self.logger.info(f"通过PyTorch检测到 {len(gpu_specs)} 个GPU")
                return gpu_specs
        except Exception as e:
            self.logger.warning(f"PyTorch检测失败: {e}")
        
        self.logger.warning("未检测到GPU或GPU检测失败")
        return []
    
    def _detect_gpu_with_nvml(self) -> List[Dict[str, Any]]:
        """使用NVIDIA ML库检测GPU"""
        try:
            import pynvml
            pynvml.nvmlInit()
            
            gpu_count = pynvml.nvmlDeviceGetCount()
            gpu_specs = []
            
            for i in range(gpu_count):
                handle = pynvml.nvmlDeviceGetHandleByIndex(i)
                
                # 获取GPU名称
                name = pynvml.nvmlDeviceGetName(handle)
                if isinstance(name, bytes):
                    name = name.decode('utf-8')
                
                # 获取内存信息
                memory_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
                
                # 获取其他信息
                try:
                    power_limit = pynvml.nvmlDeviceGetPowerManagementLimitConstraints(handle)[1] // 1000
                except:
                    power_limit = None
                
                try:
                    temperature = pynvml.nvmlDeviceGetTemperature(handle, pynvml.NVML_TEMPERATURE_GPU)
                except:
                    temperature = None
                
                gpu_specs.append({
                    'index': i,
                    'name': name,
                    'memory_total_gb': round(memory_info.total / (1024**3), 2),
                    'memory_free_gb': round(memory_info.free / (1024**3), 2),
                    'memory_used_gb': round(memory_info.used / (1024**3), 2),
                    'power_limit_w': power_limit,
                    'temperature_c': temperature,
                    'detection_method': 'nvml'
                })
            
            pynvml.nvmlShutdown()
            return gpu_specs
            
        except ImportError:
            raise Exception("pynvml库未安装，请安装: pip install nvidia-ml-py")
        except Exception as e:
            raise Exception(f"NVML检测失败: {e}")
    
    def _detect_gpu_with_nvidia_smi(self) -> List[Dict[str, Any]]:
        """使用nvidia-smi命令检测GPU"""
        nvidia_smi = self.utils.get_nvidia_smi_command()
        
        # 执行nvidia-smi查询命令
        command = f'{nvidia_smi} --query-gpu=index,name,memory.total,memory.free,memory.used,power.limit,temperature.gpu --format=csv,noheader,nounits'
        
        result = self.utils.execute_command(command)
        
        if not result['success']:
            raise Exception(f"nvidia-smi执行失败: {result['stderr']}")
        
        gpu_specs = []
        for line in result['stdout'].split('\n'):
            if line.strip():
                parts = [part.strip() for part in line.split(',')]
                if len(parts) >= 7:
                    try:
                        gpu_specs.append({
                            'index': int(parts[0]),
                            'name': parts[1],
                            'memory_total_gb': round(float(parts[2]) / 1024, 2),
                            'memory_free_gb': round(float(parts[3]) / 1024, 2),
                            'memory_used_gb': round(float(parts[4]) / 1024, 2),
                            'power_limit_w': float(parts[5]) if parts[5] != '[Not Supported]' else None,
                            'temperature_c': float(parts[6]) if parts[6] != '[Not Supported]' else None,
                            'detection_method': 'nvidia-smi'
                        })
                    except (ValueError, IndexError) as e:
                        self.logger.warning(f"解析GPU信息失败: {line}, 错误: {e}")
        
        return gpu_specs
    
    def _detect_gpu_with_pytorch(self) -> List[Dict[str, Any]]:
        """使用PyTorch检测GPU"""
        try:
            import torch
            
            if not torch.cuda.is_available():
                return []
            
            gpu_count = torch.cuda.device_count()
            gpu_specs = []
            
            for i in range(gpu_count):
                props = torch.cuda.get_device_properties(i)
                
                # 获取内存信息
                torch.cuda.set_device(i)
                memory_total = torch.cuda.get_device_properties(i).total_memory
                memory_allocated = torch.cuda.memory_allocated(i)
                memory_free = memory_total - memory_allocated
                
                gpu_specs.append({
                    'index': i,
                    'name': props.name,
                    'memory_total_gb': round(memory_total / (1024**3), 2),
                    'memory_free_gb': round(memory_free / (1024**3), 2),
                    'memory_used_gb': round(memory_allocated / (1024**3), 2),
                    'compute_capability': f"{props.major}.{props.minor}",
                    'multiprocessor_count': props.multi_processor_count,
                    'detection_method': 'pytorch'
                })
            
            return gpu_specs
            
        except ImportError:
            raise Exception("PyTorch未安装")
        except Exception as e:
            raise Exception(f"PyTorch GPU检测失败: {e}")
    
    def detect_cpu_specs(self) -> Dict[str, Any]:
        """检测CPU规格信息"""
        try:
            cpu_info = {
                'physical_cores': psutil.cpu_count(logical=False),
                'logical_cores': psutil.cpu_count(logical=True),
                'architecture': self.utils.get_system_info()['machine'],
                'processor': self.utils.get_system_info()['processor']
            }
            
            # 获取CPU频率信息
            try:
                freq = psutil.cpu_freq()
                if freq:
                    cpu_info.update({
                        'frequency_current_mhz': round(freq.current, 2),
                        'frequency_min_mhz': round(freq.min, 2) if freq.min else None,
                        'frequency_max_mhz': round(freq.max, 2) if freq.max else None
                    })
            except:
                pass
            
            # 获取CPU使用率
            try:
                cpu_info['usage_percent'] = psutil.cpu_percent(interval=1)
            except:
                pass
            
            self.logger.info(f"检测到CPU: {cpu_info['physical_cores']}核心/{cpu_info['logical_cores']}线程")
            return cpu_info
            
        except Exception as e:
            self.logger.error(f"CPU检测失败: {e}")
            return {}

    def detect_memory_specs(self) -> Dict[str, Any]:
        """检测内存规格信息"""
        try:
            memory = psutil.virtual_memory()
            swap = psutil.swap_memory()

            memory_info = {
                'total_gb': round(memory.total / (1024**3), 2),
                'available_gb': round(memory.available / (1024**3), 2),
                'used_gb': round(memory.used / (1024**3), 2),
                'free_gb': round(memory.free / (1024**3), 2),
                'usage_percent': memory.percent,
                'swap_total_gb': round(swap.total / (1024**3), 2),
                'swap_used_gb': round(swap.used / (1024**3), 2),
                'swap_free_gb': round(swap.free / (1024**3), 2)
            }

            self.logger.info(f"检测到内存: {memory_info['total_gb']}GB总量, {memory_info['available_gb']}GB可用")
            return memory_info

        except Exception as e:
            self.logger.error(f"内存检测失败: {e}")
            return {}

    def detect_storage_specs(self) -> Dict[str, Any]:
        """检测存储规格信息"""
        try:
            storage_info = {
                'disks': []
            }

            # 获取磁盘分区信息
            partitions = psutil.disk_partitions()

            for partition in partitions:
                try:
                    usage = psutil.disk_usage(partition.mountpoint)

                    disk_info = {
                        'device': partition.device,
                        'mountpoint': partition.mountpoint,
                        'filesystem': partition.fstype,
                        'total_gb': round(usage.total / (1024**3), 2),
                        'used_gb': round(usage.used / (1024**3), 2),
                        'free_gb': round(usage.free / (1024**3), 2),
                        'usage_percent': round((usage.used / usage.total) * 100, 2)
                    }

                    storage_info['disks'].append(disk_info)

                except PermissionError:
                    # 某些分区可能没有访问权限
                    continue

            # 计算总存储容量
            total_storage = sum(disk['total_gb'] for disk in storage_info['disks'])
            free_storage = sum(disk['free_gb'] for disk in storage_info['disks'])

            storage_info.update({
                'total_storage_gb': round(total_storage, 2),
                'free_storage_gb': round(free_storage, 2),
                'used_storage_gb': round(total_storage - free_storage, 2)
            })

            self.logger.info(f"检测到存储: {total_storage}GB总量, {free_storage}GB可用")
            return storage_info

        except Exception as e:
            self.logger.error(f"存储检测失败: {e}")
            return {}

    def detect_network_specs(self) -> Dict[str, Any]:
        """检测网络规格信息"""
        try:
            network_info = {
                'interfaces': []
            }

            # 获取网络接口信息
            interfaces = psutil.net_if_addrs()
            stats = psutil.net_if_stats()

            for interface_name, addresses in interfaces.items():
                if interface_name in stats:
                    interface_stats = stats[interface_name]

                    interface_info = {
                        'name': interface_name,
                        'is_up': interface_stats.isup,
                        'speed_mbps': interface_stats.speed if interface_stats.speed > 0 else None,
                        'mtu': interface_stats.mtu,
                        'addresses': []
                    }

                    for addr in addresses:
                        interface_info['addresses'].append({
                            'family': str(addr.family),
                            'address': addr.address,
                            'netmask': addr.netmask,
                            'broadcast': addr.broadcast
                        })

                    network_info['interfaces'].append(interface_info)

            # 获取网络IO统计
            net_io = psutil.net_io_counters()
            if net_io:
                network_info.update({
                    'bytes_sent': net_io.bytes_sent,
                    'bytes_recv': net_io.bytes_recv,
                    'packets_sent': net_io.packets_sent,
                    'packets_recv': net_io.packets_recv
                })

            active_interfaces = [iface for iface in network_info['interfaces'] if iface['is_up']]
            self.logger.info(f"检测到 {len(active_interfaces)} 个活动网络接口")

            return network_info

        except Exception as e:
            self.logger.error(f"网络检测失败: {e}")
            return {}

    def get_hardware_profile(self) -> str:
        """
        根据硬件配置生成硬件配置文件标识

        Returns:
            硬件配置文件名称，如 'single_gpu_rtx3080', 'multi_gpu_a100'
        """
        gpu_specs = self.detect_gpu_specs()

        if not gpu_specs:
            return 'cpu_only'

        gpu_count = len(gpu_specs)
        primary_gpu = gpu_specs[0]['name'].lower()

        # 简化GPU型号名称
        if 'rtx 3080' in primary_gpu:
            gpu_model = 'rtx3080'
        elif 'rtx 4090' in primary_gpu:
            gpu_model = 'rtx4090'
        elif 'a100' in primary_gpu:
            gpu_model = 'a100'
        elif 'v100' in primary_gpu:
            gpu_model = 'v100'
        elif 'rtx' in primary_gpu:
            gpu_model = 'rtx'
        elif 'gtx' in primary_gpu:
            gpu_model = 'gtx'
        else:
            gpu_model = 'gpu'

        if gpu_count == 1:
            return f'single_gpu_{gpu_model}'
        elif gpu_count <= 4:
            return f'multi_gpu_{gpu_model}'
        else:
            return f'cluster_gpu_{gpu_model}'

    def save_hardware_info(self, filepath: str) -> bool:
        """
        保存硬件信息到文件

        Args:
            filepath: 保存路径

        Returns:
            是否保存成功
        """
        try:
            hardware_info = self.detect_all_hardware()

            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(hardware_info, f, indent=2, ensure_ascii=False)

            self.logger.info(f"硬件信息已保存到: {filepath}")
            return True

        except Exception as e:
            self.logger.error(f"保存硬件信息失败: {e}")
            return False
