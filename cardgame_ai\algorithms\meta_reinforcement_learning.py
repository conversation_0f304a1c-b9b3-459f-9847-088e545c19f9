"""
元强化学习模块

实现元强化学习，使模型能够学习如何学习和适应。包括策略蒸馏、策略融合机制、
自适应探索策略和元控制器等技术，使模型能够更好地适应不同的游戏情境和对手。
"""
import os
import time
import logging
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Any, Optional, Union, Callable
from collections import defaultdict, deque
import copy

from cardgame_ai.core.agent import Agent
from cardgame_ai.core.algorithm import Algorithm
from cardgame_ai.core.base import State, Action, Experience, Batch
from cardgame_ai.algorithms.policy_distillation import PolicyDistillation
from cardgame_ai.algorithms.policy_fusion import PolicyFusion
from cardgame_ai.algorithms.adaptive_exploration import AdaptiveExploration
from cardgame_ai.algorithms.experience_distillation import ExperienceDistillation
from cardgame_ai.algorithms.exploration import ExplorationStrategy
from cardgame_ai.algorithms.hybrid_decision_system import MetaController
from cardgame_ai.algorithms.continual_learning import EWC


logger = logging.getLogger(__name__)


class MetaLearningAlgorithm:
    """元学习算法基类

    为MAML和Reptile等算法提供共享功能。
    """

    def __init__(
        self,
        model: nn.Module,
        inner_lr: float = 0.01,
        meta_lr: float = 0.001,
        inner_steps: int = 5,
        device: str = 'cuda' if torch.cuda.is_available() else 'cpu'
    ):
        """
        初始化元学习算法

        Args:
            model: 基础模型
            inner_lr: 内循环学习率
            meta_lr: 元学习率
            inner_steps: 内循环步数
            device: 设备
        """
        self.model = model
        self.inner_lr = inner_lr
        self.meta_lr = meta_lr
        self.inner_steps = inner_steps
        self.device = device
        
        # 元优化器
        self.meta_optimizer = torch.optim.Adam(self.model.parameters(), lr=self.meta_lr)
        
        # 统计信息
        self.stats = {
            "meta_updates": 0,
            "inner_updates": 0,
            "meta_losses": [],
            "task_losses": defaultdict(list),
            "adaptation_times": []
        }

    def compute_loss(self, model: nn.Module, batch: Any) -> torch.Tensor:
        """
        计算损失

        Args:
            model: 模型
            batch: 批次数据

        Returns:
            损失张量
        """
        raise NotImplementedError("子类必须实现此方法")

    def adapt_to_task(self, task_data: Any) -> nn.Module:
        """
        适应任务

        Args:
            task_data: 任务数据

        Returns:
            适应后的模型
        """
        raise NotImplementedError("子类必须实现此方法")

    def meta_update(self, tasks_data: List[Any]) -> float:
        """
        执行元更新

        Args:
            tasks_data: 任务数据列表

        Returns:
            元损失
        """
        raise NotImplementedError("子类必须实现此方法")
    
    def save(self, path: str) -> None:
        """
        保存模型和统计信息

        Args:
            path: 保存路径
        """
        state_dict = {
            "model": self.model.state_dict(),
            "meta_optimizer": self.meta_optimizer.state_dict(),
            "stats": self.stats,
            "config": {
                "inner_lr": self.inner_lr,
                "meta_lr": self.meta_lr,
                "inner_steps": self.inner_steps
            }
        }
        torch.save(state_dict, path)
        logger.info(f"元学习模型已保存到 {path}")

    def load(self, path: str) -> None:
        """
        加载模型和统计信息

        Args:
            path: 加载路径
        """
        if not os.path.exists(path):
            logger.warning(f"模型文件不存在: {path}")
            return
            
        try:
            state_dict = torch.load(path, map_location=self.device)
            self.model.load_state_dict(state_dict["model"])
            self.meta_optimizer.load_state_dict(state_dict["meta_optimizer"])
            self.stats = state_dict["stats"]
            
            # 加载配置
            config = state_dict.get("config", {})
            self.inner_lr = config.get("inner_lr", self.inner_lr)
            self.meta_lr = config.get("meta_lr", self.meta_lr)
            self.inner_steps = config.get("inner_steps", self.inner_steps)
            
            logger.info(f"元学习模型已从 {path} 加载")
        except Exception as e:
            logger.error(f"加载模型失败: {e}")


class MAML(MetaLearningAlgorithm):
    """Model-Agnostic Meta-Learning (MAML)

    实现MAML算法，使模型能够通过梯度下降快速适应新任务。
    """
    
    def __init__(
        self,
        model: nn.Module,
        inner_lr: float = 0.01,
        meta_lr: float = 0.001,
        inner_steps: int = 5,
        first_order: bool = False,
        device: str = 'cuda' if torch.cuda.is_available() else 'cpu'
    ):
        """
        初始化MAML

        Args:
            model: 基础模型
            inner_lr: 内循环学习率
            meta_lr: 元学习率
            inner_steps: 内循环步数
            first_order: 是否使用一阶近似
            device: 设备
        """
        super().__init__(model, inner_lr, meta_lr, inner_steps, device)
        self.first_order = first_order
        
        # 尝试导入higher库，用于实现高效的MAML
        try:
            global higher
            import higher
            self.higher_available = True
        except ImportError:
            logger.warning("higher库不可用，将使用手动实现MAML。为获得更好的性能，请安装higher: pip install higher")
            self.higher_available = False

    def compute_loss(self, model: nn.Module, batch: Any) -> torch.Tensor:
        """
        计算损失

        Args:
            model: 模型
            batch: 批次数据

        Returns:
            损失张量
        """
        # 这是一个模板方法，子类应该根据具体任务实现
        # 以下是示例实现，假设batch是(inputs, targets)元组
        inputs, targets = batch
        outputs = model(inputs)
        loss = F.cross_entropy(outputs, targets)
        return loss
    
    def adapt_to_task(self, task_data: Any) -> nn.Module:
        """
        适应任务

        Args:
            task_data: 任务数据，应包含support_batch和query_batch

        Returns:
            适应后的模型
        """
        # 获取任务数据
        support_batch = task_data["support_batch"]
        
        # 克隆模型
        adapted_model = copy.deepcopy(self.model)
        adapted_model.to(self.device)
        
        # 创建优化器
        optimizer = torch.optim.SGD(adapted_model.parameters(), lr=self.inner_lr)
        
        # 内循环适应
        start_time = time.time()
        adapted_model.train()
        for _ in range(self.inner_steps):
            # 计算损失
            loss = self.compute_loss(adapted_model, support_batch)
            
            # 更新参数
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            # 更新统计信息
            self.stats["inner_updates"] += 1
        
        # 记录适应时间
        adaptation_time = time.time() - start_time
        self.stats["adaptation_times"].append(adaptation_time)
        
        return adapted_model
    
    def meta_update(self, tasks_data: List[Dict[str, Any]]) -> float:
        """
        执行元更新

        Args:
            tasks_data: 任务数据列表，每个任务数据应包含support_batch和query_batch

        Returns:
            元损失
        """
        if self.higher_available:
            return self._meta_update_with_higher(tasks_data)
        
        # 元优化器
        self.meta_optimizer.zero_grad()
        
        # 保存原始参数
        original_params = [p.clone().detach() for p in self.model.parameters()]
        
        meta_loss = 0.0
        
        # 对每个任务执行元学习步骤
        for task_data in tasks_data:
            # 获取任务数据
            support_batch = task_data["support_batch"]
            query_batch = task_data["query_batch"]
            task_name = task_data.get("name", "default")
            
            # 克隆模型
            task_model = copy.deepcopy(self.model)
            task_model.to(self.device)
            
            # 创建优化器
            optimizer = torch.optim.SGD(task_model.parameters(), lr=self.inner_lr)
            
            # 内循环适应
            for _ in range(self.inner_steps):
                # 计算损失
                loss = self.compute_loss(task_model, support_batch)
                
                # 更新参数
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
            
            # 计算在查询集上的损失
            query_loss = self.compute_loss(task_model, query_batch)
            
            # 累积元损失
            meta_loss += query_loss
            
            # 记录任务损失
            self.stats["task_losses"][task_name].append(query_loss.item())
        
        # 平均元损失
        meta_loss = meta_loss / len(tasks_data)
        
        # 计算梯度
        meta_loss.backward()
        
        # 更新元模型
        self.meta_optimizer.step()
        
        # 恢复原始参数
        for p, orig_p in zip(self.model.parameters(), original_params):
            p.data.copy_(orig_p.data)
        
        # 更新统计信息
        self.stats["meta_updates"] += 1
        self.stats["meta_losses"].append(meta_loss.item())
        
        return meta_loss.item()
    
    def _meta_update_with_higher(self, tasks_data: List[Dict[str, Any]]) -> float:
        """
        使用higher库执行元更新

        Args:
            tasks_data: 任务数据列表

        Returns:
            元损失
        """
        # 元优化器
        self.meta_optimizer.zero_grad()
        
        meta_loss = 0.0
        
        # 对每个任务执行元学习步骤
        for task_data in tasks_data:
            # 获取任务数据
            support_batch = task_data["support_batch"]
            query_batch = task_data["query_batch"]
            task_name = task_data.get("name", "default")
            
            # 创建虚拟优化器
            inner_opt = torch.optim.SGD(self.model.parameters(), lr=self.inner_lr)
            
            # 使用higher创建函数式模型和优化器
            with higher.innerloop_ctx(self.model, inner_opt, copy_initial_weights=True) as (fmodel, diffopt):
                # 内循环适应
                for _ in range(self.inner_steps):
                    # 计算损失
                    loss = self.compute_loss(fmodel, support_batch)
                    
                    # 更新参数
                    diffopt.step(loss)
                
                # 计算在查询集上的损失
                query_loss = self.compute_loss(fmodel, query_batch)
                
                # 累积元损失
                meta_loss += query_loss
                
                # 记录任务损失
                self.stats["task_losses"][task_name].append(query_loss.item())
        
        # 平均元损失
        meta_loss = meta_loss / len(tasks_data)
        
        # 计算梯度
        meta_loss.backward()
        
        # 更新元模型
        self.meta_optimizer.step()
        
        # 更新统计信息
        self.stats["meta_updates"] += 1
        self.stats["meta_losses"].append(meta_loss.item())
        
        return meta_loss.item()


class Reptile(MetaLearningAlgorithm):
    """Reptile

    实现Reptile算法，一种简单但有效的元学习算法。
    """
    
    def compute_loss(self, model: nn.Module, batch: Any) -> torch.Tensor:
        """
        计算损失

        Args:
            model: 模型
            batch: 批次数据

        Returns:
            损失张量
        """
        # 这是一个模板方法，子类应该根据具体任务实现
        # 以下是示例实现，假设batch是(inputs, targets)元组
        inputs, targets = batch
        outputs = model(inputs)
        loss = F.cross_entropy(outputs, targets)
        return loss
    
    def adapt_to_task(self, task_data: Any) -> nn.Module:
        """
        适应任务

        Args:
            task_data: 任务数据，应包含support_batch

        Returns:
            适应后的模型
        """
        # 获取任务数据
        support_batch = task_data["support_batch"]
        
        # 克隆模型
        adapted_model = copy.deepcopy(self.model)
        adapted_model.to(self.device)
        
        # 创建优化器
        optimizer = torch.optim.SGD(adapted_model.parameters(), lr=self.inner_lr)
        
        # 内循环适应
        start_time = time.time()
        adapted_model.train()
        for _ in range(self.inner_steps):
            # 计算损失
            loss = self.compute_loss(adapted_model, support_batch)
            
            # 更新参数
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            # 更新统计信息
            self.stats["inner_updates"] += 1
        
        # 记录适应时间
        adaptation_time = time.time() - start_time
        self.stats["adaptation_times"].append(adaptation_time)
        
        return adapted_model
    
    def meta_update(self, tasks_data: List[Dict[str, Any]]) -> float:
        """
        执行元更新

        Args:
            tasks_data: 任务数据列表，每个任务数据应包含support_batch

        Returns:
            元损失
        """
        # 元优化器
        self.meta_optimizer.zero_grad()
        
        # 保存原始参数
        original_params = {name: param.clone() for name, param in self.model.named_parameters()}
        
        meta_loss = 0.0
        
        # 对每个任务执行元学习步骤
        for task_data in tasks_data:
            # 获取任务数据
            support_batch = task_data["support_batch"]
            task_name = task_data.get("name", "default")
            
            # 克隆模型
            task_model = copy.deepcopy(self.model)
            task_model.to(self.device)
            
            # 创建优化器
            optimizer = torch.optim.SGD(task_model.parameters(), lr=self.inner_lr)
            
            # 内循环适应
            task_loss = 0.0
            for _ in range(self.inner_steps):
                # 计算损失
                loss = self.compute_loss(task_model, support_batch)
                task_loss += loss.item()
                
                # 更新参数
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
            
            # 记录任务损失
            avg_task_loss = task_loss / self.inner_steps
            self.stats["task_losses"][task_name].append(avg_task_loss)
            
            # Reptile更新：将原始参数向适应后的参数移动
            for name, param in self.model.named_parameters():
                adapted_param = task_model.state_dict()[name]
                grad = -(original_params[name] - adapted_param) / self.inner_lr
                
                # 如果参数有梯度，则累积
                if param.grad is None:
                    param.grad = grad / len(tasks_data)
                else:
                    param.grad += grad / len(tasks_data)
        
        # 更新元模型
        self.meta_optimizer.step()
        
        # 计算元损失（参数距离）
        with torch.no_grad():
            for name, param in self.model.named_parameters():
                meta_loss += torch.norm(original_params[name] - param).item()
        
        # 更新统计信息
        self.stats["meta_updates"] += 1
        self.stats["meta_losses"].append(meta_loss)
        
        return meta_loss


class MetaReinforcementLearning:
    """
    元强化学习
    
    整合多种元学习和强化学习技术，提高模型的适应性和泛化能力。
    """
    
    def __init__(
        self,
        base_algorithm: Algorithm,
        policy_distillation: Optional[PolicyDistillation] = None,
        policy_fusion: Optional[PolicyFusion] = None,
        adaptive_exploration: Optional[AdaptiveExploration] = None,
        meta_controller: Optional[MetaController] = None,
        ewc: Optional[EWC] = None,
        config: Optional[Dict[str, Any]] = None
    ):
        """
        初始化元强化学习

        Args:
            base_algorithm: 基础算法
            policy_distillation: 策略蒸馏，可选
            policy_fusion: 策略融合，可选
            adaptive_exploration: 自适应探索，可选
            meta_controller: 元控制器，可选
            ewc: 弹性权重合并，可选
            config: 配置参数，可选
        """
        self.base_algorithm = base_algorithm
        self.policy_distillation = policy_distillation
        self.policy_fusion = policy_fusion
        self.adaptive_exploration = adaptive_exploration
        self.meta_controller = meta_controller
        self.ewc = ewc
        
        # 默认配置
        default_config = {
            "use_policy_distillation": policy_distillation is not None,
            "use_policy_fusion": policy_fusion is not None,
            "use_adaptive_exploration": adaptive_exploration is not None,
            "use_meta_controller": meta_controller is not None,
            "use_ewc": ewc is not None,
            "meta_batch_size": 32,
            "meta_update_interval": 100,
            "distillation_update_interval": 200,
            "fusion_update_interval": 150,
            "exploration_update_interval": 50,
            "exploration_temperature": 1.0
        }
        
        # 合并配置
        self.config = default_config.copy()
        if config:
            self.config.update(config)
        
        # 初始化统计信息
        self.stats = {
            "updates": 0,
            "meta_updates": 0,
            "distillation_updates": 0,
            "fusion_updates": 0,
            "exploration_updates": 0,
            "base_losses": [],
            "meta_losses": [],
            "distillation_losses": [],
            "fusion_losses": [],
            "exploration_losses": []
        }
        
        # 元批次缓冲区
        self.meta_buffer = deque(maxlen=1000)
        
        logger.info("元强化学习初始化完成")
    
    def update(self, experience: Union[Experience, Batch]) -> Dict[str, float]:
        """
        更新算法

        Args:
            experience: 经验或批次

        Returns:
            更新指标
        """
        # 更新基础算法
        base_metrics = self.base_algorithm.update(experience)
        
        # 记录基础损失
        if "loss" in base_metrics:
            self.stats["base_losses"].append(base_metrics["loss"])
        
        # 添加到元批次缓冲区
        if isinstance(experience, Experience):
            self.meta_buffer.append(experience)
        elif isinstance(experience, Batch):
            self.meta_buffer.extend(experience.experiences)
        
        # 根据更新间隔决定是否执行元更新
        should_meta_update = self.stats["updates"] % self.config["meta_update_interval"] == 0
        should_distillation_update = self.stats["updates"] % self.config["distillation_update_interval"] == 0
        should_fusion_update = self.stats["updates"] % self.config["fusion_update_interval"] == 0
        should_exploration_update = self.stats["updates"] % self.config["exploration_update_interval"] == 0
        
        # 执行元更新
        meta_metrics = {}
        if should_meta_update and len(self.meta_buffer) >= self.config["meta_batch_size"]:
            meta_metrics = self._meta_update(experience)
        
        # 更新计数器
        self.stats["updates"] += 1
        
        # 合并指标
        metrics = {**base_metrics, **meta_metrics}
        
        return metrics
    
    def predict(self, state: Union[State, np.ndarray], **kwargs) -> Tuple[List[float], float]:
        """
        预测动作概率和价值

        Args:
            state: 状态
            **kwargs: 额外参数

        Returns:
            (动作概率, 价值)
        """
        # 如果使用策略融合
        if self.config["use_policy_fusion"] and self.policy_fusion is not None:
            # 将状态转换为张量
            if isinstance(state, State):
                state_tensor = torch.tensor(state.to_array(), dtype=torch.float32).unsqueeze(0)
            else:
                state_tensor = torch.tensor(state, dtype=torch.float32).unsqueeze(0)
            
            # 融合策略
            policy = self.policy_fusion.fuse(state_tensor).squeeze(0).tolist()
            # 使用基础算法预测价值
            _, value = self.base_algorithm.predict(state, **kwargs)
            
            return policy, value
        
        # 否则使用基础算法
        return self.base_algorithm.predict(state, **kwargs)
    
    def select_action(
        self,
        state: Union[State, np.ndarray],
        legal_actions: Optional[List[int]] = None,
        **kwargs
    ) -> int:
        """
        选择动作

        Args:
            state: 状态
            legal_actions: 合法动作列表，可选
            **kwargs: 额外参数

        Returns:
            选择的动作
        """
        # 如果使用自适应探索
        if self.config["use_adaptive_exploration"] and self.adaptive_exploration is not None:
            # 获取策略和价值
            policy, value = self.predict(state, **kwargs)
            
            # 将状态转换为张量
            if isinstance(state, State):
                state_tensor = torch.tensor(state.to_array(), dtype=torch.float32).unsqueeze(0)
            else:
                state_tensor = torch.tensor(state, dtype=torch.float32).unsqueeze(0)
            
            # 将策略转换为logits
            policy_tensor = torch.tensor(policy, dtype=torch.float32).unsqueeze(0)
            
            # 选择动作
            action, is_exploration = self.adaptive_exploration.select_action(
                state_tensor,
                policy_tensor,
                is_training=kwargs.get("is_training", True)
            )
            
            # 确保动作合法
            if legal_actions is not None and action not in legal_actions:
                # 从合法动作中选择
                # 将非法动作的概率设为0
                masked_policy = np.zeros_like(policy)
                for a in legal_actions:
                    masked_policy[a] = policy[a]
                
                # 重新归一化
                if np.sum(masked_policy) > 0:
                    masked_policy = masked_policy / np.sum(masked_policy)
                else:
                    # 如果所有动作都被屏蔽，使用均匀分布
                    masked_policy = np.zeros_like(policy)
                    for a in legal_actions:
                        masked_policy[a] = 1.0 / len(legal_actions)
                
                # 选择最佳动作
                action = np.random.choice(len(masked_policy), p=masked_policy)
                
            return action
        
        # 否则使用基础算法
        return self.base_algorithm.select_action(state, legal_actions, **kwargs)
    
    def adapt(
        self,
        context: Dict[str, Any],
        adaptation_data: Optional[List[Experience]] = None
    ) -> Dict[str, float]:
        """
        适应新环境或对手

        Args:
            context: 上下文信息
            adaptation_data: 适应数据，可选

        Returns:
            适应指标
        """
        metrics = {}
        
        # 如果使用元控制器
        if self.config["use_meta_controller"] and self.meta_controller is not None:
            # 调整元控制器
            meta_metrics = self._adjust_meta_controller(context)
            metrics.update(meta_metrics)
        
        # 如果使用自适应探索
        if self.config["use_adaptive_exploration"] and self.adaptive_exploration is not None:
            # 调整探索策略
            if adaptation_data is not None:
                # 提取状态、动作和奖励
                states = []
                actions = []
                rewards = []
                is_explorations = []
                
                for exp in adaptation_data:
                    if isinstance(exp.state, State):
                        states.append(exp.state.to_array())
                    else:
                        states.append(exp.state)
                    
                    if isinstance(exp.action, Action):
                        actions.append(exp.action.to_index())
                    else:
                        actions.append(exp.action)
                    
                    rewards.append(exp.reward)
                    # 假设都是探索
                    is_explorations.append(True)
                
                # 转换为张量
                states = torch.tensor(np.array(states), dtype=torch.float32)
                actions = torch.tensor(np.array(actions), dtype=torch.long)
                rewards = torch.tensor(np.array(rewards), dtype=torch.float32)
                
                # 更新探索策略
                exploration_metrics = self.adaptive_exploration.update(states, actions, rewards, is_explorations)
                metrics.update(exploration_metrics)
        
        # 返回适应指标
        return metrics
    
    def _meta_update(self, experience: Union[Experience, Batch]) -> Dict[str, float]:
        """
        执行元更新

        Args:
            experience: 经验或批次

        Returns:
            更新指标
        """
        metrics = {}
        
        # 从元缓冲区采样
        meta_batch = list(np.random.choice(self.meta_buffer, self.config["meta_batch_size"], replace=False))
        
        # 如果使用元控制器
        if self.config["use_meta_controller"] and self.meta_controller is not None:
            meta_controller_metrics = self._update_meta_controller(meta_batch)
            metrics.update(meta_controller_metrics)
            self.stats["meta_updates"] += 1
        
        # 如果使用策略蒸馏
        if self.config["use_policy_distillation"] and self.policy_distillation is not None:
            # 更新策略蒸馏
            states = []
            for exp in meta_batch:
                if isinstance(exp.state, State):
                    states.append(exp.state.to_array())
                else:
                    states.append(exp.state)
            
            states = torch.tensor(np.array(states), dtype=torch.float32)
            distillation_metrics = self.policy_distillation.update(states)
            metrics.update({"distillation_" + k: v for k, v in distillation_metrics.items()})
            self.stats["distillation_updates"] += 1
        
        # 更新统计信息
        for key, value in metrics.items():
            if key.endswith("_loss"):
                stat_key = key.replace("_loss", "_losses")
                if stat_key in self.stats:
                    self.stats[stat_key].append(value)
        
        return metrics
    
    def _update_meta_controller(self, meta_batch: List[Experience]) -> Dict[str, float]:
        """
        更新元控制器

        Args:
            meta_batch: 元批次

        Returns:
            更新指标
        """
        if self.meta_controller is None:
            return {}
        
        # 更新元控制器
        meta_controller_metrics = self.meta_controller.update(meta_batch)
        
        return {"meta_controller_" + k: v for k, v in meta_controller_metrics.items()}
    
    def _adjust_meta_controller(self, context: Dict[str, Any]) -> Dict[str, float]:
        """
        调整元控制器

        Args:
            context: 上下文信息

        Returns:
            调整指标
        """
        if self.meta_controller is None:
            return {}
        
        # 调整元控制器
        meta_controller_metrics = self.meta_controller.adapt(context)
        
        return {"meta_controller_adjust_" + k: v for k, v in meta_controller_metrics.items()}
    
    def save(self, path: str) -> None:
        """
        保存模型

        Args:
            path: 保存路径
        """
        # 创建保存目录
        os.makedirs(path, exist_ok=True)
        
        # 保存基础算法
        self.base_algorithm.save(os.path.join(path, "base_algorithm"))
        
        # 保存策略蒸馏
        if self.policy_distillation is not None:
            self.policy_distillation.save(os.path.join(path, "policy_distillation.pt"))
        
        # 保存策略融合
        if self.policy_fusion is not None:
            self.policy_fusion.save(os.path.join(path, "policy_fusion.pt"))
        
        # 保存自适应探索
        if self.adaptive_exploration is not None:
            self.adaptive_exploration.save(os.path.join(path, "adaptive_exploration.pt"))
        
        # 保存元控制器
        if self.meta_controller is not None:
            self.meta_controller.save(os.path.join(path, "meta_controller"))
        
        # 保存EWC
        if self.ewc is not None:
            self.ewc.save(os.path.join(path, "ewc.pt"))
        
        # 保存配置和统计信息
        state = {
            "config": self.config,
            "stats": self.stats
        }
        torch.save(state, os.path.join(path, "meta_rl_state.pt"))
        
        logger.info(f"元强化学习模型已保存到 {path}")
    
    def load(self, path: str) -> None:
        """
        加载模型

        Args:
            path: 加载路径
        """
        # 加载基础算法
        self.base_algorithm.load(os.path.join(path, "base_algorithm"))
        
        # 加载策略蒸馏
        if self.policy_distillation is not None:
            policy_distillation_path = os.path.join(path, "policy_distillation.pt")
            if os.path.exists(policy_distillation_path):
                self.policy_distillation.load(policy_distillation_path)
        
        # 加载策略融合
        if self.policy_fusion is not None:
            policy_fusion_path = os.path.join(path, "policy_fusion.pt")
            if os.path.exists(policy_fusion_path):
                self.policy_fusion.load(policy_fusion_path)
        
        # 加载自适应探索
        if self.adaptive_exploration is not None:
            adaptive_exploration_path = os.path.join(path, "adaptive_exploration.pt")
            if os.path.exists(adaptive_exploration_path):
                self.adaptive_exploration.load(adaptive_exploration_path)
        
        # 加载元控制器
        if self.meta_controller is not None:
            meta_controller_path = os.path.join(path, "meta_controller")
            if os.path.exists(meta_controller_path):
                self.meta_controller.load(meta_controller_path)
        
        # 加载EWC
        if self.ewc is not None:
            ewc_path = os.path.join(path, "ewc.pt")
            if os.path.exists(ewc_path):
                self.ewc.load(ewc_path)
        
        # 加载配置和统计信息
        state_path = os.path.join(path, "meta_rl_state.pt")
        if os.path.exists(state_path):
            state = torch.load(state_path)
            self.config = state["config"]
            self.stats = state["stats"]
        
        logger.info(f"元强化学习模型已从 {path} 加载")
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            统计信息
        """
        # 获取基础算法统计信息
        base_stats = self.base_algorithm.get_stats() if hasattr(self.base_algorithm, "get_stats") else {}
        
        # 获取策略蒸馏统计信息
        distillation_stats = self.policy_distillation.get_stats() if self.policy_distillation is not None else {}
        
        # 获取策略融合统计信息
        fusion_stats = self.policy_fusion.get_stats() if self.policy_fusion is not None else {}
        
        # 获取自适应探索统计信息
        exploration_stats = self.adaptive_exploration.get_stats() if self.adaptive_exploration is not None else {}
        
        # 获取元控制器统计信息
        meta_controller_stats = self.meta_controller.get_stats() if self.meta_controller is not None else {}
        
        # 合并统计信息
        stats = {
            "meta_reinforcement_learning": self.stats,
            "base_algorithm": base_stats,
            "policy_distillation": distillation_stats,
            "policy_fusion": fusion_stats,
            "adaptive_exploration": exploration_stats,
            "meta_controller": meta_controller_stats
        }
        
        return stats
