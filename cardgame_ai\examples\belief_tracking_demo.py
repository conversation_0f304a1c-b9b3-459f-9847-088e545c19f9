"""
信念追踪演示脚本

演示如何使用DeepBeliefTracker和HybridDecisionSystem进行信念追踪和决策。
"""
import os
import sys
import time
import logging
import random
import numpy as np
import torch

# 设置日志级别
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# 导入必要的模块
from cardgame_ai.core.base import State, Action
from cardgame_ai.games.doudizhu.env import DouDizhuEnv
from cardgame_ai.games.doudizhu.state import DouDizhuState
from cardgame_ai.games.doudizhu.card import Card
from cardgame_ai.algorithms.belief_tracking.deep_belief import DeepBeliefTracker
from cardgame_ai.algorithms.hybrid_decision_system import HybridDecisionSystem
from cardgame_ai.algorithms.rule_based import RuleBasedAgent
from cardgame_ai.algorithms.key_moment_detector import KeyMomentDetector
from cardgame_ai.evaluation.belief_accuracy_evaluator import BeliefAccuracyEvaluator


def create_belief_tracker(player_id: int, input_dim: int = 54, hidden_dim: int = 128):
    """
    创建信念追踪器
    
    Args:
        player_id: 玩家ID
        input_dim: 输入维度
        hidden_dim: 隐藏层维度
        
    Returns:
        DeepBeliefTracker: 信念追踪器
    """
    # 创建信念追踪器
    belief_tracker = DeepBeliefTracker(
        player_id=player_id,
        input_dim=input_dim,
        hidden_dim=hidden_dim,
        use_transformer=True
    )
    
    return belief_tracker


def create_hybrid_agent(player_id: int, belief_tracker: DeepBeliefTracker = None):
    """
    创建混合决策系统代理
    
    Args:
        player_id: 玩家ID
        belief_tracker: 信念追踪器
        
    Returns:
        HybridDecisionSystem: 混合决策系统代理
    """
    # 创建规则代理
    rule_agent = RuleBasedAgent()
    
    # 创建关键决策点检测器
    key_moment_detector = KeyMomentDetector()
    
    # 创建符号推理组件
    from cardgame_ai.algorithms.symbolic_reasoning import SymbolicReasoningComponent
    symbolic_component = SymbolicReasoningComponent(
        use_guaranteed_win_solver=True,
        use_card_counting=True
    )
    
    # 创建混合决策系统
    hybrid_agent = HybridDecisionSystem(
        rule_agent=rule_agent,
        key_moment_detector=key_moment_detector,
        belief_tracker=belief_tracker,
        symbolic_component=symbolic_component,
        meta_strategy="adaptive",
        use_history=True,
        history_window=5
    )
    
    return hybrid_agent


def run_game_with_belief_tracking():
    """
    运行带有信念追踪的游戏
    """
    # 创建环境
    env = DouDizhuEnv()
    
    # 创建信念追踪器
    belief_tracker_0 = create_belief_tracker(player_id=0)
    belief_tracker_1 = create_belief_tracker(player_id=1)
    belief_tracker_2 = create_belief_tracker(player_id=2)
    
    # 创建代理
    agent_0 = create_hybrid_agent(player_id=0, belief_tracker=belief_tracker_0)
    agent_1 = create_hybrid_agent(player_id=1, belief_tracker=belief_tracker_1)
    agent_2 = create_hybrid_agent(player_id=2, belief_tracker=belief_tracker_2)
    
    agents = [agent_0, agent_1, agent_2]
    
    # 运行一局游戏
    state = env.reset()
    done = False
    
    while not done:
        player_id = state.get_player_id()
        agent = agents[player_id]
        
        # 获取合法动作
        legal_actions = env.get_legal_actions(state)
        
        # 获取动作
        action = agent.act(state, legal_actions)
        
        # 执行动作
        next_state, reward, done, info = env.step(action)
        
        # 更新其他代理的信念追踪器
        for i, other_agent in enumerate(agents):
            if i != player_id:
                other_agent.update_belief_tracker(state, action, player_id)
        
        # 更新状态
        state = next_state
    
    # 获取游戏结果
    payoffs = env.get_payoffs(state)
    winner = np.argmax(payoffs)
    
    logging.info(f"游戏结束，获胜玩家: {winner}")
    
    # 获取代理统计信息
    for i, agent in enumerate(agents):
        stats = agent.get_stats()
        if "belief_tracking" in stats:
            logging.info(f"玩家{i}信念追踪统计: {stats['belief_tracking']}")


def evaluate_belief_tracking(num_games: int = 10):
    """
    评估信念追踪的准确性
    
    Args:
        num_games: 评估游戏数
    """
    # 创建环境
    env = DouDizhuEnv()
    
    # 创建信念追踪器
    belief_tracker_0 = create_belief_tracker(player_id=0)
    
    # 创建代理
    agent_0 = create_hybrid_agent(player_id=0, belief_tracker=belief_tracker_0)
    agent_1 = create_hybrid_agent(player_id=1)
    agent_2 = create_hybrid_agent(player_id=2)
    
    agents = [agent_0, agent_1, agent_2]
    
    # 创建评估器
    evaluator = BeliefAccuracyEvaluator()
    
    # 评估
    result = evaluator.evaluate(env, agents, num_games, ground_truth_available=True)
    
    # 打印评估结果
    logging.info(f"信念追踪评估结果:")
    logging.info(f"平均信念准确性: {result.get('mean_belief_accuracy', 0.0):.4f}")
    logging.info(f"平均信念置信度: {result.get('mean_belief_confidence', 0.0):.4f}")
    logging.info(f"信念使用次数: {result.get('belief_usage_count', 0)}")
    logging.info(f"玩家0胜率: {result.get('player_0_win_rate', 0.0):.4f}")
    
    return result


if __name__ == "__main__":
    # 运行带有信念追踪的游戏
    logging.info("运行带有信念追踪的游戏...")
    run_game_with_belief_tracking()
    
    # 评估信念追踪的准确性
    logging.info("\n评估信念追踪的准确性...")
    evaluate_belief_tracking(num_games=5)
