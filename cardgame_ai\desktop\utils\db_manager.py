#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库管理器

管理客户端数据库，提供数据存储和查询功能。
"""

import os
import sqlite3
import logging
from typing import Dict, Any, List, Tuple, Optional

from cardgame_ai.desktop.utils.platform_utils import PlatformConfig

logger = logging.getLogger(__name__)


class DBManager:
    """数据库管理器类"""
    
    def __init__(self, db_path: Optional[str] = None):
        """
        初始化数据库管理器
        
        Args:
            db_path (Optional[str], optional): 数据库文件路径. Defaults to None.
        """
        # 数据库文件路径
        self.db_path = db_path or os.path.join(PlatformConfig.get_config_dir(), "client.db")
        
        # 数据库连接
        self.conn = None
        
        logger.info(f"数据库管理器初始化完成，数据库文件：{self.db_path}")
    
    def init_db(self) -> bool:
        """
        初始化数据库
        
        Returns:
            bool: 是否初始化成功
        """
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            
            # 连接数据库
            self.conn = sqlite3.connect(self.db_path)
            
            # 创建表
            self._create_tables()
            
            logger.info("数据库初始化成功")
            return True
        except Exception as e:
            logger.error(f"数据库初始化失败：{e}")
            return False
    
    def _create_tables(self) -> None:
        """创建数据库表"""
        # 创建游戏记录表
        self.conn.execute("""
            CREATE TABLE IF NOT EXISTS game_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                game_type TEXT NOT NULL,
                player_role TEXT NOT NULL,
                opponent_type TEXT NOT NULL,
                result TEXT NOT NULL,
                score INTEGER NOT NULL,
                duration INTEGER NOT NULL,
                timestamp INTEGER NOT NULL
            )
        """)
        
        # 创建模型记录表
        self.conn.execute("""
            CREATE TABLE IF NOT EXISTS model_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                model_name TEXT NOT NULL,
                model_type TEXT NOT NULL,
                game_type TEXT NOT NULL,
                version TEXT NOT NULL,
                path TEXT NOT NULL,
                description TEXT,
                created_at INTEGER NOT NULL
            )
        """)
        
        # 创建训练记录表
        self.conn.execute("""
            CREATE TABLE IF NOT EXISTS training_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                model_id INTEGER NOT NULL,
                algorithm TEXT NOT NULL,
                episodes INTEGER NOT NULL,
                duration INTEGER NOT NULL,
                final_reward REAL NOT NULL,
                final_loss REAL NOT NULL,
                timestamp INTEGER NOT NULL,
                FOREIGN KEY (model_id) REFERENCES model_records (id)
            )
        """)
        
        # 创建设置表
        self.conn.execute("""
            CREATE TABLE IF NOT EXISTS settings (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL
            )
        """)
        
        # 提交事务
        self.conn.commit()
        
        logger.info("数据库表创建成功")
    
    def close(self) -> None:
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()
            self.conn = None
            
            logger.info("数据库连接已关闭")
    
    def __del__(self) -> None:
        """析构函数"""
        self.close()
    
    def execute(self, sql: str, params: Tuple = ()) -> sqlite3.Cursor:
        """
        执行SQL语句
        
        Args:
            sql (str): SQL语句
            params (Tuple, optional): 参数. Defaults to ().
            
        Returns:
            sqlite3.Cursor: 游标对象
        """
        if not self.conn:
            self.init_db()
        
        return self.conn.execute(sql, params)
    
    def executemany(self, sql: str, params_list: List[Tuple]) -> sqlite3.Cursor:
        """
        执行多条SQL语句
        
        Args:
            sql (str): SQL语句
            params_list (List[Tuple]): 参数列表
            
        Returns:
            sqlite3.Cursor: 游标对象
        """
        if not self.conn:
            self.init_db()
        
        return self.conn.executemany(sql, params_list)
    
    def commit(self) -> None:
        """提交事务"""
        if self.conn:
            self.conn.commit()
    
    def rollback(self) -> None:
        """回滚事务"""
        if self.conn:
            self.conn.rollback()
    
    def get_setting(self, key: str, default: Any = None) -> Any:
        """
        获取设置
        
        Args:
            key (str): 设置键
            default (Any, optional): 默认值. Defaults to None.
            
        Returns:
            Any: 设置值
        """
        try:
            cursor = self.execute("SELECT value FROM settings WHERE key = ?", (key,))
            row = cursor.fetchone()
            
            if row:
                return row[0]
            else:
                return default
        except Exception as e:
            logger.error(f"获取设置失败：{e}")
            return default
    
    def set_setting(self, key: str, value: Any) -> bool:
        """
        设置设置
        
        Args:
            key (str): 设置键
            value (Any): 设置值
            
        Returns:
            bool: 是否设置成功
        """
        try:
            self.execute(
                "INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)",
                (key, str(value))
            )
            self.commit()
            
            logger.info(f"设置设置成功：{key} = {value}")
            return True
        except Exception as e:
            logger.error(f"设置设置失败：{e}")
            return False
