#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
专家策略池示例脚本

展示如何使用专家策略池管理和调度多种策略。
"""

import os
import sys
import argparse
import logging
from typing import Dict, List, Any

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from cardgame_ai.core.expert_pool import ExpertPolicyPool
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.games.doudizhu.state import DouDizhuState

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='专家策略池示例')
    
    parser.add_argument('--config', type=str, default=None,
                        help='专家池配置文件路径')
    parser.add_argument('--save_config', type=str, default=None,
                        help='保存专家池配置的路径')
    parser.add_argument('--filter_tags', type=str, nargs='+', default=None,
                        help='按标签筛选专家')
    parser.add_argument('--filter_types', type=str, nargs='+', default=None,
                        help='按类型筛选专家')
    parser.add_argument('--test', action='store_true',
                        help='测试专家策略')
    
    return parser.parse_args()


def test_experts(expert_pool: ExpertPolicyPool):
    """测试专家策略"""
    # 创建环境
    env = DouDizhuEnvironment()
    
    # 获取所有专家
    experts = expert_pool.get_all_experts()
    
    # 测试每个专家
    for name, expert in experts.items():
        logger.info(f"测试专家策略 '{name}'...")
        
        # 重置环境
        state = env.reset()
        
        # 获取合法动作
        legal_actions = env.get_legal_actions(state)
        
        try:
            # 让专家选择动作
            action = expert.act(state, legal_actions)
            
            # 输出结果
            logger.info(f"专家 '{name}' 选择的动作: {action}")
            
            # 获取元数据
            metadata = expert_pool.get_expert_metadata(name)
            logger.info(f"专家 '{name}' 的元数据: {metadata}")
            
        except Exception as e:
            logger.error(f"测试专家 '{name}' 时出错: {e}")


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 创建专家池
    expert_pool = ExpertPolicyPool(config_path=args.config)
    
    # 列出所有专家
    experts = expert_pool.list_experts()
    logger.info(f"专家策略列表: {experts}")
    
    # 筛选专家
    if args.filter_tags or args.filter_types:
        filtered_experts = expert_pool.filter_experts(
            tags=args.filter_tags,
            types=args.filter_types
        )
        logger.info(f"筛选后的专家策略: {list(filtered_experts.keys())}")
    
    # 测试专家
    if args.test:
        test_experts(expert_pool)
    
    # 保存配置
    if args.save_config:
        expert_pool.save_config(args.save_config)


if __name__ == "__main__":
    main()
