"""
关键决策点检测器模块

提供识别游戏中关键决策点的功能。
"""
import time
import logging
from typing import Dict, Any, Optional, List, Tuple, Union

import numpy as np

from cardgame_ai.core.base import State
from cardgame_ai.games.doudizhu.state import DouD<PERSON>huState, GamePhase
from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType
from cardgame_ai.algorithms.components.base_component import DecisionComponent


class KeyMomentDetector(DecisionComponent):
    """
    关键决策点检测器

    用于识别游戏中的关键决策点，使智能体能够在这些节点上分配更多计算资源。
    """

    def __init__(self, 
                 name: str = "key_moment_detector",
                 end_game_threshold: int = 5,
                 bomb_weight: float = 1.5,
                 rocket_weight: float = 2.0,
                 critical_card_count_threshold: float = 0.3,
                 use_ml_model: bool = False,
                 model: Optional[Any] = None):
        """
        初始化关键决策点检测器

        Args:
            name: 组件名称
            end_game_threshold: 牌数阈值，低于此值视为终局状态
            bomb_weight: 炸弹权重
            rocket_weight: 火箭权重
            critical_card_count_threshold: 关键牌数比例阈值
            use_ml_model: 是否使用机器学习模型
            model: 机器学习模型（如果use_ml_model=True）
        """
        super().__init__(name)
        self.end_game_threshold = end_game_threshold
        self.bomb_weight = bomb_weight
        self.rocket_weight = rocket_weight
        self.critical_card_count_threshold = critical_card_count_threshold
        self.use_ml_model = use_ml_model
        self.model = model

        # 添加特定统计信息
        self.stats.update({
            "key_moments_detected": 0,
            "end_game_moments": 0,
            "bomb_play_moments": 0,
            "critical_card_count_moments": 0,
            "model_predicted_moments": 0
        })

    def decide(self, state: State, legal_actions: List[Any], explain: bool = False) -> Union[bool, Tuple[bool, Dict[str, Any]]]:
        """
        判断当前状态是否为关键决策点

        Args:
            state: 当前状态
            legal_actions: 合法动作列表（此参数在这里不使用，但保留以符合DecisionComponent接口）
            explain: 是否启用解释模式

        Returns:
            如果explain=False，返回是否为关键决策点
            如果explain=True，返回(是否为关键决策点, 解释数据)元组
        """
        start_time = time.time()

        try:
            # 类型检查和转换
            if not isinstance(state, DouDizhuState):
                raise ValueError(f"Expected DouDizhuState, got {type(state)}")

            reasons = []
            importance_score = 0.0

            # 规则1: 终局状态检测
            is_end_game, end_game_score = self._check_end_game(state)
            if is_end_game:
                reasons.append("终局状态")
                importance_score += end_game_score
                self.stats["end_game_moments"] += 1

            # 规则2: 炸弹或火箭出牌检测
            is_bomb_play, bomb_score = self._check_bomb_play(state)
            if is_bomb_play:
                reasons.append("炸弹/火箭出牌")
                importance_score += bomb_score
                self.stats["bomb_play_moments"] += 1

            # 规则3: 关键牌数检测
            is_critical_card_count, card_count_score = self._check_critical_card_count(state)
            if is_critical_card_count:
                reasons.append("关键牌数")
                importance_score += card_count_score
                self.stats["critical_card_count_moments"] += 1

            # 规则4: 机器学习模型预测（如果启用）
            model_prediction = 0.0
            is_model_key_moment = False
            if self.use_ml_model and self.model is not None:
                model_prediction = self._predict_with_model(state)
                if model_prediction > 0.5:
                    reasons.append("模型预测")
                    importance_score += model_prediction
                    self.stats["model_predicted_moments"] += 1
                    is_model_key_moment = True  # 标记模型预测为关键点

            # 综合判断是否为关键决策点
            # 如果规则检测到关键点，或模型预测为关键点，则认为是关键决策点
            is_key_moment = (len(reasons) > 0 and importance_score > 1.0) or is_model_key_moment

            # 更新统计信息
            time_spent = time.time() - start_time
            self.update_stats(time_spent)
            
            if is_key_moment:
                self.stats["key_moments_detected"] += 1

            # 如果启用解释模式，返回解释数据
            if explain:
                explanation_data = self._generate_explanation(
                    state, is_key_moment, reasons, importance_score,
                    {
                        "end_game": (is_end_game, end_game_score),
                        "bomb_play": (is_bomb_play, bomb_score),
                        "critical_card_count": (is_critical_card_count, card_count_score),
                        "model_prediction": model_prediction,
                        "is_model_key_moment": is_model_key_moment
                    }
                )
                return is_key_moment, explanation_data
            else:
                return is_key_moment

        except Exception as e:
            logging.error(f"Key moment detection error: {e}")
            time_spent = time.time() - start_time
            self.update_stats(time_spent, success=False)
            
            if explain:
                explanation_data = {
                    "error": str(e),
                    "component_name": self.name,
                    "success": False
                }
                return False, explanation_data
            else:
                return False

    def is_key_moment(self, state: State) -> bool:
        """
        判断当前状态是否为关键决策点（便捷方法）

        Args:
            state: 当前状态

        Returns:
            是否为关键决策点
        """
        return self.decide(state, [])

    def _check_end_game(self, state: DouDizhuState) -> Tuple[bool, float]:
        """
        检查是否处于终局状态

        Args:
            state: 当前状态

        Returns:
            (是否为终局状态, 重要性分数)
        """
        # 仅在出牌阶段检查
        if state.game_phase != GamePhase.PLAYING:
            return False, 0.0

        # 检查任一玩家的手牌数量是否小于阈值
        for hand in state.hands:
            if len(hand) <= self.end_game_threshold:
                # 计算重要性分数：牌越少，分数越高
                score = 1.0 + (self.end_game_threshold - len(hand)) * 0.2
                return True, min(score, 2.0)  # 设置上限

        return False, 0.0

    def _check_bomb_play(self, state: DouDizhuState) -> Tuple[bool, float]:
        """
        检查是否有炸弹或火箭出牌

        Args:
            state: 当前状态

        Returns:
            (是否有炸弹或火箭出牌, 重要性分数)
        """
        # 仅在出牌阶段检查
        if state.game_phase != GamePhase.PLAYING:
            return False, 0.0

        # 检查上一手牌是否为炸弹或火箭
        if state.last_move is not None:
            card_type = state.last_move.card_type
            if card_type == CardGroupType.BOMB:
                return True, self.bomb_weight
            elif card_type == CardGroupType.ROCKET:
                return True, self.rocket_weight

        return False, 0.0

    def _check_critical_card_count(self, state: DouDizhuState) -> Tuple[bool, float]:
        """
        检查是否处于关键牌数状态

        Args:
            state: 当前状态

        Returns:
            (是否为关键牌数状态, 重要性分数)
        """
        # 仅在出牌阶段检查
        if state.game_phase != GamePhase.PLAYING:
            return False, 0.0

        # 获取初始牌数（假设每个玩家最初有17张牌）
        initial_card_count = 17
        if state.landlord is not None and state.landlord < len(state.hands):
            initial_card_count = 20  # 地主有20张牌

        # 计算当前玩家的牌数比例
        current_player = state.current_player
        current_card_count = len(state.hands[current_player])
        card_ratio = current_card_count / initial_card_count

        # 判断是否在关键牌数阈值附近（阈值宽度从0.1缩小到0.05，避免误判）
        if abs(card_ratio - self.critical_card_count_threshold) < 0.05:
            # 计算重要性分数：越接近阈值，分数越高
            score = 1.0 + (0.05 - abs(card_ratio - self.critical_card_count_threshold)) * 20
            return True, score

        return False, 0.0

    def _predict_with_model(self, state: DouDizhuState) -> float:
        """
        使用机器学习模型预测是否为关键决策点

        Args:
            state: 当前状态

        Returns:
            预测的重要性分数
        """
        if not self.use_ml_model or self.model is None:
            return 0.0

        try:
            # 提取特征
            features = self._extract_features(state)
            
            # 使用模型预测
            prediction = self.model.predict(features.reshape(1, -1))[0]
            
            return float(prediction)
        except Exception as e:
            logging.error(f"Model prediction error: {e}")
            return 0.0

    def _extract_features(self, state: DouDizhuState) -> np.ndarray:
        """
        从状态中提取特征用于模型预测

        Args:
            state: 当前状态

        Returns:
            特征向量
        """
        # 示例特征提取，实际实现应根据具体需求调整
        features = []
        
        # 1. 当前玩家手牌数量
        features.append(len(state.hands[state.current_player]))
        
        # 2. 其他玩家手牌数量
        for i in range(len(state.hands)):
            if i != state.current_player:
                features.append(len(state.hands[i]))
        
        # 3. 当前轮数（基于历史记录长度的近似值）
        features.append(len(state.history))
        
        # 4. 连续不出的次数
        features.append(state.num_passes)
        
        # 5. 已出牌的数量
        features.append(len(state.played_cards))
        
        # 6. 是否为地主
        features.append(1 if state.landlord == state.current_player else 0)
        
        return np.array(features, dtype=np.float32)

    def _generate_explanation(self, 
                              state: DouDizhuState, 
                              is_key_moment: bool, 
                              reasons: List[str],
                              importance_score: float,
                              details: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成解释数据

        Args:
            state: 当前状态
            is_key_moment: 是否为关键决策点
            reasons: 判断理由列表
            importance_score: 重要性分数
            details: 详细信息

        Returns:
            解释数据
        """
        # 获取基础解释数据
        explanation_data = self.get_explanation_data()
        
        # 添加关键决策点特定解释数据
        explanation_data.update({
            "is_key_moment": is_key_moment,
            "reasons": reasons,
            "importance_score": importance_score,
            "details": details,
            "current_player": state.current_player,
            "current_hand_size": len(state.hands[state.current_player]),
            "game_phase": state.game_phase.name,
            "total_rounds": len(state.history)
        })
        
        return explanation_data

    def get_explanation_data(self) -> Dict[str, Any]:
        """
        获取基础解释数据

        Returns:
            包含基础解释数据的字典
        """
        # 获取基类的解释数据
        explanation_data = super().get_explanation_data()
        
        # 添加特定统计信息
        explanation_data["component_stats"].update({
            "key_moments_ratio": self.stats["key_moments_detected"] / max(1, self.stats["calls"]),
            "end_game_moments": self.stats["end_game_moments"],
            "bomb_play_moments": self.stats["bomb_play_moments"],
            "critical_card_count_moments": self.stats["critical_card_count_moments"],
            "model_predicted_moments": self.stats["model_predicted_moments"]
        })
        
        return explanation_data 