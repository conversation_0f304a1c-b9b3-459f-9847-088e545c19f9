"""
数据服务适配器

该适配器将现有的数据加载系统适配为DataInterface接口，
实现zhuchengxu模块与数据服务的解耦。

设计目标:
- 适配现有数据加载系统为标准接口
- 提供基础的数据管理功能
- 实现fail-fast原则

作者: Full Stack Dev James
版本: v1.0
"""

import os
import pickle
import json
from typing import Dict, Any, Optional, List, Union, Iterator
from pathlib import Path
from datetime import datetime

from cardgame_ai.interfaces.data_interface import (
    DataInterface, DataLoadResult, DatasetInfo, DataFormat, DataSplit
)


class OptimizedDataAdapter(DataInterface):
    """优化数据服务适配器
    
    提供基础的数据管理功能，适配为DataInterface接口。
    
    注意:
        这是一个简化的实现，主要用于解耦目的。
        生产环境建议使用专业的数据管理系统。
    """
    
    def __init__(self, data_dir: str = "data"):
        """初始化数据适配器
        
        Args:
            data_dir: 数据目录
        """
        self._data_dir = Path(data_dir)
        self._data_dir.mkdir(parents=True, exist_ok=True)
        self._cache = {}
    
    def load_dataset(self, dataset_name: str, split: DataSplit = DataSplit.ALL,
                    batch_size: Optional[int] = None,
                    shuffle: bool = False) -> DataLoadResult:
        """加载数据集"""
        try:
            start_time = datetime.now()
            
            # 构建数据文件路径
            if split == DataSplit.ALL:
                data_path = self._data_dir / f"{dataset_name}.pkl"
            else:
                data_path = self._data_dir / f"{dataset_name}_{split.value}.pkl"
            
            if not data_path.exists():
                return DataLoadResult(
                    success=False,
                    error_message=f"数据文件不存在: {data_path}"
                )
            
            # 加载数据
            with open(data_path, 'rb') as f:
                data = pickle.load(f)
            
            load_time = (datetime.now() - start_time).total_seconds()
            
            return DataLoadResult(
                success=True,
                data=data,
                metadata={
                    "dataset_name": dataset_name,
                    "split": split.value,
                    "file_path": str(data_path)
                },
                load_time=load_time,
                data_size=data_path.stat().st_size
            )
            
        except Exception as e:
            return DataLoadResult(
                success=False,
                error_message=f"加载数据集失败: {e}"
            )
    
    def save_dataset(self, data: Any, dataset_name: str,
                    format: DataFormat = DataFormat.PICKLE,
                    metadata: Optional[Dict[str, Any]] = None) -> bool:
        """保存数据集"""
        try:
            if format == DataFormat.PICKLE:
                data_path = self._data_dir / f"{dataset_name}.pkl"
                with open(data_path, 'wb') as f:
                    pickle.dump(data, f)
            elif format == DataFormat.JSON:
                data_path = self._data_dir / f"{dataset_name}.json"
                with open(data_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
            else:
                raise ValueError(f"不支持的保存格式: {format}")
            
            # 保存元数据
            if metadata:
                meta_path = self._data_dir / f"{dataset_name}_metadata.json"
                with open(meta_path, 'w', encoding='utf-8') as f:
                    json.dump(metadata, f, indent=2, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            raise RuntimeError(f"保存数据集失败: {e}") from e
    
    def create_dataloader(self, dataset_name: str, batch_size: int,
                         shuffle: bool = True, num_workers: int = 0,
                         **kwargs) -> Iterator:
        """创建数据加载器"""
        try:
            # 加载数据集
            result = self.load_dataset(dataset_name)
            if not result.success:
                raise RuntimeError(result.error_message)
            
            data = result.data
            
            # 简化的数据加载器实现
            class SimpleDataLoader:
                def __init__(self, data, batch_size, shuffle):
                    self.data = data
                    self.batch_size = batch_size
                    self.shuffle = shuffle
                
                def __iter__(self):
                    indices = list(range(len(self.data)))
                    if self.shuffle:
                        import random
                        random.shuffle(indices)
                    
                    for i in range(0, len(indices), self.batch_size):
                        batch_indices = indices[i:i + self.batch_size]
                        batch = [self.data[idx] for idx in batch_indices]
                        yield batch
            
            return SimpleDataLoader(data, batch_size, shuffle)
            
        except Exception as e:
            raise RuntimeError(f"创建数据加载器失败: {e}") from e
    
    def preprocess_data(self, data: Any, preprocessing_config: Dict[str, Any]) -> Any:
        """预处理数据 - 简化实现"""
        # 当前实现不进行预处理，直接返回原数据
        return data
    
    def validate_data(self, data: Any, schema: Optional[Dict[str, Any]] = None) -> bool:
        """验证数据 - 简化实现"""
        # 基础验证：检查数据是否为空
        if data is None:
            return False
        
        # 如果是列表或字典，检查是否为空
        if isinstance(data, (list, dict)) and len(data) == 0:
            return False
        
        return True
    
    def get_dataset_info(self, dataset_name: str) -> Optional[DatasetInfo]:
        """获取数据集信息"""
        try:
            data_path = self._data_dir / f"{dataset_name}.pkl"
            if not data_path.exists():
                return None
            
            # 加载元数据
            meta_path = self._data_dir / f"{dataset_name}_metadata.json"
            metadata = {}
            if meta_path.exists():
                with open(meta_path, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
            
            stat = data_path.stat()
            
            return DatasetInfo(
                name=dataset_name,
                version=metadata.get("version", "1.0"),
                description=metadata.get("description", ""),
                total_samples=metadata.get("total_samples", 0),
                features=metadata.get("features", []),
                labels=metadata.get("labels", []),
                data_format=DataFormat.PICKLE,
                file_size=stat.st_size,
                created_at=datetime.fromtimestamp(stat.st_ctime).isoformat(),
                updated_at=datetime.fromtimestamp(stat.st_mtime).isoformat()
            )
            
        except Exception:
            return None
    
    def list_datasets(self) -> List[str]:
        """列出所有数据集"""
        try:
            datasets = []
            for file_path in self._data_dir.glob("*.pkl"):
                dataset_name = file_path.stem
                # 排除分割数据集文件
                if not any(dataset_name.endswith(f"_{split.value}") for split in DataSplit):
                    datasets.append(dataset_name)
            return datasets
        except Exception:
            return []
    
    def delete_dataset(self, dataset_name: str) -> bool:
        """删除数据集"""
        try:
            # 删除主数据文件
            data_path = self._data_dir / f"{dataset_name}.pkl"
            if data_path.exists():
                data_path.unlink()
            
            # 删除元数据文件
            meta_path = self._data_dir / f"{dataset_name}_metadata.json"
            if meta_path.exists():
                meta_path.unlink()
            
            # 删除分割数据文件
            for split in DataSplit:
                split_path = self._data_dir / f"{dataset_name}_{split.value}.pkl"
                if split_path.exists():
                    split_path.unlink()
            
            return True
            
        except Exception:
            return False
    
    def split_dataset(self, dataset_name: str, 
                     train_ratio: float = 0.8,
                     val_ratio: float = 0.1,
                     test_ratio: float = 0.1,
                     random_seed: Optional[int] = None) -> bool:
        """分割数据集 - 简化实现"""
        try:
            # 加载原始数据
            result = self.load_dataset(dataset_name)
            if not result.success:
                return False
            
            data = result.data
            if not isinstance(data, list):
                return False
            
            # 设置随机种子
            if random_seed is not None:
                import random
                random.seed(random_seed)
            
            # 随机打乱数据
            import random
            shuffled_data = data.copy()
            random.shuffle(shuffled_data)
            
            # 计算分割点
            total_size = len(shuffled_data)
            train_size = int(total_size * train_ratio)
            val_size = int(total_size * val_ratio)
            
            # 分割数据
            train_data = shuffled_data[:train_size]
            val_data = shuffled_data[train_size:train_size + val_size]
            test_data = shuffled_data[train_size + val_size:]
            
            # 保存分割后的数据
            self.save_dataset(train_data, f"{dataset_name}_train")
            self.save_dataset(val_data, f"{dataset_name}_validation")
            self.save_dataset(test_data, f"{dataset_name}_test")
            
            return True
            
        except Exception:
            return False
    
    def merge_datasets(self, dataset_names: List[str], 
                      output_name: str) -> bool:
        """合并数据集 - 简化实现"""
        try:
            merged_data = []
            
            for dataset_name in dataset_names:
                result = self.load_dataset(dataset_name)
                if result.success and isinstance(result.data, list):
                    merged_data.extend(result.data)
            
            if merged_data:
                return self.save_dataset(merged_data, output_name)
            
            return False
            
        except Exception:
            return False
    
    def sample_dataset(self, dataset_name: str, sample_size: int,
                      method: str = "random") -> DataLoadResult:
        """采样数据集"""
        try:
            result = self.load_dataset(dataset_name)
            if not result.success:
                return result
            
            data = result.data
            if not isinstance(data, list) or len(data) < sample_size:
                return DataLoadResult(
                    success=False,
                    error_message="数据不足或格式不正确"
                )
            
            if method == "random":
                import random
                sampled_data = random.sample(data, sample_size)
            else:
                # 其他采样方法的简化实现
                sampled_data = data[:sample_size]
            
            return DataLoadResult(
                success=True,
                data=sampled_data,
                metadata={
                    "original_dataset": dataset_name,
                    "sample_size": sample_size,
                    "method": method
                }
            )
            
        except Exception as e:
            return DataLoadResult(
                success=False,
                error_message=f"采样失败: {e}"
            )

    def get_data_statistics(self, dataset_name: str) -> Dict[str, Any]:
        """获取数据统计信息"""
        try:
            result = self.load_dataset(dataset_name)
            if not result.success:
                return {}

            data = result.data
            stats = {
                "sample_count": len(data) if hasattr(data, '__len__') else 0,
                "data_type": type(data).__name__
            }

            if isinstance(data, list) and data:
                stats["first_item_type"] = type(data[0]).__name__

            return stats

        except Exception:
            return {}

    def cache_dataset(self, dataset_name: str, cache_key: Optional[str] = None) -> bool:
        """缓存数据集"""
        try:
            if cache_key is None:
                cache_key = dataset_name

            result = self.load_dataset(dataset_name)
            if result.success:
                self._cache[cache_key] = result.data
                return True

            return False

        except Exception:
            return False

    def clear_cache(self, cache_key: Optional[str] = None) -> bool:
        """清除缓存"""
        try:
            if cache_key is None:
                self._cache.clear()
            elif cache_key in self._cache:
                del self._cache[cache_key]

            return True

        except Exception:
            return False

    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        return {
            "cache_size": len(self._cache),
            "cached_datasets": list(self._cache.keys()),
            "hit_rate": 0.0  # 简化实现，不计算命中率
        }

    def transform_data(self, data: Any, transformations: List[Dict[str, Any]]) -> Any:
        """转换数据 - 简化实现"""
        # 当前实现不进行转换，直接返回原数据
        return data

    def export_data(self, dataset_name: str, output_path: Union[str, Path],
                   format: DataFormat = DataFormat.CSV) -> bool:
        """导出数据 - 简化实现"""
        try:
            result = self.load_dataset(dataset_name)
            if not result.success:
                return False

            output_path = Path(output_path)

            if format == DataFormat.JSON:
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(result.data, f, indent=2, ensure_ascii=False)
                return True
            elif format == DataFormat.PICKLE:
                with open(output_path, 'wb') as f:
                    pickle.dump(result.data, f)
                return True

            return False

        except Exception:
            return False

    def import_data(self, input_path: Union[str, Path], dataset_name: str,
                   format: Optional[DataFormat] = None) -> DataLoadResult:
        """导入数据"""
        try:
            input_path = Path(input_path)

            if not input_path.exists():
                return DataLoadResult(
                    success=False,
                    error_message=f"输入文件不存在: {input_path}"
                )

            # 自动检测格式
            if format is None:
                suffix = input_path.suffix.lower()
                if suffix == '.json':
                    format = DataFormat.JSON
                elif suffix == '.pkl':
                    format = DataFormat.PICKLE
                else:
                    return DataLoadResult(
                        success=False,
                        error_message=f"不支持的文件格式: {suffix}"
                    )

            # 加载数据
            if format == DataFormat.JSON:
                with open(input_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            elif format == DataFormat.PICKLE:
                with open(input_path, 'rb') as f:
                    data = pickle.load(f)
            else:
                return DataLoadResult(
                    success=False,
                    error_message=f"不支持的格式: {format}"
                )

            # 保存数据集
            if self.save_dataset(data, dataset_name, format):
                return DataLoadResult(
                    success=True,
                    data=data,
                    metadata={
                        "imported_from": str(input_path),
                        "format": format.value
                    }
                )

            return DataLoadResult(
                success=False,
                error_message="保存数据集失败"
            )

        except Exception as e:
            return DataLoadResult(
                success=False,
                error_message=f"导入数据失败: {e}"
            )

    def backup_dataset(self, dataset_name: str,
                      backup_path: Optional[Union[str, Path]] = None) -> str:
        """备份数据集"""
        try:
            if backup_path is None:
                backup_dir = self._data_dir / "backups"
                backup_dir.mkdir(exist_ok=True)
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = backup_dir / f"{dataset_name}_{timestamp}.pkl"
            else:
                backup_path = Path(backup_path)

            # 加载并保存数据
            result = self.load_dataset(dataset_name)
            if result.success:
                with open(backup_path, 'wb') as f:
                    pickle.dump(result.data, f)
                return str(backup_path)

            raise RuntimeError("加载数据集失败")

        except Exception as e:
            raise RuntimeError(f"备份数据集失败: {e}") from e

    def restore_dataset(self, backup_path: Union[str, Path],
                       dataset_name: str) -> bool:
        """恢复数据集"""
        try:
            backup_path = Path(backup_path)

            if not backup_path.exists():
                return False

            with open(backup_path, 'rb') as f:
                data = pickle.load(f)

            return self.save_dataset(data, dataset_name)

        except Exception:
            return False
