"""
生成合成人类对局数据

用于生成合成的人类对局数据，以便训练人类策略网络。
"""
import os
import numpy as np
import json
import random
import logging
from tqdm import tqdm
import argparse
from typing import List, Dict, Any, Tuple

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
)
logger = logging.getLogger(__name__)

# 牌型常量
CARD_TYPES = {
    "PASS": 0,
    "SINGLE": 1,
    "PAIR": 2,
    "TRIO": 3,
    "TRIO_WITH_SINGLE": 4,
    "TRIO_WITH_PAIR": 5,
    "STRAIGHT": 6,
    "PAIR_STRAIGHT": 7,
    "TRIO_STRAIGHT": 8,
    "TRIO_STRAIGHT_WITH_SINGLES": 9,
    "TRIO_STRAIGHT_WITH_PAIRS": 10,
    "FOUR_WITH_TWO": 11,
    "FOUR_WITH_TWO_PAIRS": 12,
    "BOMB": 13,
    "ROCKET": 14
}

def generate_random_state(state_dim: int = 627) -> np.ndarray:
    """
    生成随机状态向量
    
    Args:
        state_dim: 状态向量维度
        
    Returns:
        随机状态向量
    """
    # 生成随机状态向量
    state = np.random.rand(state_dim)
    
    # 对于某些特征进行特殊处理，使其更接近真实数据
    
    # 手牌特征（前300维）- 使其更稀疏
    hand_cards = state[:300]
    hand_cards[np.random.rand(300) > 0.2] = 0
    
    # 已出牌特征 - 使其更稀疏
    played_cards = state[300:354]
    played_cards[np.random.rand(54) > 0.3] = 0
    
    # 角色标识（地主/农民）- 独热编码
    role_idx = np.random.randint(0, 3)
    role = np.zeros(3)
    role[role_idx] = 1
    state[354:357] = role
    
    # 当前玩家标识 - 独热编码
    current_player_idx = np.random.randint(0, 3)
    current_player = np.zeros(3)
    current_player[current_player_idx] = 1
    state[357:360] = current_player
    
    return state

def generate_action_distribution(state: np.ndarray) -> np.ndarray:
    """
    根据状态生成动作概率分布
    
    模拟人类玩家的决策偏好
    
    Args:
        state: 状态向量
        
    Returns:
        动作概率分布
    """
    # 初始化均匀分布
    probs = np.ones(15) / 15
    
    # 根据状态特征调整概率
    
    # 1. 手牌数量影响（假设前300维中非零元素数量代表手牌数量）
    hand_cards = state[:300]
    hand_count = np.count_nonzero(hand_cards)
    
    # 手牌少时更倾向于出单牌和对子
    if hand_count < 50:
        probs[CARD_TYPES["SINGLE"]] *= 1.5
        probs[CARD_TYPES["PAIR"]] *= 1.3
    # 手牌多时更倾向于出顺子和连对
    else:
        probs[CARD_TYPES["STRAIGHT"]] *= 1.4
        probs[CARD_TYPES["PAIR_STRAIGHT"]] *= 1.3
    
    # 2. 角色影响（地主/农民）
    is_landlord = state[354] > 0.5
    if is_landlord:
        # 地主更倾向于控制节奏，出单牌和对子
        probs[CARD_TYPES["SINGLE"]] *= 1.2
        probs[CARD_TYPES["PAIR"]] *= 1.2
    else:
        # 农民更倾向于配合，出大牌和炸弹
        probs[CARD_TYPES["BOMB"]] *= 1.5
        probs[CARD_TYPES["ROCKET"]] *= 1.5
    
    # 3. 随机波动（模拟人类不确定性）
    probs *= np.random.uniform(0.8, 1.2, size=15)
    
    # 归一化
    probs /= probs.sum()
    
    return probs

def sample_action(probs: np.ndarray) -> int:
    """
    根据概率分布采样动作
    
    Args:
        probs: 动作概率分布
        
    Returns:
        采样的动作索引
    """
    return np.random.choice(len(probs), p=probs)

def generate_human_data(
    num_samples: int,
    output_path: str,
    state_dim: int = 627,
    format: str = "jsonl"
) -> None:
    """
    生成人类对局数据
    
    Args:
        num_samples: 样本数量
        output_path: 输出路径
        state_dim: 状态向量维度
        format: 输出格式 (jsonl, json, npy)
    """
    logger.info(f"生成{num_samples}个合成人类对局数据样本")
    
    data = []
    
    for _ in tqdm(range(num_samples)):
        # 生成随机状态
        state = generate_random_state(state_dim)
        
        # 生成动作概率分布
        action_probs = generate_action_distribution(state)
        
        # 采样动作
        action = sample_action(action_probs)
        
        # 添加到数据集
        data.append({
            "game_state": state.tolist(),
            "human_action": int(action)
        })
    
    # 保存数据
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    if format == "jsonl":
        with open(output_path, 'w', encoding='utf-8') as f:
            for item in data:
                f.write(json.dumps(item) + '\n')
    
    elif format == "json":
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    elif format == "npy":
        # 转换为numpy数组
        states = np.array([item["game_state"] for item in data])
        actions = np.array([item["human_action"] for item in data])
        
        # 保存
        np.savez(output_path, states=states, actions=actions)
    
    else:
        raise ValueError(f"不支持的格式: {format}")
    
    logger.info(f"数据已保存至: {output_path}")

def main():
    parser = argparse.ArgumentParser(description="生成合成人类对局数据")
    parser.add_argument("--num_samples", type=int, default=1000, help="样本数量")
    parser.add_argument("--output_path", type=str, default="cardgame_ai/data/human_logs/synthetic_data.jsonl", help="输出路径")
    parser.add_argument("--state_dim", type=int, default=627, help="状态向量维度")
    parser.add_argument("--format", type=str, default="jsonl", choices=["jsonl", "json", "npy"], help="输出格式")
    
    args = parser.parse_args()
    
    generate_human_data(
        num_samples=args.num_samples,
        output_path=args.output_path,
        state_dim=args.state_dim,
        format=args.format
    )

if __name__ == "__main__":
    main()
