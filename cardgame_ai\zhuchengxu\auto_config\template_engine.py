"""
配置模板引擎

基于Jinja2模板引擎，动态生成配置文件，
支持硬件信息注入和参数自动调整。
"""

import os
import yaml
import json
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from jinja2 import Template, Environment, FileSystemLoader, TemplateNotFound
from .utils import CrossPlatformUtils

class ConfigTemplateEngine:
    """配置模板引擎"""
    
    def __init__(self, template_dir: Optional[str] = None):
        """
        初始化模板引擎
        
        Args:
            template_dir: 模板目录路径，默认为相对路径
        """
        self.logger = CrossPlatformUtils.setup_logging()
        
        # 设置模板目录
        if template_dir is None:
            current_dir = Path(__file__).parent
            template_dir = current_dir.parent / "templates"
        
        self.template_dir = Path(template_dir)
        self.template_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化Jinja2环境
        self.env = Environment(
            loader=FileSystemLoader(str(self.template_dir)),
            trim_blocks=True,
            lstrip_blocks=True
        )
        
        # 添加自定义过滤器和函数
        self.env.filters['to_json'] = json.dumps
        self.env.filters['round'] = round
        self.env.globals['min'] = min
        self.env.globals['max'] = max
        
        self.logger.info(f"模板引擎初始化完成，模板目录: {self.template_dir}")
    
    def render_config(self, template_name: str, 
                     hardware_info: Dict[str, Any],
                     tuned_params: Dict[str, Any],
                     algorithm: str = 'efficient_zero',
                     environment: str = 'doudizhu',
                     additional_vars: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        渲染配置模板
        
        Args:
            template_name: 模板文件名
            hardware_info: 硬件信息
            tuned_params: 调优后的参数
            algorithm: 算法类型
            environment: 环境类型
            additional_vars: 额外的模板变量
            
        Returns:
            渲染后的配置字典
        """
        try:
            self.logger.info(f"开始渲染模板: {template_name}")
            
            # 准备模板变量
            template_vars = self._prepare_template_vars(
                hardware_info, tuned_params, algorithm, environment, additional_vars
            )
            
            # 加载并渲染模板
            template = self.env.get_template(template_name)
            rendered_content = template.render(**template_vars)
            
            # 解析YAML配置
            config = yaml.safe_load(rendered_content)
            
            self.logger.info(f"模板渲染完成: {template_name}")
            return config
            
        except TemplateNotFound:
            self.logger.error(f"模板文件未找到: {template_name}")
            raise FileNotFoundError(f"模板文件未找到: {template_name}")
        except yaml.YAMLError as e:
            self.logger.error(f"YAML解析错误: {e}")
            raise ValueError(f"YAML解析错误: {e}")
        except Exception as e:
            self.logger.error(f"模板渲染失败: {e}")
            raise RuntimeError(f"模板渲染失败: {e}")
    
    def _prepare_template_vars(self, hardware_info: Dict[str, Any],
                              tuned_params: Dict[str, Any],
                              algorithm: str,
                              environment: str,
                              additional_vars: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """
        准备模板变量
        
        Args:
            hardware_info: 硬件信息
            tuned_params: 调优参数
            algorithm: 算法类型
            environment: 环境类型
            additional_vars: 额外变量
            
        Returns:
            模板变量字典
        """
        gpu_specs = hardware_info.get('gpu', [])
        cpu_specs = hardware_info.get('cpu', {})
        memory_specs = hardware_info.get('memory', {})
        system_info = hardware_info.get('system', {})
        
        # 基础硬件变量
        template_vars = {
            # GPU相关
            'GPU_COUNT': len(gpu_specs),
            'GPU_IDS': list(range(len(gpu_specs))),
            'GPU_MEMORY': gpu_specs[0].get('memory_total_gb', 0) if gpu_specs else 0,
            'GPU_MODEL': gpu_specs[0].get('name', 'Unknown') if gpu_specs else 'CPU',
            'TOTAL_GPU_MEMORY': sum(gpu.get('memory_total_gb', 0) for gpu in gpu_specs),
            
            # CPU相关
            'CPU_CORES': cpu_specs.get('physical_cores', 8),
            'CPU_THREADS': cpu_specs.get('logical_cores', 16),
            'CPU_ARCH': cpu_specs.get('architecture', 'x86_64'),
            
            # 内存相关
            'SYSTEM_MEMORY': memory_specs.get('total_gb', 16),
            'AVAILABLE_MEMORY': memory_specs.get('available_gb', 12),
            
            # 系统相关
            'SYSTEM_TYPE': system_info.get('system', 'Linux'),
            'IS_WINDOWS': system_info.get('system', '').lower() == 'windows',
            'IS_LINUX': system_info.get('system', '').lower() == 'linux',
            
            # 算法和环境
            'ALGORITHM': algorithm,
            'ENVIRONMENT': environment,
            
            # 调优参数
            'BATCH_SIZE': tuned_params.get('batch_size', 128),
            'LEARNING_RATE': tuned_params.get('learning_rate', 0.001),
            'NUM_WORKERS': tuned_params.get('num_workers', 8),
            'MCTS_SIMULATIONS': tuned_params.get('mcts_simulations', 50),
            'PREFETCH_FACTOR': tuned_params.get('prefetch_factor', 4),
            'MIXED_PRECISION': tuned_params.get('mixed_precision', False),
            'DISTRIBUTED_ENABLED': tuned_params.get('distributed', False),
            'GRADIENT_ACCUMULATION': tuned_params.get('gradient_accumulation_steps', 1),
            
            # 内存优化设置
            'MEMORY_OPTIMIZATION': tuned_params.get('memory_optimization', {}),
            
            # 时间戳和标识
            'TIMESTAMP': CrossPlatformUtils.execute_command('date')['stdout'] if not CrossPlatformUtils.is_windows() else CrossPlatformUtils.execute_command('echo %date% %time%')['stdout'],
            'CONFIG_VERSION': '1.0.0'
        }
        
        # 添加EfficientZero特定参数
        if algorithm == 'efficient_zero' and 'replay_buffer_size' in tuned_params:
            template_vars.update({
                'REPLAY_BUFFER_SIZE': tuned_params.get('replay_buffer_size', 100000),
                'UNROLL_STEPS': tuned_params.get('unroll_steps', 5),
                'TD_STEPS': tuned_params.get('td_steps', 5),
                'MODEL_BUFFER_SIZE': tuned_params.get('model_buffer_size', 50000),
                'REANALYZE_RATIO': tuned_params.get('reanalyze_ratio', 0.5)
            })
        
        # 合并额外变量
        if additional_vars:
            template_vars.update(additional_vars)
        
        return template_vars
    
    def create_base_template(self) -> str:
        """
        创建基础配置模板
        
        Returns:
            模板文件路径
        """
        template_content = '''# 自动生成的配置文件
# 生成时间: {{ TIMESTAMP }}
# 硬件配置: {{ GPU_COUNT }}x {{ GPU_MODEL }}, {{ CPU_CORES }}核心CPU
# 算法: {{ ALGORITHM }}, 环境: {{ ENVIRONMENT }}

defaults:
  - algorithms/{{ ALGORITHM }}: base
  - environments/{{ ENVIRONMENT }}: efficient_zero_config
  {% if DISTRIBUTED_ENABLED -%}
  - hardware: multi_gpu
  {% else -%}
  - hardware: single_gpu
  {% endif -%}
  - _self_

# 硬件信息
hardware:
  gpu:
    count: {{ GPU_COUNT }}
    memory_gb: {{ GPU_MEMORY }}
    total_memory_gb: {{ TOTAL_GPU_MEMORY }}
    model: "{{ GPU_MODEL }}"
    ids: {{ GPU_IDS | to_json }}
  cpu:
    cores: {{ CPU_CORES }}
    threads: {{ CPU_THREADS }}
    architecture: "{{ CPU_ARCH }}"
  memory:
    total_gb: {{ SYSTEM_MEMORY }}
    available_gb: {{ AVAILABLE_MEMORY }}
  system:
    type: "{{ SYSTEM_TYPE }}"
    is_windows: {{ IS_WINDOWS | lower }}
    is_linux: {{ IS_LINUX | lower }}

# 训练配置
training:
  batch_size: {{ BATCH_SIZE }}
  learning_rate: {{ LEARNING_RATE }}
  num_workers: {{ NUM_WORKERS }}
  prefetch_factor: {{ PREFETCH_FACTOR }}
  gradient_accumulation_steps: {{ GRADIENT_ACCUMULATION }}
  epochs: 1000
  
  # 优化器配置
  optimizer:
    name: "AdamW"
    weight_decay: 0.01
    betas: [0.9, 0.999]
  
  # 学习率调度
  scheduler:
    name: "CosineAnnealingWarmRestarts"
    T_0: 100
    T_mult: 2
    eta_min: 1e-6

# MCTS配置
mcts:
  num_simulations: {{ MCTS_SIMULATIONS }}
  parallel_threads: {{ min(CPU_CORES, 8) }}
  batch_size_inference: {{ min(32, BATCH_SIZE // 4) }}
  c_puct: 1.25
  dirichlet_alpha: 0.3
  exploration_fraction: 0.25

# 设备配置
device:
  type: "{% if GPU_COUNT > 0 %}cuda{% else %}cpu{% endif %}"
  ids: {{ GPU_IDS | to_json }}
  mixed_precision: {{ MIXED_PRECISION | lower }}
  benchmark: true
  deterministic: false

# 分布式配置
distributed:
  enabled: {{ DISTRIBUTED_ENABLED | lower }}
  {% if DISTRIBUTED_ENABLED -%}
  backend: "nccl"
  world_size: {{ GPU_COUNT }}
  rank: 0
  init_method: "env://"
  {% endif -%}

# 内存优化
memory:
  {% for key, value in MEMORY_OPTIMIZATION.items() -%}
  {{ key }}: {{ value | lower if value is boolean else value }}
  {% endfor -%}

# 日志配置
logging:
  level: "INFO"
  save_dir: "logs"
  tensorboard: true
  wandb: false
  
# 检查点配置
checkpoint:
  save_dir: "checkpoints"
  save_frequency: 1000
  max_to_keep: 5
  auto_resume: true

{% if ALGORITHM == 'efficient_zero' -%}
# EfficientZero特定配置
efficient_zero:
  replay_buffer_size: {{ REPLAY_BUFFER_SIZE | default(100000) }}
  unroll_steps: {{ UNROLL_STEPS | default(5) }}
  td_steps: {{ TD_STEPS | default(5) }}
  model_buffer_size: {{ MODEL_BUFFER_SIZE | default(50000) }}
  reanalyze_ratio: {{ REANALYZE_RATIO | default(0.5) }}
  value_prefix: true
  use_priority: true
{% endif -%}

# 配置元信息
meta:
  config_version: "{{ CONFIG_VERSION }}"
  generated_by: "AutoConfigManager"
  hardware_profile: "{{ GPU_COUNT }}gpu_{{ GPU_MODEL | replace(' ', '_') | lower }}"
  optimization_level: "{% if GPU_COUNT >= 4 %}high{% elif GPU_COUNT >= 2 %}medium{% else %}basic{% endif %}"
'''
        
        template_path = self.template_dir / "auto_config_template.yaml"
        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(template_content)
        
        self.logger.info(f"基础模板已创建: {template_path}")
        return str(template_path)

    def create_hardware_profiles(self) -> Dict[str, str]:
        """
        创建硬件配置文件模板

        Returns:
            创建的模板文件路径字典
        """
        profiles = {}
        hardware_profiles_dir = self.template_dir / "hardware_profiles"
        hardware_profiles_dir.mkdir(exist_ok=True)

        # 单GPU配置模板
        single_gpu_template = '''# 单GPU硬件配置模板
device:
  type: "cuda"
  ids: [0]
  mixed_precision: true
  benchmark: true

memory:
  pin_memory: true
  non_blocking: true
  persistent_workers: true
  cache_size_gb: 4

training:
  batch_size: 256
  num_workers: 8
  prefetch_factor: 4

distributed:
  enabled: false
'''

        single_gpu_path = hardware_profiles_dir / "single_gpu.yaml"
        with open(single_gpu_path, 'w', encoding='utf-8') as f:
            f.write(single_gpu_template)
        profiles['single_gpu'] = str(single_gpu_path)

        # 多GPU配置模板
        multi_gpu_template = '''# 多GPU硬件配置模板
device:
  type: "cuda"
  ids: {{ GPU_IDS | to_json }}
  mixed_precision: true
  benchmark: true

memory:
  pin_memory: true
  non_blocking: true
  persistent_workers: true
  cache_size_gb: {{ min(12, SYSTEM_MEMORY * 0.2) | round }}

training:
  batch_size: {{ BATCH_SIZE }}
  num_workers: {{ NUM_WORKERS }}
  prefetch_factor: {{ PREFETCH_FACTOR }}

distributed:
  enabled: true
  backend: "nccl"
  world_size: {{ GPU_COUNT }}
  find_unused_parameters: false
'''

        multi_gpu_path = hardware_profiles_dir / "multi_gpu.yaml"
        with open(multi_gpu_path, 'w', encoding='utf-8') as f:
            f.write(multi_gpu_template)
        profiles['multi_gpu'] = str(multi_gpu_path)

        self.logger.info(f"硬件配置模板已创建: {len(profiles)} 个")
        return profiles

    def create_algorithm_profiles(self) -> Dict[str, str]:
        """
        创建算法配置文件模板

        Returns:
            创建的模板文件路径字典
        """
        profiles = {}
        algorithm_profiles_dir = self.template_dir / "algorithm_profiles"
        algorithm_profiles_dir.mkdir(exist_ok=True)

        # EfficientZero算法模板
        efficient_zero_template = '''# EfficientZero算法配置模板
algorithm:
  name: "EfficientZero"
  version: "1.0"

model:
  representation_network:
    hidden_size: 512
    num_blocks: 16
    downsample: true

  dynamics_network:
    hidden_size: 512
    num_blocks: 16

  prediction_network:
    hidden_size: 512
    num_blocks: 2

training:
  replay_buffer_size: {{ REPLAY_BUFFER_SIZE | default(100000) }}
  batch_size: {{ BATCH_SIZE }}
  unroll_steps: {{ UNROLL_STEPS | default(5) }}
  td_steps: {{ TD_STEPS | default(5) }}

  # 损失函数权重
  value_loss_weight: 0.25
  reward_loss_weight: 1.0
  policy_loss_weight: 1.0
  consistency_loss_weight: 2.0

mcts:
  num_simulations: {{ MCTS_SIMULATIONS }}
  c_puct: 1.25
  dirichlet_alpha: 0.3
  exploration_fraction: 0.25

reanalyze:
  ratio: {{ REANALYZE_RATIO | default(0.5) }}
  buffer_size: {{ MODEL_BUFFER_SIZE | default(50000) }}
'''

        efficient_zero_path = algorithm_profiles_dir / "efficient_zero.yaml"
        with open(efficient_zero_path, 'w', encoding='utf-8') as f:
            f.write(efficient_zero_template)
        profiles['efficient_zero'] = str(efficient_zero_path)

        self.logger.info(f"算法配置模板已创建: {len(profiles)} 个")
        return profiles

    def save_config(self, config: Dict[str, Any],
                   output_path: str,
                   format: str = 'yaml') -> bool:
        """
        保存配置到文件

        Args:
            config: 配置字典
            output_path: 输出路径
            format: 输出格式 ('yaml' 或 'json')

        Returns:
            是否保存成功
        """
        try:
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)

            with open(output_path, 'w', encoding='utf-8') as f:
                if format.lower() == 'yaml':
                    yaml.dump(config, f, default_flow_style=False,
                             allow_unicode=True, indent=2)
                elif format.lower() == 'json':
                    json.dump(config, f, indent=2, ensure_ascii=False)
                else:
                    raise ValueError(f"不支持的格式: {format}")

            self.logger.info(f"配置已保存到: {output_path}")
            return True

        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")
            return False

    def validate_config(self, config: Dict[str, Any]) -> List[str]:
        """
        验证配置文件

        Args:
            config: 配置字典

        Returns:
            验证错误列表
        """
        errors = []

        # 检查必需的顶级键
        required_keys = ['training', 'device', 'mcts']
        for key in required_keys:
            if key not in config:
                errors.append(f"缺少必需的配置项: {key}")

        # 验证训练配置
        if 'training' in config:
            training = config['training']

            if 'batch_size' in training:
                batch_size = training['batch_size']
                if not isinstance(batch_size, int) or batch_size <= 0:
                    errors.append("batch_size必须是正整数")

            if 'learning_rate' in training:
                lr = training['learning_rate']
                if not isinstance(lr, (int, float)) or lr <= 0:
                    errors.append("learning_rate必须是正数")

            if 'num_workers' in training:
                workers = training['num_workers']
                if not isinstance(workers, int) or workers < 0:
                    errors.append("num_workers必须是非负整数")

        # 验证设备配置
        if 'device' in config:
            device = config['device']

            if 'type' in device:
                device_type = device['type']
                if device_type not in ['cuda', 'cpu']:
                    errors.append("device.type必须是'cuda'或'cpu'")

            if 'ids' in device and device.get('type') == 'cuda':
                ids = device['ids']
                if not isinstance(ids, list) or not all(isinstance(i, int) for i in ids):
                    errors.append("device.ids必须是整数列表")

        # 验证MCTS配置
        if 'mcts' in config:
            mcts = config['mcts']

            if 'num_simulations' in mcts:
                sims = mcts['num_simulations']
                if not isinstance(sims, int) or sims <= 0:
                    errors.append("mcts.num_simulations必须是正整数")

        return errors

    def get_available_templates(self) -> List[str]:
        """
        获取可用的模板列表

        Returns:
            模板文件名列表
        """
        templates = []

        for file_path in self.template_dir.rglob("*.yaml"):
            relative_path = file_path.relative_to(self.template_dir)
            templates.append(str(relative_path))

        return sorted(templates)

    def initialize_templates(self) -> Dict[str, Any]:
        """
        初始化所有模板文件

        Returns:
            创建的模板信息
        """
        self.logger.info("开始初始化模板文件...")

        result = {
            'base_template': self.create_base_template(),
            'hardware_profiles': self.create_hardware_profiles(),
            'algorithm_profiles': self.create_algorithm_profiles(),
            'available_templates': self.get_available_templates()
        }

        self.logger.info(f"模板初始化完成，共创建 {len(result['available_templates'])} 个模板")
        return result
