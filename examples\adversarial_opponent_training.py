#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
对抗性对手生成器示例

展示如何训练和使用对抗性对手生成器，以及如何将生成的对手集成到训练流程中。
"""

import os
import time
import random
import logging
import argparse
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from typing import List, Dict, Tuple, Any, Optional
import matplotlib.pyplot as plt

# 导入项目模块
from cardgame_ai.algorithms.opponent_generation.gan_policy_generator import AdversarialOpponentGenerator
from cardgame_ai.utils.opponent_distribution_switcher import OpponentDistributionSwitcher
from cardgame_ai.training.self_play import SelfPlay
from cardgame_ai.core.environment import Environment
from cardgame_ai.core.agent import Agent
from cardgame_ai.games.doudizhu.doudizhu_env import DoudizhuEnv
from cardgame_ai.games.doudizhu.agents.random_agent import RandomAgent
from cardgame_ai.games.doudizhu.agents.rule_agent import RuleAgent

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class SimplePolicyDataset(Dataset):
    """简单的策略数据集，用于GAN训练"""
    
    def __init__(self, policies, labels=None):
        """
        初始化

        Args:
            policies: 策略张量列表
            labels: 标签（可选）
        """
        self.policies = policies
        self.labels = labels
        
    def __len__(self):
        return len(self.policies)
    
    def __getitem__(self, idx):
        if self.labels is not None:
            return self.policies[idx], self.labels[idx]
        return self.policies[idx]


class SimplePolicyEncoder(nn.Module):
    """简单的策略编码器，将策略转换为向量表示"""
    
    def __init__(self, input_dim, output_dim):
        """
        初始化

        Args:
            input_dim: 输入维度
            output_dim: 输出维度
        """
        super().__init__()
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, 128),
            nn.ReLU(),
            nn.Linear(128, 128),
            nn.ReLU(),
            nn.Linear(128, output_dim)
        )
        
    def forward(self, x):
        return self.encoder(x)


class SimplePolicyDecoder(nn.Module):
    """简单的策略解码器，将向量表示转换为策略"""
    
    def __init__(self, input_dim, output_dim):
        """
        初始化

        Args:
            input_dim: 输入维度
            output_dim: 输出维度
        """
        super().__init__()
        self.decoder = nn.Sequential(
            nn.Linear(input_dim, 128),
            nn.ReLU(),
            nn.Linear(128, 128),
            nn.ReLU(),
            nn.Linear(128, output_dim),
            nn.Softmax(dim=-1)
        )
        
    def forward(self, x):
        return self.decoder(x)


class GanAgent(Agent):
    """使用GAN生成策略的代理"""
    
    def __init__(self, generator, style='random', difficulty=None):
        """
        初始化

        Args:
            generator: GAN生成器
            style: 策略风格
            difficulty: 策略难度
        """
        super().__init__()
        self.generator = generator
        self.style = style
        self.difficulty = difficulty
        self.name = f"GAN({style}"
        if difficulty:
            self.name += f", {difficulty})"
        else:
            self.name += ")"
        
    def act(self, state, legal_actions=None, is_training=False):
        """选择动作"""
        # 获取生成的策略
        policy = self.generator.generate_opponent_policy(
            style=self.style, 
            difficulty=self.difficulty
        )
        
        # 如果生成的是概率分布，则需要根据合法动作选择
        if isinstance(policy, torch.Tensor):
            policy = policy.detach().cpu().numpy().flatten()
        
        # 将策略限制在合法动作中
        if legal_actions:
            mask = np.zeros_like(policy)
            mask[legal_actions] = 1
            policy = policy * mask
            
            # 如果所有动作概率为0，则随机选择
            if np.sum(policy) == 0:
                return random.choice(legal_actions)
            
            # 否则按概率选择
            policy = policy / np.sum(policy)
            action = np.random.choice(len(policy), p=policy)
            
            # 如果选择的动作不合法，则随机选择一个合法动作
            if action not in legal_actions:
                return random.choice(legal_actions)
                
            return action
        else:
            # 如果没有合法动作限制，直接返回最高概率的动作
            return np.argmax(policy)
        
    def reset(self):
        """重置代理状态"""
        pass


class AdaptiveGanAgent(Agent):
    """自适应难度的GAN代理"""
    
    def __init__(self, generator, initial_skill=0.5):
        """
        初始化

        Args:
            generator: GAN生成器
            initial_skill: 初始玩家技能评分
        """
        super().__init__()
        self.generator = generator
        self.player_skill = initial_skill
        self.name = "AdaptiveGAN"
        
        # 记录最近的对战结果
        self.recent_results = []
        self.max_results = 10
        
    def act(self, state, legal_actions=None, is_training=False):
        """选择动作"""
        # 计算最近的胜负情况
        recent_wins = sum(1 for result in self.recent_results if result > 0)
        recent_losses = sum(1 for result in self.recent_results if result < 0)
        
        # 获取生成的策略
        policy = self.generator.generate_adaptive_opponent(
            self.player_skill,
            recent_wins,
            recent_losses
        )
        
        # 如果生成的是概率分布，则需要根据合法动作选择
        if isinstance(policy, torch.Tensor):
            policy = policy.detach().cpu().numpy().flatten()
        
        # 将策略限制在合法动作中
        if legal_actions:
            mask = np.zeros_like(policy)
            mask[legal_actions] = 1
            policy = policy * mask
            
            # 如果所有动作概率为0，则随机选择
            if np.sum(policy) == 0:
                return random.choice(legal_actions)
            
            # 否则按概率选择
            policy = policy / np.sum(policy)
            action = np.random.choice(len(policy), p=policy)
            
            # 如果选择的动作不合法，则随机选择一个合法动作
            if action not in legal_actions:
                return random.choice(legal_actions)
                
            return action
        else:
            # 如果没有合法动作限制，直接返回最高概率的动作
            return np.argmax(policy)
    
    def update_skill(self, result):
        """更新玩家技能评分和最近结果

        Args:
            result: 对战结果，正值表示胜利，负值表示失败，0表示平局
        """
        # 更新最近结果
        self.recent_results.append(result)
        if len(self.recent_results) > self.max_results:
            self.recent_results.pop(0)
            
        # 更新技能评分
        if result > 0:
            # 玩家胜利，提高技能评分
            self.player_skill = min(1.0, self.player_skill + 0.05)
        elif result < 0:
            # 玩家失败，降低技能评分
            self.player_skill = max(0.0, self.player_skill - 0.05)
    
    def reset(self):
        """重置代理状态"""
        pass


def collect_policy_data(agents: List[Agent], env: Environment, num_games: int = 100) -> List[np.ndarray]:
    """
    收集策略数据

    Args:
        agents: 代理列表
        env: 游戏环境
        num_games: 游戏数量

    Returns:
        策略向量列表
    """
    logger.info(f"开始收集策略数据，游戏数: {num_games}")
    
    policies = []
    
    for game_idx in range(num_games):
        if game_idx % 10 == 0:
            logger.info(f"已完成 {game_idx}/{num_games} 局游戏")
            
        state = env.reset()
        done = False
        
        while not done:
            player_id = state.get_player_id()
            agent = agents[player_id]
            
            # 获取合法动作
            legal_actions = env.get_legal_actions(state)
            observation = env.get_observation(state)
            
            # 获取动作概率（如果代理支持）
            if hasattr(agent, 'get_action_probs'):
                action_probs = agent.get_action_probs(observation, legal_actions)
                
                # 如果返回的是字典，转换为数组
                if isinstance(action_probs, dict):
                    probs_array = np.zeros(env.action_dim)
                    for action, prob in action_probs.items():
                        probs_array[action] = prob
                    action_probs = probs_array
                
                # 添加到策略集合
                policies.append(action_probs)
            
            # 获取动作
            action = agent.act(observation, legal_actions)
            
            # 执行动作
            state, reward, done, info = env.step(action)
    
    logger.info(f"策略数据收集完成，共收集到 {len(policies)} 个策略向量")
    return policies


def train_gan(policies: List[np.ndarray], policy_dim: int, latent_dim: int = 64, 
              condition_dim: int = 10, batch_size: int = 32, num_epochs: int = 100) -> AdversarialOpponentGenerator:
    """
    训练GAN

    Args:
        policies: 策略向量列表
        policy_dim: 策略维度
        latent_dim: 潜在空间维度
        condition_dim: 条件维度
        batch_size: 批次大小
        num_epochs: 训练轮数

    Returns:
        训练好的GAN生成器
    """
    logger.info(f"开始训练GAN，策略数: {len(policies)}, 策略维度: {policy_dim}")
    
    # 创建策略数据集
    policy_tensors = [torch.FloatTensor(p) for p in policies]
    dataset = SimplePolicyDataset(policy_tensors)
    
    # 创建策略编码器和解码器
    encoder = SimplePolicyEncoder(policy_dim, policy_dim)
    decoder = SimplePolicyDecoder(policy_dim, policy_dim)
    
    # 创建GAN生成器
    gan = AdversarialOpponentGenerator(
        policy_dim=policy_dim,
        latent_dim=latent_dim,
        condition_dim=condition_dim,
        policy_encoder=encoder,
        policy_decoder=decoder,
        use_wgan_gp=True
    )
    
    # 训练GAN
    history = gan.train(
        real_policies_dataset=dataset,
        num_epochs=num_epochs,
        batch_size=batch_size,
        save_path="models/gan",
        verbose=True,
        use_conditions=True
    )
    
    # 绘制训练历史
    plt.figure(figsize=(12, 4))
    
    plt.subplot(1, 3, 1)
    plt.plot(history['g_loss'])
    plt.title('Generator Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    
    plt.subplot(1, 3, 2)
    plt.plot(history['d_loss'])
    plt.title('Discriminator Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    
    plt.subplot(1, 3, 3)
    plt.plot(history['diversity'])
    plt.title('Diversity')
    plt.xlabel('Epoch')
    plt.ylabel('Score')
    
    plt.tight_layout()
    plt.savefig('gan_training_history.png')
    logger.info("训练历史图表已保存到 gan_training_history.png")
    
    return gan


def evaluate_gan_opponents(gan: AdversarialOpponentGenerator, baseline_agent: Agent, 
                          env: Environment, num_games: int = 100) -> Dict[str, float]:
    """
    评估GAN生成的对手

    Args:
        gan: GAN生成器
        baseline_agent: 基准代理
        env: 游戏环境
        num_games: 游戏数量

    Returns:
        评估结果
    """
    logger.info(f"开始评估GAN生成的对手，游戏数: {num_games}")
    
    # 创建不同类型的GAN代理
    gan_agents = {
        'easy': GanAgent(gan, style='balanced', difficulty='easy'),
        'medium': GanAgent(gan, style='balanced', difficulty='medium'),
        'hard': GanAgent(gan, style='balanced', difficulty='hard'),
        'aggressive': GanAgent(gan, style='aggressive'),
        'defensive': GanAgent(gan, style='defensive'),
        'extreme': GanAgent(gan, style='extreme'),
        'adaptive': AdaptiveGanAgent(gan)
    }
    
    # 评估结果
    results = {}
    
    # 对每种GAN代理进行评估
    for name, gan_agent in gan_agents.items():
        logger.info(f"评估GAN代理: {name}")
        
        # 玩家胜利次数
        player_wins = 0
        
        for game_idx in range(num_games):
            if game_idx % 10 == 0:
                logger.info(f"已完成 {game_idx}/{num_games} 局游戏")
                
            state = env.reset()
            done = False
            
            # 随机分配玩家和GAN代理的角色
            if random.random() < 0.5:
                agents = [baseline_agent, gan_agent]
                player_id = 0
            else:
                agents = [gan_agent, baseline_agent]
                player_id = 1
                
            while not done:
                current_player_id = state.get_player_id()
                agent = agents[current_player_id]
                
                # 获取合法动作
                legal_actions = env.get_legal_actions(state)
                observation = env.get_observation(state)
                
                # 获取动作
                action = agent.act(observation, legal_actions)
                
                # 执行动作
                state, reward, done, info = env.step(action)
            
            # 判断玩家是否胜利
            if done:
                winner = info.get('winner', -1)
                if winner == player_id:
                    player_wins += 1
                    
                # 如果是自适应代理，更新其技能评分
                if name == 'adaptive':
                    result = 1 if winner == player_id else -1
                    gan_agent.update_skill(result)
        
        # 计算胜率
        win_rate = player_wins / num_games
        results[name] = win_rate
        logger.info(f"GAN代理 {name} 评估完成，玩家胜率: {win_rate:.2f}")
    
    return results


def integrate_gan_with_training(gan: AdversarialOpponentGenerator, env: Environment, 
                              main_agent: Agent, num_games: int = 100) -> List[Dict]:
    """
    将GAN集成到训练中

    Args:
        gan: GAN生成器
        env: 游戏环境
        main_agent: 主要代理
        num_games: 游戏数量

    Returns:
        训练结果
    """
    logger.info(f"开始GAN辅助训练，游戏数: {num_games}")
    
    # 创建自对弈系统
    self_play = SelfPlay(save_path="experiences")
    
    # 创建对手切换器
    opponent_switcher = OpponentDistributionSwitcher(
        gan_generator=gan,
        switch_strategy='periodic',
        switch_interval=10
    )
    
    # 训练结果
    training_results = []
    
    # 模拟训练循环
    for epoch in range(10):
        logger.info(f"训练轮次 {epoch+1}/10")
        
        # 收集经验
        experiences = []
        
        for game_idx in range(num_games // 10):
            # 获取对手
            opponent = opponent_switcher.get_opponent()
            
            # 如果对手是函数形式，封装为代理
            if callable(opponent):
                opponent_agent = GanAgent(gan, style='random')
            else:
                opponent_agent = opponent
                
            # 确定代理顺序（随机分配主体代理和对手位置）
            if random.random() < 0.5:
                agents = [main_agent, opponent_agent]
                main_agent_id = 0
            else:
                agents = [opponent_agent, main_agent]
                main_agent_id = 1
                
            # 运行单局游戏
            state = env.reset()
            done = False
            game_experiences = []
            
            while not done:
                current_player_id = state.get_player_id()
                agent = agents[current_player_id]
                
                # 获取合法动作
                legal_actions = env.get_legal_actions(state)
                observation = env.get_observation(state)
                
                # 获取动作
                action = agent.act(observation, legal_actions)
                
                # 记录主体代理的经验
                if current_player_id == main_agent_id:
                    game_experiences.append({
                        'state': observation,
                        'action': action,
                        'legal_actions': legal_actions
                    })
                
                # 执行动作
                next_state, reward, done, info = env.step(action)
                
                # 更新状态
                state = next_state
                
            # 添加到经验集合
            experiences.extend(game_experiences)
            
        # 使用收集的经验更新主体代理（模拟）
        logger.info(f"收集了 {len(experiences)} 条经验")
        
        # 评估当前性能
        evaluation_result = evaluate_gan_opponents(
            gan, 
            main_agent, 
            env, 
            num_games=20  # 减少评估游戏数以加快示例运行
        )
        
        # 记录训练结果
        training_results.append({
            'epoch': epoch + 1,
            'experiences': len(experiences),
            'evaluation': evaluation_result
        })
    
    return training_results


def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="对抗性对手生成器示例")
    parser.add_argument("--mode", type=str, default="full", choices=["train", "evaluate", "integrate", "full"],
                      help="运行模式")
    parser.add_argument("--games", type=int, default=100, help="游戏数量")
    parser.add_argument("--epochs", type=int, default=50, help="训练轮数")
    parser.add_argument("--policy_dim", type=int, default=309, help="策略维度（斗地主动作空间大小）")
    parser.add_argument("--latent_dim", type=int, default=64, help="潜在空间维度")
    parser.add_argument("--condition_dim", type=int, default=10, help="条件变量维度")
    parser.add_argument("--batch_size", type=int, default=32, help="批次大小")
    parser.add_argument("--load_gan", type=str, default=None, help="加载GAN模型的路径")
    parser.add_argument("--save_dir", type=str, default="models/gan", help="保存GAN模型的目录")
    args = parser.parse_args()
    
    # 确保保存目录存在
    os.makedirs(args.save_dir, exist_ok=True)
    
    # 设置随机种子
    random.seed(42)
    np.random.seed(42)
    torch.manual_seed(42)
    
    # 创建环境和基准代理
    env = DoudizhuEnv()
    baseline_agent = RuleAgent()
    
    if args.mode == "train" or args.mode == "full":
        # 收集策略数据
        policies = collect_policy_data(
            agents=[RuleAgent(), RuleAgent(), RuleAgent()],  # 使用规则代理收集数据
            env=env,
            num_games=args.games
        )
        
        # 训练GAN
        gan = train_gan(
            policies=policies,
            policy_dim=args.policy_dim,
            latent_dim=args.latent_dim,
            condition_dim=args.condition_dim,
            batch_size=args.batch_size,
            num_epochs=args.epochs
        )
    else:
        # 加载GAN
        if args.load_gan:
            gan = AdversarialOpponentGenerator(
                policy_dim=args.policy_dim,
                latent_dim=args.latent_dim,
                condition_dim=args.condition_dim
            )
            gan.load(args.load_gan)
            logger.info(f"已加载GAN模型: {args.load_gan}")
        else:
            # 创建一个随机初始化的GAN
            gan = AdversarialOpponentGenerator(
                policy_dim=args.policy_dim,
                latent_dim=args.latent_dim,
                condition_dim=args.condition_dim
            )
            logger.warning("未指定GAN模型路径，使用随机初始化的GAN")
    
    if args.mode == "evaluate" or args.mode == "full":
        # 评估GAN对手
        evaluation_results = evaluate_gan_opponents(
            gan=gan,
            baseline_agent=baseline_agent,
            env=env,
            num_games=args.games
        )
        
        # 打印评估结果
        logger.info("评估结果:")
        for name, win_rate in evaluation_results.items():
            logger.info(f"- {name}: 胜率 {win_rate:.2f}")
    
    if args.mode == "integrate" or args.mode == "full":
        # 将GAN集成到训练中
        training_results = integrate_gan_with_training(
            gan=gan,
            env=env,
            main_agent=baseline_agent,
            num_games=args.games
        )
        
        # 打印训练结果
        logger.info("训练结果:")
        for result in training_results:
            logger.info(f"Epoch {result['epoch']}: {len(result['experiences'])} 条经验")
            for name, win_rate in result['evaluation'].items():
                logger.info(f"- 对 {name} 的胜率: {win_rate:.2f}")


if __name__ == "__main__":
    main() 