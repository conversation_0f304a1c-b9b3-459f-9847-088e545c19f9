"""
信念状态模块

定义玩家手牌信念状态的数据结构，用于表示对手可能持有的牌的概率分布。
此结构在多个模块（追踪器、规划器、决策器）之间传递，确保信息一致性。
"""
from typing import Dict, List, Tuple, Optional, Union
import dataclasses
import numpy as np
from enum import Enum


class BeliefSource(Enum):
    """信念来源枚举，用于标记信念状态的来源或更新方式"""
    INITIAL = 0       # 初始分布
    OBSERVATION = 1   # 直接观察
    INFERENCE = 2     # 基于规则推理
    NEURAL = 3        # 神经网络预测
    COMBINED = 4      # 多种来源组合


@dataclasses.dataclass
class BeliefState:
    """
    信念状态类

    表示对玩家（尤其是对手）手牌的概率分布信念。
    这个数据结构在追踪器、规划器和决策器之间传递，确保信息一致性。
    """

    player_id: str
    # 键: 牌的表示 (例如, 'H3' 表示红桃3), 值: 概率 (0.0 到 1.0)
    card_probabilities: Dict[str, float]
    # 可选: 其他推断信息，如估计的手牌数量、可能的牌型组合等
    estimated_hand_length: Optional[int] = None
    possible_combos: Optional[List[Tuple[str, float]]] = None  # 牌型组合表示和概率

    # 信念状态的来源
    source: BeliefSource = BeliefSource.INITIAL
    # 信念状态的置信度 (0.0 到 1.0)，表示对当前信念的确信程度
    confidence: float = 0.5
    # 上次更新时间戳
    last_updated: Optional[float] = None
    # 信念状态的版本号，每次更新时递增
    version: int = 0

    def update_probabilities(self, new_probs: Dict[str, float]) -> None:
        """
        更新牌的概率分布

        Args:
            new_probs (Dict[str, float]): 新的概率分布
        """
        # 合并新旧概率，确保概率值在有效范围内
        for card, prob in new_probs.items():
            self.card_probabilities[card] = max(0.0, min(1.0, prob))

    def normalize_probabilities(self) -> None:
        """
        归一化概率分布，确保所有概率之和为1
        """
        total = sum(self.card_probabilities.values())
        if total > 0:
            for card in self.card_probabilities:
                self.card_probabilities[card] /= total

    def get_most_likely_cards(self, top_n: int = 5) -> List[Tuple[str, float]]:
        """
        获取概率最高的前N张牌

        Args:
            top_n (int, optional): 返回的牌数量. Defaults to 5.

        Returns:
            List[Tuple[str, float]]: 牌和对应概率的列表，按概率降序排列
        """
        sorted_cards = sorted(
            self.card_probabilities.items(),
            key=lambda x: x[1],
            reverse=True
        )
        return sorted_cards[:top_n]

    def get_probability(self, card: str) -> float:
        """
        获取特定牌的概率

        Args:
            card (str): 牌的表示

        Returns:
            float: 该牌的概率，如果牌不在分布中则返回0
        """
        return self.card_probabilities.get(card, 0.0)

    def set_probability(self, card: str, probability: float) -> None:
        """
        设置特定牌的概率

        Args:
            card (str): 牌的表示
            probability (float): 概率值，将被限制在[0,1]范围内
        """
        self.card_probabilities[card] = max(0.0, min(1.0, probability))

    def to_numpy(self) -> np.ndarray:
        """
        将信念状态转换为NumPy数组，便于神经网络处理

        Returns:
            np.ndarray: 表示信念状态的数组
        """
        # 这里假设有一个固定的牌序列表，实际实现可能需要根据游戏类型调整
        all_cards = sorted(self.card_probabilities.keys())
        return np.array([self.card_probabilities.get(card, 0.0) for card in all_cards])

    def from_numpy(self, array: np.ndarray, card_mapping: List[str]) -> None:
        """
        从NumPy数组更新信念状态

        Args:
            array (np.ndarray): 表示信念状态的数组
            card_mapping (List[str]): 数组索引到牌表示的映射
        """
        for i, card in enumerate(card_mapping):
            if i < len(array):
                self.card_probabilities[card] = float(array[i])

    def update_from_observation(self, observed_cards: List[str], is_certain: bool = True,
                               update_source: bool = True, update_timestamp: Optional[float] = None) -> None:
        """
        根据观察到的牌更新信念状态

        Args:
            observed_cards (List[str]): 观察到的牌列表
            is_certain (bool, optional): 观察是否确定. Defaults to True.
            update_source (bool, optional): 是否更新信念来源. Defaults to True.
            update_timestamp (Optional[float], optional): 更新时间戳. Defaults to None.
        """
        if is_certain:
            # 如果确定观察到这些牌，则将它们的概率设为0（表示对手不可能有这些牌）
            for card in observed_cards:
                self.card_probabilities[card] = 0.0
        else:
            # 如果不确定，则降低这些牌的概率
            for card in observed_cards:
                if card in self.card_probabilities:
                    self.card_probabilities[card] *= 0.5

        # 重新归一化概率
        self.normalize_probabilities()

        # 更新元数据
        if update_source:
            self.source = BeliefSource.OBSERVATION

        if update_timestamp is not None:
            self.last_updated = update_timestamp

        # 更新版本号
        self.version += 1

    def merge(self, other: 'BeliefState', weight: float = 0.5) -> 'BeliefState':
        """
        将当前信念状态与另一个信念状态合并

        Args:
            other (BeliefState): 另一个信念状态
            weight (float, optional): 当前信念状态的权重. Defaults to 0.5.

        Returns:
            BeliefState: 合并后的新信念状态
        """
        if self.player_id != other.player_id:
            raise ValueError("Cannot merge belief states for different players")

        # 创建新的信念状态
        merged = BeliefState(
            player_id=self.player_id,
            card_probabilities={},
            source=BeliefSource.COMBINED,
            confidence=(self.confidence * weight + other.confidence * (1 - weight)),
            version=max(self.version, other.version) + 1
        )

        # 合并概率分布
        all_cards = set(self.card_probabilities.keys()) | set(other.card_probabilities.keys())
        for card in all_cards:
            prob1 = self.card_probabilities.get(card, 0.0)
            prob2 = other.card_probabilities.get(card, 0.0)
            merged.card_probabilities[card] = prob1 * weight + prob2 * (1 - weight)

        # 合并其他信息
        if self.estimated_hand_length is not None and other.estimated_hand_length is not None:
            merged.estimated_hand_length = int(
                self.estimated_hand_length * weight + other.estimated_hand_length * (1 - weight)
            )
        elif self.estimated_hand_length is not None:
            merged.estimated_hand_length = self.estimated_hand_length
        elif other.estimated_hand_length is not None:
            merged.estimated_hand_length = other.estimated_hand_length

        return merged

    def entropy(self) -> float:
        """
        计算信念状态的熵，表示不确定性的程度

        Returns:
            float: 熵值，越高表示不确定性越大
        """
        entropy = 0.0
        for prob in self.card_probabilities.values():
            if prob > 0:
                entropy -= prob * np.log2(prob)
        return entropy

    def information_value(self, card: str) -> float:
        """
        计算获取特定牌信息的价值

        Args:
            card (str): 牌的表示

        Returns:
            float: 信息价值，越高表示获取该信息越有价值
        """
        # 信息价值与概率的不确定性相关
        # 当概率接近0.5时，不确定性最大，信息价值最高
        prob = self.get_probability(card)
        return 1.0 - abs(2 * prob - 1.0)

    def to_dict(self) -> Dict:
        """
        将信念状态转换为字典表示，便于序列化

        Returns:
            Dict: 信念状态的字典表示
        """
        return {
            'player_id': self.player_id,
            'card_probabilities': self.card_probabilities,
            'estimated_hand_length': self.estimated_hand_length,
            'possible_combos': self.possible_combos,
            'source': self.source.value,
            'confidence': self.confidence,
            'last_updated': self.last_updated,
            'version': self.version
        }

    @classmethod
    def from_dict(cls, data: Dict) -> 'BeliefState':
        """
        从字典创建信念状态

        Args:
            data (Dict): 信念状态的字典表示

        Returns:
            BeliefState: 创建的信念状态对象
        """
        belief = cls(
            player_id=data['player_id'],
            card_probabilities=data['card_probabilities'],
            estimated_hand_length=data.get('estimated_hand_length'),
            possible_combos=data.get('possible_combos'),
            confidence=data.get('confidence', 0.5),
            version=data.get('version', 0)
        )

        # 设置来源
        if 'source' in data:
            belief.source = BeliefSource(data['source'])

        # 设置时间戳
        if 'last_updated' in data:
            belief.last_updated = data['last_updated']

        return belief

    @classmethod
    def create_uniform(cls, player_id: str, all_cards: List[str],
                      excluded_cards: Optional[List[str]] = None) -> 'BeliefState':
        """
        创建均匀分布的初始信念状态

        Args:
            player_id (str): 玩家ID
            all_cards (List[str]): 所有可能的牌列表
            excluded_cards (Optional[List[str]], optional): 已知不在玩家手中的牌. Defaults to None.

        Returns:
            BeliefState: 创建的信念状态对象
        """
        # 初始化所有牌的概率为均匀分布
        card_probabilities = {card: 1.0 for card in all_cards}

        # 排除已知不在玩家手中的牌
        if excluded_cards:
            for card in excluded_cards:
                if card in card_probabilities:
                    card_probabilities[card] = 0.0

        # 创建信念状态
        belief = cls(
            player_id=player_id,
            card_probabilities=card_probabilities,
            source=BeliefSource.INITIAL,
            confidence=0.3  # 初始信念的置信度较低
        )

        # 归一化概率
        belief.normalize_probabilities()

        return belief


class BeliefStateCollection:
    """
    信念状态集合类

    管理多个玩家的信念状态集合，提供便捷的访问和更新方法。
    """

    def __init__(self):
        """初始化信念状态集合"""
        self.beliefs: Dict[str, BeliefState] = {}

    def add_belief(self, belief: BeliefState) -> None:
        """
        添加信念状态

        Args:
            belief (BeliefState): 信念状态对象
        """
        self.beliefs[belief.player_id] = belief

    def get_belief(self, player_id: str) -> Optional[BeliefState]:
        """
        获取玩家的信念状态

        Args:
            player_id (str): 玩家ID

        Returns:
            Optional[BeliefState]: 信念状态对象，如果不存在则返回None
        """
        return self.beliefs.get(player_id)

    def update_from_observation(self, observed_cards: Dict[str, List[str]],
                               is_certain: bool = True) -> None:
        """
        根据观察到的牌更新所有信念状态

        Args:
            observed_cards (Dict[str, List[str]]): 玩家ID到观察到的牌列表的映射
            is_certain (bool, optional): 观察是否确定. Defaults to True.
        """
        for player_id, cards in observed_cards.items():
            belief = self.get_belief(player_id)
            if belief:
                belief.update_from_observation(cards, is_certain)

    def to_dict(self) -> Dict:
        """
        将信念状态集合转换为字典表示

        Returns:
            Dict: 信念状态集合的字典表示
        """
        return {
            player_id: belief.to_dict()
            for player_id, belief in self.beliefs.items()
        }

    @classmethod
    def from_dict(cls, data: Dict) -> 'BeliefStateCollection':
        """
        从字典创建信念状态集合

        Args:
            data (Dict): 信念状态集合的字典表示

        Returns:
            BeliefStateCollection: 创建的信念状态集合对象
        """
        collection = cls()
        for player_id, belief_data in data.items():
            collection.add_belief(BeliefState.from_dict(belief_data))
        return collection
