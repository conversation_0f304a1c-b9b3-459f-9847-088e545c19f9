"""
集成AI系统模块

整合多种算法的AI系统，支持闭环更新和持续学习。
"""

import os
import logging
import torch
import numpy as np
from typing import Dict, List, Tuple, Any, Optional, Union

from cardgame_ai.algorithms.efficient_zero import EfficientZero
from cardgame_ai.algorithms.enhanced_mappo import EnhancedMAPPO
from cardgame_ai.algorithms.mcts import MCTS
from cardgame_ai.core.base import Experience, Batch, State, Action
from cardgame_ai.games.common.belief_state import BeliefState
from cardgame_ai.algorithms.belief_tracking.joint_belief import JointBeliefTracker
from cardgame_ai.algorithms.explanation_manager import ExplanationManager
from cardgame_ai.training.online_collector import OnlineDataCollector
from cardgame_ai.algorithms.rlhf.preference_model import PreferenceModel
from cardgame_ai.algorithms.human_ai_collaboration import TrustEstimator
from cardgame_ai.algorithms.hybrid_decision_system import HybridDecisionSystem
from cardgame_ai.algorithms.gto_approximation.gto_policy import GTOPolicy
from cardgame_ai.algorithms.opponent_modeling.deviation_detector import DeviationDetector
from cardgame_ai.algorithms.dynamic_budget_allocator import DynamicBudgetAllocator
from cardgame_ai.algorithms.counterfactual_reasoning import CounterfactualMCTS

# 配置日志
logger = logging.getLogger(__name__)


class IntegratedAISystem:
    """
    集成AI系统类

    整合EfficientZero和EnhancedMAPPO等算法，提供统一的接口，
    支持闭环更新和持续学习。
    """

    def __init__(
        self,
        state_shape: Tuple[int, ...],
        action_shape: Tuple[int, ...],
        hidden_dim: int = 128,
        state_dim: int = 64,
        use_resnet: bool = False,
        use_belief_state: bool = True,
        use_information_value: bool = True,
        use_intrinsic_motivation: bool = False,
        use_human_feedback: bool = True,
        use_online_learning: bool = False,
        online_collector_params: Optional[Dict[str, Any]] = None,
        rlhf_params: Optional[Dict[str, Any]] = None,
        device: str = "cuda" if torch.cuda.is_available() else "cpu"
    ):
        """
        初始化集成AI系统

        Args:
            state_shape: 状态形状
            action_shape: 动作形状
            hidden_dim: 隐藏层维度
            state_dim: 状态维度
            use_resnet: 是否使用ResNet
            use_belief_state: 是否使用信念状态
            use_information_value: 是否使用信息价值
            use_intrinsic_motivation: 是否使用内在动机
            use_human_feedback: 是否使用人类反馈
            use_online_learning: 是否使用在线学习
            online_collector_params: 在线数据收集器参数，如果use_online_learning为True则必须提供
            rlhf_params: RLHF参数，如果use_human_feedback为True则必须提供
            device: 设备
        """
        self.state_shape = state_shape
        self.action_shape = action_shape
        self.hidden_dim = hidden_dim
        self.state_dim = state_dim
        self.use_resnet = use_resnet
        self.use_belief_state = use_belief_state
        self.use_information_value = use_information_value
        self.use_intrinsic_motivation = use_intrinsic_motivation
        self.use_human_feedback = use_human_feedback
        self.use_online_learning = use_online_learning
        self.online_collector_params = online_collector_params
        self.rlhf_params = rlhf_params
        self.device = device

        # 创建在线数据收集器
        self.online_collector = None
        if use_online_learning:
            if online_collector_params is None:
                online_collector_params = {
                    "buffer_size": 10000,
                    "use_prioritized_replay": True,
                    "device": device
                }
            self.online_collector = OnlineDataCollector(**online_collector_params)
            logger.info("已创建在线数据收集器")

        # 创建RLHF偏好模型
        self.preference_model = None
        if use_human_feedback:
            if rlhf_params is None:
                rlhf_params = {
                    "state_dim": state_dim,
                    "hidden_dim": hidden_dim,
                    "use_action_info": True,
                    "action_dim": action_shape[0],
                    "device": device
                }
            self.preference_model = PreferenceModel(**rlhf_params)
            logger.info("已创建RLHF偏好模型")

        # 创建EfficientZero模型
        self.efficient_zero = EfficientZero(
            state_shape=state_shape,
            action_shape=action_shape,
            hidden_dim=hidden_dim,
            state_dim=state_dim,
            use_resnet=use_resnet,
            device=device
        )

        # 创建EnhancedMAPPO模型
        self.enhanced_mappo = EnhancedMAPPO(
            state_dim=state_shape[0],
            action_dim=action_shape[0],
            hidden_dim=hidden_dim,
            use_belief_state=use_belief_state,
            device=device
        )

        # 创建MCTS
        self.mcts = MCTS(
            num_simulations=50,
            use_belief_state=use_belief_state,
            use_information_value=use_information_value,
            use_intrinsic_motivation=use_intrinsic_motivation,
            use_opponent_model_prior=True
        )

        # 当前活跃模型
        self.active_model = "efficient_zero"

        # 创建混合决策系统
        self.hybrid_decision_system = None

        # 创建信任度估计器
        self.trust_estimator = TrustEstimator(
            confidence_threshold=0.7,
            window_size=10,
            initial_trust_level=0.5,
            learning_rate=0.1
        )
        logger.info("已创建信任度估计器")

        # 创建GTO策略与偏离检测器
        self.gto_policy = GTOPolicy(policy_path=None)
        self.deviation_detector = DeviationDetector(
            state_dim=self.state_dim,
            action_dim=self.action_shape[0],
            gto_policy_source=self.gto_policy
        )
        logger.info("已创建偏离检测器和GTO策略")

        # 创建动态计算预算分配器
        self.dynamic_budget_allocator = DynamicBudgetAllocator(
            key_moment_detector=self.key_moment_detector,
            base_budget=self.mcts.original_num_simulations,
            max_budget=5000,
            amplification_factor=10,
            min_amplification=1.0,
            max_amplification=100.0,
            critical_threshold=0.5,
            adaptive_scaling=True
        )
        logger.info("已创建动态计算预算分配器")

        # 创建反事实MCTS（用于信念状态下的高级推理）
        self.counterfactual_mcts = CounterfactualMCTS(
            num_simulations=self.mcts.num_simulations,
            discount=self.mcts.discount,
            dirichlet_alpha=self.mcts.dirichlet_alpha,
            exploration_fraction=self.mcts.exploration_fraction,
            pb_c_base=self.mcts.pb_c_base,
            pb_c_init=self.mcts.pb_c_init,
            use_belief_state=self.use_belief_state,
            use_information_value=self.use_information_value,
            information_value_weight=self.mcts.information_value_weight,
            counterfactual_weight=0.3,
            max_counterfactual_assumptions=3,
            counterfactual_threshold=0.1
        )
        self.use_counterfactual = True
        logger.info("已创建反事实MCTS")

        # 创建解释管理器
        self.explanation_manager = ExplanationManager(
            enabled=False,  # 默认禁用
            detail_level="medium",
            visualization_enabled=True,
            save_explanations=False,
            save_dir="explanations"
        )
        
        # 创建联合信念追踪器(JointBeliefTracker)
        # 注意：这里仅初始化空对象，实际使用时需要根据游戏状态进行初始化
        self.joint_belief_tracker = None
        
        logger.info(f"初始化集成AI系统: active_model={self.active_model}, device={device}")

    def act(
        self,
        state: Any,
        legal_actions: List[int] = None,
        belief_trackers: Dict[str, Any] = None,
        current_player_id: str = None,
        temperature: float = 1.0,
        deterministic: bool = False,
        explain: bool = False,
        game_action_history: Optional[List[Dict[str, Any]]] = None
    ) -> Union[int, Tuple[int, Dict[str, Any]]]:
        """
        选择动作

        Args:
            state: 状态
            legal_actions: 合法动作列表
            belief_trackers: 信念追踪器字典
            current_player_id: 当前玩家ID
            temperature: 温度参数
            deterministic: 是否确定性选择
            explain: 是否返回解释信息
            game_action_history: 游戏动作历史，用于更新信念追踪器

        Returns:
            Union[int, Tuple[int, Dict[str, Any]]]: 动作或动作和解释信息
        """
        # 更新或初始化联合信念追踪器
        if self.use_belief_state:
            # 如果信念追踪器未初始化且有足够信息，则进行初始化
            if self.joint_belief_tracker is None and hasattr(state, 'get_all_player_ids') and hasattr(state, 'get_all_cards'):
                player_ids = state.get_all_player_ids()
                all_cards = state.get_all_cards()
                known_cards = {}
                initial_hand_sizes = {}
                
                # 获取已知的牌和初始手牌大小
                if hasattr(state, 'get_known_cards'):
                    known_cards = state.get_known_cards()
                if hasattr(state, 'get_hand_sizes'):
                    initial_hand_sizes = state.get_hand_sizes()
                
                # 初始化联合信念追踪器
                self.joint_belief_tracker = JointBeliefTracker(
                    player_ids=player_ids,
                    all_cards=all_cards,
                    known_cards=known_cards,
                    initial_hand_sizes=initial_hand_sizes
                )
                logger.info("已初始化联合信念追踪器(JointBeliefTracker)")
            
            # 如果已初始化且有动作历史，则更新信念
            if self.joint_belief_tracker is not None and game_action_history:
                for action_record in game_action_history:
                    player_id = action_record.get('player_id')
                    action = action_record.get('action', [])
                    action_type = action_record.get('action_type')
                    
                    if player_id and action is not None:
                        self.joint_belief_tracker.update_from_action(
                            player_id=player_id,
                            action=action,
                            action_type=action_type
                        )
                logger.debug("已更新联合信念追踪器")
        
        # 动态预算分配
        dynamic_budget = None
        if hasattr(self, 'dynamic_budget_allocator'):
            budget_conf = self.dynamic_budget_allocator.allocate_budget(
                state=state,
                state_history=None,
                context={'player_id': current_player_id}
            )
            dynamic_budget = {
                'num_simulations': budget_conf.get('num_simulations'),
                'max_time_ms': budget_conf.get('max_time_ms')
            }
        
        # 根据当前活跃模型选择动作
        if self.active_model == "efficient_zero":
            # 使用EfficientZero和MCTS选择动作
            actions_mask = None
            if legal_actions is not None:
                actions_mask = [i in legal_actions for i in range(self.action_shape[0])]

            # 构建GTO对手模型先验
            opponent_model_priors = None
            if current_player_id is not None and legal_actions is not None:
                gto_dist = self.gto_policy.get_policy(state, legal_actions)
                if gto_dist is not None:
                    opponent_model_priors = {
                        current_player_id: {a: float(gto_dist[a]) for a in legal_actions}
                    }
            # 选择 MCTS 或 反事实 MCTS
            mcts_runner = self.counterfactual_mcts if (self.use_counterfactual and self.use_belief_state and (belief_trackers or self.joint_belief_tracker) and current_player_id) else self.mcts
            
            # 准备信念追踪器参数
            # 如果外部提供了belief_trackers，优先使用外部提供的
            # 否则，使用内部的joint_belief_tracker
            belief_trackers_to_use = belief_trackers
            if self.joint_belief_tracker is not None and (belief_trackers_to_use is None or current_player_id not in belief_trackers_to_use):
                # 如果没有外部提供的belief_trackers或者外部提供的不包含当前玩家
                # 则创建一个包含joint_belief_tracker的字典传递给MCTS
                if belief_trackers_to_use is None:
                    belief_trackers_to_use = {}
                    
                # 添加joint_belief_tracker到belief_trackers中
                belief_trackers_to_use[current_player_id] = self.joint_belief_tracker
                logger.debug("使用联合信念追踪器进行MCTS搜索")
                
            visit_counts, pi, explanation = mcts_runner.run(
                root_state=state,
                model=self.efficient_zero,
                temperature=temperature,
                actions_mask=actions_mask,
                belief_trackers=belief_trackers_to_use,
                opponent_model_priors=opponent_model_priors,
                explain=explain,
                dynamic_budget=dynamic_budget
            )

            # 选择动作
            if deterministic:
                action = max(visit_counts.items(), key=lambda x: x[1])[0]
            else:
                actions = list(pi.keys())
                probs = list(pi.values())
                action = np.random.choice(actions, p=probs)

            if explain:
                # 如果启用了解释管理器，使用解释管理器解释决策
                if hasattr(self, 'explanation_manager') and self.explanation_manager.enabled:
                    explanation = self.explanation_manager.explain_decision(
                        decision_data=explanation,
                        decision_type="mcts",
                        context={
                            "state": str(state),
                            "legal_actions": legal_actions,
                            "action": action,
                            "temperature": temperature,
                            "deterministic": deterministic
                        }
                    )

                return action, explanation
            else:
                return action

        elif self.active_model == "enhanced_mappo":
            # 使用EnhancedMAPPO选择动作
            action, action_log_prob, value, explanation = self.enhanced_mappo.act(
                state=state,
                legal_actions=legal_actions,
                belief_state=belief_trackers[current_player_id].get_belief_state() if belief_trackers and current_player_id else None,
                deterministic=deterministic,
                explain=explain
            )

            if explain:
                # 如果启用了解释管理器，使用解释管理器解释决策
                if hasattr(self, 'explanation_manager') and self.explanation_manager.enabled:
                    explanation = self.explanation_manager.explain_decision(
                        decision_data=explanation,
                        decision_type="network",
                        context={
                            "state": str(state),
                            "legal_actions": legal_actions,
                            "action": action,
                            "deterministic": deterministic
                        }
                    )

                return action, explanation
            else:
                return action

        else:
            raise ValueError(f"未知的活跃模型: {self.active_model}")

    def train(
        self,
        batch: Union[Dict[str, Any], Batch],
        human_feedback_batch: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        训练模型

        Args:
            batch: 训练批次
            human_feedback_batch: 人类反馈批次
            **kwargs: 其他参数

        Returns:
            Dict[str, Any]: 训练结果
        """
        results = {}

        # 训练EfficientZero
        if self.active_model == "efficient_zero" or kwargs.get("train_all", False):
            ez_results = self.efficient_zero.train(batch, human_feedback_batch=human_feedback_batch, **kwargs)
            results["efficient_zero"] = ez_results

        # 训练EnhancedMAPPO
        if self.active_model == "enhanced_mappo" or kwargs.get("train_all", False):
            # 如果batch是字典，则需要转换为Batch对象
            if isinstance(batch, dict):
                from cardgame_ai.utils.data_loader import _convert_batch_to_experiences
                experiences = _convert_batch_to_experiences(batch)
                batch_obj = Batch(experiences)
            else:
                batch_obj = batch

            mappo_results = self.enhanced_mappo.update(batch_obj, human_feedback_samples=human_feedback_batch, **kwargs)
            results["enhanced_mappo"] = mappo_results

        return results

    def update(
        self,
        batch: Union[Dict[str, Any], Batch],
        human_feedback_samples: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        更新模型（与train方法相同）

        Args:
            batch: 训练批次
            human_feedback_samples: 人类反馈样本
            **kwargs: 其他参数

        Returns:
            Dict[str, Any]: 更新结果
        """
        return self.train(batch, human_feedback_batch=human_feedback_samples, **kwargs)

    def save(self, path: str) -> None:
        """
        保存模型

        Args:
            path: 保存路径
        """
        # 创建保存目录
        os.makedirs(os.path.dirname(path), exist_ok=True)

        # 保存模型
        state_dict = {
            "efficient_zero": self.efficient_zero.state_dict(),
            "enhanced_mappo": self.enhanced_mappo.state_dict(),
            "active_model": self.active_model,
            "config": {
                "state_shape": self.state_shape,
                "action_shape": self.action_shape,
                "hidden_dim": self.hidden_dim,
                "state_dim": self.state_dim,
                "use_resnet": self.use_resnet,
                "use_belief_state": self.use_belief_state,
                "use_information_value": self.use_information_value,
                "use_intrinsic_motivation": self.use_intrinsic_motivation,
                "use_human_feedback": self.use_human_feedback,
                "use_online_learning": self.use_online_learning,
                "online_collector_params": self.online_collector_params,
                "rlhf_params": self.rlhf_params
            }
        }

        # 保存信任度估计器状态
        if hasattr(self, 'trust_estimator') and self.trust_estimator is not None:
            state_dict["trust_estimator"] = {
                "current_trust_level": self.trust_estimator.get_trust_level(),
                "similarity_history": self.trust_estimator.similarity_history,
                "stats": self.trust_estimator.stats
            }

        # 保存偏好模型（如果有）
        if self.preference_model is not None:
            preference_model_path = os.path.join(os.path.dirname(path), "preference_model.pt")
            self.preference_model.save(preference_model_path)
            state_dict["preference_model_path"] = preference_model_path

        torch.save(state_dict, path)
        logger.info(f"已保存集成AI系统: {path}")

    def load(self, path: str) -> None:
        """
        加载模型

        Args:
            path: 加载路径
        """
        # 检查文件是否存在
        if not os.path.exists(path):
            logger.warning(f"模型文件不存在: {path}")
            return

        # 加载模型
        state_dict = torch.load(path, map_location=self.device)

        # 加载EfficientZero
        if "efficient_zero" in state_dict:
            self.efficient_zero.load_state_dict(state_dict["efficient_zero"])

        # 加载EnhancedMAPPO
        if "enhanced_mappo" in state_dict:
            self.enhanced_mappo.load_state_dict(state_dict["enhanced_mappo"])

        # 加载活跃模型
        if "active_model" in state_dict:
            self.active_model = state_dict["active_model"]

        # 加载配置
        if "config" in state_dict:
            config = state_dict["config"]
            self.state_shape = config.get("state_shape", self.state_shape)
            self.action_shape = config.get("action_shape", self.action_shape)
            self.hidden_dim = config.get("hidden_dim", self.hidden_dim)
            self.state_dim = config.get("state_dim", self.state_dim)
            self.use_resnet = config.get("use_resnet", self.use_resnet)
            self.use_belief_state = config.get("use_belief_state", self.use_belief_state)
            self.use_information_value = config.get("use_information_value", self.use_information_value)
            self.use_intrinsic_motivation = config.get("use_intrinsic_motivation", self.use_intrinsic_motivation)
            self.use_human_feedback = config.get("use_human_feedback", self.use_human_feedback)
            self.use_online_learning = config.get("use_online_learning", self.use_online_learning)
            self.online_collector_params = config.get("online_collector_params", self.online_collector_params)
            self.rlhf_params = config.get("rlhf_params", self.rlhf_params)

            # 重新创建在线数据收集器（如果需要）
            if self.use_online_learning and self.online_collector is None and self.online_collector_params is not None:
                self.online_collector = OnlineDataCollector(**self.online_collector_params)
                logger.info("已重新创建在线数据收集器")

            # 加载偏好模型（如果有）
            if "preference_model_path" in state_dict:
                preference_model_path = state_dict["preference_model_path"]
                if os.path.exists(preference_model_path):
                    self.preference_model = PreferenceModel.load(preference_model_path, device=self.device)
                    logger.info(f"已加载偏好模型: {preference_model_path}")
                else:
                    logger.warning(f"偏好模型文件不存在: {preference_model_path}")

                    # 如果文件不存在但需要偏好模型，则创建一个新的
                    if self.use_human_feedback and self.preference_model is None and self.rlhf_params is not None:
                        self.preference_model = PreferenceModel(**self.rlhf_params)
                        logger.info("已创建新的偏好模型")

        # 加载信任度估计器状态
        if "trust_estimator" in state_dict:
            trust_data = state_dict["trust_estimator"]
            if not hasattr(self, 'trust_estimator') or self.trust_estimator is None:
                self.trust_estimator = TrustEstimator(
                    confidence_threshold=0.7,
                    window_size=10,
                    initial_trust_level=trust_data["current_trust_level"],
                    learning_rate=0.1
                )
            else:
                self.trust_estimator.current_trust_level = trust_data["current_trust_level"]
                self.trust_estimator.similarity_history = trust_data["similarity_history"]
                self.trust_estimator.stats = trust_data["stats"]
            logger.info(f"已加载信任度估计器状态: 信任度={trust_data['current_trust_level']:.4f}")

        logger.info(f"已加载集成AI系统: {path}")

    def set_active_model(self, model_name: str) -> None:
        """
        设置当前活跃模型

        Args:
            model_name: 模型名称，可选"efficient_zero"或"enhanced_mappo"
        """
        if model_name not in ["efficient_zero", "enhanced_mappo"]:
            raise ValueError(f"未知的模型名称: {model_name}")

        self.active_model = model_name
        logger.info(f"已设置活跃模型: {model_name}")

    def state_dict(self) -> Dict[str, Any]:
        """
        获取模型状态字典

        Returns:
            Dict[str, Any]: 模型状态字典
        """
        return {
            "efficient_zero": self.efficient_zero.state_dict(),
            "enhanced_mappo": self.enhanced_mappo.state_dict(),
            "active_model": self.active_model
        }

    def load_state_dict(self, state_dict: Dict[str, Any]) -> None:
        """
        加载模型状态字典

        Args:
            state_dict: 模型状态字典
        """
        # 加载EfficientZero
        if "efficient_zero" in state_dict:
            self.efficient_zero.load_state_dict(state_dict["efficient_zero"])

        # 加载EnhancedMAPPO
        if "enhanced_mappo" in state_dict:
            self.enhanced_mappo.load_state_dict(state_dict["enhanced_mappo"])

        # 加载活跃模型
        if "active_model" in state_dict:
            self.active_model = state_dict["active_model"]

    def enable_explanation_mode(
        self,
        detail_level: str = "medium",
        visualization_enabled: bool = True,
        save_explanations: bool = False,
        save_dir: str = "explanations"
    ) -> None:
        """
        启用决策解释模式

        Args:
            detail_level: 解释详细程度，可选值为"low"、"medium"、"high"
            visualization_enabled: 是否启用可视化
            save_explanations: 是否保存解释结果
            save_dir: 解释结果保存目录
        """
        if hasattr(self, 'explanation_manager'):
            self.explanation_manager.set_detail_level(detail_level)
            self.explanation_manager.set_visualization_enabled(visualization_enabled)
            self.explanation_manager.set_save_explanations(save_explanations, save_dir)
            self.explanation_manager.enable_explanation_mode()
            logger.info("已启用决策解释模式")
        else:
            logger.warning("未找到解释管理器，无法启用决策解释模式")

    def disable_explanation_mode(self) -> None:
        """
        禁用决策解释模式
        """
        if hasattr(self, 'explanation_manager'):
            self.explanation_manager.disable_explanation_mode()
            logger.info("已禁用决策解释模式")
        else:
            logger.warning("未找到解释管理器，无法禁用决策解释模式")

    def process_human_action(
        self,
        state: State,
        human_action: Action,
        legal_actions: List[Action] = None
    ) -> Action:
        """
        处理人类动作，根据信任度估计决定是否采纳人类动作或使用AI建议动作

        Args:
            state: 当前游戏状态
            human_action: 人类玩家的动作
            legal_actions: 合法动作列表

        Returns:
            Action: 最终决策动作
        """
        # 如果没有信任度估计器，直接采纳人类动作
        if not hasattr(self, 'trust_estimator') or self.trust_estimator is None:
            return human_action

        # 获取AI建议动作
        ai_action = self.act(state, legal_actions)

        # 更新信任度
        if human_action is not None and ai_action is not None:
            self.trust_estimator.update_trust_level(human_action, ai_action, state)

        # 获取介入程度
        intervention_level = self.trust_estimator.get_ai_intervention_level()

        # 记录决策信息
        if not hasattr(self, 'human_ai_decisions'):
            self.human_ai_decisions = []

        self.human_ai_decisions.append({
            "trust_level": self.trust_estimator.get_trust_level(),
            "intervention_level": intervention_level,
            "human_action": str(human_action),
            "ai_action": str(ai_action)
        })

        # 根据介入程度决定最终动作
        # 这里使用0.7作为介入阈值，可以根据需要调整
        if intervention_level > 0.7:
            # AI介入
            logger.info(f"AI介入 (介入程度: {intervention_level:.2f})")
            return ai_action
        else:
            # 采纳人类决策
            logger.info(f"采纳人类决策 (介入程度: {intervention_level:.2f})")
            return human_action

    def get_trust_level(self) -> float:
        """
        获取当前信任度

        Returns:
            float: 当前信任度
        """
        if hasattr(self, 'trust_estimator') and self.trust_estimator is not None:
            return self.trust_estimator.get_trust_level()
        return 0.5  # 默认中等信任度

    def get_intervention_level(self) -> float:
        """
        获取AI介入程度

        Returns:
            float: AI介入程度
        """
        if hasattr(self, 'trust_estimator') and self.trust_estimator is not None:
            return self.trust_estimator.get_ai_intervention_level()
        return 0.5  # 默认中等介入程度

    def get_trust_stats(self) -> Dict[str, Any]:
        """
        获取信任度估计器的统计信息

        Returns:
            Dict[str, Any]: 统计信息字典
        """
        if hasattr(self, 'trust_estimator') and self.trust_estimator is not None:
            stats = self.trust_estimator.get_stats()

            # 添加人类-AI决策历史
            if hasattr(self, 'human_ai_decisions'):
                stats["human_ai_decisions"] = self.human_ai_decisions

            return stats
        return {
            "error": "信任度估计器未初始化",
            "current_trust_level": 0.5,
            "intervention_level": 0.5
        }

    def init_hybrid_decision_system(
        self,
        components: Dict[str, Any] = None,
        meta_strategy: str = "adaptive",
        enable_hrl: bool = True,
        confidence_threshold: float = 0.7,
        history_window: int = 10
    ) -> None:
        """
        初始化混合决策系统

        Args:
            components: 决策组件字典，如果为None则使用默认组件
            meta_strategy: 元控制器策略，可选值为"fixed", "random", "adaptive", "ucb"
            enable_hrl: 是否启用层次化强化学习
            confidence_threshold: 信任度阈值
            history_window: 历史窗口大小
        """
        from cardgame_ai.algorithms.hybrid_decision_system import (
            NeuralNetworkComponent, SearchComponent, RuleComponent
        )

        # 如果没有提供组件，使用默认组件
        if components is None:
            components = {}

            # 神经网络组件
            components["neural_network"] = NeuralNetworkComponent(self.efficient_zero)

            # 搜索组件
            components["search"] = SearchComponent(self.mcts)

            # 规则组件
            components["rule"] = RuleComponent(None)  # 使用默认规则引擎

        # 创建混合决策系统
        self.hybrid_decision_system = HybridDecisionSystem(
            components=components,
            meta_strategy=meta_strategy,
            enable_hrl=enable_hrl,
            confidence_threshold=confidence_threshold,
            history_window=history_window
        )
        
        # 将联合信念追踪器集成到混合决策系统中
        if hasattr(self, 'joint_belief_tracker') and self.joint_belief_tracker is not None:
            self.hybrid_decision_system.joint_belief_tracker = self.joint_belief_tracker
            logger.info("已将联合信念追踪器集成到混合决策系统中")

        # 使用已有的信任度估计器
        if hasattr(self, 'trust_estimator') and self.trust_estimator is not None:
            self.hybrid_decision_system.trust_estimator = self.trust_estimator
            self.hybrid_decision_system.meta_controller.trust_estimator = self.trust_estimator

        logger.info("已初始化混合决策系统")

    def get_explanation_history(
        self,
        limit: Optional[int] = None,
        decision_type: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        获取解释历史

        Args:
            limit: 返回的历史记录数量限制
            decision_type: 决策类型过滤

        Returns:
            List[Dict[str, Any]]: 解释历史，如果未启用决策解释模式则返回空列表
        """
        if hasattr(self, 'explanation_manager'):
            return self.explanation_manager.get_explanation_history(limit, decision_type)
        else:
            logger.warning("未找到解释管理器，无法获取解释历史")
            return []

    def get_explanation_stats(self) -> Dict[str, int]:
        """
        获取解释统计信息

        Returns:
            Dict[str, int]: 解释统计信息，如果未启用决策解释模式则返回空字典
        """
        if hasattr(self, 'explanation_manager'):
            return self.explanation_manager.get_stats()
        else:
            logger.warning("未找到解释管理器，无法获取解释统计信息")
            return {}

    def step_with_human(
        self,
        state: Any,
        human_action: Any,
        ai_action: Any,
        reward: float,
        next_state: Any,
        done: bool,
        human_feedback: Optional[Dict[str, Any]] = None,
        preference_data: Optional[Dict[str, Any]] = None,
        info: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        记录人机交互步骤数据

        Args:
            state: 当前状态
            human_action: 人类执行的动作
            ai_action: AI执行的动作
            reward: 获得的奖励
            next_state: 下一个状态
            done: 是否结束
            human_feedback: 人类反馈，可选，包含评分等信息
            preference_data: 偏好数据，可选，包含偏好比较信息
            info: 额外信息，可选
        """
        if not self.use_online_learning or self.online_collector is None:
            logger.warning("未启用在线学习或未创建在线数据收集器，无法记录交互数据")
            return

        # 收集交互数据
        self.online_collector.collect_step(
            state=state,
            action=ai_action,
            reward=reward,
            next_state=next_state,
            done=done,
            human_feedback=human_feedback,
            preference_data=preference_data,
            info=info
        )

        # 如果有人类动作，收集模仿学习数据
        if human_action is not None:
            self.online_collector.collect_imitation_data(
                state=state,
                human_action=human_action,
                info=info
            )

        logger.debug("已记录人机交互数据")
        # 如果当前局结束，触发每局在线更新
        if done:
            try:
                update_results = self.trigger_online_update()
                logger.info(f"每局结束后触发在线更新: {update_results}")
            except Exception as e:
                logger.warning(f"触发每局在线更新失败: {e}")

    def trigger_online_update(
        self,
        batch_size: int = 64,
        update_steps: int = 1
    ) -> Dict[str, Any]:
        """
        触发在线更新

        Args:
            batch_size: 批次大小
            update_steps: 更新步数

        Returns:
            Dict[str, Any]: 更新结果
        """
        if not self.use_online_learning or self.online_collector is None:
            logger.warning("未启用在线学习或未创建在线数据收集器，无法触发在线更新")
            return {}

        # 检查缓冲区大小
        if len(self.online_collector) < batch_size:
            logger.debug(f"缓冲区样本不足，当前样本数: {len(self.online_collector)}, 需要: {batch_size}")
            return {}

        # 初始化结果
        results = {}

        # 执行多步更新
        for _ in range(update_steps):
            # 从在线数据收集器获取批次数据
            batch_data = self.online_collector.get_batch(batch_size)

            # 获取人类反馈批次
            human_feedback_batch = self.online_collector.get_human_feedback_batch(batch_size)

            # 调用核心模型的在线更新方法
            update_result = self.train(batch_data, human_feedback_batch=human_feedback_batch)

            # 合并结果
            for key, value in update_result.items():
                if key not in results:
                    results[key] = value
                else:
                    # 如果是字典，合并子字典
                    if isinstance(value, dict) and isinstance(results[key], dict):
                        for sub_key, sub_value in value.items():
                            if sub_key not in results[key]:
                                results[key][sub_key] = sub_value
                            else:
                                # 如果是数值，取平均值
                                if isinstance(sub_value, (int, float)) and isinstance(results[key][sub_key], (int, float)):
                                    results[key][sub_key] = (results[key][sub_key] + sub_value) / 2
                    # 如果是数值，取平均值
                    elif isinstance(value, (int, float)) and isinstance(results[key], (int, float)):
                        results[key] = (results[key] + value) / 2

        logger.info(f"已完成在线更新，更新步数: {update_steps}")
        return results