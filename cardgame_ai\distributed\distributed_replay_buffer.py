#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
分布式经验回放缓冲区模块

提供高效的分布式经验回放缓冲区实现，支持大规模分布式训练。
使用Ray作为分布式计算框架，实现高效的数据传输和共享。
"""

import os
import time
import logging
import numpy as np
import random
from typing import Dict, List, Tuple, Any, Optional, Union
import threading
import ray

from cardgame_ai.core.base import Experience, Batch
from cardgame_ai.algorithms.parallel_replay_buffer import SumTree, ShardedSumTree, ParallelPrioritizedReplayBuffer

# 设置日志
logger = logging.getLogger(__name__)


@ray.remote
class ReplayBufferShard:
    """
    经验回放缓冲区分片

    作为Ray Actor运行，管理一部分经验数据。
    支持优先级采样和更新。
    """

    def __init__(self, capacity: int, alpha: float = 0.6, beta: float = 0.4,
                 beta_increment: float = 0.001, epsilon: float = 1e-6):
        """
        初始化经验回放缓冲区分片

        Args:
            capacity: 分片容量
            alpha: 优先级指数，控制采样概率与优先级的关系
            beta: 重要性采样指数，用于修正优先级采样的偏差
            beta_increment: beta的增量，随着训练进行逐渐增加beta
            epsilon: 小常数，防止优先级为0
        """
        self.capacity = capacity
        self.alpha = alpha
        self.beta = beta
        self.beta_increment = beta_increment
        self.epsilon = epsilon

        # 创建SumTree
        self.tree = SumTree(capacity)

        # 当前大小
        self.size = 0

        # 最大优先级，用于新添加的经验
        self.max_priority = 1.0

    def add(self, experience: Any) -> None:
        """
        添加经验

        Args:
            experience: 经验数据
        """
        # 使用最大优先级添加新经验
        priority = self.max_priority ** self.alpha
        self.tree.add(priority, experience)
        self.size = min(self.size + 1, self.capacity)

    def sample(self, batch_size: int) -> Tuple[List[Any], List[int], np.ndarray]:
        """
        采样经验

        Args:
            batch_size: 批次大小

        Returns:
            Tuple[List[Any], List[int], np.ndarray]: 经验列表、索引列表、重要性权重数组
        """
        # 检查缓冲区大小
        if self.size == 0:
            return [], [], np.array([])

        batch_size = min(batch_size, self.size)

        # 采样索引和优先级
        indices = []
        priorities = []
        experiences = []

        # 计算优先级总和
        total_priority = self.tree.total()

        # 计算每个区间的大小
        segment = total_priority / batch_size

        # 增加beta
        self.beta = min(1.0, self.beta + self.beta_increment)

        # 采样
        for i in range(batch_size):
            # 计算区间范围
            a = segment * i
            b = segment * (i + 1)

            # 在区间内随机采样
            value = random.uniform(a, b)

            # 获取对应的索引、优先级和经验
            idx, priority, experience = self.tree.get(value)

            indices.append(idx)
            priorities.append(priority)
            experiences.append(experience)

        # 计算重要性权重
        priorities = np.array(priorities) / total_priority
        weights = (self.size * priorities) ** (-self.beta)
        weights = weights / weights.max()

        return experiences, indices, weights

    def update_priorities(self, indices: List[int], priorities: List[float]) -> None:
        """
        更新优先级

        Args:
            indices: 索引列表
            priorities: 优先级列表
        """
        for idx, priority in zip(indices, priorities):
            # 添加epsilon防止优先级为0
            priority = (priority + self.epsilon) ** self.alpha

            # 更新最大优先级
            self.max_priority = max(self.max_priority, priority)

            # 更新树
            self.tree.update(idx, priority)

    def get_size(self) -> int:
        """
        获取当前大小

        Returns:
            int: 当前大小
        """
        return self.size

    def get_buffer_stats(self) -> Dict[str, Any]:
        """
        获取缓冲区统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            "size": self.size,
            "capacity": self.capacity,
            "max_priority": self.max_priority,
            "beta": self.beta
        }


@ray.remote
class DistributedReplayBuffer:
    """
    分布式经验回放缓冲区

    管理多个分布式分片，支持大规模经验数据存储和高效采样。
    作为Ray Actor运行，提供全局访问点。
    """

    def __init__(self,
                 total_capacity: int,
                 num_shards: int = 8,
                 alpha: float = 0.6,
                 beta: float = 0.4,
                 beta_increment: float = 0.001,
                 epsilon: float = 1e-6):
        """
        初始化分布式经验回放缓冲区

        Args:
            total_capacity: 总容量
            num_shards: 分片数量
            alpha: 优先级指数，控制采样概率与优先级的关系
            beta: 重要性采样指数，用于修正优先级采样的偏差
            beta_increment: beta的增量，随着训练进行逐渐增加beta
            epsilon: 小常数，防止优先级为0
        """
        self.total_capacity = total_capacity
        self.num_shards = num_shards
        self.alpha = alpha
        self.beta = beta
        self.beta_increment = beta_increment
        self.epsilon = epsilon

        # 计算每个分片的容量
        self.shard_capacity = total_capacity // num_shards

        # 创建分片
        self.shards = [
            ReplayBufferShard.remote(
                capacity=self.shard_capacity,
                alpha=alpha,
                beta=beta,
                beta_increment=beta_increment,
                epsilon=epsilon
            )
            for _ in range(num_shards)
        ]

        # 当前写入分片索引
        self.current_shard_idx = 0

        # 分片选择策略
        self.shard_selection_strategy = "round_robin"  # 可选: "round_robin", "random", "least_used"

        # 分片大小缓存，用于least_used策略
        self.shard_sizes = [0] * num_shards
        self.last_size_update = time.time()
        self.size_update_interval = 5.0  # 秒

        # 总大小
        self.total_size = 0

        # 添加计数
        self.add_count = 0

        # 采样计数
        self.sample_count = 0

        # 更新计数
        self.update_count = 0

    def _select_shard_for_write(self) -> int:
        """
        选择用于写入的分片

        Returns:
            int: 分片索引
        """
        if self.shard_selection_strategy == "round_robin":
            # 轮询策略
            shard_idx = self.current_shard_idx
            self.current_shard_idx = (self.current_shard_idx + 1) % self.num_shards
            return shard_idx
        elif self.shard_selection_strategy == "random":
            # 随机策略
            return random.randint(0, self.num_shards - 1)
        elif self.shard_selection_strategy == "least_used":
            # 最少使用策略
            # 定期更新分片大小
            current_time = time.time()
            if current_time - self.last_size_update > self.size_update_interval:
                self.shard_sizes = ray.get([shard.get_size.remote() for shard in self.shards])
                self.last_size_update = current_time

            # 选择最小的分片
            return np.argmin(self.shard_sizes)
        else:
            # 默认使用轮询策略
            shard_idx = self.current_shard_idx
            self.current_shard_idx = (self.current_shard_idx + 1) % self.num_shards
            return shard_idx

    def add(self, experience: Any) -> None:
        """
        添加经验

        Args:
            experience: 经验数据
        """
        # 选择分片
        shard_idx = self._select_shard_for_write()

        # 添加到分片
        self.shards[shard_idx].add.remote(experience)

        # 更新计数
        self.add_count += 1

        # 如果使用least_used策略，更新缓存的分片大小
        if self.shard_selection_strategy == "least_used":
            self.shard_sizes[shard_idx] += 1

    def add_batch(self, experiences: List[Any]) -> None:
        """
        批量添加经验

        Args:
            experiences: 经验数据列表
        """
        # 将经验分配到不同的分片
        shard_experiences = [[] for _ in range(self.num_shards)]

        for exp in experiences:
            shard_idx = self._select_shard_for_write()
            shard_experiences[shard_idx].append(exp)

            # 更新计数
            self.add_count += 1

            # 如果使用least_used策略，更新缓存的分片大小
            if self.shard_selection_strategy == "least_used":
                self.shard_sizes[shard_idx] += 1

        # 并行添加到各个分片
        add_ops = []
        for shard_idx, exps in enumerate(shard_experiences):
            if exps:
                add_ops.append(self.shards[shard_idx].add.remote(exps))

        # 等待所有添加操作完成
        ray.get(add_ops)

    def sample(self, batch_size: int) -> Tuple[List[Any], List[Tuple[int, int]], np.ndarray]:
        """
        采样经验

        Args:
            batch_size: 批次大小

        Returns:
            Tuple[List[Any], List[Tuple[int, int]], np.ndarray]:
                经验列表、(分片索引,分片内索引)元组列表、重要性权重数组
        """
        # 更新总大小
        self.total_size = sum(ray.get([shard.get_size.remote() for shard in self.shards]))

        if self.total_size == 0:
            return [], [], np.array([])

        # 计算每个分片的采样数量
        shard_sizes = ray.get([shard.get_size.remote() for shard in self.shards])
        total_size = sum(shard_sizes)

        if total_size == 0:
            return [], [], np.array([])

        # 根据分片大小分配采样数量
        shard_probs = np.array(shard_sizes) / total_size
        shard_sample_counts = np.random.multinomial(batch_size, shard_probs)

        # 从每个分片采样
        sample_ops = []
        for shard_idx, count in enumerate(shard_sample_counts):
            if count > 0:
                sample_ops.append((shard_idx, self.shards[shard_idx].sample.remote(count)))

        # 等待所有采样操作完成
        sample_results = [(shard_idx, ray.get(op)) for shard_idx, op in sample_ops]

        # 合并结果
        all_experiences = []
        all_indices = []
        all_weights = []

        for shard_idx, (experiences, indices, weights) in sample_results:
            all_experiences.extend(experiences)
            # 将索引转换为(分片索引,分片内索引)元组
            all_indices.extend([(shard_idx, idx) for idx in indices])
            all_weights.extend(weights)

        # 更新计数
        self.sample_count += batch_size

        return all_experiences, all_indices, np.array(all_weights)

    def update_priorities(self, indices: List[Tuple[int, int]], priorities: List[float]) -> None:
        """
        更新优先级

        Args:
            indices: (分片索引,分片内索引)元组列表
            priorities: 优先级列表
        """
        # 按分片分组
        shard_indices = {}
        shard_priorities = {}

        for (shard_idx, idx), priority in zip(indices, priorities):
            if shard_idx not in shard_indices:
                shard_indices[shard_idx] = []
                shard_priorities[shard_idx] = []

            shard_indices[shard_idx].append(idx)
            shard_priorities[shard_idx].append(priority)

        # 并行更新各个分片
        update_ops = []
        for shard_idx in shard_indices:
            update_ops.append(
                self.shards[shard_idx].update_priorities.remote(
                    shard_indices[shard_idx],
                    shard_priorities[shard_idx]
                )
            )

        # 等待所有更新操作完成
        ray.get(update_ops)

        # 更新计数
        self.update_count += len(indices)

    def get_size(self) -> int:
        """
        获取总大小

        Returns:
            int: 总大小
        """
        # 更新总大小
        self.total_size = sum(ray.get([shard.get_size.remote() for shard in self.shards]))
        return self.total_size

    def get_buffer_stats(self) -> Dict[str, Any]:
        """
        获取缓冲区统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        # 获取各个分片的统计信息
        shard_stats = ray.get([shard.get_buffer_stats.remote() for shard in self.shards])

        # 计算总大小
        total_size = sum(stats["size"] for stats in shard_stats)

        return {
            "total_size": total_size,
            "total_capacity": self.total_capacity,
            "num_shards": self.num_shards,
            "shard_stats": shard_stats,
            "add_count": self.add_count,
            "sample_count": self.sample_count,
            "update_count": self.update_count,
            "shard_selection_strategy": self.shard_selection_strategy
        }

    def set_shard_selection_strategy(self, strategy: str) -> None:
        """
        设置分片选择策略

        Args:
            strategy: 策略名称，可选值为"round_robin", "random", "least_used"
        """
        valid_strategies = ["round_robin", "random", "least_used"]
        if strategy not in valid_strategies:
            raise ValueError(f"无效的分片选择策略: {strategy}，有效值为: {valid_strategies}")

        self.shard_selection_strategy = strategy
