"""
在线信念更新器模块

提供基于对手出牌动作实时更新信念状态（对手手牌概率分布）的能力。
可以使用多种方法（如规则更新、粒子滤波等）动态调整信念状态。
"""
import logging
from typing import Dict, List, Any, Optional, Union, Tuple
import numpy as np
import torch

from cardgame_ai.games.common.belief_state import BeliefState, BeliefSource
from cardgame_ai.games.doudizhu.card import Card

logger = logging.getLogger(__name__)


class OnlineBeliefUpdater:
    """
    在线信念更新器

    根据对手的实时动作更新信念状态（对手手牌概率分布）。
    支持多种更新策略，如规则更新、粒子滤波等。
    """

    def __init__(
        self,
        update_method: str = "rule",
        decay_factor: float = 0.9,
        particle_count: int = 100,
        random_noise: float = 0.05,
        min_probability: float = 0.001,
        enable_action_impact: bool = True,
        action_impact_weight: float = 0.2,
        enable_history_weighting: bool = True,
        history_weight_decay: float = 0.8,
        confidence_boost: float = 0.1
    ):
        """
        初始化在线信念更新器

        Args:
            update_method (str, optional): 更新方法，可选 'rule'（规则）, 'particle'（粒子滤波）. Defaults to "rule".
            decay_factor (float, optional): 衰减因子，用于平滑更新. Defaults to 0.9.
            particle_count (int, optional): 粒子数量，仅在粒子滤波方法中使用. Defaults to 100.
            random_noise (float, optional): 随机噪声，用于增加探索性. Defaults to 0.05.
            min_probability (float, optional): 最小概率阈值，防止某些牌的概率降为0. Defaults to 0.001.
            enable_action_impact (bool, optional): 是否启用动作影响评估. Defaults to True.
            action_impact_weight (float, optional): 动作影响权重. Defaults to 0.2.
            enable_history_weighting (bool, optional): 是否启用历史权重. Defaults to True.
            history_weight_decay (float, optional): 历史权重衰减因子. Defaults to 0.8.
            confidence_boost (float, optional): 更新后的置信度提升量. Defaults to 0.1.
        """
        self.update_method = update_method
        self.decay_factor = decay_factor
        self.particle_count = particle_count
        self.random_noise = random_noise
        self.min_probability = min_probability
        self.enable_action_impact = enable_action_impact
        self.action_impact_weight = action_impact_weight
        self.enable_history_weighting = enable_history_weighting
        self.history_weight_decay = history_weight_decay
        self.confidence_boost = confidence_boost
        
        # 用于存储历史动作及其影响
        self.action_history = []
        self.max_history_length = 5

    def update(
        self,
        current_belief: BeliefState,
        opponent_action: Optional[List[Union[Card, str]]],
        public_info: Dict[str, Any],
        previous_actions: Optional[List[Dict[str, Any]]] = None
    ) -> BeliefState:
        """
        根据对手动作更新信念状态

        Args:
            current_belief (BeliefState): 当前信念状态
            opponent_action (Optional[List[Union[Card, str]]]): 对手的出牌动作
            public_info (Dict[str, Any]): 公共信息
            previous_actions (Optional[List[Dict[str, Any]]], optional): 历史动作. Defaults to None.

        Returns:
            BeliefState: 更新后的信念状态
        """
        # 记录历史动作
        if opponent_action is not None:
            self._record_action(opponent_action)

        # 根据选择的方法更新信念
        if self.update_method == "rule":
            return self._rule_based_update(current_belief, opponent_action, public_info, previous_actions)
        elif self.update_method == "particle":
            return self._particle_filter_update(current_belief, opponent_action, public_info, previous_actions)
        else:
            logger.warning(f"不支持的更新方法: {self.update_method}，使用规则更新")
            return self._rule_based_update(current_belief, opponent_action, public_info, previous_actions)

    def _rule_based_update(
        self,
        current_belief: BeliefState,
        opponent_action: Optional[List[Union[Card, str]]],
        public_info: Dict[str, Any],
        previous_actions: Optional[List[Dict[str, Any]]] = None
    ) -> BeliefState:
        """
        基于规则的信念更新

        Args:
            current_belief (BeliefState): 当前信念状态
            opponent_action (Optional[List[Union[Card, str]]]): 对手的出牌动作
            public_info (Dict[str, Any]): 公共信息
            previous_actions (Optional[List[Dict[str, Any]]], optional): 历史动作. Defaults to None.

        Returns:
            BeliefState: 更新后的信念状态
        """
        # 创建信念状态副本
        updated_belief = current_belief.copy() if hasattr(current_belief, 'copy') else BeliefState(
            player_id=current_belief.player_id,
            card_probabilities=current_belief.card_probabilities.copy(),
            estimated_hand_length=current_belief.estimated_hand_length,
            source=current_belief.source,
            confidence=current_belief.confidence
        )
        
        # 初始化新的概率
        new_probs = updated_belief.card_probabilities.copy()
        
        # 1. 处理对手出牌：对手出的牌概率设为0
        if opponent_action and len(opponent_action) > 0:
            action_cards = [str(card) for card in opponent_action]
            for card in action_cards:
                new_probs[card] = 0.0
            
            # 更新手牌数量估计
            if hasattr(updated_belief, 'estimated_hand_length') and updated_belief.estimated_hand_length is not None:
                updated_belief.estimated_hand_length -= len(action_cards)
        
        # 2. 处理公开信息
        if 'known_cards' in public_info:
            known_cards = public_info['known_cards']
            if isinstance(known_cards, list) and len(known_cards) > 0:
                known_cards_str = [str(card) if not isinstance(card, str) else card
                                  for card in known_cards]
                for card in known_cards_str:
                    new_probs[card] = 0.0
        
        # 3. 如果启用动作影响，根据对手动作调整特定牌型的概率
        if self.enable_action_impact and opponent_action:
            self._adjust_probabilities_by_action(new_probs, opponent_action)
        
        # 4. 如果启用历史权重，考虑历史动作的影响
        if self.enable_history_weighting and previous_actions:
            self._adjust_probabilities_by_history(new_probs, previous_actions)
        
        # 5. 添加少量随机噪声以增加探索性
        for card in new_probs:
            if new_probs[card] > 0:  # 只对可能持有的牌添加噪声
                noise = np.random.normal(0, self.random_noise)
                new_probs[card] = max(self.min_probability, new_probs[card] + noise)
        
        # 6. 应用概率更新，并确保概率和为1
        updated_belief.update_probabilities(new_probs)
        updated_belief.normalize_probabilities()
        
        # 7. 更新信念来源和置信度
        updated_belief.source = BeliefSource.ONLINE_UPDATE
        updated_belief.confidence = min(1.0, current_belief.confidence + self.confidence_boost)
        
        return updated_belief

    def _particle_filter_update(
        self,
        current_belief: BeliefState,
        opponent_action: Optional[List[Union[Card, str]]],
        public_info: Dict[str, Any],
        previous_actions: Optional[List[Dict[str, Any]]] = None
    ) -> BeliefState:
        """
        基于粒子滤波的信念更新

        Args:
            current_belief (BeliefState): 当前信念状态
            opponent_action (Optional[List[Union[Card, str]]]): 对手的出牌动作
            public_info (Dict[str, Any]): 公共信息
            previous_actions (Optional[List[Dict[str, Any]]], optional): 历史动作. Defaults to None.

        Returns:
            BeliefState: 更新后的信念状态
        """
        # 粒子滤波实现较为复杂，这里提供一个简化版本
        # 创建信念状态副本
        updated_belief = current_belief.copy() if hasattr(current_belief, 'copy') else BeliefState(
            player_id=current_belief.player_id,
            card_probabilities=current_belief.card_probabilities.copy(),
            estimated_hand_length=current_belief.estimated_hand_length,
            source=current_belief.source,
            confidence=current_belief.confidence
        )
        
        # 初始化新的概率
        new_probs = updated_belief.card_probabilities.copy()
        
        # 1. 从当前概率分布生成粒子
        particles = self._generate_particles(new_probs)
        
        # 2. 根据观察（对手动作）计算每个粒子的权重
        weights = self._calculate_particle_weights(particles, opponent_action, public_info)
        
        # 3. 重采样粒子
        resampled_particles = self._resample_particles(particles, weights)
        
        # 4. 从重采样的粒子中估计新的概率分布
        new_probs = self._estimate_probabilities(resampled_particles)
        
        # 5. 应用概率更新
        updated_belief.update_probabilities(new_probs)
        updated_belief.normalize_probabilities()
        
        # 6. 更新信念来源和置信度
        updated_belief.source = BeliefSource.ONLINE_UPDATE
        updated_belief.confidence = min(1.0, current_belief.confidence + self.confidence_boost)
        
        return updated_belief
    
    def _generate_particles(self, probabilities: Dict[str, float]) -> List[Dict[str, float]]:
        """
        根据概率分布生成粒子

        Args:
            probabilities (Dict[str, float]): 当前概率分布

        Returns:
            List[Dict[str, float]]: 粒子列表
        """
        particles = []
        cards = list(probabilities.keys())
        
        for _ in range(self.particle_count):
            particle = {card: 0.0 for card in cards}
            # 简化：每个粒子随机选择一些牌
            for card in cards:
                if np.random.random() < probabilities[card]:
                    particle[card] = 1.0
            particles.append(particle)
        
        return particles
    
    def _calculate_particle_weights(
        self,
        particles: List[Dict[str, float]],
        opponent_action: Optional[List[Union[Card, str]]],
        public_info: Dict[str, Any]
    ) -> np.ndarray:
        """
        计算每个粒子的权重

        Args:
            particles (List[Dict[str, float]]): 粒子列表
            opponent_action (Optional[List[Union[Card, str]]]): 对手动作
            public_info (Dict[str, Any]): 公共信息

        Returns:
            np.ndarray: 权重数组
        """
        weights = np.ones(len(particles))
        
        if opponent_action and len(opponent_action) > 0:
            action_cards = [str(card) for card in opponent_action]
            
            for i, particle in enumerate(particles):
                # 对于对手出的牌，如果粒子中该牌概率为0，则增加权重
                # 否则减少权重
                weight_multiplier = 1.0
                for card in action_cards:
                    if card in particle:
                        if particle[card] == 0.0:
                            weight_multiplier *= 1.5  # 增加权重
                        else:
                            weight_multiplier *= 0.5  # 减少权重
                weights[i] *= weight_multiplier
        
        # 归一化权重
        weights = weights / np.sum(weights)
        return weights
    
    def _resample_particles(
        self,
        particles: List[Dict[str, float]],
        weights: np.ndarray
    ) -> List[Dict[str, float]]:
        """
        根据权重重采样粒子

        Args:
            particles (List[Dict[str, float]]): 粒子列表
            weights (np.ndarray): 权重数组

        Returns:
            List[Dict[str, float]]: 重采样后的粒子列表
        """
        indices = np.random.choice(
            len(particles),
            size=len(particles),
            replace=True,
            p=weights
        )
        
        return [particles[i].copy() for i in indices]
    
    def _estimate_probabilities(self, particles: List[Dict[str, float]]) -> Dict[str, float]:
        """
        从粒子中估计概率分布

        Args:
            particles (List[Dict[str, float]]): 粒子列表

        Returns:
            Dict[str, float]: 估计的概率分布
        """
        if not particles:
            return {}
        
        # 获取所有可能的牌
        all_cards = particles[0].keys()
        
        # 初始化概率
        probabilities = {card: 0.0 for card in all_cards}
        
        # 统计每张牌在粒子中的平均值
        for particle in particles:
            for card, value in particle.items():
                probabilities[card] += value / len(particles)
        
        return probabilities
    
    def _record_action(self, action: Optional[List[Union[Card, str]]]) -> None:
        """
        记录动作历史

        Args:
            action (Optional[List[Union[Card, str]]]): 对手动作
        """
        if action is None or len(action) == 0:
            return
        
        # 转换为字符串表示
        action_str = [str(card) for card in action]
        
        # 添加到历史记录中
        self.action_history.append(action_str)
        
        # 保持历史记录长度
        if len(self.action_history) > self.max_history_length:
            self.action_history.pop(0)
    
    def _adjust_probabilities_by_action(
        self,
        probabilities: Dict[str, float],
        opponent_action: List[Union[Card, str]]
    ) -> None:
        """
        根据对手动作调整概率分布

        Args:
            probabilities (Dict[str, float]): 概率分布
            opponent_action (List[Union[Card, str]]): 对手动作
        """
        # 简化实现：根据对手出牌类型，调整相关牌型的概率
        # 这里可以实现更复杂的规则
        
        # 转换为字符串表示
        action_str = [str(card) for card in opponent_action]
        
        # 示例规则：如果对手出单牌，增加其他单牌的概率
        # 如果对手出对子，增加其他对子的概率，以此类推
        if len(action_str) == 1:  # 单牌
            for card in probabilities:
                if probabilities[card] > 0:  # 只调整可能持有的牌
                    # 这里可以添加更复杂的逻辑，例如考虑牌的大小关系
                    probabilities[card] *= (1 + self.action_impact_weight)
        elif len(action_str) == 2:  # 对子
            # 可以实现对子相关的调整逻辑
            pass
    
    def _adjust_probabilities_by_history(
        self,
        probabilities: Dict[str, float],
        previous_actions: List[Dict[str, Any]]
    ) -> None:
        """
        根据历史动作调整概率分布

        Args:
            probabilities (Dict[str, float]): 概率分布
            previous_actions (List[Dict[str, Any]]): 历史动作
        """
        # 简化实现：根据历史动作模式，调整概率分布
        # 这里可以实现更复杂的逻辑，例如考虑玩家的出牌偏好
        
        # 示例：考虑最近的n次动作
        recent_actions = previous_actions[-min(3, len(previous_actions)):]
        
        # 示例规则：如果玩家倾向于出特定类型的牌，调整相应的概率
        # 这里只是一个简化的示例
        for action_data in recent_actions:
            if 'cards' in action_data and action_data['cards']:
                action_cards = action_data['cards']
                if len(action_cards) == 1:  # 单牌
                    # 调整单牌概率
                    pass
                elif len(action_cards) == 2:  # 对子
                    # 调整对子概率
                    pass 