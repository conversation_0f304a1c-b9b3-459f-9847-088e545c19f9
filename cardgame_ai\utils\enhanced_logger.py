"""
增强日志系统

提供统一的、结构化的日志记录功能，支持训练监控、性能分析和错误追踪。
"""

import os
import sys
import json
import logging
import time
from datetime import datetime
from typing import Dict, Any, Optional, List, Union
from pathlib import Path
import threading
from collections import defaultdict, deque


class StructuredFormatter(logging.Formatter):
    """结构化日志格式器"""
    
    def format(self, record):
        # 基础信息
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # 添加额外的结构化数据
        if hasattr(record, 'extra_data'):
            log_entry.update(record.extra_data)
            
        return json.dumps(log_entry, ensure_ascii=False)


class TrainingMetricsLogger:
    """训练指标日志记录器"""
    
    def __init__(self, log_dir: str):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # 指标存储
        self.metrics_history = defaultdict(deque)
        self.episode_metrics = {}
        self.performance_stats = {}
        
        # 文件句柄
        self.metrics_file = None
        self.performance_file = None
        self._init_files()
        
    def _init_files(self):
        """初始化日志文件"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 训练指标文件
        metrics_path = self.log_dir / f'training_metrics_{timestamp}.jsonl'
        self.metrics_file = open(metrics_path, 'w', encoding='utf-8')
        
        # 性能统计文件
        performance_path = self.log_dir / f'performance_stats_{timestamp}.jsonl'
        self.performance_file = open(performance_path, 'w', encoding='utf-8')
        
    def log_episode_metrics(self, episode: int, metrics: Dict[str, Any]):
        """记录回合指标"""
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'episode': episode,
            'metrics': metrics
        }
        
        # 写入文件
        self.metrics_file.write(json.dumps(log_entry, ensure_ascii=False) + '\n')
        self.metrics_file.flush()
        
        # 更新历史记录
        for key, value in metrics.items():
            if isinstance(value, (int, float)):
                self.metrics_history[key].append(value)
                # 保持最近1000个值
                if len(self.metrics_history[key]) > 1000:
                    self.metrics_history[key].popleft()
    
    def log_performance_stats(self, stats: Dict[str, Any]):
        """记录性能统计"""
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'stats': stats
        }
        
        self.performance_file.write(json.dumps(log_entry, ensure_ascii=False) + '\n')
        self.performance_file.flush()
        
    def get_recent_metrics(self, metric_name: str, count: int = 100) -> List[float]:
        """获取最近的指标值"""
        if metric_name in self.metrics_history:
            return list(self.metrics_history[metric_name])[-count:]
        return []
    
    def close(self):
        """关闭文件句柄"""
        if self.metrics_file:
            self.metrics_file.close()
        if self.performance_file:
            self.performance_file.close()


class EnhancedLogger:
    """增强日志记录器"""
    
    def __init__(
        self,
        name: str = "cardgame_ai",
        log_dir: str = "logs",
        level: str = "INFO",
        enable_structured_logging: bool = True,
        enable_metrics_logging: bool = True,
        enable_console: bool = True,
        enable_file: bool = True
    ):
        """
        初始化增强日志记录器
        
        Args:
            name: 日志记录器名称
            log_dir: 日志目录
            level: 日志级别
            enable_structured_logging: 是否启用结构化日志
            enable_metrics_logging: 是否启用指标日志
            enable_console: 是否启用控制台输出
            enable_file: 是否启用文件输出
        """
        self.name = name
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置主日志记录器
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, level.upper()))
        
        # 清除现有处理器
        self.logger.handlers.clear()
        
        # 时间戳
        self.timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 设置处理器
        if enable_console:
            self._setup_console_handler(enable_structured_logging)
        
        if enable_file:
            self._setup_file_handler(enable_structured_logging)
            
        # 指标日志记录器
        self.metrics_logger = None
        if enable_metrics_logging:
            self.metrics_logger = TrainingMetricsLogger(str(self.log_dir))
            
        # 性能监控
        self.start_time = time.time()
        self.last_log_time = self.start_time
        
    def _setup_console_handler(self, structured: bool):
        """设置控制台处理器"""
        console_handler = logging.StreamHandler(sys.stdout)
        
        if structured:
            formatter = StructuredFormatter()
        else:
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
    def _setup_file_handler(self, structured: bool):
        """设置文件处理器"""
        # 主日志文件
        log_file = self.log_dir / f'{self.name}_{self.timestamp}.log'
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        
        if structured:
            formatter = StructuredFormatter()
        else:
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
        
        # 错误日志文件
        error_file = self.log_dir / f'{self.name}_errors_{self.timestamp}.log'
        error_handler = logging.FileHandler(error_file, encoding='utf-8')
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        self.logger.addHandler(error_handler)
    
    def info(self, message: str, extra_data: Optional[Dict[str, Any]] = None):
        """记录INFO级别日志"""
        self._log_with_extra(logging.INFO, message, extra_data)
    
    def debug(self, message: str, extra_data: Optional[Dict[str, Any]] = None):
        """记录DEBUG级别日志"""
        self._log_with_extra(logging.DEBUG, message, extra_data)
        
    def warning(self, message: str, extra_data: Optional[Dict[str, Any]] = None):
        """记录WARNING级别日志"""
        self._log_with_extra(logging.WARNING, message, extra_data)
        
    def error(self, message: str, extra_data: Optional[Dict[str, Any]] = None):
        """记录ERROR级别日志"""
        self._log_with_extra(logging.ERROR, message, extra_data)
        
    def _log_with_extra(self, level: int, message: str, extra_data: Optional[Dict[str, Any]]):
        """带额外数据的日志记录"""
        # 添加性能信息
        current_time = time.time()
        if extra_data is None:
            extra_data = {}
            
        extra_data.update({
            'elapsed_time': current_time - self.start_time,
            'time_since_last_log': current_time - self.last_log_time
        })
        
        # 创建日志记录
        record = self.logger.makeRecord(
            self.logger.name, level, __file__, 0, message, (), None
        )
        record.extra_data = extra_data
        
        # 发送到处理器
        self.logger.handle(record)
        
        self.last_log_time = current_time
    
    def log_training_progress(
        self,
        episode: int,
        progress: float,
        metrics: Dict[str, Any],
        performance_stats: Optional[Dict[str, Any]] = None
    ):
        """记录训练进度"""
        # 基础进度信息
        progress_info = {
            'episode': episode,
            'progress': progress,
            'metrics': metrics
        }
        
        if performance_stats:
            progress_info['performance'] = performance_stats
            
        self.info(f"训练进度 - 回合 {episode} ({progress:.1f}%)", progress_info)
        
        # 记录到指标日志
        if self.metrics_logger:
            self.metrics_logger.log_episode_metrics(episode, metrics)
            if performance_stats:
                self.metrics_logger.log_performance_stats(performance_stats)
    
    def log_reward_details(
        self,
        episode: int,
        player_id: int,
        action: str,
        reward_components: Dict[str, float],
        total_reward: float
    ):
        """记录奖励详情"""
        reward_info = {
            'episode': episode,
            'player_id': player_id,
            'action': action,
            'reward_components': reward_components,
            'total_reward': total_reward
        }
        
        self.debug(f"奖励详情 - P{player_id} 动作:{action} 总奖励:{total_reward:.4f}", reward_info)
    
    def log_config_info(self, config: Dict[str, Any]):
        """记录配置信息"""
        self.info("训练配置", {'config': config})
    
    def close(self):
        """关闭日志记录器"""
        if self.metrics_logger:
            self.metrics_logger.close()
            
        # 关闭所有处理器
        for handler in self.logger.handlers:
            handler.close()
            self.logger.removeHandler(handler)


# 全局日志记录器实例
_global_logger: Optional[EnhancedLogger] = None


def get_logger(
    name: str = "cardgame_ai",
    log_dir: str = "logs",
    level: str = "INFO",
    **kwargs
) -> EnhancedLogger:
    """获取全局日志记录器实例"""
    global _global_logger
    
    if _global_logger is None:
        _global_logger = EnhancedLogger(
            name=name,
            log_dir=log_dir,
            level=level,
            **kwargs
        )
    
    return _global_logger


def setup_training_logger(config: Dict[str, Any]) -> EnhancedLogger:
    """根据配置设置训练日志记录器"""
    log_config = config.get('logging', {})
    
    return EnhancedLogger(
        name=config.get('experiment', {}).get('name', 'training'),
        log_dir=log_config.get('log_dir', 'logs'),
        level=log_config.get('level', 'INFO'),
        enable_structured_logging=log_config.get('structured', True),
        enable_metrics_logging=log_config.get('metrics', True),
        enable_console=log_config.get('console', True),
        enable_file=log_config.get('file', True)
    )
