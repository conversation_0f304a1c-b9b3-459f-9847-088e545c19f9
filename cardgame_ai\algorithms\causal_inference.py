"""
因果推断模块

提供对AI决策过程的因果分析，帮助解释特定打法或牌序的因果影响。
"""

import os
import logging
import numpy as np
from typing import Dict, List, Any, Optional, Tuple, Union

from cardgame_ai.core.base import State, Action
from cardgame_ai.games.doudizhu.state import DouD<PERSON>huState
from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType

# 配置日志
logger = logging.getLogger(__name__)


class CausalInferenceModule:
    """
    因果推断模块

    分析AI决策过程中的因果关系，解释特定打法或牌序的影响。
    """

    def __init__(self, confidence_threshold: float = 0.7, enable_counterfactual: bool = False,
                 enable_intervention: bool = False, enable_feature_attribution: bool = False):
        """
        初始化因果推断模块

        Args:
            confidence_threshold: 因果关系置信度阈值
            enable_counterfactual: 是否启用反事实分析
            enable_intervention: 是否启用干预分析
            enable_feature_attribution: 是否启用特征归因分析
        """
        self.confidence_threshold = confidence_threshold
        self.enable_counterfactual = enable_counterfactual
        self.enable_intervention = enable_intervention
        self.enable_feature_attribution = enable_feature_attribution

        # 存储因果关系图
        self.causal_graph = {}

        # 存储历史分析结果
        self.analysis_history = []

        # 统计信息
        self.stats = {
            "total_analyses": 0,
            "high_confidence_analyses": 0,
            "avg_confidence": 0.0,
            "counterfactual_analyses": 0,
            "intervention_analyses": 0,
            "feature_attribution_analyses": 0
        }

        logger.info(f"初始化因果推断模块 (confidence_threshold={confidence_threshold}, "
                   f"enable_counterfactual={enable_counterfactual}, "
                   f"enable_intervention={enable_intervention}, "
                   f"enable_feature_attribution={enable_feature_attribution})")

    def analyze_action_impact(
        self,
        state: State,
        action: Action,
        alternative_actions: List[Action],
        search_results: Dict[str, Any],
        detailed_mode: bool = False
    ) -> Dict[str, Any]:
        """
        分析动作的因果影响

        比较选择的动作与备选动作的差异，推断因果关系。

        Args:
            state: 当前游戏状态
            action: 选择的动作
            alternative_actions: 备选动作列表
            search_results: 搜索结果，包含访问计数、价值估计等
            detailed_mode: 是否启用详细分析模式

        Returns:
            Dict[str, Any]: 因果分析结果
        """
        # 更新统计信息
        self.stats["total_analyses"] += 1

        # 初始化分析结果
        analysis = {
            "action": str(action),
            "state": str(state),
            "causal_effects": [],
            "confidence": 0.0,
            "summary": "",
            "analysis_time": self._get_current_timestamp(),
            "analysis_depth": "detailed" if detailed_mode else "standard"
        }

        # 获取选择动作的价值和访问次数
        action_value = self._get_action_value(action, search_results)
        action_visits = self._get_action_visits(action, search_results)

        # 提取更多动作特征（如果可用）
        action_features = self._extract_action_features(action, search_results)
        if action_features:
            analysis["action_features"] = action_features

        # 分析每个备选动作
        for alt_action in alternative_actions:
            # 跳过与选择动作相同的动作
            if alt_action == action:
                continue

            # 获取备选动作的价值和访问次数
            alt_value = self._get_action_value(alt_action, search_results)
            alt_visits = self._get_action_visits(alt_action, search_results)

            # 计算价值差异
            value_diff = action_value - alt_value

            # 计算置信度（基于访问次数和价值差异）
            confidence = self._calculate_confidence(action_visits, alt_visits, value_diff)

            # 如果置信度足够高或者是详细模式，添加因果效应
            if confidence >= self.confidence_threshold or detailed_mode:
                effect = self._infer_causal_effect(action, alt_action, value_diff, state)

                # 添加到分析结果
                causal_effect = {
                    "alternative_action": str(alt_action),
                    "value_difference": float(value_diff),
                    "confidence": float(confidence),
                    "effect": effect,
                    "visit_ratio": float(action_visits) / max(1, alt_visits)
                }

                # 如果启用特征归因，添加特征差异分析
                if self.enable_feature_attribution:
                    alt_features = self._extract_action_features(alt_action, search_results)
                    if alt_features:
                        feature_diff = self._analyze_feature_differences(action_features, alt_features)
                        causal_effect["feature_attribution"] = feature_diff
                        self.stats["feature_attribution_analyses"] += 1

                analysis["causal_effects"].append(causal_effect)

        # 计算整体置信度（所有因果效应的平均置信度）
        if analysis["causal_effects"]:
            analysis["confidence"] = sum(effect["confidence"] for effect in analysis["causal_effects"]) / len(analysis["causal_effects"])

            # 生成摘要
            analysis["summary"] = self._generate_summary(analysis)

            # 更新统计信息
            self.stats["high_confidence_analyses"] += 1
            self.stats["avg_confidence"] = (self.stats["avg_confidence"] * (self.stats["total_analyses"] - 1) + analysis["confidence"]) / self.stats["total_analyses"]

            # 如果启用反事实分析，添加反事实分析结果
            if self.enable_counterfactual and detailed_mode:
                counterfactual_analysis = self._perform_counterfactual_analysis(state, action, alternative_actions, search_results)
                if counterfactual_analysis:
                    analysis["counterfactual_analysis"] = counterfactual_analysis
                    self.stats["counterfactual_analyses"] += 1

            # 如果启用干预分析，添加干预分析结果
            if self.enable_intervention and detailed_mode:
                intervention_analysis = self._perform_intervention_analysis(state, action, search_results)
                if intervention_analysis:
                    analysis["intervention_analysis"] = intervention_analysis
                    self.stats["intervention_analyses"] += 1

        # 保存分析结果到历史记录
        self.analysis_history.append(analysis)

        return analysis

    def _get_action_value(self, action: Action, search_results: Dict[str, Any]) -> float:
        """获取动作的价值估计"""
        # 从搜索结果中提取动作的价值
        if "value_estimates" in search_results and action in search_results["value_estimates"]:
            return search_results["value_estimates"][action]

        # 如果没有直接的价值估计，尝试从其他数据中提取
        if "mcts_data" in search_results and "top_actions" in search_results["mcts_data"]:
            for action_data in search_results["mcts_data"]["top_actions"]:
                if action_data.get("action") == action:
                    return action_data.get("value", 0.0)

        # 默认返回0.0
        return 0.0

    def _get_action_visits(self, action: Action, search_results: Dict[str, Any]) -> int:
        """获取动作的访问次数"""
        # 从搜索结果中提取动作的访问次数
        if "visit_counts" in search_results and action in search_results["visit_counts"]:
            return search_results["visit_counts"][action]

        # 如果没有直接的访问次数，尝试从其他数据中提取
        if "mcts_data" in search_results and "top_actions" in search_results["mcts_data"]:
            for action_data in search_results["mcts_data"]["top_actions"]:
                if action_data.get("action") == action:
                    return action_data.get("visit_count", 0)

        # 默认返回0
        return 0

    def _calculate_confidence(self, action_visits: int, alt_visits: int, value_diff: float) -> float:
        """
        计算因果关系的置信度

        基于访问次数和价值差异计算置信度。

        Args:
            action_visits: 选择动作的访问次数
            alt_visits: 备选动作的访问次数
            value_diff: 价值差异

        Returns:
            float: 置信度，范围[0, 1]
        """
        # 如果访问次数太少，置信度低
        if action_visits < 10 or alt_visits < 5:
            return 0.3

        # 计算访问次数比例
        visit_ratio = min(action_visits, alt_visits) / max(action_visits, alt_visits)

        # 计算价值差异的绝对值
        abs_value_diff = abs(value_diff)

        # 基于访问次数比例和价值差异计算置信度
        # 访问次数比例越高，价值差异越大，置信度越高
        confidence = 0.5 * visit_ratio + 0.5 * min(abs_value_diff * 2, 1.0)

        # 确保置信度在[0, 1]范围内
        return max(0.0, min(1.0, confidence))

    def _infer_causal_effect(self, action: Action, alt_action: Action, value_diff: float, state: State) -> str:
        """
        推断因果效应

        基于动作、备选动作和价值差异推断因果效应。

        Args:
            action: 选择的动作
            alt_action: 备选动作
            value_diff: 价值差异
            state: 当前游戏状态

        Returns:
            str: 因果效应描述
        """
        # 默认效应描述
        effect = "选择此动作可能导致更好的结果"

        # 如果是斗地主游戏状态和动作，进行特定分析
        if isinstance(state, DouDizhuState) and isinstance(action, CardGroup) and isinstance(alt_action, CardGroup):
            # 分析牌型差异
            if action.card_type != alt_action.card_type:
                if value_diff > 0:
                    effect = f"选择{action.card_type.name}牌型而不是{alt_action.card_type.name}牌型可能更有利"
                else:
                    effect = f"选择{action.card_type.name}牌型而不是{alt_action.card_type.name}可能不太理想"
            else:
                # 相同牌型，分析牌值差异
                if value_diff > 0:
                    effect = f"选择更高牌值的{action.card_type.name}可能更有利"
                else:
                    effect = f"保留更高牌值可能更为明智"

        # 根据价值差异调整描述
        if abs(value_diff) > 0.5:
            effect += "，差异显著"

        return effect

    def _generate_summary(self, analysis: Dict[str, Any]) -> str:
        """
        生成分析摘要

        Args:
            analysis: 分析结果

        Returns:
            str: 分析摘要
        """
        # 如果没有因果效应，返回默认摘要
        if not analysis["causal_effects"]:
            return "没有发现显著的因果关系"

        # 获取置信度最高的因果效应
        top_effect = max(analysis["causal_effects"], key=lambda x: x["confidence"])

        # 生成摘要
        summary = f"分析结果（置信度：{analysis['confidence']:.2f}）：{top_effect['effect']}"

        # 如果有多个因果效应，添加数量信息
        if len(analysis["causal_effects"]) > 1:
            summary += f"，共发现{len(analysis['causal_effects'])}个潜在因果关系"

        return summary

    def _get_current_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def _extract_action_features(self, action: Action, search_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取动作的特征

        Args:
            action: 动作
            search_results: 搜索结果

        Returns:
            Dict[str, Any]: 动作特征
        """
        features = {}

        # 从搜索结果中提取动作特征
        if "mcts_data" in search_results and "top_actions" in search_results["mcts_data"]:
            for action_data in search_results["mcts_data"]["top_actions"]:
                if action_data.get("action") == action:
                    # 提取基本特征
                    features["value"] = action_data.get("value", 0.0)
                    features["visit_count"] = action_data.get("visit_count", 0)
                    features["prior"] = action_data.get("prior", 0.0)

                    # 提取高级特征（如果有）
                    if "ucb_score" in action_data:
                        features["ucb_score"] = action_data["ucb_score"]
                    if "q_value" in action_data:
                        features["q_value"] = action_data["q_value"]
                    if "reward" in action_data:
                        features["reward"] = action_data["reward"]

                    # 提取特殊特征
                    for key in ["belief_confidence", "info_value", "risk_value", "intrinsic_bonus"]:
                        if key in action_data:
                            features[key] = action_data[key]

                    break

        # 如果是斗地主游戏动作，提取牌型特征
        if isinstance(action, CardGroup):
            features["card_type"] = action.card_type.name
            features["card_count"] = len(action.cards)
            features["main_value"] = action.main_value

        return features

    def _analyze_feature_differences(self, action_features: Dict[str, Any], alt_features: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析特征差异

        Args:
            action_features: 选择动作的特征
            alt_features: 备选动作的特征

        Returns:
            Dict[str, Any]: 特征差异分析
        """
        diff_analysis = {
            "key_differences": [],
            "similarity_score": 0.0
        }

        # 计算数值特征的差异
        numeric_diffs = {}
        for key in ["value", "prior", "ucb_score", "q_value", "reward"]:
            if key in action_features and key in alt_features:
                diff = action_features[key] - alt_features[key]
                if abs(diff) > 0.05:  # 只关注显著差异
                    numeric_diffs[key] = diff

        # 添加关键差异
        for key, diff in numeric_diffs.items():
            diff_analysis["key_differences"].append({
                "feature": key,
                "difference": diff,
                "percentage_diff": diff / max(0.001, abs(alt_features[key])) if key in alt_features else 0.0
            })

        # 分析类别特征差异
        for key in ["card_type"]:
            if key in action_features and key in alt_features and action_features[key] != alt_features[key]:
                diff_analysis["key_differences"].append({
                    "feature": key,
                    "action_value": action_features[key],
                    "alt_value": alt_features[key],
                    "is_categorical": True
                })

        # 计算相似度分数
        common_keys = set(action_features.keys()) & set(alt_features.keys())
        if common_keys:
            similarity_count = sum(1 for k in common_keys if isinstance(action_features[k], (int, float)) and
                                  isinstance(alt_features[k], (int, float)) and
                                  abs(action_features[k] - alt_features[k]) < 0.1 * max(0.001, abs(alt_features[k])))
            diff_analysis["similarity_score"] = similarity_count / len(common_keys)

        return diff_analysis

    def _perform_counterfactual_analysis(self, state: State, action: Action,
                                        alternative_actions: List[Action],
                                        search_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行反事实分析

        分析如果选择了不同的动作，游戏可能会如何发展。

        Args:
            state: 当前游戏状态
            action: 选择的动作
            alternative_actions: 备选动作列表
            search_results: 搜索结果

        Returns:
            Dict[str, Any]: 反事实分析结果
        """
        # 初始化反事实分析结果
        counterfactual_analysis = {
            "scenarios": [],
            "summary": ""
        }

        # 获取主要变化路径（如果有）
        principal_variation = []
        if "mcts_data" in search_results and "principal_variation" in search_results["mcts_data"]:
            principal_variation = search_results["mcts_data"]["principal_variation"]

        # 选择最有价值的备选动作进行反事实分析
        top_alternatives = []
        for alt_action in alternative_actions:
            if alt_action == action:
                continue

            alt_value = self._get_action_value(alt_action, search_results)
            alt_visits = self._get_action_visits(alt_action, search_results)

            if alt_visits > 0:
                top_alternatives.append((alt_action, alt_value, alt_visits))

        # 按价值排序，选择前2个备选动作
        top_alternatives.sort(key=lambda x: x[1], reverse=True)
        top_alternatives = top_alternatives[:2]

        # 为每个备选动作创建反事实场景
        for alt_action, alt_value, alt_visits in top_alternatives:
            # 创建反事实场景
            scenario = {
                "alternative_action": str(alt_action),
                "expected_value": float(alt_value),
                "confidence": min(1.0, alt_visits / 50.0),  # 基于访问次数的置信度
                "potential_outcome": self._generate_counterfactual_outcome(state, action, alt_action, alt_value)
            }

            counterfactual_analysis["scenarios"].append(scenario)

        # 生成反事实分析摘要
        if counterfactual_analysis["scenarios"]:
            best_scenario = max(counterfactual_analysis["scenarios"], key=lambda x: x["expected_value"])
            worst_scenario = min(counterfactual_analysis["scenarios"], key=lambda x: x["expected_value"])

            if best_scenario["expected_value"] > self._get_action_value(action, search_results):
                counterfactual_analysis["summary"] = f"选择{best_scenario['alternative_action']}可能会带来更好的结果：{best_scenario['potential_outcome']}"
            else:
                counterfactual_analysis["summary"] = f"当前选择可能是最优的，选择{worst_scenario['alternative_action']}可能会导致：{worst_scenario['potential_outcome']}"

        return counterfactual_analysis

    def _generate_counterfactual_outcome(self, state: State, action: Action,
                                         alt_action: Action, alt_value: float) -> str:
        """
        生成反事实结果描述

        Args:
            state: 当前游戏状态
            action: 选择的动作
            alt_action: 备选动作
            alt_value: 备选动作的价值

        Returns:
            str: 反事实结果描述
        """
        # 基于价值差异生成结果描述
        value_diff = alt_value - self._get_action_value(action, {})

        if isinstance(state, DouDizhuState) and isinstance(action, CardGroup) and isinstance(alt_action, CardGroup):
            if value_diff > 0.3:
                return f"使用{alt_action.card_type.name}牌型可能会显著提高获胜机会"
            elif value_diff > 0.1:
                return f"使用{alt_action.card_type.name}牌型可能会略微提高获胜机会"
            elif value_diff > -0.1:
                return f"使用{alt_action.card_type.name}牌型可能会有类似的结果"
            elif value_diff > -0.3:
                return f"使用{alt_action.card_type.name}牌型可能会略微降低获胜机会"
            else:
                return f"使用{alt_action.card_type.name}牌型可能会显著降低获胜机会"
        else:
            if value_diff > 0.3:
                return "可能会显著提高获胜机会"
            elif value_diff > 0.1:
                return "可能会略微提高获胜机会"
            elif value_diff > -0.1:
                return "可能会有类似的结果"
            elif value_diff > -0.3:
                return "可能会略微降低获胜机会"
            else:
                return "可能会显著降低获胜机会"

    def _perform_intervention_analysis(self, state: State, action: Action,
                                      search_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行干预分析

        分析游戏状态中的关键因素如何影响决策。

        Args:
            state: 当前游戏状态
            action: 选择的动作
            search_results: 搜索结果

        Returns:
            Dict[str, Any]: 干预分析结果
        """
        # 初始化干预分析结果
        intervention_analysis = {
            "key_factors": [],
            "summary": ""
        }

        # 如果是斗地主游戏状态，分析关键因素
        if isinstance(state, DouDizhuState):
            # 分析手牌数量
            hand_cards_count = len(state.current_hand)
            if hand_cards_count < 5:
                intervention_analysis["key_factors"].append({
                    "factor": "hand_cards_count",
                    "value": hand_cards_count,
                    "impact": "high" if hand_cards_count < 3 else "medium",
                    "description": "手牌数量较少，优先考虑出牌"
                })

            # 分析是否是地主
            is_landlord = state.current_player == state.landlord_player
            intervention_analysis["key_factors"].append({
                "factor": "is_landlord",
                "value": is_landlord,
                "impact": "medium",
                "description": "作为地主，策略更加进取" if is_landlord else "作为农民，策略更加保守"
            })

            # 分析牌型分布
            if hasattr(state, "card_type_distribution"):
                card_distribution = state.card_type_distribution
                intervention_analysis["key_factors"].append({
                    "factor": "card_distribution",
                    "value": str(card_distribution),
                    "impact": "medium",
                    "description": "牌型分布影响出牌策略"
                })

        # 生成干预分析摘要
        if intervention_analysis["key_factors"]:
            high_impact_factors = [f for f in intervention_analysis["key_factors"] if f["impact"] == "high"]
            if high_impact_factors:
                factor = high_impact_factors[0]
                intervention_analysis["summary"] = f"{factor['factor']}是影响决策的关键因素：{factor['description']}"
            else:
                intervention_analysis["summary"] = "多个因素共同影响了决策，没有单一决定性因素"

        return intervention_analysis

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        return self.stats
