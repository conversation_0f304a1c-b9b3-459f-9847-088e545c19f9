"""
残局库管理系统

管理和存储斗地主游戏中的典型残局模式，提供残局检索和更新功能。
"""

import os
import json
import logging
import uuid
from typing import Dict, List, Optional, Tuple, Any
from enum import Enum

from cardgame_ai.algorithms.endgame_types import EndgameType


class EndgamePattern:
    """残局模式类"""

    def __init__(
        self,
        pattern_id: str,
        name: str,
        endgame_type: EndgameType,
        description: str,
        features: Dict[str, Any],
        solution: Dict[str, Any],
        win_rate: float = 0.0,
        usage_count: int = 0
    ):
        """
        初始化残局模式

        Args:
            pattern_id: 模式ID
            name: 模式名称
            endgame_type: 残局类型
            description: 描述
            features: 特征（用于匹配）
            solution: 解决方案
            win_rate: 胜率
            usage_count: 使用次数
        """
        self.pattern_id = pattern_id
        self.name = name
        self.endgame_type = endgame_type
        self.description = description
        self.features = features
        self.solution = solution
        self.win_rate = win_rate
        self.usage_count = usage_count

    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典

        Returns:
            Dict: 字典表示
        """
        return {
            "pattern_id": self.pattern_id,
            "name": self.name,
            "endgame_type": self.endgame_type.name,
            "description": self.description,
            "features": self.features,
            "solution": self.solution,
            "win_rate": self.win_rate,
            "usage_count": self.usage_count
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EndgamePattern':
        """
        从字典创建

        Args:
            data: 字典数据

        Returns:
            EndgamePattern: 残局模式对象
        """
        return cls(
            pattern_id=data["pattern_id"],
            name=data["name"],
            endgame_type=EndgameType[data["endgame_type"]],
            description=data["description"],
            features=data["features"],
            solution=data["solution"],
            win_rate=data.get("win_rate", 0.0),
            usage_count=data.get("usage_count", 0)
        )


class EndgameLibrary:
    """残局库管理类"""

    def __init__(self, library_path: str = None):
        """
        初始化残局库

        Args:
            library_path: 残局库文件路径，默认为None（使用默认路径）
        """
        self.patterns: Dict[str, EndgamePattern] = {}
        self.library_path = library_path or os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            "data",
            "endgame_patterns.json"
        )
        self.load_library()

    def load_library(self) -> bool:
        """
        加载残局库

        Returns:
            bool: 是否成功加载
        """
        if not os.path.exists(self.library_path):
            logging.info(f"残局库文件不存在，将创建新库: {self.library_path}")
            self._initialize_default_patterns()
            self.save_library()
            return True

        try:
            with open(self.library_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            self.patterns = {}
            for pattern_data in data:
                pattern = EndgamePattern.from_dict(pattern_data)
                self.patterns[pattern.pattern_id] = pattern

            logging.info(f"成功加载残局库，共 {len(self.patterns)} 个模式")
            return True
        except Exception as e:
            logging.error(f"加载残局库失败: {e}")
            return False

    def save_library(self) -> bool:
        """
        保存残局库

        Returns:
            bool: 是否成功保存
        """
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.library_path), exist_ok=True)

            # 将模式转换为字典列表
            data = [pattern.to_dict() for pattern in self.patterns.values()]

            # 保存到文件
            with open(self.library_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=4)

            logging.info(f"成功保存残局库，共 {len(self.patterns)} 个模式")
            return True
        except Exception as e:
            logging.error(f"保存残局库失败: {e}")
            return False

    def add_pattern(self, pattern: EndgamePattern) -> bool:
        """
        添加残局模式

        Args:
            pattern: 残局模式

        Returns:
            bool: 是否成功添加
        """
        if pattern.pattern_id in self.patterns:
            logging.warning(f"残局模式已存在: {pattern.pattern_id}")
            return False

        self.patterns[pattern.pattern_id] = pattern
        self.save_library()
        return True

    def update_pattern(self, pattern: EndgamePattern) -> bool:
        """
        更新残局模式

        Args:
            pattern: 残局模式

        Returns:
            bool: 是否成功更新
        """
        if pattern.pattern_id not in self.patterns:
            logging.warning(f"残局模式不存在: {pattern.pattern_id}")
            return False

        self.patterns[pattern.pattern_id] = pattern
        self.save_library()
        return True

    def remove_pattern(self, pattern_id: str) -> bool:
        """
        删除残局模式

        Args:
            pattern_id: 模式ID

        Returns:
            bool: 是否成功删除
        """
        if pattern_id not in self.patterns:
            logging.warning(f"残局模式不存在: {pattern_id}")
            return False

        del self.patterns[pattern_id]
        self.save_library()
        return True

    def get_pattern(self, pattern_id: str) -> Optional[EndgamePattern]:
        """
        获取残局模式

        Args:
            pattern_id: 模式ID

        Returns:
            Optional[EndgamePattern]: 残局模式，如果不存在则返回None
        """
        return self.patterns.get(pattern_id)

    def get_patterns_by_type(self, endgame_type: EndgameType) -> List[EndgamePattern]:
        """
        按类型获取残局模式

        Args:
            endgame_type: 残局类型

        Returns:
            List[EndgamePattern]: 残局模式列表
        """
        return [pattern for pattern in self.patterns.values() if pattern.endgame_type == endgame_type]

    def _initialize_default_patterns(self):
        """初始化默认残局模式"""
        # 王炸残局
        self.patterns[str(uuid.uuid4())] = EndgamePattern(
            pattern_id=str(uuid.uuid4()),
            name="王炸残局",
            endgame_type=EndgameType.KING_BOMB,
            description="手中有大小王，且游戏处于残局状态",
            features={
                "has_small_joker": True,
                "has_big_joker": True,
                "max_hand_cards": 8
            },
            solution={
                "strategy": "保留王炸，直到关键时刻使用",
                "priority": "high"
            }
        )

        # 单张控制残局
        self.patterns[str(uuid.uuid4())] = EndgamePattern(
            pattern_id=str(uuid.uuid4()),
            name="单张控制残局",
            endgame_type=EndgameType.SINGLE_CARD_CONTROL,
            description="手中主要是单张牌，且牌的数量较少",
            features={
                "single_card_ratio": 0.6,
                "max_hand_cards": 5
            },
            solution={
                "strategy": "从最小的单张开始出牌",
                "priority": "medium"
            }
        )

        # 炸弹残局
        self.patterns[str(uuid.uuid4())] = EndgamePattern(
            pattern_id=str(uuid.uuid4()),
            name="炸弹残局",
            endgame_type=EndgameType.BOMB_ENDGAME,
            description="手中有炸弹，且其他玩家手牌较少",
            features={
                "has_bomb": True,
                "opponent_max_cards": 6
            },
            solution={
                "strategy": "保留炸弹，直到关键时刻使用",
                "priority": "high"
            }
        )
