#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
训练监控模块

提供实时的训练监控和可视化功能，包括：
- TensorBoard集成
- 实时指标记录
- 系统资源监控
- 训练进度跟踪
"""

import torch
import psutil
import logging
import time
from typing import Dict, Any, Optional
from pathlib import Path

try:
    from torch.utils.tensorboard import SummaryWriter
    TENSORBOARD_AVAILABLE = True
except ImportError:
    TENSORBOARD_AVAILABLE = False
    SummaryWriter = None

try:
    import wandb
    WANDB_AVAILABLE = True
except ImportError:
    WANDB_AVAILABLE = False
    wandb = None

logger = logging.getLogger(__name__)


class TrainingMonitor:
    """训练监控器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化训练监控器
        
        Args:
            config: 监控配置
        """
        self.config = config
        self.enabled = config.get('enabled', True)
        
        # 初始化TensorBoard
        self.tensorboard_writer = None
        if self.enabled and config.get('tensorboard', {}).get('enabled', True):
            self._setup_tensorboard()
        
        # 初始化Weights & Biases
        self.wandb_enabled = False
        if self.enabled and config.get('wandb', {}).get('enabled', False):
            self._setup_wandb()
        
        # 系统监控
        self.system_monitoring = config.get('system', {}).get('enabled', True)
        self.monitor_interval = config.get('system', {}).get('interval', 10)
        
        logger.info(f"训练监控器初始化完成，TensorBoard: {self.tensorboard_writer is not None}")
    
    def _setup_tensorboard(self):
        """设置TensorBoard"""
        if not TENSORBOARD_AVAILABLE:
            logger.warning("TensorBoard不可用，跳过TensorBoard设置")
            return
        
        log_dir = self.config.get('tensorboard', {}).get('log_dir', 'logs/tensorboard')
        log_path = Path(log_dir)
        log_path.mkdir(parents=True, exist_ok=True)
        
        self.tensorboard_writer = SummaryWriter(log_dir=str(log_path))
        logger.info(f"TensorBoard日志目录: {log_path}")
    
    def _setup_wandb(self):
        """设置Weights & Biases"""
        if not WANDB_AVAILABLE:
            logger.warning("Weights & Biases不可用，跳过wandb设置")
            return
        
        wandb_config = self.config.get('wandb', {})
        
        try:
            wandb.init(
                project=wandb_config.get('project', 'doudizhu-ai'),
                entity=wandb_config.get('entity'),
                tags=wandb_config.get('tags', []),
                notes=wandb_config.get('notes', '')
            )
            self.wandb_enabled = True
            logger.info("Weights & Biases初始化成功")
        except Exception as e:
            logger.warning(f"Weights & Biases初始化失败: {e}")
    
    def log_metrics(
        self,
        metrics: Dict[str, float],
        step: int,
        prefix: str = ""
    ):
        """
        记录训练指标
        
        Args:
            metrics: 指标字典
            step: 训练步数
            prefix: 指标前缀
        """
        if not self.enabled:
            return
        
        # 添加前缀
        if prefix:
            prefixed_metrics = {f"{prefix}/{key}": value for key, value in metrics.items()}
        else:
            prefixed_metrics = metrics
        
        # 记录到TensorBoard
        if self.tensorboard_writer:
            for key, value in prefixed_metrics.items():
                self.tensorboard_writer.add_scalar(key, value, step)
            self.tensorboard_writer.flush()
        
        # 记录到Weights & Biases
        if self.wandb_enabled:
            wandb.log(prefixed_metrics, step=step)
        
        # 记录到日志
        metrics_str = ", ".join([f"{key}: {value:.4f}" for key, value in metrics.items()])
        logger.info(f"Step {step} - {prefix} - {metrics_str}")
    
    def log_system_metrics(self, step: int):
        """
        记录系统指标
        
        Args:
            step: 训练步数
        """
        if not self.enabled or not self.system_monitoring:
            return
        
        system_metrics = {}
        
        # CPU使用率
        system_metrics['cpu_usage'] = psutil.cpu_percent()
        
        # 内存使用率
        memory = psutil.virtual_memory()
        system_metrics['memory_usage'] = memory.percent
        system_metrics['memory_available_gb'] = memory.available / (1024**3)
        
        # GPU指标
        if torch.cuda.is_available():
            for i in range(torch.cuda.device_count()):
                # GPU内存使用
                memory_allocated = torch.cuda.memory_allocated(i) / (1024**3)
                memory_reserved = torch.cuda.memory_reserved(i) / (1024**3)
                
                system_metrics[f'gpu_{i}_memory_allocated_gb'] = memory_allocated
                system_metrics[f'gpu_{i}_memory_reserved_gb'] = memory_reserved
                
                # GPU利用率（如果可用）
                try:
                    import pynvml
                    pynvml.nvmlInit()
                    handle = pynvml.nvmlDeviceGetHandleByIndex(i)
                    utilization = pynvml.nvmlDeviceGetUtilizationRates(handle)
                    system_metrics[f'gpu_{i}_utilization'] = utilization.gpu
                except ImportError:
                    pass  # pynvml不可用
        
        # 记录系统指标
        self.log_metrics(system_metrics, step, prefix="system")
    
    def log_model_info(self, model: torch.nn.Module, step: int):
        """
        记录模型信息
        
        Args:
            model: 模型
            step: 训练步数
        """
        if not self.enabled:
            return
        
        model_metrics = {}
        
        # 模型参数数量
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        model_metrics['total_parameters'] = total_params
        model_metrics['trainable_parameters'] = trainable_params
        
        # 梯度统计
        if hasattr(model, 'parameters'):
            grad_norms = []
            for param in model.parameters():
                if param.grad is not None:
                    grad_norms.append(param.grad.norm().item())
            
            if grad_norms:
                model_metrics['gradient_norm_mean'] = sum(grad_norms) / len(grad_norms)
                model_metrics['gradient_norm_max'] = max(grad_norms)
        
        # 记录模型指标
        self.log_metrics(model_metrics, step, prefix="model")
    
    def close(self):
        """关闭监控器"""
        if self.tensorboard_writer:
            self.tensorboard_writer.close()
            logger.info("TensorBoard writer已关闭")
        
        if self.wandb_enabled:
            wandb.finish()
            logger.info("Weights & Biases已关闭")


def create_training_monitor(config: Dict[str, Any]) -> TrainingMonitor:
    """
    创建训练监控器的工厂函数
    
    Args:
        config: 监控配置
        
    Returns:
        TrainingMonitor: 监控器实例
    """
    return TrainingMonitor(config)
