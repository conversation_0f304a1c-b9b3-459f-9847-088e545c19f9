#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
训练视图

提供训练相关的UI和功能。
"""

import logging
from typing import Dict, Any

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
    QTabWidget, QLabel, QScrollArea
)
from PySide6.QtCore import Qt, Slot
from PySide6.QtGui import QFont

from cardgame_ai.desktop.views.game_selector import GameSelector
from cardgame_ai.desktop.views.parameter_panel import ParameterPanel
from cardgame_ai.desktop.views.training_control_panel import TrainingControlPanel
from cardgame_ai.desktop.views.training_monitor import TrainingMonitor

logger = logging.getLogger(__name__)


class TrainingView(QWidget):
    """训练视图类"""

    def __init__(self, config, parent=None):
        """
        初始化训练视图

        Args:
            config: 客户端配置
            parent: 父部件
        """
        super().__init__(parent)

        # 保存配置
        self.config = config

        # 设置对象名称
        self.setObjectName("trainingView")

        # 初始化UI
        self.setup_ui()

        logger.info("训练视图初始化完成")

    def setup_ui(self):
        """设置UI布局"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # 创建游戏选择区（顶部）
        game_selection_widget = self.create_game_selection_widget()
        main_layout.addWidget(game_selection_widget)

        # 创建标签页导航（二级导航栏）
        self.tab_widget = QTabWidget()
        self.tab_widget.setDocumentMode(True)
        main_layout.addWidget(self.tab_widget)

        # 创建"训练"标签页
        training_tab = QWidget()
        self.create_training_tab_content(training_tab)
        self.tab_widget.addTab(training_tab, "训练")

        # 创建"评估"标签页
        evaluation_tab = QWidget()
        self.create_empty_tab_content(evaluation_tab, "评估功能即将推出")
        self.tab_widget.addTab(evaluation_tab, "评估")

        # 创建"模型分析"标签页
        analysis_tab = QWidget()
        self.create_empty_tab_content(analysis_tab, "模型分析功能即将推出")
        self.tab_widget.addTab(analysis_tab, "模型分析")

        # 创建"可视化"标签页
        visualization_tab = QWidget()
        self.create_empty_tab_content(visualization_tab, "可视化功能即将推出")
        self.tab_widget.addTab(visualization_tab, "可视化")

        logger.info("训练视图UI布局设置完成")

    def create_training_tab_content(self, tab_widget):
        """
        创建训练标签页内容

        Args:
            tab_widget: 标签页部件
        """
        # 创建标签页布局
        tab_layout = QVBoxLayout(tab_widget)
        tab_layout.setContentsMargins(0, 10, 0, 0)
        tab_layout.setSpacing(10)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QScrollArea.NoFrame)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # 创建滚动区域内容部件
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setContentsMargins(5, 5, 5, 5)
        scroll_layout.setSpacing(10)

        # 创建中央区域
        central_widget = QWidget()
        central_layout = QHBoxLayout(central_widget)
        central_layout.setContentsMargins(0, 0, 0, 0)
        central_layout.setSpacing(10)

        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)

        # 创建左侧面板（参数配置区和模型选择区）
        left_panel = self.create_left_panel()
        splitter.addWidget(left_panel)

        # 创建中央区域（训练监控区）
        monitor_widget = self.create_training_monitor_widget()
        splitter.addWidget(monitor_widget)

        # 设置分割器初始大小
        splitter.setSizes([300, 700])

        # 添加分割器到中央布局
        central_layout.addWidget(splitter)

        # 添加中央区域到滚动布局
        scroll_layout.addWidget(central_widget)

        # 设置滚动区域的内容部件
        scroll_area.setWidget(scroll_content)

        # 添加滚动区域到标签页布局
        tab_layout.addWidget(scroll_area)

        # 添加更多间距，使底部控制区下移
        tab_layout.addSpacing(30)

        # 创建训练控制区（底部）- 放在滚动区域外面
        control_widget = self.create_training_control_widget()
        tab_layout.addWidget(control_widget)

    def create_empty_tab_content(self, tab_widget, message):
        """
        创建空白标签页内容

        Args:
            tab_widget: 标签页部件
            message: 显示消息
        """
        # 创建标签页布局
        tab_layout = QVBoxLayout(tab_widget)
        tab_layout.setContentsMargins(20, 20, 20, 20)

        # 创建提示标签
        label = QLabel(message)
        label.setAlignment(Qt.AlignCenter)
        label.setFont(QFont("Microsoft YaHei", 14))
        label.setStyleSheet("color: #888888;")

        # 添加标签到布局
        tab_layout.addWidget(label)

    def create_game_selection_widget(self):
        """
        创建游戏选择区

        Returns:
            QWidget: 游戏选择区部件
        """
        # 创建游戏选择组件
        self.game_selector = GameSelector(self.config, self)
        self.game_selector.setMinimumHeight(60)
        self.game_selector.setMaximumHeight(80)

        # 连接游戏变化信号
        self.game_selector.game_changed.connect(self.on_game_changed)

        return self.game_selector

    def create_left_panel(self):
        """
        创建左侧面板（参数配置区和模型选择区）

        Returns:
            QWidget: 左侧面板部件
        """
        # 创建参数配置组件
        self.parameter_panel = ParameterPanel(self.config, self)
        self.parameter_panel.setMinimumWidth(250)

        # 连接参数变化信号
        self.parameter_panel.parameter_changed.connect(self.on_parameter_changed)

        return self.parameter_panel

    def create_training_monitor_widget(self):
        """
        创建训练监控区

        Returns:
            QWidget: 训练监控区部件
        """
        # 创建训练监控组件
        self.training_monitor = TrainingMonitor(self.config, self)

        return self.training_monitor

    def create_training_control_widget(self):
        """
        创建训练控制区

        Returns:
            QWidget: 训练控制区部件
        """
        # 创建训练控制组件
        self.training_control = TrainingControlPanel(self.config, self)
        self.training_control.setMinimumHeight(180)  # 增加最小高度
        # 增加最大高度限制，给按钮更多空间
        self.training_control.setMaximumHeight(250)  # 增加最大高度

        # 连接训练控制信号
        self.training_control.training_started.connect(self.on_training_started)
        self.training_control.training_paused.connect(self.on_training_paused)
        self.training_control.training_resumed.connect(self.on_training_resumed)
        self.training_control.training_stopped.connect(self.on_training_stopped)

        return self.training_control

    @Slot(str)
    def on_game_changed(self, game_id):
        """
        游戏变化处理

        Args:
            game_id (str): 游戏ID
        """
        # 获取游戏信息
        game_info = self.game_selector.get_current_game_info()

        if game_info:
            logger.info(f"选择游戏：{game_info['name']}（{game_id}）")

            # 更新参数配置
            self.parameter_panel.set_game(game_id, game_info)
        else:
            logger.warning(f"游戏信息不存在：{game_id}")

    @Slot(str, object)
    def on_parameter_changed(self, param_name, param_value):
        """
        参数变化处理

        Args:
            param_name (str): 参数名称
            param_value: 参数值
        """
        logger.info(f"参数变化：{param_name} = {param_value}")

    @Slot(object)
    def on_training_started(self, _):
        """
        训练开始处理

        Args:
            _ (object): 训练参数（未使用）
        """
        logger.info("训练开始")

        # 获取训练参数
        training_params = self.parameter_panel.get_parameters()

        # 设置训练参数
        self.training_control.set_training_params(training_params)

        # 获取完整的训练参数（包括模型路径和自动保存选项）
        complete_params = self.training_control.get_training_params()

        # 记录训练参数
        model_path = complete_params.get("model_path", "")
        auto_save = complete_params.get("auto_save", True)

        if model_path:
            logger.info(f"继续训练模型: {model_path}")

        logger.info(f"自动保存模型: {'启用' if auto_save else '禁用'}")

        # 模拟训练过程
        self.simulate_training()

    @Slot()
    def on_training_paused(self):
        """训练暂停处理"""
        logger.info("训练暂停")

    @Slot()
    def on_training_resumed(self):
        """训练恢复处理"""
        logger.info("训练恢复")

    @Slot()
    def on_training_stopped(self):
        """训练停止处理"""
        logger.info("训练停止")

    def simulate_training(self):
        """模拟训练过程（仅用于演示）"""
        import random
        import threading
        import time
        import os

        def training_thread():
            # 获取完整的训练参数
            training_params = self.training_control.get_training_params()
            epochs = int(training_params.get("epochs", 100))
            model_path = training_params.get("model_path", "")
            auto_save = training_params.get("auto_save", True)

            # 如果有模型路径，模拟加载模型
            if model_path and os.path.exists(model_path):
                logger.info(f"模拟加载模型: {model_path}")
                # 这里只是模拟，实际应用中应该加载模型
                time.sleep(1)  # 模拟加载时间

            # 模拟训练过程
            for epoch in range(1, epochs + 1):
                # 检查训练是否停止
                if self.training_control.status != self.training_control.TrainingStatus.RUNNING:
                    break

                # 模拟训练数据
                loss = random.uniform(0.1, 1.0) * (1 - epoch / epochs)
                reward = random.uniform(0, 1.0) * (epoch / epochs)
                win_rate = random.uniform(0, 100) * (epoch / epochs)

                # 更新训练监控
                self.training_monitor.update_data(epoch, loss, reward, win_rate, epochs)

                # 更新训练进度
                self.training_control.set_progress(epoch, epochs)

                # 如果启用了自动保存，每10轮保存一次模型
                if auto_save and epoch % 10 == 0:
                    # 获取模型保存路径
                    save_dir = self.config.get("paths.models", "models")
                    save_path = os.path.join(save_dir, f"model_epoch_{epoch}.pt")

                    # 模拟保存模型
                    logger.info(f"模拟自动保存模型: {save_path}")
                    # 这里只是模拟，实际应用中应该保存模型

                # 暂停一下，模拟训练过程
                time.sleep(0.1)

        # 创建训练线程
        thread = threading.Thread(target=training_thread)
        thread.daemon = True
        thread.start()
