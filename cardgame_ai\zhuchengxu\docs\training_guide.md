# 斗地主AI训练指南

## 📋 概述

本指南介绍如何使用重构后的斗地主AI训练系统。项目已经过全面重构，提供了清晰的结构和统一的接口。

## 🚀 快速开始

### 方式1：主训练脚本（推荐）
```bash
# 基础训练
python main_training.py

# 指定GPU设备
python main_training.py --device cuda:0

# 恢复训练
python main_training.py --resume

# 自定义配置
python main_training.py --config configs/training/optimized.yaml
```

### 方式2：快速启动脚本
```bash
# IDE中直接运行
python quick_start.py
```

### 方式3：兼容性脚本
```bash
# 使用legacy脚本
python legacy/run_efficient_zero_training.py
```

## 📁 项目结构

```
cardgame_ai/zhuchengxu/
├── main_training.py          # 🎯 主训练入口
├── quick_start.py           # ⚡ 快速测试脚本
├── auto_deploy.py           # 🤖 自动部署脚本
├── legacy/                  # 📦 兼容性脚本
│   └── run_efficient_zero_training.py
├── auto_config/             # 🔧 自动配置管理模块
│   ├── __init__.py
│   ├── hardware_detector.py # 硬件检测
│   ├── parameter_tuner.py   # 参数调优
│   ├── template_engine.py   # 模板引擎
│   ├── deployment_manager.py # 部署管理
│   └── utils.py            # 工具函数
├── templates/               # 📄 配置模板
│   ├── auto_config_template.yaml
│   ├── hardware_profiles/
│   └── algorithm_profiles/
├── docs/                    # 📚 项目文档
│   └── training_guide.md    # 本文档
├── checkpoints/             # 💾 模型检查点
├── logs/                    # 📊 训练日志
└── models/                  # 🤖 训练模型

configs/
├── base.yaml               # 🔧 基础配置
├── training/               # 📁 训练配置
│   ├── efficient_zero.yaml
│   └── optimized.yaml
├── algorithms/             # 📁 算法配置
│   └── efficient_zero/
├── environments/           # 📁 环境配置
│   └── doudizhu/
└── hardware/              # 📁 硬件配置
    ├── single_gpu.yaml
    └── multi_gpu.yaml
```

## ⚙️ 配置系统

### 配置文件层次
1. **base.yaml** - 项目基础配置
2. **training/** - 训练相关配置
3. **algorithms/** - 算法特定配置
4. **environments/** - 环境特定配置
5. **hardware/** - 硬件特定配置

### 配置选择
- **单GPU训练**: 使用 `hardware/single_gpu.yaml`
- **多GPU训练**: 使用 `hardware/multi_gpu.yaml`
- **优化训练**: 使用 `training/optimized.yaml`

## 🎯 训练特性

### 核心优化
- ✨ EfficientZero算法优化 (MCTS: 50→100-200次模拟)
- 📦 批次大小优化 (128→256-320)
- 🎯 学习率精细调整
- 🤝 增强多智能体协作
- 📊 实时性能监控
- 🔄 分布式训练支持

### 监控系统
- 实时训练指标
- TensorBoard集成
- 性能报告生成
- 自动检查点保存

## 🔧 故障排除

### 常见问题
1. **GPU内存不足**: 调整batch_size配置
2. **配置文件错误**: 检查YAML语法
3. **依赖缺失**: 安装所需依赖包

### 日志查看
训练日志保存在 `logs/` 目录，包含详细的训练信息和错误诊断。

## 📞 支持

如有问题，请查看：
1. 训练日志文件
2. 配置文件语法
3. 硬件资源状态
