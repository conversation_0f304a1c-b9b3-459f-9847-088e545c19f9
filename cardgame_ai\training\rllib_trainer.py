"""
RLlib训练器模块

使用Ray RLlib框架进行分布式强化学习训练。
"""

import os
import argparse
import logging
import numpy as np
import torch
import ray
from typing import Dict, Any, Optional

from ray import tune
from ray.rllib.algorithms.impala import ImpalaConfig
from ray.rllib.algorithms.ppo import PPOConfig
from ray.rllib.algorithms.apex_dqn import ApexDQNConfig
from ray.rllib.models import ModelCatalog
from ray.tune.registry import register_env

from cardgame_ai.environments.rllib_doudizhu_env import RLlibDouDizhuEnv
from cardgame_ai.algorithms.rllib_adapter import RLlibEfficientZeroModel

# 配置日志
logger = logging.getLogger(__name__)


def register_models_and_envs():
    """
    注册模型和环境
    """
    # 注册环境
    register_env(
        "doudizhu", 
        lambda config: RLlibDouDizhuEnv(config)
    )
    
    # 注册模型
    ModelCatalog.register_custom_model(
        "efficient_zero_model", 
        RLlibEfficientZeroModel
    )


def create_impala_config(args: argparse.Namespace) -> ImpalaConfig:
    """
    创建IMPALA配置
    
    Args:
        args: 命令行参数
        
    Returns:
        ImpalaConfig: IMPALA配置
    """
    config = (
        ImpalaConfig()
        .environment(env="doudizhu")
        .framework("torch")
        .rollouts(
            num_rollout_workers=args.num_workers,
            num_envs_per_worker=args.num_envs_per_worker,
            rollout_fragment_length=args.rollout_fragment_length,
            batch_mode="truncate_episodes"
        )
        .training(
            train_batch_size=args.train_batch_size,
            lr=args.learning_rate,
            gamma=args.gamma,
            lambda_=args.lambda_,
            vf_loss_coeff=args.vf_loss_coeff,
            entropy_coeff=args.entropy_coeff,
            clip_rewards=args.clip_rewards,
            model={
                "custom_model": "efficient_zero_model",
                "custom_model_config": {
                    "hidden_dim": args.hidden_dim,
                    "state_dim": args.state_dim,
                    "use_resnet": args.use_resnet,
                    "projection_dim": args.projection_dim,
                    "prediction_dim": args.prediction_dim,
                    "value_prefix_length": args.value_prefix_length
                }
            }
        )
        .resources(
            num_gpus=args.num_gpus,
            num_gpus_per_worker=args.num_gpus_per_worker
        )
        .fault_tolerance(
            recreate_failed_workers=True,
            restart_failed_sub_environments=True
        )
        .debugging(
            log_level=args.log_level,
            logger_config={
                "type": "ray.tune.logger.TBXLogger",
                "logdir": args.log_dir
            }
        )
    )
    
    return config


def create_ppo_config(args: argparse.Namespace) -> PPOConfig:
    """
    创建PPO配置
    
    Args:
        args: 命令行参数
        
    Returns:
        PPOConfig: PPO配置
    """
    config = (
        PPOConfig()
        .environment(env="doudizhu")
        .framework("torch")
        .rollouts(
            num_rollout_workers=args.num_workers,
            num_envs_per_worker=args.num_envs_per_worker,
            rollout_fragment_length=args.rollout_fragment_length,
            batch_mode="truncate_episodes"
        )
        .training(
            train_batch_size=args.train_batch_size,
            lr=args.learning_rate,
            gamma=args.gamma,
            lambda_=args.lambda_,
            vf_loss_coeff=args.vf_loss_coeff,
            entropy_coeff=args.entropy_coeff,
            clip_param=args.clip_param,
            kl_coeff=args.kl_coeff,
            num_sgd_iter=args.num_sgd_iter,
            sgd_minibatch_size=args.sgd_minibatch_size,
            model={
                "custom_model": "efficient_zero_model",
                "custom_model_config": {
                    "hidden_dim": args.hidden_dim,
                    "state_dim": args.state_dim,
                    "use_resnet": args.use_resnet,
                    "projection_dim": args.projection_dim,
                    "prediction_dim": args.prediction_dim,
                    "value_prefix_length": args.value_prefix_length
                }
            }
        )
        .resources(
            num_gpus=args.num_gpus,
            num_gpus_per_worker=args.num_gpus_per_worker
        )
        .fault_tolerance(
            recreate_failed_workers=True,
            restart_failed_sub_environments=True
        )
        .debugging(
            log_level=args.log_level,
            logger_config={
                "type": "ray.tune.logger.TBXLogger",
                "logdir": args.log_dir
            }
        )
    )
    
    return config


def create_apex_dqn_config(args: argparse.Namespace) -> ApexDQNConfig:
    """
    创建Apex DQN配置
    
    Args:
        args: 命令行参数
        
    Returns:
        ApexDQNConfig: Apex DQN配置
    """
    config = (
        ApexDQNConfig()
        .environment(env="doudizhu")
        .framework("torch")
        .rollouts(
            num_rollout_workers=args.num_workers,
            num_envs_per_worker=args.num_envs_per_worker,
            rollout_fragment_length=args.rollout_fragment_length,
            batch_mode="truncate_episodes"
        )
        .training(
            train_batch_size=args.train_batch_size,
            lr=args.learning_rate,
            gamma=args.gamma,
            target_network_update_freq=args.target_network_update_freq,
            learning_starts=args.learning_starts,
            buffer_size=args.buffer_size,
            prioritized_replay=args.prioritized_replay,
            prioritized_replay_alpha=args.prioritized_replay_alpha,
            prioritized_replay_beta=args.prioritized_replay_beta,
            final_prioritized_replay_beta=args.final_prioritized_replay_beta,
            n_step=args.n_step,
            model={
                "custom_model": "efficient_zero_model",
                "custom_model_config": {
                    "hidden_dim": args.hidden_dim,
                    "state_dim": args.state_dim,
                    "use_resnet": args.use_resnet,
                    "projection_dim": args.projection_dim,
                    "prediction_dim": args.prediction_dim,
                    "value_prefix_length": args.value_prefix_length
                }
            }
        )
        .resources(
            num_gpus=args.num_gpus,
            num_gpus_per_worker=args.num_gpus_per_worker
        )
        .fault_tolerance(
            recreate_failed_workers=True,
            restart_failed_sub_environments=True
        )
        .debugging(
            log_level=args.log_level,
            logger_config={
                "type": "ray.tune.logger.TBXLogger",
                "logdir": args.log_dir
            }
        )
    )
    
    return config


def parse_args():
    """
    解析命令行参数
    
    Returns:
        argparse.Namespace: 命令行参数
    """
    parser = argparse.ArgumentParser(description="RLlib训练器")
    
    # 通用参数
    parser.add_argument("--algorithm", type=str, default="impala", choices=["impala", "ppo", "apex_dqn"], help="训练算法")
    parser.add_argument("--num_iterations", type=int, default=1000, help="训练迭代次数")
    parser.add_argument("--checkpoint_freq", type=int, default=10, help="检查点保存频率")
    parser.add_argument("--checkpoint_dir", type=str, default="checkpoints", help="检查点保存目录")
    parser.add_argument("--log_dir", type=str, default="logs", help="日志保存目录")
    parser.add_argument("--log_level", type=str, default="INFO", help="日志级别")
    
    # 资源参数
    parser.add_argument("--num_workers", type=int, default=4, help="工作进程数量")
    parser.add_argument("--num_envs_per_worker", type=int, default=1, help="每个工作进程的环境数量")
    parser.add_argument("--num_gpus", type=float, default=1.0, help="GPU数量")
    parser.add_argument("--num_gpus_per_worker", type=float, default=0.0, help="每个工作进程的GPU数量")
    
    # 训练参数
    parser.add_argument("--learning_rate", type=float, default=0.0005, help="学习率")
    parser.add_argument("--gamma", type=float, default=0.99, help="折扣因子")
    parser.add_argument("--lambda_", type=float, default=0.95, help="GAE参数")
    parser.add_argument("--vf_loss_coeff", type=float, default=0.5, help="值函数损失系数")
    parser.add_argument("--entropy_coeff", type=float, default=0.01, help="熵正则化系数")
    parser.add_argument("--clip_rewards", type=bool, default=True, help="是否裁剪奖励")
    parser.add_argument("--rollout_fragment_length", type=int, default=50, help="每个工作进程的片段长度")
    parser.add_argument("--train_batch_size", type=int, default=500, help="训练批次大小")
    
    # PPO特定参数
    parser.add_argument("--clip_param", type=float, default=0.3, help="PPO裁剪参数")
    parser.add_argument("--kl_coeff", type=float, default=0.0, help="KL散度系数")
    parser.add_argument("--num_sgd_iter", type=int, default=10, help="SGD迭代次数")
    parser.add_argument("--sgd_minibatch_size", type=int, default=128, help="SGD小批次大小")
    
    # Apex DQN特定参数
    parser.add_argument("--target_network_update_freq", type=int, default=50000, help="目标网络更新频率")
    parser.add_argument("--learning_starts", type=int, default=10000, help="学习开始步数")
    parser.add_argument("--buffer_size", type=int, default=1000000, help="经验回放缓冲区大小")
    parser.add_argument("--prioritized_replay", type=bool, default=True, help="是否使用优先经验回放")
    parser.add_argument("--prioritized_replay_alpha", type=float, default=0.6, help="优先经验回放alpha参数")
    parser.add_argument("--prioritized_replay_beta", type=float, default=0.4, help="优先经验回放beta参数")
    parser.add_argument("--final_prioritized_replay_beta", type=float, default=1.0, help="最终优先经验回放beta参数")
    parser.add_argument("--n_step", type=int, default=3, help="n步TD学习")
    
    # 模型参数
    parser.add_argument("--hidden_dim", type=int, default=256, help="隐藏层维度")
    parser.add_argument("--state_dim", type=int, default=64, help="状态维度")
    parser.add_argument("--use_resnet", type=bool, default=True, help="是否使用ResNet")
    parser.add_argument("--projection_dim", type=int, default=256, help="投影维度")
    parser.add_argument("--prediction_dim", type=int, default=128, help="预测维度")
    parser.add_argument("--value_prefix_length", type=int, default=5, help="值前缀长度")
    
    return parser.parse_args()


def main():
    """
    主函数
    """
    # 解析命令行参数
    args = parse_args()
    
    # 创建检查点和日志目录
    os.makedirs(args.checkpoint_dir, exist_ok=True)
    os.makedirs(args.log_dir, exist_ok=True)
    
    # 初始化Ray
    ray.init()
    
    # 注册模型和环境
    register_models_and_envs()
    
    # 根据算法选择配置
    if args.algorithm == "impala":
        config = create_impala_config(args)
    elif args.algorithm == "ppo":
        config = create_ppo_config(args)
    elif args.algorithm == "apex_dqn":
        config = create_apex_dqn_config(args)
    else:
        raise ValueError(f"不支持的算法: {args.algorithm}")
    
    # 创建训练器
    trainer = config.build()
    
    # 训练循环
    for i in range(args.num_iterations):
        # 训练一个迭代
        result = trainer.train()
        
        # 打印训练结果
        logger.info(f"迭代 {i + 1}/{args.num_iterations}")
        logger.info(f"  训练奖励: {result['episode_reward_mean']}")
        logger.info(f"  训练长度: {result['episode_len_mean']}")
        logger.info(f"  训练时间: {result['time_total_s']}秒")
        
        # 保存检查点
        if (i + 1) % args.checkpoint_freq == 0 or i + 1 == args.num_iterations:
            checkpoint_path = trainer.save(args.checkpoint_dir)
            logger.info(f"  保存检查点: {checkpoint_path}")
    
    # 关闭训练器
    trainer.stop()
    
    # 关闭Ray
    ray.shutdown()


if __name__ == "__main__":
    main()
