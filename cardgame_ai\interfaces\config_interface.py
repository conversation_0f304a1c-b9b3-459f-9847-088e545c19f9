"""
配置管理接口定义

该接口定义了配置管理服务的标准契约，用于解耦zhuchengxu模块与具体配置实现的依赖关系。
通过该接口，zhuchengxu模块可以与任何实现了ConfigInterface的配置服务进行交互。

设计目标:
- 解耦zhuchengxu与UnifiedConfigManager的直接依赖
- 提供标准化的配置管理接口
- 支持多种配置格式和来源
- 实现配置的验证和管理

作者: Architect Timmy
版本: v1.0
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass
from enum import Enum
from pathlib import Path


class ConfigFormat(Enum):
    """配置文件格式枚举"""
    YAML = "yaml"
    JSON = "json"
    TOML = "toml"
    INI = "ini"
    PYTHON = "python"


class ValidationLevel(Enum):
    """验证级别枚举"""
    STRICT = "strict"      # 严格验证，任何错误都失败
    NORMAL = "normal"      # 正常验证，忽略警告
    LOOSE = "loose"        # 宽松验证，只检查关键错误


@dataclass
class ConfigValidationResult:
    """配置验证结果数据类"""
    is_valid: bool                          # 是否有效
    errors: List[str]                       # 错误列表
    warnings: List[str]                     # 警告列表
    missing_keys: List[str]                 # 缺失的键
    invalid_values: Dict[str, str]          # 无效值及原因
    suggestions: List[str]                  # 修复建议


@dataclass
class ConfigMetadata:
    """配置元数据"""
    source: str                             # 配置来源
    format: ConfigFormat                    # 配置格式
    version: str                           # 配置版本
    created_at: str                        # 创建时间
    modified_at: str                       # 修改时间
    checksum: str                          # 配置校验和


class ConfigInterface(ABC):
    """配置管理接口
    
    定义了配置管理服务必须实现的标准方法，用于解耦zhuchengxu模块与具体配置实现。
    
    实现该接口的类必须提供:
    1. 配置加载和保存功能
    2. 配置验证和合并功能
    3. 配置模板和默认值管理
    4. 配置变更监控功能
    
    注意:
        所有方法都必须是线程安全的，支持并发调用。
        配置变更应该通过事件机制通知相关组件。
    """
    
    @abstractmethod
    def load_config(self, source: Union[str, Path, Dict[str, Any]]) -> Dict[str, Any]:
        """加载配置
        
        Args:
            source: 配置源，可以是文件路径、字典或URL
            
        Returns:
            Dict[str, Any]: 加载的配置字典
            
        Raises:
            ConfigLoadError: 配置加载失败
            
        注意:
            支持多种配置源：文件、字典、环境变量、远程配置等。
            应该自动检测配置格式并使用相应的解析器。
        """
        pass
    
    @abstractmethod
    def save_config(self, config: Dict[str, Any], target: Union[str, Path], 
                   format: Optional[ConfigFormat] = None) -> bool:
        """保存配置
        
        Args:
            config: 要保存的配置字典
            target: 保存目标路径
            format: 保存格式，None表示自动检测
            
        Returns:
            bool: 是否成功保存
            
        注意:
            应该保持配置的格式化和注释。
        """
        pass
    
    @abstractmethod
    def validate_config(self, config: Dict[str, Any], 
                       schema: Optional[Dict[str, Any]] = None,
                       level: ValidationLevel = ValidationLevel.NORMAL) -> ConfigValidationResult:
        """验证配置
        
        Args:
            config: 要验证的配置
            schema: 验证模式，None表示使用默认模式
            level: 验证级别
            
        Returns:
            ConfigValidationResult: 验证结果
            
        注意:
            应该提供详细的错误信息和修复建议。
        """
        pass
    
    @abstractmethod
    def merge_configs(self, *configs: Dict[str, Any], 
                     strategy: str = "deep") -> Dict[str, Any]:
        """合并多个配置
        
        Args:
            *configs: 要合并的配置列表
            strategy: 合并策略 ("deep", "shallow", "override")
            
        Returns:
            Dict[str, Any]: 合并后的配置
            
        注意:
            应该处理配置冲突，提供灵活的合并策略。
        """
        pass
    
    @abstractmethod
    def get_default_config(self, config_type: str) -> Dict[str, Any]:
        """获取默认配置
        
        Args:
            config_type: 配置类型 (如: "training", "environment", "algorithm")
            
        Returns:
            Dict[str, Any]: 默认配置
            
        注意:
            应该提供各种场景的默认配置模板。
        """
        pass
    
    @abstractmethod
    def get_config_template(self, template_name: str) -> Dict[str, Any]:
        """获取配置模板
        
        Args:
            template_name: 模板名称
            
        Returns:
            Dict[str, Any]: 配置模板
            
        注意:
            模板应该包含完整的配置结构和说明。
        """
        pass
    
    @abstractmethod
    def list_templates(self) -> List[str]:
        """列出可用的配置模板
        
        Returns:
            List[str]: 模板名称列表
        """
        pass
    
    @abstractmethod
    def get_config_value(self, config: Dict[str, Any], key_path: str, 
                        default: Any = None) -> Any:
        """获取配置值
        
        Args:
            config: 配置字典
            key_path: 键路径，支持点号分隔 (如: "training.batch_size")
            default: 默认值
            
        Returns:
            Any: 配置值
            
        注意:
            应该支持嵌套键访问和类型转换。
        """
        pass
    
    @abstractmethod
    def set_config_value(self, config: Dict[str, Any], key_path: str, 
                        value: Any) -> Dict[str, Any]:
        """设置配置值
        
        Args:
            config: 配置字典
            key_path: 键路径
            value: 要设置的值
            
        Returns:
            Dict[str, Any]: 更新后的配置
            
        注意:
            应该创建必要的嵌套结构。
        """
        pass
    
    @abstractmethod
    def interpolate_config(self, config: Dict[str, Any], 
                          variables: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """插值配置变量
        
        Args:
            config: 包含变量的配置
            variables: 变量字典，None表示使用环境变量
            
        Returns:
            Dict[str, Any]: 插值后的配置
            
        注意:
            应该支持环境变量、配置内引用等多种插值方式。
        """
        pass
    
    @abstractmethod
    def watch_config(self, source: Union[str, Path], 
                    callback: callable) -> bool:
        """监控配置变更
        
        Args:
            source: 要监控的配置源
            callback: 变更回调函数
            
        Returns:
            bool: 是否成功开始监控
            
        注意:
            回调函数应该接收 (old_config, new_config) 参数。
        """
        pass
    
    @abstractmethod
    def stop_watching(self, source: Union[str, Path]) -> bool:
        """停止监控配置
        
        Args:
            source: 要停止监控的配置源
            
        Returns:
            bool: 是否成功停止
        """
        pass
    
    @abstractmethod
    def get_metadata(self, source: Union[str, Path]) -> Optional[ConfigMetadata]:
        """获取配置元数据
        
        Args:
            source: 配置源
            
        Returns:
            Optional[ConfigMetadata]: 配置元数据，不存在返回None
        """
        pass
    
    @abstractmethod
    def backup_config(self, source: Union[str, Path], 
                     backup_dir: Optional[Union[str, Path]] = None) -> str:
        """备份配置
        
        Args:
            source: 要备份的配置源
            backup_dir: 备份目录，None表示使用默认目录
            
        Returns:
            str: 备份文件路径
        """
        pass
    
    @abstractmethod
    def restore_config(self, backup_path: Union[str, Path], 
                      target: Union[str, Path]) -> bool:
        """恢复配置
        
        Args:
            backup_path: 备份文件路径
            target: 恢复目标路径
            
        Returns:
            bool: 是否成功恢复
        """
        pass
