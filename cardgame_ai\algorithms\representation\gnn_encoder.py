"""
GNN手牌编码器模块

该模块实现了基于图神经网络的手牌编码器，将玩家手牌和场面公共信息构建成图结构，
使用图神经网络(GNN)提取特征，以增强模型对牌张关系的理解。
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Any, Optional, Union

# PyTorch Geometric imports
import torch_geometric
from torch_geometric.nn import GCNConv, GATConv, global_mean_pool, global_add_pool
from torch_geometric.data import Data


class GNNHandEncoder(nn.Module):
    """
    基于图神经网络的手牌编码器

    将手牌和公共信息构建为图结构，使用GNN提取特征
    """
    
    def __init__(
        self,
        node_feature_dim: int = 16,
        gnn_hidden_dim: int = 64,
        output_dim: int = 128,
        num_gnn_layers: int = 2,
        gnn_type: str = 'gcn',
        dropout: float = 0.1,
        use_gate: bool = True,
        device: Optional[str] = None
    ):
        """
        初始化GNNHandEncoder

        Args:
            node_feature_dim (int): 节点特征维度
            gnn_hidden_dim (int): GNN隐藏层维度
            output_dim (int): 输出特征维度
            num_gnn_layers (int): GNN层数
            gnn_type (str): GNN类型，'gcn'或'gat'
            dropout (float): Dropout概率
            use_gate (bool): 是否使用门控机制
            device (str, optional): 计算设备
        """
        super(GNNHandEncoder, self).__init__()
        
        self.node_feature_dim = node_feature_dim
        self.gnn_hidden_dim = gnn_hidden_dim
        self.output_dim = output_dim
        self.num_gnn_layers = num_gnn_layers
        self.gnn_type = gnn_type
        self.use_gate = use_gate
        
        # 设置设备
        self.device = device if device else ('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 初始化GNN层
        self.gnn_layers = nn.ModuleList()
        
        # 第一层
        if gnn_type == 'gcn':
            self.gnn_layers.append(GCNConv(node_feature_dim, gnn_hidden_dim))
        elif gnn_type == 'gat':
            self.gnn_layers.append(GATConv(node_feature_dim, gnn_hidden_dim))
        else:
            raise ValueError(f"不支持的GNN类型: {gnn_type}")
        
        # 后续层
        for _ in range(num_gnn_layers - 1):
            if gnn_type == 'gcn':
                self.gnn_layers.append(GCNConv(gnn_hidden_dim, gnn_hidden_dim))
            elif gnn_type == 'gat':
                self.gnn_layers.append(GATConv(gnn_hidden_dim, gnn_hidden_dim))
        
        # 全局池化后的特征转换
        self.post_pooling = nn.Sequential(
            nn.Linear(gnn_hidden_dim, gnn_hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 输出层
        self.output_layer = nn.Linear(gnn_hidden_dim, output_dim)
        
        # 特征门控层（可选）
        if use_gate:
            self.gate = nn.Sequential(
                nn.Linear(gnn_hidden_dim, gnn_hidden_dim),
                nn.Sigmoid()
            )
    
    def forward(self, graph_data: Data) -> torch.Tensor:
        """
        前向传播

        Args:
            graph_data (Data): PyTorch Geometric图数据

        Returns:
            torch.Tensor: 图嵌入表示
        """
        x, edge_index, batch = graph_data.x, graph_data.edge_index, graph_data.batch
        
        # 通过GNN层
        for i, gnn_layer in enumerate(self.gnn_layers):
            x = gnn_layer(x, edge_index)
            if i < len(self.gnn_layers) - 1:  # 非最后一层
                x = F.relu(x)
                x = F.dropout(x, p=0.1, training=self.training)
        
        # 应用门控机制（如果启用）
        if self.use_gate:
            gate_values = self.gate(x)
            x = x * gate_values
        
        # 全局池化（读出函数）
        # 使用平均池化将所有节点的特征聚合为一个图级表示
        pooled = global_mean_pool(x, batch)
        
        # 后处理
        pooled = self.post_pooling(pooled)
        
        # 输出层
        output = self.output_layer(pooled)
        
        return output


def build_card_graph(
    hand_cards: List[int], 
    public_info: Optional[Dict[str, Any]] = None,
    edge_types: Optional[List[str]] = None
) -> Data:
    """
    构建卡牌图结构

    将手牌和公共信息构建为图结构，包括节点特征和边关系

    Args:
        hand_cards (List[int]): 手牌ID列表
        public_info (Dict[str, Any], optional): 公共信息
        edge_types (List[str], optional): 要创建的边类型列表

    Returns:
        Data: PyTorch Geometric图数据对象
    """
    if edge_types is None:
        edge_types = ['same_suit', 'sequence', 'same_value']
    
    # 创建节点（每张牌是一个节点）
    num_nodes = len(hand_cards)
    
    # 基础节点特征（可以扩展）
    # 假设卡牌ID可以解码为花色(0-3)和点数(0-12)
    node_features = []
    
    for card_id in hand_cards:
        # 解码卡牌ID为花色和点数
        # 假设card_id = suit * 13 + rank
        suit = card_id // 13  # 花色 0-3
        rank = card_id % 13   # 点数 0-12
        
        # 创建one-hot编码或嵌入
        suit_onehot = [0] * 4
        suit_onehot[suit] = 1
        
        rank_onehot = [0] * 13
        rank_onehot[rank] = 1
        
        # 组合特征（也可以添加其他特征）
        # 目前特征维度为4(花色)+13(点数)=17维
        feature = suit_onehot + rank_onehot
        
        node_features.append(feature)
    
    # 转换为张量
    node_features_tensor = torch.tensor(node_features, dtype=torch.float)
    
    # 创建边（牌之间的关系）
    edge_list = []
    
    # 遍历所有牌对，构建边
    for i in range(num_nodes):
        suit_i = hand_cards[i] // 13
        rank_i = hand_cards[i] % 13
        
        for j in range(i + 1, num_nodes):
            suit_j = hand_cards[j] // 13
            rank_j = hand_cards[j] % 13
            
            # 相同花色的边
            if 'same_suit' in edge_types and suit_i == suit_j:
                # 无向图，添加双向边
                edge_list.append([i, j])
                edge_list.append([j, i])
            
            # 顺子关系的边（相邻点数）
            if 'sequence' in edge_types and abs(rank_i - rank_j) == 1:
                edge_list.append([i, j])
                edge_list.append([j, i])
            
            # 相同点数的边
            if 'same_value' in edge_types and rank_i == rank_j:
                edge_list.append([i, j])
                edge_list.append([j, i])
    
    # 如果没有边，添加自环
    if not edge_list and num_nodes > 0:
        edge_list = [[i, i] for i in range(num_nodes)]
    
    # 转换为张量
    edge_index = torch.tensor(edge_list, dtype=torch.long).t().contiguous()
    
    # 创建PyTorch Geometric的Data对象
    graph_data = Data(x=node_features_tensor, edge_index=edge_index)
    
    return graph_data


def add_public_nodes_to_graph(
    graph_data: Data, 
    public_cards: List[int],
    hand_cards_count: int
) -> Data:
    """
    向现有图中添加公共牌节点

    Args:
        graph_data (Data): 原始图数据
        public_cards (List[int]): 公共牌ID列表
        hand_cards_count (int): 手牌节点数量

    Returns:
        Data: 添加了公共牌节点的图数据
    """
    if not public_cards:
        return graph_data
    
    # 提取现有节点特征和边
    x = graph_data.x
    edge_index = graph_data.edge_index
    
    # 为公共牌创建节点特征
    public_node_features = []
    
    for card_id in public_cards:
        suit = card_id // 13  # 花色 0-3
        rank = card_id % 13   # 点数 0-12
        
        suit_onehot = [0] * 4
        suit_onehot[suit] = 1
        
        rank_onehot = [0] * 13
        rank_onehot[rank] = 1
        
        # 可以添加一个标志位表示这是公共牌
        is_public = [1]  # 手牌节点这一位是0
        
        feature = suit_onehot + rank_onehot + is_public
        public_node_features.append(feature)
    
    # 调整手牌节点特征以匹配维度（添加is_public=0标志）
    hand_node_features = torch.cat([x, torch.zeros(x.size(0), 1)], dim=1)
    
    # 合并节点特征
    public_node_features_tensor = torch.tensor(public_node_features, dtype=torch.float)
    combined_node_features = torch.cat([hand_node_features, public_node_features_tensor], dim=0)
    
    # 创建新的边
    new_edges = []
    num_public_nodes = len(public_cards)
    
    # 遍历所有手牌和公共牌，添加关系
    for i in range(hand_cards_count):
        hand_card = x[i]
        hand_suit = hand_card[:4].argmax().item()
        hand_rank = hand_card[4:].argmax().item()
        
        for j in range(num_public_nodes):
            public_node_idx = hand_cards_count + j
            public_card = public_node_features[j]
            public_suit = public_card[:4].index(1)
            public_rank = public_card[4:17].index(1)
            
            # 相同花色
            if hand_suit == public_suit:
                new_edges.append([i, public_node_idx])
                new_edges.append([public_node_idx, i])
            
            # 相邻点数
            if abs(hand_rank - public_rank) == 1:
                new_edges.append([i, public_node_idx])
                new_edges.append([public_node_idx, i])
            
            # 相同点数
            if hand_rank == public_rank:
                new_edges.append([i, public_node_idx])
                new_edges.append([public_node_idx, i])
    
    # 将新边添加到现有边
    if new_edges:
        new_edges_tensor = torch.tensor(new_edges, dtype=torch.long).t().contiguous()
        combined_edge_index = torch.cat([edge_index, new_edges_tensor], dim=1)
    else:
        combined_edge_index = edge_index
    
    # 创建更新后的图数据
    updated_graph_data = Data(x=combined_node_features, edge_index=combined_edge_index)
    
    return updated_graph_data


# 测试函数
def test_gnn_hand_encoder():
    """
    测试GNNHandEncoder的功能
    """
    # 创建随机手牌
    hand_cards = [0, 13, 26, 39, 1, 14, 27]  # 示例牌
    
    # 构建图
    graph_data = build_card_graph(hand_cards)
    
    # 添加批次维度（对于单个图）
    batch = torch.zeros(graph_data.x.size(0), dtype=torch.long)
    graph_data.batch = batch
    
    # 实例化编码器
    encoder = GNNHandEncoder(
        node_feature_dim=17,  # 4(花色) + 13(点数)
        gnn_hidden_dim=32,
        output_dim=64,
        num_gnn_layers=2
    )
    
    # 前向传播
    output = encoder(graph_data)
    
    print(f"输入节点数: {graph_data.x.size(0)}")
    print(f"输入边数: {graph_data.edge_index.size(1) // 2}")  # 除以2因为是无向图
    print(f"输出特征形状: {output.shape}")
    
    return output


if __name__ == "__main__":
    test_gnn_hand_encoder() 