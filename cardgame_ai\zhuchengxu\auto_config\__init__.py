"""
自动配置管理模块

该模块提供硬件检测、参数调优、配置模板生成等功能，
支持Windows和Ubuntu系统的跨平台部署。

主要功能:
- 硬件检测: GPU、CPU、内存规格自动检测
- 参数调优: 基于硬件规格自动计算最优训练参数
- 模板引擎: 动态生成配置文件
- 部署管理: 自动化部署和监控

作者: <PERSON> (全栈开发工程师)
版本: v1.0
"""

from .hardware_detector import HardwareDetector
from .parameter_tuner import ParameterTuner
from .template_engine import ConfigTemplateEngine
from .deployment_manager import DeploymentManager
from .utils import CrossPlatformUtils

__version__ = "1.0.0"
__author__ = "Alex"

__all__ = [
    "HardwareDetector",
    "ParameterTuner", 
    "ConfigTemplateEngine",
    "DeploymentManager",
    "CrossPlatformUtils"
]
