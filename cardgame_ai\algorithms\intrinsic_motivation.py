"""
内在动机探索策略模块

实现基于内在动机的探索策略，如好奇心驱动、信息增益、预测误差等，
用于鼓励AI探索能够带来更多信息的动作。
"""

import logging
import numpy as np
import torch
from typing import Dict, List, Optional, Any, Tuple, Union, Callable

from cardgame_ai.games.common.belief_state import BeliefState
from cardgame_ai.algorithms.information_value import calculate_information_value

# 配置日志
logger = logging.getLogger(__name__)


class IntrinsicMotivation:
    """
    内在动机基类

    提供计算内在奖励的接口，子类需要实现具体的计算方法。
    """

    def __init__(self, weight: float = 0.5):
        """
        初始化内在动机

        Args:
            weight: 内在奖励的权重
        """
        self.weight = weight

    def compute_bonus(self, state: Any, action: int, **kwargs) -> float:
        """
        计算内在奖励

        Args:
            state: 当前状态
            action: 动作
            **kwargs: 其他参数

        Returns:
            float: 内在奖励
        """
        raise NotImplementedError("子类需要实现compute_bonus方法")


class InformationGainMotivation(IntrinsicMotivation):
    """
    基于信息增益的内在动机

    使用信息价值评估功能计算动作的信息增益，作为内在奖励。
    """

    def __init__(
        self,
        weight: float = 0.5,
        method: str = 'combined',
        min_uncertainty_threshold: float = 0.1,
        max_uncertainty_threshold: float = 0.9
    ):
        """
        初始化基于信息增益的内在动机

        Args:
            weight: 内在奖励的权重
            method: 信息价值计算方法，可选'basic'、'entropy'、'action'或'combined'
            min_uncertainty_threshold: 最小不确定性阈值，低于此值的牌不考虑信息价值
            max_uncertainty_threshold: 最大不确定性阈值，高于此值的牌不考虑信息价值
        """
        super().__init__(weight)
        self.method = method
        self.min_uncertainty_threshold = min_uncertainty_threshold
        self.max_uncertainty_threshold = max_uncertainty_threshold

    def compute_bonus(
        self,
        state: Any,
        action: int,
        belief_state: Optional[BeliefState] = None,
        **kwargs
    ) -> float:
        """
        计算基于信息增益的内在奖励

        Args:
            state: 当前状态
            action: 动作
            belief_state: 信念状态
            **kwargs: 其他参数

        Returns:
            float: 内在奖励
        """
        # 如果没有信念状态，无法计算信息增益
        if belief_state is None:
            return 0.0

        # 获取所有牌
        card_probs = getattr(belief_state, 'card_probabilities', {})
        if not card_probs:
            return 0.0

        # 选择一些代表性的牌进行评估（为了效率）
        top_cards = []
        for card, prob in card_probs.items():
            # 只考虑概率在阈值范围内的牌（不确定的牌）
            if self.min_uncertainty_threshold < prob < self.max_uncertainty_threshold:
                top_cards.append(card)
                if len(top_cards) >= 5:  # 最多评估5张牌
                    break

        # 如果没有不确定的牌，则返回0
        if not top_cards:
            return 0.0

        # 计算这些牌的平均信息价值
        total_info_value = 0.0
        for card in top_cards:
            # 使用信息价值计算模块计算信息价值
            card_info_value = calculate_information_value(
                belief_state,
                card,
                method=self.method
            )
            total_info_value += card_info_value

        # 计算平均信息价值
        avg_info_value = total_info_value / len(top_cards)

        # 根据动作特性调整信息价值
        action_factor = self._get_action_info_factor(action, belief_state)

        # 最终内在奖励
        intrinsic_bonus = avg_info_value * action_factor * self.weight

        return intrinsic_bonus

    def _get_action_info_factor(self, action: int, belief_state: BeliefState) -> float:
        """
        获取动作的信息因子

        根据动作特性估计其揭示信息的潜力。

        Args:
            action: 动作
            belief_state: 信念状态

        Returns:
            float: 信息因子，范围在[0, 1]
        """
        # 这里可以根据具体游戏规则和动作特性进行更精确的估计
        # 默认实现是一个简单的启发式方法

        # 假设动作ID与牌有某种映射关系
        # 例如，在斗地主中，某些动作（如出单张）可能比其他动作（如出顺子）揭示更多信息

        # 简单起见，这里使用一个基于动作ID的简单映射
        # 实际应用中，应该基于游戏规则和动作特性进行更精确的估计
        action_type = action % 10  # 简单地取动作ID的个位数作为动作类型

        if action_type == 0:  # 假设0代表"不出"
            return 0.1  # "不出"通常揭示较少信息
        elif action_type == 1:  # 假设1代表"出单张"
            return 1.0  # 出单张通常揭示较多信息
        elif action_type == 2:  # 假设2代表"出对子"
            return 0.8  # 出对子揭示中等信息
        else:
            return 0.5  # 其他动作默认中等信息潜力


class CuriosityMotivation(IntrinsicMotivation):
    """
    基于好奇心的内在动机

    使用预测误差作为内在奖励，鼓励AI探索难以预测的状态。
    """

    def __init__(
        self,
        weight: float = 0.5,
        prediction_model: Optional[Any] = None,
        prediction_error_scale: float = 1.0,
        use_normalized_error: bool = True
    ):
        """
        初始化基于好奇心的内在动机

        Args:
            weight: 内在奖励的权重
            prediction_model: 用于预测下一个状态的模型
            prediction_error_scale: 预测误差的缩放因子
            use_normalized_error: 是否使用归一化的预测误差
        """
        super().__init__(weight)
        self.prediction_model = prediction_model
        self.prediction_error_scale = prediction_error_scale
        self.use_normalized_error = use_normalized_error
        self.error_stats = {
            'count': 0,
            'mean': 0.0,
            'std': 1.0,
            'min': 0.0,
            'max': 1.0
        }

    def compute_bonus(
        self,
        state: Any,
        action: int,
        next_state: Optional[Any] = None,
        **kwargs
    ) -> float:
        """
        计算基于好奇心的内在奖励

        Args:
            state: 当前状态
            action: 动作
            next_state: 下一个状态
            **kwargs: 其他参数

        Returns:
            float: 内在奖励
        """
        # 如果没有预测模型或下一个状态，无法计算预测误差
        if self.prediction_model is None or next_state is None:
            return 0.0

        # 使用预测模型预测下一个状态
        predicted_next_state = self.prediction_model.predict(state, action)

        # 计算预测误差
        prediction_error = self._compute_prediction_error(predicted_next_state, next_state)

        # 更新误差统计信息
        self._update_error_stats(prediction_error)

        # 如果使用归一化误差，则进行归一化
        if self.use_normalized_error:
            normalized_error = self._normalize_error(prediction_error)
            intrinsic_bonus = normalized_error * self.weight
        else:
            intrinsic_bonus = prediction_error * self.prediction_error_scale * self.weight

        return intrinsic_bonus

    def _compute_prediction_error(self, predicted_state: Any, actual_state: Any) -> float:
        """
        计算预测误差

        Args:
            predicted_state: 预测的状态
            actual_state: 实际的状态

        Returns:
            float: 预测误差
        """
        # 如果状态是张量，则计算MSE
        if isinstance(predicted_state, torch.Tensor) and isinstance(actual_state, torch.Tensor):
            with torch.no_grad():
                error = torch.mean((predicted_state - actual_state) ** 2).item()
        # 如果状态是numpy数组，则计算MSE
        elif isinstance(predicted_state, np.ndarray) and isinstance(actual_state, np.ndarray):
            error = np.mean((predicted_state - actual_state) ** 2)
        # 其他情况，尝试计算差异
        else:
            try:
                error = abs(predicted_state - actual_state)
            except:
                logger.warning("无法计算预测误差，返回默认值")
                error = 0.0

        return error

    def _update_error_stats(self, error: float) -> None:
        """
        更新误差统计信息

        Args:
            error: 预测误差
        """
        # 更新计数
        self.error_stats['count'] += 1

        # 更新均值和标准差
        count = self.error_stats['count']
        old_mean = self.error_stats['mean']
        old_std = self.error_stats['std']

        # 增量更新均值
        new_mean = old_mean + (error - old_mean) / count

        # 增量更新标准差
        if count > 1:
            new_std = np.sqrt(((count - 1) * old_std ** 2 + (error - old_mean) * (error - new_mean)) / count)
        else:
            new_std = old_std

        # 更新最小值和最大值
        new_min = min(self.error_stats['min'], error)
        new_max = max(self.error_stats['max'], error)

        # 更新统计信息
        self.error_stats['mean'] = new_mean
        self.error_stats['std'] = new_std
        self.error_stats['min'] = new_min
        self.error_stats['max'] = new_max

    def _normalize_error(self, error: float) -> float:
        """
        归一化预测误差

        Args:
            error: 预测误差

        Returns:
            float: 归一化后的预测误差
        """
        # 如果没有足够的样本，直接返回原始误差
        if self.error_stats['count'] < 10:
            return error

        # 使用Z-score归一化
        mean = self.error_stats['mean']
        std = max(self.error_stats['std'], 1e-6)  # 避免除以0
        normalized = (error - mean) / std

        # 将归一化后的误差限制在[0, 1]范围内
        normalized = max(0.0, min(1.0, (normalized + 3) / 6))  # 假设误差在[-3, 3]范围内

        return normalized


class EntropyBasedMotivation(IntrinsicMotivation):
    """
    基于熵的内在动机

    使用策略熵作为内在奖励，鼓励AI探索多样化的动作。
    """

    def __init__(
        self,
        weight: float = 0.5,
        entropy_scale: float = 1.0,
        target_entropy: float = 0.5
    ):
        """
        初始化基于熵的内在动机

        Args:
            weight: 内在奖励的权重
            entropy_scale: 熵的缩放因子
            target_entropy: 目标熵值
        """
        super().__init__(weight)
        self.entropy_scale = entropy_scale
        self.target_entropy = target_entropy

    def compute_bonus(
        self,
        state: Any,
        action: int,
        policy: Optional[np.ndarray] = None,
        **kwargs
    ) -> float:
        """
        计算基于熵的内在奖励

        Args:
            state: 当前状态
            action: 动作
            policy: 策略分布
            **kwargs: 其他参数

        Returns:
            float: 内在奖励
        """
        # 如果没有策略分布，无法计算熵
        if policy is None:
            return 0.0

        # 计算策略熵
        entropy = self._compute_entropy(policy)

        # 计算熵奖励
        # 如果熵低于目标熵，则给予正奖励，鼓励探索
        # 如果熵高于目标熵，则给予负奖励，鼓励利用
        entropy_bonus = (self.target_entropy - entropy) * self.entropy_scale

        # 最终内在奖励
        intrinsic_bonus = entropy_bonus * self.weight

        return intrinsic_bonus

    def _compute_entropy(self, policy: np.ndarray) -> float:
        """
        计算策略熵

        Args:
            policy: 策略分布

        Returns:
            float: 策略熵
        """
        # 过滤掉概率为0的动作
        valid_policy = policy[policy > 0]

        # 计算熵
        entropy = -np.sum(valid_policy * np.log(valid_policy))

        return entropy


class CompositeMotivation(IntrinsicMotivation):
    """
    组合内在动机

    组合多种内在动机，计算综合内在奖励。
    """

    def __init__(
        self,
        motivations: List[IntrinsicMotivation],
        weights: Optional[List[float]] = None
    ):
        """
        初始化组合内在动机

        Args:
            motivations: 内在动机列表
            weights: 各内在动机的权重列表，如果为None，则使用每个动机自己的权重
        """
        super().__init__(1.0)  # 总权重为1.0，各动机权重由weights决定
        self.motivations = motivations

        # 如果没有提供权重，则使用每个动机自己的权重
        if weights is None:
            self.weights = [motivation.weight for motivation in motivations]
        else:
            # 确保权重列表长度与动机列表长度相同
            assert len(weights) == len(motivations), "权重列表长度必须与动机列表长度相同"
            self.weights = weights

        # 归一化权重
        total_weight = sum(self.weights)
        if total_weight > 0:
            self.weights = [w / total_weight for w in self.weights]

    def compute_bonus(self, state: Any, action: int, **kwargs) -> float:
        """
        计算组合内在奖励

        Args:
            state: 当前状态
            action: 动作
            **kwargs: 其他参数

        Returns:
            float: 内在奖励
        """
        # 计算每个动机的内在奖励
        bonuses = []
        for i, motivation in enumerate(self.motivations):
            bonus = motivation.compute_bonus(state, action, **kwargs)
            bonuses.append(bonus * self.weights[i])

        # 计算总内在奖励
        total_bonus = sum(bonuses)

        return total_bonus


# 工厂函数
def create_intrinsic_motivation(
    motivation_type: str,
    **kwargs
) -> IntrinsicMotivation:
    """
    创建内在动机

    Args:
        motivation_type: 内在动机类型，可选'information_gain'、'curiosity'、'entropy'或'composite'
        **kwargs: 其他参数

    Returns:
        IntrinsicMotivation: 内在动机实例
    """
    if motivation_type == 'information_gain':
        return InformationGainMotivation(**kwargs)
    elif motivation_type == 'curiosity':
        return CuriosityMotivation(**kwargs)
    elif motivation_type == 'entropy':
        return EntropyBasedMotivation(**kwargs)
    elif motivation_type == 'composite':
        # 创建组合内在动机
        motivations = kwargs.pop('motivations', [])
        weights = kwargs.pop('weights', None)
        return CompositeMotivation(motivations, weights)
    else:
        raise ValueError(f"未知的内在动机类型: {motivation_type}")


def create_default_motivation() -> IntrinsicMotivation:
    """
    创建默认内在动机

    Returns:
        IntrinsicMotivation: 默认内在动机实例
    """
    # 创建基于信息增益的内在动机
    info_gain = InformationGainMotivation(weight=0.7)

    # 创建基于熵的内在动机
    entropy = EntropyBasedMotivation(weight=0.3)

    # 创建组合内在动机
    return CompositeMotivation([info_gain, entropy])
