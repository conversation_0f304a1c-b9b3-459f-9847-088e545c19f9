"""
隐式通信机制模块

实现农民之间的隐式通信机制，提高多智能体协作效果。包括注意力引导的隐式通信、
意图推理模块和元通信学习等技术，使农民智能体能够更有效地协作。
"""
import os
import time
import logging
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Any, Optional, Union
from collections import defaultdict, deque

from cardgame_ai.multi_agent.multi_agent_framework import FarmerCooperation, RoleManager
from cardgame_ai.games.doudizhu.card import Card
from cardgame_ai.games.doudizhu.card_group import CardGroup
# 导入牌型常量
CARD_TYPE = {
    "PASS": 0,
    "SINGLE": 1,
    "PAIR": 2,
    "TRIO": 3,
    "TRIO_WITH_SINGLE": 4,
    "TRIO_WITH_PAIR": 5,
    "STRAIGHT": 6,
    "PAIR_STRAIGHT": 7,
    "BOMB": 8,
    "ROCKET": 9
}

# 将CARD_TYPE字典转换为对象，便于访问
class CardTypeEnum:
    pass

for key, value in CARD_TYPE.items():
    setattr(CardTypeEnum, key, value)

CARD_TYPE = CardTypeEnum


class CardPatternRecognizer:
    """
    牌型识别器

    识别不同的牌型模式，用于隐式信号的编码和解码。
    """

    def __init__(self):
        """
        初始化牌型识别器
        """
        # 牌型到模式的映射
        self.type_to_pattern = {
            CARD_TYPE.SINGLE: "single",
            CARD_TYPE.PAIR: "pair",
            CARD_TYPE.TRIO: "trio",
            CARD_TYPE.TRIO_WITH_SINGLE: "trio_with_single",
            CARD_TYPE.TRIO_WITH_PAIR: "trio_with_pair",
            CARD_TYPE.STRAIGHT: "straight",
            CARD_TYPE.PAIR_STRAIGHT: "pair_straight",
            CARD_TYPE.BOMB: "bomb",
            CARD_TYPE.ROCKET: "rocket",
            CARD_TYPE.PASS: "pass"
        }

        # 牌值分类阈值
        self.value_thresholds = {
            "small": 7,  # 7及以下为小牌
            "medium": 10,  # 8-10为中牌
            "big": 20  # 11及以上为大牌
        }

    def recognize_pattern(self, action: int, observation: Any) -> str:
        """
        识别动作的牌型模式

        Args:
            action: 动作 ID
            observation: 当前观察

        Returns:
            牌型模式字符串
        """
        # 如果是过牌
        if action == 0 or action is None:
            return "pass"

        # 从观察中获取动作对应的牌型
        card_type = self._get_card_type_from_action(action, observation)

        # 获取基本牌型
        base_pattern = self.type_to_pattern.get(card_type, "unknown")

        # 如果是单牌、对牌或三张，进一步分类
        if base_pattern in ["single", "pair", "trio"]:
            # 获取牌值
            card_value = self._get_card_value_from_action(action, observation)

            # 根据牌值分类
            if card_value <= self.value_thresholds["small"]:
                return f"{base_pattern}_small"
            elif card_value <= self.value_thresholds["medium"]:
                return f"{base_pattern}_medium"
            else:
                return f"{base_pattern}_big"

        return base_pattern

    def _get_card_type_from_action(self, action: int, observation: Any) -> int:
        """
        从动作中获取牌型

        Args:
            action: 动作 ID
            observation: 当前观察

        Returns:
            牌型编码
        """
        # 实际实现中需要根据环境的实现来获取牌型
        # 这里提供一个简化的示例

        # 假设动作编码中包含牌型信息
        # 例如：动作 ID 的前两位表示牌型
        card_type = action // 100

        # 映射到我们的牌型常量
        if card_type == 1:
            return CARD_TYPE.SINGLE
        elif card_type == 2:
            return CARD_TYPE.PAIR
        elif card_type == 3:
            return CARD_TYPE.TRIO
        elif card_type == 4:
            return CARD_TYPE.STRAIGHT
        elif card_type == 5:
            return CARD_TYPE.PAIR_STRAIGHT
        elif card_type == 6:
            return CARD_TYPE.BOMB
        elif card_type == 7:
            return CARD_TYPE.ROCKET
        else:
            return CARD_TYPE.SINGLE  # 默认为单牌

    def _get_card_value_from_action(self, action: int, observation: Any) -> int:
        """
        从动作中获取牌值

        Args:
            action: 动作 ID
            observation: 当前观察

        Returns:
            牌值
        """
        # 实际实现中需要根据环境的实现来获取牌值
        # 这里提供一个简化的示例

        # 假设动作编码中包含牌值信息
        # 例如：动作 ID 的后两位表示牌值
        card_value = action % 100

        # 返回牌值，范围为 3-17（3为3，13为K，14为A，15为2，16为小王，17为大王）
        return min(max(card_value, 3), 17)


class ImplicitCommunicationMechanism:
    """
    隐式通信机制

    实现农民之间的隐式通信，通过出牌行为传递信息，而不需要显式的通信通道。
    """

    def __init__(self, communication_threshold: float = 0.7):
        """
        初始化隐式通信机制

        Args:
            communication_threshold: 通信阈值，控制何时触发隐式通信
        """
        self.communication_threshold = communication_threshold

        # 信号编码和解码器
        self.signal_encoder = ImplicitSignalEncoder()
        self.signal_decoder = ImplicitSignalDecoder()

        # 意图推理模块
        self.intention_inference = IntentionInferenceModule()

        # 历史信号记录
        self.signal_history = defaultdict(list)

        # 通信状态
        self.communication_state = defaultdict(dict)

        # 统计信息
        self.stats = {
            "signals_sent": 0,
            "signals_received": 0,
            "successful_communications": 0
        }

    def encode_intention(self, agent_id: str, intention: str, legal_actions: List[int],
                        observation: Any) -> Optional[int]:
        """
        将意图编码为动作信号

        Args:
            agent_id: 智能体ID
            intention: 意图
            legal_actions: 合法动作列表
            observation: 当前观察

        Returns:
            编码后的动作，如果无法编码则返回None
        """
        # 检查是否应该发送信号
        if np.random.random() > self.communication_threshold:
            return None

        # 编码意图为动作
        action = self.signal_encoder.encode(intention, legal_actions, observation)

        # 记录信号
        if action is not None:
            self.signal_history[agent_id].append({
                "intention": intention,
                "action": action,
                "timestamp": time.time()
            })
            self.stats["signals_sent"] += 1

        return action

    def decode_signals(self, agent_id: str, teammate_id: str, recent_actions: List[int],
                      observations: Dict[str, Any]) -> Dict[str, Any]:
        """
        解码队友的动作信号

        Args:
            agent_id: 当前智能体ID
            teammate_id: 队友ID
            recent_actions: 队友最近的动作
            observations: 所有智能体的观察

        Returns:
            解码后的意图信息
        """
        # 解码信号
        decoded_intentions = self.signal_decoder.decode(
            recent_actions,
            self.signal_history.get(teammate_id, []),
            observations.get(teammate_id, None)
        )

        # 记录接收到的信号
        if decoded_intentions:
            self.stats["signals_received"] += 1

            # 检查是否成功解码了意图
            if any(decoded_intentions.values()):
                self.stats["successful_communications"] += 1

        return decoded_intentions

    def infer_intentions(self, agent_id: str, teammate_id: str,
                        observations: Dict[str, Any],
                        action_history: Dict[str, List[int]]) -> Dict[str, float]:
        """
        推断队友的意图

        Args:
            agent_id: 当前智能体ID
            teammate_id: 队友ID
            observations: 所有智能体的观察
            action_history: 动作历史

        Returns:
            推断的意图概率分布
        """
        return self.intention_inference.infer(
            teammate_id,
            observations.get(teammate_id, None),
            action_history.get(teammate_id, [])
        )

    def get_communication_state(self, agent_id: str) -> Dict[str, Any]:
        """
        获取通信状态

        Args:
            agent_id: 智能体ID

        Returns:
            通信状态
        """
        return self.communication_state.get(agent_id, {})

    def update_communication_state(self, agent_id: str, state_update: Dict[str, Any]) -> None:
        """
        更新通信状态

        Args:
            agent_id: 智能体ID
            state_update: 状态更新
        """
        if agent_id in self.communication_state:
            self.communication_state[agent_id].update(state_update)
        else:
            self.communication_state[agent_id] = state_update

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            统计信息
        """
        return self.stats


class ImplicitSignalEncoder:
    """
    隐式信号编码器

    将意图编码为隐式信号（动作），用于农民之间的隐式通信。
    """

    def __init__(self):
        """
        初始化隐式信号编码器
        """
        # 意图到牌型模式的映射
        self.intention_patterns = {
            "has_big_cards": ["single_big", "pair_big", "trio_big"],
            "has_bomb": ["bomb", "rocket"],
            "has_many_pairs": ["pair", "pair_straight"],
            "has_many_singles": ["single", "straight"],
            "need_control": ["pass", "single_small"],
            "cooperative_attack": ["trio", "trio_with_single"],
            "defensive_play": ["single_small", "pair_small"]
        }

        # 牌型识别器
        self.pattern_recognizer = CardPatternRecognizer()

        # 优先级映射，控制信号的选择优先级
        self.priority_mapping = {
            "has_bomb": 5,
            "has_big_cards": 4,
            "cooperative_attack": 3,
            "has_many_pairs": 2,
            "has_many_singles": 2,
            "need_control": 1,
            "defensive_play": 0
        }

    def encode(self, intention: str, legal_actions: List[int], observation: Any) -> Optional[int]:
        """
        将意图编码为动作信号

        Args:
            intention: 意图
            legal_actions: 合法动作列表
            observation: 当前观察

        Returns:
            编码后的动作，如果无法编码则返回None
        """
        # 检查意图是否在映射中
        if intention not in self.intention_patterns:
            return None

        # 获取意图对应的牌型模式
        target_patterns = self.intention_patterns[intention]

        # 如果没有合法动作，返回None
        if not legal_actions:
            return None

        # 对每个合法动作识别牌型
        action_patterns = {}
        for action in legal_actions:
            pattern = self.pattern_recognizer.recognize_pattern(action, observation)
            action_patterns[action] = pattern

        # 找到与目标模式匹配的动作
        matching_actions = []
        for action, pattern in action_patterns.items():
            if pattern in target_patterns:
                matching_actions.append(action)

        # 如果没有匹配的动作，返回None
        if not matching_actions:
            return None

        # 如果有多个匹配的动作，选择最优的一个
        if len(matching_actions) > 1:
            # 根据一定的策略选择最优的动作
            return self._select_best_action(matching_actions, action_patterns, intention, observation)

        # 返回匹配的动作
        return matching_actions[0]

    def _select_best_action(self, actions: List[int], action_patterns: Dict[int, str],
                          intention: str, observation: Any) -> int:
        """
        选择最优的动作

        Args:
            actions: 候选动作列表
            action_patterns: 动作到牌型模式的映射
            intention: 意图
            observation: 当前观察

        Returns:
            最优的动作
        """
        # 如果意图是表示有大牌，选择最大的牌
        if intention == "has_big_cards":
            # 获取每个动作的牌值
            card_values = {action: self.pattern_recognizer._get_card_value_from_action(action, observation)
                          for action in actions}
            # 返回牌值最大的动作
            return max(card_values.items(), key=lambda x: x[1])[0]

        # 如果意图是表示有炸弹，选择炸弹或火箭
        elif intention == "has_bomb":
            # 优先选择火箭
            for action in actions:
                if action_patterns[action] == "rocket":
                    return action
            # 其次选择炸弹
            for action in actions:
                if action_patterns[action] == "bomb":
                    return action

        # 如果意图是需要控制权，优先选择过牌
        elif intention == "need_control":
            for action in actions:
                if action_patterns[action] == "pass":
                    return action

        # 如果意图是防守性打法，选择最小的牌
        elif intention == "defensive_play":
            # 获取每个动作的牌值
            card_values = {action: self.pattern_recognizer._get_card_value_from_action(action, observation)
                          for action in actions}
            # 返回牌值最小的动作
            return min(card_values.items(), key=lambda x: x[1])[0]

        # 其他情况，随机选择一个动作
        import random
        return random.choice(actions)


class ImplicitSignalDecoder:
    """
    隐式信号解码器

    将动作信号解码为意图，用于农民之间的隐式通信。
    """

    def __init__(self):
        """
        初始化隐式信号解码器
        """
        # 牌型模式到意图的映射
        self.pattern_intentions = {
            "single_big": ["has_big_cards", "aggressive_play"],
            "pair_big": ["has_big_cards", "aggressive_play"],
            "trio_big": ["has_big_cards", "aggressive_play"],
            "bomb": ["has_bomb", "strong_hand"],
            "rocket": ["has_bomb", "strong_hand"],
            "pair": ["has_many_pairs", "balanced_hand"],
            "pair_straight": ["has_many_pairs", "balanced_hand"],
            "single": ["has_many_singles", "weak_hand"],
            "straight": ["has_many_singles", "balanced_hand"],
            "pass": ["need_control", "weak_hand"],
            "single_small": ["need_control", "defensive_play"],
            "trio": ["cooperative_attack", "balanced_hand"],
            "trio_with_single": ["cooperative_attack", "balanced_hand"],
            "pair_small": ["defensive_play", "weak_hand"]
        }

        # 牌型识别器
        self.pattern_recognizer = CardPatternRecognizer()

        # 上下文分析权重
        self.context_weight = 0.7  # 上下文在解码中的权重

    def decode(self, recent_actions: List[int], action_history: List[Dict[str, Any]],
              observation: Optional[Any] = None) -> Dict[str, Any]:
        """
        解码动作信号

        Args:
            recent_actions: 最近的动作
            action_history: 动作历史
            observation: 当前观察（可选）

        Returns:
            解码后的意图信息
        """
        # 如果没有动作，返回空字典
        if not recent_actions:
            return {}

        # 识别最近动作的牌型模式
        patterns = []
        for action in recent_actions:
            pattern = self.pattern_recognizer.recognize_pattern(action, observation)
            patterns.append(pattern)

        # 初始化意图字典
        intentions = {}

        # 分析单个牌型模式
        for pattern in patterns:
            # 如果模式在映射中
            if pattern in self.pattern_intentions:
                # 获取可能的意图
                possible_intentions = self.pattern_intentions[pattern]
                # 将每个意图的概率设置为1.0
                for intention in possible_intentions:
                    intentions[intention] = 1.0

        # 分析牌型序列
        if len(patterns) >= 2:
            sequence_intentions = self._analyze_pattern_sequence(patterns)
            # 合并序列分析结果
            for intention, prob in sequence_intentions.items():
                if intention in intentions:
                    intentions[intention] = max(intentions[intention], prob)
                else:
                    intentions[intention] = prob

        # 如果有动作历史，考虑上下文
        if action_history:
            context_intentions = self._analyze_context(action_history, patterns)
            # 合并上下文分析结果，使用加权平均
            for intention, prob in context_intentions.items():
                if intention in intentions:
                    intentions[intention] = (1 - self.context_weight) * intentions[intention] + \
                                          self.context_weight * prob
                else:
                    intentions[intention] = self.context_weight * prob

        # 过滤低概率的意图
        filtered_intentions = {}
        for intention, prob in intentions.items():
            if prob > 0.3:  # 概率阈值
                filtered_intentions[intention] = prob

        return filtered_intentions

    def _analyze_pattern_sequence(self, patterns: List[str]) -> Dict[str, float]:
        """
        分析牌型序列

        Args:
            patterns: 牌型模式序列

        Returns:
            意图概率字典
        """
        # 初始化意图字典
        sequence_intentions = {}

        # 分析相邻的牌型对
        for i in range(len(patterns) - 1):
            pattern_pair = (patterns[i], patterns[i+1])

            # 分析特定的牌型对
            if pattern_pair == ("pass", "single_big"):
                sequence_intentions["need_big_cards"] = 0.8
            elif pattern_pair == ("single_small", "single_big"):
                sequence_intentions["ascending_play"] = 0.7
            elif pattern_pair == ("pair", "pair"):
                sequence_intentions["has_many_pairs"] = 0.9
            elif pattern_pair == ("trio", "trio"):
                sequence_intentions["has_many_trios"] = 0.9
            elif pattern_pair == ("single_big", "pass"):
                sequence_intentions["no_more_big_cards"] = 0.8

        return sequence_intentions

    def _analyze_context(self, action_history: List[Dict[str, Any]],
                        current_patterns: List[str]) -> Dict[str, float]:
        """
        分析动作历史上下文

        Args:
            action_history: 动作历史
            current_patterns: 当前牌型模式

        Returns:
            意图概率字典
        """
        # 初始化意图字典
        context_intentions = {}

        # 如果历史为空，返回空字典
        if not action_history:
            return {}

        # 提取历史中的意图
        historical_intentions = []
        for entry in action_history:
            if "intention" in entry:
                historical_intentions.append(entry["intention"])

        # 分析历史意图模式
        if historical_intentions:
            # 计算各个意图的出现频率
            intention_counts = {}
            for intention in historical_intentions:
                if intention in intention_counts:
                    intention_counts[intention] += 1
                else:
                    intention_counts[intention] = 1

            # 转换为概率
            total = len(historical_intentions)
            for intention, count in intention_counts.items():
                context_intentions[intention] = count / total

        # 分析当前模式与历史的关系
        if current_patterns and historical_intentions:
            # 检查当前模式是否与历史意图一致
            for pattern in current_patterns:
                for intention in historical_intentions:
                    # 如果当前模式与历史意图相关
                    if self._is_pattern_related_to_intention(pattern, intention):
                        # 增强该意图的概率
                        if intention in context_intentions:
                            context_intentions[intention] = min(1.0, context_intentions[intention] + 0.2)
                        else:
                            context_intentions[intention] = 0.7

        return context_intentions

    def _is_pattern_related_to_intention(self, pattern: str, intention: str) -> bool:
        """
        检查牌型模式是否与意图相关

        Args:
            pattern: 牌型模式
            intention: 意图

        Returns:
            是否相关
        """
        # 如果模式在映射中
        if pattern in self.pattern_intentions:
            # 如果意图在模式的可能意图中
            return intention in self.pattern_intentions[pattern]

        return False


class IntentionInferenceModule:
    """
    意图推理模块

    基于动作历史和当前状态推断队友的意图。
    """

    def __init__(self):
        """
        初始化意图推理模块
        """
        # 意图类型
        self.intention_types = [
            "has_big_cards",
            "has_bomb",
            "has_many_pairs",
            "has_many_singles",
            "need_control",
            "cooperative_attack",
            "defensive_play",
            "aggressive_play",
            "balanced_hand",
            "weak_hand",
            "strong_hand"
        ]

        # 意图推理模型
        self.inference_model = None  # 实际实现中可以使用神经网络或规则系统

        # 意图历史
        self.intention_history = defaultdict(list)

        # 意图转移矩阵，表示从一种意图转移到另一种意图的概率
        self.intention_transition = self._initialize_transition_matrix()

    def infer(self, agent_id: str, observation: Optional[Any],
             action_history: List[int]) -> Dict[str, float]:
        """
        推断意图

        Args:
            agent_id: 智能体ID
            observation: 当前观察（可选）
            action_history: 动作历史

        Returns:
            意图概率分布
        """
        # 初始化意图概率分布
        intention_probs = {intention: 0.0 for intention in self.intention_types}

        # 如果没有动作历史，使用均匀分布
        if not action_history:
            uniform_prob = 1.0 / len(self.intention_types)
            return {intention: uniform_prob for intention in self.intention_types}

        # 基于动作历史推断意图
        pattern_recognizer = CardPatternRecognizer()
        patterns = [pattern_recognizer.recognize_pattern(action, observation) for action in action_history]

        # 统计牌型模式
        pattern_counts = {}
        for pattern in patterns:
            if pattern in pattern_counts:
                pattern_counts[pattern] += 1
            else:
                pattern_counts[pattern] = 1

        # 基于牌型模式推断意图
        for pattern, count in pattern_counts.items():
            # 如果是大牌类型
            if pattern in ["single_big", "pair_big", "trio_big"]:
                intention_probs["has_big_cards"] += 0.2 * count
                intention_probs["aggressive_play"] += 0.1 * count
                intention_probs["strong_hand"] += 0.1 * count
            # 如果是炸弹类型
            elif pattern in ["bomb", "rocket"]:
                intention_probs["has_bomb"] += 0.3 * count
                intention_probs["strong_hand"] += 0.2 * count
            # 如果是对牌类型
            elif pattern in ["pair", "pair_straight"]:
                intention_probs["has_many_pairs"] += 0.2 * count
                intention_probs["balanced_hand"] += 0.1 * count
            # 如果是单牌类型
            elif pattern in ["single", "straight"]:
                intention_probs["has_many_singles"] += 0.2 * count
                if pattern == "single":
                    intention_probs["weak_hand"] += 0.1 * count
            # 如果是过牌
            elif pattern == "pass":
                intention_probs["need_control"] += 0.2 * count
                intention_probs["weak_hand"] += 0.1 * count
            # 如果是三张类型
            elif pattern in ["trio", "trio_with_single", "trio_with_pair"]:
                intention_probs["cooperative_attack"] += 0.2 * count
                intention_probs["balanced_hand"] += 0.1 * count

        # 归一化概率
        total_prob = sum(intention_probs.values())
        if total_prob > 0:
            for intention in intention_probs:
                intention_probs[intention] /= total_prob
        else:
            # 如果所有概率都为0，使用均匀分布
            uniform_prob = 1.0 / len(self.intention_types)
            intention_probs = {intention: uniform_prob for intention in self.intention_types}

        # 更新意图历史
        self.intention_history[agent_id].append({
            "intentions": intention_probs,
            "timestamp": time.time()
        })

        return intention_probs

    def _initialize_transition_matrix(self) -> Dict[str, Dict[str, float]]:
        """
        初始化意图转移矩阵

        Returns:
            意图转移矩阵
        """
        # 初始化转移矩阵
        transition_matrix = {}
        for from_intention in self.intention_types:
            transition_matrix[from_intention] = {}
            for to_intention in self.intention_types:
                # 默认转移概率
                transition_matrix[from_intention][to_intention] = 0.1

        # 设置特定的转移概率
        # 从有大牌到没有大牌的概率较高
        transition_matrix["has_big_cards"]["need_control"] = 0.3
        # 从有炸弹到强牌的概率较高
        transition_matrix["has_bomb"]["strong_hand"] = 0.4
        # 从需要控制到防守性打法的概率较高
        transition_matrix["need_control"]["defensive_play"] = 0.3
        # 从弱牌到需要控制的概率较高
        transition_matrix["weak_hand"]["need_control"] = 0.4
        # 从强牌到进攻性打法的概率较高
        transition_matrix["strong_hand"]["aggressive_play"] = 0.4

        return transition_matrix


def enhance_farmer_cooperation(farmer_cooperation: FarmerCooperation) -> None:
    """
    增强农民协作机制

    将隐式通信机制集成到现有的农民协作机制中。

    Args:
        farmer_cooperation: 现有的农民协作机制
    """
    # 创建隐式通信机制
    implicit_communication = ImplicitCommunicationMechanism()

    # 将隐式通信机制添加到农民协作机制中
    farmer_cooperation.implicit_communication = implicit_communication

    # 增强原有的coordinate_actions方法
    original_coordinate_actions = farmer_cooperation.coordinate_actions

    def enhanced_coordinate_actions(agent_id, observations, legal_actions):
        # 先尝试使用隐式通信
        teammate_id = farmer_cooperation.role_manager.get_teammates(agent_id)[0] if farmer_cooperation.role_manager.get_teammates(agent_id) else None

        if teammate_id:
            # 获取队友最近的动作
            recent_actions = farmer_cooperation.action_history.get(teammate_id, [])[-5:] if teammate_id in farmer_cooperation.action_history else []

            # 解码队友的信号
            decoded_intentions = implicit_communication.decode_signals(
                agent_id, teammate_id, recent_actions, observations
            )

            # 如果解码到了意图，根据意图调整动作
            if decoded_intentions:
                # 如果队友需要大牌
                if decoded_intentions.get("need_big_cards", 0) > 0.5:
                    # 尝试出大牌
                    for action in legal_actions[agent_id]:
                        pattern = implicit_communication.signal_encoder.pattern_recognizer.recognize_pattern(
                            action, observations[agent_id]
                        )
                        if pattern in ["single_big", "pair_big", "trio_big"]:
                            return action

        # 如果隐式通信没有给出建议，使用原有的协调方法
        return original_coordinate_actions(agent_id, observations, legal_actions)

    # 替换原有的coordinate_actions方法
    farmer_cooperation.coordinate_actions = enhanced_coordinate_actions


def test_implicit_communication():
    """
    测试隐式通信机制
    """
    print("\n=== 测试隐式通信机制 ===")

    # 创建隐式通信机制
    implicit_comm = ImplicitCommunicationMechanism()

    # 模拟动作和观察
    observation = {"hand_cards": [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}
    legal_actions = [101, 102, 103, 104, 105, 201, 202, 301, 601, 701]

    # 测试编码
    print("\n测试编码意图为动作:")
    intentions = ["has_big_cards", "has_bomb", "need_control", "cooperative_attack"]
    for intention in intentions:
        action = implicit_comm.encode_intention("agent_1", intention, legal_actions, observation)
        print(f"  意图 '{intention}' 编码为动作: {action}")

    # 测试解码
    print("\n测试解码动作为意图:")
    recent_actions = [101, 201, 301]
    decoded = implicit_comm.decode_signals("agent_1", "agent_2", recent_actions, {"agent_2": observation})
    print(f"  解码结果: {decoded}")

    # 测试意图推理
    print("\n测试意图推理:")
    intention_inference = IntentionInferenceModule()
    action_history = [101, 102, 201, 301, 601]
    inferred = intention_inference.infer("agent_2", observation, action_history)
    print(f"  推理结果: {inferred}")

    # 测试增强农民协作
    print("\n测试增强农民协作:")
    # 创建角色管理器和农民协作机制
    role_manager = RoleManager(None)
    # 模拟角色管理器的方法
    role_manager.get_role = lambda agent_id: "farmer" if agent_id in ["agent_1", "agent_2"] else "landlord"
    role_manager.get_teammates = lambda agent_id: ["agent_2"] if agent_id == "agent_1" else ["agent_1"] if agent_id == "agent_2" else []

    farmer_cooperation = FarmerCooperation(role_manager)
    # 模拟动作历史
    farmer_cooperation.action_history = {"agent_1": [101, 201], "agent_2": [102, 301]}

    # 增强农民协作
    enhance_farmer_cooperation(farmer_cooperation)

    # 测试增强后的协调动作
    observations = {"agent_1": observation, "agent_2": observation}
    legal_actions = {"agent_1": [101, 102, 103, 104, 105, 201, 202, 301, 601, 701]}
    action = farmer_cooperation.coordinate_actions("agent_1", observations, legal_actions)
    print(f"  协调动作结果: {action}")

    return {
        "implicit_communication": implicit_comm,
        "intention_inference": intention_inference,
        "farmer_cooperation": farmer_cooperation
    }


if __name__ == "__main__":
    # 如果直接运行这个文件，则执行测试
    test_implicit_communication()
