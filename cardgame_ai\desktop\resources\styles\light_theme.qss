/*
 * AI棋牌强化学习框架 - 深色主题样式表
 */

/* ==================== NavigationBar and Left Side Area ==================== */
/* 整个侧边栏区域 - 使用!important确保样式应用 */
#navigationBar {
    background-color: #1a2533 !important; /* 更深的背景色 */
    border-right: 1px solid #0f1823 !important; /* 更深的边框 */
    color: #85c1e9 !important; /* 确保文本颜色 */
}

/* 侧边栏中的所有部件都设置为透明背景 */
#navigationBar QWidget,
#navigationBar QLabel,
#navigationBar QPushButton {
    background-color: transparent !important;
    color: #85c1e9 !important; /* 天蓝色字体 */
}

/* 标题区域logo部分 */
#appLogo {
    color: #85c1e9 !important; /* 天蓝色字体 */
    font-weight: bold !important;
    background-color: transparent !important;
}

/* 所有导航按钮 */
.NavButton, #navigationBar .NavButton {
    color: #85c1e9 !important; /* 天蓝色字体 */
    background-color: transparent !important; /* 透明背景 */
    border: none !important;
    padding: 10px 15px !important;
    text-align: left !important;
    width: 100% !important; /* 确保按钮填满整个宽度 */
}

.NavButton:hover, #navigationBar .NavButton:hover {
    background-color: #2c3e50 !important; /* 悬停时略浅一点的背景 */
    color: #aed6f1 !important; /* 悬停时浅一点的天蓝 */
}

.NavButton:checked, #navigationBar .NavButton:checked {
    background-color: #34495e !important; /* 选中时稍浅的背景 */
    color: #ffffff !important; /* 选中时白色字体，对比更清晰 */
    border-left: 3px solid #3498db !important; /* 选中指示器颜色 */
    padding-left: 12px !important;
}

/* ==================== Global Styles ==================== */
/* 全局样式 */
QWidget {
    background-color: #1a2533;
    color: #FFFFFF;
    font-family: "Microsoft YaHei", "Segoe UI", "Roboto", sans-serif;
    font-size: 12px;
}

/* 主窗口 */
QMainWindow {
    background-color: #1a2533;
}

/* 菜单栏 */
QMenuBar {
    background-color: #1a2533;
    color: #FFFFFF;
    border-bottom: 1px solid #0f1823;
}

QMenuBar::item {
    background-color: transparent;
    padding: 6px 10px;
}

QMenuBar::item:selected {
    background-color: #2c3e50;
    color: #FFFFFF;
}

QMenuBar::item:pressed {
    background-color: #2c3e50;
    color: #FFFFFF;
}

/* 菜单 */
QMenu {
    background-color: #1a2533;
    color: #FFFFFF;
    border: 1px solid #0f1823;
    border-radius: 4px;
    padding: 4px 0px;
}

QMenu::item {
    padding: 6px 20px;
}

QMenu::item:selected {
    background-color: #2c3e50;
    color: #FFFFFF;
}

QMenu::separator {
    height: 1px;
    background-color: #0f1823;
    margin: 4px 0px;
}

/* 工具栏 */
QToolBar {
    background-color: #1a2533;
    border-bottom: 1px solid #0f1823;
    spacing: 6px;
    padding: 3px;
}

QToolButton {
    background-color: transparent;
    border-radius: 4px;
    padding: 4px;
    color: #FFFFFF;
}

QToolButton:hover {
    background-color: #2c3e50;
}

QToolButton:pressed {
    background-color: #2c3e50;
}

QToolButton:checked {
    background-color: #2c3e50;
    color: #FFFFFF;
}

/* 状态栏 */
QStatusBar {
    background-color: #1a2533;
    color: #AAAAAA;
    border-top: 1px solid #0f1823;
}

/* 标签页 */
QTabWidget::pane {
    border: 1px solid #E0E6ED;
    border-radius: 4px;
    background-color: #FFFFFF;
}

QTabBar::tab {
    background-color: #F5F7FA;
    color: #7F8C8D;
    border: 1px solid #E0E6ED;
    border-bottom: none;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    padding: 6px 12px;
    margin-right: 2px;
}

QTabBar::tab:selected {
    background-color: #FFFFFF;
    color: #2C3E50;
    border-bottom: none;
}

QTabBar::tab:hover:!selected {
    background-color: #E0E6ED;
}

/* 按钮 */
QPushButton {
    background-color: #2980B9;
    color: #FFFFFF;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #2573A7;
}

QPushButton:pressed {
    background-color: #1F618D;
}

QPushButton:disabled {
    background-color: #BDC3C7;
    color: #FFFFFF;
}

/* 输入框 */
QLineEdit, QTextEdit, QPlainTextEdit {
    background-color: #FFFFFF;
    color: #2C3E50;
    border: 1px solid #E0E6ED;
    border-radius: 4px;
    padding: 4px;
    selection-background-color: #2980B9;
    selection-color: #FFFFFF;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
    border: 1px solid #2980B9;
}

/* 下拉框 */
QComboBox {
    background-color: #FFFFFF;
    color: #2C3E50;
    border: 1px solid #E0E6ED;
    border-radius: 4px;
    padding: 4px 8px;
    min-width: 100px;
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: center right;
    width: 20px;
    border-left: 1px solid #E0E6ED;
}

QComboBox::down-arrow {
    image: url(desktop/resources/icons/down_arrow.png);
    width: 12px;
    height: 12px;
}

QComboBox QAbstractItemView {
    background-color: #FFFFFF;
    color: #2C3E50;
    border: 1px solid #E0E6ED;
    selection-background-color: #2980B9;
    selection-color: #FFFFFF;
}

/* 复选框 */
QCheckBox {
    spacing: 8px;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border: 1px solid #E0E6ED;
    border-radius: 3px;
    background-color: #FFFFFF;
}

QCheckBox::indicator:checked {
    background-color: #2980B9;
    image: url(desktop/resources/icons/check.png);
}

QCheckBox::indicator:unchecked:hover {
    border: 1px solid #2980B9;
}

/* 单选框 */
QRadioButton {
    spacing: 8px;
}

QRadioButton::indicator {
    width: 16px;
    height: 16px;
    border: 1px solid #E0E6ED;
    border-radius: 8px;
    background-color: #FFFFFF;
}

QRadioButton::indicator:checked {
    background-color: #2980B9;
    border: 4px solid #FFFFFF;
}

QRadioButton::indicator:unchecked:hover {
    border: 1px solid #2980B9;
}

/* 滑块 */
QSlider::groove:horizontal {
    height: 4px;
    background-color: #E0E6ED;
    border-radius: 2px;
}

QSlider::handle:horizontal {
    background-color: #2980B9;
    border: none;
    width: 16px;
    height: 16px;
    margin: -6px 0;
    border-radius: 8px;
}

QSlider::handle:horizontal:hover {
    background-color: #2573A7;
}

/* 进度条 */
QProgressBar {
    background-color: #FFFFFF;
    border: 1px solid #E0E6ED;
    border-radius: 4px;
    text-align: center;
    color: #2C3E50;
}

QProgressBar::chunk {
    background-color: #2980B9;
    border-radius: 3px;
}

/* 分组框 */
QGroupBox {
    border: 1px solid #E0E6ED;
    border-radius: 4px;
    margin-top: 12px;
    padding-top: 12px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top left;
    padding: 0 5px;
    color: #2C3E50;
}

/* 列表视图 */
QListView, QTreeView, QTableView {
    background-color: #FFFFFF;
    border: 1px solid #E0E6ED;
    border-radius: 4px;
    alternate-background-color: #F5F7FA;
}

QListView::item, QTreeView::item, QTableView::item {
    padding: 4px;
    color: #2C3E50;
}

QListView::item:selected, QTreeView::item:selected, QTableView::item:selected {
    background-color: #2980B9;
    color: #FFFFFF;
}

QListView::item:hover, QTreeView::item:hover, QTableView::item:hover {
    background-color: rgba(41, 128, 185, 0.1);
}

QHeaderView::section {
    background-color: #E0E6ED;
    color: #2C3E50;
    padding: 4px;
    border: 1px solid #D0DAE6;
}

/* 滚动条 */
QScrollBar:vertical {
    background-color: #F5F7FA;
    width: 12px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #2980B9;
    min-height: 20px;
    border-radius: 6px;
}

QScrollBar::handle:vertical:hover {
    background-color: #2573A7;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    background-color: #F5F7FA;
    height: 12px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background-color: #2980B9;
    min-width: 20px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #2573A7;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

/* 分割器 */
QSplitter::handle {
    background-color: #E0E6ED;
}

QSplitter::handle:horizontal {
    width: 2px;
}

QSplitter::handle:vertical {
    height: 2px;
}

/* 提示框 */
QToolTip {
    background-color: #FFFFFF;
    color: #2C3E50;
    border: 1px solid #E0E6ED;
    border-radius: 4px;
    padding: 4px;
}

/* 对话框 */
QDialog {
    background-color: #F5F7FA;
}

/* 消息框 */
QMessageBox {
    background-color: #F5F7FA;
}

/* ==================== Content Area ==================== */
#contentArea {
    background-color: #F5F7FA;
    color: #2C3E50;
    padding: 10px;
}

/* 内容区域内的控件 - 浅色样式 */
#contentArea QWidget {
    background-color: transparent;
    color: #2C3E50;
}

#contentArea QLabel {
    color: #2C3E50;
}

#contentArea QPushButton {
    background-color: #2980B9;
    color: #FFFFFF;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    min-width: 80px;
}

#contentArea QPushButton:hover {
    background-color: #2573A7;
}

#contentArea QPushButton:pressed {
    background-color: #1F618D;
}

#contentArea QPushButton:disabled {
    background-color: #BDC3C7;
    color: #FFFFFF;
}

/* 输入框 */
#contentArea QLineEdit, #contentArea QTextEdit, #contentArea QPlainTextEdit {
    background-color: #FFFFFF;
    color: #2C3E50;
    border: 1px solid #E0E6ED;
    border-radius: 4px;
    padding: 4px;
    selection-background-color: #2980B9;
    selection-color: #FFFFFF;
}

#contentArea QLineEdit:focus, #contentArea QTextEdit:focus, #contentArea QPlainTextEdit:focus {
    border: 1px solid #2980B9;
}

/* 下拉框 */
#contentArea QComboBox {
    background-color: #FFFFFF;
    color: #2C3E50;
    border: 1px solid #E0E6ED;
    border-radius: 4px;
    padding: 4px 8px;
    min-width: 100px;
}

#contentArea QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: center right;
    width: 20px;
    border-left: 1px solid #E0E6ED;
}

#contentArea QComboBox::down-arrow {
    image: url(desktop/resources/icons/down_arrow.png);
    width: 12px;
    height: 12px;
}

#contentArea QComboBox QAbstractItemView {
    background-color: #FFFFFF;
    color: #2C3E50;
    border: 1px solid #E0E6ED;
    selection-background-color: #2980B9;
    selection-color: #FFFFFF;
}

/* 卡片样式 */
#contentArea .Card {
    background-color: #FFFFFF;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 标题样式 */
#contentArea .Title {
    font-size: 18px;
    font-weight: bold;
    color: #2C3E50;
    margin-bottom: 10px;
}

#contentArea .Subtitle {
    font-size: 14px;
    color: #7F8C8D;
    margin-bottom: 5px;
}

/* 训练视图样式 */
#trainingView {
    background-color: #F5F7FA;
}

/* 游戏选择区 */
#gameSelector {
    background-color: #FFFFFF;
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 参数配置区 */
#parameterPanel {
    background-color: #FFFFFF;
    border-radius: 8px;
    padding: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 训练控制区 */
#trainingControlPanel {
    background-color: #FFFFFF;
    border-radius: 8px;
    padding: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 训练监控区 */
#trainingMonitor {
    background-color: #FFFFFF;
    border-radius: 8px;
    padding: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 模型管理区 */
#modelManager {
    background-color: #FFFFFF;
    border-radius: 8px;
    padding: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 重要：再次确保侧边栏样式优先级 */
#navigationBar, #navigationBar * {
    background-color: #1a2533 !important;
}
