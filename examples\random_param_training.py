#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
斗地主游戏随机参数训练脚本

这个脚本用于使用多组随机参数进行斗地主训练，检查是否存在bug。
包括完整的游戏流程（发牌、叫地主、抢地主和出牌阶段）。
"""

import os
import sys
import time
import logging
import random
import argparse
import gc
import json
import psutil
import numpy as np
import torch
from collections import defaultdict
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime
from tqdm import tqdm

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入必要的模块
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.games.doudizhu.state import GamePhase
from cardgame_ai.core.agent import Agent
from cardgame_ai.games.doudizhu.action import BidAction, GrabAction
from cardgame_ai.training.doudizhu_self_play import play_doudizhu_game
from cardgame_ai.training.hyperparameter_config import (
    SELF_PLAY_PARAM_RANGES,
    DQN_PARAM_RANGES,
    TRAINING_PARAM_RANGES
)

# 导入修复版的DouDizhuSelfPlay类
from examples.fixed_doudizhu_self_play import FixedDouDizhuSelfPlay


def adjust_param_ranges(min_games=None, max_games=None, min_temp=None, max_temp=None):
    """
    调整参数范围

    Args:
        min_games (int, optional): 最小游戏数量. Defaults to None.
        max_games (int, optional): 最大游戏数量. Defaults to None.
        min_temp (float, optional): 最小温度参数. Defaults to None.
        max_temp (float, optional): 最大温度参数. Defaults to None.
    """
    global RANDOM_PARAM_RANGES

    # 调整游戏数量范围
    if min_games is not None or max_games is not None:
        current_games = RANDOM_PARAM_RANGES['num_games']
        if isinstance(current_games, list):
            # 如果是列表，过滤出符合范围的值
            filtered_games = [g for g in current_games if
                             (min_games is None or g >= min_games) and
                             (max_games is None or g <= max_games)]
            if filtered_games:
                RANDOM_PARAM_RANGES['num_games'] = filtered_games
            else:
                # 如果过滤后为空，创建新的范围
                new_min = min_games if min_games is not None else min(current_games)
                new_max = max_games if max_games is not None else max(current_games)
                RANDOM_PARAM_RANGES['num_games'] = list(range(new_min, new_max + 1, 5))
        else:
            # 如果是元组，创建新的范围
            new_min = min_games if min_games is not None else current_games[0]
            new_max = max_games if max_games is not None else current_games[1]
            RANDOM_PARAM_RANGES['num_games'] = (new_min, new_max)

    # 调整温度参数范围
    if min_temp is not None or max_temp is not None:
        current_temp = RANDOM_PARAM_RANGES['temperature']
        new_min = min_temp if min_temp is not None else current_temp[0]
        new_max = max_temp if max_temp is not None else current_temp[1]
        RANDOM_PARAM_RANGES['temperature'] = (new_min, new_max)

# 定义随机参数范围
RANDOM_PARAM_RANGES = {
    # 温度参数，控制动作采样的随机性
    "temperature": (0.5, 2.0),

    # 游戏数量，控制训练时间
    "num_games": [5, 10, 15, 20],

    # 批量大小
    "batch_size": [32, 64, 128],

    # 学习率
    "learning_rate": (0.0001, 0.001),

    # 折扣因子
    "gamma": (0.9, 0.99),

    # 是否保存经验
    "save_experiences": [True, False],

    # 是否使用并行
    "parallel": [True, False]
}

# 设置日志格式
def setup_logger(log_dir: str, name: str = 'random_param_training') -> logging.Logger:
    """
    设置日志记录器

    Args:
        log_dir (str): 日志目录
        name (str, optional): 日志名称. Defaults to 'random_param_training'.

    Returns:
        logging.Logger: 日志记录器
    """
    # 创建日志目录
    os.makedirs(log_dir, exist_ok=True)

    # 创建日志记录器
    logger = logging.getLogger(name)
    logger.setLevel(logging.INFO)

    # 清除现有的处理器
    if logger.handlers:
        logger.handlers.clear()

    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_format = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(console_format)
    logger.addHandler(console_handler)

    # 创建文件处理器
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"{name}_{timestamp}.log")
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    file_format = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(file_format)
    logger.addHandler(file_handler)

    return logger


class RandomAgent:
    """随机代理，用于测试"""

    def __init__(self, seed=None):
        """初始化随机代理"""
        self.rng = np.random.RandomState(seed)

    def act(self, observation, legal_actions, is_training=False, temperature=1.0):
        """随机选择一个合法动作"""
        return self.rng.choice(legal_actions)


def format_param_value(param_name: str, value: Any) -> str:
    """
    格式化参数值，用于日志记录

    Args:
        param_name (str): 参数名称
        value (Any): 参数值

    Returns:
        str: 格式化后的参数值字符串
    """
    if isinstance(value, float):
        # 浮点数参数，保留4位小数
        return f"{value:.4f}"
    elif isinstance(value, bool):
        # 布尔参数，转换为中文
        return "是" if value else "否"
    else:
        # 其他类型参数，直接转换为字符串
        return str(value)


def is_param_in_range(param_name: str, value: Any) -> Tuple[bool, str]:
    """
    检查参数是否在预定义的范围内

    Args:
        param_name (str): 参数名称
        value (Any): 参数值

    Returns:
        Tuple[bool, str]: (是否在范围内, 错误信息)
    """
    if param_name not in RANDOM_PARAM_RANGES:
        return True, ""  # 不在预定义范围内的参数默认为有效

    param_range = RANDOM_PARAM_RANGES[param_name]

    if isinstance(param_range, tuple) and len(param_range) == 2:
        # 范围参数
        min_val, max_val = param_range
        if value < min_val or value > max_val:
            return False, f"参数 {param_name} 的值 {value} 超出范围 [{min_val}, {max_val}]"

    elif isinstance(param_range, list):
        # 列表选项
        if value not in param_range:
            return False, f"参数 {param_name} 的值 {value} 不在可选值 {param_range} 中"

    return True, ""


def validate_params(params: Dict[str, Any]) -> Tuple[bool, str]:
    """
    验证参数组合是否有效

    Args:
        params (Dict[str, Any]): 参数字典

    Returns:
        Tuple[bool, str]: (是否有效, 错误信息)
    """
    # 检查必要参数是否存在
    required_params = ['temperature', 'num_games']
    for param in required_params:
        if param not in params:
            return False, f"缺少必要参数: {param}"

    # 检查参数值是否在合理范围内
    if params.get('temperature', 1.0) <= 0:
        return False, "温度参数必须大于0"

    if params.get('num_games', 10) <= 0:
        return False, "游戏数量必须大于0"

    if params.get('batch_size', 32) <= 0:
        return False, "批量大小必须大于0"

    if params.get('learning_rate', 0.001) <= 0:
        return False, "学习率必须大于0"

    if params.get('gamma', 0.99) <= 0 or params.get('gamma', 0.99) >= 1:
        return False, "折扣因子必须在(0,1)范围内"

    # 检查参数是否在预定义的范围内
    for param_name, value in params.items():
        is_in_range, error_msg = is_param_in_range(param_name, value)
        if not is_in_range:
            return False, error_msg

    # 检查参数组合是否合理
    if params.get('parallel', False) and params.get('num_games', 10) < 5:
        return False, "并行模式下游戏数量应不少于5"

    return True, ""


def generate_random_params() -> Dict[str, Any]:
    """
    生成随机训练参数

    从预定义的参数范围中随机生成一组训练参数，并确保参数组合有效

    Returns:
        Dict[str, Any]: 随机生成的参数字典
    """
    max_attempts = 10  # 最大尝试次数

    for attempt in range(max_attempts):
        params = {}

        # 从参数范围中随机生成参数
        for param_name, param_range in RANDOM_PARAM_RANGES.items():
            if isinstance(param_range, tuple) and len(param_range) == 2:
                # 范围参数
                min_val, max_val = param_range
                if isinstance(min_val, int) and isinstance(max_val, int):
                    # 整数范围
                    params[param_name] = random.randint(min_val, max_val)
                else:
                    # 浮点数范围
                    params[param_name] = random.uniform(min_val, max_val)
            elif isinstance(param_range, list):
                # 列表选项
                params[param_name] = random.choice(param_range)
            else:
                raise ValueError(f"不支持的参数范围格式: {param_range}")

        # 验证参数组合是否有效
        is_valid, error_msg = validate_params(params)
        if is_valid:
            return params

        # 如果是最后一次尝试，记录错误信息
        if attempt == max_attempts - 1:
            print(f"警告: 生成有效参数组合失败，使用默认参数。错误: {error_msg}")

    # 如果多次尝试后仍未生成有效参数，使用默认参数
    return {
        'temperature': 1.0,
        'num_games': 10,
        'batch_size': 64,
        'learning_rate': 0.001,
        'gamma': 0.99,
        'save_experiences': False,
        'parallel': False
    }


def train_with_params(params: Dict[str, Any], logger: logging.Logger) -> Dict[str, Any]:
    """
    使用指定参数进行训练

    Args:
        params (Dict[str, Any]): 训练参数
        logger (logging.Logger): 日志记录器

    Returns:
        Dict[str, Any]: 训练结果和统计数据
    """
    # 记录开始时间
    start_time = time.time()

    # 记录内存使用情况
    memory_before = psutil.Process(os.getpid()).memory_info().rss / 1024 / 1024
    logger.info(f"训练前内存使用: {memory_before:.2f} MB")

    # 创建环境
    env_seed = random.randint(1, 10000)
    try:
        env = DouDizhuEnvironment(seed=env_seed)
        logger.info(f"环境创建成功，种子: {env_seed}")
    except Exception as e:
        logger.error(f"环境创建失败: {str(e)}")
        return {
            'success': False,
            'error': f"环境创建失败: {str(e)}",
            'elapsed_time': time.time() - start_time
        }

    # 创建随机代理
    agent_seed = random.randint(1, 10000)
    try:
        agent = RandomAgent(seed=agent_seed)
        logger.info(f"代理创建成功，种子: {agent_seed}")
    except Exception as e:
        logger.error(f"代理创建失败: {str(e)}")
        return {
            'success': False,
            'error': f"代理创建失败: {str(e)}",
            'elapsed_time': time.time() - start_time
        }

    # 创建自我对弈对象
    save_path = 'models/random_param_training'
    os.makedirs(save_path, exist_ok=True)
    try:
        self_play = FixedDouDizhuSelfPlay(save_path=save_path)
        logger.info(f"自我对弈对象创建成功，保存路径: {save_path}")
    except Exception as e:
        logger.error(f"自我对弈对象创建失败: {str(e)}")
        return {
            'success': False,
            'error': f"自我对弈对象创建失败: {str(e)}",
            'elapsed_time': time.time() - start_time
        }

    # 提取参数
    num_games = params.get('num_games', 10)
    temperature = params.get('temperature', 1.0)
    batch_size = params.get('batch_size', 64)
    learning_rate = params.get('learning_rate', 0.001)
    gamma = params.get('gamma', 0.99)
    # 注意：DouDizhuSelfPlay类中的save_experiences方法不存在，所以我们总是将save设置为False
    save_experiences = params.get('save_experiences', False)
    parallel = params.get('parallel', False)

    # 记录参数
    logger.info(f"训练参数:")
    for key, value in params.items():
        formatted_value = format_param_value(key, value)
        logger.info(f"  {key}: {formatted_value}")

    logger.info(f"环境种子: {env_seed}, 代理种子: {agent_seed}")

    # 生成经验
    logger.info(f"开始生成经验，游戏数: {num_games}")

    # 记录开始时间
    generation_start_time = time.time()

    try:
        # 创建叫地主阶段处理器（如果需要）
        bidding_handler = None
        if params.get('use_bidding_handler', False):
            from cardgame_ai.training.bidding_phase_handler import BiddingPhaseHandler
            bidding_handler = BiddingPhaseHandler()
            logger.info("使用叫地主阶段处理器")

        # 生成经验
        experiences = self_play.generate_experience(
            env=env,
            agent=agent,
            num_games=num_games,
            temperature=temperature,
            save=save_experiences,
            parallel=parallel,
            bidding_handler=bidding_handler
        )

        # 记录经验生成完成
        generation_elapsed_time = time.time() - generation_start_time
        logger.info(f"经验生成完成，共 {len(experiences)} 个经验，耗时 {generation_elapsed_time:.2f}s")

        # 记录每局游戏平均经验数
        avg_exp_per_game = len(experiences) / num_games if num_games > 0 else 0
        logger.info(f"平均每局游戏经验数: {avg_exp_per_game:.2f}")

        # 收集统计数据
        stats_start_time = time.time()
        stats = collect_statistics(experiences)
        stats_elapsed_time = time.time() - stats_start_time
        logger.info(f"统计数据收集完成，耗时 {stats_elapsed_time:.2f}s")

        # 记录内存使用情况
        memory_after = psutil.Process(os.getpid()).memory_info().rss / 1024 / 1024
        memory_used = memory_after - memory_before
        logger.info(f"训练后内存使用: {memory_after:.2f} MB (增加: {memory_used:.2f} MB)")

        # 返回结果
        total_elapsed_time = time.time() - start_time
        return {
            'success': True,
            'num_experiences': len(experiences),
            'elapsed_time': total_elapsed_time,
            'generation_time': generation_elapsed_time,
            'stats_time': stats_elapsed_time,
            'stats': stats,
            'env_seed': env_seed,
            'agent_seed': agent_seed,
            'memory_before': memory_before,
            'memory_after': memory_after,
            'memory_used': memory_used,
            'avg_exp_per_game': avg_exp_per_game,
            'params': params  # 添加参数到结果中，方便后续分析
        }

    except Exception as e:
        # 记录错误
        logger.error(f"训练过程中出现错误: {str(e)}", exc_info=True)

        # 记录内存使用情况
        memory_after = psutil.Process(os.getpid()).memory_info().rss / 1024 / 1024
        memory_used = memory_after - memory_before

        # 返回错误结果
        return {
            'success': False,
            'error': str(e),
            'error_type': type(e).__name__,
            'elapsed_time': time.time() - start_time,
            'env_seed': env_seed,
            'agent_seed': agent_seed,
            'memory_before': memory_before,
            'memory_after': memory_after,
            'memory_used': memory_used,
            'params': params  # 添加参数到结果中，方便后续分析
        }


def collect_statistics(experiences: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    收集统计数据

    Args:
        experiences (List[Dict[str, Any]]): 经验数据列表

    Returns:
        Dict[str, Any]: 统计数据
    """
    stats = {
        'phase_counts': defaultdict(int),  # 各阶段的次数
        'bid_actions': defaultdict(int),   # 叫地主动作的次数
        'grab_actions': defaultdict(int),  # 抢地主动作的次数
        'play_actions': defaultdict(int),  # 出牌阶段动作的次数
        'bid_rewards': defaultdict(list),  # 叫地主动作的奖励
        'grab_rewards': defaultdict(list), # 抢地主动作的奖励
        'play_rewards': defaultdict(list), # 出牌阶段动作的奖励
        'phase_transitions': defaultdict(int),  # 阶段转换的次数
        'landlord_win_rate': 0.0,          # 地主胜率
        'farmer_win_rate': 0.0,            # 农民胜率
        'avg_game_length': 0.0,            # 平均游戏长度
        'max_game_length': 0,              # 最长游戏长度
        'min_game_length': float('inf'),   # 最短游戏长度
        'total_reward': 0.0,               # 总奖励
        'avg_reward': 0.0,                 # 平均奖励
        'positive_reward_count': 0,        # 正奖励次数
        'negative_reward_count': 0,        # 负奖励次数
        'zero_reward_count': 0,            # 零奖励次数
    }

    # 游戏计数和长度
    game_count = 0
    game_lengths = []
    current_game_length = 0
    landlord_wins = 0
    farmer_wins = 0

    # 上一个阶段
    last_phase = None

    # 当前游戏的玩家角色
    current_game_roles = {}

    # 记录每个玩家的奖励
    player_rewards = defaultdict(list)

    for exp in experiences:
        state = exp['state']
        action = exp['action']
        reward = exp['reward']
        next_state = exp['next_state']
        done = exp['done']

        # 记录总奖励和奖励分布
        stats['total_reward'] += reward
        if reward > 0:
            stats['positive_reward_count'] += 1
        elif reward < 0:
            stats['negative_reward_count'] += 1
        else:
            stats['zero_reward_count'] += 1

        # 记录阶段
        current_phase = state.game_phase
        stats['phase_counts'][current_phase.name] += 1

        # 记录阶段转换
        if last_phase is not None and last_phase != current_phase:
            transition = f"{last_phase.name}_to_{current_phase.name}"
            stats['phase_transitions'][transition] += 1

        last_phase = current_phase

        # 记录动作和奖励
        if current_phase == GamePhase.BIDDING:
            action_name = action.name if hasattr(action, 'name') else str(action)
            stats['bid_actions'][action_name] += 1
            stats['bid_rewards'][action_name].append(reward)

        elif current_phase == GamePhase.GRABBING:
            action_name = action.name if hasattr(action, 'name') else str(action)
            stats['grab_actions'][action_name] += 1
            stats['grab_rewards'][action_name].append(reward)

        elif current_phase == GamePhase.PLAYING:
            # 对于出牌阶段，记录动作类型（如单牌、对子、顺子等）
            if hasattr(action, 'get_type') and callable(action.get_type):
                action_type = action.get_type()
                stats['play_actions'][action_type] += 1
                stats['play_rewards'][action_type].append(reward)
            else:
                action_str = str(action)
                stats['play_actions'][action_str] += 1
                stats['play_rewards'][action_str].append(reward)

            # 记录当前玩家的角色（地主或农民）
            current_player = state.current_player
            if state.landlord is not None:
                role = "landlord" if current_player == state.landlord else "farmer"
                current_game_roles[current_player] = role
                player_rewards[role].append(reward)

        # 记录游戏长度
        current_game_length += 1

        # 如果游戏结束，更新统计数据
        if done:
            game_count += 1
            game_lengths.append(current_game_length)

            # 更新最长和最短游戏长度
            stats['max_game_length'] = max(stats['max_game_length'], current_game_length)
            stats['min_game_length'] = min(stats['min_game_length'], current_game_length)

            current_game_length = 0

            # 记录地主胜负
            if next_state.landlord is not None:
                payoffs = next_state.get_payoffs()
                if payoffs[next_state.landlord] > 0:
                    landlord_wins += 1
                else:
                    farmer_wins += 1

    # 计算平均值
    if game_count > 0:
        stats['landlord_win_rate'] = landlord_wins / game_count
        stats['farmer_win_rate'] = farmer_wins / game_count
        stats['avg_game_length'] = sum(game_lengths) / game_count

    # 如果没有游戏，设置最短游戏长度为0
    if stats['min_game_length'] == float('inf'):
        stats['min_game_length'] = 0

    # 计算平均奖励
    for action, rewards in stats['bid_rewards'].items():
        if rewards:
            stats['bid_rewards'][action] = sum(rewards) / len(rewards)

    for action, rewards in stats['grab_rewards'].items():
        if rewards:
            stats['grab_rewards'][action] = sum(rewards) / len(rewards)

    for action, rewards in stats['play_rewards'].items():
        if rewards:
            stats['play_rewards'][action] = sum(rewards) / len(rewards)

    # 计算平均奖励
    if len(experiences) > 0:
        stats['avg_reward'] = stats['total_reward'] / len(experiences)

    # 计算角色平均奖励
    stats['role_rewards'] = {}
    for role, rewards in player_rewards.items():
        if rewards:
            stats['role_rewards'][role] = sum(rewards) / len(rewards)

    # 计算游戏长度标准差
    if game_lengths:
        mean_length = stats['avg_game_length']
        variance = sum((length - mean_length) ** 2 for length in game_lengths) / len(game_lengths)
        stats['game_length_std'] = variance ** 0.5
    else:
        stats['game_length_std'] = 0.0

    return stats


def print_statistics(stats: Dict[str, Any], trial_num: int = None):
    """
    打印统计数据

    Args:
        stats (Dict[str, Any]): 统计数据
        trial_num (int, optional): 训练试验编号. Defaults to None.
    """
    # 创建标题
    title = "统计数据"
    if trial_num is not None:
        title = f"训练 {trial_num} 统计数据"

    # 打印标题
    print(f"\n{'-' * 20} {title} {'-' * 20}")

    # 打印阶段计数
    print("\n阶段计数:")
    for phase, count in stats.get('phase_counts', {}).items():
        print(f"  {phase}: {count}")

    # 打印叫地主动作
    print("\n叫地主动作:")
    for action, count in stats.get('bid_actions', {}).items():
        avg_reward = stats.get('bid_rewards', {}).get(action, 0.0)
        print(f"  {action}: {count} 次, 平均奖励: {avg_reward:.4f}")

    # 打印抢地主动作
    print("\n抢地主动作:")
    for action, count in stats.get('grab_actions', {}).items():
        avg_reward = stats.get('grab_rewards', {}).get(action, 0.0)
        print(f"  {action}: {count} 次, 平均奖励: {avg_reward:.4f}")

    # 打印出牌阶段动作（仅显示前5种最常见的动作类型）
    if stats.get('play_actions'):
        print("\n出牌阶段动作 (前5种最常见):")
        sorted_actions = sorted(stats['play_actions'].items(), key=lambda x: x[1], reverse=True)[:5]
        for action, count in sorted_actions:
            avg_reward = stats.get('play_rewards', {}).get(action, 0.0)
            print(f"  {action}: {count} 次, 平均奖励: {avg_reward:.4f}")

    # 打印阶段转换
    print("\n阶段转换:")
    for transition, count in stats.get('phase_transitions', {}).items():
        print(f"  {transition}: {count} 次")

    # 打印游戏统计数据
    print("\n游戏统计数据:")
    print(f"  地主胜率: {stats.get('landlord_win_rate', 0.0):.4f}")
    print(f"  农民胜率: {stats.get('farmer_win_rate', 0.0):.4f}")
    print(f"  平均游戏长度: {stats.get('avg_game_length', 0.0):.2f} (标准差: {stats.get('game_length_std', 0.0):.2f})")
    print(f"  最短游戏长度: {stats.get('min_game_length', 0)}")
    print(f"  最长游戏长度: {stats.get('max_game_length', 0)}")

    # 打印奖励统计数据
    print("\n奖励统计数据:")
    print(f"  总奖励: {stats.get('total_reward', 0.0):.2f}")
    print(f"  平均奖励: {stats.get('avg_reward', 0.0):.4f}")
    print(f"  正奖励次数: {stats.get('positive_reward_count', 0)}")
    print(f"  负奖励次数: {stats.get('negative_reward_count', 0)}")
    print(f"  零奖励次数: {stats.get('zero_reward_count', 0)}")

    # 打印角色奖励
    if stats.get('role_rewards'):
        print("\n角色平均奖励:")
        for role, reward in stats['role_rewards'].items():
            print(f"  {role}: {reward:.4f}")

    print(f"{'-' * 50}\n")


def analyze_results(results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    分析训练结果

    Args:
        results (List[Dict[str, Any]]): 训练结果列表

    Returns:
        Dict[str, Any]: 分析结果
    """
    # 初始化分析结果
    analysis = {
        'total_trials': len(results),
        'successful_trials': 0,
        'failed_trials': 0,
        'avg_elapsed_time': 0.0,
        'avg_num_experiences': 0.0,
        'avg_landlord_win_rate': 0.0,
        'avg_farmer_win_rate': 0.0,
        'avg_game_length': 0.0,
        'avg_game_length_std': 0.0,
        'min_game_length': float('inf'),
        'max_game_length': 0,
        'avg_memory_usage': 0.0,
        'avg_reward': 0.0,
        'best_params': None,
        'worst_params': None,
        'fastest_params': None,
        'slowest_params': None,
        'best_landlord_win_rate': 0.0,
        'worst_landlord_win_rate': 1.0,
        'fastest_time': float('inf'),
        'slowest_time': 0.0,
        'common_errors': defaultdict(int),
        'parameter_correlations': {},
        'phase_distribution': defaultdict(float)
    }

    # 统计成功和失败的试验
    successful_trials = [r for r in results if r.get('success', False)]
    failed_trials = [r for r in results if not r.get('success', False)]

    analysis['successful_trials'] = len(successful_trials)
    analysis['failed_trials'] = len(failed_trials)

    # 计算成功率
    if results:
        analysis['success_rate'] = analysis['successful_trials'] / len(results)
    else:
        analysis['success_rate'] = 0.0

    # 统计错误
    for trial in failed_trials:
        error = trial.get('error', 'Unknown error')
        error_type = trial.get('error_type', 'Unknown')
        analysis['common_errors'][f"{error_type}: {error}"] += 1

    # 计算平均值
    if successful_trials:
        # 基本统计
        analysis['avg_elapsed_time'] = sum(r.get('elapsed_time', 0.0) for r in successful_trials) / len(successful_trials)
        analysis['avg_num_experiences'] = sum(r.get('num_experiences', 0) for r in successful_trials) / len(successful_trials)

        # 内存使用统计
        memory_usages = [r.get('memory_used', 0.0) for r in successful_trials if 'memory_used' in r]
        if memory_usages:
            analysis['avg_memory_usage'] = sum(memory_usages) / len(memory_usages)
            analysis['max_memory_usage'] = max(memory_usages)
            analysis['min_memory_usage'] = min(memory_usages)

        # 游戏统计
        landlord_win_rates = [r.get('stats', {}).get('landlord_win_rate', 0.0) for r in successful_trials]
        farmer_win_rates = [r.get('stats', {}).get('farmer_win_rate', 0.0) for r in successful_trials]
        game_lengths = [r.get('stats', {}).get('avg_game_length', 0.0) for r in successful_trials]
        game_length_stds = [r.get('stats', {}).get('game_length_std', 0.0) for r in successful_trials]
        min_game_lengths = [r.get('stats', {}).get('min_game_length', float('inf')) for r in successful_trials]
        max_game_lengths = [r.get('stats', {}).get('max_game_length', 0) for r in successful_trials]

        # 奖励统计
        rewards = [r.get('stats', {}).get('avg_reward', 0.0) for r in successful_trials]
        positive_rewards = [r.get('stats', {}).get('positive_reward_count', 0) for r in successful_trials]
        negative_rewards = [r.get('stats', {}).get('negative_reward_count', 0) for r in successful_trials]
        zero_rewards = [r.get('stats', {}).get('zero_reward_count', 0) for r in successful_trials]

        # 计算平均值
        if landlord_win_rates:
            analysis['avg_landlord_win_rate'] = sum(landlord_win_rates) / len(landlord_win_rates)

        if farmer_win_rates:
            analysis['avg_farmer_win_rate'] = sum(farmer_win_rates) / len(farmer_win_rates)

        if game_lengths:
            analysis['avg_game_length'] = sum(game_lengths) / len(game_lengths)

        if game_length_stds:
            analysis['avg_game_length_std'] = sum(game_length_stds) / len(game_length_stds)

        if min_game_lengths:
            analysis['min_game_length'] = min(min_game_lengths)

        if max_game_lengths:
            analysis['max_game_length'] = max(max_game_lengths)

        if rewards:
            analysis['avg_reward'] = sum(rewards) / len(rewards)

        if positive_rewards and negative_rewards and zero_rewards:
            total_rewards = sum(positive_rewards) + sum(negative_rewards) + sum(zero_rewards)
            if total_rewards > 0:
                analysis['positive_reward_ratio'] = sum(positive_rewards) / total_rewards
                analysis['negative_reward_ratio'] = sum(negative_rewards) / total_rewards
                analysis['zero_reward_ratio'] = sum(zero_rewards) / total_rewards

        # 阶段分布统计
        phase_counts = defaultdict(int)
        total_phases = 0

        for trial in successful_trials:
            stats = trial.get('stats', {})
            phase_distribution = stats.get('phase_counts', {})

            for phase, count in phase_distribution.items():
                phase_counts[phase] += count
                total_phases += count

        if total_phases > 0:
            for phase, count in phase_counts.items():
                analysis['phase_distribution'][phase] = count / total_phases

        # 找出最佳和最差的参数
        for trial in successful_trials:
            # 胜率相关
            win_rate = trial.get('stats', {}).get('landlord_win_rate', 0.0)
            if win_rate > analysis['best_landlord_win_rate']:
                analysis['best_landlord_win_rate'] = win_rate
                analysis['best_params'] = trial.get('params', {})

            if win_rate < analysis['worst_landlord_win_rate']:
                analysis['worst_landlord_win_rate'] = win_rate
                analysis['worst_params'] = trial.get('params', {})

            # 时间相关
            elapsed_time = trial.get('elapsed_time', 0.0)
            if elapsed_time < analysis['fastest_time']:
                analysis['fastest_time'] = elapsed_time
                analysis['fastest_params'] = trial.get('params', {})

            if elapsed_time > analysis['slowest_time']:
                analysis['slowest_time'] = elapsed_time
                analysis['slowest_params'] = trial.get('params', {})

        # 计算参数与性能指标的相关性
        if len(successful_trials) >= 3:  # 至少需要3个样本才能计算相关性
            # 提取所有参数名称
            param_names = set()
            for trial in successful_trials:
                param_names.update(trial.get('params', {}).keys())

            # 计算每个参数与性能指标的相关性
            for param_name in param_names:
                # 提取参数值和性能指标
                param_values = []
                win_rates = []
                times = []

                for trial in successful_trials:
                    params = trial.get('params', {})
                    if param_name in params:
                        param_value = params[param_name]
                        # 只处理数值类型参数
                        if isinstance(param_value, (int, float)):
                            param_values.append(param_value)
                            win_rates.append(trial.get('stats', {}).get('landlord_win_rate', 0.0))
                            times.append(trial.get('elapsed_time', 0.0))

                # 如果有足够的数据，计算相关性
                if len(param_values) >= 3:
                    # 计算参数与胜率的相关性
                    win_rate_corr = calculate_correlation(param_values, win_rates)
                    # 计算参数与训练时间的相关性
                    time_corr = calculate_correlation(param_values, times)

                    analysis['parameter_correlations'][param_name] = {
                        'win_rate_correlation': win_rate_corr,
                        'time_correlation': time_corr
                    }

    return analysis


def calculate_correlation(x: List[float], y: List[float]) -> float:
    """
    计算两个列表的相关系数

    Args:
        x (List[float]): 第一个列表
        y (List[float]): 第二个列表

    Returns:
        float: 相关系数
    """
    if len(x) != len(y) or len(x) < 2:
        return 0.0

    n = len(x)

    # 计算均值
    mean_x = sum(x) / n
    mean_y = sum(y) / n

    # 计算协方差和标准差
    cov = sum((x[i] - mean_x) * (y[i] - mean_y) for i in range(n))
    std_x = (sum((val - mean_x) ** 2 for val in x)) ** 0.5
    std_y = (sum((val - mean_y) ** 2 for val in y)) ** 0.5

    # 避免除以零
    if std_x == 0 or std_y == 0:
        return 0.0

    # 计算相关系数
    return cov / (std_x * std_y)


def detect_bugs(results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    检测训练过程中的bug

    Args:
        results (List[Dict[str, Any]]): 训练结果列表

    Returns:
        List[Dict[str, Any]]: 检测到的bug列表
    """
    bugs = []

    # 检查失败的训练
    for i, result in enumerate(results):
        if not result.get('success', False):
            error = result.get('error', 'Unknown error')
            error_type = result.get('error_type', 'Unknown')

            # 根据错误类型提供更具体的建议
            possible_cause = '可能是参数设置不当或代码逻辑错误'
            fix_suggestion = '检查参数范围和训练代码'

            if 'ImportError' in error_type or 'ModuleNotFoundError' in error_type:
                possible_cause = '缺少必要的模块或导入路径错误'
                fix_suggestion = '检查导入语句和PYTHONPATH设置，确保所有依赖已安装'
            elif 'AttributeError' in error_type:
                possible_cause = '访问了不存在的属性或方法'
                fix_suggestion = '检查对象是否有该属性或方法，可能需要更新类定义'
            elif 'TypeError' in error_type:
                possible_cause = '类型错误，可能是参数类型不匹配'
                fix_suggestion = '检查函数参数类型，确保与期望类型一致'
            elif 'ValueError' in error_type:
                possible_cause = '参数值错误，可能超出了有效范围'
                fix_suggestion = '检查参数值是否在有效范围内'
            elif 'IndexError' in error_type or 'KeyError' in error_type:
                possible_cause = '访问了不存在的索引或键'
                fix_suggestion = '检查数据结构的索引或键是否存在'
            elif 'MemoryError' in error_type:
                possible_cause = '内存不足，可能是数据量过大或内存泄漏'
                fix_suggestion = '减少数据量或检查是否有内存泄漏'

            bugs.append({
                'id': len(bugs) + 1,
                'trial': i + 1,
                'type': 'training_failure',
                'description': f"训练失败: {error}",
                'severity': 'high',
                'error_type': error_type,
                'possible_cause': possible_cause,
                'fix_suggestion': fix_suggestion
            })

    # 检查异常的训练结果
    successful_trials = [r for r in results if r.get('success', False)]

    if successful_trials:
        # 计算平均值和标准差
        elapsed_times = [r.get('elapsed_time', 0.0) for r in successful_trials]
        num_experiences = [r.get('num_experiences', 0) for r in successful_trials]
        landlord_win_rates = [r.get('stats', {}).get('landlord_win_rate', 0.0) for r in successful_trials]
        farmer_win_rates = [r.get('stats', {}).get('farmer_win_rate', 0.0) for r in successful_trials]
        game_lengths = [r.get('stats', {}).get('avg_game_length', 0.0) for r in successful_trials]
        game_length_stds = [r.get('stats', {}).get('game_length_std', 0.0) for r in successful_trials]
        rewards = [r.get('stats', {}).get('avg_reward', 0.0) for r in successful_trials]
        positive_rewards = [r.get('stats', {}).get('positive_reward_count', 0) for r in successful_trials]
        negative_rewards = [r.get('stats', {}).get('negative_reward_count', 0) for r in successful_trials]
        zero_rewards = [r.get('stats', {}).get('zero_reward_count', 0) for r in successful_trials]

        avg_elapsed_time = sum(elapsed_times) / len(elapsed_times) if elapsed_times else 0.0
        avg_num_experiences = sum(num_experiences) / len(num_experiences) if num_experiences else 0.0
        avg_landlord_win_rate = sum(landlord_win_rates) / len(landlord_win_rates) if landlord_win_rates else 0.0
        avg_farmer_win_rate = sum(farmer_win_rates) / len(farmer_win_rates) if farmer_win_rates else 0.0
        avg_game_length = sum(game_lengths) / len(game_lengths) if game_lengths else 0.0
        avg_game_length_std = sum(game_length_stds) / len(game_length_stds) if game_length_stds else 0.0
        avg_reward = sum(rewards) / len(rewards) if rewards else 0.0

        # 检查异常值
        for i, result in enumerate(successful_trials):
            trial_num = result.get('trial', i + 1)

            # 检查训练时间异常
            elapsed_time = result.get('elapsed_time', 0.0)
            if elapsed_time > avg_elapsed_time * 2:
                bugs.append({
                    'id': len(bugs) + 1,
                    'trial': trial_num,
                    'type': 'performance_issue',
                    'description': f"训练时间异常: {elapsed_time:.2f}s (平均: {avg_elapsed_time:.2f}s)",
                    'severity': 'medium',
                    'possible_cause': '可能是参数设置导致训练变慢或系统资源不足',
                    'fix_suggestion': '检查参数设置，特别是游戏数量和并行设置'
                })

            # 检查经验数量异常
            num_exp = result.get('num_experiences', 0)
            if num_exp < avg_num_experiences * 0.5:
                bugs.append({
                    'id': len(bugs) + 1,
                    'trial': trial_num,
                    'type': 'data_issue',
                    'description': f"经验数量异常: {num_exp} (平均: {avg_num_experiences:.2f})",
                    'severity': 'medium',
                    'possible_cause': '可能是游戏提前结束或经验收集逻辑有问题',
                    'fix_suggestion': '检查游戏逻辑和经验收集代码'
                })

            # 检查地主胜率异常
            win_rate = result.get('stats', {}).get('landlord_win_rate', 0.0)
            if win_rate == 0.0 or win_rate == 1.0:
                bugs.append({
                    'id': len(bugs) + 1,
                    'trial': trial_num,
                    'type': 'logic_issue',
                    'description': f"地主胜率异常: {win_rate:.2f}",
                    'severity': 'high',
                    'possible_cause': '可能是游戏逻辑有问题或随机代理策略不平衡',
                    'fix_suggestion': '检查游戏规则和代理策略'
                })

            # 检查游戏长度异常
            game_length = result.get('stats', {}).get('avg_game_length', 0.0)
            if game_length < avg_game_length * 0.5 or game_length > avg_game_length * 2:
                bugs.append({
                    'id': len(bugs) + 1,
                    'trial': trial_num,
                    'type': 'logic_issue',
                    'description': f"游戏长度异常: {game_length:.2f} (平均: {avg_game_length:.2f})",
                    'severity': 'medium',
                    'possible_cause': '可能是游戏逻辑有问题或随机代理策略导致游戏过短/过长',
                    'fix_suggestion': '检查游戏规则和代理策略'
                })

            # 检查游戏长度标准差异常
            game_length_std = result.get('stats', {}).get('game_length_std', 0.0)
            if game_length_std > avg_game_length:
                bugs.append({
                    'id': len(bugs) + 1,
                    'trial': trial_num,
                    'type': 'stability_issue',
                    'description': f"游戏长度标准差异常: {game_length_std:.2f} (平均长度: {avg_game_length:.2f})",
                    'severity': 'low',
                    'possible_cause': '游戏长度不稳定，可能是随机性过大或游戏规则不一致',
                    'fix_suggestion': '检查游戏规则的一致性和随机性'
                })

            # 检查奖励分布异常
            avg_reward = result.get('stats', {}).get('avg_reward', 0.0)
            if abs(avg_reward) > 10.0:  # 假设正常奖励范围在[-10, 10]
                bugs.append({
                    'id': len(bugs) + 1,
                    'trial': trial_num,
                    'type': 'reward_issue',
                    'description': f"平均奖励异常: {avg_reward:.4f}",
                    'severity': 'medium',
                    'possible_cause': '奖励设置可能不合理或奖励计算有误',
                    'fix_suggestion': '检查奖励函数和奖励计算逻辑'
                })

            # 检查奖励分布不平衡
            positive_count = result.get('stats', {}).get('positive_reward_count', 0)
            negative_count = result.get('stats', {}).get('negative_reward_count', 0)
            zero_count = result.get('stats', {}).get('zero_reward_count', 0)
            total_count = positive_count + negative_count + zero_count

            if total_count > 0:
                positive_ratio = positive_count / total_count
                negative_ratio = negative_count / total_count
                zero_ratio = zero_count / total_count

                # 检查奖励是否过于集中在某一类型
                if positive_ratio > 0.9 or negative_ratio > 0.9 or zero_ratio > 0.9:
                    dominant_type = "正奖励" if positive_ratio > 0.9 else ("负奖励" if negative_ratio > 0.9 else "零奖励")
                    bugs.append({
                        'id': len(bugs) + 1,
                        'trial': trial_num,
                        'type': 'reward_distribution_issue',
                        'description': f"奖励分布不平衡: {dominant_type}占比{max(positive_ratio, negative_ratio, zero_ratio):.2%}",
                        'severity': 'medium',
                        'possible_cause': '奖励函数可能设计不合理，导致奖励分布过于集中',
                        'fix_suggestion': '重新设计奖励函数，使奖励分布更加均衡'
                    })

    return bugs


def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='斗地主随机参数训练')

    # 基本参数
    parser.add_argument('--num_trials', type=int, default=5, help='训练次数')
    parser.add_argument('--log_dir', type=str, default='logs', help='日志目录')
    parser.add_argument('--verbose', action='store_true', help='是否输出详细信息')
    parser.add_argument('--seed', type=int, default=None, help='随机种子')

    # 输出参数
    parser.add_argument('--output_file', type=str, default=None,
                        help='分析结果输出文件路径，默认不输出到文件')
    parser.add_argument('--save_results', action='store_true',
                        help='是否保存每次训练的详细结果')
    parser.add_argument('--results_dir', type=str, default='results',
                        help='训练结果保存目录，仅在--save_results为True时有效')

    # 训练控制参数
    parser.add_argument('--max_time', type=int, default=0,
                        help='最大训练时间(秒)，0表示不限制')
    parser.add_argument('--continue_on_error', action='store_true',
                        help='出错时是否继续训练')
    parser.add_argument('--show_progress', action='store_true',
                        help='是否显示训练进度')

    # 参数范围调整
    parser.add_argument('--min_games', type=int, default=None,
                        help='最小游戏数量，覆盖默认参数范围')
    parser.add_argument('--max_games', type=int, default=None,
                        help='最大游戏数量，覆盖默认参数范围')
    parser.add_argument('--min_temp', type=float, default=None,
                        help='最小温度参数，覆盖默认参数范围')
    parser.add_argument('--max_temp', type=float, default=None,
                        help='最大温度参数，覆盖默认参数范围')

    args = parser.parse_args()

    # 设置日志
    logger = setup_logger(args.log_dir)
    logger.info("开始斗地主随机参数训练")
    logger.info(f"训练次数: {args.num_trials}")

    # 设置随机种子
    if args.seed is not None:
        random.seed(args.seed)
        np.random.seed(args.seed)
        torch.manual_seed(args.seed)
        logger.info(f"设置随机种子: {args.seed}")

    # 调整参数范围
    if args.min_games is not None or args.max_games is not None or args.min_temp is not None or args.max_temp is not None:
        adjust_param_ranges(
            min_games=args.min_games,
            max_games=args.max_games,
            min_temp=args.min_temp,
            max_temp=args.max_temp
        )
        logger.info("参数范围已调整")

    # 创建结果目录
    if args.save_results:
        os.makedirs(args.results_dir, exist_ok=True)
        logger.info(f"训练结果将保存到: {args.results_dir}")

    # 记录其他参数
    if args.verbose:
        logger.info("详细模式已启用")
    if args.max_time > 0:
        logger.info(f"最大训练时间: {args.max_time}秒")
    if args.continue_on_error:
        logger.info("出错时将继续训练")
    if args.show_progress:
        logger.info("将显示训练进度")
    if args.output_file:
        logger.info(f"分析结果将保存到: {args.output_file}")

    # 存储所有训练结果
    all_results = []

    # 记录开始时间
    start_time = time.time()

    # 创建进度条
    if args.show_progress:
        progress_bar = tqdm(total=args.num_trials, desc="训练进度")

    # 多次训练循环
    for trial in range(args.num_trials):
        logger.info(f"开始第 {trial + 1}/{args.num_trials} 次训练")
        trial_start_time = time.time()

        # 检查是否超过最大训练时间
        if args.max_time > 0 and time.time() - start_time > args.max_time:
            logger.info(f"已达到最大训练时间 {args.max_time}秒，停止训练")
            break

        try:
            # 生成随机参数
            params = generate_random_params()

            # 格式化参数输出
            param_str = ", ".join([f"{k}={format_param_value(k, v)}" for k, v in params.items()])
            logger.info(f"随机参数: {param_str}")

            # 使用参数进行训练
            result = train_with_params(params, logger)

            # 添加参数信息到结果中
            result['params'] = params
            result['trial'] = trial + 1
            result['trial_time'] = time.time() - trial_start_time

            # 添加到结果列表
            all_results.append(result)

            # 打印统计数据
            if args.verbose:
                print_statistics(result.get('stats', {}), trial + 1)

            # 保存训练结果
            if args.save_results:
                result_file = os.path.join(args.results_dir, f"trial_{trial+1}.json")
                try:
                    # 将结果转换为可序列化的格式
                    serializable_result = {}
                    for k, v in result.items():
                        if isinstance(v, (int, float, str, bool, list, dict, type(None))):
                            serializable_result[k] = v
                        else:
                            serializable_result[k] = str(v)

                    with open(result_file, 'w', encoding='utf-8') as f:
                        json.dump(serializable_result, f, ensure_ascii=False, indent=2)
                    logger.info(f"训练结果已保存到: {result_file}")
                except Exception as e:
                    logger.error(f"保存训练结果时出错: {str(e)}")

        except Exception as e:
            logger.error(f"训练过程中出现错误: {str(e)}", exc_info=True)
            error_result = {
                'params': params if 'params' in locals() else {},
                'trial': trial + 1,
                'error': str(e),
                'error_type': type(e).__name__,
                'success': False,
                'trial_time': time.time() - trial_start_time
            }
            all_results.append(error_result)

            # 如果不继续训练，则退出循环
            if not args.continue_on_error:
                logger.info("由于出错且未设置continue_on_error，停止训练")
                break

        # 更新进度条
        if args.show_progress:
            progress_bar.update(1)
            # 计算预计剩余时间
            elapsed = time.time() - start_time
            avg_time_per_trial = elapsed / (trial + 1)
            remaining_trials = args.num_trials - (trial + 1)
            eta = avg_time_per_trial * remaining_trials
            progress_bar.set_postfix({"ETA": f"{eta:.1f}s", "当前耗时": f"{elapsed:.1f}s"})

        # 主动进行垃圾回收
        gc.collect()

    # 关闭进度条
    if args.show_progress:
        progress_bar.close()

    # 记录总训练时间
    total_time = time.time() - start_time
    logger.info(f"总训练时间: {total_time:.2f}秒")

    # 分析结果
    logger.info("开始分析训练结果")
    analysis_result = analyze_results(all_results)
    if analysis_result is None:
        analysis_result = {}

    # 检测bug
    logger.info("开始检测bug")
    bugs = detect_bugs(all_results)
    if bugs is None:
        bugs = []

    # 输出分析结果
    logger.info("训练结果分析:")
    for key, value in analysis_result.items():
        if key == 'common_errors':
            logger.info(f"  常见错误:")
            for error, count in value.items():
                logger.info(f"    {error}: {count} 次")
        elif key in ['best_params', 'worst_params']:
            logger.info(f"  {key}:")
            if value:
                for param_key, param_value in value.items():
                    logger.info(f"    {param_key}: {param_value}")
            else:
                logger.info(f"    无")
        else:
            logger.info(f"  {key}: {value}")

    # 输出检测到的bug
    if bugs:
        logger.info(f"检测到 {len(bugs)} 个bug:")
        for i, bug in enumerate(bugs):
            logger.info(f"  Bug {i+1}: {bug['description']}")
            logger.info(f"    严重程度: {bug['severity']}")
            logger.info(f"    可能原因: {bug['possible_cause']}")
            logger.info(f"    建议修复: {bug['fix_suggestion']}")
    else:
        logger.info("未检测到bug")

    # 保存分析结果和bug到文件
    if args.output_file:
        try:
            output_data = {
                'analysis': analysis_result,
                'bugs': bugs,
                'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'num_trials': args.num_trials,
                'total_time': total_time,
                'successful_trials': len([r for r in all_results if r.get('success', False)]),
                'failed_trials': len([r for r in all_results if not r.get('success', False)])
            }

            # 确保输出目录存在
            output_dir = os.path.dirname(args.output_file)
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)

            with open(args.output_file, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)
            logger.info(f"分析结果已保存到: {args.output_file}")
        except Exception as e:
            logger.error(f"保存分析结果时出错: {str(e)}")

    # 输出总结
    logger.info("训练总结:")
    logger.info(f"  总训练次数: {args.num_trials}")
    logger.info(f"  成功训练次数: {len([r for r in all_results if r.get('success', False)])}")
    logger.info(f"  失败训练次数: {len([r for r in all_results if not r.get('success', False)])}")
    logger.info(f"  总训练时间: {total_time:.2f}秒")
    logger.info(f"  平均每次训练时间: {total_time / len(all_results) if all_results else 0:.2f}秒")
    logger.info(f"  检测到的bug数量: {len(bugs)}")

    logger.info("斗地主随机参数训练完成")


if __name__ == "__main__":
    main()
