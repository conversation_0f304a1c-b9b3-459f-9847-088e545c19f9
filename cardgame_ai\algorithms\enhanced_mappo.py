"""
增强版多智能体近端策略优化算法模块

实现增强版多智能体近端策略优化(Enhanced MAPPO)算法，优化了角色特定的策略网络、
中心化批评家网络和信用分配机制，提高其在斗地主多智能体环境中的性能。
"""
import os
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from typing import Dict, List, Tuple, Any, Optional, Union

from cardgame_ai.core.base import State, Action, Experience, Batch
from cardgame_ai.core.algorithm import PolicyBasedAlgorithm
from cardgame_ai.core.environment import MultiAgentEnvironment
from cardgame_ai.algorithms.mappo import MAPPO, MAPPONetwork


class MultiHeadCritic(nn.Module):
    """
    多头批评家网络

    实现多头批评家网络，每个头关注不同的价值组件，
    如全局价值、角色特定价值、团队价值等。
    """

    def __init__(
        self,
        input_dim: int,
        hidden_dims: List[int] = [256, 128],
        num_heads: int = 3,
        head_dims: Optional[List[int]] = None
    ):
        """
        初始化多头批评家网络

        Args:
            input_dim: 输入维度（全局状态维度）
            hidden_dims: 共享特征提取层的隐藏层维度
            num_heads: 批评家头数
            head_dims: 每个头的隐藏层维度，默认与共享层相同
        """
        super(MultiHeadCritic, self).__init__()

        self.num_heads = num_heads

        # 共享特征提取层
        feature_layers = []
        current_dim = input_dim

        for hidden_dim in hidden_dims:
            feature_layers.append(nn.Linear(current_dim, hidden_dim))
            feature_layers.append(nn.ReLU())
            current_dim = hidden_dim

        self.feature_extractor = nn.Sequential(*feature_layers)
        self.feature_dim = current_dim

        # 如果没有指定头维度，使用默认值
        if head_dims is None:
            head_dims = [64] * num_heads

        # 创建多个批评家头
        self.value_heads = nn.ModuleList()
        for i in range(num_heads):
            head_layers = []
            head_current_dim = self.feature_dim

            # 每个头可以有不同的结构
            if i < len(head_dims):
                head_layers.append(nn.Linear(head_current_dim, head_dims[i]))
                head_layers.append(nn.ReLU())
                head_current_dim = head_dims[i]

            # 最终输出层
            head_layers.append(nn.Linear(head_current_dim, 1))

            self.value_heads.append(nn.Sequential(*head_layers))

    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, List[torch.Tensor]]:
        """
        前向传播

        Args:
            x: 输入张量（全局状态）

        Returns:
            组合价值和各个头的价值
        """
        # 提取共享特征
        features = self.feature_extractor(x)

        # 计算每个头的价值
        head_values = [head(features) for head in self.value_heads]

        # 组合所有头的价值（简单平均）
        combined_value = torch.mean(torch.cat(head_values, dim=1), dim=1, keepdim=True)

        return combined_value, head_values

    def get_head_values(self, x: torch.Tensor) -> List[torch.Tensor]:
        """
        获取每个头的价值

        Args:
            x: 输入张量（全局状态）

        Returns:
            各个头的价值列表
        """
        features = self.feature_extractor(x)
        return [head(features) for head in self.value_heads]


class EnhancedMAPPONetwork(nn.Module):
    """
    增强版多智能体PPO网络

    扩展原始MAPPO网络，增强了角色特定的策略网络和多头批评家网络。
    """

    def __init__(
        self,
        obs_dim: int,
        act_dim: int,
        global_state_dim: Optional[int] = None,
        hidden_dims: List[int] = [256, 128],
        shared_layers: bool = True,
        use_central_critic: bool = True,
        role: Optional[str] = None,
        use_multi_head_critic: bool = True,
        num_critic_heads: int = 3,
        head_dims: Optional[List[int]] = None
    ):
        """
        初始化增强版多智能体PPO网络

        Args:
            obs_dim: 局部观察维度
            act_dim: 动作维度
            global_state_dim: 全局状态维度，默认为None（使用局部观察）
            hidden_dims: 隐藏层维度
            shared_layers: 是否共享特征提取层
            use_central_critic: 是否使用中心化评论家（全局状态输入）
            role: 角色名称，如'landlord'、'farmer1'或'farmer2'
            use_multi_head_critic: 是否使用多头批评家
            num_critic_heads: 批评家头数
            head_dims: 每个头的隐藏层维度
        """
        super(EnhancedMAPPONetwork, self).__init__()

        self.shared_layers = shared_layers
        self.use_central_critic = use_central_critic
        self.use_multi_head_critic = use_multi_head_critic
        self.role = role

        # 如果指定了角色，添加角色编码
        self.role_encoding_dim = 3 if role is not None else 0  # 三个角色的one-hot编码
        obs_dim_with_role = obs_dim + self.role_encoding_dim

        # 策略网络（分散执行）- 使用局部观察
        if shared_layers:
            # 共享特征提取层
            self.actor_feature_layer = nn.Sequential(
                nn.Linear(obs_dim_with_role, hidden_dims[0]),
                nn.ReLU()
            )

            # 策略头
            policy_layers = []
            policy_input_dim = hidden_dims[0]

            for i in range(1, len(hidden_dims)):
                policy_layers.append(nn.Linear(policy_input_dim, hidden_dims[i]))
                policy_layers.append(nn.ReLU())
                policy_input_dim = hidden_dims[i]

            policy_layers.append(nn.Linear(policy_input_dim, act_dim))
            self.policy_head = nn.Sequential(*policy_layers)

        else:
            # 独立的策略网络
            policy_layers = []
            policy_input_dim = obs_dim_with_role

            for hidden_dim in hidden_dims:
                policy_layers.append(nn.Linear(policy_input_dim, hidden_dim))
                policy_layers.append(nn.ReLU())
                policy_input_dim = hidden_dim

            policy_layers.append(nn.Linear(policy_input_dim, act_dim))
            self.policy_net = nn.Sequential(*policy_layers)

        # 价值网络（中心化训练）- 使用全局状态或局部观察
        critic_input_dim = global_state_dim if use_central_critic and global_state_dim is not None else obs_dim_with_role

        if use_multi_head_critic:
            # 使用多头批评家
            self.critic = MultiHeadCritic(
                input_dim=critic_input_dim,
                hidden_dims=hidden_dims,
                num_heads=num_critic_heads,
                head_dims=head_dims
            )
        else:
            # 使用标准批评家
            if shared_layers:
                # 共享特征提取层
                self.critic_feature_layer = nn.Sequential(
                    nn.Linear(critic_input_dim, hidden_dims[0]),
                    nn.ReLU()
                )

                # 价值头
                value_layers = []
                value_input_dim = hidden_dims[0]

                for i in range(1, len(hidden_dims)):
                    value_layers.append(nn.Linear(value_input_dim, hidden_dims[i]))
                    value_layers.append(nn.ReLU())
                    value_input_dim = hidden_dims[i]

                value_layers.append(nn.Linear(value_input_dim, 1))
                self.value_head = nn.Sequential(*value_layers)

            else:
                # 独立的价值网络
                value_layers = []
                value_input_dim = critic_input_dim

                for hidden_dim in hidden_dims:
                    value_layers.append(nn.Linear(value_input_dim, hidden_dim))
                    value_layers.append(nn.ReLU())
                    value_input_dim = hidden_dim

                value_layers.append(nn.Linear(value_input_dim, 1))
                self.value_net = nn.Sequential(*value_layers)

    def _add_role_encoding(self, obs: torch.Tensor) -> torch.Tensor:
        """
        添加角色编码到观察

        Args:
            obs: 原始观察张量，不包含角色编码

        Returns:
            添加了角色编码的观察张量
        """
        if self.role is None or self.role_encoding_dim == 0:
            return obs

        batch_size = obs.shape[0]

        # 创建角色one-hot编码
        role_encoding = torch.zeros(batch_size, self.role_encoding_dim, device=obs.device)

        if self.role == 'landlord':
            role_encoding[:, 0] = 1.0
        elif self.role == 'farmer1':
            role_encoding[:, 1] = 1.0
        elif self.role == 'farmer2':
            role_encoding[:, 2] = 1.0

        # 连接原始观察和角色编码
        return torch.cat([obs, role_encoding], dim=1)

    def forward_actor(self, obs: torch.Tensor) -> torch.Tensor:
        """
        前向传播 - 策略网络

        Args:
            obs: 局部观察张量，不包含角色编码

        Returns:
            动作对数概率
        """
        # 添加角色编码
        obs_with_role = self._add_role_encoding(obs)

        if self.shared_layers:
            features = self.actor_feature_layer(obs_with_role)
            action_logits = self.policy_head(features)
        else:
            action_logits = self.policy_net(obs_with_role)

        return action_logits

    def forward_critic(self, state: torch.Tensor) -> Union[torch.Tensor, Tuple[torch.Tensor, List[torch.Tensor]]]:
        """
        前向传播 - 价值网络

        Args:
            state: 状态张量（全局状态或局部观察）

        Returns:
            如果使用多头批评家，返回(组合价值, 各头价值)
            否则返回状态价值
        """
        # 如果使用局部观察作为评论家输入，则添加角色编码
        if not self.use_central_critic or state.shape[1] == (self.obs_dim - self.role_encoding_dim):
            state = self._add_role_encoding(state)

        if self.use_multi_head_critic:
            return self.critic(state)
        else:
            if self.shared_layers:
                features = self.critic_feature_layer(state)
                state_values = self.value_head(features)
            else:
                state_values = self.value_net(state)

            return state_values

    def evaluate_actions(self, obs: torch.Tensor, state: torch.Tensor, actions: torch.Tensor) -> Union[Tuple[torch.Tensor, torch.Tensor, torch.Tensor], Tuple[torch.Tensor, torch.Tensor, torch.Tensor, List[torch.Tensor]]]:
        """
        评估动作

        Args:
            obs: 局部观察张量
            state: 全局状态张量
            actions: 动作张量

        Returns:
            如果使用标准批评家，返回(动作对数概率, 状态价值, 动作熵)
            如果使用多头批评家，返回(动作对数概率, 状态价值, 动作熵, 各头价值)
        """
        action_logits = self.forward_actor(obs)
        action_log_probs = F.log_softmax(action_logits, dim=-1)
        action_probs = F.softmax(action_logits, dim=-1)

        action_log_probs = action_log_probs.gather(1, actions.unsqueeze(1))
        entropy = -(action_probs * action_log_probs).sum(dim=-1).mean()

        critic_output = self.forward_critic(state)

        if self.use_multi_head_critic:
            values, head_values = critic_output
            return action_log_probs.squeeze(-1), values.squeeze(-1), entropy, head_values
        else:
            values = critic_output
            return action_log_probs.squeeze(-1), values.squeeze(-1), entropy

    def get_action_log_probs(self, obs: torch.Tensor) -> torch.Tensor:
        """
        获取动作对数概率

        Args:
            obs: 局部观察张量

        Returns:
            动作对数概率
        """
        action_logits = self.forward_actor(obs)
        return F.log_softmax(action_logits, dim=-1)

    def get_value(self, state: torch.Tensor) -> torch.Tensor:
        """
        获取状态价值

        Args:
            state: 状态张量（全局状态或局部观察）

        Returns:
            状态价值
        """
        critic_output = self.forward_critic(state)

        if self.use_multi_head_critic:
            values, _ = critic_output
            return values
        else:
            return critic_output


class CreditAssignment:
    """
    信用分配机制

    实现信用分配机制，用于在多智能体环境中合理分配奖励。
    """

    def __init__(
        self,
        team_reward_weight: float = 0.5,
        individual_reward_weight: float = 0.5,
        contribution_estimation_method: str = 'difference',
        decay_factor: float = 0.9
    ):
        """
        初始化信用分配机制

        Args:
            team_reward_weight: 团队奖励权重
            individual_reward_weight: 个体奖励权重
            contribution_estimation_method: 贡献度评估方法，可选'difference'、'shapley'或'counterfactual'
            decay_factor: 奖励衰减因子
        """
        self.team_reward_weight = team_reward_weight
        self.individual_reward_weight = individual_reward_weight
        self.contribution_estimation_method = contribution_estimation_method
        self.decay_factor = decay_factor

        # 存储历史贡献度
        self.contribution_history = {}

    def assign_credit(
        self,
        rewards: Dict[str, float],
        agent_ids: List[str],
        team_reward: float,
        value_estimates: Optional[Dict[str, List[float]]] = None
    ) -> Dict[str, float]:
        """
        分配信用

        Args:
            rewards: 原始奖励字典，键为智能体ID，值为奖励值
            agent_ids: 智能体ID列表
            team_reward: 团队总奖励
            value_estimates: 价值估计字典，键为智能体ID，值为价值估计列表

        Returns:
            分配后的奖励字典
        """
        # 计算贡献度
        contributions = self._estimate_contributions(rewards, agent_ids, team_reward, value_estimates)

        # 分配奖励
        assigned_rewards = {}
        for agent_id in agent_ids:
            # 混合团队奖励和个体奖励
            original_reward = rewards.get(agent_id, 0.0)
            contribution = contributions.get(agent_id, 0.0)

            # 加权平均
            assigned_rewards[agent_id] = (
                self.team_reward_weight * (contribution * team_reward) +
                self.individual_reward_weight * original_reward
            )

        return assigned_rewards

    def _estimate_contributions(
        self,
        rewards: Dict[str, float],
        agent_ids: List[str],
        team_reward: float,
        value_estimates: Optional[Dict[str, List[float]]] = None
    ) -> Dict[str, float]:
        """
        评估智能体的贡献度

        Args:
            rewards: 原始奖励字典
            agent_ids: 智能体ID列表
            team_reward: 团队总奖励
            value_estimates: 价值估计字典

        Returns:
            贡献度字典
        """
        if self.contribution_estimation_method == 'difference':
            return self._difference_based_contribution(rewards, agent_ids, team_reward)
        elif self.contribution_estimation_method == 'shapley':
            return self._shapley_value_contribution(rewards, agent_ids, team_reward, value_estimates)
        elif self.contribution_estimation_method == 'counterfactual':
            return self._counterfactual_contribution(rewards, agent_ids, team_reward, value_estimates)
        else:
            # 默认使用均匀分配
            return {agent_id: 1.0 / len(agent_ids) for agent_id in agent_ids}

    def _difference_based_contribution(self, rewards: Dict[str, float], agent_ids: List[str], team_reward: float) -> Dict[str, float]:
        """
        基于差异的贡献度评估

        计算每个智能体的奖励与平均奖励的差异，作为贡献度的估计。

        Args:
            rewards: 原始奖励字典
            agent_ids: 智能体ID列表
            team_reward: 团队总奖励

        Returns:
            贡献度字典
        """
        # 计算平均奖励
        avg_reward = sum(rewards.values()) / len(rewards) if rewards else 0.0

        # 计算差异
        differences = {}
        for agent_id in agent_ids:
            reward = rewards.get(agent_id, 0.0)
            differences[agent_id] = max(0, reward - avg_reward)  # 只考虑正差异

        # 归一化
        total_diff = sum(differences.values())
        if total_diff > 0:
            return {agent_id: diff / total_diff for agent_id, diff in differences.items()}
        else:
            # 如果没有差异，均匀分配
            return {agent_id: 1.0 / len(agent_ids) for agent_id in agent_ids}

    def _shapley_value_contribution(
        self,
        rewards: Dict[str, float],
        agent_ids: List[str],
        team_reward: float,
        value_estimates: Optional[Dict[str, List[float]]] = None
    ) -> Dict[str, float]:
        """
        基于Shapley值的贡献度评估

        使用Shapley值计算每个智能体对团队成功的边际贡献。
        由于计算完整的Shapley值复杂度过高，这里使用近似方法。

        Args:
            rewards: 原始奖励字典
            agent_ids: 智能体ID列表
            team_reward: 团队总奖励
            value_estimates: 价值估计字典

        Returns:
            贡献度字典
        """
        # 使用价值估计作为贡献度的代理
        if value_estimates is not None:
            # 使用多头批评家的价值估计
            contributions = {}
            for agent_id in agent_ids:
                if agent_id in value_estimates and len(value_estimates[agent_id]) > 0:
                    # 使用第一个头的价值作为个人贡献度
                    # 使用第二个头的价值作为团队贡献度
                    if len(value_estimates[agent_id]) >= 2:
                        individual_value = value_estimates[agent_id][0].item()
                        team_value = value_estimates[agent_id][1].item()
                        contributions[agent_id] = 0.7 * individual_value + 0.3 * team_value
                    else:
                        contributions[agent_id] = value_estimates[agent_id][0].item()
                else:
                    # 如果没有价值估计，使用原始奖励
                    contributions[agent_id] = rewards.get(agent_id, 0.0)
        else:
            # 如果没有价值估计，使用简化的Shapley值计算
            contributions = self._simplified_shapley(rewards, agent_ids)

        # 归一化
        total_contribution = sum(contributions.values())
        if total_contribution > 0:
            return {agent_id: contrib / total_contribution for agent_id, contrib in contributions.items()}
        else:
            # 如果没有贡献，均匀分配
            return {agent_id: 1.0 / len(agent_ids) for agent_id in agent_ids}

    def _simplified_shapley(self, rewards: Dict[str, float], agent_ids: List[str]) -> Dict[str, float]:
        """
        简化的Shapley值计算

        使用奖励和历史贡献度计算简化的Shapley值。

        Args:
            rewards: 原始奖励字典
            agent_ids: 智能体ID列表

        Returns:
            贡献度字典
        """
        # 计算当前贡献度
        current_contributions = {}
        for agent_id in agent_ids:
            reward = rewards.get(agent_id, 0.0)
            # 使用奖励的比例作为贡献度
            current_contributions[agent_id] = max(0, reward)

        # 归一化当前贡献度
        total_current = sum(current_contributions.values())
        if total_current > 0:
            current_contributions = {agent_id: contrib / total_current for agent_id, contrib in current_contributions.items()}
        else:
            current_contributions = {agent_id: 1.0 / len(agent_ids) for agent_id in agent_ids}

        # 结合历史贡献度
        final_contributions = {}
        for agent_id in agent_ids:
            # 如果有历史贡献度，使用指数加权平均
            if agent_id in self.contribution_history:
                final_contributions[agent_id] = (
                    self.decay_factor * self.contribution_history[agent_id] +
                    (1 - self.decay_factor) * current_contributions[agent_id]
                )
            else:
                final_contributions[agent_id] = current_contributions[agent_id]

            # 更新历史贡献度
            self.contribution_history[agent_id] = final_contributions[agent_id]

        return final_contributions

    def _counterfactual_contribution(
        self,
        rewards: Dict[str, float],
        agent_ids: List[str],
        team_reward: float,
        value_estimates: Optional[Dict[str, List[float]]] = None
    ) -> Dict[str, float]:
        """
        基于反事实的贡献度评估

        使用反事实思想计算每个智能体的贡献度，即“如果没有这个智能体，团队会如何表现”。

        Args:
            rewards: 原始奖励字典
            agent_ids: 智能体ID列表
            team_reward: 团队总奖励
            value_estimates: 价值估计字典

        Returns:
            贡献度字典
        """
        # 使用多头批评家的价值估计计算反事实贡献度
        if value_estimates is not None and len(value_estimates) > 0:
            # 假设第三个头的价值估计是反事实价值
            contributions = {}
            for agent_id in agent_ids:
                if agent_id in value_estimates and len(value_estimates[agent_id]) >= 3:
                    # 使用第三个头的价值作为反事实价值
                    counterfactual_value = value_estimates[agent_id][2].item()
                    # 计算贡献度：当前团队价值 - 没有该智能体的团队价值
                    contributions[agent_id] = team_reward - counterfactual_value
                else:
                    # 如果没有反事实价值，使用原始奖励
                    contributions[agent_id] = rewards.get(agent_id, 0.0)
        else:
            # 如果没有多头批评家的价值估计，使用简化的反事实计算
            contributions = {}
            avg_reward = sum(rewards.values()) / len(rewards) if rewards else 0.0

            for agent_id in agent_ids:
                reward = rewards.get(agent_id, 0.0)
                # 计算贡献度：当前奖励 - 平均奖励
                contributions[agent_id] = max(0, reward - avg_reward)

        # 归一化
        total_contribution = sum(contributions.values())
        if total_contribution > 0:
            return {agent_id: contrib / total_contribution for agent_id, contrib in contributions.items()}
        else:
            # 如果没有贡献，均匀分配
            return {agent_id: 1.0 / len(agent_ids) for agent_id in agent_ids}


class EnhancedMAPPO(MAPPO):
    """
    增强版多智能体近端策略优化(Enhanced MAPPO)算法

    扩展MAPPO算法，增强了角色特定的策略网络、中心化批评家网络和信用分配机制。
    """

    def __init__(
        self,
        obs_shape: Tuple[int, ...],
        act_shape: Tuple[int, ...],
        global_state_shape: Optional[Tuple[int, ...]] = None,
        hidden_dims: List[int] = [256, 128],
        shared_network: bool = True,
        use_central_critic: bool = True,
        role: Optional[str] = None,
        use_multi_head_critic: bool = True,
        num_critic_heads: int = 3,
        head_dims: Optional[List[int]] = None,
        learning_rate: float = 0.0003,
        gamma: float = 0.99,
        gae_lambda: float = 0.95,
        clip_ratio: float = 0.2,
        value_coef: float = 0.5,
        entropy_coef: float = 0.01,
        max_grad_norm: float = 0.5,
        update_epochs: int = 10,
        batch_size: int = 64,
        team_reward_weight: float = 0.5,
        individual_reward_weight: float = 0.5,
        contribution_estimation_method: str = 'difference',
        decay_factor: float = 0.9,
        use_rlhf: bool = False,
        rlhf_coef: float = 0.5,
        device: Optional[str] = None
    ):
        """
        初始化增强版MAPPO算法

        Args:
            obs_shape: 观察空间形状
            act_shape: 动作空间形状
            global_state_shape: 全局状态形状，默认为None（使用观察）
            hidden_dims: 隐藏层维度
            shared_network: 是否共享特征提取层
            use_central_critic: 是否使用中心化评论家
            role: 角色名称，如'landlord'、'farmer1'或'farmer2'
            use_multi_head_critic: 是否使用多头批评家
            num_critic_heads: 批评家头数
            head_dims: 每个头的隐藏层维度
            learning_rate: 学习率
            gamma: 折扣因子
            gae_lambda: GAE参数
            clip_ratio: PPO截断参数
            value_coef: 价值损失权重
            entropy_coef: 熵正则化权重
            max_grad_norm: 梯度裁剪范数
            update_epochs: 更新轮数
            batch_size: 批次大小
            team_reward_weight: 团队奖励权重
            individual_reward_weight: 个体奖励权重
            contribution_estimation_method: 贡献度评估方法
            decay_factor: 奖励衰减因子
            use_rlhf: 是否使用人类反馈强化学习
            rlhf_coef: 人类反馈损失权重
            device: 计算设备
        """
        # 设置设备
        if device is None:
            self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        else:
            self.device = torch.device(device)

        # 解析输入形状
        self.obs_dim = int(np.prod(obs_shape))
        self.act_dim = int(np.prod(act_shape))
        self.global_state_dim = int(np.prod(global_state_shape)) if global_state_shape is not None else None

        # 保存超参数
        self.hidden_dims = hidden_dims
        self.shared_network = shared_network
        self.use_central_critic = use_central_critic
        self.role = role
        self.use_multi_head_critic = use_multi_head_critic
        self.num_critic_heads = num_critic_heads
        self.head_dims = head_dims
        self.gamma = gamma
        self.gae_lambda = gae_lambda
        self.clip_ratio = clip_ratio
        self.value_coef = value_coef
        self.entropy_coef = entropy_coef
        self.max_grad_norm = max_grad_norm
        self.update_epochs = update_epochs
        self.batch_size = batch_size

        # RLHF相关参数
        self.use_rlhf = use_rlhf
        self.rlhf_coef = rlhf_coef

        # 创建增强版网络
        self.network = EnhancedMAPPONetwork(
            obs_dim=self.obs_dim,
            act_dim=self.act_dim,
            global_state_dim=self.global_state_dim,
            hidden_dims=hidden_dims,
            shared_layers=shared_network,
            use_central_critic=use_central_critic,
            role=role,
            use_multi_head_critic=use_multi_head_critic,
            num_critic_heads=num_critic_heads,
            head_dims=head_dims
        ).to(self.device)

        # 创建优化器
        self.optimizer = optim.Adam(self.network.parameters(), lr=learning_rate)

        # 创建信用分配机制
        self.credit_assignment = CreditAssignment(
            team_reward_weight=team_reward_weight,
            individual_reward_weight=individual_reward_weight,
            contribution_estimation_method=contribution_estimation_method,
            decay_factor=decay_factor
        )

        # 存储训练数据
        self.observations = []
        self.global_states = []
        self.actions = []
        self.action_log_probs = []
        self.rewards = []
        self.values = []
        self.head_values = []
        self.dones = []

    def update(self, experience: Union[Experience, Batch], human_feedback_samples: Optional[Dict[str, Any]] = None) -> Dict[str, float]:
        """
        使用经验数据更新模型

        Args:
            experience: 单个经验或经验批次
            human_feedback_samples: 人类反馈样本，可选

        Returns:
            更新指标，如损失值等
        """
        # 存储经验数据
        if isinstance(experience, Experience):
            self._store_experience(experience)
        elif isinstance(experience, Batch):
            for exp in experience.experiences:
                self._store_experience(exp)

        # 检查是否有足够的数据更新
        if len(self.observations) < self.batch_size:
            return {'policy_loss': 0.0, 'value_loss': 0.0, 'entropy': 0.0, 'rlhf_loss': 0.0}

        # 处理所有存储的数据
        return self._update_policy(human_feedback_samples)

    def _store_experience(self, experience: Experience) -> None:
        """
        存储经验数据

        Args:
            experience: 单个经验
        """
        # 处理观察
        if isinstance(experience.state, State):
            obs_array = experience.state.to_dict().get('observation', np.zeros(self.obs_dim))
            global_state_array = experience.state.to_dict().get('global_state', obs_array)
        else:
            obs_array = experience.state
            global_state_array = experience.state  # 如果没有全局状态，使用观察

        # 处理动作
        action = experience.action.action_id if hasattr(experience.action, 'action_id') else experience.action

        # 转换为张量并预测动作概率和价值
        obs_tensor = torch.FloatTensor(obs_array).unsqueeze(0).to(self.device)
        state_tensor = torch.FloatTensor(global_state_array).unsqueeze(0).to(self.device) if self.use_central_critic else obs_tensor

        with torch.no_grad():
            action_log_probs = self.network.get_action_log_probs(obs_tensor)
            action_log_prob = action_log_probs[0, action].cpu().item()

            # 获取价值估计
            if self.use_multi_head_critic:
                value, head_values_list = self.network.forward_critic(state_tensor)
                value = value.cpu().item()
                head_values = [head_value.cpu().item() for head_value in head_values_list]
            else:
                value = self.network.get_value(state_tensor).cpu().item()
                head_values = [value]  # 如果不使用多头批评家，使用单一价值

        # 存储数据
        self.observations.append(obs_array)
        self.global_states.append(global_state_array)
        self.actions.append(action)
        self.action_log_probs.append(action_log_prob)
        self.rewards.append(experience.reward)
        self.values.append(value)
        self.head_values.append(head_values)
        self.dones.append(float(experience.done))

    def _update_policy(self, human_feedback_samples: Optional[Dict[str, Any]] = None) -> Dict[str, float]:
        """
        更新策略

        Args:
            human_feedback_samples: 人类反馈样本，可选

        Returns:
            更新指标
        """
        # 应用信用分配机制
        agent_ids = [str(i) for i in range(len(self.rewards))]
        original_rewards = {agent_id: reward for agent_id, reward in zip(agent_ids, self.rewards)}
        team_reward = sum(self.rewards)

        # 收集价值估计用于信用分配
        value_estimates = {agent_id: head_values for agent_id, head_values in zip(agent_ids, self.head_values)}

        # 分配信用
        assigned_rewards = self.credit_assignment.assign_credit(
            rewards=original_rewards,
            agent_ids=agent_ids,
            team_reward=team_reward,
            value_estimates=value_estimates
        )

        # 更新奖励
        adjusted_rewards = [assigned_rewards.get(agent_id, reward) for agent_id, reward in zip(agent_ids, self.rewards)]

        # 计算优势和回报
        advantages, returns = self._compute_advantages_and_returns(adjusted_rewards)

        # 转换为张量
        observations = torch.FloatTensor(self.observations).to(self.device)
        global_states = torch.FloatTensor(self.global_states).to(self.device) if self.use_central_critic else observations
        actions = torch.LongTensor(self.actions).to(self.device)
        old_action_log_probs = torch.FloatTensor(self.action_log_probs).to(self.device)
        returns = torch.FloatTensor(returns).to(self.device)
        advantages = torch.FloatTensor(advantages).to(self.device)

        # 归一化优势
        advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)

        # 多轮更新
        total_policy_loss = 0
        total_value_loss = 0
        total_entropy = 0
        total_rlhf_loss = 0

        for _ in range(self.update_epochs):
            # 生成批次索引
            indices = torch.randperm(len(observations))

            # 按批次更新
            for start_idx in range(0, len(observations), self.batch_size):
                # 获取批次索引
                batch_indices = indices[start_idx:start_idx + self.batch_size]

                # 提取批次数据
                batch_observations = observations[batch_indices]
                batch_global_states = global_states[batch_indices]
                batch_actions = actions[batch_indices]
                batch_old_action_log_probs = old_action_log_probs[batch_indices]
                batch_returns = returns[batch_indices]
                batch_advantages = advantages[batch_indices]

                # 评估动作
                if self.use_multi_head_critic:
                    action_log_probs, values, entropy, head_values = self.network.evaluate_actions(
                        batch_observations, batch_global_states, batch_actions
                    )
                else:
                    action_log_probs, values, entropy = self.network.evaluate_actions(
                        batch_observations, batch_global_states, batch_actions
                    )

                # 计算比率和裁剪目标
                ratio = torch.exp(action_log_probs - batch_old_action_log_probs)
                surr1 = ratio * batch_advantages
                surr2 = torch.clamp(ratio, 1.0 - self.clip_ratio, 1.0 + self.clip_ratio) * batch_advantages

                # 计算策略损失
                policy_loss = -torch.min(surr1, surr2).mean()

                # 计算价值损失
                value_loss = F.mse_loss(values, batch_returns)

                # 计算RLHF损失（如果有人类反馈数据）
                rlhf_loss = torch.tensor(0.0, device=self.device)
                if human_feedback_samples is not None and len(human_feedback_samples) > 0:
                    rlhf_loss = self.compute_rlhf_adjustment(batch_observations, batch_actions, human_feedback_samples)

                # 计算总损失
                loss = policy_loss + self.value_coef * value_loss - self.entropy_coef * entropy + rlhf_loss

                # 更新网络
                self.optimizer.zero_grad()
                loss.backward()
                nn.utils.clip_grad_norm_(self.network.parameters(), self.max_grad_norm)
                self.optimizer.step()

                # 累加损失
                total_policy_loss += policy_loss.item()
                total_value_loss += value_loss.item()
                total_entropy += entropy.item()
                total_rlhf_loss += rlhf_loss.item() if isinstance(rlhf_loss, torch.Tensor) else rlhf_loss

        # 清空存储的数据
        self.observations = []
        self.global_states = []
        self.actions = []
        self.action_log_probs = []
        self.rewards = []
        self.values = []
        self.head_values = []
        self.dones = []

        # 返回更新指标
        return {
            'policy_loss': total_policy_loss / self.update_epochs,
            'value_loss': total_value_loss / self.update_epochs,
            'entropy': total_entropy / self.update_epochs,
            'rlhf_loss': total_rlhf_loss / self.update_epochs
        }

    def compute_rlhf_adjustment(self, observations: torch.Tensor, actions: torch.Tensor, human_feedback_samples: Dict[str, Any]) -> torch.Tensor:
        """
        计算基于人类反馈的调整

        Args:
            observations: 观察张量
            actions: 动作张量
            human_feedback_samples: 人类反馈样本

        Returns:
            torch.Tensor: RLHF损失
        """
        # 如果未启用RLHF，直接返回零损失
        if not self.use_rlhf:
            return torch.tensor(0.0, device=self.device)

        # 初始化RLHF损失
        rlhf_loss = torch.tensor(0.0, device=self.device)

        # 1. 基于人类偏好的损失计算（偏好学习）
        if 'preferences' in human_feedback_samples:
            preference_loss = self._calculate_preference_loss(observations, actions, human_feedback_samples)
            rlhf_loss += preference_loss

        # 2. 基于人类反馈评分的损失计算
        if 'feedback_scores' in human_feedback_samples:
            feedback_loss = self._calculate_feedback_score_loss(observations, human_feedback_samples)
            rlhf_loss += feedback_loss

        # 3. 基于人类动作模仿的损失计算（行为克隆）
        if 'human_actions' in human_feedback_samples:
            imitation_loss = self._calculate_imitation_loss(observations, human_feedback_samples)
            rlhf_loss += imitation_loss

        # 应用RLHF权重
        rlhf_loss = self.rlhf_coef * rlhf_loss

        return rlhf_loss

    def _calculate_preference_loss(self, observations: torch.Tensor, actions: torch.Tensor, human_feedback_samples: Dict[str, Any]) -> torch.Tensor:
        """
        计算基于人类偏好的损失（偏好学习）

        Args:
            observations: 观察张量
            actions: 动作张量
            human_feedback_samples: 人类反馈样本

        Returns:
            torch.Tensor: 偏好损失
        """
        # 提取偏好数据
        preferred_obs = human_feedback_samples.get('preferred_observations')
        rejected_obs = human_feedback_samples.get('rejected_observations')

        if preferred_obs is None or rejected_obs is None:
            return torch.tensor(0.0, device=self.device)

        # 将数据转换为张量
        preferred_obs = torch.FloatTensor(preferred_obs).to(self.device)
        rejected_obs = torch.FloatTensor(rejected_obs).to(self.device)

        # 获取全局状态（如果使用中心化评论家）
        if self.use_central_critic and 'preferred_global_states' in human_feedback_samples and 'rejected_global_states' in human_feedback_samples:
            preferred_states = torch.FloatTensor(human_feedback_samples['preferred_global_states']).to(self.device)
            rejected_states = torch.FloatTensor(human_feedback_samples['rejected_global_states']).to(self.device)
        else:
            preferred_states = preferred_obs
            rejected_states = rejected_obs

        # 评估价值
        if self.use_multi_head_critic:
            _, preferred_values, _, _ = self.network.evaluate_actions(
                preferred_obs, preferred_states, torch.zeros_like(actions[:1])  # 动作参数在这里不重要
            )
            _, rejected_values, _, _ = self.network.evaluate_actions(
                rejected_obs, rejected_states, torch.zeros_like(actions[:1])
            )
        else:
            _, preferred_values, _ = self.network.evaluate_actions(
                preferred_obs, preferred_states, torch.zeros_like(actions[:1])
            )
            _, rejected_values, _ = self.network.evaluate_actions(
                rejected_obs, rejected_states, torch.zeros_like(actions[:1])
            )

        # 计算偏好损失（使用Bradley-Terry模型）
        # 目标：使preferred_values > rejected_values
        preference_loss = F.softplus(rejected_values - preferred_values).mean()

        return preference_loss

    def _calculate_feedback_score_loss(self, observations: torch.Tensor, human_feedback_samples: Dict[str, Any]) -> torch.Tensor:
        """
        计算基于人类反馈评分的损失

        Args:
            observations: 观察张量
            human_feedback_samples: 人类反馈样本

        Returns:
            torch.Tensor: 反馈评分损失
        """
        # 提取反馈评分数据
        feedback_obs = human_feedback_samples.get('feedback_observations')
        feedback_scores = human_feedback_samples.get('feedback_scores')

        if feedback_obs is None or feedback_scores is None:
            return torch.tensor(0.0, device=self.device)

        # 将数据转换为张量
        feedback_obs = torch.FloatTensor(feedback_obs).to(self.device)
        feedback_scores = torch.FloatTensor(feedback_scores).to(self.device)

        # 获取全局状态（如果使用中心化评论家）
        if self.use_central_critic and 'feedback_global_states' in human_feedback_samples:
            feedback_states = torch.FloatTensor(human_feedback_samples['feedback_global_states']).to(self.device)
        else:
            feedback_states = feedback_obs

        # 评估价值
        dummy_actions = torch.zeros(feedback_obs.size(0), 1, dtype=torch.long, device=self.device)
        if self.use_multi_head_critic:
            _, predicted_values, _, _ = self.network.evaluate_actions(feedback_obs, feedback_states, dummy_actions)
        else:
            _, predicted_values, _ = self.network.evaluate_actions(feedback_obs, feedback_states, dummy_actions)

        # 计算MSE损失（使模型价值接近人类反馈评分）
        feedback_loss = F.mse_loss(predicted_values, feedback_scores)

        return feedback_loss

    def _calculate_imitation_loss(self, observations: torch.Tensor, human_feedback_samples: Dict[str, Any]) -> torch.Tensor:
        """
        计算基于人类动作模仿的损失（行为克隆）

        Args:
            observations: 观察张量
            human_feedback_samples: 人类反馈样本

        Returns:
            torch.Tensor: 模仿损失
        """
        # 提取模仿数据
        imitation_obs = human_feedback_samples.get('human_observations')
        human_actions = human_feedback_samples.get('human_actions')

        if imitation_obs is None or human_actions is None:
            return torch.tensor(0.0, device=self.device)

        # 将数据转换为张量
        imitation_obs = torch.FloatTensor(imitation_obs).to(self.device)
        human_actions = torch.LongTensor(human_actions).to(self.device)

        # 获取全局状态（如果使用中心化评论家）
        if self.use_central_critic and 'human_global_states' in human_feedback_samples:
            imitation_states = torch.FloatTensor(human_feedback_samples['human_global_states']).to(self.device)
        else:
            imitation_states = imitation_obs

        # 获取策略输出
        action_logits = self.network.forward_actor(imitation_obs)

        # 计算交叉熵损失（使模型策略接近人类动作）
        imitation_loss = F.cross_entropy(action_logits, human_actions)

        return imitation_loss

    def _compute_advantages_and_returns(self, rewards: List[float]) -> Tuple[List[float], List[float]]:
        """
        计算优势和回报

        Args:
            rewards: 奖励列表

        Returns:
            优势和回报列表
        """
        # 计算GAE优势估计
        advantages = []
        returns = []
        gae = 0

        # 逆序遍历奖励、价值和完成标志
        for t in reversed(range(len(rewards))):
            if t == len(rewards) - 1:
                next_value = 0.0
            else:
                next_value = self.values[t + 1]

            # 计算时差目标
            delta = rewards[t] + self.gamma * next_value * (1 - self.dones[t]) - self.values[t]

            # 计算GAE
            gae = delta + self.gamma * self.gae_lambda * (1 - self.dones[t]) * gae

            # 存储优势和回报
            advantages.insert(0, gae)
            returns.insert(0, gae + self.values[t])

        return advantages, returns
