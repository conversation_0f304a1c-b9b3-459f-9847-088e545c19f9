#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
关键决策点数据生成脚本

用于生成关键决策点标注数据，包括游戏状态和是否为关键决策点的标签。
"""

import os
import sys
import argparse
import logging
import json
import random
import numpy as np
from datetime import datetime
from typing import Dict, List, Tuple, Any, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from cardgame_ai.games.doudizhu.state import DouDizhuState, GamePhase
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.games.doudizhu.agent import DouDizhuAgent

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def is_key_moment(state: DouDizhuState, next_states: List[DouDizhuState], rewards: List[float]) -> float:
    """
    判断当前状态是否为关键决策点
    
    通过分析不同动作导致的奖励差异来判断。
    
    Args:
        state: 当前状态
        next_states: 执行不同动作后的下一个状态列表
        rewards: 执行不同动作后的奖励列表
        
    Returns:
        float: 关键程度评分，范围[0, 1]
    """
    # 如果没有下一个状态，返回0
    if not next_states or not rewards:
        return 0.0
    
    # 计算奖励差异
    reward_diff = max(rewards) - min(rewards)
    
    # 如果奖励差异很大，说明是关键决策点
    if reward_diff > 1.0:
        return 1.0
    
    # 根据奖励差异计算关键程度
    key_score = min(1.0, reward_diff)
    
    # 考虑游戏阶段
    if state.game_phase == GamePhase.PLAYING:
        # 出牌阶段的关键决策点
        
        # 如果是地主且手牌数量少，更可能是关键决策点
        if state.landlord == state.current_player and len(state.hands[state.current_player]) <= 5:
            key_score = max(key_score, 0.7)
        
        # 如果是农民且地主手牌数量少，更可能是关键决策点
        if state.landlord != state.current_player and len(state.hands[state.landlord]) <= 3:
            key_score = max(key_score, 0.8)
        
        # 如果连续不出次数为1，可能是关键决策点
        if state.num_passes == 1:
            key_score = max(key_score, 0.6)
    
    return key_score


def generate_key_moment_data(
    output_path: str,
    num_games: int = 100,
    model_path: Optional[str] = None,
    seed: Optional[int] = None
) -> None:
    """
    生成关键决策点数据
    
    Args:
        output_path: 输出文件路径
        num_games: 游戏数量
        model_path: 模型路径，可选
        seed: 随机种子，可选
    """
    # 设置随机种子
    if seed is not None:
        random.seed(seed)
        np.random.seed(seed)
    
    # 创建环境
    env = DouDizhuEnvironment(seed=seed)
    
    # 创建智能体
    agent = DouDizhuAgent(model_path=model_path)
    
    # 创建输出目录
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # 打开输出文件
    with open(output_path, 'w', encoding='utf-8') as f:
        # 生成游戏数据
        for game_idx in range(num_games):
            logger.info(f"生成游戏 {game_idx+1}/{num_games}")
            
            # 重置环境
            state = env.reset()
            
            # 游戏循环
            done = False
            while not done:
                # 获取当前玩家
                current_player = state.current_player
                
                # 获取合法动作
                legal_actions = state.get_legal_actions()
                
                # 如果没有合法动作，跳过
                if not legal_actions:
                    break
                
                # 获取下一个状态和奖励
                next_states = []
                rewards = []
                
                # 对每个合法动作，计算下一个状态和奖励
                for action in legal_actions[:min(5, len(legal_actions))]:  # 最多考虑5个动作
                    next_state = state.next_state(action)
                    
                    # 如果游戏结束，获取奖励
                    if next_state.is_terminal():
                        reward = next_state.get_payoffs()[current_player]
                    else:
                        # 使用智能体评估状态价值
                        _, value = agent.evaluate(next_state)
                        reward = value
                    
                    next_states.append(next_state)
                    rewards.append(reward)
                
                # 判断是否为关键决策点
                key_score = is_key_moment(state, next_states, rewards)
                
                # 创建数据条目
                data_entry = {
                    'game_id': f"game_{game_idx}",
                    'timestamp': datetime.now().isoformat(),
                    'game_state': state.to_dict(),
                    'is_key_moment': key_score,
                    'legal_actions_count': len(legal_actions),
                    'reward_range': max(rewards) - min(rewards) if rewards else 0.0
                }
                
                # 写入数据
                f.write(json.dumps(data_entry, ensure_ascii=False) + '\n')
                
                # 随机选择一个动作
                action = random.choice(legal_actions)
                
                # 执行动作
                state, _, done, _ = env.step(action)
            
            # 游戏结束
            logger.info(f"游戏 {game_idx+1} 结束")
    
    logger.info(f"数据生成完成，已保存到: {output_path}")


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='生成关键决策点数据')
    
    parser.add_argument('--output', type=str, default='data/labeled_critical_moments.jsonl',
                        help='输出文件路径')
    parser.add_argument('--num_games', type=int, default=100,
                        help='游戏数量')
    parser.add_argument('--model_path', type=str, default=None,
                        help='模型路径')
    parser.add_argument('--seed', type=int, default=None,
                        help='随机种子')
    
    return parser.parse_args()


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 生成数据
    generate_key_moment_data(
        output_path=args.output,
        num_games=args.num_games,
        model_path=args.model_path,
        seed=args.seed
    )
    
    return 0


if __name__ == "__main__":
    main()
