"""
深度Q网络算法模块

实现深度Q网络(DQN)算法及其变种，包括Double DQN和Dueling DQN。
"""
import os
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from typing import Dict, Any, List, Tuple, Optional, Union

from cardgame_ai.core.base import State, Action, Experience, Batch
from cardgame_ai.core.algorithm import ValueBasedAlgorithm
from cardgame_ai.algorithms.replay_buffer import ReplayBuffer, PrioritizedReplayBuffer


class QNetwork(nn.Module):
    """
    Q网络

    用于DQN算法的神经网络模型，预测状态的Q值。
    """

    def __init__(self, state_dim: int, action_dim: int, hidden_dims: List[int] = [128, 128]):
        """
        初始化Q网络

        Args:
            state_dim (int): 状态维度
            action_dim (int): 动作维度
            hidden_dims (List[int], optional): 隐藏层维度. Defaults to [128, 128].
        """
        super(QNetwork, self).__init__()

        # 构建网络层
        layers = []
        input_dim = state_dim

        for hidden_dim in hidden_dims:
            layers.append(nn.Linear(input_dim, hidden_dim))
            layers.append(nn.ReLU())
            input_dim = hidden_dim

        # 输出层
        layers.append(nn.Linear(input_dim, action_dim))

        # 构建序列模型
        self.model = nn.Sequential(*layers)

    def forward(self, state: torch.Tensor) -> torch.Tensor:
        """
        前向传播

        Args:
            state (torch.Tensor): 状态张量

        Returns:
            torch.Tensor: Q值
        """
        return self.model(state)


class DuelingQNetwork(nn.Module):
    """
    Dueling Q网络

    用于Dueling DQN算法的神经网络模型，分别预测状态价值V和优势函数A。
    """

    def __init__(self, state_dim: int, action_dim: int, hidden_dims: List[int] = [128, 128]):
        """
        初始化Dueling Q网络

        Args:
            state_dim (int): 状态维度
            action_dim (int): 动作维度
            hidden_dims (List[int], optional): 隐藏层维度. Defaults to [128, 128].
        """
        super(DuelingQNetwork, self).__init__()

        # 共享层
        self.feature_layer = nn.Sequential(
            nn.Linear(state_dim, hidden_dims[0]),
            nn.ReLU()
        )

        # 价值流
        value_layers = []
        value_input_dim = hidden_dims[0]

        for i in range(1, len(hidden_dims)):
            value_layers.append(nn.Linear(value_input_dim, hidden_dims[i]))
            value_layers.append(nn.ReLU())
            value_input_dim = hidden_dims[i]

        value_layers.append(nn.Linear(value_input_dim, 1))
        self.value_stream = nn.Sequential(*value_layers)

        # 优势流
        advantage_layers = []
        advantage_input_dim = hidden_dims[0]

        for i in range(1, len(hidden_dims)):
            advantage_layers.append(nn.Linear(advantage_input_dim, hidden_dims[i]))
            advantage_layers.append(nn.ReLU())
            advantage_input_dim = hidden_dims[i]

        advantage_layers.append(nn.Linear(advantage_input_dim, action_dim))
        self.advantage_stream = nn.Sequential(*advantage_layers)

    def forward(self, state: torch.Tensor) -> torch.Tensor:
        """
        前向传播

        Args:
            state (torch.Tensor): 状态张量

        Returns:
            torch.Tensor: Q值
        """
        features = self.feature_layer(state)
        value = self.value_stream(features)
        advantage = self.advantage_stream(features)

        # Q(s,a) = V(s) + (A(s,a) - mean(A(s,a')))
        return value + advantage - advantage.mean(dim=1, keepdim=True)


class DQN(ValueBasedAlgorithm):
    """
    深度Q网络(DQN)算法

    实现基础的DQN算法，包括经验回放和目标网络。
    """

    def __init__(
        self,
        state_shape: Tuple[int, ...],
        action_shape: Tuple[int, ...],
        hidden_dims: List[int] = [128, 128],
        learning_rate: float = 0.001,
        buffer_size: int = 10000,
        batch_size: int = 64,
        gamma: float = 0.99,
        tau: float = 0.005,
        update_frequency: int = 4,
        target_update_frequency: int = 100,
        device: str = None
    ):
        """
        初始化DQN算法

        Args:
            state_shape (Tuple[int, ...]): 状态形状
            action_shape (Tuple[int, ...]): 动作形状
            hidden_dims (List[int], optional): 隐藏层维度. Defaults to [128, 128].
            learning_rate (float, optional): 学习率. Defaults to 0.001.
            buffer_size (int, optional): 经验回放缓冲区大小. Defaults to 10000.
            batch_size (int, optional): 批次大小. Defaults to 64.
            gamma (float, optional): 折扣因子. Defaults to 0.99.
            tau (float, optional): 目标网络更新系数. Defaults to 0.005.
            update_frequency (int, optional): 更新频率. Defaults to 4.
            target_update_frequency (int, optional): 目标网络更新频率. Defaults to 100.
            device (str, optional): 设备. Defaults to None.
        """
        super().__init__(state_shape, action_shape, gamma)

        # 设置设备
        self.device = device if device is not None else ('cuda' if torch.cuda.is_available() else 'cpu')

        # 创建经验回放缓冲区
        self.replay_buffer = ReplayBuffer(buffer_size)

        # 计算状态和动作维度
        self.state_dim = np.prod(state_shape)
        self.action_dim = np.prod(action_shape)

        # 创建Q网络和目标网络
        self.q_network = QNetwork(self.state_dim, self.action_dim, hidden_dims).to(self.device)
        self.target_network = QNetwork(self.state_dim, self.action_dim, hidden_dims).to(self.device)

        # 复制参数到目标网络
        self.target_network.load_state_dict(self.q_network.state_dict())

        # 创建优化器
        self.optimizer = optim.Adam(self.q_network.parameters(), lr=learning_rate)

        # 设置超参数
        self.batch_size = batch_size
        self.tau = tau
        self.update_frequency = update_frequency
        self.target_update_frequency = target_update_frequency

        # 记录更新次数
        self.update_count = 0

    def update(self, experience: Union[Experience, Batch]) -> Dict[str, float]:
        """
        使用经验数据更新模型

        Args:
            experience (Union[Experience, Batch]): 单个经验或经验批次

        Returns:
            Dict[str, float]: 更新指标，如损失值等
        """
        # 将经验添加到回放缓冲区
        if isinstance(experience, Experience):
            self.replay_buffer.add(experience)
        elif isinstance(experience, Batch):
            for exp in experience.experiences:
                self.replay_buffer.add(exp)

        # 检查缓冲区大小
        if len(self.replay_buffer) < self.batch_size:
            return {'loss': 0.0}

        # 检查更新频率
        self.update_count += 1
        if self.update_count % self.update_frequency != 0:
            return {'loss': 0.0}

        # 从缓冲区采样批次
        batch = self.replay_buffer.sample(self.batch_size)

        # 提取批次数据
        states = torch.FloatTensor(self._process_states(batch)).to(self.device)
        actions = torch.LongTensor(self._process_actions(batch)).to(self.device)
        rewards = torch.FloatTensor(self._process_rewards(batch)).to(self.device)
        next_states = torch.FloatTensor(self._process_states(batch, next_state=True)).to(self.device)
        dones = torch.FloatTensor(self._process_dones(batch)).to(self.device)

        # 计算当前Q值
        current_q_values = self.q_network(states).gather(1, actions.unsqueeze(1)).squeeze(1)

        # 计算目标Q值
        with torch.no_grad():
            next_q_values = self.target_network(next_states).max(1)[0]
            target_q_values = rewards + (1 - dones) * self.gamma * next_q_values

        # 计算损失
        loss = F.mse_loss(current_q_values, target_q_values)

        # 更新网络
        self.optimizer.zero_grad()
        loss.backward()
        self.optimizer.step()

        # 更新目标网络
        if self.update_count % self.target_update_frequency == 0:
            self._update_target_network()

        return {'loss': loss.item()}

    def predict(self, state: Union[State, np.ndarray]) -> Tuple[List[float], float]:
        """
        预测状态的动作概率分布和价值

        Args:
            state (Union[State, np.ndarray]): 游戏状态或观察

        Returns:
            Tuple[List[float], float]: 动作概率分布、状态价值
        """
        # 处理状态
        if isinstance(state, State):
            # 将状态转换为特征向量
            state_array = state.to_dict().get('observation', np.zeros(self.state_dim))
            state_tensor = torch.FloatTensor(state_array).to(self.device)
        else:
            state_tensor = torch.FloatTensor(state).to(self.device)

        # 重塑状态
        if state_tensor.dim() == 1:
            state_tensor = state_tensor.unsqueeze(0)

        # 预测Q值
        with torch.no_grad():
            q_values = self.q_network(state_tensor).cpu().numpy()[0]

        # 转换为动作概率（将Q值通过softmax转换为概率）
        action_probs = np.zeros(self.action_dim)
        action_probs[np.argmax(q_values)] = 1.0

        # 状态价值是最大Q值
        state_value = float(np.max(q_values))

        return list(action_probs), state_value

    def save(self, path: str) -> None:
        """
        保存模型

        Args:
            path (str): 保存路径
        """
        # 确保目录存在
        os.makedirs(os.path.dirname(path), exist_ok=True)

        # 保存模型
        torch.save({
            'q_network_state_dict': self.q_network.state_dict(),
            'target_network_state_dict': self.target_network.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'update_count': self.update_count
        }, path)

    def load(self, path: str) -> None:
        """
        加载模型

        Args:
            path (str): 加载路径
        """
        # 加载模型
        checkpoint = torch.load(path, map_location=self.device)

        self.q_network.load_state_dict(checkpoint['q_network_state_dict'])
        self.target_network.load_state_dict(checkpoint['target_network_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.update_count = checkpoint['update_count']

    @property
    def name(self) -> str:
        """
        获取算法名称

        Returns:
            str: 算法名称
        """
        return "DQN"

    def _process_states(self, batch: Batch, next_state: bool = False) -> np.ndarray:
        """
        处理批次中的状态

        Args:
            batch (Batch): 经验批次
            next_state (bool, optional): 是否处理下一状态. Defaults to False.

        Returns:
            np.ndarray: 处理后的状态数组
        """
        states = []

        for experience in batch.experiences:
            if next_state:
                state = experience.next_state
            else:
                state = experience.state

            if isinstance(state, State):
                # 将状态转换为特征向量
                state_array = state.to_dict().get('observation', np.zeros(self.state_dim))
            else:
                state_array = state

            states.append(state_array)

        return np.array(states)

    def _process_actions(self, batch: Batch) -> np.ndarray:
        """
        处理批次中的动作

        Args:
            batch (Batch): 经验批次

        Returns:
            np.ndarray: 处理后的动作数组
        """
        actions = []

        for experience in batch.experiences:
            if isinstance(experience.action, Action):
                # 将动作转换为索引
                action = experience.action.to_dict().get('index', 0)
            else:
                action = experience.action

            actions.append(action)

        return np.array(actions)

    def _process_rewards(self, batch: Batch) -> np.ndarray:
        """
        处理批次中的奖励

        Args:
            batch (Batch): 经验批次

        Returns:
            np.ndarray: 处理后的奖励数组
        """
        return np.array([experience.reward for experience in batch.experiences])

    def _process_dones(self, batch: Batch) -> np.ndarray:
        """
        处理批次中的结束标志

        Args:
            batch (Batch): 经验批次

        Returns:
            np.ndarray: 处理后的结束标志数组
        """
        return np.array([float(experience.done) for experience in batch.experiences])

    def _update_target_network(self) -> None:
        """
        更新目标网络
        """
        # 软更新
        for target_param, local_param in zip(self.target_network.parameters(), self.q_network.parameters()):
            target_param.data.copy_(self.tau * local_param.data + (1.0 - self.tau) * target_param.data)


class DoubleDQN(DQN):
    """
    Double DQN算法

    实现Double DQN算法，减轻Q值过高估计问题。
    """

    def update(self, experience: Union[Experience, Batch]) -> Dict[str, float]:
        """
        使用经验数据更新模型

        Args:
            experience (Union[Experience, Batch]): 单个经验或经验批次

        Returns:
            Dict[str, float]: 更新指标，如损失值等
        """
        # 将经验添加到回放缓冲区
        if isinstance(experience, Experience):
            self.replay_buffer.add(experience)
        elif isinstance(experience, Batch):
            for exp in experience.experiences:
                self.replay_buffer.add(exp)

        # 检查缓冲区大小
        if len(self.replay_buffer) < self.batch_size:
            return {'loss': 0.0}

        # 检查更新频率
        self.update_count += 1
        if self.update_count % self.update_frequency != 0:
            return {'loss': 0.0}

        # 从缓冲区采样批次
        batch = self.replay_buffer.sample(self.batch_size)

        # 提取批次数据
        states = torch.FloatTensor(self._process_states(batch)).to(self.device)
        actions = torch.LongTensor(self._process_actions(batch)).to(self.device)
        rewards = torch.FloatTensor(self._process_rewards(batch)).to(self.device)
        next_states = torch.FloatTensor(self._process_states(batch, next_state=True)).to(self.device)
        dones = torch.FloatTensor(self._process_dones(batch)).to(self.device)

        # 计算当前Q值
        current_q_values = self.q_network(states).gather(1, actions.unsqueeze(1)).squeeze(1)

        # 计算目标Q值 (Double DQN)
        with torch.no_grad():
            # 使用当前网络选择动作
            next_actions = self.q_network(next_states).argmax(1, keepdim=True)
            # 使用目标网络评估动作
            next_q_values = self.target_network(next_states).gather(1, next_actions).squeeze(1)
            target_q_values = rewards + (1 - dones) * self.gamma * next_q_values

        # 计算损失
        loss = F.mse_loss(current_q_values, target_q_values)

        # 更新网络
        self.optimizer.zero_grad()
        loss.backward()
        self.optimizer.step()

        # 更新目标网络
        if self.update_count % self.target_update_frequency == 0:
            self._update_target_network()

        return {'loss': loss.item()}

    @property
    def name(self) -> str:
        """
        获取算法名称

        Returns:
            str: 算法名称
        """
        return "DoubleDQN"


class DuelingDQN(DQN):
    """
    Dueling DQN算法

    实现Dueling DQN算法，分别估计状态价值V和优势函数A。
    """

    def __init__(
        self,
        state_shape: Tuple[int, ...],
        action_shape: Tuple[int, ...],
        hidden_dims: List[int] = [128, 128],
        learning_rate: float = 0.001,
        buffer_size: int = 10000,
        batch_size: int = 64,
        gamma: float = 0.99,
        tau: float = 0.005,
        update_frequency: int = 4,
        target_update_frequency: int = 100,
        device: str = None
    ):
        """
        初始化Dueling DQN算法

        Args:
            state_shape (Tuple[int, ...]): 状态形状
            action_shape (Tuple[int, ...]): 动作形状
            hidden_dims (List[int], optional): 隐藏层维度. Defaults to [128, 128].
            learning_rate (float, optional): 学习率. Defaults to 0.001.
            buffer_size (int, optional): 经验回放缓冲区大小. Defaults to 10000.
            batch_size (int, optional): 批次大小. Defaults to 64.
            gamma (float, optional): 折扣因子. Defaults to 0.99.
            tau (float, optional): 目标网络更新系数. Defaults to 0.005.
            update_frequency (int, optional): 更新频率. Defaults to 4.
            target_update_frequency (int, optional): 目标网络更新频率. Defaults to 100.
            device (str, optional): 设备. Defaults to None.
        """
        # 调用父类初始化，但不创建网络
        super(ValueBasedAlgorithm, self).__init__(state_shape, action_shape, gamma)

        # 设置设备
        self.device = device if device is not None else ('cuda' if torch.cuda.is_available() else 'cpu')

        # 创建经验回放缓冲区
        self.replay_buffer = ReplayBuffer(buffer_size)

        # 计算状态和动作维度
        self.state_dim = np.prod(state_shape)
        self.action_dim = np.prod(action_shape)

        # 创建Dueling Q网络和目标网络
        self.q_network = DuelingQNetwork(self.state_dim, self.action_dim, hidden_dims).to(self.device)
        self.target_network = DuelingQNetwork(self.state_dim, self.action_dim, hidden_dims).to(self.device)

        # 复制参数到目标网络
        self.target_network.load_state_dict(self.q_network.state_dict())

        # 创建优化器
        self.optimizer = optim.Adam(self.q_network.parameters(), lr=learning_rate)

        # 设置超参数
        self.batch_size = batch_size
        self.tau = tau
        self.update_frequency = update_frequency
        self.target_update_frequency = target_update_frequency

        # 记录更新次数
        self.update_count = 0

    @property
    def name(self) -> str:
        """
        获取算法名称

        Returns:
            str: 算法名称
        """
        return "DuelingDQN"


# 为了兼容性，创建DQNAgent作为DQN的别名
DQNAgent = DQN