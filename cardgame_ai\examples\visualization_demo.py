"""
可视化系统示例

展示如何使用可视化系统的各种功能。
"""
import os
import json
import numpy as np
import matplotlib.pyplot as plt
import random
from typing import Dict, Any, List, Tuple

from cardgame_ai.visualization import (
    VisualizationConfig, 
    TrainingVisualizer, 
    GameVisualizer,
    default_config
)
from cardgame_ai.visualization.integration import VisualizationIntegrator

# 示例数据生成函数
def generate_training_log(num_episodes: int = 100, 
                         save_path: str = 'demo_data/training_log.jsonl') -> str:
    """
    生成示例训练日志
    
    Args:
        num_episodes (int, optional): 回合数. Defaults to 100.
        save_path (str, optional): 保存路径. Defaults to 'demo_data/training_log.jsonl'.
        
    Returns:
        str: 日志文件路径
    """
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    
    # 生成模拟训练数据
    with open(save_path, 'w') as f:
        for episode in range(1, num_episodes + 1):
            # 生成模拟损失（随着训练逐渐下降）
            loss = max(0.1, 2.0 * np.exp(-0.02 * episode) + 0.2 * np.random.randn())
            
            # 生成模拟奖励（随着训练逐渐上升）
            reward = min(1.0, 0.2 + 0.8 * (1 - np.exp(-0.03 * episode)) + 0.1 * np.random.randn())
            
            # 生成模拟胜率（随着训练逐渐上升）
            win_rate = min(0.9, 0.1 + 0.8 * (1 - np.exp(-0.025 * episode)) + 0.05 * np.random.randn())
            
            # 创建日志条目
            log_entry = {
                'episode': episode,
                'timestamp': f"2023-01-01T{episode:02d}:00:00",
                'losses': {
                    'total_loss': loss,
                    'policy_loss': loss * 0.6 + 0.05 * np.random.randn(),
                    'value_loss': loss * 0.4 + 0.05 * np.random.randn()
                },
                'rewards': {
                    'mean_reward': reward,
                    'max_reward': reward + 0.2 * np.random.rand(),
                    'min_reward': max(0, reward - 0.3 * np.random.rand())
                },
                'metrics': {
                    'win_rate': win_rate,
                    'draw_rate': 0.1 + 0.05 * np.random.rand(),
                    'mean_game_length': 50 + 10 * np.random.randn()
                },
                'training_time': 10 + 2 * np.random.rand(),
                'experience_buffer_size': min(10000, episode * 100)
            }
            
            # 写入文件
            f.write(json.dumps(log_entry) + '\n')
    
    print(f"训练日志已生成：{save_path}")
    return save_path

def generate_evaluation_results(num_agents: int = 3, num_games: int = 50,
                             save_path: str = 'demo_data/eval_results.json') -> str:
    """
    生成示例评估结果
    
    Args:
        num_agents (int, optional): 智能体数量. Defaults to 3.
        num_games (int, optional): 游戏数量. Defaults to 50.
        save_path (str, optional): 保存路径. Defaults to 'demo_data/eval_results.json'.
        
    Returns:
        str: 评估结果文件路径
    """
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    
    agent_names = ['DQN', 'PPO', 'Random'] if num_agents == 3 else \
                 [f"Agent{i+1}" for i in range(num_agents)]
    
    # 设置性能差异
    win_weights = [0.6, 0.3, 0.1] if num_agents == 3 else \
                 [random.uniform(0.1, 0.9) for _ in range(num_agents)]
    
    # 确保权重总和为1
    win_weights = [w / sum(win_weights) for w in win_weights]
    
    # 生成胜利结果
    win_counts = {name: 0 for name in agent_names}
    game_lengths = []
    rewards = {name: [] for name in agent_names}
    decision_times = {name: [] for name in agent_names}
    
    for _ in range(num_games):
        # 随机选择胜者，根据权重
        winner = np.random.choice(agent_names, p=win_weights)
        win_counts[winner] += 1
        
        # 随机游戏长度
        game_length = int(40 + 20 * np.random.randn())
        game_length = max(10, game_length)
        game_lengths.append(game_length)
        
        # 随机奖励
        for name in agent_names:
            if name == winner:
                reward = 1.0 + 0.2 * np.random.randn()
            else:
                reward = -0.5 + 0.3 * np.random.randn()
            rewards[name].append(reward)
        
        # 随机决策时间
        for name in agent_names:
            # 更好的智能体决策时间更短
            idx = agent_names.index(name)
            base_time = 0.02 + 0.03 * idx
            times = [base_time + 0.01 * np.random.randn() for _ in range(game_length // 3)]
            decision_times[name].extend([max(0.001, t) for t in times])
    
    # 计算平均值
    win_rates = {name: count / num_games for name, count in win_counts.items()}
    mean_rewards = {name: np.mean(rews) for name, rews in rewards.items()}
    mean_decision_times = {name: np.mean(times) for name, times in decision_times.items()}
    
    # 创建评估结果
    results = {
        'num_games': num_games,
        'agent_names': agent_names,
        'win_counts': win_counts,
        'win_rates': win_rates,
        'game_lengths': game_lengths,
        'mean_game_length': np.mean(game_lengths),
        'rewards': rewards,
        'mean_rewards': mean_rewards,
        'decision_times': decision_times,
        'mean_decision_times': mean_decision_times,
        'timestamp': "2023-01-02T12:00:00"
    }
    
    # 保存结果
    with open(save_path, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"评估结果已生成：{save_path}")
    return save_path

def generate_hyperparameter_results(num_trials: int = 50,
                                  save_path: str = 'demo_data/hyperparameter_results.json') -> str:
    """
    生成示例超参数调优结果
    
    Args:
        num_trials (int, optional): 试验数量. Defaults to 50.
        save_path (str, optional): 保存路径. Defaults to 'demo_data/hyperparameter_results.json'.
        
    Returns:
        str: 超参数结果文件路径
    """
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    
    # 定义超参数范围
    param_ranges = {
        'learning_rate': (0.0001, 0.01),
        'discount_factor': (0.9, 0.999),
        'batch_size': [32, 64, 128, 256],
        'hidden_size': [64, 128, 256, 512],
        'epsilon_decay': (0.99, 0.9999),
        'target_update_interval': [100, 200, 500, 1000],
        'double_dqn': [True, False],
        'dueling_dqn': [True, False]
    }
    
    # 定义超参数重要性（用于生成相关性）
    param_importance = {
        'learning_rate': 0.8,
        'discount_factor': 0.5,
        'batch_size': 0.3,
        'hidden_size': 0.6,
        'epsilon_decay': 0.4,
        'target_update_interval': 0.2,
        'double_dqn': 0.7,
        'dueling_dqn': 0.6
    }
    
    trials = []
    
    for i in range(num_trials):
        # 随机生成超参数配置
        params = {}
        for param, range_val in param_ranges.items():
            if isinstance(range_val, tuple):
                # 连续参数
                params[param] = float(np.random.uniform(range_val[0], range_val[1]))
            elif isinstance(range_val, list):
                # 离散参数
                params[param] = random.choice(range_val)
        
        # 生成目标分数（基于参数重要性）
        score = 0.2 + np.random.randn() * 0.05  # 基础分数和噪声
        
        for param, value in params.items():
            importance = param_importance.get(param, 0.1)
            
            if isinstance(value, bool):
                # 布尔参数，True认为是更好的选择
                if value:
                    score += importance * 0.5
                else:
                    score -= importance * 0.2
            elif param == 'learning_rate':
                # 学习率有最佳点，过大或过小都不好
                optimum = 0.001
                score -= importance * abs(np.log10(value) - np.log10(optimum)) * 0.7
            elif param in ['batch_size', 'hidden_size']:
                # 大一些通常更好，但有边际收益递减
                norm_value = (np.log2(value) - 5) / 4  # 归一化到大约[-0.25, 1]范围
                score += importance * min(norm_value, 0.8) * 0.5
            elif param == 'discount_factor':
                # 接近1通常更好
                score += importance * (value - 0.9) * 5
            elif param == 'epsilon_decay':
                # 接近1但不要太接近
                score += importance * (0.5 - abs(value - 0.998) * 50)
            elif param == 'target_update_interval':
                # 中等值最好
                optimum = 400
                score -= importance * abs(value - optimum) / 500
        
        # 添加试验结果
        trials.append({
            'params': params,
            'score': max(0, min(1, score)),  # 限制在[0, 1]范围内
            'trial_id': i + 1,
            'timestamp': f"2023-01-03T{i//4:02d}:{(i%4)*15:02d}:00"
        })
    
    # 按分数排序
    trials.sort(key=lambda x: x['score'], reverse=True)
    
    # 创建结果
    results = {
        'num_trials': num_trials,
        'best_params': trials[0]['params'],
        'best_score': trials[0]['score'],
        'param_ranges': param_ranges,
        'trials': trials,
        'method': 'bayesian_optimization',
        'timestamp': "2023-01-03T18:00:00"
    }
    
    # 保存结果
    with open(save_path, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"超参数调优结果已生成：{save_path}")
    return save_path

def generate_game_record(save_path: str = 'demo_data/game_record.json') -> Tuple[Dict[str, Any], str]:
    """
    生成示例游戏记录
    
    Args:
        save_path (str, optional): 保存路径. Defaults to 'demo_data/game_record.json'.
        
    Returns:
        Tuple[Dict[str, Any], str]: 游戏记录和文件路径
    """
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    
    # 模拟一局游戏数据
    num_actions = 30
    players = [0, 1, 2]  # 三个玩家
    
    # 生成动作序列
    actions = []
    for i in range(num_actions):
        player = players[i % len(players)]
        
        action_type = random.choice(['出牌', '过牌'])
        if action_type == '出牌':
            action = random.randint(1, 13)  # 简化的牌值
        else:
            action = 0  # 过牌用0表示
        
        # 决策时间
        decision_time = max(0.01, 0.1 + 0.1 * np.random.randn())
        
        # 创建动作记录
        action_record = {
            'turn': i + 1,
            'player': player,
            'action': action,
            'action_type': action_type,
            'decision_time': decision_time
        }
        
        actions.append(action_record)
    
    # 随机选择获胜者
    winner = random.choice(players)
    
    # 创建游戏记录
    game_record = {
        'game_id': 'demo-game-1',
        'players': players,
        'winner': winner,
        'game_length': num_actions,
        'actions': actions,
        'timestamp': "2023-01-04T12:30:00"
    }
    
    # 保存记录
    with open(save_path, 'w') as f:
        json.dump(game_record, f, indent=2)
    
    print(f"游戏记录已生成：{save_path}")
    return game_record, save_path

def generate_decision_data(save_path: str = 'demo_data/decision_data.json') -> Tuple[Dict[str, Any], str]:
    """
    生成示例决策数据
    
    Args:
        save_path (str, optional): 保存路径. Defaults to 'demo_data/decision_data.json'.
        
    Returns:
        Tuple[Dict[str, Any], str]: 决策数据和文件路径
    """
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    
    # 生成动作概率
    num_actions = 10
    actions = list(range(1, num_actions + 1))
    
    # 非均匀概率分布
    probs = np.random.dirichlet(np.ones(num_actions) * 0.5)
    action_probs = {str(a): float(p) for a, p in zip(actions, probs)}
    
    # 选择概率最高的动作
    selected_action = int(max(action_probs.items(), key=lambda x: x[1])[0])
    
    # 创建决策数据
    decision_data = {
        'player': 0,
        'turn': 5,
        'action_probs': action_probs,
        'selected_action': selected_action,
        'decision_time': 0.12,
        'temperature': 1.0,
        'state_value': 0.7
    }
    
    # 保存数据
    with open(save_path, 'w') as f:
        json.dump(decision_data, f, indent=2)
    
    print(f"决策数据已生成：{save_path}")
    return decision_data, save_path

def demo_training_visualization():
    """
    演示训练可视化功能
    """
    print("\n=== 演示训练可视化 ===")
    
    # 生成示例数据
    log_file = generate_training_log(num_episodes=100)
    eval_file = generate_evaluation_results(num_agents=3, num_games=50)
    
    # 创建训练可视化器
    training_viz = TrainingVisualizer(save_dir='demo_output/training')
    
    # 1. 绘制训练曲线
    print("1. 绘制训练曲线...")
    fig1 = training_viz.plot_training_curve(
        log_file=log_file,
        metrics=['losses.total_loss', 'rewards.mean_reward', 'metrics.win_rate'],
        window_size=5,
        save_as='training_curves.png'
    )
    
    # 2. 绘制奖励分布
    print("2. 绘制奖励分布...")
    fig2 = training_viz.plot_reward_distribution(
        eval_results_file=eval_file,
        save_as='reward_distribution.png'
    )
    
    # 3. 绘制胜率
    print("3. 绘制胜率...")
    fig3 = training_viz.plot_win_rates(
        eval_results_files=[eval_file],
        labels=['评估结果'],
        save_as='win_rates.png'
    )
    
    # 4. 创建训练仪表盘
    print("4. 创建训练仪表盘...")
    fig4 = training_viz.plot_training_dashboard(
        log_file=log_file,
        eval_file=eval_file,
        save_as='training_dashboard.png'
    )
    
    print("训练可视化演示完成！输出保存在 demo_output/training 目录中")
    return fig1, fig2, fig3, fig4

def demo_game_visualization():
    """
    演示游戏可视化功能
    """
    print("\n=== 演示游戏可视化 ===")
    
    # 生成示例数据
    game_record, game_file = generate_game_record()
    decision_data, decision_file = generate_decision_data()
    
    # 创建游戏可视化器
    game_viz = GameVisualizer(save_dir='demo_output/games')
    
    # 1. 可视化游戏记录
    print("1. 可视化游戏记录...")
    fig1 = game_viz.visualize_game_record(
        game_record=game_record,
        save_as='game_record.png'
    )
    
    # 2. 可视化决策过程
    print("2. 可视化决策过程...")
    fig2 = game_viz.visualize_decision_process(
        decision_data=decision_data,
        save_as='decision_process.png'
    )
    
    # 3. 生成状态价值热力图
    print("3. 生成状态价值热力图...")
    # 生成一些状态价值示例
    num_states = 15
    state_ids = [f"状态{i}" for i in range(1, num_states + 1)]
    state_values = {}
    for i, state_id in enumerate(state_ids):
        # 一些状态有正值，一些有负值
        value = 2 * np.random.rand() - 1
        state_values[state_id] = value
    
    fig3 = game_viz.visualize_value_heatmap(
        state_values=state_values,
        save_as='value_heatmap.png'
    )
    
    print("游戏可视化演示完成！输出保存在 demo_output/games 目录中")
    return fig1, fig2, fig3

def demo_integration():
    """
    演示集成功能
    """
    print("\n=== 演示可视化集成 ===")
    
    # 生成示例数据
    log_file = generate_training_log(num_episodes=100)
    eval_file = generate_evaluation_results(num_agents=3, num_games=50)
    game_record, game_file = generate_game_record()
    hyper_file = generate_hyperparameter_results(num_trials=50)
    
    # 创建集成器
    integrator = VisualizationIntegrator(save_dir='demo_output/integrated')
    
    # 1. 可视化训练过程
    print("1. 可视化训练过程...")
    figs1 = integrator.visualize_training_process(
        log_file=log_file,
        eval_file=eval_file,
        metrics=['losses.total_loss', 'rewards.mean_reward', 'metrics.win_rate']
    )
    
    # 2. 可视化超参数调优结果
    print("2. 可视化超参数调优结果...")
    fig2 = integrator.visualize_hyperparameter_tuning(
        results_file=hyper_file,
        top_n=8
    )
    
    # 3. 可视化游戏记录
    print("3. 可视化游戏记录...")
    fig3 = integrator.visualize_game_record(
        game_record=game_record,
        save_as='integrated_game.png'
    )
    
    # 模拟多个评估结果文件
    eval_files = []
    agent_names = ['DQN', 'PPO', 'A2C', 'REINFORCE']
    for i, agent in enumerate(agent_names[:3]):
        weights = [0.1] * 3
        weights[i] = 0.8  # 让当前智能体表现更好
        file = f'demo_data/eval_agent{i+1}.json'
        eval_files.append(generate_evaluation_results(
            num_agents=3, 
            num_games=30, 
            save_path=file
        ))
    
    # 4. 可视化智能体比较
    print("4. 可视化智能体比较...")
    fig4 = integrator.visualize_agent_comparison(
        eval_results_files=eval_files,
        agent_names=agent_names[:3],
        metrics=['win_rate', 'mean_reward']
    )
    
    print("集成可视化演示完成！输出保存在 demo_output/integrated 目录中")
    return figs1, fig2, fig3, fig4

def demo_configuration():
    """
    演示配置功能
    """
    print("\n=== 演示可视化配置 ===")
    
    # 创建不同主题的配置
    themes = ['default', 'dark', 'pastel', 'colorblind']
    
    for theme in themes:
        print(f"生成 {theme} 主题的示例图表...")
        
        # 创建配置
        config = VisualizationConfig()
        config.update_config({'theme': theme, 'figure_size': 'medium'})
        
        # 创建图形和轴
        fig, ax = config.create_figure()
        
        # 绘制示例数据
        x = np.linspace(0, 10, 100)
        for i in range(5):
            ax.plot(x, np.sin(x + i*0.5), 
                   label=f'序列 {i+1}',
                   linewidth=2)
        
        ax.set_title(f'{theme.capitalize()} 主题示例')
        ax.set_xlabel('X轴')
        ax.set_ylabel('Y轴')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 保存图形
        save_path = config.save_figure(fig, f'{theme}_theme_example.png', subdir='themes')
    
    print("配置演示完成！输出保存在 demo_output/visualizations/themes 目录中")

def main():
    """
    主函数，运行所有演示
    """
    print("=== 卡牌游戏AI可视化系统示例 ===")
    
    # 确保输出目录存在
    os.makedirs('demo_output', exist_ok=True)
    
    # 运行训练可视化演示
    demo_training_visualization()
    
    # 运行游戏可视化演示
    demo_game_visualization()
    
    # 运行集成演示
    demo_integration()
    
    # 运行配置演示
    demo_configuration()
    
    print("\n所有演示已完成！请查看 demo_output 目录以查看结果。")

if __name__ == "__main__":
    main() 