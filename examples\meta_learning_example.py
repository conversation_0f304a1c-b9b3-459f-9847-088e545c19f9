"""
MAML元学习示例脚本

展示如何使用MAML元学习框架对斗地主游戏中的模型进行训练，以便快速适应新对手。
"""

import os
import time
import argparse
import logging
import numpy as np
import torch
import torch.optim as optim
from typing import Dict, List, Tuple, Any

from cardgame_ai.algorithms.transformer_policy import TransformerPolicy
from cardgame_ai.training.meta_learner import (
    MetaTask,
    MetaTaskSampler,
    MAMLMetaLearner
)
from cardgame_ai.core.base import Experience
from cardgame_ai.games.doudizhu import DoudizhuEnv
from cardgame_ai.games.doudizhu import <PERSON><PERSON>zhuAgent
from cardgame_ai.agents import RandomAgent, RuleBasedAgent, HumanAgent

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_opponent_agents(num_opponents=5, styles=None):
    """
    创建多种风格的对手智能体
    
    Args:
        num_opponents: 对手数量
        styles: 对手风格列表，如["aggressive", "defensive", "balanced"]
        
    Returns:
        对手智能体列表
    """
    if styles is None:
        styles = ["aggressive", "defensive", "balanced", "random", "conservative"]
    
    agents = []
    
    for i in range(num_opponents):
        # 选择风格
        style = styles[i % len(styles)]
        
        # 根据风格创建智能体
        if style == "random":
            # 完全随机智能体
            agent = RandomAgent()
        elif style == "aggressive":
            # 激进风格：总是出最大的牌
            agent = RuleBasedAgent(aggression=0.9, risk_taking=0.7)
        elif style == "defensive":
            # 防守风格：保留大牌
            agent = RuleBasedAgent(aggression=0.2, risk_taking=0.3)
        elif style == "balanced":
            # 平衡风格
            agent = RuleBasedAgent(aggression=0.5, risk_taking=0.5)
        elif style == "conservative":
            # 保守风格：出小牌，避免风险
            agent = RuleBasedAgent(aggression=0.3, risk_taking=0.2)
        else:
            # 默认为随机智能体
            agent = RandomAgent()
        
        agents.append(agent)
    
    return agents


def collect_experiences(agent, opponent, env, num_episodes=10):
    """
    收集智能体与对手交互的经验
    
    Args:
        agent: 智能体
        opponent: 对手
        env: 环境
        num_episodes: 对局次数
        
    Returns:
        收集到的经验列表
    """
    experiences = []
    
    for _ in range(num_episodes):
        # 重置环境
        state = env.reset()
        done = False
        
        while not done:
            # 当前玩家
            current_player = env.current_player
            
            if current_player == 0:  # 主智能体
                # 获取动作
                action = agent.act(state)
                
                # 记录经验
                next_state, reward, done, info = env.step(action)
                
                # 创建Experience对象
                exp = {
                    "state": state,
                    "action": action,
                    "reward": reward,
                    "next_state": next_state,
                    "done": done
                }
                
                experiences.append(exp)
                
                # 更新状态
                state = next_state
            else:  # 对手
                # 获取对手动作
                action = opponent.act(state)
                
                # 执行动作
                next_state, reward, done, info = env.step(action)
                
                # 更新状态
                state = next_state
    
    return experiences


def create_meta_tasks(num_tasks=5, episodes_per_task=10):
    """
    创建元任务
    
    Args:
        num_tasks: 任务数量
        episodes_per_task: 每个任务的对局次数
        
    Returns:
        任务采样器
    """
    # 创建任务采样器
    task_sampler = MetaTaskSampler()
    
    # 创建环境
    env = DoudizhuEnv()
    
    # 创建主智能体
    main_agent = RandomAgent()  # 使用随机智能体收集初始数据
    
    # 创建对手
    opponents = create_opponent_agents(num_tasks)
    
    # 为每个对手创建任务
    for i, opponent in enumerate(opponents):
        # 创建任务配置
        task_config = {
            "opponent_type": opponent.__class__.__name__,
            "opponent_style": ["aggressive", "defensive", "balanced", "random", "conservative"][i % 5],
            "difficulty": np.random.uniform(0.1, 1.0)
        }
        
        # 创建任务
        task_id = f"opponent_{i}"
        task = MetaTask(task_id, task_config)
        
        # 收集经验
        experiences = collect_experiences(main_agent, opponent, env, num_episodes=episodes_per_task)
        
        # 划分支持集和查询集
        split_idx = int(len(experiences) * 0.7)
        support_data = experiences[:split_idx]
        query_data = experiences[split_idx:]
        
        # 添加数据到任务
        task.add_support_data(support_data)
        task.add_query_data(query_data)
        
        # 添加任务到采样器
        task_sampler.add_task(task)
        
        logger.info(f"已创建任务 {task_id}，支持集大小: {len(support_data)}，查询集大小: {len(query_data)}")
    
    return task_sampler


def main(args):
    """
    主函数
    
    Args:
        args: 命令行参数
    """
    # 设置设备
    device = torch.device("cuda" if torch.cuda.is_available() and not args.cpu else "cpu")
    logger.info(f"使用设备: {device}")
    
    # 检查higher库是否可用
    try:
        import higher
        logger.info("higher库可用，将使用MAML算法")
    except ImportError:
        logger.error("higher库不可用，MAML算法将不可用。请使用pip install higher安装。")
        return
    
    # 创建任务
    logger.info("创建元任务...")
    task_sampler = create_meta_tasks(num_tasks=args.num_tasks, episodes_per_task=args.episodes_per_task)
    
    # 创建基础模型
    logger.info("创建Transformer策略模型...")
    base_model = TransformerPolicy(
        state_shape=(100, 64),  # 根据游戏状态设置合适的尺寸
        action_shape=(1000,),   # 根据动作空间设置合适的尺寸
        hidden_dim=args.hidden_dim,
        num_heads=args.num_heads,
        num_layers=args.num_layers,
        device=device
    )
    
    # 创建元优化器
    meta_optimizer = optim.Adam(base_model.parameters(), lr=args.meta_lr)
    
    # 创建MAML元学习器
    logger.info("创建MAML元学习器...")
    maml = MAMLMetaLearner(
        base_model=base_model,
        meta_optimizer=meta_optimizer,
        task_sampler=task_sampler,
        inner_lr=args.inner_lr,
        num_inner_steps=args.num_inner_steps,
        first_order=args.first_order,
        device=device
    )
    
    # 元训练
    logger.info("开始元训练...")
    for step in range(args.meta_steps):
        start_time = time.time()
        
        # 执行元训练步骤
        meta_loss = maml.meta_train_step(num_tasks=args.num_tasks_per_step)
        
        # 记录训练信息
        time_spent = time.time() - start_time
        logger.info(f"Meta Step {step + 1}/{args.meta_steps}, Meta Loss: {meta_loss:.4f}, Time: {time_spent:.2f}s")
        
        # 保存检查点
        if (step + 1) % args.save_interval == 0:
            checkpoint_path = os.path.join(args.save_dir, f"maml_checkpoint_{step + 1}.pt")
            os.makedirs(args.save_dir, exist_ok=True)
            maml.save(checkpoint_path)
            logger.info(f"保存检查点到 {checkpoint_path}")
    
    # 保存最终模型
    final_path = os.path.join(args.save_dir, "maml_final.pt")
    maml.save(final_path)
    logger.info(f"保存最终模型到 {final_path}")
    
    # 测试快速适应
    if args.test_adaptation:
        logger.info("测试快速适应...")
        
        # 采样一个任务进行测试
        test_tasks = task_sampler.sample_tasks(num_tasks=1)
        if test_tasks:
            test_task = test_tasks[0]
            
            # 评估基础模型
            base_results = maml.evaluate(test_task)
            logger.info(f"基础模型性能: 损失={base_results['loss']:.4f}, 准确率={base_results['accuracy']:.4f}")
            
            # 快速适应
            adapted_model = maml.adapt(test_task, num_steps=args.adapt_steps)
            
            # 评估适应后的模型
            adapted_results = maml.evaluate(test_task, adapted_model)
            logger.info(f"适应后模型性能: 损失={adapted_results['loss']:.4f}, 准确率={adapted_results['accuracy']:.4f}")
            
            # 保存适应后的模型
            adapt_path = os.path.join(args.save_dir, f"adapted_model_{test_task.task_id}.pt")
            torch.save(adapted_model.state_dict(), adapt_path)
            logger.info(f"保存适应后的模型到 {adapt_path}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="MAML元学习示例")
    
    # 模型参数
    parser.add_argument("--hidden-dim", type=int, default=256, help="隐藏层维度")
    parser.add_argument("--num-heads", type=int, default=4, help="注意力头数量")
    parser.add_argument("--num-layers", type=int, default=4, help="Transformer层数量")
    
    # 元学习参数
    parser.add_argument("--meta-lr", type=float, default=0.001, help="元学习率")
    parser.add_argument("--inner-lr", type=float, default=0.01, help="内循环学习率")
    parser.add_argument("--num-inner-steps", type=int, default=5, help="内循环步数")
    parser.add_argument("--first-order", action="store_true", help="使用一阶近似")
    
    # 训练参数
    parser.add_argument("--num-tasks", type=int, default=5, help="任务数量")
    parser.add_argument("--episodes-per-task", type=int, default=10, help="每个任务的对局次数")
    parser.add_argument("--meta-steps", type=int, default=100, help="元训练步数")
    parser.add_argument("--num-tasks-per-step", type=int, default=3, help="每步使用的任务数量")
    
    # 测试参数
    parser.add_argument("--test-adaptation", action="store_true", help="测试快速适应")
    parser.add_argument("--adapt-steps", type=int, default=3, help="适应步数")
    
    # 其他参数
    parser.add_argument("--cpu", action="store_true", help="强制使用CPU")
    parser.add_argument("--save-dir", type=str, default="models/meta_learning", help="保存目录")
    parser.add_argument("--save-interval", type=int, default=10, help="保存间隔")
    
    args = parser.parse_args()
    
    main(args) 