"""
人机协同模块

实现人机协同相关的功能，包括人机交互数据收集、人类反馈学习和动态角色分配与信任度估计。
"""

import os
import time
import logging
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple, Union, Callable

from cardgame_ai.core.base import State, Action
from cardgame_ai.games.doudizhu.state import Dou<PERSON><PERSON>huState
from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType

# 配置日志
logger = logging.getLogger(__name__)


class TrustEstimator:
    """
    信任度估计器类

    评估人类玩家的信任度，通过比较人类玩家的动作与AI建议动作的相似度来估计信任水平。
    此信任水平用于动态调整AI介入程度。
    """

    def __init__(
        self,
        confidence_threshold: float = 0.7,
        window_size: int = 10,
        initial_trust_level: float = 0.5,
        learning_rate: float = 0.1,
        min_trust_level: float = 0.0,
        max_trust_level: float = 1.0
    ):
        """
        初始化信任度估计器

        Args:
            confidence_threshold: 信心阈值，超过此值认为人类玩家可信
            window_size: 历史窗口大小，用于平滑信任度估计
            initial_trust_level: 初始信任度，范围[0, 1]
            learning_rate: 学习率，控制信任度更新速度
            min_trust_level: 最小信任度
            max_trust_level: 最大信任度
        """
        self.confidence_threshold = confidence_threshold
        self.window_size = window_size
        self.similarity_history = []
        self.current_trust_level = initial_trust_level
        self.learning_rate = learning_rate
        self.min_trust_level = min_trust_level
        self.max_trust_level = max_trust_level

        # 统计信息
        self.stats = {
            "updates": 0,
            "avg_similarity": 0.0,
            "min_similarity": 1.0,
            "max_similarity": 0.0,
            "trust_level_history": []
        }

    def update_trust_level(self, human_action: Action, ai_suggested_action: Action, state: State) -> float:
        """
        更新人类玩家的信任度

        通过比较人类动作与AI建议动作的相似度来更新信任度。

        Args:
            human_action: 人类玩家的实际动作
            ai_suggested_action: AI建议的动作
            state: 当前游戏状态

        Returns:
            float: 更新后的信任度（0-1之间的值）
        """
        # 计算人类行动与AI建议行动的相似度
        similarity = self._compute_action_similarity(human_action, ai_suggested_action, state)

        # 更新信任历史
        self.similarity_history.append(similarity)
        if len(self.similarity_history) > self.window_size:
            self.similarity_history.pop(0)

        # 更新当前信任水平：如果相似度高，增加信任；如果相似度低，降低信任
        target_trust = similarity  # 简化：相似度作为目标信任度
        self.current_trust_level = (1 - self.learning_rate) * self.current_trust_level + self.learning_rate * target_trust

        # 确保信任度在有效范围内
        self.current_trust_level = max(self.min_trust_level, min(self.max_trust_level, self.current_trust_level))

        # 更新统计信息
        self.stats["updates"] += 1
        self.stats["avg_similarity"] = sum(self.similarity_history) / len(self.similarity_history) if self.similarity_history else 0.0
        self.stats["min_similarity"] = min(self.stats["min_similarity"], similarity)
        self.stats["max_similarity"] = max(self.stats["max_similarity"], similarity)
        self.stats["trust_level_history"].append(self.current_trust_level)

        logger.debug(f"更新信任度: {self.current_trust_level:.4f}, 动作相似度: {similarity:.4f}")

        return self.current_trust_level

    def is_human_trusted(self) -> bool:
        """
        判断当前人类玩家是否可信

        Returns:
            bool: 是否可信的布尔值
        """
        return self.current_trust_level >= self.confidence_threshold

    def get_trust_level(self) -> float:
        """
        获取当前信任度

        Returns:
            float: 当前信任度
        """
        return self.current_trust_level

    def get_ai_intervention_level(self) -> float:
        """
        获取AI应该干预的程度

        Returns:
            float: AI干预程度（0-1之间的值）
        """
        # 干预程度与信任度成反比
        return 1.0 - self.current_trust_level

    def _compute_action_similarity(self, action1: Action, action2: Action, state: State) -> float:
        """
        计算两个动作的相似度

        Args:
            action1: 第一个动作
            action2: 第二个动作
            state: 当前游戏状态

        Returns:
            float: 相似度，范围[0, 1]
        """
        # 如果动作完全相同，返回最高相似度
        if action1 == action2:
            return 1.0

        # 如果其中一个动作为None，返回最低相似度
        if action1 is None or action2 is None:
            return 0.0

        # 检查是否为斗地主游戏状态和动作
        if isinstance(state, DouDizhuState) and isinstance(action1, CardGroup) and isinstance(action2, CardGroup):
            return self._calculate_doudizhu_action_similarity(action1, action2, state)

        # 尝试使用动作的内部比较方法（如果有）
        if hasattr(action1, 'similarity_to') and callable(getattr(action1, 'similarity_to')):
            return action1.similarity_to(action2)

        # 如果没有特定的相似度计算方法，使用二元相似度（相同为1，不同为0）
        return 0.0

    def _calculate_doudizhu_action_similarity(self, action1: CardGroup, action2: CardGroup, state: DouDizhuState) -> float:
        """
        计算斗地主游戏中两个动作的相似度

        Args:
            action1: 第一个动作
            action2: 第二个动作
            state: 当前游戏状态

        Returns:
            float: 相似度，范围[0, 1]
        """
        # 如果牌型不同，相似度较低
        if action1.card_type != action2.card_type:
            # 如果都是PASS，相似度为1
            if action1.card_type == CardGroupType.PASS and action2.card_type == CardGroupType.PASS:
                return 1.0
            # 其他牌型不同的情况，相似度为0.1
            return 0.1

        # 牌型相同，计算牌值相似度
        # 计算牌面值差异
        value_similarity = self._calculate_card_value_similarity(action1, action2)

        # 计算牌数量相似度
        count_similarity = 1.0 if len(action1.cards) == len(action2.cards) else 0.8

        # 综合相似度（牌型相同时，主要考虑牌值相似度）
        return 0.7 * value_similarity + 0.3 * count_similarity

    def _calculate_card_value_similarity(self, action1: CardGroup, action2: CardGroup) -> float:
        """
        计算两个牌组的牌值相似度

        Args:
            action1: 第一个牌组
            action2: 第二个牌组

        Returns:
            float: 牌值相似度，范围[0, 1]
        """
        # 如果牌组长度不同，使用最小长度进行比较
        min_length = min(len(action1.cards), len(action2.cards))

        # 如果没有牌，返回最高相似度
        if min_length == 0:
            return 1.0

        # 计算牌值差异
        total_diff = 0
        for i in range(min_length):
            # 假设牌有数值属性，计算差异
            if hasattr(action1.cards[i], 'value') and hasattr(action2.cards[i], 'value'):
                total_diff += abs(action1.cards[i].value - action2.cards[i].value)
            else:
                # 如果没有数值属性，使用二元比较
                total_diff += 0 if action1.cards[i] == action2.cards[i] else 1

        # 归一化差异到0-1之间的相似度
        max_possible_diff = 15 * min_length  # 假设最大牌值差为15
        normalized_diff = min(1.0, total_diff / max_possible_diff if max_possible_diff > 0 else 0)

        return 1.0 - normalized_diff

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            Dict[str, Any]: 统计信息字典
        """
        # 更新一些统计信息
        self.stats["current_trust_level"] = self.current_trust_level
        self.stats["is_trusted"] = self.is_human_trusted()
        self.stats["intervention_level"] = self.get_ai_intervention_level()

        return self.stats
