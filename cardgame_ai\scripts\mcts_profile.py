#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCTS性能剖析脚本
用于对MCTS.run方法进行CPU和内存使用剖析
"""
import os
import cProfile
import torch
import numpy as np
# 移除memory_profiler导入
from cardgame_ai.algorithms.mcts import MCTS
import time
import json


class MockModel:
    """
    模拟模型类，用于MCTS测试
    提供represent、dynamics和predict方法
    """
    def __init__(self):
        self.state_size = 64
        self.action_size = 10

    def represent(self, state):
        """返回状态的隐藏表示"""
        # 返回随机向量作为隐藏状态
        return torch.rand(self.state_size)

    def dynamics(self, hidden_state, action):
        """预测下一个隐藏状态和奖励"""
        # 返回随机向量作为下一个隐藏状态和随机奖励
        next_state = torch.rand(self.state_size)
        reward = torch.rand(1).item()
        return next_state, reward

    def predict(self, hidden_state):
        """预测策略和价值"""
        # 返回随机策略和价值
        policy = torch.softmax(torch.rand(self.action_size), dim=0).tolist()
        value = torch.rand(1).item()
        return policy, value


def get_simplified_state():
    """
    获取简化的初始状态，用于MCTS测试。
    """
    # 返回随机数组作为状态
    return np.random.rand(64)


# 移除profile装饰器
def run_search(root_state, model=None):
    """
    单次运行MCTS搜索，用于性能剖析
    Args:
        root_state: 搜索的初始状态
        model: 可选的模型，用于MuZero等场景
    Returns:
        搜索结果，包括动作选择和策略概率
    """
    # 如果没有提供模型，创建一个模拟模型
    if model is None:
        model = MockModel()

    # 实例化MCTS算法，模拟次数可按需调整
    mcts = MCTS(num_simulations=50)

    # 执行搜索并返回结果
    return mcts.run(root_state, model=model)


def main():
    print("开始MCTS性能剖析...")
    # 设置随机种子，保证实验可重复
    torch.manual_seed(42)
    np.random.seed(42)

    # 获取简化的初始状态
    print("创建简化状态...")
    root_state = get_simplified_state()

    # 创建模拟模型
    print("创建模拟模型...")
    model = MockModel()

    # 性能测量: 记录运行时间和内存使用
    print("开始执行MCTS搜索...")
    start_time = time.time()
    try:
        # 简化内存测量，避免使用memory_usage
        run_search(root_state, model)
        print("MCTS搜索完成")
    except Exception as e:
        print(f"MCTS搜索出错: {e}")
        import traceback
        traceback.print_exc()
    elapsed = time.time() - start_time
    print(f"搜索耗时: {elapsed:.3f}秒")
    max_mem = 0.0  # 简化内存测量

    # 创建并启用CPU性能剖析器
    profiler = cProfile.Profile()
    profiler.enable()

    # 执行搜索以收集性能数据
    run_search(root_state, model)

    profiler.disable()

    # 保存CPU性能统计数据
    stats_dir = os.path.dirname(__file__)
    cpu_profile_path = os.path.join(stats_dir, 'mcts_cpu.pstats')
    profiler.dump_stats(cpu_profile_path)
    print(f'CPU性能数据已保存至: {cpu_profile_path}')

    # 添加 GPU 性能剖析
    if torch.cuda.is_available():
        from torch.profiler import profile as torch_profile, record_function, ProfilerActivity
        print("开始 GPU 性能剖析...")
        with torch_profile(activities=[ProfilerActivity.CPU, ProfilerActivity.CUDA], record_shapes=True) as gpu_prof:
            with record_function("mcts_run"):
                run_search(root_state, model)
        gpu_trace_path = os.path.join(stats_dir, 'mcts_gpu_trace.json')
        gpu_prof.export_chrome_trace(gpu_trace_path)
        print(f'GPU 性能跟踪已保存至: {gpu_trace_path}')

    # 保存性能摘要为 JSON，用于回归测试
    perf_summary = {'avg_time': elapsed, 'max_memory': max_mem}
    json_path = os.path.join(os.getcwd(), 'mcts_performance.json')
    with open(json_path, 'w', encoding='utf-8') as jf:
        json.dump(perf_summary, jf, ensure_ascii=False, indent=4)
    print(f'性能摘要已保存至: {json_path}')

    # memory_profiler自动生成mcts_profile.py.log文件


if __name__ == '__main__':
    main()