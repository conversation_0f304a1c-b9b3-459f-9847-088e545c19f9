#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
模型参数量分析和智能保存示例

演示如何使用增强的ModelSaver来：
1. 统计模型参数量
2. 生成包含参数量的文件名
3. 保存模型并自动添加参数量后缀
4. 查看已保存模型的参数信息

使用方法:
    python examples/model_parameter_analysis_example.py
    python examples/model_parameter_analysis_example.py --model-type large
    python examples/model_parameter_analysis_example.py --save-dir custom_models
"""

import sys
import os
import argparse
import torch
import torch.nn as nn
from pathlib import Path

# 添加项目根目录到路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

from cardgame_ai.utils.model_saver import ModelSaver, count_model_parameters, save_model_with_param_suffix


class SimpleResNet(nn.Module):
    """简单的ResNet模型用于演示"""
    
    def __init__(self, input_dim: int = 512, hidden_dim: int = 256, output_dim: int = 128, num_layers: int = 4):
        super().__init__()
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.output_dim = output_dim
        self.num_layers = num_layers
        
        # 输入层
        self.input_layer = nn.Linear(input_dim, hidden_dim)
        
        # 隐藏层
        self.hidden_layers = nn.ModuleList([
            nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.1)
            ) for _ in range(num_layers)
        ])
        
        # 输出层
        self.output_layer = nn.Linear(hidden_dim, output_dim)
        
        # 激活函数
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.1)
    
    def forward(self, x):
        # 输入层
        x = self.relu(self.input_layer(x))
        x = self.dropout(x)
        
        # 隐藏层（带残差连接）
        for layer in self.hidden_layers:
            residual = x
            x = layer(x)
            x = x + residual  # 残差连接
        
        # 输出层
        x = self.output_layer(x)
        return x


def create_model(model_type: str = "medium") -> nn.Module:
    """
    创建不同大小的模型
    
    Args:
        model_type: 模型类型 ("small", "medium", "large", "xlarge")
        
    Returns:
        创建的模型
    """
    model_configs = {
        "small": {"input_dim": 256, "hidden_dim": 128, "output_dim": 64, "num_layers": 2},
        "medium": {"input_dim": 512, "hidden_dim": 256, "output_dim": 128, "num_layers": 4},
        "large": {"input_dim": 1024, "hidden_dim": 512, "output_dim": 256, "num_layers": 6},
        "xlarge": {"input_dim": 2048, "hidden_dim": 1024, "output_dim": 512, "num_layers": 8}
    }
    
    if model_type not in model_configs:
        raise ValueError(f"不支持的模型类型: {model_type}. 支持的类型: {list(model_configs.keys())}")
    
    config = model_configs[model_type]
    return SimpleResNet(**config)


def analyze_model_parameters(model: nn.Module, model_name: str = "模型"):
    """分析并显示模型参数信息"""
    print(f"\n🔍 {model_name} 参数分析:")
    print("=" * 50)
    
    # 使用便捷函数快速获取参数量
    param_summary = count_model_parameters(model)
    print(f"📊 {param_summary}")
    
    # 使用详细的参数统计
    param_stats = ModelSaver.count_parameters(model)
    
    print(f"📈 详细统计:")
    print(f"   总参数量: {param_stats['total_parameters']:,}")
    print(f"   可训练参数: {param_stats['trainable_parameters']:,}")
    print(f"   不可训练参数: {param_stats['non_trainable_parameters']:,}")
    
    # 显示各层参数量（只显示前5层）
    print(f"📋 各层参数量 (前5层):")
    layer_params = param_stats['layer_parameters']
    for i, (layer_name, param_count) in enumerate(list(layer_params.items())[:5]):
        formatted_count = ModelSaver.format_parameter_count(param_count)
        print(f"   {layer_name}: {formatted_count} ({param_count:,})")
    
    if len(layer_params) > 5:
        print(f"   ... 还有 {len(layer_params) - 5} 层")


def demonstrate_filename_generation():
    """演示文件名生成功能"""
    print(f"\n📝 文件名生成演示:")
    print("=" * 50)
    
    # 不同的文件名生成示例
    examples = [
        {"base_name": "resnet", "param_count": 1234567},
        {"base_name": "transformer", "param_count": 12345678, "epoch": 100, "performance": 0.95},
        {"base_name": "efficient_zero", "param_count": 987654, "tag": "best", "timestamp": False},
        {"base_name": "muzero", "param_count": 5432109, "epoch": 500, "tag": "checkpoint"}
    ]
    
    for i, params in enumerate(examples, 1):
        filename = ModelSaver.generate_model_filename(**params)
        print(f"   示例{i}: {filename}")


def save_and_analyze_models(save_dir: str = "models"):
    """保存不同大小的模型并分析"""
    print(f"\n💾 模型保存演示:")
    print("=" * 50)
    
    model_types = ["small", "medium", "large"]
    saved_models = []
    
    for model_type in model_types:
        print(f"\n🔧 创建并保存 {model_type} 模型...")
        
        # 创建模型
        model = create_model(model_type)
        
        # 分析参数
        analyze_model_parameters(model, f"{model_type.upper()} 模型")
        
        # 保存模型（包含参数量后缀）
        model_path = save_model_with_param_suffix(
            model=model,
            save_dir=save_dir,
            base_name=f"resnet_{model_type}",
            tag=model_type,
            performance=0.85 + 0.05 * model_types.index(model_type)  # 模拟性能
        )
        
        saved_models.append(model_path)
        print(f"✅ 已保存到: {model_path}")
    
    return saved_models


def analyze_saved_models(model_paths: list):
    """分析已保存的模型"""
    print(f"\n📂 已保存模型分析:")
    print("=" * 50)
    
    for model_path in model_paths:
        print(f"\n📄 分析模型: {Path(model_path).name}")
        
        # 获取模型信息（不加载权重）
        model_info = ModelSaver.get_model_info(model_path)
        
        if 'error' in model_info:
            print(f"❌ 错误: {model_info['error']}")
            continue
        
        print(f"   文件名: {model_info['filename']}")
        print(f"   保存时间: {model_info['save_timestamp']}")
        print(f"   参数量: {model_info.get('formatted_params', 'Unknown')}")
        
        if model_info['performance']:
            print(f"   性能: {model_info['performance']:.3f}")
        
        if model_info['tag']:
            print(f"   标签: {model_info['tag']}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="模型参数量分析和智能保存示例")
    parser.add_argument('--model-type', type=str, default='medium',
                       choices=['small', 'medium', 'large', 'xlarge'],
                       help='模型类型')
    parser.add_argument('--save-dir', type=str, default='models',
                       help='模型保存目录')
    parser.add_argument('--analyze-only', action='store_true',
                       help='仅分析模型，不保存')
    
    args = parser.parse_args()
    
    print("🎯 模型参数量分析和智能保存示例")
    print("=" * 60)
    
    # 1. 创建并分析单个模型
    print(f"\n🔧 创建 {args.model_type} 模型...")
    model = create_model(args.model_type)
    analyze_model_parameters(model, f"{args.model_type.upper()} 模型")
    
    # 2. 演示文件名生成
    demonstrate_filename_generation()
    
    if not args.analyze_only:
        # 3. 保存多个模型
        saved_models = save_and_analyze_models(args.save_dir)
        
        # 4. 分析已保存的模型
        analyze_saved_models(saved_models)
        
        print(f"\n🎉 演示完成！模型已保存到 {args.save_dir} 目录")
    else:
        print(f"\n🎉 分析完成！")


if __name__ == "__main__":
    main()
