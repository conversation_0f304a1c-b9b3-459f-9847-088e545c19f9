{"experts": [{"name": "rule_based", "class": "cardgame_ai.algorithms.rule_based.RuleBasedAgent", "args": {}, "metadata": {"type": "rule_based", "description": "基于规则的专家", "tags": ["rule_based"]}}, {"name": "efficient_zero", "class": "cardgame_ai.algorithms.efficient_zero.EfficientZero", "args": {"model_path": "models/efficient_zero.pt"}, "metadata": {"type": "efficient_zero", "description": "基于EfficientZero的专家", "tags": ["neural", "search"]}}, {"name": "mcts", "class": "cardgame_ai.algorithms.mcts.MCTSAgent", "args": {"num_simulations": 50, "discount": 0.997}, "metadata": {"type": "mcts", "description": "基于MCTS的专家", "tags": ["search"]}}, {"name": "dqn", "class": "cardgame_ai.algorithms.dqn.DQNAgent", "args": {"model_path": "models/dqn.pt"}, "metadata": {"type": "dqn", "description": "基于DQN的专家", "tags": ["neural"]}}, {"name": "random", "class": "cardgame_ai.core.policy.RandomPolicy", "args": {"name": "random_agent"}, "metadata": {"type": "random", "description": "随机策略专家", "tags": ["random"]}}]}