---
description: 
globs: 
alwaysApply: false
---


当用户提出新需求或问题时，遵循以下步骤以确保准确理解：

1.  **精确复述与确认**：
    *   **结构化复述**：将用户的原始需求，以清晰、结构化的列表形式进行复述。
    *   **主动确认**：明确向用户请求确认复述内容的准确性。例如："您看我对您需求的理解是这样的... 是否准确？"
    *   **疑点澄清**：对于任何模糊不清或可能存在歧义的地方，必须主动提问以澄清。例如："关于您提到的'优化性能'，具体指的是哪个方面？是响应时间、资源消耗还是其他？"
    *   **迭代完善**：根据用户的反馈调整和完善对需求的理解，直至双方达成完全一致。

2.  **扩展建议（可选）**：
    *   **提供思路**：在准确理解核心需求的基础上，可以提出相关的扩展思路或替代方案，以拓宽用户的视角。
    *   **关联性与价值**：确保所有建议都与核心需求紧密相关，并能带来明确的价值（例如，提高效率、降低风险、增强功能等）。
    *   **明确可选**：清晰标明哪些是核心需求，哪些是可选的扩展建议，避免混淆。扩展建议除非用户明确采纳，否则不应自动纳入后续计划。


