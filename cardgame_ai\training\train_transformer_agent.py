"""
Transformer策略训练脚本

使用Transformer架构训练强化学习代理，适用于斗地主等卡牌游戏。
"""
import os
import sys
import json
import time
import logging
import argparse
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from typing import Dict, List, Tuple, Any, Optional

from cardgame_ai.core.base import State, Action
from cardgame_ai.core.environment import Environment
from cardgame_ai.core.agent import Agent
from cardgame_ai.algorithms.transformer_policy import TransformerPolicy
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.evaluation.evaluator import ComprehensiveEvaluator
from cardgame_ai.algorithms.rule_based import RuleBasedAgent
from cardgame_ai.utils.logger import setup_logger
from cardgame_ai.training.config import TrainingConfig

# 设置日志
logger = setup_logger("transformer_training", level=logging.INFO)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="Transformer策略训练脚本")

    # 配置文件
    parser.add_argument("--config", type=str, default="config/transformer_policy_config.json",
                        help="配置文件路径")

    # 训练参数
    parser.add_argument("--num_episodes", type=int, help="训练回合数")
    parser.add_argument("--batch_size", type=int, help="批次大小")
    parser.add_argument("--learning_rate", type=float, help="学习率")

    # 模型参数
    parser.add_argument("--hidden_dim", type=int, help="隐藏层维度")
    parser.add_argument("--num_heads", type=int, help="注意力头数")
    parser.add_argument("--num_layers", type=int, help="Transformer层数")
    parser.add_argument("--seq_len", type=int, help="序列长度")
    parser.add_argument("--use_mixed_precision", action="store_true", help="是否使用混合精度训练")

    # 保存和加载
    parser.add_argument("--save_dir", type=str, default="models/transformer", help="模型保存目录")
    parser.add_argument("--load_model", type=str, help="加载模型路径")

    # 评估参数
    parser.add_argument("--eval_interval", type=int, help="评估间隔回合数")
    parser.add_argument("--eval_episodes", type=int, help="评估回合数")

    # 其他参数
    parser.add_argument("--seed", type=int, default=42, help="随机种子")
    parser.add_argument("--device", type=str, default="auto", help="设备: cpu, cuda, auto")

    return parser.parse_args()


def load_config(args):
    """加载配置"""
    # 加载配置文件
    if os.path.exists(args.config):
        with open(args.config, 'r', encoding='utf-8') as f:
            config = json.load(f)
        logger.info(f"已加载配置文件: {args.config}")
    else:
        logger.warning(f"配置文件不存在: {args.config}，使用默认配置")
        config = {
            "policy": {
                "type": "transformer",
                "hidden_dim": 256,
                "num_heads": 4,
                "num_layers": 4,
                "ff_dim": 512,
                "seq_len": 100
            },
            "training": {
                "learning_rate": 1e-4,
                "batch_size": 64,
                "num_episodes": 1000
            }
        }

    # 命令行参数覆盖配置文件
    if args.num_episodes is not None:
        config["training"]["num_episodes"] = args.num_episodes
    if args.batch_size is not None:
        config["training"]["batch_size"] = args.batch_size
    if args.learning_rate is not None:
        config["training"]["learning_rate"] = args.learning_rate
    if args.hidden_dim is not None:
        config["policy"]["hidden_dim"] = args.hidden_dim
    if args.num_heads is not None:
        config["policy"]["num_heads"] = args.num_heads
    if args.num_layers is not None:
        config["policy"]["num_layers"] = args.num_layers
    if args.seq_len is not None:
        config["policy"]["seq_len"] = args.seq_len
    if args.use_mixed_precision:
        config["policy"]["use_mixed_precision"] = True

    return config


def create_transformer_policy(config, state_shape, action_shape, device):
    """创建Transformer策略"""
    policy_config = config["policy"]

    # 创建Transformer策略
    policy = TransformerPolicy(
        state_shape=state_shape,
        action_shape=action_shape,
        hidden_dim=policy_config.get("hidden_dim", 256),
        num_heads=policy_config.get("num_heads", 4),
        num_layers=policy_config.get("num_layers", 4),
        ff_dim=policy_config.get("ff_dim", 512),
        lr=config["training"].get("learning_rate", 1e-4),
        gamma=config["training"].get("gamma", 0.99),
        gae_lambda=config["training"].get("gae_lambda", 0.95),
        clip_ratio=config["training"].get("clip_ratio", 0.2),
        value_coef=config["training"].get("value_coef", 0.5),
        entropy_coef=config["training"].get("entropy_coef", 0.01),
        max_grad_norm=config["training"].get("max_grad_norm", 0.5),
        update_epochs=config["training"].get("update_epochs", 4),
        batch_size=config["training"].get("batch_size", 64),
        seq_len=policy_config.get("seq_len", 100),
        device=device,
        use_mixed_precision=policy_config.get("use_mixed_precision", False)
    )

    # 加载预训练模型（如果有）
    if "load_model" in config and config["load_model"]:
        model_path = config["load_model"]
        if os.path.exists(model_path):
            policy.load(model_path)
            logger.info(f"已加载预训练模型: {model_path}")
        else:
            logger.warning(f"预训练模型不存在: {model_path}")

    return policy


def setup_environment(config):
    """设置环境"""
    env_config = config.get("environment", {})
    env_name = env_config.get("name", "doudizhu")

    # 创建环境
    if env_name == "doudizhu":
        env = DouDizhuEnvironment()
    else:
        raise ValueError(f"不支持的环境: {env_name}")

    return env


def train_episode(env, agent, config):
    """训练一个回合"""
    # 重置环境
    state = env.reset()
    done = False
    episode_reward = 0
    step_count = 0

    # 回合循环
    while not done and step_count < config["training"].get("max_steps_per_episode", 1000):
        # 获取当前玩家
        current_player = state.get_player_id()

        # 获取合法动作
        legal_actions = env.get_legal_actions(state)

        # 选择动作
        action = agent.act(state, legal_actions)

        # 执行动作
        next_state, reward, done, info = env.step(action)

        # 更新经验
        agent.algorithm.update({
            "state": state,
            "action": action,
            "reward": reward,
            "next_state": next_state,
            "done": done
        })

        # 更新状态
        state = next_state
        episode_reward += reward
        step_count += 1

    # 返回回合统计信息
    return {
        "reward": episode_reward,
        "steps": step_count
    }


def evaluate(env, agent, num_episodes=10):
    """评估代理"""
    total_reward = 0
    win_count = 0

    for episode in range(num_episodes):
        # 重置环境
        state = env.reset()
        done = False
        episode_reward = 0

        # 回合循环
        while not done:
            # 获取合法动作
            legal_actions = env.get_legal_actions(state)

            # 选择动作（评估模式）
            action = agent.act(state, legal_actions, deterministic=True)

            # 执行动作
            next_state, reward, done, info = env.step(action)

            # 更新状态
            state = next_state
            episode_reward += reward

        # 更新统计信息
        total_reward += episode_reward
        if hasattr(env, 'get_winner') and env.get_winner() == 0:  # 假设0是我们的代理
            win_count += 1

    # 计算平均奖励和胜率
    avg_reward = total_reward / num_episodes
    win_rate = win_count / num_episodes

    return {
        "avg_reward": avg_reward,
        "win_rate": win_rate
    }


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()

    # 设置随机种子
    if args.seed is not None:
        np.random.seed(args.seed)
        torch.manual_seed(args.seed)
        torch.cuda.manual_seed_all(args.seed)

    # 设置设备
    if args.device == "auto":
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    else:
        device = torch.device(args.device)
    logger.info(f"使用设备: {device}")

    # 加载配置
    config = load_config(args)

    # 设置环境
    env = setup_environment(config)

    # 获取状态和动作空间
    state_shape = env.observation_space.shape
    action_shape = env.action_space.shape

    # 创建Transformer策略
    transformer_policy = create_transformer_policy(config, state_shape, action_shape, device)

    # 创建代理
    agent = Agent(algorithm=transformer_policy, name="transformer_agent")

    # 创建评估器
    evaluator = ComprehensiveEvaluator(save_path=os.path.join(args.save_dir, "eval"))

    # 训练循环
    logger.info("开始训练...")
    total_steps = 0
    best_win_rate = 0.0

    for episode in range(config["training"]["num_episodes"]):
        # 训练一个回合
        start_time = time.time()
        episode_stats = train_episode(env, agent, config)
        elapsed_time = time.time() - start_time

        # 更新总步数
        total_steps += episode_stats["steps"]

        # 打印训练信息
        logger.info(f"回合 {episode+1}/{config['training']['num_episodes']}, "
                   f"奖励: {episode_stats['reward']:.2f}, "
                   f"步数: {episode_stats['steps']}, "
                   f"耗时: {elapsed_time:.2f}秒")

        # 定期评估
        if (episode + 1) % config["training"].get("eval_interval", 50) == 0:
            # 评估当前策略
            eval_stats = evaluate(env, agent, config["training"].get("eval_episodes", 20))

            # 打印评估结果
            logger.info(f"评估结果 - 平均奖励: {eval_stats['avg_reward']:.2f}, "
                       f"胜率: {eval_stats['win_rate']:.2f}")

            # 保存最佳模型
            if eval_stats["win_rate"] > best_win_rate:
                best_win_rate = eval_stats["win_rate"]
                best_model_path = os.path.join(args.save_dir, "best_model.pt")
                transformer_policy.save(best_model_path)
                logger.info(f"保存最佳模型，胜率: {best_win_rate:.2f}")

        # 定期保存模型
        if (episode + 1) % config["training"].get("save_interval", 100) == 0:
            # 保存当前模型
            model_path = os.path.join(args.save_dir, f"model_ep{episode+1}.pt")
            transformer_policy.save(model_path)
            logger.info(f"保存模型: {model_path}")

    # 训练结束
    logger.info("训练完成!")

    # 保存最终模型
    final_model_path = os.path.join(args.save_dir, "final_model.pt")
    transformer_policy.save(final_model_path)
    logger.info(f"保存最终模型: {final_model_path}")

    # 最终评估
    final_eval_stats = evaluate(env, agent, config["training"].get("eval_episodes", 50))
    logger.info(f"最终评估结果 - 平均奖励: {final_eval_stats['avg_reward']:.2f}, "
               f"胜率: {final_eval_stats['win_rate']:.2f}")

    return 0


if __name__ == "__main__":
    sys.exit(main())
