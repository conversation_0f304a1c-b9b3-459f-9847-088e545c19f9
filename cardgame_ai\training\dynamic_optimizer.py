#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
动态优化器模块

提供动态批次大小和学习率优化功能：
- 动态批次大小调整
- 自适应学习率调度
- GPU内存监控
- 性能自动调优
"""

import time
import logging
import threading
from typing import Dict, List, Any, Optional, Tuple
import numpy as np
import torch
import torch.optim as optim
from torch.optim.lr_scheduler import _LRScheduler
import psutil

logger = logging.getLogger(__name__)


class DynamicBatchSizeManager:
    """动态批次大小管理器"""
    
    def __init__(self, base_batch_size: int = 256, min_batch_size: int = 128, 
                 max_batch_size: int = 384, memory_threshold: float = 0.85):
        """
        初始化动态批次大小管理器
        
        Args:
            base_batch_size: 基础批次大小
            min_batch_size: 最小批次大小
            max_batch_size: 最大批次大小
            memory_threshold: GPU内存使用阈值
        """
        self.base_batch_size = base_batch_size
        self.min_batch_size = min_batch_size
        self.max_batch_size = max_batch_size
        self.memory_threshold = memory_threshold
        self.current_batch_size = base_batch_size
        
        # 性能监控
        self.memory_history = []
        self.throughput_history = []
        self.adjustment_count = 0
        
        logger.info(f"初始化动态批次大小管理器: base={base_batch_size}, range=[{min_batch_size}, {max_batch_size}]")
    
    def get_optimal_batch_size(self) -> int:
        """获取当前最优批次大小"""
        try:
            # 获取GPU内存使用情况
            if torch.cuda.is_available():
                memory_used = torch.cuda.memory_allocated() / torch.cuda.max_memory_allocated()
                self.memory_history.append(memory_used)
                
                # 保持历史记录在合理范围内
                if len(self.memory_history) > 100:
                    self.memory_history = self.memory_history[-50:]
                
                # 根据内存使用情况调整批次大小
                if memory_used > self.memory_threshold:
                    # 内存使用过高，减小批次大小
                    new_batch_size = max(self.min_batch_size, int(self.current_batch_size * 0.8))
                elif memory_used < 0.6:
                    # 内存使用较低，可以增大批次大小
                    new_batch_size = min(self.max_batch_size, int(self.current_batch_size * 1.2))
                else:
                    # 内存使用合理，保持当前批次大小
                    new_batch_size = self.current_batch_size
                
                # 更新批次大小
                if new_batch_size != self.current_batch_size:
                    logger.info(f"调整批次大小: {self.current_batch_size} -> {new_batch_size} (内存使用: {memory_used:.2%})")
                    self.current_batch_size = new_batch_size
                    self.adjustment_count += 1
            
            return self.current_batch_size
            
        except Exception as e:
            logger.warning(f"获取最优批次大小时出错: {e}")
            return self.base_batch_size
    
    def record_throughput(self, throughput: float) -> None:
        """记录训练吞吐量"""
        self.throughput_history.append(throughput)
        if len(self.throughput_history) > 100:
            self.throughput_history = self.throughput_history[-50:]
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'current_batch_size': self.current_batch_size,
            'adjustment_count': self.adjustment_count,
            'avg_memory_usage': np.mean(self.memory_history) if self.memory_history else 0.0,
            'avg_throughput': np.mean(self.throughput_history) if self.throughput_history else 0.0
        }


class AdaptiveLearningRateScheduler(_LRScheduler):
    """自适应学习率调度器"""
    
    def __init__(self, optimizer, base_lr: float = 0.0005, warmup_steps: int = 2000,
                 patience: int = 20, factor: float = 0.8, min_lr: float = 1e-7):
        """
        初始化自适应学习率调度器
        
        Args:
            optimizer: 优化器
            base_lr: 基础学习率
            warmup_steps: 预热步数
            patience: 耐心值（多少步无改善后降低学习率）
            factor: 学习率衰减因子
            min_lr: 最小学习率
        """
        self.base_lr = base_lr
        self.warmup_steps = warmup_steps
        self.patience = patience
        self.factor = factor
        self.min_lr = min_lr
        
        # 状态跟踪
        self.step_count = 0
        self.best_loss = float('inf')
        self.patience_count = 0
        self.loss_history = []
        
        super().__init__(optimizer)
        
        logger.info(f"初始化自适应学习率调度器: base_lr={base_lr}, warmup_steps={warmup_steps}")
    
    def get_lr(self) -> List[float]:
        """计算当前学习率"""
        if self.step_count < self.warmup_steps:
            # 预热阶段：线性增长
            warmup_factor = self.step_count / self.warmup_steps
            current_lr = self.base_lr * warmup_factor
        else:
            # 正常训练阶段
            current_lr = self.base_lr
            
            # 应用衰减
            decay_factor = self.factor ** (self.patience_count // self.patience)
            current_lr *= decay_factor
            
            # 确保不低于最小学习率
            current_lr = max(current_lr, self.min_lr)
        
        return [current_lr for _ in self.optimizer.param_groups]
    
    def step_with_loss(self, loss: float) -> None:
        """根据损失值更新学习率"""
        self.step_count += 1
        self.loss_history.append(loss)
        
        # 保持历史记录在合理范围内
        if len(self.loss_history) > 200:
            self.loss_history = self.loss_history[-100:]
        
        # 检查是否需要降低学习率
        if loss < self.best_loss:
            self.best_loss = loss
            self.patience_count = 0
        else:
            self.patience_count += 1
        
        # 基于损失趋势的额外调整
        if len(self.loss_history) >= 20:
            recent_trend = np.mean(self.loss_history[-10:]) - np.mean(self.loss_history[-20:-10])
            if recent_trend > 0.01:  # 损失明显上升
                self.patience_count += 5  # 加速学习率衰减
        
        # 更新学习率
        self.step()
        
        # 记录学习率变化
        if self.step_count % 1000 == 0:
            current_lr = self.get_last_lr()[0]
            logger.info(f"步骤 {self.step_count}: 学习率={current_lr:.2e}, 最佳损失={self.best_loss:.6f}")


class PerformanceOptimizer:
    """性能优化器"""
    
    def __init__(self, model: torch.nn.Module, base_config: Dict[str, Any]):
        """
        初始化性能优化器
        
        Args:
            model: 要优化的模型
            base_config: 基础配置
        """
        self.model = model
        self.base_config = base_config
        
        # 初始化组件
        self.batch_size_manager = DynamicBatchSizeManager(
            base_batch_size=base_config.get('batch_size', 256),
            min_batch_size=base_config.get('min_batch_size', 128),
            max_batch_size=base_config.get('max_batch_size', 384),
            memory_threshold=base_config.get('memory_threshold', 0.85)
        )
        
        # 创建优化器
        self.optimizer = self._create_optimizer()
        
        # 创建学习率调度器
        self.lr_scheduler = AdaptiveLearningRateScheduler(
            optimizer=self.optimizer,
            base_lr=base_config.get('learning_rate', 0.0005),
            warmup_steps=base_config.get('warmup_steps', 2000),
            patience=base_config.get('patience', 20),
            factor=base_config.get('lr_decay_factor', 0.8),
            min_lr=base_config.get('min_lr', 1e-7)
        )
        
        # 性能监控
        self.training_start_time = None
        self.step_times = []
        
        logger.info("初始化性能优化器完成")
    
    def _create_optimizer(self) -> torch.optim.Optimizer:
        """创建优化器"""
        optimizer_type = self.base_config.get('optimizer_type', 'adamw')
        learning_rate = self.base_config.get('learning_rate', 0.0005)
        weight_decay = self.base_config.get('weight_decay', 1e-4)
        
        if optimizer_type.lower() == 'adamw':
            return optim.AdamW(
                self.model.parameters(),
                lr=learning_rate,
                weight_decay=weight_decay,
                betas=(0.9, 0.999),
                eps=1e-8
            )
        elif optimizer_type.lower() == 'adam':
            return optim.Adam(
                self.model.parameters(),
                lr=learning_rate,
                weight_decay=weight_decay
            )
        else:
            return optim.SGD(
                self.model.parameters(),
                lr=learning_rate,
                weight_decay=weight_decay,
                momentum=0.9
            )
    
    def optimize_step(self, loss: float) -> Dict[str, Any]:
        """执行优化步骤"""
        step_start_time = time.time()
        
        # 反向传播
        loss.backward()
        
        # 梯度裁剪
        clip_norm = self.base_config.get('gradient_clip_norm', 5.0)
        if clip_norm > 0:
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), clip_norm)
        
        # 优化器步骤
        self.optimizer.step()
        self.optimizer.zero_grad()
        
        # 更新学习率
        self.lr_scheduler.step_with_loss(loss.item())
        
        # 记录性能
        step_time = time.time() - step_start_time
        self.step_times.append(step_time)
        if len(self.step_times) > 100:
            self.step_times = self.step_times[-50:]
        
        # 更新批次大小管理器
        if len(self.step_times) >= 10:
            avg_step_time = np.mean(self.step_times[-10:])
            throughput = 1.0 / avg_step_time if avg_step_time > 0 else 0.0
            self.batch_size_manager.record_throughput(throughput)
        
        return {
            'learning_rate': self.lr_scheduler.get_last_lr()[0],
            'optimal_batch_size': self.batch_size_manager.get_optimal_batch_size(),
            'step_time': step_time,
            'loss': loss.item()
        }
    
    def get_current_config(self) -> Dict[str, Any]:
        """获取当前优化配置"""
        return {
            'batch_size': self.batch_size_manager.get_optimal_batch_size(),
            'learning_rate': self.lr_scheduler.get_last_lr()[0],
            'batch_size_stats': self.batch_size_manager.get_statistics(),
            'avg_step_time': np.mean(self.step_times) if self.step_times else 0.0
        }
