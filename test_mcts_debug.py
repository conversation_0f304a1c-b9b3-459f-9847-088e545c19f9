#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
MCTS深度调试测试脚本

专门用于测试第40次模拟的详细调试日志，定位死循环问题。

使用方法:
    python test_mcts_debug.py
"""

import os
import sys
import time
import logging
from pathlib import Path

# 添加项目路径到sys.path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from cardgame_ai.algorithms.mcts import MCTS
from cardgame_ai.games.doudizhu.state import DouDizhuState, GamePhase
from cardgame_ai.games.doudizhu.action import BidAction
from cardgame_ai.games.doudizhu.deck import Deck

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class MockModel:
    """模拟模型，用于测试MCTS"""
    
    def __init__(self):
        self.call_count = 0
    
    def represent(self, state):
        """模拟表示网络方法"""
        import torch
        # 返回随机隐藏状态
        hidden_state = torch.randn(1, 256)  # 假设隐藏状态维度为256
        return hidden_state
    
    def predict(self, hidden_state):
        """模拟预测方法"""
        self.call_count += 1
        # 返回随机策略和价值
        import random
        import torch
        policy_logits = torch.randn(1, 162)  # 斗地主动作空间大小
        value = torch.tensor([[random.random() - 0.5]])
        return policy_logits, value
    
    def dynamics(self, hidden_state, action):
        """模拟动态网络方法"""
        import torch
        # 返回下一个隐藏状态和奖励
        next_hidden_state = torch.randn_like(hidden_state)
        reward = torch.tensor([[0.0]])
        return next_hidden_state, reward


def create_test_state():
    """创建测试用的游戏状态"""
    deck = Deck()
    hands, landlord_cards = deck.deal(3)
    
    state = DouDizhuState(
        hands=hands,
        landlord_cards=landlord_cards,
        landlord=None,
        current_player=0,
        game_phase=GamePhase.BIDDING,
        bid_history=[],
        grab_history=[],
        highest_bidder=None,
        highest_bid=0,
        last_move=None,
        last_player=None,
        num_passes=0,
        history=[],
        played_cards=[]
    )
    
    return state


def test_mcts_debug():
    """测试MCTS深度调试功能"""
    logger.info("开始MCTS深度调试测试")
    
    # 创建MCTS实例，使用足够的模拟次数来触发第40次模拟
    mcts = MCTS(num_simulations=50)  # 确保能到达第40次模拟
    model = MockModel()
    state = create_test_state()
    
    start_time = time.time()
    
    try:
        logger.info("开始运行MCTS，将特别关注第40次模拟...")
        
        # 运行MCTS，设置较短的超时来快速发现问题
        visit_counts, action_probs = mcts.run(
            root_state=state,
            model=model,
            max_time_ms=10000  # 10秒超时
        )
        
        elapsed_time = time.time() - start_time
        logger.info(f"MCTS运行完成，耗时: {elapsed_time:.2f}s")
        logger.info(f"完成模拟次数: {mcts.actual_simulations}")
        logger.info(f"模型调用次数: {model.call_count}")
        
        if mcts.actual_simulations >= 40:
            logger.info("✅ 成功完成第40次模拟，未发现死循环")
        else:
            logger.warning(f"⚠️ 只完成了 {mcts.actual_simulations} 次模拟，可能存在问题")
        
        return True
        
    except Exception as e:
        elapsed_time = time.time() - start_time
        logger.error(f"❌ MCTS运行失败: {e}")
        logger.error(f"失败时间: {elapsed_time:.2f}s")
        logger.error(f"完成模拟次数: {mcts.actual_simulations}")
        return False


def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("MCTS深度调试测试")
    logger.info("=" * 60)
    
    # 运行测试
    success = test_mcts_debug()
    
    if success:
        logger.info("🎉 测试完成！请检查日志中的第40次模拟详细信息。")
    else:
        logger.error("❌ 测试失败！请检查错误信息和日志。")
    
    logger.info("=" * 60)
    logger.info("测试结束")
    logger.info("=" * 60)
    
    return 0 if success else 1


if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        logger.info("\n用户取消测试")
        sys.exit(1)
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
