"""
符号推理组件模块

提供基于符号逻辑和精确计算的推理能力，用于处理那些符号逻辑或精确计算更擅长的子博弈或局面。
"""

import logging
from typing import List, Optional, Dict, Any, Tuple, Set

from cardgame_ai.core.base import State, Action
from cardgame_ai.games.doudizhu.state import DouDizhuState
from cardgame_ai.games.doudizhu.card import Card, CardRank, CardSuit
from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType

logger = logging.getLogger(__name__)


class SymbolicReasoningComponent:
    """
    符号推理组件

    提供基于符号逻辑和精确计算的推理能力，用于处理那些符号逻辑或精确计算更擅长的子博弈或局面。
    """

    def __init__(self, use_guaranteed_win_solver: bool = True, use_card_counting: bool = True):
        """
        初始化符号推理组件

        Args:
            use_guaranteed_win_solver: 是否使用必胜局面求解器
            use_card_counting: 是否使用牌数统计
        """
        self.use_guaranteed_win_solver = use_guaranteed_win_solver
        self.use_card_counting = use_card_counting
        self.stats = {
            "calls": 0,
            "successful_solves": 0,
            "solve_types": {}
        }

    def can_solve(self, state: State) -> bool:
        """
        判断符号推理组件是否可以处理当前状态
        
        Args:
            state: 当前状态
            
        Returns:
            bool: 是否可以处理
        """
        return self._can_handle(state)

    def solve(self, state: State, legal_actions: List[Action]) -> Optional[Action]:
        """
        尝试使用符号方法解决子博弈

        Args:
            state: 当前状态
            legal_actions: 合法动作列表

        Returns:
            Optional[Action]: 最优动作，如果无法解决则返回None
        """
        # 更新统计信息
        self.stats["calls"] += 1

        # 检查是否可以处理当前状态
        if not self._can_handle(state):
            return None

        # 尝试使用不同的符号推理方法
        action = None

        # 1. 尝试使用必胜局面求解器
        if self.use_guaranteed_win_solver:
            action = self._solve_guaranteed_win(state, legal_actions)
            if action:
                self._update_stats("guaranteed_win")
                return action

        # 2. 尝试使用牌数统计
        if self.use_card_counting:
            action = self._solve_card_counting(state, legal_actions)
            if action:
                self._update_stats("card_counting")
                return action

        return None

    def _can_handle(self, state: State) -> bool:
        """
        判断是否可以处理当前状态

        Args:
            state: 当前状态

        Returns:
            bool: 是否可以处理
        """
        # 只处理斗地主游戏状态
        if not isinstance(state, DouDizhuState):
            return False

        # 只处理出牌阶段
        if state.game_phase.name != "PLAYING":
            return False

        # 检查是否是残局（至少有一个玩家手牌数量较少）
        is_endgame = False
        for hand in state.hands:
            if 0 < len(hand) <= 5:
                is_endgame = True
                break

        # 只处理残局
        if not is_endgame:
            return False

        # 检查是否是必胜/必败局面
        if self.use_guaranteed_win_solver and self._is_guaranteed_win_scenario(state):
            return True

        # 检查是否适合使用牌数统计
        if self.use_card_counting and self._is_card_counting_scenario(state):
            return True

        return False

    def _is_guaranteed_win_scenario(self, state: DouDizhuState) -> bool:
        """
        判断是否是必胜/必败局面

        Args:
            state: 当前状态

        Returns:
            bool: 是否是必胜/必败局面
        """
        # 检查当前玩家手牌数量
        current_player = state.current_player
        hand = state.hands[current_player]

        # 如果只剩一张牌，可能是必胜局面
        if len(hand) == 1:
            return True

        # 如果只剩两张牌，检查是否是对子或王炸
        if len(hand) == 2:
            # 检查是否是对子
            if hand[0].rank == hand[1].rank:
                return True

            # 检查是否是王炸
            has_small_joker = any(card.rank == CardRank.SMALL_JOKER for card in hand)
            has_big_joker = any(card.rank == CardRank.BIG_JOKER for card in hand)
            if has_small_joker and has_big_joker:
                return True

        # 如果只剩三张牌，检查是否是三张
        if len(hand) == 3:
            # 检查是否是三张
            if hand[0].rank == hand[1].rank == hand[2].rank:
                return True

        # 如果只剩四张牌，检查是否是炸弹
        if len(hand) == 4:
            # 检查是否是炸弹
            if hand[0].rank == hand[1].rank == hand[2].rank == hand[3].rank:
                return True

        # 检查是否是最后一个出牌的玩家
        if state.last_player == current_player:
            return True

        # 检查是否有玩家只剩一张牌
        for i, h in enumerate(state.hands):
            if i != current_player and len(h) == 1:
                return True

        return False

    def _is_card_counting_scenario(self, state: DouDizhuState) -> bool:
        """
        判断是否适合使用牌数统计

        Args:
            state: 当前状态

        Returns:
            bool: 是否适合使用牌数统计
        """
        # 检查是否有足够的已出牌信息
        if len(state.played_cards) < 20:
            return False

        # 检查是否是残局
        current_player = state.current_player
        hand = state.hands[current_player]

        # 如果手牌数量较少，适合使用牌数统计
        if len(hand) <= 5:
            return True

        # 如果是地主，且农民手牌数量较少，适合使用牌数统计
        if current_player == state.landlord:
            for i, h in enumerate(state.hands):
                if i != current_player and len(h) <= 3:
                    return True

        # 如果是农民，且队友手牌数量较少，适合使用牌数统计
        else:
            teammate_id = (current_player + 1) % 3 if (current_player + 1) % 3 != state.landlord else (current_player + 2) % 3
            if len(state.hands[teammate_id]) <= 3:
                return True

        return False

    def _solve_guaranteed_win(self, state: DouDizhuState, legal_actions: List[CardGroup]) -> Optional[CardGroup]:
        """
        使用必胜局面求解器

        Args:
            state: 当前状态
            legal_actions: 合法动作列表

        Returns:
            Optional[CardGroup]: 最优动作，如果无法解决则返回None
        """
        current_player = state.current_player
        hand = state.hands[current_player]

        # 如果只剩一张牌，直接出
        if len(hand) == 1 and legal_actions:
            # 找出非PASS的动作
            non_pass_actions = [action for action in legal_actions if action.card_type != CardGroupType.PASS]
            if non_pass_actions:
                return non_pass_actions[0]

        # 如果只剩两张牌，检查是否是对子或王炸
        if len(hand) == 2:
            # 检查是否是对子
            if hand[0].rank == hand[1].rank:
                # 找出对子动作
                pair_actions = [action for action in legal_actions if action.card_type == CardGroupType.PAIR]
                if pair_actions:
                    return pair_actions[0]

            # 检查是否是王炸
            has_small_joker = any(card.rank == CardRank.SMALL_JOKER for card in hand)
            has_big_joker = any(card.rank == CardRank.BIG_JOKER for card in hand)
            if has_small_joker and has_big_joker:
                # 找出王炸动作
                rocket_actions = [action for action in legal_actions if action.card_type == CardGroupType.ROCKET]
                if rocket_actions:
                    return rocket_actions[0]

        # 如果只剩三张牌，检查是否是三张
        if len(hand) == 3:
            # 检查是否是三张
            if hand[0].rank == hand[1].rank == hand[2].rank:
                # 找出三张动作
                trio_actions = [action for action in legal_actions if action.card_type == CardGroupType.TRIO]
                if trio_actions:
                    return trio_actions[0]

        # 如果只剩四张牌，检查是否是炸弹
        if len(hand) == 4:
            # 检查是否是炸弹
            if hand[0].rank == hand[1].rank == hand[2].rank == hand[3].rank:
                # 找出炸弹动作
                bomb_actions = [action for action in legal_actions if action.card_type == CardGroupType.BOMB]
                if bomb_actions:
                    return bomb_actions[0]

        # 如果是最后一个出牌的玩家，选择最小的合法动作
        if state.last_player == current_player and legal_actions:
            # 找出非PASS的动作
            non_pass_actions = [action for action in legal_actions if action.card_type != CardGroupType.PASS]
            if non_pass_actions:
                # 按照牌型和大小排序
                sorted_actions = sorted(non_pass_actions, key=lambda x: (x.card_type.value, x.main_rank.value if x.main_rank else 0))
                return sorted_actions[0]

        return None

    def _solve_card_counting(self, state: DouDizhuState, legal_actions: List[CardGroup]) -> Optional[CardGroup]:
        """
        使用牌数统计

        Args:
            state: 当前状态
            legal_actions: 合法动作列表

        Returns:
            Optional[CardGroup]: 最优动作，如果无法解决则返回None
        """
        # 统计已出牌
        played_ranks = {}
        for card in state.played_cards:
            if card.rank not in played_ranks:
                played_ranks[card.rank] = 0
            played_ranks[card.rank] += 1

        # 统计当前玩家手牌
        current_player = state.current_player
        hand = state.hands[current_player]
        hand_ranks = {}
        for card in hand:
            if card.rank not in hand_ranks:
                hand_ranks[card.rank] = 0
            hand_ranks[card.rank] += 1

        # 估计其他玩家手牌
        other_players_cards = {}
        for rank in CardRank:
            # 跳过大小王
            if rank in [CardRank.SMALL_JOKER, CardRank.BIG_JOKER]:
                continue

            # 计算该点数的总牌数（4张）
            total_count = 4
            # 减去已出牌数量
            if rank in played_ranks:
                total_count -= played_ranks[rank]
            # 减去当前玩家手牌数量
            if rank in hand_ranks:
                total_count -= hand_ranks[rank]
            # 剩余的牌在其他玩家手中
            if total_count > 0:
                other_players_cards[rank] = total_count

        # 处理大小王
        for rank in [CardRank.SMALL_JOKER, CardRank.BIG_JOKER]:
            # 计算该点数的总牌数（1张）
            total_count = 1
            # 减去已出牌数量
            if rank in played_ranks:
                total_count -= played_ranks[rank]
            # 减去当前玩家手牌数量
            if rank in hand_ranks:
                total_count -= hand_ranks[rank]
            # 剩余的牌在其他玩家手中
            if total_count > 0:
                other_players_cards[rank] = total_count

        # 根据牌数统计做出决策
        if not legal_actions:
            return None

        # 如果是新的一轮出牌（上一个出牌的是当前玩家，或者连续两次不出）
        if state.last_player == current_player or state.num_passes >= 2:
            # 找出非PASS的动作
            non_pass_actions = [action for action in legal_actions if action.card_type != CardGroupType.PASS]
            if not non_pass_actions:
                return None

            # 优先出单张
            single_actions = [action for action in non_pass_actions if action.card_type == CardGroupType.SINGLE]
            if single_actions:
                # 找出其他玩家没有的点数
                safe_singles = []
                for action in single_actions:
                    rank = action.cards[0].rank
                    if rank not in other_players_cards or other_players_cards[rank] == 0:
                        safe_singles.append(action)

                # 如果有安全的单张，选择最小的
                if safe_singles:
                    return min(safe_singles, key=lambda x: x.cards[0].rank.value)

                # 否则，选择最小的单张
                return min(single_actions, key=lambda x: x.cards[0].rank.value)

            # 如果没有单张，选择最小的合法动作
            return min(non_pass_actions, key=lambda x: (x.card_type.value, x.main_rank.value if x.main_rank else 0))

        # 如果需要跟牌
        else:
            # 如果上一手是单张
            if state.last_move and state.last_move.card_type == CardGroupType.SINGLE:
                # 找出能大过上一手的单张
                single_actions = [action for action in legal_actions if action.card_type == CardGroupType.SINGLE and action.can_beat(state.last_move)]
                if single_actions:
                    # 找出其他玩家没有的点数
                    safe_singles = []
                    for action in single_actions:
                        rank = action.cards[0].rank
                        if rank not in other_players_cards or other_players_cards[rank] == 0:
                            safe_singles.append(action)

                    # 如果有安全的单张，选择最小的
                    if safe_singles:
                        return min(safe_singles, key=lambda x: x.cards[0].rank.value)

                    # 否则，选择最小的单张
                    return min(single_actions, key=lambda x: x.cards[0].rank.value)

            # 如果上一手是对子
            elif state.last_move and state.last_move.card_type == CardGroupType.PAIR:
                # 找出能大过上一手的对子
                pair_actions = [action for action in legal_actions if action.card_type == CardGroupType.PAIR and action.can_beat(state.last_move)]
                if pair_actions:
                    return min(pair_actions, key=lambda x: x.main_rank.value if x.main_rank else 0)

            # 默认选择PASS
            pass_actions = [action for action in legal_actions if action.card_type == CardGroupType.PASS]
            if pass_actions:
                return pass_actions[0]

        return None

    def _update_stats(self, solve_type: str):
        """
        更新统计信息

        Args:
            solve_type: 解决类型
        """
        self.stats["successful_solves"] += 1

        if solve_type not in self.stats["solve_types"]:
            self.stats["solve_types"][solve_type] = 0
        self.stats["solve_types"][solve_type] += 1

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        return self.stats.copy()
