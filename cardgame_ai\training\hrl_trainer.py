"""
层次化强化学习训练器模块

实现层次化强化学习的训练流程，包括批量训练、模型更新和保存等功能。
"""

import os
import time
import copy
import logging
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Any, Optional, Union, Callable

from cardgame_ai.core.environment import Environment
from cardgame_ai.algorithms.hrl.hierarchical_policy import HierarchicalPolicy
from cardgame_ai.algorithms.hrl.replay_buffer import ReplayBuffer, PrioritizedReplayBuffer

# 配置日志
logger = logging.getLogger(__name__)


class HRLTrainer:
    """
    层次化强化学习训练器

    实现层次化强化学习的训练流程，包括批量训练、模型更新和保存等功能。
    """

    def __init__(
        self,
        env: Environment,
        policy: HierarchicalPolicy,
        optimizer: torch.optim.Optimizer,
        replay_buffer_size: int = 10000,
        batch_size: int = 64,
        gamma: float = 0.99,
        tau: float = 0.001,
        high_level_update_freq: int = 1,
        low_level_update_freq: int = 1,
        training_mode: str = "end_to_end",  # 可选："end_to_end", "hierarchical"
        prioritized_replay: bool = False,
        alpha: float = 0.6,
        beta: float = 0.4,
        beta_increment: float = 0.001,
        target_update_freq: int = 100,
        save_path: str = "models/hrl",
        save_interval: int = 1000,
        device: Optional[str] = None
    ):
        """
        初始化层次化强化学习训练器

        Args:
            env: 游戏环境
            policy: 层次化策略
            optimizer: 优化器
            replay_buffer_size: 经验回放缓冲区大小
            batch_size: 批次大小
            gamma: 折扣因子
            tau: 目标网络软更新系数
            high_level_update_freq: 高层策略更新频率
            low_level_update_freq: 低层策略更新频率
            training_mode: 训练模式，可选："end_to_end", "hierarchical"
            prioritized_replay: 是否使用优先经验回放
            alpha: 优先级指数
            beta: 重要性采样指数
            beta_increment: beta的增量
            target_update_freq: 目标网络更新频率
            save_path: 模型保存路径
            save_interval: 模型保存间隔
            device: 设备
        """
        self.env = env
        self.policy = policy
        self.optimizer = optimizer
        self.batch_size = batch_size
        self.gamma = gamma
        self.tau = tau
        self.high_level_update_freq = high_level_update_freq
        self.low_level_update_freq = low_level_update_freq
        self.training_mode = training_mode
        self.target_update_freq = target_update_freq
        self.save_path = save_path
        self.save_interval = save_interval

        # 设置设备
        self.device = device if device is not None else ('cuda' if torch.cuda.is_available() else 'cpu')
        self.policy.to(self.device)

        # 创建目标网络
        self.target_policy = copy.deepcopy(policy)
        self.target_policy.to(self.device)

        # 创建经验回放缓冲区
        if prioritized_replay:
            self.replay_buffer = PrioritizedReplayBuffer(
                capacity=replay_buffer_size,
                alpha=alpha,
                beta=beta,
                beta_increment=beta_increment
            )
            self.prioritized_replay = True
        else:
            self.replay_buffer = ReplayBuffer(capacity=replay_buffer_size)
            self.prioritized_replay = False

        # 创建保存目录
        os.makedirs(self.save_path, exist_ok=True)

        # 训练统计
        self.stats = {
            "episodes": 0,
            "steps": 0,
            "high_level_updates": 0,
            "low_level_updates": 0,
            "rewards": [],
            "losses": {
                "high_level": [],
                "low_level": []
            }
        }

    def train(self, num_episodes: int, max_steps_per_episode: int = 1000) -> Dict[str, Any]:
        """
        训练层次化策略

        Args:
            num_episodes: 训练回合数
            max_steps_per_episode: 每回合最大步数

        Returns:
            训练统计信息
        """
        logger.info(f"开始训练，回合数：{num_episodes}，每回合最大步数：{max_steps_per_episode}")
        logger.info(f"训练模式：{self.training_mode}，设备：{self.device}")

        start_time = time.time()

        for episode in range(num_episodes):
            # 重置环境
            state = self.env.reset()
            episode_reward = 0

            for step in range(max_steps_per_episode):
                # 选择动作
                state_tensor = torch.FloatTensor(state).to(self.device)
                (high_level_action, low_level_action), _ = self.policy.act(state_tensor)

                # 执行动作
                next_state, reward, done, _ = self.env.step(low_level_action)

                # 存储经验
                self.replay_buffer.add(
                    state, high_level_action, low_level_action, reward, next_state, done
                )

                # 更新状态和累积奖励
                state = next_state
                episode_reward += reward
                self.stats["steps"] += 1

                # 如果经验池中有足够的样本，进行训练
                if len(self.replay_buffer) >= self.batch_size:
                    # 根据训练模式选择不同的更新方法
                    if self.training_mode == "end_to_end":
                        self._update_end_to_end()
                    elif self.training_mode == "hierarchical":
                        self._update_hierarchical()

                # 如果达到目标网络更新频率，更新目标网络
                if self.stats["steps"] % self.target_update_freq == 0:
                    self._update_target_network()

                # 如果达到保存间隔，保存模型
                if self.stats["steps"] % self.save_interval == 0:
                    self.save(os.path.join(self.save_path, f"model_{self.stats['steps']}"))

                # 如果游戏结束，跳出循环
                if done:
                    break

            # 更新统计信息
            self.stats["episodes"] += 1
            self.stats["rewards"].append(episode_reward)

            # 打印训练进度
            if (episode + 1) % 10 == 0 or episode == 0:
                avg_reward = np.mean(self.stats["rewards"][-10:])
                elapsed_time = time.time() - start_time
                logger.info(
                    f"回合 {episode + 1}/{num_episodes} | "
                    f"步数 {self.stats['steps']} | "
                    f"平均奖励 {avg_reward:.2f} | "
                    f"用时 {elapsed_time:.2f}秒"
                )

        # 训练结束，保存最终模型
        final_model_path = os.path.join(self.save_path, "final_model")
        self.save(final_model_path)
        logger.info(f"训练完成，最终模型已保存到 {final_model_path}")

        # 计算总用时
        total_time = time.time() - start_time
        logger.info(f"总用时：{total_time:.2f}秒，平均每回合用时：{total_time / num_episodes:.2f}秒")

        return self.stats

    def _update_end_to_end(self) -> None:
        """
        端到端更新策略网络
        """
        # 从经验池中采样
        if self.prioritized_replay:
            states, high_level_actions, low_level_actions, rewards, next_states, dones, weights, indices = \
                self.replay_buffer.sample(self.batch_size)
        else:
            states, high_level_actions, low_level_actions, rewards, next_states, dones = \
                self.replay_buffer.sample(self.batch_size)
            weights = torch.ones_like(rewards)

        # 将数据移动到设备
        states = states.to(self.device)
        high_level_actions = high_level_actions.to(self.device)
        low_level_actions = low_level_actions.to(self.device)
        rewards = rewards.to(self.device)
        next_states = next_states.to(self.device)
        dones = dones.to(self.device)
        weights = weights.to(self.device)

        # 计算目标Q值
        with torch.no_grad():
            next_high_level_logits, next_low_level_logits = self.target_policy(next_states)
            next_high_level_probs = F.softmax(next_high_level_logits, dim=-1)

            # 计算每个高层动作的期望值
            next_values = []
            for high_level_action in range(next_high_level_logits.shape[1]):
                next_low_level_probs = F.softmax(next_low_level_logits[high_level_action], dim=-1)
                next_values.append(torch.sum(next_low_level_probs * next_low_level_logits[high_level_action], dim=-1))

            next_values = torch.stack(next_values, dim=-1)
            next_value = torch.sum(next_high_level_probs * next_values, dim=-1)
            target_q = rewards + self.gamma * (1 - dones) * next_value

        # 计算当前Q值
        high_level_logits, low_level_logits = self.policy(states)

        # 获取高层动作的Q值
        high_level_q = high_level_logits.gather(1, high_level_actions.unsqueeze(1)).squeeze(1)

        # 获取低层动作的Q值
        batch_indices = torch.arange(self.batch_size, device=self.device)
        low_level_q = low_level_logits[high_level_actions].gather(1, low_level_actions.unsqueeze(1)).squeeze(1)

        # 计算损失
        high_level_loss = F.mse_loss(high_level_q, target_q, reduction='none')
        low_level_loss = F.mse_loss(low_level_q, target_q, reduction='none')

        # 应用重要性权重
        high_level_loss = (high_level_loss * weights).mean()
        low_level_loss = (low_level_loss * weights).mean()

        # 总损失
        loss = high_level_loss + low_level_loss

        # 更新网络
        self.optimizer.zero_grad()
        loss.backward()
        self.optimizer.step()

        # 如果使用优先经验回放，更新优先级
        if self.prioritized_replay:
            # 计算TD误差
            with torch.no_grad():
                td_error = torch.abs(target_q - low_level_q).cpu().numpy()

            # 更新优先级
            self.replay_buffer.update_priorities(indices, td_error + 1e-6)

        # 更新统计信息
        self.stats["high_level_updates"] += 1
        self.stats["low_level_updates"] += 1
        self.stats["losses"]["high_level"].append(high_level_loss.item())
        self.stats["losses"]["low_level"].append(low_level_loss.item())

    def _update_hierarchical(self) -> None:
        """
        分层更新策略网络
        """
        # 从经验池中采样
        if self.prioritized_replay:
            states, high_level_actions, low_level_actions, rewards, next_states, dones, weights, indices = \
                self.replay_buffer.sample(self.batch_size)
        else:
            states, high_level_actions, low_level_actions, rewards, next_states, dones = \
                self.replay_buffer.sample(self.batch_size)
            weights = torch.ones_like(rewards)

        # 将数据移动到设备
        states = states.to(self.device)
        high_level_actions = high_level_actions.to(self.device)
        low_level_actions = low_level_actions.to(self.device)
        rewards = rewards.to(self.device)
        next_states = next_states.to(self.device)
        dones = dones.to(self.device)
        weights = weights.to(self.device)

        # 更新低层策略
        if self.stats["steps"] % self.low_level_update_freq == 0:
            # 计算目标Q值
            with torch.no_grad():
                _, next_low_level_logits = self.target_policy(next_states)
                next_low_level_logits = [logits[high_level_actions[i]] for i, logits in enumerate(next_low_level_logits)]
                next_low_level_logits = torch.stack(next_low_level_logits)
                next_low_level_probs = F.softmax(next_low_level_logits, dim=-1)
                next_value = torch.sum(next_low_level_probs * next_low_level_logits, dim=-1)
                target_q = rewards + self.gamma * (1 - dones) * next_value

            # 计算当前Q值
            _, low_level_logits = self.policy(states)
            low_level_logits = [logits[high_level_actions[i]] for i, logits in enumerate(low_level_logits)]
            low_level_logits = torch.stack(low_level_logits)
            low_level_q = low_level_logits.gather(1, low_level_actions.unsqueeze(1)).squeeze(1)

            # 计算损失
            low_level_loss = F.mse_loss(low_level_q, target_q, reduction='none')

            # 应用重要性权重
            low_level_loss = (low_level_loss * weights).mean()

            # 更新网络
            self.optimizer.zero_grad()
            low_level_loss.backward()
            self.optimizer.step()

            # 如果使用优先经验回放，更新优先级
            if self.prioritized_replay:
                # 计算TD误差
                with torch.no_grad():
                    td_error = torch.abs(target_q - low_level_q).cpu().numpy()

                # 更新优先级
                self.replay_buffer.update_priorities(indices, td_error + 1e-6)

            # 更新统计信息
            self.stats["low_level_updates"] += 1
            self.stats["losses"]["low_level"].append(low_level_loss.item())

        # 更新高层策略
        if self.stats["steps"] % self.high_level_update_freq == 0:
            # 计算目标Q值
            with torch.no_grad():
                next_high_level_logits, _ = self.target_policy(next_states)
                next_high_level_probs = F.softmax(next_high_level_logits, dim=-1)
                next_value = torch.sum(next_high_level_probs * next_high_level_logits, dim=-1)
                target_q = rewards + self.gamma * (1 - dones) * next_value

            # 计算当前Q值
            high_level_logits, _ = self.policy(states)
            high_level_q = high_level_logits.gather(1, high_level_actions.unsqueeze(1)).squeeze(1)

            # 计算损失
            high_level_loss = F.mse_loss(high_level_q, target_q, reduction='none')

            # 应用重要性权重
            high_level_loss = (high_level_loss * weights).mean()

            # 更新网络
            self.optimizer.zero_grad()
            high_level_loss.backward()
            self.optimizer.step()

            # 如果使用优先经验回放，更新优先级
            if self.prioritized_replay:
                # 计算TD误差
                with torch.no_grad():
                    td_error = torch.abs(target_q - high_level_q).cpu().numpy()

                # 更新优先级
                self.replay_buffer.update_priorities(indices, td_error + 1e-6)

            # 更新统计信息
            self.stats["high_level_updates"] += 1
            self.stats["losses"]["high_level"].append(high_level_loss.item())

    def _update_target_network(self) -> None:
        """
        更新目标网络
        """
        # 软更新目标网络
        for param, target_param in zip(self.policy.parameters(), self.target_policy.parameters()):
            target_param.data.copy_(self.tau * param.data + (1 - self.tau) * target_param.data)

    def save(self, path: str) -> None:
        """
        保存模型

        Args:
            path: 保存路径
        """
        # 创建保存目录
        os.makedirs(os.path.dirname(path), exist_ok=True)

        # 保存模型
        torch.save({
            "policy": self.policy.state_dict(),
            "target_policy": self.target_policy.state_dict(),
            "optimizer": self.optimizer.state_dict(),
            "stats": self.stats
        }, path)

        logger.info(f"模型已保存到 {path}")

    def load(self, path: str) -> None:
        """
        加载模型

        Args:
            path: 加载路径
        """
        # 检查文件是否存在
        if not os.path.exists(path):
            logger.warning(f"模型文件不存在: {path}")
            return

        # 加载模型
        checkpoint = torch.load(path, map_location=self.device)

        # 加载参数
        self.policy.load_state_dict(checkpoint["policy"])
        self.target_policy.load_state_dict(checkpoint["target_policy"])
        self.optimizer.load_state_dict(checkpoint["optimizer"])
        self.stats = checkpoint["stats"]

        logger.info(f"模型已加载: {path}")
