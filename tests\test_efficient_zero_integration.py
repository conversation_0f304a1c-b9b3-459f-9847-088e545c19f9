#!/usr/bin/env python3
"""
EfficientZero集成测试

该测试脚本验证EfficientZero模块的完整功能，包括：
1. 模块导入测试
2. 模型创建测试
3. 算法初始化测试
4. 混合精度支持测试
5. 训练函数测试
6. 工具函数测试
7. 向后兼容性测试
"""

import sys
import os
import unittest
import torch
import numpy as np
from unittest.mock import Mock, patch

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from cardgame_ai.algorithms.efficient_zero import (
    EfficientZero, EfficientZeroModel, EfficientZeroAMP,
    train_efficient_zero, get_module_info,
    test_efficient_zero_amp, test_importance_weighted_training,
    monitor_training_performance, debug_model_gradients, analyze_replay_buffer
)


class TestEfficientZeroIntegration(unittest.TestCase):
    """EfficientZero集成测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.observation_shape = (84, 84, 3)
        self.state_shape = (84, 84, 3)
        self.action_shape = (18,)
        self.hidden_dim = 64
        self.state_dim = 32
        self.device = 'cpu'
    
    def test_module_info(self):
        """测试模块信息"""
        info = get_module_info()
        self.assertIsInstance(info, dict)
        self.assertIn('name', info)
        self.assertIn('version', info)
        self.assertIn('components', info)
        self.assertEqual(info['name'], 'EfficientZero')
        self.assertEqual(info['version'], '2.0.0')
        self.assertEqual(len(info['components']), 9)
        print("✓ 模块信息测试通过")
    
    def test_model_creation(self):
        """测试模型创建"""
        # 测试EfficientZeroModel
        model = EfficientZeroModel(
            observation_shape=self.observation_shape,
            action_shape=self.action_shape,
            hidden_dim=self.hidden_dim,
            state_dim=self.state_dim
        )
        self.assertIsNotNone(model)
        self.assertTrue(hasattr(model, 'representation_network'))
        self.assertTrue(hasattr(model, 'dynamics_network'))
        self.assertTrue(hasattr(model, 'prediction_network'))
        print("✓ EfficientZeroModel创建测试通过")
    
    def test_algorithm_creation(self):
        """测试算法创建"""
        algorithm = EfficientZero(
            state_shape=self.state_shape,
            action_shape=self.action_shape,
            hidden_dim=self.hidden_dim,
            state_dim=self.state_dim,
            device=self.device
        )
        self.assertIsNotNone(algorithm)
        self.assertTrue(hasattr(algorithm, 'model'))
        self.assertTrue(hasattr(algorithm, 'mcts'))
        print("✓ EfficientZero算法创建测试通过")
    
    def test_amp_creation(self):
        """测试混合精度版本创建"""
        amp_model = EfficientZeroAMP(
            state_shape=self.state_shape,
            action_shape=self.action_shape,
            hidden_dim=self.hidden_dim,
            state_dim=self.state_dim,
            use_mixed_precision=True,
            device=self.device
        )
        self.assertIsNotNone(amp_model)
        self.assertTrue(hasattr(amp_model, 'amp_enabled'))
        self.assertTrue(amp_model.amp_enabled)
        print("✓ EfficientZeroAMP创建测试通过")
    
    def test_model_forward(self):
        """测试模型前向传播"""
        model = EfficientZeroModel(
            observation_shape=self.observation_shape,
            action_shape=self.action_shape,
            hidden_dim=self.hidden_dim,
            state_dim=self.state_dim
        )

        # 确保模型在CPU上
        model = model.to('cpu')

        # 创建测试输入
        batch_size = 2
        observation = torch.randn(batch_size, *self.observation_shape).to('cpu')

        # 测试表示网络
        hidden_state = model.representation_network(observation)
        self.assertEqual(hidden_state.shape[0], batch_size)
        self.assertEqual(hidden_state.shape[1], self.state_dim)

        # 测试预测网络
        policy_logits, value = model.prediction_network(hidden_state)
        self.assertEqual(policy_logits.shape[0], batch_size)
        self.assertEqual(policy_logits.shape[1], self.action_shape[0])
        self.assertEqual(value.shape[0], batch_size)

        print("✓ 模型前向传播测试通过")
    
    def test_utility_functions(self):
        """测试工具函数"""
        # 这些函数需要实际的模型和数据，这里只测试它们是否可调用
        self.assertTrue(callable(test_efficient_zero_amp))
        self.assertTrue(callable(test_importance_weighted_training))
        self.assertTrue(callable(monitor_training_performance))
        self.assertTrue(callable(debug_model_gradients))
        self.assertTrue(callable(analyze_replay_buffer))
        print("✓ 工具函数测试通过")
    
    def test_training_function(self):
        """测试训练函数"""
        self.assertTrue(callable(train_efficient_zero))
        print("✓ 训练函数测试通过")
    
    def test_backward_compatibility(self):
        """测试向后兼容性"""
        # 测试从主包导入
        from cardgame_ai.algorithms import EfficientZero as EZ, EfficientZeroModel as EZM
        self.assertEqual(EZ, EfficientZero)
        self.assertEqual(EZM, EfficientZeroModel)
        print("✓ 向后兼容性测试通过")
    
    def test_device_compatibility(self):
        """测试设备兼容性"""
        # 测试CPU设备
        algorithm_cpu = EfficientZero(
            state_shape=self.state_shape,
            action_shape=self.action_shape,
            hidden_dim=self.hidden_dim,
            state_dim=self.state_dim,
            device='cpu'
        )
        self.assertEqual(str(algorithm_cpu.device), 'cpu')
        
        # 如果有CUDA，测试CUDA设备
        if torch.cuda.is_available():
            algorithm_cuda = EfficientZero(
                state_shape=self.state_shape,
                action_shape=self.action_shape,
                hidden_dim=self.hidden_dim,
                state_dim=self.state_dim,
                device='cuda'
            )
            self.assertTrue('cuda' in str(algorithm_cuda.device))
            print("✓ CUDA设备兼容性测试通过")
        
        print("✓ 设备兼容性测试通过")


def run_performance_test():
    """运行性能测试"""
    print("\n=== 性能测试 ===")
    
    import time
    
    # 测试模型创建性能
    start_time = time.time()
    for _ in range(10):
        model = EfficientZeroModel(
            observation_shape=(84, 84, 3),
            action_shape=(18,),
            hidden_dim=64,
            state_dim=32
        )
    creation_time = time.time() - start_time
    print(f"✓ 模型创建性能: {creation_time/10:.4f}秒/次")
    
    # 测试前向传播性能
    model = EfficientZeroModel(
        observation_shape=(84, 84, 3),
        action_shape=(18,),
        hidden_dim=64,
        state_dim=32
    )
    model = model.to('cpu')  # 确保模型在CPU上
    observation = torch.randn(1, 84, 84, 3).to('cpu')  # 确保输入在CPU上

    start_time = time.time()
    for _ in range(100):
        with torch.no_grad():
            hidden_state = model.representation_network(observation)
            policy_logits, value = model.prediction_network(hidden_state)
    forward_time = time.time() - start_time
    print(f"✓ 前向传播性能: {forward_time/100:.4f}秒/次")


if __name__ == '__main__':
    print("开始EfficientZero集成测试...")
    
    # 运行单元测试
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # 运行性能测试
    run_performance_test()
    
    print("\n🎉 所有测试完成！")
