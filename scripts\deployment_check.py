#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
部署检查脚本

验证斗地主AI优化系统的完整性和就绪状态
"""

import os
import sys
import subprocess
import logging
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from cardgame_ai.utils.logging import setup_logging

logger = logging.getLogger(__name__)


class DeploymentChecker:
    """部署检查器"""
    
    def __init__(self):
        """初始化检查器"""
        self.project_root = project_root
        self.checks_passed = 0
        self.checks_failed = 0
        self.warnings = 0
    
    def run_all_checks(self) -> bool:
        """运行所有检查"""
        print("🔍 斗地主AI优化系统 - 部署检查")
        print("=" * 50)
        
        checks = [
            ("项目结构", self.check_project_structure),
            ("配置文件", self.check_config_files),
            ("Python依赖", self.check_dependencies),
            ("代码质量", self.check_code_quality),
            ("GPU环境", self.check_gpu_environment),
            ("核心模块", self.check_core_modules),
            ("训练脚本", self.check_training_scripts),
            ("文档完整性", self.check_documentation)
        ]
        
        for check_name, check_func in checks:
            print(f"\n📋 检查: {check_name}")
            try:
                if check_func():
                    print(f"✅ {check_name} - 通过")
                    self.checks_passed += 1
                else:
                    print(f"❌ {check_name} - 失败")
                    self.checks_failed += 1
            except Exception as e:
                print(f"⚠️ {check_name} - 异常: {e}")
                self.checks_failed += 1
        
        # 显示总结
        self.show_summary()
        
        return self.checks_failed == 0
    
    def check_project_structure(self) -> bool:
        """检查项目结构"""
        required_dirs = [
            "cardgame_ai",
            "cardgame_ai/algorithms",
            "cardgame_ai/training",
            "cardgame_ai/evaluation",
            "cardgame_ai/utils",
            "configs",
            "scripts",
            "docs",
            "tests"
        ]
        
        missing_dirs = []
        for dir_path in required_dirs:
            full_path = self.project_root / dir_path
            if not full_path.exists():
                missing_dirs.append(dir_path)
        
        if missing_dirs:
            logger.error(f"缺少目录: {missing_dirs}")
            return False
        
        return True
    
    def check_config_files(self) -> bool:
        """检查配置文件"""
        required_configs = [
            "configs/base.yaml",
            "configs/training/efficient_zero.yaml",
            "pyproject.toml",
            ".flake8",
            ".pre-commit-config.yaml"
        ]
        
        missing_configs = []
        for config_path in required_configs:
            full_path = self.project_root / config_path
            if not full_path.exists():
                missing_configs.append(config_path)
        
        if missing_configs:
            logger.error(f"缺少配置文件: {missing_configs}")
            return False
        
        return True
    
    def check_dependencies(self) -> bool:
        """检查Python依赖"""
        required_packages = [
            "torch",
            "numpy",
            "ray",
            "hydra-core",
            "structlog",
            "psutil"
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package.replace("-", "_"))
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            logger.error(f"缺少依赖包: {missing_packages}")
            logger.info("请运行: pip install -r requirements.txt")
            return False
        
        return True
    
    def check_code_quality(self) -> bool:
        """检查代码质量"""
        try:
            # 检查是否有语法错误
            result = subprocess.run(
                [sys.executable, "-m", "py_compile", "scripts/optimized_training.py"],
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            if result.returncode != 0:
                logger.error(f"语法错误: {result.stderr}")
                return False
            
            return True
        except Exception as e:
            logger.error(f"代码质量检查失败: {e}")
            return False
    
    def check_gpu_environment(self) -> bool:
        """检查GPU环境"""
        try:
            import torch
            
            if not torch.cuda.is_available():
                logger.warning("未检测到CUDA GPU，将使用CPU训练")
                self.warnings += 1
                return True
            
            gpu_count = torch.cuda.device_count()
            logger.info(f"检测到 {gpu_count} 个GPU")
            
            for i in range(gpu_count):
                gpu_name = torch.cuda.get_device_name(i)
                memory_gb = torch.cuda.get_device_properties(i).total_memory / (1024**3)
                logger.info(f"GPU {i}: {gpu_name} ({memory_gb:.1f}GB)")
            
            return True
        except Exception as e:
            logger.error(f"GPU环境检查失败: {e}")
            return False
    
    def check_core_modules(self) -> bool:
        """检查核心模块"""
        core_modules = [
            "cardgame_ai.algorithms.efficient_zero",
            "cardgame_ai.training.distributed_trainer",
            "cardgame_ai.evaluation.performance_evaluator",
            "cardgame_ai.utils.logging",
            "cardgame_ai.utils.monitoring"
        ]
        
        failed_imports = []
        for module in core_modules:
            try:
                __import__(module)
            except ImportError as e:
                failed_imports.append((module, str(e)))
        
        if failed_imports:
            for module, error in failed_imports:
                logger.error(f"模块导入失败 {module}: {error}")
            return False
        
        return True
    
    def check_training_scripts(self) -> bool:
        """检查训练脚本"""
        scripts = [
            "scripts/optimized_training.py",
            "scripts/quick_start.py",
            "scripts/deployment_check.py"
        ]
        
        missing_scripts = []
        for script_path in scripts:
            full_path = self.project_root / script_path
            if not full_path.exists():
                missing_scripts.append(script_path)
        
        if missing_scripts:
            logger.error(f"缺少脚本文件: {missing_scripts}")
            return False
        
        return True
    
    def check_documentation(self) -> bool:
        """检查文档完整性"""
        required_docs = [
            "README_OPTIMIZED.md",
            "docs/project-structure.md",
            "docs/operational-guidelines.md",
            "docs/tech-stack.md",
            "斗地主AI优化架构文档.md"
        ]
        
        missing_docs = []
        for doc_path in required_docs:
            full_path = self.project_root / doc_path
            if not full_path.exists():
                missing_docs.append(doc_path)
        
        if missing_docs:
            logger.warning(f"缺少文档文件: {missing_docs}")
            self.warnings += 1
            return True  # 文档缺失不算致命错误
        
        return True
    
    def show_summary(self):
        """显示检查总结"""
        print("\n" + "=" * 50)
        print("📊 检查总结")
        print("=" * 50)
        
        total_checks = self.checks_passed + self.checks_failed
        success_rate = (self.checks_passed / total_checks * 100) if total_checks > 0 else 0
        
        print(f"✅ 通过检查: {self.checks_passed}")
        print(f"❌ 失败检查: {self.checks_failed}")
        print(f"⚠️ 警告数量: {self.warnings}")
        print(f"📈 成功率: {success_rate:.1f}%")
        
        if self.checks_failed == 0:
            print("\n🎉 系统部署检查完全通过！")
            print("✨ 斗地主AI优化系统已准备就绪")
            print("\n🚀 下一步操作:")
            print("1. 运行快速启动: python scripts/quick_start.py")
            print("2. 开始训练: python scripts/optimized_training.py")
            print("3. 查看文档: README_OPTIMIZED.md")
        else:
            print("\n⚠️ 发现问题，请修复后重新检查")
            print("💡 建议:")
            print("1. 检查依赖安装: pip install -r requirements.txt")
            print("2. 验证环境配置")
            print("3. 重新运行检查脚本")


def main():
    """主函数"""
    # 设置日志
    setup_logging(level="INFO", log_dir="logs")
    
    # 运行检查
    checker = DeploymentChecker()
    success = checker.run_all_checks()
    
    # 退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
