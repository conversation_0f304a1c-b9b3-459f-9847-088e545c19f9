"""
人类策略网络集成示例

展示如何在MCTS和EfficientZero中集成人类策略网络。
"""

import os
import torch
import numpy as np
import argparse
import logging
from typing import Dict, List, Any, Optional

from cardgame_ai.algorithms.human_policy_network import (
    HumanPolicyNetwork,
    EnhancedHumanPolicyNetwork
)
from cardgame_ai.algorithms.mcts import MCTS
from cardgame_ai.algorithms.efficient_zero import EfficientZero

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='人类策略网络集成示例')

    # 模型路径
    parser.add_argument('--human_model_path', type=str, default='models/human_policy/enhanced_human_policy.pt',
                        help='人类策略网络模型路径')

    # 模型参数
    parser.add_argument('--state_dim', type=int, default=256,
                        help='状态维度')
    parser.add_argument('--action_dim', type=int, default=309,
                        help='动作维度')

    # MCTS参数
    parser.add_argument('--num_simulations', type=int, default=50,
                        help='MCTS模拟次数')
    parser.add_argument('--human_prior_weight', type=float, default=0.5,
                        help='人类先验权重')

    # 风格参数
    parser.add_argument('--style_id', type=int, default=None,
                        help='人类风格ID，如果为None则随机选择')

    # 设备
    parser.add_argument('--device', type=str, default=None,
                        help='计算设备，如果为None则自动选择')

    return parser.parse_args()


class HumanPolicyAdapter:
    """
    人类策略网络适配器

    将人类策略网络适配为MCTS可用的先验策略提供者。
    """

    def __init__(self, human_policy: Any, style_id: Optional[int] = None, weight: float = 0.5):
        """
        初始化人类策略网络适配器

        Args:
            human_policy: 人类策略网络
            style_id: 风格ID，如果为None则随机选择
            weight: 人类先验权重
        """
        self.human_policy = human_policy
        self.style_id = style_id
        self.weight = weight

    def get_prior(self, state: Any, legal_actions: List[int]) -> Dict[int, float]:
        """
        获取人类先验概率

        Args:
            state: 游戏状态
            legal_actions: 合法动作列表

        Returns:
            动作到概率的映射
        """
        # 转换状态为张量
        if not isinstance(state, torch.Tensor):
            state_tensor = torch.tensor(state, dtype=torch.float32)
        else:
            state_tensor = state

        # 获取人类策略
        if isinstance(self.human_policy, EnhancedHumanPolicyNetwork):
            # 使用增强版人类策略网络
            action_probs = self.human_policy.predict(state_tensor, self.style_id)
        else:
            # 使用基础版人类策略网络
            action_probs = self.human_policy.predict(state_tensor)

        # 转换为NumPy数组
        if isinstance(action_probs, torch.Tensor):
            action_probs = action_probs.detach().cpu().numpy()

        # 创建动作到概率的映射
        prior_dict = {}
        for action in legal_actions:
            if action < len(action_probs):
                prior_dict[action] = float(action_probs[action])
            else:
                prior_dict[action] = 0.0

        return prior_dict


def integrate_with_mcts(args):
    """
    将人类策略网络集成到MCTS中

    Args:
        args: 命令行参数
    """
    # 设置设备
    device = args.device if args.device else ('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")

    # 加载人类策略网络
    logger.info(f"加载人类策略网络: {args.human_model_path}")

    if args.human_model_path.endswith('enhanced_human_policy.pt'):
        # 加载增强版人类策略网络
        human_policy = EnhancedHumanPolicyNetwork.load(args.human_model_path, device)
        logger.info("已加载增强版人类策略网络")

        # 获取风格信息
        style_id = args.style_id
        if style_id is not None:
            style_name = human_policy.get_style_description(style_id)
            logger.info(f"使用风格: {style_name} (ID: {style_id})")
        else:
            logger.info("将随机选择风格")
    else:
        # 加载基础版人类策略网络
        checkpoint = torch.load(args.human_model_path, map_location=device)
        human_policy = HumanPolicyNetwork(
            state_dim=args.state_dim,
            action_dim=args.action_dim
        ).to(device)
        human_policy.load_state_dict(checkpoint['model_state_dict'])
        logger.info("已加载基础版人类策略网络")

        # 基础版没有风格
        style_id = None

    # 创建人类策略网络适配器
    human_adapter = HumanPolicyAdapter(
        human_policy=human_policy,
        style_id=style_id,
        weight=args.human_prior_weight
    )

    # 创建MCTS
    mcts = MCTS(
        num_simulations=args.num_simulations,
        use_opponent_model_prior=True,  # 启用对手模型先验
        opponent_model_prior_weight=args.human_prior_weight  # 设置对手模型先验权重
    )

    logger.info(f"已创建MCTS，模拟次数: {args.num_simulations}，人类先验权重: {args.human_prior_weight}")

    # 模拟使用
    logger.info("模拟MCTS搜索过程中使用人类策略网络先验...")

    # 创建模拟状态和合法动作
    state = np.random.rand(args.state_dim)  # 模拟状态
    legal_actions = list(range(10))  # 模拟合法动作

    # 获取人类先验
    human_priors = human_adapter.get_prior(state, legal_actions)

    # 打印人类先验
    logger.info("人类策略网络先验:")
    for action, prior in sorted(human_priors.items(), key=lambda x: x[1], reverse=True)[:5]:
        logger.info(f"  动作 {action}: 先验概率 {prior:.4f}")

    # 在实际使用中，可以这样将人类先验传递给MCTS
    logger.info("在MCTS.run()方法中使用人类先验:")
    logger.info("mcts.run(root_state, model, opponent_model_priors={'player_id': human_priors})")

    return human_policy, mcts


def integrate_with_efficient_zero(args, human_policy):
    """
    将人类策略网络集成到EfficientZero中

    Args:
        args: 命令行参数
        human_policy: 人类策略网络
    """
    # 设置设备
    device = args.device if args.device else ('cuda' if torch.cuda.is_available() else 'cpu')

    # 创建EfficientZero
    logger.info("创建EfficientZero实例...")

    # 注意：这里只是示例，实际使用时需要提供正确的参数
    efficient_zero = EfficientZero(
        state_shape=(args.state_dim,),
        action_shape=(args.action_dim,),
        hidden_dim=128,
        device=device
    )

    # 获取风格ID
    style_id = args.style_id
    if isinstance(human_policy, EnhancedHumanPolicyNetwork) and style_id is not None:
        style_name = human_policy.get_style_description(style_id)
        logger.info(f"使用风格: {style_name} (ID: {style_id})")

    # 创建人类策略网络适配器
    human_adapter = HumanPolicyAdapter(
        human_policy=human_policy,
        style_id=style_id,
        weight=args.human_prior_weight
    )

    # 模拟使用
    logger.info("模拟在EfficientZero中使用人类策略网络...")

    # 创建模拟状态和合法动作
    state = np.random.rand(args.state_dim)  # 模拟状态
    legal_actions = list(range(10))  # 模拟合法动作

    # 获取人类先验
    human_priors = human_adapter.get_prior(state, legal_actions)

    # 在EfficientZero中，可以这样使用人类先验
    logger.info("在EfficientZero.act()方法中使用人类先验:")
    logger.info("efficient_zero.act(state, opponent_model_priors={'player_id': human_priors})")

    # 在实际训练中，可以这样设置对手模型先验
    logger.info("在训练循环中使用人类先验:")
    logger.info("""
    # 训练循环
    for episode in range(num_episodes):
        # 获取当前状态
        state = env.reset()

        while not done:
            # 获取合法动作
            legal_actions = env.get_legal_actions()

            # 获取人类先验
            human_priors = human_adapter.get_prior(state, legal_actions)

            # 使用人类先验选择动作
            action = efficient_zero.act(
                state,
                opponent_model_priors={'player_id': human_priors}
            )

            # 执行动作
            next_state, reward, done, info = env.step(action)

            # 更新状态
            state = next_state
    """)

    return efficient_zero


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()

    # 集成到MCTS
    human_policy, mcts = integrate_with_mcts(args)

    # 集成到EfficientZero
    efficient_zero = integrate_with_efficient_zero(args, human_policy)

    logger.info("人类策略网络集成示例完成")


if __name__ == '__main__':
    main()
