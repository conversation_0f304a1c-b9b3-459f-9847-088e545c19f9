"""
低层策略网络模块

实现层次化强化学习中的低层策略网络，负责选择具体的牌张组合。
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Any, Optional, Union

from cardgame_ai.games.doudizhu.card_group import CardGroupType


class LowLevelPolicy(nn.Module):
    """
    低层策略网络
    
    负责根据高层策略选择的牌型，从手牌中选择最佳的牌张组合。
    """
    
    def __init__(
        self, 
        state_dim: int, 
        low_level_action_dim: int,
        hidden_dims: List[int] = [256, 128],
        dropout: float = 0.1
    ):
        """
        初始化低层策略网络
        
        Args:
            state_dim: 状态维度
            low_level_action_dim: 低层动作维度（具体牌张组合的数量）
            hidden_dims: 隐藏层维度列表
            dropout: Dropout概率
        """
        super().__init__()
        
        # 保存参数
        self.state_dim = state_dim
        self.low_level_action_dim = low_level_action_dim
        
        # 定义高层动作空间（对应不同的牌型）
        self.high_level_action_dim = len(CardGroupType)
        
        # 高层动作编码器
        self.high_level_encoder = nn.Embedding(self.high_level_action_dim, 64)
        
        # 状态编码器
        self.state_encoder = nn.Sequential(
            nn.Linear(state_dim, 256),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 组合编码器
        self.combined_encoder = nn.Sequential(
            nn.Linear(256 + 64, hidden_dims[0]),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 构建网络层
        layers = []
        input_dim = hidden_dims[0]
        
        for hidden_dim in hidden_dims[1:]:
            layers.append(nn.Linear(input_dim, hidden_dim))
            layers.append(nn.ReLU())
            layers.append(nn.Dropout(dropout))
            input_dim = hidden_dim
        
        # 输出层
        layers.append(nn.Linear(input_dim, low_level_action_dim))
        
        # 构建序列模型
        self.network = nn.Sequential(*layers)
    
    def forward(
        self, 
        state: torch.Tensor, 
        high_level_action: torch.Tensor
    ) -> torch.Tensor:
        """
        前向传播
        
        Args:
            state: 状态张量
            high_level_action: 高层动作张量
            
        Returns:
            低层动作的logits
        """
        # 确保输入形状正确
        if state.dim() == 1:
            state = state.unsqueeze(0)  # 添加批次维度
        
        if high_level_action.dim() == 0:
            high_level_action = high_level_action.unsqueeze(0)  # 添加批次维度
        
        # 编码状态
        state_features = self.state_encoder(state)
        
        # 编码高层动作
        high_level_features = self.high_level_encoder(high_level_action)
        
        # 组合特征
        combined_features = torch.cat([state_features, high_level_features], dim=1)
        combined_features = self.combined_encoder(combined_features)
        
        # 预测低层动作
        logits = self.network(combined_features)
        
        return logits
    
    def act(
        self, 
        state: torch.Tensor, 
        high_level_action: int, 
        legal_actions: Optional[List[int]] = None, 
        temperature: float = 1.0
    ) -> Tuple[int, torch.Tensor]:
        """
        根据状态和高层动作选择低层动作
        
        Args:
            state: 状态张量
            high_level_action: 高层动作
            legal_actions: 合法动作列表
            temperature: 温度参数，控制探索程度
            
        Returns:
            选择的低层动作和动作概率
        """
        # 确保输入形状正确
        if state.dim() == 1:
            state = state.unsqueeze(0)  # 添加批次维度
        
        # 转换高层动作为张量
        high_level_action_tensor = torch.tensor(high_level_action, device=state.device)
        
        # 获取logits
        logits = self.forward(state, high_level_action_tensor)
        
        # 如果提供了合法动作，只考虑合法动作
        if legal_actions is not None:
            mask = torch.zeros_like(logits)
            mask[0, legal_actions] = 1
            logits = logits + (mask - 1) * 1e9
        
        # 应用温度参数
        if temperature != 1.0:
            logits = logits / temperature
        
        # 计算概率分布
        probs = F.softmax(logits, dim=-1)
        
        # 采样动作
        action = torch.multinomial(probs[0], 1).item()
        
        return action, probs[0]
        
    def execute_with_goal(
        self, 
        state_tensor: torch.Tensor, 
        goal: Dict[str, Any], 
        legal_actions: List[int]
    ) -> int:
        """
        根据高层目标执行低层动作
        
        Args:
            state_tensor: 状态张量
            goal: 高层策略设定的目标
            legal_actions: 合法动作列表
            
        Returns:
            选择的动作
        """
        # 从目标中提取高层动作ID
        high_level_action = goal.get('action_id')
        if high_level_action is None:
            # 如果没有提供action_id，尝试从card_type获取
            card_type = goal.get('card_type')
            if isinstance(card_type, CardGroupType):
                high_level_action = list(CardGroupType).index(card_type)
            else:
                # 如果无法获取高层动作，使用默认方法
                return self.execute(state_tensor, legal_actions)
        
        # 从目标中获取其他信息
        priority = goal.get('priority', 'medium')
        strategy = goal.get('strategy', 'balanced')
        
        # 确定温度参数
        if priority == 'high':
            temperature = 0.5  # 低温度，更确定性的选择
        elif priority == 'low':
            temperature = 2.0  # 高温度，更随机的选择
        else:
            temperature = 1.0  # 默认温度
        
        # 获取低层动作和概率
        action, _ = self.act(state_tensor, high_level_action, legal_actions, temperature)
        
        return action
    
    def execute(
        self, 
        state_tensor: torch.Tensor, 
        legal_actions: List[int], 
        context: Optional[Dict[str, Any]] = None
    ) -> int:
        """
        直接执行低层动作，不依赖高层目标
        
        Args:
            state_tensor: 状态张量
            legal_actions: 合法动作列表
            context: 上下文信息，可能包含历史目标等
            
        Returns:
            选择的动作
        """
        # 选择一个合理的高层动作
        # 默认使用第一种牌型（可以根据具体需求修改）
        high_level_action = 0
        
        # 如果提供了上下文且包含最近目标
        if context and 'recent_goals' in context:
            recent_goals = context['recent_goals']
            if recent_goals:
                # 使用最近目标中的高层动作
                latest_goal = recent_goals[-1]
                high_level_action = latest_goal.get('action_id', 0)
        
        # 获取低层动作
        action, _ = self.act(state_tensor, high_level_action, legal_actions)
        
        return action
        
    def evaluate_goal_achievement(
        self, 
        state_tensor: torch.Tensor, 
        goal: Dict[str, Any], 
        action: int, 
        next_state_tensor: torch.Tensor
    ) -> Dict[str, Any]:
        """
        评估目标达成情况
        
        Args:
            state_tensor: 执行动作前的状态
            goal: 高层策略设定的目标
            action: 执行的动作
            next_state_tensor: 执行动作后的状态
            
        Returns:
            包含评估结果的字典:
            - 'success': bool - 是否成功达成目标
            - 'score': float - 目标达成的分数 (0.0-1.0)
            - 'feedback': str - 反馈信息
        """
        # 基本实现，返回一个简单的评估结果
        # 实际应用中可以根据状态转换和目标信息进行更复杂的评估
        
        # 从目标中获取信息
        card_type = goal.get('card_type')
        confidence = goal.get('confidence', 0.5)
        
        # 简单评估：如果置信度高，认为目标更可能达成
        success = confidence > 0.6
        score = min(1.0, confidence + 0.2)  # 置信度加上一个奖励因子
        
        # 构建评估结果
        result = {
            'success': success,
            'score': score,
            'feedback': "目标执行成功" if success else "目标执行效果一般"
        }
        
        return result
