# 自动生成的配置文件
# 生成时间: {{ TIMESTAMP }}
# 硬件配置: {{ GPU_COUNT }}x {{ GPU_MODEL }}, {{ CPU_CORES }}核心CPU
# 算法: {{ ALGORITHM }}, 环境: {{ ENVIRONMENT }}

defaults:
  - algorithms/{{ ALGORITHM }}: base
  - environments/{{ ENVIRONMENT }}: efficient_zero_config
  {% if DISTRIBUTED_ENABLED -%}
  - hardware: multi_gpu
  {% else -%}
  - hardware: single_gpu
  {% endif -%}
  - _self_

# 硬件信息
hardware:
  gpu:
    count: {{ GPU_COUNT }}
    memory_gb: {{ GPU_MEMORY }}
    total_memory_gb: {{ TOTAL_GPU_MEMORY }}
    model: "{{ GPU_MODEL }}"
    ids: {{ GPU_IDS | to_json }}
  cpu:
    cores: {{ CPU_CORES }}
    threads: {{ CPU_THREADS }}
    architecture: "{{ CPU_ARCH }}"
  memory:
    total_gb: {{ SYSTEM_MEMORY }}
    available_gb: {{ AVAILABLE_MEMORY }}
  system:
    type: "{{ SYSTEM_TYPE }}"
    is_windows: {{ IS_WINDOWS | lower }}
    is_linux: {{ IS_LINUX | lower }}

# 训练配置
training:
  batch_size: {{ BATCH_SIZE }}
  learning_rate: {{ LEARNING_RATE }}
  num_workers: {{ NUM_WORKERS }}
  prefetch_factor: {{ PREFETCH_FACTOR }}
  gradient_accumulation_steps: {{ GRADIENT_ACCUMULATION }}
  epochs: 1000
  
  # 优化器配置
  optimizer:
    name: "AdamW"
    weight_decay: 0.01
    betas: [0.9, 0.999]
  
  # 学习率调度
  scheduler:
    name: "CosineAnnealingWarmRestarts"
    T_0: 100
    T_mult: 2
    eta_min: 1e-6

# MCTS配置
mcts:
  num_simulations: {{ MCTS_SIMULATIONS }}
  parallel_threads: {{ min(CPU_CORES, 8) }}
  batch_size_inference: {{ min(32, BATCH_SIZE // 4) }}
  c_puct: 1.25
  dirichlet_alpha: 0.3
  exploration_fraction: 0.25

# 设备配置
device:
  type: "{% if GPU_COUNT > 0 %}cuda{% else %}cpu{% endif %}"
  ids: {{ GPU_IDS | to_json }}
  mixed_precision: {{ MIXED_PRECISION | lower }}
  benchmark: true
  deterministic: false

# 分布式配置
distributed:
  enabled: {{ DISTRIBUTED_ENABLED | lower }}
  {% if DISTRIBUTED_ENABLED -%}
  backend: "nccl"
  world_size: {{ GPU_COUNT }}
  rank: 0
  init_method: "env://"
  {% endif -%}

# 内存优化
memory:
  {% for key, value in MEMORY_OPTIMIZATION.items() -%}
  {{ key }}: {{ value | lower if value is boolean else value }}
  {% endfor -%}

# 日志配置
logging:
  level: "INFO"
  save_dir: "logs"
  tensorboard: true
  wandb: false
  
# 检查点配置
checkpoint:
  save_dir: "checkpoints"
  save_frequency: 1000
  max_to_keep: 5
  auto_resume: true

{% if ALGORITHM == 'efficient_zero' -%}
# EfficientZero特定配置
efficient_zero:
  replay_buffer_size: {{ REPLAY_BUFFER_SIZE | default(100000) }}
  unroll_steps: {{ UNROLL_STEPS | default(5) }}
  td_steps: {{ TD_STEPS | default(5) }}
  model_buffer_size: {{ MODEL_BUFFER_SIZE | default(50000) }}
  reanalyze_ratio: {{ REANALYZE_RATIO | default(0.5) }}
  value_prefix: true
  use_priority: true
{% endif -%}

# 配置元信息
meta:
  config_version: "{{ CONFIG_VERSION }}"
  generated_by: "AutoConfigManager"
  hardware_profile: "{{ GPU_COUNT }}gpu_{{ GPU_MODEL | replace(' ', '_') | lower }}"
  optimization_level: "{% if GPU_COUNT >= 4 %}high{% elif GPU_COUNT >= 2 %}medium{% else %}basic{% endif %}"
