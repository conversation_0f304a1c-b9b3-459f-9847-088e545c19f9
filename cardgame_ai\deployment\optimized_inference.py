"""
优化推理模块

提供优化后的模型推理接口，支持量化模型、ONNX模型和TensorRT模型。
"""

import os
import time
import torch
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Union
import logging

# 配置日志
logger = logging.getLogger(__name__)

# 尝试导入ONNX相关库
try:
    import onnx
    import onnxruntime
    ONNX_AVAILABLE = True
except ImportError:
    logger.warning("ONNX相关库未安装，ONNX推理功能将不可用")
    ONNX_AVAILABLE = False

# 尝试导入TensorRT相关库
try:
    import tensorrt as trt
    import pycuda.driver as cuda
    import pycuda.autoinit
    TENSORRT_AVAILABLE = True
except ImportError:
    logger.warning("TensorRT相关库未安装，TensorRT推理功能将不可用")
    TENSORRT_AVAILABLE = False


class OptimizedInferenceModel:
    """
    优化推理模型

    封装不同类型的优化模型（原始PyTorch、量化、ONNX、TensorRT），提供统一的推理接口。
    """

    def __init__(
        self,
        model_path: str,
        model_type: str,
        device: str = 'cuda',
        providers: Optional[List[str]] = None
    ):
        """
        初始化优化推理模型

        Args:
            model_path: 模型路径
            model_type: 模型类型，可选值为'pytorch'、'pytorch_quantized'、'onnx'、'tensorrt'
            device: 计算设备
            providers: ONNX推理提供者列表
        """
        self.model_path = model_path
        self.model_type = model_type
        self.device = device

        # 加载模型
        if model_type == 'pytorch':
            # 加载PyTorch模型
            self.model = torch.load(model_path)
            self.model.eval()
            self.model.to(device)
            logger.info(f"已加载PyTorch模型: {model_path}")

        elif model_type == 'pytorch_quantized':
            # 加载量化PyTorch模型
            self.model = torch.load(model_path)
            self.model.eval()
            logger.info(f"已加载量化PyTorch模型: {model_path}")

        elif model_type == 'onnx':
            # 检查ONNX是否可用
            if not ONNX_AVAILABLE:
                raise ImportError("ONNX相关库未安装，无法加载ONNX模型")

            # 设置ONNX推理提供者
            if providers is None:
                providers = ['CUDAExecutionProvider'] if device == 'cuda' else ['CPUExecutionProvider']

            # 创建ONNX推理会话
            self.model = onnxruntime.InferenceSession(model_path, providers=providers)
            logger.info(f"已加载ONNX模型: {model_path}")
            logger.info(f"使用提供者: {self.model.get_providers()}")

        elif model_type == 'tensorrt':
            # 检查TensorRT是否可用
            if not TENSORRT_AVAILABLE:
                raise ImportError("TensorRT相关库未安装，无法加载TensorRT模型")

            # 加载TensorRT引擎
            logger.info(f"加载TensorRT引擎: {model_path}")
            with open(model_path, 'rb') as f:
                engine_data = f.read()

            # 创建TensorRT运行时和引擎
            self.trt_runtime = trt.Runtime(trt.Logger(trt.Logger.WARNING))
            self.trt_engine = self.trt_runtime.deserialize_cuda_engine(engine_data)

            # 创建执行上下文
            self.trt_context = self.trt_engine.create_execution_context()

            # 创建CUDA流
            self.trt_stream = cuda.Stream()

            # 获取输入和输出绑定信息
            self.input_binding_idx = 0
            self.output_binding_idxs = [i for i in range(1, self.trt_engine.num_bindings)]

            logger.info(f"已加载TensorRT模型: {model_path}")

        else:
            raise ValueError(f"不支持的模型类型: {model_type}")

        # 记录推理次数和总时间，用于计算平均推理时间
        self.inference_count = 0
        self.total_inference_time = 0.0

    def infer(self, inputs: Union[torch.Tensor, np.ndarray]) -> Dict[str, np.ndarray]:
        """
        执行推理

        Args:
            inputs: 输入数据，可以是PyTorch张量或NumPy数组

        Returns:
            推理结果字典，键为输出名称，值为NumPy数组
        """
        # 记录开始时间
        start_time = time.time()

        # 根据模型类型执行推理
        if self.model_type == 'pytorch':
            # 确保输入是PyTorch张量
            if isinstance(inputs, np.ndarray):
                inputs = torch.from_numpy(inputs).to(self.device)
            elif isinstance(inputs, torch.Tensor) and inputs.device != self.device:
                inputs = inputs.to(self.device)

            # 执行推理
            with torch.no_grad():
                outputs = self.model(inputs)

            # 处理输出
            if isinstance(outputs, tuple):
                # 如果输出是元组，假设是(policy_logits, value)
                policy_logits, value = outputs
                results = {
                    'policy_logits': policy_logits.cpu().numpy(),
                    'value': value.cpu().numpy()
                }
            else:
                # 如果输出是单个张量
                results = {'output': outputs.cpu().numpy()}

        elif self.model_type == 'pytorch_quantized':
            # 确保输入是CPU上的PyTorch张量
            if isinstance(inputs, np.ndarray):
                inputs = torch.from_numpy(inputs)
            elif isinstance(inputs, torch.Tensor) and inputs.device.type != 'cpu':
                inputs = inputs.cpu()

            # 执行推理
            with torch.no_grad():
                outputs = self.model(inputs)

            # 处理输出
            if isinstance(outputs, tuple):
                # 如果输出是元组，假设是(policy_logits, value)
                policy_logits, value = outputs
                results = {
                    'policy_logits': policy_logits.numpy(),
                    'value': value.numpy()
                }
            else:
                # 如果输出是单个张量
                results = {'output': outputs.numpy()}

        elif self.model_type == 'onnx':
            # 确保输入是NumPy数组
            if isinstance(inputs, torch.Tensor):
                inputs = inputs.cpu().numpy()

            # 获取输入名称
            input_name = self.model.get_inputs()[0].name

            # 准备输入
            onnx_inputs = {input_name: inputs}

            # 执行推理
            outputs = self.model.run(None, onnx_inputs)

            # 获取输出名称
            output_names = [output.name for output in self.model.get_outputs()]

            # 构建结果字典
            results = {name: output for name, output in zip(output_names, outputs)}

        elif self.model_type == 'tensorrt':
            # 确保输入是NumPy数组
            if isinstance(inputs, torch.Tensor):
                inputs = inputs.cpu().numpy()

            # 获取输入形状
            input_shape = self.trt_engine.get_binding_shape(self.input_binding_idx)

            # 分配输入/输出缓冲区
            h_input = cuda.pagelocked_empty(trt.volume(input_shape), dtype=np.float32)

            # 准备输出缓冲区
            h_outputs = []
            d_outputs = []

            for idx in self.output_binding_idxs:
                output_shape = self.trt_engine.get_binding_shape(idx)
                h_output = cuda.pagelocked_empty(trt.volume(output_shape), dtype=np.float32)
                d_output = cuda.mem_alloc(h_output.nbytes)
                h_outputs.append(h_output)
                d_outputs.append(d_output)

            # 复制输入数据
            np.copyto(h_input, inputs.ravel())

            # 分配设备内存
            d_input = cuda.mem_alloc(h_input.nbytes)

            # 复制输入数据到设备
            cuda.memcpy_htod_async(d_input, h_input, self.trt_stream)

            # 执行推理
            bindings = [int(d_input)] + [int(d_output) for d_output in d_outputs]
            self.trt_context.execute_async_v2(bindings=bindings, stream_handle=self.trt_stream.handle)

            # 复制输出数据到主机
            for i, d_output in enumerate(d_outputs):
                cuda.memcpy_dtoh_async(h_outputs[i], d_output, self.trt_stream)

            # 同步流
            self.trt_stream.synchronize()

            # 构建结果字典
            results = {}
            for i, idx in enumerate(self.output_binding_idxs):
                output_name = self.trt_engine.get_binding_name(idx)
                output_shape = self.trt_engine.get_binding_shape(idx)
                results[output_name] = h_outputs[i].reshape(output_shape)

        else:
            raise ValueError(f"不支持的模型类型: {self.model_type}")

        # 计算推理时间
        inference_time = time.time() - start_time

        # 更新统计信息
        self.inference_count += 1
        self.total_inference_time += inference_time

        return results

    def get_avg_inference_time(self) -> float:
        """
        获取平均推理时间（秒）

        Returns:
            平均推理时间
        """
        if self.inference_count == 0:
            return 0.0
        return self.total_inference_time / self.inference_count

    def get_fps(self) -> float:
        """
        获取每秒推理帧数

        Returns:
            每秒推理帧数
        """
        avg_time = self.get_avg_inference_time()
        if avg_time == 0.0:
            return 0.0
        return 1.0 / avg_time

    def __del__(self):
        """
        析构函数，释放资源
        """
        # 释放TensorRT资源
        if self.model_type == 'tensorrt' and hasattr(self, 'trt_context'):
            del self.trt_context
            del self.trt_engine
            del self.trt_runtime


def create_optimized_inference_model(
    model_path: str,
    model_type: str,
    device: str = 'cuda',
    providers: Optional[List[str]] = None
) -> OptimizedInferenceModel:
    """
    创建优化推理模型

    Args:
        model_path: 模型路径
        model_type: 模型类型，可选值为'pytorch'、'pytorch_quantized'、'onnx'、'tensorrt'
        device: 计算设备
        providers: ONNX推理提供者列表

    Returns:
        优化推理模型
    """
    return OptimizedInferenceModel(
        model_path=model_path,
        model_type=model_type,
        device=device,
        providers=providers
    )


class OptimizedEfficientZero:
    """
    优化版EfficientZero

    使用优化推理模型包装EfficientZero，提供与原始EfficientZero相同的接口。
    """

    def __init__(
        self,
        model_path: str,
        model_type: str = 'pytorch',
        device: str = 'cuda',
        providers: Optional[List[str]] = None
    ):
        """
        初始化优化版EfficientZero

        Args:
            model_path: 模型路径
            model_type: 模型类型，可选值为'pytorch'、'pytorch_quantized'、'onnx'、'tensorrt'
            device: 计算设备
            providers: ONNX推理提供者列表
        """
        self.model = create_optimized_inference_model(
            model_path=model_path,
            model_type=model_type,
            device=device,
            providers=providers
        )
        self.device = device

    def predict(self, state: Union[Any, np.ndarray]) -> Tuple[List[float], float]:
        """
        预测状态的动作概率分布和价值

        Args:
            state: 游戏状态或观察

        Returns:
            Tuple[List[float], float]: 动作概率分布、状态价值
        """
        # 将状态转换为张量
        if hasattr(state, 'vectorize'):
            # 如果是State对象，调用vectorize方法
            state_tensor = state.vectorize()
        elif not isinstance(state, (np.ndarray, torch.Tensor)):
            # 如果不是NumPy数组或PyTorch张量，尝试转换
            state_tensor = np.array(state)
        else:
            state_tensor = state

        # 添加批次维度
        if isinstance(state_tensor, np.ndarray) and state_tensor.ndim == 1:
            state_tensor = state_tensor[np.newaxis, :]
        elif isinstance(state_tensor, torch.Tensor) and state_tensor.dim() == 1:
            state_tensor = state_tensor.unsqueeze(0)

        # 执行推理
        outputs = self.model.infer(state_tensor)

        # 处理输出
        if 'policy_logits' in outputs and 'value' in outputs:
            # 计算动作概率
            policy_logits = outputs['policy_logits']
            policy = np.exp(policy_logits - np.max(policy_logits, axis=1, keepdims=True))
            policy = policy / np.sum(policy, axis=1, keepdims=True)

            # 获取价值
            value = outputs['value']

            return policy[0].tolist(), float(value[0])
        else:
            # 如果输出格式不符合预期，返回默认值
            logger.warning("输出格式不符合预期，返回默认值")
            return [1.0 / 656] * 656, 0.0

    def get_stats(self) -> Dict[str, float]:
        """
        获取推理统计信息

        Returns:
            统计信息字典
        """
        return {
            'avg_inference_time': self.model.get_avg_inference_time(),
            'fps': self.model.get_fps(),
            'inference_count': self.model.inference_count
        }
