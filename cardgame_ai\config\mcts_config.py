"""
MCTS配置模块

包含MCTS算法的默认配置参数和预设配置。
"""

DEFAULT_MCTS_CONFIG = {
    # 基础MCTS参数
    "num_simulations": 50,  # 每次决策的模拟次数
    "discount": 0.997,  # 折扣因子
    "dirichlet_alpha": 0.25,  # Dirichlet噪声参数
    "exploration_fraction": 0.25,  # 探索噪声比例
    "pb_c_base": 19652,  # PUCT公式基础常数
    "pb_c_init": 1.25,  # PUCT公式初始常数
    "root_exploration_noise": True,  # 是否在根节点添加探索噪声
    
    # 自适应计算时间
    "use_act": True,  # 是否使用自适应计算时间
    "act_min_simulations": 10,  # ACT最小模拟次数
    "act_confidence_threshold": 0.95,  # ACT置信度阈值
    "act_visit_threshold": 20,  # ACT访问次数阈值
    
    # 信念状态
    "use_belief_state": False,  # 是否使用信念状态
    "use_information_value": False,  # 是否使用信息价值评估
    "information_value_weight": 0.3,  # 信息价值权重
    "information_value_method": "combined",  # 信息价值计算方法
    
    # 深度信念追踪器
    "use_deep_belief_tracker": False,  # 是否使用深度信念追踪器
    "deep_belief_weight": 0.7,  # 深度信念权重
    "deepbelief_model_path": "models/belief_tracker/transformer_belief_model.pt",  # 深度信念模型路径
    
    # 对手模型
    "use_opponent_model_prior": False,  # 是否使用对手模型先验
    "opponent_model_prior_weight": 0.5,  # 对手模型先验权重
    "opponent_model_path": "models/opponent_model/opponent_model.pt",  # 对手模型路径
    
    # 风险敏感决策
    "use_risk_sensitive_decision": False,  # 是否使用风险敏感决策
    "risk_alpha": 0.05,  # CVaR的置信水平
    "risk_beta": 0.1,  # 风险厌恶系数
    
    # 关键时刻检测
    "use_key_moment_detector": False,  # 是否使用关键时刻检测器
    "key_moment_factor": 2.0,  # 关键时刻因子
    
    # 反事实推理
    "use_counterfactual": False,  # 是否启用反事实推理
    "spectral_clusters": 2,  # 谱聚类簇数量
}

# 预设配置
FAST_MCTS_CONFIG = {
    **DEFAULT_MCTS_CONFIG,
    "num_simulations": 20,
    "act_min_simulations": 5,
}

STRONG_MCTS_CONFIG = {
    **DEFAULT_MCTS_CONFIG,
    "num_simulations": 100,
    "use_belief_state": True,
    "use_information_value": True,
}

# 启用深度信念追踪器的配置
DEEP_BELIEF_MCTS_CONFIG = {
    **STRONG_MCTS_CONFIG,
    "use_deep_belief_tracker": True,
    "deep_belief_weight": 0.7,
}

# 综合全部高级功能的配置
ADVANCED_MCTS_CONFIG = {
    **DEEP_BELIEF_MCTS_CONFIG,
    "use_opponent_model_prior": True,
    "use_risk_sensitive_decision": True,
    "use_key_moment_detector": True,
}

def get_mcts_config(config_name: str = "default") -> dict:
    """
    获取指定名称的MCTS配置
    
    Args:
        config_name (str, optional): 配置名称. Defaults to "default".
        
    Returns:
        dict: MCTS配置
    """
    config_name = config_name.lower()
    
    if config_name == "default":
        return DEFAULT_MCTS_CONFIG
    elif config_name == "fast":
        return FAST_MCTS_CONFIG
    elif config_name == "strong":
        return STRONG_MCTS_CONFIG
    elif config_name == "deep_belief":
        return DEEP_BELIEF_MCTS_CONFIG
    elif config_name == "advanced":
        return ADVANCED_MCTS_CONFIG
    else:
        print(f"Warning: Unknown config name '{config_name}'. Using default config.")
        return DEFAULT_MCTS_CONFIG 