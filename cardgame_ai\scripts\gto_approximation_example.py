#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
GTO近似示例脚本

展示如何使用GTO近似模块，生成和利用博弈论最优(GTO)策略。
"""

import os
import sys
import argparse
import logging
import numpy as np
import torch
from typing import Dict, List, Any, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from cardgame_ai.algorithms.gto_approximation import GTOPolicy, SimplifiedCFR, GTORegularizer
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.games.doudizhu.state import DouDizhuState
from cardgame_ai.algorithms.efficient_zero import EfficientZero

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('gto_approximation_example.log')
    ]
)
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='GTO近似示例')
    
    parser.add_argument('--mode', type=str, default='train',
                        choices=['train', 'evaluate', 'generate'],
                        help='运行模式：训练、评估或生成GTO策略')
    parser.add_argument('--gto_policy_path', type=str, default='data/gto_policy.pkl',
                        help='GTO策略文件路径')
    parser.add_argument('--model_path', type=str, default=None,
                        help='模型路径')
    parser.add_argument('--gto_regularization_weight', type=float, default=0.1,
                        help='GTO正则化权重')
    parser.add_argument('--num_episodes', type=int, default=10,
                        help='训练或评估的回合数')
    parser.add_argument('--batch_size', type=int, default=32,
                        help='批次大小')
    parser.add_argument('--learning_rate', type=float, default=0.001,
                        help='学习率')
    parser.add_argument('--cfr_iterations', type=int, default=1000,
                        help='CFR迭代次数')
    
    return parser.parse_args()


def state_feature_extractor(state: DouDizhuState) -> str:
    """
    状态特征提取器
    
    将状态转换为特征字符串，用于索引GTO策略。
    
    Args:
        state: 斗地主游戏状态
        
    Returns:
        状态特征字符串
    """
    # 简化的特征提取，实际应用中可能需要更复杂的特征
    features = []
    
    # 添加当前玩家
    features.append(f"player:{state.current_player}")
    
    # 添加地主
    features.append(f"landlord:{state.landlord}")
    
    # 添加手牌数量
    for i, hand in enumerate(state.hands):
        features.append(f"hand{i}:{len(hand)}")
    
    # 添加上一手牌
    if state.last_move:
        features.append(f"last_move:{state.last_move.type.name}")
    else:
        features.append("last_move:None")
    
    # 添加上一个出牌的玩家
    features.append(f"last_player:{state.last_player}")
    
    # 添加连续不出的次数
    features.append(f"num_passes:{state.num_passes}")
    
    return "|".join(features)


def default_policy_generator(state: DouDizhuState, legal_actions: List[int]) -> np.ndarray:
    """
    默认策略生成器
    
    为没有预计算策略的状态生成近似GTO策略。
    
    Args:
        state: 斗地主游戏状态
        legal_actions: 合法动作列表
        
    Returns:
        近似GTO策略分布
    """
    # 简化的策略生成，实际应用中可能需要更复杂的逻辑
    policy = np.zeros(max(legal_actions) + 1)
    
    # 如果有炸弹或火箭，增加其概率
    for action in legal_actions:
        card_group = state.get_card_group_from_action(action)
        if card_group and card_group.type.name in ['BOMB', 'ROCKET']:
            policy[action] = 2.0
        else:
            policy[action] = 1.0
    
    # 归一化
    policy = policy / np.sum(policy)
    
    return policy


def generate_gto_policy(args):
    """
    使用简化CFR生成GTO策略
    
    Args:
        args: 命令行参数
    """
    # 创建环境
    env = DouDizhuEnvironment()
    
    # 定义CFR所需的函数
    def is_terminal(state_key: str) -> bool:
        """判断状态是否为终止状态"""
        # 解析状态键
        state_dict = {}
        for feature in state_key.split("|"):
            key, value = feature.split(":")
            state_dict[key] = value
        
        # 如果有玩家手牌为0，则为终止状态
        for i in range(3):
            if f"hand{i}" in state_dict and state_dict[f"hand{i}"] == "0":
                return True
        
        return False
    
    def get_legal_actions(state_key: str) -> List[int]:
        """获取合法动作"""
        # 在实际应用中，需要从状态键中解析出状态，然后获取合法动作
        # 这里简化处理，返回一些示例动作
        return list(range(10))
    
    def get_next_state(state_key: str, action: int) -> str:
        """获取下一个状态"""
        # 在实际应用中，需要从状态键中解析出状态，然后获取下一个状态
        # 这里简化处理，返回一个示例状态
        return f"{state_key}|action:{action}"
    
    def utility_function(state_key: str, action: int) -> float:
        """效用函数"""
        # 在实际应用中，需要从状态键中解析出状态，然后计算效用
        # 这里简化处理，返回一个示例效用
        return 0.0
    
    # 创建简化CFR
    cfr = SimplifiedCFR(
        num_actions=env.action_space.n,
        utility_function=utility_function,
        is_terminal=is_terminal,
        get_legal_actions=get_legal_actions,
        get_next_state=get_next_state,
        max_iterations=args.cfr_iterations
    )
    
    # 训练CFR
    logger.info(f"开始训练CFR，迭代次数: {args.cfr_iterations}")
    initial_state = "player:0|landlord:0|hand0:17|hand1:17|hand2:17|last_move:None|last_player:None|num_passes:0"
    gto_policy = cfr.train(initial_state)
    
    # 保存GTO策略
    gto_policy.save(args.gto_policy_path)
    logger.info(f"已保存GTO策略: {args.gto_policy_path}")


def train_with_gto_regularization(args):
    """
    使用GTO正则化训练模型
    
    Args:
        args: 命令行参数
    """
    # 创建环境
    env = DouDizhuEnvironment()
    
    # 获取观察和动作空间
    observation_shape = env.observation_space.shape
    action_shape = (env.action_space.n,)
    
    # 创建GTO策略
    gto_policy = GTOPolicy(
        policy_path=args.gto_policy_path if os.path.exists(args.gto_policy_path) else None,
        feature_extractor=state_feature_extractor,
        default_policy_generator=default_policy_generator
    )
    
    # 创建EfficientZero模型
    model = EfficientZero(
        state_shape=observation_shape,
        action_shape=action_shape,
        hidden_dim=128,
        state_dim=64,
        use_resnet=False,
        batch_size=args.batch_size,
        learning_rate=args.learning_rate,
        use_gto_regularization=True,
        gto_policy_path=args.gto_policy_path if os.path.exists(args.gto_policy_path) else None,
        gto_regularization_weight=args.gto_regularization_weight,
        gto_feature_extractor=state_feature_extractor,
        gto_default_policy_generator=default_policy_generator
    )
    
    # 如果有预训练模型，加载参数
    if args.model_path and os.path.exists(args.model_path):
        model.load(args.model_path)
        logger.info(f"已加载预训练模型: {args.model_path}")
    
    # 训练模型
    logger.info("开始训练...")
    
    for episode in range(args.num_episodes):
        # 重置环境
        state = env.reset()
        done = False
        episode_reward = 0
        
        # 收集轨迹
        trajectory = []
        
        while not done:
            # 选择动作
            action = model.select_action(state)
            
            # 执行动作
            next_state, reward, done, info = env.step(action)
            
            # 存储经验
            trajectory.append({
                'state': state,
                'action': action,
                'reward': reward,
                'next_state': next_state,
                'done': done
            })
            
            # 更新状态和奖励
            state = next_state
            episode_reward += reward
        
        # 训练模型
        batch = {
            'states': np.array([exp['state'] for exp in trajectory]),
            'actions': np.array([exp['action'] for exp in trajectory]),
            'rewards': np.array([exp['reward'] for exp in trajectory]),
            'next_states': np.array([exp['next_state'] for exp in trajectory]),
            'dones': np.array([exp['done'] for exp in trajectory])
        }
        
        losses = model.train(batch)
        
        # 输出训练信息
        logger.info(f"Episode {episode + 1}/{args.num_episodes}, Reward: {episode_reward}")
        logger.info(f"Losses: {losses}")
    
    # 保存模型
    if args.model_path:
        model.save(args.model_path)
        logger.info(f"已保存模型: {args.model_path}")
    
    # 保存GTO策略
    gto_policy.save(args.gto_policy_path)
    logger.info(f"已保存GTO策略: {args.gto_policy_path}")


def evaluate_with_gto(args):
    """
    使用GTO策略评估模型
    
    Args:
        args: 命令行参数
    """
    # 创建环境
    env = DouDizhuEnvironment()
    
    # 获取观察和动作空间
    observation_shape = env.observation_space.shape
    action_shape = (env.action_space.n,)
    
    # 创建GTO策略
    gto_policy = GTOPolicy(
        policy_path=args.gto_policy_path if os.path.exists(args.gto_policy_path) else None,
        feature_extractor=state_feature_extractor,
        default_policy_generator=default_policy_generator
    )
    
    # 创建EfficientZero模型
    model = EfficientZero(
        state_shape=observation_shape,
        action_shape=action_shape,
        hidden_dim=128,
        state_dim=64,
        use_resnet=False
    )
    
    # 如果有预训练模型，加载参数
    if args.model_path and os.path.exists(args.model_path):
        model.load(args.model_path)
        logger.info(f"已加载预训练模型: {args.model_path}")
    else:
        logger.warning("未提供预训练模型，将使用随机初始化的模型")
    
    # 评估模型
    logger.info("开始评估...")
    
    total_reward = 0
    gto_agreement = 0
    total_actions = 0
    
    for episode in range(args.num_episodes):
        # 重置环境
        state = env.reset()
        done = False
        episode_reward = 0
        episode_agreement = 0
        episode_actions = 0
        
        while not done:
            # 获取合法动作
            legal_actions = state.get_legal_actions()
            
            # 模型选择动作
            model_action = model.select_action(state)
            
            # GTO策略选择动作
            gto_action = gto_policy.get_action(state, legal_actions)
            
            # 检查一致性
            if model_action == gto_action:
                episode_agreement += 1
            
            # 执行动作
            next_state, reward, done, info = env.step(model_action)
            
            # 更新状态和奖励
            state = next_state
            episode_reward += reward
            episode_actions += 1
        
        # 更新统计信息
        total_reward += episode_reward
        gto_agreement += episode_agreement
        total_actions += episode_actions
        
        # 输出评估信息
        logger.info(f"Episode {episode + 1}/{args.num_episodes}, Reward: {episode_reward}")
        logger.info(f"GTO Agreement: {episode_agreement}/{episode_actions} ({episode_agreement/episode_actions:.2%})")
    
    # 输出总体评估结果
    logger.info(f"Average Reward: {total_reward/args.num_episodes}")
    logger.info(f"Overall GTO Agreement: {gto_agreement}/{total_actions} ({gto_agreement/total_actions:.2%})")


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 根据模式运行
    if args.mode == 'train':
        train_with_gto_regularization(args)
    elif args.mode == 'evaluate':
        evaluate_with_gto(args)
    elif args.mode == 'generate':
        generate_gto_policy(args)
    else:
        logger.error(f"不支持的模式: {args.mode}")


if __name__ == "__main__":
    main()
