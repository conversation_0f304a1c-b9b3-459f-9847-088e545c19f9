#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
参数验证模块

提供对命令行参数的验证功能，确保命令行参数的有效性。
特别是对设备(device)参数进行格式检查和GPU可用性验证。

使用示例:
    from cardgame_ai.utils.arg_validation import validate_device
    
    is_valid, error_msg = validate_device('cuda:0')
    if not is_valid:
        print(f"设备参数无效: {error_msg}")
"""

import argparse
import logging
from typing import Tuple, Dict, List, Any, Optional, Union

# 设置日志
logger = logging.getLogger(__name__)

# 设备相关常量
VALID_DEVICE_PREFIXES = ['cpu', 'cuda', 'cuda:']  # 有效的设备前缀
MAX_CUDA_DEVICES = 8  # 支持的最大CUDA设备数量（一般不会超过8个GPU）


def validate_device(device: Optional[str]) -> Tuple[bool, str]:
    """
    验证设备参数的有效性。
    
    Args:
        device: 设备参数，如'cpu', 'cuda', 'cuda:0'等
        
    Returns:
        Tuple[bool, str]: (验证是否通过, 错误或警告信息)
    """
    # 如果没有指定device参数，不需要验证
    if not device:
        return True, ""
    
    # 验证device格式
    device = device.lower()
    
    # 多GPU配置验证（用逗号分隔的设备列表）
    if ',' in device:
        devices = [d.strip() for d in device.split(',')]
        
        # 检查每个设备
        for dev in devices:
            # 验证每个设备的有效性
            if not any(dev.startswith(prefix) for prefix in VALID_DEVICE_PREFIXES):
                return False, f"无效的设备格式: '{dev}'。有效格式: 'cpu', 'cuda', 'cuda:0', 'cuda:1', ..."
            
            # 验证GPU索引
            if dev.startswith('cuda:'):
                try:
                    index = int(dev.split(':')[1])
                    if index < 0 or index >= MAX_CUDA_DEVICES:
                        return False, f"无效的CUDA设备索引: {index}。索引应在0到{MAX_CUDA_DEVICES-1}之间。"
                except ValueError:
                    return False, f"无效的CUDA设备索引格式: '{dev}'。有效格式如: 'cuda:0'"
        
        # 检查CUDA支持和设备数量
        try:
            import torch
            if not torch.cuda.is_available():
                return False, "指定了CUDA设备，但CUDA不可用。请检查CUDA安装或使用'cpu'作为设备。"
            
            available_devices = torch.cuda.device_count()
            for dev in devices:
                if dev.startswith('cuda:'):
                    index = int(dev.split(':')[1])
                    if index >= available_devices:
                        return False, f"指定的CUDA设备索引 {index} 超出了可用设备数量 {available_devices}。"
        except ImportError:
            return False, "PyTorch导入失败，无法验证CUDA设备。请检查PyTorch安装。"
        except Exception as e:
            return False, f"验证CUDA设备时出错: {str(e)}"
        
        return True, ""
    
    # 单一设备验证
    if not any(device.startswith(prefix) for prefix in VALID_DEVICE_PREFIXES):
        return False, f"无效的设备格式: '{device}'。有效格式: 'cpu', 'cuda', 'cuda:0', 'cuda:1', ..."
    
    # 如果是CPU，无需进一步验证
    if device == 'cpu':
        return True, ""
    
    # 验证CUDA设备
    try:
        import torch
        if not torch.cuda.is_available():
            return False, "指定了CUDA设备，但CUDA不可用。请检查CUDA安装或使用'cpu'作为设备。"
        
        # 如果只是指定了'cuda'，意味着使用默认设备（通常是cuda:0）
        if device == 'cuda':
            return True, ""
        
        # 验证特定的CUDA设备索引
        if device.startswith('cuda:'):
            try:
                index = int(device.split(':')[1])
                available_devices = torch.cuda.device_count()
                
                if index < 0 or index >= MAX_CUDA_DEVICES:
                    return False, f"无效的CUDA设备索引: {index}。索引应在0到{MAX_CUDA_DEVICES-1}之间。"
                
                if index >= available_devices:
                    return False, f"指定的CUDA设备索引 {index} 超出了可用设备数量 {available_devices}。"
            except ValueError:
                return False, f"无效的CUDA设备索引格式: '{device}'。有效格式如: 'cuda:0'"
    except ImportError:
        return False, "PyTorch导入失败，无法验证CUDA设备。请检查PyTorch安装。"
    except Exception as e:
        return False, f"验证CUDA设备时出错: {str(e)}"
    
    return True, ""


def validate_args(args: argparse.Namespace) -> Tuple[bool, str]:
    """
    验证命令行参数的有效性，特别是device参数。
    
    Args:
        args: 解析后的命令行参数
        
    Returns:
        Tuple[bool, str]: (验证是否通过, 错误或警告信息)
    """
    # 验证device参数
    if hasattr(args, 'device'):
        return validate_device(args.device)
    
    return True, ""


def validate_learning_rate(lr: Union[float, str]) -> Tuple[bool, str]:
    """
    验证学习率参数的有效性。
    
    Args:
        lr: 学习率参数，应为正浮点数
        
    Returns:
        Tuple[bool, str]: (验证是否通过, 错误或警告信息)
    """
    try:
        # 将字符串转换为浮点数
        if isinstance(lr, str):
            lr = float(lr)
        
        # 验证学习率范围
        if lr <= 0:
            return False, f"学习率应为正数，当前值: {lr}"
        
        # 警告极端值
        if lr > 1.0:
            logger.warning(f"警告: 学习率 {lr} 可能过大")
        elif lr < 1e-5:
            logger.warning(f"警告: 学习率 {lr} 可能过小")
        
        return True, ""
    except ValueError:
        return False, f"无法将学习率转换为浮点数: {lr}"


def validate_batch_size(batch_size: Union[int, str]) -> Tuple[bool, str]:
    """
    验证批次大小参数的有效性。
    
    Args:
        batch_size: 批次大小参数，应为正整数
        
    Returns:
        Tuple[bool, str]: (验证是否通过, 错误或警告信息)
    """
    try:
        # 将字符串转换为整数
        if isinstance(batch_size, str):
            batch_size = int(batch_size)
        
        # 验证批次大小范围
        if batch_size <= 0:
            return False, f"批次大小应为正整数，当前值: {batch_size}"
        
        # 警告极端值
        if batch_size == 1:
            logger.warning("警告: 批次大小为1，训练可能非常慢且不稳定")
        elif batch_size > 1024:
            logger.warning(f"警告: 批次大小 {batch_size} 可能过大，可能会导致内存不足")
        
        return True, ""
    except ValueError:
        return False, f"无法将批次大小转换为整数: {batch_size}"


def validate_epochs(epochs: Union[int, str]) -> Tuple[bool, str]:
    """
    验证训练轮数参数的有效性。
    
    Args:
        epochs: 训练轮数参数，应为正整数
        
    Returns:
        Tuple[bool, str]: (验证是否通过, 错误或警告信息)
    """
    try:
        # 将字符串转换为整数
        if isinstance(epochs, str):
            epochs = int(epochs)
        
        # 验证训练轮数范围
        if epochs <= 0:
            return False, f"训练轮数应为正整数，当前值: {epochs}"
        
        # 警告极端值
        if epochs < 5:
            logger.warning(f"警告: 训练轮数 {epochs} 可能过少，模型可能无法充分学习")
        elif epochs > 1000:
            logger.warning(f"警告: 训练轮数 {epochs} 可能过多，训练时间可能很长")
        
        return True, ""
    except ValueError:
        return False, f"无法将训练轮数转换为整数: {epochs}"


def validate_seed(seed: Union[int, str, None]) -> Tuple[bool, str]:
    """
    验证随机种子参数的有效性。
    
    Args:
        seed: 随机种子参数，应为非负整数或None
        
    Returns:
        Tuple[bool, str]: (验证是否通过, 错误或警告信息)
    """
    # 如果种子为None，则直接通过验证
    if seed is None:
        return True, ""
    
    try:
        # 将字符串转换为整数
        if isinstance(seed, str):
            seed = int(seed)
        
        # 验证种子范围
        if seed < 0:
            return False, f"随机种子应为非负整数，当前值: {seed}"
        
        return True, ""
    except ValueError:
        return False, f"无法将随机种子转换为整数: {seed}"


# 验证函数字典，用于快速查找特定参数的验证函数
VALIDATION_FUNCTIONS = {
    'device': validate_device,
    'learning_rate': validate_learning_rate,
    'lr': validate_learning_rate,
    'batch_size': validate_batch_size,
    'epochs': validate_epochs,
    'seed': validate_seed
}


def validate_parameter(param_name: str, param_value: Any) -> Tuple[bool, str]:
    """
    验证单个参数的有效性。
    
    Args:
        param_name: 参数名称
        param_value: 参数值
        
    Returns:
        Tuple[bool, str]: (验证是否通过, 错误或警告信息)
    """
    # 查找对应的验证函数
    validation_func = VALIDATION_FUNCTIONS.get(param_name)
    
    # 如果没有找到验证函数，则默认通过验证
    if validation_func is None:
        return True, ""
    
    # 使用验证函数验证参数
    return validation_func(param_value)


def validate_parameters(params: Dict[str, Any]) -> Dict[str, Tuple[bool, str]]:
    """
    验证多个参数的有效性。
    
    Args:
        params: 参数字典，键为参数名称，值为参数值
        
    Returns:
        Dict[str, Tuple[bool, str]]: 验证结果字典，键为参数名称，值为(验证是否通过, 错误或警告信息)
    """
    results = {}
    
    for param_name, param_value in params.items():
        results[param_name] = validate_parameter(param_name, param_value)
    
    return results


# 如果直接运行此脚本，则执行示例代码
if __name__ == "__main__":
    # 设置日志级别
    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
    
    # 示例参数
    example_params = {
        'device': 'cuda:0',
        'learning_rate': 0.001,
        'batch_size': 32,
        'epochs': 100,
        'seed': 42
    }
    
    # 验证参数
    print("验证示例参数:")
    results = validate_parameters(example_params)
    
    # 输出验证结果
    for param_name, (is_valid, message) in results.items():
        status = "有效" if is_valid else "无效"
        print(f"  - {param_name}: {status}")
        if message:
            print(f"    消息: {message}")
    
    # 验证其他设备格式
    print("\n验证其他设备格式:")
    devices = ['cpu', 'cuda', 'cuda:0', 'cuda:1', 'cuda:0,cuda:1', 'gpu', 'invalid']
    
    for device in devices:
        is_valid, message = validate_device(device)
        status = "有效" if is_valid else "无效"
        print(f"  - {device}: {status}")
        if message:
            print(f"    消息: {message}") 