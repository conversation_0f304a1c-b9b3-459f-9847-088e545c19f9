"""
训练器接口模块

定义训练器的接口和抽象类，是框架的核心组件之一。
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Tuple, Optional, Union, Callable
import numpy as np
import os
import time
import logging

from cardgame_ai.core.base import State, Action, Experience, Batch
from cardgame_ai.core.environment import Environment
from cardgame_ai.core.agent import Agent


class Trainer(ABC):
    """
    训练器接口
    
    定义训练器的标准接口，包括训练代理、自我对弈等方法。
    所有具体训练器都应该实现这个接口。
    """
    
    @abstractmethod
    def train(self, env: Environment, agent: Agent, num_episodes: int, **kwargs) -> Dict[str, Any]:
        """
        训练代理
        
        Args:
            env (Environment): 游戏环境
            agent (Agent): 要训练的代理
            num_episodes (int): 训练的回合数
            **kwargs: 其他参数
            
        Returns:
            Dict[str, Any]: 训练结果，如训练指标等
        """
        pass
    
    @abstractmethod
    def self_play(self, env: Environment, agent: Agent, num_games: int, **kwargs) -> List[Experience]:
        """
        自我对弈，生成训练数据
        
        Args:
            env (Environment): 游戏环境
            agent (Agent): 用于自我对弈的代理
            num_games (int): 对弈的游戏数
            **kwargs: 其他参数
            
        Returns:
            List[Experience]: 生成的经验数据
        """
        pass


class BaseTrainer(Trainer):
    """
    基础训练器抽象类
    
    为训练器提供一些通用实现。
    """
    
    def __init__(self, save_path: str = 'models', save_interval: int = 100, log_interval: int = 10):
        """
        初始化基础训练器
        
        Args:
            save_path (str, optional): 模型保存路径. Defaults to 'models'.
            save_interval (int, optional): 保存间隔（回合数）. Defaults to 100.
            log_interval (int, optional): 日志间隔（回合数）. Defaults to 10.
        """
        self.save_path = save_path
        self.save_interval = save_interval
        self.log_interval = log_interval
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 创建保存目录
        os.makedirs(save_path, exist_ok=True)
    
    def train(self, env: Environment, agent: Agent, num_episodes: int, 
              eval_func: Optional[Callable[[Environment, Agent, int], Dict[str, Any]]] = None,
              eval_interval: int = 100, **kwargs) -> Dict[str, Any]:
        """
        训练代理
        
        Args:
            env (Environment): 游戏环境
            agent (Agent): 要训练的代理
            num_episodes (int): 训练的回合数
            eval_func (Optional[Callable[[Environment, Agent, int], Dict[str, Any]]], optional): 
                评估函数. Defaults to None.
            eval_interval (int, optional): 评估间隔（回合数）. Defaults to 100.
            **kwargs: 其他参数
            
        Returns:
            Dict[str, Any]: 训练结果，如训练指标等
        """
        start_time = time.time()
        metrics = {'episode_rewards': [], 'episode_lengths': [], 'losses': []}
        
        for episode in range(1, num_episodes + 1):
            # 重置环境
            state = env.reset()
            done = False
            episode_reward = 0
            episode_length = 0
            episode_loss = []
            
            # 运行一个回合
            while not done:
                # 获取动作
                legal_actions = env.get_legal_actions(state)
                observation = env.get_observation(state)
                action = agent.act(observation, legal_actions, is_training=True)
                
                # 执行动作
                next_state, reward, done, info = env.step(action)
                
                # 创建经验
                experience = Experience(state, action, reward, next_state, done, info)
                
                # 训练代理
                loss = agent.train(experience)
                if loss:
                    episode_loss.append(loss)
                
                # 更新状态
                state = next_state
                episode_reward += reward
                episode_length += 1
            
            # 记录指标
            metrics['episode_rewards'].append(episode_reward)
            metrics['episode_lengths'].append(episode_length)
            if episode_loss:
                avg_loss = {k: np.mean([l[k] for l in episode_loss if k in l]) for k in episode_loss[0]}
                metrics['losses'].append(avg_loss)
            
            # 日志
            if episode % self.log_interval == 0:
                elapsed_time = time.time() - start_time
                self.logger.info(
                    f"Episode {episode}/{num_episodes} | "
                    f"Reward: {episode_reward:.2f} | "
                    f"Length: {episode_length} | "
                    f"Time: {elapsed_time:.2f}s"
                )
                if episode_loss:
                    loss_str = " | ".join([f"{k}: {v:.4f}" for k, v in avg_loss.items()])
                    self.logger.info(f"Losses: {loss_str}")
            
            # 保存模型
            if episode % self.save_interval == 0:
                save_path = os.path.join(self.save_path, f"agent_episode_{episode}.pt")
                agent.save(save_path)
                self.logger.info(f"Model saved to {save_path}")
            
            # 评估
            if eval_func and episode % eval_interval == 0:
                eval_metrics = eval_func(env, agent, 10)  # 评估10局游戏
                eval_str = " | ".join([f"{k}: {v:.4f}" for k, v in eval_metrics.items()])
                self.logger.info(f"Evaluation: {eval_str}")
                
                # 添加评估指标
                for k, v in eval_metrics.items():
                    if f'eval_{k}' not in metrics:
                        metrics[f'eval_{k}'] = []
                    metrics[f'eval_{k}'].append(v)
        
        # 保存最终模型
        save_path = os.path.join(self.save_path, "agent_final.pt")
        agent.save(save_path)
        self.logger.info(f"Final model saved to {save_path}")
        
        # 计算总训练时间
        total_time = time.time() - start_time
        metrics['total_time'] = total_time
        
        return metrics
    
    def self_play(self, env: Environment, agent: Agent, num_games: int, **kwargs) -> List[Experience]:
        """
        自我对弈，生成训练数据
        
        Args:
            env (Environment): 游戏环境
            agent (Agent): 用于自我对弈的代理
            num_games (int): 对弈的游戏数
            **kwargs: 其他参数
            
        Returns:
            List[Experience]: 生成的经验数据
        """
        experiences = []
        
        for game in range(num_games):
            # 重置环境
            state = env.reset()
            done = False
            game_experiences = []
            
            # 运行一局游戏
            while not done:
                # 获取动作
                legal_actions = env.get_legal_actions(state)
                observation = env.get_observation(state)
                action = agent.act(observation, legal_actions, is_training=False)
                
                # 执行动作
                next_state, reward, done, info = env.step(action)
                
                # 创建经验
                experience = Experience(state, action, reward, next_state, done, info)
                game_experiences.append(experience)
                
                # 更新状态
                state = next_state
            
            # 添加到经验列表
            experiences.extend(game_experiences)
            
            # 日志
            if (game + 1) % 10 == 0:
                self.logger.info(f"Self-play: {game + 1}/{num_games} games completed")
        
        return experiences
