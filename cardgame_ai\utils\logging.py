#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
日志设置模块

提供统一的日志配置和管理功能
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional

try:
    import structlog
    STRUCTLOG_AVAILABLE = True
except ImportError:
    STRUCTLOG_AVAILABLE = False
    structlog = None


def setup_logging(
    level: str = "INFO",
    log_dir: str = "logs",
    use_structlog: bool = True
) -> None:
    """
    设置项目日志配置
    
    Args:
        level: 日志级别
        log_dir: 日志目录
        use_structlog: 是否使用结构化日志
    """
    # 创建日志目录
    log_path = Path(log_dir)
    log_path.mkdir(parents=True, exist_ok=True)
    
    # 设置日志级别
    log_level = getattr(logging, level.upper(), logging.INFO)
    
    if use_structlog and STRUCTLOG_AVAILABLE:
        _setup_structlog(log_level, log_path)
    else:
        _setup_standard_logging(log_level, log_path)


def _setup_structlog(log_level: int, log_path: Path) -> None:
    """设置结构化日志"""
    # 配置structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # 配置标准库日志
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=log_level,
    )
    
    # 添加文件处理器
    file_handler = logging.handlers.RotatingFileHandler(
        log_path / "training.log",
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    file_handler.setLevel(log_level)
    
    # 获取根日志器并添加处理器
    root_logger = logging.getLogger()
    root_logger.addHandler(file_handler)


def _setup_standard_logging(log_level: int, log_path: Path) -> None:
    """设置标准日志"""
    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(log_level)
    console_handler.setFormatter(formatter)
    
    # 文件处理器
    file_handler = logging.handlers.RotatingFileHandler(
        log_path / "training.log",
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    file_handler.setLevel(log_level)
    file_handler.setFormatter(formatter)
    
    # 配置根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)


def get_logger(name: str) -> logging.Logger:
    """
    获取日志器
    
    Args:
        name: 日志器名称
        
    Returns:
        logging.Logger: 日志器实例
    """
    if STRUCTLOG_AVAILABLE:
        return structlog.get_logger(name)
    else:
        return logging.getLogger(name)
