"""
任务采样器模块

提供任务采样功能，用于元强化学习中生成不同的对手或环境变化。
"""

import os
import random
import logging
import numpy as np
from typing import Dict, List, Tuple, Any, Optional, Union, Callable

from cardgame_ai.core.base import Experience, Batch
from cardgame_ai.core.agent import Agent
from cardgame_ai.core.environment import Environment

# 配置日志
logger = logging.getLogger(__name__)


class Task:
    """
    任务类

    表示一个特定的任务，如特定的对手或环境配置。
    """

    def __init__(self, task_id: str, config: Dict[str, Any]):
        """
        初始化任务

        Args:
            task_id: 任务ID
            config: 任务配置
        """
        self.task_id = task_id
        self.config = config
        self.support_data = []  # 支持集数据
        self.query_data = []  # 查询集数据

    def add_support_data(self, data: Any):
        """
        添加支持集数据

        Args:
            data: 支持集数据
        """
        self.support_data.append(data)

    def add_query_data(self, data: Any):
        """
        添加查询集数据

        Args:
            data: 查询集数据
        """
        self.query_data.append(data)

    def get_support_data(self) -> List[Any]:
        """
        获取支持集数据

        Returns:
            支持集数据列表
        """
        return self.support_data

    def get_query_data(self) -> List[Any]:
        """
        获取查询集数据

        Returns:
            查询集数据列表
        """
        return self.query_data

    def clear_data(self):
        """
        清除数据
        """
        self.support_data = []
        self.query_data = []

    def __str__(self) -> str:
        """
        字符串表示

        Returns:
            字符串表示
        """
        return f"Task(id={self.task_id}, config={self.config})"


class TaskSampler:
    """
    任务采样器

    用于生成不同的任务，如不同的对手或环境配置。
    """

    def __init__(self, task_configs: Optional[List[Dict[str, Any]]] = None):
        """
        初始化任务采样器

        Args:
            task_configs: 任务配置列表，可选
        """
        self.tasks = {}

        # 如果提供了任务配置，创建任务
        if task_configs:
            for i, config in enumerate(task_configs):
                task_id = config.get("task_id", f"task_{i}")
                self.tasks[task_id] = Task(task_id, config)

    def add_task(self, task_id: str, config: Dict[str, Any]) -> Task:
        """
        添加任务

        Args:
            task_id: 任务ID
            config: 任务配置

        Returns:
            创建的任务
        """
        task = Task(task_id, config)
        self.tasks[task_id] = task
        return task

    def get_task(self, task_id: str) -> Optional[Task]:
        """
        获取任务

        Args:
            task_id: 任务ID

        Returns:
            任务，如果不存在则返回None
        """
        return self.tasks.get(task_id)

    def remove_task(self, task_id: str) -> bool:
        """
        移除任务

        Args:
            task_id: 任务ID

        Returns:
            是否成功移除
        """
        if task_id in self.tasks:
            del self.tasks[task_id]
            return True
        return False

    def get_all_tasks(self) -> List[Task]:
        """
        获取所有任务

        Returns:
            所有任务列表
        """
        return list(self.tasks.values())

    def sample_tasks(self, num_tasks: int = 1) -> List[Task]:
        """
        采样任务

        Args:
            num_tasks: 任务数量

        Returns:
            采样的任务列表
        """
        all_tasks = self.get_all_tasks()
        if not all_tasks:
            logger.warning("没有可用的任务")
            return []

        # 如果任务数量不足，返回所有任务
        if len(all_tasks) <= num_tasks:
            return all_tasks

        # 随机采样任务
        return random.sample(all_tasks, num_tasks)

    def sample_data(self, task: Union[str, Task], data_type: str = 'support',
                   batch_size: Optional[int] = None) -> List[Any]:
        """
        采样数据

        Args:
            task: 任务或任务ID
            data_type: 数据类型，'support'或'query'
            batch_size: 批次大小，如果为None则返回所有数据

        Returns:
            采样的数据列表
        """
        # 获取任务
        if isinstance(task, str):
            task = self.get_task(task)
            if task is None:
                logger.warning(f"任务不存在: {task}")
                return []

        # 获取数据
        if data_type == 'support':
            data = task.get_support_data()
        elif data_type == 'query':
            data = task.get_query_data()
        else:
            logger.warning(f"未知的数据类型: {data_type}")
            return []

        # 如果没有数据，返回空列表
        if not data:
            return []

        # 如果没有指定批次大小，返回所有数据
        if batch_size is None:
            return data

        # 如果数据量不足，返回所有数据
        if len(data) <= batch_size:
            return data

        # 随机采样数据
        return random.sample(data, batch_size)

    def generate_opponent_tasks(self, num_tasks: int = 5,
                              difficulty_range: Tuple[float, float] = (0.1, 1.0),
                              style_options: List[str] = None) -> List[Task]:
        """
        生成对手任务

        Args:
            num_tasks: 任务数量
            difficulty_range: 难度范围
            style_options: 风格选项列表

        Returns:
            生成的任务列表
        """
        if style_options is None:
            style_options = ["aggressive", "defensive", "balanced", "random"]

        tasks = []
        for i in range(num_tasks):
            # 随机生成难度和风格
            difficulty = random.uniform(difficulty_range[0], difficulty_range[1])
            style = random.choice(style_options)

            # 创建任务配置
            config = {
                "difficulty": difficulty,
                "style": style,
                "randomness": random.uniform(0.0, 0.3)
            }

            # 创建任务
            task_id = f"opponent_{i}"
            task = self.add_task(task_id, config)
            tasks.append(task)

        return tasks
