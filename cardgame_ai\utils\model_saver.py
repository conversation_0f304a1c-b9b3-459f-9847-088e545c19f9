"""
模型保存工具模块

提供模型保存和加载功能，支持参数量统计和智能命名。
"""
import os
import torch
import pickle
import json
import time
from datetime import datetime
from typing import Dict, Any, Optional, Union, Tuple
from pathlib import Path


class ModelSaver:
    """
    增强的模型保存器

    提供模型保存和加载功能，支持参数量统计、智能命名和元数据管理。
    """

    @staticmethod
    def count_parameters(model: torch.nn.Module) -> Dict[str, int]:
        """
        统计模型参数量

        Args:
            model: PyTorch模型

        Returns:
            包含参数统计信息的字典
        """
        # 总参数量
        total_params = sum(p.numel() for p in model.parameters())

        # 可训练参数量
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)

        # 不可训练参数量
        non_trainable_params = total_params - trainable_params

        # 按层统计参数量
        layer_params = {}
        for name, param in model.named_parameters():
            layer_params[name] = param.numel()

        return {
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'non_trainable_parameters': non_trainable_params,
            'layer_parameters': layer_params
        }

    @staticmethod
    def format_parameter_count(param_count: int) -> str:
        """
        格式化参数量为可读字符串

        Args:
            param_count: 参数数量

        Returns:
            格式化后的字符串，如 "1.2M", "345K", "12.5B"
        """
        if param_count >= 1_000_000_000:
            return f"{param_count / 1_000_000_000:.1f}B"
        elif param_count >= 1_000_000:
            return f"{param_count / 1_000_000:.1f}M"
        elif param_count >= 1_000:
            return f"{param_count / 1_000:.1f}K"
        else:
            return str(param_count)

    @staticmethod
    def generate_model_filename(
        base_name: str = "model",
        param_count: Optional[int] = None,
        timestamp: bool = True,
        epoch: Optional[int] = None,
        performance: Optional[float] = None,
        tag: Optional[str] = None
    ) -> str:
        """
        生成智能模型文件名

        Args:
            base_name: 基础名称
            param_count: 参数量
            timestamp: 是否包含时间戳
            epoch: 训练轮次
            performance: 性能指标
            tag: 自定义标签

        Returns:
            生成的文件名（不含扩展名）
        """
        parts = [base_name]

        # 添加参数量
        if param_count is not None:
            param_str = ModelSaver.format_parameter_count(param_count)
            parts.append(f"{param_str}params")

        # 添加轮次
        if epoch is not None:
            parts.append(f"epoch{epoch}")

        # 添加性能指标
        if performance is not None:
            parts.append(f"perf{performance:.3f}")

        # 添加自定义标签
        if tag is not None:
            parts.append(tag)

        # 添加时间戳
        if timestamp:
            timestamp_str = datetime.now().strftime("%Y%m%d_%H%M%S")
            parts.append(timestamp_str)

        return "_".join(parts)

    @staticmethod
    def save_model_with_params(
        model: torch.nn.Module,
        save_dir: str = "models",
        base_name: str = "model",
        include_params_in_name: bool = True,
        save_metadata: bool = True,
        epoch: Optional[int] = None,
        performance: Optional[float] = None,
        tag: Optional[str] = None,
        additional_data: Optional[Dict[str, Any]] = None
    ) -> Tuple[str, Dict[str, Any]]:
        """
        保存模型并自动生成包含参数量的文件名

        Args:
            model: PyTorch模型
            save_dir: 保存目录
            base_name: 基础文件名
            include_params_in_name: 是否在文件名中包含参数量
            save_metadata: 是否保存元数据文件
            epoch: 训练轮次
            performance: 性能指标
            tag: 自定义标签
            additional_data: 额外要保存的数据

        Returns:
            (模型文件路径, 参数统计信息)
        """
        # 统计参数量
        param_stats = ModelSaver.count_parameters(model)
        total_params = param_stats['total_parameters']

        # 生成文件名
        if include_params_in_name:
            filename = ModelSaver.generate_model_filename(
                base_name=base_name,
                param_count=total_params,
                epoch=epoch,
                performance=performance,
                tag=tag
            )
        else:
            filename = ModelSaver.generate_model_filename(
                base_name=base_name,
                epoch=epoch,
                performance=performance,
                tag=tag
            )

        # 创建保存目录
        save_path = Path(save_dir)
        save_path.mkdir(parents=True, exist_ok=True)

        # 模型文件路径
        model_path = save_path / f"{filename}.pt"

        # 准备保存数据
        save_data = {
            'model_state_dict': model.state_dict(),
            'parameter_stats': param_stats,
            'save_timestamp': datetime.now().isoformat(),
            'filename': filename
        }

        # 添加可选数据
        if epoch is not None:
            save_data['epoch'] = epoch
        if performance is not None:
            save_data['performance'] = performance
        if tag is not None:
            save_data['tag'] = tag
        if additional_data is not None:
            save_data.update(additional_data)

        # 保存模型
        torch.save(save_data, model_path)

        # 保存元数据文件
        if save_metadata:
            metadata_path = save_path / f"{filename}_metadata.json"
            metadata = {
                'model_info': {
                    'filename': filename,
                    'model_path': str(model_path),
                    'save_timestamp': save_data['save_timestamp'],
                    'parameter_stats': param_stats
                },
                'training_info': {
                    'epoch': epoch,
                    'performance': performance,
                    'tag': tag
                },
                'model_architecture': {
                    'model_class': model.__class__.__name__,
                    'model_modules': list(dict(model.named_modules()).keys())
                }
            }

            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)

        print(f"✅ 模型已保存: {model_path}")
        print(f"📊 参数量: {ModelSaver.format_parameter_count(total_params)} ({total_params:,})")
        if save_metadata:
            print(f"📄 元数据: {metadata_path}")

        return str(model_path), param_stats

    @staticmethod
    def load_model_with_params(model_path: str) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        加载模型并返回参数统计信息

        Args:
            model_path: 模型文件路径

        Returns:
            (模型数据, 参数统计信息)
        """
        # 加载模型数据
        model_data = torch.load(model_path, map_location='cpu')

        # 提取参数统计信息
        param_stats = model_data.get('parameter_stats', {})

        return model_data, param_stats

    @staticmethod
    def get_model_info(model_path: str) -> Dict[str, Any]:
        """
        获取模型信息（不加载模型权重）

        Args:
            model_path: 模型文件路径

        Returns:
            模型信息字典
        """
        try:
            # 只加载模型信息，不加载权重
            model_data = torch.load(model_path, map_location='cpu')

            info = {
                'filename': model_data.get('filename', 'unknown'),
                'parameter_stats': model_data.get('parameter_stats', {}),
                'save_timestamp': model_data.get('save_timestamp', 'unknown'),
                'epoch': model_data.get('epoch'),
                'performance': model_data.get('performance'),
                'tag': model_data.get('tag')
            }

            # 格式化参数量显示
            total_params = info['parameter_stats'].get('total_parameters', 0)
            if total_params > 0:
                info['formatted_params'] = ModelSaver.format_parameter_count(total_params)

            return info

        except Exception as e:
            return {'error': str(e)}


# 便捷函数
def count_model_parameters(model: torch.nn.Module) -> str:
    """
    快速获取模型参数量的格式化字符串

    Args:
        model: PyTorch模型

    Returns:
        格式化的参数量字符串，如 "1.2M parameters"
    """
    param_stats = ModelSaver.count_parameters(model)
    total_params = param_stats['total_parameters']
    formatted = ModelSaver.format_parameter_count(total_params)
    return f"{formatted} parameters ({total_params:,} total)"


def save_model_with_param_suffix(
    model: torch.nn.Module,
    save_dir: str = "models",
    base_name: str = "model",
    **kwargs
) -> str:
    """
    保存模型并在文件名中包含参数量后缀的便捷函数

    Args:
        model: PyTorch模型
        save_dir: 保存目录
        base_name: 基础文件名
        **kwargs: 传递给 save_model_with_params 的其他参数

    Returns:
        保存的模型文件路径
    """
    model_path, _ = ModelSaver.save_model_with_params(
        model=model,
        save_dir=save_dir,
        base_name=base_name,
        include_params_in_name=True,
        **kwargs
    )
    return model_path
