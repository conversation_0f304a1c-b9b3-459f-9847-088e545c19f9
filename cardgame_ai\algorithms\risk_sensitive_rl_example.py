"""
风险敏感强化学习模块使用示例

展示如何在现有的强化学习算法中集成条件风险价值（CVaR）计算。
"""

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import matplotlib.pyplot as plt

from cardgame_ai.algorithms.risk_sensitive_rl import CVaRCalculator, cvar_policy_loss, cvar_value_loss


def simple_example():
    """简单的CVaR计算示例"""
    
    # 创建一个CVaR计算器，alpha=0.05表示关注分布最坏的5%
    cvar_calc = CVaRCalculator(alpha=0.05, beta=0.2)
    
    # 假设我们有一些回报样本
    returns = np.random.normal(1.0, 2.0, 1000)  # 均值为1，标准差为2的正态分布
    
    # 计算标准统计量
    mean_return = np.mean(returns)
    
    # 计算风险度量
    var = cvar_calc.compute_var(returns)
    cvar = cvar_calc.compute_cvar(returns)
    
    # 计算风险敏感损失
    risk_sensitive_return = cvar_calc.compute_risk_sensitive_loss(returns)
    
    print(f"平均回报: {mean_return:.4f}")
    print(f"风险价值(VaR): {var:.4f}")
    print(f"条件风险价值(CVaR): {cvar:.4f}")
    print(f"风险敏感回报: {risk_sensitive_return:.4f}")
    
    # 分析回报分布
    stats = cvar_calc.analyze_return_distribution(returns)
    
    # 打印统计信息
    print("\n回报分布统计信息:")
    print(f"均值: {stats['mean']:.4f}")
    print(f"中位数: {stats['median']:.4f}")
    print(f"标准差: {stats['std']:.4f}")
    print(f"最小值: {stats['min']:.4f}")
    print(f"最大值: {stats['max']:.4f}")
    print(f"偏度: {stats['skewness']:.4f}")
    print(f"峰度: {stats['kurtosis']:.4f}")
    print(f"VaR(1%): {stats['var_01']:.4f}")
    print(f"VaR(5%): {stats['var_05']:.4f}")
    print(f"VaR(10%): {stats['var_10']:.4f}")


def plot_risk_comparison():
    """绘制不同风险测度的比较图"""
    
    # 创建两个不同形状的分布
    np.random.seed(42)
    normal_returns = np.random.normal(1.0, 1.0, 1000)  # 正态分布
    
    # 创建一个有更多极端负值的偏斜分布
    skewed_returns = np.random.normal(1.0, 0.5, 1000)  # 基础是一个正态分布
    # 添加一些极端负值事件
    extreme_indices = np.random.choice(1000, 50)  # 随机选择50个点
    skewed_returns[extreme_indices] = np.random.normal(-3.0, 1.0, 50)  # 替换为严重负值
    
    # 计算各种风险度量
    calc = CVaRCalculator(alpha=0.05, beta=0.3)
    
    # 计算正态分布的指标
    normal_mean = np.mean(normal_returns)
    normal_var = calc.compute_var(normal_returns)
    normal_cvar = calc.compute_cvar(normal_returns)
    normal_risk_return = calc.compute_risk_sensitive_loss(normal_returns)
    
    # 计算偏斜分布的指标
    skewed_mean = np.mean(skewed_returns)
    skewed_var = calc.compute_var(skewed_returns)
    skewed_cvar = calc.compute_cvar(skewed_returns)
    skewed_risk_return = calc.compute_risk_sensitive_loss(skewed_returns)
    
    # 创建图表
    plt.figure(figsize=(12, 8))
    
    # 绘制分布直方图
    plt.subplot(2, 1, 1)
    plt.hist(normal_returns, bins=50, alpha=0.5, label='正态分布回报')
    plt.hist(skewed_returns, bins=50, alpha=0.5, label='偏斜分布回报（有极端负值）')
    plt.axvline(normal_mean, color='blue', linestyle='--', label=f'正态-均值: {normal_mean:.2f}')
    plt.axvline(normal_var, color='blue', linestyle='-.', label=f'正态-VaR: {normal_var:.2f}')
    plt.axvline(normal_cvar, color='blue', linestyle=':', label=f'正态-CVaR: {normal_cvar:.2f}')
    plt.axvline(skewed_mean, color='red', linestyle='--', label=f'偏斜-均值: {skewed_mean:.2f}')
    plt.axvline(skewed_var, color='red', linestyle='-.', label=f'偏斜-VaR: {skewed_var:.2f}')
    plt.axvline(skewed_cvar, color='red', linestyle=':', label=f'偏斜-CVaR: {skewed_cvar:.2f}')
    plt.legend(loc='upper left')
    plt.title('回报分布比较')
    plt.xlabel('回报')
    plt.ylabel('频数')
    
    # 绘制风险敏感度对比柱状图
    plt.subplot(2, 1, 2)
    labels = ['平均回报', 'VaR', 'CVaR', '风险敏感回报']
    normal_metrics = [normal_mean, normal_var, normal_cvar, normal_risk_return]
    skewed_metrics = [skewed_mean, skewed_var, skewed_cvar, skewed_risk_return]
    
    x = range(len(labels))
    width = 0.35
    
    plt.bar([i - width/2 for i in x], normal_metrics, width, label='正态分布')
    plt.bar([i + width/2 for i in x], skewed_metrics, width, label='偏斜分布')
    
    plt.axhline(0, color='black', linestyle='-', linewidth=0.5)
    plt.xlabel('风险度量')
    plt.ylabel('值')
    plt.title('不同分布的风险度量比较')
    plt.xticks(x, labels)
    plt.legend()
    
    plt.tight_layout()
    plt.savefig('risk_comparison.png')
    plt.show()


class SimplePolicyNetwork(nn.Module):
    """简单的策略网络示例"""
    
    def __init__(self, state_dim, action_dim):
        super().__init__()
        self.fc1 = nn.Linear(state_dim, 64)
        self.fc2 = nn.Linear(64, 64)
        self.fc3 = nn.Linear(64, action_dim)
        self.value_head = nn.Linear(64, 1)
        
    def forward(self, x):
        x = F.relu(self.fc1(x))
        x = F.relu(self.fc2(x))
        action_logits = self.fc3(x)
        value = self.value_head(x).squeeze(-1)
        return action_logits, value


def risk_sensitive_ppo_loss_example():
    """展示如何在PPO算法中使用风险敏感损失函数"""
    
    # 创建随机批次数据
    batch_size = 32
    state_dim = 8
    action_dim = 4
    
    # 随机生成状态、动作和回报
    states = torch.randn(batch_size, state_dim)
    actions = torch.randint(0, action_dim, (batch_size,))
    advantages = torch.randn(batch_size)
    old_values = torch.randn(batch_size)
    returns = old_values + advantages
    
    # 创建策略网络
    policy_net = SimplePolicyNetwork(state_dim, action_dim)
    
    # 前向传播获取动作分布和价值
    action_logits, predicted_values = policy_net(states)
    
    # 标准PPO策略损失
    log_probs = F.log_softmax(action_logits, dim=-1)
    action_log_probs = log_probs.gather(1, actions.unsqueeze(1)).squeeze(1)
    ratio = torch.exp(action_log_probs)
    standard_policy_loss = -torch.min(
        ratio * advantages,
        torch.clamp(ratio, 0.8, 1.2) * advantages
    ).mean()
    
    # 标准PPO价值损失
    standard_value_loss = F.mse_loss(predicted_values, returns)
    
    # 风险敏感策略损失
    rs_policy_loss = cvar_policy_loss(
        action_logits, actions, advantages, 
        alpha=0.1, beta=0.2
    )
    
    # 风险敏感价值损失
    rs_value_loss = cvar_value_loss(
        predicted_values, returns,
        alpha=0.1, beta=0.2
    )
    
    # 计算总损失
    alpha_policy = 0.7  # 策略损失权重
    alpha_value = 0.3  # 价值损失权重
    
    # 标准总损失
    standard_loss = alpha_policy * standard_policy_loss + alpha_value * standard_value_loss
    
    # 风险敏感总损失
    risk_sensitive_loss = alpha_policy * rs_policy_loss + alpha_value * rs_value_loss
    
    print("\nPPO算法损失函数比较:")
    print(f"标准策略损失: {standard_policy_loss.item():.4f}")
    print(f"标准价值损失: {standard_value_loss.item():.4f}")
    print(f"标准总损失: {standard_loss.item():.4f}")
    print(f"风险敏感策略损失: {rs_policy_loss.item():.4f}")
    print(f"风险敏感价值损失: {rs_value_loss.item():.4f}")
    print(f"风险敏感总损失: {risk_sensitive_loss.item():.4f}")


def adaptive_risk_aversion_demo():
    """演示自适应风险厌恶系数"""
    
    # 创建自适应风险厌恶的CVaR计算器
    calc = CVaRCalculator(
        alpha=0.05, 
        beta=0.2,
        min_beta=0.1,
        max_beta=0.8,
        adaptive_beta=True,
        beta_update_rate=0.1
    )
    
    # 模拟回报序列，波动性逐渐增加
    num_episodes = 200
    mean_returns = []
    volatilities = []
    betas = []
    
    for i in range(num_episodes):
        # 随着时间增加波动性
        volatility = 0.5 + 1.5 * (i / num_episodes)
        volatilities.append(volatility)
        
        # 生成回报
        returns = np.random.normal(0.0, volatility, 100)
        mean_return = np.mean(returns)
        mean_returns.append(mean_return)
        
        # 计算风险敏感回报和更新beta
        _ = calc.compute_risk_sensitive_loss(returns)
        calc.update_risk_aversion()
        betas.append(calc.beta)
    
    # 绘制结果
    plt.figure(figsize=(10, 8))
    
    plt.subplot(3, 1, 1)
    plt.plot(mean_returns)
    plt.title('平均回报')
    plt.ylabel('均值')
    
    plt.subplot(3, 1, 2)
    plt.plot(volatilities)
    plt.title('回报波动性')
    plt.ylabel('标准差')
    
    plt.subplot(3, 1, 3)
    plt.plot(betas)
    plt.title('自适应风险厌恶系数')
    plt.ylabel('Beta')
    plt.xlabel('回合')
    
    plt.tight_layout()
    plt.savefig('adaptive_risk_aversion.png')
    plt.show()


if __name__ == "__main__":
    # 运行简单示例
    simple_example()
    
    # 绘制风险比较图
    try:
        plot_risk_comparison()
    except ImportError:
        print("matplotlib未安装，跳过绘图示例")
    
    # PPO算法中的风险敏感损失示例
    risk_sensitive_ppo_loss_example()
    
    # 自适应风险厌恶系数示例
    try:
        adaptive_risk_aversion_demo()
    except ImportError:
        print("matplotlib未安装，跳过自适应风险厌恶示例") 