"""
高层策略网络模块

实现层次化强化学习中的高层策略网络，负责决定使用哪种牌型（如单张、对子、顺子等）。
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Any, Optional, Union

from cardgame_ai.games.doudizhu.card_group import CardGroupType


class HighLevelPolicy(nn.Module):
    """
    高层策略网络
    
    负责决定使用哪种牌型（如单张、对子、顺子等）或者选择不出牌（PASS）。
    """
    
    def __init__(
        self, 
        state_dim: int, 
        hidden_dims: List[int] = [256, 128],
        dropout: float = 0.1
    ):
        """
        初始化高层策略网络
        
        Args:
            state_dim: 状态维度
            hidden_dims: 隐藏层维度列表
            dropout: Dropout概率
        """
        super().__init__()
        
        # 定义高层动作空间（对应不同的牌型）
        self.high_level_action_dim = len(CardGroupType)
        
        # 构建网络层
        layers = []
        input_dim = state_dim
        
        for hidden_dim in hidden_dims:
            layers.append(nn.Linear(input_dim, hidden_dim))
            layers.append(nn.ReLU())
            layers.append(nn.Dropout(dropout))
            input_dim = hidden_dim
        
        # 输出层
        layers.append(nn.Linear(input_dim, self.high_level_action_dim))
        
        # 构建序列模型
        self.network = nn.Sequential(*layers)
    
    def forward(self, state: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            state: 状态张量
            
        Returns:
            高层动作的logits
        """
        return self.network(state)
    
    def act(
        self, 
        state: torch.Tensor, 
        legal_actions: Optional[List[int]] = None, 
        temperature: float = 1.0
    ) -> Tuple[int, torch.Tensor]:
        """
        根据状态选择高层动作
        
        Args:
            state: 状态张量
            legal_actions: 合法动作列表
            temperature: 温度参数，控制探索程度
            
        Returns:
            选择的高层动作和动作概率
        """
        # 确保输入形状正确
        if state.dim() == 1:
            state = state.unsqueeze(0)  # 添加批次维度
        
        # 获取logits
        logits = self.forward(state)
        
        # 如果提供了合法动作，只考虑合法动作
        if legal_actions is not None:
            mask = torch.zeros_like(logits)
            mask[0, legal_actions] = 1
            logits = logits + (mask - 1) * 1e9
        
        # 应用温度参数
        if temperature != 1.0:
            logits = logits / temperature
        
        # 计算概率分布
        probs = F.softmax(logits, dim=-1)
        
        # 采样动作
        action = torch.multinomial(probs[0], 1).item()
        
        return action, probs[0]
    
    def get_card_type_from_action(self, action: int) -> CardGroupType:
        """
        从高层动作获取对应的牌型
        
        Args:
            action: 高层动作
            
        Returns:
            对应的牌型
        """
        return list(CardGroupType)[action]
    
    def get_action_from_card_type(self, card_type: CardGroupType) -> int:
        """
        从牌型获取对应的高层动作
        
        Args:
            card_type: 牌型
            
        Returns:
            对应的高层动作
        """
        return list(CardGroupType).index(card_type)
        
    def set_goal(self, state_tensor: torch.Tensor) -> Dict[str, Any]:
        """
        根据当前状态设定目标
        
        Args:
            state_tensor: 状态张量
            
        Returns:
            包含目标信息的字典，至少包含:
            - 'card_type': CardGroupType - 建议使用的牌型
            - 'priority': str - 优先级 ('high', 'medium', 'low')
            - 'strategy': str - 策略类型 ('aggressive', 'defensive', 'balanced')
            - 'confidence': float - 对该目标的置信度 (0.0-1.0)
            - 'action_id': int - 原始高层动作ID
        """
        # 使用act方法获取高层动作和概率
        high_level_action, probs = self.act(state_tensor)
        
        # 获取对应的牌型
        card_type = self.get_card_type_from_action(high_level_action)
        
        # 获取置信度（该动作的概率值）
        confidence = float(probs[high_level_action].item() if isinstance(probs, torch.Tensor) else probs[high_level_action])
        
        # 基于置信度设置优先级和策略
        if confidence > 0.8:
            priority = 'high'
            strategy = 'aggressive'
        elif confidence > 0.5:
            priority = 'medium'
            strategy = 'balanced'
        else:
            priority = 'low'
            strategy = 'defensive'
        
        # 构建目标字典
        goal = {
            'card_type': card_type,
            'priority': priority,
            'strategy': strategy,
            'confidence': confidence,
            'action_id': high_level_action
        }
        
        return goal
