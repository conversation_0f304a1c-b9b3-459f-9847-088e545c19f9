#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
并行优先级经验回放缓冲区模块

提供高效的并行优先级经验回放缓冲区实现，支持并发访问和更新。
使用SumTree数据结构实现高效的优先级采样，并通过分片和锁机制确保并发安全。
"""

import numpy as np
import random
import threading
import time
from typing import List, Tuple, Dict, Any, Optional, Union
from collections import deque
import logging

# 添加 Ray 和 Horovod 支持
try:
    import ray
except ImportError:
    ray = None

try:
    import horovod.torch as hvd
except ImportError:
    hvd = None

from cardgame_ai.core.base import Experience, Batch

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SumTree:
    """
    SumTree数据结构

    一种二叉树数据结构，用于高效地存储和采样优先级。
    叶节点存储经验的优先级，非叶节点存储其子节点优先级的和。
    """

    def __init__(self, capacity: int):
        """
        初始化SumTree

        Args:
            capacity (int): 叶节点容量，必须是2的幂
        """
        # 确保容量是2的幂
        self.capacity = 1
        while self.capacity < capacity:
            self.capacity *= 2

        # 初始化树
        # 树的总节点数 = 2*capacity - 1
        # 非叶节点数 = capacity - 1
        # 叶节点数 = capacity
        self.tree = np.zeros(2 * self.capacity - 1, dtype=np.float32)

        # 数据存储
        self.data = np.zeros(self.capacity, dtype=object)

        # 当前写入位置
        self.write_index = 0

        # 当前存储的数据量
        self.size = 0

        # 锁，用于并发访问控制
        self.lock = threading.RLock()

    def total(self) -> float:
        """
        获取树的总和（根节点的值）

        Returns:
            float: 总和
        """
        return self.tree[0]

    def add(self, priority: float, data: Any) -> None:
        """
        添加数据和优先级

        Args:
            priority (float): 优先级
            data (Any): 数据
        """
        with self.lock:
            # 计算叶节点索引
            leaf_idx = self.write_index + self.capacity - 1

            # 存储数据
            self.data[self.write_index] = data

            # 更新树
            self.update(leaf_idx, priority)

            # 更新写入位置
            self.write_index = (self.write_index + 1) % self.capacity

            # 更新大小
            if self.size < self.capacity:
                self.size += 1

    def update(self, idx: int, priority: float) -> None:
        """
        更新节点的优先级

        Args:
            idx (int): 节点索引
            priority (float): 新的优先级
        """
        with self.lock:
            # 计算变化量
            change = priority - self.tree[idx]

            # 更新节点
            self.tree[idx] = priority

            # 更新父节点
            self._propagate(idx, change)

    def _propagate(self, idx: int, change: float) -> None:
        """
        向上传播更新

        Args:
            idx (int): 节点索引
            change (float): 变化量
        """
        # 获取父节点索引
        parent = (idx - 1) // 2

        # 更新父节点
        self.tree[parent] += change

        # 如果不是根节点，继续向上传播
        if parent != 0:
            self._propagate(parent, change)

    def get(self, s: float) -> Tuple[int, int, float, Any]:
        """
        根据优先级采样

        Args:
            s (float): 采样值，范围[0, total)

        Returns:
            Tuple[int, int, float, Any]: 树索引、数据索引、优先级和数据
        """
        with self.lock:
            # 从根节点开始检索
            idx = self._retrieve(0, s)

            # 获取数据索引
            data_idx = idx - self.capacity + 1

            return idx, data_idx, self.tree[idx], self.data[data_idx]

    def _retrieve(self, idx: int, s: float) -> int:
        """
        检索给定采样值对应的叶节点索引

        Args:
            idx (int): 当前节点索引
            s (float): 采样值

        Returns:
            int: 叶节点索引
        """
        # 如果是叶节点，返回
        left_idx = 2 * idx + 1
        if left_idx >= len(self.tree):
            return idx

        # 如果采样值小于左子树的和，进入左子树
        if s <= self.tree[left_idx]:
            return self._retrieve(left_idx, s)
        # 否则进入右子树，并减去左子树的和
        else:
            right_idx = left_idx + 1
            return self._retrieve(right_idx, s - self.tree[left_idx])

    def __len__(self) -> int:
        """
        获取存储的数据量

        Returns:
            int: 数据量
        """
        return self.size


class ShardedSumTree:
    """
    分片SumTree

    将SumTree分成多个分片，每个分片负责一部分数据。
    这样可以减少锁竞争，提高并发性能。
    """

    def __init__(self, capacity: int, num_shards: int = 4):
        """
        初始化分片SumTree

        Args:
            capacity (int): 总容量
            num_shards (int, optional): 分片数量. Defaults to 4.
        """
        self.num_shards = num_shards
        self.shard_capacity = capacity // num_shards

        # 创建分片
        self.shards = [SumTree(self.shard_capacity) for _ in range(num_shards)]

        # 当前写入分片
        self.current_shard = 0

        # 分片锁
        self.shard_lock = threading.Lock()

    def total(self) -> float:
        """
        获取所有分片的总和

        Returns:
            float: 总和
        """
        return sum(shard.total() for shard in self.shards)

    def add(self, priority: float, data: Any) -> None:
        """
        添加数据和优先级

        Args:
            priority (float): 优先级
            data (Any): 数据
        """
        with self.shard_lock:
            # 选择当前分片
            shard = self.shards[self.current_shard]

            # 如果当前分片已满，切换到下一个分片
            if len(shard) >= self.shard_capacity:
                self.current_shard = (self.current_shard + 1) % self.num_shards
                shard = self.shards[self.current_shard]

            # 添加数据
            shard.add(priority, data)

    def update(self, shard_idx: int, idx: int, priority: float) -> None:
        """
        更新节点的优先级

        Args:
            shard_idx (int): 分片索引
            idx (int): 节点索引
            priority (float): 新的优先级
        """
        self.shards[shard_idx].update(idx, priority)

    def get(self, s: float) -> Tuple[int, int, float, Any]:
        """
        根据优先级采样

        Args:
            s (float): 采样值，范围[0, total)

        Returns:
            Tuple[int, int, float, Any]: 分片索引、树索引、数据索引、优先级和数据
        """
        # 计算总和
        total = self.total()

        # 生成采样值
        if s >= total:
            s = total - 1e-5

        # 选择分片
        shard_idx = 0
        shard_total = 0

        for i, shard in enumerate(self.shards):
            shard_total += shard.total()
            if s < shard_total:
                shard_idx = i
                s = s - (shard_total - shard.total())  # 调整采样值
                break

        # 从选定分片中采样
        idx, data_idx, priority, data = self.shards[shard_idx].get(s)

        return shard_idx, idx, data_idx, priority, data

    def __len__(self) -> int:
        """
        获取存储的数据量

        Returns:
            int: 数据量
        """
        return sum(len(shard) for shard in self.shards)


class ParallelPrioritizedReplayBuffer:
    """
    并行优先级经验回放缓冲区

    使用分片SumTree实现高效的并行优先级经验回放。
    支持并发访问和更新，适用于多线程和分布式环境。
    """

    def __init__(self, capacity: int, alpha: float = 0.6, beta: float = 0.4, beta_increment: float = 0.001,
                 epsilon: float = 1e-6, num_shards: int = 4, batch_update_size: int = 32):
        """
        初始化并行优先级经验回放缓冲区

        Args:
            capacity (int): 缓冲区容量
            alpha (float, optional): 优先级指数，控制采样概率与优先级的关系. Defaults to 0.6.
            beta (float, optional): 重要性采样指数，用于修正优先级采样的偏差. Defaults to 0.4.
            beta_increment (float, optional): beta的增量，随着训练进行逐渐增加beta. Defaults to 0.001.
            epsilon (float, optional): 小常数，防止优先级为0. Defaults to 1e-6.
            num_shards (int, optional): 分片数量. Defaults to 4.
            batch_update_size (int, optional): 批量更新大小. Defaults to 32.
        """
        self.capacity = capacity
        self.alpha = alpha
        self.beta = beta
        self.beta_increment = beta_increment
        self.epsilon = epsilon
        self.num_shards = num_shards
        self.batch_update_size = batch_update_size

        # 创建分片SumTree
        self.tree = ShardedSumTree(capacity, num_shards)

        # 批量更新缓冲区
        self.update_buffer = []
        self.update_lock = threading.Lock()

        # 统计信息
        self.stats = {
            'add_count': 0,
            'sample_count': 0,
            'update_count': 0,
            'batch_update_count': 0
        }
        self.stats_lock = threading.Lock()

    def add(self, experience: Experience) -> None:
        """
        添加经验

        Args:
            experience (Experience): 经验数据
        """
        # 设置最大优先级
        max_priority = self.tree.total() / max(1, len(self.tree))
        if max_priority <= 0:
            max_priority = 1.0

        # 添加经验
        self.tree.add(max_priority ** self.alpha, experience)

        # 更新统计信息
        with self.stats_lock:
            self.stats['add_count'] += 1

    def sample(self, batch_size: int) -> Tuple[Batch, List[Tuple[int, int, int]], np.ndarray]:
        """
        采样经验批次

        Args:
            batch_size (int): 批次大小

        Returns:
            Tuple[Batch, List[Tuple[int, int, int]], np.ndarray]: 经验批次、索引和重要性权重
        """
        # 确保缓冲区中有足够的经验
        buffer_size = len(self.tree)
        if batch_size > buffer_size:
            batch_size = buffer_size

        # 如果缓冲区为空，返回空批次
        if buffer_size == 0:
            return Batch([]), [], np.array([])

        # 采样索引和经验
        indices = []
        experiences = []
        priorities = np.zeros(batch_size, dtype=np.float32)

        # 计算采样间隔
        segment = self.tree.total() / batch_size

        # 增加beta
        self.beta = min(1.0, self.beta + self.beta_increment)

        for i in range(batch_size):
            # 计算采样区间
            a = segment * i
            b = segment * (i + 1)

            # 生成随机采样值
            s = random.uniform(a, b)

            # 采样经验
            shard_idx, tree_idx, data_idx, priority, experience = self.tree.get(s)

            # 存储索引和经验
            indices.append((shard_idx, tree_idx, data_idx))
            experiences.append(experience)
            priorities[i] = priority

        # 计算重要性权重
        # w_i = (1/N * 1/P(i))^beta
        sampling_probabilities = priorities / self.tree.total()
        weights = (buffer_size * sampling_probabilities) ** (-self.beta)
        weights /= weights.max()  # 归一化

        # 更新统计信息
        with self.stats_lock:
            self.stats['sample_count'] += 1

        return Batch(experiences), indices, weights

    def update_priorities(self, indices: List[Tuple[int, int, int]], priorities: np.ndarray) -> None:
        """
        更新优先级

        Args:
            indices (List[Tuple[int, int, int]]): 索引，每个元素是(shard_idx, tree_idx, data_idx)
            priorities (np.ndarray): 优先级
        """
        # 添加到更新缓冲区
        with self.update_lock:
            for idx, priority in zip(indices, priorities):
                # 添加小常数，防止优先级为0
                priority = abs(priority) + self.epsilon

                # 应用alpha
                priority = priority ** self.alpha

                # 添加到更新缓冲区
                self.update_buffer.append((idx, priority))

            # 更新统计信息
            with self.stats_lock:
                self.stats['update_count'] += len(indices)

            # 如果更新缓冲区足够大，执行批量更新
            if len(self.update_buffer) >= self.batch_update_size:
                self._batch_update()

    def _batch_update(self) -> None:
        """
        批量更新优先级
        """
        with self.update_lock:
            # 复制更新缓冲区并清空
            updates = self.update_buffer.copy()
            self.update_buffer.clear()

        # 按分片分组
        shard_updates = {}
        for (shard_idx, tree_idx, data_idx), priority in updates:
            if shard_idx not in shard_updates:
                shard_updates[shard_idx] = []
            shard_updates[shard_idx].append((tree_idx, priority))

        # 对每个分片执行更新
        for shard_idx, shard_updates_list in shard_updates.items():
            for tree_idx, priority in shard_updates_list:
                self.tree.update(shard_idx, tree_idx, priority)

        # 更新统计信息
        with self.stats_lock:
            self.stats['batch_update_count'] += 1

    def force_update(self) -> None:
        """
        强制执行批量更新
        """
        if len(self.update_buffer) > 0:
            self._batch_update()

    def clear(self) -> None:
        """
        清空缓冲区
        """
        # 创建新的分片SumTree
        self.tree = ShardedSumTree(self.capacity, self.num_shards)

        # 清空更新缓冲区
        with self.update_lock:
            self.update_buffer.clear()

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        with self.stats_lock:
            stats = self.stats.copy()

        # 添加其他信息
        stats['size'] = len(self.tree)
        stats['capacity'] = self.capacity
        stats['alpha'] = self.alpha
        stats['beta'] = self.beta
        stats['num_shards'] = self.num_shards
        stats['pending_updates'] = len(self.update_buffer)

        return stats

    def __len__(self) -> int:
        """
        获取缓冲区大小

        Returns:
            int: 缓冲区大小
        """
        return len(self.tree)

    def __str__(self) -> str:
        """
        转换为字符串表示

        Returns:
            str: 字符串表示
        """
        return f"ParallelPrioritizedReplayBuffer(size={len(self)}, capacity={self.capacity}, "\
               f"alpha={self.alpha}, beta={self.beta}, num_shards={self.num_shards})"

# 在文件末尾添加 Ray Actor 用于分布式回放缓冲区
if ray is not None:
    @ray.remote
    class RemoteReplayBuffer:
        def __init__(self, capacity: int, alpha: float = 0.6, beta: float = 0.4, beta_increment: float = 0.001,
                     epsilon: float = 1e-6, num_shards: int = 4, batch_update_size: int = 32):
            # 初始化分布式回放缓冲区
            self.buffer = ParallelPrioritizedReplayBuffer(capacity, alpha, beta, beta_increment,
                                                         epsilon, num_shards, batch_update_size)

        def add(self, experience: Experience) -> None:
            self.buffer.add(experience)

        def sample(self, batch_size: int) -> Tuple[Batch, List[Tuple[int,int,int]], np.ndarray]:
            return self.buffer.sample(batch_size)

        def update_priorities(self, indices: List[Tuple[int,int,int]], priorities: np.ndarray) -> None:
            self.buffer.update_priorities(indices, priorities)

        def clear(self) -> None:
            self.buffer.clear()

        def force_update(self) -> None:
            self.buffer.force_update()

        def get_stats(self) -> Dict[str, Any]:
            return self.buffer.get_stats()