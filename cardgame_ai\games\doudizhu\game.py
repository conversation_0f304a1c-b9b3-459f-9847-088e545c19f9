"""
斗地主游戏实现

包含斗地主游戏的核心逻辑、规则和状态管理。
"""

import random
import numpy as np
from typing import List, Dict, Tuple, Optional, Union, Set, Any

from cardgame_ai.core.base import Game
from cardgame_ai.core.agent import Agent
from cardgame_ai.games.doudizhu.state import DouDizhuState, GamePhase
from cardgame_ai.games.doudizhu.action import DouDizhuAction, BidAction, GrabAction
from cardgame_ai.games.doudizhu.card import Card, CardSuit, CardRank
from cardgame_ai.games.doudizhu.deck import Deck
from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType, get_card_group_type
from cardgame_ai.games.doudizhu.rules import check_card_play_legality


class DouDizhuGame(Game):
    """
    斗地主游戏实现类

    实现了完整的斗地主游戏逻辑，包括发牌、叫地主、抢地主和出牌阶段。
    """

    def __init__(self, seed: Optional[int] = None):
        """
        初始化斗地主游戏

        Args:
            seed: 随机种子，用于确保游戏可重现
        """
        self.seed = seed
        if seed is not None:
            random.seed(seed)
            np.random.seed(seed)

        # 初始化游戏状态
        self.state = DouDizhuState()
        self.deck = Deck()
        self.landlord_index = -1
        self.current_player_index = 0
        self.base_score = 1
        self.bomb_count = 0
        self.history = []
        self.game_over = False
        self.winner = None

    def reset(self, seed: Optional[int] = None) -> DouDizhuState:
        """
        重置游戏状态，开始新的游戏

        Args:
            seed: 随机种子，设置为None时使用构造函数中提供的种子

        Returns:
            初始游戏状态
        """
        if seed is not None:
            self.seed = seed
            random.seed(seed)
            np.random.seed(seed)

        # 重置游戏状态
        self.state = DouDizhuState()
        self.deck = Deck()
        self.landlord_index = -1
        self.current_player_index = random.randint(0, 2)  # 随机选择起始玩家
        self.base_score = 1
        self.bomb_count = 0
        self.history = []
        self.game_over = False
        self.winner = None

        # 发牌
        self._deal_cards()

        # 设置游戏阶段为叫地主
        self.state.phase = GamePhase.BIDDING
        self.state.current_player = self.current_player_index

        return self.state.copy()

    def _deal_cards(self) -> None:
        """
        发牌阶段，为三名玩家分发手牌，并留下三张底牌
        """
        self.deck.shuffle()
        cards = self.deck.get_all_cards()

        # 每人17张牌，留3张底牌
        player_cards = [cards[i:i+17] for i in range(0, 51, 17)]
        landlord_cards = cards[51:]

        # 设置玩家手牌
        for i in range(3):
            self.state.player_hands[i] = player_cards[i]

        # 设置底牌
        self.state.landlord_cards = landlord_cards

    def step(self, action: Union[DouDizhuAction, BidAction, GrabAction]) -> Tuple[DouDizhuState, float, bool, Dict]:
        """
        执行一步游戏动作

        Args:
            action: 玩家动作，可以是叫地主动作、抢地主动作或出牌动作

        Returns:
            (下一状态, 奖励, 游戏是否结束, 额外信息)
        """
        if self.game_over:
            return self.state.copy(), 0, True, {"game_over": True, "winner": self.winner}

        # 记录当前玩家和动作，用于后续在info中提供对手信息
        current_player_id = self.current_player_index

        # 记录动作到历史
        self.history.append((self.current_player_index, action))

        # 根据游戏阶段处理不同类型的动作
        if self.state.phase == GamePhase.BIDDING:
            self._handle_bidding(action)
        elif self.state.phase == GamePhase.GRABBING:
            self._handle_grabbing(action)
        elif self.state.phase == GamePhase.PLAYING:
            self._handle_playing(action)

        # 检查游戏是否结束
        rewards = [0, 0, 0]

        # 构建基本的info字典
        info = {
            "phase": self.state.phase.name,
            "opponent_id": current_player_id,  # 当前玩家的ID，从下一个玩家角度看是"对手"
            "opponent_action": action,  # 当前玩家的动作，从下一个玩家角度看是"对手动作"
            "num_cards_left": [len(hand) for hand in self.state.player_hands],  # 各玩家剩余手牌数
            "last_player": self.state.last_player,  # 最后出牌的玩家
        }

        # 添加游戏阶段特定信息
        if self.state.phase == GamePhase.PLAYING:
            # 添加出牌阶段特定的上下文，用于上下文相关的对手建模
            if self.landlord_index != -1:
                # 判断当前玩家是地主还是农民
                is_landlord = current_player_id == self.landlord_index
                player_role = "landlord" if is_landlord else "farmer"
                info["context_key"] = player_role

                # 根据手牌数量添加上下文
                cards_left = len(self.state.player_hands[current_player_id])
                if cards_left <= 5:
                    info["context_key"] += "_few_cards"
                elif cards_left >= 12:
                    info["context_key"] += "_many_cards"

        if self.game_over:
            # 计算奖励
            if self.winner is not None:
                points = self.base_score * (2 ** self.bomb_count)
                if self.winner == self.landlord_index:
                    # 地主获胜，获得2倍分数，农民各损失1倍分数
                    rewards[self.landlord_index] = 2 * points
                    for i in range(3):
                        if i != self.landlord_index:
                            rewards[i] = -points
                else:
                    # 农民获胜，地主损失2倍分数，胜利农民获得1倍分数
                    rewards[self.landlord_index] = -2 * points
                    rewards[self.winner] = points

            info["game_over"] = True
            info["winner"] = self.winner

        # 获取下一个玩家
        self.current_player_index = (self.current_player_index + 1) % 3
        self.state.current_player = self.current_player_index

        # 返回结果
        return self.state.copy(), rewards[self.current_player_index], self.game_over, info

    def _handle_bidding(self, action: BidAction) -> None:
        """
        处理叫地主阶段的动作

        Args:
            action: 叫地主动作
        """
        if action.is_bid:
            # 玩家叫地主
            self.landlord_index = self.current_player_index
            self.state.landlord = self.current_player_index

            # 进入抢地主阶段
            self.state.phase = GamePhase.GRABBING
        else:
            # 玩家不叫地主，检查是否所有玩家都不叫
            bid_count = sum(1 for action, _ in self.history if isinstance(action, BidAction) and action.is_bid)
            if bid_count == 0 and len(self.history) >= 3:
                # 所有玩家都不叫，重新发牌
                self._deal_cards()

    def _handle_grabbing(self, action: GrabAction) -> None:
        """
        处理抢地主阶段的动作

        Args:
            action: 抢地主动作
        """
        if action.is_grab:
            # 玩家抢地主
            self.landlord_index = self.current_player_index
            self.state.landlord = self.current_player_index
            self.base_score *= 2  # 抢地主翻倍底分

        # 检查是否所有玩家都有机会抢地主
        if len(self.history) >= 5:  # 至少一轮叫地主+抢地主
            # 进入出牌阶段
            self.state.phase = GamePhase.PLAYING

            # 地主获得底牌
            self.state.player_hands[self.landlord_index].extend(self.state.landlord_cards)

            # 设置当前玩家为地主
            self.current_player_index = self.landlord_index
            self.state.current_player = self.current_player_index

    def _handle_playing(self, action: DouDizhuAction) -> None:
        """
        处理出牌阶段的动作

        Args:
            action: 出牌动作
        """
        player_index = self.current_player_index

        if not action.is_pass:
            # 玩家出牌
            cards = action.cards

            # 从玩家手牌中移除出的牌
            for card in cards:
                if card in self.state.player_hands[player_index]:
                    self.state.player_hands[player_index].remove(card)

            # 更新最后出牌
            self.state.last_action = action
            self.state.last_player = player_index

            # 检查是否是炸弹或火箭，增加炸弹计数
            card_group = CardGroup(cards)
            card_group_type = get_card_group_type(card_group)
            if card_group_type in [CardGroupType.BOMB, CardGroupType.ROCKET]:
                self.bomb_count += 1

            # 检查玩家是否已出完所有牌
            if len(self.state.player_hands[player_index]) == 0:
                self.game_over = True
                self.winner = player_index

        # 更新当前玩家的最后动作
        self.state.player_last_actions[player_index] = action

    def get_legal_actions(self, player_index: Optional[int] = None) -> List[Union[DouDizhuAction, BidAction, GrabAction]]:
        """
        获取当前玩家的合法动作列表

        Args:
            player_index: 玩家索引，默认为当前玩家

        Returns:
            合法动作列表
        """
        if player_index is None:
            player_index = self.current_player_index

        legal_actions = []

        if self.state.phase == GamePhase.BIDDING:
            # 叫地主阶段：可以叫或不叫
            legal_actions = [BidAction(True), BidAction(False)]
        elif self.state.phase == GamePhase.GRABBING:
            # 抢地主阶段：可以抢或不抢
            legal_actions = [GrabAction(True), GrabAction(False)]
        elif self.state.phase == GamePhase.PLAYING:
            # 出牌阶段
            hand_cards = self.state.player_hands[player_index]

            # 地主首次出牌（游戏刚开始）时，不能选择"不出"
            if self.state.phase == GamePhase.PLAYING and self.state.last_player is None and player_index == self.landlord_index:
                # 生成所有可能的牌组合，但排除"不出"选项
                legal_actions = self._generate_all_card_combinations(hand_cards)
                legal_actions = [action for action in legal_actions if not action.is_pass]
            # 如果是当前回合第一个出牌的玩家或上一个出牌的玩家是自己，可以任意出牌
            elif self.state.last_player == player_index or self.state.last_player == -1:
                # 生成所有可能的牌组合
                legal_actions = self._generate_all_card_combinations(hand_cards)

                # 如果上一个出牌的是当前玩家（即自己出牌后所有人都不要），不能选择"不出"
                if self.state.last_player == player_index:
                    # 移除"不出"选项（如果存在）
                    legal_actions = [action for action in legal_actions if not action.is_pass]
            else:
                # 必须大过上一个玩家的牌，或者选择不出
                legal_actions = [DouDizhuAction([])]  # 不出
                last_action = self.state.last_action

                if last_action and not last_action.is_pass:
                    # 生成能够大过上一个动作的牌组合
                    legal_actions.extend(self._generate_greater_card_combinations(hand_cards, last_action.cards))

        return legal_actions

    def _generate_all_card_combinations(self, cards: List[Card]) -> List[DouDizhuAction]:
        """
        生成所有可能的牌组合

        Args:
            cards: 手牌列表

        Returns:
            所有合法的出牌动作
        """
        # 这里简化实现，实际需要生成所有合法的牌型组合
        # 这是一个复杂的算法，取决于斗地主规则的具体实现
        # 返回一些基本的组合示例

        actions = []

        # 单牌
        for card in cards:
            actions.append(DouDizhuAction([card]))

        # TODO: 添加更多牌型组合：对子、三张、顺子、连对等
        # 这需要更复杂的逻辑来生成所有合法组合

        return actions

    def _generate_greater_card_combinations(self, cards: List[Card], last_cards: List[Card]) -> List[DouDizhuAction]:
        """
        生成能够大过上一个动作的牌组合

        Args:
            cards: 手牌列表
            last_cards: 上一个动作的牌列表

        Returns:
            所有能大过上一个动作的合法出牌动作
        """
        # 同样，这是一个复杂的算法，取决于斗地主规则的具体实现
        # 这里提供一个简化版本

        actions = []
        last_group = CardGroup(last_cards)
        last_type = get_card_group_type(last_group)

        # TODO: 根据last_type和牌值，找出所有能大过的组合
        # 这需要更复杂的逻辑来实现

        return actions

    def get_observation(self, player_index: int) -> Dict[str, Any]:
        """
        获取指定玩家的游戏观察

        Args:
            player_index: 玩家索引

        Returns:
            游戏观察字典
        """
        obs = {
            "hand_cards": self.state.player_hands[player_index],
            "num_cards_left": [len(hand) for hand in self.state.player_hands],
            "current_player": self.state.current_player,
            "last_action": self.state.last_action,
            "last_player": self.state.last_player,
            "landlord": self.state.landlord,
            "phase": self.state.phase,
            "landlord_cards": self.state.landlord_cards if self.state.phase == GamePhase.PLAYING else [],
            "history": self.history
        }

        return obs