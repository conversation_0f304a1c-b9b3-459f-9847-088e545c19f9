"""
数据加载模块

提供加载和处理各类游戏数据的功能，包括人类对局数据和完整对局轨迹数据。
"""

import os
import json
import pickle
import logging
import torch
import numpy as np
from typing import Dict, List, Tuple, Any, Optional, Union
from torch.utils.data import Dataset, DataLoader

from cardgame_ai.core.base import Experience, Batch

# 配置日志
logger = logging.getLogger(__name__)


class HumanGameDataset(Dataset):
    """
    人类对局数据集

    加载和处理人类对局数据，用于训练人类策略网络。
    """

    def __init__(self, data_dir: str, transform=None):
        """
        初始化数据集

        Args:
            data_dir: 数据目录路径
            transform: 数据转换函数
        """
        self.data_dir = data_dir
        self.transform = transform
        self.samples = []

        # 加载数据
        self._load_data()

    def _load_data(self):
        """加载交互数据文件"""
        interactions_dir = os.path.join(self.data_dir, "interactions")
        if not os.path.exists(interactions_dir):
            logger.warning(f"交互数据目录不存在: {interactions_dir}")
            return

        # 遍历所有交互数据文件
        for filename in os.listdir(interactions_dir):
            if not filename.endswith(".jsonl"):
                continue

            filepath = os.path.join(interactions_dir, filename)
            self._process_interaction_file(filepath)

        logger.info(f"加载了 {len(self.samples)} 个人类对局样本")

    def _process_interaction_file(self, filepath: str):
        """处理单个交互数据文件"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        # 解析JSON行
                        interaction = json.loads(line.strip())

                        # 提取游戏状态和人类动作
                        game_state = interaction.get('game_state')
                        human_action = interaction.get('human_action')

                        if game_state and human_action:
                            self.samples.append({
                                'state': game_state,
                                'action': human_action
                            })
                    except json.JSONDecodeError:
                        logger.warning(f"无法解析JSON行: {line[:50]}...")
        except Exception as e:
            logger.error(f"处理文件 {filepath} 时出错: {e}")

    def __len__(self):
        """返回数据集大小"""
        return len(self.samples)

    def __getitem__(self, idx):
        """获取指定索引的样本"""
        sample = self.samples[idx]

        # 转换状态为特征向量
        state_vector = self._state_to_vector(sample['state'])

        # 转换动作为索引
        action_index = self._action_to_index(sample['action'])

        # 应用转换（如果有）
        if self.transform:
            state_vector, action_index = self.transform(state_vector, action_index)

        return state_vector, action_index

    def _state_to_vector(self, state_dict: Dict) -> np.ndarray:
        """
        将状态字典转换为特征向量

        注意：这里的实现依赖于具体的游戏状态结构，
        可能需要根据实际情况调整。
        """
        # 这里简化处理，假设状态已经是向量形式
        # 实际应用中需要根据游戏状态结构进行特征提取
        if isinstance(state_dict, list):
            return np.array(state_dict, dtype=np.float32)
        elif isinstance(state_dict, dict) and 'features' in state_dict:
            return np.array(state_dict['features'], dtype=np.float32)
        else:
            # 简单处理：将字典扁平化为一维数组
            # 实际应用中应该有更复杂的特征提取逻辑
            return np.array([1.0], dtype=np.float32)  # 占位符

    def _action_to_index(self, action_dict: Dict) -> int:
        """
        将动作字典转换为动作索引

        注意：这里的实现依赖于具体的动作结构，
        可能需要根据实际情况调整。
        """
        # 这里简化处理，假设动作已经有索引
        if isinstance(action_dict, int):
            return action_dict
        elif isinstance(action_dict, dict) and 'action_index' in action_dict:
            return action_dict['action_index']
        elif isinstance(action_dict, dict) and 'action_value' in action_dict:
            return int(action_dict['action_value'])
        else:
            # 简单处理：返回默认动作索引
            # 实际应用中应该有更复杂的动作映射逻辑
            return 0  # 默认动作索引（通常是"不出"）


def load_human_log_data(data_dir: str, batch_size: int = 32,
                       shuffle: bool = True, num_workers: int = 4,
                       train_ratio: float = 0.8) -> Tuple[DataLoader, DataLoader]:
    """
    加载人类对局日志数据

    Args:
        data_dir: 数据目录路径
        batch_size: 批次大小
        shuffle: 是否打乱数据
        num_workers: 数据加载线程数
        train_ratio: 训练集比例

    Returns:
        训练数据加载器和验证数据加载器
    """
    # 创建数据集
    dataset = HumanGameDataset(data_dir)

    # 检查数据集大小
    if len(dataset) == 0:
        logger.warning(f"数据集为空: {data_dir}")
        # 返回空数据加载器
        empty_loader = DataLoader(dataset, batch_size=1)
        return empty_loader, empty_loader

    # 划分训练集和验证集
    train_size = int(len(dataset) * train_ratio)
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(
        dataset, [train_size, val_size]
    )

    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        num_workers=num_workers
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers
    )

    return train_loader, val_loader


def _convert_batch_to_experiences(batch: Dict[str, Any]) -> List[Experience]:
    """
    将批次数据转换为Experience对象列表

    Args:
        batch: 批次数据

    Returns:
        List[Experience]: Experience对象列表
    """
    experiences = []

    observations = batch.get("observations", [])
    actions = batch.get("actions", [])
    rewards = batch.get("rewards", [])
    next_observations = batch.get("next_observations", [])
    dones = batch.get("dones", [])
    infos = batch.get("infos", [])

    # 确保所有列表长度一致
    min_length = min(
        len(observations),
        len(actions),
        len(rewards),
        len(next_observations),
        len(dones),
        len(infos)
    )

    for i in range(min_length):
        exp = Experience(
            state=observations[i],
            action=actions[i],
            reward=rewards[i],
            next_state=next_observations[i],
            done=dones[i],
            info=infos[i]
        )
        experiences.append(exp)

    return experiences


def _convert_batch_to_tensor(batch: Dict[str, Any]) -> Dict[str, Any]:
    """
    将批次数据转换为PyTorch张量

    Args:
        batch: 批次数据

    Returns:
        Dict[str, Any]: 转换后的批次数据
    """
    tensor_batch = {}

    for key, value in batch.items():
        if key == "infos" or key == "human_feedback":
            # 保持infos和human_feedback为原始格式
            tensor_batch[key] = value
        elif isinstance(value, list):
            # 尝试转换为张量
            try:
                tensor_batch[key] = torch.tensor(value)
            except:
                # 如果无法转换，保持原始格式
                tensor_batch[key] = value
        else:
            # 其他情况，保持原始格式
            tensor_batch[key] = value

    return tensor_batch


def load_trajectory_data(filepath: str) -> Optional[Dict[str, Any]]:
    """
    加载轨迹数据

    Args:
        filepath: 轨迹文件路径

    Returns:
        Optional[Dict[str, Any]]: 轨迹数据，如果加载失败则返回None
    """
    try:
        # 根据文件扩展名选择加载方法
        if filepath.endswith('.json'):
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
        elif filepath.endswith('.pkl'):
            with open(filepath, 'rb') as f:
                data = pickle.load(f)
        else:
            logger.warning(f"不支持的文件格式: {filepath}")
            return None

        # 检查数据格式
        if not isinstance(data, dict):
            logger.warning(f"数据格式错误，应为字典: {filepath}")
            return None

        # 检查必要的字段
        required_fields = ["observations", "actions", "rewards"]
        for field in required_fields:
            if field not in data:
                logger.warning(f"数据缺少必要字段 '{field}': {filepath}")
                return None

        return data

    except Exception as e:
        logger.error(f"加载轨迹数据时出错: {e}")
        return None


def load_trajectory_to_batch(filepath: str) -> Optional[Batch]:
    """
    加载轨迹数据并转换为Batch对象

    Args:
        filepath: 轨迹文件路径

    Returns:
        Optional[Batch]: Batch对象，如果加载失败则返回None
    """
    # 加载轨迹数据
    data = load_trajectory_data(filepath)
    if data is None:
        return None

    # 转换为Experience对象列表
    experiences = _convert_batch_to_experiences(data)
    if not experiences:
        logger.warning(f"无法转换为Experience对象: {filepath}")
        return None

    # 创建Batch对象
    return Batch(experiences)


def load_trajectory_to_experiences(filepath: str) -> Optional[List[Experience]]:
    """
    加载轨迹数据并转换为Experience对象列表

    Args:
        filepath: 轨迹文件路径

    Returns:
        Optional[List[Experience]]: Experience对象列表，如果加载失败则返回None
    """
    # 加载轨迹数据
    data = load_trajectory_data(filepath)
    if data is None:
        return None

    # 转换为Experience对象列表
    experiences = _convert_batch_to_experiences(data)
    if not experiences:
        logger.warning(f"无法转换为Experience对象: {filepath}")
        return None

    return experiences

