"""
风险敏感强化学习模块

实现条件风险价值（Conditional Value at Risk，CVaR）算法，
用于评估极端损失风险，提高策略的稳定性和鲁棒性。
"""

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import List, Tuple, Dict, Optional, Union, Any


class CVaRCalculator:
    """
    条件风险价值（CVaR）计算器

    用于计算风险敏感强化学习中的条件风险价值（CVaR），
    这是一种常用的风险度量，对极端情况下的风险更为敏感。

    CVaR_α(X) = E[X | X ≤ VaR_α(X)]，其中VaR_α(X)是风险价值，
    表示在给定置信水平α下的分位数。
    """

    def __init__(
        self,
        alpha: float = 0.05,
        beta: float = 0.1,
        min_beta: float = 0.0,
        max_beta: float = 1.0,
        adaptive_beta: bool = False,
        beta_update_rate: float = 0.001
    ):
        """
        初始化CVaR计算器

        Args:
            alpha: 置信水平，表示关注分布最低alpha比例的样本，取值范围(0,1)
            beta: 风险厌恶系数，控制风险敏感度，取值范围[0,1]
                  beta=0表示风险中性，beta=1表示完全风险厌恶
            min_beta: 风险厌恶系数的最小值
            max_beta: 风险厌恶系数的最大值
            adaptive_beta: 是否自适应调整风险厌恶系数
            beta_update_rate: 风险厌恶系数的更新率
        """
        assert 0 < alpha < 1, "置信水平alpha必须在(0,1)范围内"
        assert 0 <= beta <= 1, "风险厌恶系数beta必须在[0,1]范围内"
        assert min_beta <= beta <= max_beta, "beta必须在[min_beta, max_beta]范围内"

        self.alpha = alpha
        self.beta = beta
        self.min_beta = min_beta
        self.max_beta = max_beta
        self.adaptive_beta = adaptive_beta
        self.beta_update_rate = beta_update_rate

        # 记录历史回报统计信息
        self.return_history = []
        self.var_history = []
        self.cvar_history = []

    def compute_var(
        self,
        returns: Union[np.ndarray, torch.Tensor],
        dim: int = None
    ) -> Union[float, np.ndarray, torch.Tensor]:
        """
        计算风险价值（Value at Risk, VaR）

        VaR_α(X)是在给定置信水平α下的分位数，
        表示损失不会超过VaR的概率为1-α。

        Args:
            returns: 回报样本，可以是numpy数组或PyTorch张量
            dim: 在多维张量中计算VaR的维度，默认为None（全局计算）

        Returns:
            计算得到的VaR值
        """
        if isinstance(returns, torch.Tensor):
            if dim is not None:
                # 在指定维度上计算分位数
                sorted_returns, _ = torch.sort(returns, dim=dim)
                var_idx = max(int(self.alpha * returns.shape[dim]), 0)
                return sorted_returns.index_select(dim, torch.tensor([var_idx], device=returns.device)).squeeze(dim)
            else:
                # 全局计算：进行排序并取 alpha 分位的元素（不做插值），与手动索引保持一致
                sorted_returns = torch.sort(returns.flatten())[0]
                var_idx = max(int(self.alpha * len(sorted_returns)), 0)
                return sorted_returns[var_idx].item()
        else:
            # numpy数组计算
            returns_np = returns if isinstance(returns, np.ndarray) else np.array(returns)
            if dim is not None:
                # 沿着指定轴计算分位数
                return np.quantile(returns_np, self.alpha, axis=dim)
            else:
                # 全局计算：进行排序并取 alpha 分位的元素（不做插值），与手动索引保持一致
                sorted_returns = np.sort(returns_np)
                var_idx = int(self.alpha * len(sorted_returns))
                # 防止下标越界
                var_idx = max(min(var_idx, len(sorted_returns) - 1), 0)
                return sorted_returns[var_idx]

    def compute_cvar(
        self,
        returns: Union[np.ndarray, torch.Tensor],
        dim: int = None
    ) -> Union[float, np.ndarray, torch.Tensor]:
        """
        计算条件风险价值（Conditional Value at Risk, CVaR）

        CVaR_α(X) = E[X | X ≤ VaR_α(X)]，是VaR以下损失的期望值，
        提供了对尾部风险更敏感的度量。

        Args:
            returns: 回报样本，可以是numpy数组或PyTorch张量
            dim: 在多维张量中计算CVaR的维度，默认为None（全局计算）

        Returns:
            计算得到的CVaR值
        """
        if isinstance(returns, torch.Tensor):
            # 计算VaR
            var = self.compute_var(returns, dim)

            if dim is not None:
                # 创建掩码，标记小于等于VaR的元素
                mask = returns <= var.unsqueeze(dim)
                # 计算CVaR（在掩码区域内的平均值）
                masked_returns = returns * mask.float()
                # 计算有效元素数量
                count = mask.sum(dim=dim).float()
                # 避免除以零
                count = torch.maximum(count, torch.tensor(1.0, device=count.device))
                # 计算平均值
                cvar = torch.sum(masked_returns, dim=dim) / count
                return cvar
            else:
                # 全局计算
                mask = returns <= var
                masked_returns = returns[mask]
                # 如果没有元素小于等于VaR，返回VaR本身
                if len(masked_returns) == 0:
                    return var
                return masked_returns.mean().item()
        else:
            # numpy数组计算
            returns_np = returns if isinstance(returns, np.ndarray) else np.array(returns)
            var = self.compute_var(returns_np, dim)

            if dim is not None:
                # 创建广播兼容的掩码
                mask = np.expand_dims(returns_np <= var, axis=dim)
                # 计算CVaR
                masked_returns = returns_np * mask
                # 计算有效元素数量
                count = np.sum(mask, axis=dim)
                # 避免除以零
                count = np.maximum(count, 1)
                # 计算平均值
                cvar = np.sum(masked_returns, axis=dim) / count
                return cvar
            else:
                # 全局计算
                mask = returns_np <= var
                masked_returns = returns_np[mask]
                # 如果没有元素小于等于VaR，返回VaR本身
                if len(masked_returns) == 0:
                    return var
                return np.mean(masked_returns)

    def compute_risk_sensitive_value(
        self,
        values: Union[np.ndarray, torch.Tensor],
        alpha: Optional[float] = None,
        beta: Optional[float] = None,
        dim: int = None
    ) -> Union[float, np.ndarray, torch.Tensor]:
        """
        计算风险敏感价值

        V_risk = (1-β) * E[V] + β * CVaR_α(V)

        其中E[V]是期望价值，CVaR_α(V)是价值的条件风险价值，
        β是风险厌恶系数。

        Args:
            values: 价值样本或分布
            alpha: CVaR的置信水平，如果为None则使用self.alpha
            beta: 风险厌恶系数，如果为None则使用self.beta
            dim: 在多维张量中计算价值的维度，默认为None（全局计算）

        Returns:
            风险敏感价值
        """
        # 使用默认参数
        alpha = alpha if alpha is not None else self.alpha
        beta = beta if beta is not None else self.beta

        # 计算期望价值
        if isinstance(values, torch.Tensor):
            expected_value = torch.mean(values, dim=dim) if dim is not None else torch.mean(values)
        else:
            values_np = values if isinstance(values, np.ndarray) else np.array(values)
            expected_value = np.mean(values_np, axis=dim) if dim is not None else np.mean(values_np)

        # 计算CVaR
        cvar = self.compute_cvar(values, dim=dim)

        # 计算风险敏感价值
        risk_sensitive_value = (1 - beta) * expected_value + beta * cvar

        return risk_sensitive_value

    def compute_risk_sensitive_loss(
        self,
        returns: Union[np.ndarray, torch.Tensor],
        expected_returns: Optional[Union[np.ndarray, torch.Tensor]] = None,
        dim: int = None
    ) -> Union[float, np.ndarray, torch.Tensor]:
        """
        计算风险敏感损失

        L = (1-β) * E[R] - β * CVaR_α(-R)

        其中E[R]是期望回报，CVaR_α(-R)是回报的负值的条件风险价值，
        β是风险厌恶系数。

        Args:
            returns: 回报样本
            expected_returns: 期望回报，如果为None，则使用returns的平均值
            dim: 在多维张量中计算损失的维度，默认为None（全局计算）

        Returns:
            风险敏感损失
        """
        # 计算期望回报
        if expected_returns is None:
            if isinstance(returns, torch.Tensor):
                expected_returns = torch.mean(returns, dim=dim) if dim is not None else torch.mean(returns)
            else:
                returns_np = returns if isinstance(returns, np.ndarray) else np.array(returns)
                expected_returns = np.mean(returns_np, axis=dim) if dim is not None else np.mean(returns_np)

        # 计算负回报的CVaR
        if isinstance(returns, torch.Tensor):
            negative_returns = -returns
            cvar_negative = self.compute_cvar(negative_returns, dim)

            # 计算风险敏感损失
            risk_sensitive_loss = (1 - self.beta) * expected_returns - self.beta * cvar_negative

            # 记录历史数据（仅在全局计算模式下）
            if dim is None:
                self.return_history.append(expected_returns.item())
                var_value = self.compute_var(negative_returns)
                self.var_history.append(var_value)
                self.cvar_history.append(cvar_negative)

            return risk_sensitive_loss
        else:
            returns_np = returns if isinstance(returns, np.ndarray) else np.array(returns)
            negative_returns = -returns_np
            cvar_negative = self.compute_cvar(negative_returns, dim)

            # 计算风险敏感损失
            risk_sensitive_loss = (1 - self.beta) * expected_returns - self.beta * cvar_negative

            # 记录历史数据（仅在全局计算模式下）
            if dim is None:
                self.return_history.append(float(expected_returns))
                var_value = self.compute_var(negative_returns)
                self.var_history.append(float(var_value))
                self.cvar_history.append(float(cvar_negative))

            return risk_sensitive_loss

    def update_risk_aversion(
        self,
        returns_volatility: Optional[float] = None,
        recent_returns: Optional[List[float]] = None,
        num_recent_returns: int = 100
    ) -> float:
        """
        根据回报的波动性更新风险厌恶系数

        Args:
            returns_volatility: 回报的波动性（标准差），如果为None则从历史数据计算
            recent_returns: 最近的回报列表，如果为None则使用内部记录的历史数据
            num_recent_returns: 计算波动性时考虑的最近回报数量

        Returns:
            更新后的风险厌恶系数
        """
        if not self.adaptive_beta:
            return self.beta
        # 如果未提供任何参数，则不使用历史数据更新，直接返回当前 beta
        if returns_volatility is None and recent_returns is None:
            return self.beta

        # 如果未提供波动性，则从历史数据计算
        if returns_volatility is None:
            if recent_returns is not None:
                # 使用提供的最近回报
                returns_to_use = recent_returns
            elif len(self.return_history) > 0:
                # 使用内部记录的历史回报
                returns_to_use = self.return_history[-min(num_recent_returns, len(self.return_history)):]
            else:
                # 没有足够的历史数据
                return self.beta

            # 计算标准差作为波动性度量
            returns_volatility = np.std(returns_to_use)

        # 根据波动性调整风险厌恶系数
        # 波动性越大，风险厌恶系数越高
        target_beta = min(max(self.min_beta, returns_volatility), self.max_beta)

        # 平滑更新
        self.beta = (1 - self.beta_update_rate) * self.beta + self.beta_update_rate * target_beta

        return self.beta

    def analyze_return_distribution(
        self,
        returns: Union[List[float], np.ndarray, torch.Tensor],
        num_bins: int = 20
    ) -> Dict[str, Any]:
        """
        分析回报分布，提供风险统计信息

        Args:
            returns: 回报样本
            num_bins: 直方图的分箱数

        Returns:
            包含风险统计信息的字典
        """
        # 转换为numpy数组
        if isinstance(returns, torch.Tensor):
            returns_np = returns.detach().cpu().numpy()
        elif isinstance(returns, list):
            returns_np = np.array(returns)
        else:
            returns_np = returns

        # 基本统计信息
        mean = np.mean(returns_np)
        median = np.median(returns_np)
        std = np.std(returns_np)
        min_return = np.min(returns_np)
        max_return = np.max(returns_np)

        # 计算VaR和CVaR
        var = self.compute_var(returns_np)
        cvar = self.compute_cvar(returns_np)

        # 计算不同置信水平的VaR
        var_01 = np.quantile(returns_np, 0.01)
        var_05 = np.quantile(returns_np, 0.05)
        var_10 = np.quantile(returns_np, 0.10)

        # 计算直方图
        hist, bin_edges = np.histogram(returns_np, bins=num_bins)
        hist = hist.tolist()
        bin_edges = bin_edges.tolist()

        # 计算偏度和峰度
        skewness = np.mean(((returns_np - mean) / std) ** 3) if std > 0 else 0
        kurtosis = np.mean(((returns_np - mean) / std) ** 4) if std > 0 else 0

        # 汇总统计信息
        stats = {
            "mean": float(mean),
            "median": float(median),
            "std": float(std),
            "min": float(min_return),
            "max": float(max_return),
            "var_alpha": float(var),
            "cvar_alpha": float(cvar),
            "var_01": float(var_01),
            "var_05": float(var_05),
            "var_10": float(var_10),
            "skewness": float(skewness),
            "kurtosis": float(kurtosis),
            "histogram": {
                "counts": hist,
                "bin_edges": bin_edges
            }
        }

        return stats


def cvar_policy_loss(
    policy_logits: torch.Tensor,
    actions: torch.Tensor,
    advantages: torch.Tensor,
    alpha: float = 0.05,
    beta: float = 0.1
) -> torch.Tensor:
    """
    计算风险敏感的策略梯度损失

    结合了标准策略梯度损失和CVaR风险度量，使训练更关注极端情况下的性能。

    Args:
        policy_logits: 策略网络输出的动作logits
        actions: 实际选择的动作
        advantages: 优势值
        alpha: CVaR的置信水平
        beta: 风险厌恶系数

    Returns:
        风险敏感的策略梯度损失
    """
    # 创建CVaR计算器
    cvar_calculator = CVaRCalculator(alpha=alpha, beta=beta)

    # 计算标准策略梯度损失
    log_probs = F.log_softmax(policy_logits, dim=-1)
    action_log_probs = log_probs.gather(1, actions.unsqueeze(1)).squeeze(1)
    policy_loss = -action_log_probs * advantages

    # 计算风险敏感损失
    # 对每个batch样本单独计算
    batch_size = policy_loss.size(0)
    risk_sensitive_losses = []

    for i in range(batch_size):
        # 获取当前样本的策略损失
        sample_loss = policy_loss[i].unsqueeze(0)
        # 计算风险敏感损失
        risk_loss = cvar_calculator.compute_risk_sensitive_loss(sample_loss)
        risk_sensitive_losses.append(risk_loss)

    # 组合所有样本的风险敏感损失
    risk_sensitive_policy_loss = torch.stack(risk_sensitive_losses).mean()

    return risk_sensitive_policy_loss


def cvar_value_loss(
    predicted_values: torch.Tensor,
    target_values: torch.Tensor,
    alpha: float = 0.05,
    beta: float = 0.1
) -> torch.Tensor:
    """
    计算风险敏感的价值损失

    结合了标准价值损失和CVaR风险度量，使训练更关注极端情况下的性能。

    Args:
        predicted_values: 预测的价值
        target_values: 目标价值
        alpha: CVaR的置信水平
        beta: 风险厌恶系数

    Returns:
        风险敏感的价值损失
    """
    # 创建CVaR计算器
    cvar_calculator = CVaRCalculator(alpha=alpha, beta=beta)

    # 计算标准价值损失
    value_loss = F.mse_loss(predicted_values, target_values, reduction='none')

    # 计算风险敏感损失
    risk_sensitive_loss = cvar_calculator.compute_risk_sensitive_loss(value_loss, dim=0)

    # 取平均
    return risk_sensitive_loss.mean()