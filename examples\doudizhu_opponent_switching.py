#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
使用对手分布切换的训练示例

展示如何使用对手分布切换器进行训练，提高主策略的泛化能力和鲁棒性。
"""

import os
import sys
import argparse
import logging
import numpy as np
import torch
import random
import time
from typing import Dict, List, Any, Optional, Tuple

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.algorithms.efficient_zero import EfficientZero
from cardgame_ai.core.expert_pool import ExpertPolicyPool
from cardgame_ai.algorithms.opponent_generation.gan_policy_generator import GANPolicyGenerator
from cardgame_ai.algorithms.opponent_generation.self_play_evolution import SelfPlayEvolution
from cardgame_ai.algorithms.opponent_generation.human_style_generator import HumanStyleGenerator
from cardgame_ai.utils.opponent_distribution_switcher import OpponentDistributionSwitcher
from cardgame_ai.training.enhanced_self_play import EnhancedSelfPlay
from cardgame_ai.training.trainer import Trainer

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='使用对手分布切换的训练示例')
    
    # 训练参数
    parser.add_argument('--num_epochs', type=int, default=100, help='训练轮数')
    parser.add_argument('--games_per_epoch', type=int, default=100, help='每轮游戏数')
    parser.add_argument('--batch_size', type=int, default=128, help='批次大小')
    parser.add_argument('--temperature', type=float, default=1.0, help='温度参数')
    parser.add_argument('--save_path', type=str, default='models', help='模型保存路径')
    parser.add_argument('--experience_path', type=str, default='experiences', help='经验数据保存路径')
    
    # 对手分布切换参数
    parser.add_argument('--switch_strategy', type=str, default='periodic', 
                       choices=['periodic', 'random', 'performance'], help='切换策略')
    parser.add_argument('--switch_interval', type=int, default=1000, help='切换间隔（仅对周期性切换有效）')
    parser.add_argument('--expert_pool_weight', type=float, default=1.0, help='专家池权重')
    parser.add_argument('--gan_weight', type=float, default=1.0, help='GAN生成器权重')
    parser.add_argument('--self_play_weight', type=float, default=1.0, help='自对弈历史权重')
    parser.add_argument('--human_style_weight', type=float, default=1.0, help='人类风格生成器权重')
    
    # 其他参数
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    parser.add_argument('--device', type=str, default='cuda' if torch.cuda.is_available() else 'cpu', help='计算设备')
    
    return parser.parse_args()

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 设置随机种子
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    random.seed(args.seed)
    
    # 创建保存目录
    os.makedirs(args.save_path, exist_ok=True)
    os.makedirs(args.experience_path, exist_ok=True)
    
    # 创建环境
    env = DouDizhuEnvironment()
    
    # 创建主策略代理
    agent = EfficientZero(
        observation_shape=env.observation_shape,
        action_shape=env.action_shape,
        device=args.device
    )
    
    # 创建对手来源
    # 1. 专家池
    expert_pool = ExpertPolicyPool(config_path='configs/expert_pool.json')
    
    # 2. GAN生成器
    gan_generator = GANPolicyGenerator(
        latent_dim=128,
        policy_dim=env.action_shape[0],
        device=args.device
    )
    
    # 3. 自对弈历史
    self_play_sampler = SelfPlayEvolution(
        checkpoint_dir=args.save_path,
        sampling_strategy='latest_k',
        k=5,
        model_class=EfficientZero,
        device=args.device
    )
    
    # 4. 人类风格生成器
    human_style_generator = HumanStyleGenerator(
        model_path='models/human_style_generator.pt',
        device=args.device
    )
    
    # 创建对手分布切换器
    distribution_weights = {
        'expert_pool': args.expert_pool_weight,
        'gan_generator': args.gan_weight,
        'self_play': args.self_play_weight,
        'human_style': args.human_style_weight
    }
    
    opponent_switcher = OpponentDistributionSwitcher(
        expert_pool=expert_pool,
        gan_generator=gan_generator,
        self_play_sampler=self_play_sampler,
        human_style_generator=human_style_generator,
        switch_strategy=args.switch_strategy,
        switch_interval=args.switch_interval,
        distribution_weights=distribution_weights,
        random_seed=args.seed
    )
    
    # 创建增强版自我对弈
    self_play = EnhancedSelfPlay(save_path=args.experience_path)
    self_play.set_opponent_switcher(opponent_switcher)
    
    # 创建训练器
    trainer = Trainer(save_path=args.save_path)
    
    # 训练循环
    for epoch in range(1, args.num_epochs + 1):
        logger.info(f"开始第 {epoch}/{args.num_epochs} 轮训练")
        
        # 自我对弈生成经验
        start_time = time.time()
        logger.info("开始自我对弈...")
        
        experiences = self_play.generate_experience_with_opponent_switcher(
            env=env,
            agent=agent,
            num_games=args.games_per_epoch,
            temperature=args.temperature,
            save=True,
            parallel=False
        )
        
        logger.info(f"自我对弈完成，生成 {len(experiences)} 个经验，耗时 {time.time() - start_time:.2f}s")
        
        # 打印对手分布切换器统计信息
        stats = opponent_switcher.get_stats()
        logger.info(f"对手分布切换器统计信息: {stats}")
        
        # 训练智能体
        start_time = time.time()
        logger.info("开始训练智能体...")
        
        train_metrics = trainer.train_with_experiences(
            agent=agent,
            experiences=experiences,
            batch_size=min(args.batch_size, len(experiences)),
            num_epochs=5,
            shuffle=True
        )
        
        logger.info(f"训练完成，耗时 {time.time() - start_time:.2f}s")
        logger.info(f"训练指标: {train_metrics}")
        
        # 保存模型
        if epoch % 10 == 0 or epoch == args.num_epochs:
            model_path = os.path.join(args.save_path, f"agent_epoch_{epoch}.pt")
            agent.save(model_path)
            logger.info(f"保存模型: {model_path}")
    
    logger.info("训练完成！")

if __name__ == '__main__':
    main()
