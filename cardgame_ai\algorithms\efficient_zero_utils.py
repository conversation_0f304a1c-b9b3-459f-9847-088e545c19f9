"""
EfficientZero工具函数模块

该模块包含EfficientZero算法的辅助工具函数，包括：
- 测试函数
- 性能评估工具
- 调试辅助函数
- 数据处理工具

主要功能：
- test_efficient_zero_amp: 测试混合精度训练
- test_importance_weighted_training: 测试重要性加权训练
- 性能监控和分析工具
- 调试和可视化辅助函数
"""

import torch
import numpy as np
import logging
import time
from typing import Dict, Any, List, Tuple, Optional

# 配置日志
logger = logging.getLogger(__name__)


def test_efficient_zero_amp():
    """
    测试EfficientZero混合精度训练功能
    
    该函数创建一个关键决策点检测器和一个EfficientZero模型，
    然后使用关键决策点检测器对样本进行加权训练。
    
    Returns:
        bool: 测试是否成功
    """
    import time
    import torch
    import numpy as np
    from cardgame_ai.games.doudizhu import DouDizhuEnvironment

    try:
        logger.info("开始测试EfficientZero混合精度训练...")

        # 创建游戏环境
        env = DouDizhuEnvironment()
        state_shape = env.observation_space.shape
        action_shape = (env.action_space.n,)

        # 创建EfficientZeroAMP模型
        from .efficient_zero_amp import EfficientZeroAMP
        model = EfficientZeroAMP(
            state_shape=state_shape,
            action_shape=action_shape,
            hidden_dim=128,  # 使用较小的维度进行测试
            state_dim=32,
            use_mixed_precision=True,
            device='cuda' if torch.cuda.is_available() else 'cpu'
        )

        logger.info(f"模型创建成功，使用设备: {model.device}")
        logger.info(f"混合精度训练: {'启用' if model.amp_enabled else '禁用'}")

        # 测试前向传播
        state = env.reset()
        state_tensor = torch.FloatTensor(state.vectorize()).unsqueeze(0).to(model.device)

        with torch.no_grad():
            policy, value = model.predict(state_tensor)
            logger.info(f"前向传播测试成功 - 策略形状: {policy.shape}, 价值形状: {value.shape}")

        logger.info("EfficientZero混合精度训练测试完成!")
        return True

    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        logger.exception(e)
        return False


def test_importance_weighted_training():
    """
    测试重要性加权训练功能

    该函数测试EfficientZero算法的重要性加权训练机制，
    验证关键时刻检测和动态预算分配功能。

    Returns:
        bool: 测试是否成功
    """
    try:
        logger.info("开始测试重要性加权训练...")

        # 创建游戏环境
        from cardgame_ai.games.doudizhu import DouDizhuEnvironment
        env = DouDizhuEnvironment()
        state_shape = env.observation_space.shape
        action_shape = (env.action_space.n,)

        # 创建EfficientZero模型
        from .efficient_zero_algorithm import EfficientZero
        model = EfficientZero(
            state_shape=state_shape,
            action_shape=action_shape,
            hidden_dim=128,
            state_dim=32,
            device='cuda' if torch.cuda.is_available() else 'cpu'
        )

        logger.info("模型创建成功")

        # 测试关键时刻检测
        state = env.reset()
        is_key_moment = model.key_moment_detector.detect(state)
        logger.info(f"关键时刻检测测试: {is_key_moment}")

        # 测试动态预算分配
        budget_info = {'total_budget': 100, 'remaining_budget': 80}
        allocated_budget = model.dynamic_budget_allocator.allocate_budget(
            state, model.num_simulations, budget_info
        )
        logger.info(f"动态预算分配测试: {allocated_budget}")

        logger.info("重要性加权训练测试完成!")
        return True

    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        logger.exception(e)
        return False


def monitor_training_performance(model, batch_data: Dict[str, Any]) -> Dict[str, float]:
    """
    监控训练性能指标

    Args:
        model: EfficientZero模型实例
        batch_data: 批次训练数据

    Returns:
        Dict[str, float]: 性能指标字典
    """
    metrics = {}

    try:
        # 记录开始时间
        start_time = time.time()

        # 执行训练步骤
        losses = model.train(batch_data)

        # 计算训练时间
        training_time = time.time() - start_time
        metrics['training_time'] = training_time

        # 记录损失指标
        metrics.update(losses)

        # 计算GPU内存使用（如果可用）
        if torch.cuda.is_available():
            metrics['gpu_memory_allocated'] = torch.cuda.memory_allocated() / 1024**3  # GB
            metrics['gpu_memory_reserved'] = torch.cuda.memory_reserved() / 1024**3   # GB

        # 记录回放缓冲区状态
        if hasattr(model, 'replay_buffer'):
            metrics['replay_buffer_size'] = len(model.replay_buffer)
            metrics['replay_buffer_capacity'] = model.replay_buffer.capacity

        return metrics

    except Exception as e:
        logger.error(f"性能监控失败: {str(e)}")
        return {'error': str(e)}


def debug_model_gradients(model) -> Dict[str, float]:
    """
    调试模型梯度信息

    Args:
        model: EfficientZero模型实例

    Returns:
        Dict[str, float]: 梯度统计信息
    """
    gradient_stats = {}

    try:
        total_norm = 0.0
        param_count = 0

        for name, param in model.named_parameters():
            if param.grad is not None:
                param_norm = param.grad.data.norm(2)
                total_norm += param_norm.item() ** 2
                param_count += 1

                # 记录每层的梯度范数
                gradient_stats[f'grad_norm_{name}'] = param_norm.item()

        # 计算总梯度范数
        total_norm = total_norm ** (1. / 2)
        gradient_stats['total_grad_norm'] = total_norm
        gradient_stats['param_count_with_grad'] = param_count

        return gradient_stats

    except Exception as e:
        logger.error(f"梯度调试失败: {str(e)}")
        return {'error': str(e)}


def analyze_replay_buffer(replay_buffer) -> Dict[str, Any]:
    """
    分析回放缓冲区数据分布

    Args:
        replay_buffer: 回放缓冲区实例

    Returns:
        Dict[str, Any]: 数据分析结果
    """
    analysis = {}

    try:
        if len(replay_buffer) == 0:
            return {'message': '回放缓冲区为空'}

        # 采样一些数据进行分析
        sample_size = min(1000, len(replay_buffer))
        samples = replay_buffer.sample(sample_size)

        # 分析奖励分布
        rewards = [exp.reward for exp in samples]
        analysis['reward_mean'] = np.mean(rewards)
        analysis['reward_std'] = np.std(rewards)
        analysis['reward_min'] = np.min(rewards)
        analysis['reward_max'] = np.max(rewards)

        # 分析动作分布
        actions = [exp.action.action_id if hasattr(exp.action, 'action_id') else 0 for exp in samples]
        unique_actions, action_counts = np.unique(actions, return_counts=True)
        analysis['action_distribution'] = dict(zip(unique_actions.tolist(), action_counts.tolist()))

        # 分析游戏结束状态
        dones = [exp.done for exp in samples]
        analysis['done_ratio'] = np.mean(dones)

        analysis['buffer_size'] = len(replay_buffer)
        analysis['sample_size'] = sample_size

        return analysis

    except Exception as e:
        logger.error(f"回放缓冲区分析失败: {str(e)}")
        return {'error': str(e)}
