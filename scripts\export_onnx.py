#!/usr/bin/env python
"""
ONNX模型导出脚本

这个脚本用于将训练好的PyTorch模型导出为ONNX格式，以便在不同平台和框架上进行推理。
支持多种模型类型的导出，包括但不限于ValuePolicyNet、EnhancedTransformerPolicyNetwork等。
"""

import os
import sys
import argparse
import logging
import torch
import torch.nn as nn
import torch.onnx
import numpy as np
import onnx
import onnxruntime as ort
from typing import Dict, List, Tuple, Any, Optional, Union, Type

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cardgame_ai.models.value_policy_net import ValuePolicyNet, DuelingValuePolicyNet
from cardgame_ai.models.enhanced_transformer_policy import EnhancedTransformerPolicyNetwork
from cardgame_ai.models.enhanced_value_policy_net import EnhancedValuePolicyNet
from cardgame_ai.models.enhanced_efficient_zero import EnhancedEfficientZero
from cardgame_ai.models.gnn_enhanced_efficient_zero import GNNEnhancedEfficientZero
from cardgame_ai.games.common.belief_state import BeliefState


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("export_onnx")


def parse_args():
    """
    解析命令行参数
    
    Returns:
        argparse.Namespace: 解析后的参数
    """
    parser = argparse.ArgumentParser(description="将PyTorch模型导出为ONNX格式")
    
    # 必需的参数
    parser.add_argument(
        "--model_path", 
        type=str, 
        required=True,
        help="PyTorch模型文件路径 (.pt 或 .pth)"
    )
    
    parser.add_argument(
        "--output_path", 
        type=str, 
        required=True,
        help="ONNX模型输出路径 (.onnx)"
    )
    
    # 可选参数
    parser.add_argument(
        "--model_type", 
        type=str, 
        default="value_policy_net",
        choices=[
            "value_policy_net", 
            "dueling_value_policy_net", 
            "enhanced_transformer", 
            "enhanced_value_policy_net",
            "efficient_zero", 
            "enhanced_efficient_zero", 
            "gnn_enhanced_efficient_zero"
        ],
        help="模型类型"
    )
    
    parser.add_argument(
        "--opset_version", 
        type=int, 
        default=14,
        help="ONNX算子集版本"
    )
    
    parser.add_argument(
        "--state_dim", 
        type=int, 
        default=342,
        help="状态维度（用于创建示例输入）"
    )
    
    parser.add_argument(
        "--belief_dim", 
        type=int, 
        default=162,
        help="信念状态维度（用于创建示例输入）"
    )
    
    parser.add_argument(
        "--action_dim", 
        type=int, 
        default=54,
        help="动作维度（用于创建示例输入）"
    )
    
    parser.add_argument(
        "--dynamic_axes", 
        action="store_true",
        help="是否使用动态轴（批量大小可变）"
    )
    
    parser.add_argument(
        "--batch_size", 
        type=int, 
        default=1,
        help="示例输入的批量大小"
    )
    
    parser.add_argument(
        "--seq_len", 
        type=int, 
        default=100,
        help="Transformer模型的序列长度"
    )
    
    parser.add_argument(
        "--verify", 
        action="store_true",
        help="是否验证导出的ONNX模型"
    )
    
    parser.add_argument(
        "--verbose", 
        action="store_true",
        help="是否输出详细日志"
    )
    
    return parser.parse_args()


def create_dummy_input(args) -> Dict[str, torch.Tensor]:
    """
    创建示例输入

    Args:
        args: 命令行参数

    Returns:
        Dict[str, torch.Tensor]: 包含示例输入的字典
    """
    # 基础输入字典
    dummy_inputs = {}
    
    # 根据模型类型创建不同的示例输入
    if args.model_type in ["value_policy_net", "dueling_value_policy_net", "enhanced_value_policy_net"]:
        # ValuePolicyNet 系列模型需要状态和可选的信念状态输入
        dummy_inputs["state_input"] = torch.randn(args.batch_size, args.state_dim)
        dummy_inputs["belief_input"] = torch.randn(args.batch_size, args.belief_dim)
        
    elif args.model_type in ["enhanced_transformer"]:
        # Transformer 模型需要序列输入
        dummy_inputs["x"] = torch.randn(args.batch_size, args.seq_len, args.state_dim)
        
    elif args.model_type in ["efficient_zero", "enhanced_efficient_zero", "gnn_enhanced_efficient_zero"]:
        # Efficient Zero 系列模型需要状态输入
        dummy_inputs["state"] = torch.randn(args.batch_size, args.state_dim)
        
    logger.info(f"创建了示例输入: {[(k, v.shape) for k, v in dummy_inputs.items()]}")
    return dummy_inputs


def load_model(args) -> nn.Module:
    """
    加载PyTorch模型

    Args:
        args: 命令行参数

    Returns:
        nn.Module: 加载的PyTorch模型
    """
    logger.info(f"加载模型: {args.model_path}")
    try:
        # 先尝试直接加载完整模型
        model = torch.load(args.model_path, map_location="cpu")
        logger.info("已加载完整模型")
    except Exception as e:
        logger.warning(f"加载完整模型失败 ({e})，尝试加载模型权重...")
        
        # 如果直接加载失败，尝试创建模型实例并加载权重
        # 根据模型类型初始化空模型
        if args.model_type == "value_policy_net":
            model = ValuePolicyNet(args.state_dim, args.belief_dim, args.action_dim)
        elif args.model_type == "dueling_value_policy_net":
            model = DuelingValuePolicyNet(args.state_dim, args.belief_dim, args.action_dim)
        elif args.model_type == "enhanced_transformer":
            model = EnhancedTransformerPolicyNetwork(
                input_dim=args.state_dim,
                action_dim=args.action_dim,
                seq_len=args.seq_len
            )
        elif args.model_type == "enhanced_value_policy_net":
            model = EnhancedValuePolicyNet(args.state_dim, args.belief_dim, args.action_dim)
        elif args.model_type == "enhanced_efficient_zero":
            model = EnhancedEfficientZero(args.state_dim, args.action_dim)
        elif args.model_type == "gnn_enhanced_efficient_zero":
            model = GNNEnhancedEfficientZero(args.state_dim, args.action_dim)
        else:
            raise ValueError(f"不支持的模型类型: {args.model_type}")
            
        # 加载权重
        model.load_state_dict(torch.load(args.model_path, map_location="cpu"))
        logger.info(f"已创建 {args.model_type} 模型并加载权重")
        
    # 设置为评估模式
    model.eval()
    return model


def export_to_onnx(model: nn.Module, dummy_inputs: Dict[str, torch.Tensor], args):
    """
    将模型导出为ONNX格式

    Args:
        model (nn.Module): 要导出的PyTorch模型
        dummy_inputs (Dict[str, torch.Tensor]): 示例输入
        args: 命令行参数
    """
    logger.info(f"开始导出模型到ONNX格式: {args.output_path}")
    
    # 准备动态轴配置
    dynamic_axes = {}
    if args.dynamic_axes:
        for input_name in dummy_inputs.keys():
            dynamic_axes[input_name] = {0: "batch_size"}
        dynamic_axes["policy_logits"] = {0: "batch_size"}
        dynamic_axes["value"] = {0: "batch_size"}
        logger.info("使用动态轴配置（批量大小可变）")
    
    # 将输入字典转换为按位置顺序的参数列表
    input_args = list(dummy_inputs.values())
    input_names = list(dummy_inputs.keys())
    output_names = ["policy_logits", "value"]
    
    try:
        # 导出模型
        torch.onnx.export(
            model,                       # 要导出的模型
            args=tuple(input_args),      # 模型输入
            f=args.output_path,          # 输出文件
            input_names=input_names,     # 输入张量的名称
            output_names=output_names,   # 输出张量的名称
            opset_version=args.opset_version,  # ONNX版本
            do_constant_folding=True,    # 是否折叠常量
            export_params=True,          # 是否导出模型参数
            verbose=args.verbose,        # 是否输出详细信息
            dynamic_axes=dynamic_axes if args.dynamic_axes else None  # 动态轴配置
        )
        logger.info(f"模型已成功导出到: {args.output_path}")
    except Exception as e:
        logger.error(f"导出模型失败: {e}")
        raise


def verify_onnx_model(args, dummy_inputs: Dict[str, torch.Tensor], pytorch_outputs: Tuple):
    """
    验证导出的ONNX模型

    Args:
        args: 命令行参数
        dummy_inputs (Dict[str, torch.Tensor]): 示例输入
        pytorch_outputs (Tuple): PyTorch模型的输出
    """
    logger.info("开始验证ONNX模型...")
    
    # 加载ONNX模型
    try:
        onnx_model = onnx.load(args.output_path)
        onnx.checker.check_model(onnx_model)
        logger.info("ONNX模型格式正确")
    except Exception as e:
        logger.error(f"ONNX模型验证失败: {e}")
        return False
    
    # 创建ONNX Runtime会话
    try:
        session = ort.InferenceSession(args.output_path)
        logger.info("已创建ONNX Runtime会话")
    except Exception as e:
        logger.error(f"创建ONNX Runtime会话失败: {e}")
        return False
    
    # 准备ONNX输入
    onnx_inputs = {}
    for input_name, input_tensor in dummy_inputs.items():
        onnx_inputs[input_name] = input_tensor.numpy()
    
    # 运行ONNX推理
    try:
        onnx_outputs = session.run(None, onnx_inputs)
        logger.info(f"ONNX推理成功，输出形状: {[o.shape for o in onnx_outputs]}")
    except Exception as e:
        logger.error(f"ONNX推理失败: {e}")
        return False
    
    # 比较PyTorch和ONNX输出
    policy_diff = np.abs(pytorch_outputs[0].detach().numpy() - onnx_outputs[0]).max()
    value_diff = np.abs(pytorch_outputs[1].detach().numpy() - onnx_outputs[1]).max()
    
    logger.info(f"PyTorch vs ONNX - 策略差异最大值: {policy_diff:.6f}, 价值差异最大值: {value_diff:.6f}")
    
    if policy_diff > 1e-4 or value_diff > 1e-4:
        logger.warning("PyTorch和ONNX输出存在较大差异，请检查模型导出过程")
        return False
    else:
        logger.info("验证通过: PyTorch和ONNX输出一致")
        return True


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 设置日志级别
    if args.verbose:
        logger.setLevel(logging.DEBUG)
    
    # 创建输出目录（如果不存在）
    os.makedirs(os.path.dirname(os.path.abspath(args.output_path)), exist_ok=True)
    
    # 加载模型
    model = load_model(args)
    
    # 创建示例输入
    dummy_inputs = create_dummy_input(args)
    
    # 获取PyTorch模型输出（用于验证）
    with torch.no_grad():
        if args.model_type in ["value_policy_net", "dueling_value_policy_net", "enhanced_value_policy_net"]:
            pytorch_outputs = model(dummy_inputs["state_input"], dummy_inputs.get("belief_input"))
        else:
            # 确保输入按正确的顺序传递给模型
            first_input_name = list(dummy_inputs.keys())[0]
            pytorch_outputs = model(dummy_inputs[first_input_name])
    
    # 导出模型到ONNX
    export_to_onnx(model, dummy_inputs, args)
    
    # 验证导出的模型（如果需要）
    if args.verify:
        is_valid = verify_onnx_model(args, dummy_inputs, pytorch_outputs)
        if is_valid:
            logger.info("ONNX模型验证通过!")
        else:
            logger.error("ONNX模型验证失败!")
    
    logger.info("完成 ONNX 模型导出")


if __name__ == "__main__":
    main() 