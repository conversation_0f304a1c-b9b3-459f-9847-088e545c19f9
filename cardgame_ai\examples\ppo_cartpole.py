"""
PPO算法示例：CartPole环境

使用PPO算法在CartPole环境中训练智能体，展示PPO算法的基本用法。
"""
import gym
import numpy as np
import matplotlib.pyplot as plt
from typing import List, Tuple, Dict
import torch
import os
import time

from cardgame_ai.algorithms.ppo import PPO
from cardgame_ai.core.base import Experience


def test_ppo_cartpole():
    """
    在CartPole环境中测试PPO算法
    """
    # 创建环境
    env = gym.make('CartPole-v1')
    
    # 获取状态和动作空间
    state_shape = env.observation_space.shape
    action_shape = (env.action_space.n,)
    
    # 创建PPO算法
    ppo = PPO(
        state_shape=state_shape,
        action_shape=action_shape,
        hidden_dims=[64, 64],
        learning_rate=0.001,
        gamma=0.99,
        gae_lambda=0.95,
        clip_ratio=0.2,
        value_coef=0.5,
        entropy_coef=0.01,
        max_grad_norm=0.5,
        update_epochs=4,
        shared_network=True,
        batch_size=128,
    )
    
    # 训练参数
    num_episodes = 500
    max_steps = 500
    
    # 记录奖励
    rewards_history = []
    avg_rewards_history = []
    
    # 训练循环
    for episode in range(num_episodes):
        # 重置环境
        state = env.reset()
        total_reward = 0
        experiences = []
        
        # 每个回合的步骤
        for step in range(max_steps):
            # 预测动作概率
            action_probs, _ = ppo.predict(state)
            
            # 根据概率分布采样动作
            action = np.random.choice(len(action_probs), p=action_probs)
            
            # 执行动作
            next_state, reward, done, _ = env.step(action)
            
            # 存储经验
            experience = Experience(
                state=state,
                action=action,
                reward=reward,
                next_state=next_state,
                done=done
            )
            experiences.append(experience)
            
            # 更新状态和奖励
            state = next_state
            total_reward += reward
            
            # 如果回合结束，则跳出循环
            if done:
                break
        
        # 更新模型
        for exp in experiences:
            ppo.update(exp)
        
        # 记录奖励
        rewards_history.append(total_reward)
        avg_reward = np.mean(rewards_history[-100:])
        avg_rewards_history.append(avg_reward)
        
        # 打印进度
        if (episode + 1) % 10 == 0:
            print(f"Episode {episode + 1}/{num_episodes}, Reward: {total_reward:.2f}, Avg Reward: {avg_reward:.2f}")
        
        # 如果平均奖励达到目标，则提前结束
        if avg_reward >= 475.0:
            print(f"环境已解决！在第 {episode + 1} 回合达到平均奖励 {avg_reward:.2f}")
            break
    
    # 关闭环境
    env.close()
    
    # 绘制奖励曲线
    plt.figure(figsize=(10, 5))
    plt.plot(rewards_history, label='奖励')
    plt.plot(avg_rewards_history, label='平均奖励', color='red')
    plt.xlabel('回合')
    plt.ylabel('奖励')
    plt.title('PPO在CartPole环境中的训练奖励')
    plt.legend()
    plt.grid()
    
    # 创建保存目录
    os.makedirs('results', exist_ok=True)
    plt.savefig('results/ppo_cartpole_rewards.png')
    plt.show()
    
    # 测试训练好的模型
    test_model(env, ppo)


def test_model(env: gym.Env, ppo: PPO, num_episodes: int = 5):
    """
    测试训练好的模型
    
    Args:
        env (gym.Env): 环境
        ppo (PPO): PPO算法
        num_episodes (int, optional): 测试回合数. Defaults to 5.
    """
    print("\n正在测试训练好的模型...")
    
    for episode in range(num_episodes):
        state = env.reset()
        total_reward = 0
        done = False
        
        while not done:
            # 渲染环境
            env.render()
            
            # 使用模型选择动作
            action_probs, _ = ppo.predict(state)
            action = np.argmax(action_probs)
            
            # 执行动作
            state, reward, done, _ = env.step(action)
            total_reward += reward
            
            # 减慢速度，便于观察
            time.sleep(0.01)
        
        print(f"测试回合 {episode + 1}/{num_episodes}, 奖励: {total_reward}")
    
    env.close()


if __name__ == "__main__":
    test_ppo_cartpole() 