"""
游戏状态模块

定义斗地主游戏状态及其转换方法。
"""
from typing import List, Dict, Any, Optional, Tuple, Set, Union
import numpy as np
from enum import Enum
import logging

from cardgame_ai.core.base import State
from cardgame_ai.games.doudizhu.card import Card, CardRank, CardSuit
from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType

logger = logging.getLogger(__name__)


class GamePhase(Enum):
    """游戏阶段枚举"""
    DEALING = 0      # 发牌阶段
    BIDDING = 1      # 叫地主阶段
    GRABBING = 2     # 抢地主阶段
    PLAYING = 3      # 出牌阶段


class DouDizhuState(State):
    """
    斗地主游戏状态类

    表示斗地主游戏的状态，包括玩家手牌、历史出牌、当前玩家等信息。
    """

    def __init__(
        self,
        hands: List[List[Card]],
        landlord_cards: List[Card],  # 新增：底牌
        landlord: Optional[int] = None,  # 修改：地主可能尚未确定
        current_player: int = 0,
        game_phase: GamePhase = GamePhase.DEALING,  # 新增：游戏阶段
        bid_history: Optional[List[Tuple[int, int]]] = None,  # 新增：叫分历史 (玩家索引, 叫分)
        grab_history: Optional[List[Tuple[int, bool]]] = None,  # 新增：抢地主历史 (玩家索引, 是否抢地主)
        highest_bidder: Optional[int] = None,  # 新增：最高叫分玩家
        highest_bid: int = 0,  # 新增：最高叫分
        last_move: Optional[CardGroup] = None,
        last_player: Optional[int] = None,
        num_passes: int = 0,
        history: Optional[List[Tuple[int, CardGroup]]] = None,
        played_cards: Optional[List[Card]] = None,
        infoset: Optional[Dict[str, Any]] = None
    ):
        """
        初始化游戏状态

        Args:
            hands (List[List[Card]]): 玩家手牌
            landlord_cards (List[Card]): 底牌
            landlord (Optional[int], optional): 地主玩家索引，可能尚未确定. Defaults to None.
            current_player (int, optional): 当前玩家索引. Defaults to 0.
            game_phase (GamePhase, optional): 游戏阶段. Defaults to GamePhase.DEALING.
            bid_history (Optional[List[Tuple[int, int]]], optional): 叫分历史. Defaults to None.
            grab_history (Optional[List[Tuple[int, bool]]], optional): 抢地主历史. Defaults to None.
            highest_bidder (Optional[int], optional): 最高叫分玩家. Defaults to None.
            highest_bid (int, optional): 最高叫分. Defaults to 0.
            last_move (Optional[CardGroup], optional): 上一手牌. Defaults to None.
            last_player (Optional[int], optional): 上一个出牌的玩家. Defaults to None.
            num_passes (int, optional): 连续不出的次数. Defaults to 0.
            history (Optional[List[Tuple[int, CardGroup]]], optional): 历史出牌记录. Defaults to None.
            played_cards (Optional[List[Card]], optional): 已出的牌. Defaults to None.
            infoset (Optional[Dict[str, Any]], optional): 额外信息. Defaults to None.
        """
        self.hands = [sorted(hand) for hand in hands]
        self.landlord_cards = sorted(landlord_cards)  # 新增：底牌
        self.landlord = landlord
        self.current_player = current_player
        self.game_phase = game_phase  # 新增：游戏阶段
        self.bid_history = bid_history if bid_history is not None else []  # 新增：叫分历史
        self.grab_history = grab_history if grab_history is not None else []  # 新增：抢地主历史
        self.highest_bidder = highest_bidder  # 新增：最高叫分玩家
        self.highest_bid = highest_bid  # 新增：最高叫分
        self.last_move = last_move
        self.last_player = last_player
        self.num_passes = num_passes
        self.history = history if history is not None else []
        self.played_cards = played_cards if played_cards is not None else []
        self.infoset = infoset if infoset is not None else {}

    def get_player_id(self) -> int:
        """
        获取当前玩家ID

        Returns:
            int: 当前玩家ID
        """
        return self.current_player

    def get_legal_actions(self) -> List[CardGroup]:
        """
        获取当前玩家的合法动作

        Returns:
            List[CardGroup]: 合法动作列表
        """
        # 获取当前玩家的手牌
        hand = self.hands[self.current_player]
        legal_actions: List[CardGroup] = []

        # 规则1: 如果游戏阶段不是 PLAYING (例如 BIDDING, GRABBING)，则这里不应返回出牌动作
        # (此处的实现主要针对 PLAYING 阶段，其他阶段的合法动作应在游戏流程中单独处理或扩展此方法)
        if self.game_phase != GamePhase.PLAYING:
            # 非出牌阶段，根据具体阶段返回相应动作，例如叫分、抢地主等
            # 为简化，此处假设非PLAYING阶段没有基于CardGroup的动作，或由其他逻辑处理
            # 如果需要，这里可以返回一个空的CardGroup代表PASS，或者特定阶段的动作
            return [CardGroup([])] # 示例：允许PASS或无动作

        # 规则 2: 任何时候都可以选择 "不出" (PASS)，除非是特定情况（如地主首次出牌，或轮到自己必须出牌）
        can_pass = True

        # 情况 A: 地主首次出牌 (self.last_player is None and self.current_player == self.landlord)
        # 或者，轮到自己出牌 (因为之前所有人都PASS了，self.num_passes >= 2 且出牌权回到 self.current_player)
        # 或者，自己是上一个出牌者，且由于其他人PASS，出牌权又轮回到自己 (self.last_player == self.current_player)
        # 在这些情况下，玩家必须出牌，不能PASS。
        is_new_round_starter = (
            (self.last_player is None and self.current_player == self.landlord) or # 地主首次出牌
            (self.num_passes >= 2 and self.last_player is not None and self.current_player == self.last_player) or # 连续PASS后，出牌权回到上一个出牌者
            (self.last_player == self.current_player and self.num_passes == 0) # 自己刚出完牌，其他人PASS，轮回到自己 (num_passes=0确保是紧接着的回合)
            # 注意：get_next_state中，如果num_passes >=2, last_move会置为None。所以下面也需要考虑last_move is None
            or (self.last_move is None and self.num_passes == 0 and self.last_player is not None and self.current_player == self.last_player) # 兼容get_next_state中last_move被清空的情况
            or (self.last_move is None and self.num_passes == 0 and self.last_player is None and self.current_player == self.landlord) # 地主首次，last_move也是None
        )

        if is_new_round_starter:
            can_pass = False # 新一轮开始，必须出牌
            all_groups = self._get_all_card_groups(hand) # 获取所有可能的牌型
            # 排除空牌组 (PASS)
            legal_actions.extend([group for group in all_groups if group.cards])
        else:
            # 情况 B: 正常跟牌，需要出比 self.last_move 更大的牌
            if self.last_move is not None:
                legal_actions.extend(self._get_greater_card_groups(hand, self.last_move))
            else:
                # 理论上，如果不是 is_new_round_starter，last_move 不应该是 None
                # 但作为防御，如果 last_move 是 None (例如，游戏开始非地主玩家，或代码逻辑意外清空)
                # 此时应该允许出任意牌
                all_groups = self._get_all_card_groups(hand)
                legal_actions.extend([group for group in all_groups if group.cards])

        # 如果允许PASS，且PASS动作尚未加入 (例如 _get_greater_card_groups 不包含PASS，或上述逻辑未加入PASS)
        # _get_greater_card_groups 和 _get_all_card_groups 内部实现已经包含了 CardGroup([]) 即PASS
        # 所以这里不需要额外添加，除非它们的实现改变了。
        # 为了明确，确保PASS在允许时一定存在：
        if can_pass:
            pass_action = CardGroup([])
            if pass_action not in legal_actions:
                 # _get_greater_card_groups 和 _get_all_card_groups 通常会返回PASS
                 # 如果出现它们没返回PASS但又can_pass的情况，说明那两个函数逻辑有变，这里补充一下
                 # 但通常情况下，下面这行是不必要的
                 legal_actions.append(pass_action)
        elif not any(group.cards for group in legal_actions) and not can_pass:
            # 如果不允许PASS，但合法的出牌动作列表为空（例如，没牌可打大过上家，但规则又不允许他PASS）
            # 这通常指示一个游戏逻辑问题或玩家已无有效牌可出但游戏未结束的特殊状态
            # 在某些规则中，这可能强制玩家PASS（如果规则允许），或者意味着游戏卡死
            # 按照当前严格的 must_play 逻辑，这里不添加PASS
            # print(f"警告: 玩家 {self.current_player} 不允许PASS但没有合法出牌动作。")
            pass # 保持现状，依赖调用者或游戏环境处理此死锁

        # 去重并确保PASS总是在列表中（如果允许）
        # 使用set去重，然后转回list。PASS动作 CardGroup([]) 比较特殊，确保它在需要时存在。
        unique_actions = []
        seen_actions_str = set()
        pass_present_if_can_pass = not can_pass # 如果不能pass，则认为pass已正确处理

        for action in legal_actions:
            action_str = str(action.cards) # 用牌的字符串表示来去重
            if action_str not in seen_actions_str:
                unique_actions.append(action)
                seen_actions_str.add(action_str)
            if not action.cards and can_pass: # 检查PASS动作是否已在unique_actions中
                pass_present_if_can_pass = True

        if can_pass and not pass_present_if_can_pass:
            # 如果经历了上面的逻辑，允许pass，但最终的unique_actions里没有pass，则补上
            # 这种情况通常是_get_all_card_groups或_get_greater_card_groups意外地没有返回pass时
            unique_actions.append(CardGroup([]))

        # 如果最终unique_actions为空，且玩家又不能PASS，这代表没牌可出且不能过，是个问题
        if not unique_actions and not can_pass:
            # print(f"错误：玩家 {self.current_player} 必须出牌，但没有可出的牌！手牌: {hand}")
            # 这种情况下，根据游戏规则，可能需要有特殊处理，例如判负或游戏异常
            # 为了让游戏能继续（或暴露问题），可以强制返回一个PASS，但这违反了can_pass=False的设定
            # 或者返回一个空列表，让上层决定如何处理
            return [] # 返回空列表表示没有合法动作，即使是必须出牌

        return unique_actions

    def _get_all_card_groups(self, hand: List[Card]) -> List[CardGroup]:
        """
        获取所有可能的牌型

        Args:
            hand (List[Card]): 手牌

        Returns:
            List[CardGroup]: 所有可能的牌型
        """
        # 不出
        result = [CardGroup([])]

        # 单张
        for card in hand:
            result.append(CardGroup([card]))

        # 对子
        for i in range(len(hand)):
            for j in range(i + 1, len(hand)):
                if hand[i].rank == hand[j].rank:
                    result.append(CardGroup([hand[i], hand[j]]))

        # 三张
        for i in range(len(hand)):
            for j in range(i + 1, len(hand)):
                for k in range(j + 1, len(hand)):
                    if hand[i].rank == hand[j].rank == hand[k].rank:
                        result.append(CardGroup([hand[i], hand[j], hand[k]]))

        # 炸弹
        for i in range(len(hand)):
            for j in range(i + 1, len(hand)):
                for k in range(j + 1, len(hand)):
                    for l in range(k + 1, len(hand)):
                        if hand[i].rank == hand[j].rank == hand[k].rank == hand[l].rank:
                            result.append(CardGroup([hand[i], hand[j], hand[k], hand[l]]))

        # 火箭
        small_joker = None
        big_joker = None
        for card in hand:
            if card.rank == CardRank.SMALL_JOKER:
                small_joker = card
            elif card.rank == CardRank.BIG_JOKER:
                big_joker = card

        if small_joker and big_joker:
            result.append(CardGroup([small_joker, big_joker]))

        # 其他牌型（顺子、连对、飞机等）
        # 这里简化处理，只考虑基本牌型
        # 实际实现中应该考虑所有可能的牌型组合

        return [group for group in result if group.is_valid()]

    def _get_greater_card_groups(self, hand: List[Card], last_move: CardGroup) -> List[CardGroup]:
        """
        获取比上一手牌大的牌型

        Args:
            hand (List[Card]): 手牌
            last_move (CardGroup): 上一手牌

        Returns:
            List[CardGroup]: 比上一手牌大的牌型
        """
        # 不出
        result = [CardGroup([])]

        # 获取所有可能的牌型
        # 优化：如果last_move为None，理论上任何牌都可以出，但此函数通常在需要打出更大牌时调用
        # 为保持接口清晰，调用者应确保 last_move 在此场景下不为 None，或 get_legal_actions 中做相应处理
        if last_move is None: # 防御性编程：如果上一个动作是None，说明可以出任意牌，但这里主要找更大的牌
             all_groups = self._get_all_card_groups(hand)
             # 此时，不过滤，因为没有比较对象，或者说任何牌都比 "None" 大
             # 但通常 get_legal_actions 会处理这种情况，这里返回空或所有牌取决于具体约定
             # 按照原意，这里应该返回空，因为没有"更大"的牌型可言，除非约定 last_move is None 代表最小牌
             # 暂时维持原逻辑，依赖 get_legal_actions 的正确调用
        else:
            all_groups = self._get_all_card_groups(hand)


        # 筛选出比上一手牌大的牌型
        for group in all_groups:
            if group.cards and last_move and group.can_beat(last_move): # 确保 group.cards 非空，last_move也非空
                result.append(group)
            elif group.cards and last_move is None: # 如果 last_move 是 None，任何有效出牌都行（除了PASS）
                result.append(group)


        return result

    def get_next_state(self, action: CardGroup) -> 'DouDizhuState':
        """
        获取执行动作后的下一个状态

        Args:
            action (CardGroup): 动作

        Returns:
            DouDizhuState: 下一个状态
        """
        # 复制当前状态
        next_hands = [hand.copy() for hand in self.hands]
        next_history = self.history.copy()
        next_played_cards = self.played_cards.copy()

        # 更新手牌
        if action.cards:  # 玩家出牌
            hand_before_remove = next_hands[self.current_player].copy() # 复制一份用于日志
            cards_in_action_for_log = action.cards.copy() # 复制动作牌用于日志

            # 使用 self.current_player, 因为这是调用 get_next_state 时的当前玩家
            player_for_debug_log = self.current_player

            logger.debug(f"P{player_for_debug_log} get_next_state: 手牌(移除前): {[str(c) for c in hand_before_remove]} (len={len(hand_before_remove)})")
            logger.debug(f"P{player_for_debug_log} get_next_state: 动作牌: {[str(c) for c in cards_in_action_for_log]}")
            
            successful_removals = 0
            # 首先检查所有牌是否都在手中 (基于值的比较，Card需要实现__eq__)
            temp_hand_for_check = next_hands[self.current_player].copy()
            all_action_cards_in_hand = True
            for card_to_check in action.cards:
                try:
                    temp_hand_for_check.remove(card_to_check)
                except ValueError:
                    all_action_cards_in_hand = False
                    logger.error(f"P{player_for_debug_log} get_next_state: 检查失败 - 动作牌 {str(card_to_check)} 不在临时手牌副本中。原始手牌: {[str(c) for c in next_hands[self.current_player]]}")
                    break # 一旦发现有牌不在，后续检查无意义
            
            if not all_action_cards_in_hand:
                # 这个错误非常严重，说明上游的合法动作生成或动作选择有问题
                raise ValueError(f"P{player_for_debug_log} 非法操作: 尝试打出不在手牌中的牌。动作: {[str(c) for c in action.cards]}, 当前手牌: {[str(c) for c in next_hands[self.current_player]]}")

            # 如果所有牌都在手中，则安全地移除
            for card_in_action in action.cards: # card_in_action 是一个 Card 对象
                try:
                    original_hand_len = len(next_hands[self.current_player])
                    next_hands[self.current_player].remove(card_in_action) # 修改的是 next_hands[self.current_player]
                    if len(next_hands[self.current_player]) < original_hand_len:
                        successful_removals += 1
                        logger.debug(f"P{player_for_debug_log} get_next_state: 成功移除 {str(card_in_action)}")
                    else:
                        # 这通常不应该发生，除非Card的__eq__有问题或列表中有多个完全相同的对象但remove行为异常
                        logger.warning(f"P{player_for_debug_log} get_next_state: remove({str(card_in_action)}) 但手牌长度未变! 当前手牌: {[str(c) for c in next_hands[self.current_player]]}")
                except ValueError:
                    # 这个ValueError理论上不应在此处触发，因为上面已经检查过了
                    # 但如果发生了，说明检查逻辑和移除逻辑之间存在不一致
                    logger.error(f"P{player_for_debug_log} get_next_state: 内部错误 - 尝试移除牌 {str(card_in_action)} 时发生ValueError，尽管它之前被认为在手牌中。手牌: {[str(c) for c in next_hands[self.current_player]]}")
                    # 可以考虑是否在此处抛出异常，或者让游戏继续但记录错误
            
            if successful_removals != len(action.cards):
                logger.error(f"P{player_for_debug_log} get_next_state: 动作牌数量 {len(action.cards)}, 成功移除数量 {successful_removals}! 这可能导致手牌不一致。")

            logger.debug(f"P{player_for_debug_log} get_next_state: 手牌(移除后): {[str(c) for c in next_hands[self.current_player]]} (len={len(next_hands[self.current_player])})")
            
            # 将打出的牌加入到 played_cards 列表
            for card_played in action.cards:
                next_played_cards.append(card_played) # 使用原始 action.cards 中的牌对象

        # 更新历史记录
        next_history.append((self.current_player, action))

        next_current_player: int # 类型提示
        next_last_move: Optional[CardGroup]
        next_last_player: Optional[int]
        next_num_passes: int

        if action.cards:  # 玩家实际出牌
            next_num_passes = 0
            next_last_move = action
            next_last_player = self.current_player
            next_current_player = (self.current_player + 1) % 3 # 轮到下一个玩家
        else:  # 玩家PASS
            next_num_passes = self.num_passes + 1
            next_last_move = self.last_move # PASS不改变上一手牌
            next_last_player = self.last_player # PASS不改变上一个出牌的玩家

            if next_num_passes >= 2: # 如果已经连续PASS了两次（包括当前玩家的PASS）
                # 出牌权回到上一个实际出牌的玩家 (next_last_player)
                # 此时 next_last_player 记录的是上一个出非PASS牌的玩家
                if next_last_player is None:
                    # 这种情况理论上不应发生（例如游戏开始地主必须出牌）
                    # 但作为防御，如果真的发生了，则按顺序轮转
                    next_current_player = (self.current_player + 1) % 3
                else:
                    next_current_player = next_last_player
                next_num_passes = 0 # 重置PASS计数，新一轮开始
                next_last_move = None # 新一轮出牌，不再受上一轮牌型约束
            else: # 还没有连续PASS两次，正常轮转
                next_current_player = (self.current_player + 1) % 3

        # 游戏结束的判断应该在状态生成之后，或者在主循环中进行
        # is_terminal 和 get_payoffs 会处理

        return DouDizhuState(
            hands=next_hands,
            landlord_cards=self.landlord_cards,
            landlord=self.landlord,
            current_player=next_current_player, # 使用新计算的当前玩家
            game_phase=self.game_phase,
            bid_history=self.bid_history,
            grab_history=self.grab_history,
            highest_bidder=self.highest_bidder,
            highest_bid=self.highest_bid,
            last_move=next_last_move, # 使用新计算的上一手牌
            last_player=next_last_player, # 使用新计算的上一个出牌玩家
            num_passes=next_num_passes, # 使用新计算的PASS次数
            history=next_history,
            played_cards=next_played_cards,
            infoset=self.infoset  # 确保传递infoset
        )

    def is_terminal(self) -> bool:
        """
        判断游戏是否结束

        Returns:
            bool: 是否结束
        """
        # 有玩家出完牌，游戏结束
        for hand in self.hands:
            if not hand:
                return True

        return False

    def get_payoffs(self) -> List[float]:
        """
        获取各玩家的收益

        Returns:
            List[float]: 各玩家的收益
        """
        # 如果游戏未结束，返回全0
        if not self.is_terminal():
            return [0.0, 0.0, 0.0]

        # 找出获胜的玩家
        winner = -1
        for i, hand in enumerate(self.hands):
            if not hand:
                winner = i
                break

        # 计算收益
        payoffs = [0.0, 0.0, 0.0]

        # 地主获胜
        if winner == self.landlord:
            payoffs[self.landlord] = 2.0
            payoffs[(self.landlord + 1) % 3] = -1.0
            payoffs[(self.landlord + 2) % 3] = -1.0
        # 农民获胜
        else:
            payoffs[self.landlord] = -2.0
            payoffs[(self.landlord + 1) % 3] = 1.0
            payoffs[(self.landlord + 2) % 3] = 1.0

        return payoffs

    def get_observation(self, player_id: Optional[int] = None) -> np.ndarray:
        """
        获取观察

        Args:
            player_id (Optional[int], optional): 玩家ID. Defaults to None.

        Returns:
            np.ndarray: 观察
        """
        if player_id is None:
            player_id = self.current_player

        # 构建观察特征
        # 1. 自己的手牌（15维独热编码 * 20张牌 = 300维）
        # 2. 其他玩家的手牌数量（2维）
        # 3. 上一手牌（15维独热编码 * 20张牌 = 300维）
        # 4. 已出的牌（15维）
        # 5. 地主标识（3维）
        # 6. 当前玩家标识（3维）
        # 7. 上一个出牌的玩家标识（3维）
        # 8. 连续不出的次数（1维）
        # 9. 游戏阶段（4维独热编码）
        # 10. 叫分历史（3 * 4 = 12维，每个玩家的叫分情况）
        # 11. 抢地主历史（3 * 2 = 6维，每个玩家的抢地主情况）
        # 12. 最高叫分（4维独热编码）
        # 13. 最高叫分玩家（3维独热编码）
        # 总共656维

        # 1. 自己的手牌
        hand_feature = np.zeros(300)
        for i, card in enumerate(self.hands[player_id][:20]):  # 最多考虑20张牌
            card_feature = np.zeros(15)
            card_feature[int(card.rank)] = 1
            hand_feature[i*15:(i+1)*15] = card_feature

        # 2. 其他玩家的手牌数量
        other_hands_feature = np.array([
            len(self.hands[(player_id + 1) % 3]),
            len(self.hands[(player_id + 2) % 3])
        ])

        # 3. 上一手牌
        last_move_feature = np.zeros(300)
        if self.last_move and self.last_move.cards:
            for i, card in enumerate(self.last_move.cards[:20]):  # 最多考虑20张牌
                card_feature = np.zeros(15)
                card_feature[int(card.rank)] = 1
                last_move_feature[i*15:(i+1)*15] = card_feature

        # 4. 已出的牌
        played_cards_feature = np.zeros(15)
        for card in self.played_cards:
            played_cards_feature[int(card.rank)] += 1

        # 5. 地主标识
        landlord_feature = np.zeros(3)
        if self.landlord is not None:
            landlord_feature[(self.landlord - player_id) % 3] = 1

        # 6. 当前玩家标识
        current_player_feature = np.zeros(3)
        current_player_feature[(self.current_player - player_id) % 3] = 1

        # 7. 上一个出牌的玩家标识
        last_player_feature = np.zeros(3)
        if self.last_player is not None:
            last_player_feature[(self.last_player - player_id) % 3] = 1

        # 8. 连续不出的次数
        num_passes_feature = np.array([self.num_passes])

        # 9. 游戏阶段（4维独热编码）
        game_phase_feature = np.zeros(4)
        game_phase_feature[self.game_phase.value] = 1

        # 10. 叫分历史（3 * 4 = 12维，每个玩家的叫分情况）
        bid_history_feature = np.zeros(12)
        for player, bid in self.bid_history:
            # 相对于当前玩家的位置
            relative_player = (player - player_id) % 3
            bid_history_feature[relative_player * 4 + bid] = 1

        # 11. 抢地主历史（3 * 2 = 6维，每个玩家的抢地主情况）
        grab_history_feature = np.zeros(6)
        for player, grab in self.grab_history:
            # 相对于当前玩家的位置
            relative_player = (player - player_id) % 3
            grab_history_feature[relative_player * 2 + (1 if grab else 0)] = 1

        # 12. 最高叫分（4维独热编码）
        highest_bid_feature = np.zeros(4)
        highest_bid_feature[self.highest_bid] = 1

        # 13. 最高叫分玩家（3维独热编码）
        highest_bidder_feature = np.zeros(3)
        if self.highest_bidder is not None:
            highest_bidder_feature[(self.highest_bidder - player_id) % 3] = 1

        # 合并特征
        observation = np.concatenate([
            hand_feature,
            other_hands_feature,
            last_move_feature,
            played_cards_feature,
            landlord_feature,
            current_player_feature,
            last_player_feature,
            num_passes_feature,
            game_phase_feature,
            bid_history_feature,
            grab_history_feature,
            highest_bid_feature,
            highest_bidder_feature
        ])

        return observation

    def to_dict(self) -> Dict[str, Any]:
        """
        将状态转换为字典表示

        Returns:
            Dict[str, Any]: 状态的字典表示
        """
        return {
            'hands': [[card.to_dict() for card in hand] for hand in self.hands],
            'landlord_cards': [card.to_dict() for card in self.landlord_cards],
            'landlord': self.landlord,
            'current_player': self.current_player,
            'game_phase': self.game_phase.value,
            'bid_history': self.bid_history,
            'grab_history': self.grab_history,
            'highest_bidder': self.highest_bidder,
            'highest_bid': self.highest_bid,
            'last_move': self.last_move.to_dict() if self.last_move else None,
            'last_player': self.last_player,
            'num_passes': self.num_passes,
            'history': [(player, move.to_dict()) for player, move in self.history],
            'played_cards': [card.to_dict() for card in self.played_cards],
            'infoset': self.infoset
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DouDizhuState':
        """
        从字典创建状态

        Args:
            data (Dict[str, Any]): 状态的字典表示

        Returns:
            DouDizhuState: 状态对象
        """
        hands = [[Card.from_dict(card_data) for card_data in hand] for hand in data['hands']]
        landlord_cards = [Card.from_dict(card_data) for card_data in data['landlord_cards']]
        last_move = CardGroup.from_dict(data['last_move']) if data['last_move'] else None
        history = [(player, CardGroup.from_dict(move_data)) for player, move_data in data['history']]
        played_cards = [Card.from_dict(card_data) for card_data in data['played_cards']]

        return cls(
            hands=hands,
            landlord_cards=landlord_cards,
            landlord=data['landlord'],
            current_player=data['current_player'],
            game_phase=GamePhase(data['game_phase']),
            bid_history=data['bid_history'],
            grab_history=data['grab_history'],
            highest_bidder=data['highest_bidder'],
            highest_bid=data['highest_bid'],
            last_move=last_move,
            last_player=data['last_player'],
            num_passes=data['num_passes'],
            history=history,
            played_cards=played_cards,
            infoset=data['infoset']
        )

    def __str__(self) -> str:
        """
        转换为字符串表示

        Returns:
            str: 字符串表示
        """
        result = []
        result.append(f"游戏阶段: {self.game_phase.name}")

        if self.game_phase == GamePhase.BIDDING:
            result.append(f"叫分历史: {self.bid_history}")
            result.append(f"最高叫分: {self.highest_bid} (玩家{self.highest_bidder if self.highest_bidder is not None else 'None'})")
        elif self.game_phase == GamePhase.GRABBING:
            result.append(f"叫分历史: {self.bid_history}")
            result.append(f"最高叫分: {self.highest_bid} (玩家{self.highest_bidder if self.highest_bidder is not None else 'None'})")
            result.append(f"抢地主历史: {self.grab_history}")

        if self.landlord is not None:
            result.append(f"地主: 玩家{self.landlord}")
        else:
            result.append("地主: 未确定")

        result.append(f"当前玩家: 玩家{self.current_player}")

        for i, hand in enumerate(self.hands):
            result.append(f"玩家{i}的手牌: {' '.join(str(card) for card in hand)}")

        if self.game_phase != GamePhase.PLAYING:
            result.append(f"底牌: {' '.join(str(card) for card in self.landlord_cards)}")

        if self.last_move:
            result.append(f"上一手牌: {self.last_move}")

        if self.last_player is not None:
            result.append(f"上一个出牌的玩家: 玩家{self.last_player}")

        result.append(f"连续不出的次数: {self.num_passes}")

        return "\n".join(result)
