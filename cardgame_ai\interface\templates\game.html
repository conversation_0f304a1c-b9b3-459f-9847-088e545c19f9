<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI棋牌强化学习框架 - 斗地主游戏</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/game.css') }}">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light" data-theme="{{ theme }}" data-game-id="{{ game_id }}" data-card-style="{{ card_style }}" data-animation-speed="{{ animation_speed }}" data-sound-enabled="{{ sound_enabled }}">
    <div class="game-container">
        <!-- 游戏头部信息 -->
        <header class="game-header">
            <div class="row align-items-center">
                <div class="col-4">
                    <h2 class="game-title">斗地主</h2>
                </div>
                <div class="col-4 text-center">
                    <div id="game-status" class="game-status">等待游戏开始...</div>
                </div>
                <div class="col-4 text-end">
                    <div class="btn-group">
                        <button id="restart-btn" class="btn btn-warning">
                            <i class="fas fa-redo"></i> 重新开始
                        </button>
                        <button id="exit-btn" class="btn btn-danger">
                            <i class="fas fa-home"></i> 返回首页
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- 游戏主体区域 -->
        <div class="game-main">
            <!-- AI玩家1区域 -->
            <div class="player-area ai-player" id="player-farmer1">
                <div class="player-info">
                    <span class="player-name">农民1</span>
                    <span class="player-role farmer">农民</span>
                    <span class="card-count">17张</span>
                </div>
                <div class="card-container ai-cards hidden-cards"></div>
                <div class="play-area"></div>
            </div>

            <!-- 中央游戏区域 -->
            <div class="central-area">
                <!-- 地主底牌区域 -->
                <div class="landlord-cards-container">
                    <div class="landlord-cards"></div>
                    <div class="landlord-label">地主牌</div>
                </div>

                <!-- 出牌历史和游戏信息 -->
                <div class="game-info-container">
                    <div id="game-info" class="game-info"></div>
                    <div id="play-history" class="play-history"></div>
                </div>
            </div>

            <!-- AI玩家2区域 -->
            <div class="player-area ai-player" id="player-farmer2">
                <div class="player-info">
                    <span class="player-name">农民2</span>
                    <span class="player-role farmer">农民</span>
                    <span class="card-count">17张</span>
                </div>
                <div class="card-container ai-cards hidden-cards"></div>
                <div class="play-area"></div>
            </div>

            <!-- 人类玩家区域 -->
            <div class="player-area human-player" id="player-human">
                <div class="player-info">
                    <span class="player-name">我</span>
                    <span class="player-role landlord">地主</span>
                    <span class="card-count">20张</span>
                </div>
                <div class="card-container human-cards"></div>
                <div class="action-buttons">
                    <button id="play-btn" class="btn btn-success action-btn" disabled>出牌</button>
                    <button id="pass-btn" class="btn btn-secondary action-btn" disabled>不出</button>
                    <button id="hint-btn" class="btn btn-info action-btn">提示</button>
                </div>
            </div>
        </div>

        <!-- 游戏结束弹窗 -->
        <div class="modal fade" id="game-over-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">游戏结束</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="result-message"></div>
                        <div class="game-stats mt-3">
                            <h6>游戏统计</h6>
                            <ul class="list-group">
                                <li class="list-group-item">出牌回合：<span id="turns-count">0</span></li>
                                <li class="list-group-item">游戏时长：<span id="game-duration">0</span></li>
                            </ul>
                        </div>
                        <div class="feedback-form mt-3">
                            <h6>游戏反馈</h6>
                            <div class="mb-3">
                                <label for="ai-rating" class="form-label">AI表现评分</label>
                                <select class="form-select" id="ai-rating">
                                    <option value="5">非常出色</option>
                                    <option value="4">出色</option>
                                    <option value="3" selected>一般</option>
                                    <option value="2">较差</option>
                                    <option value="1">很差</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="feedback-text" class="form-label">附加反馈</label>
                                <textarea class="form-control" id="feedback-text" rows="3" placeholder="请输入您对AI表现的评价或建议..."></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" id="submit-feedback-btn">提交反馈</button>
                        <button type="button" class="btn btn-success" id="play-again-btn">再玩一局</button>
                        <button type="button" class="btn btn-secondary" id="back-to-home-btn">返回首页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="{{ url_for('static', filename='js/visualization.js') }}"></script>
    <script src="{{ url_for('static', filename='js/game.js') }}"></script>

    <script>
        // 初始化可视化组件
        document.addEventListener('DOMContentLoaded', function() {
            if (window.AIVisualization) {
                window.AIVisualization.init();
            }
        });
    </script>
</body>
</html>