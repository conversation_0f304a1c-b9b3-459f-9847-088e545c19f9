"""
经验回放缓冲区模块

提供经验回放缓冲区的实现，包括标准回放缓冲区和优先级经验回放缓冲区。
"""
import random
import numpy as np
from typing import List, Tuple, Dict, Any, Optional, Union
from collections import deque

from cardgame_ai.core.base import Experience, Batch


class ReplayBuffer:
    """
    经验回放缓冲区
    
    存储和采样经验数据，用于离线训练。
    """
    
    def __init__(self, capacity: int):
        """
        初始化经验回放缓冲区
        
        Args:
            capacity (int): 缓冲区容量
        """
        self.capacity = capacity
        self.buffer = deque(maxlen=capacity)
        self.position = 0
    
    def add(self, experience: Experience) -> None:
        """
        添加经验
        
        Args:
            experience (Experience): 经验数据
        """
        self.buffer.append(experience)
    
    def sample(self, batch_size: int) -> Batch:
        """
        采样经验批次
        
        Args:
            batch_size (int): 批次大小
            
        Returns:
            Batch: 经验批次
        """
        # 确保缓冲区中有足够的经验
        if batch_size > len(self.buffer):
            batch_size = len(self.buffer)
        
        # 随机采样
        experiences = random.sample(self.buffer, batch_size)
        
        # 创建批次
        return Batch(experiences)
    
    def clear(self) -> None:
        """
        清空缓冲区
        """
        self.buffer.clear()
    
    def __len__(self) -> int:
        """
        获取缓冲区大小
        
        Returns:
            int: 缓冲区大小
        """
        return len(self.buffer)
    
    def __str__(self) -> str:
        """
        转换为字符串表示
        
        Returns:
            str: 字符串表示
        """
        return f"ReplayBuffer(capacity={self.capacity}, size={len(self)})"
    
    def __repr__(self) -> str:
        """
        转换为详细字符串表示
        
        Returns:
            str: 详细字符串表示
        """
        return self.__str__()


class PrioritizedReplayBuffer(ReplayBuffer):
    """
    优先级经验回放缓冲区
    
    基于TD误差的优先级经验回放，优先采样高TD误差的经验。
    """
    
    def __init__(self, capacity: int, alpha: float = 0.6, beta: float = 0.4, beta_increment: float = 0.001, epsilon: float = 1e-6):
        """
        初始化优先级经验回放缓冲区
        
        Args:
            capacity (int): 缓冲区容量
            alpha (float, optional): 优先级指数，控制采样概率与优先级的关系. Defaults to 0.6.
            beta (float, optional): 重要性采样指数，用于修正优先级采样的偏差. Defaults to 0.4.
            beta_increment (float, optional): beta的增量，随着训练进行逐渐增加beta. Defaults to 0.001.
            epsilon (float, optional): 小常数，防止优先级为0. Defaults to 1e-6.
        """
        super().__init__(capacity)
        self.alpha = alpha
        self.beta = beta
        self.beta_increment = beta_increment
        self.epsilon = epsilon
        self.priorities = np.zeros((capacity,), dtype=np.float32)
        self.max_priority = 1.0
    
    def add(self, experience: Experience) -> None:
        """
        添加经验
        
        Args:
            experience (Experience): 经验数据
        """
        # 获取当前位置
        idx = self.position
        
        # 添加经验
        if len(self.buffer) < self.capacity:
            self.buffer.append(experience)
        else:
            self.buffer[idx] = experience
        
        # 设置最大优先级
        self.priorities[idx] = self.max_priority
        
        # 更新位置
        self.position = (self.position + 1) % self.capacity
    
    def sample(self, batch_size: int) -> Tuple[Batch, np.ndarray, np.ndarray]:
        """
        采样经验批次
        
        Args:
            batch_size (int): 批次大小
            
        Returns:
            Tuple[Batch, np.ndarray, np.ndarray]: 经验批次、索引和重要性权重
        """
        # 确保缓冲区中有足够的经验
        if batch_size > len(self.buffer):
            batch_size = len(self.buffer)
        
        # 计算采样概率
        priorities = self.priorities[:len(self.buffer)]
        probabilities = priorities ** self.alpha
        probabilities /= probabilities.sum()
        
        # 采样索引
        indices = np.random.choice(len(self.buffer), batch_size, replace=False, p=probabilities)
        
        # 计算重要性权重
        weights = (len(self.buffer) * probabilities[indices]) ** (-self.beta)
        weights /= weights.max()
        
        # 增加beta
        self.beta = min(1.0, self.beta + self.beta_increment)
        
        # 获取经验
        experiences = [self.buffer[idx] for idx in indices]
        
        # 创建批次
        return Batch(experiences), indices, weights
    
    def update_priorities(self, indices: np.ndarray, priorities: np.ndarray) -> None:
        """
        更新优先级
        
        Args:
            indices (np.ndarray): 索引
            priorities (np.ndarray): 优先级
        """
        for idx, priority in zip(indices, priorities):
            # 添加小常数，防止优先级为0
            priority = abs(priority) + self.epsilon
            
            # 更新优先级
            self.priorities[idx] = priority
            
            # 更新最大优先级
            self.max_priority = max(self.max_priority, priority)
    
    def __str__(self) -> str:
        """
        转换为字符串表示
        
        Returns:
            str: 字符串表示
        """
        return f"PrioritizedReplayBuffer(capacity={self.capacity}, size={len(self)}, alpha={self.alpha}, beta={self.beta})"
