"""
游戏配置系统模块

提供灵活的游戏参数配置功能，允许不同游戏使用统一的配置接口，
并支持从文件加载、保存配置以及动态修改配置项。
"""
from typing import Dict, Any, List, Tuple, Optional, Union, Type
import os
import json
import yaml
import copy
from abc import ABC, abstractmethod


class ConfigurationError(Exception):
    """配置错误异常，当配置参数无效时抛出"""
    pass


class GameConfig:
    """
    游戏配置类
    
    用于管理游戏的配置参数
    """
    
    def __init__(self, config_data: Dict[str, Any] = None):
        """
        初始化游戏配置
        
        Args:
            config_data (Dict[str, Any], optional): 初始配置数据. Defaults to None.
        """
        # 基础配置
        self._base_config = {
            "game": {
                "name": "未命名游戏",
                "version": "1.0.0",
                "description": "游戏描述",
                "max_players": 4,
                "min_players": 2,
                "default_players": 3
            },
            "rules": {
                "turn_based": True,
                "random_first_player": True,
                "allow_pass": False,
                "max_turns": 100,
                "win_condition": "points"
            },
            "ui": {
                "card_size": "medium",
                "animation_speed": "normal",
                "theme": "default",
                "sound_enabled": True,
                "language": "zh_CN"
            },
            "ai": {
                "difficulty": "medium",
                "search_depth": 3,
                "use_neural_network": True,
                "model_path": "",
                "thinking_time": 1.0
            },
            "logging": {
                "enabled": True,
                "level": "info",
                "save_games": True,
                "log_directory": "./logs"
            }
        }
        
        # 实际配置，会覆盖基础配置
        self._config = copy.deepcopy(self._base_config)
        
        # 如果提供了配置数据，则更新配置
        if config_data:
            self.update(config_data)
        
        # 验证配置
        self.validate()
    
    def __getitem__(self, key: str) -> Any:
        """
        获取配置项
        
        Args:
            key (str): 配置项的键
            
        Returns:
            Any: 配置项的值
            
        Raises:
            KeyError: 如果配置项不存在
        """
        if key in self._config:
            return self._config[key]
        raise KeyError(f"配置项 '{key}' 不存在")
    
    def __setitem__(self, key: str, value: Any) -> None:
        """
        设置配置项
        
        Args:
            key (str): 配置项的键
            value (Any): 配置项的值
        """
        self._config[key] = value
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置项，如果不存在则返回默认值
        
        Args:
            key (str): 配置项的键
            default (Any, optional): 默认值. Defaults to None.
            
        Returns:
            Any: 配置项的值，如果不存在则返回默认值
        """
        keys = key.split('.')
        config = self._config
        
        for k in keys[:-1]:
            if k not in config:
                return default
            config = config[k]
        
        if keys[-1] not in config:
            return default
            
        return config[keys[-1]]
    
    def set(self, key: str, value: Any) -> None:
        """
        设置配置项
        
        Args:
            key (str): 配置项的键，可以是点分隔的路径
            value (Any): 配置项的值
            
        Raises:
            ConfigurationError: 如果配置项路径无效
        """
        keys = key.split('.')
        config = self._config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            if not isinstance(config[k], dict):
                raise ConfigurationError(f"配置路径 '{key}' 无效，'{k}' 不是一个字典")
            config = config[k]
        
        config[keys[-1]] = value
    
    def update(self, config_data: Dict[str, Any]) -> None:
        """
        更新配置
        
        Args:
            config_data (Dict[str, Any]): 新的配置数据
        """
        self._recursive_update(self._config, config_data)
    
    def _recursive_update(self, d1: Dict[str, Any], d2: Dict[str, Any]) -> None:
        """
        递归更新字典
        
        Args:
            d1 (Dict[str, Any]): 待更新的字典
            d2 (Dict[str, Any]): 新的数据
        """
        for k, v in d2.items():
            if k in d1 and isinstance(d1[k], dict) and isinstance(v, dict):
                self._recursive_update(d1[k], v)
            else:
                d1[k] = v
    
    def validate(self) -> bool:
        """
        验证配置是否有效
        
        Returns:
            bool: 如果配置有效则返回True
            
        Raises:
            ConfigurationError: 如果配置无效
        """
        # 验证必需的配置项
        required_keys = ['game', 'rules']
        for key in required_keys:
            if key not in self._config:
                raise ConfigurationError(f"缺少必需的配置项 '{key}'")
        
        # 验证游戏配置
        game_config = self._config.get('game', {})
        if not isinstance(game_config, dict):
            raise ConfigurationError("游戏配置必须是一个字典")
        
        # 验证玩家数量
        min_players = game_config.get('min_players', 2)
        max_players = game_config.get('max_players', 4)
        default_players = game_config.get('default_players', 3)
        
        if min_players < 1:
            raise ConfigurationError("最小玩家数不能小于1")
        if max_players < min_players:
            raise ConfigurationError("最大玩家数不能小于最小玩家数")
        if default_players < min_players or default_players > max_players:
            raise ConfigurationError("默认玩家数必须在最小和最大玩家数之间")
        
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典
        
        Returns:
            Dict[str, Any]: 配置字典
        """
        return copy.deepcopy(self._config)
    
    def save(self, path: str, format: str = 'json') -> None:
        """
        保存配置到文件
        
        Args:
            path (str): 文件路径
            format (str, optional): 文件格式，支持'json'和'yaml'. Defaults to 'json'.
            
        Raises:
            ValueError: 如果文件格式不支持
        """
        # 创建目录
        dir_path = os.path.dirname(os.path.abspath(path))
        os.makedirs(dir_path, exist_ok=True)
        
        # 保存配置
        if format.lower() == 'json':
            with open(path, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=4, ensure_ascii=False)
        elif format.lower() == 'yaml':
            with open(path, 'w', encoding='utf-8') as f:
                yaml.dump(self._config, f, default_flow_style=False, allow_unicode=True)
        else:
            raise ValueError(f"不支持的文件格式：{format}")
    
    @classmethod
    def load(cls, path: str) -> 'GameConfig':
        """
        从文件加载配置
        
        Args:
            path (str): 文件路径
            
        Returns:
            GameConfig: 游戏配置对象
            
        Raises:
            FileNotFoundError: 如果文件不存在
            ValueError: 如果文件格式不支持
        """
        if not os.path.exists(path):
            raise FileNotFoundError(f"配置文件不存在：{path}")
        
        ext = os.path.splitext(path)[1].lower()
        
        if ext == '.json':
            with open(path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
        elif ext in ['.yml', '.yaml']:
            with open(path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
        else:
            raise ValueError(f"不支持的文件格式：{ext}")
        
        return cls(config_data)
    
    @classmethod
    def create_template(cls, game_type: str = 'generic') -> 'GameConfig':
        """
        创建配置模板
        
        Args:
            game_type (str, optional): 游戏类型，支持'generic'、'card'、'board'和'mahjong'. 
                                       Defaults to 'generic'.
            
        Returns:
            GameConfig: 游戏配置对象
            
        Raises:
            ValueError: 如果游戏类型不支持
        """
        config = cls()
        
        if game_type == 'generic':
            # 通用配置，使用默认值
            pass
        elif game_type == 'card':
            # 纸牌游戏配置
            config.update({
                "game": {
                    "name": "纸牌游戏",
                    "description": "纸牌游戏通用配置"
                },
                "rules": {
                    "deck_type": "standard",
                    "num_decks": 1,
                    "include_jokers": False,
                    "deal_cards_count": 13,
                    "allow_draw": True,
                    "allow_discard": True
                },
                "card_values": {
                    "A": 14,
                    "K": 13,
                    "Q": 12,
                    "J": 11,
                    "10": 10,
                    "9": 9,
                    "8": 8,
                    "7": 7,
                    "6": 6,
                    "5": 5,
                    "4": 4,
                    "3": 3,
                    "2": 2
                }
            })
        elif game_type == 'board':
            # 棋盘游戏配置
            config.update({
                "game": {
                    "name": "棋盘游戏",
                    "description": "棋盘游戏通用配置"
                },
                "rules": {
                    "board_size": [8, 8],
                    "starting_position": "default",
                    "move_rules": "standard",
                    "capture_rules": "standard"
                },
                "piece_values": {
                    "pawn": 1,
                    "knight": 3,
                    "bishop": 3,
                    "rook": 5,
                    "queen": 9,
                    "king": 0
                }
            })
        elif game_type == 'mahjong':
            # 麻将游戏配置
            config.update({
                "game": {
                    "name": "麻将",
                    "description": "麻将游戏通用配置",
                    "default_players": 4,
                    "min_players": 4,
                    "max_players": 4
                },
                "rules": {
                    "style": "chinese",
                    "points_system": "standard",
                    "allow_kong": True,
                    "allow_chow": True,
                    "allow_pong": True,
                    "wind_rounds": True,
                    "flower_tiles": True
                },
                "tile_values": {
                    "dragon": 2,
                    "wind": 1.5,
                    "terminal": 1.5,
                    "simple": 1
                }
            })
        else:
            raise ValueError(f"不支持的游戏类型：{game_type}")
        
        return config


class GameConfigRegistry:
    """
    游戏配置注册表
    
    用于管理和访问不同游戏的配置
    """
    
    _configs: Dict[str, GameConfig] = {}
    
    @classmethod
    def register(cls, game_name: str, config: GameConfig) -> None:
        """
        注册游戏配置
        
        Args:
            game_name (str): 游戏名称
            config (GameConfig): 游戏配置
        """
        cls._configs[game_name] = config
    
    @classmethod
    def get(cls, game_name: str) -> Optional[GameConfig]:
        """
        获取游戏配置
        
        Args:
            game_name (str): 游戏名称
            
        Returns:
            Optional[GameConfig]: 游戏配置，如果不存在则返回None
        """
        return cls._configs.get(game_name)
    
    @classmethod
    def get_all(cls) -> Dict[str, GameConfig]:
        """
        获取所有游戏配置
        
        Returns:
            Dict[str, GameConfig]: 游戏配置字典
        """
        return cls._configs.copy()
    
    @classmethod
    def load_configs(cls, directory: str) -> int:
        """
        从目录加载所有配置文件
        
        Args:
            directory (str): 配置文件目录
            
        Returns:
            int: 加载的配置文件数量
        """
        count = 0
        
        if not os.path.exists(directory):
            return count
        
        for filename in os.listdir(directory):
            if filename.endswith(('.json', '.yml', '.yaml')):
                try:
                    path = os.path.join(directory, filename)
                    config = GameConfig.load(path)
                    game_name = config.get('game.name')
                    if game_name:
                        cls.register(game_name, config)
                        count += 1
                except Exception as e:
                    print(f"加载配置文件 {filename} 失败: {e}")
        
        return count
    
    @classmethod
    def save_configs(cls, directory: str, format: str = 'json') -> int:
        """
        保存所有配置到目录
        
        Args:
            directory (str): 配置文件目录
            format (str, optional): 文件格式. Defaults to 'json'.
            
        Returns:
            int: 保存的配置文件数量
        """
        count = 0
        
        # 创建目录
        os.makedirs(directory, exist_ok=True)
        
        for game_name, config in cls._configs.items():
            try:
                filename = f"{game_name.lower().replace(' ', '_')}.{format}"
                path = os.path.join(directory, filename)
                config.save(path, format)
                count += 1
            except Exception as e:
                print(f"保存配置 {game_name} 失败: {e}")
        
        return count


class ConfigurableGame(ABC):
    """
    可配置游戏接口
    
    定义了可配置游戏应该实现的方法
    """
    
    @abstractmethod
    def get_config(self) -> GameConfig:
        """
        获取游戏配置
        
        Returns:
            GameConfig: 游戏配置
        """
        pass
    
    @abstractmethod
    def apply_config(self, config: GameConfig) -> None:
        """
        应用游戏配置
        
        Args:
            config (GameConfig): 游戏配置
        """
        pass
    
    @abstractmethod
    def validate_config(self, config: GameConfig) -> bool:
        """
        验证游戏配置是否有效
        
        Args:
            config (GameConfig): 游戏配置
            
        Returns:
            bool: 如果配置有效则返回True
        """
        pass 