"""
基于GAN的策略生成器

使用生成对抗网络（GAN）生成多样化且具有挑战性的对手策略。
"""

import os
import time
import logging
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from typing import Dict, List, Tuple, Any, Optional, Union, Callable

from cardgame_ai.core.agent import Agent
from cardgame_ai.core.state import State
from cardgame_ai.core.policy import Policy
from cardgame_ai.utils.model_saver import ModelSaver

# 配置日志
logger = logging.getLogger(__name__)


class Generator(nn.Module):
    """
    生成器网络

    生成策略参数或轨迹。
    """

    def __init__(
        self,
        latent_dim: int,
        output_dim: int,
        condition_dim: int = 0,
        hidden_dims: List[int] = [256, 512, 256],
        activation: nn.Module = nn.LeakyReLU(0.2),
        output_activation: nn.Module = nn.Tanh(),
        use_batch_norm: bool = True,
        dropout: float = 0.1
    ):
        """
        初始化生成器网络

        Args:
            latent_dim: 潜在空间维度
            output_dim: 输出维度（策略参数或轨迹表示）
            condition_dim: 条件变量维度，用于条件式生成
            hidden_dims: 隐藏层维度列表
            activation: 激活函数
            output_activation: 输出层激活函数
            use_batch_norm: 是否使用批归一化
            dropout: Dropout比率
        """
        super().__init__()

        self.latent_dim = latent_dim
        self.output_dim = output_dim
        self.condition_dim = condition_dim

        # 构建网络层
        layers = []

        # 输入层（合并潜在向量和条件变量）
        input_dim = latent_dim + condition_dim
        layers.append(nn.Linear(input_dim, hidden_dims[0]))
        if use_batch_norm:
            layers.append(nn.BatchNorm1d(hidden_dims[0]))
        layers.append(activation)
        layers.append(nn.Dropout(dropout))

        # 隐藏层
        for i in range(len(hidden_dims) - 1):
            layers.append(nn.Linear(hidden_dims[i], hidden_dims[i + 1]))
            if use_batch_norm:
                layers.append(nn.BatchNorm1d(hidden_dims[i + 1]))
            layers.append(activation)
            layers.append(nn.Dropout(dropout))

        # 输出层
        layers.append(nn.Linear(hidden_dims[-1], output_dim))
        layers.append(output_activation)

        self.model = nn.Sequential(*layers)

    def forward(self, z: torch.Tensor, condition: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        前向传播

        Args:
            z: 潜在向量
            condition: 条件变量，可选

        Returns:
            生成的策略参数或轨迹表示
        """
        # 合并条件变量（如果有）
        if condition is not None and self.condition_dim > 0:
            # 确保条件维度正确
            if condition.dim() == 1:
                condition = condition.unsqueeze(0)  # 单个条件扩展为批次
            z = torch.cat([z, condition], dim=1)
        return self.model(z)


class Discriminator(nn.Module):
    """
    判别器网络

    区分生成的策略/轨迹与真实的策略/轨迹。
    """

    def __init__(
        self,
        input_dim: int,
        condition_dim: int = 0,
        hidden_dims: List[int] = [256, 128, 64],
        activation: nn.Module = nn.LeakyReLU(0.2),
        use_batch_norm: bool = True,
        dropout: float = 0.1,
        use_spectral_norm: bool = True
    ):
        """
        初始化判别器网络

        Args:
            input_dim: 输入维度（策略参数或轨迹表示）
            condition_dim: 条件变量维度，用于条件式判别
            hidden_dims: 隐藏层维度列表
            activation: 激活函数
            use_batch_norm: 是否使用批归一化
            dropout: Dropout比率
            use_spectral_norm: 是否使用谱归一化（提高GAN稳定性）
        """
        super().__init__()

        self.input_dim = input_dim
        self.condition_dim = condition_dim

        # 计算总输入维度（包含条件）
        total_input_dim = input_dim + condition_dim

        # 构建网络层
        layers = []

        # 输入层
        if use_spectral_norm:
            layers.append(nn.utils.spectral_norm(nn.Linear(total_input_dim, hidden_dims[0])))
        else:
            layers.append(nn.Linear(total_input_dim, hidden_dims[0]))

        if use_batch_norm:
            layers.append(nn.BatchNorm1d(hidden_dims[0]))

        layers.append(activation)
        layers.append(nn.Dropout(dropout))

        # 隐藏层
        for i in range(len(hidden_dims) - 1):
            if use_spectral_norm:
                layers.append(nn.utils.spectral_norm(nn.Linear(hidden_dims[i], hidden_dims[i + 1])))
            else:
                layers.append(nn.Linear(hidden_dims[i], hidden_dims[i + 1]))

            if use_batch_norm:
                layers.append(nn.BatchNorm1d(hidden_dims[i + 1]))

            layers.append(activation)
            layers.append(nn.Dropout(dropout))

        # 输出层
        if use_spectral_norm:
            layers.append(nn.utils.spectral_norm(nn.Linear(hidden_dims[-1], 1)))
        else:
            layers.append(nn.Linear(hidden_dims[-1], 1))

        self.model = nn.Sequential(*layers)

    def forward(self, x: torch.Tensor, condition: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        前向传播

        Args:
            x: 策略参数或轨迹表示
            condition: 条件变量，可选

        Returns:
            真实性得分
        """
        # 合并条件变量（如果有）
        if condition is not None and self.condition_dim > 0:
            # 确保条件维度正确
            if condition.dim() == 1:
                condition = condition.unsqueeze(0)  # 单个条件扩展为批次
            if condition.size(0) == 1 and x.size(0) > 1:
                condition = condition.expand(x.size(0), -1)  # 广播条件到批次大小
            x = torch.cat([x, condition], dim=1)
        return self.model(x)


class AdversarialOpponentGenerator:
    """
    对抗性对手生成器

    使用生成对抗网络生成多样化且具有挑战性的对手策略，支持条件式生成和难度自适应。
    """

    def __init__(
        self,
        policy_dim: int,
        latent_dim: int = 64,
        condition_dim: int = 10,
        generator_hidden_dims: List[int] = [256, 512, 256],
        discriminator_hidden_dims: List[int] = [256, 128, 64],
        policy_encoder: Optional[nn.Module] = None,
        policy_decoder: Optional[nn.Module] = None,
        device: str = None,
        learning_rate: float = 0.0002,
        beta1: float = 0.5,
        beta2: float = 0.999,
        use_wgan_gp: bool = True,
        gp_weight: float = 10.0,
        n_critic: int = 5
    ):
        """
        初始化对抗性对手生成器

        Args:
            policy_dim: 策略维度
            latent_dim: 潜在空间维度
            condition_dim: A条件变量维度，用于控制生成策略的特性（如强度、风格等）
            generator_hidden_dims: 生成器隐藏层维度列表
            discriminator_hidden_dims: 判别器隐藏层维度列表
            policy_encoder: 策略编码器，将策略转换为向量表示
            policy_decoder: 策略解码器，将向量表示转换为策略
            device: 计算设备
            learning_rate: 学习率
            beta1: Adam优化器的beta1参数
            beta2: Adam优化器的beta2参数
            use_wgan_gp: 是否使用WGAN-GP（Wasserstein GAN with Gradient Penalty）
            gp_weight: 梯度惩罚权重
            n_critic: 每次生成器更新前判别器更新的次数
        """
        # 设置设备
        self.device = device if device else ('cuda' if torch.cuda.is_available() else 'cpu')

        # 保存参数
        self.policy_dim = policy_dim
        self.latent_dim = latent_dim
        self.condition_dim = condition_dim
        self.use_wgan_gp = use_wgan_gp
        self.gp_weight = gp_weight
        self.n_critic = n_critic

        # 创建生成器和判别器
        self.generator = Generator(
            latent_dim=latent_dim,
            output_dim=policy_dim,
            condition_dim=condition_dim,
            hidden_dims=generator_hidden_dims
        ).to(self.device)

        self.discriminator = Discriminator(
            input_dim=policy_dim,
            condition_dim=condition_dim,
            hidden_dims=discriminator_hidden_dims
        ).to(self.device)

        # 设置策略编码器和解码器
        self.policy_encoder = policy_encoder
        self.policy_decoder = policy_decoder

        # 创建优化器
        self.generator_optimizer = optim.Adam(
            self.generator.parameters(),
            lr=learning_rate,
            betas=(beta1, beta2)
        )

        self.discriminator_optimizer = optim.Adam(
            self.discriminator.parameters(),
            lr=learning_rate,
            betas=(beta1, beta2)
        )

        # 训练状态
        self.train_step_count = 0
        self.generator_losses = []
        self.discriminator_losses = []
        self.diversity_scores = []
        self.difficulty_scores = []
        
        # 预定义条件向量用于生成不同类型的策略
        self.predefined_conditions = {
            # 不同难度级别
            'easy': torch.tensor([1.0, 0.0, 0.0, 0.2, 0.3, 0.2, 0.1, 0.1, 0.0, 0.1], device=self.device),
            'medium': torch.tensor([0.0, 1.0, 0.0, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5], device=self.device),
            'hard': torch.tensor([0.0, 0.0, 1.0, 0.8, 0.7, 0.8, 0.9, 0.9, 1.0, 0.9], device=self.device),
            
            # 不同风格
            'aggressive': torch.tensor([0.3, 0.3, 0.8, 0.9, 0.2, 0.7, 0.8, 0.3, 0.6, 0.8], device=self.device),
            'defensive': torch.tensor([0.3, 0.3, 0.5, 0.2, 0.8, 0.3, 0.2, 0.7, 0.4, 0.2], device=self.device),
            'balanced': torch.tensor([0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5], device=self.device),
            'random': torch.tensor([0.5, 0.5, 0.3, 0.4, 0.6, 0.4, 0.5, 0.5, 0.4, 0.5], device=self.device),
            
            # 极端策略
            'extreme': torch.tensor([0.0, 0.0, 1.0, 1.0, 0.0, 1.0, 1.0, 0.0, 1.0, 1.0], device=self.device),
        }

    def _compute_gradient_penalty(self, real_samples: torch.Tensor, fake_samples: torch.Tensor, 
                                condition: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        计算梯度惩罚（用于WGAN-GP）

        Args:
            real_samples: 真实策略样本
            fake_samples: 生成的策略样本
            condition: 条件变量，可选

        Returns:
            梯度惩罚
        """
        # 生成随机权重
        batch_size = real_samples.size(0)
        alpha = torch.rand(batch_size, 1, device=self.device)

        # 生成插值点
        interpolates = alpha * real_samples + (1 - alpha) * fake_samples
        interpolates = interpolates.requires_grad_(True)

        # 计算判别器输出
        d_interpolates = self.discriminator(interpolates, condition)

        # 计算梯度
        gradients = torch.autograd.grad(
            outputs=d_interpolates,
            inputs=interpolates,
            grad_outputs=torch.ones_like(d_interpolates),
            create_graph=True,
            retain_graph=True,
            only_inputs=True
        )[0]

        # 计算梯度惩罚
        gradient_penalty = ((gradients.norm(2, dim=1) - 1) ** 2).mean()

        return gradient_penalty

    def train_step(self, real_policies: torch.Tensor, 
                  condition: Optional[torch.Tensor] = None) -> Dict[str, float]:
        """
        执行一步训练

        Args:
            real_policies: 真实策略样本
            condition: 条件变量，可选

        Returns:
            训练指标
        """
        batch_size = real_policies.size(0)
        real_policies = real_policies.to(self.device)
        
        # 准备条件变量
        if condition is not None:
            condition = condition.to(self.device)
        
        # ---------------------
        # 训练判别器
        # ---------------------
        
        # 多次更新判别器
        d_loss = 0
        
        for _ in range(self.n_critic):
            self.discriminator_optimizer.zero_grad()

            # 生成随机潜在向量
            z = torch.randn(batch_size, self.latent_dim, device=self.device)

            # 生成假策略
            fake_policies = self.generator(z, condition)

            # 计算判别器损失
            if self.use_wgan_gp:
                # Wasserstein损失
                real_validity = self.discriminator(real_policies, condition)
                fake_validity = self.discriminator(fake_policies.detach(), condition)
                d_real_loss = -torch.mean(real_validity)
                d_fake_loss = torch.mean(fake_validity)

                # 梯度惩罚
                gradient_penalty = self._compute_gradient_penalty(real_policies, fake_policies, condition)

                # 总损失
                d_loss = d_real_loss + d_fake_loss + self.gp_weight * gradient_penalty
            else:
                # 标准GAN损失
                real_validity = self.discriminator(real_policies, condition)
                fake_validity = self.discriminator(fake_policies.detach(), condition)
                real_labels = torch.ones_like(real_validity, device=self.device)
                fake_labels = torch.zeros_like(fake_validity, device=self.device)

                d_real_loss = F.binary_cross_entropy_with_logits(real_validity, real_labels)
                d_fake_loss = F.binary_cross_entropy_with_logits(fake_validity, fake_labels)

                # 总损失
                d_loss = d_real_loss + d_fake_loss

            # 反向传播和优化
            d_loss.backward()
            self.discriminator_optimizer.step()

        # ---------------------
        # 训练生成器
        # ---------------------
        self.generator_optimizer.zero_grad()

        # 生成随机潜在向量
        z = torch.randn(batch_size, self.latent_dim, device=self.device)

        # 生成假策略
        fake_policies = self.generator(z, condition)

        # 计算多样性损失（可选）
        diversity_loss = 0
        if batch_size > 1:
            # 计算策略之间的距离，鼓励多样性
            distances = []
            for i in range(batch_size):
                for j in range(i + 1, batch_size):
                    dist = F.pairwise_distance(fake_policies[i].unsqueeze(0), fake_policies[j].unsqueeze(0))
                    distances.append(dist)
            if distances:
                diversity_loss = -torch.mean(torch.stack(distances))  # 负值鼓励多样性

        # 计算生成器损失
        if self.use_wgan_gp:
            # Wasserstein损失
            fake_validity = self.discriminator(fake_policies, condition)
            g_loss = -torch.mean(fake_validity) + 0.1 * diversity_loss
        else:
            # 标准GAN损失
            fake_validity = self.discriminator(fake_policies, condition)
            real_labels = torch.ones_like(fake_validity, device=self.device)
            g_loss = F.binary_cross_entropy_with_logits(fake_validity, real_labels) + 0.1 * diversity_loss

        # 反向传播和优化
        g_loss.backward()
        self.generator_optimizer.step()

        # 记录损失
        self.train_step_count += 1
        self.generator_losses.append(g_loss.item())
        self.discriminator_losses.append(d_loss.item())
        if batch_size > 1 and diversity_loss != 0:
            self.diversity_scores.append(-diversity_loss.item())  # 存储正值表示多样性

        # 返回指标
        return {
            "g_loss": g_loss.item(),
            "d_loss": d_loss.item(),
            "diversity": -diversity_loss.item() if diversity_loss != 0 else 0
        }

    def train(
        self,
        real_policies_dataset: torch.utils.data.Dataset,
        num_epochs: int = 100,
        batch_size: int = 64,
        save_interval: int = 10,
        save_path: str = None,
        verbose: bool = True,
        use_conditions: bool = False,
        condition_generator: Optional[Callable] = None
    ) -> Dict[str, List[float]]:
        """
        训练GAN

        Args:
            real_policies_dataset: 真实策略数据集
            num_epochs: 训练轮数
            batch_size: 批次大小
            save_interval: 保存间隔
            save_path: 保存路径
            verbose: 是否打印训练信息
            use_conditions: 是否使用条件
            condition_generator: 条件生成函数，返回条件张量

        Returns:
            训练历史
        """
        history = {
            "g_loss": [],
            "d_loss": [],
            "diversity": []
        }

        # 创建数据加载器
        dataloader = torch.utils.data.DataLoader(
            real_policies_dataset,
            batch_size=batch_size,
            shuffle=True,
            drop_last=True
        )

        # 训练循环
        start_time = time.time()
        for epoch in range(num_epochs):
            epoch_metrics = {
                "g_loss": 0,
                "d_loss": 0,
                "diversity": 0
            }
            
            num_batches = 0
            
            for batch in dataloader:
                # 获取真实策略
                if isinstance(batch, tuple) and len(batch) >= 1:
                    real_policies = batch[0]
                else:
                    real_policies = batch

                # 生成条件（如果启用）
                condition = None
                if use_conditions:
                    if condition_generator is not None:
                        condition = condition_generator(batch_size)
                    else:
                        # 默认为随机条件
                        condition = torch.rand(batch_size, self.condition_dim, device=self.device)

                # 执行训练步骤
                step_metrics = self.train_step(real_policies, condition)
                
                # 更新指标
                for key in epoch_metrics:
                    if key in step_metrics:
                        epoch_metrics[key] += step_metrics[key]
                        
                num_batches += 1
            
            # 计算平均指标
            for key in epoch_metrics:
                if num_batches > 0:
                    epoch_metrics[key] /= num_batches
                history[key].append(epoch_metrics[key])
            
            # 打印训练信息
            if verbose and (epoch + 1) % max(1, num_epochs // 20) == 0:
                elapsed_time = time.time() - start_time
                logger.info(
                    f"Epoch [{epoch+1}/{num_epochs}] | "
                    f"G Loss: {epoch_metrics['g_loss']:.4f} | "
                    f"D Loss: {epoch_metrics['d_loss']:.4f} | "
                    f"Diversity: {epoch_metrics['diversity']:.4f} | "
                    f"Time: {elapsed_time:.2f}s"
                )
            
            # 保存模型
            if save_path and (epoch + 1) % save_interval == 0:
                self.save(os.path.join(save_path, f"gan_epoch_{epoch+1}"))
        
        # 保存最终模型
        if save_path:
            self.save(os.path.join(save_path, "gan_final"))
            
        return history

    def generate_policies(self, num_policies: int = 1, 
                         condition: Optional[torch.Tensor] = None,
                         noise: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        生成策略

        Args:
            num_policies: 生成策略数量
            condition: 条件变量，可选
            noise: 潜在噪声，可选

        Returns:
            生成的策略参数
        """
        self.generator.eval()
        
        with torch.no_grad():
            # 生成随机潜在向量（如果未提供）
            if noise is None:
                noise = torch.randn(num_policies, self.latent_dim, device=self.device)
                
            # 处理条件
            if condition is not None:
                if condition.dim() == 1:
                    condition = condition.unsqueeze(0)
                if condition.size(0) == 1 and num_policies > 1:
                    condition = condition.expand(num_policies, -1)
            
            # 生成策略
            policies = self.generator(noise, condition)
            
        return policies

    def generate_diverse_policies(self, num_policies: int = 10, 
                                diversity_strength: float = 1.0) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        生成多样化策略

        使用多次采样并选择差异最大的策略集合。

        Args:
            num_policies: 生成策略数量
            diversity_strength: 多样性强度，控制策略差异的重要性

        Returns:
            (生成的策略参数, 使用的潜在向量)
        """
        # 采样次数（比需要的策略数量多）
        num_samples = max(100, num_policies * 3)
        
        # 生成随机潜在向量
        z_samples = torch.randn(num_samples, self.latent_dim, device=self.device)
        
        # 生成随机条件向量（在各种难度和风格之间变化）
        conditions = torch.rand(num_samples, self.condition_dim, device=self.device)
        
        # 生成策略
        with torch.no_grad():
            policy_samples = self.generator(z_samples, conditions)
        
        # 计算策略之间的距离矩阵
        distance_matrix = torch.zeros(num_samples, num_samples, device=self.device)
        for i in range(num_samples):
            for j in range(i+1, num_samples):
                distance = F.pairwise_distance(
                    policy_samples[i].unsqueeze(0), 
                    policy_samples[j].unsqueeze(0)
                )
                distance_matrix[i, j] = distance
                distance_matrix[j, i] = distance
                
        # 使用最大最小距离采样选择多样化的策略
        selected_indices = []
        remaining_indices = list(range(num_samples))
        
        # 随机选择第一个策略
        first_idx = random.choice(remaining_indices)
        selected_indices.append(first_idx)
        remaining_indices.remove(first_idx)
        
        # 贪婪选择剩余策略
        while len(selected_indices) < num_policies and remaining_indices:
            max_min_distance = -float('inf')
            best_idx = None
            
            for idx in remaining_indices:
                # 计算与已选策略的最小距离
                min_distance = float('inf')
                for selected_idx in selected_indices:
                    dist = distance_matrix[idx, selected_idx]
                    min_distance = min(min_distance, dist)
                
                # 更新最大最小距离
                if min_distance > max_min_distance:
                    max_min_distance = min_distance
                    best_idx = idx
            
            selected_indices.append(best_idx)
            remaining_indices.remove(best_idx)
        
        # 返回选择的策略和潜在向量
        selected_policies = policy_samples[selected_indices]
        selected_z = z_samples[selected_indices]
        selected_conditions = conditions[selected_indices]
        
        return selected_policies, selected_z, selected_conditions

    def decode_policy(self, policy_vector: torch.Tensor) -> Any:
        """
        解码策略向量为可执行策略

        Args:
            policy_vector: 策略向量

        Returns:
            解码后的策略
        """
        if self.policy_decoder is not None:
            return self.policy_decoder(policy_vector)
        else:
            # 如果没有提供解码器，直接返回策略向量
            return policy_vector

    def generate_opponent_policy(self, style: str = 'random', 
                               difficulty: str = None,
                               diversity_strength: float = 1.0) -> Any:
        """
        生成对手策略

        Args:
            style: 策略风格，可选值为'random', 'aggressive', 'defensive', 'balanced', 'extreme'
            difficulty: 策略难度，可选值为'easy', 'medium', 'hard'，如果指定，将覆盖风格中的难度设置
            diversity_strength: 多样性强度，控制策略差异的重要性

        Returns:
            生成的对手策略
        """
        # 获取风格的条件向量
        if style in self.predefined_conditions:
            condition = self.predefined_conditions[style].clone()
        else:
            # 随机条件
            condition = torch.rand(self.condition_dim, device=self.device)
            
        # 应用难度设置（如果指定）
        if difficulty and difficulty in self.predefined_conditions:
            # 将难度条件的前3个元素（难度控制）复制到当前条件
            difficulty_condition = self.predefined_conditions[difficulty]
            condition[:3] = difficulty_condition[:3]
            
        # 添加一些随机扰动，增加变化
        perturbation = torch.randn_like(condition) * 0.1
        condition = torch.clamp(condition + perturbation, 0, 1)
        
        # 生成策略
        z = torch.randn(1, self.latent_dim, device=self.device)
        policy_vector = self.generator(z, condition.unsqueeze(0))
        
        # 解码策略
        policy = self.decode_policy(policy_vector)
        
        return policy
        
    def generate_adaptive_opponent(self, player_skill: float, recent_wins: int,
                                 recent_losses: int) -> Any:
        """
        生成自适应难度的对手

        根据玩家技能和最近的战绩调整对手难度。

        Args:
            player_skill: 玩家技能评分 (0.0-1.0)
            recent_wins: 最近的胜利次数
            recent_losses: 最近的失败次数

        Returns:
            生成的对手策略
        """
        # 计算目标难度
        win_rate = recent_wins / max(1, recent_wins + recent_losses)
        
        # 理想的胜率约为40%-60%
        if win_rate > 0.6:
            # 玩家胜率过高，增加难度
            target_difficulty = min(1.0, player_skill + 0.2)
        elif win_rate < 0.4:
            # 玩家胜率过低，降低难度
            target_difficulty = max(0.0, player_skill - 0.2)
        else:
            # 胜率适中，保持相近难度
            target_difficulty = player_skill
            
        # 创建难度条件向量
        condition = torch.zeros(self.condition_dim, device=self.device)
        
        # 设置难度（前3个元素用于难度）
        if target_difficulty < 0.33:
            # 容易
            condition[0] = 1.0
        elif target_difficulty < 0.67:
            # 中等
            condition[1] = 1.0
        else:
            # 困难
            condition[2] = 1.0
            
        # 随机设置其他特性（风格等）
        condition[3:] = torch.rand(self.condition_dim - 3, device=self.device)
        
        # 生成策略
        return self.generate_opponent_policy(condition=condition)

    def save(self, path: str) -> None:
        """
        保存模型

        Args:
            path: 保存路径
        """
        os.makedirs(os.path.dirname(path), exist_ok=True)
        state_dict = {
            "generator": self.generator.state_dict(),
            "discriminator": self.discriminator.state_dict(),
            "generator_optimizer": self.generator_optimizer.state_dict(),
            "discriminator_optimizer": self.discriminator_optimizer.state_dict(),
            "train_step_count": self.train_step_count,
            "generator_losses": self.generator_losses,
            "discriminator_losses": self.discriminator_losses,
            "diversity_scores": self.diversity_scores,
            "difficulty_scores": self.difficulty_scores,
            "predefined_conditions": self.predefined_conditions,
            "config": {
                "policy_dim": self.policy_dim,
                "latent_dim": self.latent_dim,
                "condition_dim": self.condition_dim,
                "use_wgan_gp": self.use_wgan_gp,
                "gp_weight": self.gp_weight,
                "n_critic": self.n_critic
            }
        }
        torch.save(state_dict, path)
        logger.info(f"模型已保存到 {path}")

    def load(self, path: str) -> None:
        """
        加载模型

        Args:
            path: 加载路径
        """
        if not os.path.exists(path):
            logger.warning(f"模型文件不存在: {path}")
            return

        try:
            state_dict = torch.load(path, map_location=self.device)
            
            self.generator.load_state_dict(state_dict["generator"])
            self.discriminator.load_state_dict(state_dict["discriminator"])
            self.generator_optimizer.load_state_dict(state_dict["generator_optimizer"])
            self.discriminator_optimizer.load_state_dict(state_dict["discriminator_optimizer"])
            
            self.train_step_count = state_dict.get("train_step_count", 0)
            self.generator_losses = state_dict.get("generator_losses", [])
            self.discriminator_losses = state_dict.get("discriminator_losses", [])
            self.diversity_scores = state_dict.get("diversity_scores", [])
            self.difficulty_scores = state_dict.get("difficulty_scores", [])
            
            if "predefined_conditions" in state_dict:
                self.predefined_conditions = state_dict["predefined_conditions"]
                
            logger.info(f"模型已从 {path} 加载")
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
