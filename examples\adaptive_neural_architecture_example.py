#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
自适应神经架构示例

演示如何使用自适应神经架构，包括神经架构搜索、动态网络扩展、条件计算路径和模块化网络设计。
"""
import os
import sys
import argparse
import logging
import numpy as np
import torch
import time
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cardgame_ai.algorithms import (
    ArchitectureSearchSpace, NeuralArchitectureSearch,
    DynamicNetworkExtension, ConditionalComputationPath,
    ModularNetworkDesign
)


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser(description="自适应神经架构示例")
    parser.add_argument("--demo_type", type=str, default="nas", 
                        choices=["nas", "dynamic", "conditional", "modular"], 
                        help="演示类型")
    parser.add_argument("--input_dim", type=int, default=128, help="输入维度")
    parser.add_argument("--output_dim", type=int, default=64, help="输出维度")
    parser.add_argument("--batch_size", type=int, default=32, help="批次大小")
    parser.add_argument("--epochs", type=int, default=10, help="训练轮数")
    parser.add_argument("--seed", type=int, default=42, help="随机种子")
    parser.add_argument("--log_level", type=str, default="INFO", 
                        choices=["DEBUG", "INFO", "WARNING", "ERROR"], help="日志级别")
    
    return parser.parse_args()


def setup_logging(log_level):
    """
    设置日志
    """
    numeric_level = getattr(logging, log_level.upper(), None)
    if not isinstance(numeric_level, int):
        raise ValueError(f"Invalid log level: {log_level}")
    
    logging.basicConfig(
        level=numeric_level,
        format="%(asctime)s [%(levelname)s] %(message)s",
        handlers=[
            logging.StreamHandler()
        ]
    )


def generate_dummy_data(input_dim, output_dim, batch_size, num_batches=10):
    """
    生成虚拟数据
    
    Args:
        input_dim: 输入维度
        output_dim: 输出维度
        batch_size: 批次大小
        num_batches: 批次数量
        
    Returns:
        data_loader: 数据加载器
    """
    # 生成虚拟数据
    X = torch.randn(batch_size * num_batches, input_dim)
    y = torch.randn(batch_size * num_batches, output_dim)
    
    # 创建数据集
    dataset = torch.utils.data.TensorDataset(X, y)
    
    # 创建数据加载器
    data_loader = torch.utils.data.DataLoader(
        dataset, batch_size=batch_size, shuffle=True
    )
    
    return data_loader


def evaluate_architecture(config):
    """
    评估架构
    
    Args:
        config: 架构配置
        
    Returns:
        fitness: 评估分数
    """
    # 提取配置
    hidden_dim = config["width"]
    num_layers = config["depth"] // 2  # 每层包含线性层和激活函数
    activation = config["activation"]
    
    # 创建模型
    model = torch.nn.Sequential()
    model.add_module("input", torch.nn.Linear(128, hidden_dim))
    model.add_module("activation_input", activation())
    
    for i in range(num_layers - 1):
        model.add_module(f"hidden_{i}", torch.nn.Linear(hidden_dim, hidden_dim))
        model.add_module(f"activation_{i}", activation())
    
    model.add_module("output", torch.nn.Linear(hidden_dim, 64))
    
    # 计算参数数量
    num_params = sum(p.numel() for p in model.parameters())
    
    # 计算理论计算复杂度（FLOPs）
    flops = 2 * 128 * hidden_dim  # 输入层
    flops += 2 * (num_layers - 1) * hidden_dim * hidden_dim  # 隐藏层
    flops += 2 * hidden_dim * 64  # 输出层
    
    # 计算适应度分数（越高越好）
    # 这里我们假设更小的模型和更低的计算复杂度更好
    fitness = 1.0 / (0.001 * num_params + 0.0000001 * flops)
    
    return fitness


def demo_neural_architecture_search(args):
    """
    演示神经架构搜索
    
    Args:
        args: 命令行参数
    """
    logging.info("演示神经架构搜索")
    
    # 创建架构搜索空间
    search_space = ArchitectureSearchSpace()
    
    # 创建神经架构搜索
    nas = NeuralArchitectureSearch(
        search_space=search_space,
        evaluation_function=evaluate_architecture,
        population_size=10,
        num_generations=5,
        mutation_rate=0.2,
        crossover_rate=0.5,
        tournament_size=3,
        elite_size=2
    )
    
    # 执行搜索
    best_architecture = nas.search()
    
    # 打印结果
    logging.info("搜索完成")
    logging.info(f"最佳架构: {best_architecture}")
    logging.info(f"最佳适应度: {nas.best_fitness:.4f}")
    
    # 打印搜索历史
    logging.info("搜索历史:")
    for i, (fitness, avg_fitness) in enumerate(zip(
        nas.history["best_fitness"], nas.history["avg_fitness"]
    )):
        logging.info(f"  代数 {i}: 最佳适应度 = {fitness:.4f}, 平均适应度 = {avg_fitness:.4f}")


def demo_dynamic_network_extension(args):
    """
    演示动态网络扩展
    
    Args:
        args: 命令行参数
    """
    logging.info("演示动态网络扩展")
    
    # 设置随机种子
    torch.manual_seed(args.seed)
    np.random.seed(args.seed)
    
    # 创建动态网络
    model = DynamicNetworkExtension(
        input_dim=args.input_dim,
        output_dim=args.output_dim,
        hidden_dims=[64, 64],
        max_hidden_dims=[256, 256],
        growth_threshold=0.01,
        growth_factor=1.5,
        min_growth=16,
        activation=torch.nn.ReLU,
        dropout=0.1
    )
    
    # 创建优化器
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    
    # 创建损失函数
    criterion = torch.nn.MSELoss()
    
    # 生成虚拟数据
    data_loader = generate_dummy_data(
        args.input_dim, args.output_dim, args.batch_size, num_batches=10
    )
    
    # 训练模型
    logging.info("开始训练")
    for epoch in range(args.epochs):
        epoch_loss = 0.0
        
        for X, y in data_loader:
            # 前向传播
            outputs = model(X)
            loss = criterion(outputs, y)
            
            # 反向传播
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            # 累加损失
            epoch_loss += loss.item()
        
        # 计算平均损失
        avg_loss = epoch_loss / len(data_loader)
        logging.info(f"Epoch {epoch+1}/{args.epochs}, Loss: {avg_loss:.4f}")
        
        # 尝试扩展网络
        extended = model.extend_network(avg_loss)
        if extended:
            logging.info(f"网络已扩展: {model.hidden_dims}")
            
            # 重新创建优化器
            optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    
    # 打印扩展历史
    logging.info("扩展历史:")
    for i, history in enumerate(model.extension_history):
        logging.info(f"  扩展 {i+1}: {history['old_dims']} -> {history['new_dims']}, Loss: {history['loss']:.4f}")


def demo_conditional_computation_path(args):
    """
    演示条件计算路径
    
    Args:
        args: 命令行参数
    """
    logging.info("演示条件计算路径")
    
    # 设置随机种子
    torch.manual_seed(args.seed)
    np.random.seed(args.seed)
    
    # 创建条件计算路径模型
    model = ConditionalComputationPath(
        input_dim=args.input_dim,
        output_dim=args.output_dim,
        num_experts=4,
        expert_hidden_dims=[128, 128],
        gate_hidden_dims=[64],
        activation=torch.nn.ReLU,
        dropout=0.1,
        sparsity_threshold=0.2,
        capacity_factor=1.5
    )
    
    # 创建优化器
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    
    # 创建损失函数
    criterion = torch.nn.MSELoss()
    
    # 生成虚拟数据
    data_loader = generate_dummy_data(
        args.input_dim, args.output_dim, args.batch_size, num_batches=10
    )
    
    # 训练模型
    logging.info("开始训练")
    for epoch in range(args.epochs):
        epoch_loss = 0.0
        
        for X, y in data_loader:
            # 前向传播
            outputs = model(X)
            loss = criterion(outputs, y)
            
            # 反向传播
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            # 累加损失
            epoch_loss += loss.item()
        
        # 计算平均损失
        avg_loss = epoch_loss / len(data_loader)
        logging.info(f"Epoch {epoch+1}/{args.epochs}, Loss: {avg_loss:.4f}")
    
    # 打印专家使用统计
    stats = model.get_expert_usage_stats()
    logging.info("专家使用统计:")
    for key, value in stats.items():
        logging.info(f"  {key}: {value:.4f}")


def demo_modular_network_design(args):
    """
    演示模块化网络设计
    
    Args:
        args: 命令行参数
    """
    logging.info("演示模块化网络设计")
    
    # 设置随机种子
    torch.manual_seed(args.seed)
    np.random.seed(args.seed)
    
    # 创建模块配置
    module_configs = [
        {
            "type": "linear",
            "hidden_dim": 128,
            "activation": torch.nn.ReLU
        },
        {
            "type": "conditional",
            "num_experts": 4,
            "expert_hidden_dims": [128, 128],
            "gate_hidden_dims": [64],
            "sparsity_threshold": 0.2
        },
        {
            "type": "dynamic",
            "hidden_dims": [128, 128],
            "max_hidden_dims": [256, 256],
            "growth_threshold": 0.01
        }
    ]
    
    # 创建模块化网络
    model = ModularNetworkDesign(
        input_dim=args.input_dim,
        output_dim=args.output_dim,
        module_configs=module_configs,
        dropout=0.1
    )
    
    # 创建优化器
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    
    # 创建损失函数
    criterion = torch.nn.MSELoss()
    
    # 生成虚拟数据
    data_loader = generate_dummy_data(
        args.input_dim, args.output_dim, args.batch_size, num_batches=10
    )
    
    # 训练模型
    logging.info("开始训练")
    for epoch in range(args.epochs):
        epoch_loss = 0.0
        
        for X, y in data_loader:
            # 前向传播
            outputs = model(X)
            loss = criterion(outputs, y)
            
            # 反向传播
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            # 累加损失
            epoch_loss += loss.item()
        
        # 计算平均损失
        avg_loss = epoch_loss / len(data_loader)
        logging.info(f"Epoch {epoch+1}/{args.epochs}, Loss: {avg_loss:.4f}")
    
    # 打印模块使用统计
    stats = model.get_module_usage_stats()
    logging.info("模块使用统计:")
    for key, value in stats.items():
        logging.info(f"  {key}: {value}")


def main():
    """
    主函数
    """
    # 解析命令行参数
    args = parse_args()
    
    # 设置日志
    setup_logging(args.log_level)
    
    # 根据演示类型选择演示函数
    if args.demo_type == "nas":
        demo_neural_architecture_search(args)
    elif args.demo_type == "dynamic":
        demo_dynamic_network_extension(args)
    elif args.demo_type == "conditional":
        demo_conditional_computation_path(args)
    elif args.demo_type == "modular":
        demo_modular_network_design(args)
    else:
        logging.error(f"未知的演示类型: {args.demo_type}")


if __name__ == "__main__":
    main()
