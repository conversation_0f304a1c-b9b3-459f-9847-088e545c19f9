#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
元强化学习示例

展示如何使用MAML和Reptile算法进行元强化学习训练。
"""

import os
import time
import logging
import argparse
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Any

from cardgame_ai.utils.task_sampler import TaskSampler, Task
from cardgame_ai.algorithms.meta_reinforcement_learning import MAML, Reptile


# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class SimplePolicy(nn.Module):
    """简单的策略网络"""
    
    def __init__(self, input_dim: int, hidden_dim: int, output_dim: int):
        """
        初始化
        
        Args:
            input_dim: 输入维度
            hidden_dim: 隐层维度
            output_dim: 输出维度（动作空间大小）
        """
        super().__init__()
        self.network = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, output_dim)
        )
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播"""
        return self.network(x)


def generate_synthetic_tasks(num_tasks: int = 10, num_samples: int = 100,
                           input_dim: int = 10, output_dim: int = 5) -> List[Dict]:
    """
    生成合成任务数据，用于元学习算法的演示
    
    这里我们生成简单的分类任务，每个任务有不同的决策边界。
    在实际应用中，任务数据应该来自不同的游戏情境或不同风格的对手。
    
    Args:
        num_tasks: 任务数量
        num_samples: 每个任务的样本数量
        input_dim: 输入维度
        output_dim: 输出维度（动作空间大小）
    
    Returns:
        任务数据列表
    """
    tasks = []
    
    for i in range(num_tasks):
        # 为每个任务创建一个随机决策边界
        boundary = torch.randn(input_dim, output_dim)
        
        # 创建任务数据
        observations = torch.randn(num_samples, input_dim)
        logits = torch.matmul(observations, boundary)
        actions = torch.argmax(logits, dim=1)
        
        # 将数据划分为支持集和查询集
        split_idx = int(num_samples * 0.7)
        
        support_data = {
            "observations": observations[:split_idx],
            "actions": actions[:split_idx]
        }
        
        query_data = {
            "observations": observations[split_idx:],
            "actions": actions[split_idx:]
        }
        
        # 添加到任务列表
        tasks.append({
            "support": support_data,
            "query": query_data,
            "boundary": boundary  # 保存决策边界，用于评估
        })
    
    return tasks


def evaluate_model(model: nn.Module, tasks: List[Dict], num_adapt_steps: int = 3,
                 inner_lr: float = 0.01) -> Dict[str, float]:
    """
    评估模型在多个任务上的性能
    
    Args:
        model: 模型
        tasks: 任务列表
        num_adapt_steps: 适应步数
        inner_lr: 内循环学习率
    
    Returns:
        评估指标
    """
    model.eval()
    metrics = {
        "pre_adapt_acc": [],
        "post_adapt_acc": []
    }
    
    for task in tasks:
        support_data = task["support"]
        query_data = task["query"]
        
        # 计算适应前在查询集上的准确率
        with torch.no_grad():
            logits = model(query_data["observations"])
            predictions = torch.argmax(logits, dim=1)
            pre_adapt_acc = (predictions == query_data["actions"]).float().mean().item()
            metrics["pre_adapt_acc"].append(pre_adapt_acc)
        
        # 创建模型副本进行适应
        adapted_model = copy.deepcopy(model)
        optimizer = torch.optim.SGD(adapted_model.parameters(), lr=inner_lr)
        
        # 适应任务
        for _ in range(num_adapt_steps):
            optimizer.zero_grad()
            logits = adapted_model(support_data["observations"])
            loss = F.cross_entropy(logits, support_data["actions"])
            loss.backward()
            optimizer.step()
        
        # 计算适应后在查询集上的准确率
        with torch.no_grad():
            logits = adapted_model(query_data["observations"])
            predictions = torch.argmax(logits, dim=1)
            post_adapt_acc = (predictions == query_data["actions"]).float().mean().item()
            metrics["post_adapt_acc"].append(post_adapt_acc)
    
    # 计算平均指标
    for key in metrics:
        metrics[key] = sum(metrics[key]) / len(metrics[key])
    
    return metrics


def train_maml(args):
    """
    使用MAML算法进行训练
    
    Args:
        args: 命令行参数
    """
    logger.info("创建模型和任务数据")
    
    # 创建模型
    model = SimplePolicy(
        input_dim=args.input_dim,
        hidden_dim=args.hidden_dim,
        output_dim=args.output_dim
    )
    
    # 创建MAML算法
    maml = MAML(
        model=model,
        inner_lr=args.inner_lr,
        meta_lr=args.meta_lr,
        inner_steps=args.inner_steps,
        first_order=args.first_order,
        device=args.device
    )
    
    # 生成任务数据
    train_tasks = generate_synthetic_tasks(
        num_tasks=args.num_tasks,
        num_samples=args.num_samples,
        input_dim=args.input_dim,
        output_dim=args.output_dim
    )
    
    # 创建测试任务
    test_tasks = generate_synthetic_tasks(
        num_tasks=args.num_test_tasks,
        num_samples=args.num_samples,
        input_dim=args.input_dim,
        output_dim=args.output_dim
    )
    
    logger.info("开始MAML训练")
    
    # 训练循环
    for epoch in range(args.epochs):
        # 执行元更新
        meta_loss = maml.meta_update(train_tasks)
        
        # 每隔一定间隔评估模型
        if (epoch + 1) % args.eval_interval == 0:
            metrics = evaluate_model(
                model=maml.model,
                tasks=test_tasks,
                num_adapt_steps=args.inner_steps,
                inner_lr=args.inner_lr
            )
            
            logger.info(f"Epoch {epoch+1}/{args.epochs}, "
                      f"Meta Loss: {meta_loss:.4f}, "
                      f"Pre-Adapt Acc: {metrics['pre_adapt_acc']:.4f}, "
                      f"Post-Adapt Acc: {metrics['post_adapt_acc']:.4f}")
    
    # 保存模型
    if args.save_model:
        save_path = os.path.join(args.save_dir, f"maml_model_{int(time.time())}.pt")
        maml.save(save_path)
        logger.info(f"模型已保存到 {save_path}")


def train_reptile(args):
    """
    使用Reptile算法进行训练
    
    Args:
        args: 命令行参数
    """
    logger.info("创建模型和任务数据")
    
    # 创建模型
    model = SimplePolicy(
        input_dim=args.input_dim,
        hidden_dim=args.hidden_dim,
        output_dim=args.output_dim
    )
    
    # 创建Reptile算法
    reptile = Reptile(
        model=model,
        inner_lr=args.inner_lr,
        meta_lr=args.meta_lr,
        inner_steps=args.inner_steps,
        device=args.device
    )
    
    # 生成任务数据
    train_tasks = generate_synthetic_tasks(
        num_tasks=args.num_tasks,
        num_samples=args.num_samples,
        input_dim=args.input_dim,
        output_dim=args.output_dim
    )
    
    # 创建测试任务
    test_tasks = generate_synthetic_tasks(
        num_tasks=args.num_test_tasks,
        num_samples=args.num_samples,
        input_dim=args.input_dim,
        output_dim=args.output_dim
    )
    
    logger.info("开始Reptile训练")
    
    # 训练循环
    for epoch in range(args.epochs):
        # 执行元更新
        meta_loss = reptile.meta_update(train_tasks)
        
        # 每隔一定间隔评估模型
        if (epoch + 1) % args.eval_interval == 0:
            metrics = evaluate_model(
                model=reptile.model,
                tasks=test_tasks,
                num_adapt_steps=args.inner_steps,
                inner_lr=args.inner_lr
            )
            
            logger.info(f"Epoch {epoch+1}/{args.epochs}, "
                      f"Meta Loss: {meta_loss:.4f}, "
                      f"Pre-Adapt Acc: {metrics['pre_adapt_acc']:.4f}, "
                      f"Post-Adapt Acc: {metrics['post_adapt_acc']:.4f}")
    
    # 保存模型
    if args.save_model:
        save_path = os.path.join(args.save_dir, f"reptile_model_{int(time.time())}.pt")
        reptile.save(save_path)
        logger.info(f"模型已保存到 {save_path}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="元强化学习示例")
    
    # 通用参数
    parser.add_argument("--algorithm", type=str, default="maml", choices=["maml", "reptile"],
                       help="元学习算法")
    parser.add_argument("--device", type=str, default="cuda" if torch.cuda.is_available() else "cpu",
                       help="设备")
    parser.add_argument("--save_model", action="store_true", help="是否保存模型")
    parser.add_argument("--save_dir", type=str, default="models", help="模型保存目录")
    
    # 模型参数
    parser.add_argument("--input_dim", type=int, default=10, help="输入维度")
    parser.add_argument("--hidden_dim", type=int, default=64, help="隐层维度")
    parser.add_argument("--output_dim", type=int, default=5, help="输出维度")
    
    # 训练参数
    parser.add_argument("--epochs", type=int, default=100, help="训练轮数")
    parser.add_argument("--num_tasks", type=int, default=10, help="训练任务数量")
    parser.add_argument("--num_test_tasks", type=int, default=5, help="测试任务数量")
    parser.add_argument("--num_samples", type=int, default=100, help="每个任务的样本数量")
    parser.add_argument("--eval_interval", type=int, default=10, help="评估间隔")
    
    # 元学习参数
    parser.add_argument("--inner_lr", type=float, default=0.01, help="内循环学习率")
    parser.add_argument("--meta_lr", type=float, default=0.001, help="元学习率")
    parser.add_argument("--inner_steps", type=int, default=5, help="内循环步数")
    parser.add_argument("--first_order", action="store_true", help="是否使用一阶近似（仅对MAML有效）")
    
    args = parser.parse_args()
    
    # 创建保存目录
    if args.save_model and not os.path.exists(args.save_dir):
        os.makedirs(args.save_dir)
    
    # 根据算法选择训练函数
    if args.algorithm == "maml":
        train_maml(args)
    elif args.algorithm == "reptile":
        train_reptile(args)
    else:
        raise ValueError(f"不支持的算法: {args.algorithm}")


if __name__ == "__main__":
    # 导入这里需要的模块
    import copy
    
    # 运行主函数
    main()