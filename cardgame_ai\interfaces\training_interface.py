"""
训练服务接口定义

该接口定义了训练服务的标准契约，用于解耦zhuchengxu模块与具体训练实现的依赖关系。
通过该接口，zhuchengxu模块可以与任何实现了TrainingInterface的训练服务进行交互。

设计目标:
- 解耦zhuchengxu与train_efficient_zero的直接依赖
- 提供标准化的训练服务接口
- 支持多种训练算法的插拔式使用
- 实现训练过程的标准化管理

作者: Architect Timmy
版本: v1.0
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass
from enum import Enum


class TrainingStatus(Enum):
    """训练状态枚举"""
    IDLE = "idle"                    # 空闲状态
    INITIALIZING = "initializing"    # 初始化中
    TRAINING = "training"            # 训练中
    PAUSED = "paused"               # 暂停中
    COMPLETED = "completed"         # 已完成
    FAILED = "failed"               # 失败
    CANCELLED = "cancelled"         # 已取消


@dataclass
class TrainingConfig:
    """训练配置数据类
    
    包含训练所需的所有配置参数，用于标准化配置传递。
    """
    # 基础配置
    game: str = "doudizhu"                    # 游戏类型
    algorithm: str = "efficient_zero"         # 训练算法
    device: str = "auto"                      # 设备配置
    seed: int = 42                           # 随机种子
    
    # 训练参数
    epochs: int = 1000                       # 训练轮数
    batch_size: int = 256                    # 批次大小
    learning_rate: float = 0.0005            # 学习率
    num_simulations: int = 100               # MCTS模拟次数
    
    # 保存和评估
    save_frequency: int = 100                # 保存频率
    eval_frequency: int = 50                 # 评估频率
    log_frequency: int = 10                  # 日志频率
    
    # 多智能体配置
    farmer_cooperation_enabled: bool = True   # 农民协作开关
    cooperation_weight: float = 0.8          # 协作权重
    team_reward_weight: float = 0.9          # 团队奖励权重
    
    # 路径配置
    checkpoint_dir: str = "checkpoints"       # 检查点目录
    log_dir: str = "logs"                    # 日志目录
    model_dir: str = "models"                # 模型目录
    
    # 高级配置
    resume: bool = False                     # 是否恢复训练
    distributed: bool = False                # 是否分布式训练
    mixed_precision: bool = False            # 是否混合精度
    
    # 自定义配置
    custom_config: Optional[Dict[str, Any]] = None


@dataclass
class TrainingResult:
    """训练结果数据类
    
    包含训练完成后的所有结果信息。
    """
    # 基础结果
    status: TrainingStatus                   # 训练状态
    exit_code: int                          # 退出码 (0=成功, 非0=失败)
    message: str                            # 结果消息
    
    # 训练统计
    total_epochs: int = 0                   # 总训练轮数
    completed_epochs: int = 0               # 已完成轮数
    training_time: float = 0.0              # 训练时间(秒)
    
    # 性能指标
    final_loss: Optional[float] = None      # 最终损失
    best_reward: Optional[float] = None     # 最佳奖励
    win_rate: Optional[float] = None        # 胜率
    
    # 模型信息
    model_path: Optional[str] = None        # 模型保存路径
    checkpoint_path: Optional[str] = None   # 检查点路径
    
    # 详细信息
    metrics: Optional[Dict[str, Any]] = None # 详细指标
    logs: Optional[List[str]] = None        # 训练日志


class TrainingInterface(ABC):
    """训练服务接口
    
    定义了训练服务必须实现的标准方法，用于解耦zhuchengxu模块与具体训练实现。
    
    实现该接口的类必须提供:
    1. 训练启动和控制功能
    2. 配置验证和管理功能  
    3. 状态监控和查询功能
    4. 结果获取和报告功能
    
    注意:
        所有方法都必须是线程安全的，支持并发调用。
        实现类应该处理所有异常，不应该让异常传播到调用方。
    """
    
    @abstractmethod
    def validate_config(self, config: TrainingConfig) -> bool:
        """验证训练配置
        
        Args:
            config: 训练配置对象
            
        Returns:
            bool: 配置是否有效
            
        注意:
            此方法应该验证配置的完整性和有效性，
            包括参数范围检查、依赖检查等。
        """
        pass
    
    @abstractmethod
    def start_training(self, config: TrainingConfig) -> bool:
        """启动训练
        
        Args:
            config: 训练配置对象
            
        Returns:
            bool: 是否成功启动
            
        注意:
            此方法应该是异步的，启动训练后立即返回。
            实际训练过程在后台进行。
        """
        pass
    
    @abstractmethod
    def stop_training(self) -> bool:
        """停止训练
        
        Returns:
            bool: 是否成功停止
            
        注意:
            此方法应该优雅地停止训练，保存当前状态。
        """
        pass
    
    @abstractmethod
    def pause_training(self) -> bool:
        """暂停训练
        
        Returns:
            bool: 是否成功暂停
        """
        pass
    
    @abstractmethod
    def resume_training(self) -> bool:
        """恢复训练
        
        Returns:
            bool: 是否成功恢复
        """
        pass
    
    @abstractmethod
    def get_status(self) -> TrainingStatus:
        """获取训练状态
        
        Returns:
            TrainingStatus: 当前训练状态
        """
        pass
    
    @abstractmethod
    def get_progress(self) -> Dict[str, Any]:
        """获取训练进度
        
        Returns:
            Dict[str, Any]: 训练进度信息，包含:
                - current_epoch: 当前轮数
                - total_epochs: 总轮数
                - progress_percent: 进度百分比
                - estimated_time_remaining: 预计剩余时间
                - current_metrics: 当前指标
        """
        pass
    
    @abstractmethod
    def get_result(self) -> Optional[TrainingResult]:
        """获取训练结果
        
        Returns:
            Optional[TrainingResult]: 训练结果，如果训练未完成返回None
        """
        pass
    
    @abstractmethod
    def wait_for_completion(self, timeout: Optional[float] = None) -> TrainingResult:
        """等待训练完成
        
        Args:
            timeout: 超时时间(秒)，None表示无限等待
            
        Returns:
            TrainingResult: 训练结果
            
        Raises:
            TimeoutError: 超时异常
        """
        pass
    
    @abstractmethod
    def register_callback(self, event: str, callback: Callable) -> bool:
        """注册事件回调
        
        Args:
            event: 事件名称 (如: "epoch_completed", "training_completed", "error_occurred")
            callback: 回调函数
            
        Returns:
            bool: 是否成功注册
        """
        pass
    
    @abstractmethod
    def get_supported_algorithms(self) -> List[str]:
        """获取支持的算法列表
        
        Returns:
            List[str]: 支持的算法名称列表
        """
        pass
    
    @abstractmethod
    def get_default_config(self, algorithm: str) -> TrainingConfig:
        """获取算法的默认配置
        
        Args:
            algorithm: 算法名称
            
        Returns:
            TrainingConfig: 默认配置
        """
        pass
