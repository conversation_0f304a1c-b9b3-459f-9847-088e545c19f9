# 斗地主AI优化项目架构文档

## 介绍 / 前言

本文档概述了斗地主AI优化项目的整体架构，包括核心算法系统、分布式训练框架和性能监控体系。其主要目标是为AI驱动的开发提供指导性架构蓝图，确保一致性并遵循选定的模式和技术。

## 目录

1. [技术摘要](#技术摘要)
2. [高级概览](#高级概览)
3. [架构/设计模式](#架构设计模式采用)
4. [组件视图](#组件视图)
5. [项目结构](#项目结构)
6. [核心工作流程](#核心工作流程序列图)
7. [确定性技术栈选择](#确定性技术栈选择)
8. [基础设施和部署概览](#基础设施和部署概览)
9. [错误处理策略](#错误处理策略)
10. [编码标准](#编码标准)
11. [整体测试策略](#整体测试策略)
12. [安全最佳实践](#安全最佳实践)

## 技术摘要

斗地主AI优化项目采用模块化单体架构，基于EfficientZero算法构建高性能多智能体协作系统。核心技术栈包括Python/PyTorch用于AI算法实现，Ray框架支持分布式训练，以及完整的性能监控和评估体系。项目目标是将AI胜率从60-70%提升至85-95%超人类水平，同时保持高效的训练过程和优秀的系统可扩展性。

## 高级概览

**主要架构风格：** 模块化单体 + 分布式计算
**仓库结构：** Monorepo
**核心交互流程：** 训练数据生成 → 分布式训练 → 模型评估 → 性能优化

```mermaid
graph TB
    subgraph "AI训练系统"
        A[EfficientZero核心算法] --> B[多智能体协作模块]
        B --> C[分布式训练引擎]
        C --> D[性能评估系统]
        D --> A
    end

    subgraph "支撑系统"
        E[配置管理] --> A
        F[监控系统] --> C
        G[数据管理] --> C
    end

    subgraph "外部接口"
        H[训练接口] --> A
        I[评估接口] --> D
        J[监控接口] --> F
    end
```

## 架构/设计模式采用

- **模块化设计模式** - 将AI系统分解为独立的功能模块，支持独立开发和测试
- **策略模式** - 支持多种AI算法和训练策略的动态切换
- **观察者模式** - 实现训练过程的实时监控和事件通知
- **工厂模式** - 统一管理不同类型的AI模型和组件创建
- **命令模式** - 封装训练任务和评估任务的执行逻辑
- **适配器模式** - 适配不同的硬件环境和计算资源

## 组件视图

### 核心AI组件
- **EfficientZero引擎**：核心算法实现，负责MCTS搜索和神经网络训练
- **多智能体协作器**：实现农民间协作和角色专门化
- **分布式训练管理器**：协调多GPU/多机器的训练任务
- **模型评估器**：性能测试和基准对比

### 支撑服务组件
- **配置服务**：统一管理训练参数和系统配置
- **监控服务**：实时监控训练状态和系统性能
- **数据服务**：管理训练数据和模型检查点
- **日志服务**：结构化日志记录和分析

```mermaid
graph TD
    subgraph "核心AI层"
        A1[EfficientZero引擎]
        A2[多智能体协作器]
        A3[分布式训练管理器]
        A4[模型评估器]
    end

    subgraph "服务支撑层"
        B1[配置服务]
        B2[监控服务]
        B3[数据服务]
        B4[日志服务]
    end

    subgraph "基础设施层"
        C1[Ray分布式框架]
        C2[PyTorch深度学习]
        C3[CUDA GPU加速]
        C4[文件系统存储]
    end

    A1 --> B1
    A2 --> B2
    A3 --> B3
    A4 --> B4

    B1 --> C1
    B2 --> C2
    B3 --> C3
    B4 --> C4
```

## 项目结构

```plaintext
cardgame_ai/
├── .github/                    # CI/CD工作流
│   └── workflows/
│       └── training.yml
├── algorithms/                 # 核心AI算法
│   ├── efficient_zero/         # EfficientZero算法实现
│   ├── multi_agent/           # 多智能体协作
│   ├── mcts/                  # 蒙特卡洛树搜索
│   └── components/            # 算法组件
├── training/                  # 训练系统
│   ├── distributed/           # 分布式训练
│   ├── self_play/            # 自对弈训练
│   ├── config/               # 训练配置
│   └── schedulers/           # 训练调度
├── evaluation/               # 评估系统
│   ├── evaluators/           # 评估器
│   ├── benchmarks/           # 基准测试
│   └── metrics/              # 性能指标
├── games/                    # 游戏环境
│   └── doudizhu/            # 斗地主游戏实现
├── utils/                    # 工具库
│   ├── config/              # 配置管理
│   ├── logging/             # 日志系统
│   └── monitoring/          # 监控工具
├── interface/               # 外部接口
│   ├── api/                 # API接口
│   └── cli/                 # 命令行工具
├── tests/                   # 测试代码
│   ├── unit/                # 单元测试
│   ├── integration/         # 集成测试
│   └── performance/         # 性能测试
├── configs/                 # 配置文件
├── models/                  # 模型存储
├── logs/                    # 日志文件
├── docs/                    # 项目文档
└── scripts/                 # 工具脚本
```

### 关键目录说明

- **algorithms/**: 包含所有AI算法的核心实现，模块化设计支持独立开发
- **training/**: 分布式训练系统，支持大规模并行训练
- **evaluation/**: 完整的评估体系，包括性能测试和基准对比
- **games/**: 游戏环境实现，提供标准化的训练和测试环境
- **utils/**: 通用工具库，支持配置管理、日志记录和系统监控

## 核心工作流程/序列图

### 分布式训练工作流程

```mermaid
sequenceDiagram
    participant TM as 训练管理器
    participant DT as 分布式训练器
    participant AG as AI智能体
    participant EV as 评估器
    participant MS as 监控系统

    TM->>DT: 启动分布式训练
    DT->>AG: 创建多个训练实例

    loop 训练循环
        AG->>AG: 自对弈生成数据
        AG->>DT: 上传训练数据
        DT->>DT: 聚合梯度更新
        DT->>AG: 同步模型参数
        DT->>MS: 报告训练指标
    end

    DT->>EV: 触发性能评估
    EV->>EV: 执行基准测试
    EV->>TM: 返回评估结果
    TM->>TM: 决定是否继续训练
```

### 多智能体协作流程

```mermaid
sequenceDiagram
    participant P1 as 玩家1(农民)
    participant P2 as 玩家2(农民)
    participant P3 as 玩家3(地主)
    participant CM as 协作管理器

    P1->>CM: 请求协作建议
    CM->>CM: 分析当前局势
    CM->>P2: 获取队友状态
    P2->>CM: 返回手牌信息
    CM->>P1: 提供协作策略
    P1->>P1: 执行协作动作
    P1->>CM: 报告动作结果
    CM->>P2: 更新协作状态
```

## 确定性技术栈选择

| 类别 | 技术 | 版本/详情 | 描述/目的 | 理由 |
|:-----|:-----|:----------|:----------|:-----|
| **语言** | Python | 3.8-3.11 | 主要开发语言 | AI/ML生态系统成熟，丰富的库支持 |
| **深度学习框架** | PyTorch | 1.12+ | 神经网络训练和推理 | 动态图支持，研究友好，CUDA集成优秀 |
| **分布式计算** | Ray | 2.0+ | 分布式训练和并行计算 | 原生Python支持，易于扩展 |
| **GPU加速** | CUDA | 11.0+ | GPU计算加速 | NVIDIA GPU标准，性能优秀 |
| **数据处理** | NumPy | 1.21+ | 数值计算 | 科学计算标准库 |
|  | Pandas | 1.4+ | 数据分析和处理 | 数据操作便利 |
| **配置管理** | Hydra | 1.2+ | 配置文件管理 | 层次化配置，实验管理友好 |
| **监控可视化** | TensorBoard | 2.8+ | 训练监控和可视化 | PyTorch原生集成 |
|  | Weights & Biases | Latest | 实验跟踪(可选) | 高级实验管理 |
| **测试框架** | pytest | 7.0+ | 单元测试和集成测试 | Python测试标准 |
|  | pytest-cov | Latest | 代码覆盖率 | 测试质量保证 |
| **代码质量** | Black | Latest | 代码格式化 | 一致的代码风格 |
|  | Flake8 | Latest | 代码检查 | 代码质量保证 |
|  | MyPy | Latest | 类型检查 | 静态类型验证 |
| **容器化** | Docker | Latest | 容器化部署 | 环境一致性 |
| **版本控制** | Git | Latest | 代码版本管理 | 标准版本控制 |
| **CI/CD** | GitHub Actions | N/A | 持续集成/部署 | 与GitHub集成便利 |

## 基础设施和部署概览

- **计算平台**: 本地GPU集群 + 云端扩展(可选)
- **核心服务**: NVIDIA GPU集群, 高速存储, 网络互联
- **容器化**: Docker支持，便于环境管理和部署
- **部署策略**: 本地开发 → 集群训练 → 性能验证
- **环境**: Development(本地) → Staging(小规模集群) → Production(全规模集群)
- **环境提升**: dev → staging(自动化测试通过) → production(手动批准)
- **回滚策略**: 检查点回滚，配置版本控制，快速模型切换

## 错误处理策略

- **通用方法**: 使用Python异常机制，定义层次化的自定义异常类
- **日志记录**:
  - 库/方法: Python `logging`模块 + `structlog`结构化日志
  - 格式: JSON格式，便于分析和监控
  - 级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
  - 上下文: 包含训练ID、模型版本、GPU设备ID、关键参数
- **特定处理模式**:
  - **GPU内存错误**: 自动降低批次大小，重试训练
  - **网络通信错误**: 指数退避重试，最大重试3次
  - **模型收敛问题**: 自动调整学习率，记录异常指标
  - **分布式同步错误**: 检查点恢复，重新初始化分布式环境

## 编码标准

**这些标准对所有AI代理和人类开发者生成的代码都是强制性的。**

- **主要运行时**: Python 3.8-3.11
- **风格指南和检查器**: Black + Flake8 + MyPy，配置文件: `pyproject.toml`
- **命名约定**:
  - 变量: `snake_case`
  - 函数/方法: `snake_case`
  - 类/类型: `PascalCase`
  - 常量: `UPPER_SNAKE_CASE`
  - 文件: `snake_case.py`
  - 模块/包: `snake_case`
- **文件结构**: 遵循"项目结构"部分定义的布局
- **单元测试文件组织**: `test_*.py`在`tests/`目录中，镜像源代码结构
- **异步操作**: 始终使用`async`/`await`进行基于Promise的操作
- **类型安全**: 启用MyPy严格模式，所有新代码必须严格类型化
- **注释和文档**:
  - 代码注释: 解释"为什么"而不是"什么"，使用Google风格的docstring
  - README: 每个模块都应有README说明其目的、设置和使用方法
- **依赖管理**: 使用pip/conda，优先固定版本，安全漏洞扫描

### Python特定约定

- **不可变性**: 优先使用元组表示不可变序列，考虑`@dataclass(frozen=True)`
- **函数式vs面向对象**: 实体和服务使用类，无状态操作使用函数
- **错误处理**: 抛出继承自基础`AppException`的特定自定义异常
- **资源管理**: 始终使用`with`语句处理文件或数据库连接等资源
- **类型提示**: 所有新函数和方法必须有完整的类型提示
- **日志记录**: 使用配置为结构化输出的`logging`模块，包含相关ID
- **关键库使用约定**: HTTP请求使用`httpx`并设置明确的超时，数据操作适当使用`pandas`

## 整体测试策略

- **工具**: pytest, pytest-cov, pytest-mock, pytest-asyncio
- **单元测试**:
  - **范围**: 测试单个函数、方法、类或小模块的隔离功能
  - **位置**: `tests/unit/`目录，镜像源代码结构
  - **模拟/存根**: 使用`unittest.mock`，模拟所有外部依赖
  - **AI代理责任**: 为所有新代码生成覆盖公共方法、逻辑路径、边缘情况的单元测试
- **集成测试**:
  - **范围**: 测试应用程序边界内多个组件或服务的交互
  - **位置**: `tests/integration/`
  - **环境**: 使用内存数据库或测试容器
  - **AI代理责任**: 为关键服务交互生成集成测试
- **性能测试**:
  - **范围**: 验证训练性能、推理速度、内存使用等关键指标
  - **位置**: `tests/performance/`
  - **工具**: 自定义性能测试框架 + 基准测试
- **测试覆盖率**:
  - **目标**: 80%行/分支覆盖率(指导性，测试质量优于原始覆盖率数字)
  - **测量**: Coverage.py
- **测试数据管理**: 使用工厂模式和fixtures，设置/清理脚本，专用测试数据生成

## 安全最佳实践

- **输入清理/验证**: 使用Pydantic进行所有外部输入验证，在边界处进行验证
- **输出编码**: 所有动态数据必须进行上下文相关的编码以防止注入攻击
- **密钥管理**: 通过指定的配置模块访问密钥，永不硬编码或记录密钥
- **依赖安全**: 运行`pip-audit`进行漏洞扫描，及时更新有漏洞的依赖
- **认证/授权检查**: 所有API端点必须强制执行认证，在服务层执行授权检查
- **最小权限原则**: 数据库连接用户仅具有必要权限，IAM角色范围狭窄
- **API安全**: 强制HTTPS，实施速率限制，使用标准HTTP安全头
- **错误处理和信息泄露**: 确保错误消息不向最终用户泄露敏感信息
- **定期安全审计**: 计划渗透测试，在CI中使用SAST/DAST工具

## 关键参考文档

- **EfficientZero算法文档**: `docs/algorithms/efficient_zero.md`
- **多智能体协作设计**: `docs/multi_agent/cooperation_design.md`
- **分布式训练指南**: `docs/training/distributed_training.md`
- **性能优化手册**: `docs/optimization/performance_guide.md`
- **API参考文档**: `docs/api/api_reference.md`

## 变更日志

| 变更 | 日期 | 版本 | 描述 | 作者 |
|------|------|------|------|------|
| 初始创建 | 2024-01-XX | v1.0 | 基于PRD创建初始架构文档 | Architect (Timmy) |

---

## 📋 架构文档完成

**架构设计要点总结：**

1. **模块化设计**: 清晰的组件分离，支持独立开发和测试
2. **可扩展架构**: 支持从单机到多机集群的无缝扩展
3. **性能优化**: 针对AI训练的专门优化，保证高效资源利用
4. **质量保证**: 完整的测试策略和代码质量标准
5. **安全考虑**: 全面的安全最佳实践

**下一步建议：**
- 开始实施核心算法模块的重构
- 建立分布式训练环境
- 实现性能监控和评估系统
