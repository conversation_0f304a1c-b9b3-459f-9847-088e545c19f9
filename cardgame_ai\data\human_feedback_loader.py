"""
用于加载和处理人类偏好反馈数据的模块。
"""
import json
import logging
from typing import List, Tuple, Any, Dict, Optional

import torch
from torch.utils.data import Dataset, DataLoader

# 导入状态和动作处理函数
from cardgame_ai.utils.data_format import process_state, process_action, prepare_rlhf_batch

logger = logging.getLogger(__name__)

class HumanPreferenceDataset(Dataset):
    """
    用于加载人类偏好数据的 PyTorch Dataset。
    数据格式假定为 JSON 文件，包含一个列表，每个元素是字典：
    {'state': ..., 'chosen': ..., 'rejected': ...}
    """

    def __init__(self, data_path: str):
        """
        初始化数据集。

        Args:
            data_path (str): 指向包含偏好数据的 JSON 文件路径。
        """
        self.data_path = data_path
        self.data: List[Tuple[Any, Any, Any]] = []
        self._load_data()

    def _load_data(self) -> None:
        """
        从 JSON 文件加载并预处理数据。
        """
        try:
            with open(self.data_path, 'r', encoding='utf-8') as f:
                raw_data = json.load(f)
        except FileNotFoundError:
            logger.error(f"RLHF 数据文件未找到: {self.data_path}")
            return
        except json.JSONDecodeError:
            logger.error(f"无法解析 RLHF 数据文件 (非有效 JSON): {self.data_path}")
            return
        except Exception as e:
            logger.error(f"加载 RLHF 数据文件时出错: {self.data_path} - {e}")
            return

        if not isinstance(raw_data, list):
            logger.error(f"RLHF 数据文件格式错误：顶层应为列表。路径: {self.data_path}")
            return

        processed_count = 0
        error_count = 0
        for i, item in enumerate(raw_data):
            if not isinstance(item, dict) or 'state' not in item or 'chosen' not in item or 'rejected' not in item:
                logger.warning(f"跳过格式错误的 RLHF 数据项 #{i} (缺少键): {item} in {self.data_path}")
                error_count += 1
                continue

            try:
                # 使用导入的函数处理状态和动作
                processed_state = process_state(item['state'])
                processed_chosen = process_action(item['chosen'])
                processed_rejected = process_action(item['rejected'])

                # 添加到数据列表
                self.data.append((processed_state, processed_chosen, processed_rejected))
                processed_count += 1
            except Exception as e:
                logger.warning(f"处理 RLHF 数据项 #{i} 时出错: {item} - {e} in {self.data_path}")
                error_count += 1

        logger.info(f"成功加载 {processed_count} 条 RLHF 数据, 跳过 {error_count} 条错误数据 (来自 {self.data_path})")

    def __len__(self) -> int:
        """
        返回数据集中的样本数量。
        """
        return len(self.data)

    def __getitem__(self, idx: int) -> Tuple[Any, Any, Any]:
        """
        获取指定索引的样本。

        Args:
            idx (int): 样本索引。

        Returns:
            一个元组 (processed_state, processed_chosen_action, processed_rejected_action)。
        """
        if not 0 <= idx < len(self.data):
            raise IndexError(f"索引 {idx} 超出范围 [0, {len(self.data) - 1}]")
        return self.data[idx]

def load_human_preference_data(
    data_path: str, 
    batch_size: int = 32,
    shuffle: bool = True, 
    num_workers: int = 4,
    train_ratio: float = 0.8
) -> Tuple[DataLoader, DataLoader]:
    """
    加载人类偏好数据并创建用于训练和验证的数据加载器。

    Args:
        data_path: 数据文件路径
        batch_size: 批次大小
        shuffle: 是否打乱数据
        num_workers: 数据加载线程数
        train_ratio: 训练集比例

    Returns:
        训练数据加载器和验证数据加载器的元组
    """
    # 创建数据集
    dataset = HumanPreferenceDataset(data_path)
    
    # 检查数据集大小
    if len(dataset) == 0:
        logger.warning(f"RLHF数据集为空: {data_path}")
        # 返回空数据加载器
        empty_loader = DataLoader(dataset, batch_size=1)
        return empty_loader, empty_loader
    
    # 划分训练集和验证集
    train_size = int(len(dataset) * train_ratio)
    val_size = len(dataset) - train_size
    
    if val_size > 0:
        train_dataset, val_dataset = torch.utils.data.random_split(
            dataset, [train_size, val_size]
        )
    else:
        train_dataset = dataset
        val_dataset = dataset
        logger.warning(f"数据集太小，无法进行有效划分: {len(dataset)} 个样本。使用全部数据作为训练和验证集。")
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        num_workers=num_workers,
        collate_fn=None  # 使用默认的collate函数
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        collate_fn=None  # 使用默认的collate函数
    )
    
    logger.info(f"RLHF数据加载完成：训练集 {train_size} 样本，验证集 {val_size} 样本")
    
    return train_loader, val_loader

# 示例用法 (可选, 用于测试)
if __name__ == '__main__':
    # 创建一个临时的测试 JSON 文件
    test_data = [
        {'state': [1, 2, 3], 'chosen': 0, 'rejected': 1},
        {'state': [4, 5, 6], 'chosen': 2, 'rejected': 0},
        {'state': [7, 8, 9], 'chosen': 1, 'rejected': 2},
        {'malformed': 'data'},
        {'state': [10], 'chosen': 0} # Missing rejected
    ]
    test_file_path = 'temp_rlhf_test.json'
    try:
        with open(test_file_path, 'w') as f:
            json.dump(test_data, f)

        # 配置日志以查看输出
        logging.basicConfig(level=logging.INFO)

        # 测试数据集加载
        print(f"测试加载文件: {test_file_path}")
        dataset = HumanPreferenceDataset(test_file_path)
        print(f"数据集大小: {len(dataset)}")

        # 测试获取数据项
        if len(dataset) > 0:
            print("第一个数据项:", dataset[0])

        # 测试与 DataLoader 配合使用
        from torch.utils.data import DataLoader
        if len(dataset) > 0:
            dataloader = DataLoader(dataset, batch_size=2, shuffle=True)
            print("\n测试 DataLoader:")
            for batch in dataloader:
                states, chosen, rejected = batch
                print("  批次状态形状:", states.shape if isinstance(states, torch.Tensor) else type(states))
                print("  批次选择动作形状:", chosen.shape if isinstance(chosen, torch.Tensor) else type(chosen))
                print("  批次拒绝动作形状:", rejected.shape if isinstance(rejected, torch.Tensor) else type(rejected))
                # break # 只展示一个批次

        # 添加训练/验证划分测试
        test_file_path = 'temp_rlhf_test.json'
        try:
            with open(test_file_path, 'w') as f:
                # 创建一个更大的测试数据集
                larger_test_data = []
                for i in range(20):  # 20个样本
                    larger_test_data.append({
                        'state': [i, i+1, i+2], 
                        'chosen': i % 5,  # 0-4的循环
                        'rejected': (i+2) % 5  # 不同于chosen的值
                    })
                json.dump(larger_test_data, f)
            
            print("\n测试数据集划分和数据加载器创建:")
            train_loader, val_loader = load_human_preference_data(
                test_file_path, batch_size=4, train_ratio=0.7
            )
            
            print(f"训练加载器批次数: {len(train_loader)}")
            print(f"验证加载器批次数: {len(val_loader)}")
            
            # 获取一个批次进行检查
            print("\n检查训练批次:")
            train_batch = next(iter(train_loader))
            formatted_batch = prepare_rlhf_batch(train_batch)
            for key, value in formatted_batch.items():
                print(f"  {key}: {type(value)}, 形状: {value.shape}")
            
        except Exception as e:
            print(f"扩展测试中出现错误: {e}")
        finally:
            # 清理
            import os
            if os.path.exists(test_file_path):
                os.remove(test_file_path)

    except Exception as e:
        print(f"测试过程中发生错误: {e}")
    finally:
        # 清理临时文件
        if os.path.exists(test_file_path):
            os.remove(test_file_path)
            print(f"\n已删除临时测试文件: {test_file_path}") 