#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
斗地主游戏规则模块

定义斗地主游戏的规则和牌型判断。
"""

from typing import List, Optional
from cardgame_ai.games.doudizhu.card import Card
from cardgame_ai.games.doudizhu.card_group import CardGroup, get_card_group_type, CardGroupType

def check_card_play_legality(cards: List[Card], last_cards: Optional[List[Card]], hand_cards: List[Card]) -> bool:
    """检查出牌是否合法

    Args:
        cards: 当前出的牌
        last_cards: 上家出的牌，如果是None表示当前玩家是本轮第一个出牌
        hand_cards: 当前玩家的手牌

    Returns:
        是否合法
    """
    # 检查是否有牌
    if not cards:
        return False

    # 检查是否是手牌的子集
    for card in cards:
        if card not in hand_cards:
            return False

    # 获取牌型
    card_group = CardGroup(cards)
    card_type = get_card_group_type(card_group)

    # 如果是第一个出牌，只需要检查牌型是否有效
    if last_cards is None:
        return card_type != CardGroupType.PASS

    # 获取上家牌型
    last_card_group = CardGroup(last_cards)
    last_card_type = get_card_group_type(last_card_group)

    # 如果上家牌型无效，当前出牌只需要牌型有效
    if last_card_type == CardGroupType.PASS:
        return card_type != CardGroupType.PASS

    # 特殊规则：王炸可以打任何牌
    if card_type == CardGroupType.ROCKET:
        return True

    # 特殊规则：炸弹可以打非炸弹牌型
    if card_type == CardGroupType.BOMB and last_card_type != CardGroupType.BOMB and last_card_type != CardGroupType.ROCKET:
        return True

    # 普通规则：牌型必须相同，且当前牌必须大于上家牌
    if card_type == last_card_type:
        # 检查牌型长度是否相同（对于顺子、连对等）
        if len(cards) != len(last_cards):
            return False

        # 比较牌值
        return card_group.main_rank > last_card_group.main_rank

    # 牌型不同，不能出牌
    return False
