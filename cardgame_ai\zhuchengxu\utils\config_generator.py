"""
配置生成器模块

基于硬件检测结果和配置模板，自动生成优化的训练配置文件。
支持Jinja2模板引擎和参数自动缩放算法。

主要功能:
- 配置模板渲染：基于Jinja2模板生成配置
- 参数自动缩放：根据硬件性能自动调整参数
- 配置验证：验证生成配置的有效性
- 缓存管理：缓存生成的配置以提高性能
"""

import os
import yaml
import json
import hashlib
import logging
from typing import Dict, Any, Optional, List
from pathlib import Path
from jinja2 import Environment, FileSystemLoader, Template
from dataclasses import asdict

from .hardware_detector import HardwareProfile, HardwareDetector


class ConfigGenerator:
    """配置生成器主类"""
    
    def __init__(self, config_root: str = "configs"):
        self.config_root = Path(config_root)
        self.templates_dir = self.config_root / "templates"
        self.generated_dir = self.config_root / "generated"
        self.cache_dir = self.config_root / ".config_cache"
        
        # 创建必要目录
        self.generated_dir.mkdir(parents=True, exist_ok=True)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化Jinja2环境
        self.jinja_env = Environment(
            loader=FileSystemLoader(str(self.templates_dir)),
            trim_blocks=True,
            lstrip_blocks=True
        )
        
        self.logger = logging.getLogger(__name__)
        
        # 参数缩放规则
        self.scaling_rules = self._load_scaling_rules()
    
    def _load_scaling_rules(self) -> Dict:
        """加载参数缩放规则"""
        return {
            "batch_size": {
                "base": 128,
                "memory_factor": 0.8,  # 每GB显存增加的batch_size
                "gpu_factor": 1.2,     # 每个GPU的倍数
                "max_value": 512,
                "min_value": 32
            },
            "num_workers": {
                "base": 4,
                "cpu_factor": 0.5,     # CPU核心数的倍数
                "max_value": 16,
                "min_value": 2
            },
            "mcts_simulations": {
                "base": 50,
                "gpu_factor": 25,      # 每个GPU增加的模拟次数
                "memory_factor": 0.5,  # 每GB显存增加的模拟次数
                "max_value": 300,
                "min_value": 25
            },
            "learning_rate": {
                "base": 0.001,
                "batch_factor": -0.1,  # batch_size增大时学习率调整
                "gpu_factor": 0.1,     # 多GPU时的调整
                "max_value": 0.01,
                "min_value": 0.0001
            },
            "prefetch_factor": {
                "base": 4,
                "worker_factor": 0.5,  # 基于num_workers调整
                "max_value": 8,
                "min_value": 2
            }
        }
    
    def calculate_optimal_parameters(self, hardware_profile: HardwareProfile) -> Dict[str, Any]:
        """根据硬件配置计算最优参数"""
        gpus = hardware_profile.gpus
        cpu = hardware_profile.cpu
        
        # 计算硬件指标
        total_gpu_memory = sum(gpu.memory_total for gpu in gpus) / 1024  # 转换为GB
        gpu_count = len(gpus)
        cpu_cores = cpu.cores_logical
        
        optimal_params = {}
        
        for param_name, rules in self.scaling_rules.items():
            base_value = rules["base"]
            
            if param_name == "batch_size":
                # batch_size = base + (total_memory * memory_factor) * (gpu_count * gpu_factor)
                value = base_value + (total_gpu_memory * rules["memory_factor"]) * (gpu_count * rules["gpu_factor"])
                value = max(rules["min_value"], min(rules["max_value"], int(value)))
                
                # 确保batch_size是2的幂次
                value = 2 ** round(math.log2(value))
                
            elif param_name == "num_workers":
                value = int(cpu_cores * rules["cpu_factor"])
                value = max(rules["min_value"], min(rules["max_value"], value))
                
            elif param_name == "mcts_simulations":
                value = base_value + (gpu_count * rules["gpu_factor"]) + (total_gpu_memory * rules["memory_factor"])
                value = max(rules["min_value"], min(rules["max_value"], int(value)))
                
            elif param_name == "learning_rate":
                # 根据batch_size调整学习率
                batch_size = optimal_params.get("batch_size", 128)
                batch_factor = (batch_size / 128) ** rules["batch_factor"]
                gpu_factor = (gpu_count ** rules["gpu_factor"]) if gpu_count > 1 else 1
                value = base_value * batch_factor * gpu_factor
                value = max(rules["min_value"], min(rules["max_value"], value))
                
            elif param_name == "prefetch_factor":
                num_workers = optimal_params.get("num_workers", 4)
                value = int(base_value + num_workers * rules["worker_factor"])
                value = max(rules["min_value"], min(rules["max_value"], value))
                
            else:
                value = base_value
                
            optimal_params[param_name] = value
        
        return optimal_params
    
    def generate_config_from_template(self, 
                                    template_name: str, 
                                    hardware_profile: HardwareProfile,
                                    additional_vars: Optional[Dict] = None) -> Dict[str, Any]:
        """从模板生成配置"""
        try:
            # 加载模板
            template = self.jinja_env.get_template(template_name)
            
            # 计算最优参数
            optimal_params = self.calculate_optimal_parameters(hardware_profile)
            
            # 准备模板变量
            template_vars = {
                "hardware": asdict(hardware_profile),
                "optimal": optimal_params,
                "gpu_count": len(hardware_profile.gpus),
                "total_memory_gb": sum(gpu.memory_total for gpu in hardware_profile.gpus) / 1024,
                "cpu_cores": hardware_profile.cpu.cores_logical,
                "performance_tier": hardware_profile.performance_tier
            }
            
            # 添加额外变量
            if additional_vars:
                template_vars.update(additional_vars)
            
            # 渲染模板
            rendered_config = template.render(**template_vars)
            
            # 解析YAML配置
            config = yaml.safe_load(rendered_config)
            
            return config
            
        except Exception as e:
            self.logger.error(f"从模板生成配置失败: {e}")
            raise
    
    def generate_hardware_specific_config(self, 
                                        base_config_path: str,
                                        hardware_profile: HardwareProfile,
                                        output_name: Optional[str] = None) -> str:
        """生成硬件特定的配置文件"""
        
        # 生成配置文件名
        if not output_name:
            hardware_hash = self._get_hardware_hash(hardware_profile)
            output_name = f"auto_config_{hardware_hash}.yaml"
        
        output_path = self.generated_dir / output_name
        
        # 检查缓存
        cache_key = self._get_cache_key(base_config_path, hardware_profile)
        cached_config = self._load_from_cache(cache_key)
        
        if cached_config:
            self.logger.info(f"使用缓存配置: {output_path}")
            with open(output_path, 'w', encoding='utf-8') as f:
                yaml.dump(cached_config, f, default_flow_style=False, allow_unicode=True)
            return str(output_path)
        
        # 加载基础配置
        with open(base_config_path, 'r', encoding='utf-8') as f:
            base_config = yaml.safe_load(f)
        
        # 计算最优参数
        optimal_params = self.calculate_optimal_parameters(hardware_profile)
        
        # 更新配置
        updated_config = self._merge_configs(base_config, optimal_params, hardware_profile)
        
        # 验证配置
        if self._validate_config(updated_config):
            # 保存配置
            with open(output_path, 'w', encoding='utf-8') as f:
                yaml.dump(updated_config, f, default_flow_style=False, allow_unicode=True)
            
            # 缓存配置
            self._save_to_cache(cache_key, updated_config)
            
            self.logger.info(f"生成硬件特定配置: {output_path}")
            return str(output_path)
        else:
            raise ValueError("生成的配置验证失败")
    
    def _merge_configs(self, base_config: Dict, optimal_params: Dict, hardware_profile: HardwareProfile) -> Dict:
        """合并基础配置和最优参数"""
        config = base_config.copy()
        
        # 更新设备配置
        if hardware_profile.gpus:
            config.setdefault("device", {})
            config["device"]["type"] = "cuda"
            config["device"]["ids"] = list(range(len(hardware_profile.gpus)))
            config["device"]["mixed_precision"] = True
        else:
            config.setdefault("device", {})
            config["device"]["type"] = "cpu"
        
        # 更新训练配置
        config.setdefault("training", {})
        config["training"]["batch_size"] = optimal_params["batch_size"]
        config["training"]["learning_rate"] = optimal_params["learning_rate"]
        
        # 更新数据配置
        config.setdefault("data", {})
        config["data"]["num_workers"] = optimal_params["num_workers"]
        config["data"]["prefetch_factor"] = optimal_params["prefetch_factor"]
        
        # 更新MCTS配置
        config.setdefault("mcts", {})
        config["mcts"]["num_simulations"] = optimal_params["mcts_simulations"]
        config["mcts"]["parallel_threads"] = min(8, len(hardware_profile.gpus) * 2) if hardware_profile.gpus else 2
        
        # 更新分布式配置
        config.setdefault("distributed", {})
        config["distributed"]["enabled"] = len(hardware_profile.gpus) > 1
        
        # 添加硬件信息注释
        config["_hardware_info"] = {
            "generated_at": str(datetime.now()),
            "performance_tier": hardware_profile.performance_tier,
            "gpu_count": len(hardware_profile.gpus),
            "total_memory_gb": sum(gpu.memory_total for gpu in hardware_profile.gpus) / 1024,
            "cpu_cores": hardware_profile.cpu.cores_logical
        }
        
        return config
    
    def _validate_config(self, config: Dict) -> bool:
        """验证配置有效性"""
        try:
            # 检查必要字段
            required_fields = ["device", "training", "data", "mcts"]
            for field in required_fields:
                if field not in config:
                    self.logger.error(f"配置缺少必要字段: {field}")
                    return False
            
            # 检查参数范围
            batch_size = config["training"].get("batch_size", 0)
            if not (32 <= batch_size <= 512):
                self.logger.error(f"batch_size超出有效范围: {batch_size}")
                return False
            
            num_workers = config["data"].get("num_workers", 0)
            if not (1 <= num_workers <= 32):
                self.logger.error(f"num_workers超出有效范围: {num_workers}")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return False
    
    def _get_hardware_hash(self, hardware_profile: HardwareProfile) -> str:
        """生成硬件配置的哈希值"""
        hardware_str = json.dumps(asdict(hardware_profile), sort_keys=True)
        return hashlib.md5(hardware_str.encode()).hexdigest()[:8]
    
    def _get_cache_key(self, base_config_path: str, hardware_profile: HardwareProfile) -> str:
        """生成缓存键"""
        base_config_hash = hashlib.md5(open(base_config_path, 'rb').read()).hexdigest()[:8]
        hardware_hash = self._get_hardware_hash(hardware_profile)
        return f"{base_config_hash}_{hardware_hash}"
    
    def _save_to_cache(self, cache_key: str, config: Dict):
        """保存配置到缓存"""
        cache_file = self.cache_dir / f"{cache_key}.json"
        try:
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.warning(f"保存缓存失败: {e}")
    
    def _load_from_cache(self, cache_key: str) -> Optional[Dict]:
        """从缓存加载配置"""
        cache_file = self.cache_dir / f"{cache_key}.json"
        if cache_file.exists():
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.warning(f"加载缓存失败: {e}")
        return None


# 导入必要模块
import math
from datetime import datetime
