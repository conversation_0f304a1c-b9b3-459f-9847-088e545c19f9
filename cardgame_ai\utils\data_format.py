"""
数据格式处理模块

提供处理和转换游戏状态、动作等数据的函数。主要用于RLHF（基于人类反馈的强化学习）
和其他需要特定数据格式的场景。
"""

import logging
import numpy as np
import torch
from typing import Dict, List, Tuple, Any, Optional, Union

# 配置日志
logger = logging.getLogger(__name__)

def process_state(state_data: Any) -> torch.Tensor:
    """
    处理原始状态数据，转换为模型可用的张量格式。
    
    Args:
        state_data: 原始状态数据，可能是字典、列表或其他格式
        
    Returns:
        处理后的状态张量
    """
    try:
        # 根据输入类型进行不同处理
        if isinstance(state_data, torch.Tensor):
            # 已经是张量，直接返回
            return state_data
        
        elif isinstance(state_data, np.ndarray):
            # 将NumPy数组转换为张量
            return torch.from_numpy(state_data).float()
        
        elif isinstance(state_data, list):
            # 直接转换列表为张量
            return torch.tensor(state_data, dtype=torch.float32)
        
        elif isinstance(state_data, dict):
            # 根据字典内容提取特征
            if 'features' in state_data:
                # 如果有预提取的特征，直接使用
                return torch.tensor(state_data['features'], dtype=torch.float32)
            
            elif 'obs' in state_data:
                # 如果是观察向量
                return torch.tensor(state_data['obs'], dtype=torch.float32)
            
            elif 'hand_cards' in state_data:
                # 斗地主特定格式：手牌
                hand_cards = state_data.get('hand_cards', [])
                history_actions = state_data.get('history_actions', [])
                landlord_pos = state_data.get('landlord_pos', 0)
                current_pos = state_data.get('current_pos', 0)
                
                # 简单合并特征（实际应用中可能需要更复杂的特征工程）
                hand_cards_tensor = torch.tensor(hand_cards, dtype=torch.float32)
                history_tensor = torch.zeros(10)  # 简化历史动作表示
                pos_tensor = torch.tensor([landlord_pos, current_pos], dtype=torch.float32)
                
                # 合并所有特征
                return torch.cat([hand_cards_tensor, history_tensor, pos_tensor])
            
            else:
                # 其他情况：将字典值扁平化为向量
                # 这是一种简单的后备方案，实际应用中应根据具体字典结构设计更好的处理方法
                logger.warning(f"状态字典格式未识别，使用简化处理: {state_data}")
                values = []
                for key in sorted(state_data.keys()):  # 排序确保顺序一致性
                    value = state_data[key]
                    if isinstance(value, (int, float)):
                        values.append(float(value))
                
                if values:
                    return torch.tensor(values, dtype=torch.float32)
                else:
                    logger.error(f"无法从状态字典提取数值: {state_data}")
                    return torch.zeros(1)  # 返回单元素零张量作为后备
        
        else:
            # 无法处理的类型
            logger.warning(f"无法处理的状态数据类型: {type(state_data)}")
            return torch.zeros(1)  # 返回单元素零张量作为后备
            
    except Exception as e:
        logger.error(f"处理状态数据时出错: {e}, 数据: {state_data}")
        return torch.zeros(1)  # 错误情况下返回单元素零张量

def process_action(action_data: Any) -> torch.Tensor:
    """
    处理原始动作数据，转换为模型可用的张量格式。
    
    Args:
        action_data: 原始动作数据，可能是整数、字典或其他格式
        
    Returns:
        处理后的动作张量（通常是动作ID）
    """
    try:
        # 根据输入类型进行不同处理
        if isinstance(action_data, torch.Tensor):
            # 已经是张量，确保是长整型
            return action_data.long()
        
        elif isinstance(action_data, np.ndarray):
            # 将NumPy数组转换为长整型张量
            return torch.from_numpy(action_data).long()
        
        elif isinstance(action_data, (int, float)):
            # 直接转换数字为标量张量
            return torch.tensor(int(action_data), dtype=torch.long)
        
        elif isinstance(action_data, list):
            # 如果是列表，尝试提取动作编号或直接使用
            if len(action_data) == 1 and isinstance(action_data[0], (int, float)):
                # 单元素列表，取第一个元素
                return torch.tensor(int(action_data[0]), dtype=torch.long)
            else:
                # 多元素列表，可能是one-hot编码或动作特征
                return torch.tensor(action_data, dtype=torch.long)
        
        elif isinstance(action_data, dict):
            # 尝试从字典中提取动作ID
            if 'action_id' in action_data:
                return torch.tensor(action_data['action_id'], dtype=torch.long)
            
            elif 'action_index' in action_data:
                return torch.tensor(action_data['action_index'], dtype=torch.long)
            
            elif 'action' in action_data:
                # 递归处理'action'字段
                return process_action(action_data['action'])
            
            elif 'action_value' in action_data:
                # 某些斗地主格式可能使用这个字段
                return torch.tensor(int(action_data['action_value']), dtype=torch.long)
            
            else:
                # 无法从字典中提取动作ID
                logger.warning(f"无法从动作字典中提取ID: {action_data}")
                return torch.tensor(-1, dtype=torch.long)  # 表示无效动作
        
        else:
            # 无法处理的类型
            logger.warning(f"无法处理的动作数据类型: {type(action_data)}")
            return torch.tensor(-1, dtype=torch.long)  # 表示无效动作
            
    except Exception as e:
        logger.error(f"处理动作数据时出错: {e}, 数据: {action_data}")
        return torch.tensor(-1, dtype=torch.long)  # 错误情况下返回-1表示无效动作

def prepare_rlhf_batch(batch: Tuple[torch.Tensor, torch.Tensor, torch.Tensor]) -> Dict[str, torch.Tensor]:
    """
    将RLHF数据批次转换为训练所需的字典格式。
    
    Args:
        batch: 包含(states, chosen_actions, rejected_actions)的元组
        
    Returns:
        包含'states', 'chosen_actions', 'rejected_actions'的字典
    """
    states, chosen_actions, rejected_actions = batch
    
    return {
        'states': states,
        'chosen_actions': chosen_actions,
        'rejected_actions': rejected_actions
    }

# 测试函数
if __name__ == '__main__':
    # 测试不同类型的状态数据
    test_states = [
        [1, 2, 3, 4],  # 列表
        {'features': [5, 6, 7, 8]},  # 带features的字典
        {'hand_cards': [1, 0, 1, 1, 0], 'landlord_pos': 0, 'current_pos': 1},  # 游戏状态字典
        np.array([9, 10, 11, 12])  # NumPy数组
    ]
    
    print("测试状态处理:")
    for i, state in enumerate(test_states):
        processed = process_state(state)
        print(f"  状态 {i+1}: {type(state)} -> {type(processed)}, 形状: {processed.shape}")
    
    # 测试不同类型的动作数据
    test_actions = [
        0,  # 整数
        [1],  # 单元素列表
        {'action_id': 2},  # 带action_id的字典
        {'action_index': 3},  # 带action_index的字典
        np.array(4)  # NumPy标量
    ]
    
    print("\n测试动作处理:")
    for i, action in enumerate(test_actions):
        processed = process_action(action)
        print(f"  动作 {i+1}: {type(action)} -> {type(processed)}, 值: {processed.item()}") 