"""
高级经验回放模块

实现高级经验回放机制，提高样本利用效率和训练效果。包括优先级经验回放的高级变体、
经验过滤机制和经验增强等技术，使AI能够更高效地利用训练数据。
"""
import os
import time
import logging
import random
import numpy as np
import torch
import torch.nn.functional as F
from typing import Dict, List, Tuple, Any, Optional, Union, Callable
from collections import deque

from cardgame_ai.core.base import Experience, Batch
from cardgame_ai.algorithms.replay_buffer import ReplayBuffer, PrioritizedReplayBuffer
from cardgame_ai.algorithms.experience_filter import ExperienceFilter, CompositeMetric
from cardgame_ai.algorithms.experience_filter import TDErrorMetric, RewardMetric, RarityMetric, DiversityMetric


class HindsightExperienceReplay(ReplayBuffer):
    """
    事后经验回放缓冲区

    实现Hindsight Experience Replay (HER)算法，通过重新定义目标来增强稀疏奖励环境中的学习。
    """

    def __init__(
        self,
        capacity: int,
        k_goals: int = 4,
        strategy: str = 'future',
        goal_selection_probability: float = 0.8,
        virtual_reward: float = 1.0
    ):
        """
        初始化事后经验回放缓冲区

        Args:
            capacity: 缓冲区容量
            k_goals: 每个经验生成的虚拟目标数量
            strategy: 目标选择策略，支持'future'、'episode'、'random'
            goal_selection_probability: 选择虚拟目标的概率
            virtual_reward: 达到虚拟目标时的奖励
        """
        super().__init__(capacity)
        self.k_goals = k_goals
        self.strategy = strategy
        self.goal_selection_probability = goal_selection_probability
        self.virtual_reward = virtual_reward

        # 存储当前回合的经验
        self.current_episode = []

        # 统计信息
        self.stats = {
            "original_experiences": 0,
            "hindsight_experiences": 0,
            "episodes": 0
        }

    def add(self, experience: Experience) -> None:
        """
        添加经验

        Args:
            experience: 经验数据
        """
        # 将经验添加到当前回合
        self.current_episode.append(experience)

        # 更新统计信息
        self.stats["original_experiences"] += 1

        # 如果经验表示回合结束，处理当前回合
        if experience.done:
            self._process_episode()

    def _process_episode(self) -> None:
        """
        处理当前回合的经验
        """
        # 更新统计信息
        self.stats["episodes"] += 1

        # 如果回合为空，直接返回
        if not self.current_episode:
            return

        # 将原始经验添加到缓冲区
        for exp in self.current_episode:
            super().add(exp)

        # 生成虚拟目标经验
        self._generate_hindsight_experiences()

        # 清空当前回合
        self.current_episode = []

    def _generate_hindsight_experiences(self) -> None:
        """
        生成虚拟目标经验
        """
        episode_length = len(self.current_episode)

        # 对每个经验生成虚拟目标
        for i, experience in enumerate(self.current_episode):
            # 对每个经验生成k个虚拟目标
            for _ in range(self.k_goals):
                # 如果随机数大于目标选择概率，跳过该经验
                if random.random() > self.goal_selection_probability:
                    continue

                # 根据策略选择虚拟目标
                if self.strategy == 'future' and i < episode_length - 1:
                    # 从当前经验之后的经验中选择目标
                    future_idx = random.randint(i + 1, episode_length - 1)
                    virtual_goal = self._extract_goal(self.current_episode[future_idx])
                elif self.strategy == 'episode':
                    # 从整个回合中随机选择目标
                    random_idx = random.randint(0, episode_length - 1)
                    virtual_goal = self._extract_goal(self.current_episode[random_idx])
                elif self.strategy == 'random':
                    # 生成随机目标
                    virtual_goal = self._generate_random_goal()
                else:
                    # 默认使用最后一个经验作为目标
                    virtual_goal = self._extract_goal(self.current_episode[-1])

                # 创建新的虚拟目标经验
                hindsight_exp = self._create_hindsight_experience(experience, virtual_goal)

                # 将虚拟目标经验添加到缓冲区
                super().add(hindsight_exp)

                # 更新统计信息
                self.stats["hindsight_experiences"] += 1

    def _extract_goal(self, experience: Experience) -> Any:
        """
        从经验中提取目标

        Args:
            experience: 经验数据

        Returns:
            目标状态
        """
        # 如果经验有目标属性，直接返回
        if hasattr(experience, 'goal'):
            return experience.goal

        # 如果经验的状态是字典并包含目标字段
        if isinstance(experience.state, dict) and 'goal' in experience.state:
            return experience.state['goal']

        # 如果经验的下一个状态是字典并包含目标字段
        if hasattr(experience, 'next_state') and isinstance(experience.next_state, dict) and 'goal' in experience.next_state:
            return experience.next_state['goal']

        # 默认使用下一个状态作为目标
        if hasattr(experience, 'next_state'):
            return experience.next_state

        # 如果都不可用，使用当前状态
        return experience.state

    def _generate_random_goal(self) -> Any:
        """
        生成随机目标

        Returns:
            随机目标状态
        """
        # 如果缓冲区中有经验，从中随机选择一个目标
        if len(self.buffer) > 0:
            random_exp = random.choice(self.buffer)
            return self._extract_goal(random_exp)

        # 如果当前回合中有经验，从中随机选择一个目标
        if len(self.current_episode) > 0:
            random_exp = random.choice(self.current_episode)
            return self._extract_goal(random_exp)

        # 如果都不可用，返回None
        return None

    def _create_hindsight_experience(self, experience: Experience, goal: Any) -> Experience:
        """
        创建虚拟目标经验

        Args:
            experience: 原始经验
            goal: 虚拟目标

        Returns:
            新的经验对象
        """
        # 检查是否达到目标
        achieved = self._is_goal_achieved(experience, goal)

        # 计算新的奖励
        new_reward = self.virtual_reward if achieved else experience.reward

        # 创建新的状态（包含虚拟目标）
        new_state = self._update_state_with_goal(experience.state, goal)

        # 如果经验有下一个状态，也更新它
        new_next_state = None
        if hasattr(experience, 'next_state'):
            new_next_state = self._update_state_with_goal(experience.next_state, goal)

        # 创建新的经验对象
        hindsight_exp = Experience(
            state=new_state,
            action=experience.action,
            reward=new_reward,
            next_state=new_next_state,
            done=achieved or experience.done
        )

        # 添加目标属性
        hindsight_exp.goal = goal
        hindsight_exp.is_hindsight = True

        return hindsight_exp

    def _is_goal_achieved(self, experience: Experience, goal: Any) -> bool:
        """
        检查是否达到目标

        Args:
            experience: 经验数据
            goal: 目标

        Returns:
            是否达到目标
        """
        # 如果经验有下一个状态，检查它是否与目标相等
        if hasattr(experience, 'next_state'):
            return self._states_equal(experience.next_state, goal)

        # 如果没有下一个状态，检查当前状态是否与目标相等
        return self._states_equal(experience.state, goal)

    def _states_equal(self, state1: Any, state2: Any) -> bool:
        """
        检查两个状态是否相等

        Args:
            state1: 第一个状态
            state2: 第二个状态

        Returns:
            是否相等
        """
        # 如果两个状态都是字典
        if isinstance(state1, dict) and isinstance(state2, dict):
            # 如果两个状态都有目标字段，只比较目标
            if 'goal' in state1 and 'goal' in state2:
                return np.array_equal(state1['goal'], state2['goal'])

            # 如果两个状态都有观察字段，只比较观察
            if 'observation' in state1 and 'observation' in state2:
                return np.array_equal(state1['observation'], state2['observation'])

        # 如果两个状态都是数组
        if isinstance(state1, (np.ndarray, list)) and isinstance(state2, (np.ndarray, list)):
            return np.array_equal(state1, state2)

        # 如果两个状态都是张量
        if isinstance(state1, torch.Tensor) and isinstance(state2, torch.Tensor):
            return torch.all(state1 == state2).item()

        # 其他情况，直接比较
        return state1 == state2

    def _update_state_with_goal(self, state: Any, goal: Any) -> Any:
        """
        使用目标更新状态

        Args:
            state: 原始状态
            goal: 目标

        Returns:
            更新后的状态
        """
        # 如果状态是字典
        if isinstance(state, dict):
            # 创建状态的副本
            new_state = state.copy()
            # 添加或更新目标字段
            new_state['goal'] = goal
            return new_state

        # 如果状态是数组或张量，尝试连接目标
        if isinstance(state, (np.ndarray, list)):
            # 将目标转换为数组
            goal_array = np.array(goal) if not isinstance(goal, np.ndarray) else goal
            # 连接状态和目标
            return np.concatenate([np.array(state), goal_array])

        if isinstance(state, torch.Tensor):
            # 将目标转换为张量
            goal_tensor = torch.tensor(goal) if not isinstance(goal, torch.Tensor) else goal
            # 连接状态和目标
            return torch.cat([state, goal_tensor])

        # 其他情况，返回原始状态
        return state


class TDErrorPrioritizedReplayBuffer(PrioritizedReplayBuffer):
    """
    基于时序差分误差的优先级经验回放缓冲区

    扩展基本的优先级经验回放，使用时序差分误差作为优先级指标，
    并支持自适应优先级调整。
    """

    def __init__(
        self,
        capacity: int,
        alpha: float = 0.6,
        beta: float = 0.4,
        beta_increment: float = 0.001,
        epsilon: float = 1e-6,
        td_error_max: float = 1.0,
        td_error_min: float = 0.01,
        adaptive_priority: bool = True,
        priority_decay: float = 0.9
    ):
        """
        初始化基于时序差分误差的优先级经验回放缓冲区

        Args:
            capacity: 缓冲区容量
            alpha: 优先级指数，控制采样概率与优先级的关系
            beta: 重要性采样指数，用于修正优先级采样的偏差
            beta_increment: beta的增量，随着训练进行逐渐增加beta
            epsilon: 小常数，防止优先级为0
            td_error_max: 时序差分误差的最大值，用于裁剪
            td_error_min: 时序差分误差的最小值，用于防止优先级过低
            adaptive_priority: 是否使用自适应优先级
            priority_decay: 优先级衰减因子，用于自适应优先级
        """
        super().__init__(capacity, alpha, beta, beta_increment, epsilon)
        self.td_error_max = td_error_max
        self.td_error_min = td_error_min
        self.adaptive_priority = adaptive_priority
        self.priority_decay = priority_decay

        # 存储每个经验的时序差分误差
        self.td_errors = np.zeros((capacity,), dtype=np.float32)

        # 存储每个经验的年龄（添加时间）
        self.ages = np.zeros((capacity,), dtype=np.float32)

        # 当前时间步
        self.current_step = 0

    def add(self, experience: Experience, td_error: Optional[float] = None) -> None:
        """
        添加经验

        Args:
            experience: 经验数据
            td_error: 时序差分误差，如果为None，则使用最大优先级
        """
        # 获取当前位置
        idx = self.position

        # 添加经验
        if len(self.buffer) < self.capacity:
            self.buffer.append(experience)
        else:
            self.buffer[idx] = experience

        # 设置优先级
        if td_error is not None:
            # 裁剪时序差分误差
            td_error = max(min(td_error, self.td_error_max), self.td_error_min)
            # 存储时序差分误差
            self.td_errors[idx] = td_error
            # 计算优先级
            priority = self._compute_priority(td_error, idx)
        else:
            # 如果没有提供时序差分误差，使用最大优先级
            priority = self.max_priority
            self.td_errors[idx] = self.td_error_max

        # 设置优先级
        self.priorities[idx] = priority

        # 更新年龄
        self.ages[idx] = self.current_step
        self.current_step += 1

        # 更新位置
        self.position = (self.position + 1) % self.capacity

    def _compute_priority(self, td_error: float, idx: int) -> float:
        """
        计算优先级

        Args:
            td_error: 时序差分误差
            idx: 经验索引

        Returns:
            优先级
        """
        # 基本优先级（基于时序差分误差）
        priority = (abs(td_error) + self.epsilon) ** self.alpha

        # 如果使用自适应优先级
        if self.adaptive_priority:
            # 计算经验的年龄因子（越新的经验年龄因子越高）
            age_factor = self.priority_decay ** (self.current_step - self.ages[idx])
            # 结合时序差分误差和年龄因子
            priority = priority * age_factor

        return priority

    def update_priorities(self, indices: np.ndarray, td_errors: np.ndarray) -> None:
        """
        更新优先级

        Args:
            indices: 经验索引
            td_errors: 时序差分误差
        """
        # 更新时序差分误差和优先级
        for idx, td_error in zip(indices, td_errors):
            # 裁剪时序差分误差
            td_error = max(min(td_error, self.td_error_max), self.td_error_min)
            # 存储时序差分误差
            self.td_errors[idx] = td_error
            # 计算优先级
            self.priorities[idx] = self._compute_priority(td_error, idx)

        # 更新最大优先级
        self.max_priority = max(self.priorities.max(), self.max_priority)

    def sample(self, batch_size: int) -> Tuple[Batch, np.ndarray, np.ndarray]:
        """
        采样经验批次

        Args:
            batch_size: 批次大小

        Returns:
            经验批次、索引和重要性权重
        """
        # 确保缓冲区中有足够的经验
        buffer_size = len(self.buffer)
        if batch_size > buffer_size:
            batch_size = buffer_size

        # 计算采样概率
        priorities = self.priorities[:buffer_size]

        # 如果使用自适应优先级，考虑年龄因子
        if self.adaptive_priority:
            # 计算年龄因子
            ages = self.ages[:buffer_size]
            age_factors = self.priority_decay ** (self.current_step - ages)
            # 结合优先级和年龄因子
            probabilities = priorities * age_factors
        else:
            probabilities = priorities

        # 归一化概率
        probabilities = probabilities / probabilities.sum()

        # 采样索引
        indices = np.random.choice(buffer_size, batch_size, replace=False, p=probabilities)

        # 计算重要性权重
        weights = (buffer_size * probabilities[indices]) ** (-self.beta)
        weights /= weights.max()  # 归一化

        # 增加beta
        self.beta = min(1.0, self.beta + self.beta_increment)

        # 获取经验
        experiences = [self.buffer[idx] for idx in indices]

        # 创建批次
        return Batch(experiences), indices, weights

    def get_td_errors(self) -> np.ndarray:
        """
        获取时序差分误差

        Returns:
            时序差分误差数组
        """
        return self.td_errors[:len(self.buffer)]

    def get_priorities(self) -> np.ndarray:
        """
        获取优先级

        Returns:
            优先级数组
        """
        return self.priorities[:len(self.buffer)]


class ExperienceAugmentor:
    """
    经验增强器

    实现经验增强机制，通过数据增强技术生成更多样的经验样本，
    提高模型的泛化能力和样本效率。
    """

    def __init__(
        self,
        augmentation_types: List[str] = ['noise', 'permutation', 'dropout', 'mixup'],
        noise_scale: float = 0.05,
        dropout_prob: float = 0.1,
        mixup_alpha: float = 0.2,
        augmentation_prob: float = 0.5,
        max_augmentations_per_experience: int = 2
    ):
        """
        初始化经验增强器

        Args:
            augmentation_types: 增强类型列表，支持'noise'、'permutation'、'dropout'、'mixup'
            noise_scale: 噪声尺度，用于噪声增强
            dropout_prob: 丢弃概率，用于丢弃增强
            mixup_alpha: 混合参数，用于混合增强
            augmentation_prob: 应用增强的概率
            max_augmentations_per_experience: 每个经验的最大增强数量
        """
        self.augmentation_types = augmentation_types
        self.noise_scale = noise_scale
        self.dropout_prob = dropout_prob
        self.mixup_alpha = mixup_alpha
        self.augmentation_prob = augmentation_prob
        self.max_augmentations_per_experience = max_augmentations_per_experience

        # 统计信息
        self.stats = {
            "original_experiences": 0,
            "augmented_experiences": 0,
            "augmentation_types": {aug_type: 0 for aug_type in augmentation_types}
        }

    def augment(self, experience: Experience) -> List[Experience]:
        """
        增强经验

        Args:
            experience: 原始经验

        Returns:
            增强后的经验列表，包含原始经验
        """
        # 更新统计信息
        self.stats["original_experiences"] += 1

        # 初始化结果列表，包含原始经验
        augmented_experiences = [experience]

        # 随机决定应用的增强数量
        num_augmentations = random.randint(0, self.max_augmentations_per_experience)

        # 应用增强
        for _ in range(num_augmentations):
            # 如果随机数大于增强概率，跳过该经验
            if random.random() > self.augmentation_prob:
                continue

            # 随机选择一种增强类型
            aug_type = random.choice(self.augmentation_types)

            # 应用选定的增强
            if aug_type == 'noise':
                aug_exp = self._apply_noise(experience)
            elif aug_type == 'permutation':
                aug_exp = self._apply_permutation(experience)
            elif aug_type == 'dropout':
                aug_exp = self._apply_dropout(experience)
            elif aug_type == 'mixup' and len(augmented_experiences) > 1:
                # 混合需要至少两个经验
                other_exp = random.choice(augmented_experiences[:-1])  # 不选最后一个（刚刚添加的）
                aug_exp = self._apply_mixup(experience, other_exp)
            else:
                continue

            # 如果增强成功，添加到结果列表
            if aug_exp is not None:
                augmented_experiences.append(aug_exp)

                # 更新统计信息
                self.stats["augmented_experiences"] += 1
                self.stats["augmentation_types"][aug_type] += 1

        return augmented_experiences

    def _apply_noise(self, experience: Experience) -> Optional[Experience]:
        """
        应用噪声增强

        Args:
            experience: 原始经验

        Returns:
            增强后的经验，如果增强失败则返回None
        """
        try:
            # 尝试将状态转换为数组
            state_array = self._to_numpy_array(experience.state)
            if state_array is None:
                return None

            # 添加高斯噪声
            noise = np.random.normal(0, self.noise_scale, state_array.shape)
            noisy_state = state_array + noise

            # 创建新的经验对象
            aug_exp = Experience(
                state=noisy_state,
                action=experience.action,
                reward=experience.reward,
                next_state=experience.next_state if hasattr(experience, 'next_state') else None,
                done=experience.done
            )

            # 标记为增强经验
            aug_exp.is_augmented = True
            aug_exp.augmentation_type = 'noise'

            return aug_exp
        except Exception as e:
            # 如果增强失败，返回None
            return None

    def _apply_permutation(self, experience: Experience) -> Optional[Experience]:
        """
        应用排列增强

        Args:
            experience: 原始经验

        Returns:
            增强后的经验，如果增强失败则返回None
        """
        try:
            # 尝试将状态转换为数组
            state_array = self._to_numpy_array(experience.state)
            if state_array is None or len(state_array.shape) < 1 or state_array.shape[0] < 2:
                return None

            # 随机排列部分特征
            perm_state = state_array.copy()

            # 如果是一维数组
            if len(state_array.shape) == 1:
                # 随机选择一部分特征进行排列
                feature_size = state_array.shape[0]
                perm_size = min(feature_size, max(2, int(feature_size * 0.2)))  # 排列至少20%的特征

                # 随机选择要排列的特征索引
                indices = np.random.choice(feature_size, perm_size, replace=False)

                # 打乱选定的特征
                perm_indices = np.random.permutation(indices)
                perm_state[indices] = state_array[perm_indices]
            # 如果是多维数组
            elif len(state_array.shape) > 1:
                # 随机选择一个维度进行排列
                dim = random.randint(0, len(state_array.shape) - 1)
                # 在选定维度上随机排列
                perm_indices = np.random.permutation(state_array.shape[dim])
                # 根据维度不同，排列方式也不同
                if dim == 0:
                    perm_state = state_array[perm_indices]
                elif dim == 1 and len(state_array.shape) >= 2:
                    perm_state = state_array[:, perm_indices]
                elif dim == 2 and len(state_array.shape) >= 3:
                    perm_state = state_array[:, :, perm_indices]

            # 创建新的经验对象
            aug_exp = Experience(
                state=perm_state,
                action=experience.action,
                reward=experience.reward,
                next_state=experience.next_state if hasattr(experience, 'next_state') else None,
                done=experience.done
            )

            # 标记为增强经验
            aug_exp.is_augmented = True
            aug_exp.augmentation_type = 'permutation'

            return aug_exp
        except Exception as e:
            # 如果增强失败，返回None
            return None

    def _apply_dropout(self, experience: Experience) -> Optional[Experience]:
        """
        应用丢弃增强

        Args:
            experience: 原始经验

        Returns:
            增强后的经验，如果增强失败则返回None
        """
        try:
            # 尝试将状态转换为数组
            state_array = self._to_numpy_array(experience.state)
            if state_array is None:
                return None

            # 创建丢弃掩码
            dropout_mask = np.random.binomial(1, 1 - self.dropout_prob, state_array.shape)
            # 应用丢弃
            dropout_state = state_array * dropout_mask

            # 创建新的经验对象
            aug_exp = Experience(
                state=dropout_state,
                action=experience.action,
                reward=experience.reward,
                next_state=experience.next_state if hasattr(experience, 'next_state') else None,
                done=experience.done
            )

            # 标记为增强经验
            aug_exp.is_augmented = True
            aug_exp.augmentation_type = 'dropout'

            return aug_exp
        except Exception as e:
            # 如果增强失败，返回None
            return None

    def _apply_mixup(self, experience1: Experience, experience2: Experience) -> Optional[Experience]:
        """
        应用混合增强

        Args:
            experience1: 第一个经验
            experience2: 第二个经验

        Returns:
            增强后的经验，如果增强失败则返回None
        """
        try:
            # 尝试将状态转换为数组
            state_array1 = self._to_numpy_array(experience1.state)
            state_array2 = self._to_numpy_array(experience2.state)

            if state_array1 is None or state_array2 is None or state_array1.shape != state_array2.shape:
                return None

            # 生成混合参数
            lam = np.random.beta(self.mixup_alpha, self.mixup_alpha)
            # 混合状态
            mixed_state = lam * state_array1 + (1 - lam) * state_array2

            # 混合奖励
            mixed_reward = lam * experience1.reward + (1 - lam) * experience2.reward

            # 混合完成标志（使用逻辑或）
            mixed_done = experience1.done or experience2.done

            # 创建新的经验对象
            aug_exp = Experience(
                state=mixed_state,
                action=experience1.action,  # 使用第一个经验的动作
                reward=mixed_reward,
                next_state=None,  # 混合经验不包含下一个状态
                done=mixed_done
            )

            # 标记为增强经验
            aug_exp.is_augmented = True
            aug_exp.augmentation_type = 'mixup'
            aug_exp.mixup_lambda = lam

            return aug_exp
        except Exception as e:
            # 如果增强失败，返回None
            return None

    def _to_numpy_array(self, state: Any) -> Optional[np.ndarray]:
        """
        将状态转换为数组

        Args:
            state: 状态

        Returns:
            数组形式的状态，如果转换失败则返回None
        """
        try:
            # 如果状态已经是数组
            if isinstance(state, np.ndarray):
                return state

            # 如果状态是列表或元组
            if isinstance(state, (list, tuple)):
                return np.array(state, dtype=np.float32)

            # 如果状态是张量
            if isinstance(state, torch.Tensor):
                return state.detach().cpu().numpy()

            # 如果状态是字典
            if isinstance(state, dict):
                # 如果字典包含观察字段
                if 'observation' in state:
                    return self._to_numpy_array(state['observation'])

                # 尝试将字典展平为数组
                try:
                    flattened = []
                    for k, v in sorted(state.items()):
                        if isinstance(v, (int, float, bool)):
                            flattened.append(float(v))
                        elif isinstance(v, (np.ndarray, list, tuple, torch.Tensor)):
                            flat_v = self._to_numpy_array(v)
                            if flat_v is not None:
                                flattened.extend(flat_v.flatten())
                    return np.array(flattened, dtype=np.float32)
                except:
                    return None

            # 如果状态是标量
            if isinstance(state, (int, float, bool)):
                return np.array([float(state)], dtype=np.float32)

            # 其他情况，返回None
            return None
        except:
            return None

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            统计信息字典
        """
        return self.stats


def test_advanced_replay():
    """
    测试高级经验回放

    这个函数用于测试事后经验回放、基于时序差分误差的优先级经验回放和经验增强器的性能。
    """
    import time
    import numpy as np
    import torch
    from cardgame_ai.core.base import Experience

    # 设置随机种子以确保可重现性
    np.random.seed(42)
    random.seed(42)
    torch.manual_seed(42)

    # 测试事后经验回放
    print("\n=== 测试事后经验回放 ===")

    # 创建事后经验回放缓冲区
    her_buffer = HindsightExperienceReplay(
        capacity=1000,
        k_goals=4,
        strategy='future',
        goal_selection_probability=0.8
    )

    # 生成测试数据
    states = np.random.rand(10, 10)  # 10个状态，每个状态是10维向量
    actions = np.random.randint(0, 5, 10)  # 10个动作，每个动作是0-4的整数
    rewards = np.random.rand(10)  # 10个奖励
    next_states = np.random.rand(10, 10)  # 10个下一个状态
    dones = np.zeros(10)  # 10个完成标志
    dones[-1] = 1  # 最后一个经验表示回合结束

    # 添加经验
    for i in range(10):
        experience = Experience(
            state=states[i],
            action=actions[i],
            reward=rewards[i],
            next_state=next_states[i],
            done=bool(dones[i])
        )
        her_buffer.add(experience)

    # 采样经验
    batch = her_buffer.sample(5)

    print(f"\u539f始经验数量: {her_buffer.stats['original_experiences']}")
    print(f"\u4e8b后经验数量: {her_buffer.stats['hindsight_experiences']}")
    print(f"\u91c7样批次大小: {len(batch)}")

    # 测试基于时序差分误差的优先级经验回放
    print("\n=== 测试基于时序差分误差的优先级经验回放 ===")

    # 创建基于时序差分误差的优先级经验回放缓冲区
    td_buffer = TDErrorPrioritizedReplayBuffer(
        capacity=1000,
        alpha=0.6,
        beta=0.4,
        adaptive_priority=True
    )

    # 添加经验和时序差分误差
    td_errors = np.abs(np.random.randn(10))  # 10个时序差分误差

    for i in range(10):
        experience = Experience(
            state=states[i],
            action=actions[i],
            reward=rewards[i],
            next_state=next_states[i],
            done=bool(dones[i])
        )
        td_buffer.add(experience, td_errors[i])

    # 采样经验
    batch, indices, weights = td_buffer.sample(5)

    print(f"\u91c7样批次大小: {len(batch)}")
    print(f"\u6743重形状: {weights.shape}")
    print(f"\u6743重范围: [{weights.min():.4f}, {weights.max():.4f}]")

    # 更新优先级
    new_td_errors = np.abs(np.random.randn(len(indices)))
    td_buffer.update_priorities(indices, new_td_errors)

    # 测试经验增强器
    print("\n=== 测试经验增强器 ===")

    # 创建经验增强器
    augmentor = ExperienceAugmentor(
        augmentation_types=['noise', 'permutation', 'dropout'],
        noise_scale=0.05,
        dropout_prob=0.1,
        augmentation_prob=0.8,
        max_augmentations_per_experience=2
    )

    # 生成测试数据
    experience = Experience(
        state=np.random.rand(20),
        action=np.random.randint(0, 5),
        reward=np.random.rand(),
        next_state=np.random.rand(20),
        done=False
    )

    # 应用增强
    start_time = time.time()
    augmented_experiences = augmentor.augment(experience)
    augmentation_time = time.time() - start_time

    print(f"\u539f始经验数量: {augmentor.stats['original_experiences']}")
    print(f"\u589e强经验数量: {augmentor.stats['augmented_experiences']}")
    print(f"\u603b经验数量: {len(augmented_experiences)}")
    print(f"\u589e强时间: {augmentation_time:.4f} 秒")
    print(f"\u589e强类型统计: {augmentor.stats['augmentation_types']}")

    return {
        'her_buffer': her_buffer,
        'td_buffer': td_buffer,
        'augmentor': augmentor
    }


if __name__ == "__main__":
    # 如果直接运行这个文件，则执行测试
    test_advanced_replay()