"""
图处理工具类

提供用于处理图结构的工具函数，支持图神经网络的数据处理和转换。
"""

import torch
import numpy as np
import networkx as nx
from typing import Dict, List, Tuple, Any, Optional, Union

# 尝试导入PyTorch Geometric，如果不可用则提供备用实现
try:
    import torch_geometric
    from torch_geometric.data import Data as PyGData
    from torch_geometric.data import Batch
    TORCH_GEOMETRIC_AVAILABLE = True
except ImportError:
    TORCH_GEOMETRIC_AVAILABLE = False
    
from cardgame_ai.games.doudizhu.card import Card, CardRank, CardSuit


def get_card_feature_dim() -> int:
    """
    获取牌特征维度
    
    Returns:
        int: 牌特征维度
    """
    # 点数特征(15) + 花色特征(4) + 是否是王(1) + 是否是2(1) + 是否是A(1)
    return 22


def get_card_features(card: Card) -> List[float]:
    """
    获取牌的特征向量
    
    Args:
        card: 牌对象
        
    Returns:
        List[float]: 特征向量
    """
    # 点数独热编码 (15维)
    rank_onehot = [0.0] * 15
    rank_onehot[int(card.rank)] = 1.0
    
    # 花色独热编码 (4维)
    suit_onehot = [0.0] * 4
    if card.suit is not None:
        suit_onehot[int(card.suit)] = 1.0
    
    # 是否是王 (1维)
    is_joker = 1.0 if card.rank in [CardRank.SMALL_JOKER, CardRank.BIG_JOKER] else 0.0
    
    # 是否是2 (1维)
    is_two = 1.0 if card.rank == CardRank.TWO else 0.0
    
    # 是否是A (1维)
    is_ace = 1.0 if card.rank == CardRank.ACE else 0.0
    
    # 组合特征
    features = rank_onehot + suit_onehot + [is_joker, is_two, is_ace]
    
    return features


def get_edge_feature_dim() -> int:
    """
    获取边特征维度
    
    Returns:
        int: 边特征维度
    """
    # 边类型独热编码
    return 8


def get_edge_features(edge_type: str) -> List[float]:
    """
    获取边的特征向量
    
    Args:
        edge_type: 边类型
        
    Returns:
        List[float]: 特征向量
    """
    # 边类型映射
    edge_type_mapping = {
        'same_rank': 0,
        'consecutive': 1,
        'same_suit': 2,
        'pair': 3,
        'triplet': 4,
        'bomb': 5,
        'straight': 6,
        'default': 7
    }
    
    # 边类型独热编码
    edge_type_idx = edge_type_mapping.get(edge_type, edge_type_mapping['default'])
    edge_features = [0.0] * len(edge_type_mapping)
    edge_features[edge_type_idx] = 1.0
    
    return edge_features


def networkx_to_pyg(G: nx.Graph) -> Optional[PyGData]:
    """
    将NetworkX图转换为PyTorch Geometric数据对象
    
    Args:
        G: NetworkX图
        
    Returns:
        Optional[PyGData]: PyTorch Geometric数据对象，如果PyTorch Geometric不可用则返回None
    """
    if not TORCH_GEOMETRIC_AVAILABLE:
        raise ImportError("PyTorch Geometric不可用，请安装相关依赖")
    
    # 节点特征
    node_features = []
    node_mapping = {}  # 节点名称到索引的映射
    
    # 为每个节点分配索引
    for i, (node, attrs) in enumerate(G.nodes(data=True)):
        node_mapping[node] = i
        
        # 获取节点特征
        features = attrs.get('features', [])
        if not features:
            # 如果没有特征，使用节点类型的独热编码
            node_type = attrs.get('type', 'unknown')
            type_mapping = {'card': 0, 'player': 1, 'opponent': 2, 'unknown': 3}
            type_idx = type_mapping.get(node_type, 3)
            features = [0.0] * 4
            features[type_idx] = 1.0
        
        node_features.append(features)
    
    # 边索引和属性
    edge_index = []
    edge_attr = []
    
    # 处理边
    for u, v, attrs in G.edges(data=True):
        # 获取节点索引
        u_idx = node_mapping[u]
        v_idx = node_mapping[v]
        
        # 添加边（无向图需要添加两个方向）
        edge_index.append([u_idx, v_idx])
        edge_index.append([v_idx, u_idx])
        
        # 获取边特征
        edge_type = attrs.get('type', 'default')
        features = get_edge_features(edge_type)
        
        # 添加边特征（两个方向都添加）
        edge_attr.append(features)
        edge_attr.append(features)
    
    # 转换为PyTorch张量
    x = torch.tensor(node_features, dtype=torch.float)
    edge_index = torch.tensor(edge_index, dtype=torch.long).t().contiguous()
    edge_attr = torch.tensor(edge_attr, dtype=torch.float)
    
    # 创建PyTorch Geometric数据对象
    data = PyGData(x=x, edge_index=edge_index, edge_attr=edge_attr)
    
    return data


def batch_graphs(graph_list: List[PyGData]) -> PyGData:
    """
    批量处理多个图
    
    Args:
        graph_list: 图列表
        
    Returns:
        PyGData: 批处理后的图
    """
    if not TORCH_GEOMETRIC_AVAILABLE:
        raise ImportError("PyTorch Geometric不可用，请安装相关依赖")
    
    return Batch.from_data_list(graph_list)


def visualize_card_graph(G: nx.Graph, save_path: Optional[str] = None):
    """
    可视化手牌图
    
    Args:
        G: 手牌图
        save_path: 保存路径，如果为None则显示图像
    """
    import matplotlib.pyplot as plt
    
    # 创建图形
    plt.figure(figsize=(12, 10))
    
    # 获取节点位置
    pos = nx.spring_layout(G, seed=42)
    
    # 获取节点类型
    node_types = {}
    for node, attrs in G.nodes(data=True):
        node_type = attrs.get('type', 'unknown')
        if node_type not in node_types:
            node_types[node_type] = []
        node_types[node_type].append(node)
    
    # 获取边类型
    edge_types = {}
    for u, v, attrs in G.edges(data=True):
        edge_type = attrs.get('type', 'default')
        if edge_type not in edge_types:
            edge_types[edge_type] = []
        edge_types[edge_type].append((u, v))
    
    # 绘制节点
    for node_type, nodes in node_types.items():
        nx.draw_networkx_nodes(G, pos, nodelist=nodes, node_color=get_node_color(node_type),
                              node_size=500, alpha=0.8, label=node_type)
    
    # 绘制边
    for edge_type, edges in edge_types.items():
        nx.draw_networkx_edges(G, pos, edgelist=edges, width=2, alpha=0.5,
                              edge_color=get_edge_color(edge_type), label=edge_type)
    
    # 绘制节点标签
    node_labels = {}
    for node, attrs in G.nodes(data=True):
        if 'card' in attrs:
            card = attrs['card']
            node_labels[node] = f"{card.rank.name}"
        else:
            node_labels[node] = node
    
    nx.draw_networkx_labels(G, pos, labels=node_labels, font_size=10)
    
    # 添加图例
    plt.legend()
    plt.title("Card Graph Visualization")
    plt.axis('off')
    
    # 保存或显示图像
    if save_path:
        plt.savefig(save_path)
        plt.close()
    else:
        plt.show()


def get_node_color(node_type: str) -> str:
    """
    获取节点颜色
    
    Args:
        node_type: 节点类型
        
    Returns:
        str: 颜色代码
    """
    color_mapping = {
        'card': 'skyblue',
        'player': 'lightgreen',
        'opponent': 'salmon',
        'unknown': 'gray'
    }
    return color_mapping.get(node_type, 'gray')


def get_edge_color(edge_type: str) -> str:
    """
    获取边颜色
    
    Args:
        edge_type: 边类型
        
    Returns:
        str: 颜色代码
    """
    color_mapping = {
        'same_rank': 'red',
        'consecutive': 'blue',
        'same_suit': 'green',
        'pair': 'purple',
        'triplet': 'orange',
        'bomb': 'brown',
        'straight': 'cyan',
        'default': 'gray'
    }
    return color_mapping.get(edge_type, 'gray')
