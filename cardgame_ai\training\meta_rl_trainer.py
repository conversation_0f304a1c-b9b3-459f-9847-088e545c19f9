"""
元强化学习训练器模块

实现基于元强化学习的快速适应机制，使AI能够对新对手或环境变化进行快速适应。
"""

import os
import time
import logging
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Any, Optional, Union, Callable
from collections import defaultdict, deque

from cardgame_ai.core.base import Experience, Batch
from cardgame_ai.core.agent import Agent
from cardgame_ai.core.environment import Environment
from cardgame_ai.utils.task_sampler import TaskSampler, Task
from cardgame_ai.training.ewc import EWCPenalty  # 导入EWC模块

# 尝试导入higher库，用于实现MAML
try:
    import higher
    HIGHER_AVAILABLE = True
except ImportError:
    HIGHER_AVAILABLE = False
    logging.warning("higher库不可用，MAML算法将不可用。请使用pip install higher安装。")

# 配置日志
logger = logging.getLogger(__name__)


class MetaRLTrainer:
    """
    元强化学习训练器

    实现基于元强化学习的快速适应机制，使AI能够对新对手或环境变化进行快速适应。
    支持MAML和Reptile等算法。
    """

    def __init__(
        self,
        base_model: nn.Module,
        meta_optimizer: torch.optim.Optimizer,
        task_sampler: TaskSampler,
        inner_lr: float = 0.01,
        num_inner_steps: int = 5,
        meta_algorithm: str = 'maml',
        device: str = 'cuda' if torch.cuda.is_available() else 'cpu',
        # EWC相关参数
        use_ewc: bool = False,
        ewc_lambda: float = 1.0,
        ewc_diagonal_fisher: bool = True,
        ewc_fisher_samples: int = 200
    ):
        """
        初始化元强化学习训练器

        Args:
            base_model: 基础模型
            meta_optimizer: 元优化器
            task_sampler: 任务采样器
            inner_lr: 内循环学习率
            num_inner_steps: 内循环步数
            meta_algorithm: 元算法，可选'maml'或'reptile'
            device: 设备
            use_ewc: 是否使用EWC防遗忘机制
            ewc_lambda: EWC正则化强度
            ewc_diagonal_fisher: 是否使用对角Fisher近似
            ewc_fisher_samples: Fisher矩阵估计样本数
        """
        self.base_model = base_model
        self.meta_optimizer = meta_optimizer
        self.task_sampler = task_sampler
        self.inner_lr = inner_lr
        self.num_inner_steps = num_inner_steps
        self.meta_algorithm = meta_algorithm
        self.device = device
        
        # EWC相关参数
        self.use_ewc = use_ewc
        self.ewc_lambda = ewc_lambda
        self.ewc_diagonal_fisher = ewc_diagonal_fisher
        self.ewc_fisher_samples = ewc_fisher_samples
        
        # 如果启用EWC，初始化EWC惩罚计算器
        self.ewc_penalty = None
        if self.use_ewc:
            logger.info(f"初始化EWC防遗忘机制，lambda={ewc_lambda}, diagonal_fisher={ewc_diagonal_fisher}")
            self.ewc_penalty = EWCPenalty(
                model=base_model,
                device=device,
                diagonal_fisher=ewc_diagonal_fisher,
                fisher_estimation_samples=ewc_fisher_samples
            )

        # 检查meta_algorithm是否有效，支持 'maml'、'reptile' 及 'both'
        if meta_algorithm not in ['maml', 'reptile', 'both']:
            raise ValueError(f"不支持的元算法: {meta_algorithm}")

        # 如果使用MAML，检查higher库是否可用
        if meta_algorithm == 'maml' and not HIGHER_AVAILABLE:
            raise ImportError("MAML算法需要higher库。请使用pip install higher安装。")

        # 统计信息
        self.stats = {
            "meta_updates": 0,
            "inner_updates": 0,
            "meta_losses": [],
            "adaptation_times": [],
            "meta_update_times": []
        }

    def _calculate_loss(self, model: nn.Module, batch: Any) -> torch.Tensor:
        """
        计算损失

        Args:
            model: 模型
            batch: 批次数据，可以是字典、字典列表或其他格式

        Returns:
            损失
        """
        # 添加调试信息
        logger.debug(f"批次数据类型: {type(batch)}")
        if isinstance(batch, list) and batch:
            logger.debug(f"批次数据第一个元素类型: {type(batch[0])}")
            if isinstance(batch[0], dict):
                logger.debug(f"批次数据第一个元素键: {batch[0].keys()}")

        # 处理列表类型的批次数据
        if isinstance(batch, list):
            if not batch:
                return torch.tensor(0.0, device=self.device)

            # 直接使用第一个元素
            # 在测试中，每个元素都是一个字典，包含observations和actions
            if len(batch) > 0:
                sample = batch[0]
                if isinstance(sample, dict) and "observations" in sample and "actions" in sample:
                    observations = sample["observations"]
                    actions = sample["actions"]
                else:
                    logger.warning(f"不支持的批次数据格式: {type(sample)}")
                    return torch.tensor(0.0, device=self.device)
            else:
                return torch.tensor(0.0, device=self.device)
        elif isinstance(batch, dict):
            # 处理字典类型的批次数据
            observations = batch.get("observations", [])
            actions = batch.get("actions", [])

            if not observations or not actions:
                return torch.tensor(0.0, device=self.device)
        else:
            # 不支持的批次数据类型
            logger.warning(f"不支持的批次数据类型: {type(batch)}")
            return torch.tensor(0.0, device=self.device)

        # 添加调试信息
        logger.debug(f"observations类型: {type(observations)}")
        logger.debug(f"actions类型: {type(actions)}")

        # 确保数据是张量
        if not isinstance(observations, torch.Tensor):
            logger.debug("observations不是张量，尝试转换")
            observations = torch.tensor(observations, device=self.device)
        if not isinstance(actions, torch.Tensor):
            logger.debug("actions不是张量，尝试转换")
            actions = torch.tensor(actions, device=self.device)

        # 前向传播
        logits = model(observations)

        # 计算交叉熵损失
        loss = F.cross_entropy(logits, actions)

        return loss
    
    def _calculate_total_loss(self, model: nn.Module, batch: Any) -> torch.Tensor:
        """
        计算包含EWC惩罚项的总损失
        
        Args:
            model: 模型
            batch: 批次数据
            
        Returns:
            总损失
        """
        # 计算任务损失
        task_loss = self._calculate_loss(model, batch)
        
        # 计算EWC惩罚项
        ewc_penalty = torch.tensor(0.0, device=self.device)
        if self.use_ewc and self.ewc_penalty is not None:
            ewc_penalty = self.ewc_penalty.calculate()
            if ewc_penalty.item() > 0:
                logger.debug(f"EWC惩罚项: {ewc_penalty.item()}")
                
        # 返回总损失
        total_loss = task_loss + self.ewc_lambda * ewc_penalty
        return total_loss

    def meta_train_step_maml(self, num_tasks: int = 5) -> float:
        """
        使用MAML算法进行元训练步骤

        Args:
            num_tasks: 任务数量

        Returns:
            元损失
        """
        if not HIGHER_AVAILABLE:
            raise ImportError("MAML算法需要higher库。请使用pip install higher安装。")

        self.meta_optimizer.zero_grad()
        meta_update_start_time = time.time()

        # 采样任务
        tasks = self.task_sampler.sample_tasks(num_tasks)
        if not tasks:
            logger.warning("没有可用的任务")
            return 0.0

        total_meta_loss = 0.0

        for task in tasks:
            # 创建内循环优化器
            inner_optimizer = torch.optim.SGD(
                self.base_model.parameters(),
                lr=self.inner_lr
            )

            # 使用higher库创建可微分优化循环
            with higher.innerloop_ctx(self.base_model, inner_optimizer) as (fmodel, diffopt):
                # 内循环：适应任务
                for _ in range(self.num_inner_steps):
                    # 采样支持集数据
                    support_batch = self.task_sampler.sample_data(task, 'support')
                    if not support_batch:
                        continue

                    # 计算支持集损失（使用包含EWC惩罚的总损失）
                    support_loss = self._calculate_total_loss(fmodel, support_batch)

                    # 更新模型
                    diffopt.step(support_loss)

                    # 更新统计信息
                    self.stats["inner_updates"] += 1

                # 外循环：在查询集上评估
                query_batch = self.task_sampler.sample_data(task, 'query')
                if query_batch:
                    # 计算查询集损失（不使用EWC惩罚，仅评估性能）
                    query_loss = self._calculate_loss(fmodel, query_batch)

                    # 累加元损失
                    total_meta_loss += query_loss

        # 如果没有累积任何损失，返回0
        if total_meta_loss == 0.0:
            return 0.0

        # 反向传播
        total_meta_loss.backward()

        # 更新元模型
        self.meta_optimizer.step()

        # 更新统计信息
        meta_loss_value = total_meta_loss.item()
        self.stats["meta_losses"].append(meta_loss_value)
        self.stats["meta_updates"] += 1
        self.stats["meta_update_times"].append(time.time() - meta_update_start_time)

        return meta_loss_value

    def meta_train_step_reptile(self, num_tasks: int = 5) -> float:
        """
        使用Reptile算法进行元训练步骤

        Args:
            num_tasks: 任务数量

        Returns:
            元损失
        """
        meta_update_start_time = time.time()

        # 采样任务
        tasks = self.task_sampler.sample_tasks(num_tasks)
        if not tasks:
            logger.warning("没有可用的任务")
            return 0.0

        # 保存原始参数
        original_params = [p.clone() for p in self.base_model.parameters()]

        total_meta_loss = 0.0
        task_params_list = []

        for task in tasks:
            # 创建任务特定模型（克隆基础模型）
            task_model = type(self.base_model)(*self.base_model.init_args, **self.base_model.init_kwargs)
            task_model.load_state_dict(self.base_model.state_dict())
            task_model.to(self.device)

            # 创建内循环优化器
            inner_optimizer = torch.optim.SGD(
                task_model.parameters(),
                lr=self.inner_lr
            )

            # 内循环：适应任务
            for _ in range(self.num_inner_steps):
                # 采样支持集数据
                support_batch = self.task_sampler.sample_data(task, 'support')
                if not support_batch:
                    continue

                # 计算支持集损失
                support_loss = self._calculate_loss(task_model, support_batch)

                # 更新模型
                inner_optimizer.zero_grad()
                support_loss.backward()
                inner_optimizer.step()

                # 更新统计信息
                self.stats["inner_updates"] += 1

            # 计算任务损失（用于统计）
            query_batch = self.task_sampler.sample_data(task, 'query')
            if query_batch:
                with torch.no_grad():
                    query_loss = self._calculate_loss(task_model, query_batch)
                    total_meta_loss += query_loss.item()

            # 保存任务特定参数
            task_params = [p.clone() for p in task_model.parameters()]
            task_params_list.append(task_params)

        # 如果没有任务参数，返回0
        if not task_params_list:
            return 0.0

        # 计算元更新
        self.meta_optimizer.zero_grad()

        # Reptile更新：将基础模型参数向任务特定参数的平均方向移动
        for i, p in enumerate(self.base_model.parameters()):
            # 计算所有任务参数的平均值
            mean_task_param = torch.stack([task_params[i] for task_params in task_params_list]).mean(0)

            # 计算梯度（原始参数 - 平均任务参数）
            grad = p.data - mean_task_param
            p.grad = grad

        # 更新元模型
        self.meta_optimizer.step()

        # 计算平均元损失
        avg_meta_loss = total_meta_loss / len(tasks) if tasks else 0.0

        # 更新统计信息
        self.stats["meta_losses"].append(avg_meta_loss)
        self.stats["meta_updates"] += 1
        self.stats["meta_update_times"].append(time.time() - meta_update_start_time)

        return avg_meta_loss

    def meta_train_step(self, num_tasks: int = 5):
        """
        执行元训练步骤

        Args:
            num_tasks: 任务数量

        Returns:
            元损失
        """
        if self.meta_algorithm == 'maml':
            return self.meta_train_step_maml(num_tasks)
        elif self.meta_algorithm == 'reptile':
            return self.meta_train_step_reptile(num_tasks)
        elif self.meta_algorithm == 'both':
            # 同时执行MAML和Reptile元训练，并返回平均元损失
            maml_loss = self.meta_train_step_maml(num_tasks)
            reptile_loss = self.meta_train_step_reptile(num_tasks)
            combined_loss = (maml_loss + reptile_loss) / 2.0
            return combined_loss
        else:
            raise ValueError(f"不支持的元算法: {self.meta_algorithm}")

    def adapt(self, task: Union[str, Task], num_steps: int = None,
             learning_rate: float = None) -> nn.Module:
        """
        快速适应到特定任务

        Args:
            task: 任务或任务ID
            num_steps: 适应步数，如果为None则使用self.num_inner_steps
            learning_rate: 学习率，如果为None则使用self.inner_lr

        Returns:
            适应后的模型
        """
        adaptation_start_time = time.time()

        # 获取任务
        if isinstance(task, str):
            task = self.task_sampler.get_task(task)
            if task is None:
                raise ValueError(f"任务不存在: {task}")

        # 使用默认值
        if num_steps is None:
            num_steps = self.num_inner_steps
        if learning_rate is None:
            learning_rate = self.inner_lr

        # 创建适应模型（克隆基础模型）
        adapted_model = type(self.base_model)(*self.base_model.init_args, **self.base_model.init_kwargs)
        adapted_model.load_state_dict(self.base_model.state_dict())
        adapted_model.to(self.device)

        # 创建优化器
        optimizer = torch.optim.SGD(
            adapted_model.parameters(),
            lr=learning_rate
        )

        # 适应循环
        for _ in range(num_steps):
            # 采样支持集数据
            support_batch = self.task_sampler.sample_data(task, 'support')
            if not support_batch:
                continue

            # 计算损失
            loss = self._calculate_loss(adapted_model, support_batch)

            # 更新模型
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()

        # 更新统计信息
        adaptation_time = time.time() - adaptation_start_time
        self.stats["adaptation_times"].append(adaptation_time)

        return adapted_model

    def evaluate(self, task: Union[str, Task], model: Optional[nn.Module] = None) -> Dict[str, float]:
        """
        评估模型在特定任务上的性能

        Args:
            task: 任务或任务ID
            model: 要评估的模型，如果为None则使用基础模型

        Returns:
            评估结果
        """
        # 获取任务
        if isinstance(task, str):
            task = self.task_sampler.get_task(task)
            if task is None:
                raise ValueError(f"任务不存在: {task}")

        # 使用基础模型（如果没有提供模型）
        if model is None:
            model = self.base_model

        # 采样查询集数据
        query_batch = self.task_sampler.sample_data(task, 'query')
        if not query_batch:
            return {"loss": float('inf'), "accuracy": 0.0}

        # 计算损失
        with torch.no_grad():
            loss = self._calculate_loss(model, query_batch)

            # 计算准确率（如果可能）
            accuracy = 0.0
            if "observations" in query_batch and "actions" in query_batch:
                observations = query_batch["observations"]
                actions = query_batch["actions"]

                if not isinstance(observations, torch.Tensor):
                    observations = torch.tensor(observations, device=self.device)
                if not isinstance(actions, torch.Tensor):
                    actions = torch.tensor(actions, device=self.device)

                logits = model(observations)
                predictions = torch.argmax(logits, dim=1)
                accuracy = (predictions == actions).float().mean().item()

        return {"loss": loss.item(), "accuracy": accuracy}

    def save(self, path: str) -> None:
        """
        保存元强化学习训练器

        Args:
            path: 保存路径
        """
        # 创建保存目录
        os.makedirs(os.path.dirname(path), exist_ok=True)

        # 保存模型
        model_path = os.path.join(path, "base_model.pt")
        torch.save(self.base_model.state_dict(), model_path)

        # 保存优化器
        optimizer_path = os.path.join(path, "meta_optimizer.pt")
        torch.save(self.meta_optimizer.state_dict(), optimizer_path)

        # 保存配置和统计信息
        config = {
            "inner_lr": self.inner_lr,
            "num_inner_steps": self.num_inner_steps,
            "meta_algorithm": self.meta_algorithm,
            "device": self.device,
            "stats": self.stats
        }
        config_path = os.path.join(path, "config.pt")
        torch.save(config, config_path)

        # 如果启用了EWC，保存EWC状态
        if self.use_ewc and self.ewc_penalty is not None:
            ewc_path = os.path.splitext(path)[0] + "_ewc.pt"
            self.ewc_penalty.save(ewc_path)
            config['ewc_path'] = ewc_path

        logger.info(f"元强化学习训练器已保存到: {path}")

    def load(self, path: str) -> None:
        """
        加载元强化学习训练器

        Args:
            path: 加载路径
        """
        # 加载模型
        model_path = os.path.join(path, "base_model.pt")
        if os.path.exists(model_path):
            self.base_model.load_state_dict(torch.load(model_path, map_location=self.device))

        # 加载优化器
        optimizer_path = os.path.join(path, "meta_optimizer.pt")
        if os.path.exists(optimizer_path):
            self.meta_optimizer.load_state_dict(torch.load(optimizer_path, map_location=self.device))

        # 加载配置和统计信息
        config_path = os.path.join(path, "config.pt")
        if os.path.exists(config_path):
            config = torch.load(config_path, map_location=self.device)
            self.inner_lr = config.get("inner_lr", self.inner_lr)
            self.num_inner_steps = config.get("num_inner_steps", self.num_inner_steps)
            self.meta_algorithm = config.get("meta_algorithm", self.meta_algorithm)
            self.stats = config.get("stats", self.stats)

        # EWC相关
        self.use_ewc = config.get('use_ewc', self.use_ewc)
        self.ewc_lambda = config.get('ewc_lambda', self.ewc_lambda)
        self.ewc_diagonal_fisher = config.get('ewc_diagonal_fisher', self.ewc_diagonal_fisher)
        self.ewc_fisher_samples = config.get('ewc_fisher_samples', self.ewc_fisher_samples)

        # 如果启用了EWC并且有EWC状态文件，则加载EWC状态
        if self.use_ewc and 'ewc_path' in config:
            if self.ewc_penalty is None:
                self.ewc_penalty = EWCPenalty(
                    model=self.base_model,
                    device=self.device,
                    diagonal_fisher=self.ewc_diagonal_fisher,
                    fisher_estimation_samples=self.ewc_fisher_samples
                )
            
            self.ewc_penalty.load(config['ewc_path'])
        elif self.use_ewc and self.ewc_penalty is None:
            self.ewc_penalty = EWCPenalty(
                model=self.base_model,
                device=self.device,
                diagonal_fisher=self.ewc_diagonal_fisher,
                fisher_estimation_samples=self.ewc_fisher_samples
            )

        logger.info(f"元强化学习训练器已加载: {path}")

    def register_ewc_task(self, 
                      task: Union[str, Task], 
                      data_loader: Optional[torch.utils.data.DataLoader] = None) -> Optional[str]:
        """
        注册EWC任务，用于在线学习过程中防止遗忘
        
        Args:
            task: 任务名称或任务对象
            data_loader: 用于计算Fisher矩阵的数据加载器，如果为None则使用任务采样器获取
            
        Returns:
            任务ID，如果EWC未启用则返回None
        """
        if not self.use_ewc or self.ewc_penalty is None:
            logger.warning("EWC未启用，无法注册任务")
            return None
            
        # 如果是任务对象，则获取任务ID
        task_id = task if isinstance(task, str) else task.id
        logger.info(f"注册EWC任务: {task_id}")
        
        # 如果未提供数据加载器，则从任务采样器获取
        task_data = None
        if data_loader is None:
            task_obj = task if isinstance(task, Task) else self.task_sampler.get_task(task)
            if task_obj:
                task_data = self.task_sampler.sample_data(task_obj, 'support', batch_size=self.ewc_fisher_samples)
        
        # 创建用于计算Fisher矩阵的函数
        def log_likelihood_fn(model, batch):
            # 计算负对数似然（与任务损失相关）
            return -self._calculate_loss(model, batch)
        
        # 如果有数据加载器，则使用数据加载器
        if data_loader is not None:
            task_id = self.ewc_penalty.register_task(
                task_id=task_id,
                data_loader=data_loader,
                log_likelihood_fn=log_likelihood_fn
            )
        # 如果有任务数据，则创建一个简单的数据加载器
        elif task_data is not None:
            # 创建一个简单的数据加载器
            class SimpleDataLoader:
                def __init__(self, data):
                    self.data = data
                
                def __iter__(self):
                    yield self.data
            
            task_id = self.ewc_penalty.register_task(
                task_id=task_id,
                data_loader=SimpleDataLoader(task_data),
                log_likelihood_fn=log_likelihood_fn
            )
        else:
            logger.warning(f"无法获取任务 {task_id} 的数据，跳过注册")
            return None
            
        return task_id
