#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
人机交互数据处理脚本

从游戏服务器日志或本地对局记录中提取、清洗、处理和存储人机交互经验数据。
"""

import os
import sys
import json
import logging
import argparse
import datetime
import glob
import time
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union

# 添加项目根目录到系统路径，以便导入项目模块
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

from cardgame_ai.core.replay_buffer import ReplayBuffer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class HumanDataProcessor:
    """
    人机交互数据处理器类
    
    从多个数据源收集、处理和存储人机交互数据。
    """
    
    def __init__(
        self,
        input_dir: str,
        output_dir: str,
        game_type: str = 'doudizhu',
        buffer_capacity: int = 100000,
        min_quality_score: float = 0.3
    ):
        """
        初始化数据处理器
        
        Args:
            input_dir: 输入数据目录
            output_dir: 输出数据目录
            game_type: 游戏类型，默认为斗地主
            buffer_capacity: 经验回放缓冲区容量
            min_quality_score: 最低数据质量分数
        """
        self.input_dir = input_dir
        self.output_dir = output_dir
        self.game_type = game_type
        self.buffer_capacity = buffer_capacity
        self.min_quality_score = min_quality_score
        
        # 创建经验回放缓冲区
        self.replay_buffer = ReplayBuffer(capacity=buffer_capacity)
        
        # 创建输出目录（如果不存在）
        os.makedirs(output_dir, exist_ok=True)
        
        # 记录处理统计数据
        self.stats = {
            'total_files': 0,
            'processed_files': 0,
            'skipped_files': 0,
            'total_experiences': 0,
            'valid_experiences': 0,
            'invalid_experiences': 0,
            'low_quality_experiences': 0
        }
        
        logger.info(f"数据处理器初始化完成，输入目录：{input_dir}，输出目录：{output_dir}")
    
    def process_files(self, pattern: str = "*.json") -> None:
        """
        处理输入目录中匹配指定模式的所有文件
        
        Args:
            pattern: 文件匹配模式，默认为所有JSON文件
        """
        # 获取所有匹配的文件
        file_paths = glob.glob(os.path.join(self.input_dir, pattern))
        self.stats['total_files'] = len(file_paths)
        
        if len(file_paths) == 0:
            logger.warning(f"未找到匹配的文件：{os.path.join(self.input_dir, pattern)}")
            return
        
        logger.info(f"找到 {len(file_paths)} 个匹配的文件")
        
        # 处理每个文件
        for file_path in file_paths:
            try:
                self._process_single_file(file_path)
                self.stats['processed_files'] += 1
            except Exception as e:
                logger.error(f"处理文件时出错：{file_path}, 错误：{e}")
                self.stats['skipped_files'] += 1
        
        logger.info(f"文件处理完成，成功处理 {self.stats['processed_files']} 个文件，"
                  f"跳过 {self.stats['skipped_files']} 个文件")
    
    def _process_single_file(self, file_path: str) -> None:
        """
        处理单个文件
        
        Args:
            file_path: 文件路径
        """
        logger.info(f"正在处理文件：{file_path}")
        
        # 根据文件类型选择解析方法
        file_ext = os.path.splitext(file_path)[1].lower()
        
        if file_ext == '.json':
            experiences = self._parse_json_file(file_path)
        elif file_ext == '.log':
            experiences = self._parse_log_file(file_path)
        else:
            logger.warning(f"不支持的文件类型：{file_ext}")
            return
        
        if not experiences:
            logger.warning(f"文件中未找到有效经验数据：{file_path}")
            return
        
        self.stats['total_experiences'] += len(experiences)
        
        # 清洗和验证数据
        valid_experiences = self._clean_and_validate_experiences(experiences)
        self.stats['valid_experiences'] += len(valid_experiences)
        self.stats['invalid_experiences'] += len(experiences) - len(valid_experiences)
        
        # 评估数据质量
        quality_experiences = self._evaluate_quality(valid_experiences)
        self.stats['low_quality_experiences'] += len(valid_experiences) - len(quality_experiences)
        
        # 添加到回放缓冲区
        self.replay_buffer.add_experiences(quality_experiences)
        
        logger.info(f"文件处理完成：{file_path}，有效经验数据 {len(quality_experiences)} 条")
    
    def _parse_json_file(self, file_path: str) -> List[Dict[str, Any]]:
        """
        解析JSON格式的经验数据文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            List[Dict[str, Any]]: 经验数据列表
        """
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 检查数据格式
        if isinstance(data, list):
            # 已经是经验列表格式
            return data
        elif isinstance(data, dict) and 'experiences' in data:
            # 带有元数据的经验数据格式
            return data['experiences']
        elif isinstance(data, dict) and 'game_record' in data:
            # 游戏记录格式，需要转换为经验数据
            return self._convert_game_record_to_experiences(data)
        else:
            logger.warning(f"未知的JSON数据格式：{file_path}")
            return []
    
    def _parse_log_file(self, file_path: str) -> List[Dict[str, Any]]:
        """
        解析日志格式的经验数据文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            List[Dict[str, Any]]: 经验数据列表
        """
        experiences = []
        
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line:
                    continue
                
                try:
                    # 尝试解析JSON行
                    record = json.loads(line)
                    if self._is_valid_experience_record(record):
                        experiences.append(record)
                except json.JSONDecodeError:
                    # 如果不是JSON格式，尝试其他解析方法
                    experience = self._parse_log_line(line)
                    if experience:
                        experiences.append(experience)
        
        return experiences
    
    def _parse_log_line(self, line: str) -> Optional[Dict[str, Any]]:
        """
        解析单行日志记录
        
        Args:
            line: 日志行
            
        Returns:
            Optional[Dict[str, Any]]: 解析出的经验数据，如果无法解析则返回None
        """
        # 这里需要根据具体的日志格式进行定制
        # 示例格式：[timestamp] [player_id] [state] [action] [reward]
        try:
            # 简单示例，实际应根据真实日志格式调整
            parts = line.split('|')
            if len(parts) >= 5:
                timestamp = parts[0].strip()
                player_id = parts[1].strip()
                state = parts[2].strip()
                action = parts[3].strip()
                reward = float(parts[4].strip())
                
                return {
                    'timestamp': timestamp,
                    'player_id': player_id,
                    'state': state,
                    'action': action,
                    'reward': reward
                }
        except Exception as e:
            logger.debug(f"解析日志行时出错：{line}, 错误：{e}")
        
        return None
    
    def _is_valid_experience_record(self, record: Dict[str, Any]) -> bool:
        """
        检查记录是否是有效的经验数据
        
        Args:
            record: 记录数据
            
        Returns:
            bool: 是否是有效的经验数据
        """
        # 检查必要字段是否存在
        required_fields = ['state', 'action', 'reward']
        
        return all(field in record for field in required_fields)
    
    def _convert_game_record_to_experiences(self, game_record: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        将游戏记录转换为经验数据
        
        Args:
            game_record: 游戏记录
            
        Returns:
            List[Dict[str, Any]]: 经验数据列表
        """
        experiences = []
        
        # 检查游戏记录格式
        if 'game_record' not in game_record or 'actions' not in game_record['game_record']:
            logger.warning("游戏记录格式无效")
            return experiences
        
        # 提取游戏记录数据
        game_data = game_record['game_record']
        actions = game_data.get('actions', [])
        states = game_data.get('states', [])
        rewards = game_data.get('rewards', [])
        
        # 如果没有状态数据，可能需要从其他字段重建
        if not states and 'initial_state' in game_data:
            states = self._reconstruct_states(game_data['initial_state'], actions)
        
        # 确保数据长度一致
        min_length = min(len(actions), len(states))
        if len(rewards) < min_length:
            # 如果奖励数据不足，使用0填充
            rewards.extend([0] * (min_length - len(rewards)))
        
        # 构建经验数据
        for i in range(min_length):
            if i < len(states) - 1:  # 确保有下一个状态
                experience = {
                    'state': states[i],
                    'action': actions[i],
                    'reward': rewards[i] if i < len(rewards) else 0,
                    'next_state': states[i + 1],
                    'done': i == min_length - 1  # 最后一步设置为done
                }
                experiences.append(experience)
        
        return experiences
    
    def _reconstruct_states(self, initial_state: Dict[str, Any], actions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        根据初始状态和动作序列重建状态序列（简化版）
        
        Args:
            initial_state: 初始状态
            actions: 动作序列
            
        Returns:
            List[Dict[str, Any]]: 状态序列
        """
        # 注意：这是一个简化实现，实际应根据游戏规则进行状态转换
        # 在实际应用中，应使用游戏引擎的状态转换函数
        
        states = [initial_state.copy()]
        current_state = initial_state.copy()
        
        for action in actions:
            # 简单地将动作信息合并到当前状态
            # 实际实现应根据游戏规则进行正确的状态转换
            if isinstance(current_state, dict) and isinstance(action, dict):
                new_state = current_state.copy()
                new_state['last_action'] = action
                states.append(new_state)
                current_state = new_state
            else:
                # 如果状态或动作不是字典格式，简单地添加原始状态的副本
                states.append(current_state.copy() if isinstance(current_state, dict) else current_state)
        
        return states
    
    def _clean_and_validate_experiences(self, experiences: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        清洗和验证经验数据
        
        Args:
            experiences: 原始经验数据列表
            
        Returns:
            List[Dict[str, Any]]: 清洗和验证后的经验数据列表
        """
        valid_experiences = []
        
        for exp in experiences:
            try:
                # 验证必要字段
                if not self._is_valid_experience_record(exp):
                    continue
                
                # 数据类型转换和标准化
                cleaned_exp = self._normalize_experience(exp)
                
                # 添加到有效经验列表
                valid_experiences.append(cleaned_exp)
            except Exception as e:
                logger.debug(f"清洗经验数据时出错：{e}")
        
        return valid_experiences
    
    def _normalize_experience(self, experience: Dict[str, Any]) -> Dict[str, Any]:
        """
        标准化经验数据格式
        
        Args:
            experience: 原始经验数据
            
        Returns:
            Dict[str, Any]: 标准化后的经验数据
        """
        normalized = experience.copy()
        
        # 确保状态和下一个状态是numpy数组格式
        if 'state' in normalized and not isinstance(normalized['state'], np.ndarray):
            if isinstance(normalized['state'], list):
                normalized['state'] = np.array(normalized['state'], dtype=np.float32)
            elif isinstance(normalized['state'], dict):
                # 保持字典格式，不转换为数组
                pass
            elif isinstance(normalized['state'], str):
                # 如果是字符串格式，可能需要进一步解析
                try:
                    normalized['state'] = json.loads(normalized['state'])
                except json.JSONDecodeError:
                    # 如果无法解析为JSON，保留原始字符串
                    pass
        
        # 对下一个状态执行相同的转换
        if 'next_state' in normalized and not isinstance(normalized['next_state'], np.ndarray):
            if isinstance(normalized['next_state'], list):
                normalized['next_state'] = np.array(normalized['next_state'], dtype=np.float32)
            elif isinstance(normalized['next_state'], dict):
                # 保持字典格式，不转换为数组
                pass
            elif isinstance(normalized['next_state'], str):
                # 如果是字符串格式，可能需要进一步解析
                try:
                    normalized['next_state'] = json.loads(normalized['next_state'])
                except json.JSONDecodeError:
                    # 如果无法解析为JSON，保留原始字符串
                    pass
        
        # 确保奖励是浮点数
        if 'reward' in normalized:
            if not isinstance(normalized['reward'], (int, float)):
                try:
                    normalized['reward'] = float(normalized['reward'])
                except (ValueError, TypeError):
                    normalized['reward'] = 0.0
        
        # 确保done字段是布尔值
        if 'done' in normalized:
            if not isinstance(normalized['done'], bool):
                normalized['done'] = bool(normalized['done'])
        else:
            # 如果没有done字段，默认为False
            normalized['done'] = False
        
        # 添加时间戳（如果不存在）
        if 'timestamp' not in normalized:
            normalized['timestamp'] = datetime.datetime.now().isoformat()
        
        return normalized
    
    def _evaluate_quality(self, experiences: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        评估经验数据质量
        
        Args:
            experiences: 经验数据列表
            
        Returns:
            List[Dict[str, Any]]: 质量合格的经验数据列表
        """
        quality_experiences = []
        
        for exp in experiences:
            quality_score = self._calculate_quality_score(exp)
            
            # 添加质量分数到经验数据
            exp['quality_score'] = quality_score
            
            # 如果质量分数高于阈值，添加到合格列表
            if quality_score >= self.min_quality_score:
                quality_experiences.append(exp)
        
        return quality_experiences
    
    def _calculate_quality_score(self, experience: Dict[str, Any]) -> float:
        """
        计算经验数据的质量分数
        
        Args:
            experience: 经验数据
            
        Returns:
            float: 质量分数（0.0-1.0）
        """
        # 这里实现基于业务规则的质量评分逻辑
        # 示例规则：
        # 1. 如果有奖励字段，且奖励非零，可能是一个关键决策点，分数较高
        # 2. 如果有玩家评分字段，可以使用玩家评分作为参考
        # 3. 如果经验数据完整（包含所有必要字段），分数较高
        
        score = 0.5  # 默认中等质量
        
        # 检查奖励
        if 'reward' in experience:
            reward = experience['reward']
            if isinstance(reward, (int, float)) and reward != 0:
                # 非零奖励，可能是关键决策点
                score += 0.2
        
        # 检查玩家评分
        if 'player_rating' in experience:
            try:
                rating = float(experience['player_rating'])
                # 将评分归一化到0-0.3范围
                normalized_rating = min(max(rating / 5.0, 0), 1) * 0.3
                score += normalized_rating
            except (ValueError, TypeError):
                pass
        
        # 检查数据完整性
        required_fields = ['state', 'action', 'reward']
        optional_fields = ['next_state', 'done', 'info']
        
        completeness = sum(1 for field in required_fields if field in experience) / len(required_fields)
        completeness_score = completeness * 0.3
        
        # 可选字段加分
        optional_score = sum(0.05 for field in optional_fields if field in experience)
        
        score += completeness_score + min(optional_score, 0.2)
        
        # 限制分数在0.0-1.0范围内
        return min(max(score, 0.0), 1.0)
    
    def save_buffer(self, filename: Optional[str] = None) -> str:
        """
        保存经验回放缓冲区到文件
        
        Args:
            filename: 文件名，如果为None则自动生成
            
        Returns:
            str: 保存的文件路径
        """
        if filename is None:
            # 自动生成文件名
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{self.game_type}_experiences_{timestamp}.json"
        
        output_path = os.path.join(self.output_dir, filename)
        
        # 将回放缓冲区序列化为JSON格式
        experiences_data = {
            'metadata': {
                'game_type': self.game_type,
                'created_at': datetime.datetime.now().isoformat(),
                'stats': self.stats,
                'buffer_size': len(self.replay_buffer),
                'buffer_capacity': self.buffer_capacity
            },
            'experiences': [exp for exp in self.replay_buffer]
        }
        
        # 保存为JSON文件
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(experiences_data, f, indent=2)
        
        logger.info(f"经验回放缓冲区已保存到文件：{output_path}")
        logger.info(f"统计信息：{self.stats}")
        
        return output_path
    
    def print_stats(self) -> None:
        """
        打印处理统计信息
        """
        logger.info("处理统计信息：")
        logger.info(f"总文件数：{self.stats['total_files']}")
        logger.info(f"处理文件数：{self.stats['processed_files']}")
        logger.info(f"跳过文件数：{self.stats['skipped_files']}")
        logger.info(f"总经验数：{self.stats['total_experiences']}")
        logger.info(f"有效经验数：{self.stats['valid_experiences']}")
        logger.info(f"无效经验数：{self.stats['invalid_experiences']}")
        logger.info(f"低质量经验数：{self.stats['low_quality_experiences']}")
        logger.info(f"经验缓冲区大小：{len(self.replay_buffer)}")
        logger.info(f"经验缓冲区容量：{self.buffer_capacity}")


def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='处理人机交互经验数据')
    parser.add_argument('--input', '-i', type=str, required=True, help='输入数据目录')
    parser.add_argument('--output', '-o', type=str, required=True, help='输出数据目录')
    parser.add_argument('--game', '-g', type=str, default='doudizhu', help='游戏类型')
    parser.add_argument('--pattern', '-p', type=str, default='*.json', help='文件匹配模式')
    parser.add_argument('--capacity', '-c', type=int, default=100000, help='缓冲区容量')
    parser.add_argument('--quality', '-q', type=float, default=0.3, help='最低质量分数')
    
    args = parser.parse_args()
    
    # 创建并运行处理器
    processor = HumanDataProcessor(
        input_dir=args.input,
        output_dir=args.output,
        game_type=args.game,
        buffer_capacity=args.capacity,
        min_quality_score=args.quality
    )
    
    processor.process_files(pattern=args.pattern)
    processor.save_buffer()
    processor.print_stats()


if __name__ == '__main__':
    main() 