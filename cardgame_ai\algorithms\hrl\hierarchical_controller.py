"""
层次控制器模块

实现层次化强化学习中的层次控制器，用于动态选择执行高层策略还是低层策略。
支持对手建模集成，增强针对性决策能力。
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import logging
import numpy as np
from typing import Dict, List, Tuple, Any, Optional, Union

from cardgame_ai.algorithms.hrl.high_level_policy import HighLevelPolicy
from cardgame_ai.algorithms.hrl.low_level_policy import LowLevelPolicy
from cardgame_ai.core.base import State, Action

# 配置日志
logger = logging.getLogger(__name__)


class HierarchicalController:
    """
    层次控制器

    根据当前局面或高层策略的输出，动态地选择执行高层策略（设定目标）
    还是直接调用低层策略进行微调。实现粗粒度到细粒度的智能调度。
    
    支持集成对手建模，针对不同对手特征自适应调整决策流程和策略选择。
    """

    def __init__(
        self,
        high_level_policy: HighLevelPolicy,
        low_level_policy: LowLevelPolicy,
        complexity_threshold: float = 0.6,
        confidence_threshold: float = 0.8,
        dynamic_scheduling: bool = True,
        use_history: bool = True,
        history_window: int = 5,
        use_opponent_modeling: bool = False,
        config_path: Optional[str] = None
    ):
        """
        初始化层次控制器

        Args:
            high_level_policy: 高层策略
            low_level_policy: 低层策略
            complexity_threshold: 复杂度阈值，高于此值使用高层策略
            confidence_threshold: 置信度阈值，高于此值直接使用低层策略
            dynamic_scheduling: 是否使用动态调度
            use_history: 是否使用历史信息
            history_window: 历史窗口大小
            use_opponent_modeling: 是否使用对手建模功能
            config_path: 配置文件路径，如果提供则从配置文件加载参数
        """
        self.high_level_policy = high_level_policy
        self.low_level_policy = low_level_policy
        
        # 设置默认参数
        self.complexity_threshold = complexity_threshold
        self.confidence_threshold = confidence_threshold
        self.dynamic_scheduling = dynamic_scheduling
        self.use_history = use_history
        self.history_window = history_window
        self.use_opponent_modeling = use_opponent_modeling
        
        # 如果提供了配置文件路径，从配置文件加载参数
        self.config = None
        if config_path:
            self._load_config(config_path)
            self._update_from_config()

        # 初始化历史记录
        self.action_history = []
        self.goal_history = []
        
        # 【新增】历史状态跟踪 - 用于状态相似度检测
        self.state_history = []
        self.max_state_history = 20  # 最多保存20个历史状态
        
        # 【新增】性能历史记录 - 用于评估决策质量
        self.performance_history = []
        self.max_performance_history = 50  # 最多保存50个性能记录
        
        # 【新增】自适应探索系统
        self.adaptation_phase = 0  # 初始适应阶段
        self.adaptation_cycle_length = 50  # 每个适应周期的决策次数
        self.enable_adaptation = True  # 是否启用自适应探索
        
        # 缓存最近的状态对象，用于评估
        self._last_state = None
        
        # 当前对手表示
        self.current_opponent_representation = None

        # 初始化统计信息
        self.stats = {
            "high_level_calls": 0,
            "low_level_calls": 0,
            "direct_calls": 0,
            "total_calls": 0,
            "avg_complexity": 0.0,
            "avg_confidence": 0.0,
            "last_mode": "unknown",  # 记录最近一次的决策模式
            "mode_performance": {    # 【新增】记录不同决策模式的性能
                "high_level": [],
                "low_level": [],
                "direct": []
            }
        }
        
        # 对手类型的决策调整系数
        self.opponent_type_adjustments = {
            "aggressive": {
                "complexity_adjust": -0.1,  # 降低复杂度阈值，更倾向于使用高层策略应对激进对手
                "confidence_adjust": 0.1    # 提高置信度阈值，降低直接决策的频率
            },
            "conservative": {
                "complexity_adjust": 0.1,   # 提高复杂度阈值，更倾向于使用低层策略应对保守对手
                "confidence_adjust": -0.1   # 降低置信度阈值，增加直接决策的频率
            },
            "balanced": {
                "complexity_adjust": 0.0,   # 保持默认阈值
                "confidence_adjust": 0.0
            },
            "unpredictable": {
                "complexity_adjust": -0.15, # 显著降低复杂度阈值，更积极地使用高层策略应对不可预测对手
                "confidence_adjust": 0.15   # 显著提高置信度阈值，减少直接决策的频率
            },
            "adaptive": {
                "complexity_adjust": -0.05, # 略微调整阈值
                "confidence_adjust": 0.05
            },
            "unknown": {
                "complexity_adjust": 0.0,   # 保持默认阈值
                "confidence_adjust": 0.0
            }
        }
        
        # 如果配置文件中有对手类型调整参数，使用配置文件中的值
        if self.config and 'controller' in self.config and 'opponent_type_adjustments' in self.config['controller']:
            self._update_opponent_adjustments()
        
        # 日志配置
        self.verbose_logging = False
        self.log_scheduling_decisions = True
        self.stats_interval = 50
        
        # 如果配置文件中有日志配置，使用配置文件中的值
        if self.config and 'logging' in self.config:
            self._update_logging_config()

    def _load_config(self, config_path: str) -> None:
        """
        从YAML配置文件加载配置参数
        
        Args:
            config_path: 配置文件路径
        """
        try:
            import yaml
            with open(config_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
            logger.info(f"从配置文件加载HRL控制器参数: {config_path}")
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            self.config = None
    
    def _update_from_config(self) -> None:
        """
        从加载的配置中更新控制器参数
        """
        if not self.config:
            return
            
        if 'controller' in self.config:
            controller_config = self.config['controller']
            
            # 更新基本参数
            if 'dynamic_scheduling' in controller_config:
                self.dynamic_scheduling = controller_config['dynamic_scheduling']
                
            if 'complexity_threshold' in controller_config:
                self.complexity_threshold = controller_config['complexity_threshold']
                
            if 'confidence_threshold' in controller_config:
                self.confidence_threshold = controller_config['confidence_threshold']
                
            if 'use_history' in controller_config:
                self.use_history = controller_config['use_history']
                
            if 'history_window' in controller_config:
                self.history_window = controller_config['history_window']
                
            if 'use_opponent_modeling' in controller_config:
                self.use_opponent_modeling = controller_config['use_opponent_modeling']
    
    def _update_opponent_adjustments(self) -> None:
        """
        从配置中更新对手类型调整参数
        """
        if not self.config or 'controller' not in self.config or 'opponent_type_adjustments' not in self.config['controller']:
            return
            
        config_adjustments = self.config['controller']['opponent_type_adjustments']
        
        for opponent_type, adjustments in config_adjustments.items():
            if opponent_type in self.opponent_type_adjustments:
                if 'complexity_adjust' in adjustments:
                    self.opponent_type_adjustments[opponent_type]['complexity_adjust'] = adjustments['complexity_adjust']
                    
                if 'confidence_adjust' in adjustments:
                    self.opponent_type_adjustments[opponent_type]['confidence_adjust'] = adjustments['confidence_adjust']
    
    def _update_logging_config(self) -> None:
        """
        从配置中更新日志配置
        """
        if not self.config or 'logging' not in self.config:
            return
            
        logging_config = self.config['logging']
        
        if 'verbose' in logging_config:
            self.verbose_logging = logging_config['verbose']
            
        if 'log_scheduling_decisions' in logging_config:
            self.log_scheduling_decisions = logging_config['log_scheduling_decisions']
            
        if 'stats_interval' in logging_config:
            self.stats_interval = logging_config['stats_interval']
            
    def set_opponent_representation(self, opponent_representation: Dict[str, Any]) -> None:
        """
        设置当前对手表示，用于后续决策

        Args:
            opponent_representation: 对手表示，包含 'representation'、'type' 等信息
        """
        if not self.use_opponent_modeling:
            return
            
        self.current_opponent_representation = opponent_representation
        
        # 记录对手类型更新到统计信息
        if 'type' in opponent_representation:
            self.stats["opponent_type"] = opponent_representation["type"]

    def update_performance(self, performance_value: float, decision_mode: Optional[str] = None) -> None:
        """
        更新策略性能历史，用于动态调整决策阈值
        
        Args:
            performance_value: 性能评估值，通常为奖励值或胜负指标（正值为好结果，负值为差结果）
            decision_mode: 决策模式（"high_level"、"low_level"、"direct" 或 None）
        """
        # 添加到全局性能历史
        self.performance_history.append(performance_value)
        
        # 限制历史记录大小
        if len(self.performance_history) > self.max_performance_history:
            self.performance_history.pop(0)
        
        # 如果提供了决策模式，更新对应模式的性能记录
        if decision_mode is not None and decision_mode in self.stats["mode_performance"]:
            self.stats["mode_performance"][decision_mode].append(performance_value)
            
            # 限制每种模式的历史记录大小
            if len(self.stats["mode_performance"][decision_mode]) > 20:  # 保留最近20条记录
                self.stats["mode_performance"][decision_mode].pop(0)
        
        # 记录日志
        if self.verbose_logging:
            logger.debug(f"更新性能记录: {performance_value:.4f}, 模式: {decision_mode or '未指定'}")

    def decide(
        self,
        state: State,
        legal_actions: List[Action]
    ) -> Tuple[Action, Dict[str, Any]]:
        """
        做出决策

        根据当前局面动态选择使用高层策略还是低层策略。
        如果启用了对手建模，将根据对手特征调整决策流程。

        Args:
            state: 当前状态
            legal_actions: 合法动作列表

        Returns:
            选择的动作和决策信息
        """
        # 更新统计信息
        self.stats["total_calls"] += 1

        # 【新增】更新状态历史记录
        if hasattr(state, 'get_fingerprint'):
            # 如果状态对象支持获取指纹，使用指纹识别唯一状态
            if self.use_history and len(self.state_history) < self.max_state_history:
                self.state_history.append(state)
            elif self.use_history and len(self.state_history) >= self.max_state_history:
                self.state_history.pop(0)
                self.state_history.append(state)
        
        # 【新增】更新自适应探索阶段
        if self.enable_adaptation and self.stats["total_calls"] % self.adaptation_cycle_length == 0:
            self.adaptation_phase = (self.adaptation_phase + 1) % 4
            if self.verbose_logging:
                logger.info(f"自适应探索阶段更新为: {self.adaptation_phase}")

        # 缓存当前状态对象，用于评估
        self._last_state = state

        # 转换状态为张量
        state_tensor = self._prepare_state_tensor(state)

        # 获取调整后的阈值（基于对手类型、游戏阶段和历史表现）
        complexity_threshold, confidence_threshold = self._get_adjusted_thresholds()

        # 评估局面复杂度
        complexity = self._evaluate_complexity(state)
        self.stats["avg_complexity"] = (
            (self.stats["avg_complexity"] * (self.stats["total_calls"] - 1) + complexity) /
            self.stats["total_calls"]
        )

        # 评估低层策略置信度
        confidence = self._evaluate_confidence(state_tensor, legal_actions)
        self.stats["avg_confidence"] = (
            (self.stats["avg_confidence"] * (self.stats["total_calls"] - 1) + confidence) /
            self.stats["total_calls"]
        )

        # 决策信息
        decision_info = {
            "complexity": complexity,
            "confidence": confidence,
            "complexity_threshold": complexity_threshold,
            "confidence_threshold": confidence_threshold,
            "mode": None
        }
        
        # 如果使用对手建模，添加对手信息
        if self.use_opponent_modeling and self.current_opponent_representation:
            decision_info["opponent_type"] = self.current_opponent_representation.get("type", "unknown")
        
        # 【新增】添加自适应探索信息
        if self.enable_adaptation:
            decision_info["adaptation_phase"] = self.adaptation_phase

        # 根据复杂度和置信度选择决策模式
        if self.dynamic_scheduling:
            if complexity > complexity_threshold:
                # 复杂局面，使用高层策略设定目标
                action, goal = self._use_high_level_policy(state_tensor, legal_actions)
                decision_info["mode"] = "high_level"
                self.stats["high_level_calls"] += 1
                self.stats["last_mode"] = "high_level"
                
                # 详细日志记录
                if self.verbose_logging:
                    logger.debug(f"使用高层策略 - 复杂度: {complexity:.4f} > {complexity_threshold:.4f}")
            elif confidence > confidence_threshold:
                # 低层策略置信度高，直接使用低层策略
                action = self._use_direct_policy(state_tensor, legal_actions)
                goal = None
                decision_info["mode"] = "direct"
                self.stats["direct_calls"] += 1
                self.stats["last_mode"] = "direct"
                
                # 详细日志记录
                if self.verbose_logging:
                    logger.debug(f"直接使用低层策略 - 置信度: {confidence:.4f} > {confidence_threshold:.4f}")
            else:
                # 其他情况，使用低层策略微调
                action = self._use_low_level_policy(state_tensor, legal_actions)
                goal = None
                decision_info["mode"] = "low_level"
                self.stats["low_level_calls"] += 1
                self.stats["last_mode"] = "low_level"
                
                # 详细日志记录
                if self.verbose_logging:
                    logger.debug(f"使用低层策略微调 - 复杂度: {complexity:.4f} <= {complexity_threshold:.4f}, "
                                f"置信度: {confidence:.4f} <= {confidence_threshold:.4f}")
        else:
            # 不使用动态调度，始终使用高层策略
            action, goal = self._use_high_level_policy(state_tensor, legal_actions)
            decision_info["mode"] = "high_level"
            self.stats["high_level_calls"] += 1
            self.stats["last_mode"] = "high_level"
            
            # 详细日志记录
            if self.verbose_logging:
                logger.debug("不使用动态调度，始终使用高层策略")

        # 周期性记录统计信息
        if self.log_scheduling_decisions and self.stats["total_calls"] % self.stats_interval == 0:
            self._log_stats()

        # 更新历史记录
        if self.use_history:
            self.action_history.append(action)
            if goal is not None:
                self.goal_history.append(goal)

            # 保持历史记录在窗口大小以内
            if len(self.action_history) > self.history_window:
                self.action_history.pop(0)
            if len(self.goal_history) > self.history_window:
                self.goal_history.pop(0)

        return action, decision_info
        
    def _get_adjusted_thresholds(self) -> Tuple[float, float]:
        """
        根据多种因素动态获取调整后的决策阈值:
        - 对手类型和特征
        - 游戏阶段
        - 历史表现数据
        - 当前局势状态
        
        Returns:
            调整后的复杂度阈值和置信度阈值
        """
        # 默认使用原始阈值
        complexity_threshold = self.complexity_threshold
        confidence_threshold = self.confidence_threshold
        
        # 1. 对手建模调整
        if self.use_opponent_modeling and self.current_opponent_representation:
            opponent_type = self.current_opponent_representation.get("type", "unknown")
            
            # 获取对应对手类型的基础调整系数
            base_adjustments = self.opponent_type_adjustments.get(opponent_type, 
                                                          self.opponent_type_adjustments["unknown"])
            
            # 应用基础调整
            complexity_adjust = base_adjustments["complexity_adjust"]
            confidence_adjust = base_adjustments["confidence_adjust"]
            
            # 1.1 对手具体特征的细粒度调整
            if "style" in self.current_opponent_representation:
                style = self.current_opponent_representation["style"]
                
                # 根据对手风格做细粒度调整
                if style == "risk_averse":  # 风险厌恶型对手
                    complexity_adjust += 0.05  # 更倾向于使用低层策略，对手行为可预测
                    confidence_adjust -= 0.05  # 降低直接决策的频率
                elif style == "risk_seeking":  # 风险寻求型对手
                    complexity_adjust -= 0.05  # 更倾向于使用高层策略应对
                    confidence_adjust += 0.05  # 提高对直接决策的门槛
            
            # 1.2 对手策略的预测置信度调整
            if "confidence" in self.current_opponent_representation:
                prediction_confidence = self.current_opponent_representation["confidence"]
                
                # 预测置信度越高，调整越显著
                confidence_factor = max(0.5, min(1.5, prediction_confidence * 2))
                complexity_adjust *= confidence_factor
                confidence_adjust *= confidence_factor
                
            # 应用所有对手相关调整
            complexity_threshold += complexity_adjust
            confidence_threshold += confidence_adjust
        
        # 2. 游戏阶段调整
        if hasattr(self, '_last_state') and self._last_state is not None:
            state = self._last_state
            
            # 2.1 基于手牌数量判断游戏阶段
            if hasattr(state, 'player_hands') and hasattr(state, 'current_player'):
                total_cards = sum(len(hand) for hand in state.player_hands.values())
                player_count = len(state.player_hands)
                avg_cards = total_cards / max(1, player_count)
                
                # 游戏阶段调整系数
                if avg_cards < 5:  # 游戏后期
                    # 后期提高复杂度阈值（更倾向于低层策略）并降低置信度阈值（更多直接决策）
                    complexity_threshold += 0.05
                    confidence_threshold -= 0.1
                elif avg_cards > 12:  # 游戏前期
                    # 前期降低复杂度阈值（更倾向于高层策略）并提高置信度阈值（减少直接决策）
                    complexity_threshold -= 0.05
                    confidence_threshold += 0.05
        
        # 3. 历史表现调整
        if hasattr(self, 'stats') and self.stats.get("total_calls", 0) > 10:
            # 3.1 根据不同决策模式的历史表现调整阈值
            high_level_ratio = self.stats.get("high_level_calls", 0) / max(1, self.stats.get("total_calls", 1))
            low_level_ratio = self.stats.get("low_level_calls", 0) / max(1, self.stats.get("total_calls", 1))
            direct_ratio = self.stats.get("direct_calls", 0) / max(1, self.stats.get("total_calls", 1))
            
            # 如果高层策略使用比例过高，调整阈值以增加低层策略和直接决策的使用
            if high_level_ratio > 0.6:
                complexity_threshold += 0.05
            
            # 如果直接决策使用比例过高，调整阈值以减少直接决策的使用
            if direct_ratio > 0.6:
                confidence_threshold += 0.05
            
            # 如果低层策略使用比例过高，调整阈值以增加高层策略和直接决策的使用
            if low_level_ratio > 0.6:
                complexity_threshold -= 0.05
                confidence_threshold -= 0.05
            
            # 3.2 如果存在性能评估数据
            if hasattr(self, 'performance_history') and len(self.performance_history) >= 5:
                # 计算近期表现
                recent_performance = sum(self.performance_history[-5:]) / 5
                
                # 根据性能调整阈值
                if recent_performance > 0.6:  # 表现良好
                    # 维持当前策略
                    pass
                elif recent_performance < 0.3:  # 表现不佳
                    # 较大调整，尝试不同的决策模式
                    if self.stats.get("last_mode") == "high_level":
                        # 如果高层策略表现不佳，增加使用低层策略的可能性
                        complexity_threshold += 0.1
                    elif self.stats.get("last_mode") == "direct":
                        # 如果直接策略表现不佳，减少直接决策的可能性
                        confidence_threshold += 0.1
                    elif self.stats.get("last_mode") == "low_level":
                        # 如果低层策略表现不佳，增加使用高层策略的可能性
                        complexity_threshold -= 0.1
        
        # 4. 系统参数调整
        # 4.1 自适应调节 - 在不同阶段微调阈值以探索更优的决策边界
        if hasattr(self, 'adaptation_phase'):
            phase = getattr(self, 'adaptation_phase', 0) % 4
            
            # 根据不同的探索阶段调整阈值
            if phase == 0:  # 基线阶段
                pass  # 使用标准阈值
            elif phase == 1:  # 探索高层策略
                complexity_threshold -= 0.05
            elif phase == 2:  # 探索直接决策
                confidence_threshold -= 0.05
            elif phase == 3:  # 探索低层策略
                complexity_threshold += 0.05
                confidence_threshold += 0.05
        
        # 确保阈值在合理范围内
        complexity_threshold = max(0.1, min(0.9, complexity_threshold))
        confidence_threshold = max(0.1, min(0.9, confidence_threshold))
        
        return complexity_threshold, confidence_threshold

    def _prepare_state_tensor(self, state: State) -> torch.Tensor:
        """
        准备状态张量

        Args:
            state: 状态

        Returns:
            状态张量
        """
        if isinstance(state, torch.Tensor):
            return state
        elif isinstance(state, np.ndarray):
            return torch.FloatTensor(state)
        else:
            # 尝试转换为张量
            try:
                return torch.FloatTensor(state)
            except:
                raise ValueError(f"无法将状态转换为张量: {type(state)}")

    def _evaluate_complexity(self, state: State) -> float:
        """
        评估局面复杂度，用于决定是否使用高层策略。
        
        引入更多状态特征来更准确地评估局面复杂度，包括：
        - 手牌数量和分布情况
        - 牌型种类和多样性
        - 当前局面的关键程度
        - 对手行为和历史模式
        - 动作序列模式
        - 游戏阶段感知

        Args:
            state: 当前游戏状态

        Returns:
            复杂度评分 (0-1)，值越高表示局面越复杂
        """
        # 初始默认中等复杂度
        complexity = 0.5
        
        # 优化：只有在斗地主游戏状态下才进行特定评估
        if not (hasattr(state, 'current_player') and hasattr(state, 'player_hands')):
            return complexity
            
        # 获取当前玩家的手牌
        hand = state.player_hands[state.current_player]
        hand_size = len(hand)
        
        # 1. 手牌数量评估 - 非线性映射，避免简单的线性关系
        # 中等数量的牌(8-12张)复杂度适中，过多或过少都增加复杂度
        if hand_size > 15:
            complexity += 0.25  # 手牌非常多，需要规划
        elif hand_size > 10:
            complexity += 0.15  # 手牌较多
        elif hand_size < 5:
            complexity += 0.10  # 接近游戏结束，关键决策
        elif hand_size < 3:
            complexity += 0.20  # 非常关键的决策
            
        # 2. 手牌分布熵 - 计算手牌多样性
        if hand:
            # 计算牌值分布
            card_values = {}
            for card in hand:
                value = card.get_value() if hasattr(card, 'get_value') else getattr(card, 'value', 0)
                card_values[value] = card_values.get(value, 0) + 1
                
            # 计算牌型多样性/熵
            diversity = 0
            for count in card_values.values():
                # 单张、对子、三张及以上的牌，复杂度各不相同
                if count == 1:  # 单张
                    diversity += 0.3
                elif count == 2:  # 对子
                    diversity += 0.2
                elif count == 3:  # 三张
                    diversity += 0.1
                else:  # 四张或更多
                    diversity += 0.05
                    
            # 归一化并应用到复杂度
            normalized_diversity = min(1.0, diversity / max(5.0, hand_size / 2))
            complexity += normalized_diversity * 0.2
            
            # 2.1 【新增】手牌连贯性评估 - 分析手牌中的连续牌值
            consecutive_counts = 0
            sorted_values = sorted(card_values.keys())
            for i in range(len(sorted_values) - 1):
                if sorted_values[i+1] - sorted_values[i] == 1:
                    consecutive_counts += 1
            
            # 连贯性越高，潜在的直牌和连对可能性越大，复杂度降低
            consecutive_ratio = consecutive_counts / max(1, len(sorted_values) - 1)
            if consecutive_ratio > 0.5 and hand_size > 5:
                complexity -= 0.15  # 高连贯性减少复杂度
            
            # 2.2 【新增】关键牌型识别 - 识别关键牌组合
            # 检查是否有炸弹/火箭
            has_bomb = False
            has_rocket = False
            
            for count in card_values.values():
                if count >= 4:
                    has_bomb = True
                    break
            
            # 检查大小王
            if hasattr(state, 'contains_cards'):
                has_rocket = state.contains_cards(hand, ['BJ', 'CJ'])
            elif len(hand) >= 2:
                # 尝试基于牌值判断
                high_cards = [card for card in hand if (hasattr(card, 'is_joker') and card.is_joker()) or 
                              (hasattr(card, 'rank') and card.rank in ['BJ', 'CJ'])]
                has_rocket = len(high_cards) >= 2
            
            # 有炸弹或火箭降低复杂度（减少决策难度）
            if has_bomb:
                complexity -= 0.1
            if has_rocket:
                complexity -= 0.15
            
        # 3. 局面关键程度
        is_landlord = hasattr(state, 'landlord_player') and state.current_player == state.landlord_player
        
        # 地主与农民角色考虑
        if is_landlord:
            # 地主通常需要更多策略规划
            complexity += 0.12
            
            # 如果是地主且手牌很少，考虑是否有可能一手出完
            if hand_size <= 5:
                complexity -= 0.1  # 减少复杂度，更倾向于直接决策
        else:
            # 对于农民，检查是否处于配合阶段
            other_farmer_id = -1
            for player_id in range(len(state.player_hands)):
                if player_id != state.current_player and (not hasattr(state, 'landlord_player') or player_id != state.landlord_player):
                    other_farmer_id = player_id
                    break
                    
            # 如果队友(另一个农民)手牌很少，增加复杂度
            if other_farmer_id >= 0 and hasattr(state, 'player_hand_sizes'):
                other_hand_size = state.player_hand_sizes[other_farmer_id]
                if other_hand_size <= 3:
                    complexity += 0.15  # 队友即将出完，需要策略配合
        
        # 4. 出牌历史和约束评估
        if hasattr(state, 'last_move') and state.last_move:
            # 如果上家出牌了，增加复杂度
            complexity += 0.1
            
            # 检查是否被动跟牌
            if hasattr(state, 'last_player') and state.last_player != state.current_player:
                complexity += 0.15  # 被迫跟牌，需要更多策略考虑
                
                # 【新增】检查上家是否是队友
                is_teammate = False
                if hasattr(state, 'landlord_player'):
                    is_landlord = state.current_player == state.landlord_player
                    is_prev_landlord = state.last_player == state.landlord_player
                    is_teammate = (is_landlord == is_prev_landlord)
                
                # 如果上家是队友，降低复杂度
                if is_teammate:
                    complexity -= 0.1
            
            # 复杂牌型评估
            if hasattr(state.last_move, 'card_type'):
                from cardgame_ai.games.doudizhu.card_group import CardGroupType
                
                # 牌型复杂度映射
                type_complexity_map = {
                    CardGroupType.SINGLE: 0.0,
                    CardGroupType.PAIR: 0.05,
                    CardGroupType.TRIO: 0.1,
                    CardGroupType.TRIO_WITH_SINGLE: 0.15,
                    CardGroupType.TRIO_WITH_PAIR: 0.2,
                    CardGroupType.STRAIGHT: 0.25,
                    CardGroupType.STRAIGHT_PAIR: 0.3,
                    CardGroupType.AIRPLANE: 0.25,
                    CardGroupType.AIRPLANE_WITH_SINGLE: 0.35,
                    CardGroupType.AIRPLANE_WITH_PAIR: 0.4,
                    CardGroupType.FOUR_WITH_TWO_SINGLE: 0.35,
                    CardGroupType.FOUR_WITH_TWO_PAIR: 0.4,
                    CardGroupType.BOMB: 0.2,
                    CardGroupType.ROCKET: 0.1
                }
                
                card_type = state.last_move.card_type
                complexity += type_complexity_map.get(card_type, 0.2)
        
        # 5. 考虑可能的出牌选择数量
        if hasattr(state, 'get_legal_actions'):
            legal_actions = state.get_legal_actions()
            action_count = len(legal_actions)
            
            # 选择过多会增加复杂度，选择过少会降低复杂度
            if action_count > 10:
                complexity += 0.2
            elif action_count > 5:
                complexity += 0.1
            elif action_count < 3:
                complexity -= 0.1
                
        # 6. 对手建模集成
        if self.use_opponent_modeling and self.current_opponent_representation:
            opponent_type = self.current_opponent_representation.get("type", "unknown")
            
            # 对于不同类型的对手，调整复杂度
            opponent_complexity_map = {
                "unpredictable": 0.2,  # 不可预测的对手增加复杂度
                "aggressive": 0.15,    # 激进的对手增加复杂度
                "adaptive": 0.1,       # 适应性对手增加复杂度
                "balanced": 0.0,       # 平衡型对手保持不变
                "conservative": -0.05, # 保守的对手降低复杂度
                "unknown": 0.05        # 未知类型增加少量复杂度
            }
            
            complexity += opponent_complexity_map.get(opponent_type, 0.0)
            
            # 如果对手模型提供了信心评分，也考虑这一因素
            if "confidence" in self.current_opponent_representation:
                opponent_confidence = self.current_opponent_representation["confidence"]
                if opponent_confidence < 0.3:
                    complexity += 0.1  # 对手模型信心低，增加复杂度
        
        # 7. 【新增】动作序列模式识别
        if self.use_history and len(self.action_history) >= 3:
            # 分析最近的动作模式
            recent_actions = self.action_history[-3:]
            
            # 检查是否形成轮回模式（如A出单，B跟单，C跟单）
            if all(hasattr(action, 'card_type') for action in recent_actions):
                action_types = [action.card_type for action in recent_actions]
                if len(set(action_types)) == 1:  # 相同动作类型
                    complexity -= 0.1  # 稳定模式降低复杂度
                    
                # 检测复杂交互模式（多种类型混合）
                elif len(set(action_types)) == len(action_types):
                    complexity += 0.1  # 无规律增加复杂度
        
        # 8. 【新增】游戏阶段感知
        if hasattr(state, 'round') and hasattr(state, 'max_rounds'):
            # 计算游戏进度
            progress = state.round / state.max_rounds
            
            # 游戏初期和结束阶段复杂度不同
            if progress < 0.2:  # 游戏初期
                complexity += 0.1  # 增加复杂度，需要战略规划
            elif progress > 0.8:  # 游戏末期
                complexity += 0.15  # 增加复杂度，关键决策阶段
            
        # 分析所有玩家的手牌数量分布
        if hasattr(state, 'player_hand_sizes'):
            # 计算手牌数量差异
            hand_sizes = list(state.player_hand_sizes.values())
            if len(hand_sizes) > 1:
                max_diff = max(hand_sizes) - min(hand_sizes)
                
                # 手牌差异大表示游戏接近终局或不平衡
                if max_diff > 5:
                    complexity += 0.1
                elif max_diff > 10:
                    complexity += 0.2
        
        # 返回规范化的复杂度分数，确保在[0,1]范围内
        return min(1.0, max(0.0, complexity))

    def _evaluate_confidence(
        self,
        state_tensor: torch.Tensor,
        legal_actions: List[Action]
    ) -> float:
        """
        评估低层策略置信度，用于决定是否直接使用低层策略。
        
        引入更多特征和启发式规则来提高决策可靠性：
        - 动作空间大小和分布
        - 动作值分布的熵和峰度
        - 历史决策的一致性
        - 状态特征的清晰度
        - Q值分布的详细分析
        - 基于历史表现的置信评估
        - 特定游戏阶段的置信度调整

        Args:
            state_tensor: 状态张量
            legal_actions: 合法动作列表

        Returns:
            置信度评分 (0-1)，值越高表示对低层策略的决策越有信心
        """
        # 初始默认置信度
        confidence = 0.5
        
        # 1. 动作空间评估 - 当合法动作少时，通常更有信心
        action_count = len(legal_actions)
        if action_count == 1:
            # 只有一个可能的动作，完全确定
            confidence = 0.95
            return confidence  # 可以直接返回，无需进一步评估
        elif action_count <= 3:
            # 较少的动作选择，增加置信度
            confidence += 0.2
        elif action_count >= 10:
            # 大量可能的动作，降低置信度
            confidence -= 0.2
        
        # 2. 对特定游戏状态的分析
        # 获取状态对象（如果传入的是张量表示，尝试恢复原始状态对象）
        state = None
        if hasattr(self, '_last_state'):
            state = self._last_state  # 如果有缓存状态，使用它
            
        if state is not None:
            # 2.1 终局状态检测 - 接近游戏结束时提高置信度
            if hasattr(state, 'player_hands') and hasattr(state, 'current_player'):
                hand_size = len(state.player_hands[state.current_player])
                if hand_size <= 2:
                    confidence += 0.25  # 手牌很少，策略更确定
                elif hand_size <= 4:
                    confidence += 0.15  # 手牌较少
                    
            # 2.2 关键牌型检测 - 某些关键牌型决策更加明确
            if hasattr(state, 'player_hands') and hasattr(state, 'current_player'):
                hand = state.player_hands[state.current_player]
                
                # 检查是否有炸弹或火箭
                has_bomb = False
                
                # 牌值计数
                card_counts = {}
                for card in hand:
                    value = card.get_value() if hasattr(card, 'get_value') else getattr(card, 'value', 0)
                    card_counts[value] = card_counts.get(value, 0) + 1
                
                # 检查是否有炸弹（四张相同点数的牌）
                for count in card_counts.values():
                    if count >= 4:
                        has_bomb = True
                        break
                
                # 火箭检测（对王）
                has_rocket = False
                if hasattr(state, 'contains_cards'):
                    has_rocket = state.contains_cards(hand, ['BJ', 'CJ'])  # 大小王
                
                # 有炸弹或火箭通常决策更明确
                if has_bomb:
                    confidence += 0.1
                if has_rocket:
                    confidence += 0.15
            
            # 2.3 跟牌场景评估 - 跟牌时通常决策更明确
            if hasattr(state, 'last_move') and state.last_move:
                if hasattr(state, 'last_player') and hasattr(state, 'current_player'):
                    # 如果是对手出牌后我方跟牌
                    if state.last_player != state.current_player:
                        # 跟牌场景下，动作空间通常受限，决策更明确
                        confidence += 0.1
                        
                        # 如果同时动作空间较小，进一步提高置信度
                        if action_count <= 5:
                            confidence += 0.1
        
        # 3. 使用低层策略模型的输出特征评估置信度
        if hasattr(self.low_level_policy, 'estimate_confidence'):
            # 如果低层策略实现了置信度估计方法，则使用它
            model_confidence = self.low_level_policy.estimate_confidence(state_tensor, legal_actions)
            
            # 融合启发式置信度和模型置信度
            # 当动作空间较小时，更依赖启发式规则；当动作空间较大时，更依赖模型
            if action_count <= 3:
                # 动作少，启发式规则更可靠
                confidence = 0.7 * confidence + 0.3 * model_confidence
            else:
                # 动作多，模型评估更可靠
                confidence = 0.3 * confidence + 0.7 * model_confidence
        else:
            # 如果没有模型置信度估计，可以尝试分析动作值分布
            try:
                # 尝试获取动作值预测
                action_values = self._get_action_values(state_tensor, legal_actions)
                
                if action_values is not None and len(action_values) > 1:
                    # 【改进】更详细的Q值分布分析
                    sorted_values = sorted(action_values, reverse=True)
                    
                    # 3.1 计算前两个动作的值差异
                    top_diff = sorted_values[0] - sorted_values[1]
                    
                    # 如果最优动作明显优于次优动作，提高置信度
                    if top_diff > 0.5:
                        confidence += 0.2
                    elif top_diff > 0.2:
                        confidence += 0.1
                    
                    # 3.2 分析Q值分布形状
                    if len(sorted_values) > 2:
                        # 标准差分析
                        std_dev = np.std(sorted_values)
                        if std_dev < 0.1:  # 值非常接近，不确定
                            confidence -= 0.15
                        elif std_dev > 0.3:  # 值分散，某些动作明显更好
                            confidence += 0.15
                        
                        # 【新增】偏度分析 - 检测分布是否偏向某一侧
                        mean_value = np.mean(sorted_values)
                        median_value = np.median(sorted_values)
                        skew = (mean_value - median_value) / (std_dev + 1e-8)  # 防止除零
                        
                        # 正偏度表示有少数高价值动作，提高置信度
                        if skew > 1.0:
                            confidence += 0.1
                        
                        # 【新增】峰度分析 - 检测分布的"尖锐程度"
                        n = len(sorted_values)
                        mean_dev_4 = sum((v - mean_value) ** 4 for v in sorted_values) / n
                        kurtosis = mean_dev_4 / ((std_dev ** 4) + 1e-8) - 3  # 减3使正态分布峰度为0
                        
                        # 高峰度表示分布有明显峰值，提高置信度
                        if kurtosis > 1.5:
                            confidence += 0.1
                        elif kurtosis < -1.0:  # 低峰度，平坦分布
                            confidence -= 0.1
                    
                    # 3.3 【新增】评估动作值的绝对大小
                    top_value = sorted_values[0]
                    if top_value > 0.8:  # 最优动作绝对值很高
                        confidence += 0.15
                    elif top_value < 0.3:  # 最优动作绝对值很低
                        confidence -= 0.1
            except Exception as e:
                # 无法获取动作值，记录错误但不影响程序流程
                if hasattr(self, 'verbose_logging') and self.verbose_logging:
                    logger.debug(f"Q值分析出错: {e}")
                pass
                
        # 4. 历史一致性评估
        if self.use_history and len(self.action_history) >= 2:
            # 检查最近的动作是否具有一致性模式
            # 这里简化为：如果最近两次动作类型相同，增加置信度
            if hasattr(self.action_history[-1], 'card_type') and hasattr(self.action_history[-2], 'card_type'):
                if self.action_history[-1].card_type == self.action_history[-2].card_type:
                    confidence += 0.05
        
        # 5. 对手建模集成
        if self.use_opponent_modeling and self.current_opponent_representation:
            opponent_type = self.current_opponent_representation.get("type", "unknown")
            
            # 对于不同类型的对手，调整置信度
            opponent_confidence_map = {
                "conservative": 0.15,  # 保守对手行为可预测，提高置信度
                "balanced": 0.05,      # 平衡型对手相对可预测
                "adaptive": -0.05,     # 适应性对手降低置信度
                "aggressive": -0.1,    # 激进对手行为难以预测，降低置信度
                "unpredictable": -0.2, # 不可预测对手大幅降低置信度
                "unknown": -0.05       # 未知类型稍微降低置信度
            }
            
            confidence += opponent_confidence_map.get(opponent_type, 0.0)
            
            # 如果对手模型提供了更多特征，进一步细化
            if "predictability" in self.current_opponent_representation:
                predictability = self.current_opponent_representation["predictability"]
                confidence += 0.2 * predictability  # 可预测性直接影响置信度
            
            # 【新增】对手行为模式分析
            if "action_pattern" in self.current_opponent_representation:
                pattern = self.current_opponent_representation["action_pattern"]
                
                # 如果对手行为有明显模式，提高置信度
                if pattern == "repetitive":
                    confidence += 0.1
                elif pattern == "random":
                    confidence -= 0.1
        
        # 6. 【新增】游戏阶段相关置信度调整
        if state is not None:
            # 根据游戏阶段调整置信度
            game_stage = "mid"  # 默认中期
            
            # 根据剩余牌数判断游戏阶段
            if hasattr(state, 'player_hands') and hasattr(state, 'current_player'):
                total_cards = sum(len(hand) for hand in state.player_hands.values())
                if total_cards < 20:  # 假设少于20张为后期
                    game_stage = "late"
                elif total_cards > 40:  # 假设超过40张为前期
                    game_stage = "early"
            
            # 根据游戏阶段调整置信度
            if game_stage == "early":
                # 游戏早期，模型通常更不确定
                confidence -= 0.05
            elif game_stage == "late":
                # 游戏后期，模型通常更确定
                confidence += 0.1
        
        # 7. 【新增】历史表现评估
        if hasattr(self, 'performance_history') and len(self.performance_history) > 0:
            # 计算低层策略的历史表现
            recent_successes = sum(1 for result in self.performance_history[-5:] if result > 0)
            
            # 根据历史表现调整置信度
            if recent_successes >= 4:  # 近期表现非常好
                confidence += 0.15
            elif recent_successes >= 3:  # 近期表现不错
                confidence += 0.1
            elif recent_successes <= 1:  # 近期表现不佳
                confidence -= 0.1
        
        # 8. 【新增】检测重复状态
        if state is not None and hasattr(self, 'state_history') and len(self.state_history) > 0:
            # 简化的状态相似度检测
            similar_state_found = False
            
            # 这里应该使用更复杂的相似度度量，简化示例
            if hasattr(state, 'get_fingerprint') and any(s.get_fingerprint() == state.get_fingerprint() 
                                                      for s in self.state_history[-5:]):
                similar_state_found = True
                
            # 如果发现相似状态，增加置信度
            if similar_state_found:
                confidence += 0.1
        
        # 返回规范化的置信度分数，确保在[0,1]范围内
        return min(1.0, max(0.0, confidence))
    
    def _get_action_values(self, state_tensor: torch.Tensor, legal_actions: List[Action]) -> Optional[List[float]]:
        """
        尝试从低层策略中获取动作值预测。
        
        Args:
            state_tensor: 状态张量
            legal_actions: 合法动作列表
            
        Returns:
            动作值列表或None（如果无法获取）
        """
        # 尝试不同的方法获取动作值
        if hasattr(self.low_level_policy, 'get_action_values'):
            # 直接获取动作值
            return self.low_level_policy.get_action_values(state_tensor, legal_actions)
        elif hasattr(self.low_level_policy, 'model') and hasattr(self.low_level_policy.model, 'forward'):
            # 尝试从模型中获取值
            try:
                with torch.no_grad():
                    outputs = self.low_level_policy.model.forward(state_tensor.unsqueeze(0))
                    if isinstance(outputs, tuple) and len(outputs) > 0:
                        logits = outputs[0].squeeze(0)
                        
                        # 提取合法动作的值
                        values = []
                        for action in legal_actions:
                            if hasattr(action, 'action_id'):
                                action_id = action.action_id
                                if 0 <= action_id < len(logits):
                                    values.append(float(logits[action_id].item()))
                                    
                        if values:
                            return values
            except Exception as e:
                logger.debug(f"获取动作值时出错: {e}")
                
        return None

    def _use_high_level_policy(
        self,
        state_tensor: torch.Tensor,
        legal_actions: List[Action]
    ) -> Tuple[Action, Any]:
        """
        使用高层策略，设定目标并执行

        Args:
            state_tensor: 状态张量
            legal_actions: 合法动作列表

        Returns:
            选择的动作和设定的目标
        """
        # 使用高层策略设定目标
        goal = self.high_level_policy.set_goal(state_tensor)
        
        # 如果启用对手建模，传递对手表示
        if self.use_opponent_modeling and self.current_opponent_representation and hasattr(self.low_level_policy, 'set_opponent_representation'):
            self.low_level_policy.set_opponent_representation(self.current_opponent_representation)
            
        # 将目标传递给低层策略执行
        action = self.low_level_policy.execute_with_goal(state_tensor, goal, legal_actions)
        
        return action, goal

    def _use_low_level_policy(
        self,
        state_tensor: torch.Tensor,
        legal_actions: List[Action]
    ) -> Action:
        """
        使用低层策略执行微调

        Args:
            state_tensor: 状态张量
            legal_actions: 合法动作列表

        Returns:
            选择的动作
        """
        # 如果启用对手建模，传递对手表示
        if self.use_opponent_modeling and self.current_opponent_representation and hasattr(self.low_level_policy, 'set_opponent_representation'):
            self.low_level_policy.set_opponent_representation(self.current_opponent_representation)
            
        # 考虑历史上下文（如果使用历史）
        context = None
        if self.use_history and self.goal_history:
            # 使用最近的目标作为上下文
            context = {"recent_goals": self.goal_history[-min(len(self.goal_history), 3):]}
            
        # 使用低层策略微调
        action = self.low_level_policy.execute(state_tensor, legal_actions, context=context)
        
        return action

    def _use_direct_policy(
        self,
        state_tensor: torch.Tensor,
        legal_actions: List[Action]
    ) -> Action:
        """
        直接使用低层策略，无需微调

        Args:
            state_tensor: 状态张量
            legal_actions: 合法动作列表

        Returns:
            选择的动作
        """
        # 如果启用对手建模，传递对手表示
        if self.use_opponent_modeling and self.current_opponent_representation and hasattr(self.low_level_policy, 'set_opponent_representation'):
            self.low_level_policy.set_opponent_representation(self.current_opponent_representation)
            
        # 使用低层策略的直接决策方法
        if hasattr(self.low_level_policy, 'decide_directly'):
            return self.low_level_policy.decide_directly(state_tensor, legal_actions)
        else:
            # 如果没有直接决策方法，退回到普通执行
            return self.low_level_policy.execute(state_tensor, legal_actions)

    def get_stats(self) -> Dict[str, Any]:
        """
        获取控制器详细统计信息，包括:
        - 调用次数和比例
        - 各种平均值
        - 性能历史数据
        - 状态历史信息
        - 自适应探索状态
        - 对手建模信息

        Returns:
            包含统计数据的字典
        """
        total_calls = max(1, self.stats["total_calls"])
        
        # 计算最近的性能指标
        recent_performance = 0.0
        recent_samples = 0
        if hasattr(self, 'performance_history') and len(self.performance_history) > 0:
            recent_count = min(10, len(self.performance_history))
            recent_performance = sum(self.performance_history[-recent_count:]) / recent_count
            recent_samples = recent_count
        
        # 计算各决策模式的性能
        mode_performance = {}
        for mode, history in self.stats.get("mode_performance", {}).items():
            if history:
                mode_performance[mode] = {
                    "average": sum(history) / len(history),
                    "recent": sum(history[-min(5, len(history)):]) / min(5, len(history)) if history else 0.0,
                    "samples": len(history)
                }
        
        # 基础统计
        stats_dict = {
            "calls": {
                "total": self.stats["total_calls"],
                "high_level": self.stats["high_level_calls"],
                "low_level": self.stats["low_level_calls"],
                "direct": self.stats["direct_calls"]
            },
            "ratios": {
                "high_level": self.stats["high_level_calls"] / total_calls,
                "low_level": self.stats["low_level_calls"] / total_calls,
                "direct": self.stats["direct_calls"] / total_calls
            },
            "averages": {
                "complexity": self.stats["avg_complexity"],
                "confidence": self.stats["avg_confidence"]
            },
            "last_mode": self.stats["last_mode"],
            "opponent_modeling": {
                "enabled": self.use_opponent_modeling,
                "current_opponent_type": self.stats.get("opponent_type", "unknown") if self.use_opponent_modeling else "disabled"
            }
        }
        
        # 【新增】性能历史统计
        stats_dict["performance"] = {
            "history_size": len(getattr(self, 'performance_history', [])),
            "recent_performance": recent_performance,
            "recent_samples": recent_samples,
            "by_mode": mode_performance
        }
        
        # 【新增】状态历史统计
        stats_dict["state_history"] = {
            "enabled": self.use_history,
            "size": len(getattr(self, 'state_history', [])),
            "max_size": getattr(self, 'max_state_history', 0)
        }
        
        # 【新增】自适应探索统计
        stats_dict["adaptation"] = {
            "enabled": getattr(self, 'enable_adaptation', False),
            "current_phase": getattr(self, 'adaptation_phase', 0),
            "cycle_length": getattr(self, 'adaptation_cycle_length', 0),
            "progress_in_cycle": self.stats["total_calls"] % getattr(self, 'adaptation_cycle_length', 1) 
                if getattr(self, 'enable_adaptation', False) else 0
        }
        
        # 【新增】阈值信息
        stats_dict["thresholds"] = {
            "base": {
                "complexity": self.complexity_threshold,
                "confidence": self.confidence_threshold
            },
            "adjusted": {}  # 在需要时可以填充实际调整后的值
        }
        
        return stats_dict

    def _log_stats(self) -> None:
        """
        记录控制器详细统计信息到日志系统
        """
        total = self.stats["total_calls"]
        high_level = self.stats["high_level_calls"]
        low_level = self.stats["low_level_calls"]
        direct = self.stats["direct_calls"]
        
        high_level_pct = high_level / total * 100 if total > 0 else 0
        low_level_pct = low_level / total * 100 if total > 0 else 0
        direct_pct = direct / total * 100 if total > 0 else 0
        
        logger.info(f"HRL控制器统计 [{total}次调用]:")
        logger.info(f"  高层策略: {high_level}次 ({high_level_pct:.1f}%)")
        logger.info(f"  低层微调: {low_level}次 ({low_level_pct:.1f}%)")
        logger.info(f"  直接决策: {direct}次 ({direct_pct:.1f}%)")
        logger.info(f"  平均复杂度: {self.stats['avg_complexity']:.4f}")
        logger.info(f"  平均置信度: {self.stats['avg_confidence']:.4f}")
        
        # 【新增】性能统计日志
        if hasattr(self, 'performance_history') and len(self.performance_history) > 0:
            recent_count = min(10, len(self.performance_history))
            recent_performance = sum(self.performance_history[-recent_count:]) / recent_count
            logger.info(f"  近期性能: {recent_performance:.4f} (基于{recent_count}个样本)")
            
            # 输出各决策模式的性能
            if "mode_performance" in self.stats:
                logger.info("  决策模式性能:")
                for mode, history in self.stats["mode_performance"].items():
                    if history:
                        avg = sum(history) / len(history)
                        recent = sum(history[-min(5, len(history)):]) / min(5, len(history))
                        logger.info(f"    - {mode}: 平均={avg:.4f}, 近期={recent:.4f} (样本数={len(history)})")
        
        # 【新增】输出自适应探索信息
        if hasattr(self, 'enable_adaptation') and self.enable_adaptation:
            phase_names = ["基线", "高层探索", "直接探索", "低层探索"]
            current_phase = phase_names[self.adaptation_phase] if 0 <= self.adaptation_phase < len(phase_names) else f"阶段{self.adaptation_phase}"
            progress = (total % self.adaptation_cycle_length) / self.adaptation_cycle_length * 100
            logger.info(f"  自适应探索: {current_phase} (进度: {progress:.1f}%)")
        
        # 【新增】输出当前阈值
        adjusted_thresholds = self._get_adjusted_thresholds()
        logger.info(f"  当前阈值: 复杂度={adjusted_thresholds[0]:.4f} (基准={self.complexity_threshold:.4f}), "
                    f"置信度={adjusted_thresholds[1]:.4f} (基准={self.confidence_threshold:.4f})")
        
        # 对手建模信息
        if self.use_opponent_modeling and 'opponent_type' in self.stats:
            logger.info(f"  当前对手类型: {self.stats['opponent_type']}")
            
            # 如果有对手表示的更多信息，也记录它们
            if self.current_opponent_representation:
                if "style" in self.current_opponent_representation:
                    logger.info(f"  对手风格: {self.current_opponent_representation['style']}")
                if "confidence" in self.current_opponent_representation:
                    logger.info(f"  对手预测置信度: {self.current_opponent_representation['confidence']:.4f}")
                if "predictability" in self.current_opponent_representation:
                    logger.info(f"  对手可预测性: {self.current_opponent_representation['predictability']:.4f}")
