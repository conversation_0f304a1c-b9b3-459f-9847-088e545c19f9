"""
图构建模块

提供将原始手牌数据转换为图结构的功能，支持不同类型的图构建策略。
"""

import networkx as nx
import numpy as np
from typing import Dict, List, Tuple, Set, Any, Optional, Union

from cardgame_ai.core.base import State
from cardgame_ai.games.doudizhu.state import DouDizhuState
from cardgame_ai.games.doudizhu.card import Card, CardRank, CardSuit
from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType
from cardgame_ai.utils.graph_utils import get_card_features, get_edge_features


class CardGraphBuilder:
    """
    手牌图构建器

    提供多种策略将手牌转换为图结构，支持不同的图构建方法。
    """

    def __init__(self, strategy: str = 'semantic'):
        """
        初始化手牌图构建器

        Args:
            strategy: 图构建策略，可选值为'semantic'（语义关系）, 'hierarchical'（层次关系）, 'complete'（完全图）
        """
        self.strategy = strategy

    def build_graph(self, cards: List[Card], game_type: str = 'doudizhu') -> nx.Graph:
        """
        构建手牌图

        Args:
            cards: 手牌列表
            game_type: 游戏类型，目前支持 'doudizhu'

        Returns:
            nx.Graph: 手牌图
        """
        if game_type.lower() != 'doudizhu':
            raise ValueError(f"不支持的游戏类型: {game_type}")

        # 根据策略选择构建方法
        if self.strategy == 'semantic':
            return self._build_semantic_graph(cards)
        elif self.strategy == 'hierarchical':
            return self._build_hierarchical_graph(cards)
        elif self.strategy == 'complete':
            return self._build_complete_graph(cards)
        else:
            raise ValueError(f"不支持的图构建策略: {self.strategy}")

    def _build_semantic_graph(self, cards: List[Card]) -> nx.Graph:
        """
        构建语义关系图

        根据牌之间的语义关系（如同点数、连续点数、同花色等）构建图。

        Args:
            cards: 手牌列表

        Returns:
            nx.Graph: 手牌图
        """
        G = nx.Graph()

        # 如果手牌为空，返回空图
        if not cards:
            return G

        # 添加牌节点
        for i, card in enumerate(cards):
            node_id = f'card_{i}'
            G.add_node(node_id, type='card', card=card, features=get_card_features(card))

        # 按点数和花色分组
        rank_groups = {}
        suit_groups = {}

        for i, card in enumerate(cards):
            node_id = f'card_{i}'

            # 按点数分组
            if card.rank not in rank_groups:
                rank_groups[card.rank] = []
            rank_groups[card.rank].append((node_id, card))

            # 按花色分组（王牌没有花色）
            if card.suit is not None:
                if card.suit not in suit_groups:
                    suit_groups[card.suit] = []
                suit_groups[card.suit].append((node_id, card))

        # 添加同点数边
        for rank, nodes in rank_groups.items():
            # 确定边类型
            edge_type = 'same_rank'  # 默认类型
            if len(nodes) == 2:
                edge_type = 'pair'
            elif len(nodes) == 3:
                edge_type = 'triplet'
            elif len(nodes) == 4:
                edge_type = 'bomb'

            # 对所有同点数的牌添加边
            for i in range(len(nodes)):
                for j in range(i+1, len(nodes)):
                    node1, _ = nodes[i]
                    node2, _ = nodes[j]
                    G.add_edge(node1, node2, type=edge_type, weight=1.0)

        # 添加同花色边
        for suit, nodes in suit_groups.items():
            for i in range(len(nodes)):
                for j in range(i+1, len(nodes)):
                    node1, _ = nodes[i]
                    node2, _ = nodes[j]
                    G.add_edge(node1, node2, type='same_suit', weight=0.5)

        # 添加连续点数边（可能组成顺子）
        sorted_cards = sorted([(f'card_{i}', card) for i, card in enumerate(cards)],
                             key=lambda x: x[1].rank.value)

        # 只考虑A-K的连续关系，不考虑2和王
        valid_cards = [(node_id, card) for node_id, card in sorted_cards
                      if card.rank.value < CardRank.TWO.value]

        # 检查连续的牌
        for i in range(len(valid_cards) - 1):
            node1, card1 = valid_cards[i]
            node2, card2 = valid_cards[i + 1]

            # 如果点数差为1，添加连续边
            if card2.rank.value - card1.rank.value == 1:
                G.add_edge(node1, node2, type='consecutive', weight=0.8)

        # 检测可能的顺子（连续5张或更多）
        self._add_straight_edges(G, valid_cards)

        return G

    def _add_straight_edges(self, G: nx.Graph, sorted_cards: List[Tuple[str, Card]]):
        """
        添加顺子边

        检测可能的顺子（连续5张或更多），并添加顺子边。

        Args:
            G: 图
            sorted_cards: 按点数排序的牌列表
        """
        if len(sorted_cards) < 5:
            return

        # 查找所有可能的顺子
        for start in range(len(sorted_cards) - 4):
            # 检查是否有连续5张或更多的牌
            consecutive_count = 1
            straight_nodes = [sorted_cards[start][0]]

            for i in range(start + 1, len(sorted_cards)):
                _, prev_card = sorted_cards[i - 1]
                _, curr_card = sorted_cards[i]

                if curr_card.rank.value - prev_card.rank.value == 1:
                    consecutive_count += 1
                    straight_nodes.append(sorted_cards[i][0])
                else:
                    break

            # 如果找到顺子（连续5张或更多）
            if consecutive_count >= 5:
                # 添加顺子边
                for i in range(len(straight_nodes)):
                    for j in range(i + 1, len(straight_nodes)):
                        G.add_edge(straight_nodes[i], straight_nodes[j], type='straight', weight=1.0)

    def _build_hierarchical_graph(self, cards: List[Card]) -> nx.Graph:
        """
        构建层次关系图

        将手牌组织为层次结构，包括单牌层、组合层和牌型层。

        Args:
            cards: 手牌列表

        Returns:
            nx.Graph: 手牌图
        """
        G = nx.Graph()

        # 如果手牌为空，返回空图
        if not cards:
            return G

        # 添加牌节点（单牌层）
        for i, card in enumerate(cards):
            node_id = f'card_{i}'
            G.add_node(node_id, type='card', layer='single', card=card, features=get_card_features(card))

        # 按点数分组
        rank_groups = {}
        for i, card in enumerate(cards):
            if card.rank not in rank_groups:
                rank_groups[card.rank] = []
            rank_groups[card.rank].append((f'card_{i}', card))

        # 添加组合层节点（对子、三张、炸弹）
        combination_id = 0

        for rank, nodes in rank_groups.items():
            # 对子
            if len(nodes) == 2:
                comb_node = f'pair_{combination_id}'
                G.add_node(comb_node, type='combination', layer='combination',
                          combination_type='pair', rank=rank)
                combination_id += 1

                # 连接单牌到组合
                for node_id, _ in nodes:
                    G.add_edge(node_id, comb_node, type='belongs_to', weight=1.0)

            # 三张
            elif len(nodes) == 3:
                comb_node = f'triplet_{combination_id}'
                G.add_node(comb_node, type='combination', layer='combination',
                          combination_type='triplet', rank=rank)
                combination_id += 1

                # 连接单牌到组合
                for node_id, _ in nodes:
                    G.add_edge(node_id, comb_node, type='belongs_to', weight=1.0)

            # 炸弹
            elif len(nodes) == 4:
                comb_node = f'bomb_{combination_id}'
                G.add_node(comb_node, type='combination', layer='combination',
                          combination_type='bomb', rank=rank)
                combination_id += 1

                # 连接单牌到组合
                for node_id, _ in nodes:
                    G.add_edge(node_id, comb_node, type='belongs_to', weight=1.0)

        # 检测顺子
        sorted_cards = sorted([(f'card_{i}', card) for i, card in enumerate(cards)],
                             key=lambda x: x[1].rank.value)

        # 只考虑A-K的连续关系，不考虑2和王
        valid_cards = [(node_id, card) for node_id, card in sorted_cards
                      if card.rank.value < CardRank.TWO.value]

        # 查找所有可能的顺子
        for start in range(len(valid_cards) - 4):
            # 检查是否有连续5张或更多的牌
            consecutive_count = 1
            straight_nodes = [valid_cards[start][0]]

            for i in range(start + 1, len(valid_cards)):
                _, prev_card = valid_cards[i - 1]
                _, curr_card = valid_cards[i]

                if curr_card.rank.value - prev_card.rank.value == 1:
                    consecutive_count += 1
                    straight_nodes.append(valid_cards[i][0])
                else:
                    break

            # 如果找到顺子（连续5张或更多）
            if consecutive_count >= 5:
                # 添加顺子节点
                straight_node = f'straight_{combination_id}'
                G.add_node(straight_node, type='combination', layer='combination',
                          combination_type='straight', length=consecutive_count)
                combination_id += 1

                # 连接单牌到顺子
                for node_id in straight_nodes:
                    G.add_edge(node_id, straight_node, type='belongs_to', weight=1.0)

        return G

    def _build_complete_graph(self, cards: List[Card]) -> nx.Graph:
        """
        构建完全图

        将所有牌连接为完全图，边的权重基于牌之间的关系。

        Args:
            cards: 手牌列表

        Returns:
            nx.Graph: 手牌图
        """
        G = nx.Graph()

        # 如果手牌为空，返回空图
        if not cards:
            return G

        # 添加牌节点
        for i, card in enumerate(cards):
            node_id = f'card_{i}'
            G.add_node(node_id, type='card', card=card, features=get_card_features(card))

        # 添加所有可能的边（完全图）
        for i in range(len(cards)):
            for j in range(i + 1, len(cards)):
                node1 = f'card_{i}'
                node2 = f'card_{j}'
                card1 = cards[i]
                card2 = cards[j]

                # 计算边的权重和类型
                edge_type, weight = self._calculate_edge_weight(card1, card2)

                # 添加边
                G.add_edge(node1, node2, type=edge_type, weight=weight)

        return G

    def _calculate_edge_weight(self, card1: Card, card2: Card) -> Tuple[str, float]:
        """
        计算两张牌之间的边类型和权重

        Args:
            card1: 第一张牌
            card2: 第二张牌

        Returns:
            Tuple[str, float]: (边类型, 权重)
        """
        # 同点数
        if card1.rank == card2.rank:
            if card1.rank in [CardRank.SMALL_JOKER, CardRank.BIG_JOKER]:
                return 'rocket', 1.0
            else:
                return 'same_rank', 1.0

        # 同花色
        if card1.suit is not None and card2.suit is not None and card1.suit == card2.suit:
            return 'same_suit', 0.5

        # 连续点数（可能组成顺子）
        if (card1.rank.value < CardRank.TWO.value and
            card2.rank.value < CardRank.TWO.value and
            abs(card1.rank.value - card2.rank.value) == 1):
            return 'consecutive', 0.8

        # 默认边
        return 'default', 0.1