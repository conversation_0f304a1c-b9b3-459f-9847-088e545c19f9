"""
GTO策略模块

提供用于表示和加载GTO策略的类。
"""

import os
import pickle
import numpy as np
import logging
from typing import Dict, Any, Optional, List, Tuple, Union, Callable

# 配置日志
logger = logging.getLogger(__name__)

class GTOPolicy:
    """
    GTO策略类
    
    用于表示和加载博弈论最优(GTO)策略。
    """
    
    def __init__(
        self,
        policy_path: Optional[str] = None,
        feature_extractor: Optional[Callable[[Any], str]] = None,
        default_policy_generator: Optional[Callable[[Any, List[int]], np.ndarray]] = None
    ):
        """
        初始化GTO策略
        
        Args:
            policy_path: GTO策略文件路径，如果提供，将从文件加载策略
            feature_extractor: 状态特征提取器，用于将状态转换为特征字符串
            default_policy_generator: 默认策略生成器，用于生成没有预计算策略的状态的策略
        """
        self.policy = {}
        self.feature_extractor = feature_extractor
        self.default_policy_generator = default_policy_generator
        
        if policy_path and os.path.exists(policy_path):
            self.load(policy_path)
            logger.info(f"已从 {policy_path} 加载GTO策略，包含 {len(self.policy)} 个状态")
        elif policy_path:
            logger.warning(f"GTO策略文件 {policy_path} 不存在，将使用空策略")
    
    def load(self, policy_path: str) -> None:
        """
        从文件加载GTO策略
        
        Args:
            policy_path: GTO策略文件路径
        """
        try:
            with open(policy_path, 'rb') as f:
                self.policy = pickle.load(f)
            logger.info(f"成功加载GTO策略，包含 {len(self.policy)} 个状态")
        except Exception as e:
            logger.error(f"加载GTO策略时出错: {e}")
            self.policy = {}
    
    def save(self, policy_path: str) -> None:
        """
        将GTO策略保存到文件
        
        Args:
            policy_path: GTO策略文件路径
        """
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(policy_path), exist_ok=True)
            
            with open(policy_path, 'wb') as f:
                pickle.dump(self.policy, f)
            logger.info(f"成功保存GTO策略到 {policy_path}，包含 {len(self.policy)} 个状态")
        except Exception as e:
            logger.error(f"保存GTO策略时出错: {e}")
    
    def get_state_key(self, state: Any) -> str:
        """
        获取状态的唯一标识符
        
        Args:
            state: 状态
            
        Returns:
            状态的唯一标识符
        """
        if self.feature_extractor:
            return self.feature_extractor(state)
        return str(state)
    
    def get_policy(self, state: Any, legal_actions: Optional[List[int]] = None) -> np.ndarray:
        """
        获取状态的GTO策略分布
        
        Args:
            state: 状态
            legal_actions: 合法动作列表
            
        Returns:
            状态的GTO策略分布
        """
        state_key = self.get_state_key(state)
        
        # 如果有预计算的策略，直接返回
        if state_key in self.policy:
            return self.policy[state_key]
        
        # 如果有默认策略生成器，使用它生成策略
        if self.default_policy_generator and legal_actions:
            return self.default_policy_generator(state, legal_actions)
        
        # 否则，返回均匀分布
        if legal_actions:
            policy = np.zeros(max(legal_actions) + 1)
            policy[legal_actions] = 1.0 / len(legal_actions)
            return policy
        
        # 如果没有合法动作信息，返回None
        return None
    
    def set_policy(self, state: Any, policy_distribution: np.ndarray) -> None:
        """
        设置状态的GTO策略分布
        
        Args:
            state: 状态
            policy_distribution: 策略分布
        """
        state_key = self.get_state_key(state)
        self.policy[state_key] = policy_distribution
    
    def update_policy(self, state: Any, policy_distribution: np.ndarray, alpha: float = 0.1) -> None:
        """
        更新状态的GTO策略分布
        
        Args:
            state: 状态
            policy_distribution: 新的策略分布
            alpha: 更新率
        """
        state_key = self.get_state_key(state)
        
        if state_key in self.policy:
            # 如果已有策略，进行加权平均
            self.policy[state_key] = (1 - alpha) * self.policy[state_key] + alpha * policy_distribution
        else:
            # 如果没有策略，直接设置
            self.policy[state_key] = policy_distribution
    
    def get_action(self, state: Any, legal_actions: Optional[List[int]] = None) -> Optional[int]:
        """
        根据GTO策略获取最佳动作
        
        Args:
            state: 状态
            legal_actions: 合法动作列表
            
        Returns:
            最佳动作的索引
        """
        policy = self.get_policy(state, legal_actions)
        if policy is None:
            return None
        
        # 如果有合法动作，只考虑合法动作
        if legal_actions:
            legal_policy = policy[legal_actions]
            return legal_actions[np.argmax(legal_policy)]
        
        return np.argmax(policy)
