"""
配置工具模块

为训练主程序提供配置加载、合并和日志设置等工具函数。
"""
import os
import sys
import logging
import yaml
from typing import Dict, Any, Optional, List, Tuple, Union

# 从项目现有工具中导入
from cardgame_ai.utils.config import ConfigManager
from cardgame_ai.utils.config_loader import _deep_merge


def setup_logging(log_level: str = 'INFO', log_file: Optional[str] = None) -> None:
    """
    设置日志系统
    
    Args:
        log_level (str): 日志级别，默认为 'INFO'
        log_file (Optional[str]): 日志文件路径，默认为 None（仅控制台输出）
    """
    # 转换日志级别字符串为对应的常量
    numeric_level = getattr(logging, log_level.upper(), None)
    if not isinstance(numeric_level, int):
        raise ValueError(f'无效的日志级别: {log_level}')
    
    # 基本配置
    logging_config = {
        'level': numeric_level,
        'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        'datefmt': '%Y-%m-%d %H:%M:%S',
    }
    
    # 如果指定了日志文件，添加文件处理器
    if log_file:
        # 确保日志目录存在
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
        
        logging_config['filename'] = log_file
        logging_config['filemode'] = 'a'  # 追加模式
    
    # 应用配置
    logging.basicConfig(**logging_config)
    
    # 设置第三方库的日志级别为WARNING（减少不必要的输出）
    logging.getLogger("matplotlib").setLevel(logging.WARNING)
    logging.getLogger("PIL").setLevel(logging.WARNING)
    
    # 返回根日志记录器
    return logging.getLogger()


def load_config(game: str, algo: str, custom_config_path: Optional[str] = None) -> Dict[str, Any]:
    """
    加载配置文件
    
    如果提供了自定义配置路径，则直接加载；否则根据游戏和算法名称推断默认配置路径。
    
    Args:
        game (str): 游戏名称
        algo (str): 算法名称
        custom_config_path (Optional[str]): 自定义配置文件路径
        
    Returns:
        Dict[str, Any]: 加载的配置字典
        
    Raises:
        FileNotFoundError: 如果配置文件不存在
        ValueError: 如果配置文件格式不支持
    """
    logger = logging.getLogger(__name__)
    
    # 如果提供了自定义配置路径，直接加载
    if custom_config_path:
        logger.info(f"从自定义路径加载配置: {custom_config_path}")
        if not os.path.exists(custom_config_path):
            raise FileNotFoundError(f"自定义配置文件不存在: {custom_config_path}")
        
        config_manager = ConfigManager(custom_config_path)
        return config_manager.config
    
    # 否则，尝试多个可能的默认路径
    possible_paths = [
        f"configs/{game}/{algo}_config.yaml",  # 例如: configs/doudizhu/muzero_config.yaml
        f"configs/{game}/{algo}.yaml",         # 例如: configs/doudizhu/muzero.yaml
        f"config/{game}/{algo}_config.yaml",   # 备选目录
        f"config/{game}/{algo}.yaml",          # 备选目录
        f"cardgame_ai/configs/{game}/{algo}_config.yaml",  # 项目内路径
        f"cardgame_ai/configs/{game}/{algo}.yaml",         # 项目内路径
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            logger.info(f"从默认路径加载配置: {path}")
            config_manager = ConfigManager(path)
            return config_manager.config
    
    # 如果找不到配置文件，则报错
    error_msg = f"找不到 {game}/{algo} 的配置文件，尝试过以下路径:\n" + "\n".join(possible_paths)
    logger.error(error_msg)
    raise FileNotFoundError(error_msg)


def merge_cli_args(config: Dict[str, Any], unknown_args: List[str]) -> Dict[str, Any]:
    """
    将命令行参数合并到配置字典中
    
    处理 unknown_args 列表，将形如 --key=value 或 --key value 的参数转换为嵌套键值对
    并合并到配置字典中。支持嵌套键，例如 --model.learning_rate=0.001 会设置 config['model']['learning_rate']
    
    Args:
        config (Dict[str, Any]): 原始配置字典
        unknown_args (List[str]): 未知命令行参数列表
        
    Returns:
        Dict[str, Any]: 合并后的配置字典
    """
    logger = logging.getLogger(__name__)
    result = config.copy()
    
    # 处理每一对未知参数
    i = 0
    while i < len(unknown_args):
        arg = unknown_args[i]
        
        # 跳过非选项参数
        if not arg.startswith('--'):
            i += 1
            continue
        
        # 移除前缀 '--'
        arg = arg[2:]
        
        # 处理 --key=value 形式
        if '=' in arg:
            key, value = arg.split('=', 1)
            i += 1
        # 处理 --key value 形式
        elif i + 1 < len(unknown_args) and not unknown_args[i + 1].startswith('--'):
            key = arg
            value = unknown_args[i + 1]
            i += 2
        # 处理布尔标志 --key（没有值）
        else:
            key = arg
            value = 'true'
            i += 1
        
        # 尝试将值转换为适当的类型
        if value.lower() == 'true':
            value = True
        elif value.lower() == 'false':
            value = False
        elif value.isdigit():
            value = int(value)
        else:
            try:
                value = float(value)
            except ValueError:
                # 保持为字符串
                pass
        
        # 处理嵌套键（例如：model.learning_rate）
        if '.' in key:
            keys = key.split('.')
            current = result
            
            # 创建嵌套字典路径
            for k in keys[:-1]:
                if k not in current or not isinstance(current[k], dict):
                    current[k] = {}
                current = current[k]
            
            # 设置最后一个键的值
            current[keys[-1]] = value
            logger.debug(f"设置嵌套配置: {key} = {value}")
        else:
            # 直接设置键值
            result[key] = value
            logger.debug(f"设置配置: {key} = {value}")
    
    # 合并命令行参数指定的显式参数（未使用 unknown_args 传递的）
    return result


def merge_explicit_args(config: Dict[str, Any], args_dict: Dict[str, Any]) -> Dict[str, Any]:
    """
    将显式命令行参数合并到配置字典中
    
    Args:
        config (Dict[str, Any]): 原始配置字典
        args_dict (Dict[str, Any]): 参数字典，通常来自 argparse 解析的参数
        
    Returns:
        Dict[str, Any]: 合并后的配置字典
    """
    result = config.copy()
    
    # 遍历参数字典
    for key, value in args_dict.items():
        # 仅处理非None的值（允许覆盖默认设置）
        if value is not None:
            # 将下划线替换为点，以支持嵌套配置（例如：batch_size -> training.batch_size）
            # 这里假设参数命名约定与配置结构匹配
            if key == 'epochs' and value is not None:
                if 'training' not in result:
                    result['training'] = {}
                result['training']['epochs'] = value
            elif key == 'batch_size' and value is not None:
                if 'training' not in result:
                    result['training'] = {}
                result['training']['batch_size'] = value
            elif key == 'learning_rate' and value is not None:
                if 'training' not in result:
                    result['training'] = {}
                result['training']['learning_rate'] = value
            elif key == 'device' and value is not None:
                result['device'] = value
            elif key == 'output_dir' and value is not None:
                result['output_dir'] = value
    
    return result 