#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
优化训练集成脚本的单元测试

测试重构后的训练脚本是否正确实现了fail-fast原则，
移除了所有模拟组件和备用策略机制。
"""

import pytest
import sys
import os
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from cardgame_ai.zhuchengxu.optimized_training_integrated import OptimizedTrainingSystem


class TestOptimizedTrainingSystem:
    """优化训练系统测试类"""

    def setup_method(self):
        """测试前置设置"""
        self.training_system = OptimizedTrainingSystem()

    def test_initialization_success(self):
        """测试正常初始化"""
        assert self.training_system.project_root is not None
        assert self.training_system.logger is None  # 初始化时未设置
        assert self.training_system.monitor is None
        assert self.training_system.algorithm is None
        assert self.training_system.trainer is None
        assert self.training_system.evaluator is None

    def test_setup_logging(self, tmp_path):
        """测试日志设置"""
        log_dir = tmp_path / "logs"
        self.training_system.setup_logging("INFO", str(log_dir))
        
        assert self.training_system.logger is not None
        assert log_dir.exists()

    @patch('cardgame_ai.zhuchengxu.optimized_training_integrated.torch')
    def test_detect_device_cuda_available(self, mock_torch):
        """测试CUDA设备检测"""
        mock_torch.cuda.is_available.return_value = True
        mock_torch.cuda.device_count.return_value = 2
        
        # 设置日志以避免错误
        self.training_system.setup_logging()
        
        device = self.training_system.detect_device()
        assert device == "cuda:0"

    @patch('cardgame_ai.zhuchengxu.optimized_training_integrated.torch')
    def test_detect_device_cuda_unavailable(self, mock_torch):
        """测试CUDA不可用时的设备检测"""
        mock_torch.cuda.is_available.return_value = False
        
        # 设置日志以避免错误
        self.training_system.setup_logging()
        
        device = self.training_system.detect_device()
        assert device == "cpu"

    def test_detect_device_torch_not_available(self):
        """测试PyTorch不可用时的设备检测"""
        # 设置日志以避免错误
        self.training_system.setup_logging()
        
        with patch('cardgame_ai.zhuchengxu.optimized_training_integrated.torch', side_effect=ImportError):
            device = self.training_system.detect_device()
            assert device == "cpu"

    def test_load_config_file_not_found(self, tmp_path):
        """测试配置文件不存在时立即失败"""
        # 设置日志以避免错误
        self.training_system.setup_logging()
        
        non_existent_config = str(tmp_path / "non_existent.yaml")
        
        with pytest.raises(FileNotFoundError) as exc_info:
            self.training_system.load_config(non_existent_config)
        
        assert "配置文件不存在" in str(exc_info.value)
        assert "无法继续训练" in str(exc_info.value)

    def test_load_config_invalid_format(self, tmp_path):
        """测试不支持的配置文件格式时立即失败"""
        # 设置日志以避免错误
        self.training_system.setup_logging()
        
        # 创建一个JSON格式的配置文件
        json_config = tmp_path / "config.json"
        json_config.write_text('{"test": "value"}')
        
        with pytest.raises(ValueError) as exc_info:
            self.training_system.load_config(str(json_config))
        
        assert "不支持的配置文件格式" in str(exc_info.value)
        assert "只支持YAML格式" in str(exc_info.value)

    def test_load_config_yaml_parse_error(self, tmp_path):
        """测试YAML解析错误时立即失败"""
        # 设置日志以避免错误
        self.training_system.setup_logging()
        
        # 创建一个无效的YAML文件
        invalid_yaml = tmp_path / "invalid.yaml"
        invalid_yaml.write_text("invalid: yaml: content: [")
        
        with pytest.raises(RuntimeError) as exc_info:
            self.training_system.load_config(str(invalid_yaml))
        
        assert "配置文件加载失败，无法继续训练" in str(exc_info.value)

    def test_load_config_success(self, tmp_path):
        """测试成功加载配置文件"""
        # 设置日志以避免错误
        self.training_system.setup_logging()
        
        # 创建一个有效的YAML文件
        valid_yaml = tmp_path / "valid.yaml"
        valid_yaml.write_text("""
game: doudizhu
algorithm: efficient_zero
training:
  epochs: 100
  batch_size: 64
""")
        
        config = self.training_system.load_config(str(valid_yaml))
        
        assert config['game'] == 'doudizhu'
        assert config['algorithm'] == 'efficient_zero'
        assert config['training']['epochs'] == 100
        assert config['training']['batch_size'] == 64

    @patch('cardgame_ai.zhuchengxu.optimized_training_integrated.train_efficient_zero')
    def test_execute_training_loop_success(self, mock_train):
        """测试训练循环成功执行"""
        # 设置日志以避免错误
        self.training_system.setup_logging()
        
        # 模拟训练成功
        mock_train.return_value = 0
        
        config = {
            'training': {'epochs': 10},
            'device': 'cpu'
        }
        
        result = self.training_system._execute_training_loop(config, 'cpu', False)
        assert result is True
        mock_train.assert_called_once()

    @patch('cardgame_ai.zhuchengxu.optimized_training_integrated.train_efficient_zero')
    def test_execute_training_loop_failure(self, mock_train):
        """测试训练循环失败时立即抛出异常"""
        # 设置日志以避免错误
        self.training_system.setup_logging()
        
        # 模拟训练失败
        mock_train.return_value = 1
        
        config = {
            'training': {'epochs': 10},
            'device': 'cpu'
        }
        
        with pytest.raises(RuntimeError) as exc_info:
            self.training_system._execute_training_loop(config, 'cpu', False)
        
        assert "训练失败，返回码: 1" in str(exc_info.value)

    @patch('cardgame_ai.zhuchengxu.optimized_training_integrated.train_efficient_zero')
    def test_execute_training_loop_exception(self, mock_train):
        """测试训练过程中异常时立即抛出"""
        # 设置日志以避免错误
        self.training_system.setup_logging()
        
        # 模拟训练抛出异常
        mock_train.side_effect = Exception("训练模块内部错误")
        
        config = {
            'training': {'epochs': 10},
            'device': 'cpu'
        }
        
        with pytest.raises(RuntimeError) as exc_info:
            self.training_system._execute_training_loop(config, 'cpu', False)
        
        assert "训练失败，无法继续" in str(exc_info.value)

    def test_no_fallback_mechanisms(self):
        """测试确保没有备用机制"""
        # 检查类中不存在任何备用相关的属性或方法
        training_system = OptimizedTrainingSystem()
        
        # 确保没有HAS_*标志
        assert not hasattr(training_system, 'use_unified_system')
        assert not hasattr(training_system, 'use_optimization')
        
        # 确保没有备用方法
        assert not hasattr(training_system, '_fallback_training')
        assert not hasattr(training_system, '_simulation_mode')
        assert not hasattr(training_system, '_mock_training')

    def test_no_simulation_components(self):
        """测试确保没有模拟组件"""
        # 检查模块级别不存在模拟相关的类或函数
        import cardgame_ai.zhuchengxu.optimized_training_integrated as module
        
        # 确保没有模拟类
        assert not hasattr(module, 'NumpyFallback')
        assert not hasattr(module, 'MockTrainer')
        assert not hasattr(module, 'SimulationMode')
        
        # 确保没有HAS_*标志
        assert not hasattr(module, 'HAS_NUMPY')
        assert not hasattr(module, 'HAS_YAML')
        assert not hasattr(module, 'HAS_UNIFIED_SYSTEM')
        assert not hasattr(module, 'HAS_TRAINING_MODULE')
        assert not hasattr(module, 'HAS_OPTIMIZATION_COMPONENTS')


class TestFailFastBehavior:
    """测试fail-fast行为"""

    def test_import_failures_cause_immediate_failure(self):
        """测试导入失败时立即失败"""
        # 这个测试验证必需的导入失败时会立即抛出ImportError
        # 而不是使用备用策略
        
        with patch('builtins.__import__', side_effect=ImportError("numpy not found")):
            with pytest.raises(ImportError):
                # 重新导入模块应该失败
                import importlib
                import cardgame_ai.zhuchengxu.optimized_training_integrated
                importlib.reload(cardgame_ai.zhuchengxu.optimized_training_integrated)

    def test_no_silent_failures(self):
        """测试没有静默失败"""
        # 验证所有可能的错误路径都会抛出异常
        training_system = OptimizedTrainingSystem()
        training_system.setup_logging()
        
        # 测试配置加载失败
        with pytest.raises((FileNotFoundError, RuntimeError, ValueError)):
            training_system.load_config("non_existent_file.yaml")
        
        # 测试训练失败
        with patch('cardgame_ai.zhuchengxu.optimized_training_integrated.train_efficient_zero', 
                  side_effect=Exception("Training failed")):
            with pytest.raises(RuntimeError):
                training_system._execute_training_loop({}, 'cpu', False)


if __name__ == "__main__":
    pytest.main([__file__])
