"""
决策解释模块

提供用于解释AI决策过程的工具和函数。
"""

import json
import numpy as np
import torch
from typing import Dict, List, Any, Optional, Union, Tuple
import matplotlib.pyplot as plt
from matplotlib.figure import Figure
import io
import base64


class DecisionExplainer:
    """
    决策解释器

    用于解释AI的决策过程，包括MCTS搜索和神经网络预测。
    """

    def __init__(self, verbose: bool = False):
        """
        初始化决策解释器

        Args:
            verbose (bool, optional): 是否输出详细信息. Defaults to False.
        """
        self.verbose = verbose

    def explain_mcts_decision(self, explanation_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        解释MCTS决策过程

        Args:
            explanation_data (Dict[str, Any]): MCTS搜索的解释数据

        Returns:
            Dict[str, Any]: 格式化的解释结果
        """
        # 提取基本信息
        root_info = explanation_data.get('root_info', {})
        total_simulations = explanation_data.get('total_simulations', 0)
        actual_simulations = explanation_data.get('actual_simulations', total_simulations)
        search_policy = explanation_data.get('search_policy', {})
        top_actions = explanation_data.get('top_actions', [])
        principal_variation = explanation_data.get('principal_variation', [])
        decision_metrics = explanation_data.get('decision_metrics', {})
        tree_stats = explanation_data.get('tree_stats', {})
        tree_visualization = explanation_data.get('tree_visualization', {})

        # 格式化解释结果
        explanation = {
            'summary': self._generate_decision_summary(top_actions, total_simulations, actual_simulations),
            'confidence': self._calculate_decision_confidence(search_policy),
            'top_actions': self._format_top_actions(top_actions),
            'principal_variation': self._format_principal_variation(principal_variation),
            'decision_metrics': self._format_decision_metrics(decision_metrics),
            'tree_statistics': self._format_tree_statistics(tree_stats),
            'search_efficiency': {
                'simulation_efficiency': actual_simulations / total_simulations if total_simulations > 0 else 1.0,
                'early_stopping': actual_simulations < total_simulations,
                'total_simulations': total_simulations,
                'actual_simulations': actual_simulations
            },
            'visualizations': {
                'policy_distribution': self._generate_policy_distribution_chart(search_policy),
                'search_tree': self._generate_enhanced_search_tree_visualization(tree_visualization),
                'decision_metrics': self._generate_decision_metrics_chart(decision_metrics),
                'value_distribution': self._generate_value_distribution_chart(top_actions)
            }
        }

        # 如果有信念状态信息，添加到解释中
        if 'belief_state' in root_info or 'belief_confidence' in root_info:
            explanation['belief_state'] = {
                'confidence': root_info.get('belief_confidence', 0.5),
                'entropy': root_info.get('belief_entropy', 0.0)
            }

            # 如果有信息价值，添加到解释中
            if 'info_value' in root_info:
                explanation['belief_state']['info_value'] = root_info.get('info_value', 0.0)

        # 如果有信息价值，添加到解释中
        if any('info_value' in action for action in top_actions):
            explanation['information_value'] = {
                'summary': self._generate_info_value_summary(top_actions),
                'details': self._format_info_value_details(top_actions)
            }

        # 如果有ACT信息，添加到解释中
        if 'act_info' in explanation_data:
            explanation['adaptive_computation'] = self._format_act_info(explanation_data.get('act_info', {}))

        # 如果有风险敏感决策信息，添加到解释中
        if 'risk_sensitive_decision_info' in explanation_data:
            explanation['risk_sensitivity'] = self._format_risk_info(explanation_data.get('risk_sensitive_decision_info', {}))

        # 如果有内在动机信息，添加到解释中
        if 'intrinsic_motivation_info' in explanation_data:
            explanation['intrinsic_motivation'] = self._format_intrinsic_motivation_info(explanation_data.get('intrinsic_motivation_info', {}))

        # 如果有对手模型先验信息，添加到解释中
        if 'opponent_model_prior_info' in explanation_data:
            explanation['opponent_modeling'] = self._format_opponent_model_info(explanation_data.get('opponent_model_prior_info', {}))

        return explanation

    def explain_network_prediction(self, explanation_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        解释神经网络预测

        Args:
            explanation_data (Dict[str, Any]): 神经网络预测的解释数据

        Returns:
            Dict[str, Any]: 格式化的解释结果
        """
        # 提取网络输出信息
        network_output = explanation_data.get('network_output', {})
        feature_dims = explanation_data.get('feature_dims', {})
        belief_state = explanation_data.get('belief_state', {})

        # 格式化解释结果
        explanation = {
            'summary': self._generate_prediction_summary(network_output),
            'value_estimate': network_output.get('value', 0.0),
            'top_actions': self._format_network_top_actions(network_output.get('top_actions', [])),
            'feature_dimensions': self._format_feature_dimensions(feature_dims),
            'visualizations': {
                'action_distribution': self._generate_action_distribution_chart(network_output.get('top_actions', []))
            }
        }

        # 如果有信念状态信息，添加到解释中
        if belief_state:
            explanation['belief_state'] = {
                'player_id': belief_state.get('player_id', ''),
                'confidence': belief_state.get('confidence', 0.5),
                'entropy': belief_state.get('entropy', 0.0)
            }

        # 如果有优势信息，添加到解释中
        if 'advantage_mean' in network_output:
            explanation['advantage'] = {
                'mean': network_output.get('advantage_mean', 0.0)
            }

        return explanation

    def _generate_decision_summary(self, top_actions: List[Dict[str, Any]], total_simulations: int, actual_simulations: int = None) -> str:
        """
        生成决策摘要

        Args:
            top_actions (List[Dict[str, Any]]): 顶级动作列表
            total_simulations (int): 总模拟次数
            actual_simulations (int, optional): 实际执行的模拟次数. Defaults to None.

        Returns:
            str: 决策摘要
        """
        if not top_actions:
            return "没有足够的信息生成决策摘要。"

        # 获取最佳动作
        best_action = top_actions[0]
        action_id = best_action.get('action', -1)
        visit_count = best_action.get('visit_count', 0)
        visit_percentage = best_action.get('visit_percentage', 0.0)
        value = best_action.get('value', 0.0)
        prior = best_action.get('prior', 0.0)

        # 生成摘要
        summary = f"AI选择了动作{action_id}，"

        # 如果有实际模拟次数，且与总模拟次数不同，则添加到摘要中
        if actual_simulations is not None and actual_simulations < total_simulations:
            summary += f"在计划的{total_simulations}次模拟中实际执行了{actual_simulations}次（{actual_simulations/total_simulations:.1%}），"
            summary += f"该动作被访问了{visit_count}次（{visit_percentage:.1%}）。"
        else:
            summary += f"该动作在{total_simulations}次模拟中被访问了{visit_count}次（{visit_percentage:.1%}）。"

        summary += f"预期价值为{value:.3f}，先验概率为{prior:.3f}。"

        # 如果有信息价值，添加到摘要中
        if 'info_value' in best_action:
            info_value = best_action.get('info_value', 0.0)
            summary += f"该动作具有{info_value:.3f}的信息价值。"

        # 如果有第二好的动作，添加对比信息
        if len(top_actions) > 1:
            second_best = top_actions[1]
            second_action_id = second_best.get('action', -1)
            second_value = second_best.get('value', 0.0)
            value_diff = value - second_value

            summary += f"\n次优选择是动作{second_action_id}，预期价值为{second_value:.3f}，"
            summary += f"比最佳动作低{abs(value_diff):.3f}。"

        return summary

    def _calculate_decision_confidence(self, search_policy: Dict[int, float]) -> float:
        """
        计算决策置信度

        Args:
            search_policy (Dict[int, float]): 搜索策略

        Returns:
            float: 决策置信度，范围在[0, 1]
        """
        if not search_policy:
            return 0.0

        # 获取概率值
        probs = list(search_policy.values())

        # 如果只有一个动作，则置信度为1
        if len(probs) == 1:
            return 1.0

        # 计算最高概率与次高概率的差距
        sorted_probs = sorted(probs, reverse=True)
        if len(sorted_probs) >= 2:
            # 最高概率与次高概率的差距越大，置信度越高
            gap = sorted_probs[0] - sorted_probs[1]
            # 将差距映射到[0, 1]范围
            confidence = min(1.0, gap * 2.0)
            return confidence

        return sorted_probs[0]

    def _format_top_actions(self, top_actions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        格式化顶级动作信息

        Args:
            top_actions (List[Dict[str, Any]]): 顶级动作列表

        Returns:
            List[Dict[str, Any]]: 格式化后的顶级动作列表
        """
        formatted_actions = []

        for action_info in top_actions:
            formatted_action = {
                'action': action_info.get('action', -1),
                'visit_count': action_info.get('visit_count', 0),
                'visit_percentage': f"{action_info.get('visit_percentage', 0.0):.1%}",
                'value': f"{action_info.get('value', 0.0):.3f}",
                'prior': f"{action_info.get('prior', 0.0):.3f}",
                'policy_prob': f"{action_info.get('policy_prob', 0.0):.3f}"
            }

            # 如果有信息价值，添加到格式化动作中
            if 'info_value' in action_info:
                formatted_action['info_value'] = f"{action_info.get('info_value', 0.0):.3f}"

            formatted_actions.append(formatted_action)

        return formatted_actions

    def _format_principal_variation(self, principal_variation: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        格式化主要变化路径

        Args:
            principal_variation (List[Dict[str, Any]]): 主要变化路径

        Returns:
            List[Dict[str, Any]]: 格式化后的主要变化路径
        """
        formatted_variation = []

        for step in principal_variation:
            action = step.get('action', -1)
            node_info = step.get('node_info', {})

            formatted_step = {
                'action': action,
                'value': f"{node_info.get('value', 0.0):.3f}",
                'visit_count': node_info.get('visit_count', 0),
                'player_id': node_info.get('player_id', '')
            }

            # 如果有信念状态信息，添加到格式化步骤中
            if 'belief_confidence' in node_info:
                formatted_step['belief_confidence'] = f"{node_info.get('belief_confidence', 0.5):.3f}"

            if 'belief_entropy' in node_info:
                formatted_step['belief_entropy'] = f"{node_info.get('belief_entropy', 0.0):.3f}"

            # 如果有信息价值，添加到格式化步骤中
            if 'info_value' in node_info:
                formatted_step['info_value'] = f"{node_info.get('info_value', 0.0):.3f}"

            formatted_variation.append(formatted_step)

        return formatted_variation

    def _generate_info_value_summary(self, top_actions: List[Dict[str, Any]]) -> str:
        """
        生成信息价值摘要

        Args:
            top_actions (List[Dict[str, Any]]): 顶级动作列表

        Returns:
            str: 信息价值摘要
        """
        # 过滤出有信息价值的动作
        info_value_actions = [action for action in top_actions if 'info_value' in action]

        if not info_value_actions:
            return "没有动作具有显著的信息价值。"

        # 获取信息价值最高的动作
        max_info_action = max(info_value_actions, key=lambda x: x.get('info_value', 0.0))
        action_id = max_info_action.get('action', -1)
        info_value = max_info_action.get('info_value', 0.0)

        # 生成摘要
        summary = f"动作{action_id}具有最高的信息价值（{info_value:.3f}），"
        summary += "这意味着执行该动作可能会揭示更多关于对手手牌的信息。"

        return summary

    def _format_info_value_details(self, top_actions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        格式化信息价值详情

        Args:
            top_actions (List[Dict[str, Any]]): 顶级动作列表

        Returns:
            List[Dict[str, Any]]: 格式化后的信息价值详情
        """
        # 过滤出有信息价值的动作
        info_value_actions = [action for action in top_actions if 'info_value' in action]

        if not info_value_actions:
            return []

        # 格式化信息价值详情
        formatted_details = []

        for action in info_value_actions:
            formatted_detail = {
                'action': action.get('action', -1),
                'info_value': f"{action.get('info_value', 0.0):.3f}",
                'visit_count': action.get('visit_count', 0),
                'value': f"{action.get('value', 0.0):.3f}"
            }

            formatted_details.append(formatted_detail)

        return formatted_details

    def _generate_policy_distribution_chart(self, search_policy: Dict[int, float]) -> str:
        """
        生成策略分布图表

        Args:
            search_policy (Dict[int, float]): 搜索策略

        Returns:
            str: Base64编码的图表
        """
        # 如果没有策略数据，返回空字符串
        if not search_policy:
            return ""

        # 创建图表
        fig, ax = plt.subplots(figsize=(8, 6))

        # 获取动作和概率
        actions = list(search_policy.keys())
        probs = list(search_policy.values())

        # 绘制条形图
        ax.bar(actions, probs)

        # 设置标题和标签
        ax.set_title("动作概率分布")
        ax.set_xlabel("动作")
        ax.set_ylabel("概率")

        # 将图表转换为Base64编码的字符串
        return self._fig_to_base64(fig)

    def _generate_search_tree_visualization(self, principal_variation: List[Dict[str, Any]]) -> str:
        """
        生成搜索树可视化

        Args:
            principal_variation (List[Dict[str, Any]]): 主要变化路径

        Returns:
            str: Base64编码的图表
        """
        # 如果没有变化路径数据，返回空字符串
        if not principal_variation:
            return ""

        # 创建图表
        fig, ax = plt.subplots(figsize=(10, 6))

        # 获取动作和值
        actions = [step.get('action', -1) for step in principal_variation]
        values = [step.get('node_info', {}).get('value', 0.0) for step in principal_variation]

        # 绘制折线图
        ax.plot(range(len(actions)), values, marker='o')

        # 设置标题和标签
        ax.set_title("主要变化路径")
        ax.set_xlabel("步骤")
        ax.set_ylabel("值估计")

        # 设置x轴刻度
        ax.set_xticks(range(len(actions)))
        ax.set_xticklabels([f"动作{a}" for a in actions])

        # 将图表转换为Base64编码的字符串
        return self._fig_to_base64(fig)

    def _generate_enhanced_search_tree_visualization(self, tree_visualization: Dict[str, Any]) -> str:
        """
        生成增强的搜索树可视化

        Args:
            tree_visualization (Dict[str, Any]): 搜索树可视化数据

        Returns:
            str: Base64编码的图表
        """
        # 如果没有树可视化数据，返回空字符串
        if not tree_visualization or 'root' not in tree_visualization:
            return ""

        # 创建图表
        fig, ax = plt.subplots(figsize=(12, 8))

        # 递归绘制树结构
        self._draw_tree_node(ax, tree_visualization['root'], x=0, y=0, width=10, level=0, max_level=3)

        # 设置标题和标签
        ax.set_title("搜索树结构")

        # 关闭坐标轴
        ax.axis('off')

        # 调整布局
        plt.tight_layout()

        # 将图表转换为Base64编码的字符串
        return self._fig_to_base64(fig)

    def _draw_tree_node(self, ax, node, x, y, width, level, max_level):
        """
        递归绘制树节点

        Args:
            ax: 图表轴
            node: 节点数据
            x: x坐标
            y: y坐标
            width: 节点宽度
            level: 当前层级
            max_level: 最大层级
        """
        # 如果超过最大层级，不再绘制
        if level > max_level:
            return

        # 获取节点信息
        visit_count = node.get('visit_count', 0)
        value = node.get('value', 0.0)
        children = node.get('children', [])

        # 计算节点大小（基于访问次数）
        size = max(100, min(1000, visit_count * 10))

        # 计算节点颜色（基于值）
        color = plt.cm.RdYlGn((value + 1) / 2)  # 将[-1, 1]映射到[0, 1]

        # 绘制节点
        circle = plt.Circle((x, y), size/1000, color=color, alpha=0.7)
        ax.add_patch(circle)

        # 添加节点文本
        if level == 0:
            ax.text(x, y, f"Root\nV:{value:.2f}\nN:{visit_count}", ha='center', va='center', fontsize=8)
        else:
            # 获取动作ID
            action = node.get('action', -1)
            ax.text(x, y, f"A:{action}\nV:{value:.2f}\nN:{visit_count}", ha='center', va='center', fontsize=8)

        # 如果有子节点，递归绘制
        if children:
            # 计算子节点间隔
            child_width = width / len(children)

            # 计算子节点起始x坐标
            start_x = x - width/2 + child_width/2

            # 绘制每个子节点
            for i, child in enumerate(children):
                # 计算子节点坐标
                child_x = start_x + i * child_width
                child_y = y - 2  # 固定垂直间距

                # 绘制连接线
                ax.plot([x, child_x], [y, child_y], 'k-', alpha=0.3)

                # 递归绘制子节点
                self._draw_tree_node(ax, child, child_x, child_y, child_width, level+1, max_level)

    def _format_decision_metrics(self, decision_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """
        格式化决策指标

        Args:
            decision_metrics: 决策指标数据

        Returns:
            Dict[str, Any]: 格式化后的决策指标
        """
        if not decision_metrics:
            return {}

        formatted_metrics = {
            'exploration_exploitation': {
                'ratio': decision_metrics.get('exploration_exploitation_ratio', 0.0),
                'description': self._get_exploration_exploitation_description(
                    decision_metrics.get('exploration_exploitation_ratio', 0.0)
                )
            },
            'decision_entropy': {
                'value': decision_metrics.get('decision_entropy', 0.0),
                'description': self._get_entropy_description(
                    decision_metrics.get('decision_entropy', 0.0)
                )
            },
            'value_confidence': {
                'value': decision_metrics.get('value_confidence', 0.0),
                'description': self._get_confidence_description(
                    decision_metrics.get('value_confidence', 0.0)
                )
            }
        }

        # 如果有搜索深度分布信息，添加到格式化指标中
        if 'search_depth_distribution' in decision_metrics:
            depth_dist = decision_metrics.get('search_depth_distribution', {})
            formatted_metrics['search_depth'] = {
                'min_depth': depth_dist.get('min_depth', 0),
                'max_depth': depth_dist.get('max_depth', 0),
                'avg_depth': depth_dist.get('avg_depth', 0.0),
                'median_depth': depth_dist.get('median_depth', 0),
                'description': self._get_depth_distribution_description(depth_dist)
            }

        return formatted_metrics

    def _format_tree_statistics(self, tree_stats: Dict[str, Any]) -> Dict[str, Any]:
        """
        格式化树统计信息

        Args:
            tree_stats: 树统计信息

        Returns:
            Dict[str, Any]: 格式化后的树统计信息
        """
        if not tree_stats:
            return {}

        return {
            'node_count': tree_stats.get('node_count', 0),
            'max_depth': tree_stats.get('max_depth', 0),
            'avg_depth': tree_stats.get('avg_depth', 0.0),
            'leaf_node_percentage': tree_stats.get('leaf_node_percentage', 0.0),
            'branching_factor': tree_stats.get('avg_branching_factor', 0.0),
            'value_distribution': tree_stats.get('value_distribution', {})
        }

    def _generate_decision_metrics_chart(self, decision_metrics: Dict[str, Any]) -> str:
        """
        生成决策指标图表

        Args:
            decision_metrics (Dict[str, Any]): 决策指标数据

        Returns:
            str: Base64编码的图表
        """
        # 如果没有决策指标数据，返回空字符串
        if not decision_metrics:
            return ""

        # 创建图表
        fig, ax = plt.subplots(figsize=(10, 6))

        # 提取指标
        metrics = {
            '探索/利用比': decision_metrics.get('exploration_exploitation_ratio', 0.0),
            '决策熵': decision_metrics.get('decision_entropy', 0.0),
            '值置信度': decision_metrics.get('value_confidence', 0.0)
        }

        # 绘制条形图
        bars = ax.bar(metrics.keys(), metrics.values())

        # 设置颜色
        for i, bar in enumerate(bars):
            bar.set_color(plt.cm.viridis(i / len(metrics)))

        # 添加数值标签
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{height:.2f}', ha='center', va='bottom')

        # 设置标题和标签
        ax.set_title("决策指标")
        ax.set_ylim(0, 1.1)

        # 添加网格线
        ax.grid(True, linestyle='--', alpha=0.7)

        # 调整布局
        plt.tight_layout()

        # 将图表转换为Base64编码的字符串
        return self._fig_to_base64(fig)

    def _generate_value_distribution_chart(self, top_actions: List[Dict[str, Any]]) -> str:
        """
        生成值分布图表

        Args:
            top_actions (List[Dict[str, Any]]): 顶级动作列表

        Returns:
            str: Base64编码的图表
        """
        # 如果没有顶级动作数据，返回空字符串
        if not top_actions:
            return ""

        # 创建图表
        fig, ax = plt.subplots(figsize=(10, 6))

        # 提取动作和值
        actions = [str(action.get('action', -1)) for action in top_actions]
        values = [action.get('value', 0.0) for action in top_actions]

        # 绘制条形图
        bars = ax.bar(actions, values)

        # 设置颜色（基于值）
        for i, bar in enumerate(bars):
            bar.set_color(plt.cm.RdYlGn((values[i] + 1) / 2))  # 将[-1, 1]映射到[0, 1]

        # 添加数值标签
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01 if height >= 0 else height - 0.05,
                    f'{height:.2f}', ha='center', va='bottom' if height >= 0 else 'top')

        # 设置标题和标签
        ax.set_title("顶级动作值分布")
        ax.set_xlabel("动作")
        ax.set_ylabel("值估计")

        # 添加水平线表示零点
        ax.axhline(y=0, color='k', linestyle='-', alpha=0.3)

        # 添加网格线
        ax.grid(True, linestyle='--', alpha=0.7)

        # 调整布局
        plt.tight_layout()

        # 将图表转换为Base64编码的字符串
        return self._fig_to_base64(fig)

    def _generate_prediction_summary(self, network_output: Dict[str, Any]) -> str:
        """
        生成预测摘要

        Args:
            network_output (Dict[str, Any]): 网络输出

        Returns:
            str: 预测摘要
        """
        # 获取值估计
        value = network_output.get('value', 0.0)

        # 获取顶级动作
        top_actions = network_output.get('top_actions', [])

        if not top_actions:
            return f"神经网络预测当前状态的值为{value:.3f}。"

        # 获取最佳动作
        best_action = top_actions[0]
        action_id = best_action.get('action', -1)

        # 获取概率或Q值
        if 'probability' in best_action:
            prob = best_action.get('probability', 0.0)
            summary = f"神经网络预测最佳动作为{action_id}，概率为{prob:.3f}，状态值为{value:.3f}。"
        elif 'q_value' in best_action:
            q_value = best_action.get('q_value', 0.0)
            summary = f"神经网络预测最佳动作为{action_id}，Q值为{q_value:.3f}，状态值为{value:.3f}。"
        else:
            summary = f"神经网络预测最佳动作为{action_id}，状态值为{value:.3f}。"

        return summary

    def _format_network_top_actions(self, top_actions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        格式化网络顶级动作

        Args:
            top_actions (List[Dict[str, Any]]): 顶级动作列表

        Returns:
            List[Dict[str, Any]]: 格式化后的顶级动作列表
        """
        formatted_actions = []

        for action_info in top_actions:
            formatted_action = {
                'action': action_info.get('action', -1)
            }

            # 添加概率或Q值
            if 'probability' in action_info:
                formatted_action['probability'] = f"{action_info.get('probability', 0.0):.3f}"
            elif 'q_value' in action_info:
                formatted_action['q_value'] = f"{action_info.get('q_value', 0.0):.3f}"

            formatted_actions.append(formatted_action)

        return formatted_actions

    def _get_exploration_exploitation_description(self, ratio: float) -> str:
        """
        获取探索与利用比率的描述

        Args:
            ratio: 探索与利用比率

        Returns:
            str: 描述文本
        """
        if ratio > 0.8:
            return "搜索非常注重探索，广泛考虑了多种可能性。"
        elif ratio > 0.6:
            return "搜索较为平衡，但偏向探索多种可能性。"
        elif ratio > 0.4:
            return "搜索在探索与利用之间取得了良好的平衡。"
        elif ratio > 0.2:
            return "搜索较为平衡，但偏向利用已知的有利路径。"
        else:
            return "搜索非常注重利用，集中于少数几个有利路径。"

    def _get_entropy_description(self, entropy: float) -> str:
        """
        获取决策熵的描述

        Args:
            entropy: 决策熵

        Returns:
            str: 描述文本
        """
        if entropy > 0.8:
            return "决策高度不确定，多个动作具有相似的评估值。"
        elif entropy > 0.6:
            return "决策存在较大不确定性，有多个可行的选择。"
        elif entropy > 0.4:
            return "决策中等程度的不确定性，有几个合理的选择。"
        elif entropy > 0.2:
            return "决策相对确定，但仍有少数替代选择。"
        else:
            return "决策高度确定，一个动作明显优于其他选择。"

    def _get_confidence_description(self, confidence: float) -> str:
        """
        获取值置信度的描述

        Args:
            confidence: 值置信度

        Returns:
            str: 描述文本
        """
        if confidence > 0.8:
            return "AI对其评估值有很高的置信度，预期结果相当可靠。"
        elif confidence > 0.6:
            return "AI对其评估值有较高的置信度，预期结果比较可靠。"
        elif confidence > 0.4:
            return "AI对其评估值有中等置信度，预期结果存在一定不确定性。"
        elif confidence > 0.2:
            return "AI对其评估值置信度较低，预期结果存在较大不确定性。"
        else:
            return "AI对其评估值置信度很低，预期结果高度不确定。"

    def _get_depth_distribution_description(self, depth_dist: Dict[str, Any]) -> str:
        """
        获取深度分布的描述

        Args:
            depth_dist: 深度分布数据

        Returns:
            str: 描述文本
        """
        min_depth = depth_dist.get('min_depth', 0)
        max_depth = depth_dist.get('max_depth', 0)
        avg_depth = depth_dist.get('avg_depth', 0.0)

        if max_depth - min_depth < 2:
            return f"搜索深度非常均匀，集中在{avg_depth:.1f}层左右。"
        elif max_depth > avg_depth * 2:
            return f"搜索深度分布不均，平均深度为{avg_depth:.1f}层，但在某些分支达到了{max_depth}层。"
        else:
            return f"搜索深度分布较为均匀，平均深度为{avg_depth:.1f}层，范围在{min_depth}到{max_depth}层之间。"

    def _format_act_info(self, act_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        格式化自适应计算时间信息

        Args:
            act_info: 自适应计算时间信息

        Returns:
            Dict[str, Any]: 格式化后的自适应计算时间信息
        """
        if not act_info:
            return {}

        return {
            'early_stopping': act_info.get('early_stopping', False),
            'stopping_criterion': act_info.get('stopping_criterion', ''),
            'computation_saved': act_info.get('computation_saved', 0.0),
            'confidence_threshold': act_info.get('confidence_threshold', 0.0),
            'description': self._get_act_description(act_info)
        }

    def _get_act_description(self, act_info: Dict[str, Any]) -> str:
        """
        获取自适应计算时间的描述

        Args:
            act_info: 自适应计算时间信息

        Returns:
            str: 描述文本
        """
        early_stopping = act_info.get('early_stopping', False)
        computation_saved = act_info.get('computation_saved', 0.0)

        if not early_stopping:
            return "搜索使用了全部计算预算，没有提前停止。"

        criterion = act_info.get('stopping_criterion', '')
        if criterion == 'confidence':
            return f"搜索提前停止，节省了{computation_saved:.1%}的计算量，因为已达到足够的决策置信度。"
        elif criterion == 'value_stability':
            return f"搜索提前停止，节省了{computation_saved:.1%}的计算量，因为值估计已经稳定。"
        elif criterion == 'policy_stability':
            return f"搜索提前停止，节省了{computation_saved:.1%}的计算量，因为策略分布已经稳定。"
        else:
            return f"搜索提前停止，节省了{computation_saved:.1%}的计算量。"

    def _format_risk_info(self, risk_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        格式化风险敏感决策信息

        Args:
            risk_info: 风险敏感决策信息

        Returns:
            Dict[str, Any]: 格式化后的风险敏感决策信息
        """
        if not risk_info:
            return {}

        return {
            'risk_sensitivity': risk_info.get('risk_sensitivity', 0.0),
            'risk_metric': risk_info.get('risk_metric', 'cvar'),
            'worst_case_value': risk_info.get('worst_case_value', 0.0),
            'best_case_value': risk_info.get('best_case_value', 0.0),
            'description': self._get_risk_description(risk_info)
        }

    def _get_risk_description(self, risk_info: Dict[str, Any]) -> str:
        """
        获取风险敏感决策的描述

        Args:
            risk_info: 风险敏感决策信息

        Returns:
            str: 描述文本
        """
        sensitivity = risk_info.get('risk_sensitivity', 0.0)

        if sensitivity > 0.8:
            return "AI采取了高度风险规避的策略，优先考虑最坏情况下的表现。"
        elif sensitivity > 0.6:
            return "AI采取了较为风险规避的策略，倾向于避免潜在的大损失。"
        elif sensitivity > 0.4:
            return "AI在风险态度上保持中立，平衡考虑了收益和风险。"
        elif sensitivity > 0.2:
            return "AI采取了较为风险寻求的策略，愿意承担一定风险以获取更高收益。"
        else:
            return "AI采取了高度风险寻求的策略，优先考虑最佳情况下的表现。"

    def _format_intrinsic_motivation_info(self, intrinsic_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        格式化内在动机信息

        Args:
            intrinsic_info: 内在动机信息

        Returns:
            Dict[str, Any]: 格式化后的内在动机信息
        """
        if not intrinsic_info:
            return {}

        return {
            'curiosity_weight': intrinsic_info.get('curiosity_weight', 0.0),
            'novelty_weight': intrinsic_info.get('novelty_weight', 0.0),
            'exploration_bonus': intrinsic_info.get('exploration_bonus', 0.0),
            'description': self._get_intrinsic_motivation_description(intrinsic_info)
        }

    def _get_intrinsic_motivation_description(self, intrinsic_info: Dict[str, Any]) -> str:
        """
        获取内在动机的描述

        Args:
            intrinsic_info: 内在动机信息

        Returns:
            str: 描述文本
        """
        curiosity = intrinsic_info.get('curiosity_weight', 0.0)
        novelty = intrinsic_info.get('novelty_weight', 0.0)

        if curiosity > 0.5 and novelty > 0.5:
            return "AI表现出强烈的好奇心和新奇性偏好，积极探索未知状态。"
        elif curiosity > 0.5:
            return "AI表现出强烈的好奇心，倾向于探索预测误差较大的状态。"
        elif novelty > 0.5:
            return "AI表现出强烈的新奇性偏好，倾向于探索罕见或未访问的状态。"
        elif curiosity > 0.2 or novelty > 0.2:
            return "AI表现出适度的内在动机，在优化外部奖励的同时保持一定的探索性。"
        else:
            return "AI主要关注外部奖励，内在动机影响较小。"

    def _format_opponent_model_info(self, opponent_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        格式化对手模型信息

        Args:
            opponent_info: 对手模型信息

        Returns:
            Dict[str, Any]: 格式化后的对手模型信息
        """
        if not opponent_info:
            return {}

        return {
            'model_confidence': opponent_info.get('model_confidence', 0.0),
            'predicted_strategy': opponent_info.get('predicted_strategy', ''),
            'adaptation_level': opponent_info.get('adaptation_level', 0.0),
            'description': self._get_opponent_model_description(opponent_info)
        }

    def _get_opponent_model_description(self, opponent_info: Dict[str, Any]) -> str:
        """
        获取对手模型的描述

        Args:
            opponent_info: 对手模型信息

        Returns:
            str: 描述文本
        """
        confidence = opponent_info.get('model_confidence', 0.0)
        adaptation = opponent_info.get('adaptation_level', 0.0)

        if confidence > 0.8:
            if adaptation > 0.8:
                return "AI对对手策略有很高的把握，并且高度适应了对手的行为模式。"
            elif adaptation > 0.4:
                return "AI对对手策略有很高的把握，并且适度调整了自身策略以应对。"
            else:
                return "AI对对手策略有很高的把握，但没有显著调整自身策略。"
        elif confidence > 0.4:
            if adaptation > 0.8:
                return "AI对对手策略有一定把握，并且高度适应了对手的行为模式。"
            elif adaptation > 0.4:
                return "AI对对手策略有一定把握，并且适度调整了自身策略以应对。"
            else:
                return "AI对对手策略有一定把握，但没有显著调整自身策略。"
        else:
            if adaptation > 0.4:
                return "AI对对手策略把握不足，但仍尝试适应对手的行为模式。"
            else:
                return "AI对对手策略把握不足，主要依靠自身策略进行决策。"