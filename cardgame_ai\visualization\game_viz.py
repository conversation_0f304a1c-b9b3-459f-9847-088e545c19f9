"""
游戏可视化模块

实现游戏过程和决策的可视化，包括游戏状态、决策过程和动作分布。
"""
import os
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, Any, List, Tuple, Optional, Union
from matplotlib.figure import Figure
from matplotlib.axes import Axes
import matplotlib.animation as animation


class GameVisualizer:
    """
    游戏可视化器
    
    可视化游戏过程和决策，包括游戏状态、决策过程和动作分布。
    """
    
    def __init__(self, save_dir: str = 'visualizations/games'):
        """
        初始化游戏可视化器
        
        Args:
            save_dir (str, optional): 可视化结果保存目录. Defaults to 'visualizations/games'.
        """
        self.save_dir = save_dir
        os.makedirs(save_dir, exist_ok=True)
        
        # 设置可视化样式
        sns.set_theme(style="whitegrid")
        plt.rcParams.update({
            'font.size': 12,
            'axes.titlesize': 14,
            'axes.labelsize': 12,
            'xtick.labelsize': 10,
            'ytick.labelsize': 10,
            'legend.fontsize': 10,
            'figure.titlesize': 16
        })
    
    def visualize_game_record(self, game_record: Dict[str, Any], figsize: Tuple[int, int] = (12, 8),
                            save_as: str = None) -> Figure:
        """
        可视化游戏记录
        
        Args:
            game_record (Dict[str, Any]): 游戏记录
            figsize (Tuple[int, int], optional): 图形大小. Defaults to (12, 8).
            save_as (str, optional): 保存文件名. Defaults to None.
            
        Returns:
            Figure: matplotlib图形对象
        """
        # 检查游戏记录是否包含必要的字段
        if 'actions' not in game_record:
            print("警告: 游戏记录中没有找到actions字段")
            return None
        
        # 提取游戏信息
        actions = game_record['actions']
        winner = game_record.get('winner', 'Unknown')
        game_length = len(actions)
        
        # 创建图形
        fig, axes = plt.subplots(2, 2, figsize=figsize)
        
        # 玩家动作分布
        player_actions = {}
        for action_entry in actions:
            player = action_entry.get('player', -1)
            action = action_entry.get('action', None)
            
            if player not in player_actions:
                player_actions[player] = []
            
            if action is not None:
                player_actions[player].append(action)
        
        # 绘制动作频率（每个玩家）
        ax = axes[0, 0]
        for player, player_action_list in player_actions.items():
            if player >= 0:
                # 统计动作频率
                action_counts = {}
                for action in player_action_list:
                    action_str = str(action)
                    if action_str not in action_counts:
                        action_counts[action_str] = 0
                    action_counts[action_str] += 1
                
                # 只保留前10个最常用的动作
                if len(action_counts) > 10:
                    sorted_actions = sorted(action_counts.items(), key=lambda x: x[1], reverse=True)[:10]
                    action_counts = dict(sorted_actions)
                
                # 绘制柱状图
                x = list(range(len(action_counts)))
                ax.bar([p + 0.25 * player for p in x], list(action_counts.values()), 
                      width=0.2, label=f'玩家 {player}')
                
                # 设置x轴标签
                if player == 0:  # 只在第一个玩家时设置
                    ax.set_xticks(x)
                    ax.set_xticklabels(list(action_counts.keys()), rotation=45, ha='right')
        
        ax.set_title('玩家动作频率')
        ax.set_xlabel('动作')
        ax.set_ylabel('频率')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 绘制动作时序（按时间顺序）
        ax = axes[0, 1]
        for player, player_action_list in player_actions.items():
            if player >= 0 and player_action_list:
                # 获取动作索引（用于Y轴）
                time_steps = list(range(len(player_action_list)))
                
                # 绘制散点图
                ax.plot(time_steps, player_action_list, 'o-', label=f'玩家 {player}', alpha=0.7)
        
        ax.set_title('动作时序')
        ax.set_xlabel('时间步')
        ax.set_ylabel('动作')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 绘制决策时间分布（如果有）
        ax = axes[1, 0]
        decision_times = {}
        
        for action_entry in actions:
            player = action_entry.get('player', -1)
            decision_time = action_entry.get('decision_time', None)
            
            if player not in decision_times:
                decision_times[player] = []
            
            if decision_time is not None:
                decision_times[player].append(decision_time)
        
        if any(times for times in decision_times.values()):
            for player, times in decision_times.items():
                if player >= 0 and times:
                    sns.kdeplot(times, ax=ax, label=f'玩家 {player}')
            
            ax.set_title('决策时间分布')
            ax.set_xlabel('决策时间 (秒)')
            ax.set_ylabel('密度')
            ax.legend()
            ax.grid(True, alpha=0.3)
        else:
            ax.set_visible(False)
        
        # 绘制游戏信息摘要
        ax = axes[1, 1]
        ax.axis('off')
        info_text = (
            f"游戏摘要\n"
            f"--------------\n"
            f"胜利者: 玩家 {winner}\n"
            f"游戏长度: {game_length} 步\n"
        )
        
        # 添加玩家平均决策时间（如果有）
        for player, times in decision_times.items():
            if player >= 0 and times:
                info_text += f"玩家 {player} 平均决策时间: {np.mean(times):.4f} 秒\n"
        
        # 添加玩家动作数量
        for player, actions in player_actions.items():
            if player >= 0:
                info_text += f"玩家 {player} 动作数量: {len(actions)}\n"
        
        ax.text(0.05, 0.95, info_text, transform=ax.transAxes, fontsize=12,
              verticalalignment='top', bbox=dict(boxstyle='round', alpha=0.1))
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图形
        if save_as:
            save_path = os.path.join(self.save_dir, save_as)
            plt.savefig(save_path)
            print(f"游戏记录可视化已保存至 {save_path}")
        
        return fig
    
    def visualize_decision_process(self, decision_data: Dict[str, Any], figsize: Tuple[int, int] = (10, 6),
                                 save_as: str = None) -> Figure:
        """
        可视化决策过程
        
        Args:
            decision_data (Dict[str, Any]): 决策数据
            figsize (Tuple[int, int], optional): 图形大小. Defaults to (10, 6).
            save_as (str, optional): 保存文件名. Defaults to None.
            
        Returns:
            Figure: matplotlib图形对象
        """
        # 检查决策数据是否包含必要的字段
        if 'action_probs' not in decision_data:
            print("警告: 决策数据中没有找到action_probs字段")
            return None
        
        # 提取决策信息
        action_probs = decision_data['action_probs']
        selected_action = decision_data.get('selected_action', None)
        
        # 创建图形
        fig, ax = plt.subplots(figsize=figsize)
        
        # 对动作概率排序（降序）
        sorted_actions = sorted(action_probs.items(), key=lambda x: x[1], reverse=True)
        
        # 只保留概率最高的前10个动作
        if len(sorted_actions) > 10:
            sorted_actions = sorted_actions[:10]
        
        actions = [str(a[0]) for a in sorted_actions]
        probs = [a[1] for a in sorted_actions]
        
        # 绘制柱状图
        bars = ax.bar(actions, probs, alpha=0.7)
        
        # 如果有选定的动作，标记出来
        if selected_action is not None:
            selected_action_str = str(selected_action)
            for i, action in enumerate(actions):
                if action == selected_action_str:
                    bars[i].set_color('red')
                    bars[i].set_alpha(1.0)
        
        ax.set_title('动作概率分布')
        ax.set_xlabel('动作')
        ax.set_ylabel('概率')
        plt.setp(ax.get_xticklabels(), rotation=45, ha='right')
        ax.grid(True, alpha=0.3)
        
        # 添加阈值线（如果有）
        if 'temperature' in decision_data:
            temperature = decision_data['temperature']
            ax.axhline(y=1.0/len(action_probs) * temperature, 
                     color='g', linestyle='--', alpha=0.7,
                     label=f'温度阈值 (T={temperature:.2f})')
            ax.legend()
        
        # 添加信息文本
        info_text = ""
        
        if 'decision_time' in decision_data:
            info_text += f"决策时间: {decision_data['decision_time']:.4f} 秒\n"
        
        if 'player' in decision_data:
            info_text += f"玩家: {decision_data['player']}\n"
        
        if selected_action is not None:
            info_text += f"选定动作: {selected_action}\n"
            if selected_action_str in action_probs:
                info_text += f"选定动作概率: {action_probs[selected_action_str]:.4f}\n"
        
        if info_text:
            ax.text(0.02, 0.98, info_text, transform=ax.transAxes, fontsize=10,
                  verticalalignment='top', bbox=dict(boxstyle='round', alpha=0.1))
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图形
        if save_as:
            save_path = os.path.join(self.save_dir, save_as)
            plt.savefig(save_path)
            print(f"决策过程可视化已保存至 {save_path}")
        
        return fig
    
    def animate_game(self, game_states: List[Dict[str, Any]], render_func: callable, 
                   interval: int = 500, figsize: Tuple[int, int] = (8, 8),
                   save_as: str = None) -> Figure:
        """
        创建游戏动画
        
        Args:
            game_states (List[Dict[str, Any]]): 游戏状态列表
            render_func (callable): 渲染函数，接受游戏状态和axes参数，返回绘图对象
            interval (int, optional): 帧间隔（毫秒）. Defaults to 500.
            figsize (Tuple[int, int], optional): 图形大小. Defaults to (8, 8).
            save_as (str, optional): 保存文件名. Defaults to None.
            
        Returns:
            Figure: matplotlib图形对象
        """
        # 创建图形和轴
        fig, ax = plt.subplots(figsize=figsize)
        
        # 初始化函数
        def init():
            ax.clear()
            return render_func(game_states[0], ax)
        
        # 更新函数
        def update(frame):
            ax.clear()
            return render_func(game_states[frame], ax)
        
        # 创建动画
        ani = animation.FuncAnimation(fig, update, frames=len(game_states),
                                    init_func=init, blit=True, interval=interval)
        
        # 保存动画
        if save_as:
            if not save_as.endswith('.gif') and not save_as.endswith('.mp4'):
                save_as += '.gif'
            
            save_path = os.path.join(self.save_dir, save_as)
            
            if save_as.endswith('.gif'):
                ani.save(save_path, writer='pillow', fps=1000/interval)
            else:
                ani.save(save_path, writer='ffmpeg', fps=1000/interval)
            
            print(f"游戏动画已保存至 {save_path}")
        
        # 返回图形和动画对象
        return fig, ani
    
    def visualize_game_states(self, game_states: List[Dict[str, Any]], render_func: callable,
                            num_states: int = 4, figsize: Tuple[int, int] = (15, 10),
                            save_as: str = None) -> Figure:
        """
        可视化游戏状态序列
        
        Args:
            game_states (List[Dict[str, Any]]): 游戏状态列表
            render_func (callable): 渲染函数，接受游戏状态和axes参数
            num_states (int, optional): 要显示的状态数量. Defaults to 4.
            figsize (Tuple[int, int], optional): 图形大小. Defaults to (15, 10).
            save_as (str, optional): 保存文件名. Defaults to None.
            
        Returns:
            Figure: matplotlib图形对象
        """
        if not game_states:
            print("警告: 游戏状态列表为空")
            return None
        
        # 选择要显示的状态
        if len(game_states) <= num_states:
            selected_states = game_states
        else:
            # 均匀选择状态
            indices = np.linspace(0, len(game_states) - 1, num_states, dtype=int)
            selected_states = [game_states[i] for i in indices]
        
        # 计算行列数
        nrows = int(np.ceil(np.sqrt(len(selected_states))))
        ncols = int(np.ceil(len(selected_states) / nrows))
        
        # 创建图形
        fig, axes = plt.subplots(nrows, ncols, figsize=figsize)
        
        if len(selected_states) == 1:
            axes = np.array([axes])
        
        axes = axes.flatten()
        
        # 绘制每个状态
        for i, state in enumerate(selected_states):
            if i < len(axes):
                render_func(state, axes[i])
                
                # 添加状态索引
                if len(game_states) > num_states:
                    state_idx = indices[i]
                else:
                    state_idx = i
                
                axes[i].set_title(f'状态 {state_idx}')
        
        # 隐藏未使用的子图
        for i in range(len(selected_states), len(axes)):
            axes[i].set_visible(False)
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图形
        if save_as:
            save_path = os.path.join(self.save_dir, save_as)
            plt.savefig(save_path)
            print(f"游戏状态可视化已保存至 {save_path}")
        
        return fig
    
    def visualize_value_heatmap(self, state_values: Dict[str, float], state_names: List[str] = None,
                              figsize: Tuple[int, int] = (12, 8), save_as: str = None) -> Figure:
        """
        可视化状态价值热力图
        
        Args:
            state_values (Dict[str, float]): 状态价值字典
            state_names (List[str], optional): 状态名称列表. Defaults to None.
            figsize (Tuple[int, int], optional): 图形大小. Defaults to (12, 8).
            save_as (str, optional): 保存文件名. Defaults to None.
            
        Returns:
            Figure: matplotlib图形对象
        """
        # 创建图形
        fig, ax = plt.subplots(figsize=figsize)
        
        # 如果没有提供状态名称，使用字典的键
        if not state_names:
            state_names = list(state_values.keys())
        
        # 从状态名称中获取对应的值
        values = [state_values.get(name, 0) for name in state_names]
        
        # 如果状态太多，只保留前20个
        if len(state_names) > 20:
            # 按价值排序
            sorted_indices = np.argsort(values)[::-1]  # 降序
            state_names = [state_names[i] for i in sorted_indices[:20]]
            values = [values[i] for i in sorted_indices[:20]]
        
        # 创建热力图数据
        # 将值归一化到[-1, 1]范围
        max_abs_value = max(abs(min(values)), abs(max(values)))
        if max_abs_value > 0:
            normalized_values = np.array(values) / max_abs_value
        else:
            normalized_values = np.array(values)
        
        # 重塑为2D数组
        if len(normalized_values) == 1:
            # 特殊情况：只有一个状态
            heatmap_data = np.array([[normalized_values[0]]])
        else:
            # 计算网格大小
            grid_size = int(np.ceil(np.sqrt(len(normalized_values))))
            
            # 填充网格
            heatmap_data = np.zeros((grid_size, grid_size))
            for i, value in enumerate(normalized_values):
                row = i // grid_size
                col = i % grid_size
                heatmap_data[row, col] = value
        
        # 创建热力图
        cmap = plt.cm.RdBu  # 红蓝配色方案，负值为红色，正值为蓝色
        im = ax.imshow(heatmap_data, cmap=cmap, vmin=-1, vmax=1)
        
        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax)
        cbar.set_label('归一化状态价值')
        
        # 添加状态名称
        for i, name in enumerate(state_names):
            row = i // heatmap_data.shape[1]
            col = i % heatmap_data.shape[1]
            
            # 显示名称（如果太长则截断）
            if len(name) > 10:
                display_name = name[:7] + '...'
            else:
                display_name = name
            
            # 显示名称和值
            text = f"{display_name}\n{values[i]:.2f}"
            
            # 根据值的大小选择文字颜色
            if abs(heatmap_data[row, col]) > 0.5:
                text_color = 'white'
            else:
                text_color = 'black'
            
            ax.text(col, row, text, ha='center', va='center',
                  color=text_color, fontsize=9)
        
        # 隐藏坐标轴刻度
        ax.set_xticks([])
        ax.set_yticks([])
        
        # 设置标题
        ax.set_title('状态价值热力图')
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图形
        if save_as:
            save_path = os.path.join(self.save_dir, save_as)
            plt.savefig(save_path)
            print(f"状态价值热力图已保存至 {save_path}")
        
        return fig 