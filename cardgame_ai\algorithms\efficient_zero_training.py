"""
EfficientZero训练函数模块

该模块实现了EfficientZero算法的训练主函数，包括：
- train_efficient_zero: 主训练函数
- 训练循环管理
- 数据收集和模型更新
- 性能监控和日志记录
- 模型保存和加载

主要功能：
- 游戏环境交互：收集训练数据
- 模型训练：使用收集的数据训练模型
- 性能评估：定期评估模型性能
- 检查点管理：保存和恢复训练状态
"""

import os
import torch
import numpy as np
import logging
import time
import glob
from typing import Dict, Any
from torch.utils.data import DataLoader

from .efficient_zero_algorithm import EfficientZero
from .efficient_zero_amp import EfficientZeroAMP
from cardgame_ai.games.doudizhu.state import GamePhase
from cardgame_ai.games.doudizhu.action import BidAction, GrabAction

# 配置日志
logger = logging.getLogger(__name__)


def train_efficient_zero(game: str, config: Dict[str, Any]) -> int:
    """
    训练EfficientZero模型的主函数
    
    该函数是EfficientZero算法的训练入口点，负责：
    1. 初始化训练环境和模型
    2. 执行训练循环
    3. 收集游戏数据
    4. 更新模型参数
    5. 保存训练结果
    
    Args:
        game (str): 游戏名称，例如"doudizhu"
        config (Dict[str, Any]): 训练配置，包含所有必要的超参数
        
    Returns:
        int: 返回状态码 (0: 成功, 非0: 错误)
    """
    try:
        # 设置日志
        logger.info(f"开始训练 {game} 游戏的 EfficientZero 模型")

        # 提取配置参数
        model_config = config.get('model', {})
        mcts_config = config.get('mcts', {})
        training_config = config.get('training', {})
        environment_config = config.get('environment', {})
        continual_learning_config = config.get('continual_learning', {})
        rlhf_config = config.get('rlhf', {})

        # 提取 num_workers 参数，优先从 training 子配置获取，其次从根配置获取，最后使用默认值 4
        # 在Windows系统上优化多进程配置以避免死锁和资源竞争
        import platform
        default_workers = 4
        if platform.system() == 'Windows':
            # Windows上减少worker数量以避免spawn上下文问题
            default_workers = min(2, default_workers)
            logger.info("检测到Windows系统，优化DataLoader多进程配置")

        num_workers = training_config.get('num_workers', config.get('num_workers', default_workers))

        # 进一步限制worker数量以避免资源竞争
        if num_workers > 8:
            logger.warning(f"num_workers={num_workers}过高，可能导致资源竞争，建议设置为<=8")

        logger.info(f"train_efficient_zero: 使用 num_workers = {num_workers} 创建 DataLoader")

        # 设备配置
        device = config.get('device', 'cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"使用设备: {device}")

        # 创建游戏环境
        if game == 'doudizhu':
            from cardgame_ai.games.doudizhu import DouDizhuEnvironment
            game_env = DouDizhuEnvironment()
        else:
            raise ValueError(f"不支持的游戏: {game}")

        # 获取状态和动作空间
        state_shape = game_env.observation_space.shape  # 观察空间的形状，例如(656,)
        action_shape = (1,)  # 动作是标量索引，所以维度是1

        # 训练参数
        num_epochs = training_config.get('epochs', 100)
        episodes_per_epoch = training_config.get('episodes_per_epoch', 10)
        updates_per_epoch = training_config.get('updates_per_epoch', 100)
        save_interval = training_config.get('save_interval', 10)
        save_dir = training_config.get('save_dir', 'models/muzero_doudizhu')

        # 创建保存目录
        os.makedirs(save_dir, exist_ok=True)

        # 创建EfficientZero模型
        use_mixed_precision = training_config.get('use_mixed_precision', True)
        
        if use_mixed_precision:
            model = EfficientZeroAMP(
                state_shape=state_shape,
                action_shape=action_shape,
                hidden_dim=model_config.get('hidden_dim', 256),
                state_dim=model_config.get('state_dim', 64),
                use_resnet=model_config.get('use_resnet', True),
                projection_dim=model_config.get('projection_dim', 128),
                prediction_dim=model_config.get('prediction_dim', 64),
                value_prefix_length=model_config.get('value_prefix_length', 5),
                use_distributional_value=model_config.get('use_distributional_value', False),
                value_support_size=model_config.get('value_support_size', 601),
                value_min=model_config.get('value_min', -300),
                value_max=model_config.get('value_max', 300),
                risk_alpha=model_config.get('risk_alpha', 0.05),
                risk_beta=model_config.get('risk_beta', 0.1),
                use_belief_state=model_config.get('use_belief_state', False),
                belief_dim=model_config.get('belief_dim', 128),
                num_simulations=mcts_config.get('num_simulations', 50),
                c_puct=mcts_config.get('c_puct', 1.25),
                dirichlet_alpha=mcts_config.get('dirichlet_alpha', 0.3),
                exploration_fraction=mcts_config.get('exploration_fraction', 0.25),
                replay_buffer_size=training_config.get('replay_buffer_size', 100000),
                batch_size=training_config.get('batch_size', 256),
                num_unroll_steps=training_config.get('num_unroll_steps', 5),
                td_steps=training_config.get('td_steps', 5),
                value_loss_weight=training_config.get('value_loss_weight', 0.25),
                policy_loss_weight=training_config.get('policy_loss_weight', 1.0),
                consistency_loss_weight=training_config.get('consistency_loss_weight', 2.0),
                self_supervised_loss_weight=training_config.get('self_supervised_loss_weight', 2.0),
                use_ewc=continual_learning_config.get('use_ewc', False),
                ewc_lambda=continual_learning_config.get('ewc_lambda', 100.0),
                learning_rate=training_config.get('learning_rate', 0.001),
                weight_decay=training_config.get('weight_decay', 1e-4),
                lr_scheduler=training_config.get('lr_scheduler', 'step'),
                use_mixed_precision=use_mixed_precision,
                dynamic_loss_scaling=training_config.get('dynamic_loss_scaling', True),
                device=device
            )
        else:
            model = EfficientZero(
                state_shape=state_shape,
                action_shape=action_shape,
                hidden_dim=model_config.get('hidden_dim', 256),
                state_dim=model_config.get('state_dim', 64),
                use_resnet=model_config.get('use_resnet', True),
                projection_dim=model_config.get('projection_dim', 128),
                prediction_dim=model_config.get('prediction_dim', 64),
                value_prefix_length=model_config.get('value_prefix_length', 5),
                use_distributional_value=model_config.get('use_distributional_value', False),
                value_support_size=model_config.get('value_support_size', 601),
                value_min=model_config.get('value_min', -300),
                value_max=model_config.get('value_max', 300),
                risk_alpha=model_config.get('risk_alpha', 0.05),
                risk_beta=model_config.get('risk_beta', 0.1),
                use_belief_state=model_config.get('use_belief_state', False),
                belief_dim=model_config.get('belief_dim', 128),
                num_simulations=mcts_config.get('num_simulations', 50),
                c_puct=mcts_config.get('c_puct', 1.25),
                dirichlet_alpha=mcts_config.get('dirichlet_alpha', 0.3),
                exploration_fraction=mcts_config.get('exploration_fraction', 0.25),
                replay_buffer_size=training_config.get('replay_buffer_size', 100000),
                batch_size=training_config.get('batch_size', 256),
                num_unroll_steps=training_config.get('num_unroll_steps', 5),
                td_steps=training_config.get('td_steps', 5),
                value_loss_weight=training_config.get('value_loss_weight', 0.25),
                policy_loss_weight=training_config.get('policy_loss_weight', 1.0),
                consistency_loss_weight=training_config.get('consistency_loss_weight', 2.0),
                self_supervised_loss_weight=training_config.get('self_supervised_loss_weight', 2.0),
                use_ewc=continual_learning_config.get('use_ewc', False),
                ewc_lambda=continual_learning_config.get('ewc_lambda', 100.0),
                learning_rate=training_config.get('learning_rate', 0.001),
                weight_decay=training_config.get('weight_decay', 1e-4),
                lr_scheduler=training_config.get('lr_scheduler', 'step'),
                device=device
            )

        logger.info("EfficientZero模型创建完成")

        # 初始化EWC（如果启用）
        if continual_learning_config.get('use_ewc', False):
            try:
                # 检查回放缓冲区是否有足够的样本
                ewc_batch_size = continual_learning_config.get('ewc_batch_size', 32)
                ewc_num_batches = continual_learning_config.get('ewc_num_batches', 10)
                
                if len(model.replay_buffer) >= ewc_batch_size * ewc_num_batches:
                    # 创建数据集和加载器
                    from torch.utils.data import Dataset
                    
                    class ReplayBufferDataset(Dataset):
                        def __init__(self, replay_buffer, num_samples, batch_size):
                            self.replay_buffer = replay_buffer
                            self.num_samples = num_samples
                            self.batch_size = batch_size

                        def __len__(self):
                            return self.num_samples

                        def __getitem__(self, idx):
                            # 获取一个批次，并返回其中的状态
                            batch = self.replay_buffer.sample(self.batch_size)
                            # 注意：这里需要根据实际的批次结构提取状态
                            states = torch.stack([torch.tensor(exp.state) for exp in batch])
                            return states

                    # 创建数据集和加载器
                    ewc_dataset = ReplayBufferDataset(model.replay_buffer, ewc_num_batches, ewc_batch_size)

                    # 创建数据加载器，使用提取的 num_workers 参数
                    # 在Windows上优化multiprocessing配置
                    dataloader_kwargs = {
                        'batch_size': 1,  # 每次返回一个批次
                        'shuffle': True,
                        'num_workers': num_workers  # 使用提取的 num_workers 参数
                    }

                    # Windows系统优化：避免spawn上下文问题
                    if platform.system() == 'Windows' and num_workers > 0:
                        # 在Windows上禁用多进程以避免死锁
                        dataloader_kwargs['num_workers'] = 0
                        logger.info("Windows系统检测：禁用DataLoader多进程以避免死锁")

                    ewc_dataloader = DataLoader(ewc_dataset, **dataloader_kwargs)

                    # 初始化 EWC
                    model.initialize_ewc(dataloader=ewc_dataloader)
                    logger.info(f"EWC 初始化成功，使用 num_workers={num_workers}")
                else:
                    logger.warning(f"回放缓冲区样本数量不足，跳过 EWC 初始化")
            except Exception as e:
                logger.error(f"初始化 EWC 时出错: {e}")
                logger.exception(e)

        logger.info(f"训练设置: {num_epochs}轮，每轮{episodes_per_epoch}局游戏，每轮{updates_per_epoch}次更新")

        # 创建环境和收集经验
        logger.info("开始训练循环")

        # 定义训练阶段权重，用于计算总体进度
        episode_collection_weight = 0.4  # 游戏收集占轮次的40%
        update_weight = 0.6  # 模型更新占轮次的60%

        # 训练主循环
        for epoch in range(num_epochs):
            epoch_start_time = time.time()
            logger.info(f"开始第 {epoch+1}/{num_epochs} 轮训练")

            # 阶段1：收集游戏数据
            logger.info(f"收集游戏数据: {episodes_per_epoch} 局游戏")

            # 初始化统计信息
            episode_rewards = []
            episode_lengths = []
            landlord_info_logged_this_episode = False
            previous_game_phase_for_log = None

            for episode in range(episodes_per_epoch):
                episode_start_time = time.time()

                # 重置环境
                state = game_env.reset()
                done = False
                step = 0
                episode_reward = 0

                logger.debug(f"  开始第 {episode+1}/{episodes_per_epoch} 局游戏")

                while not done:
                    # 获取当前玩家（从状态中获取，而不是环境）
                    acting_player_id = state.current_player

                    # 使用模型选择动作
                    act_start_time = time.time()
                    act_output = model.act(
                        state,
                        explain=True
                    )
                    action_idx = act_output[0]
                    action_probs_for_log = act_output[1]
                    mcts_data_for_log = act_output[2] if len(act_output) > 2 and act_output[2] else {}
                    act_duration = time.time() - act_start_time
                    logger.debug(f"    [局 {episode+1}/{episodes_per_epoch}] 步 {step}: P{acting_player_id} model.act 耗时: {act_duration:.4f}s")

                    # 获取合法动作列表
                    legal_actions = game_env.get_legal_actions(state)

                    # 将动作索引转换为实际动作对象
                    if action_idx < len(legal_actions):
                        action = legal_actions[action_idx]
                    else:
                        # 如果动作索引超出范围，选择第一个合法动作
                        logger.warning(f"动作索引 {action_idx} 超出合法动作范围 [0, {len(legal_actions)-1}]，使用第一个合法动作")
                        action = legal_actions[0] if legal_actions else None

                    if action is None:
                        logger.error("没有可用的合法动作，跳过此步")
                        break

                    # 记录游戏阶段信息
                    if hasattr(state, 'game_phase'):
                        previous_game_phase_for_log = state.game_phase

                    # 执行动作
                    try:
                        next_state, reward, done, info = game_env.step(action)
                    except Exception as e:
                        logger.error(f"执行动作时出错 [轮次 {epoch+1}/{num_epochs}, 局 {episode+1}/{episodes_per_epoch}, 步 {step}]: {str(e)}")
                        logger.error(f"动作信息: 玩家 {acting_player_id}, 动作索引 {action_idx}, 动作 {action}")
                        break

                    # 记录地主确定信息
                    if hasattr(next_state, 'game_phase') and hasattr(next_state, 'landlord'):
                        just_transitioned_to_play = (previous_game_phase_for_log in [GamePhase.BIDDING, GamePhase.GRABBING] and
                                                    next_state.game_phase == GamePhase.PLAYING)

                        if not landlord_info_logged_this_episode and next_state.landlord is not None:
                            landlord_id_log = next_state.landlord
                            logger.info(f"  [局 {episode+1}] 地主确定: P{landlord_id_log}")
                            landlord_info_logged_this_episode = True

                    # 创建经验并添加到回放缓冲区
                    from cardgame_ai.core.base import Experience
                    experience = Experience(
                        state=state,
                        action=action,
                        reward=reward,
                        next_state=next_state,
                        done=done
                    )
                    model.replay_buffer.add(experience)

                    # 更新状态和统计信息
                    state = next_state
                    episode_reward += reward
                    step += 1

                # 记录游戏统计信息
                episode_rewards.append(episode_reward)
                episode_lengths.append(step)
                episode_duration = time.time() - episode_start_time

                logger.debug(f"  第 {episode+1} 局完成: 奖励={episode_reward:.3f}, 步数={step}, 耗时={episode_duration:.2f}s")

            # 阶段2：模型更新
            logger.info(f"模型更新: {updates_per_epoch} 次更新")

            update_losses = []
            for update in range(updates_per_epoch):
                if len(model.replay_buffer) >= model.batch_size:
                    # 从回放缓冲区采样批次数据
                    batch = model.replay_buffer.sample(model.batch_size)

                    # 训练模型
                    losses = model.train(batch)
                    update_losses.append(losses)

                    if update % 20 == 0:  # 每20次更新记录一次
                        logger.debug(f"    更新 {update+1}/{updates_per_epoch}: 总损失={losses.get('total_loss', 0):.4f}")

            # 计算平均损失
            if update_losses:
                avg_losses = {}
                for key in update_losses[0].keys():
                    avg_losses[key] = np.mean([loss[key] for loss in update_losses])
            else:
                avg_losses = {}

            # 记录轮次统计信息
            epoch_duration = time.time() - epoch_start_time
            avg_episode_reward = np.mean(episode_rewards) if episode_rewards else 0
            avg_episode_length = np.mean(episode_lengths) if episode_lengths else 0

            logger.info(f"第 {epoch+1} 轮完成:")
            logger.info(f"  平均奖励: {avg_episode_reward:.3f}")
            logger.info(f"  平均步数: {avg_episode_length:.1f}")
            logger.info(f"  回放缓冲区大小: {len(model.replay_buffer)}")
            if avg_losses:
                logger.info(f"  平均总损失: {avg_losses.get('total_loss', 0):.4f}")
            logger.info(f"  轮次耗时: {epoch_duration:.2f}s")

            # 保存模型
            if (epoch + 1) % save_interval == 0:
                save_path = os.path.join(save_dir, f'efficient_zero_epoch_{epoch+1}.pt')
                model.save(save_path)
                logger.info(f"模型已保存到: {save_path}")

        logger.info("训练完成!")
        return 0

    except Exception as e:
        logger.error(f"训练过程中出现错误: {str(e)}")
        logger.exception(e)
        return 1
