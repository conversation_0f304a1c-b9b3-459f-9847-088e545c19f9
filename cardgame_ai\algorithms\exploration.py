"""
探索策略模块

提供各种探索策略的实现，包括ε-greedy、UC<PERSON>和Thompson采样等。
"""
import random
import numpy as np
from typing import List, Dict, Any, Optional, Union, Callable
from abc import ABC, abstractmethod


class ExplorationStrategy(ABC):
    """
    探索策略接口
    
    定义探索策略的标准接口。
    """
    
    @abstractmethod
    def select_action(self, values: np.ndarray, **kwargs) -> int:
        """
        选择动作
        
        Args:
            values (np.ndarray): 动作价值
            **kwargs: 额外参数
            
        Returns:
            int: 选择的动作索引
        """
        pass
    
    @abstractmethod
    def update(self, **kwargs) -> None:
        """
        更新策略参数
        
        Args:
            **kwargs: 更新参数
        """
        pass


class EpsilonGreedy(ExplorationStrategy):
    """
    ε-greedy探索策略
    
    以1-ε的概率选择最优动作，以ε的概率随机选择动作。
    """
    
    def __init__(self, epsilon: float = 0.1, epsilon_decay: float = 0.995, epsilon_min: float = 0.01):
        """
        初始化ε-greedy策略
        
        Args:
            epsilon (float, optional): 探索概率. Defaults to 0.1.
            epsilon_decay (float, optional): ε衰减率. Defaults to 0.995.
            epsilon_min (float, optional): 最小ε值. Defaults to 0.01.
        """
        self.epsilon = epsilon
        self.epsilon_decay = epsilon_decay
        self.epsilon_min = epsilon_min
    
    def select_action(self, values: np.ndarray, **kwargs) -> int:
        """
        选择动作
        
        Args:
            values (np.ndarray): 动作价值
            **kwargs: 额外参数，包括：
                - mask (np.ndarray, optional): 动作掩码，1表示合法动作，0表示非法动作
            
        Returns:
            int: 选择的动作索引
        """
        # 获取动作掩码
        mask = kwargs.get('mask', np.ones_like(values))
        
        # 确保至少有一个合法动作
        if not np.any(mask):
            raise ValueError("没有合法动作")
        
        # 随机探索
        if random.random() < self.epsilon:
            # 只从合法动作中随机选择
            legal_actions = np.where(mask)[0]
            return np.random.choice(legal_actions)
        
        # 贪婪选择
        # 将非法动作的价值设为负无穷
        masked_values = values.copy()
        masked_values[mask == 0] = -np.inf
        
        # 选择最大价值的动作
        return np.argmax(masked_values)
    
    def update(self, **kwargs) -> None:
        """
        更新ε值
        
        Args:
            **kwargs: 更新参数，包括：
                - decay (bool, optional): 是否衰减ε. Defaults to True.
        """
        # 是否衰减ε
        decay = kwargs.get('decay', True)
        
        if decay:
            self.epsilon = max(self.epsilon_min, self.epsilon * self.epsilon_decay)
    
    def __str__(self) -> str:
        """
        转换为字符串表示
        
        Returns:
            str: 字符串表示
        """
        return f"EpsilonGreedy(epsilon={self.epsilon:.4f}, decay={self.epsilon_decay}, min={self.epsilon_min})"


class UCB(ExplorationStrategy):
    """
    UCB (Upper Confidence Bound) 探索策略
    
    基于置信区间上界选择动作，平衡探索和利用。
    """
    
    def __init__(self, c: float = 2.0):
        """
        初始化UCB策略
        
        Args:
            c (float, optional): 探索参数，控制置信区间的宽度. Defaults to 2.0.
        """
        self.c = c
        self.action_counts = None
        self.total_count = 0
    
    def select_action(self, values: np.ndarray, **kwargs) -> int:
        """
        选择动作
        
        Args:
            values (np.ndarray): 动作价值
            **kwargs: 额外参数，包括：
                - mask (np.ndarray, optional): 动作掩码，1表示合法动作，0表示非法动作
            
        Returns:
            int: 选择的动作索引
        """
        # 获取动作掩码
        mask = kwargs.get('mask', np.ones_like(values))
        
        # 确保至少有一个合法动作
        if not np.any(mask):
            raise ValueError("没有合法动作")
        
        # 初始化动作计数
        if self.action_counts is None:
            self.action_counts = np.zeros_like(values)
        
        # 对于未尝试过的动作，优先选择
        if np.any((self.action_counts == 0) & (mask == 1)):
            untried_actions = np.where((self.action_counts == 0) & (mask == 1))[0]
            return np.random.choice(untried_actions)
        
        # 计算UCB值
        ucb_values = values + self.c * np.sqrt(np.log(self.total_count + 1) / (self.action_counts + 1e-6))
        
        # 将非法动作的UCB值设为负无穷
        ucb_values[mask == 0] = -np.inf
        
        # 选择最大UCB值的动作
        return np.argmax(ucb_values)
    
    def update(self, **kwargs) -> None:
        """
        更新动作计数
        
        Args:
            **kwargs: 更新参数，包括：
                - action (int): 选择的动作
        """
        # 获取选择的动作
        action = kwargs.get('action')
        
        if action is not None:
            # 更新动作计数
            self.action_counts[action] += 1
            self.total_count += 1
    
    def __str__(self) -> str:
        """
        转换为字符串表示
        
        Returns:
            str: 字符串表示
        """
        return f"UCB(c={self.c})"


class ThompsonSampling(ExplorationStrategy):
    """
    Thompson采样探索策略
    
    基于贝叶斯方法，从动作价值的后验分布中采样选择动作。
    """
    
    def __init__(self, alpha: float = 1.0, beta: float = 1.0):
        """
        初始化Thompson采样策略
        
        Args:
            alpha (float, optional): Beta分布的α参数. Defaults to 1.0.
            beta (float, optional): Beta分布的β参数. Defaults to 1.0.
        """
        self.alpha = alpha
        self.beta = beta
        self.action_alpha = None
        self.action_beta = None
    
    def select_action(self, values: np.ndarray, **kwargs) -> int:
        """
        选择动作
        
        Args:
            values (np.ndarray): 动作价值
            **kwargs: 额外参数，包括：
                - mask (np.ndarray, optional): 动作掩码，1表示合法动作，0表示非法动作
            
        Returns:
            int: 选择的动作索引
        """
        # 获取动作掩码
        mask = kwargs.get('mask', np.ones_like(values))
        
        # 确保至少有一个合法动作
        if not np.any(mask):
            raise ValueError("没有合法动作")
        
        # 初始化动作参数
        if self.action_alpha is None:
            self.action_alpha = np.ones_like(values) * self.alpha
            self.action_beta = np.ones_like(values) * self.beta
        
        # 从Beta分布中采样
        samples = np.random.beta(self.action_alpha, self.action_beta)
        
        # 将非法动作的采样值设为负无穷
        samples[mask == 0] = -np.inf
        
        # 选择最大采样值的动作
        return np.argmax(samples)
    
    def update(self, **kwargs) -> None:
        """
        更新Beta分布参数
        
        Args:
            **kwargs: 更新参数，包括：
                - action (int): 选择的动作
                - reward (float): 获得的奖励
        """
        # 获取选择的动作和奖励
        action = kwargs.get('action')
        reward = kwargs.get('reward')
        
        if action is not None and reward is not None:
            # 更新Beta分布参数
            if reward > 0:
                self.action_alpha[action] += reward
            else:
                self.action_beta[action] -= reward
    
    def __str__(self) -> str:
        """
        转换为字符串表示
        
        Returns:
            str: 字符串表示
        """
        return f"ThompsonSampling(alpha={self.alpha}, beta={self.beta})"
