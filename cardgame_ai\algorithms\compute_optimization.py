"""
计算优化模块

提供各种计算优化技术，用于提高模型训练和推理的效率。
包括梯度检查点、激活重计算、混合精度训练和分布式训练包装器。
"""

import torch
import torch.nn as nn
from torch.cuda.amp import autocast, GradScaler
import torch.distributed as dist
from typing import Dict, List, Tuple, Any, Optional, Union, Callable, Type
import functools
import time
import logging

logger = logging.getLogger(__name__)


class GradientCheckpointing:
    """
    梯度检查点技术
    
    通过在前向传播中只保存特定的激活，而在反向传播时重新计算中间激活来减少内存使用。
    牺牲一些计算时间来节省显存，适用于大型模型训练。
    """
    
    def __init__(self, checkpoint_segments: int = 2):
        """
        初始化梯度检查点
        
        Args:
            checkpoint_segments: 将模型划分为多少个检查点段
        """
        self.checkpoint_segments = checkpoint_segments
        
    def apply(self, model: nn.Module) -> nn.Module:
        """
        将梯度检查点应用到模型
        
        Args:
            model: 需要应用梯度检查点的模型
            
        Returns:
            应用了梯度检查点的模型
        """
        # 记录初始状态
        logger.info(f"应用梯度检查点，将模型划分为 {self.checkpoint_segments} 个段")
        
        # 检查模型是否支持梯度检查点
        self._validate_model(model)
        
        if hasattr(model, 'enable_checkpointing'):
            # 如果模型有内置的检查点方法，直接调用
            model.enable_checkpointing()
            logger.info("使用模型内置的梯度检查点机制")
            return model
        
        # 对不同类型的模型应用不同的检查点策略
        if hasattr(model, 'transformer') and hasattr(model.transformer, 'layers'):
            # Transformer架构模型
            self._checkpoint_transformer(model)
        elif hasattr(model, 'blocks') or hasattr(model, 'layers'):
            # 具有blocks或layers属性的模型
            self._checkpoint_blocks(model)
        else:
            # 一般模型，尝试应用torch.utils.checkpoint
            self._apply_general_checkpointing(model)
            
        return model
    
    def _validate_model(self, model: nn.Module) -> None:
        """
        验证模型是否支持梯度检查点
        
        Args:
            model: 需要验证的模型
        """
        # 检查模型是否有需要的属性
        if not any(hasattr(model, attr) for attr in ['transformer', 'blocks', 'layers', 'forward']):
            logger.warning("模型结构可能不支持标准梯度检查点，将尝试一般方法")
    
    def _checkpoint_transformer(self, model: nn.Module) -> None:
        """
        对Transformer架构模型应用梯度检查点
        
        Args:
            model: Transformer模型
        """
        from torch.utils.checkpoint import checkpoint
        
        # 获取transformer层
        layers = model.transformer.layers
        n_layers = len(layers)
        
        # 计算每个检查点段包含的层数
        layers_per_segment = max(1, n_layers // self.checkpoint_segments)
        
        # 保存原始前向传播函数
        orig_forward_fns = {}
        
        # 为每层应用检查点
        for i, layer in enumerate(layers):
            orig_forward_fns[i] = layer.forward
            
            # 使用functools.partial创建新的前向传播函数
            layer.forward = functools.partial(
                checkpoint,
                layer.forward,
                use_reentrant=False
            )
            
        logger.info(f"对Transformer模型的 {n_layers} 层应用了梯度检查点")
    
    def _checkpoint_blocks(self, model: nn.Module) -> None:
        """
        对具有blocks或layers属性的模型应用梯度检查点
        
        Args:
            model: 模型
        """
        from torch.utils.checkpoint import checkpoint
        
        # 确定模型的块属性名称
        block_attr = 'blocks' if hasattr(model, 'blocks') else 'layers'
        blocks = getattr(model, block_attr)
        
        if isinstance(blocks, nn.ModuleList):
            n_blocks = len(blocks)
            
            # 计算每个检查点段包含的块数
            blocks_per_segment = max(1, n_blocks // self.checkpoint_segments)
            
            # 保存原始前向传播函数
            orig_forward_fns = {}
            
            # 为每个块应用检查点
            for i, block in enumerate(blocks):
                orig_forward_fns[i] = block.forward
                
                # 使用functools.partial创建新的前向传播函数
                block.forward = functools.partial(
                    checkpoint,
                    block.forward,
                    use_reentrant=False
                )
                
            logger.info(f"对模型的 {n_blocks} 个 {block_attr} 应用了梯度检查点")
        else:
            logger.warning(f"模型的 {block_attr} 不是 ModuleList，无法应用标准梯度检查点")
            self._apply_general_checkpointing(model)
    
    def _apply_general_checkpointing(self, model: nn.Module) -> None:
        """
        对一般模型应用梯度检查点
        
        Args:
            model: 模型
        """
        from torch.utils.checkpoint import checkpoint
        
        # 保存原始前向传播函数
        orig_forward = model.forward
        
        # 定义新的前向传播函数
        def checkpointed_forward(*args, **kwargs):
            return checkpoint(orig_forward, *args, use_reentrant=False, **kwargs)
        
        # 替换模型的前向传播函数
        model.forward = checkpointed_forward
        
        logger.info("对整个模型应用了一般梯度检查点")


class ActivationRecomputation:
    """
    激活重计算技术
    
    类似于梯度检查点，但更专注于激活值的管理。
    在内存受限的情况下，通过重新计算激活值而不是存储它们来节省内存。
    """
    
    def __init__(self, recompute_ratio: float = 0.5):
        """
        初始化激活重计算
        
        Args:
            recompute_ratio: 需要重计算的激活比例，范围[0, 1]
        """
        self.recompute_ratio = max(0.0, min(1.0, recompute_ratio))
        
    def apply(self, model: nn.Module) -> nn.Module:
        """
        将激活重计算应用到模型
        
        Args:
            model: 需要应用激活重计算的模型
            
        Returns:
            应用了激活重计算的模型
        """
        # 记录初始状态
        logger.info(f"应用激活重计算，重计算比例: {self.recompute_ratio}")
        
        # 如果模型已经有激活重计算方法，直接调用
        if hasattr(model, 'enable_activation_recomputation'):
            model.enable_activation_recomputation(self.recompute_ratio)
            logger.info("使用模型内置的激活重计算机制")
            return model
        
        # 在这里实现具体的激活重计算逻辑
        # 由于激活重计算通常与特定模型架构紧密相关，这里提供一个简化实现
        
        # 注册钩子来管理存储激活
        self._register_hooks(model)
        
        return model
    
    def _register_hooks(self, model: nn.Module) -> None:
        """
        注册前向和后向钩子以管理激活重计算
        
        Args:
            model: 模型
        """
        # 对模型中的每个模块应用钩子
        for name, module in model.named_modules():
            # 只对特定类型的层应用激活重计算
            if isinstance(module, (nn.ReLU, nn.LeakyReLU, nn.GELU, nn.SiLU)):
                # 随机决定是否对此模块应用重计算
                if torch.rand(1).item() < self.recompute_ratio:
                    # 保存原始前向传播函数
                    orig_forward = module.forward
                    
                    # 定义新的前向传播函数，不保存中间激活
                    def recompute_forward(self, x):
                        with torch.no_grad():
                            return orig_forward(x)
                    
                    # 替换模块的前向传播函数
                    module.forward = functools.partial(recompute_forward, module)
                    
                    logger.debug(f"对模块 {name} 应用了激活重计算")


class MixedPrecisionTraining:
    """
    混合精度训练
    
    使用较低精度（如float16）进行前向和反向传播计算，同时保持权重更新在float32，提高训练效率。
    """
    
    def __init__(self, enabled: bool = True, dtype: torch.dtype = torch.float16, init_scale: float = 2**16):
        """
        初始化混合精度训练
        
        Args:
            enabled: 是否启用混合精度训练
            dtype: 混合精度训练使用的数据类型，通常为torch.float16或torch.bfloat16
            init_scale: 初始梯度缩放值
        """
        self.enabled = enabled and torch.cuda.is_available()
        self.dtype = dtype
        self.scaler = GradScaler(init_scale=init_scale) if self.enabled else None
        
    def apply_to_optimizer(self, optimizer: torch.optim.Optimizer) -> torch.optim.Optimizer:
        """
        将混合精度应用到优化器
        
        Args:
            optimizer: 优化器
            
        Returns:
            原始优化器（用于跟踪）
        """
        # 混合精度训练不直接修改优化器，而是通过scaler在step时使用
        logger.info(f"准备混合精度训练，使用 {self.dtype} 数据类型")
        return optimizer
    
    def step(self, optimizer: torch.optim.Optimizer, loss: torch.Tensor, model: Optional[nn.Module] = None) -> None:
        """
        执行优化器步骤
        
        Args:
            optimizer: 优化器
            loss: 损失张量
            model: 可选的模型，用于检查梯度
        """
        if self.enabled and self.scaler is not None:
            # 使用scaler缩放损失并执行反向传播
            self.scaler.scale(loss).backward()
            
            # 检查梯度是否存在NaN值（可选）
            if model is not None:
                self._check_gradients(model)
            
            # 更新权重
            self.scaler.step(optimizer)
            self.scaler.update()
        else:
            # 常规训练
            loss.backward()
            optimizer.step()
    
    def autocast_context(self):
        """
        创建自动转换精度的上下文管理器
        
        Returns:
            上下文管理器
        """
        if self.enabled:
            return autocast(dtype=self.dtype)
        else:
            # 返回一个不执行任何操作的上下文管理器
            return torch.no_grad()
    
    def _check_gradients(self, model: nn.Module) -> None:
        """
        检查模型梯度中是否存在NaN或无穷大值
        
        Args:
            model: 要检查的模型
        """
        bad_grads = False
        for name, param in model.named_parameters():
            if param.grad is not None:
                if torch.isnan(param.grad).any() or torch.isinf(param.grad).any():
                    logger.warning(f"检测到 {name} 参数的梯度存在NaN或无穷大值")
                    bad_grads = True
        
        if bad_grads:
            logger.warning("存在问题梯度，可能需要调整学习率或gradient clipping")


class DistributedTrainingWrapper:
    """
    分布式训练包装器
    
    封装模型和优化器以支持分布式训练，使用PyTorch的DistributedDataParallel。
    """
    
    def __init__(self, backend: str = 'nccl', find_unused_parameters: bool = False):
        """
        初始化分布式训练包装器
        
        Args:
            backend: 分布式后端，通常为'nccl'（GPU）或'gloo'（CPU）
            find_unused_parameters: 是否在分布式训练中查找未使用的参数
        """
        self.backend = backend
        self.find_unused_parameters = find_unused_parameters
        self.is_initialized = False
        self.rank = 0
        self.world_size = 1
        
    def initialize(self, rank: int = 0, world_size: int = 1) -> None:
        """
        初始化分布式训练环境
        
        Args:
            rank: 当前进程的排名
            world_size: 总进程数
        """
        if dist.is_available() and not dist.is_initialized():
            # 初始化分布式环境
            try:
                dist.init_process_group(
                    backend=self.backend,
                    init_method='env://',
                    world_size=world_size,
                    rank=rank
                )
                self.is_initialized = True
                self.rank = rank
                self.world_size = world_size
                
                # 设置当前设备
                if torch.cuda.is_available():
                    torch.cuda.set_device(rank)
                
                logger.info(f"初始化分布式训练环境，进程排名: {rank}/{world_size-1}")
            except Exception as e:
                logger.error(f"初始化分布式环境失败: {str(e)}")
        else:
            if dist.is_initialized():
                self.is_initialized = True
                self.rank = dist.get_rank()
                self.world_size = dist.get_world_size()
                logger.info(f"使用已存在的分布式环境，进程排名: {self.rank}/{self.world_size-1}")
            else:
                logger.warning("分布式训练不可用，将使用单进程训练")
    
    def wrap_model(self, model: nn.Module) -> nn.Module:
        """
        将模型包装为DistributedDataParallel模型
        
        Args:
            model: 要包装的模型
            
        Returns:
            分布式数据并行模型
        """
        if not self.is_initialized or not torch.cuda.is_available():
            logger.info("不使用DDP包装模型")
            return model
        
        # 将模型移动到当前GPU
        model = model.cuda()
        
        # 包装为DistributedDataParallel
        from torch.nn.parallel import DistributedDataParallel as DDP
        
        ddp_model = DDP(
            model, 
            device_ids=[self.rank],
            output_device=self.rank,
            find_unused_parameters=self.find_unused_parameters
        )
        
        logger.info(f"模型已包装为DistributedDataParallel，设备ID: {self.rank}")
        return ddp_model
    
    def prepare_dataloader(self, dataloader: torch.utils.data.DataLoader) -> torch.utils.data.DataLoader:
        """
        准备数据加载器以用于分布式训练
        
        Args:
            dataloader: 原始数据加载器
            
        Returns:
            用于分布式训练的数据加载器
        """
        if not self.is_initialized:
            return dataloader
        
        # 获取原始数据集和批处理大小
        dataset = dataloader.dataset
        batch_size = dataloader.batch_size
        
        # 创建分布式采样器
        from torch.utils.data.distributed import DistributedSampler
        sampler = DistributedSampler(
            dataset,
            num_replicas=self.world_size,
            rank=self.rank,
            shuffle=True
        )
        
        # 创建新的数据加载器
        new_dataloader = torch.utils.data.DataLoader(
            dataset,
            batch_size=batch_size,
            sampler=sampler,
            num_workers=dataloader.num_workers,
            pin_memory=dataloader.pin_memory,
            drop_last=dataloader.drop_last
        )
        
        logger.info(f"数据加载器已准备用于分布式训练，进程排名: {self.rank}")
        return new_dataloader
    
    def cleanup(self) -> None:
        """
        清理分布式训练环境
        """
        if self.is_initialized and dist.is_initialized():
            dist.destroy_process_group()
            self.is_initialized = False
            logger.info("分布式训练环境已清理")
    
    def is_main_process(self) -> bool:
        """
        检查当前进程是否为主进程
        
        Returns:
            当rank=0时为True，否则为False
        """
        return self.rank == 0 