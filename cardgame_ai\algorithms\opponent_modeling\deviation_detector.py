"""
对手偏离检测器模块

实现一个检测对手（尤其是人类玩家）偏离GTO策略的机制，
并在检测到偏离时提供剥削性的对策。
"""

import os
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import logging
from typing import Dict, Any, List, Tuple, Optional, Union, Callable

from cardgame_ai.core.base import State, Action
from cardgame_ai.algorithms.human_policy_network import HumanPolicyNetwork
from cardgame_ai.algorithms.gto_approximation import GTOPolicy

# 配置日志
logger = logging.getLogger(__name__)


class DeviationDetector(nn.Module):
    """
    偏离检测器

    检测对手（尤其是人类玩家）偏离GTO策略的行为模式，
    并在检测到偏离时提供剥削性的对策。
    """

    def __init__(
        self,
        state_dim: int,
        action_dim: int,
        gto_policy_source: GTOPolicy,
        human_policy_network: Optional[HumanPolicyNetwork] = None,
        human_policy_path: Optional[str] = None,
        feature_extractor: Optional[Callable] = None,
        deviation_threshold: float = 0.3
    ):
        """
        初始化偏离检测器

        Args:
            state_dim: 状态维度
            action_dim: 动作维度
            gto_policy_source: GTO策略源，用于获取GTO策略
            human_policy_network: 人类策略网络，用于预测人类行为
            human_policy_path: 人类策略网络路径，如果提供则加载模型
            feature_extractor: 特征提取器，用于将状态转换为特征向量
            deviation_threshold: 偏离阈值，超过此值被视为显著偏离
        """
        super().__init__()

        # 保存参数
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.gto_policy_source = gto_policy_source
        self.feature_extractor = feature_extractor
        self.deviation_threshold = deviation_threshold

        # 初始化人类策略网络
        if human_policy_network is not None:
            self.opponent_policy_predictor = human_policy_network
        else:
            self.opponent_policy_predictor = HumanPolicyNetwork(state_dim, action_dim)

            # 如果提供了模型路径，加载模型
            if human_policy_path and os.path.exists(human_policy_path):
                self._load_human_policy(human_policy_path)

        # 初始化统计信息
        self.stats = {
            "total_checks": 0,
            "deviations_detected": 0,
            "avg_deviation_score": 0.0,
            "max_deviation_score": 0.0
        }

        # 初始化偏离历史记录
        self.deviation_history = []
        self.max_history_length = 20  # 保留最近20次动作的偏离记录

    def _load_human_policy(self, model_path: str) -> None:
        """
        加载人类策略网络

        Args:
            model_path: 模型路径
        """
        try:
            checkpoint = torch.load(model_path, map_location='cpu')

            # 检查是否是字典格式的检查点
            if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
                self.opponent_policy_predictor.load_state_dict(checkpoint['model_state_dict'])
            else:
                self.opponent_policy_predictor.load_state_dict(checkpoint)

            logger.info(f"已加载人类策略网络: {model_path}")
        except Exception as e:
            logger.error(f"加载人类策略网络失败: {e}")

    def forward(self, state: torch.Tensor, opponent_action: int) -> float:
        """
        前向传播，计算偏离分数

        Args:
            state: 游戏状态
            opponent_action: 对手动作

        Returns:
            偏离分数
        """
        # 确保输入是张量
        if not isinstance(state, torch.Tensor):
            state = torch.tensor(state, dtype=torch.float32)

        # 预测对手可能的动作分布
        with torch.no_grad():
            predicted_opponent_logits = self.opponent_policy_predictor(state)
            predicted_opponent_probs = F.softmax(predicted_opponent_logits, dim=-1)

        # 获取GTO策略
        if self.feature_extractor:
            state_features = self.feature_extractor(state.cpu().numpy())
            gto_probs = self.gto_policy_source.get_policy(state_features)
        else:
            gto_probs = self.gto_policy_source.get_policy(state.cpu().numpy())

        # 确保GTO策略是张量
        if not isinstance(gto_probs, torch.Tensor):
            gto_probs = torch.tensor(gto_probs, dtype=torch.float32)

        # 计算KL散度
        kl_divergence = self._calculate_kl_divergence(predicted_opponent_probs, gto_probs)

        # 计算实际动作的概率差异
        prob_diff_actual_action = 0.0
        if opponent_action < len(predicted_opponent_probs) and opponent_action < len(gto_probs):
            prob_diff_actual_action = abs(
                predicted_opponent_probs[opponent_action].item() -
                gto_probs[opponent_action].item()
            )

        # 计算偏离分数（结合KL散度和实际动作概率差异）
        deviation_score = 0.7 * kl_divergence + 0.3 * prob_diff_actual_action

        # 更新统计信息
        self.stats["total_checks"] += 1
        self.stats["avg_deviation_score"] = (
            (self.stats["avg_deviation_score"] * (self.stats["total_checks"] - 1) + deviation_score) /
            self.stats["total_checks"]
        )
        self.stats["max_deviation_score"] = max(self.stats["max_deviation_score"], deviation_score)

        if deviation_score > self.deviation_threshold:
            self.stats["deviations_detected"] += 1

        # 更新偏离历史
        self.deviation_history.append({
            "action": opponent_action,
            "score": deviation_score,
            "kl_divergence": kl_divergence,
            "prob_diff": prob_diff_actual_action
        })

        # 保持历史记录在最大长度以内
        if len(self.deviation_history) > self.max_history_length:
            self.deviation_history.pop(0)

        return deviation_score

    def _calculate_kl_divergence(self, p: torch.Tensor, q: torch.Tensor) -> float:
        """
        计算KL散度

        Args:
            p: 第一个概率分布
            q: 第二个概率分布

        Returns:
            KL散度
        """
        # 确保概率分布有效（非零）
        p = torch.clamp(p, min=1e-8)
        q = torch.clamp(q, min=1e-8)

        # 计算KL散度: KL(p||q) = sum(p * log(p/q))
        kl = torch.sum(p * torch.log(p / q))

        return kl.item()

    def get_deviation_pattern(self) -> Dict[str, Any]:
        """
        获取偏离模式

        分析历史偏离记录，识别对手的偏离模式。

        Returns:
            偏离模式信息
        """
        if not self.deviation_history:
            return {"pattern": "unknown", "confidence": 0.0}

        # 计算平均偏离分数
        avg_score = sum(item["score"] for item in self.deviation_history) / len(self.deviation_history)

        # 计算偏离频率
        deviation_count = sum(1 for item in self.deviation_history if item["score"] > self.deviation_threshold)
        deviation_frequency = deviation_count / len(self.deviation_history)

        # 识别模式
        pattern = "unknown"
        confidence = 0.0

        if deviation_frequency > 0.7:
            pattern = "high_deviation"  # 高频偏离
            confidence = min(1.0, deviation_frequency)
        elif deviation_frequency > 0.3:
            pattern = "moderate_deviation"  # 中等偏离
            confidence = deviation_frequency
        else:
            pattern = "low_deviation"  # 低频偏离
            confidence = 1.0 - deviation_frequency

        return {
            "pattern": pattern,
            "confidence": confidence,
            "avg_score": avg_score,
            "frequency": deviation_frequency,
            "sample_size": len(self.deviation_history)
        }

    def get_exploitation_strategy(self, state: torch.Tensor, legal_actions: List[int]) -> Dict[str, Any]:
        """
        获取剥削策略

        根据对手的偏离模式，提供剥削性的对策。

        Args:
            state: 当前游戏状态
            legal_actions: 合法动作列表

        Returns:
            剥削策略信息
        """
        # 获取偏离模式
        pattern_info = self.get_deviation_pattern()
        pattern = pattern_info["pattern"]
        confidence = pattern_info["confidence"]

        # 获取GTO策略
        if self.feature_extractor:
            state_features = self.feature_extractor(state.cpu().numpy() if isinstance(state, torch.Tensor) else state)
            gto_probs = self.gto_policy_source.get_policy(state_features)
        else:
            gto_probs = self.gto_policy_source.get_policy(
                state.cpu().numpy() if isinstance(state, torch.Tensor) else state
            )

        # 确保GTO策略是numpy数组
        if isinstance(gto_probs, torch.Tensor):
            gto_probs = gto_probs.cpu().numpy()

        # 根据偏离模式调整策略
        exploitation_probs = np.copy(gto_probs)

        if pattern == "high_deviation":
            # 对于高频偏离，采用更激进的剥削策略
            # 这里简化处理，实际应用中可能需要更复杂的逻辑
            for action in legal_actions:
                if action < len(exploitation_probs):
                    # 增强GTO中高概率动作，弱化低概率动作
                    if gto_probs[action] > 0.1:
                        exploitation_probs[action] = gto_probs[action] * (1 + 0.5 * confidence)
                    else:
                        exploitation_probs[action] = gto_probs[action] * (1 - 0.3 * confidence)
        elif pattern == "moderate_deviation":
            # 对于中等偏离，采用适度的剥削策略
            for action in legal_actions:
                if action < len(exploitation_probs):
                    # 轻微调整GTO策略
                    if gto_probs[action] > 0.1:
                        exploitation_probs[action] = gto_probs[action] * (1 + 0.2 * confidence)
                    else:
                        exploitation_probs[action] = gto_probs[action] * (1 - 0.1 * confidence)

        # 归一化概率
        if np.sum(exploitation_probs) > 0:
            exploitation_probs = exploitation_probs / np.sum(exploitation_probs)

        # 返回剥削策略信息
        return {
            "pattern": pattern,
            "confidence": confidence,
            "gto_probs": gto_probs,
            "exploitation_probs": exploitation_probs,
            "legal_actions": legal_actions
        }

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            统计信息
        """
        return {
            **self.stats,
            "deviation_rate": self.stats["deviations_detected"] / max(1, self.stats["total_checks"]),
            "pattern": self.get_deviation_pattern()
        }

    def analyze_deviation(self, state: Any, opponent_action: int) -> Dict[str, Any]:
        """
        分析对手行为的偏离情况
        
        Args:
            state: 游戏状态
            opponent_action: 对手动作
            
        Returns:
            偏离分析结果字典，包含偏离分数、偏离类型和详细信息
        """
        # 确保输入是张量
        if not isinstance(state, torch.Tensor) and hasattr(state, '__array__'):
            state = torch.tensor(np.array(state), dtype=torch.float32)
        elif not isinstance(state, torch.Tensor) and not hasattr(state, '__array__'):
            # 尝试提取特征
            if self.feature_extractor:
                state_features = self.feature_extractor(state)
                state = torch.tensor(state_features, dtype=torch.float32)
        
        # 计算偏离分数
        deviation_score = self.forward(state, opponent_action)
        
        # 获取GTO策略
        if self.feature_extractor:
            state_features = self.feature_extractor(state.cpu().numpy() if isinstance(state, torch.Tensor) else state)
            gto_probs = self.gto_policy_source.get_policy(state_features)
        else:
            gto_probs = self.gto_policy_source.get_policy(
                state.cpu().numpy() if isinstance(state, torch.Tensor) else state
            )
        
        # 确保GTO策略是numpy数组
        if isinstance(gto_probs, torch.Tensor):
            gto_probs = gto_probs.cpu().numpy()
        
        # 获取动作类型和价值
        action_type = "UNKNOWN"
        action_value = 0.0
        expected_value = 0.0
        card_value = 0
        
        # 对于斗地主游戏特定逻辑
        if hasattr(state, 'get_card_play_action_features'):
            try:
                # 获取动作特征
                action_features = state.get_card_play_action_features(opponent_action)
                action_type = action_features.get("type", "UNKNOWN")
                card_value = action_features.get("rank", 0)
            except Exception as e:
                logging.error(f"获取动作特征出错: {e}")
        
        # 估计动作价值
        if opponent_action < len(gto_probs):
            expected_value = gto_probs[opponent_action]
        
        # 预测对手可能的动作分布
        with torch.no_grad():
            predicted_opponent_logits = self.opponent_policy_predictor(state)
            predicted_opponent_probs = F.softmax(predicted_opponent_logits, dim=-1)
        
        if opponent_action < len(predicted_opponent_probs):
            action_value = predicted_opponent_probs[opponent_action].item()
        
        # 判断偏离类型
        deviation_type = "normal"
        if deviation_score > self.deviation_threshold:
            if action_value > expected_value:
                deviation_type = "aggressive"  # 对手比GTO更积极
            else:
                deviation_type = "conservative"  # 对手比GTO更保守
        
        # 构建偏离分析结果
        deviation_info = {
            "deviation_score": deviation_score,
            "deviation_type": deviation_type,
            "action_type": action_type,
            "expected_value": expected_value,
            "actual_value": action_value,
            "card_value": card_value,
            "is_significant": deviation_score > self.deviation_threshold
        }
        
        return deviation_info

class DeviationToExploitMapper:
    """
    偏离信号剥削映射器
    
    负责将 DeviationDetector 检测到的偏离信号转化为对策略网络 logits 的修改，
    以实现对不同类型偏离的有效剥削。
    """
    
    def __init__(
        self,
        deviation_detector: DeviationDetector,
        min_confidence: float = 0.3,
        max_logit_adjustment: float = 2.0,
        enable_pattern_specific_exploitation: bool = True,
        exploitation_strength: float = 1.0
    ):
        """
        初始化偏离信号剥削映射器
        
        Args:
            deviation_detector: 偏离检测器实例
            min_confidence: 最小置信度阈值，低于此值不进行剥削
            max_logit_adjustment: 最大logit调整值
            enable_pattern_specific_exploitation: 是否启用针对特定模式的剥削策略
            exploitation_strength: 剥削强度系数(0.0-2.0)，值越大剥削越激进
        """
        self.deviation_detector = deviation_detector
        self.min_confidence = min_confidence
        self.max_logit_adjustment = max_logit_adjustment
        self.enable_pattern_specific_exploitation = enable_pattern_specific_exploitation
        self.exploitation_strength = min(2.0, max(0.0, exploitation_strength))
        
        # 初始化偏离模式库，用于识别常见偏离模式
        self.pattern_library = {
            "risk_averse": {
                "description": "风险规避型玩家，偏好保守策略",
                "exploitation": "更加激进，利用对手不愿冒险的倾向"
            },
            "risk_seeking": {
                "description": "风险追求型玩家，偏好激进策略",
                "exploitation": "采取稳健策略，等待对手犯错"
            },
            "pattern_player": {
                "description": "模式化玩家，行动具有可预测性",
                "exploitation": "针对性打破对手模式，出其不意"
            },
            "passive": {
                "description": "被动型玩家，很少主动出牌",
                "exploitation": "增强进攻性，控制节奏"
            },
            "aggressive": {
                "description": "激进型玩家，喜欢主动出牌",
                "exploitation": "保持防守，伺机反击"
            },
            "card_hoarding": {
                "description": "囤牌型玩家，不愿打出高价值牌",
                "exploitation": "迫使对手出牌，破坏囤牌策略"
            }
        }
        
        # 初始化记忆存储，用于记录对手行为模式
        self.pattern_memory = {pattern: 0.0 for pattern in self.pattern_library.keys()}
        self.detection_history = []
        self.max_history_size = 20
    
    def detect_exploitable_pattern(self, states: List[State], actions: List[Action]) -> Tuple[bool, Dict[str, Any]]:
        """
        检测可剥削的行为模式
        
        Args:
            states: 历史状态列表
            actions: 对应的动作列表
            
        Returns:
            is_exploitable: 是否存在可剥削模式
            exploitation_info: 剥削信息字典，包含模式类型、置信度等
        """
        if len(states) != len(actions) or len(states) == 0:
            return False, {"pattern_type": "unknown", "confidence": 0.0}
        
        try:
            # 重置模式评分
            pattern_scores = {pattern: 0.0 for pattern in self.pattern_library.keys()}
            
            # 基于历史状态和动作，计算各种行为模式的得分
            for i, (state, action) in enumerate(zip(states, actions)):
                # 调用偏离检测器获取偏离信息
                deviation_info = self.deviation_detector.analyze_deviation(state, action)
                
                # 根据偏离信息更新模式评分
                self._update_pattern_scores(pattern_scores, deviation_info)
                
                # 将检测结果添加到历史记录
                self.detection_history.append(deviation_info)
                if len(self.detection_history) > self.max_history_size:
                    self.detection_history.pop(0)
            
            # 归一化模式得分
            total_score = sum(pattern_scores.values()) + 1e-10  # 避免除以0
            normalized_scores = {k: v/total_score for k, v in pattern_scores.items()}
            
            # 找出得分最高的模式
            best_pattern = max(normalized_scores.items(), key=lambda x: x[1])
            pattern_type, confidence = best_pattern
            
            # 更新长期记忆
            for pattern, score in normalized_scores.items():
                self.pattern_memory[pattern] = 0.9 * self.pattern_memory[pattern] + 0.1 * score
            
            # 检查是否可剥削（置信度超过阈值）
            is_exploitable = confidence >= self.min_confidence
            
            return is_exploitable, {
                "pattern_type": pattern_type,
                "confidence": confidence,
                "all_patterns": normalized_scores,
                "long_term_patterns": self.pattern_memory
            }
            
        except Exception as e:
            logging.error(f"检测可剥削模式时出错: {e}")
            return False, {"pattern_type": "unknown", "confidence": 0.0}
    
    def _update_pattern_scores(self, pattern_scores: Dict[str, float], deviation_info: Dict[str, Any]):
        """
        根据偏离信息更新模式评分
        
        Args:
            pattern_scores: 模式评分字典
            deviation_info: 偏离信息字典
        """
        # 从偏离信息中提取关键特征
        deviation_score = deviation_info.get("deviation_score", 0.0)
        expected_action_value = deviation_info.get("expected_value", 0.0)
        actual_action_value = deviation_info.get("actual_value", 0.0)
        value_diff = expected_action_value - actual_action_value
        action_type = deviation_info.get("action_type", "unknown")
        
        # 根据偏离特征更新各模式得分
        # 风险规避型特征
        if value_diff > 0 and action_type in ["PASS", "SINGLE", "PAIR"]:
            pattern_scores["risk_averse"] += deviation_score * 1.5
        
        # 风险追求型特征
        if value_diff < 0 and action_type in ["BOMB", "ROCKET", "STRAIGHT"]:
            pattern_scores["risk_seeking"] += deviation_score * 1.2
        
        # 模式化玩家特征
        if len(self.detection_history) >= 3:
            # 检查是否有重复模式
            recent_actions = [info.get("action_type", "") for info in self.detection_history[-3:]]
            if len(set(recent_actions)) < len(recent_actions):
                pattern_scores["pattern_player"] += deviation_score * 1.3
        
        # 被动型玩家特征
        if action_type == "PASS" and deviation_score > 0.3:
            pattern_scores["passive"] += deviation_score * 1.4
        
        # 激进型玩家特征
        if action_type != "PASS" and value_diff < -0.2:
            pattern_scores["aggressive"] += deviation_score * 1.2
        
        # 囤牌型玩家特征
        if "card_value" in deviation_info and deviation_info["card_value"] > 10 and action_type == "PASS":
            pattern_scores["card_hoarding"] += deviation_score * 1.1
    
    def adjust_policy_logits(self, policy_logits: torch.Tensor, state: State, legal_actions: List[Action]) -> torch.Tensor:
        """
        根据检测到的对手偏离模式，调整策略网络的logits以实现剥削
        
        Args:
            policy_logits: 原始策略网络输出的logits
            state: 当前游戏状态
            legal_actions: 合法动作列表
            
        Returns:
            adjusted_logits: 调整后的logits
        """
        # 创建深拷贝避免修改原始tensor
        adjusted_logits = policy_logits.clone()
        
        # 如果没有历史记录或禁用了模式特定剥削，则直接返回原始logits
        if len(self.detection_history) == 0 or not self.enable_pattern_specific_exploitation:
            return adjusted_logits
        
        try:
            # 获取偏离模式信息
            pattern_info = self.deviation_detector.get_deviation_pattern()
            pattern_type = pattern_info.get("pattern", "unknown")
            confidence = pattern_info.get("confidence", 0.0)
            
            # 如果是直接从deviation_detector获取，也检查主要的检测到的对手模式
            if confidence < self.min_confidence:
                dominant_pattern = max(self.pattern_memory.items(), key=lambda x: x[1])
                pattern_type, confidence = dominant_pattern
            
            # 如果置信度仍然不够，不进行调整
            if confidence < self.min_confidence:
                return adjusted_logits
            
            # 获取动作类型映射，将索引转换为动作类型
            action_types = self._map_actions_to_types(legal_actions, state)
            
            # 根据游戏阶段调整策略的剥削强度
            game_stage = self._determine_game_stage(state)
            stage_multiplier = 1.0
            if game_stage == "early":
                stage_multiplier = 0.7  # 早期阶段剥削较温和
            elif game_stage == "mid":
                stage_multiplier = 1.0  # 中期阶段正常剥削
            elif game_stage == "late":
                stage_multiplier = 1.3  # 后期阶段剥削更激进
            
            # 计算实际应用的剥削强度
            effective_strength = self.exploitation_strength * stage_multiplier * confidence
            adjustment_value = self.max_logit_adjustment * effective_strength
            
            # 获取对手最近的偏离历史
            recent_deviations = self.detection_history[-min(5, len(self.detection_history)):]
            
            # 根据偏离模式类型调整logits
            if pattern_type == "high_deviation":
                # 对高度偏离的对手，基于具体偏离类型采取不同策略
                deviation_types = [d.get("deviation_type", "") for d in recent_deviations]
                common_type = max(set(deviation_types), key=deviation_types.count) if deviation_types else "unknown"
                
                if common_type == "aggressive":
                    # 对手过于激进，采取更加保守的策略
                    defensive_indices = [i for i, act_type in enumerate(action_types) 
                                      if act_type in ["PASS", "BOMB", "ROCKET"]]
                    for idx in defensive_indices:
                        if idx < len(adjusted_logits):
                            adjusted_logits[idx] += adjustment_value
                
                elif common_type == "conservative":
                    # 对手过于保守，采取更加激进的策略
                    offensive_indices = [i for i, act_type in enumerate(action_types) 
                                      if act_type in ["STRAIGHT", "AIRPLANE", "STRAIGHT_PAIR"]]
                    for idx in offensive_indices:
                        if idx < len(adjusted_logits):
                            adjusted_logits[idx] += adjustment_value
            
            elif pattern_type == "risk_averse":
                # 对风险规避型对手，增强激进动作的概率
                aggressive_indices = [i for i, act_type in enumerate(action_types) 
                                    if act_type in ["BOMB", "ROCKET", "STRAIGHT", "AIRPLANE"]]
                for idx in aggressive_indices:
                    if idx < len(adjusted_logits):
                        adjusted_logits[idx] += adjustment_value
                
                # 特别强化：如果对手经常PASS，则增强出单牌和对子的概率
                if any(d.get("action_type", "") == "PASS" for d in recent_deviations):
                    single_pair_indices = [i for i, act_type in enumerate(action_types)
                                         if act_type in ["SINGLE", "PAIR"]]
                    for idx in single_pair_indices:
                        if idx < len(adjusted_logits):
                            adjusted_logits[idx] += adjustment_value * 0.8
            
            elif pattern_type == "risk_seeking":
                # 对风险追求型对手，增强稳健动作的概率
                conservative_indices = [i for i, act_type in enumerate(action_types) 
                                      if act_type in ["SINGLE", "PAIR", "TRIO"]]
                for idx in conservative_indices:
                    if idx < len(adjusted_logits):
                        adjusted_logits[idx] += adjustment_value
                
                # 特别强化：如果对手喜欢出炸弹，则增强高价值单牌的概率
                bomb_loving = any(d.get("action_type", "") in ["BOMB", "ROCKET"] for d in recent_deviations)
                if bomb_loving:
                    high_single_indices = [i for i, act_type in enumerate(action_types)
                                         if act_type == "SINGLE" and self._is_high_card(legal_actions[i], state)]
                    for idx in high_single_indices:
                        if idx < len(adjusted_logits):
                            adjusted_logits[idx] += adjustment_value * 0.9
            
            elif pattern_type == "pattern_player":
                # 对模式化玩家，打破常规模式
                # 找出最近使用过的动作类型
                recent_types = [d.get("action_type", "") for d in recent_deviations]
                
                # 增强不同类型动作的概率
                different_indices = [i for i, act_type in enumerate(action_types) 
                                   if act_type not in recent_types]
                for idx in different_indices:
                    if idx < len(adjusted_logits):
                        adjusted_logits[idx] += adjustment_value * 0.8
                
                # 特别强化：如果对手有明显的牌型偏好，则针对性破坏
                if len(recent_types) >= 3:
                    preferred_type = max(set(recent_types), key=recent_types.count)
                    counter_indices = self._get_counter_actions(preferred_type, action_types, legal_actions, state)
                    for idx in counter_indices:
                        if idx < len(adjusted_logits):
                            adjusted_logits[idx] += adjustment_value * 1.1
            
            elif pattern_type == "passive":
                # 对被动型玩家，增强控场动作的概率
                control_indices = [i for i, act_type in enumerate(action_types) 
                                 if act_type in ["STRAIGHT", "STRAIGHT_PAIR", "AIRPLANE"]]
                for idx in control_indices:
                    if idx < len(adjusted_logits):
                        adjusted_logits[idx] += adjustment_value
                
                # 特别强化：如果处于游戏后期，加强单牌和对子
                if game_stage == "late":
                    small_indices = [i for i, act_type in enumerate(action_types)
                                   if act_type in ["SINGLE", "PAIR"]]
                    for idx in small_indices:
                        if idx < len(adjusted_logits):
                            adjusted_logits[idx] += adjustment_value * 1.2
            
            elif pattern_type == "aggressive":
                # 对激进型玩家，增强防守和反击动作的概率
                counter_indices = [i for i, act_type in enumerate(action_types) 
                                 if act_type in ["BOMB", "ROCKET", "PASS"]]
                for idx in counter_indices:
                    if idx < len(adjusted_logits):
                        adjusted_logits[idx] += adjustment_value
                
                # 特别强化：如果对手频繁出大牌，则增强高价值牌的保留
                big_card_lover = any(d.get("card_value", 0) > 10 for d in recent_deviations)
                if big_card_lover:
                    pass_index = action_types.index("PASS") if "PASS" in action_types else -1
                    if pass_index >= 0 and pass_index < len(adjusted_logits):
                        adjusted_logits[pass_index] += adjustment_value * 0.7
            
            elif pattern_type == "card_hoarding":
                # 对囤牌型玩家，增强逼牌动作的概率
                force_indices = [i for i, act_type in enumerate(action_types) 
                              if act_type in ["SINGLE", "PAIR"] and self._is_high_card(legal_actions[i], state)]
                for idx in force_indices:
                    if idx < len(adjusted_logits):
                        adjusted_logits[idx] += adjustment_value
                
                # 特别强化：如果对手囤积高牌，增强连牌出牌概率
                straight_indices = [i for i, act_type in enumerate(action_types)
                                  if "STRAIGHT" in act_type]
                for idx in straight_indices:
                    if idx < len(adjusted_logits):
                        adjusted_logits[idx] += adjustment_value * 0.9
            
            # 新增：手牌差距感知的调整
            if hasattr(state, 'get_player_num_cards'):
                try:
                    # 获取双方手牌数量
                    my_num_cards = state.get_player_num_cards(state.player_id)
                    opponent_num_cards = state.get_player_num_cards(1 - state.player_id)
                    card_diff = my_num_cards - opponent_num_cards
                    
                    if card_diff > 3:
                        # 我方手牌多，加强快速出牌
                        quick_play_indices = [i for i, act_type in enumerate(action_types)
                                            if act_type in ["STRAIGHT", "AIRPLANE", "TRIO_PAIR"]]
                        for idx in quick_play_indices:
                            if idx < len(adjusted_logits):
                                adjusted_logits[idx] += adjustment_value * 0.8
                    
                    elif card_diff < -3:
                        # 我方手牌少，加强高效出牌和炸弹
                        efficient_indices = [i for i, act_type in enumerate(action_types)
                                           if act_type in ["BOMB", "ROCKET"] or 
                                           (act_type == "SINGLE" and self._is_high_card(legal_actions[i], state))]
                        for idx in efficient_indices:
                            if idx < len(adjusted_logits):
                                adjusted_logits[idx] += adjustment_value * 1.0
                except:
                    pass
            
            # 新增：学习化策略的调整
            self._apply_learned_adjustments(adjusted_logits, state, action_types, legal_actions, recent_deviations)
            
            # 记录调整情况
            logging.debug(f"调整策略logits以剥削{pattern_type}模式，置信度: {confidence:.4f}, 强度: {effective_strength:.4f}")
            
            return adjusted_logits
            
        except Exception as e:
            logging.error(f"调整策略logits出错: {e}")
            return policy_logits  # 出错时返回原始logits
    
    def _determine_game_stage(self, state: State) -> str:
        """
        判断当前游戏阶段
        
        Args:
            state: 当前游戏状态
            
        Returns:
            stage: 游戏阶段 ("early", "mid", "late")
        """
        try:
            # 如果状态有获取剩余牌数的方法
            if hasattr(state, 'get_player_num_cards'):
                my_num_cards = state.get_player_num_cards(state.player_id)
                total_cards = 17 if state.player_id == 0 else 20  # 假设斗地主规则（地主17张，农民20张）
                
                # 根据剩余牌比例判断游戏阶段
                remaining_ratio = my_num_cards / total_cards
                
                if remaining_ratio > 0.7:
                    return "early"
                elif remaining_ratio > 0.3:
                    return "mid"
                else:
                    return "late"
            
            # 如果状态有回合数信息
            elif hasattr(state, 'turn_count'):
                # 假设斗地主一般不超过30回合
                if state.turn_count < 10:
                    return "early"
                elif state.turn_count < 20:
                    return "mid"
                else:
                    return "late"
            
            else:
                # 默认中期
                return "mid"
        except:
            return "mid"
    
    def _get_counter_actions(self, action_type: str, all_types: List[str], 
                           legal_actions: List[Action], state: State) -> List[int]:
        """
        获取针对特定动作类型的反制动作索引
        
        Args:
            action_type: 目标动作类型
            all_types: 所有动作的类型列表
            legal_actions: 合法动作列表
            state: 当前状态
            
        Returns:
            counter_indices: 反制动作的索引列表
        """
        counter_indices = []
        
        if action_type == "PASS":
            # 反制PASS: 出小牌
            counter_indices = [i for i, t in enumerate(all_types) if t in ["SINGLE", "PAIR"]]
        
        elif action_type == "SINGLE":
            # 反制单牌: 出高单牌或炸弹
            counter_indices = [i for i, t in enumerate(all_types) 
                             if (t == "SINGLE" and self._is_high_card(legal_actions[i], state)) or
                               t in ["BOMB", "ROCKET"]]
        
        elif action_type == "PAIR":
            # 反制对子: 出高对子或炸弹
            counter_indices = [i for i, t in enumerate(all_types) 
                             if (t == "PAIR" and self._is_high_card(legal_actions[i], state)) or
                               t in ["BOMB", "ROCKET"]]
        
        elif action_type in ["BOMB", "ROCKET"]:
            # 反制炸弹: PASS或更大的炸弹
            counter_indices = [i for i, t in enumerate(all_types) if t == "PASS"]
            
            # 如果有更大的炸弹，也考虑
            bomb_indices = [i for i, t in enumerate(all_types) if t in ["BOMB", "ROCKET"]]
            if bomb_indices:
                counter_indices.extend(bomb_indices)
        
        elif "STRAIGHT" in action_type:
            # 反制顺子: 出炸弹或大顺子
            counter_indices = [i for i, t in enumerate(all_types) 
                             if t in ["BOMB", "ROCKET"] or
                              ("STRAIGHT" in t and self._is_high_card(legal_actions[i], state))]
        
        else:
            # 默认反制: 出炸弹或PASS
            counter_indices = [i for i, t in enumerate(all_types) if t in ["BOMB", "ROCKET", "PASS"]]
        
        return counter_indices
    
    def _apply_learned_adjustments(self, logits: torch.Tensor, state: State, 
                                 action_types: List[str], legal_actions: List[Action],
                                 recent_deviations: List[Dict[str, Any]]) -> None:
        """
        应用学习化的调整策略
        
        Args:
            logits: 需要调整的logits
            state: 当前状态
            action_types: 动作类型列表
            legal_actions: 合法动作列表
            recent_deviations: 最近的偏离记录
        """
        # 如果没有足够的历史数据，不进行学习化调整
        if len(recent_deviations) < 3:
            return
        
        try:
            # 分析历史数据中对手的响应模式
            response_patterns = {}
            
            # 统计对手对不同类型动作的响应频率
            for i in range(len(recent_deviations) - 1):
                current_type = recent_deviations[i].get("action_type", "UNKNOWN")
                next_type = recent_deviations[i+1].get("action_type", "UNKNOWN")
                
                if current_type != "UNKNOWN" and next_type != "UNKNOWN":
                    key = f"{current_type}_to_{next_type}"
                    response_patterns[key] = response_patterns.get(key, 0) + 1
            
            # 寻找最常见的响应模式
            if response_patterns:
                most_common = max(response_patterns.items(), key=lambda x: x[1])
                common_pattern, frequency = most_common
                
                # 提取模式对应的动作类型
                if "_to_" in common_pattern:
                    trigger_type, response_type = common_pattern.split("_to_")
                    
                    # 根据发现的模式调整logits
                    # 如果对手对某类型动作有明确的响应模式，我们可以利用这点
                    if trigger_type and response_type:
                        # 找出能有效针对该响应类型的动作
                        counter_response_indices = []
                        
                        if response_type == "PASS":
                            # 如果对手经常PASS，则增强中小牌的出牌概率
                            counter_response_indices = [i for i, t in enumerate(action_types)
                                                      if t in ["SINGLE", "PAIR", "TRIO"]]
                        
                        elif response_type in ["BOMB", "ROCKET"]:
                            # 如果对手经常出炸弹，则增强PASS或更大炸弹的概率
                            counter_response_indices = [i for i, t in enumerate(action_types)
                                                      if t == "PASS" or t in ["BOMB", "ROCKET"]]
                        
                        elif "STRAIGHT" in response_type:
                            # 如果对手经常出顺子，则增强炸弹和大单牌的概率
                            counter_response_indices = [i for i, t in enumerate(action_types)
                                                      if t in ["BOMB", "ROCKET", "SINGLE", "PAIR"] and
                                                       self._is_high_card(legal_actions[i], state)]
                        
                        else:
                            # 对其他响应类型，增强相应的反制动作概率
                            counter_response_indices = self._get_counter_actions(response_type, action_types, legal_actions, state)
                        
                        # 应用调整
                        for idx in counter_response_indices:
                            if idx < len(logits):
                                # 调整幅度与模式的频率和信心成正比
                                adjustment = self.max_logit_adjustment * 0.5 * (frequency / sum(response_patterns.values()))
                                logits[idx] += adjustment
                                
                        logging.debug(f"应用学习化调整针对模式: {common_pattern}, 频率: {frequency}")
        
        except Exception as e:
            logging.error(f"应用学习化调整时出错: {e}")
    
    def _map_actions_to_types(self, actions: List[Action], state: State) -> List[str]:
        """
        将动作映射为动作类型
        
        Args:
            actions: 动作列表
            state: 当前游戏状态
            
        Returns:
            action_types: 动作类型列表
        """
        action_types = []
        
        # 这里处理斗地主游戏的特定逻辑
        if hasattr(state, 'get_card_play_action_features'):
            for action in actions:
                try:
                    # 尝试获取动作特征
                    action_features = state.get_card_play_action_features(action)
                    action_type = action_features.get("type", "UNKNOWN")
                    action_types.append(action_type)
                except:
                    # 如果获取失败，使用一个默认类型
                    action_types.append("UNKNOWN")
        else:
            # 对于其他游戏，使用通用映射
            # 这里可以根据具体游戏类型扩展
            for _ in actions:
                action_types.append("UNKNOWN")
        
        return action_types
    
    def _is_high_card(self, action: Action, state: State) -> bool:
        """
        判断是否为高牌
        
        Args:
            action: 动作
            state: 当前游戏状态
            
        Returns:
            is_high: 是否为高牌
        """
        # 斗地主游戏特定逻辑
        if hasattr(state, 'get_card_play_action_features'):
            try:
                action_features = state.get_card_play_action_features(action)
                card_value = action_features.get("rank", 0)
                return card_value >= 10  # 10及以上视为高牌
            except:
                return False
        return False
