#!/usr/bin/env python
"""
人类游戏数据收集脚本

用于收集人类玩家在斗地主游戏中的状态-动作对，以训练人类策略网络。
"""

import os
import sys
import argparse
import logging
import numpy as np
import time
import json
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cardgame_ai.core.agent import HumanAgent, RandomAgent
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.utils.data_collection import InteractionDataCollector

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
)
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="人类游戏数据收集脚本")
    
    # 数据收集参数
    parser.add_argument('--data_dir', type=str, default='data/human_games',
                        help="数据保存目录")
    parser.add_argument('--file_format', type=str, default='jsonl', choices=['jsonl', 'npz'],
                        help="数据保存格式")
    parser.add_argument('--buffer_size', type=int, default=10,
                        help="缓冲区大小，达到此大小时自动保存")
    
    # 游戏参数
    parser.add_argument('--num_games', type=int, default=3,
                        help="计划收集的游戏局数")
    parser.add_argument('--human_role', type=str, default='landlord', choices=['landlord', 'farmer', 'both'],
                        help="人类玩家角色: landlord(地主), farmer(农民), both(两者都有)")
    parser.add_argument('--collect_mode', type=str, default='interactive', choices=['interactive', 'replay'],
                        help="数据收集模式: interactive(交互式), replay(回放)")
    
    # 其他参数
    parser.add_argument('--verbose', action='store_true',
                        help="是否显示详细信息")
    
    return parser.parse_args()


class DataCollectionGame:
    """数据收集游戏"""
    
    def __init__(self, data_dir, file_format='jsonl', buffer_size=10, verbose=False):
        """
        初始化数据收集游戏
        
        Args:
            data_dir: 数据保存目录
            file_format: 数据保存格式
            buffer_size: 缓冲区大小
            verbose: 是否显示详细信息
        """
        self.data_dir = data_dir
        self.file_format = file_format
        self.buffer_size = buffer_size
        self.verbose = verbose
        
        # 创建保存目录
        os.makedirs(data_dir, exist_ok=True)
        
        # 创建游戏环境
        self.env = DouDizhuEnvironment()
        
        # 初始化数据收集器
        self.data_collector = InteractionDataCollector(
            data_dir=data_dir,
            enabled=True,
            format=file_format,
            buffer_size=buffer_size
        )
        
        # 游戏统计
        self.stats = {
            "games_played": 0,
            "human_actions": 0,
            "human_wins": 0,
            "total_steps": 0
        }
    
    def setup_agents(self, human_role='landlord'):
        """
        设置代理
        
        Args:
            human_role: 人类玩家角色
            
        Returns:
            代理列表和人类玩家索引列表
        """
        agents = []
        human_indices = []
        
        # 根据角色设置人类代理和AI代理
        if human_role == 'landlord':
            # 地主为人类玩家，两个农民为随机AI
            agents = [HumanAgent(), RandomAgent(), RandomAgent()]
            human_indices = [0]
        elif human_role == 'farmer':
            # 一个农民为人类玩家，地主和另一个农民为随机AI
            agents = [RandomAgent(), HumanAgent(), RandomAgent()]
            human_indices = [1]
        elif human_role == 'both':
            # 随机分配人类角色(地主或其中一个农民)
            human_idx = np.random.choice([0, 1])
            agents = [RandomAgent(), RandomAgent(), RandomAgent()]
            agents[human_idx] = HumanAgent()
            human_indices = [human_idx]
        else:
            raise ValueError(f"不支持的人类角色: {human_role}")
        
        if self.verbose:
            role_names = ["地主", "农民1", "农民2"]
            for i, agent in enumerate(agents):
                agent_type = "人类玩家" if isinstance(agent, HumanAgent) else "随机AI"
                logger.info(f"玩家{i} ({role_names[i]}): {agent_type}")
        
        return agents, human_indices
    
    def play_game(self, agents, human_indices):
        """
        进行一局游戏并收集数据
        
        Args:
            agents: 代理列表
            human_indices: 人类玩家索引列表
            
        Returns:
            游戏结果
        """
        # 重置环境
        observation, info = self.env.reset()
        done = False
        
        # 记录每一步
        steps = 0
        human_actions_count = 0
        
        # 游戏循环
        while not done:
            # 获取当前玩家
            current_player = self.env.state.current_player
            agent = agents[current_player]
            
            # 获取合法动作
            legal_actions = self.env.legal_actions()
            
            # 选择动作
            action = agent.act(observation, legal_actions)
            
            # 如果是人类玩家的动作，记录数据
            if current_player in human_indices:
                # 获取游戏状态的观察表示
                state_observation = self.env.state.get_observation(current_player)
                
                # 记录交互数据
                self.data_collector.log_interaction(
                    game_state=state_observation,
                    ai_suggestion=None,  # 这里没有AI建议
                    human_action=action,
                    game_id=f"game_{self.stats['games_played'] + 1}"
                )
                
                human_actions_count += 1
            
            # 执行动作
            observation, reward, done, truncated, info = self.env.step(action)
            steps += 1
        
        # 游戏结束
        game_result = {
            "winner": "landlord" if reward[self.env.state.landlord] > 0 else "farmer",
            "steps": steps,
            "reward": reward.tolist(),
            "human_actions": human_actions_count
        }
        
        # 更新统计信息
        self.stats["games_played"] += 1
        self.stats["human_actions"] += human_actions_count
        self.stats["total_steps"] += steps
        
        # 检查人类玩家是否获胜
        for human_idx in human_indices:
            if (human_idx == self.env.state.landlord and game_result["winner"] == "landlord") or \
               (human_idx != self.env.state.landlord and game_result["winner"] == "farmer"):
                self.stats["human_wins"] += 1
                break
        
        return game_result
    
    def save_stats(self):
        """保存统计信息"""
        stats_path = os.path.join(self.data_dir, "collection_stats.json")
        
        # 添加时间戳
        self.stats["timestamp"] = datetime.now().isoformat()
        
        with open(stats_path, 'w', encoding='utf-8') as f:
            json.dump(self.stats, f, indent=2, ensure_ascii=False)
        
        logger.info(f"统计信息已保存至: {stats_path}")
    
    def collect_data(self, num_games=3, human_role='landlord'):
        """
        收集数据
        
        Args:
            num_games: 游戏局数
            human_role: 人类玩家角色
            
        Returns:
            统计信息
        """
        logger.info(f"计划收集{num_games}局游戏数据，人类角色: {human_role}")
        
        for game_id in range(num_games):
            logger.info(f"开始第{game_id+1}局游戏数据收集...")
            
            # 设置代理
            agents, human_indices = self.setup_agents(human_role)
            
            # 进行游戏
            result = self.play_game(agents, human_indices)
            
            logger.info(f"第{game_id+1}局游戏结束，胜利方: {result['winner']}, 步数: {result['steps']}, 收集的人类动作: {result['human_actions']}")
            
            # 如果是最后一局，确保所有数据都保存了
            if game_id == num_games - 1:
                self.data_collector.save_buffer()
        
        # 保存统计信息
        self.save_stats()
        
        logger.info("数据收集完成")
        logger.info(f"总共收集了{self.stats['games_played']}局游戏，{self.stats['human_actions']}个人类动作")
        logger.info(f"人类玩家胜率: {self.stats['human_wins'] / self.stats['games_played'] * 100:.1f}%")
        
        return self.stats


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 创建数据收集游戏
    game = DataCollectionGame(
        data_dir=args.data_dir,
        file_format=args.file_format,
        buffer_size=args.buffer_size,
        verbose=args.verbose
    )
    
    # 收集数据
    stats = game.collect_data(
        num_games=args.num_games,
        human_role=args.human_role
    )
    
    # 打印统计信息
    print("\n数据收集统计:")
    print(f"总游戏局数: {stats['games_played']}")
    print(f"收集的人类动作数: {stats['human_actions']}")
    print(f"人类胜利次数: {stats['human_wins']} ({stats['human_wins'] / stats['games_played'] * 100:.1f}%)")
    print(f"平均每局步数: {stats['total_steps'] / stats['games_played']:.1f}")
    print(f"平均每局人类动作数: {stats['human_actions'] / stats['games_played']:.1f}")
    
    logger.info("数据收集脚本执行完毕")


if __name__ == "__main__":
    main() 