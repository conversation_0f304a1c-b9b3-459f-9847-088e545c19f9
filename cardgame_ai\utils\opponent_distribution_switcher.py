#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
对手分布切换器

管理多种对手来源，并实现不同的切换策略，以提高主策略的泛化能力和鲁棒性。
"""

import os
import random
import logging
import numpy as np
from typing import Dict, List, Any, Optional, Union, Tuple

# 配置日志
logger = logging.getLogger(__name__)

class OpponentDistributionSwitcher:
    """
    对手分布切换器
    
    管理多种对手来源，并实现不同的切换策略，以提高主策略的泛化能力和鲁棒性。
    """
    
    def __init__(self, 
                 expert_pool=None, 
                 gan_generator=None, 
                 self_play_sampler=None,
                 human_style_generator=None,
                 switch_strategy='periodic',
                 switch_interval=1000,
                 distribution_weights=None,
                 random_seed=None):
        """
        初始化对手分布切换器
        
        Args:
            expert_pool: 专家策略池
            gan_generator: GAN策略生成器
            self_play_sampler: 自对弈采样器
            human_style_generator: 人类风格生成器
            switch_strategy: 切换策略，可选值为'periodic'（周期性）, 'random'（随机）, 'performance'（基于性能）
            switch_interval: 切换间隔（仅对周期性切换有效）
            distribution_weights: 分布权重，字典形式，键为分布名称，值为权重
            random_seed: 随机种子
        """
        # 设置随机种子
        if random_seed is not None:
            random.seed(random_seed)
            np.random.seed(random_seed)
        
        # 保存对手来源
        self.expert_pool = expert_pool
        self.gan_generator = gan_generator
        self.self_play_sampler = self_play_sampler
        self.human_style_generator = human_style_generator
        
        # 确定可用的分布
        self.available_distributions = []
        if expert_pool is not None:
            self.available_distributions.append('expert_pool')
        if gan_generator is not None:
            self.available_distributions.append('gan_generator')
        if self_play_sampler is not None:
            self.available_distributions.append('self_play')
        if human_style_generator is not None:
            self.available_distributions.append('human_style')
            
        if not self.available_distributions:
            raise ValueError("至少需要一种对手来源")
            
        # 设置切换策略
        self.switch_strategy = switch_strategy
        self.switch_interval = switch_interval
        
        # 设置分布权重
        if distribution_weights is None:
            # 默认均匀分布
            self.distribution_weights = {dist: 1.0 for dist in self.available_distributions}
        else:
            # 验证权重
            self.distribution_weights = {}
            for dist in self.available_distributions:
                if dist in distribution_weights:
                    self.distribution_weights[dist] = distribution_weights[dist]
                else:
                    self.distribution_weights[dist] = 0.0
                    
            # 确保至少有一个分布有非零权重
            if sum(self.distribution_weights.values()) == 0:
                self.distribution_weights = {dist: 1.0 for dist in self.available_distributions}
                
        # 归一化权重
        total_weight = sum(self.distribution_weights.values())
        self.distribution_weights = {dist: weight / total_weight for dist, weight in self.distribution_weights.items()}
        
        # 初始化状态
        self.current_distribution = self._select_distribution()
        self.steps_since_switch = 0
        self.total_steps = 0
        
        # 性能指标
        self.performance_metrics = {dist: 0.0 for dist in self.available_distributions}
        self.distribution_usage = {dist: 0 for dist in self.available_distributions}
        
        # 日志
        logger.info(f"初始化对手分布切换器，可用分布: {self.available_distributions}")
        logger.info(f"当前分布: {self.current_distribution}, 切换策略: {self.switch_strategy}")
        
    def _select_distribution(self):
        """
        选择对手分布
        
        Returns:
            str: 分布名称
        """
        if self.switch_strategy == 'random':
            # 随机选择
            distributions = list(self.distribution_weights.keys())
            weights = list(self.distribution_weights.values())
            return random.choices(distributions, weights=weights, k=1)[0]
        elif self.switch_strategy == 'performance':
            # 基于性能选择
            # 使用UCB算法平衡探索与利用
            if min(self.distribution_usage.values()) == 0:
                # 如果有分布从未使用过，优先选择
                unused = [dist for dist, usage in self.distribution_usage.items() if usage == 0]
                return random.choice(unused)
            else:
                # 计算UCB分数
                total_usage = sum(self.distribution_usage.values())
                ucb_scores = {}
                for dist in self.available_distributions:
                    # UCB公式：value + c * sqrt(ln(total_usage) / usage)
                    exploration = 0.5 * np.sqrt(np.log(total_usage) / self.distribution_usage[dist])
                    ucb_scores[dist] = self.performance_metrics[dist] + exploration
                
                # 选择UCB分数最高的分布
                return max(ucb_scores.items(), key=lambda x: x[1])[0]
        else:  # periodic
            # 周期性选择
            distributions = list(self.distribution_weights.keys())
            weights = list(self.distribution_weights.values())
            return random.choices(distributions, weights=weights, k=1)[0]
        
    def get_opponent(self, state=None, legal_actions=None):
        """
        获取当前分布下的对手
        
        Args:
            state: 当前状态（可选）
            legal_actions: 合法动作列表（可选）
            
        Returns:
            对手策略
        """
        # 更新计数器
        self.steps_since_switch += 1
        self.total_steps += 1
        self.distribution_usage[self.current_distribution] += 1
        
        # 检查是否需要切换分布
        if self.switch_strategy == 'periodic' and self.steps_since_switch >= self.switch_interval:
            self.current_distribution = self._select_distribution()
            self.steps_since_switch = 0
            logger.info(f"周期性切换对手分布，当前分布: {self.current_distribution}")
        
        # 根据当前分布获取对手
        if self.current_distribution == 'expert_pool':
            # 从专家池获取对手
            expert_name = random.choice(list(self.expert_pool.experts.keys()))
            return self.expert_pool.get_expert(expert_name)
        
        elif self.current_distribution == 'gan_generator':
            # 从GAN生成器获取对手
            style = random.choice(['random', 'diverse', 'extreme'])
            return self.gan_generator.generate_opponent_policy(style=style)
        
        elif self.current_distribution == 'self_play':
            # 从自对弈历史获取对手
            return self.self_play_sampler.sample_opponent_policy()
        
        elif self.current_distribution == 'human_style':
            # 从人类风格生成器获取对手
            style = random.choice(['random', 'conservative', 'aggressive'])
            return self.human_style_generator.generate_opponent_policy(state, style=style)
        
    def update(self, metrics=None):
        """
        更新切换器状态
        
        Args:
            metrics: 性能指标，用于基于性能的切换策略
        """
        # 如果使用基于性能的切换策略，更新性能指标
        if self.switch_strategy == 'performance' and metrics is not None:
            # 更新当前分布的性能指标
            self.performance_metrics[self.current_distribution] = metrics
            
            # 选择新的分布
            new_distribution = self._select_distribution()
            
            # 如果分布发生变化，重置计数器
            if new_distribution != self.current_distribution:
                self.current_distribution = new_distribution
                self.steps_since_switch = 0
                logger.info(f"基于性能切换对手分布，当前分布: {self.current_distribution}")
        
        # 如果使用随机切换策略，随机决定是否切换
        elif self.switch_strategy == 'random':
            # 以一定概率切换
            if random.random() < 0.01:  # 1%的概率切换
                new_distribution = self._select_distribution()
                if new_distribution != self.current_distribution:
                    self.current_distribution = new_distribution
                    self.steps_since_switch = 0
                    logger.info(f"随机切换对手分布，当前分布: {self.current_distribution}")
    
    def get_stats(self):
        """
        获取统计信息
        
        Returns:
            dict: 统计信息
        """
        return {
            'current_distribution': self.current_distribution,
            'steps_since_switch': self.steps_since_switch,
            'total_steps': self.total_steps,
            'distribution_usage': self.distribution_usage,
            'performance_metrics': self.performance_metrics
        }
