"""
游戏规则解析器模块

提供游戏规则描述语言（GRDL）的解析器，用于解析游戏规则并生成游戏描述器。
"""
from typing import Dict, Any, List, Tuple, Optional, Union
import json
import yaml
import os
import re
from pathlib import Path

from cardgame_ai.games.common.game_descriptor import GameDescriptor


class GameRules:
    """
    游戏规则类
    
    包含游戏规则的详细描述，如玩家数量、回合机制、胜利条件、牌型规则等。
    """
    
    def __init__(
        self,
        name: str,
        num_players: int,
        turn_mechanism: str,
        win_conditions: List[str],
        card_types: Optional[Dict[str, Any]] = None,
        scoring_rules: Optional[Dict[str, Any]] = None,
        special_rules: Optional[Dict[str, Any]] = None,
        players_roles: Optional[Dict[str, Any]] = None,
        custom_rules: Optional[Dict[str, Any]] = None
    ):
        """
        初始化游戏规则
        
        Args:
            name (str): 游戏名称
            num_players (int): 玩家数量
            turn_mechanism (str): 回合机制，如"sequential", "simultaneous"等
            win_conditions (List[str]): 胜利条件列表
            card_types (Optional[Dict[str, Any]], optional): 牌型规则. Defaults to None.
            scoring_rules (Optional[Dict[str, Any]], optional): 计分规则. Defaults to None.
            special_rules (Optional[Dict[str, Any]], optional): 特殊规则. Defaults to None.
            players_roles (Optional[Dict[str, Any]], optional): 玩家角色. Defaults to None.
            custom_rules (Optional[Dict[str, Any]], optional): 自定义规则. Defaults to None.
        """
        self.name = name
        self.num_players = num_players
        self.turn_mechanism = turn_mechanism
        self.win_conditions = win_conditions
        self.card_types = card_types or {}
        self.scoring_rules = scoring_rules or {}
        self.special_rules = special_rules or {}
        self.players_roles = players_roles or {}
        self.custom_rules = custom_rules or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """
        将游戏规则转换为字典
        
        Returns:
            Dict[str, Any]: 字典表示
        """
        return {
            'name': self.name,
            'num_players': self.num_players,
            'turn_mechanism': self.turn_mechanism,
            'win_conditions': self.win_conditions,
            'card_types': self.card_types,
            'scoring_rules': self.scoring_rules,
            'special_rules': self.special_rules,
            'players_roles': self.players_roles,
            'custom_rules': self.custom_rules
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'GameRules':
        """
        从字典创建游戏规则
        
        Args:
            data (Dict[str, Any]): 字典表示
            
        Returns:
            GameRules: 游戏规则对象
        """
        return cls(
            name=data.get('name', ''),
            num_players=data.get('num_players', 0),
            turn_mechanism=data.get('turn_mechanism', ''),
            win_conditions=data.get('win_conditions', []),
            card_types=data.get('card_types', {}),
            scoring_rules=data.get('scoring_rules', {}),
            special_rules=data.get('special_rules', {}),
            players_roles=data.get('players_roles', {}),
            custom_rules=data.get('custom_rules', {})
        )
    
    def generate_game_descriptor(
        self,
        state_shape: Tuple[int, ...],
        action_shape: Tuple[int, ...],
        is_zero_sum: bool = True,
        is_perfect_information: bool = False,
        is_deterministic: bool = False,
        description: Optional[str] = None
    ) -> GameDescriptor:
        """
        根据游戏规则生成游戏描述器
        
        Args:
            state_shape (Tuple[int, ...]): 状态的形状
            action_shape (Tuple[int, ...]): 动作的形状
            is_zero_sum (bool, optional): 是否为零和游戏. Defaults to True.
            is_perfect_information (bool, optional): 是否为完全信息游戏. Defaults to False.
            is_deterministic (bool, optional): 是否为确定性游戏. Defaults to False.
            description (Optional[str], optional): 游戏描述. Defaults to None.
            
        Returns:
            GameDescriptor: 游戏描述器对象
        """
        return GameDescriptor(
            name=self.name,
            num_players=self.num_players,
            is_zero_sum=is_zero_sum,
            is_perfect_information=is_perfect_information,
            is_deterministic=is_deterministic,
            rules=self.to_dict(),
            state_shape=state_shape,
            action_shape=action_shape,
            description=description
        )


class GameRulesParser:
    """
    游戏规则解析器类
    
    用于解析游戏规则描述语言（GRDL），支持JSON和YAML格式。
    """
    
    @staticmethod
    def parse_json(json_str: str) -> GameRules:
        """
        解析JSON格式的游戏规则
        
        Args:
            json_str (str): JSON字符串
            
        Returns:
            GameRules: 游戏规则对象
        """
        data = json.loads(json_str)
        return GameRules.from_dict(data)
    
    @staticmethod
    def parse_yaml(yaml_str: str) -> GameRules:
        """
        解析YAML格式的游戏规则
        
        Args:
            yaml_str (str): YAML字符串
            
        Returns:
            GameRules: 游戏规则对象
        """
        data = yaml.safe_load(yaml_str)
        return GameRules.from_dict(data)
    
    @staticmethod
    def parse_file(file_path: str) -> GameRules:
        """
        从文件解析游戏规则
        
        Args:
            file_path (str): 文件路径
            
        Returns:
            GameRules: 游戏规则对象
            
        Raises:
            ValueError: 不支持的文件格式
        """
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if file_path.endswith('.json'):
            return GameRulesParser.parse_json(content)
        elif file_path.endswith('.yaml') or file_path.endswith('.yml'):
            return GameRulesParser.parse_yaml(content)
        else:
            raise ValueError(f"不支持的文件格式: {file_path}")
    
    @staticmethod
    def generate_rule_template(game_type: str, output_path: Optional[str] = None) -> str:
        """
        生成游戏规则模板
        
        Args:
            game_type (str): 游戏类型，如"card_game", "board_game", "mahjong", "poker"等
            output_path (Optional[str], optional): 输出路径. Defaults to None.
            
        Returns:
            str: 模板内容
            
        Raises:
            ValueError: 不支持的游戏类型
        """
        templates = {
            'card_game': {
                'name': '卡牌游戏',
                'num_players': 2,
                'turn_mechanism': 'sequential',
                'win_conditions': ['所有玩家牌出完'],
                'card_types': {
                    'basic': ['单张', '对子', '三张'],
                    'advanced': ['顺子', '连对', '炸弹']
                },
                'scoring_rules': {
                    'win': 1,
                    'lose': -1
                },
                'special_rules': {},
                'players_roles': {},
                'custom_rules': {}
            },
            'mahjong': {
                'name': '麻将',
                'num_players': 4,
                'turn_mechanism': 'sequential',
                'win_conditions': ['和牌'],
                'card_types': {
                    'basic': ['筒子', '条子', '万子', '风牌', '箭牌'],
                    'advanced': ['碰', '杠', '吃']
                },
                'scoring_rules': {
                    'basic': {'和牌': 1, '自摸': 2},
                    'advanced': {'七对': 4, '清一色': 6}
                },
                'special_rules': {'杠上开花': True, '抢杠和': True},
                'players_roles': {},
                'custom_rules': {}
            },
            'poker': {
                'name': '德州扑克',
                'num_players': 9,
                'turn_mechanism': 'sequential',
                'win_conditions': ['所有其他玩家弃牌', '摊牌赢得最大牌型'],
                'card_types': {
                    'basic': ['高牌', '一对', '两对', '三条'],
                    'advanced': ['顺子', '同花', '葫芦', '四条', '同花顺', '皇家同花顺']
                },
                'scoring_rules': {'pot': '奖池机制'},
                'special_rules': {'blind': '大小盲注', 'betting_rounds': ['preflop', 'flop', 'turn', 'river']},
                'players_roles': {},
                'custom_rules': {'ante': 0, 'small_blind': 1, 'big_blind': 2}
            }
        }
        
        if game_type not in templates:
            raise ValueError(f"不支持的游戏类型: {game_type}，支持的类型有: {list(templates.keys())}")
        
        template = templates[game_type]
        template_str = yaml.dump(template, default_flow_style=False, allow_unicode=True)
        
        if output_path:
            os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(template_str)
        
        return template_str
    
    @staticmethod
    def validate_rules(rules: GameRules) -> Tuple[bool, List[str]]:
        """
        验证游戏规则是否有效
        
        Args:
            rules (GameRules): 游戏规则对象
            
        Returns:
            Tuple[bool, List[str]]: 是否有效，错误信息列表
        """
        errors = []
        
        # 验证基本字段
        if not rules.name:
            errors.append("游戏名称不能为空")
        
        if rules.num_players <= 0:
            errors.append("玩家数量必须大于0")
        
        if not rules.turn_mechanism:
            errors.append("回合机制不能为空")
        
        if not rules.win_conditions:
            errors.append("胜利条件不能为空")
        
        # 验证游戏特定规则
        if 'mahjong' in rules.name.lower():
            if 'basic' not in rules.card_types:
                errors.append("麻将游戏必须定义基本牌型")
        
        if 'poker' in rules.name.lower():
            if 'betting_rounds' not in rules.special_rules:
                errors.append("德州扑克游戏必须定义下注轮次")
        
        return len(errors) == 0, errors


class GameRulesDSL:
    """
    游戏规则DSL解析器类
    
    用于解析自定义DSL格式的游戏规则，提供更灵活的规则描述方式。
    """
    
    @staticmethod
    def parse(dsl_str: str) -> GameRules:
        """
        解析DSL格式的游戏规则
        
        Args:
            dsl_str (str): DSL字符串
            
        Returns:
            GameRules: 游戏规则对象
        """
        # 解析游戏名称
        name_match = re.search(r'Game\s+name:\s*(.+)', dsl_str)
        name = name_match.group(1).strip() if name_match else ""
        
        # 解析玩家数量
        players_match = re.search(r'Players:\s*(\d+)', dsl_str)
        num_players = int(players_match.group(1)) if players_match else 0
        
        # 解析回合机制
        turn_match = re.search(r'Turn\s+mechanism:\s*(.+)', dsl_str)
        turn_mechanism = turn_match.group(1).strip() if turn_match else ""
        
        # 解析胜利条件
        win_conditions = []
        win_block_match = re.search(r'Win\s+conditions:\s*\{(.*?)\}', dsl_str, re.DOTALL)
        if win_block_match:
            win_block = win_block_match.group(1)
            for line in win_block.strip().split('\n'):
                line = line.strip()
                if line and not line.startswith('#'):
                    win_conditions.append(line)
        
        # 解析牌型规则
        card_types = {}
        card_block_match = re.search(r'Card\s+types:\s*\{(.*?)\}', dsl_str, re.DOTALL)
        if card_block_match:
            card_block = card_block_match.group(1)
            current_category = "default"
            for line in card_block.strip().split('\n'):
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                
                category_match = re.search(r'(\w+):\s*\{', line)
                if category_match:
                    current_category = category_match.group(1)
                    card_types[current_category] = []
                elif line != '}':
                    card_types.setdefault(current_category, []).append(line)
        
        # 解析计分规则
        scoring_rules = {}
        scoring_block_match = re.search(r'Scoring\s+rules:\s*\{(.*?)\}', dsl_str, re.DOTALL)
        if scoring_block_match:
            scoring_block = scoring_block_match.group(1)
            for line in scoring_block.strip().split('\n'):
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                
                rule_match = re.search(r'(\w+):\s*(.+)', line)
                if rule_match:
                    key, value = rule_match.group(1), rule_match.group(2)
                    try:
                        value = int(value)
                    except ValueError:
                        pass
                    scoring_rules[key] = value
        
        # 解析特殊规则
        special_rules = {}
        special_block_match = re.search(r'Special\s+rules:\s*\{(.*?)\}', dsl_str, re.DOTALL)
        if special_block_match:
            special_block = special_block_match.group(1)
            for line in special_block.strip().split('\n'):
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                
                rule_match = re.search(r'(\w+):\s*(.+)', line)
                if rule_match:
                    key, value = rule_match.group(1), rule_match.group(2)
                    if value.lower() in ['true', 'yes', '1']:
                        value = True
                    elif value.lower() in ['false', 'no', '0']:
                        value = False
                    else:
                        try:
                            value = int(value)
                        except ValueError:
                            pass
                    special_rules[key] = value
        
        # 解析玩家角色
        players_roles = {}
        roles_block_match = re.search(r'Players\s+roles:\s*\{(.*?)\}', dsl_str, re.DOTALL)
        if roles_block_match:
            roles_block = roles_block_match.group(1)
            for line in roles_block.strip().split('\n'):
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                
                role_match = re.search(r'(\w+):\s*(.+)', line)
                if role_match:
                    key, value = role_match.group(1), role_match.group(2)
                    players_roles[key] = value
        
        # 解析自定义规则
        custom_rules = {}
        custom_block_match = re.search(r'Custom\s+rules:\s*\{(.*?)\}', dsl_str, re.DOTALL)
        if custom_block_match:
            custom_block = custom_block_match.group(1)
            for line in custom_block.strip().split('\n'):
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                
                rule_match = re.search(r'(\w+):\s*(.+)', line)
                if rule_match:
                    key, value = rule_match.group(1), rule_match.group(2)
                    try:
                        value = eval(value)
                    except:
                        pass
                    custom_rules[key] = value
        
        return GameRules(
            name=name,
            num_players=num_players,
            turn_mechanism=turn_mechanism,
            win_conditions=win_conditions,
            card_types=card_types,
            scoring_rules=scoring_rules,
            special_rules=special_rules,
            players_roles=players_roles,
            custom_rules=custom_rules
        )
    
    @staticmethod
    def generate_dsl(rules: GameRules) -> str:
        """
        根据游戏规则生成DSL字符串
        
        Args:
            rules (GameRules): 游戏规则对象
            
        Returns:
            str: DSL字符串
        """
        dsl = []
        
        dsl.append(f"Game name: {rules.name}")
        dsl.append(f"Players: {rules.num_players}")
        dsl.append(f"Turn mechanism: {rules.turn_mechanism}")
        
        dsl.append("Win conditions: {")
        for condition in rules.win_conditions:
            dsl.append(f"    {condition}")
        dsl.append("}")
        
        dsl.append("Card types: {")
        for category, types in rules.card_types.items():
            dsl.append(f"    {category}: {{")
            for card_type in types:
                dsl.append(f"        {card_type}")
            dsl.append("    }")
        dsl.append("}")
        
        dsl.append("Scoring rules: {")
        for rule, value in rules.scoring_rules.items():
            dsl.append(f"    {rule}: {value}")
        dsl.append("}")
        
        dsl.append("Special rules: {")
        for rule, value in rules.special_rules.items():
            dsl.append(f"    {rule}: {value}")
        dsl.append("}")
        
        dsl.append("Players roles: {")
        for role, desc in rules.players_roles.items():
            dsl.append(f"    {role}: {desc}")
        dsl.append("}")
        
        dsl.append("Custom rules: {")
        for rule, value in rules.custom_rules.items():
            dsl.append(f"    {rule}: {repr(value)}")
        dsl.append("}")
        
        return "\n".join(dsl) 