# EfficientZero算法配置模板
algorithm:
  name: "EfficientZero"
  version: "1.0"

model:
  representation_network:
    hidden_size: 512
    num_blocks: 16
    downsample: true

  dynamics_network:
    hidden_size: 512
    num_blocks: 16

  prediction_network:
    hidden_size: 512
    num_blocks: 2

training:
  replay_buffer_size: {{ REPLAY_BUFFER_SIZE | default(100000) }}
  batch_size: {{ BATCH_SIZE }}
  unroll_steps: {{ UNROLL_STEPS | default(5) }}
  td_steps: {{ TD_STEPS | default(5) }}

  # 损失函数权重
  value_loss_weight: 0.25
  reward_loss_weight: 1.0
  policy_loss_weight: 1.0
  consistency_loss_weight: 2.0

mcts:
  num_simulations: {{ MCTS_SIMULATIONS }}
  c_puct: 1.25
  dirichlet_alpha: 0.3
  exploration_fraction: 0.25

reanalyze:
  ratio: {{ REANALYZE_RATIO | default(0.5) }}
  buffer_size: {{ MODEL_BUFFER_SIZE | default(50000) }}
