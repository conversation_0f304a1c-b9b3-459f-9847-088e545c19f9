"""
游戏扩展机制模块

提供动态加载和注册新游戏的功能，支持插件式架构，
允许在不修改核心代码的情况下扩展游戏种类。
"""
from typing import Dict, Any, List, Tuple, Optional, Union, Type, Callable
import os
import sys
import importlib
import importlib.util
import inspect
import json
import logging
from abc import ABC, abstractmethod
from pathlib import Path

from .game_config import GameConfig, ConfigurableGame


# 配置日志
logger = logging.getLogger(__name__)


class GamePlugin(ABC):
    """
    游戏插件基类
    
    所有游戏插件都必须继承此类
    """
    
    @property
    @abstractmethod
    def name(self) -> str:
        """
        获取游戏名称
        
        Returns:
            str: 游戏名称
        """
        pass
    
    @property
    @abstractmethod
    def version(self) -> str:
        """
        获取游戏版本
        
        Returns:
            str: 游戏版本
        """
        pass
    
    @property
    @abstractmethod
    def description(self) -> str:
        """
        获取游戏描述
        
        Returns:
            str: 游戏描述
        """
        pass
    
    @abstractmethod
    def create_game(self, config: Optional[GameConfig] = None) -> ConfigurableGame:
        """
        创建游戏实例
        
        Args:
            config (Optional[GameConfig], optional): 游戏配置. Defaults to None.
            
        Returns:
            ConfigurableGame: 游戏实例
        """
        pass
    
    @abstractmethod
    def get_default_config(self) -> GameConfig:
        """
        获取默认配置
        
        Returns:
            GameConfig: 默认游戏配置
        """
        pass


class GameExpansionManager:
    """
    游戏扩展管理器
    
    负责管理和加载游戏扩展
    """
    
    _instance = None
    
    def __new__(cls):
        """
        单例模式实现
        
        Returns:
            GameExpansionManager: 单例实例
        """
        if cls._instance is None:
            cls._instance = super(GameExpansionManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """
        初始化游戏扩展管理器
        """
        if self._initialized:
            return
        
        self._plugins: Dict[str, GamePlugin] = {}
        self._plugin_paths: List[str] = []
        self._hooks: Dict[str, List[Callable]] = {
            'pre_load': [],
            'post_load': [],
            'pre_create': [],
            'post_create': []
        }
        self._initialized = True
    
    def register_plugin(self, plugin: GamePlugin) -> bool:
        """
        注册游戏插件
        
        Args:
            plugin (GamePlugin): 游戏插件
            
        Returns:
            bool: 注册是否成功
        """
        if not isinstance(plugin, GamePlugin):
            logger.error(f"无法注册插件：{plugin} 不是有效的 GamePlugin 实例")
            return False
        
        plugin_name = plugin.name
        
        if plugin_name in self._plugins:
            logger.warning(f"插件 '{plugin_name}' 已存在，将覆盖旧版本")
        
        self._plugins[plugin_name] = plugin
        logger.info(f"已注册游戏插件：{plugin_name} v{plugin.version}")
        return True
    
    def unregister_plugin(self, plugin_name: str) -> bool:
        """
        取消注册游戏插件
        
        Args:
            plugin_name (str): 游戏插件名称
            
        Returns:
            bool: 取消注册是否成功
        """
        if plugin_name not in self._plugins:
            logger.warning(f"插件 '{plugin_name}' 不存在，无法取消注册")
            return False
        
        del self._plugins[plugin_name]
        logger.info(f"已取消注册游戏插件：{plugin_name}")
        return True
    
    def get_plugin(self, plugin_name: str) -> Optional[GamePlugin]:
        """
        获取游戏插件
        
        Args:
            plugin_name (str): 游戏插件名称
            
        Returns:
            Optional[GamePlugin]: 游戏插件实例，如果不存在则返回None
        """
        return self._plugins.get(plugin_name)
    
    def get_all_plugins(self) -> Dict[str, GamePlugin]:
        """
        获取所有游戏插件
        
        Returns:
            Dict[str, GamePlugin]: 游戏插件字典
        """
        return self._plugins.copy()
    
    def add_plugin_path(self, path: str) -> bool:
        """
        添加插件搜索路径
        
        Args:
            path (str): 插件搜索路径
            
        Returns:
            bool: 添加是否成功
        """
        if not os.path.exists(path):
            logger.error(f"插件路径不存在：{path}")
            return False
        
        if not os.path.isdir(path):
            logger.error(f"插件路径不是目录：{path}")
            return False
        
        if path in self._plugin_paths:
            logger.warning(f"插件路径已存在：{path}")
            return False
        
        self._plugin_paths.append(path)
        logger.info(f"已添加插件搜索路径：{path}")
        return True
    
    def remove_plugin_path(self, path: str) -> bool:
        """
        移除插件搜索路径
        
        Args:
            path (str): 插件搜索路径
            
        Returns:
            bool: 移除是否成功
        """
        if path not in self._plugin_paths:
            logger.warning(f"插件路径不存在：{path}")
            return False
        
        self._plugin_paths.remove(path)
        logger.info(f"已移除插件搜索路径：{path}")
        return True
    
    def get_plugin_paths(self) -> List[str]:
        """
        获取所有插件搜索路径
        
        Returns:
            List[str]: 插件搜索路径列表
        """
        return self._plugin_paths.copy()
    
    def discover_plugins(self) -> int:
        """
        发现并加载所有插件
        
        Returns:
            int: 加载的插件数量
        """
        count = 0
        
        for path in self._plugin_paths:
            count += self._load_plugins_from_path(path)
        
        return count
    
    def _load_plugins_from_path(self, path: str) -> int:
        """
        从路径加载插件
        
        Args:
            path (str): 插件路径
            
        Returns:
            int: 加载的插件数量
        """
        count = 0
        
        # 执行加载前钩子
        self._run_hooks('pre_load', path)
        
        # 遍历目录
        for item in os.listdir(path):
            item_path = os.path.join(path, item)
            
            # 如果是目录，检查是否包含插件清单
            if os.path.isdir(item_path):
                manifest_path = os.path.join(item_path, 'plugin.json')
                if os.path.exists(manifest_path):
                    if self._load_plugin_from_manifest(manifest_path):
                        count += 1
            
            # 如果是Python文件，尝试作为插件加载
            elif item.endswith('.py') and not item.startswith('_'):
                if self._load_plugin_from_file(item_path):
                    count += 1
        
        # 执行加载后钩子
        self._run_hooks('post_load', path, count)
        
        return count
    
    def _load_plugin_from_manifest(self, manifest_path: str) -> bool:
        """
        从清单文件加载插件
        
        Args:
            manifest_path (str): 清单文件路径
            
        Returns:
            bool: 加载是否成功
        """
        try:
            with open(manifest_path, 'r', encoding='utf-8') as f:
                manifest = json.load(f)
            
            plugin_dir = os.path.dirname(manifest_path)
            
            # 验证清单格式
            if 'name' not in manifest or 'main' not in manifest:
                logger.error(f"插件清单格式无效：{manifest_path}")
                return False
            
            # 加载主模块
            main_file = os.path.join(plugin_dir, manifest['main'])
            if not os.path.exists(main_file):
                logger.error(f"插件主文件不存在：{main_file}")
                return False
            
            # 动态导入模块
            module_name = f"game_plugin_{manifest['name']}"
            spec = importlib.util.spec_from_file_location(module_name, main_file)
            if spec is None:
                logger.error(f"无法从文件创建模块规范：{main_file}")
                return False
            
            module = importlib.util.module_from_spec(spec)
            sys.modules[module_name] = module
            spec.loader.exec_module(module)
            
            # 查找插件类
            plugin_class = None
            for name, obj in inspect.getmembers(module):
                if (inspect.isclass(obj) and 
                    issubclass(obj, GamePlugin) and 
                    obj is not GamePlugin):
                    plugin_class = obj
                    break
            
            if plugin_class is None:
                logger.error(f"在模块 {module_name} 中未找到 GamePlugin 的子类")
                return False
            
            # 实例化并注册插件
            plugin = plugin_class()
            return self.register_plugin(plugin)
            
        except Exception as e:
            logger.error(f"加载插件清单失败：{manifest_path}, 错误：{str(e)}")
            return False
    
    def _load_plugin_from_file(self, file_path: str) -> bool:
        """
        从文件加载插件
        
        Args:
            file_path (str): 文件路径
            
        Returns:
            bool: 加载是否成功
        """
        try:
            # 提取模块名
            module_name = os.path.basename(file_path)[:-3]  # 移除 .py 扩展名
            module_name = f"game_plugin_{module_name}"
            
            # 动态导入模块
            spec = importlib.util.spec_from_file_location(module_name, file_path)
            if spec is None:
                logger.error(f"无法从文件创建模块规范：{file_path}")
                return False
            
            module = importlib.util.module_from_spec(spec)
            sys.modules[module_name] = module
            spec.loader.exec_module(module)
            
            # 查找插件类
            plugin_class = None
            for name, obj in inspect.getmembers(module):
                if (inspect.isclass(obj) and 
                    issubclass(obj, GamePlugin) and 
                    obj is not GamePlugin):
                    plugin_class = obj
                    break
            
            if plugin_class is None:
                logger.debug(f"在模块 {module_name} 中未找到 GamePlugin 的子类")
                return False
            
            # 实例化并注册插件
            plugin = plugin_class()
            return self.register_plugin(plugin)
            
        except Exception as e:
            logger.error(f"加载插件文件失败：{file_path}, 错误：{str(e)}")
            return False
    
    def create_game(self, plugin_name: str, config: Optional[GameConfig] = None) -> Optional[ConfigurableGame]:
        """
        创建游戏实例
        
        Args:
            plugin_name (str): 插件名称
            config (Optional[GameConfig], optional): 游戏配置. Defaults to None.
            
        Returns:
            Optional[ConfigurableGame]: 游戏实例，如果创建失败则返回None
        """
        plugin = self.get_plugin(plugin_name)
        if plugin is None:
            logger.error(f"插件 '{plugin_name}' 不存在")
            return None
        
        try:
            # 执行创建前钩子
            self._run_hooks('pre_create', plugin_name)
            
            # 创建游戏实例
            game = plugin.create_game(config)
            
            # 执行创建后钩子
            self._run_hooks('post_create', plugin_name, game)
            
            return game
        except Exception as e:
            logger.error(f"创建游戏 '{plugin_name}' 失败：{str(e)}")
            return None
    
    def register_hook(self, hook_type: str, callback: Callable) -> bool:
        """
        注册钩子函数
        
        Args:
            hook_type (str): 钩子类型
            callback (Callable): 回调函数
            
        Returns:
            bool: 注册是否成功
        """
        if hook_type not in self._hooks:
            logger.error(f"钩子类型 '{hook_type}' 不存在")
            return False
        
        self._hooks[hook_type].append(callback)
        return True
    
    def unregister_hook(self, hook_type: str, callback: Callable) -> bool:
        """
        取消注册钩子函数
        
        Args:
            hook_type (str): 钩子类型
            callback (Callable): 回调函数
            
        Returns:
            bool: 取消注册是否成功
        """
        if hook_type not in self._hooks:
            logger.error(f"钩子类型 '{hook_type}' 不存在")
            return False
        
        if callback not in self._hooks[hook_type]:
            logger.warning(f"回调函数不在 '{hook_type}' 钩子中")
            return False
        
        self._hooks[hook_type].remove(callback)
        return True
    
    def _run_hooks(self, hook_type: str, *args, **kwargs) -> None:
        """
        运行钩子函数
        
        Args:
            hook_type (str): 钩子类型
            *args: 位置参数
            **kwargs: 关键字参数
        """
        if hook_type not in self._hooks:
            return
        
        for callback in self._hooks[hook_type]:
            try:
                callback(*args, **kwargs)
            except Exception as e:
                logger.error(f"运行钩子 '{hook_type}' 失败：{str(e)}")


class PluginMetadata:
    """
    插件元数据类
    
    用于描述插件的基本信息
    """
    
    def __init__(self, 
                 name: str, 
                 version: str, 
                 description: str, 
                 author: str = "", 
                 website: str = "", 
                 dependencies: List[Dict[str, str]] = None):
        """
        初始化插件元数据
        
        Args:
            name (str): 插件名称
            version (str): 插件版本
            description (str): 插件描述
            author (str, optional): 作者. Defaults to "".
            website (str, optional): 网站. Defaults to "".
            dependencies (List[Dict[str, str]], optional): 依赖项. Defaults to None.
        """
        self.name = name
        self.version = version
        self.description = description
        self.author = author
        self.website = website
        self.dependencies = dependencies or []
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典
        
        Returns:
            Dict[str, Any]: 元数据字典
        """
        return {
            "name": self.name,
            "version": self.version,
            "description": self.description,
            "author": self.author,
            "website": self.website,
            "dependencies": self.dependencies
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PluginMetadata':
        """
        从字典创建插件元数据
        
        Args:
            data (Dict[str, Any]): 元数据字典
            
        Returns:
            PluginMetadata: 插件元数据对象
        """
        return cls(
            name=data.get("name", ""),
            version=data.get("version", "0.1.0"),
            description=data.get("description", ""),
            author=data.get("author", ""),
            website=data.get("website", ""),
            dependencies=data.get("dependencies", [])
        )
    
    def save_to_file(self, path: str) -> bool:
        """
        保存元数据到文件
        
        Args:
            path (str): 文件路径
            
        Returns:
            bool: 保存是否成功
        """
        try:
            with open(path, 'w', encoding='utf-8') as f:
                json.dump(self.to_dict(), f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            logger.error(f"保存元数据失败：{str(e)}")
            return False
    
    @classmethod
    def load_from_file(cls, path: str) -> Optional['PluginMetadata']:
        """
        从文件加载元数据
        
        Args:
            path (str): 文件路径
            
        Returns:
            Optional[PluginMetadata]: 插件元数据对象，如果加载失败则返回None
        """
        try:
            with open(path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return cls.from_dict(data)
        except Exception as e:
            logger.error(f"加载元数据失败：{str(e)}")
            return None


def create_plugin_template(output_dir: str, metadata: PluginMetadata) -> bool:
    """
    创建插件模板
    
    Args:
        output_dir (str): 输出目录
        metadata (PluginMetadata): 插件元数据
        
    Returns:
        bool: 创建是否成功
    """
    try:
        # 创建插件目录
        plugin_dir = os.path.join(output_dir, metadata.name)
        os.makedirs(plugin_dir, exist_ok=True)
        
        # 创建元数据文件
        metadata_path = os.path.join(plugin_dir, 'plugin.json')
        metadata_dict = metadata.to_dict()
        metadata_dict['main'] = 'plugin.py'  # 设置主文件
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata_dict, f, indent=4, ensure_ascii=False)
        
        # 创建主文件
        main_path = os.path.join(plugin_dir, 'plugin.py')
        with open(main_path, 'w', encoding='utf-8') as f:
            f.write(f'''"""
{metadata.name} 插件

{metadata.description}

版本: {metadata.version}
作者: {metadata.author}
网站: {metadata.website}
"""
from typing import Optional
from cardgame_ai.games.common.game_config import GameConfig, ConfigurableGame
from cardgame_ai.games.common.game_expansion import GamePlugin


class {metadata.name.title().replace(" ", "")}Game(ConfigurableGame):
    """
    {metadata.name} 游戏实现
    """
    
    def __init__(self, config: Optional[GameConfig] = None):
        """
        初始化游戏
        
        Args:
            config (Optional[GameConfig], optional): 游戏配置. Defaults to None.
        """
        self._config = config or {metadata.name.title().replace(" ", "")}Plugin().get_default_config()
    
    def get_config(self) -> GameConfig:
        """
        获取游戏配置
        
        Returns:
            GameConfig: 游戏配置
        """
        return self._config
    
    def apply_config(self, config: GameConfig) -> None:
        """
        应用游戏配置
        
        Args:
            config (GameConfig): 游戏配置
        """
        if self.validate_config(config):
            self._config = config
    
    def validate_config(self, config: GameConfig) -> bool:
        """
        验证游戏配置是否有效
        
        Args:
            config (GameConfig): 游戏配置
            
        Returns:
            bool: 如果配置有效则返回True
        """
        # 在这里实现配置验证逻辑
        return True


class {metadata.name.title().replace(" ", "")}Plugin(GamePlugin):
    """
    {metadata.name} 插件实现
    """
    
    @property
    def name(self) -> str:
        """
        获取游戏名称
        
        Returns:
            str: 游戏名称
        """
        return "{metadata.name}"
    
    @property
    def version(self) -> str:
        """
        获取游戏版本
        
        Returns:
            str: 游戏版本
        """
        return "{metadata.version}"
    
    @property
    def description(self) -> str:
        """
        获取游戏描述
        
        Returns:
            str: 游戏描述
        """
        return "{metadata.description}"
    
    def create_game(self, config: Optional[GameConfig] = None) -> ConfigurableGame:
        """
        创建游戏实例
        
        Args:
            config (Optional[GameConfig], optional): 游戏配置. Defaults to None.
            
        Returns:
            ConfigurableGame: 游戏实例
        """
        return {metadata.name.title().replace(" ", "")}Game(config or self.get_default_config())
    
    def get_default_config(self) -> GameConfig:
        """
        获取默认配置
        
        Returns:
            GameConfig: 默认游戏配置
        """
        # 在这里创建默认配置
        return GameConfig.create_template('generic')
''')
        
        # 创建README文件
        readme_path = os.path.join(plugin_dir, 'README.md')
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(f'''# {metadata.name}

{metadata.description}

## 版本

{metadata.version}

## 作者

{metadata.author}

## 网站

{metadata.website}

## 安装

1. 将此目录复制到游戏的插件目录中
2. 重启游戏或重新加载插件

## 使用方法

[在这里添加使用说明]
''')
        
        return True
    except Exception as e:
        logger.error(f"创建插件模板失败：{str(e)}")
        return False


# 获取全局扩展管理器实例
def get_expansion_manager() -> GameExpansionManager:
    """
    获取全局扩展管理器实例
    
    Returns:
        GameExpansionManager: 扩展管理器实例
    """
    return GameExpansionManager() 