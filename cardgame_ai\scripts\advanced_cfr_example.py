#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
高级CFR示例脚本

展示如何使用高级CFR算法(CFR+和DCFR)生成和利用博弈论最优(GTO)策略。
"""

import os
import sys
import argparse
import logging
import time
import matplotlib.pyplot as plt
import numpy as np
from typing import Dict, List, Any, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from cardgame_ai.algorithms.gto_approximation import GTOPolicy, SimplifiedCFR, AdvancedCFR
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.games.doudizhu.state import DouDizhuState

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('advanced_cfr_example.log')
    ]
)
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='高级CFR示例')
    
    parser.add_argument('--algorithm', type=str, default='cfr+',
                       choices=['simplified', 'cfr+', 'dcfr'],
                       help='CFR算法变体：simplified(简化CFR), cfr+(CFR+), dcfr(折扣CFR)')
    parser.add_argument('--max_iterations', type=int, default=1000,
                       help='CFR迭代次数')
    parser.add_argument('--output_path', type=str, default='data/gto_policies/',
                       help='GTO策略保存目录')
    parser.add_argument('--compare', action='store_true',
                       help='比较不同算法的收敛速度')
    parser.add_argument('--pruning_threshold', type=float, default=0.0,
                       help='剪枝阈值')
    
    # DCFR参数
    parser.add_argument('--alpha_regret', type=float, default=1.5,
                       help='DCFR的遗憾递增指数')
    parser.add_argument('--beta_strategy', type=float, default=0.5,
                       help='DCFR的策略递增指数')
    parser.add_argument('--gamma_discount', type=float, default=2.0,
                       help='DCFR的折扣参数')
    
    return parser.parse_args()


def state_feature_extractor(state: DouDizhuState) -> str:
    """
    状态特征提取器
    
    将状态转换为特征字符串，用于索引GTO策略。
    
    Args:
        state: 斗地主游戏状态
        
    Returns:
        状态特征字符串
    """
    # 简化的特征提取，实际应用中可能需要更复杂的特征
    features = []
    
    # 添加当前玩家
    features.append(f"player:{state.current_player}")
    
    # 添加地主
    features.append(f"landlord:{state.landlord}")
    
    # 添加手牌数量
    for i, hand in enumerate(state.hands):
        features.append(f"hand{i}:{len(hand)}")
    
    # 添加上一手牌
    if state.last_move:
        features.append(f"last_move:{state.last_move.type.name}")
    else:
        features.append("last_move:None")
    
    # 添加上一个出牌的玩家
    features.append(f"last_player:{state.last_player}")
    
    # 添加连续不出的次数
    features.append(f"num_passes:{state.num_passes}")
    
    return "|".join(features)


def create_toy_game_cfr(algorithm='cfr+', max_iterations=1000, **kwargs):
    """
    创建一个简化的游戏并使用CFR算法计算GTO策略
    
    Args:
        algorithm: CFR算法变体
        max_iterations: 最大迭代次数
        **kwargs: 算法特定参数
        
    Returns:
        训练得到的GTO策略和训练历史
    """
    logger.info(f"创建简化游戏并使用{algorithm}算法训练GTO策略")
    
    # 定义一个简单的扑克游戏
    # 在这个游戏中，有4种动作：弃牌(0)，跟注(1)，加注(2)，全压(3)
    num_actions = 4
    
    # 状态表示：当前轮数|当前玩家|筹码量|底池大小|前一个动作
    # 例如：1|0|100|20|1 表示第1轮，玩家0，筹码100，底池20，前一个动作是跟注
    
    # 定义需要的函数
    def is_terminal(state_key: str) -> bool:
        """判断状态是否为终止状态"""
        parts = state_key.split('|')
        round_num = int(parts[0])
        prev_action = int(parts[4])
        
        # 如果有人弃牌或者到了最后一轮，则为终止状态
        if prev_action == 0 or round_num >= 3:
            return True
        return False
    
    def get_legal_actions(state_key: str) -> List[int]:
        """获取合法动作"""
        parts = state_key.split('|')
        round_num = int(parts[0])
        chips = int(parts[2])
        pot = int(parts[3])
        prev_action = int(parts[4])
        
        # 如果是终止状态，没有合法动作
        if is_terminal(state_key):
            return []
            
        # 所有状态下都可以弃牌和跟注
        legal_actions = [0, 1]
        
        # 如果筹码足够，可以加注和全押
        if chips > 0:
            legal_actions.append(2)  # 加注
            legal_actions.append(3)  # 全压
            
        return legal_actions
    
    def get_next_state(state_key: str, action: int) -> str:
        """获取下一个状态"""
        parts = state_key.split('|')
        round_num = int(parts[0])
        player = int(parts[1])
        chips = int(parts[2])
        pot = int(parts[3])
        
        # 如果是弃牌，直接到终止状态
        if action == 0:
            return f"{round_num}|{1-player}|{chips}|{pot}|{action}"
            
        # 如果是跟注，进入下一轮
        if action == 1:
            return f"{round_num+1}|{1-player}|{chips}|{pot}|{action}"
            
        # 如果是加注，减少筹码，增加底池
        if action == 2:
            new_chips = chips - 10
            new_pot = pot + 10
            return f"{round_num}|{1-player}|{new_chips}|{new_pot}|{action}"
            
        # 如果是全押，筹码为0，底池增加
        if action == 3:
            new_pot = pot + chips
            return f"{round_num}|{1-player}|0|{new_pot}|{action}"
            
        # 默认情况
        return state_key
    
    def utility_function(state_key: str, action: int) -> float:
        """效用函数"""
        if not is_terminal(state_key):
            return 0.0
            
        parts = state_key.split('|')
        player = int(parts[1])
        pot = int(parts[3])
        prev_action = int(parts[4])
        
        # 如果是弃牌，当前玩家获得底池
        if prev_action == 0:
            return pot if player == 0 else -pot
            
        # 如果到了终局，随机决定胜负
        # 在实际应用中，这里应该有更复杂的逻辑
        # 这里简化为50%的胜率
        return pot / 2 if player == 0 else -pot / 2
    
    # 收敛历史
    convergence_history = {
        'iterations': [],
        'exploitability': []
    }
    
    # 定义回调函数，用于记录收敛历史
    def callback(iteration: int, current_policy: GTOPolicy) -> bool:
        # 每50次迭代记录一次
        if (iteration + 1) % 50 == 0 or iteration == 0:
            if algorithm == 'simplified':
                # 对于simplified CFR，没有内置的计算exploitability的方法
                # 这里简单地使用迭代次数作为代理指标
                exploitability = 1.0 / (iteration + 1)
            else:
                # 使用高级CFR的exploitability计算
                exploitability = cfr.compute_exploitability(current_policy, initial_state)
                
            convergence_history['iterations'].append(iteration + 1)
            convergence_history['exploitability'].append(exploitability)
            
            logger.info(f"迭代 {iteration + 1}, Exploitability: {exploitability:.6f}")
        
        # 返回True表示继续训练
        return True
    
    # 初始状态：第1轮，玩家0，筹码100，底池20，前一个动作是跟注
    initial_state = "1|0|100|20|1"
    
    # 根据选择的算法创建CFR实例
    if algorithm == 'simplified':
        cfr = SimplifiedCFR(
            num_actions=num_actions,
            utility_function=utility_function,
            is_terminal=is_terminal,
            get_legal_actions=get_legal_actions,
            get_next_state=get_next_state,
            max_iterations=max_iterations
        )
        # SimplifiedCFR不支持回调
        gto_policy = cfr.train(initial_state)
        
        # 手动计算收敛历史
        for i in range(0, max_iterations, 50):
            iteration = i if i > 0 else 1
            exploitability = 1.0 / iteration
            convergence_history['iterations'].append(iteration)
            convergence_history['exploitability'].append(exploitability)
            
    else:  # 'cfr+' 或 'dcfr'
        cfr = AdvancedCFR(
            num_actions=num_actions,
            utility_function=utility_function,
            is_terminal=is_terminal,
            get_legal_actions=get_legal_actions,
            get_next_state=get_next_state,
            algorithm=algorithm,
            max_iterations=max_iterations,
            pruning_threshold=kwargs.get('pruning_threshold', 0.0),
            alpha_regret=kwargs.get('alpha_regret', 1.5),
            beta_strategy=kwargs.get('beta_strategy', 0.5),
            gamma_discount=kwargs.get('gamma_discount', 2.0)
        )
        
        # 训练并使用回调函数记录收敛历史
        gto_policy = cfr.train(initial_state, callback)
    
    return gto_policy, convergence_history


def compare_algorithms(args):
    """
    比较不同CFR算法的收敛速度
    
    Args:
        args: 命令行参数
    """
    logger.info("比较不同CFR算法的收敛速度")
    
    # 设置参数
    max_iterations = args.max_iterations
    pruning_threshold = args.pruning_threshold
    
    # DCFR参数
    dcfr_params = {
        'alpha_regret': args.alpha_regret,
        'beta_strategy': args.beta_strategy,
        'gamma_discount': args.gamma_discount
    }
    
    # 运行不同的算法
    algorithms = ['simplified', 'cfr+', 'dcfr']
    results = {}
    
    for algorithm in algorithms:
        logger.info(f"运行 {algorithm} 算法...")
        start_time = time.time()
        _, history = create_toy_game_cfr(
            algorithm=algorithm,
            max_iterations=max_iterations,
            pruning_threshold=pruning_threshold,
            **dcfr_params
        )
        elapsed_time = time.time() - start_time
        
        results[algorithm] = {
            'history': history,
            'time': elapsed_time
        }
        
        logger.info(f"{algorithm} 完成，耗时: {elapsed_time:.2f}秒")
    
    # 绘制比较图
    plt.figure(figsize=(12, 6))
    
    # 绘制收敛速度
    plt.subplot(1, 2, 1)
    for algorithm, result in results.items():
        history = result['history']
        plt.plot(history['iterations'], history['exploitability'], label=algorithm)
    
    plt.title('CFR算法收敛速度比较')
    plt.xlabel('迭代次数')
    plt.ylabel('Exploitability')
    plt.yscale('log')
    plt.grid(True)
    plt.legend()
    
    # 绘制运行时间
    plt.subplot(1, 2, 2)
    algorithms_list = list(results.keys())
    times = [results[algorithm]['time'] for algorithm in algorithms_list]
    
    plt.bar(algorithms_list, times)
    plt.title('CFR算法运行时间比较')
    plt.xlabel('算法')
    plt.ylabel('时间 (秒)')
    
    # 保存图表
    os.makedirs('results', exist_ok=True)
    plt.tight_layout()
    plt.savefig('results/cfr_comparison.png')
    logger.info("比较结果已保存到 results/cfr_comparison.png")
    
    # 显示图表
    plt.show()


def main():
    """主函数"""
    args = parse_args()
    
    # 如果选择比较模式，则比较不同算法
    if args.compare:
        compare_algorithms(args)
        return
    
    # 创建输出目录
    os.makedirs(args.output_path, exist_ok=True)
    
    # 使用指定的算法生成GTO策略
    algorithm = args.algorithm
    max_iterations = args.max_iterations
    
    # 设置算法特定参数
    kwargs = {}
    if algorithm in ['cfr+', 'dcfr']:
        kwargs['pruning_threshold'] = args.pruning_threshold
        
    if algorithm == 'dcfr':
        kwargs['alpha_regret'] = args.alpha_regret
        kwargs['beta_strategy'] = args.beta_strategy
        kwargs['gamma_discount'] = args.gamma_discount
    
    # 训练策略
    gto_policy, history = create_toy_game_cfr(
        algorithm=algorithm,
        max_iterations=max_iterations,
        **kwargs
    )
    
    # 保存策略
    policy_path = os.path.join(args.output_path, f"gto_policy_{algorithm}.pkl")
    gto_policy.save(policy_path)
    logger.info(f"GTO策略已保存到: {policy_path}")
    
    # 绘制收敛曲线
    plt.figure(figsize=(10, 6))
    plt.plot(history['iterations'], history['exploitability'])
    plt.title(f'{algorithm} 收敛曲线')
    plt.xlabel('迭代次数')
    plt.ylabel('Exploitability')
    plt.yscale('log')
    plt.grid(True)
    
    # 保存图表
    os.makedirs('results', exist_ok=True)
    plt.savefig(f'results/{algorithm}_convergence.png')
    logger.info(f"收敛曲线已保存到: results/{algorithm}_convergence.png")
    
    # 显示图表
    plt.show()


if __name__ == "__main__":
    main() 