"""
斗地主游戏描述器模块

提供斗地主游戏的描述器实例。
"""
from typing import Dict, Any, List, Tuple, Optional, Union

from cardgame_ai.games.common.game_descriptor import GameDescriptor


# 创建斗地主游戏描述器
DOUDIZHU_DESCRIPTOR = GameDescriptor(
    name="斗地主",
    num_players=3,
    is_zero_sum=True,
    is_perfect_information=False,
    is_deterministic=False,
    rules={
        "card_count": 54,
        "landlord_cards": 3,
        "min_straight_length": 5,
        "min_straight_pair_length": 3,
        "min_airplane_length": 2,
        "card_ranks": [
            "3", "4", "5", "6", "7", "8", "9", "10", "J", "Q", "K", "A", "2", "小王", "大王"
        ],
        "card_suits": ["梅花", "方块", "红桃", "黑桃"],
        "card_types": [
            "单张", "对子", "三张", "三带一", "三带二", "顺子", "连对", "飞机", "飞机带单牌", 
            "飞机带对子", "四带二单", "四带二对", "炸弹", "火箭"
        ],
        "special_rules": {
            "bomb_can_beat_any": True,
            "rocket_can_beat_any": True,
            "landlord_plays_first": True
        }
    },
    state_shape=(627,),
    action_shape=(1,),
    description="""
    斗地主是一种流行的中国扑克牌游戏，通常由三个玩家参与。
    
    游戏规则：
    1. 使用一副54张的扑克牌（包括大小王）。
    2. 游戏开始时，随机选择一名玩家作为"地主"，其他两名玩家为"农民"。
    3. 地主获得额外的3张牌（底牌）。
    4. 地主先出牌，然后按顺时针方向轮流出牌。
    5. 玩家可以选择出牌或不出。如果选择出牌，必须大于前一位玩家出的牌。
    6. 当一位玩家出牌后，其他两位玩家都不出，则该玩家获得出牌权，可以出任意牌型。
    7. 最先出完手中所有牌的玩家获胜。
    8. 如果地主获胜，地主得2分，农民各扣1分；如果农民获胜，地主扣2分，农民各得1分。
    
    牌型：
    - 单张：任意一张牌
    - 对子：两张相同点数的牌
    - 三张：三张相同点数的牌
    - 三带一：三张相同点数的牌 + 一张单牌
    - 三带二：三张相同点数的牌 + 一对
    - 顺子：五张或更多的连续单牌（不包括2和王）
    - 连对：三对或更多的连续对牌（不包括2和王）
    - 飞机：两个或更多的连续三张（不包括2和王）
    - 飞机带单牌：飞机 + 相应数量的单牌
    - 飞机带对子：飞机 + 相应数量的对子
    - 四带二单：四张相同点数的牌 + 两张单牌
    - 四带二对：四张相同点数的牌 + 两对
    - 炸弹：四张相同点数的牌
    - 火箭：大王 + 小王
    
    牌型大小：
    - 火箭最大，可以打过任何牌型
    - 炸弹次之，可以打过除火箭外的任何牌型
    - 其他牌型只能打过同种牌型且点数更大的牌
    - 单牌、对子、三张、炸弹的大小顺序：3 < 4 < 5 < 6 < 7 < 8 < 9 < 10 < J < Q < K < A < 2 < 小王 < 大王
    """
)


def get_doudizhu_descriptor() -> GameDescriptor:
    """
    获取斗地主游戏描述器
    
    Returns:
        GameDescriptor: 斗地主游戏描述器
    """
    return DOUDIZHU_DESCRIPTOR
