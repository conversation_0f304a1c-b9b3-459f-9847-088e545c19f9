"""
Transformer网络模块

实现基于Transformer架构的神经网络模型，用于增强MuZero算法的表示能力。
主要用于处理卡牌游戏中的序列数据和长期依赖关系。
"""
import math
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional, Any, Union


class MultiHeadAttention(nn.Module):
    """
    多头注意力机制
    
    实现标准的多头注意力机制，用于捕获不同位置的依赖关系。
    """
    
    def __init__(self, hidden_dim: int, num_heads: int, dropout: float = 0.1):
        """
        初始化多头注意力层
        
        Args:
            hidden_dim (int): 隐藏层维度，必须能被num_heads整除
            num_heads (int): 注意力头数量
            dropout (float, optional): Dropout比率. Defaults to 0.1.
        """
        super(MultiHeadAttention, self).__init__()
        assert hidden_dim % num_heads == 0, "隐藏层维度必须能被注意力头数量整除"
        
        self.hidden_dim = hidden_dim
        self.num_heads = num_heads
        self.head_dim = hidden_dim // num_heads
        
        # 线性投影层
        self.query = nn.Linear(hidden_dim, hidden_dim)
        self.key = nn.Linear(hidden_dim, hidden_dim)
        self.value = nn.Linear(hidden_dim, hidden_dim)
        
        # 输出投影
        self.output = nn.Linear(hidden_dim, hidden_dim)
        
        # Dropout层
        self.dropout = nn.Dropout(dropout)
        
        # 用于缩放点积注意力
        self.scale = 1 / (self.head_dim ** 0.5)
    
    def forward(self, query: torch.Tensor, key: torch.Tensor, value: torch.Tensor, 
                mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        前向传播
        
        Args:
            query (torch.Tensor): 查询张量，形状为 [batch_size, seq_len, hidden_dim]
            key (torch.Tensor): 键张量，形状为 [batch_size, seq_len, hidden_dim]
            value (torch.Tensor): 值张量，形状为 [batch_size, seq_len, hidden_dim]
            mask (Optional[torch.Tensor], optional): 掩码张量. Defaults to None.
            
        Returns:
            torch.Tensor: 注意力输出，形状为 [batch_size, seq_len, hidden_dim]
        """
        batch_size = query.shape[0]
        
        # 线性投影并分割为多个头
        Q = self.query(query).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        K = self.key(key).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        V = self.value(value).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        
        # 计算注意力分数
        scores = torch.matmul(Q, K.transpose(-2, -1)) * self.scale
        
        # 应用掩码（如果提供）
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
        
        # 计算注意力权重
        attention = F.softmax(scores, dim=-1)
        attention = self.dropout(attention)
        
        # 应用注意力权重
        out = torch.matmul(attention, V)
        
        # 重塑并连接所有头
        out = out.transpose(1, 2).contiguous().view(batch_size, -1, self.hidden_dim)
        
        # 最终线性投影
        out = self.output(out)
        
        return out


class PositionalEncoding(nn.Module):
    """
    位置编码
    
    为Transformer输入添加位置信息。
    """
    
    def __init__(self, hidden_dim: int, max_seq_length: int = 100, dropout: float = 0.1):
        """
        初始化位置编码层
        
        Args:
            hidden_dim (int): 隐藏层维度
            max_seq_length (int, optional): 最大序列长度. Defaults to 100.
            dropout (float, optional): Dropout比率. Defaults to 0.1.
        """
        super(PositionalEncoding, self).__init__()
        self.dropout = nn.Dropout(dropout)
        
        # 创建位置编码矩阵
        pe = torch.zeros(max_seq_length, hidden_dim)
        position = torch.arange(0, max_seq_length, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(
            torch.arange(0, hidden_dim, 2).float() * (-math.log(10000.0) / hidden_dim)
        )
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0)
        
        # 注册为非模型参数
        self.register_buffer('pe', pe)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x (torch.Tensor): 输入张量，形状为 [batch_size, seq_len, hidden_dim]
            
        Returns:
            torch.Tensor: 添加位置编码后的张量
        """
        x = x + self.pe[:, :x.size(1), :]
        return self.dropout(x)


class TransformerEncoderLayer(nn.Module):
    """
    Transformer编码器层
    
    包含自注意力和前馈网络的标准Transformer编码器层。
    """
    
    def __init__(self, hidden_dim: int, num_heads: int, ff_dim: int, dropout: float = 0.1):
        """
        初始化Transformer编码器层
        
        Args:
            hidden_dim (int): 隐藏层维度
            num_heads (int): 注意力头数量
            ff_dim (int): 前馈网络内部维度
            dropout (float, optional): Dropout比率. Defaults to 0.1.
        """
        super(TransformerEncoderLayer, self).__init__()
        
        # 多头自注意力
        self.self_attention = MultiHeadAttention(hidden_dim, num_heads, dropout)
        
        # 前馈网络
        self.feed_forward = nn.Sequential(
            nn.Linear(hidden_dim, ff_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(ff_dim, hidden_dim)
        )
        
        # 层归一化
        self.norm1 = nn.LayerNorm(hidden_dim)
        self.norm2 = nn.LayerNorm(hidden_dim)
        
        # Dropout
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x (torch.Tensor): 输入张量，形状为 [batch_size, seq_len, hidden_dim]
            mask (Optional[torch.Tensor], optional): 掩码张量. Defaults to None.
            
        Returns:
            torch.Tensor: 编码器层输出
        """
        # 自注意力部分（带残差连接）
        attn_output = self.self_attention(x, x, x, mask)
        x = self.norm1(x + self.dropout(attn_output))
        
        # 前馈网络部分（带残差连接）
        ff_output = self.feed_forward(x)
        x = self.norm2(x + self.dropout(ff_output))
        
        return x


class TransformerEncoder(nn.Module):
    """
    Transformer编码器
    
    堆叠多个Transformer编码器层。
    """
    
    def __init__(self, hidden_dim: int, num_heads: int, ff_dim: int, num_layers: int, dropout: float = 0.1):
        """
        初始化Transformer编码器
        
        Args:
            hidden_dim (int): 隐藏层维度
            num_heads (int): 注意力头数量
            ff_dim (int): 前馈网络内部维度
            num_layers (int): 编码器层数量
            dropout (float, optional): Dropout比率. Defaults to 0.1.
        """
        super(TransformerEncoder, self).__init__()
        
        # 编码器层堆叠
        self.layers = nn.ModuleList([
            TransformerEncoderLayer(hidden_dim, num_heads, ff_dim, dropout)
            for _ in range(num_layers)
        ])
    
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x (torch.Tensor): 输入张量，形状为 [batch_size, seq_len, hidden_dim]
            mask (Optional[torch.Tensor], optional): 掩码张量. Defaults to None.
            
        Returns:
            torch.Tensor: 编码器输出
        """
        for layer in self.layers:
            x = layer(x, mask)
        return x


class TransformerPredictionNetwork(nn.Module):
    """
    基于Transformer的预测网络
    
    使用Transformer架构替代MuZero中的预测网络，从隐藏状态预测策略和价值。
    """
    
    def __init__(self, state_dim: int, action_dim: int, hidden_dim: int, num_heads: int = 4, 
                 num_layers: int = 2, dropout: float = 0.1):
        """
        初始化基于Transformer的预测网络
        
        Args:
            state_dim (int): 状态维度
            action_dim (int): 动作维度
            hidden_dim (int): 隐藏层维度
            num_heads (int, optional): 注意力头数量. Defaults to 4.
            num_layers (int, optional): Transformer层数量. Defaults to 2.
            dropout (float, optional): Dropout比率. Defaults to 0.1.
        """
        super(TransformerPredictionNetwork, self).__init__()
        
        # 输入嵌入层
        self.embedding = nn.Linear(state_dim, hidden_dim)
        
        # Transformer编码器
        self.transformer = TransformerEncoder(
            hidden_dim=hidden_dim,
            num_heads=num_heads,
            ff_dim=hidden_dim * 4,
            num_layers=num_layers,
            dropout=dropout
        )
        
        # 策略头
        self.policy_head = nn.Linear(hidden_dim, action_dim)
        
        # 价值头
        self.value_head = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, 1)
        )
    
    def forward(self, state: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            state (torch.Tensor): 隐藏状态张量，形状为 [batch_size, state_dim]
            
        Returns:
            Tuple[torch.Tensor, torch.Tensor]: 策略对数和价值
        """
        # 调整输入形状 (batch_size, state_dim) -> (batch_size, 1, state_dim)
        x = state.unsqueeze(1)
        
        # 嵌入
        x = self.embedding(x)
        
        # Transformer编码
        x = self.transformer(x)
        
        # 提取第一个位置的输出作为整个序列的表示
        x = x[:, 0, :]
        
        # 策略预测
        policy_logits = self.policy_head(x)
        
        # 价值预测
        value = self.value_head(x).squeeze(-1)
        
        return policy_logits, value


class TransformerRepresentationNetwork(nn.Module):
    """
    基于Transformer的表示网络
    
    使用Transformer架构替代MuZero中的表示网络，将观察转换为隐藏状态。
    特别适合处理卡牌游戏中的序列数据。
    """
    
    def __init__(self, input_dim: int, hidden_dim: int, state_dim: int, seq_len: int = 1,
                 num_heads: int = 4, num_layers: int = 2, dropout: float = 0.1):
        """
        初始化基于Transformer的表示网络
        
        Args:
            input_dim (int): 输入维度
            hidden_dim (int): 隐藏层维度
            state_dim (int): 状态维度（输出）
            seq_len (int, optional): 序列长度. Defaults to 1.
            num_heads (int, optional): 注意力头数量. Defaults to 4.
            num_layers (int, optional): Transformer层数量. Defaults to 2.
            dropout (float, optional): Dropout比率. Defaults to 0.1.
        """
        super(TransformerRepresentationNetwork, self).__init__()
        
        self.seq_len = seq_len
        
        # 输入嵌入层
        self.embedding = nn.Linear(input_dim, hidden_dim)
        
        # 位置编码
        self.positional_encoding = PositionalEncoding(hidden_dim, max_seq_length=seq_len, dropout=dropout)
        
        # Transformer编码器
        self.transformer = TransformerEncoder(
            hidden_dim=hidden_dim,
            num_heads=num_heads,
            ff_dim=hidden_dim * 4,
            num_layers=num_layers,
            dropout=dropout
        )
        
        # 输出层
        self.output = nn.Linear(hidden_dim, state_dim)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x (torch.Tensor): 输入观察，形状为 [batch_size, input_dim] 或 [batch_size, seq_len, input_dim]
            
        Returns:
            torch.Tensor: 隐藏状态
        """
        batch_size = x.shape[0]
        
        # 调整输入形状
        if len(x.shape) == 2:  # [batch_size, input_dim]
            x = x.unsqueeze(1)  # [batch_size, 1, input_dim]
        
        # 嵌入
        x = self.embedding(x)
        
        # 添加位置编码
        x = self.positional_encoding(x)
        
        # Transformer编码
        x = self.transformer(x)
        
        # 提取第一个位置的输出作为整个序列的表示
        x = x[:, 0, :]
        
        # 输出层
        x = self.output(x)
        
        return x


class TransformerDynamicsNetwork(nn.Module):
    """
    基于Transformer的动态网络
    
    使用Transformer架构替代MuZero中的动态网络，预测下一个隐藏状态和奖励。
    特别适合处理卡牌游戏中的状态转移。
    """
    
    def __init__(self, state_dim: int, action_dim: int, hidden_dim: int, 
                 num_heads: int = 4, num_layers: int = 2, dropout: float = 0.1):
        """
        初始化基于Transformer的动态网络
        
        Args:
            state_dim (int): 状态维度
            action_dim (int): 动作维度
            hidden_dim (int): 隐藏层维度
            num_heads (int, optional): 注意力头数量. Defaults to 4.
            num_layers (int, optional): Transformer层数量. Defaults to 2.
            dropout (float, optional): Dropout比率. Defaults to 0.1.
        """
        super(TransformerDynamicsNetwork, self).__init__()
        
        # 动作编码
        self.action_encoder = nn.Embedding(action_dim, hidden_dim // 4)
        
        # 将状态和动作嵌入合并到隐藏维度
        self.input_projection = nn.Linear(state_dim + hidden_dim // 4, hidden_dim)
        
        # Transformer编码器
        self.transformer = TransformerEncoder(
            hidden_dim=hidden_dim,
            num_heads=num_heads,
            ff_dim=hidden_dim * 4,
            num_layers=num_layers,
            dropout=dropout
        )
        
        # 状态输出
        self.state_output = nn.Linear(hidden_dim, state_dim)
        
        # 奖励输出
        self.reward_output = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, 1)
        )
    
    def forward(self, state: torch.Tensor, action: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            state (torch.Tensor): 当前隐藏状态，形状为 [batch_size, state_dim]
            action (torch.Tensor): 执行的动作，形状为 [batch_size]
            
        Returns:
            Tuple[torch.Tensor, torch.Tensor]: 下一个隐藏状态和奖励
        """
        # 动作编码
        action_embedding = self.action_encoder(action)
        
        # 合并状态和动作
        x = torch.cat([state, action_embedding], dim=1)
        
        # 投影到隐藏维度
        x = self.input_projection(x)
        
        # 调整为序列形式 [batch_size, 1, hidden_dim]
        x = x.unsqueeze(1)
        
        # Transformer编码
        x = self.transformer(x)
        
        # 提取特征 [batch_size, hidden_dim]
        x = x.squeeze(1)
        
        # 预测下一个状态
        next_state = self.state_output(x)
        
        # 预测奖励
        reward = self.reward_output(x).squeeze(-1)
        
        return next_state, reward


# 辅助函数，用于创建注意力掩码
def create_padding_mask(seq: torch.Tensor, pad_idx: int = 0) -> torch.Tensor:
    """
    创建用于掩盖填充位置的注意力掩码
    
    Args:
        seq (torch.Tensor): 输入序列，形状为 [batch_size, seq_len]
        pad_idx (int, optional): 填充索引值. Defaults to 0.
        
    Returns:
        torch.Tensor: 注意力掩码，形状为 [batch_size, 1, 1, seq_len]
    """
    mask = (seq != pad_idx).unsqueeze(1).unsqueeze(2)
    return mask 