"""
部署管理模块

支持Windows和Ubuntu系统的自动化部署，
包括远程部署、配置验证、任务启动等功能。
"""

import os
import time
import json
import sys
import subprocess
import signal
import atexit
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from .utils import CrossPlatformUtils

# 简化的进程管理器 - 仅用于信号处理，不自动终止进程
class ProcessManager:
    """进程管理器 - 仅处理信号，不自动终止训练进程"""

    def __init__(self):
        self.active_processes = {}  # {pid: process_info}
        self.auto_terminate = False  # 默认不自动终止
        self.setup_signal_handlers()

    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            print(f"\n🛑 接收到信号 {signum}")
            if self.auto_terminate and self.active_processes:
                print("正在终止训练进程...")
                self.terminate_all_processes()
            else:
                print("💡 训练进程将继续运行")
                print("💡 如需终止训练，请使用: python 终止训练进程.py --kill-all")
            sys.exit(0)

        # 注册信号处理器
        signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
        if hasattr(signal, 'SIGTERM'):
            signal.signal(signal.SIGTERM, signal_handler)  # 终止信号

    def enable_auto_terminate(self):
        """启用自动终止功能"""
        self.auto_terminate = True
        atexit.register(self.terminate_all_processes)

    def register_process(self, process, info):
        """注册进程"""
        self.active_processes[process.pid] = {
            'process': process,
            'info': info,
            'start_time': time.time()
        }
        # 不再打印注册信息，避免干扰

    def terminate_all_processes(self):
        """终止所有活跃进程"""
        if not self.active_processes:
            return

        print(f"🔄 正在终止 {len(self.active_processes)} 个活跃进程...")

        for pid, proc_data in list(self.active_processes.items()):
            try:
                process = proc_data['process']
                info = proc_data['info']

                print(f"⏹️ 终止进程 PID: {pid} ({info.get('command', 'unknown')})")

                # 尝试优雅终止
                if hasattr(process, 'terminate'):
                    process.terminate()

                    # 等待进程结束
                    try:
                        process.wait(timeout=5)
                        print(f"✅ 进程 {pid} 已优雅终止")
                    except subprocess.TimeoutExpired:
                        # 强制终止
                        print(f"⚠️ 进程 {pid} 未响应，强制终止")
                        process.kill()
                        process.wait()
                        print(f"💀 进程 {pid} 已强制终止")

                del self.active_processes[pid]

            except Exception as e:
                print(f"❌ 终止进程 {pid} 时出错: {e}")

        print("✅ 所有进程已终止")

# 全局进程管理器实例
_process_manager = ProcessManager()

class DeploymentManager:
    """部署管理器"""
    
    def __init__(self):
        self.logger = CrossPlatformUtils.setup_logging()
        self.utils = CrossPlatformUtils()
    
    def deploy_local(self, config_path: str,
                    training_script: str = "main_training.py",
                    dry_run: bool = False,
                    monitor: bool = False) -> Dict[str, Any]:
        """
        本地部署
        
        Args:
            config_path: 配置文件路径
            training_script: 训练脚本路径
            dry_run: 是否为预演模式
            monitor: 是否启用监控
            
        Returns:
            部署结果字典
        """
        self.logger.info(f"开始本地部署: {config_path}")
        
        deployment_result = {
            'success': False,
            'config_path': config_path,
            'training_script': training_script,
            'start_time': time.time(),
            'errors': [],
            'warnings': [],
            'process_id': None,
            'log_file': None
        }
        
        try:
            # 1. 环境检查
            env_check = self._check_environment()
            if not env_check['success']:
                deployment_result['errors'].extend(env_check['errors'])
                return deployment_result
            
            # 2. 配置验证
            config_check = self._validate_config_file(config_path)
            if not config_check['success']:
                deployment_result['errors'].extend(config_check['errors'])
                return deployment_result
            
            # 3. 依赖检查
            deps_check = self._check_dependencies()
            if not deps_check['success']:
                deployment_result['errors'].extend(deps_check['errors'])
                return deployment_result
            
            # 4. 预演模式
            if dry_run:
                self.logger.info("预演模式：所有检查通过，实际部署将会成功")
                deployment_result['success'] = True
                deployment_result['dry_run'] = True
                return deployment_result
            
            # 5. 启动训练
            launch_result = self._launch_training(config_path, training_script)
            if launch_result['success']:
                deployment_result.update(launch_result)
                deployment_result['success'] = True
                
                # 6. 启用监控
                if monitor:
                    self._start_monitoring(deployment_result['process_id'])
            else:
                deployment_result['errors'].extend(launch_result['errors'])
            
        except Exception as e:
            self.logger.error(f"部署过程中发生错误: {e}")
            deployment_result['errors'].append(str(e))
        
        deployment_result['end_time'] = time.time()
        deployment_result['duration'] = deployment_result['end_time'] - deployment_result['start_time']
        
        return deployment_result
    
    def deploy_remote(self, target_host: str,
                     config_path: str,
                     username: Optional[str] = None,
                     ssh_key: Optional[str] = None,
                     remote_path: str = "/tmp/training") -> Dict[str, Any]:
        """
        远程部署
        
        Args:
            target_host: 目标主机地址
            config_path: 配置文件路径
            username: SSH用户名
            ssh_key: SSH密钥路径
            remote_path: 远程部署路径
            
        Returns:
            部署结果字典
        """
        self.logger.info(f"开始远程部署到: {target_host}")
        
        deployment_result = {
            'success': False,
            'target_host': target_host,
            'config_path': config_path,
            'remote_path': remote_path,
            'start_time': time.time(),
            'errors': [],
            'warnings': []
        }
        
        try:
            # 1. SSH连接测试
            ssh_test = self._test_ssh_connection(target_host, username, ssh_key)
            if not ssh_test['success']:
                deployment_result['errors'].extend(ssh_test['errors'])
                return deployment_result
            
            # 2. 上传文件
            upload_result = self._upload_files(target_host, config_path, remote_path, username, ssh_key)
            if not upload_result['success']:
                deployment_result['errors'].extend(upload_result['errors'])
                return deployment_result
            
            # 3. 远程环境检查
            remote_check = self._check_remote_environment(target_host, username, ssh_key)
            if not remote_check['success']:
                deployment_result['errors'].extend(remote_check['errors'])
                return deployment_result
            
            # 4. 远程启动训练
            remote_launch = self._launch_remote_training(target_host, remote_path, username, ssh_key)
            if remote_launch['success']:
                deployment_result.update(remote_launch)
                deployment_result['success'] = True
            else:
                deployment_result['errors'].extend(remote_launch['errors'])
        
        except Exception as e:
            self.logger.error(f"远程部署过程中发生错误: {e}")
            deployment_result['errors'].append(str(e))
        
        deployment_result['end_time'] = time.time()
        deployment_result['duration'] = deployment_result['end_time'] - deployment_result['start_time']
        
        return deployment_result
    
    def batch_deploy(self, targets: List[Dict[str, str]],
                    parallel: bool = True,
                    max_workers: int = 5) -> Dict[str, Any]:
        """
        批量部署
        
        Args:
            targets: 目标列表，每个目标包含host、config_path等信息
            parallel: 是否并行部署
            max_workers: 最大并行数
            
        Returns:
            批量部署结果
        """
        self.logger.info(f"开始批量部署到 {len(targets)} 个目标")
        
        batch_result = {
            'success': True,
            'total_targets': len(targets),
            'successful_deployments': 0,
            'failed_deployments': 0,
            'results': {},
            'start_time': time.time()
        }
        
        if parallel:
            # 并行部署 (简化实现，实际可使用ThreadPoolExecutor)
            for i, target in enumerate(targets):
                target_id = target.get('host', f'target_{i}')
                self.logger.info(f"部署到目标 {i+1}/{len(targets)}: {target_id}")
                
                if 'host' in target:
                    # 远程部署
                    result = self.deploy_remote(
                        target_host=target['host'],
                        config_path=target['config_path'],
                        username=target.get('username'),
                        ssh_key=target.get('ssh_key'),
                        remote_path=target.get('remote_path', '/tmp/training')
                    )
                else:
                    # 本地部署
                    result = self.deploy_local(
                        config_path=target['config_path'],
                        training_script=target.get('training_script', 'main_training.py'),
                        dry_run=target.get('dry_run', False),
                        monitor=target.get('monitor', False)
                    )
                
                batch_result['results'][target_id] = result
                
                if result['success']:
                    batch_result['successful_deployments'] += 1
                else:
                    batch_result['failed_deployments'] += 1
                    batch_result['success'] = False
        
        batch_result['end_time'] = time.time()
        batch_result['duration'] = batch_result['end_time'] - batch_result['start_time']
        
        self.logger.info(f"批量部署完成: {batch_result['successful_deployments']}/{batch_result['total_targets']} 成功")
        return batch_result
    
    def _check_environment(self) -> Dict[str, Any]:
        """检查本地环境"""
        self.logger.info("检查本地环境...")
        
        result = {'success': True, 'errors': [], 'warnings': []}
        
        # 检查Python版本
        python_check = self.utils.execute_command('python --version')
        if not python_check['success']:
            result['errors'].append("Python未安装或不在PATH中")
            result['success'] = False
        else:
            self.logger.info(f"Python版本: {python_check['stdout']}")
        
        # 检查CUDA (如果有GPU)
        nvidia_check = self.utils.execute_command(f'{self.utils.get_nvidia_smi_command()} --version')
        if nvidia_check['success']:
            self.logger.info("NVIDIA驱动已安装")
        else:
            result['warnings'].append("未检测到NVIDIA驱动，将使用CPU训练")
        
        # 检查磁盘空间
        if self.utils.is_windows():
            disk_check = self.utils.execute_command('dir /-c')
        else:
            disk_check = self.utils.execute_command('df -h .')
        
        if disk_check['success']:
            self.logger.info("磁盘空间检查通过")
        else:
            result['warnings'].append("无法检查磁盘空间")
        
        return result
    
    def _validate_config_file(self, config_path: str) -> Dict[str, Any]:
        """验证配置文件"""
        self.logger.info(f"验证配置文件: {config_path}")
        
        result = {'success': True, 'errors': []}
        
        try:
            if not os.path.exists(config_path):
                result['errors'].append(f"配置文件不存在: {config_path}")
                result['success'] = False
                return result
            
            # 尝试解析YAML文件
            import yaml
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 基本验证
            if not isinstance(config, dict):
                result['errors'].append("配置文件格式错误")
                result['success'] = False
            
            self.logger.info("配置文件验证通过")
            
        except Exception as e:
            result['errors'].append(f"配置文件验证失败: {e}")
            result['success'] = False
        
        return result

    def _check_dependencies(self) -> Dict[str, Any]:
        """检查Python依赖"""
        self.logger.info("检查Python依赖...")

        result = {'success': True, 'errors': [], 'warnings': []}

        required_packages = [
            'torch', 'numpy', 'pyyaml', 'psutil'
        ]

        optional_packages = [
            'nvidia-ml-py', 'tensorboard', 'wandb'
        ]

        for package in required_packages:
            # 特殊处理某些包名
            import_name = package
            if package == 'pyyaml':
                import_name = 'yaml'

            check_cmd = f'python -c "import {import_name}; print({import_name}.__version__)"'
            check_result = self.utils.execute_command(check_cmd)

            if check_result['success']:
                self.logger.info(f"{package}: {check_result['stdout']}")
            else:
                result['errors'].append(f"缺少必需依赖: {package}")
                result['success'] = False

        for package in optional_packages:
            check_cmd = f'python -c "import {package}; print({package}.__version__)"'
            check_result = self.utils.execute_command(check_cmd)

            if not check_result['success']:
                result['warnings'].append(f"缺少可选依赖: {package}")

        return result

    def _launch_training(self, config_path: str, training_script: str) -> Dict[str, Any]:
        """启动训练任务"""
        self.logger.info(f"启动训练任务: {training_script}")

        result = {'success': False, 'errors': [], 'process_id': None, 'log_file': None}

        try:
            # 准备命令 - 检查脚本路径并使用正确的路径
            script_path = training_script

            # 如果脚本名不包含路径分隔符，尝试在常见位置查找
            if os.sep not in script_path and '/' not in script_path:
                # 检查常见的脚本位置
                possible_paths = [
                    script_path,  # 当前目录
                    f"cardgame_ai/zhuchengxu/{script_path}",  # 主程序目录
                    f"cardgame_ai\\zhuchengxu\\{script_path}",  # Windows路径
                ]

                for path in possible_paths:
                    if os.path.exists(path):
                        script_path = path
                        self.logger.info(f"找到训练脚本: {script_path}")
                        break
                else:
                    # 如果都找不到，尝试默认的cardgame_ai/zhuchengxu路径
                    fallback = Path("cardgame_ai") / "zhuchengxu" / training_script
                    if fallback.exists():
                        script_path = str(fallback)
                        self.logger.warning(f"使用默认脚本路径: {script_path}")
                    else:
                        result['errors'].append(f"训练脚本未找到: {training_script}")
                        return result

            # 创建日志文件
            log_dir = Path("logs")
            log_dir.mkdir(exist_ok=True)
            log_file = log_dir / f"training_{int(time.time())}.log"

            # 启动进程 - 修复版本，支持Ctrl+C终止
            try:
                # 准备启动参数和环境变量
                cmd_args = [sys.executable, script_path, '--config', config_path]

                # 设置UTF-8编码环境变量
                env = os.environ.copy()
                env['PYTHONIOENCODING'] = 'utf-8'
                env['PYTHONLEGACYWINDOWSSTDIO'] = '0'  # 强制使用UTF-8

                if self.utils.is_windows():
                    # Windows: 独立进程启动
                    env['LANG'] = 'zh_CN.UTF-8'
                    env['LC_ALL'] = 'zh_CN.UTF-8'
                    env['PYTHONPATH'] = os.getcwd()

                    # 打开日志文件，确保UTF-8编码
                    with open(log_file, 'w', encoding='utf-8') as f:
                        process = subprocess.Popen(
                            cmd_args,
                            stdout=f,
                            stderr=subprocess.STDOUT,
                            env=env,
                            cwd=os.getcwd(),
                            creationflags=subprocess.DETACHED_PROCESS  # 使用DETACHED_PROCESS而不是CREATE_NEW_PROCESS_GROUP
                        )

                    launch_result = {'success': True, 'stdout': '', 'stderr': ''}
                    self.logger.info(f"Windows进程已启动，PID: {process.pid} (独立运行)")

                else:
                    # Linux: 独立进程启动
                    env['LC_ALL'] = 'C.UTF-8'
                    env['LANG'] = 'C.UTF-8'

                    with open(log_file, 'w', encoding='utf-8') as f:
                        process = subprocess.Popen(
                            cmd_args,
                            stdout=f,
                            stderr=subprocess.STDOUT,
                            env=env,
                            cwd=os.getcwd(),
                            start_new_session=True  # Linux下使用start_new_session让进程独立运行
                        )

                    launch_result = {'success': True, 'stdout': '', 'stderr': ''}
                    self.logger.info(f"Linux进程已启动，PID: {process.pid} (独立运行)")

                # 注册进程到全局管理器（但不会自动终止）
                process_info = {
                    'command': ' '.join(cmd_args),
                    'config_path': config_path,
                    'log_file': str(log_file)
                }
                _process_manager.register_process(process, process_info)

            except Exception as e:
                launch_result = {'success': False, 'stderr': str(e)}

            if launch_result['success']:
                result['success'] = True
                result['log_file'] = str(log_file)
                result['command'] = ' '.join(cmd_args)
                result['process_id'] = process.pid
                self.logger.info(f"训练任务已启动，PID: {process.pid}，日志文件: {log_file}")
                self.logger.info("💡 提示: 使用 Ctrl+C 可以终止训练进程")
            else:
                result['errors'].append(f"启动失败: {launch_result['stderr']}")

        except Exception as e:
            result['errors'].append(f"启动训练任务时发生错误: {e}")

        return result

    def _start_monitoring(self, process_id: Optional[int]) -> None:
        """启动监控"""
        self.logger.info("启动训练监控...")
        # 这里可以实现监控逻辑，如GPU使用率监控、日志监控等
        pass
