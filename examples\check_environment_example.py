#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
环境检查示例

该脚本展示如何使用环境检查功能，以确保训练环境满足要求。
它演示了如何检查GPU可用性、GPU显存、系统内存和CPU核心数。
"""

import os
import sys
import argparse
import logging
from typing import Tuple, Dict, Optional, Any

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# 将项目根目录添加到路径中
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(script_dir, '..'))
sys.path.insert(0, project_root)

# 导入环境检查功能
try:
    from check_environment import (
        check_gpu, check_gpu_memory, check_system_memory,
        check_cpu_cores, check_environment,
        DEFAULT_MIN_GPU_MEMORY, DEFAULT_MIN_SYSTEM_MEMORY, DEFAULT_MIN_CPU_CORES
    )
    HAS_ENVIRONMENT_CHECK = True
except ImportError:
    HAS_ENVIRONMENT_CHECK = False
    logger.error("无法导入环境检查模块，请确保check_environment.py存在")
    
    # 定义兼容接口，以防模块不可用
    DEFAULT_MIN_GPU_MEMORY = 4
    DEFAULT_MIN_SYSTEM_MEMORY = 8
    DEFAULT_MIN_CPU_CORES = 4
    
    def check_environment(min_gpu_memory=4, min_system_memory=8, 
                        min_cpu_cores=4, require_gpu=True, 
                        warn_only=False) -> Tuple[bool, str]:
        """兼容接口"""
        return True, "环境检查模块不可用，跳过检查"


def run_detailed_check(args: argparse.Namespace) -> int:
    """运行详细的环境检查
    
    Args:
        args: 命令行参数
        
    Returns:
        int: 返回状态码，0为成功，非0为失败
    """
    if not HAS_ENVIRONMENT_CHECK:
        logger.error("环境检查模块不可用，请确保check_environment.py存在")
        return 1
    
    # 打印分隔线和标题
    print("\n" + "=" * 60)
    print(" " * 20 + "环境检查详情")
    print("=" * 60 + "\n")
    
    # 检查GPU可用性
    print("[1] 检查GPU可用性:")
    is_available, message, gpu_info = check_gpu()
    
    if is_available:
        print(f"  - ✅ {message}")
        
        # 如果GPU可用，打印详细信息
        if gpu_info:
            print("  - GPU设备信息:")
            for device in gpu_info['devices']:
                print(f"    * GPU {device['index']}: {device['name']} ({device['memory_gb']}GB)")
    else:
        print(f"  - ❌ {message}")
    
    print()
    
    # 如果有GPU，检查GPU显存
    if is_available:
        print(f"[2] 检查GPU显存 (要求 ≥ {args.min_gpu_memory}GB):")
        is_enough, message, memory_info = check_gpu_memory(args.min_gpu_memory)
        
        if is_enough:
            print(f"  - ✅ {message}")
        else:
            print(f"  - ❌ {message}")
        
        print()
    
    # 检查系统内存
    print(f"[3] 检查系统内存 (要求 ≥ {args.min_system_memory}GB):")
    is_enough, message, memory_info = check_system_memory(args.min_system_memory)
    
    if is_enough:
        print(f"  - ✅ {message}")
        
        # 如果有详细信息，打印
        if memory_info:
            print(f"  - 内存使用: {memory_info['used_gb']}GB/{memory_info['total_gb']}GB ({memory_info['percent_used']}%)")
    else:
        print(f"  - ❌ {message}")
    
    print()
    
    # 检查CPU核心数
    print(f"[4] 检查CPU核心数 (要求 ≥ {args.min_cpu_cores}核):")
    is_enough, message, cpu_info = check_cpu_cores(args.min_cpu_cores)
    
    if is_enough:
        print(f"  - ✅ {message}")
        
        # 如果有详细信息，打印
        if cpu_info and 'physical_cores' in cpu_info and cpu_info['physical_cores']:
            print(f"  - 物理核心: {cpu_info['physical_cores']}核")
            print(f"  - 逻辑核心: {cpu_info['logical_cores']}核")
            if 'cpu_percent' in cpu_info:
                print(f"  - CPU使用率: {cpu_info['cpu_percent']}%")
    else:
        print(f"  - ❌ {message}")
    
    print()
    
    # 运行综合环境检查
    print("[5] 综合环境检查:")
    is_ok, message = check_environment(
        min_gpu_memory=args.min_gpu_memory,
        min_system_memory=args.min_system_memory,
        min_cpu_cores=args.min_cpu_cores,
        require_gpu=args.require_gpu,
        warn_only=args.warn_only
    )
    
    if is_ok:
        print(f"  - ✅ 通过: {message}")
    else:
        print(f"  - ❌ 失败: {message}")
    
    print("\n" + "=" * 60)
    
    # 返回检查结果
    return 0 if is_ok else 1


def run_quick_check(args: argparse.Namespace) -> int:
    """运行快速环境检查
    
    Args:
        args: 命令行参数
        
    Returns:
        int: 返回状态码，0为成功，非0为失败
    """
    if not HAS_ENVIRONMENT_CHECK:
        logger.error("环境检查模块不可用，请确保check_environment.py存在")
        return 1
    
    # 运行综合环境检查
    is_ok, message = check_environment(
        min_gpu_memory=args.min_gpu_memory,
        min_system_memory=args.min_system_memory,
        min_cpu_cores=args.min_cpu_cores,
        require_gpu=args.require_gpu,
        warn_only=args.warn_only
    )
    
    if is_ok:
        print(f"✅ 环境检查通过: {message}")
    else:
        print(f"❌ 环境检查失败: {message}")
    
    return 0 if is_ok else 1


def simulate_training(args: argparse.Namespace) -> int:
    """模拟训练过程，首先检查环境
    
    Args:
        args: 命令行参数
        
    Returns:
        int: 返回状态码，0为成功，非0为失败
    """
    print("\n开始训练流程...")
    print("第1步: 检查环境...")
    
    # 进行环境检查
    is_ok, message = check_environment(
        min_gpu_memory=args.min_gpu_memory,
        min_system_memory=args.min_system_memory,
        min_cpu_cores=args.min_cpu_cores,
        require_gpu=args.require_gpu,
        warn_only=args.warn_only
    )
    
    if not is_ok:
        print(f"❌ 环境检查失败，无法开始训练: {message}")
        return 1
    
    print(f"✅ 环境检查通过: {message}")
    
    # 模拟训练过程
    print("\n第2步: 准备训练数据...")
    print("✅ 数据准备完成")
    
    print("\n第3步: 初始化模型...")
    print("✅ 模型初始化完成")
    
    print("\n第4步: 开始训练 (模拟)...")
    for epoch in range(1, 6):
        print(f"  - Epoch {epoch}/5 完成，损失: {1.0 - epoch*0.15:.2f}")
    
    print("\n✅ 训练完成！")
    return 0


def main() -> int:
    """主函数
    
    Returns:
        int: 返回状态码，0为成功，非0为失败
    """
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(
        description='环境检查示例',
        epilog='示例: python check_environment_example.py --detailed'
    )
    
    # 添加命令行参数
    parser.add_argument('--min-gpu-memory', type=float, default=DEFAULT_MIN_GPU_MEMORY,
                       help=f'最低要求的GPU显存，单位为GB（默认: {DEFAULT_MIN_GPU_MEMORY}）')
    parser.add_argument('--min-system-memory', type=float, default=DEFAULT_MIN_SYSTEM_MEMORY,
                       help=f'最低要求的系统内存，单位为GB（默认: {DEFAULT_MIN_SYSTEM_MEMORY}）')
    parser.add_argument('--min-cpu-cores', type=int, default=DEFAULT_MIN_CPU_CORES,
                       help=f'最低要求的CPU核心数（默认: {DEFAULT_MIN_CPU_CORES}）')
    parser.add_argument('--no-gpu', dest='require_gpu', action='store_false',
                       help='不要求GPU可用（默认要求GPU）')
    parser.add_argument('--warn-only', action='store_true',
                       help='仅警告，不阻止执行（默认：检查失败会阻止执行）')
    parser.add_argument('--detailed', action='store_true',
                       help='显示详细的检查信息（默认：只显示简单结果）')
    parser.add_argument('--simulate-training', action='store_true',
                       help='模拟训练过程，首先进行环境检查')
    
    # 默认要求GPU
    parser.set_defaults(require_gpu=True)
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 根据参数执行不同的操作
    if args.simulate_training:
        return simulate_training(args)
    elif args.detailed:
        return run_detailed_check(args)
    else:
        return run_quick_check(args)


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 