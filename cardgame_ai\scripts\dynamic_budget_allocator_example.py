"""
动态计算预算分配器示例脚本

演示如何使用动态计算预算分配器。
"""

import os
import sys
import argparse
import logging
import numpy as np
import torch
import time
from typing import Dict, List, Any, Optional

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cardgame_ai.algorithms.dynamic_budget_allocator import DynamicBudgetAllocator
from cardgame_ai.algorithms.key_moment_detector import KeyMomentDetector
from cardgame_ai.algorithms.efficient_zero import EfficientZero
from cardgame_ai.games.doudizhu.game import DouDizhuGame
from cardgame_ai.games.doudizhu.state import DouDizhuState
from cardgame_ai.games.doudizhu.card import Card

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def parse_args():
    """
    解析命令行参数
    
    Returns:
        argparse.Namespace: 解析后的参数
    """
    parser = argparse.ArgumentParser(description='动态计算预算分配器示例')
    parser.add_argument('--model_path', type=str, default='models/efficient_zero_model.pth', help='模型路径')
    parser.add_argument('--base_budget', type=int, default=50, help='基础计算预算（MCTS模拟次数）')
    parser.add_argument('--max_budget', type=int, default=500, help='最大计算预算')
    parser.add_argument('--amplification_factor', type=int, default=10, help='放大因子')
    parser.add_argument('--critical_threshold', type=float, default=0.5, help='关键决策点阈值')
    parser.add_argument('--adaptive_scaling', action='store_true', help='是否使用自适应缩放')
    parser.add_argument('--max_time_ms', type=int, default=1000, help='最大搜索时间（毫秒）')
    parser.add_argument('--num_decisions', type=int, default=10, help='决策次数')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    
    return parser.parse_args()


def create_test_state() -> DouDizhuState:
    """
    创建测试状态
    
    Returns:
        DouDizhuState: 测试状态
    """
    # 创建游戏
    game = DouDizhuGame()
    
    # 初始化状态
    state = game.get_init_state()
    
    # 模拟发牌
    landlord_cards = [
        Card.from_string('3'), Card.from_string('3'), Card.from_string('4'), Card.from_string('4'),
        Card.from_string('5'), Card.from_string('6'), Card.from_string('7'), Card.from_string('8'),
        Card.from_string('9'), Card.from_string('10'), Card.from_string('J'), Card.from_string('Q'),
        Card.from_string('K'), Card.from_string('A'), Card.from_string('2'), Card.from_string('2'),
        Card.from_string('JOKER')
    ]
    
    farmer1_cards = [
        Card.from_string('3'), Card.from_string('4'), Card.from_string('5'), Card.from_string('5'),
        Card.from_string('6'), Card.from_string('6'), Card.from_string('7'), Card.from_string('8'),
        Card.from_string('9'), Card.from_string('10'), Card.from_string('J'), Card.from_string('Q'),
        Card.from_string('K'), Card.from_string('A'), Card.from_string('A'), Card.from_string('JOKER')
    ]
    
    farmer2_cards = [
        Card.from_string('3'), Card.from_string('4'), Card.from_string('5'), Card.from_string('6'),
        Card.from_string('7'), Card.from_string('7'), Card.from_string('8'), Card.from_string('8'),
        Card.from_string('9'), Card.from_string('9'), Card.from_string('10'), Card.from_string('10'),
        Card.from_string('J'), Card.from_string('Q'), Card.from_string('K'), Card.from_string('K')
    ]
    
    # 设置手牌
    state.player_cards = {
        0: landlord_cards,
        1: farmer1_cards,
        2: farmer2_cards
    }
    
    # 设置地主
    state.landlord = 0
    
    # 设置当前玩家
    state.current_player = 0
    
    # 设置历史动作
    state.history = []
    
    return state


def load_model(model_path: str) -> EfficientZero:
    """
    加载模型
    
    Args:
        model_path: 模型路径
        
    Returns:
        EfficientZero: 加载的模型
    """
    # 检查模型文件是否存在
    if not os.path.exists(model_path):
        logger.warning(f"模型文件不存在: {model_path}，使用随机初始化的模型")
        # 创建随机初始化的模型
        model = EfficientZero(
            state_shape=(54 * 4 + 54 * 3,),
            action_shape=(54 * 3,),
            hidden_dim=128,
            state_dim=64,
            num_simulations=50
        )
        return model
    
    # 加载模型
    try:
        checkpoint = torch.load(model_path, map_location='cpu')
        model = EfficientZero(
            state_shape=(54 * 4 + 54 * 3,),
            action_shape=(54 * 3,),
            hidden_dim=128,
            state_dim=64,
            num_simulations=50
        )
        model.model.load_state_dict(checkpoint['model_state_dict'])
        logger.info(f"成功加载模型: {model_path}")
        return model
    except Exception as e:
        logger.error(f"加载模型失败: {e}")
        # 创建随机初始化的模型
        model = EfficientZero(
            state_shape=(54 * 4 + 54 * 3,),
            action_shape=(54 * 3,),
            hidden_dim=128,
            state_dim=64,
            num_simulations=50
        )
        return model


def main():
    """
    主函数
    """
    # 解析命令行参数
    args = parse_args()
    
    # 设置随机种子
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    
    # 创建关键决策点检测器
    key_moment_detector = KeyMomentDetector(
        model_type='rule_based',
        threshold=args.critical_threshold
    )
    
    # 创建动态计算预算分配器
    budget_allocator = DynamicBudgetAllocator(
        key_moment_detector=key_moment_detector,
        base_budget=args.base_budget,
        max_budget=args.max_budget,
        amplification_factor=args.amplification_factor,
        critical_threshold=args.critical_threshold,
        adaptive_scaling=args.adaptive_scaling,
        resource_constraint={"max_time_ms": args.max_time_ms}
    )
    
    # 加载模型
    model = load_model(args.model_path)
    
    # 创建测试状态
    state = create_test_state()
    
    # 创建状态历史
    state_history = []
    
    # 模拟多次决策
    logger.info(f"模拟 {args.num_decisions} 次决策...")
    for i in range(args.num_decisions):
        # 记录开始时间
        start_time = time.time()
        
        # 分配计算预算
        budget_config = budget_allocator.allocate_budget(
            state=state,
            state_history=state_history,
            context={"game_stage": "mid", "remaining_cards": len(state.player_cards[state.current_player])}
        )
        
        # 打印预算配置
        logger.info(f"决策 {i+1}/{args.num_decisions}:")
        logger.info(f"  是否关键决策点: {budget_config['is_critical']}")
        logger.info(f"  关键程度: {budget_config['criticality_score']:.4f}")
        logger.info(f"  分配模拟次数: {budget_config['num_simulations']}")
        
        # 使用动态预算进行决策
        action, action_probs, explanation = model.act(
            state=state,
            temperature=1.0,
            dynamic_budget=budget_config,
            explain=True
        )
        
        # 记录结束时间
        end_time = time.time()
        elapsed_time = (end_time - start_time) * 1000  # 转换为毫秒
        
        # 打印决策结果
        logger.info(f"  选择动作: {action}")
        logger.info(f"  实际模拟次数: {explanation['budget_info']['actual_simulations']}")
        logger.info(f"  耗时: {elapsed_time:.2f} 毫秒")
        
        # 更新状态（这里简单地随机修改状态）
        # 在实际应用中，应该执行动作并获取新状态
        state_history.append(state)
        
        # 创建新的测试状态（简单地修改当前玩家）
        state.current_player = (state.current_player + 1) % 3
        
        # 随机移除一些牌，模拟游戏进行
        for player_id in range(3):
            if state.player_cards[player_id]:
                num_cards_to_remove = min(1, len(state.player_cards[player_id]))
                for _ in range(num_cards_to_remove):
                    card_idx = np.random.randint(0, len(state.player_cards[player_id]))
                    state.player_cards[player_id].pop(card_idx)
    
    # 打印统计信息
    logger.info("统计信息:")
    stats = budget_allocator.get_stats()
    for key, value in stats.items():
        logger.info(f"  {key}: {value}")


if __name__ == '__main__':
    main()
