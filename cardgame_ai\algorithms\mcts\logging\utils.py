"""
MCTS日志工具函数模块

提供日志系统所需的各种工具函数，包括：
- 会话ID生成
- 时间戳格式化
- 数据序列化
- 性能测量
"""

import time
import uuid
import hashlib
import threading
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from functools import wraps
import json


def generate_session_id() -> str:
    """
    生成唯一的MCTS会话ID
    
    会话ID格式: mcts_YYYYMMDD_HHMMSS_XXXX
    其中XXXX是基于时间戳的4位哈希值
    
    Returns:
        str: 唯一的会话ID
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    hash_suffix = hashlib.md5(str(time.time()).encode()).hexdigest()[:4]
    return f"mcts_{timestamp}_{hash_suffix}"


def format_timestamp(timestamp: Optional[float] = None) -> str:
    """
    格式化时间戳为ISO格式字符串
    
    Args:
        timestamp: Unix时间戳，如果为None则使用当前时间
        
    Returns:
        str: ISO格式的时间戳字符串
    """
    if timestamp is None:
        timestamp = time.time()
    
    return datetime.fromtimestamp(timestamp).isoformat()


def safe_serialize(obj: Any) -> Any:
    """
    安全序列化对象，处理不可序列化的类型
    
    Args:
        obj: 要序列化的对象
        
    Returns:
        Any: 可序列化的对象
    """
    if obj is None:
        return None
    
    # 基本类型直接返回
    if isinstance(obj, (str, int, float, bool)):
        return obj
    
    # 列表和元组
    if isinstance(obj, (list, tuple)):
        return [safe_serialize(item) for item in obj]
    
    # 字典
    if isinstance(obj, dict):
        return {str(key): safe_serialize(value) for key, value in obj.items()}
    
    # NumPy数组
    try:
        import numpy as np
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        if isinstance(obj, (np.integer, np.floating)):
            return obj.item()
    except ImportError:
        pass
    
    # PyTorch张量
    try:
        import torch
        if isinstance(obj, torch.Tensor):
            return obj.detach().cpu().numpy().tolist()
    except ImportError:
        pass
    
    # 对象有__dict__属性
    if hasattr(obj, '__dict__'):
        return safe_serialize(obj.__dict__)
    
    # 其他情况转换为字符串
    return str(obj)


def truncate_data(data: Any, max_length: int = 1000) -> Any:
    """
    截断数据以避免日志过长
    
    Args:
        data: 要截断的数据
        max_length: 最大长度
        
    Returns:
        Any: 截断后的数据
    """
    if isinstance(data, str):
        if len(data) > max_length:
            return data[:max_length] + "...[截断]"
        return data
    
    if isinstance(data, list):
        if len(data) > max_length:
            return data[:max_length] + ["...[截断]"]
        return [truncate_data(item, max_length) for item in data]
    
    if isinstance(data, dict):
        if len(data) > max_length:
            # 保留前max_length个键值对
            truncated = dict(list(data.items())[:max_length])
            truncated["...[截断]"] = f"省略了{len(data) - max_length}个项目"
            return truncated
        return {key: truncate_data(value, max_length) for key, value in data.items()}
    
    return data


class PerformanceTimer:
    """
    性能计时器，用于测量代码执行时间
    """
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
    
    def start(self) -> None:
        """开始计时"""
        self.start_time = time.perf_counter()
    
    def stop(self) -> float:
        """
        停止计时并返回耗时
        
        Returns:
            float: 耗时（秒）
        """
        if self.start_time is None:
            raise ValueError("计时器未启动")
        
        self.end_time = time.perf_counter()
        return self.end_time - self.start_time
    
    def elapsed(self) -> float:
        """
        获取当前耗时（不停止计时器）
        
        Returns:
            float: 当前耗时（秒）
        """
        if self.start_time is None:
            raise ValueError("计时器未启动")
        
        return time.perf_counter() - self.start_time


def timing_decorator(func):
    """
    装饰器：自动测量函数执行时间
    
    Args:
        func: 要测量的函数
        
    Returns:
        装饰后的函数
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        timer = PerformanceTimer()
        timer.start()
        try:
            result = func(*args, **kwargs)
            execution_time = timer.stop()
            
            # 如果函数返回字典，添加执行时间
            if isinstance(result, dict):
                result['execution_time'] = execution_time
            
            return result
        except Exception as e:
            execution_time = timer.stop()
            # 在异常中也记录执行时间
            if hasattr(e, 'execution_time'):
                e.execution_time = execution_time
            raise
    
    return wrapper


class ThreadSafeCounter:
    """
    线程安全的计数器
    """
    
    def __init__(self, initial_value: int = 0):
        self._value = initial_value
        self._lock = threading.Lock()
    
    def increment(self, delta: int = 1) -> int:
        """
        增加计数器值
        
        Args:
            delta: 增加的值
            
        Returns:
            int: 增加后的值
        """
        with self._lock:
            self._value += delta
            return self._value
    
    def decrement(self, delta: int = 1) -> int:
        """
        减少计数器值
        
        Args:
            delta: 减少的值
            
        Returns:
            int: 减少后的值
        """
        with self._lock:
            self._value -= delta
            return self._value
    
    def get(self) -> int:
        """
        获取当前值
        
        Returns:
            int: 当前值
        """
        with self._lock:
            return self._value
    
    def reset(self) -> int:
        """
        重置计数器
        
        Returns:
            int: 重置前的值
        """
        with self._lock:
            old_value = self._value
            self._value = 0
            return old_value


def format_memory_size(size_bytes: int) -> str:
    """
    格式化内存大小为可读字符串
    
    Args:
        size_bytes: 字节数
        
    Returns:
        str: 格式化的大小字符串
    """
    if size_bytes >= 1024**3:
        return f"{size_bytes / (1024**3):.2f} GB"
    elif size_bytes >= 1024**2:
        return f"{size_bytes / (1024**2):.2f} MB"
    elif size_bytes >= 1024:
        return f"{size_bytes / 1024:.2f} KB"
    else:
        return f"{size_bytes} B"


def get_memory_usage() -> Dict[str, Any]:
    """
    获取当前进程的内存使用情况
    
    Returns:
        Dict[str, Any]: 内存使用信息
    """
    try:
        import psutil
        process = psutil.Process()
        memory_info = process.memory_info()
        
        return {
            'rss': memory_info.rss,  # 物理内存
            'vms': memory_info.vms,  # 虚拟内存
            'rss_formatted': format_memory_size(memory_info.rss),
            'vms_formatted': format_memory_size(memory_info.vms),
            'percent': process.memory_percent()
        }
    except ImportError:
        return {
            'error': 'psutil not available',
            'rss': 0,
            'vms': 0,
            'rss_formatted': '0 B',
            'vms_formatted': '0 B',
            'percent': 0.0
        }


def validate_json_serializable(data: Any) -> bool:
    """
    验证数据是否可以JSON序列化
    
    Args:
        data: 要验证的数据
        
    Returns:
        bool: 是否可以序列化
    """
    try:
        json.dumps(data)
        return True
    except (TypeError, ValueError):
        return False
