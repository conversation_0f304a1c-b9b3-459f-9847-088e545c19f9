"""
组件注册表模块

用于注册和获取环境、代理和训练器类。
"""
import logging
from typing import Dict, Any, Type, Optional

from cardgame_ai.core.agent import Agent
from cardgame_ai.core.environment import Environment
from cardgame_ai.core.trainer import Trainer

logger = logging.getLogger(__name__)

# 注册表
_environment_registry: Dict[str, Type[Environment]] = {}
_agent_registry: Dict[str, Type[Agent]] = {}
_trainer_registry: Dict[str, Type[Trainer]] = {}


def register_env(name: str, env_class: Type[Environment]) -> None:
    """
    注册环境类

    Args:
        name (str): 环境名称
        env_class (Type[Environment]): 环境类
    """
    if name in _environment_registry:
        logger.warning(f"环境 '{name}' 已经注册，将被覆盖")
    
    _environment_registry[name] = env_class
    logger.debug(f"已注册环境: {name}")


def register_agent(name: str, agent_class: Type[Agent]) -> None:
    """
    注册代理类

    Args:
        name (str): 代理名称
        agent_class (Type[Agent]): 代理类
    """
    if name in _agent_registry:
        logger.warning(f"代理 '{name}' 已经注册，将被覆盖")
    
    _agent_registry[name] = agent_class
    logger.debug(f"已注册代理: {name}")


def register_trainer(name: str, trainer_class: Type[Trainer]) -> None:
    """
    注册训练器类

    Args:
        name (str): 训练器名称
        trainer_class (Type[Trainer]): 训练器类
    """
    if name in _trainer_registry:
        logger.warning(f"训练器 '{name}' 已经注册，将被覆盖")
    
    _trainer_registry[name] = trainer_class
    logger.debug(f"已注册训练器: {name}")


def get_env_cls(name: str) -> Optional[Type[Environment]]:
    """
    获取环境类

    Args:
        name (str): 环境名称

    Returns:
        Optional[Type[Environment]]: 环境类，如果不存在则返回None
    """
    if name not in _environment_registry:
        logger.warning(f"环境 '{name}' 未注册")
        return None
    
    return _environment_registry[name]


def get_agent_cls(name: str) -> Optional[Type[Agent]]:
    """
    获取代理类

    Args:
        name (str): 代理名称

    Returns:
        Optional[Type[Agent]]: 代理类，如果不存在则返回None
    """
    if name not in _agent_registry:
        logger.warning(f"代理 '{name}' 未注册")
        return None
    
    return _agent_registry[name]


def get_trainer_cls(name: str) -> Optional[Type[Trainer]]:
    """
    获取训练器类

    Args:
        name (str): 训练器名称

    Returns:
        Optional[Type[Trainer]]: 训练器类，如果不存在则返回None
    """
    if name not in _trainer_registry:
        logger.warning(f"训练器 '{name}' 未注册")
        return None
    
    return _trainer_registry[name]


def list_registered_envs() -> Dict[str, Type[Environment]]:
    """
    列出所有注册的环境

    Returns:
        Dict[str, Type[Environment]]: 环境名称到环境类的映射
    """
    return _environment_registry.copy()


def list_registered_agents() -> Dict[str, Type[Agent]]:
    """
    列出所有注册的代理

    Returns:
        Dict[str, Type[Agent]]: 代理名称到代理类的映射
    """
    return _agent_registry.copy()


def list_registered_trainers() -> Dict[str, Type[Trainer]]:
    """
    列出所有注册的训练器

    Returns:
        Dict[str, Type[Trainer]]: 训练器名称到训练器类的映射
    """
    return _trainer_registry.copy()


# 自动注册已知的环境、代理和训练器
def _auto_register():
    """
    自动注册已知的环境、代理和训练器
    """
    # 注册环境
    try:
        from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
        register_env("doudizhu", DouDizhuEnvironment)
    except ImportError:
        logger.debug("无法导入 DouDizhuEnvironment")
    
    # 注册代理
    try:
        from cardgame_ai.core.agent import RandomAgent
        register_agent("random", RandomAgent)
    except ImportError:
        logger.debug("无法导入 RandomAgent")
    
    try:
        from cardgame_ai.agents.dqn_agent import DQNAgent
        register_agent("dqn", DQNAgent)
    except ImportError:
        logger.debug("无法导入 DQNAgent")
    
    try:
        from cardgame_ai.agents.muzero_agent import MuZeroAgent
        register_agent("muzero", MuZeroAgent)
    except ImportError:
        logger.debug("无法导入 MuZeroAgent")
    
    # 注册训练器
    try:
        from cardgame_ai.training.trainer import AdvancedTrainer
        register_trainer("default", AdvancedTrainer)
    except ImportError:
        logger.debug("无法导入 AdvancedTrainer")
    
    try:
        from cardgame_ai.training.dqn_trainer import DQNTrainer
        register_trainer("dqn", DQNTrainer)
    except ImportError:
        logger.debug("无法导入 DQNTrainer")
    
    try:
        from cardgame_ai.training.muzero_trainer import MuZeroTrainer
        register_trainer("muzero", MuZeroTrainer)
    except ImportError:
        logger.debug("无法导入 MuZeroTrainer")


# 自动注册
_auto_register() 