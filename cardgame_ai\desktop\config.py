#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
客户端配置

管理桌面应用程序的配置选项。
"""

import os
import json
import logging
from typing import Dict, Any, Optional, Union

from cardgame_ai.desktop.utils.platform_utils import PlatformConfig

logger = logging.getLogger(__name__)


class ClientConfig:
    """客户端配置类"""

    def __init__(self, config_file: Optional[str] = None):
        """
        初始化客户端配置

        Args:
            config_file (Optional[str], optional): 配置文件路径. Defaults to None.
        """
        # 默认配置
        self._default_config = {
            "ui": {
                "theme": "light",  # 主题
                "language": "zh_CN",  # 语言：zh_CN, en_US
                "font_size": 12,  # 字体大小
                "window_width": 1200,  # 窗口宽度
                "window_height": 800,  # 窗口高度
                "show_toolbar": True,  # 是否显示工具栏
                "show_statusbar": True,  # 是否显示状态栏
                "animation": True  # 是否启用动画
            },
            "paths": {
                "models": os.path.join(PlatformConfig.get_base_dir(), "models"),  # 模型路径
                "configs": os.path.join(PlatformConfig.get_base_dir(), "configs"),  # 配置路径
                "logs": os.path.join(PlatformConfig.get_base_dir(), "logs"),  # 日志路径
                "data": os.path.join(PlatformConfig.get_base_dir(), "data")  # 数据路径
            },
            "training": {
                "default_algorithm": "dqn",  # 默认算法
                "use_gpu": True,  # 是否使用GPU
                "gpu_id": 0  # GPU ID
            },
            "inference": {
                "default_model": "",  # 默认模型
                "timeout": 5.0  # 推理超时时间（秒）
            },
            "battle": {
                "server_host": "localhost",  # 服务器主机
                "server_port": 5000,  # 服务器端口
                "auto_start": False  # 是否自动启动服务器
            },
            "accessibility": {
                "keyboard_navigation": True,  # 是否启用键盘导航
                "screen_reader": False,  # 是否启用屏幕阅读器
                "high_contrast": False,  # 是否启用高对比度
                "color_blind_mode": "none",  # 色盲模式：none, protanopia, deuteranopia, tritanopia
                "font_scale": 1.0,  # 字体缩放
                "reduced_motion": False  # 是否减少动画
            }
        }

        # 当前配置
        self._config = self._default_config.copy()

        # 配置文件路径
        self.config_file = config_file or os.path.join(PlatformConfig.get_config_dir(), "client.json")

        # 加载配置
        self.load()

        logger.info(f"客户端配置初始化完成，配置文件：{self.config_file}")

    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置项

        Args:
            key (str): 配置项键，使用点分隔，如"ui.theme"
            default (Any, optional): 默认值. Defaults to None.

        Returns:
            Any: 配置项值
        """
        # 分割键
        keys = key.split(".")

        # 获取配置项
        config = self._config
        for k in keys:
            if isinstance(config, dict) and k in config:
                config = config[k]
            else:
                return default

        return config

    def set(self, key: str, value: Any) -> None:
        """
        设置配置项

        Args:
            key (str): 配置项键，使用点分隔，如"ui.theme"
            value (Any): 配置项值
        """
        # 分割键
        keys = key.split(".")

        # 设置配置项
        config = self._config
        for i, k in enumerate(keys[:-1]):
            if k not in config:
                config[k] = {}
            config = config[k]

        config[keys[-1]] = value

        logger.info(f"设置配置项：{key} = {value}")

    def load(self) -> bool:
        """
        加载配置

        Returns:
            bool: 是否加载成功
        """
        try:
            # 如果配置文件不存在，则创建
            if not os.path.exists(self.config_file):
                # 确保目录存在
                os.makedirs(os.path.dirname(self.config_file), exist_ok=True)

                # 保存默认配置
                self.save()

                logger.info(f"创建默认配置文件：{self.config_file}")
                return True

            # 加载配置
            with open(self.config_file, "r", encoding="utf-8") as f:
                loaded_config = json.load(f)

            # 更新配置
            self._update_config(self._config, loaded_config)

            logger.info(f"加载配置文件：{self.config_file}")
            return True
        except Exception as e:
            logger.error(f"加载配置文件失败：{e}")
            return False

    def save(self) -> bool:
        """
        保存配置

        Returns:
            bool: 是否保存成功
        """
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)

            # 保存配置
            with open(self.config_file, "w", encoding="utf-8") as f:
                json.dump(self._config, f, indent=4, ensure_ascii=False)

            logger.info(f"保存配置文件：{self.config_file}")
            return True
        except Exception as e:
            logger.error(f"保存配置文件失败：{e}")
            return False

    def reset(self) -> None:
        """重置配置为默认值"""
        self._config = self._default_config.copy()
        logger.info("重置配置为默认值")

    def _update_config(self, target: Dict[str, Any], source: Dict[str, Any]) -> None:
        """
        递归更新配置

        Args:
            target (Dict[str, Any]): 目标配置
            source (Dict[str, Any]): 源配置
        """
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._update_config(target[key], value)
            else:
                target[key] = value
