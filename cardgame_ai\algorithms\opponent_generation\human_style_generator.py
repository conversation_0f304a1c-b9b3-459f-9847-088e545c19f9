"""
基于人类风格的生成器

使用模仿学习和风格迁移技术生成具有人类风格的对手策略。
"""

import os
import time
import logging
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from typing import Dict, List, Tuple, Any, Optional, Union, Callable

from cardgame_ai.core.agent import Agent
from cardgame_ai.core.state import State
from cardgame_ai.core.policy import Policy
from cardgame_ai.utils.model_saver import ModelSaver

# 配置日志
logger = logging.getLogger(__name__)


class StyleTransferNetwork(nn.Module):
    """
    风格迁移网络

    将AI策略转换为具有人类风格的策略。
    """

    def __init__(
        self,
        input_dim: int,
        output_dim: int,
        hidden_dims: List[int] = [256, 128],
        style_dim: int = 32,
        num_styles: int = 3,
        activation: nn.Module = nn.ReLU(),
        dropout: float = 0.1
    ):
        """
        初始化风格迁移网络

        Args:
            input_dim: 输入维度（AI策略）
            output_dim: 输出维度（人类风格策略）
            hidden_dims: 隐藏层维度列表
            style_dim: 风格嵌入维度
            num_styles: 风格数量
            activation: 激活函数
            dropout: Dropout比率
        """
        super().__init__()

        self.input_dim = input_dim
        self.output_dim = output_dim
        self.style_dim = style_dim
        self.num_styles = num_styles

        # 风格嵌入
        self.style_embeddings = nn.Embedding(num_styles, style_dim)

        # 编码器
        encoder_layers = []
        encoder_layers.append(nn.Linear(input_dim, hidden_dims[0]))
        encoder_layers.append(activation)
        encoder_layers.append(nn.Dropout(dropout))

        for i in range(len(hidden_dims) - 1):
            encoder_layers.append(nn.Linear(hidden_dims[i], hidden_dims[i + 1]))
            encoder_layers.append(activation)
            encoder_layers.append(nn.Dropout(dropout))

        self.encoder = nn.Sequential(*encoder_layers)

        # 解码器
        decoder_layers = []
        decoder_layers.append(nn.Linear(hidden_dims[-1] + style_dim, hidden_dims[-1]))
        decoder_layers.append(activation)
        decoder_layers.append(nn.Dropout(dropout))

        for i in range(len(hidden_dims) - 1, 0, -1):
            decoder_layers.append(nn.Linear(hidden_dims[i], hidden_dims[i - 1]))
            decoder_layers.append(activation)
            decoder_layers.append(nn.Dropout(dropout))

        decoder_layers.append(nn.Linear(hidden_dims[0], output_dim))
        decoder_layers.append(nn.Softmax(dim=1))

        self.decoder = nn.Sequential(*decoder_layers)

    def forward(self, x: torch.Tensor, style_id: int) -> torch.Tensor:
        """
        前向传播

        Args:
            x: 输入策略
            style_id: 风格ID

        Returns:
            具有指定风格的策略
        """
        # 获取风格嵌入
        style_embedding = self.style_embeddings(torch.tensor(style_id, device=x.device))

        # 编码
        encoded = self.encoder(x)

        # 扩展风格嵌入以匹配批次大小
        if encoded.dim() > 1:
            style_embedding = style_embedding.unsqueeze(0).expand(encoded.size(0), -1)

        # 连接编码和风格嵌入
        combined = torch.cat((encoded, style_embedding), dim=-1)

        # 解码
        return self.decoder(combined)


class HumanStyleGenerator:
    """
    人类风格生成器

    使用模仿学习和风格迁移技术生成具有人类风格的对手策略。
    """

    def __init__(
        self,
        ai_policy: Any,
        human_data_path: str,
        policy_dim: int,
        style_dim: int = 32,
        num_styles: int = 3,
        device: str = None,
        learning_rate: float = 0.001
    ):
        """
        初始化人类风格生成器

        Args:
            ai_policy: AI策略
            human_data_path: 人类数据路径
            policy_dim: 策略维度
            style_dim: 风格嵌入维度
            num_styles: 风格数量
            device: 计算设备
            learning_rate: 学习率
        """
        # 设置设备
        self.device = device if device else ('cuda' if torch.cuda.is_available() else 'cpu')

        # 保存参数
        self.ai_policy = ai_policy
        self.human_data_path = human_data_path
        self.policy_dim = policy_dim

        # 创建风格迁移网络
        self.style_transfer = StyleTransferNetwork(
            input_dim=policy_dim,
            output_dim=policy_dim,
            style_dim=style_dim,
            num_styles=num_styles
        ).to(self.device)

        # 创建优化器
        self.optimizer = optim.Adam(self.style_transfer.parameters(), lr=learning_rate)

        # 加载人类数据
        self.human_data = self._load_human_data()

        # 初始化风格描述
        self.style_descriptions = {
            0: "保守型",  # 保守型玩家，倾向于保留强牌
            1: "激进型",  # 激进型玩家，倾向于快速出牌
            2: "随机型"   # 随机型玩家，行为不可预测
        }

        # 初始化统计信息
        self.stats = {
            "training_loss": [],
            "validation_loss": [],
            "style_usage": {i: 0 for i in range(num_styles)}
        }

    def _load_human_data(self) -> Dict[str, Any]:
        """
        加载人类数据

        Returns:
            人类数据
        """
        # 检查数据路径是否存在
        if not os.path.exists(self.human_data_path):
            logger.warning(f"人类数据路径不存在: {self.human_data_path}")
            return {"states": [], "actions": [], "styles": []}

        try:
            # 加载数据
            data = np.load(self.human_data_path, allow_pickle=True)

            # 提取数据
            states = data["states"] if "states" in data else []
            actions = data["actions"] if "actions" in data else []
            styles = data["styles"] if "styles" in data else []

            # 如果没有风格标签，随机分配
            if not styles and states:
                styles = np.random.randint(0, self.style_transfer.num_styles, len(states))

            return {
                "states": states,
                "actions": actions,
                "styles": styles
            }

        except Exception as e:
            logger.error(f"加载人类数据失败: {e}")
            return {"states": [], "actions": [], "styles": []}

    def train(
        self,
        num_epochs: int = 100,
        batch_size: int = 32,
        validation_split: float = 0.2,
        save_interval: int = 10,
        save_path: str = None,
        verbose: bool = True
    ) -> Dict[str, List[float]]:
        """
        训练风格迁移网络

        Args:
            num_epochs: 训练轮数
            batch_size: 批次大小
            validation_split: 验证集比例
            save_interval: 保存间隔（轮数）
            save_path: 保存路径
            verbose: 是否打印训练信息

        Returns:
            训练统计信息
        """
        # 检查是否有人类数据
        if not self.human_data["states"] or not self.human_data["actions"]:
            logger.warning("没有人类数据，无法训练")
            return self.stats

        # 准备数据
        states = np.array(self.human_data["states"])
        actions = np.array(self.human_data["actions"])
        styles = np.array(self.human_data["styles"])

        # 划分训练集和验证集
        num_samples = len(states)
        indices = np.random.permutation(num_samples)
        split_idx = int(num_samples * (1 - validation_split))

        train_indices = indices[:split_idx]
        val_indices = indices[split_idx:]

        train_states = states[train_indices]
        train_actions = actions[train_indices]
        train_styles = styles[train_indices]

        val_states = states[val_indices]
        val_actions = actions[val_indices]
        val_styles = styles[val_indices]

        # 训练循环
        start_time = time.time()
        for epoch in range(num_epochs):
            epoch_start_time = time.time()

            # 训练模式
            self.style_transfer.train()

            # 打乱训练数据
            train_indices = np.random.permutation(len(train_states))

            # 批次训练
            train_loss = 0.0
            num_batches = (len(train_indices) + batch_size - 1) // batch_size

            for i in range(num_batches):
                # 获取批次索引
                batch_start = i * batch_size
                batch_end = min((i + 1) * batch_size, len(train_indices))
                batch_indices = train_indices[batch_start:batch_end]

                # 获取批次数据
                batch_states = train_states[batch_indices]
                batch_actions = train_actions[batch_indices]
                batch_styles = train_styles[batch_indices]

                # 转换为张量
                batch_states_tensor = torch.tensor(batch_states, dtype=torch.float32).to(self.device)
                batch_actions_tensor = torch.tensor(batch_actions, dtype=torch.float32).to(self.device)

                # 获取AI策略
                with torch.no_grad():
                    ai_policies = self._get_ai_policies(batch_states_tensor)

                # 前向传播
                batch_loss = 0.0
                for j, style_id in enumerate(batch_styles):
                    # 获取风格化策略
                    styled_policy = self.style_transfer(ai_policies[j:j+1], style_id)

                    # 计算损失
                    loss = F.mse_loss(styled_policy, batch_actions_tensor[j:j+1])
                    batch_loss += loss

                # 平均损失
                batch_loss /= len(batch_indices)

                # 反向传播和优化
                self.optimizer.zero_grad()
                batch_loss.backward()
                self.optimizer.step()

                # 累加损失
                train_loss += batch_loss.item()

            # 计算平均训练损失
            train_loss /= num_batches

            # 验证模式
            self.style_transfer.eval()

            # 验证
            val_loss = 0.0
            with torch.no_grad():
                # 批次验证
                val_num_batches = (len(val_indices) + batch_size - 1) // batch_size

                for i in range(val_num_batches):
                    # 获取批次索引
                    batch_start = i * batch_size
                    batch_end = min((i + 1) * batch_size, len(val_indices))
                    batch_indices = np.arange(batch_start, batch_end)

                    # 获取批次数据
                    batch_states = val_states[batch_indices]
                    batch_actions = val_actions[batch_indices]
                    batch_styles = val_styles[batch_indices]

                    # 转换为张量
                    batch_states_tensor = torch.tensor(batch_states, dtype=torch.float32).to(self.device)
                    batch_actions_tensor = torch.tensor(batch_actions, dtype=torch.float32).to(self.device)

                    # 获取AI策略
                    ai_policies = self._get_ai_policies(batch_states_tensor)

                    # 前向传播
                    batch_loss = 0.0
                    for j, style_id in enumerate(batch_styles):
                        # 获取风格化策略
                        styled_policy = self.style_transfer(ai_policies[j:j+1], style_id)

                        # 计算损失
                        loss = F.mse_loss(styled_policy, batch_actions_tensor[j:j+1])
                        batch_loss += loss

                    # 平均损失
                    batch_loss /= len(batch_indices)

                    # 累加损失
                    val_loss += batch_loss.item()

                # 计算平均验证损失
                val_loss /= val_num_batches

            # 更新统计信息
            self.stats["training_loss"].append(train_loss)
            self.stats["validation_loss"].append(val_loss)

            # 打印训练信息
            if verbose:
                epoch_time = time.time() - epoch_start_time
                logger.info(
                    f"[Epoch {epoch+1}/{num_epochs}] "
                    f"Train Loss: {train_loss:.4f}, "
                    f"Val Loss: {val_loss:.4f}, "
                    f"Time: {epoch_time:.2f}s"
                )

            # 保存模型
            if save_path and (epoch + 1) % save_interval == 0:
                self.save(os.path.join(save_path, f"human_style_epoch_{epoch+1}.pt"))

        # 打印总训练时间
        total_time = time.time() - start_time
        if verbose:
            logger.info(f"训练完成，总时间: {total_time:.2f}s")

        # 返回训练统计信息
        return self.stats

    def _get_ai_policies(self, states: torch.Tensor) -> torch.Tensor:
        """
        获取AI策略

        Args:
            states: 状态张量

        Returns:
            AI策略张量
        """
        # 如果AI策略是PyTorch模型
        if isinstance(self.ai_policy, torch.nn.Module):
            # 设置为评估模式
            self.ai_policy.eval()

            # 获取策略
            with torch.no_grad():
                if hasattr(self.ai_policy, 'predict'):
                    # 使用predict方法
                    policies = []
                    for state in states:
                        policy, _ = self.ai_policy.predict(state.cpu().numpy())
                        policies.append(torch.tensor(policy, dtype=torch.float32))
                    return torch.stack(policies).to(self.device)
                else:
                    # 直接使用前向传播
                    return self.ai_policy(states)

        # 如果AI策略是函数
        elif callable(self.ai_policy):
            # 调用函数
            policies = []
            for state in states:
                policy = self.ai_policy(state.cpu().numpy())
                policies.append(torch.tensor(policy, dtype=torch.float32))
            return torch.stack(policies).to(self.device)

        # 如果AI策略是其他类型
        else:
            raise TypeError(f"不支持的AI策略类型: {type(self.ai_policy)}")

    def generate_human_style_policy(self, state: Any, style_id: Optional[int] = None) -> torch.Tensor:
        """
        生成具有人类风格的策略

        Args:
            state: 游戏状态
            style_id: 风格ID，如果为None则随机选择

        Returns:
            具有人类风格的策略
        """
        # 设置为评估模式
        self.style_transfer.eval()

        # 如果没有指定风格ID，随机选择
        if style_id is None:
            style_id = np.random.randint(0, self.style_transfer.num_styles)

        # 记录风格使用
        self.stats["style_usage"][style_id] += 1

        # 转换状态为张量
        if not isinstance(state, torch.Tensor):
            state_tensor = torch.tensor(state, dtype=torch.float32).unsqueeze(0).to(self.device)
        else:
            state_tensor = state.unsqueeze(0) if state.dim() == 1 else state
            state_tensor = state_tensor.to(self.device)

        # 获取AI策略
        ai_policy = self._get_ai_policies(state_tensor)

        # 生成具有人类风格的策略
        with torch.no_grad():
            human_style_policy = self.style_transfer(ai_policy, style_id)

        return human_style_policy.squeeze(0)

    def generate_opponent_policy(self, state: Any, style: str = 'random') -> torch.Tensor:
        """
        生成对手策略

        Args:
            state: 游戏状态
            style: 风格，可选值为'random'（随机）, 'conservative'（保守）, 'aggressive'（激进）

        Returns:
            生成的对手策略
        """
        # 映射风格到风格ID
        style_map = {
            'random': 2,
            'conservative': 0,
            'aggressive': 1
        }

        # 获取风格ID
        style_id = style_map.get(style, None)

        # 生成具有人类风格的策略
        return self.generate_human_style_policy(state, style_id)

    def save(self, path: str) -> None:
        """
        保存模型

        Args:
            path: 保存路径
        """
        # 创建目录
        os.makedirs(os.path.dirname(path), exist_ok=True)

        # 保存模型
        torch.save({
            'style_transfer_state_dict': self.style_transfer.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'stats': self.stats,
            'style_descriptions': self.style_descriptions
        }, path)

        logger.info(f"模型已保存到: {path}")

    def load(self, path: str) -> None:
        """
        加载模型

        Args:
            path: 加载路径
        """
        # 加载模型
        checkpoint = torch.load(path, map_location=self.device)

        # 加载状态
        self.style_transfer.load_state_dict(checkpoint['style_transfer_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.stats = checkpoint['stats']
        self.style_descriptions = checkpoint['style_descriptions']

        logger.info(f"模型已加载: {path}")

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            统计信息
        """
        return {
            "training_loss": self.stats["training_loss"],
            "validation_loss": self.stats["validation_loss"],
            "style_usage": self.stats["style_usage"],
            "style_descriptions": self.style_descriptions
        }
