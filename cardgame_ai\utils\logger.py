"""
日志工具模块

提供日志记录功能。
"""
import logging
import os
import sys
from typing import Optional


def setup_logger(name: str, log_file: Optional[str] = None, level: int = logging.INFO) -> logging.Logger:
    """
    设置日志记录器
    
    Args:
        name (str): 日志记录器名称
        log_file (Optional[str], optional): 日志文件路径. Defaults to None.
        level (int, optional): 日志级别. Defaults to logging.INFO.
        
    Returns:
        logging.Logger: 日志记录器
    """
    # 创建日志记录器
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(level)
    
    # 创建格式化器
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    
    # 添加处理器
    logger.addHandler(console_handler)
    
    # 如果指定了日志文件，则创建文件处理器
    if log_file:
        # 创建日志目录
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        
        # 创建文件处理器
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        
        # 添加处理器
        logger.addHandler(file_handler)
    
    return logger


def get_logger(name: str, log_file: Optional[str] = None, level: int = logging.INFO) -> logging.Logger:
    """
    获取日志记录器，如果日志记录器已存在则返回现有的，否则创建新的
    
    Args:
        name (str): 日志记录器名称
        log_file (Optional[str], optional): 日志文件路径. Defaults to None.
        level (int, optional): 日志级别. Defaults to logging.INFO.
        
    Returns:
        logging.Logger: 日志记录器
    """
    logger = logging.getLogger(name)
    
    # 如果日志记录器已经配置过，直接返回
    if logger.handlers:
        return logger
    
    # 否则创建新的日志记录器
    return setup_logger(name, log_file, level)


class Logger:
    """
    日志记录器类
    
    提供更加灵活的日志记录功能。
    """
    
    def __init__(
        self, 
        name: str = "cardgame_ai", 
        log_file: Optional[str] = None, 
        level: int = logging.INFO,
        console_output: bool = True,
        file_output: bool = True,
        format_str: str = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    ):
        """
        初始化日志记录器
        
        Args:
            name (str, optional): 日志记录器名称. Defaults to "cardgame_ai".
            log_file (Optional[str], optional): 日志文件路径. Defaults to None.
            level (int, optional): 日志级别. Defaults to logging.INFO.
            console_output (bool, optional): 是否输出到控制台. Defaults to True.
            file_output (bool, optional): 是否输出到文件. Defaults to True.
            format_str (str, optional): 日志格式. Defaults to '%(asctime)s - %(name)s - %(levelname)s - %(message)s'.
        """
        self.name = name
        self.log_file = log_file
        self.level = level
        self.console_output = console_output
        self.file_output = file_output
        self.format_str = format_str
        
        # 创建日志记录器
        self.logger = logging.getLogger(name)
        self.logger.setLevel(level)
        
        # 清除已有的处理器
        if self.logger.handlers:
            self.logger.handlers.clear()
        
        # 创建格式化器
        formatter = logging.Formatter(format_str)
        
        # 添加控制台处理器
        if console_output:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(level)
            console_handler.setFormatter(formatter)
            self.logger.addHandler(console_handler)
        
        # 添加文件处理器
        if file_output and log_file:
            # 创建日志目录
            os.makedirs(os.path.dirname(log_file), exist_ok=True)
            
            # 创建文件处理器
            file_handler = logging.FileHandler(log_file)
            file_handler.setLevel(level)
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
    
    def debug(self, msg: str, *args, **kwargs) -> None:
        """记录DEBUG级别日志"""
        self.logger.debug(msg, *args, **kwargs)
    
    def info(self, msg: str, *args, **kwargs) -> None:
        """记录INFO级别日志"""
        self.logger.info(msg, *args, **kwargs)
    
    def warning(self, msg: str, *args, **kwargs) -> None:
        """记录WARNING级别日志"""
        self.logger.warning(msg, *args, **kwargs)
    
    def error(self, msg: str, *args, **kwargs) -> None:
        """记录ERROR级别日志"""
        self.logger.error(msg, *args, **kwargs)
    
    def critical(self, msg: str, *args, **kwargs) -> None:
        """记录CRITICAL级别日志"""
        self.logger.critical(msg, *args, **kwargs)
    
    def set_level(self, level: int) -> None:
        """设置日志级别"""
        self.level = level
        self.logger.setLevel(level)
        for handler in self.logger.handlers:
            handler.setLevel(level)
    
    def add_file_handler(self, log_file: str, level: Optional[int] = None) -> None:
        """添加文件处理器"""
        if not level:
            level = self.level
        
        # 创建日志目录
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        
        # 创建格式化器
        formatter = logging.Formatter(self.format_str)
        
        # 创建文件处理器
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        
        # 添加处理器
        self.logger.addHandler(file_handler)
