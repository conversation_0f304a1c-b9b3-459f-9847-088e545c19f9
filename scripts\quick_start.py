#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速启动脚本

用于快速测试和验证优化后的斗地主AI系统
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from cardgame_ai.utils.logging import setup_logging

logger = logging.getLogger(__name__)


def main():
    """主函数"""
    print("=" * 60)
    print("🎯 斗地主AI优化系统 - 快速启动")
    print("=" * 60)
    
    # 设置日志
    setup_logging(level="INFO", log_dir="logs")
    
    logger.info("系统启动中...")
    
    # 检查环境
    check_environment()
    
    # 显示系统信息
    show_system_info()
    
    # 显示优化特性
    show_optimization_features()
    
    logger.info("快速启动完成！")
    print("\n✅ 系统已准备就绪！")
    print("\n📋 下一步操作:")
    print("1. 运行训练: python scripts/optimized_training.py")
    print("2. 运行评估: python scripts/evaluate_model.py")
    print("3. 查看配置: configs/base.yaml")


def check_environment():
    """检查环境依赖"""
    logger.info("检查环境依赖...")
    
    # 检查Python版本
    python_version = sys.version_info
    logger.info(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查关键依赖
    dependencies = [
        ('torch', 'PyTorch'),
        ('numpy', 'NumPy'),
        ('ray', 'Ray'),
    ]
    
    missing_deps = []
    
    for module_name, display_name in dependencies:
        try:
            __import__(module_name)
            logger.info(f"✅ {display_name} 已安装")
        except ImportError:
            logger.warning(f"❌ {display_name} 未安装")
            missing_deps.append(display_name)
    
    if missing_deps:
        logger.warning(f"缺少依赖: {', '.join(missing_deps)}")
        logger.warning("请运行: pip install -r requirements.txt")
    else:
        logger.info("所有核心依赖已满足")


def show_system_info():
    """显示系统信息"""
    logger.info("系统信息:")
    
    # GPU信息
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            logger.info(f"GPU数量: {gpu_count}")
            for i in range(gpu_count):
                gpu_name = torch.cuda.get_device_name(i)
                logger.info(f"GPU {i}: {gpu_name}")
        else:
            logger.info("未检测到CUDA GPU")
    except ImportError:
        logger.warning("PyTorch未安装，无法检测GPU")
    
    # 内存信息
    try:
        import psutil
        memory = psutil.virtual_memory()
        logger.info(f"系统内存: {memory.total / (1024**3):.1f} GB")
        logger.info(f"可用内存: {memory.available / (1024**3):.1f} GB")
    except ImportError:
        logger.warning("psutil未安装，无法检测内存信息")


def show_optimization_features():
    """显示优化特性"""
    logger.info("🚀 系统优化特性:")
    
    features = [
        "✨ EfficientZero算法优化 (MCTS: 50→100-200次模拟)",
        "🤝 增强多智能体协作机制 (MAPPO算法集成)",
        "⚡ 分布式训练支持 (Ray框架)",
        "📊 实时性能监控 (TensorBoard集成)",
        "🎯 动态奖励机制 (团队协作优化)",
        "🔧 模块化架构设计 (易于扩展)",
        "📈 性能评估体系 (多维度指标)",
        "🛡️ 完整的错误处理和日志系统"
    ]
    
    for feature in features:
        logger.info(feature)
    
    logger.info("\n🎯 性能目标:")
    logger.info("• 胜率提升: 60-70% → 85-95%")
    logger.info("• 训练效率提升: 50%")
    logger.info("• 推理速度: < 1秒/步")
    logger.info("• 农民协作效率: > 90%")


if __name__ == "__main__":
    main()
