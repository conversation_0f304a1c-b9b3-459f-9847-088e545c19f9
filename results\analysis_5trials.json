{"analysis": {"total_trials": 5, "successful_trials": 5, "failed_trials": 0, "avg_elapsed_time": 0.40486693382263184, "avg_num_experiences": 790.0, "avg_landlord_win_rate": 0.36, "avg_farmer_win_rate": 0.64, "avg_game_length": 87.42, "avg_game_length_std": 10.03074492673033, "min_game_length": 66, "max_game_length": 116, "avg_memory_usage": 0.93359375, "avg_reward": 0.01622335697808102, "best_params": {"temperature": 0.8395227446965248, "num_games": 10, "batch_size": 64, "learning_rate": 0.0008441627016229127, "gamma": 0.9652220259608041, "save_experiences": false, "parallel": false}, "worst_params": {"temperature": 1.3444150612650045, "num_games": 10, "batch_size": 32, "learning_rate": 0.0006081610001241993, "gamma": 0.9427316907021845, "save_experiences": true, "parallel": true}, "fastest_params": {"temperature": 1.8318327601618078, "num_games": 5, "batch_size": 32, "learning_rate": 0.0001389515573728028, "gamma": 0.9580433941195857, "save_experiences": true, "parallel": true}, "slowest_params": {"temperature": 1.3444150612650045, "num_games": 10, "batch_size": 32, "learning_rate": 0.0006081610001241993, "gamma": 0.9427316907021845, "save_experiences": true, "parallel": true}, "best_landlord_win_rate": 0.7, "worst_landlord_win_rate": 0.2, "fastest_time": 0.20048856735229492, "slowest_time": 0.5066218376159668, "common_errors": {}, "parameter_correlations": {"parallel": {"win_rate_correlation": -0.6163156344279366, "time_correlation": -0.1857943460333098}, "gamma": {"win_rate_correlation": 0.6407002635471412, "time_correlation": -0.3501837960530489}, "save_experiences": {"win_rate_correlation": -0.39620290784653056, "time_correlation": -0.3458802099486588}, "temperature": {"win_rate_correlation": -0.2810455385505895, "time_correlation": -0.753025099154072}, "num_games": {"win_rate_correlation": -0.10783277320343848, "time_correlation": 0.9612475738965748}, "batch_size": {"win_rate_correlation": -0.1968748077395395, "time_correlation": 0.3625549500310062}, "learning_rate": {"win_rate_correlation": 0.06066663672856555, "time_correlation": 0.8517333378388042}}, "phase_distribution": {"BIDDING": 0.03569620253164557, "GRABBING": 0.03417721518987342, "PLAYING": 0.930126582278481}, "success_rate": 1.0, "max_memory_usage": 2.3359375, "min_memory_usage": 0.03125, "positive_reward_ratio": 0.06582278481012659, "negative_reward_ratio": 0.015443037974683544, "zero_reward_ratio": 0.9187341772151899}, "bugs": [{"id": 1, "trial": 1, "type": "reward_distribution_issue", "description": "奖励分布不平衡: 零奖励占比91.69%", "severity": "medium", "possible_cause": "奖励函数可能设计不合理，导致奖励分布过于集中", "fix_suggestion": "重新设计奖励函数，使奖励分布更加均衡"}, {"id": 2, "trial": 2, "type": "reward_distribution_issue", "description": "奖励分布不平衡: 零奖励占比92.24%", "severity": "medium", "possible_cause": "奖励函数可能设计不合理，导致奖励分布过于集中", "fix_suggestion": "重新设计奖励函数，使奖励分布更加均衡"}, {"id": 3, "trial": 3, "type": "reward_distribution_issue", "description": "奖励分布不平衡: 零奖励占比91.24%", "severity": "medium", "possible_cause": "奖励函数可能设计不合理，导致奖励分布过于集中", "fix_suggestion": "重新设计奖励函数，使奖励分布更加均衡"}, {"id": 4, "trial": 4, "type": "reward_distribution_issue", "description": "奖励分布不平衡: 零奖励占比92.10%", "severity": "medium", "possible_cause": "奖励函数可能设计不合理，导致奖励分布过于集中", "fix_suggestion": "重新设计奖励函数，使奖励分布更加均衡"}, {"id": 5, "trial": 5, "type": "reward_distribution_issue", "description": "奖励分布不平衡: 零奖励占比91.92%", "severity": "medium", "possible_cause": "奖励函数可能设计不合理，导致奖励分布过于集中", "fix_suggestion": "重新设计奖励函数，使奖励分布更加均衡"}], "timestamp": "2025-04-29 02:41:32", "num_trials": 5, "total_time": 2.4137039184570312, "successful_trials": 5, "failed_trials": 0}