#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Device参数验证示例

演示如何使用device参数验证功能，并展示不同情况下的错误处理。
此示例可作为开发者了解device参数验证功能的参考。
"""

import argparse
import sys
import os
import logging
from typing import Tuple, Optional

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# 将项目根目录添加到路径中
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(script_dir, '..'))
sys.path.insert(0, project_root)

# 导入参数验证功能
try:
    from cardgame_ai.主程序.run_efficient_zero_training import validate_args
    HAS_VALIDATION = True
except ImportError:
    HAS_VALIDATION = False
    logger.error("无法导入参数验证功能，将使用简化版本")
    
    # 简化的参数验证函数
    def validate_args(args: argparse.Namespace) -> Tuple[bool, str]:
        """简化的参数验证函数，仅作为后备方案"""
        if not args.device:
            return True, ""
        
        device = args.device.lower()
        if device == 'cpu' or device.startswith('cuda'):
            return True, ""
        else:
            return False, f"无效的设备: {device}"


def main() -> int:
    """主函数，处理命令行参数并演示参数验证"""
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(
        description='Device参数验证示例',
        epilog='示例: python device_validation_example.py --device cuda:0'
    )
    
    # 添加device参数
    parser.add_argument('--device', type=str, default=None,
                        help='训练使用的设备，例如 "cuda:0" 或 "cpu"')
    
    # 添加验证级别参数
    parser.add_argument('--validation-level', type=str, default='strict',
                        choices=['strict', 'warn', 'none'],
                        help='参数验证级别: strict(严格), warn(警告), none(无)')
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 显示开始信息
    logger.info("===== Device参数验证示例 =====")
    logger.info(f"当前设置: device={args.device}, validation_level={args.validation_level}")
    
    if not HAS_VALIDATION:
        logger.warning("使用简化的参数验证函数")
    
    # 根据验证级别执行不同的验证行为
    if args.validation_level == 'none':
        logger.info("跳过参数验证")
        return run_with_device(args.device)
    
    # 执行参数验证
    is_valid, error_msg = validate_args(args)
    
    if is_valid:
        logger.info("参数验证通过")
        return run_with_device(args.device)
    else:
        if args.validation_level == 'strict':
            logger.error(f"参数验证失败: {error_msg}")
            return 1
        else:  # warn
            logger.warning(f"参数验证警告: {error_msg}")
            logger.warning("继续执行，但可能会出现问题")
            return run_with_device(args.device)


def run_with_device(device: Optional[str]) -> int:
    """使用指定的设备运行（模拟）"""
    try:
        logger.info(f"正在使用设备: {device or 'default'}")
        
        # 模拟设备初始化
        logger.info("模拟设备初始化...")
        
        # 检查PyTorch和CUDA可用性
        try:
            import torch
            has_torch = True
            
            if device and 'cuda' in device:
                if not torch.cuda.is_available():
                    logger.warning("警告: 请求使用CUDA，但CUDA不可用")
                    logger.warning("将自动切换到CPU")
                    device = 'cpu'
                else:
                    if device == 'cuda':
                        # 默认使用第一个CUDA设备
                        logger.info(f"使用默认CUDA设备: {torch.cuda.get_device_name(0)}")
                    elif ',' in device:
                        # 多GPU设置
                        devices = [d.strip() for d in device.split(',')]
                        logger.info(f"使用多GPU设置: {devices}")
                        for i, dev in enumerate(devices):
                            if dev.startswith('cuda:'):
                                idx = int(dev.split(':')[1])
                                logger.info(f"GPU {i}: {torch.cuda.get_device_name(idx)}")
                    else:
                        # 特定CUDA设备
                        idx = int(device.split(':')[1])
                        logger.info(f"使用CUDA设备 {idx}: {torch.cuda.get_device_name(idx)}")
            else:
                logger.info("使用CPU设备")
        except ImportError:
            has_torch = False
            logger.warning("无法导入PyTorch，仅执行模拟")
        
        # 模拟训练过程
        logger.info("模拟训练过程开始...")
        logger.info("模拟训练过程完成")
        
        return 0
    except Exception as e:
        logger.exception(f"运行过程中出错: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main()) 