#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
层次化强化学习示例脚本

展示如何使用层次化强化学习框架训练斗地主AI。
"""

import os
import sys
import argparse
import logging
import torch
import numpy as np
import random
from typing import Dict, List, Tuple, Any, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from cardgame_ai.algorithms.hrl import HierarchicalPolicy
from cardgame_ai.training.hrl_trainer import HRLTrainer
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='层次化强化学习示例')

    parser.add_argument('--state_dim', type=int, default=512,
                        help='状态维度')
    parser.add_argument('--low_level_action_dim', type=int, default=1000,
                        help='低层动作维度')
    parser.add_argument('--high_level_hidden_dims', type=int, nargs='+', default=[256, 128],
                        help='高层策略网络隐藏层维度')
    parser.add_argument('--low_level_hidden_dims', type=int, nargs='+', default=[256, 128],
                        help='低层策略网络隐藏层维度')
    parser.add_argument('--dropout', type=float, default=0.1,
                        help='Dropout概率')
    parser.add_argument('--lr', type=float, default=0.001,
                        help='学习率')
    parser.add_argument('--gamma', type=float, default=0.99,
                        help='折扣因子')
    parser.add_argument('--tau', type=float, default=0.001,
                        help='目标网络软更新系数')
    parser.add_argument('--batch_size', type=int, default=64,
                        help='批次大小')
    parser.add_argument('--replay_buffer_size', type=int, default=10000,
                        help='经验回放缓冲区大小')
    parser.add_argument('--training_mode', type=str, default='end_to_end',
                        choices=['end_to_end', 'hierarchical'],
                        help='训练模式')
    parser.add_argument('--prioritized_replay', action='store_true',
                        help='是否使用优先经验回放')
    parser.add_argument('--alpha', type=float, default=0.6,
                        help='优先级指数')
    parser.add_argument('--beta', type=float, default=0.4,
                        help='重要性采样指数')
    parser.add_argument('--beta_increment', type=float, default=0.001,
                        help='beta的增量')
    parser.add_argument('--high_level_update_freq', type=int, default=1,
                        help='高层策略更新频率')
    parser.add_argument('--low_level_update_freq', type=int, default=1,
                        help='低层策略更新频率')
    parser.add_argument('--target_update_freq', type=int, default=100,
                        help='目标网络更新频率')
    parser.add_argument('--num_episodes', type=int, default=1000,
                        help='训练回合数')
    parser.add_argument('--max_steps_per_episode', type=int, default=1000,
                        help='每回合最大步数')
    parser.add_argument('--save_path', type=str, default='models/hrl',
                        help='模型保存路径')
    parser.add_argument('--save_interval', type=int, default=100,
                        help='模型保存间隔')
    parser.add_argument('--load_path', type=str, default=None,
                        help='模型加载路径')
    parser.add_argument('--seed', type=int, default=42,
                        help='随机种子')
    parser.add_argument('--device', type=str, default=None,
                        help='设备')

    return parser.parse_args()


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()

    # 设置随机种子
    random.seed(args.seed)
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)

    # 设置设备
    device = args.device
    if device is None:
        device = 'cuda' if torch.cuda.is_available() else 'cpu'

    # 创建环境
    env = DouDizhuEnvironment()

    # 创建层次化策略
    policy = HierarchicalPolicy(
        state_dim=args.state_dim,
        low_level_action_dim=args.low_level_action_dim,
        high_level_hidden_dims=args.high_level_hidden_dims,
        low_level_hidden_dims=args.low_level_hidden_dims,
        dropout=args.dropout
    )

    # 创建优化器
    optimizer = torch.optim.Adam(policy.parameters(), lr=args.lr)

    # 创建训练器
    trainer = HRLTrainer(
        env=env,
        policy=policy,
        optimizer=optimizer,
        replay_buffer_size=args.replay_buffer_size,
        batch_size=args.batch_size,
        gamma=args.gamma,
        tau=args.tau,
        high_level_update_freq=args.high_level_update_freq,
        low_level_update_freq=args.low_level_update_freq,
        training_mode=args.training_mode,
        prioritized_replay=args.prioritized_replay,
        alpha=args.alpha,
        beta=args.beta,
        beta_increment=args.beta_increment,
        target_update_freq=args.target_update_freq,
        save_path=args.save_path,
        save_interval=args.save_interval,
        device=device
    )

    # 加载模型（如果有）
    if args.load_path and os.path.exists(args.load_path):
        trainer.load(args.load_path)
        logger.info(f"已加载模型: {args.load_path}")

    # 训练模型
    logger.info(f"开始训练: {args.num_episodes}回合，每回合最大{args.max_steps_per_episode}步")
    stats = trainer.train(args.num_episodes, args.max_steps_per_episode)

    # 打印训练统计信息
    logger.info("训练完成，统计信息:")
    logger.info(f"回合数: {stats['episodes']}")
    logger.info(f"总步数: {stats['steps']}")
    logger.info(f"高层更新次数: {stats['high_level_updates']}")
    logger.info(f"低层更新次数: {stats['low_level_updates']}")

    # 计算平均奖励
    if stats['rewards']:
        avg_reward = np.mean(stats['rewards'])
        logger.info(f"平均奖励: {avg_reward:.2f}")

    # 计算平均损失
    if stats['losses']['high_level']:
        avg_high_level_loss = np.mean(stats['losses']['high_level'])
        logger.info(f"高层平均损失: {avg_high_level_loss:.4f}")

    if stats['losses']['low_level']:
        avg_low_level_loss = np.mean(stats['losses']['low_level'])
        logger.info(f"低层平均损失: {avg_low_level_loss:.4f}")

    return 0


if __name__ == "__main__":
    main()
