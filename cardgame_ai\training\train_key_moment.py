#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
关键决策点检测器训练脚本

用于训练关键决策点检测器模型，识别对局中可能影响胜负的关键时刻。
"""

import os
import sys
import argparse
import logging
import json
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from cardgame_ai.algorithms.key_moment_detector import KeyMomentDetector
from cardgame_ai.games.doudizhu.state import DouDizhuState
from cardgame_ai.games.doudizhu.descriptor import DOUDIZHU_DESCRIPTOR

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class KeyMomentDataset(Dataset):
    """
    关键决策点数据集

    用于加载和处理关键决策点数据，包括游戏状态和标签。
    """

    def __init__(self, data_path: str, transform=None):
        """
        初始化数据集

        Args:
            data_path: 数据文件路径
            transform: 数据转换函数
        """
        self.data_path = data_path
        self.transform = transform
        self.samples = []

        # 加载数据
        self._load_data()

    def _load_data(self):
        """加载数据"""
        logger.info(f"加载数据: {self.data_path}")

        try:
            with open(self.data_path, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        # 解析JSON行
                        sample = json.loads(line.strip())

                        # 提取状态和标签
                        state_dict = sample.get('game_state')
                        label = sample.get('is_key_moment', 0.0)

                        if state_dict:
                            self.samples.append({
                                'state': state_dict,
                                'label': label
                            })
                    except json.JSONDecodeError:
                        logger.warning(f"无法解析JSON行: {line[:50]}...")
        except Exception as e:
            logger.error(f"加载数据失败: {e}")

        logger.info(f"加载了 {len(self.samples)} 个样本")

    def __len__(self):
        """返回数据集大小"""
        return len(self.samples)

    def __getitem__(self, idx):
        """获取指定索引的样本"""
        sample = self.samples[idx]

        # 转换状态为特征向量
        state_dict = sample['state']
        label = sample['label']

        # 应用转换（如果有）
        if self.transform:
            state_dict, label = self.transform(state_dict, label)

        return state_dict, label


def preprocess_state(state_dict):
    """
    预处理状态字典

    Args:
        state_dict: 状态字典

    Returns:
        np.ndarray: 预处理后的状态特征
    """
    # 如果是DouDizhuState对象的字典表示
    if isinstance(state_dict, dict) and 'hands' in state_dict:
        try:
            # 尝试从字典创建DouDizhuState对象
            state = DouDizhuState.from_dict(state_dict)
            # 获取观察
            return state.get_observation()
        except Exception as e:
            logger.warning(f"无法从字典创建DouDizhuState: {e}")

    # 如果已经是特征向量
    if isinstance(state_dict, list) and all(isinstance(x, (int, float)) for x in state_dict):
        return np.array(state_dict, dtype=np.float32)

    # 如果是特征字典
    if isinstance(state_dict, dict) and 'features' in state_dict:
        return np.array(state_dict['features'], dtype=np.float32)

    # 默认情况，尝试将字典扁平化为特征向量
    try:
        # 将字典转换为一维数组
        features = []
        for key, value in state_dict.items():
            if isinstance(value, (int, float)):
                features.append(value)

        if features:
            return np.array(features, dtype=np.float32)
    except Exception as e:
        logger.warning(f"无法将状态字典转换为特征向量: {e}")

    # 如果所有方法都失败，返回空特征向量
    logger.warning(f"无法处理状态字典，返回空特征向量: {state_dict}")
    return np.zeros(DOUDIZHU_DESCRIPTOR.state_shape[0], dtype=np.float32)


def train_key_moment_detector(
    train_data_path: str,
    val_data_path: Optional[str] = None,
    model_save_path: str = "models/key_moment_detector",
    state_dim: int = DOUDIZHU_DESCRIPTOR.state_shape[0],
    hidden_dims: List[int] = [256, 128, 64],
    batch_size: int = 32,
    learning_rate: float = 0.001,
    num_epochs: int = 50,
    use_attention: bool = True,
    use_history: bool = True,
    device: Optional[str] = None
) -> KeyMomentDetector:
    """
    训练关键决策点检测器

    Args:
        train_data_path: 训练数据路径
        val_data_path: 验证数据路径，可选
        model_save_path: 模型保存路径
        state_dim: 状态维度
        hidden_dims: 隐藏层维度列表
        batch_size: 批次大小
        learning_rate: 学习率
        num_epochs: 训练轮数
        use_attention: 是否使用注意力机制
        use_history: 是否使用历史状态
        device: 计算设备

    Returns:
        KeyMomentDetector: 训练好的模型
    """
    # 设置设备
    if device is None:
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
    logger.info(f"使用设备: {device}")

    # 创建保存目录
    os.makedirs(os.path.dirname(model_save_path), exist_ok=True)

    # 定义数据转换函数
    def transform_fn(state_dict, label):
        # 预处理状态
        state_features = preprocess_state(state_dict)
        # 确保标签是浮点数
        label = float(label)
        return state_features, label

    # 创建数据集
    train_dataset = KeyMomentDataset(train_data_path, transform=transform_fn)

    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=4
    )

    # 如果有验证数据，创建验证数据集和加载器
    val_loader = None
    if val_data_path:
        val_dataset = KeyMomentDataset(val_data_path, transform=transform_fn)
        val_loader = DataLoader(
            val_dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=4
        )

    # 创建模型
    model = KeyMomentDetector(
        state_dim=state_dim,
        hidden_dims=hidden_dims,
        output_dim=1,  # 回归模式
        use_attention=use_attention,
        use_history=use_history,
        device=device
    ).to(device)

    # 定义损失函数和优化器
    criterion = nn.BCELoss()  # 二元交叉熵损失
    optimizer = optim.Adam(model.parameters(), lr=learning_rate)

    # 训练循环
    best_val_loss = float('inf')
    best_epoch = 0

    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0

        for batch_states, batch_labels in train_loader:
            # 将数据移动到设备
            batch_states = torch.FloatTensor(np.stack(batch_states)).to(device)
            batch_labels = torch.FloatTensor(batch_labels).to(device)

            # 前向传播
            outputs = model(batch_states)

            # 计算损失
            loss = criterion(outputs, batch_labels.unsqueeze(1))

            # 反向传播和优化
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()

            # 累加损失
            train_loss += loss.item() * batch_states.size(0)

        # 计算平均训练损失
        train_loss = train_loss / len(train_dataset)

        # 验证阶段
        val_loss = 0.0
        if val_loader:
            model.eval()
            with torch.no_grad():
                for batch_states, batch_labels in val_loader:
                    # 将数据移动到设备
                    batch_states = torch.FloatTensor(np.stack(batch_states)).to(device)
                    batch_labels = torch.FloatTensor(batch_labels).to(device)

                    # 前向传播
                    outputs = model(batch_states)

                    # 计算损失
                    loss = criterion(outputs, batch_labels.unsqueeze(1))

                    # 累加损失
                    val_loss += loss.item() * batch_states.size(0)

                # 计算平均验证损失
                val_loss = val_loss / len(val_dataset)

        # 打印训练信息
        log_msg = f"Epoch {epoch+1}/{num_epochs}, Train Loss: {train_loss:.4f}"
        if val_loader:
            log_msg += f", Val Loss: {val_loss:.4f}"
        logger.info(log_msg)

        # 保存最佳模型
        if val_loader and val_loss < best_val_loss:
            best_val_loss = val_loss
            best_epoch = epoch

            # 保存模型
            model.save(model_save_path)
            logger.info(f"保存最佳模型 (Epoch {epoch+1}, Val Loss: {val_loss:.4f})")

    # 如果没有验证集，保存最后一个模型
    if not val_loader:
        model.save(model_save_path)
        logger.info(f"保存最终模型 (Epoch {num_epochs})")

    logger.info(f"训练完成. 最佳模型: Epoch {best_epoch+1}")

    return model


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='训练关键决策点检测器')

    parser.add_argument('--train_data', type=str, required=True,
                        help='训练数据路径')
    parser.add_argument('--val_data', type=str, default=None,
                        help='验证数据路径')
    parser.add_argument('--model_path', type=str, default='models/key_moment_detector.pth',
                        help='模型保存路径')
    parser.add_argument('--state_dim', type=int, default=DOUDIZHU_DESCRIPTOR.state_shape[0],
                        help='状态维度')
    parser.add_argument('--hidden_dims', type=str, default='256,128,64',
                        help='隐藏层维度，用逗号分隔')
    parser.add_argument('--batch_size', type=int, default=32,
                        help='批次大小')
    parser.add_argument('--lr', type=float, default=0.001,
                        help='学习率')
    parser.add_argument('--epochs', type=int, default=50,
                        help='训练轮数')
    parser.add_argument('--no_attention', action='store_false', dest='use_attention',
                        help='不使用注意力机制')
    parser.add_argument('--no_history', action='store_false', dest='use_history',
                        help='不使用历史状态')
    parser.add_argument('--device', type=str, default=None,
                        help='计算设备')

    return parser.parse_args()


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()

    # 解析隐藏层维度
    hidden_dims = [int(dim) for dim in args.hidden_dims.split(',')]

    # 训练模型
    model = train_key_moment_detector(
        train_data_path=args.train_data,
        val_data_path=args.val_data,
        model_save_path=args.model_path,
        state_dim=args.state_dim,
        hidden_dims=hidden_dims,
        batch_size=args.batch_size,
        learning_rate=args.lr,
        num_epochs=args.epochs,
        use_attention=args.use_attention,
        use_history=args.use_history,
        device=args.device
    )

    return 0


if __name__ == "__main__":
    main()
