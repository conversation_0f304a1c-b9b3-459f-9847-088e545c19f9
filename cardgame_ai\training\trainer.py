"""
训练系统模块

实现训练系统，包括批量训练流程、模型更新和保存、训练监控和早停机制，以及分布式训练支持。
"""
import os
import time
import json
import logging
import datetime
import numpy as np
from typing import Dict, Any, List, Tuple, Optional, Union, Callable, Iterator
from collections import deque

import torch
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel as DDP
from torch.utils.data import DataLoader

from cardgame_ai.core.base import Experience, Batch
from cardgame_ai.core.environment import Environment
from cardgame_ai.core.agent import Agent
from cardgame_ai.core.trainer import BaseTrainer
from cardgame_ai.core.evaluator import Evaluator
from cardgame_ai.training.self_play import SelfPlay
from cardgame_ai.data.human_feedback_loader import load_human_preference_data


class AdvancedTrainer(BaseTrainer):
    """
    高级训练器
    
    实现更完整的训练流程，包括批量训练、训练监控和早停机制。
    """
    
    def __init__(self, save_path: str = 'models', save_interval: int = 100, log_interval: int = 10,
                 checkpoint_interval: int = 1000, max_checkpoints: int = 10,
                 patience: int = 10, min_delta: float = 0.001, early_stop_metric: str = 'eval_win_rate'):
        """
        初始化高级训练器
        
        Args:
            save_path (str, optional): 模型保存路径. Defaults to 'models'.
            save_interval (int, optional): 保存间隔（回合数）. Defaults to 100.
            log_interval (int, optional): 日志间隔（回合数）. Defaults to 10.
            checkpoint_interval (int, optional): 检查点保存间隔. Defaults to 1000.
            max_checkpoints (int, optional): 最大保存检查点数. Defaults to 10.
            patience (int, optional): 早停耐心值. Defaults to 10.
            min_delta (float, optional): 早停最小提升阈值. Defaults to 0.001.
            early_stop_metric (str, optional): 早停监控指标. Defaults to 'eval_win_rate'.
        """
        super().__init__(save_path, save_interval, log_interval)
        
        self.checkpoint_interval = checkpoint_interval
        self.max_checkpoints = max_checkpoints
        self.patience = patience
        self.min_delta = min_delta
        self.early_stop_metric = early_stop_metric
        
        # 创建检查点目录
        self.checkpoint_dir = os.path.join(save_path, 'checkpoints')
        os.makedirs(self.checkpoint_dir, exist_ok=True)
        
        # 训练日志
        self.log_file = os.path.join(save_path, 'training_log.jsonl')
        
        # 检查点记录
        self.checkpoint_file = os.path.join(save_path, 'checkpoints.json')
        if os.path.exists(self.checkpoint_file):
            with open(self.checkpoint_file, 'r') as f:
                self.checkpoints = json.load(f)
        else:
            self.checkpoints = {'checkpoints': []}
    
    def train_with_experiences(self, agent: Agent, experiences: List[Experience], 
                              batch_size: int = 32, num_epochs: int = 1, 
                              shuffle: bool = True,
                              rlhf_data_path: Optional[str] = None,
                              rlhf_batch_size: Optional[int] = None,
                              rlhf_weight: float = 0.1) -> Dict[str, Any]:
        """
        使用已有经验数据训练代理
        
        Args:
            agent (Agent): 要训练的代理
            experiences (List[Experience]): 经验数据列表
            batch_size (int, optional): 批大小. Defaults to 32.
            num_epochs (int, optional): 训练轮次. Defaults to 1.
            shuffle (bool, optional): 是否打乱数据. Defaults to True.
            rlhf_data_path (Optional[str], optional): RLHF数据路径. Defaults to None.
            rlhf_batch_size (Optional[int], optional): RLHF批次大小. Defaults to None.
            rlhf_weight (float, optional): RLHF损失权重. Defaults to 0.1.
            
        Returns:
            Dict[str, Any]: 训练结果
        """
        start_time = time.time()
        metrics = {'losses': []}
        
        self.logger.info(f"开始使用 {len(experiences)} 条经验数据训练代理")
        
        # 转换为张量数据
        states = [exp.state for exp in experiences]
        actions = [exp.action for exp in experiences]
        rewards = [exp.reward for exp in experiences]
        next_states = [exp.next_state for exp in experiences]
        dones = [exp.done for exp in experiences]
        infos = [exp.info for exp in experiences]
        
        # 加载RLHF数据（如果指定）
        rlhf_train_loader = None
        rlhf_val_loader = None
        if rlhf_data_path and rlhf_batch_size:
            try:
                self.logger.info(f"加载RLHF数据：{rlhf_data_path}")
                rlhf_train_loader, rlhf_val_loader = load_human_preference_data(
                    data_path=rlhf_data_path,
                    batch_size=rlhf_batch_size,
                    shuffle=True,
                    num_workers=2
                )
                self.logger.info(f"RLHF数据加载成功：训练集 {len(rlhf_train_loader.dataset)} 样本，"
                                f"验证集 {len(rlhf_val_loader.dataset)} 样本")
                
                # 设置RLHF相关参数
                if hasattr(agent, 'use_rlhf'):
                    agent.use_rlhf = True
                if hasattr(agent, 'rlhf_loss_weight'):
                    agent.rlhf_loss_weight = rlhf_weight
            except Exception as e:
                self.logger.error(f"加载RLHF数据时出错：{e}")
                rlhf_train_loader = None
                rlhf_val_loader = None
        
        # 创建RLHF数据迭代器
        rlhf_iterator = None
        if rlhf_train_loader is not None:
            rlhf_iterator = iter(rlhf_train_loader)
        
        # 训练多个轮次
        for epoch in range(num_epochs):
            epoch_losses = []
            
            # 创建索引并可选打乱
            indices = np.arange(len(experiences))
            if shuffle:
                np.random.shuffle(indices)
            
            # 批量训练
            for i in range(0, len(indices), batch_size):
                batch_indices = indices[i:i+batch_size]
                
                # 创建批次
                batch = Batch(
                    states=[states[j] for j in batch_indices],
                    actions=[actions[j] for j in batch_indices],
                    rewards=[rewards[j] for j in batch_indices],
                    next_states=[next_states[j] for j in batch_indices],
                    dones=[dones[j] for j in batch_indices],
                    infos=[infos[j] for j in batch_indices]
                )
                
                # 获取RLHF批次（如果有）
                rlhf_batch = None
                if rlhf_iterator is not None:
                    try:
                        rlhf_batch = next(rlhf_iterator)
                    except StopIteration:
                        # 重置迭代器
                        rlhf_iterator = iter(rlhf_train_loader)
                        rlhf_batch = next(rlhf_iterator)
                
                # 训练单批次
                if hasattr(agent, 'train') and rlhf_batch is not None and hasattr(agent, 'use_rlhf'):
                    # 如果代理支持RLHF
                    loss = agent.train(batch, rlhf_batch)
                else:
                    # 标准训练
                    loss = agent.train_batch(batch)
                
                if loss:
                    epoch_losses.append(loss)
            
            # 计算本轮平均损失
            if epoch_losses:
                avg_loss = {k: np.mean([l[k] for l in epoch_losses if k in l]) for k in epoch_losses[0]}
                metrics['losses'].append(avg_loss)
                
                # 打印损失
                if (epoch + 1) % 5 == 0 or epoch == 0:
                    loss_str = " | ".join([f"{k}: {v:.4f}" for k, v in avg_loss.items()])
                    elapsed_time = time.time() - start_time
                    self.logger.info(
                        f"轮次 {epoch + 1}/{num_epochs} | "
                        f"损失: {loss_str} | "
                        f"时间: {elapsed_time:.2f}s"
                    )
        
        return metrics
    
    def train_with_self_play(self, env: Environment, agent: Agent, 
                           num_episodes: int, games_per_episode: int = 10,
                           eval_func: Optional[Callable[[Environment, Agent, int], Dict[str, Any]]] = None,
                           eval_interval: int = 100,
                           self_play_temp: float = 1.0,
                           batch_size: int = 32,
                           epochs_per_episode: int = 1,
                           max_buffer_size: int = 100000,
                           save_experiences: bool = True,
                           parallel: bool = True,
                           rlhf_data_path: Optional[str] = None,
                           rlhf_batch_size: Optional[int] = None,
                           rlhf_weight: float = 0.1) -> Dict[str, Any]:
        """
        使用自我对弈训练代理
        
        Args:
            env (Environment): 游戏环境
            agent (Agent): 要训练的代理
            num_episodes (int): 训练的回合数
            games_per_episode (int, optional): 每回合的游戏数. Defaults to 10.
            eval_func (Optional[Callable], optional): 评估函数. Defaults to None.
            eval_interval (int, optional): 评估间隔. Defaults to 100.
            self_play_temp (float, optional): 自我对弈温度参数. Defaults to 1.0.
            batch_size (int, optional): 批大小. Defaults to 32.
            epochs_per_episode (int, optional): 每回合训练轮次. Defaults to 1.
            max_buffer_size (int, optional): 最大缓冲区大小. Defaults to 100000.
            save_experiences (bool, optional): 是否保存经验. Defaults to True.
            parallel (bool, optional): 是否并行自我对弈. Defaults to True.
            rlhf_data_path (Optional[str], optional): RLHF数据路径. Defaults to None.
            rlhf_batch_size (Optional[int], optional): RLHF批次大小. Defaults to None.
            rlhf_weight (float, optional): RLHF损失权重. Defaults to 0.1.
            
        Returns:
            Dict[str, Any]: 训练结果
        """
        start_time = time.time()
        metrics = {
            'episode_losses': [],
            'eval_metrics': [],
            'self_play_times': [],
            'training_times': []
        }
        
        # 创建自我对弈系统
        self_play = SelfPlay(save_path=os.path.join(self.save_path, 'experiences'))
        
        # 创建经验缓冲区
        experience_buffer = deque(maxlen=max_buffer_size)
        
        # 加载RLHF数据（如果指定）
        rlhf_train_loader = None
        rlhf_val_loader = None
        if rlhf_data_path and rlhf_batch_size:
            try:
                self.logger.info(f"加载RLHF数据：{rlhf_data_path}")
                rlhf_train_loader, rlhf_val_loader = load_human_preference_data(
                    data_path=rlhf_data_path,
                    batch_size=rlhf_batch_size,
                    shuffle=True,
                    num_workers=2
                )
                self.logger.info(f"RLHF数据加载成功：训练集 {len(rlhf_train_loader.dataset)} 样本，"
                                f"验证集 {len(rlhf_val_loader.dataset)} 样本")
                
                # 设置RLHF相关参数
                if hasattr(agent, 'use_rlhf'):
                    agent.use_rlhf = True
                if hasattr(agent, 'rlhf_loss_weight'):
                    agent.rlhf_loss_weight = rlhf_weight
                    
                # 添加RLHF相关指标
                metrics['rlhf_metrics'] = {
                    'preference_loss': [],
                    'feedback_loss': [],
                    'imitation_loss': []
                }
            except Exception as e:
                self.logger.error(f"加载RLHF数据时出错：{e}")
                rlhf_train_loader = None
                rlhf_val_loader = None
        
        # 创建RLHF数据迭代器
        rlhf_iterator = None
        if rlhf_train_loader is not None:
            rlhf_iterator = iter(rlhf_train_loader)
        
        # 早停计数器和最佳指标
        patience_counter = 0
        best_metric = float('-inf')
        
        for episode in range(1, num_episodes + 1):
            episode_start_time = time.time()
            
            # 自我对弈生成数据
            self_play_start_time = time.time()
            self.logger.info(f"回合 {episode}/{num_episodes}：开始自我对弈")
            
            new_experiences = self_play.generate_games(
                env, agent, 
                num_games=games_per_episode,
                temperature=self_play_temp,
                save=save_experiences,
                parallel=parallel
            )
            
            self_play_time = time.time() - self_play_start_time
            metrics['self_play_times'].append(self_play_time)
            
            # 将新经验添加到缓冲区
            experience_buffer.extend(new_experiences)
            self.logger.info(f"回合 {episode}/{num_episodes}：收集了 {len(new_experiences)} 条经验，"
                          f"缓冲区现在包含 {len(experience_buffer)} 条经验。")
            
            # 使用缓冲区数据训练代理
            training_start_time = time.time()
            self.logger.info(f"回合 {episode}/{num_episodes}：开始训练")
            
            # 如果开启了RLHF，使用RLHF数据训练
            episode_losses = self.train_with_experiences(
                agent, 
                list(experience_buffer),
                batch_size=batch_size,
                num_epochs=epochs_per_episode,
                shuffle=True,
                rlhf_data_path=rlhf_data_path,
                rlhf_batch_size=rlhf_batch_size,
                rlhf_weight=rlhf_weight
            )
            
            training_time = time.time() - training_start_time
            metrics['training_times'].append(training_time)
            metrics['episode_losses'].append(episode_losses)
            
            # 在评估间隔时评估代理
            if eval_func is not None and episode % eval_interval == 0:
                self.logger.info(f"回合 {episode}/{num_episodes}：开始评估")
                eval_metrics = eval_func(env, agent, episode)
                metrics['eval_metrics'].append(eval_metrics)
                
                # 特别提取RLHF损失指标
                if rlhf_train_loader is not None and 'losses' in episode_losses:
                    last_epoch_loss = episode_losses['losses'][-1] if episode_losses['losses'] else {}
                    if 'rlhf_loss' in last_epoch_loss:
                        self.logger.info(f"回合 {episode}/{num_episodes} RLHF损失: {last_epoch_loss['rlhf_loss']:.4f}")
                    
                    # 可能还有更具体的RLHF相关损失
                    for loss_type in ['preference_loss', 'feedback_loss', 'imitation_loss']:
                        if loss_type in last_epoch_loss and 'rlhf_metrics' in metrics:
                            metrics['rlhf_metrics'][loss_type].append(last_epoch_loss[loss_type])
                
                # 判断是否需要早停
                if self.early_stop_metric in eval_metrics:
                    current_metric = eval_metrics[self.early_stop_metric]
                    
                    # 如果是需要最小化的指标（如错误率），则取负值
                    if self.early_stop_metric.endswith('loss') or self.early_stop_metric.endswith('error'):
                        current_metric = -current_metric
                    
                    # 检查是否有提升
                    if current_metric - best_metric > self.min_delta:
                        best_metric = current_metric
                        patience_counter = 0
                        
                        # 保存最佳模型
                        if hasattr(agent, 'save'):
                            best_model_path = os.path.join(self.save_path, 'best_model')
                            agent.save(best_model_path)
                            self.logger.info(f"回合 {episode}/{num_episodes}：保存最佳模型，"
                                          f"{self.early_stop_metric} = {eval_metrics[self.early_stop_metric]}")
                    else:
                        patience_counter += 1
                        self.logger.info(f"回合 {episode}/{num_episodes}：性能未提升，"
                                      f"耐心计数器: {patience_counter}/{self.patience}")
                        
                        # 如果达到耐心阈值，执行早停
                        if patience_counter >= self.patience:
                            self.logger.info(f"回合 {episode}/{num_episodes}：达到早停条件，停止训练")
                            # 加载最佳模型
                            if hasattr(agent, 'load'):
                                best_model_path = os.path.join(self.save_path, 'best_model')
                                if os.path.exists(best_model_path):
                                    agent.load(best_model_path)
                                    self.logger.info(f"回合 {episode}/{num_episodes}：加载最佳模型")
                            break
            
            # 计算并记录回合时间
            episode_time = time.time() - episode_start_time
            
            # 保存检查点
            if episode % self.checkpoint_interval == 0 and hasattr(agent, 'save'):
                checkpoint_path = os.path.join(self.checkpoint_dir, f'checkpoint_{episode}')
                agent.save(checkpoint_path)
                
                # 更新检查点记录
                self.checkpoints['checkpoints'].append({
                    'episode': episode,
                    'path': checkpoint_path,
                    'time': datetime.datetime.now().isoformat(),
                    'metrics': eval_metrics if eval_func is not None and episode % eval_interval == 0 else None
                })
                
                # 如果超过最大检查点数，删除最早的检查点
                if len(self.checkpoints['checkpoints']) > self.max_checkpoints:
                    oldest_checkpoint = self.checkpoints['checkpoints'].pop(0)
                    if os.path.exists(oldest_checkpoint['path']):
                        import shutil
                        shutil.rmtree(oldest_checkpoint['path'])
                
                # 保存检查点记录
                with open(self.checkpoint_file, 'w') as f:
                    json.dump(self.checkpoints, f, indent=2)
                
                self.logger.info(f"回合 {episode}/{num_episodes}：保存检查点 {checkpoint_path}")
            
            # 保存训练日志
            with open(self.log_file, 'a') as f:
                log_entry = {
                    'episode': episode,
                    'time': datetime.datetime.now().isoformat(),
                    'episode_time': episode_time,
                    'self_play_time': self_play_time,
                    'training_time': training_time,
                    'metrics': {
                        'losses': episode_losses.get('losses', [])[-1] if episode_losses.get('losses') else None,
                        'eval': eval_metrics if eval_func is not None and episode % eval_interval == 0 else None
                    }
                }
                
                # 如果有RLHF数据，记录RLHF相关指标
                if rlhf_train_loader is not None and 'losses' in episode_losses:
                    last_epoch_loss = episode_losses['losses'][-1] if episode_losses['losses'] else {}
                    if 'rlhf_loss' in last_epoch_loss:
                        log_entry['metrics']['rlhf_loss'] = last_epoch_loss['rlhf_loss']
                
                f.write(json.dumps(log_entry) + '\n')
            
            # 打印进度
            if episode % self.log_interval == 0:
                elapsed_time = time.time() - start_time
                episodes_per_sec = episode / elapsed_time
                est_time_left = (num_episodes - episode) / episodes_per_sec if episodes_per_sec > 0 else float('inf')
                
                self.logger.info(
                    f"回合 {episode}/{num_episodes} 完成 | "
                    f"总用时: {elapsed_time:.2f}s | "
                    f"回合用时: {episode_time:.2f}s | "
                    f"预计剩余时间: {est_time_left:.2f}s"
                )
        
        # 最终保存
        if hasattr(agent, 'save'):
            final_model_path = os.path.join(self.save_path, 'final_model')
            agent.save(final_model_path)
            self.logger.info(f"训练完成：最终模型保存到 {final_model_path}")
        
        total_time = time.time() - start_time
        self.logger.info(f"训练完成：总用时 {total_time:.2f}s")
        
        return metrics


class DistributedTrainer(AdvancedTrainer):
    """
    分布式训练器
    
    支持多GPU训练和分布式训练。
    """
    
    def __init__(self, save_path: str = 'models', save_interval: int = 100, log_interval: int = 10,
                 checkpoint_interval: int = 1000, max_checkpoints: int = 10,
                 patience: int = 10, min_delta: float = 0.001, early_stop_metric: str = 'eval_win_rate',
                 num_nodes: int = 1, gpus_per_node: int = 1, node_rank: int = 0,
                 master_addr: str = 'localhost', master_port: str = '12355'):
        """
        初始化分布式训练器
        
        Args:
            save_path (str, optional): 模型保存路径. Defaults to 'models'.
            save_interval (int, optional): 保存间隔（回合数）. Defaults to 100.
            log_interval (int, optional): 日志间隔（回合数）. Defaults to 10.
            checkpoint_interval (int, optional): 检查点保存间隔. Defaults to 1000.
            max_checkpoints (int, optional): 最大保存检查点数. Defaults to 10.
            patience (int, optional): 早停耐心值. Defaults to 10.
            min_delta (float, optional): 早停最小提升阈值. Defaults to 0.001.
            early_stop_metric (str, optional): 早停监控指标. Defaults to 'eval_win_rate'.
            num_nodes (int, optional): 节点数. Defaults to 1.
            gpus_per_node (int, optional): 每节点GPU数. Defaults to 1.
            node_rank (int, optional): 当前节点排名. Defaults to 0.
            master_addr (str, optional): 主节点地址. Defaults to 'localhost'.
            master_port (str, optional): 主节点端口. Defaults to '12355'.
        """
        super().__init__(save_path, save_interval, log_interval,
                         checkpoint_interval, max_checkpoints,
                         patience, min_delta, early_stop_metric)
        
        self.num_nodes = num_nodes
        self.gpus_per_node = gpus_per_node
        self.node_rank = node_rank
        self.world_size = num_nodes * gpus_per_node
        
        # 设置分布式环境变量
        os.environ['MASTER_ADDR'] = master_addr
        os.environ['MASTER_PORT'] = master_port
        
        self.is_distributed = self.world_size > 1
        self.distributed_initialized = False
    
    def init_distributed(self):
        """
        初始化分布式环境
        """
        if self.is_distributed and not self.distributed_initialized:
            # 初始化进程组
            dist.init_process_group(backend='nccl')
            self.distributed_initialized = True
            self.logger.info(
                f"分布式训练已初始化 | "
                f"世界大小: {dist.get_world_size()} | "
                f"排名: {dist.get_rank()}"
            )
    
    def is_master(self) -> bool:
        """
        检查当前进程是否为主进程
        
        Returns:
            bool: 是否为主进程
        """
        if not self.is_distributed:
            return True
        
        return dist.get_rank() == 0
    
    def wrap_model(self, model: torch.nn.Module) -> torch.nn.Module:
        """
        将模型包装为分布式模型
        
        Args:
            model (torch.nn.Module): 要包装的模型
            
        Returns:
            torch.nn.Module: 包装后的模型
        """
        if self.is_distributed:
            if not self.distributed_initialized:
                self.init_distributed()
            
            # 确保模型在相应的GPU上
            device_id = dist.get_rank() % torch.cuda.device_count()
            model = model.to(device_id)
            
            # 包装为DDP模型
            model = DDP(model, device_ids=[device_id])
            self.logger.info(
                f"模型已包装为DDP | "
                f"设备: {device_id} | "
                f"排名: {dist.get_rank()}"
            )
        
        return model
    
    def cleanup(self):
        """
        清理分布式环境
        """
        if self.is_distributed and self.distributed_initialized:
            dist.destroy_process_group()
            self.distributed_initialized = False
            self.logger.info("分布式训练环境已清理")
    
    def train_distributed(self, env_creator: Callable[[], Environment], agent_creator: Callable[[], Agent],
                        num_episodes: int, games_per_episode: int = 10,
                        eval_func: Optional[Callable[[Environment, Agent, int], Dict[str, Any]]] = None,
                        eval_interval: int = 100,
                        **kwargs) -> Dict[str, Any]:
        """
        分布式训练代理
        
        Args:
            env_creator (Callable[[], Environment]): 环境创建函数
            agent_creator (Callable[[], Agent]): 代理创建函数
            num_episodes (int): 训练的回合数
            games_per_episode (int, optional): 每回合的游戏数. Defaults to 10.
            eval_func (Optional[Callable], optional): 评估函数. Defaults to None.
            eval_interval (int, optional): 评估间隔. Defaults to 100.
            **kwargs: 其他参数
            
        Returns:
            Dict[str, Any]: 训练结果
        """
        try:
            # 初始化分布式环境
            self.init_distributed()
            
            # 创建环境和代理
            env = env_creator()
            agent = agent_creator()
            
            # 包装模型（如果代理有model属性）
            if hasattr(agent, 'model') and isinstance(agent.model, torch.nn.Module):
                agent.model = self.wrap_model(agent.model)
            
            # 调用普通训练函数
            metrics = self.train_with_self_play(
                env=env,
                agent=agent,
                num_episodes=num_episodes,
                games_per_episode=games_per_episode,
                eval_func=eval_func if self.is_master() else None,
                eval_interval=eval_interval,
                **kwargs
            )
            
            return metrics
        finally:
            # 清理分布式环境
            self.cleanup() 