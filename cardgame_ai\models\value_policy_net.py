"""
价值-策略网络模块

实现用于MCTS搜索的价值-策略网络，提供状态价值评估和动作概率预测。
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Tuple, Optional, Dict, Any


class ValuePolicyNet(nn.Module):
    """
    价值-策略网络
    
    用于MCTS搜索的神经网络，同时输出状态价值和动作概率分布。
    适用于斗地主等卡牌游戏的AI决策。
    """
    
    def __init__(
        self,
        input_dim: int = 108,
        hidden_dim: int = 512,
        action_dim: int = 310,
        num_layers: int = 3,
        dropout: float = 0.1,
        activation: str = 'relu'
    ):
        """
        初始化价值-策略网络
        
        Args:
            input_dim: 输入维度（观察空间大小）
            hidden_dim: 隐藏层维度
            action_dim: 动作空间大小
            num_layers: 隐藏层数量
            dropout: Dropout概率
            activation: 激活函数类型
        """
        super(ValuePolicyNet, self).__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.action_dim = action_dim
        self.num_layers = num_layers
        
        # 选择激活函数
        if activation == 'relu':
            self.activation = nn.ReLU()
        elif activation == 'tanh':
            self.activation = nn.Tanh()
        elif activation == 'gelu':
            self.activation = nn.GELU()
        else:
            self.activation = nn.ReLU()
        
        # 共享特征提取层
        layers = []
        current_dim = input_dim
        
        for i in range(num_layers):
            layers.append(nn.Linear(current_dim, hidden_dim))
            layers.append(self.activation)
            if dropout > 0:
                layers.append(nn.Dropout(dropout))
            current_dim = hidden_dim
            
        self.shared_layers = nn.Sequential(*layers)
        
        # 价值头 - 输出单个标量值
        self.value_head = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            self.activation,
            nn.Dropout(dropout) if dropout > 0 else nn.Identity(),
            nn.Linear(hidden_dim // 2, 1)
        )
        
        # 策略头 - 输出动作概率分布
        self.policy_head = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            self.activation,
            nn.Dropout(dropout) if dropout > 0 else nn.Identity(),
            nn.Linear(hidden_dim // 2, action_dim)
        )
        
        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化网络权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                # 使用Xavier初始化
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)
    
    def forward(self, x: torch.Tensor, action_mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            x: 输入状态，形状为 [batch_size, input_dim]
            action_mask: 动作掩码，形状为 [batch_size, action_dim]，1表示合法动作，0表示非法动作
            
        Returns:
            value: 状态价值，形状为 [batch_size, 1]
            policy_logits: 动作logits，形状为 [batch_size, action_dim]
        """
        # 确保输入是正确的形状
        if x.dim() == 1:
            x = x.unsqueeze(0)
        
        # 共享特征提取
        features = self.shared_layers(x)
        
        # 价值预测
        value = self.value_head(features)
        
        # 策略预测
        policy_logits = self.policy_head(features)
        
        # 应用动作掩码（如果提供）
        if action_mask is not None:
            # 将非法动作的logits设置为负无穷
            policy_logits = policy_logits + (action_mask - 1) * 1e9
        
        return value, policy_logits
    
    def predict_value(self, x: torch.Tensor) -> torch.Tensor:
        """
        仅预测状态价值
        
        Args:
            x: 输入状态
            
        Returns:
            value: 状态价值
        """
        with torch.no_grad():
            value, _ = self.forward(x)
            return value
    
    def predict_policy(self, x: torch.Tensor, action_mask: Optional[torch.Tensor] = None, temperature: float = 1.0) -> torch.Tensor:
        """
        仅预测动作概率分布
        
        Args:
            x: 输入状态
            action_mask: 动作掩码
            temperature: 温度参数，用于控制探索程度
            
        Returns:
            policy_probs: 动作概率分布
        """
        with torch.no_grad():
            _, policy_logits = self.forward(x, action_mask)
            
            # 应用温度参数
            if temperature != 1.0:
                policy_logits = policy_logits / temperature
            
            # 转换为概率分布
            policy_probs = F.softmax(policy_logits, dim=-1)
            return policy_probs
    
    def get_action(self, x: torch.Tensor, action_mask: Optional[torch.Tensor] = None, deterministic: bool = False) -> int:
        """
        根据当前策略选择动作
        
        Args:
            x: 输入状态
            action_mask: 动作掩码
            deterministic: 是否使用确定性策略
            
        Returns:
            action: 选择的动作索引
        """
        policy_probs = self.predict_policy(x, action_mask)
        
        if deterministic:
            # 选择概率最高的动作
            action = torch.argmax(policy_probs, dim=-1)
        else:
            # 根据概率分布采样
            action = torch.multinomial(policy_probs, 1).squeeze(-1)
        
        return action.item() if action.dim() == 0 else action[0].item()
    
    def compute_loss(
        self, 
        states: torch.Tensor, 
        actions: torch.Tensor, 
        values_target: torch.Tensor,
        policy_target: torch.Tensor,
        action_masks: Optional[torch.Tensor] = None,
        value_weight: float = 1.0,
        policy_weight: float = 1.0
    ) -> Dict[str, torch.Tensor]:
        """
        计算训练损失
        
        Args:
            states: 状态批次
            actions: 动作批次
            values_target: 目标价值
            policy_target: 目标策略分布
            action_masks: 动作掩码
            value_weight: 价值损失权重
            policy_weight: 策略损失权重
            
        Returns:
            losses: 包含各项损失的字典
        """
        # 前向传播
        values_pred, policy_logits = self.forward(states, action_masks)
        
        # 价值损失（MSE）
        value_loss = F.mse_loss(values_pred.squeeze(-1), values_target)
        
        # 策略损失（交叉熵）
        policy_loss = F.cross_entropy(policy_logits, policy_target)
        
        # 总损失
        total_loss = value_weight * value_loss + policy_weight * policy_loss
        
        return {
            'total_loss': total_loss,
            'value_loss': value_loss,
            'policy_loss': policy_loss
        }
    
    def save(self, path: str):
        """保存模型"""
        torch.save({
            'model_state_dict': self.state_dict(),
            'config': {
                'input_dim': self.input_dim,
                'hidden_dim': self.hidden_dim,
                'action_dim': self.action_dim,
                'num_layers': self.num_layers
            }
        }, path)
    
    def load(self, path: str):
        """加载模型"""
        checkpoint = torch.load(path, map_location='cpu')
        self.load_state_dict(checkpoint['model_state_dict'])
    
    @classmethod
    def from_checkpoint(cls, path: str) -> 'ValuePolicyNet':
        """从检查点创建模型"""
        checkpoint = torch.load(path, map_location='cpu')
        config = checkpoint['config']
        
        model = cls(**config)
        model.load_state_dict(checkpoint['model_state_dict'])
        
        return model
