"""
信任可视化模块

提供用于可视化AI决策过程的信任相关组件。
"""

import logging
from typing import Dict, List, Any, Optional
import time

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QTabWidget,
    QGroupBox, QSizePolicy, QScrollArea
)
from PySide6.QtCore import Qt, Signal, Slot

from cardgame_ai.data.metrics_collector import MetricsCollector
from cardgame_ai.ui.visualization_components import (
    ComplexityScoreWidget, ComponentUsageWidget, ValueChangeWidget
)

logger = logging.getLogger(__name__)


class TrustVisualizationWidget(QWidget):
    """信任可视化组件"""

    def __init__(self, parent=None):
        """初始化信任可视化组件"""
        super().__init__(parent)
        
        # 设置对象名称
        self.setObjectName("trustVisualizationWidget")
        
        # 创建指标收集器
        self.metrics_collector = MetricsCollector()
        
        # 初始化UI
        self.setup_ui()
        
        logger.info("初始化信任可视化组件")
        
    def setup_ui(self):
        """设置UI"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 创建标题标签
        title_label = QLabel("AI信任可视化")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        main_layout.addWidget(title_label)
        
        # 创建顶部区域（复杂度评分和组件调用统计）
        top_layout = QHBoxLayout()
        top_layout.setSpacing(10)
        
        # 创建复杂度评分组件
        self.complexity_score_widget = ComplexityScoreWidget()
        top_layout.addWidget(self.complexity_score_widget)
        
        # 创建组件调用统计组件
        self.component_usage_widget = ComponentUsageWidget()
        top_layout.addWidget(self.component_usage_widget)
        
        main_layout.addLayout(top_layout)
        
        # 创建价值变化组件
        self.value_change_widget = ValueChangeWidget()
        main_layout.addWidget(self.value_change_widget)
        
    def update_visualization(self, explanation_data: Dict[str, Any]):
        """
        更新可视化
        
        Args:
            explanation_data: 解释数据
        """
        # 收集指标
        metrics_data = self.metrics_collector.collect_metrics(explanation_data)
        
        # 更新复杂度评分
        complexity_score = metrics_data.get("complexity_score", 0.5)
        self.complexity_score_widget.set_complexity_score(complexity_score)
        
        # 更新组件调用统计
        component_calls = metrics_data.get("component_calls", {})
        self.component_usage_widget.update_component_usage(component_calls)
        
        # 更新价值变化
        value_history = metrics_data.get("value_history", [])
        self.value_change_widget.update_value_history(value_history)
        
        logger.debug("更新信任可视化")
        
    def clear_visualization(self):
        """清除可视化"""
        # 清除指标收集器历史记录
        self.metrics_collector.clear_history()
        
        # 重置复杂度评分
        self.complexity_score_widget.set_complexity_score(0.0)
        
        # 重置组件调用统计
        self.component_usage_widget.update_component_usage({
            "mcts": 0,
            "network": 0,
            "rule": 0,
            "hybrid": 0
        })
        
        # 重置价值变化
        self.value_change_widget.update_value_history([])
        
        logger.info("清除信任可视化")
