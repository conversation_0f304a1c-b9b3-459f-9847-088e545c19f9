"""
游戏环境模块

定义斗地主游戏环境及其交互方法。
"""
from typing import List, Dict, Any, Optional, Tuple, Union
import numpy as np
import random
import logging

from cardgame_ai.core.environment import Environment
from cardgame_ai.core.base import State, Action, Space
from cardgame_ai.games.doudizhu.card import Card, CardRank, CardSuit
from cardgame_ai.games.doudizhu.deck import Deck
from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType
from cardgame_ai.games.doudizhu.action import BidAction, GrabAction, DouDizhuAction
from cardgame_ai.games.doudizhu.state import DouDizhuState, GamePhase

logger = logging.getLogger(__name__)

class DouDizhuEnvironment(Environment):
    """
    斗地主游戏环境类

    实现斗地主游戏的环境和规则。
    """

    def __init__(self, seed: Optional[int] = None):
        """
        初始化游戏环境

        Args:
            seed (Optional[int], optional): 随机种子. Defaults to None.
        """
        self.rng = random.Random(seed)
        self.deck = Deck(seed)
        self.state = None
        self._num_players = 3
        self._state_shape = (656,)  # 观察特征的形状（包含游戏阶段、叫分历史等信息）
        self._action_shape = (1,)   # 动作的形状（简化处理）

        # 添加observation_space属性
        self.observation_space = Space(shape=self._state_shape)

        # 添加action_space属性
        # 动作空间的大小需要根据斗地主游戏的特性来确定
        # 根据get_observation返回的维度656和state.py中的注释，我们可以推断
        # 动作空间大小应该与特征维度相同
        self.action_space = Space(n=656)  # 动作空间的大小

    def reset(self) -> DouDizhuState:
        """
        重置游戏环境，返回初始状态

        Returns:
            DouDizhuState: 初始状态
        """
        # 发牌
        hands, landlord_cards = self.deck.deal(self._num_players)

        # 随机选择起始玩家（庄家）
        start_player = self.rng.randint(0, self._num_players - 1)

        # 创建初始状态（发牌阶段）
        self.state = DouDizhuState(
            hands=hands,
            landlord_cards=landlord_cards,  # 底牌
            landlord=None,  # 地主尚未确定
            current_player=start_player,  # 从随机选择的玩家开始
            game_phase=GamePhase.DEALING,  # 发牌阶段
            bid_history=[],
            grab_history=[],
            highest_bidder=None,
            highest_bid=0,
            last_move=None,
            last_player=None,
            num_passes=0,
            history=[],
            played_cards=[]
        )

        # 从发牌阶段进入叫地主阶段
        self.state = self._transition_to_bidding_phase(self.state)

        return self.state

    def _transition_to_bidding_phase(self, state: DouDizhuState) -> DouDizhuState:
        """
        从发牌阶段转换到叫地主阶段

        Args:
            state (DouDizhuState): 当前状态（发牌阶段）

        Returns:
            DouDizhuState: 新状态（叫地主阶段）
        """
        # 检查当前是否为发牌阶段
        if state.game_phase != GamePhase.DEALING:
            raise ValueError(f"无法从{state.game_phase.name}阶段转换到叫地主阶段")

        # 创建新状态，将游戏阶段设置为叫地主阶段
        new_state = DouDizhuState(
            hands=state.hands.copy(),
            landlord_cards=state.landlord_cards.copy(),
            landlord=state.landlord,
            current_player=(state.current_player + 1) % self._num_players,
            game_phase=GamePhase.BIDDING,  # 设置为叫地主阶段
            bid_history=state.bid_history.copy(),
            grab_history=state.grab_history.copy(),
            highest_bidder=state.highest_bidder,
            highest_bid=state.highest_bid,
            last_move=state.last_move,
            last_player=state.last_player,
            num_passes=state.num_passes,
            history=state.history.copy(),
            played_cards=state.played_cards.copy(),
            infoset=state.infoset.copy()
        )

        return new_state

    def step(self, action: DouDizhuAction) -> Tuple[DouDizhuState, float, bool, Dict[str, Any]]:
        """
        执行动作，返回新状态、奖励、是否结束和额外信息

        Args:
            action (DouDizhuAction): 要执行的动作

        Returns:
            Tuple[DouDizhuState, float, bool, Dict[str, Any]]:
                新状态、奖励、是否结束、额外信息
        """
        # 检查动作是否合法
        legal_actions = self.get_legal_actions(self.state)

        # 根据动作类型进行不同的比较
        if isinstance(action, BidAction) or isinstance(action, GrabAction):
            # 对于枚举类型的动作，比较名称和值
            action_found = False
            for legal_action in legal_actions:
                if isinstance(legal_action, type(action)) and legal_action.name == action.name and legal_action.value == action.value:
                    action_found = True
                    break
            if not action_found:
                raise ValueError(f"非法动作: {action}")
        elif action not in legal_actions:
            # 对于其他类型的动作，使用原有的比较方式
            raise ValueError(f"非法动作: {action}")

        # 获取当前玩家和游戏阶段
        current_player = self.state.current_player
        current_phase = self.state.game_phase

        # 初始化奖励
        reward = 0.0

        # 记录当前状态的潜在函数值（用于后续计算潜在函数型奖励）
        current_potential = self._calculate_potential(self.state, current_player)

        # 保存当前游戏阶段，用于后续处理
        original_phase = current_phase

        # 根据游戏阶段处理不同类型的动作
        if current_phase == GamePhase.BIDDING:
            # 叫地主阶段的奖励设计
            if isinstance(action, BidAction) and action.is_bid:
                # 鼓励叫地主，但叫分越高风险越大
                bid_value = action.value
                reward += 0.01 * bid_value  # 小奖励鼓励叫分

            # 记录当前玩家的叫分动作
            logger.info(f"    [局] 步 {len(self.state.bid_history)} (叫分阶段): P{current_player} 动作 {action.value} ({action}), 奖励 {reward:.2f}")

            # 处理叫分动作
            next_state = self._handle_bidding_action(self.state, action)
        elif current_phase == GamePhase.GRABBING:
            # 抢地主阶段的奖励设计
            if isinstance(action, GrabAction) and action.is_grab:
                # 鼓励抢地主
                reward += 0.02  # 小奖励鼓励抢地主
            next_state = self._handle_grabbing_action(self.state, action)
        elif current_phase == GamePhase.PLAYING:
            # 如果动作是牌列表，转换为CardGroup
            if isinstance(action, list):
                action = CardGroup(action)

            # 计算出牌阶段的即时奖励
            if action.card_type != CardGroupType.PASS:
                # 使用牌型价值计算函数获取基础奖励
                card_value = self._calculate_card_group_value(action)

                # 将牌型价值转换为适当的奖励大小（缩放到0.01-0.2范围）
                type_reward = min(card_value / 100.0, 0.2)

                # 计算出牌数量奖励（出牌数量越多越好）
                count_reward = min(0.01 * len(action.cards), 0.1)

                # 计算剩余牌数奖励（剩余牌越少越好）
                current_hand = self.state.hands[current_player]
                remaining_cards = len(current_hand) - len(action.cards)
                progress_reward = 0.0

                # 剩余牌数越少，奖励越高（指数增长）
                if remaining_cards == 0:  # 出完所有牌
                    progress_reward = 0.3  # 额外奖励
                elif remaining_cards <= 3:
                    progress_reward = 0.15
                elif remaining_cards <= 5:
                    progress_reward = 0.1
                elif remaining_cards <= 10:
                    progress_reward = 0.05

                # 如果有上一手牌，检查是否能打过
                combo_reward = 0.0
                if self.state.last_move is not None and self.state.last_player != current_player:
                    if action.can_beat(self.state.last_move):
                        combo_reward = 0.05  # 奖励打过其他玩家的牌

                # 检查是否是炸弹或火箭，给予奖励
                bomb_reward = 0.0
                if action.card_type in [CardGroupType.BOMB, CardGroupType.ROCKET]:
                    bomb_reward = 0.05  # 炸弹和火箭的奖励

                    # 记录炸弹使用（玩家级别）
                    if not hasattr(self.state, 'bomb_count'):
                        self.state.bomb_count = [0, 0, 0]  # 为每个玩家初始化计数器

                    # 增加当前玩家的炸弹使用计数
                    self.state.bomb_count[current_player] += 1

                # 如果是地主，根据角色调整奖励
                if self.state.landlord is not None:
                    is_landlord = (current_player == self.state.landlord)

                    # 地主和农民的策略不同，调整奖励
                    if is_landlord:
                        # 地主更注重快速出牌
                        role_factor = 1.0
                        reward += (type_reward + count_reward * 1.2 + progress_reward * 1.5 + combo_reward + bomb_reward) * role_factor
                    else:
                        # 农民更注重合作和牌型价值
                        role_factor = 0.9
                        base_reward = (type_reward * 1.2 + count_reward + progress_reward + combo_reward * 1.5 + bomb_reward) * role_factor

                        # 计算农民协作奖励
                        cooperation_reward = self._calculate_farmer_cooperation_reward(self.state, action, current_player)

                        # 将协作奖励添加到基础奖励中
                        reward += base_reward + cooperation_reward
                else:
                    # 如果地主还未确定，使用基础奖励
                    reward += type_reward + count_reward + bomb_reward

                # 对于"不出"牌，奖励为0，既不鼓励也不惩罚
            elif self.state.last_player != current_player:  # 只有在需要跟牌时才考虑不出
                reward += 0.0  # 将负奖励改为0

            next_state = self.state.get_next_state(action)

            # 计算潜在函数型奖励（仅在出牌阶段）
            if current_phase == GamePhase.PLAYING:
                # 计算个人潜在函数奖励
                next_potential = self._calculate_potential(next_state, current_player)
                gamma = 0.99  # 折扣因子
                personal_potential_reward = gamma * next_potential - current_potential

                # 计算团队潜在函数奖励（仅对农民）
                team_potential_reward = 0.0
                if self.state.landlord is not None and current_player != self.state.landlord:
                    team_potential_reward = self._calculate_team_potential_reward(self.state, next_state, current_player)

                # 将潜在函数型奖励添加到总奖励中
                # 对于地主，只使用个人潜在函数奖励
                # 对于农民，使用个人和团队潜在函数奖励的加权和
                if self.state.landlord is not None and current_player != self.state.landlord:
                    # 农民：个人奖励权重0.3，团队奖励权重0.7
                    reward += 0.3 * personal_potential_reward + 0.7 * team_potential_reward
                else:
                    # 地主：只使用个人奖励
                    reward += personal_potential_reward
        else:
            raise ValueError(f"不支持的游戏阶段: {current_phase}")

        self.state = next_state

        # 检查游戏是否结束
        done = self.state.is_terminal()

        # 检查游戏阶段是否发生变化
        if original_phase != self.state.game_phase:
            # 记录游戏阶段变化的详细信息
            logger.info(f"游戏阶段变化: 从 {original_phase.name} 转换到 {self.state.game_phase.name}")

            # 如果从叫分阶段转换到出牌阶段，添加额外信息
            if original_phase == GamePhase.BIDDING and self.state.game_phase == GamePhase.PLAYING:
                logger.info(f"游戏阶段已从叫分阶段转换到出牌阶段，地主为玩家{self.state.landlord}")
                logger.info(f"叫分历史: {self.state.bid_history}, 最高叫分: {self.state.highest_bid}, 最高叫分玩家: {self.state.highest_bidder}")
            # 如果从抢地主阶段转换到出牌阶段，添加额外信息
            elif original_phase == GamePhase.GRABBING and self.state.game_phase == GamePhase.PLAYING:
                logger.info(f"游戏阶段已从抢地主阶段转换到出牌阶段，地主为玩家{self.state.landlord}")
                logger.info(f"抢地主历史: {self.state.grab_history}")
            # 如果从叫分阶段转换到抢地主阶段，添加额外信息
            elif original_phase == GamePhase.BIDDING and self.state.game_phase == GamePhase.GRABBING:
                logger.info(f"游戏阶段已从叫分阶段转换到抢地主阶段，最高叫分玩家: {self.state.highest_bidder}, 最高叫分: {self.state.highest_bid}")
                logger.info(f"叫分历史: {self.state.bid_history}")

        # 如果游戏结束，更新奖励
        if done:
            # 获取基础收益
            payoffs = self.state.get_payoffs()
            base_reward = payoffs[current_player]

            # 检查是否使用了炸弹
            bomb_used = False
            if hasattr(self.state, 'bomb_count'):
                bomb_used = sum(self.state.bomb_count) > 0

            # 根据炸弹使用情况调整终局奖励
            if bomb_used:
                # 如果使用了炸弹，根据胜负调整奖励
                if base_reward > 0:  # 胜利
                    reward = 2.0  # 使用炸弹且赢了，奖励+2.0
                else:  # 失败
                    reward = -2.5  # 使用炸弹但输了，惩罚-2.5
            else:
                # 如果没有使用炸弹，使用标准奖励
                reward = base_reward

        # 额外信息
        info = {
            'payoffs': self.state.get_payoffs() if done else [0.0, 0.0, 0.0],
            'legal_actions': self.get_legal_actions(self.state),  # 确保使用最新状态获取合法动作
            'original_phase': original_phase,
            'current_phase': self.state.game_phase,
            'game_phase': self.state.game_phase  # 添加游戏阶段信息，用于日志记录
        }

        return self.state, reward, done, info

    def get_legal_actions(self, state: Optional[DouDizhuState] = None) -> List[Union[CardGroup, BidAction, GrabAction]]:
        """
        获取在给定状态下的合法动作列表

        Args:
            state (Optional[DouDizhuState], optional): 游戏状态. Defaults to None.

        Returns:
            List[Union[CardGroup, BidAction, GrabAction]]: 合法动作列表
        """
        if state is None:
            state = self.state

        # 确保使用最新的游戏阶段
        current_phase = state.game_phase

        # 根据游戏阶段返回不同的合法动作
        if current_phase == GamePhase.BIDDING:
            return self._get_legal_bidding_actions(state)
        elif current_phase == GamePhase.GRABBING:
            return self._get_legal_grabbing_actions(state)
        elif current_phase == GamePhase.PLAYING:
            return state.get_legal_actions()  # 使用原有的方法处理出牌阶段
        else:
            raise ValueError(f"不支持的游戏阶段: {current_phase}")

    def _get_legal_bidding_actions(self, state: DouDizhuState) -> List[BidAction]:
        """
        获取叫地主阶段的合法动作

        Args:
            state (DouDizhuState): 游戏状态

        Returns:
            List[BidAction]: 合法的叫分动作列表
        """
        # 玩家可以选择不叫（0分）
        legal_actions = [BidAction.PASS]

        # 玩家可以叫比当前最高分更高的分数
        for bid in [BidAction.BID_1, BidAction.BID_2, BidAction.BID_3]:
            if bid.value > state.highest_bid:
                legal_actions.append(bid)

        return legal_actions

    def _get_legal_grabbing_actions(self, state: DouDizhuState) -> List[GrabAction]:
        """
        获取抢地主阶段的合法动作

        Args:
            state (DouDizhuState): 游戏状态

        Returns:
            List[GrabAction]: 合法的抢地主动作列表
        """
        # 玩家可以选择抢地主或不抢
        return [GrabAction.PASS, GrabAction.GRAB]

    def _handle_bidding_action(self, state: DouDizhuState, action: BidAction) -> DouDizhuState:
        """
        处理叫地主阶段的动作

        Args:
            state (DouDizhuState): 当前状态
            action (BidAction): 叫分动作

        Returns:
            DouDizhuState: 新状态
        """
        # 检查当前是否为叫地主阶段
        if state.game_phase != GamePhase.BIDDING:
            raise ValueError(f"当前不是叫地主阶段，无法处理叫分动作")

        # 获取当前玩家和叫分值
        current_player = state.current_player
        bid_value = action.value

        # 检查叫分是否有效（必须大于当前最高分，除非是不叫）
        if bid_value > 0 and bid_value <= state.highest_bid:
            raise ValueError(f"叫分必须大于当前最高分: {state.highest_bid}")

        # 更新叫分历史
        new_bid_history = state.bid_history.copy()
        new_bid_history.append((current_player, bid_value))

        # 记录日志，帮助调试 - 提升为INFO级别以便在训练日志中可见
        logger.info(f"玩家 {current_player} 叫分 {bid_value}，叫分历史: {new_bid_history}")

        # 记录当前玩家和下一个玩家
        next_player = (current_player + 1) % self._num_players
        logger.debug(f"当前玩家: {current_player}, 下一个玩家: {next_player}")

        # 更新最高分和最高叫分玩家
        new_highest_bid = state.highest_bid
        new_highest_bidder = state.highest_bidder
        if bid_value > new_highest_bid:
            new_highest_bid = bid_value
            new_highest_bidder = current_player

            # 如果有玩家叫了3分，立即结束叫分阶段
            if bid_value == BidAction.BID_3.value:
                logger.info(f"玩家 {current_player} 叫3分，立即结束叫分阶段")
                # 创建新状态，设置最高叫分者为地主
                new_state = DouDizhuState(
                    hands=state.hands.copy(),
                    landlord_cards=state.landlord_cards.copy(),
                    landlord=current_player,  # 设置地主
                    current_player=current_player,  # 当前玩家不变
                    game_phase=state.game_phase,
                    bid_history=new_bid_history,
                    grab_history=state.grab_history.copy(),
                    highest_bidder=current_player,
                    highest_bid=bid_value,
                    last_move=state.last_move,
                    last_player=state.last_player,
                    num_passes=0,  # 重置连续不出的次数
                    history=state.history.copy(),
                    played_cards=state.played_cards.copy(),
                    infoset=state.infoset.copy()
                )
                return self._transition_to_playing_phase(new_state, current_player)

        # 计算连续不叫次数（仅在已有有效叫分后计数）
        if not action.is_bid:
            new_num_passes = state.num_passes + 1 if state.highest_bid > 0 else 0
        else:
            new_num_passes = 0

        # 确定下一个玩家
        next_player = (current_player + 1) % self._num_players

        # 创建新状态
        new_state = DouDizhuState(
            hands=state.hands.copy(),
            landlord_cards=state.landlord_cards.copy(),
            landlord=state.landlord,
            current_player=next_player,
            game_phase=state.game_phase,
            bid_history=new_bid_history,
            grab_history=state.grab_history.copy(),
            highest_bidder=new_highest_bidder,
            highest_bid=new_highest_bid,
            last_move=state.last_move,
            last_player=state.last_player,
            num_passes=new_num_passes,
            history=state.history.copy(),
            played_cards=state.played_cards.copy(),
            infoset=state.infoset.copy()
        )

        # 叫分结束判定：
        # - 无人叫分且所有玩家都已操作，则由首位叫分玩家成为地主，底分为1分
        # - 有人叫分且连续两次不叫后，或有人叫3分后，结束叫分并进入出牌阶段

        # 确保所有玩家都有机会叫分 - 检查是否所有玩家都已经叫过分
        players_who_bid = set(player for player, _ in new_bid_history)

        # 添加额外检查，确保所有玩家索引都在players_who_bid中
        all_players_set = set(range(self._num_players))
        all_players_have_bid = all_players_set.issubset(players_who_bid)

        # 记录日志，帮助调试
        logger.debug(f"叫分结束判定: 已叫分玩家集合={players_who_bid}, 玩家数={self._num_players}, 最高叫分={new_highest_bid}, 连续不叫次数={new_num_passes}")
        logger.debug(f"所有玩家都已叫分? {all_players_have_bid}, 所有玩家集合={all_players_set}")

        # 只有当所有玩家都已经叫过分时，才进行结束判定
        # 确保所有三个玩家都有机会叫分，即使有连续的"不叫"动作
        # 修复：使用严格相等而不是大于等于，确保所有玩家都叫过分
        # 同时使用all_players_have_bid进行双重检查，确保所有玩家索引都在players_who_bid中
        if len(players_who_bid) == self._num_players and all_players_have_bid:
            logger.debug(f"所有玩家都已叫分，进行结束判定")
            if new_highest_bid == 0:
                # 无人叫分，首位玩家成为地主，底分1分
                first_bidder = new_bid_history[0][0]
                logger.info(f"无人叫分，玩家 {first_bidder} 成为地主，底分为1分")
                new_state.highest_bidder = first_bidder
                new_state.highest_bid = 1

                # 记录当前玩家的动作和状态，确保日志记录的一致性
                logger.info(f"叫分历史: {new_bid_history}, 最高叫分: {new_state.highest_bid}, 最高叫分玩家: {new_state.highest_bidder}")

                # 转换到出牌阶段
                return self._transition_to_playing_phase(new_state, first_bidder)
            # 注意：叫3分的情况已经在前面处理，这里不需要重复判断
            elif new_num_passes >= self._num_players - 1 and len(new_bid_history) >= self._num_players:
                # 连续两次不叫且所有玩家都已叫过分，结束叫分，最高叫分者成为地主
                logger.debug(f"连续两次不叫且所有玩家都已叫过分，叫分结束，玩家 {new_highest_bidder} 成为地主，叫分为{new_highest_bid}")

                # 记录当前玩家的动作和状态，确保日志记录的一致性
                logger.info(f"叫分历史: {new_bid_history}, 最高叫分: {new_highest_bid}, 最高叫分玩家: {new_highest_bidder}")

                # 转换到出牌阶段
                return self._transition_to_playing_phase(new_state, new_highest_bidder)
        return new_state

    def _transition_to_grabbing_phase(self, state: DouDizhuState) -> DouDizhuState:
        """
        从叫地主阶段转换到抢地主阶段

        Args:
            state (DouDizhuState): 当前状态（叫地主阶段）

        Returns:
            DouDizhuState: 新状态（抢地主阶段）
        """
        # 检查当前是否为叫地主阶段
        if state.game_phase != GamePhase.BIDDING:
            raise ValueError(f"无法从{state.game_phase.name}阶段转换到抢地主阶段")

        # 检查是否有人叫了分
        if state.highest_bid <= 0 or state.highest_bidder is None:
            raise ValueError("没有玩家叫分，无法进入抢地主阶段")

        # 创建新状态，将游戏阶段设置为抢地主阶段
        new_state = DouDizhuState(
            hands=state.hands.copy(),
            landlord_cards=state.landlord_cards.copy(),
            landlord=None,  # 地主尚未确定
            current_player=(state.highest_bidder + 1) % 3,  # 从最高叫分玩家的下一个玩家开始抢地主
            game_phase=GamePhase.GRABBING,  # 设置为抢地主阶段
            bid_history=state.bid_history.copy(),
            grab_history=state.grab_history.copy(),
            highest_bidder=state.highest_bidder,
            highest_bid=state.highest_bid,
            last_move=state.last_move,
            last_player=state.last_player,
            num_passes=state.num_passes,
            history=state.history.copy(),
            played_cards=state.played_cards.copy(),
            infoset=state.infoset.copy()
        )

        return new_state

    def _handle_grabbing_action(self, state: DouDizhuState, action: GrabAction) -> DouDizhuState:
        """
        处理抢地主阶段的动作

        Args:
            state (DouDizhuState): 当前状态
            action (GrabAction): 抢地主动作

        Returns:
            DouDizhuState: 新状态
        """
        # 检查当前是否为抢地主阶段
        if state.game_phase != GamePhase.GRABBING:
            raise ValueError(f"当前不是抢地主阶段，无法处理抢地主动作")

        # 获取当前玩家
        current_player = state.current_player

        # 获取抢地主选择
        is_grab = action.is_grab

        # 更新抢地主历史
        new_grab_history = state.grab_history.copy()
        new_grab_history.append((current_player, is_grab))

        # 更新地主候选人
        new_landlord_candidate = state.highest_bidder
        if is_grab:
            new_landlord_candidate = current_player

        # 确定下一个玩家
        next_player = (current_player + 1) % 3

        # 创建新状态
        new_state = DouDizhuState(
            hands=state.hands.copy(),
            landlord_cards=state.landlord_cards.copy(),
            landlord=state.landlord,
            current_player=next_player,
            game_phase=state.game_phase,
            bid_history=state.bid_history.copy(),
            grab_history=new_grab_history,
            highest_bidder=new_landlord_candidate,  # 更新地主候选人
            highest_bid=state.highest_bid,
            last_move=state.last_move,
            last_player=state.last_player,
            num_passes=state.num_passes,
            history=state.history.copy(),
            played_cards=state.played_cards.copy(),
            infoset=state.infoset.copy()
        )

        # 检查是否所有玩家都做出了选择
        # 抢地主阶段最多进行一轮，每个玩家最多做出一次选择
        # 如果抢地主历史的长度等于玩家数量，说明所有玩家都做出了选择
        if len(new_grab_history) >= 3:
            # 确定最终的地主
            final_landlord = new_landlord_candidate

            # 进入出牌阶段
            new_state = self._transition_to_playing_phase(new_state, final_landlord)

        return new_state

    def _transition_to_playing_phase(self, state: DouDizhuState, landlord: int) -> DouDizhuState:
        """
        从抢地主或叫地主阶段转换到出牌阶段

        Args:
            state (DouDizhuState): 当前状态（可从叫地主或抢地主阶段）
            landlord (int): 地主玩家索引

        Returns:
            DouDizhuState: 新状态（出牌阶段）
        """
        # 检查当前是否允许直接进入出牌阶段（可从叫地主或抢地主阶段）
        if state.game_phase not in (GamePhase.BIDDING, GamePhase.GRABBING):
            raise ValueError(f"无法从{state.game_phase.name}阶段转换到出牌阶段")

        # 记录转换信息
        logger.info(f"游戏阶段转换: 从{state.game_phase.name}阶段转换到出牌阶段，地主为玩家{landlord}")

        # 确保所有玩家的叫分动作都已经处理完毕
        if state.game_phase == GamePhase.BIDDING:
            # 检查是否所有玩家都已经叫过分
            players_who_bid = set(player for player, _ in state.bid_history)
            all_players_set = set(range(self._num_players))

            if not all_players_set.issubset(players_who_bid):
                logger.warning(f"警告：在所有玩家都叫分之前转换到出牌阶段。叫分历史：{state.bid_history}")

        # 复制手牌
        new_hands = [hand.copy() for hand in state.hands]

        # 地主获得底牌
        new_hands[landlord].extend(state.landlord_cards)
        new_hands[landlord].sort()  # 排序手牌

        # 创建新状态，将游戏阶段设置为出牌阶段
        new_state = DouDizhuState(
            hands=new_hands,
            landlord_cards=state.landlord_cards.copy(),
            landlord=landlord,  # 设置地主
            current_player=landlord,  # 地主先出牌
            game_phase=GamePhase.PLAYING,  # 设置为出牌阶段
            bid_history=state.bid_history.copy(),
            grab_history=state.grab_history.copy(),
            highest_bidder=state.highest_bidder,
            highest_bid=state.highest_bid,
            last_move=None,  # 重置上一手牌
            last_player=None,  # 重置上一个出牌的玩家
            num_passes=0,  # 重置连续不出的次数
            history=state.history.copy(),
            played_cards=state.played_cards.copy(),
            infoset=state.infoset.copy()
        )

        # 记录日志，确保游戏阶段转换后的状态正确
        logger.info(f"游戏阶段转换完成: 当前阶段={new_state.game_phase.name}, 地主={new_state.landlord}, 当前玩家={new_state.current_player}")

        return new_state

    def get_observation(self, state: Optional[DouDizhuState] = None) -> np.ndarray:
        """
        获取状态的观察表示

        Args:
            state (Optional[DouDizhuState], optional): 游戏状态. Defaults to None.

        Returns:
            np.ndarray: 观察表示
        """
        if state is None:
            state = self.state

        return state.get_observation()

    def is_terminal(self, state: Optional[DouDizhuState] = None) -> bool:
        """
        判断状态是否为终止状态

        Args:
            state (Optional[DouDizhuState], optional): 游戏状态. Defaults to None.

        Returns:
            bool: 是否为终止状态
        """
        if state is None:
            state = self.state

        return state.is_terminal()

    def get_payoffs(self, state: Optional[DouDizhuState] = None) -> List[float]:
        """
        获取终止状态下各玩家的收益

        Args:
            state (Optional[DouDizhuState], optional): 游戏状态. Defaults to None.

        Returns:
            List[float]: 各玩家的收益
        """
        if state is None:
            state = self.state

        # 如果游戏未结束，返回全0
        if not state.is_terminal():
            return [0.0, 0.0, 0.0]

        # 初始化奖励
        rewards = [0.0, 0.0, 0.0]

        # 找出获胜的玩家
        winner = -1
        for i, hand in enumerate(state.hands):
            if not hand:  # 手牌为空，表示出完了
                winner = i
                break

        # 检查炸弹使用情况
        bomb_used = False
        if hasattr(state, 'bomb_count'):
            bomb_used = sum(state.bomb_count) > 0

        # 根据炸弹使用情况调整基础奖励值
        if bomb_used:
            win_reward = 2.0   # 使用炸弹且赢了
            lose_reward = -2.5  # 使用炸弹但输了
        else:
            win_reward = 1.0   # 标准胜利奖励
            lose_reward = -1.0  # 标准失败惩罚

        # 地主获胜
        if winner == state.landlord:
            rewards[state.landlord] = win_reward
            # 农民共享失败惩罚
            for i in range(3):
                if i != state.landlord:
                    rewards[i] = lose_reward
        # 农民获胜
        else:
            rewards[state.landlord] = lose_reward
            # 农民共享胜利奖励，无论是哪个农民出完牌
            for i in range(3):
                if i != state.landlord:
                    rewards[i] = win_reward

        return rewards

    @property
    def num_players(self) -> int:
        """
        获取游戏的玩家数量

        Returns:
            int: 玩家数量
        """
        return self._num_players

    @property
    def state_shape(self) -> Tuple[int, ...]:
        """
        获取状态的形状

        Returns:
            Tuple[int, ...]: 状态的形状
        """
        return self._state_shape

    @property
    def action_shape(self) -> Tuple[int, ...]:
        """
        获取动作的形状

        Returns:
            Tuple[int, ...]: 动作的形状
        """
        return self._action_shape

    def render(self, mode: str = 'human') -> Optional[np.ndarray]:
        """
        渲染游戏状态

        Args:
            mode (str, optional): 渲染模式. Defaults to 'human'.

        Returns:
            Optional[np.ndarray]: 渲染结果
        """
        if mode == 'human':
            print(self.state)
            return None
        elif mode == 'rgb_array':
            # 简单返回一个空数组，实际实现中应该返回游戏画面
            return np.zeros((400, 600, 3), dtype=np.uint8)
        else:
            raise ValueError(f"不支持的渲染模式: {mode}")

    def close(self) -> None:
        """
        关闭环境
        """
        pass

    def seed(self, seed: Optional[int] = None) -> List[int]:
        """
        设置随机种子

        Args:
            seed (Optional[int], optional): 随机种子. Defaults to None.

        Returns:
            List[int]: 使用的随机种子列表
        """
        if seed is None:
            seed = random.randint(0, 2**32 - 1)

        self.rng = random.Random(seed)
        self.deck = Deck(seed)

        return [seed]

    def _calculate_card_group_value(self, card_group: CardGroup) -> float:
        """
        计算牌型的价值，用于奖励计算

        Args:
            card_group (CardGroup): 牌型

        Returns:
            float: 牌型价值
        """
        if card_group.card_type == CardGroupType.PASS:
            return 0.0

        # 基础价值
        base_values = {
            CardGroupType.SINGLE: 1.0,
            CardGroupType.PAIR: 2.0,
            CardGroupType.TRIO: 3.0,
            CardGroupType.TRIO_WITH_SINGLE: 4.0,
            CardGroupType.TRIO_WITH_PAIR: 5.0,
            CardGroupType.STRAIGHT: 6.0,
            CardGroupType.STRAIGHT_PAIR: 7.0,
            CardGroupType.AIRPLANE: 8.0,
            CardGroupType.AIRPLANE_WITH_SINGLE: 9.0,
            CardGroupType.AIRPLANE_WITH_PAIR: 10.0,
            CardGroupType.FOUR_WITH_TWO_SINGLE: 11.0,
            CardGroupType.FOUR_WITH_TWO_PAIR: 12.0,
            CardGroupType.BOMB: 20.0,
            CardGroupType.ROCKET: 30.0
        }

        base_value = base_values.get(card_group.card_type, 0.0)

        # 根据牌的点数调整价值
        rank_factor = 1.0
        if card_group.main_rank is not None:
            # 点数越大，价值越高
            rank_value = int(card_group.main_rank)
            rank_factor = 1.0 + (rank_value / 20.0)  # 最大为1.75 (大王)

        # 根据牌的数量调整价值
        count_factor = min(1.0 + (len(card_group.cards) / 20.0), 1.5)  # 最大为1.5

        # 计算最终价值
        final_value = base_value * rank_factor * count_factor

        return final_value

    def _calculate_potential(self, state: DouDizhuState, player_id: int) -> float:
        """
        计算潜在函数值

        潜在函数定义为: φ(s) = -（手牌剩余张数 / 初始手牌张数）

        Args:
            state (DouDizhuState): 游戏状态
            player_id (int): 玩家ID

        Returns:
            float: 潜在函数值
        """
        # 如果游戏不在出牌阶段，返回0
        if state.game_phase != GamePhase.PLAYING:
            return 0.0

        # 获取玩家手牌
        hand = state.hands[player_id]

        # 计算初始手牌数量（地主20张，农民17张）
        initial_hand_count = 20 if player_id == state.landlord else 17

        # 计算潜在函数值: φ(s) = -（手牌剩余张数 / 初始手牌张数）
        potential = -(len(hand) / initial_hand_count)

        return potential

    def _calculate_team_potential(self, state: DouDizhuState) -> float:
        """
        计算农民团队的潜在函数值

        潜在函数定义为: φ(s) = -（农民团队剩余手牌总数 / 初始手牌总数）

        Args:
            state: 游戏状态

        Returns:
            float: 团队潜在函数值
        """
        # 如果游戏不在出牌阶段，返回0
        if state.game_phase != GamePhase.PLAYING:
            return 0.0

        # 找出农民
        farmers = [i for i in range(3) if i != state.landlord]

        # 计算农民团队的手牌总数
        team_cards_count = sum(len(state.hands[farmer]) for farmer in farmers)

        # 计算初始手牌总数（两个农民各17张）
        initial_team_cards = 17 * 2

        # 计算团队潜在函数值: φ(s) = -（农民团队剩余手牌总数 / 初始手牌总数）
        potential = -(team_cards_count / initial_team_cards)

        return potential

    def _calculate_team_potential_reward(self, old_state: DouDizhuState, new_state: DouDizhuState, player_id: int) -> float:
        """
        计算团队潜在函数奖励

        Args:
            old_state: 动作前的状态
            new_state: 动作后的状态
            player_id: 玩家ID

        Returns:
            float: 潜在函数奖励
        """
        # 如果玩家是地主或游戏不在出牌阶段，返回0
        if old_state.landlord == player_id or old_state.game_phase != GamePhase.PLAYING:
            return 0.0

        # 计算动作前后的团队潜在函数值
        old_potential = self._calculate_team_potential(old_state)
        new_potential = self._calculate_team_potential(new_state)

        # 计算潜在函数奖励: F(s,a,s′) = γ·φ(s′) - φ(s)
        gamma = 0.99  # 折扣因子
        potential_reward = gamma * new_potential - old_potential

        # 归一化奖励，控制在合理范围内
        # 最大可能的单步变化是出完所有牌（约17张），最小是0
        max_possible_change = 17 / (17 * 2)
        normalized_reward = potential_reward / max_possible_change * 0.1  # 缩放到0.1范围内

        return normalized_reward

    def _calculate_key_cooperation_reward(self, state: DouDizhuState, action, player_id: int) -> float:
        """
        计算关键协作行为奖励（精简版）

        Args:
            state: 游戏状态
            action: 执行的动作
            player_id: 玩家ID

        Returns:
            float: 协作行为奖励
        """
        # 如果玩家是地主或游戏不在出牌阶段，返回0
        if state.landlord == player_id or state.game_phase != GamePhase.PLAYING:
            return 0.0

        # 找出队友（另一个农民）
        teammate = None
        for i in range(3):
            if i != player_id and i != state.landlord:
                teammate = i
                break

        if teammate is None:
            return 0.0

        cooperation_reward = 0.0

        # 只处理出牌阶段的CardGroup类型动作
        if isinstance(action, CardGroup):
            # 规则1: 队友出牌后选择不出（让队友控制节奏）
            if action.card_type == CardGroupType.PASS and state.last_player == teammate:
                cooperation_reward += 0.02

            # 规则2: 队友手牌少时（<=3张）避免出单张大牌
            elif len(state.hands[teammate]) <= 3 and action.card_type == CardGroupType.SINGLE:
                if action.cards[0].rank >= CardRank.QUEEN:
                    cooperation_reward -= 0.03

            # 规则3: 队友不出后接牌（接管控制权）
            if state.last_player == teammate and state.last_move and state.last_move.card_type == CardGroupType.PASS and action.card_type != CardGroupType.PASS:
                cooperation_reward += 0.02

            # 规则4: 地主手牌少时使用关键控制牌（阻止地主）
            landlord = state.landlord
            if landlord is not None and len(state.hands[landlord]) <= 3:
                if action.card_type in [CardGroupType.BOMB, CardGroupType.ROCKET]:
                    cooperation_reward += 0.05
                elif action.card_type == CardGroupType.SINGLE and action.cards[0].rank >= CardRank.ACE:
                    cooperation_reward += 0.03

        return cooperation_reward

    def _calculate_simplified_difference_reward(self, state: DouDizhuState, action, player_id: int) -> float:
        """
        计算简化版差分奖励

        Args:
            state: 游戏状态
            action: 执行的动作
            player_id: 玩家ID

        Returns:
            float: 差分奖励
        """
        # 如果玩家是地主或游戏不在出牌阶段，返回0
        if state.landlord == player_id or state.game_phase != GamePhase.PLAYING:
            return 0.0

        # 找出队友（另一个农民）
        teammate = None
        for i in range(3):
            if i != player_id and i != state.landlord:
                teammate = i
                break
        if teammate is None:
            return 0.0

        difference_reward = 0.0

        # 只处理出牌阶段的CardGroup类型动作
        if isinstance(action, CardGroup):
            player_cards = len(state.hands[player_id])
            teammate_cards = len(state.hands[teammate])
            # 玩家手牌比队友多时，应更积极出牌
            if player_cards > teammate_cards + 3:
                if action.card_type != CardGroupType.PASS:
                    difference_reward += 0.02 * len(action.cards)
                else:
                    difference_reward -= 0.01
            # 队友手牌比玩家多时，可更保守
            elif teammate_cards > player_cards + 3:
                if action.card_type == CardGroupType.PASS:
                    difference_reward += 0.01

        return difference_reward

    def _calculate_farmer_cooperation_reward(self, state: DouDizhuState, action, player_id: int) -> float:
        """
        计算农民协作奖励（综合）

        Args:
            state: 游戏状态
            action: 执行的动作
            player_id: 玩家ID

        Returns:
            float: 综合协作奖励
        """
        # 如果玩家是地主或游戏不在出牌阶段，返回0
        if state.landlord == player_id or state.game_phase != GamePhase.PLAYING:
            return 0.0

        # 关键协作行为奖励
        key_reward = self._calculate_key_cooperation_reward(state, action, player_id)
        # 简化差分奖励
        diff_reward = self._calculate_simplified_difference_reward(state, action, player_id)

        # 合并各项协作奖励
        cooperation_reward = key_reward + diff_reward

        return cooperation_reward
