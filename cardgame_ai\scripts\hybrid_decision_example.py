#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
混合决策系统示例脚本

展示如何使用混合决策系统，结合人类决策和AI决策。
"""

import os
import sys
import argparse
import logging
import numpy as np
from typing import Dict, List, Any, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from cardgame_ai.core.hybrid_decision_system import HybridDecisionSystem
from cardgame_ai.games.doudizhu.state import DouDizhuState
from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType
from cardgame_ai.games.doudizhu.agent import DouDizhuAgent

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='混合决策系统示例')
    
    parser.add_argument('--model_path', type=str, default=None,
                        help='AI模型路径')
    parser.add_argument('--initial_confidence', type=float, default=0.8,
                        help='初始信心评分')
    parser.add_argument('--intervention_threshold', type=float, default=0.5,
                        help='介入阈值')
    parser.add_argument('--use_dynamic_intervention', action='store_true',
                        help='是否使用动态介入')
    
    return parser.parse_args()


def simulate_game(hybrid_system: HybridDecisionSystem, num_rounds: int = 10):
    """
    模拟游戏
    
    Args:
        hybrid_system: 混合决策系统
        num_rounds: 模拟轮数
    """
    # 创建初始游戏状态
    state = DouDizhuState.new_game()
    
    for i in range(num_rounds):
        logger.info(f"轮次 {i+1}/{num_rounds}")
        
        # 模拟人类动作（随机选择）
        legal_actions = state.get_legal_actions()
        human_action = np.random.choice(legal_actions)
        
        # 使用混合决策系统做出决策
        final_action = hybrid_system.decide(state, human_action)
        
        # 打印决策信息
        logger.info(f"人类动作: {human_action}")
        logger.info(f"AI推荐动作: {hybrid_system.decision_history[-1]['ai_action']}")
        logger.info(f"最终动作: {final_action}")
        logger.info(f"信心评分: {hybrid_system.get_confidence_score():.4f}")
        logger.info(f"介入程度: {hybrid_system.get_intervention_level():.4f}")
        logger.info("-" * 50)
        
        # 更新游戏状态
        state = state.next_state(final_action)
        
        # 如果游戏结束，重新开始
        if state.is_terminal():
            logger.info("游戏结束，重新开始")
            state = DouDizhuState.new_game()
    
    # 打印统计信息
    logger.info("统计信息:")
    stats = hybrid_system.get_stats()
    logger.info(f"平均相似度: {stats['avg_similarity']:.4f}")
    logger.info(f"最终信心评分: {stats['current_confidence']:.4f}")


def main():
    """主函数"""
    args = parse_args()
    
    # 创建AI代理
    ai_agent = DouDizhuAgent(model_path=args.model_path)
    
    # 创建混合决策系统
    hybrid_system = HybridDecisionSystem(
        ai_agent=ai_agent,
        initial_confidence=args.initial_confidence,
        intervention_threshold=args.intervention_threshold,
        use_dynamic_intervention=args.use_dynamic_intervention
    )
    
    # 模拟游戏
    simulate_game(hybrid_system)
    
    return 0


if __name__ == "__main__":
    main()
