"""
隐式通信机制示例脚本

展示如何在MAPPO中使用隐式通信机制，提高农民智能体之间的协作效率。
"""
import os
import sys
import argparse
import logging
import numpy as np
import torch
import time
import random
from typing import Dict, List, Any, Optional

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入必要的模块
from cardgame_ai.algorithms.role_specific_mappo import RoleSpecificMAPPO
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.games.doudizhu.state import DouDizhuState


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="隐式通信机制示例")

    # 模型参数
    parser.add_argument("--farmer1_model", type=str, default=None, help="农民1模型路径")
    parser.add_argument("--farmer2_model", type=str, default=None, help="农民2模型路径")
    parser.add_argument("--landlord_model", type=str, default=None, help="地主模型路径")

    # 通信参数
    parser.add_argument("--communication_dim", type=int, default=32, help="通信嵌入维度")
    parser.add_argument("--cooperation_coef", type=float, default=0.1, help="协作奖励系数")
    parser.add_argument("--use_cooperative_strategy", action="store_true", help="是否使用协作策略")

    # 游戏参数
    parser.add_argument("--num_games", type=int, default=10, help="游戏数量")
    parser.add_argument("--seed", type=int, default=None, help="随机种子")

    return parser.parse_args()


def create_agents(args, env):
    """创建智能体"""
    # 获取观察和动作空间
    obs_shape = env.observation_space.shape
    act_shape = env.action_space.shape

    # 创建地主智能体
    landlord_agent = RoleSpecificMAPPO(
        role='landlord',
        obs_shape=obs_shape,
        act_shape=act_shape,
        hidden_dims=[256, 128],
        use_central_critic=True,
        device="cuda" if torch.cuda.is_available() else "cpu"
    )

    # 创建农民1智能体（使用隐式通信）
    farmer1_agent = RoleSpecificMAPPO(
        role='farmer1',
        obs_shape=obs_shape,
        act_shape=act_shape,
        hidden_dims=[256, 128],
        use_central_critic=True,
        use_implicit_communication=True,
        communication_dim=args.communication_dim,
        cooperation_coef=args.cooperation_coef,
        use_cooperative_strategy=args.use_cooperative_strategy,
        device="cuda" if torch.cuda.is_available() else "cpu"
    )

    # 创建农民2智能体（使用隐式通信）
    farmer2_agent = RoleSpecificMAPPO(
        role='farmer2',
        obs_shape=obs_shape,
        act_shape=act_shape,
        hidden_dims=[256, 128],
        use_central_critic=True,
        use_implicit_communication=True,
        communication_dim=args.communication_dim,
        cooperation_coef=args.cooperation_coef,
        use_cooperative_strategy=args.use_cooperative_strategy,
        device="cuda" if torch.cuda.is_available() else "cpu"
    )

    # 加载模型（如果有）
    if args.landlord_model and os.path.exists(args.landlord_model):
        landlord_agent.load(args.landlord_model)
        logger.info(f"已加载地主模型: {args.landlord_model}")

    if args.farmer1_model and os.path.exists(args.farmer1_model):
        farmer1_agent.load(args.farmer1_model)
        logger.info(f"已加载农民1模型: {args.farmer1_model}")

    if args.farmer2_model and os.path.exists(args.farmer2_model):
        farmer2_agent.load(args.farmer2_model)
        logger.info(f"已加载农民2模型: {args.farmer2_model}")
    
    # 如果使用协作策略，需要为协作策略提供角色管理器
    if args.use_cooperative_strategy:
        from cardgame_ai.multi_agent.multi_agent_framework import RoleManager
        # 创建简单的角色管理器
        role_manager = RoleManager()
        
        # 添加角色到角色管理器
        role_manager.register_role('0', 'landlord')
        role_manager.register_role('1', 'farmer1')
        role_manager.register_role('2', 'farmer2')
        
        # 设置农民智能体的协作策略角色管理器
        if hasattr(farmer1_agent, 'coop_strategy'):
            farmer1_agent.coop_strategy.role_manager = role_manager
        if hasattr(farmer2_agent, 'coop_strategy'):
            farmer2_agent.coop_strategy.role_manager = role_manager
        
        logger.info("已启用协作策略并配置角色管理器")

    return {
        'landlord': landlord_agent,
        'farmer1': farmer1_agent,
        'farmer2': farmer2_agent
    }


def simulate_game(agents, env):
    """模拟一局游戏"""
    # 重置环境
    state = env.reset()
    done = False
    
    # 游戏统计
    game_stats = {
        "coordination_attempts": 0,
        "coordination_success": 0,
        "joint_play_success": 0
    }

    # 游戏循环
    while not done:
        # 获取当前玩家
        player_id = state.get_player_id()

        # 获取当前角色
        if player_id == 0:
            role = 'landlord'
        elif player_id == 1:
            role = 'farmer1'
        else:
            role = 'farmer2'

        # 获取合法动作
        legal_actions = env.get_legal_actions(state)

        # 获取队友ID（仅对农民有效）
        teammate_id = None
        if role == 'farmer1':
            teammate_id = '2'  # 农民2的ID
        elif role == 'farmer2':
            teammate_id = '1'  # 农民1的ID

        # 使用智能体做出决策
        agent = agents[role]
        action_probs, _ = agent.predict(state, teammate_id=teammate_id)
        
        # 选择动作
        action = np.argmax(action_probs)

        # 执行动作
        next_state, reward, done, info = env.step(action)
        
        # 如果使用协作策略且为农民角色，检查是否成功配合
        if (role in ['farmer1', 'farmer2'] and 
            hasattr(agent, 'use_cooperative_strategy') and 
            agent.use_cooperative_strategy):
            # 记录协作尝试次数
            if "coordination_attempts" in agent.cooperation_stats:
                game_stats["coordination_attempts"] = agent.cooperation_stats["coordination_attempts"]
            
            # 记录协作成功次数
            if "coordination_success" in agent.cooperation_stats:
                game_stats["coordination_success"] = agent.cooperation_stats["coordination_success"]
            
            # 检查是否是有效的联合出牌
            if info and 'joint_play_success' in info:
                game_stats["joint_play_success"] += info['joint_play_success']

        # 更新状态
        state = next_state

    # 获取游戏结果
    payoffs = env.get_payoffs(state)
    winner = np.argmax(payoffs)

    # 打印游戏结果
    if winner == 0:
        logger.info("地主获胜")
        result = "landlord_win"
    else:
        logger.info("农民获胜")
        result = "farmers_win"

    # 打印协作统计
    if agents['farmer1'].use_implicit_communication:
        logger.info(f"农民1协作统计: {agents['farmer1'].cooperation_stats}")

    if agents['farmer2'].use_implicit_communication:
        logger.info(f"农民2协作统计: {agents['farmer2'].cooperation_stats}")
    
    # 打印协作策略统计
    if hasattr(agents['farmer1'], 'use_cooperative_strategy') and agents['farmer1'].use_cooperative_strategy:
        logger.info(f"游戏协作尝试次数: {game_stats['coordination_attempts']}")
        logger.info(f"游戏协作成功次数: {game_stats['coordination_success']}")
        logger.info(f"游戏联合出牌成功次数: {game_stats['joint_play_success']}")

    return result, game_stats


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()

    # 设置随机种子
    if args.seed is not None:
        np.random.seed(args.seed)
        torch.manual_seed(args.seed)
        random.seed(args.seed)

    # 创建环境
    env = DouDizhuEnvironment()

    # 创建智能体
    agents = create_agents(args, env)

    # 输出配置信息
    logger.info(f"隐式通信: 已启用")
    logger.info(f"通信嵌入维度: {args.communication_dim}")
    logger.info(f"协作奖励系数: {args.cooperation_coef}")
    logger.info(f"协作策略: {'已启用' if args.use_cooperative_strategy else '已禁用'}")

    # 模拟游戏
    results = {"landlord_win": 0, "farmers_win": 0}
    all_game_stats = {
        "coordination_attempts": 0,
        "coordination_success": 0,
        "coordination_rate": 0.0,
        "joint_play_success": 0
    }

    for i in range(args.num_games):
        logger.info(f"开始游戏 {i+1}/{args.num_games}")
        result, game_stats = simulate_game(agents, env)
        results[result] += 1
        
        # 累计游戏统计
        all_game_stats["coordination_attempts"] += game_stats["coordination_attempts"]
        all_game_stats["coordination_success"] += game_stats["coordination_success"]
        all_game_stats["joint_play_success"] += game_stats["joint_play_success"]
    
    # 计算协作成功率
    if all_game_stats["coordination_attempts"] > 0:
        all_game_stats["coordination_rate"] = (
            all_game_stats["coordination_success"] / all_game_stats["coordination_attempts"]
        )

    # 打印最终结果
    logger.info(f"游戏总数: {args.num_games}")
    logger.info(f"地主获胜: {results['landlord_win']} ({results['landlord_win']/args.num_games:.2%})")
    logger.info(f"农民获胜: {results['farmers_win']} ({results['farmers_win']/args.num_games:.2%})")
    
    # 打印协作统计汇总
    if args.use_cooperative_strategy:
        logger.info(f"协作统计汇总:")
        logger.info(f"  - 协作尝试次数: {all_game_stats['coordination_attempts']}")
        logger.info(f"  - 协作成功次数: {all_game_stats['coordination_success']}")
        logger.info(f"  - 协作成功率: {all_game_stats['coordination_rate']:.2%}")
        logger.info(f"  - 联合出牌成功次数: {all_game_stats['joint_play_success']}")

    return 0


if __name__ == "__main__":
    sys.exit(main())
