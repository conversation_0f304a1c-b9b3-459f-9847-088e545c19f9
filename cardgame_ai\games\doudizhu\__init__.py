"""
斗地主游戏模块

实现斗地主游戏环境、规则和相关组件。
"""

# 导出游戏组件
from cardgame_ai.games.doudizhu.card import Card, CardSuit, CardRank
from cardgame_ai.games.doudizhu.deck import Deck
from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType, get_card_group_type
from cardgame_ai.games.doudizhu.action import BidAction, GrabAction, DouDizhuAction
from cardgame_ai.games.doudizhu.state import DouDizhuState, GamePhase
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.games.doudizhu.descriptor import get_doudizhu_descriptor, DOUDIZHU_DESCRIPTOR
from cardgame_ai.games.doudizhu.rules import check_card_play_legality
from cardgame_ai.games.doudizhu.agent import DouDizhuAgent, DouDizhuRandomAgent
from cardgame_ai.games.doudizhu.game import DouDizhuGame

# 提供别名以兼容旧代码
DoudizhuEnv = DouDizhuEnvironment
