#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
人类策略网络训练脚本

用于训练模仿人类出牌行为的策略网络。
"""

import os
import sys
import argparse
import logging
import torch
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from cardgame_ai.algorithms.human_policy_network import train_human_policy
from cardgame_ai.games.doudizhu.descriptor import DOUDIZHU_DESCRIPTOR

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='训练人类策略网络')

    parser.add_argument('--data_dir', type=str, default='data/human_interaction',
                        help='人类交互数据目录')
    parser.add_argument('--model_dir', type=str, default='models/human_policy',
                        help='模型保存目录')
    parser.add_argument('--state_dim', type=int, default=DOUDIZHU_DESCRIPTOR.state_shape[0],
                        help='状态维度')
    parser.add_argument('--action_dim', type=int, default=1000,  # 假设动作空间大小
                        help='动作维度')
    parser.add_argument('--hidden_dims', type=int, nargs='+', default=[256, 128],
                        help='隐藏层维度')
    parser.add_argument('--learning_rate', type=float, default=0.001,
                        help='学习率')
    parser.add_argument('--batch_size', type=int, default=32,
                        help='批次大小')
    parser.add_argument('--num_epochs', type=int, default=50,
                        help='训练轮数')
    parser.add_argument('--patience', type=int, default=5,
                        help='早停耐心值')

    return parser.parse_args()


def main():
    """主函数"""
    args = parse_args()

    # 创建模型保存目录
    os.makedirs(args.model_dir, exist_ok=True)

    # 生成模型保存路径
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    model_save_path = os.path.join(
        args.model_dir,
        f"human_policy_{timestamp}.pt"
    )

    # 设置设备
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    logger.info(f"使用设备: {device}")

    # 训练模型
    logger.info("开始训练人类策略网络...")
    model = train_human_policy(
        log_data_path=args.data_dir,
        model_save_path=model_save_path,
        state_dim=args.state_dim,
        action_dim=args.action_dim,
        hidden_dims=args.hidden_dims,
        learning_rate=args.learning_rate,
        batch_size=args.batch_size,
        num_epochs=args.num_epochs,
        patience=args.patience,
        device=device
    )

    logger.info(f"训练完成，模型保存在: {model_save_path}")

    return model_save_path


if __name__ == "__main__":
    main()
