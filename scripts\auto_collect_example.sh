#!/bin/bash
# 自动收集人机交互数据示例脚本

# 激活虚拟环境
source venv/bin/activate

# 设置输入和输出目录
INPUT_DIR="data/human_games"
OUTPUT_DIR="data/processed"
ARCHIVE_DIR="data/archived"

# 运行自动收集脚本
python scripts/auto_collect_experiences.py \
    --input $INPUT_DIR \
    --output $OUTPUT_DIR \
    --game doudizhu \
    --interval 3600 \
    --patterns "*.json" \
    --patterns "*.log" \
    --capacity 100000 \
    --quality 0.3 \
    --max-files 100 \
    --archive $ARCHIVE_DIR \
    --daemon

# 设置脚本权限
# chmod +x scripts/auto_collect_example.sh 