"""
MCTS日志配置管理模块

提供灵活的日志配置管理功能，支持：
- 运行时配置修改
- 配置验证机制
- 默认配置管理
- 环境变量支持
"""

import os
import yaml
import logging
from typing import Dict, Any, Optional, Union
from pathlib import Path
from dataclasses import dataclass, field


@dataclass
class LogConfig:
    """
    MCTS日志配置类
    
    管理所有日志相关的配置选项，支持从文件、环境变量或直接参数加载配置。
    """
    
    # 基础配置
    enabled: bool = True
    level: str = "INFO"  # DEBUG, INFO, WARNING, ERROR
    output_format: str = "json"  # json, text
    
    # 功能开关
    enable_ucb_logging: bool = True
    enable_expansion_logging: bool = True
    enable_path_logging: bool = True
    enable_performance_logging: bool = True
    enable_simulation_logging: bool = True
    
    # 输出配置
    log_to_file: bool = True
    log_to_console: bool = False
    log_file_path: str = "logs/mcts_debug.log"
    max_log_file_size: str = "100MB"
    log_rotation_count: int = 5
    
    # 性能配置
    async_logging: bool = True
    buffer_size: int = 1000
    flush_interval: float = 1.0  # 秒
    
    # 详细程度配置
    max_children_logged: int = 20  # UCB计算时最多记录的子节点数
    max_path_depth_logged: int = 50  # 搜索路径最大记录深度
    include_game_state: bool = True  # 是否包含游戏状态信息
    include_timestamps: bool = True  # 是否包含时间戳
    
    # 过滤配置
    min_visit_count_for_logging: int = 1  # 最小访问次数才记录
    log_only_best_actions: bool = False  # 是否只记录最佳动作
    
    @classmethod
    def from_file(cls, config_path: Union[str, Path]) -> 'LogConfig':
        """
        从配置文件加载配置
        
        Args:
            config_path: 配置文件路径，支持YAML格式
            
        Returns:
            LogConfig实例
        """
        config_path = Path(config_path)
        if not config_path.exists():
            logging.warning(f"配置文件不存在: {config_path}，使用默认配置")
            return cls()
            
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
                
            # 提取mcts_logging部分
            mcts_config = config_data.get('mcts_logging', {})
            
            return cls(**mcts_config)
            
        except Exception as e:
            logging.error(f"加载配置文件失败: {e}，使用默认配置")
            return cls()
    
    @classmethod
    def from_env(cls) -> 'LogConfig':
        """
        从环境变量加载配置
        
        环境变量格式: MCTS_LOG_<配置名>
        例如: MCTS_LOG_LEVEL=DEBUG
        
        Returns:
            LogConfig实例
        """
        config = cls()
        
        # 映射环境变量到配置属性
        env_mapping = {
            'MCTS_LOG_ENABLED': ('enabled', lambda x: x.lower() == 'true'),
            'MCTS_LOG_LEVEL': ('level', str),
            'MCTS_LOG_FORMAT': ('output_format', str),
            'MCTS_LOG_UCB': ('enable_ucb_logging', lambda x: x.lower() == 'true'),
            'MCTS_LOG_EXPANSION': ('enable_expansion_logging', lambda x: x.lower() == 'true'),
            'MCTS_LOG_PATH': ('enable_path_logging', lambda x: x.lower() == 'true'),
            'MCTS_LOG_PERFORMANCE': ('enable_performance_logging', lambda x: x.lower() == 'true'),
            'MCTS_LOG_FILE_PATH': ('log_file_path', str),
            'MCTS_LOG_MAX_SIZE': ('max_log_file_size', str),
        }
        
        for env_var, (attr_name, converter) in env_mapping.items():
            env_value = os.getenv(env_var)
            if env_value is not None:
                try:
                    setattr(config, attr_name, converter(env_value))
                except Exception as e:
                    logging.warning(f"环境变量 {env_var} 值无效: {env_value}, 错误: {e}")
        
        return config
    
    def validate(self) -> bool:
        """
        验证配置的有效性
        
        Returns:
            bool: 配置是否有效
        """
        errors = []
        
        # 验证日志级别
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR']
        if self.level.upper() not in valid_levels:
            errors.append(f"无效的日志级别: {self.level}，有效值: {valid_levels}")
        
        # 验证输出格式
        valid_formats = ['json', 'text']
        if self.output_format.lower() not in valid_formats:
            errors.append(f"无效的输出格式: {self.output_format}，有效值: {valid_formats}")
        
        # 验证文件大小格式
        if self.log_to_file:
            try:
                self._parse_size(self.max_log_file_size)
            except ValueError as e:
                errors.append(f"无效的文件大小格式: {self.max_log_file_size}, {e}")
        
        # 验证数值范围
        if self.max_children_logged < 1:
            errors.append("max_children_logged 必须大于0")
        
        if self.max_path_depth_logged < 1:
            errors.append("max_path_depth_logged 必须大于0")
        
        if self.buffer_size < 1:
            errors.append("buffer_size 必须大于0")
        
        if self.flush_interval <= 0:
            errors.append("flush_interval 必须大于0")
        
        if errors:
            for error in errors:
                logging.error(f"配置验证失败: {error}")
            return False
        
        return True
    
    def _parse_size(self, size_str: str) -> int:
        """
        解析文件大小字符串
        
        Args:
            size_str: 大小字符串，如 "100MB", "1GB"
            
        Returns:
            int: 字节数
        """
        size_str = size_str.upper().strip()
        
        if size_str.endswith('GB'):
            return int(float(size_str[:-2]) * 1024 * 1024 * 1024)
        elif size_str.endswith('MB'):
            return int(float(size_str[:-2]) * 1024 * 1024)
        elif size_str.endswith('KB'):
            return int(float(size_str[:-2]) * 1024)
        elif size_str.endswith('B'):
            return int(size_str[:-1])
        else:
            # 默认为字节
            return int(size_str)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式
        
        Returns:
            Dict[str, Any]: 配置字典
        """
        return {
            'enabled': self.enabled,
            'level': self.level,
            'output_format': self.output_format,
            'enable_ucb_logging': self.enable_ucb_logging,
            'enable_expansion_logging': self.enable_expansion_logging,
            'enable_path_logging': self.enable_path_logging,
            'enable_performance_logging': self.enable_performance_logging,
            'enable_simulation_logging': self.enable_simulation_logging,
            'log_to_file': self.log_to_file,
            'log_to_console': self.log_to_console,
            'log_file_path': self.log_file_path,
            'max_log_file_size': self.max_log_file_size,
            'log_rotation_count': self.log_rotation_count,
            'async_logging': self.async_logging,
            'buffer_size': self.buffer_size,
            'flush_interval': self.flush_interval,
            'max_children_logged': self.max_children_logged,
            'max_path_depth_logged': self.max_path_depth_logged,
            'include_game_state': self.include_game_state,
            'include_timestamps': self.include_timestamps,
            'min_visit_count_for_logging': self.min_visit_count_for_logging,
            'log_only_best_actions': self.log_only_best_actions,
        }
    
    def save_to_file(self, config_path: Union[str, Path]) -> None:
        """
        保存配置到文件
        
        Args:
            config_path: 配置文件保存路径
        """
        config_path = Path(config_path)
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        config_data = {
            'mcts_logging': self.to_dict()
        }
        
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)


# 默认配置实例
DEFAULT_CONFIG = LogConfig()
