#!/usr/bin/env python
"""
人类策略网络训练脚本

用于训练模仿人类行为的策略网络模型，支持使用合成数据或真实人类数据。
"""

import os
import sys
import argparse
import logging
from typing import Dict, Any
import torch

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cardgame_ai.algorithms.opponent_modeling.human_policy import (
    train_human_policy, 
    HumanPolicyNetwork, 
    HumanPolicyTrainer,
    generate_synthetic_data,
    load_human_data,
    prepare_data_loaders
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
)
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="人类策略网络训练脚本")
    
    # 数据相关参数
    parser.add_argument('--data_path', type=str, default=None,
                        help="人类数据路径，如果不提供则使用合成数据")
    parser.add_argument('--synthetic_data', action='store_true',
                        help="是否使用合成数据")
    parser.add_argument('--num_synthetic_samples', type=int, default=2000,
                        help="合成样本数量")
    
    # 模型相关参数
    parser.add_argument('--network_type', type=str, default='mlp', choices=['mlp', 'cnn', 'transformer'],
                        help="网络类型: mlp, cnn, transformer")
    parser.add_argument('--hidden_dims', type=str, default='256,128',
                        help="隐藏层维度，逗号分隔")
    parser.add_argument('--dropout_rate', type=float, default=0.2,
                        help="Dropout比率")
    
    # 训练相关参数
    parser.add_argument('--epochs', type=int, default=10,
                        help="训练轮数")
    parser.add_argument('--batch_size', type=int, default=32,
                        help="批次大小")
    parser.add_argument('--learning_rate', type=float, default=0.001,
                        help="学习率")
    parser.add_argument('--device', type=str, default=None,
                        help="计算设备 (cuda或cpu)")
    
    # 保存相关参数
    parser.add_argument('--model_save_dir', type=str, default='models/human_policy',
                        help="模型保存目录")
    parser.add_argument('--save_interval', type=int, default=1,
                        help="模型保存间隔（轮数）")
    
    # 其他参数
    parser.add_argument('--verbose', action='store_true',
                        help="是否显示详细信息")
    parser.add_argument('--log_interval', type=int, default=10,
                        help="日志记录间隔（步数）")
    
    return parser.parse_args()


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 解析隐藏层维度
    hidden_dims = [int(dim) for dim in args.hidden_dims.split(',')]
    
    # 创建保存目录
    os.makedirs(args.model_save_dir, exist_ok=True)
    os.makedirs(os.path.join(args.model_save_dir, 'logs'), exist_ok=True)
    
    # 确定计算设备
    device = args.device
    if device is None:
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
    logger.info(f"使用计算设备: {device}")
    
    # 加载或生成数据
    if args.synthetic_data or args.data_path is None:
        logger.info(f"生成{args.num_synthetic_samples}个合成训练样本")
        states, actions = generate_synthetic_data(args.num_synthetic_samples)
    else:
        logger.info(f"从{args.data_path}加载训练数据")
        states, actions = load_human_data(args.data_path)
        
        if not states or not actions:
            logger.warning("没有找到有效数据，将使用合成数据")
            states, actions = generate_synthetic_data(args.num_synthetic_samples)
    
    # 准备数据加载器
    train_loader, val_loader = prepare_data_loaders(
        states, actions, batch_size=args.batch_size
    )
    
    # 创建模型
    logger.info(f"创建{args.network_type}类型的人类策略网络模型")
    model = HumanPolicyNetwork(
        input_dim=627,  # 斗地主观察空间维度
        hidden_dims=hidden_dims,
        action_dim=15,  # 15种斗地主牌型 + 不出牌
        dropout_rate=args.dropout_rate,
        network_type=args.network_type
    )
    
    # 创建训练器
    trainer = HumanPolicyTrainer(
        model=model,
        learning_rate=args.learning_rate,
        device=device,
        checkpoint_dir=args.model_save_dir,
        log_dir=os.path.join(args.model_save_dir, 'logs')
    )
    
    # 训练模型
    logger.info(f"开始训练，共{args.epochs}轮")
    stats = trainer.train(
        train_dataloader=train_loader,
        val_dataloader=val_loader,
        epochs=args.epochs,
        log_interval=args.log_interval,
        save_interval=args.save_interval,
        verbose=args.verbose
    )
    
    # 打印最终结果
    logger.info("训练完成")
    logger.info(f"最终训练集损失: {stats['train_loss'][-1]:.4f}")
    logger.info(f"最终训练集准确率: {stats['train_accuracy'][-1]:.2f}%")
    
    if stats['val_loss'] and stats['val_accuracy']:
        logger.info(f"最终验证集损失: {stats['val_loss'][-1]:.4f}")
        logger.info(f"最终验证集准确率: {stats['val_accuracy'][-1]:.2f}%")
    
    # 保存最终模型
    final_model_path = os.path.join(args.model_save_dir, 'human_policy_final.pt')
    logger.info(f"模型已保存至: {final_model_path}")
    
    # 完成
    logger.info("训练脚本执行完毕")


if __name__ == "__main__":
    main() 