# 多GPU硬件配置
# 适用于多张GPU的分布式训练配置

device:
  type: "cuda"
  ids: [0, 1, 2, 3]
  mixed_precision: true
  benchmark: true
  deterministic: false

# 内存配置
memory:
  max_memory_usage: 0.9
  gradient_checkpointing: true
  empty_cache_frequency: 500
  gc_frequency: 1000

# 数据加载配置
data:
  num_workers: 16
  pin_memory: true
  prefetch_factor: 8
  persistent_workers: true
  cache_size_gb: 12

# 训练配置
training:
  batch_size:
    base: 320
    min: 192
    max: 448
    auto_scale: true
    memory_threshold: 0.85

# MCTS配置
mcts:
  num_simulations: 150
  parallel_threads: 6
  batch_size_inference: 24

# 分布式配置
distributed:
  enabled: true
  backend: "nccl"
  sync_frequency: 10
  bucket_size_mb: 25
  data_parallel: true
