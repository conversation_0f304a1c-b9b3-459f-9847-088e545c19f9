"""
德州扑克牌型评估模块，用于评估玩家手牌的大小和类型。
"""

from enum import Enum, auto
from collections import Counter
from typing import List, Tuple, Dict, Optional


class HandRank(Enum):
    """德州扑克牌型，从低到高排序"""
    HIGH_CARD = 1
    PAIR = 2
    TWO_PAIR = 3
    THREE_OF_A_KIND = 4
    STRAIGHT = 5
    FLUSH = 6
    FULL_HOUSE = 7
    FOUR_OF_A_KIND = 8
    STRAIGHT_FLUSH = 9
    ROYAL_FLUSH = 10
    
    def __str__(self):
        """返回牌型的中文名称"""
        return {
            HandRank.HIGH_CARD: "高牌",
            HandRank.PAIR: "一对",
            HandRank.TWO_PAIR: "两对",
            HandRank.THREE_OF_A_KIND: "三条",
            HandRank.STRAIGHT: "顺子",
            HandRank.FLUSH: "同花",
            HandRank.FULL_HOUSE: "葫芦",
            HandRank.FOUR_OF_A_KIND: "四条",
            HandRank.STRAIGHT_FLUSH: "同花顺",
            HandRank.ROYAL_FLUSH: "皇家同花顺"
        }[self]


class HandEvaluator:
    """手牌评估器，用于评估德州扑克玩家的手牌"""
    
    @staticmethod
    def evaluate_hand(cards: List['PokerCard']) -> Tuple[HandRank, List[int]]:
        """
        评估一组牌的牌型和大小
        
        Args:
            cards: 包含玩家手牌和公共牌的列表，通常为7张牌
            
        Returns:
            Tuple[HandRank, List[int]]: 牌型和用于比较大小的值列表
        """
        if len(cards) < 5:
            raise ValueError("评估牌型至少需要5张牌")
            
        # 检查皇家同花顺
        royal_flush = HandEvaluator._check_royal_flush(cards)
        if royal_flush[0]:
            return HandRank.ROYAL_FLUSH, royal_flush[1]
            
        # 检查同花顺
        straight_flush = HandEvaluator._check_straight_flush(cards)
        if straight_flush[0]:
            return HandRank.STRAIGHT_FLUSH, straight_flush[1]
            
        # 检查四条
        four_kind = HandEvaluator._check_four_of_a_kind(cards)
        if four_kind[0]:
            return HandRank.FOUR_OF_A_KIND, four_kind[1]
            
        # 检查葫芦
        full_house = HandEvaluator._check_full_house(cards)
        if full_house[0]:
            return HandRank.FULL_HOUSE, full_house[1]
            
        # 检查同花
        flush = HandEvaluator._check_flush(cards)
        if flush[0]:
            return HandRank.FLUSH, flush[1]
            
        # 检查顺子
        straight = HandEvaluator._check_straight(cards)
        if straight[0]:
            return HandRank.STRAIGHT, straight[1]
            
        # 检查三条
        three_kind = HandEvaluator._check_three_of_a_kind(cards)
        if three_kind[0]:
            return HandRank.THREE_OF_A_KIND, three_kind[1]
            
        # 检查两对
        two_pair = HandEvaluator._check_two_pair(cards)
        if two_pair[0]:
            return HandRank.TWO_PAIR, two_pair[1]
            
        # 检查一对
        pair = HandEvaluator._check_pair(cards)
        if pair[0]:
            return HandRank.PAIR, pair[1]
            
        # 高牌
        high_card = HandEvaluator._check_high_card(cards)
        return HandRank.HIGH_CARD, high_card[1]
    
    @staticmethod
    def _check_royal_flush(cards: List['PokerCard']) -> Tuple[bool, List[int]]:
        """检查是否有皇家同花顺"""
        # 按花色分组
        suits = {}
        for card in cards:
            if card.suit not in suits:
                suits[card.suit] = []
            suits[card.suit].append(card)
        
        # 检查每种花色是否有足够的牌构成皇家同花顺
        for suit, suited_cards in suits.items():
            if len(suited_cards) >= 5:
                # 检查是否有A、K、Q、J、10，这些是皇家同花顺所需的牌
                ranks = [card.rank.value for card in suited_cards]
                if all(rank in ranks for rank in [14, 13, 12, 11, 10]):
                    return True, [14, 13, 12, 11, 10]
        
        return False, []
    
    @staticmethod
    def _check_straight_flush(cards: List['PokerCard']) -> Tuple[bool, List[int]]:
        """检查是否有同花顺"""
        # 按花色分组
        suits = {}
        for card in cards:
            if card.suit not in suits:
                suits[card.suit] = []
            suits[card.suit].append(card)
        
        # 检查每种花色是否有足够的牌构成顺子
        for suit, suited_cards in suits.items():
            if len(suited_cards) >= 5:
                # 检查是否有五张连续的牌
                ranks = sorted([card.rank.value for card in suited_cards], reverse=True)
                # 去重
                unique_ranks = []
                for rank in ranks:
                    if rank not in unique_ranks:
                        unique_ranks.append(rank)
                
                # 特殊情况：A-5-4-3-2
                if 14 in unique_ranks and 5 in unique_ranks and 4 in unique_ranks and 3 in unique_ranks and 2 in unique_ranks:
                    return True, [5, 4, 3, 2, 1]  # A视为1
                
                # 检查是否有5张连续的牌
                for i in range(len(unique_ranks) - 4):
                    if unique_ranks[i] - unique_ranks[i+4] == 4:
                        return True, unique_ranks[i:i+5]
        
        return False, []
    
    @staticmethod
    def _check_four_of_a_kind(cards: List['PokerCard']) -> Tuple[bool, List[int]]:
        """检查是否有四条"""
        # 统计每个点数的数量
        rank_counts = Counter([card.rank.value for card in cards])
        
        # 检查是否有四张相同点数的牌
        for rank, count in rank_counts.items():
            if count == 4:
                # 四条的点数加上最大的单牌
                kickers = [r for r in rank_counts.keys() if r != rank]
                kickers.sort(reverse=True)
                return True, [rank] * 4 + kickers[:1]
        
        return False, []
    
    @staticmethod
    def _check_full_house(cards: List['PokerCard']) -> Tuple[bool, List[int]]:
        """检查是否有葫芦(三条+对子)"""
        # 统计每个点数的数量
        rank_counts = Counter([card.rank.value for card in cards])
        
        # 寻找最大的三条
        three_kind = None
        for rank, count in rank_counts.items():
            if count >= 3:
                if three_kind is None or rank > three_kind:
                    three_kind = rank
        
        # 如果找到三条，再寻找最大的对子
        if three_kind is not None:
            pair = None
            for rank, count in rank_counts.items():
                if rank != three_kind and count >= 2:
                    if pair is None or rank > pair:
                        pair = rank
            
            if pair is not None:
                return True, [three_kind] * 3 + [pair] * 2
        
        return False, []
    
    @staticmethod
    def _check_flush(cards: List['PokerCard']) -> Tuple[bool, List[int]]:
        """检查是否有同花"""
        # 按花色分组
        suits = {}
        for card in cards:
            if card.suit not in suits:
                suits[card.suit] = []
            suits[card.suit].append(card)
        
        # 检查是否有五张或更多同花色的牌
        for suit, suited_cards in suits.items():
            if len(suited_cards) >= 5:
                # 取最大的五张牌
                ranks = sorted([card.rank.value for card in suited_cards], reverse=True)
                return True, ranks[:5]
        
        return False, []
    
    @staticmethod
    def _check_straight(cards: List['PokerCard']) -> Tuple[bool, List[int]]:
        """检查是否有顺子"""
        # 提取所有点数并排序
        ranks = sorted([card.rank.value for card in cards], reverse=True)
        # 去重
        unique_ranks = []
        for rank in ranks:
            if rank not in unique_ranks:
                unique_ranks.append(rank)
        
        # 特殊情况：A-5-4-3-2
        if 14 in unique_ranks and 5 in unique_ranks and 4 in unique_ranks and 3 in unique_ranks and 2 in unique_ranks:
            return True, [5, 4, 3, 2, 1]  # A视为1
        
        # 检查是否有5张连续的牌
        for i in range(len(unique_ranks) - 4):
            if unique_ranks[i] - unique_ranks[i+4] == 4:
                return True, unique_ranks[i:i+5]
        
        return False, []
    
    @staticmethod
    def _check_three_of_a_kind(cards: List['PokerCard']) -> Tuple[bool, List[int]]:
        """检查是否有三条"""
        # 统计每个点数的数量
        rank_counts = Counter([card.rank.value for card in cards])
        
        # 检查是否有三张相同点数的牌
        for rank, count in rank_counts.items():
            if count == 3:
                # 三条的点数加上最大的两张单牌
                kickers = [r for r in rank_counts.keys() if r != rank]
                kickers.sort(reverse=True)
                return True, [rank] * 3 + kickers[:2]
        
        return False, []
    
    @staticmethod
    def _check_two_pair(cards: List['PokerCard']) -> Tuple[bool, List[int]]:
        """检查是否有两对"""
        # 统计每个点数的数量
        rank_counts = Counter([card.rank.value for card in cards])
        
        # 找出所有对子
        pairs = [rank for rank, count in rank_counts.items() if count >= 2]
        
        # 检查是否有至少两对
        if len(pairs) >= 2:
            # 按点数从大到小排序
            pairs.sort(reverse=True)
            # 取最大的两对，加上最大的单牌
            kickers = [r for r in rank_counts.keys() if r != pairs[0] and r != pairs[1]]
            kickers.sort(reverse=True)
            return True, [pairs[0]] * 2 + [pairs[1]] * 2 + kickers[:1]
        
        return False, []
    
    @staticmethod
    def _check_pair(cards: List['PokerCard']) -> Tuple[bool, List[int]]:
        """检查是否有一对"""
        # 统计每个点数的数量
        rank_counts = Counter([card.rank.value for card in cards])
        
        # 检查是否有一对
        for rank, count in rank_counts.items():
            if count == 2:
                # 对子的点数加上最大的三张单牌
                kickers = [r for r in rank_counts.keys() if r != rank]
                kickers.sort(reverse=True)
                return True, [rank] * 2 + kickers[:3]
        
        return False, []
    
    @staticmethod
    def _check_high_card(cards: List['PokerCard']) -> Tuple[bool, List[int]]:
        """返回高牌"""
        # 提取所有点数并按从大到小排序
        ranks = sorted([card.rank.value for card in cards], reverse=True)
        # 取最大的五张牌
        return True, ranks[:5]
    
    @staticmethod
    def compare_hands(hand1: Tuple[HandRank, List[int]], hand2: Tuple[HandRank, List[int]]) -> int:
        """
        比较两手牌的大小
        
        Args:
            hand1: 第一手牌的(牌型, 值列表)
            hand2: 第二手牌的(牌型, 值列表)
            
        Returns:
            int: 1如果hand1大于hand2，-1如果hand1小于hand2，0如果相等
        """
        # 先比较牌型
        if hand1[0].value > hand2[0].value:
            return 1
        elif hand1[0].value < hand2[0].value:
            return -1
        
        # 牌型相同，比较值列表
        for v1, v2 in zip(hand1[1], hand2[1]):
            if v1 > v2:
                return 1
            elif v1 < v2:
                return -1
        
        # 完全相同
        return 0 