#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速显示训练保存路径上的模型参数量和信息

直接运行即可查看所有模型信息：
    python show_models_info.py
"""

import os
import sys
import torch
from pathlib import Path
from typing import List, Dict, Any
import json
from datetime import datetime

# 添加项目根目录到路径
project_root = os.path.abspath(os.path.dirname(__file__))
sys.path.insert(0, project_root)

try:
    from cardgame_ai.utils.model_saver import ModelSaver
except ImportError:
    # 如果导入失败，提供基本的参数统计功能
    class ModelSaver:
        @staticmethod
        def format_parameter_count(param_count: int) -> str:
            """格式化参数量为可读字符串"""
            if param_count >= 1_000_000_000:
                return f"{param_count / 1_000_000_000:.1f}B"
            elif param_count >= 1_000_000:
                return f"{param_count / 1_000_000:.1f}M"
            elif param_count >= 1_000:
                return f"{param_count / 1_000:.1f}K"
            else:
                return str(param_count)
        
        @staticmethod
        def get_model_info(model_path: str) -> Dict[str, Any]:
            """获取模型基本信息"""
            try:
                model_data = torch.load(model_path, map_location='cpu')
                
                # 尝试获取参数量
                total_params = 0
                if isinstance(model_data, dict):
                    if 'parameter_stats' in model_data:
                        total_params = model_data['parameter_stats'].get('total_parameters', 0)
                    elif 'model_state_dict' in model_data:
                        state_dict = model_data['model_state_dict']
                        total_params = sum(tensor.numel() for tensor in state_dict.values())
                    elif any(key.endswith('.weight') or key.endswith('.bias') for key in model_data.keys()):
                        total_params = sum(tensor.numel() for tensor in model_data.values())
                
                return {
                    'parameter_stats': {'total_parameters': total_params},
                    'formatted_params': ModelSaver.format_parameter_count(total_params) if total_params > 0 else 'Unknown',
                    'save_timestamp': model_data.get('save_timestamp') or model_data.get('timestamp', 'Unknown'),
                    'epoch': model_data.get('epoch'),
                    'performance': model_data.get('performance'),
                    'tag': model_data.get('tag')
                }
            except Exception as e:
                return {'error': str(e)}


def get_file_size(file_path: Path) -> str:
    """获取文件大小的可读格式"""
    try:
        size_bytes = file_path.stat().st_size
        
        if size_bytes >= 1024**3:
            return f"{size_bytes / (1024**3):.1f} GB"
        elif size_bytes >= 1024**2:
            return f"{size_bytes / (1024**2):.1f} MB"
        elif size_bytes >= 1024:
            return f"{size_bytes / 1024:.1f} KB"
        else:
            return f"{size_bytes} B"
    except:
        return "Unknown"


def find_model_directories() -> List[Path]:
    """查找可能的模型保存目录"""
    possible_dirs = [
        Path("models"),
        Path("cardgame_ai/zhuchengxu/models"),
        Path("cardgame_ai/models"),
        Path("checkpoints"),
        Path("saved_models"),
        Path("outputs"),
        Path("results")
    ]
    
    existing_dirs = []
    for dir_path in possible_dirs:
        if dir_path.exists() and dir_path.is_dir():
            existing_dirs.append(dir_path)
    
    return existing_dirs


def find_model_files(directories: List[Path]) -> List[Path]:
    """在指定目录中查找模型文件"""
    model_files = []
    
    for directory in directories:
        # 递归搜索模型文件
        for pattern in ['**/*.pt', '**/*.pth', '**/*.ckpt']:
            model_files.extend(directory.glob(pattern))
    
    # 去重并排序
    model_files = list(set(model_files))
    model_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)  # 按修改时间倒序
    
    return model_files


def analyze_model_file(file_path: Path) -> Dict[str, Any]:
    """分析单个模型文件"""
    try:
        # 基本文件信息
        file_info = {
            'file_path': str(file_path),
            'file_name': file_path.name,
            'file_size': get_file_size(file_path),
            'file_size_bytes': file_path.stat().st_size,
            'modified_time': datetime.fromtimestamp(file_path.stat().st_mtime).strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # 获取模型信息
        model_info = ModelSaver.get_model_info(str(file_path))
        
        if 'error' not in model_info:
            file_info.update(model_info)
        else:
            file_info['error'] = model_info['error']
        
        return file_info
        
    except Exception as e:
        return {
            'file_path': str(file_path),
            'file_name': file_path.name,
            'error': f"分析失败: {str(e)}"
        }


def print_model_summary(model_info: Dict[str, Any]):
    """打印模型摘要信息"""
    print(f"📄 {model_info['file_name']}")
    
    if 'error' in model_info:
        print(f"   ❌ {model_info['error']}")
        return
    
    # 参数量
    if 'formatted_params' in model_info and model_info['formatted_params'] != 'Unknown':
        print(f"   📊 参数量: {model_info['formatted_params']}")
    
    # 文件大小
    print(f"   💾 大小: {model_info['file_size']}")
    
    # 修改时间
    print(f"   ⏰ 修改时间: {model_info['modified_time']}")
    
    # 训练信息
    if model_info.get('epoch'):
        print(f"   🔄 轮次: {model_info['epoch']}")
    
    if model_info.get('performance'):
        print(f"   📈 性能: {model_info['performance']:.4f}")
    
    if model_info.get('tag'):
        print(f"   🏷️ 标签: {model_info['tag']}")
    
    print()


def main():
    """主函数"""
    print("🔍 正在搜索训练保存路径上的模型文件...")
    print("=" * 60)
    
    # 查找模型目录
    model_dirs = find_model_directories()
    
    if not model_dirs:
        print("❌ 未找到模型保存目录")
        print("💡 请确保以下目录之一存在：")
        print("   - models/")
        print("   - cardgame_ai/zhuchengxu/models/")
        print("   - checkpoints/")
        return
    
    print(f"📁 找到模型目录: {[str(d) for d in model_dirs]}")
    
    # 查找模型文件
    model_files = find_model_files(model_dirs)
    
    if not model_files:
        print("❌ 在模型目录中未找到任何模型文件 (.pt, .pth, .ckpt)")
        return
    
    print(f"🎯 找到 {len(model_files)} 个模型文件")
    print()
    
    # 分析并显示所有模型
    total_params = 0
    successful_analyses = 0
    
    for i, file_path in enumerate(model_files, 1):
        print(f"[{i}/{len(model_files)}]", end=" ")
        
        model_info = analyze_model_file(file_path)
        print_model_summary(model_info)
        
        # 统计
        if 'error' not in model_info:
            successful_analyses += 1
            param_stats = model_info.get('parameter_stats', {})
            if isinstance(param_stats, dict):
                total_params += param_stats.get('total_parameters', 0)
    
    # 显示统计摘要
    print("📊 统计摘要:")
    print(f"   总文件数: {len(model_files)}")
    print(f"   成功分析: {successful_analyses}")
    
    if total_params > 0:
        formatted_total = ModelSaver.format_parameter_count(total_params)
        print(f"   总参数量: {formatted_total} ({total_params:,})")
    
    print()
    print("✅ 分析完成！")


if __name__ == "__main__":
    main()
