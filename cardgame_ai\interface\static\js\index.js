/**
 * AI棋牌强化学习框架 - 首页脚本
 */
document.addEventListener('DOMContentLoaded', function() {
    // 表单元素
    const gameForm = document.getElementById('game-form');
    const uiConfigForm = document.getElementById('ui-config-form');
    
    // 当前UI配置
    let currentUIConfig = {};
    
    // 初始化页面
    initPage();
    
    /**
     * 初始化页面
     */
    function initPage() {
        // 加载UI配置
        loadUIConfig();
        
        // 绑定表单提交事件
        if (gameForm) {
            gameForm.addEventListener('submit', handleGameFormSubmit);
        }
        
        if (uiConfigForm) {
            uiConfigForm.addEventListener('submit', handleUIConfigFormSubmit);
        }
    }
    
    /**
     * 加载UI配置
     */
    function loadUIConfig() {
        fetch('/api/ui_config')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentUIConfig = data.config;
                    updateUIConfigForm(currentUIConfig);
                }
            })
            .catch(error => {
                console.error('加载UI配置失败:', error);
                showToast('加载UI配置失败，将使用默认配置。', 'error');
            });
    }
    
    /**
     * 更新UI配置表单
     * @param {Object} config - UI配置
     */
    function updateUIConfigForm(config) {
        const themeSelect = document.getElementById('theme');
        const cardStyleSelect = document.getElementById('card-style');
        const animationSpeedSelect = document.getElementById('animation-speed');
        const soundEnabledCheckbox = document.getElementById('sound-enabled');
        
        if (themeSelect && config.theme) {
            themeSelect.value = config.theme;
        }
        
        if (cardStyleSelect && config.card_style) {
            cardStyleSelect.value = config.card_style;
        }
        
        if (animationSpeedSelect && config.animation_speed) {
            animationSpeedSelect.value = config.animation_speed;
        }
        
        if (soundEnabledCheckbox && config.sound_enabled !== undefined) {
            soundEnabledCheckbox.checked = config.sound_enabled;
        }
        
        // 应用主题
        document.body.setAttribute('data-theme', config.theme || 'default');
    }
    
    /**
     * 处理游戏表单提交
     * @param {Event} event - 表单提交事件
     */
    function handleGameFormSubmit(event) {
        event.preventDefault();
        
        const gameType = document.getElementById('game-type').value;
        const playerRole = document.getElementById('player-role').value;
        const aiModel = document.getElementById('ai-model').value;
        
        const gameData = {
            game_type: gameType,
            player_role: playerRole,
            ai_model: aiModel
        };
        
        // 禁用提交按钮
        const submitButton = gameForm.querySelector('button[type="submit"]');
        if (submitButton) {
            submitButton.disabled = true;
            submitButton.textContent = '正在创建游戏...';
        }
        
        // 创建游戏
        fetch('/api/create_game', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(gameData)
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 跳转到游戏页面
                    window.location.href = '/game';
                } else {
                    throw new Error(data.error || '创建游戏失败');
                }
            })
            .catch(error => {
                console.error('创建游戏失败:', error);
                showToast('创建游戏失败: ' + error.message, 'error');
                
                // 恢复提交按钮
                if (submitButton) {
                    submitButton.disabled = false;
                    submitButton.textContent = '开始游戏';
                }
            });
    }
    
    /**
     * 处理UI配置表单提交
     * @param {Event} event - 表单提交事件
     */
    function handleUIConfigFormSubmit(event) {
        event.preventDefault();
        
        const theme = document.getElementById('theme').value;
        const cardStyle = document.getElementById('card-style').value;
        const animationSpeed = document.getElementById('animation-speed').value;
        const soundEnabled = document.getElementById('sound-enabled').checked;
        
        const configData = {
            theme: theme,
            card_style: cardStyle,
            animation_speed: animationSpeed,
            sound_enabled: soundEnabled
        };
        
        // 禁用提交按钮
        const submitButton = uiConfigForm.querySelector('button[type="submit"]');
        if (submitButton) {
            submitButton.disabled = true;
            submitButton.textContent = '正在保存...';
        }
        
        // 更新UI配置
        fetch('/api/ui_config', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(configData)
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentUIConfig = configData;
                    showToast('设置已保存', 'success');
                    
                    // 应用主题
                    document.body.setAttribute('data-theme', theme);
                } else {
                    throw new Error(data.error || '保存设置失败');
                }
            })
            .catch(error => {
                console.error('保存设置失败:', error);
                showToast('保存设置失败: ' + error.message, 'error');
            })
            .finally(() => {
                // 恢复提交按钮
                if (submitButton) {
                    submitButton.disabled = false;
                    submitButton.textContent = '保存设置';
                }
            });
    }
    
    /**
     * 显示提示消息
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型（success, error, info）
     */
    function showToast(message, type = 'info') {
        // 检查是否已存在toast容器
        let toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container';
            document.body.appendChild(toastContainer);
        }
        
        // 创建toast元素
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                ${message}
            </div>
        `;
        
        // 添加到容器
        toastContainer.appendChild(toast);
        
        // 显示toast
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);
        
        // 3秒后隐藏
        setTimeout(() => {
            toast.classList.remove('show');
            // 动画结束后移除元素
            setTimeout(() => {
                toastContainer.removeChild(toast);
            }, 300);
        }, 3000);
    }
}); 