"""
EfficientZero混合精度支持模块

该模块实现了EfficientZero算法的混合精度训练支持，包括：
- EfficientZeroAMP类：支持混合精度训练的EfficientZero实现
- 自动混合精度（AMP）优化
- 梯度缩放和动态损失缩放
- 内存优化和性能提升

主要功能：
- 混合精度训练：提高训练速度，减少内存使用
- 梯度缩放：防止梯度下溢
- 动态损失缩放：自适应调整损失缩放因子
- 性能监控：跟踪训练性能指标
"""

import torch
import torch.nn.functional as F
import numpy as np
import logging
from typing import Tuple, Dict, Union, Optional
from torch.cuda.amp import autocast

from .efficient_zero_algorithm import EfficientZero
from cardgame_ai.core.base import State, Action, Experience, Batch

# 配置日志
logger = logging.getLogger(__name__)


class EfficientZeroAMP(EfficientZero):
    """
    支持混合精度训练的EfficientZero算法
    
    该类扩展了EfficientZero算法，添加了自动混合精度（AMP）支持，
    可以显著提高训练速度并减少GPU内存使用。
    
    主要特性：
    - 自动混合精度训练：使用FP16和FP32混合精度
    - 梯度缩放：防止FP16训练中的梯度下溢
    - 动态损失缩放：自适应调整缩放因子
    - 内存优化：减少GPU内存占用
    """

    def __init__(
        self,
        state_shape: Tuple[int, ...],
        action_shape: Tuple[int, ...],
        hidden_dim: int = 256,
        state_dim: int = 64,
        use_resnet: bool = True,
        projection_dim: int = 128,
        prediction_dim: int = 64,
        value_prefix_length: int = 5,
        use_distributional_value: bool = False,
        value_support_size: int = 601,
        value_min: float = -300,
        value_max: float = 300,
        risk_alpha: float = 0.05,
        risk_beta: float = 0.1,
        use_belief_state: bool = False,
        belief_dim: int = 128,
        use_belief_attention: bool = True,
        belief_attention_heads: int = 4,
        use_residual_belief: bool = True,
        use_gating_mechanism: bool = True,
        num_simulations: int = 50,
        c_puct: float = 1.25,
        dirichlet_alpha: float = 0.3,
        exploration_fraction: float = 0.25,
        pb_c_base: float = 19652,
        pb_c_init: float = 1.25,
        replay_buffer_size: int = 100000,
        batch_size: int = 256,
        num_unroll_steps: int = 5,
        td_steps: int = 5,
        value_loss_weight: float = 0.25,
        policy_loss_weight: float = 1.0,
        consistency_loss_weight: float = 2.0,
        self_supervised_loss_weight: float = 2.0,
        use_ewc: bool = False,
        ewc_lambda: float = 100.0,
        ewc_state_path: Optional[str] = None,
        learning_rate: float = 0.001,
        weight_decay: float = 1e-4,
        lr_scheduler: str = 'step',
        use_mixed_precision: bool = True,
        dynamic_loss_scaling: bool = True,
        device: str = None
    ):
        """
        初始化支持混合精度的EfficientZero算法
        
        Args:
            state_shape: 状态空间形状
            action_shape: 动作空间形状
            hidden_dim: 隐藏层维度
            state_dim: 状态表示维度
            use_resnet: 是否使用ResNet架构
            projection_dim: 自监督学习投影维度
            prediction_dim: 自监督学习预测维度
            value_prefix_length: 值前缀长度
            use_distributional_value: 是否使用分布式价值头
            value_support_size: 分布式价值支持大小
            value_min: 价值范围最小值
            value_max: 价值范围最大值
            risk_alpha: CVaR的置信水平
            risk_beta: 风险厌恶系数
            use_belief_state: 是否使用信念状态
            belief_dim: 信念状态维度
            use_belief_attention: 是否使用注意力机制处理信念
            belief_attention_heads: 注意力头数
            use_residual_belief: 是否使用残差连接
            use_gating_mechanism: 是否使用门控机制
            num_simulations: MCTS模拟次数
            c_puct: UCB公式中的探索常数
            dirichlet_alpha: Dirichlet噪声参数
            exploration_fraction: 探索比例
            pb_c_base: Progressive bias基础值
            pb_c_init: Progressive bias初始值
            replay_buffer_size: 回放缓冲区大小
            batch_size: 批次大小
            num_unroll_steps: 展开步数
            td_steps: TD学习步数
            value_loss_weight: 价值损失权重
            policy_loss_weight: 策略损失权重
            consistency_loss_weight: 一致性损失权重
            self_supervised_loss_weight: 自监督损失权重
            use_ewc: 是否使用EWC算法
            ewc_lambda: EWC正则化系数
            ewc_state_path: EWC状态保存路径
            learning_rate: 学习率
            weight_decay: 权重衰减
            lr_scheduler: 学习率调度器类型
            use_mixed_precision: 是否使用混合精度训练
            dynamic_loss_scaling: 是否使用动态损失缩放
            device: 设备类型
        """
        # 调用父类初始化
        super().__init__(
            state_shape=state_shape,
            action_shape=action_shape,
            hidden_dim=hidden_dim,
            state_dim=state_dim,
            use_resnet=use_resnet,
            projection_dim=projection_dim,
            prediction_dim=prediction_dim,
            value_prefix_length=value_prefix_length,
            use_distributional_value=use_distributional_value,
            value_support_size=value_support_size,
            value_min=value_min,
            value_max=value_max,
            risk_alpha=risk_alpha,
            risk_beta=risk_beta,
            use_belief_state=use_belief_state,
            belief_dim=belief_dim,
            use_belief_attention=use_belief_attention,
            belief_attention_heads=belief_attention_heads,
            use_residual_belief=use_residual_belief,
            use_gating_mechanism=use_gating_mechanism,
            num_simulations=num_simulations,
            c_puct=c_puct,
            dirichlet_alpha=dirichlet_alpha,
            exploration_fraction=exploration_fraction,
            pb_c_base=pb_c_base,
            pb_c_init=pb_c_init,
            replay_buffer_size=replay_buffer_size,
            batch_size=batch_size,
            num_unroll_steps=num_unroll_steps,
            td_steps=td_steps,
            value_loss_weight=value_loss_weight,
            policy_loss_weight=policy_loss_weight,
            consistency_loss_weight=consistency_loss_weight,
            self_supervised_loss_weight=self_supervised_loss_weight,
            use_ewc=use_ewc,
            ewc_lambda=ewc_lambda,
            ewc_state_path=ewc_state_path,
            learning_rate=learning_rate,
            weight_decay=weight_decay,
            lr_scheduler=lr_scheduler,
            device=device
        )

        # 混合精度训练设置
        self.use_mixed_precision = use_mixed_precision and torch.cuda.is_available()
        self.dynamic_loss_scaling = dynamic_loss_scaling

        # 创建梯度缩放器（用于混合精度训练）
        # 使用新的PyTorch 2.0+ API，避免FutureWarning
        try:
            # 尝试使用新的API
            from torch.amp import GradScaler as NewGradScaler
            self.scaler = NewGradScaler('cuda', enabled=self.use_mixed_precision)
        except ImportError:
            # 如果新API不可用，回退到旧API
            from torch.cuda.amp import GradScaler
            self.scaler = GradScaler(enabled=self.use_mixed_precision)

        # 记录混合精度训练状态
        self.amp_enabled = self.use_mixed_precision

        logger.info(f"EfficientZeroAMP初始化完成，混合精度训练: {'启用' if self.amp_enabled else '禁用'}")

    def update(self, experience: Union[Experience, Batch]) -> Dict[str, float]:
        """
        使用混合精度训练更新模型
        
        Args:
            experience (Union[Experience, Batch]): 单个经验或经验批次
            
        Returns:
            Dict[str, float]: 更新指标，如损失值等
        """
        # 如果是单个经验，先存入回放缓冲区
        if isinstance(experience, Experience):
            self.replay_buffer.add(experience)

            # 如果回放缓冲区样本不足，则跳过更新
            if len(self.replay_buffer) < self.batch_size:
                return {}

            # 从回放缓冲区采样批次数据
            batch = self.replay_buffer.sample(self.batch_size)
        else:
            batch = experience

        # 将数据移动到设备上
        states = torch.FloatTensor(np.array([exp.state for exp in batch])).to(self.device)
        actions = torch.LongTensor(np.array([exp.action.action_id for exp in batch])).to(self.device)
        rewards = torch.FloatTensor(np.array([exp.reward for exp in batch])).to(self.device)
        next_states = torch.FloatTensor(np.array([exp.next_state for exp in batch])).to(self.device)
        dones = torch.FloatTensor(np.array([float(exp.done) for exp in batch])).to(self.device)

        # 清零梯度
        self.optimizer.zero_grad()

        # 使用混合精度训练
        with autocast(device_type='cuda', enabled=self.amp_enabled):
            # 训练MuZero模型
            self.model.representation_network.train()
            self.model.dynamics_network.train()
            self.model.prediction_network.train()

            # 获取初始隐藏状态
            hidden_states = self.model.representation_network(states)

            # 预测策略和价值
            if self.use_distributional_value:
                # 如果使用分布式价值头，分别获取策略和价值
                policy_logits = self.model.policy_head(hidden_states)
                value_logits = self.model.distributional_value_head(hidden_states)

                # 计算风险敏感价值（用于记录）
                values = self.model.distributional_value_head.compute_risk_sensitive_value(
                    value_logits,
                    alpha=self.risk_alpha,
                    beta=self.risk_beta
                )
            else:
                # 使用标准预测网络
                policy_logits, values = self.model.prediction_network(hidden_states)

            # 计算目标值 (n步回报)
            target_values = self._compute_target_values(rewards, dones, next_states)

            # 计算策略目标 (MCTS访问计数)
            target_policies = self._compute_target_policies(states)

            # 初始化损失
            value_loss = 0.0
            policy_loss = 0.0
            reward_loss = 0.0
            consistency_loss = 0.0
            self_supervised_loss = 0.0

            # 初始化损失缩放因子
            gradient_scale = 1.0 / self.num_unroll_steps

            # 计算初始状态的策略损失
            policy_loss += F.cross_entropy(policy_logits, target_policies)

            # 计算初始状态的值损失
            if self.use_distributional_value:
                # 使用分布式价值头计算损失
                value_loss += self._compute_distributional_value_loss(value_logits, target_values)
            else:
                # 使用标准MSE损失
                value_loss += F.mse_loss(values, target_values)

            # 展开模型动力学
            current_hidden_states = hidden_states
            for step in range(self.num_unroll_steps):
                # 获取当前步的动作
                step_actions = actions if step == 0 else torch.argmax(target_policies, dim=1)

                # 使用动态网络预测下一个状态和奖励
                next_hidden_states, predicted_rewards = self.model.dynamics_network(current_hidden_states, step_actions)

                # 使用预测网络预测策略和价值
                if self.use_distributional_value:
                    next_policy_logits = self.model.policy_head(next_hidden_states)
                    next_value_logits = self.model.distributional_value_head(next_hidden_states)
                    next_values = self.model.distributional_value_head.compute_risk_sensitive_value(
                        next_value_logits,
                        alpha=self.risk_alpha,
                        beta=self.risk_beta
                    )
                else:
                    next_policy_logits, next_values = self.model.prediction_network(next_hidden_states)

                # 计算目标策略和价值
                step_target_policies = target_policies
                step_target_values = target_values

                # 计算当前步的奖励
                step_rewards = rewards[:, step:step+1] if step < rewards.shape[1] else rewards[:, -1:]

                # 计算策略损失
                policy_loss += gradient_scale * F.cross_entropy(next_policy_logits, step_target_policies)

                # 计算值损失
                if self.use_distributional_value:
                    value_loss += gradient_scale * self._compute_distributional_value_loss(next_value_logits, step_target_values)
                else:
                    value_loss += gradient_scale * F.mse_loss(next_values, step_target_values)

                # 计算奖励损失
                reward_loss += gradient_scale * F.mse_loss(predicted_rewards, step_rewards)

                # 计算一致性损失（使用目标网络）
                if self.consistency_loss_weight > 0:
                    with torch.no_grad():
                        target_hidden_states = self.target_model.representation_network(next_states)
                    consistency_loss += gradient_scale * F.mse_loss(next_hidden_states, target_hidden_states)

                # 计算自监督损失（如果有自监督模块）
                if hasattr(self.model, 'self_supervised_module') and self.self_supervised_loss_weight > 0:
                    self_supervised_loss += gradient_scale * self.model.self_supervised_loss(
                        current_hidden_states, next_hidden_states
                    )

                # 更新当前隐藏状态
                current_hidden_states = next_hidden_states

            # 计算总损失
            total_loss = (
                self.value_loss_weight * value_loss +
                self.policy_loss_weight * policy_loss +
                reward_loss +
                self.consistency_loss_weight * consistency_loss +
                self.self_supervised_loss_weight * self_supervised_loss
            )

        # 使用梯度缩放器进行反向传播
        self.scaler.scale(total_loss).backward()

        # 梯度裁剪（在缩放后的梯度上）
        if hasattr(self, 'gradient_clip_norm') and self.gradient_clip_norm > 0:
            self.scaler.unscale_(self.optimizer)
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.gradient_clip_norm)

        # 更新参数
        self.scaler.step(self.optimizer)

        # 更新缩放器
        self.scaler.update()

        # 更新学习率
        if hasattr(self, 'lr_scheduler') and self.lr_scheduler is not None:
            self.lr_scheduler.step()

        # 返回损失信息
        return {
            'total_loss': total_loss.item(),
            'value_loss': value_loss.item(),
            'policy_loss': policy_loss.item(),
            'reward_loss': reward_loss.item(),
            'consistency_loss': consistency_loss.item(),
            'self_supervised_loss': self_supervised_loss.item(),
            'learning_rate': self.optimizer.param_groups[0]['lr']
        }
