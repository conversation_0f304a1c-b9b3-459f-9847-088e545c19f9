"""
专家调度器模块

提供动态选择最合适的专家策略的功能，根据当前局面特征（如复杂度评分、游戏阶段、历史胜率、
可用计算资源等）选择最合适的专家策略来执行，或者为不同的专家分配计算预算。
"""

import os
import logging
import json
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Any, Optional, Union, Tuple
from abc import ABC, abstractmethod

from cardgame_ai.core.expert_pool import ExpertPolicyPool
from cardgame_ai.core.policy import Policy
from cardgame_ai.core.base import State, Action

# 配置日志
logger = logging.getLogger(__name__)


class ExpertScheduler(ABC):
    """
    专家调度器基类

    定义专家调度器的标准接口。
    """

    def __init__(self, expert_pool: ExpertPolicyPool):
        """
        初始化专家调度器

        Args:
            expert_pool: 专家策略池
        """
        self.expert_pool = expert_pool
        self.expert_names = expert_pool.list_experts()

        # 初始化统计信息
        self.stats = {
            "total_decisions": 0,
            "expert_usage": {name: 0 for name in self.expert_names},
            "expert_rewards": {name: [] for name in self.expert_names},
            "expert_selection_time": {name: 0.0 for name in self.expert_names}
        }

    @abstractmethod
    def select_expert(self, state: State, legal_actions: List[Action]) -> str:
        """
        选择专家

        Args:
            state: 当前状态
            legal_actions: 合法动作列表

        Returns:
            选择的专家名称
        """
        pass

    def update_reward(self, expert_name: str, reward: float) -> None:
        """
        更新专家奖励

        Args:
            expert_name: 专家名称
            reward: 奖励值
        """
        if expert_name in self.expert_names:
            self.stats["expert_rewards"][expert_name].append(reward)

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            统计信息
        """
        stats = {
            "total_decisions": self.stats["total_decisions"],
            "expert_usage": self.stats["expert_usage"].copy()
        }

        # 计算专家使用率
        if self.stats["total_decisions"] > 0:
            stats["expert_usage_ratio"] = {
                name: count / self.stats["total_decisions"]
                for name, count in self.stats["expert_usage"].items()
            }
        else:
            stats["expert_usage_ratio"] = {name: 0.0 for name in self.expert_names}

        # 计算专家平均奖励
        stats["expert_avg_reward"] = {
            name: np.mean(rewards) if rewards else 0.0
            for name, rewards in self.stats["expert_rewards"].items()
        }

        # 计算专家平均选择时间
        stats["expert_avg_selection_time"] = {
            name: time / max(1, self.stats["expert_usage"][name])
            for name, time in self.stats["expert_selection_time"].items()
        }

        return stats

    def save(self, path: str) -> None:
        """
        保存调度器

        Args:
            path: 保存路径
        """
        # 创建目录
        os.makedirs(os.path.dirname(path), exist_ok=True)

        # 保存统计信息
        with open(path, 'w', encoding='utf-8') as f:
            json.dump(self.stats, f, indent=4)

        logger.info(f"已保存调度器统计信息到 {path}")

    def load(self, path: str) -> None:
        """
        加载调度器

        Args:
            path: 加载路径
        """
        if not os.path.exists(path):
            logger.warning(f"调度器统计信息文件不存在: {path}")
            return

        # 加载统计信息
        with open(path, 'r', encoding='utf-8') as f:
            self.stats = json.load(f)

        logger.info(f"已加载调度器统计信息: {path}")


class RuleBasedScheduler(ExpertScheduler):
    """
    基于规则的专家调度器

    使用预定义的规则选择专家。
    """

    def __init__(
        self,
        expert_pool: ExpertPolicyPool,
        rules: Optional[Dict[str, Dict[str, Any]]] = None
    ):
        """
        初始化基于规则的专家调度器

        Args:
            expert_pool: 专家策略池
            rules: 规则字典，键为条件名称，值为条件参数
        """
        super().__init__(expert_pool)

        # 设置默认规则
        if rules is None:
            self.rules = {
                "complexity": {
                    "high": "search",     # 高复杂度使用搜索
                    "medium": "neural",    # 中等复杂度使用神经网络
                    "low": "rule_based"    # 低复杂度使用规则
                },
                "game_stage": {
                    "early": "rule_based",  # 游戏早期使用规则
                    "middle": "neural",     # 游戏中期使用神经网络
                    "late": "search"        # 游戏后期使用搜索
                },
                "hand_size": {
                    "many": "neural",       # 手牌多使用神经网络
                    "few": "search"         # 手牌少使用搜索
                }
            }
        else:
            self.rules = rules

        # 初始化专家映射
        self.expert_mapping = self._create_expert_mapping()

        logger.info(f"已创建基于规则的专家调度器，规则: {self.rules}")

    def _create_expert_mapping(self) -> Dict[str, List[str]]:
        """
        创建专家映射

        将规则中的专家类型映射到实际的专家名称。

        Returns:
            专家映射字典
        """
        # 获取专家元数据
        expert_metadata = self.expert_pool.get_all_metadata()

        # 创建映射
        mapping = {
            "search": [],
            "neural": [],
            "rule_based": [],
            "random": []
        }

        # 根据专家类型进行映射
        for name, metadata in expert_metadata.items():
            expert_type = metadata.get("type", "")

            if expert_type in ["mcts", "muzero", "efficient_zero"]:
                mapping["search"].append(name)
            elif expert_type in ["dqn", "ppo", "transformer"]:
                mapping["neural"].append(name)
            elif expert_type == "rule_based":
                mapping["rule_based"].append(name)
            elif expert_type == "random":
                mapping["random"].append(name)

        return mapping

    def select_expert(self, state: State, legal_actions: List[Action]) -> str:
        """
        选择专家

        Args:
            state: 当前状态
            legal_actions: 合法动作列表

        Returns:
            选择的专家名称
        """
        import time
        start_time = time.time()

        # 更新统计信息
        self.stats["total_decisions"] += 1

        # 计算状态特征
        features = self._extract_features(state)

        # 根据规则选择专家类型
        expert_type = self._apply_rules(features)

        # 从映射中选择具体的专家
        candidates = self.expert_mapping.get(expert_type, [])

        if not candidates:
            # 如果没有匹配的专家，使用随机专家
            expert_name = np.random.choice(self.expert_names)
            logger.warning(f"没有找到类型为 '{expert_type}' 的专家，随机选择 '{expert_name}'")
        else:
            # 随机选择一个匹配的专家
            expert_name = np.random.choice(candidates)

        # 更新统计信息
        self.stats["expert_usage"][expert_name] = self.stats["expert_usage"].get(expert_name, 0) + 1
        self.stats["expert_selection_time"][expert_name] = self.stats["expert_selection_time"].get(expert_name, 0.0) + (time.time() - start_time)

        return expert_name

    def _extract_features(self, state: State) -> Dict[str, Any]:
        """
        提取状态特征

        Args:
            state: 当前状态

        Returns:
            特征字典
        """
        features = {}

        # 计算复杂度
        complexity = self._calculate_complexity(state)
        if complexity > 0.7:
            features["complexity"] = "high"
        elif complexity > 0.3:
            features["complexity"] = "medium"
        else:
            features["complexity"] = "low"

        # 计算游戏阶段
        if hasattr(state, "round") and hasattr(state, "max_rounds"):
            progress = state.round / state.max_rounds
            if progress < 0.3:
                features["game_stage"] = "early"
            elif progress < 0.7:
                features["game_stage"] = "middle"
            else:
                features["game_stage"] = "late"

        # 计算手牌数量
        if hasattr(state, "hand") and hasattr(state, "initial_hand_size"):
            hand_ratio = len(state.hand) / state.initial_hand_size
            if hand_ratio > 0.5:
                features["hand_size"] = "many"
            else:
                features["hand_size"] = "few"

        return features

    def _calculate_complexity(self, state: State) -> float:
        """
        计算状态复杂度

        Args:
            state: 当前状态

        Returns:
            复杂度分数，范围[0, 1]
        """
        # 基础复杂度
        complexity = 0.5

        # 根据状态属性调整复杂度
        if hasattr(state, "hand"):
            # 手牌数量影响复杂度
            hand_size = len(state.hand)
            if hand_size > 10:
                complexity += 0.2
            elif hand_size < 5:
                complexity -= 0.1

        # 考虑当前局面状态
        if hasattr(state, "last_move") and state.last_move:
            # 如果上家出牌了，增加复杂度
            complexity += 0.2

            # 如果上家出的是复杂牌型，进一步增加复杂度
            if hasattr(state.last_move, "card_type"):
                complex_types = ["STRAIGHT", "STRAIGHT_PAIR", "AIRPLANE", "AIRPLANE_WITH_SINGLE",
                                "AIRPLANE_WITH_PAIR", "FOUR_WITH_TWO_SINGLE", "FOUR_WITH_TWO_PAIR"]
                if state.last_move.card_type in complex_types:
                    complexity += 0.2

        # 返回规范化的复杂度分数
        return max(0.0, min(1.0, complexity))

    def _apply_rules(self, features: Dict[str, Any]) -> str:
        """
        应用规则

        Args:
            features: 特征字典

        Returns:
            选择的专家类型
        """
        # 优先级顺序：复杂度 > 游戏阶段 > 手牌数量
        priorities = ["complexity", "game_stage", "hand_size"]

        # 按优先级应用规则
        for priority in priorities:
            if priority in features and priority in self.rules:
                feature_value = features[priority]
                if feature_value in self.rules[priority]:
                    return self.rules[priority][feature_value]

        # 默认使用神经网络
        return "neural"


class NeuralScheduler(ExpertScheduler):
    """
    基于神经网络的专家调度器

    使用神经网络选择专家。
    """

    def __init__(
        self,
        expert_pool: ExpertPolicyPool,
        state_feature_dim: int,
        hidden_dims: List[int] = [128, 64],
        learning_rate: float = 0.001,
        device: Optional[str] = None
    ):
        """
        初始化基于神经网络的专家调度器

        Args:
            expert_pool: 专家策略池
            state_feature_dim: 状态特征维度
            hidden_dims: 隐藏层维度列表
            learning_rate: 学习率
            device: 计算设备
        """
        super().__init__(expert_pool)

        # 设置设备
        self.device = device if device else ('cuda' if torch.cuda.is_available() else 'cpu')

        # 创建神经网络
        self.network = self._create_network(state_feature_dim, hidden_dims, len(self.expert_names))
        self.network.to(self.device)

        # 创建优化器
        self.optimizer = torch.optim.Adam(self.network.parameters(), lr=learning_rate)

        # 创建特征提取器
        self.feature_extractor = StateFeatureExtractor(state_feature_dim)

        # 初始化训练缓冲区
        self.buffer = {
            "states": [],
            "expert_indices": [],
            "rewards": []
        }

        # 初始化额外统计信息
        self.stats.update({
            "training_loss": [],
            "training_accuracy": [],
            "entropy": []
        })

        logger.info(f"已创建基于神经网络的专家调度器，特征维度: {state_feature_dim}, 隐藏层: {hidden_dims}")

    def _create_network(self, input_dim: int, hidden_dims: List[int], output_dim: int) -> nn.Module:
        """
        创建神经网络

        Args:
            input_dim: 输入维度
            hidden_dims: 隐藏层维度列表
            output_dim: 输出维度

        Returns:
            神经网络模型
        """
        layers = []

        # 输入层
        layers.append(nn.Linear(input_dim, hidden_dims[0]))
        layers.append(nn.ReLU())

        # 隐藏层
        for i in range(len(hidden_dims) - 1):
            layers.append(nn.Linear(hidden_dims[i], hidden_dims[i + 1]))
            layers.append(nn.ReLU())

        # 输出层
        layers.append(nn.Linear(hidden_dims[-1], output_dim))

        return nn.Sequential(*layers)

    def select_expert(self, state: State, legal_actions: List[Action]) -> str:
        """
        选择专家

        Args:
            state: 当前状态
            legal_actions: 合法动作列表

        Returns:
            选择的专家名称
        """
        import time
        start_time = time.time()

        # 更新统计信息
        self.stats["total_decisions"] += 1

        # 提取状态特征
        state_features = self.feature_extractor.extract(state)
        state_tensor = torch.tensor(state_features, dtype=torch.float32).unsqueeze(0).to(self.device)

        # 前向传播
        with torch.no_grad():
            logits = self.network(state_tensor)
            probs = F.softmax(logits, dim=1)

        # 计算熵
        entropy = -torch.sum(probs * torch.log(probs + 1e-10), dim=1).item()
        self.stats["entropy"].append(entropy)

        # 选择专家
        expert_index = torch.argmax(probs, dim=1).item()
        expert_name = self.expert_names[expert_index]

        # 保存到缓冲区
        self.buffer["states"].append(state_features)
        self.buffer["expert_indices"].append(expert_index)

        # 更新统计信息
        self.stats["expert_usage"][expert_name] = self.stats["expert_usage"].get(expert_name, 0) + 1
        self.stats["expert_selection_time"][expert_name] = self.stats["expert_selection_time"].get(expert_name, 0.0) + (time.time() - start_time)

        return expert_name

    def update_reward(self, expert_name: str, reward: float) -> None:
        """
        更新专家奖励

        Args:
            expert_name: 专家名称
            reward: 奖励值
        """
        super().update_reward(expert_name, reward)

        # 更新缓冲区
        if self.buffer["expert_indices"]:
            self.buffer["rewards"].append(reward)

    def train(self, batch_size: int = 32, epochs: int = 10) -> Dict[str, float]:
        """
        训练神经网络

        Args:
            batch_size: 批次大小
            epochs: 训练轮数

        Returns:
            训练统计信息
        """
        # 检查缓冲区是否有足够的数据
        if len(self.buffer["states"]) < batch_size:
            logger.warning(f"缓冲区数据不足，需要至少 {batch_size} 条数据，当前有 {len(self.buffer['states'])} 条")
            return {"loss": 0.0, "accuracy": 0.0}

        # 准备数据
        states = torch.tensor(self.buffer["states"], dtype=torch.float32).to(self.device)
        expert_indices = torch.tensor(self.buffer["expert_indices"], dtype=torch.long).to(self.device)
        rewards = torch.tensor(self.buffer["rewards"], dtype=torch.float32).to(self.device)

        # 归一化奖励
        if len(rewards) > 1:
            rewards = (rewards - rewards.mean()) / (rewards.std() + 1e-8)

        # 训练循环
        total_loss = 0.0
        total_accuracy = 0.0

        for _ in range(epochs):
            # 随机打乱数据
            indices = torch.randperm(len(states))

            # 批次训练
            for i in range(0, len(indices), batch_size):
                # 获取批次索引
                batch_indices = indices[i:i+batch_size]

                # 获取批次数据
                batch_states = states[batch_indices]
                batch_expert_indices = expert_indices[batch_indices]
                batch_rewards = rewards[batch_indices] if i + batch_size <= len(indices) else rewards[batch_indices[:len(rewards)-i]]

                # 前向传播
                logits = self.network(batch_states)
                probs = F.softmax(logits, dim=1)

                # 计算损失
                log_probs = F.log_softmax(logits, dim=1)
                selected_log_probs = log_probs[torch.arange(len(batch_expert_indices)), batch_expert_indices]
                loss = -torch.mean(selected_log_probs * batch_rewards)

                # 反向传播
                self.optimizer.zero_grad()
                loss.backward()
                self.optimizer.step()

                # 计算准确率
                predictions = torch.argmax(probs, dim=1)
                accuracy = (predictions == batch_expert_indices).float().mean().item()

                # 累加统计信息
                total_loss += loss.item()
                total_accuracy += accuracy

        # 计算平均值
        avg_loss = total_loss / (len(states) // batch_size * epochs)
        avg_accuracy = total_accuracy / (len(states) // batch_size * epochs)

        # 更新统计信息
        self.stats["training_loss"].append(avg_loss)
        self.stats["training_accuracy"].append(avg_accuracy)

        # 清空缓冲区
        self.buffer = {
            "states": [],
            "expert_indices": [],
            "rewards": []
        }

        logger.info(f"训练完成，损失: {avg_loss:.4f}, 准确率: {avg_accuracy:.4f}")

        return {"loss": avg_loss, "accuracy": avg_accuracy}

    def save(self, path: str) -> None:
        """
        保存调度器

        Args:
            path: 保存路径
        """
        # 创建目录
        os.makedirs(os.path.dirname(path), exist_ok=True)

        # 保存模型
        model_path = os.path.join(os.path.dirname(path), f"{os.path.basename(path)}.pt")
        torch.save({
            'model_state_dict': self.network.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'expert_names': self.expert_names,
            'stats': self.stats
        }, model_path)

        # 保存统计信息
        super().save(path)

        logger.info(f"已保存神经网络调度器模型到 {model_path}")

    def load(self, path: str) -> None:
        """
        加载调度器

        Args:
            path: 加载路径
        """
        # 加载统计信息
        super().load(path)

        # 加载模型
        model_path = os.path.join(os.path.dirname(path), f"{os.path.basename(path)}.pt")
        if not os.path.exists(model_path):
            logger.warning(f"模型文件不存在: {model_path}")
            return

        checkpoint = torch.load(model_path, map_location=self.device)
        self.network.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.expert_names = checkpoint['expert_names']
        self.stats.update(checkpoint['stats'])

        logger.info(f"已加载神经网络调度器模型: {model_path}")


class StateFeatureExtractor:
    """
    状态特征提取器

    从游戏状态中提取特征。
    """

    def __init__(self, output_dim: int = 64):
        """
        初始化状态特征提取器

        Args:
            output_dim: 输出特征维度
        """
        self.output_dim = output_dim

    def extract(self, state: State) -> np.ndarray:
        """
        提取特征

        Args:
            state: 游戏状态

        Returns:
            特征向量
        """
        # 初始化特征向量
        features = np.zeros(self.output_dim)

        # 提取基本特征
        feature_idx = 0

        # 手牌特征
        if hasattr(state, "hand"):
            # 手牌数量
            hand_size = len(state.hand)
            features[feature_idx] = hand_size / 20.0  # 归一化
            feature_idx += 1

            # 手牌类型分布
            if hasattr(state, "get_card_type_distribution"):
                distribution = state.get_card_type_distribution()
                for card_type, count in distribution.items():
                    if feature_idx < self.output_dim:
                        features[feature_idx] = count / hand_size
                        feature_idx += 1

        # 游戏阶段特征
        if hasattr(state, "round") and hasattr(state, "max_rounds"):
            features[feature_idx] = state.round / state.max_rounds
            feature_idx += 1

        # 上家出牌特征
        if hasattr(state, "last_move") and state.last_move:
            features[feature_idx] = 1.0  # 上家出牌了
            feature_idx += 1

            # 上家出牌类型
            if hasattr(state.last_move, "card_type") and feature_idx < self.output_dim:
                # 简单编码牌型
                card_types = ["SINGLE", "PAIR", "TRIPLE", "BOMB", "STRAIGHT", "STRAIGHT_PAIR",
                             "AIRPLANE", "AIRPLANE_WITH_SINGLE", "AIRPLANE_WITH_PAIR",
                             "FOUR_WITH_TWO_SINGLE", "FOUR_WITH_TWO_PAIR"]
                if state.last_move.card_type in card_types:
                    type_idx = card_types.index(state.last_move.card_type)
                    features[feature_idx + type_idx] = 1.0
                feature_idx += len(card_types)
        else:
            features[feature_idx] = 0.0  # 上家没出牌
            feature_idx += 1

        # 玩家角色特征
        if hasattr(state, "role"):
            roles = ["LANDLORD", "FARMER_1", "FARMER_2"]
            if state.role in roles:
                role_idx = roles.index(state.role)
                features[feature_idx + role_idx] = 1.0
            feature_idx += len(roles)

        # 剩余玩家特征
        if hasattr(state, "num_players") and hasattr(state, "active_players"):
            features[feature_idx] = len(state.active_players) / state.num_players
            feature_idx += 1

        # 确保特征维度正确
        if feature_idx < self.output_dim:
            # 填充剩余维度
            remaining = self.output_dim - feature_idx
            features[feature_idx:] = np.random.randn(remaining) * 0.01
        elif feature_idx > self.output_dim:
            # 截断多余维度
            features = features[:self.output_dim]

        return features


class UCBScheduler(ExpertScheduler):
    """
    基于UCB的专家调度器

    使用UCB（Upper Confidence Bound）算法选择专家，平衡探索与利用。
    """

    def __init__(
        self,
        expert_pool: ExpertPolicyPool,
        c: float = 2.0,
        initial_value: float = 1.0
    ):
        """
        初始化基于UCB的专家调度器

        Args:
            expert_pool: 专家策略池
            c: 探索参数
            initial_value: 初始值
        """
        super().__init__(expert_pool)

        # 设置UCB参数
        self.c = c
        self.initial_value = initial_value

        # 初始化UCB统计信息
        self.ucb_stats = {
            name: {"value": initial_value, "count": 0}
            for name in self.expert_names
        }

        logger.info(f"已创建基于UCB的专家调度器，探索参数: {c}, 初始值: {initial_value}")

    def select_expert(self, state: State, legal_actions: List[Action]) -> str:
        """
        选择专家

        Args:
            state: 当前状态
            legal_actions: 合法动作列表

        Returns:
            选择的专家名称
        """
        import time
        start_time = time.time()

        # 更新统计信息
        self.stats["total_decisions"] += 1

        # 计算UCB分数
        total_count = sum(stats["count"] for stats in self.ucb_stats.values())

        # 如果有专家从未被选择过，优先选择
        never_selected = [name for name, stats in self.ucb_stats.items() if stats["count"] == 0]
        if never_selected:
            expert_name = np.random.choice(never_selected)
        else:
            # 计算每个专家的UCB分数
            ucb_scores = {}
            for name, stats in self.ucb_stats.items():
                # UCB公式：value + c * sqrt(ln(total_count) / count)
                exploration = self.c * np.sqrt(np.log(total_count) / stats["count"])
                ucb_scores[name] = stats["value"] + exploration

            # 选择UCB分数最高的专家
            expert_name = max(ucb_scores.items(), key=lambda x: x[1])[0]

        # 更新统计信息
        self.stats["expert_usage"][expert_name] = self.stats["expert_usage"].get(expert_name, 0) + 1
        self.stats["expert_selection_time"][expert_name] = self.stats["expert_selection_time"].get(expert_name, 0.0) + (time.time() - start_time)

        # 更新UCB统计信息
        self.ucb_stats[expert_name]["count"] += 1

        return expert_name

    def update_reward(self, expert_name: str, reward: float) -> None:
        """
        更新专家奖励

        Args:
            expert_name: 专家名称
            reward: 奖励值
        """
        super().update_reward(expert_name, reward)

        # 更新UCB统计信息
        if expert_name in self.ucb_stats:
            stats = self.ucb_stats[expert_name]
            # 增量更新平均值
            stats["value"] = (stats["value"] * (stats["count"] - 1) + reward) / stats["count"]

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            统计信息
        """
        stats = super().get_stats()

        # 添加UCB统计信息
        stats["ucb"] = {
            name: {
                "value": ucb_stats["value"],
                "count": ucb_stats["count"],
                "exploration": self.c * np.sqrt(np.log(max(1, self.stats["total_decisions"])) / max(1, ucb_stats["count"]))
            }
            for name, ucb_stats in self.ucb_stats.items()
        }

        return stats


class RuleBasedScheduler(ExpertScheduler):
    """
    基于规则的专家调度器

    使用预定义的规则选择专家。
    """

    def __init__(
        self,
        expert_pool: ExpertPolicyPool,
        rules: Optional[Dict[str, Dict[str, Any]]] = None
    ):
        """
        初始化基于规则的专家调度器

        Args:
            expert_pool: 专家策略池
            rules: 规则字典，键为条件名称，值为条件参数
        """
        super().__init__(expert_pool)

        # 设置规则
        self.rules = rules if rules else self._default_rules()

        # 初始化规则统计信息
        self.stats["rule_usage"] = {rule_name: 0 for rule_name in self.rules.keys()}

    def _default_rules(self) -> Dict[str, Dict[str, Any]]:
        """
        默认规则

        Returns:
            规则字典
        """
        # 获取专家类型
        expert_types = {}
        for name in self.expert_names:
            metadata = self.expert_pool.get_expert_metadata(name)
            if metadata and "type" in metadata:
                expert_types[name] = metadata["type"]

        # 创建默认规则
        rules = {
            "early_game": {
                "condition": lambda state, legal_actions: self._is_early_game(state),
                "expert_priority": self._get_expert_priority(
                    ["rule_based", "random"],
                    expert_types
                )
            },
            "mid_game": {
                "condition": lambda state, legal_actions: self._is_mid_game(state),
                "expert_priority": self._get_expert_priority(
                    ["efficient_zero", "mcts", "rule_based"],
                    expert_types
                )
            },
            "late_game": {
                "condition": lambda state, legal_actions: self._is_late_game(state),
                "expert_priority": self._get_expert_priority(
                    ["efficient_zero", "mcts", "transformer"],
                    expert_types
                )
            },
            "complex_situation": {
                "condition": lambda state, legal_actions: self._is_complex_situation(state, legal_actions),
                "expert_priority": self._get_expert_priority(
                    ["mcts", "efficient_zero", "transformer"],
                    expert_types
                )
            },
            "simple_situation": {
                "condition": lambda state, legal_actions: self._is_simple_situation(state, legal_actions),
                "expert_priority": self._get_expert_priority(
                    ["rule_based", "dqn", "random"],
                    expert_types
                )
            },
            "default": {
                "condition": lambda state, legal_actions: True,
                "expert_priority": self._get_expert_priority(
                    ["efficient_zero", "rule_based", "random"],
                    expert_types
                )
            }
        }

        return rules

    def _get_expert_priority(
        self,
        preferred_types: List[str],
        expert_types: Dict[str, str]
    ) -> List[str]:
        """
        获取专家优先级

        Args:
            preferred_types: 偏好的专家类型
            expert_types: 专家类型字典

        Returns:
            专家名称列表，按优先级排序
        """
        # 按偏好类型排序专家
        priority_list = []

        # 首先添加偏好类型的专家
        for ptype in preferred_types:
            for name, etype in expert_types.items():
                if etype == ptype and name not in priority_list:
                    priority_list.append(name)

        # 然后添加其他专家
        for name in self.expert_names:
            if name not in priority_list:
                priority_list.append(name)

        return priority_list

    def _is_early_game(self, state: State) -> bool:
        """
        判断是否为游戏早期

        Args:
            state: 当前状态

        Returns:
            是否为游戏早期
        """
        # 根据游戏特定逻辑判断
        # 这里使用一个简单的启发式方法：如果手牌数量大于初始手牌数量的70%，则为早期
        if hasattr(state, "hands") and hasattr(state, "initial_hands"):
            player_id = state.current_player
            if player_id < len(state.hands) and player_id < len(state.initial_hands):
                current_hand_size = len(state.hands[player_id])
                initial_hand_size = len(state.initial_hands[player_id])
                return current_hand_size > 0.7 * initial_hand_size

        # 如果没有手牌信息，使用回合数判断
        if hasattr(state, "round") and hasattr(state, "max_rounds"):
            return state.round < 0.3 * state.max_rounds

        # 默认返回False
        return False

    def _is_mid_game(self, state: State) -> bool:
        """
        判断是否为游戏中期

        Args:
            state: 当前状态

        Returns:
            是否为游戏中期
        """
        # 根据游戏特定逻辑判断
        # 这里使用一个简单的启发式方法：如果手牌数量在初始手牌数量的30%-70%之间，则为中期
        if hasattr(state, "hands") and hasattr(state, "initial_hands"):
            player_id = state.current_player
            if player_id < len(state.hands) and player_id < len(state.initial_hands):
                current_hand_size = len(state.hands[player_id])
                initial_hand_size = len(state.initial_hands[player_id])
                return 0.3 * initial_hand_size <= current_hand_size <= 0.7 * initial_hand_size

        # 如果没有手牌信息，使用回合数判断
        if hasattr(state, "round") and hasattr(state, "max_rounds"):
            return 0.3 * state.max_rounds <= state.round <= 0.7 * state.max_rounds

        # 默认返回False
        return False

    def _is_late_game(self, state: State) -> bool:
        """
        判断是否为游戏后期

        Args:
            state: 当前状态

        Returns:
            是否为游戏后期
        """
        # 根据游戏特定逻辑判断
        # 这里使用一个简单的启发式方法：如果手牌数量小于初始手牌数量的30%，则为后期
        if hasattr(state, "hands") and hasattr(state, "initial_hands"):
            player_id = state.current_player
            if player_id < len(state.hands) and player_id < len(state.initial_hands):
                current_hand_size = len(state.hands[player_id])
                initial_hand_size = len(state.initial_hands[player_id])
                return current_hand_size < 0.3 * initial_hand_size

        # 如果没有手牌信息，使用回合数判断
        if hasattr(state, "round") and hasattr(state, "max_rounds"):
            return state.round > 0.7 * state.max_rounds

        # 默认返回False
        return False
