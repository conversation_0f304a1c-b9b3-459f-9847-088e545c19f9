"""
监控服务适配器

该适配器将现有的监控系统适配为MonitoringInterface接口，
实现zhuchengxu模块与监控服务的解耦。

设计目标:
- 适配现有监控系统为标准接口
- 提供基础的监控功能
- 实现fail-fast原则

作者: Full Stack Dev James
版本: v1.0
"""

import time
import threading
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime
from collections import defaultdict, deque

from cardgame_ai.interfaces.monitoring_interface import (
    MonitoringInterface, MetricData, MetricType, AggregationType,
    AlertRule, Alert
)


class PerformanceMonitorAdapter(MonitoringInterface):
    """性能监控适配器
    
    提供基础的监控功能，适配为MonitoringInterface接口。
    
    注意:
        这是一个简化的实现，主要用于解耦目的。
        生产环境建议使用专业的监控系统。
    """
    
    def __init__(self):
        """初始化监控适配器"""
        self._is_monitoring = False
        self._metrics = defaultdict(lambda: deque(maxlen=1000))
        self._timers = {}
        self._alert_rules = {}
        self._active_alerts = {}
        self._alert_callbacks = {}
        self._callback_counter = 0
        self._lock = threading.Lock()
    
    def start_monitoring(self) -> bool:
        """启动监控服务"""
        try:
            self._is_monitoring = True
            return True
        except Exception:
            return False
    
    def stop_monitoring(self) -> bool:
        """停止监控服务"""
        try:
            self._is_monitoring = False
            return True
        except Exception:
            return False
    
    def is_monitoring(self) -> bool:
        """检查监控服务是否运行"""
        return self._is_monitoring
    
    def record_metric(self, metric: MetricData) -> bool:
        """记录监控指标"""
        try:
            with self._lock:
                self._metrics[metric.name].append(metric)
            return True
        except Exception:
            return False
    
    def record_counter(self, name: str, value: Union[int, float] = 1,
                      tags: Optional[Dict[str, str]] = None) -> bool:
        """记录计数器指标"""
        metric = MetricData(
            name=name,
            value=value,
            timestamp=datetime.now(),
            type=MetricType.COUNTER,
            tags=tags
        )
        return self.record_metric(metric)
    
    def record_gauge(self, name: str, value: Union[int, float],
                    tags: Optional[Dict[str, str]] = None) -> bool:
        """记录仪表盘指标"""
        metric = MetricData(
            name=name,
            value=value,
            timestamp=datetime.now(),
            type=MetricType.GAUGE,
            tags=tags
        )
        return self.record_metric(metric)
    
    def record_timer(self, name: str, duration: float,
                    tags: Optional[Dict[str, str]] = None) -> bool:
        """记录计时器指标"""
        metric = MetricData(
            name=name,
            value=duration,
            timestamp=datetime.now(),
            type=MetricType.TIMER,
            tags=tags,
            unit="seconds"
        )
        return self.record_metric(metric)
    
    def start_timer(self, name: str, tags: Optional[Dict[str, str]] = None) -> str:
        """启动计时器"""
        timer_id = f"{name}_{int(time.time() * 1000000)}"
        self._timers[timer_id] = {
            'name': name,
            'start_time': time.time(),
            'tags': tags
        }
        return timer_id
    
    def stop_timer(self, timer_id: str) -> Optional[float]:
        """停止计时器"""
        if timer_id not in self._timers:
            return None
        
        timer_info = self._timers.pop(timer_id)
        duration = time.time() - timer_info['start_time']
        
        # 记录计时器指标
        self.record_timer(timer_info['name'], duration, timer_info['tags'])
        
        return duration
    
    def get_metric(self, name: str, 
                  start_time: Optional[datetime] = None,
                  end_time: Optional[datetime] = None,
                  tags: Optional[Dict[str, str]] = None) -> List[MetricData]:
        """获取指标数据"""
        try:
            with self._lock:
                metrics = list(self._metrics.get(name, []))
            
            # 时间过滤
            if start_time or end_time:
                filtered_metrics = []
                for metric in metrics:
                    if start_time and metric.timestamp < start_time:
                        continue
                    if end_time and metric.timestamp > end_time:
                        continue
                    filtered_metrics.append(metric)
                metrics = filtered_metrics
            
            # 标签过滤
            if tags:
                filtered_metrics = []
                for metric in metrics:
                    if metric.tags and all(
                        metric.tags.get(k) == v for k, v in tags.items()
                    ):
                        filtered_metrics.append(metric)
                metrics = filtered_metrics
            
            return metrics
            
        except Exception:
            return []
    
    def aggregate_metric(self, name: str, aggregation: AggregationType,
                        start_time: Optional[datetime] = None,
                        end_time: Optional[datetime] = None,
                        tags: Optional[Dict[str, str]] = None) -> Optional[float]:
        """聚合指标数据"""
        try:
            metrics = self.get_metric(name, start_time, end_time, tags)
            if not metrics:
                return None
            
            values = [m.value for m in metrics]
            
            if aggregation == AggregationType.SUM:
                return sum(values)
            elif aggregation == AggregationType.AVERAGE:
                return sum(values) / len(values)
            elif aggregation == AggregationType.MIN:
                return min(values)
            elif aggregation == AggregationType.MAX:
                return max(values)
            elif aggregation == AggregationType.COUNT:
                return len(values)
            else:
                return None
                
        except Exception:
            return None
    
    def list_metrics(self) -> List[str]:
        """列出所有指标名称"""
        with self._lock:
            return list(self._metrics.keys())
    
    def add_alert_rule(self, rule: AlertRule) -> bool:
        """添加告警规则"""
        try:
            self._alert_rules[rule.name] = rule
            return True
        except Exception:
            return False
    
    def remove_alert_rule(self, rule_name: str) -> bool:
        """移除告警规则"""
        try:
            if rule_name in self._alert_rules:
                del self._alert_rules[rule_name]
                return True
            return False
        except Exception:
            return False
    
    def get_alert_rules(self) -> List[AlertRule]:
        """获取所有告警规则"""
        return list(self._alert_rules.values())
    
    def get_active_alerts(self) -> List[Alert]:
        """获取活跃告警"""
        return list(self._active_alerts.values())
    
    def get_alert_history(self, start_time: Optional[datetime] = None,
                         end_time: Optional[datetime] = None) -> List[Alert]:
        """获取告警历史 - 简化实现"""
        return []
    
    def register_alert_callback(self, callback: Callable[[Alert], None]) -> str:
        """注册告警回调"""
        callback_id = f"callback_{self._callback_counter}"
        self._callback_counter += 1
        self._alert_callbacks[callback_id] = callback
        return callback_id
    
    def unregister_alert_callback(self, callback_id: str) -> bool:
        """注销告警回调"""
        if callback_id in self._alert_callbacks:
            del self._alert_callbacks[callback_id]
            return True
        return False
    
    def export_metrics(self, output_path: str, format: str = "json",
                      start_time: Optional[datetime] = None,
                      end_time: Optional[datetime] = None) -> bool:
        """导出监控数据 - 简化实现"""
        return False
    
    def get_dashboard_data(self, dashboard_name: str) -> Dict[str, Any]:
        """获取仪表板数据 - 简化实现"""
        return {}
    
    def create_dashboard(self, name: str, config: Dict[str, Any]) -> bool:
        """创建仪表板 - 简化实现"""
        return False
