"""
统一配置管理器

提供统一的配置加载、验证和管理功能，确保所有配置文件的一致性。
"""

import os
import json
import yaml
import logging
from typing import Dict, Any, Optional, List, Union
from pathlib import Path

logger = logging.getLogger(__name__)


class UnifiedConfigManager:
    """
    统一配置管理器
    
    负责加载、验证和管理所有配置文件，确保配置的一致性和正确性。
    """
    
    def __init__(self, project_root: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            project_root: 项目根目录路径
        """
        self.project_root = project_root or self._find_project_root()
        self.configs = {}
        self._config_schema = self._define_config_schema()
        
    def _find_project_root(self) -> str:
        """查找项目根目录"""
        current_dir = Path(__file__).parent
        while current_dir.parent != current_dir:
            if (current_dir / "cardgame_ai").exists():
                return str(current_dir)
            current_dir = current_dir.parent
        return str(Path.cwd())
    
    def _define_config_schema(self) -> Dict[str, Any]:
        """定义配置文件的标准结构"""
        return {
            "training": {
                "required": ["default_algorithm", "use_gpu"],
                "optional": ["gpu_id", "config_file", "models_dir", "logs_dir"],
                "defaults": {
                    "gpu_id": 0,
                    "models_dir": "models",
                    "logs_dir": "logs"
                }
            },
            "logging": {
                "required": ["level"],
                "optional": ["format", "file", "console"],
                "defaults": {
                    "level": "INFO",
                    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                    "console": True
                }
            },
            "paths": {
                "required": [],
                "optional": ["models", "configs", "logs", "data"],
                "defaults": {
                    "models": "models",
                    "configs": "configs", 
                    "logs": "logs",
                    "data": "data"
                }
            }
        }
    
    def load_config(self, config_path: str, config_type: str = "main") -> Dict[str, Any]:
        """
        加载配置文件
        
        Args:
            config_path: 配置文件路径
            config_type: 配置类型 (main, training, client等)
            
        Returns:
            加载的配置字典
        """
        # 转换为绝对路径
        if not os.path.isabs(config_path):
            config_path = os.path.join(self.project_root, config_path)
            
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
            
        # 根据文件扩展名选择加载方法
        _, ext = os.path.splitext(config_path)
        
        try:
            if ext.lower() in ['.json']:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            elif ext.lower() in ['.yaml', '.yml']:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
            else:
                raise ValueError(f"不支持的配置文件格式: {ext}")
                
            # 验证配置
            validated_config = self._validate_config(config, config_type)
            
            # 存储配置
            self.configs[config_type] = validated_config
            
            logger.info(f"成功加载配置文件: {config_path}")
            return validated_config
            
        except Exception as e:
            logger.error(f"加载配置文件失败 {config_path}: {e}")
            raise
    
    def _validate_config(self, config: Dict[str, Any], config_type: str) -> Dict[str, Any]:
        """
        验证配置文件的完整性和正确性
        
        Args:
            config: 原始配置
            config_type: 配置类型
            
        Returns:
            验证后的配置
        """
        validated_config = config.copy()
        
        # 验证训练配置
        if "training" in config:
            training_config = config["training"]
            schema = self._config_schema["training"]
            
            # 检查必需字段
            for field in schema["required"]:
                if field not in training_config:
                    raise ValueError(f"训练配置缺少必需字段: {field}")
            
            # 添加默认值
            for field, default_value in schema["defaults"].items():
                if field not in training_config:
                    validated_config["training"][field] = default_value
                    
            # 验证算法名称
            valid_algorithms = ["efficient_zero", "muzero", "mappo", "dqn"]
            if training_config["default_algorithm"] not in valid_algorithms:
                logger.warning(f"未知算法: {training_config['default_algorithm']}")
        
        # 验证路径配置
        if "paths" in config:
            paths_config = config["paths"]
            schema = self._config_schema["paths"]
            
            # 添加默认路径
            for field, default_value in schema["defaults"].items():
                if field not in paths_config:
                    validated_config["paths"][field] = default_value
                    
            # 转换为相对路径
            for path_key, path_value in validated_config["paths"].items():
                if os.path.isabs(path_value):
                    # 转换为相对于项目根目录的路径
                    try:
                        rel_path = os.path.relpath(path_value, self.project_root)
                        validated_config["paths"][path_key] = rel_path
                        logger.info(f"路径 {path_key} 已转换为相对路径: {rel_path}")
                    except ValueError:
                        # 如果无法转换为相对路径，保持原样
                        logger.warning(f"无法转换路径为相对路径: {path_value}")
        
        return validated_config
    
    def get_unified_config(self) -> Dict[str, Any]:
        """
        获取统一的配置
        
        Returns:
            合并后的统一配置
        """
        unified = {}
        
        # 合并所有已加载的配置
        for config_type, config in self.configs.items():
            unified = self._deep_merge(unified, config)
            
        return unified
    
    def _deep_merge(self, dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
        """深度合并两个字典"""
        result = dict1.copy()
        
        for key, value in dict2.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
                
        return result
    
    def save_config(self, config: Dict[str, Any], config_path: str) -> None:
        """
        保存配置到文件
        
        Args:
            config: 要保存的配置
            config_path: 保存路径
        """
        # 转换为绝对路径
        if not os.path.isabs(config_path):
            config_path = os.path.join(self.project_root, config_path)
            
        # 确保目录存在
        os.makedirs(os.path.dirname(config_path), exist_ok=True)
        
        # 根据文件扩展名选择保存格式
        _, ext = os.path.splitext(config_path)
        
        try:
            if ext.lower() in ['.json']:
                with open(config_path, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=4, ensure_ascii=False)
            elif ext.lower() in ['.yaml', '.yml']:
                with open(config_path, 'w', encoding='utf-8') as f:
                    yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
            else:
                raise ValueError(f"不支持的配置文件格式: {ext}")
                
            logger.info(f"配置已保存到: {config_path}")
            
        except Exception as e:
            logger.error(f"保存配置文件失败 {config_path}: {e}")
            raise
    
    def validate_all_configs(self) -> List[str]:
        """
        验证所有配置文件的一致性
        
        Returns:
            发现的问题列表
        """
        issues = []
        
        # 检查算法一致性
        algorithms = set()
        for config_type, config in self.configs.items():
            if "training" in config and "default_algorithm" in config["training"]:
                algorithms.add(config["training"]["default_algorithm"])
        
        if len(algorithms) > 1:
            issues.append(f"配置文件中算法不一致: {algorithms}")
            
        # 检查路径一致性
        # 这里可以添加更多的一致性检查
        
        return issues
