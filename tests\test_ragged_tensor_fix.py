"""
测试EfficientZero算法中ragged tensor问题的修复

该测试验证批次解析能够正确处理可变长度数据，
并在检测到ragged tensor时提供清晰的错误消息。
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import numpy as np
from cardgame_ai.algorithms.efficient_zero_algorithm import EfficientZero


class MockExperience:
    """模拟经验对象"""
    def __init__(self, state, action, reward, next_state, done):
        self.state = state
        self.action = action
        self.reward = reward
        self.next_state = next_state
        self.done = done


class MockState:
    """模拟状态对象"""
    def __init__(self, observation):
        self.observation = observation
    
    def get_observation(self):
        return self.observation


class MockAction:
    """模拟动作对象"""
    def __init__(self, action_idx):
        self.action_idx = action_idx
    
    def to_index(self):
        return self.action_idx


class MockBatch:
    """模拟Batch对象"""
    def __init__(self, experiences):
        self.experiences = experiences


def test_safe_tensor_conversion():
    """测试安全张量转换功能"""
    print("=== 测试安全张量转换功能 ===\n")
    
    # 创建EfficientZero实例
    algorithm = EfficientZero(
        state_shape=(4,),  # 简单的1D状态
        action_shape=(3,),
        hidden_dim=32,
        state_dim=16,
        batch_size=2,
        device='cpu'
    )
    
    # 测试1: 正常的一致形状数据
    print("测试1: 正常的一致形状数据")
    try:
        consistent_data = [
            np.array([1.0, 2.0, 3.0, 4.0]),
            np.array([5.0, 6.0, 7.0, 8.0]),
            np.array([9.0, 10.0, 11.0, 12.0])
        ]
        result = algorithm._safe_tensor_conversion(consistent_data, torch.float32)
        print(f"✓ 成功转换，形状: {result.shape}")
        assert result.shape == (3, 4)
    except Exception as e:
        print(f"✗ 测试1失败: {e}")
    
    # 测试2: ragged tensor（形状不一致）
    print("\n测试2: ragged tensor（形状不一致）")
    try:
        ragged_data = [
            np.array([1.0, 2.0, 3.0, 4.0]),      # 形状: (4,)
            np.array([5.0, 6.0]),                 # 形状: (2,) - 不一致！
            np.array([9.0, 10.0, 11.0])          # 形状: (3,) - 不一致！
        ]
        result = algorithm._safe_tensor_conversion(ragged_data, torch.float32)
        print("✗ 测试2失败: 应该抛出错误但没有")
    except ValueError as e:
        if "ragged tensor" in str(e):
            print(f"✓ 成功检测到ragged tensor: {e}")
        else:
            print(f"✗ 抛出了ValueError但错误消息不正确: {e}")
    except Exception as e:
        print(f"✗ 抛出了错误的异常类型: {type(e).__name__}: {e}")
    
    # 测试3: 整数数据转换
    print("\n测试3: 整数数据转换")
    try:
        int_data = [1, 2, 3, 4, 5]
        result = algorithm._safe_tensor_conversion(int_data, torch.long)
        print(f"✓ 成功转换整数数据，形状: {result.shape}, 类型: {result.dtype}")
        assert result.dtype == torch.long
    except Exception as e:
        print(f"✗ 测试3失败: {e}")


def test_extract_batch_data():
    """测试批次数据提取功能"""
    print("\n=== 测试批次数据提取功能 ===\n")
    
    # 创建EfficientZero实例
    algorithm = EfficientZero(
        state_shape=(4,),
        action_shape=(3,),
        hidden_dim=32,
        state_dim=16,
        batch_size=2,
        device='cpu'
    )
    
    # 测试1: 正常的经验数据
    print("测试1: 正常的经验数据")
    try:
        experiences = [
            MockExperience(
                state=MockState(np.array([1.0, 2.0, 3.0, 4.0])),
                action=MockAction(0),
                reward=1.0,
                next_state=MockState(np.array([2.0, 3.0, 4.0, 5.0])),
                done=False
            ),
            MockExperience(
                state=MockState(np.array([5.0, 6.0, 7.0, 8.0])),
                action=MockAction(1),
                reward=-1.0,
                next_state=MockState(np.array([6.0, 7.0, 8.0, 9.0])),
                done=True
            )
        ]
        
        obs, actions, rewards, next_states, dones = algorithm._extract_batch_data(experiences)
        
        print(f"✓ 成功提取批次数据:")
        print(f"  观察形状: {obs.shape}")
        print(f"  动作形状: {actions.shape}")
        print(f"  奖励形状: {rewards.shape}")
        print(f"  下一状态形状: {next_states.shape}")
        print(f"  完成标志形状: {dones.shape}")
        
        assert obs.shape == (2, 4)
        assert actions.shape == (2,)
        assert rewards.shape == (2,)
        assert next_states.shape == (2, 4)
        assert dones.shape == (2,)
        
    except Exception as e:
        print(f"✗ 测试1失败: {e}")
    
    # 测试2: 不一致形状的经验数据（应该失败）
    print("\n测试2: 不一致形状的经验数据（应该失败）")
    try:
        ragged_experiences = [
            MockExperience(
                state=MockState(np.array([1.0, 2.0, 3.0, 4.0])),  # 形状: (4,)
                action=MockAction(0),
                reward=1.0,
                next_state=MockState(np.array([2.0, 3.0])),        # 形状: (2,) - 不一致！
                done=False
            ),
            MockExperience(
                state=MockState(np.array([5.0, 6.0])),             # 形状: (2,) - 不一致！
                action=MockAction(1),
                reward=-1.0,
                next_state=MockState(np.array([6.0, 7.0, 8.0])),   # 形状: (3,) - 不一致！
                done=True
            )
        ]
        
        obs, actions, rewards, next_states, dones = algorithm._extract_batch_data(ragged_experiences)
        print("✗ 测试2失败: 应该抛出错误但没有")
        
    except ValueError as e:
        if "ragged tensor" in str(e):
            print(f"✓ 成功检测到ragged tensor: {e}")
        else:
            print(f"✗ 抛出了ValueError但错误消息不正确: {e}")
    except Exception as e:
        print(f"✗ 抛出了错误的异常类型: {type(e).__name__}: {e}")


if __name__ == "__main__":
    print("=== 测试EfficientZero ragged tensor修复 ===\n")
    
    try:
        test_safe_tensor_conversion()
        test_extract_batch_data()
        
        print("\n=== 所有测试完成 ===")
        print("\n修复总结:")
        print("- 添加了_safe_tensor_conversion方法处理ragged tensor")
        print("- 使用np.stack确保形状一致性")
        print("- 提供清晰的错误消息指出形状不一致问题")
        print("- 添加了_extract_batch_data辅助函数减少代码重复")
        print("- 符合fail-fast原则：及早发现数据问题")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
