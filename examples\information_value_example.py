"""
信息价值评估示例脚本

展示如何使用信息价值评估功能来指导探索或信息收集行为。
"""
import sys
import os
import numpy as np
import matplotlib.pyplot as plt

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cardgame_ai.games.common.belief_state import BeliefState
from cardgame_ai.algorithms.information_value import (
    InformationValueCalculator, 
    calculate_information_value,
    calculate_all_cards_information_value
)


def create_sample_belief_state():
    """创建示例信念状态"""
    # 创建一个简单的信念状态，包含所有牌的概率分布
    player_id = "opponent"
    
    # 创建所有牌的列表
    suits = ["♥", "♦", "♣", "♠"]
    ranks = ["3", "4", "5", "6", "7", "8", "9", "10", "J", "Q", "K", "A", "2"]
    all_cards = [suit + rank for suit in suits for rank in ranks]
    all_cards.extend(["小王", "大王"])
    
    # 创建概率分布（随机生成）
    np.random.seed(42)  # 固定随机种子，确保结果可重现
    probabilities = np.random.rand(len(all_cards))
    probabilities /= probabilities.sum()  # 归一化
    card_probabilities = {card: float(prob) for card, prob in zip(all_cards, probabilities)}
    
    # 创建信念状态
    belief_state = BeliefState(
        player_id=player_id,
        card_probabilities=card_probabilities
    )
    
    return belief_state, all_cards


def create_sample_policy_function():
    """创建示例策略函数"""
    # 创建一个简单的策略函数，根据信念状态和当前状态返回动作分布
    def policy_function(belief_state, current_state):
        # 这里只是一个示例，实际应用中应该使用真实的策略网络
        # 返回一个随机的动作分布
        np.random.seed(int(sum(current_state) * 1000))  # 使用当前状态作为随机种子
        action_probs = np.random.rand(10)  # 假设有10个可能的动作
        return action_probs / action_probs.sum()  # 归一化
    
    return policy_function


def create_sample_state():
    """创建示例状态"""
    # 创建一个简单的状态向量
    return np.array([0.1, 0.2, 0.3, 0.4, 0.5])


def plot_information_values(info_values, title):
    """绘制信息价值图表"""
    # 排序
    sorted_values = sorted(info_values.items(), key=lambda x: x[1], reverse=True)
    cards = [item[0] for item in sorted_values]
    values = [item[1] for item in sorted_values]
    
    # 只显示前20张牌
    if len(cards) > 20:
        cards = cards[:20]
        values = values[:20]
    
    # 绘制条形图
    plt.figure(figsize=(12, 6))
    plt.bar(cards, values)
    plt.xlabel('Cards')
    plt.ylabel('Information Value')
    plt.title(title)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.show()


def main():
    """主函数"""
    # 创建示例数据
    belief_state, all_cards = create_sample_belief_state()
    policy_function = create_sample_policy_function()
    current_state = create_sample_state()
    
    # 计算基本信息价值
    basic_values = calculate_all_cards_information_value(
        belief_state, all_cards, method='basic'
    )
    print("基本信息价值（前5张）:")
    for card, value in sorted(basic_values.items(), key=lambda x: x[1], reverse=True)[:5]:
        print(f"  {card}: {value:.4f}")
    
    # 计算熵减少
    entropy_values = calculate_all_cards_information_value(
        belief_state, all_cards, method='entropy'
    )
    print("\n熵减少（前5张）:")
    for card, value in sorted(entropy_values.items(), key=lambda x: x[1], reverse=True)[:5]:
        print(f"  {card}: {value:.4f}")
    
    # 计算动作分布变化
    action_values = calculate_all_cards_information_value(
        belief_state, all_cards, current_state, policy_function, method='action'
    )
    print("\n动作分布变化（前5张）:")
    for card, value in sorted(action_values.items(), key=lambda x: x[1], reverse=True)[:5]:
        print(f"  {card}: {value:.4f}")
    
    # 计算组合信息价值
    combined_values = calculate_all_cards_information_value(
        belief_state, all_cards, current_state, policy_function, method='combined'
    )
    print("\n组合信息价值（前5张）:")
    for card, value in sorted(combined_values.items(), key=lambda x: x[1], reverse=True)[:5]:
        print(f"  {card}: {value:.4f}")
    
    # 绘制信息价值图表
    plot_information_values(basic_values, "Basic Information Value")
    plot_information_values(entropy_values, "Entropy Reduction")
    plot_information_values(action_values, "Action Distribution Change")
    plot_information_values(combined_values, "Combined Information Value")


if __name__ == "__main__":
    main()
