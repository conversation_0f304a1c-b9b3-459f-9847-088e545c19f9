"""
高级Transformer网络模块

实现高级Transformer架构，包括更多的注意力头、更深的层数、相对位置编码和前置层正规化等优化。
主要用于提高模型处理卡牌游戏序列信息的能力，特别是长期依赖关系和复杂模式。
"""
import math
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional, Any, Union
import matplotlib.pyplot as plt
import seaborn as sns

from cardgame_ai.algorithms.enhanced_transformer import (
    EnhancedMultiHeadAttention,
    EnhancedPositionalEncoding,
    EnhancedTransformerEncoderLayer,
    EnhancedTransformerEncoder,
    EnhancedMultiHeadAttentionWithRelPos,
    visualize_attention
)


class CrossRoundAttention(nn.Module):
    """
    跨回合注意力机制

    允许模型关注不同回合之间的信息，捕捉长期依赖关系。
    特别适合处理卡牌游戏中的历史信息和长期策略。
    """

    def __init__(
        self,
        hidden_dim: int,
        num_heads: int,
        dropout: float = 0.1,
        max_rounds: int = 10,
        output_attention: bool = False
    ):
        """
        初始化跨回合注意力层

        Args:
            hidden_dim (int): 隐藏层维度
            num_heads (int): 注意力头数量
            dropout (float, optional): Dropout比率. Defaults to 0.1.
            max_rounds (int, optional): 最大回合数. Defaults to 10.
            output_attention (bool, optional): 是否输出注意力权重. Defaults to False.
        """
        super(CrossRoundAttention, self).__init__()

        self.hidden_dim = hidden_dim
        self.num_heads = num_heads
        self.max_rounds = max_rounds
        self.output_attention = output_attention

        # 使用增强版多头注意力作为基础
        self.attention = EnhancedMultiHeadAttention(
            hidden_dim=hidden_dim,
            num_heads=num_heads,
            dropout=dropout,
            output_attention=output_attention
        )

        # 回合编码
        self.round_embeddings = nn.Parameter(torch.randn(max_rounds, hidden_dim))

        # 输出投影
        self.output_projection = nn.Linear(hidden_dim, hidden_dim)
        self.dropout = nn.Dropout(dropout)

    def forward(
        self,
        x: torch.Tensor,
        round_indices: torch.Tensor,
        mask: Optional[torch.Tensor] = None
    ) -> Union[torch.Tensor, Tuple[torch.Tensor, torch.Tensor]]:
        """
        前向传播

        Args:
            x (torch.Tensor): 输入张量，形状为 [batch_size, seq_len, hidden_dim]
            round_indices (torch.Tensor): 回合索引，形状为 [batch_size, seq_len]
            mask (Optional[torch.Tensor], optional): 注意力掉码. Defaults to None.

        Returns:
            Union[torch.Tensor, Tuple[torch.Tensor, torch.Tensor]]:
                如果output_attention为False，返回注意力输出
                如果output_attention为True，返回(注意力输出, 注意力权重)
        """
        batch_size, seq_len, _ = x.shape

        # 获取回合编码
        round_emb = self.round_embeddings[round_indices]  # [batch_size, seq_len, hidden_dim]

        # 将回合编码添加到输入中
        x = x + round_emb

        # 计算跨回合注意力
        if self.output_attention:
            output, attention_weights = self.attention(x, x, x, mask)
        else:
            output = self.attention(x, x, x, mask)

        # 输出投影
        output = self.output_projection(output)
        output = self.dropout(output)

        if self.output_attention:
            return output, attention_weights
        return output


class AdvancedTransformerEncoderLayer(nn.Module):
    """
    高级Transformer编码器层

    扩展增强版Transformer编码器层，添加了更多的注意力头、前置层正规化和深度残差连接。
    """

    def __init__(
        self,
        hidden_dim: int,
        num_heads: int,
        ff_dim: int = None,
        dropout: float = 0.1,
        activation: str = 'gelu',
        use_relative_pos: bool = True,
        max_seq_len: int = 5000,
        output_attention: bool = False,
        pre_norm: bool = True,
        use_cross_round: bool = False,
        max_rounds: int = 10
    ):
        """
        初始化高级Transformer编码器层

        Args:
            hidden_dim (int): 隐藏层维度
            num_heads (int): 注意力头数量
            ff_dim (int, optional): 前馈网络维度. Defaults to None (使用4*hidden_dim).
            dropout (float, optional): Dropout比率. Defaults to 0.1.
            activation (str, optional): 激活函数. Defaults to 'gelu'.
            use_relative_pos (bool, optional): 是否使用相对位置编码. Defaults to True.
            max_seq_len (int, optional): 最大序列长度. Defaults to 5000.
            output_attention (bool, optional): 是否输出注意力权重. Defaults to False.
            pre_norm (bool, optional): 是否使用前置层正规化. Defaults to True.
            use_cross_round (bool, optional): 是否使用跨回合注意力. Defaults to False.
            max_rounds (int, optional): 最大回合数. Defaults to 10.
        """
        super(AdvancedTransformerEncoderLayer, self).__init__()

        # 设置前馈网络维度
        if ff_dim is None:
            ff_dim = 4 * hidden_dim

        # 设置激活函数
        if activation == 'gelu':
            self.activation = F.gelu
        elif activation == 'relu':
            self.activation = F.relu
        else:
            raise ValueError(f"不支持的激活函数: {activation}")

        # 注意力层
        if use_relative_pos:
            self.attention = EnhancedMultiHeadAttentionWithRelPos(
                hidden_dim=hidden_dim,
                num_heads=num_heads,
                dropout=dropout,
                output_attention=output_attention,
                max_seq_len=max_seq_len
            )
        else:
            self.attention = EnhancedMultiHeadAttention(
                hidden_dim=hidden_dim,
                num_heads=num_heads,
                dropout=dropout,
                output_attention=output_attention
            )

        # 跨回合注意力层（可选）
        self.use_cross_round = use_cross_round
        if use_cross_round:
            self.cross_round_attention = CrossRoundAttention(
                hidden_dim=hidden_dim,
                num_heads=num_heads,
                dropout=dropout,
                max_rounds=max_rounds,
                output_attention=output_attention
            )

        # 前馈网络
        self.feed_forward = nn.Sequential(
            nn.Linear(hidden_dim, ff_dim),
            nn.Dropout(dropout),
            nn.GELU() if activation == 'gelu' else nn.ReLU(),
            nn.Linear(ff_dim, hidden_dim),
            nn.Dropout(dropout)
        )

        # 层正规化
        self.norm1 = nn.LayerNorm(hidden_dim)
        self.norm2 = nn.LayerNorm(hidden_dim)
        if use_cross_round:
            self.norm3 = nn.LayerNorm(hidden_dim)

        # 设置是否使用前置层正规化
        self.pre_norm = pre_norm
        self.output_attention = output_attention

    def forward(
        self,
        x: torch.Tensor,
        mask: Optional[torch.Tensor] = None,
        round_indices: Optional[torch.Tensor] = None
    ) -> Union[torch.Tensor, Tuple[torch.Tensor, torch.Tensor]]:
        """
        前向传播

        Args:
            x (torch.Tensor): 输入张量，形状为 [batch_size, seq_len, hidden_dim]
            mask (Optional[torch.Tensor], optional): 注意力掉码. Defaults to None.
            round_indices (Optional[torch.Tensor], optional): 回合索引，形状为 [batch_size, seq_len]. Defaults to None.

        Returns:
            Union[torch.Tensor, Tuple[torch.Tensor, torch.Tensor]]:
                如果output_attention为False，返回层输出
                如果output_attention为True，返回(层输出, 注意力权重)
        """
        # 收集注意力权重（如果需要）
        attentions = None

        # 注意力块
        if self.pre_norm:
            # 前置层正规化
            norm_x = self.norm1(x)
            if self.output_attention:
                attn_output, attn_weights = self.attention(norm_x, norm_x, norm_x, mask)
                attentions = attn_weights
            else:
                attn_output = self.attention(norm_x, norm_x, norm_x, mask)
            x = x + attn_output
        else:
            # 后置层正规化
            if self.output_attention:
                attn_output, attn_weights = self.attention(x, x, x, mask)
                attentions = attn_weights
            else:
                attn_output = self.attention(x, x, x, mask)
            x = self.norm1(x + attn_output)

        # 跨回合注意力块（如果启用）
        if self.use_cross_round and round_indices is not None:
            if self.pre_norm:
                # 前置层正规化
                norm_x = self.norm3(x)
                if self.output_attention:
                    cross_output, cross_weights = self.cross_round_attention(norm_x, round_indices, mask)
                    # 合并注意力权重
                    if attentions is not None:
                        attentions = (attentions + cross_weights) / 2
                    else:
                        attentions = cross_weights
                else:
                    cross_output = self.cross_round_attention(norm_x, round_indices, mask)
                x = x + cross_output
            else:
                # 后置层正规化
                if self.output_attention:
                    cross_output, cross_weights = self.cross_round_attention(x, round_indices, mask)
                    # 合并注意力权重
                    if attentions is not None:
                        attentions = (attentions + cross_weights) / 2
                    else:
                        attentions = cross_weights
                else:
                    cross_output = self.cross_round_attention(x, round_indices, mask)
                x = self.norm3(x + cross_output)

        # 前馈网络块
        if self.pre_norm:
            # 前置层正规化
            norm_x = self.norm2(x)
            ff_output = self.feed_forward(norm_x)
            x = x + ff_output
        else:
            # 后置层正规化
            ff_output = self.feed_forward(x)
            x = self.norm2(x + ff_output)

        if self.output_attention:
            return x, attentions
        return x


class AdvancedTransformerEncoder(nn.Module):
    """
    高级Transformer编码器

    包含多个高级Transformer编码器层和位置编码。
    支持更多的注意力头、更深的层数、相对位置编码和前置层正规化等优化。
    """

    def __init__(
        self,
        hidden_dim: int,
        num_layers: int,
        num_heads: int,
        ff_dim: int = None,
        dropout: float = 0.1,
        activation: str = 'gelu',
        pos_encoding_type: str = 'relative',
        use_relative_pos: bool = True,
        max_seq_len: int = 5000,
        output_attention: bool = False,
        pre_norm: bool = True,
        use_cross_round: bool = False,
        max_rounds: int = 10
    ):
        """
        初始化高级Transformer编码器

        Args:
            hidden_dim (int): 隐藏层维度
            num_layers (int): 编码器层数量
            num_heads (int): 注意力头数量
            ff_dim (int, optional): 前馈网络维度. Defaults to None (使用4*hidden_dim).
            dropout (float, optional): Dropout比率. Defaults to 0.1.
            activation (str, optional): 激活函数. Defaults to 'gelu'.
            pos_encoding_type (str, optional): 位置编码类型. Defaults to 'relative'.
            use_relative_pos (bool, optional): 是否使用相对位置编码. Defaults to True.
            max_seq_len (int, optional): 最大序列长度. Defaults to 5000.
            output_attention (bool, optional): 是否输出注意力权重. Defaults to False.
            pre_norm (bool, optional): 是否使用前置层正规化. Defaults to True.
            use_cross_round (bool, optional): 是否使用跨回合注意力. Defaults to False.
            max_rounds (int, optional): 最大回合数. Defaults to 10.
        """
        super(AdvancedTransformerEncoder, self).__init__()

        # 位置编码
        self.pos_encoding = EnhancedPositionalEncoding(
            d_model=hidden_dim,
            max_seq_len=max_seq_len,
            dropout=dropout,
            encoding_type=pos_encoding_type
        )

        # 编码器层
        self.layers = nn.ModuleList([
            AdvancedTransformerEncoderLayer(
                hidden_dim=hidden_dim,
                num_heads=num_heads,
                ff_dim=ff_dim,
                dropout=dropout,
                activation=activation,
                use_relative_pos=use_relative_pos,
                max_seq_len=max_seq_len,
                output_attention=output_attention,
                pre_norm=pre_norm,
                use_cross_round=use_cross_round,
                max_rounds=max_rounds
            ) for _ in range(num_layers)
        ])

        # 最终层正规化（如果使用前置层正规化）
        self.final_norm = nn.LayerNorm(hidden_dim) if pre_norm else None

        # 设置
        self.output_attention = output_attention
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        self.use_cross_round = use_cross_round

    def forward(
        self,
        x: torch.Tensor,
        mask: Optional[torch.Tensor] = None,
        round_indices: Optional[torch.Tensor] = None
    ) -> Union[torch.Tensor, Tuple[torch.Tensor, List[torch.Tensor]]]:
        """
        前向传播

        Args:
            x (torch.Tensor): 输入张量，形状为 [batch_size, seq_len, hidden_dim]
            mask (Optional[torch.Tensor], optional): 注意力掉码. Defaults to None.
            round_indices (Optional[torch.Tensor], optional): 回合索引，形状为 [batch_size, seq_len]. Defaults to None.

        Returns:
            Union[torch.Tensor, Tuple[torch.Tensor, List[torch.Tensor]]]:
                如果output_attention为False，返回编码器输出
                如果output_attention为True，返回(编码器输出, 所有层的注意力权重)
        """
        # 添加位置编码
        x = self.pos_encoding(x)

        # 收集注意力权重（如果需要）
        attentions = [] if self.output_attention else None

        # 通过所有编码器层
        for layer in self.layers:
            if self.use_cross_round and round_indices is not None:
                if self.output_attention:
                    x, attn = layer(x, mask, round_indices)
                    attentions.append(attn)
                else:
                    x = layer(x, mask, round_indices)
            else:
                if self.output_attention:
                    x, attn = layer(x, mask)
                    attentions.append(attn)
                else:
                    x = layer(x, mask)

        # 应用最终层正规化（如果使用前置层正规化）
        if self.final_norm is not None:
            x = self.final_norm(x)

        if self.output_attention:
            return x, attentions
        return x


class AdvancedRelativePositionalEncoding(nn.Module):
    """
    高级相对位置编码

    实现更高级的相对位置编码，支持时间衰减和多尺度编码。
    特别适合处理卡牌游戏中的长序列和时间依赖关系。
    """

    def __init__(
        self,
        d_model: int,
        max_seq_len: int = 5000,
        num_dims: int = 4,
        time_decay: float = 0.01,
        dropout: float = 0.1
    ):
        """
        初始化高级相对位置编码

        Args:
            d_model (int): 模型维度
            max_seq_len (int, optional): 最大序列长度. Defaults to 5000.
            num_dims (int, optional): 编码维度数. Defaults to 4.
            time_decay (float, optional): 时间衰减因子. Defaults to 0.01.
            dropout (float, optional): Dropout比率. Defaults to 0.1.
        """
        super(AdvancedRelativePositionalEncoding, self).__init__()

        self.d_model = d_model
        self.max_seq_len = max_seq_len
        self.num_dims = num_dims
        self.time_decay = time_decay

        # 创建相对位置编码矩阵
        self.rel_pos_bias = nn.Parameter(torch.randn(num_dims, max_seq_len * 2 - 1))

        # 创建时间衰减矩阵
        time_decay_matrix = torch.exp(-time_decay * torch.arange(max_seq_len).float())
        self.register_buffer('time_decay_matrix', time_decay_matrix)

        # 投影层
        self.projection = nn.Linear(d_model, d_model)
        self.dropout = nn.Dropout(dropout)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播

        Args:
            x (torch.Tensor): 输入张量，形状为 [batch_size, seq_len, d_model]

        Returns:
            torch.Tensor: 添加位置编码后的张量
        """
        batch_size, seq_len, _ = x.shape

        # 生成位置索引
        positions = torch.arange(seq_len, device=x.device).unsqueeze(0).repeat(batch_size, 1)

        # 计算相对位置编码
        rel_pos_encoding = self._get_rel_pos_encoding(seq_len)

        # 应用时间衰减
        time_decay = self.time_decay_matrix[:seq_len].unsqueeze(0).unsqueeze(-1)
        rel_pos_encoding = rel_pos_encoding * time_decay

        # 将相对位置编码投影到模型维度
        rel_pos_encoding = rel_pos_encoding.view(seq_len, -1)
        rel_pos_encoding = self.projection(rel_pos_encoding)

        # 将位置编码添加到输入中
        rel_pos_encoding = rel_pos_encoding.unsqueeze(0).repeat(batch_size, 1, 1)
        x = x + rel_pos_encoding

        return self.dropout(x)

    def _get_rel_pos_encoding(self, seq_len: int) -> torch.Tensor:
        """
        获取相对位置编码

        Args:
            seq_len (int): 序列长度

        Returns:
            torch.Tensor: 相对位置编码张量
        """
        # 计算相对位置索引
        center = self.max_seq_len - 1
        pos_indices = torch.arange(seq_len, device=self.rel_pos_bias.device)
        rel_indices = pos_indices.unsqueeze(1) - pos_indices.unsqueeze(0) + center

        # 获取相对位置编码
        rel_pos_encoding = self.rel_pos_bias[:, rel_indices]  # [num_dims, seq_len, seq_len]

        # 转置为 [seq_len, seq_len, num_dims]
        rel_pos_encoding = rel_pos_encoding.permute(1, 2, 0)

        return rel_pos_encoding


def visualize_advanced_attention(
    attention_weights: List[torch.Tensor],
    layer_idx: int = 0,
    head_idx: int = 0,
    batch_idx: int = 0,
    labels: List[str] = None,
    title: str = None,
    figsize: Tuple[int, int] = (10, 8),
    cmap: str = 'viridis',
    save_path: str = None
):
    """
    可视化高级Transformer的注意力权重

    Args:
        attention_weights (List[torch.Tensor]): 注意力权重列表，每个元素对应一个层
        layer_idx (int, optional): 要可视化的层索引. Defaults to 0.
        head_idx (int, optional): 要可视化的注意力头索引. Defaults to 0.
        batch_idx (int, optional): 要可视化的批次索引. Defaults to 0.
        labels (List[str], optional): 序列标签. Defaults to None.
        title (str, optional): 图表标题. Defaults to None.
        figsize (Tuple[int, int], optional): 图表大小. Defaults to (10, 8).
        cmap (str, optional): 色彩映射. Defaults to 'viridis'.
        save_path (str, optional): 保存路径. Defaults to None.
    """
    # 确保必要的包已导入
    import matplotlib.pyplot as plt
    import seaborn as sns
    import numpy as np

    # 获取指定层的注意力权重
    if layer_idx >= len(attention_weights):
        raise ValueError(f"层索引 {layer_idx} 超出范围，只有 {len(attention_weights)} 层")

    # 获取指定层的注意力权重
    attn = attention_weights[layer_idx]

    # 如果是张量，转换为NumPy数组
    if isinstance(attn, torch.Tensor):
        attn = attn.detach().cpu().numpy()

    # 获取指定批次和注意力头的权重
    if attn.ndim == 4:  # [batch_size, num_heads, seq_len, seq_len]
        if batch_idx >= attn.shape[0]:
            raise ValueError(f"批次索引 {batch_idx} 超出范围，只有 {attn.shape[0]} 个批次")
        if head_idx >= attn.shape[1]:
            raise ValueError(f"注意力头索引 {head_idx} 超出范围，只有 {attn.shape[1]} 个注意力头")
        attn_matrix = attn[batch_idx, head_idx]
    else:
        attn_matrix = attn

    # 创建图表
    plt.figure(figsize=figsize)

    # 设置标签
    if labels is None:
        labels = [str(i) for i in range(attn_matrix.shape[0])]

    # 绘制热力图
    ax = sns.heatmap(
        attn_matrix,
        annot=False,
        cmap=cmap,
        xticklabels=labels,
        yticklabels=labels
    )

    # 设置标题
    if title is None:
        title = f"Layer {layer_idx}, Head {head_idx} Attention Weights"
    plt.title(title)

    plt.xlabel("Target Position")
    plt.ylabel("Source Position")

    # 保存图表
    if save_path is not None:
        plt.savefig(save_path, bbox_inches='tight', dpi=300)

    plt.show()

    return ax


def test_advanced_transformer():
    """
    测试高级Transformer架构

    这个函数用于测试高级Transformer架构的性能，包括跨回合注意力、相对位置编码和前置层正规化等特性。
    """
    import torch
    import numpy as np
    import time

    # 设置随机种子以确保可重现性
    torch.manual_seed(42)
    np.random.seed(42)

    # 测试参数
    batch_size = 8
    seq_len = 100
    hidden_dim = 64
    num_heads = 8
    num_layers = 4

    # 创建测试数据
    x = torch.randn(batch_size, seq_len, hidden_dim)
    mask = torch.ones(batch_size, seq_len, seq_len).triu(diagonal=1).bool()
    round_indices = torch.randint(0, 10, (batch_size, seq_len))

    # 创建高级Transformer编码器
    encoder = AdvancedTransformerEncoder(
        hidden_dim=hidden_dim,
        num_layers=num_layers,
        num_heads=num_heads,
        ff_dim=hidden_dim * 4,
        dropout=0.1,
        activation='gelu',
        pos_encoding_type='relative',
        use_relative_pos=True,
        max_seq_len=1000,
        output_attention=True,
        pre_norm=True,
        use_cross_round=True,
        max_rounds=10
    )

    # 测试前向传播
    print("\n=== 测试高级Transformer编码器 ===")
    start_time = time.time()
    output, attentions = encoder(x, mask, round_indices)
    end_time = time.time()

    print(f"\u8f93入形状: {x.shape}")
    print(f"\u8f93出形状: {output.shape}")
    print(f"\u6ce8意力权重形状: {len(attentions)} 层, 每层 {attentions[0].shape}")
    print(f"\u524d向传播时间: {end_time - start_time:.4f} 秒")

    # 测试高级相对位置编码
    print("\n=== 测试高级相对位置编码 ===")
    rel_pos_encoding = AdvancedRelativePositionalEncoding(
        d_model=hidden_dim,
        max_seq_len=1000,
        num_dims=4,
        time_decay=0.01,
        dropout=0.1
    )

    start_time = time.time()
    pos_output = rel_pos_encoding(x)
    end_time = time.time()

    print(f"\u8f93入形状: {x.shape}")
    print(f"\u8f93出形状: {pos_output.shape}")
    print(f"\u524d向传播时间: {end_time - start_time:.4f} 秒")

    # 测试跨回合注意力
    print("\n=== 测试跨回合注意力 ===")
    cross_round_attn = CrossRoundAttention(
        hidden_dim=hidden_dim,
        num_heads=num_heads,
        dropout=0.1,
        max_rounds=10,
        output_attention=True
    )

    start_time = time.time()
    cross_output, cross_attn = cross_round_attn(x, round_indices, mask)
    end_time = time.time()

    print(f"\u8f93入形状: {x.shape}")
    print(f"\u8f93出形状: {cross_output.shape}")
    print(f"\u6ce8意力权重形状: {cross_attn.shape}")
    print(f"\u524d向传播时间: {end_time - start_time:.4f} 秒")

    # 可视化注意力权重
    try:
        import matplotlib.pyplot as plt
        import seaborn as sns

        print("\n=== 可视化注意力权重 ===")
        visualize_advanced_attention(
            attention_weights=attentions,
            layer_idx=0,
            head_idx=0,
            batch_idx=0,
            title="Advanced Transformer Attention Weights"
        )
    except ImportError:
        print("\n无法可视化注意力权重，缺少必要的包")

    return {
        'encoder_output_shape': output.shape,
        'attention_shapes': [attn.shape for attn in attentions],
        'pos_encoding_output_shape': pos_output.shape,
        'cross_round_output_shape': cross_output.shape,
        'cross_round_attention_shape': cross_attn.shape
    }


if __name__ == "__main__":
    # 如果直接运行这个文件，则执行测试
    test_advanced_transformer()