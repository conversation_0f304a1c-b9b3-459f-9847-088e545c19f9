#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
重构后系统功能验证测试

验证重构后的斗地主AI训练系统是否能够正常工作，
包括训练脚本、决策系统、模型管理等核心功能。
"""

import pytest
import sys
import os
import tempfile
import yaml
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))


class TestRefactoredTrainingSystem:
    """重构后训练系统功能测试"""

    def setup_method(self):
        """测试前置设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_path = os.path.join(self.temp_dir, "test_config.yaml")
        
        # 创建测试配置文件
        self.test_config = {
            'game': 'doudizhu',
            'algorithm': 'efficient_zero',
            'device': 'cpu',
            'training': {
                'epochs': 2,
                'episodes_per_epoch': 2,
                'updates_per_epoch': 2,
                'batch_size': 32,
                'learning_rate': 0.001,
                'save_interval': 1,
                'save_dir': os.path.join(self.temp_dir, 'models')
            },
            'model': {
                'hidden_size': 64,
                'num_layers': 2
            },
            'mcts': {
                'num_simulations': 10
            }
        }
        
        with open(self.config_path, 'w', encoding='utf-8') as f:
            yaml.dump(self.test_config, f)

    def teardown_method(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_training_system_initialization(self):
        """测试训练系统初始化"""
        from cardgame_ai.zhuchengxu.optimized_training_integrated import OptimizedTrainingSystem
        
        # 测试正常初始化
        system = OptimizedTrainingSystem()
        assert system.project_root is not None
        assert system.logger is None  # 初始化时未设置
        
        # 测试日志设置
        log_dir = os.path.join(self.temp_dir, "logs")
        system.setup_logging("INFO", log_dir)
        assert system.logger is not None
        assert os.path.exists(log_dir)

    def test_config_loading_success(self):
        """测试配置文件成功加载"""
        from cardgame_ai.zhuchengxu.optimized_training_integrated import OptimizedTrainingSystem
        
        system = OptimizedTrainingSystem()
        system.setup_logging()
        
        config = system.load_config(self.config_path)
        
        assert config['game'] == 'doudizhu'
        assert config['algorithm'] == 'efficient_zero'
        assert config['training']['epochs'] == 2

    def test_config_loading_failure_raises_exception(self):
        """测试配置文件加载失败时抛出异常"""
        from cardgame_ai.zhuchengxu.optimized_training_integrated import OptimizedTrainingSystem
        
        system = OptimizedTrainingSystem()
        system.setup_logging()
        
        # 测试文件不存在
        with pytest.raises(FileNotFoundError):
            system.load_config("non_existent_config.yaml")
        
        # 测试不支持的格式
        json_config = os.path.join(self.temp_dir, "config.json")
        with open(json_config, 'w') as f:
            f.write('{"test": "value"}')
        
        with pytest.raises(ValueError) as exc_info:
            system.load_config(json_config)
        assert "只支持YAML格式" in str(exc_info.value)

    def test_device_detection(self):
        """测试设备检测功能"""
        from cardgame_ai.zhuchengxu.optimized_training_integrated import OptimizedTrainingSystem
        
        system = OptimizedTrainingSystem()
        system.setup_logging()
        
        # 测试设备检测
        device = system.detect_device()
        assert device in ['cpu', 'cuda:0']  # 根据环境可能是CPU或GPU

    @patch('cardgame_ai.zhuchengxu.optimized_training_integrated.train_efficient_zero')
    def test_training_execution_success(self, mock_train):
        """测试训练执行成功"""
        from cardgame_ai.zhuchengxu.optimized_training_integrated import OptimizedTrainingSystem
        
        # 模拟训练成功
        mock_train.return_value = 0
        
        system = OptimizedTrainingSystem()
        system.setup_logging()
        
        config = system.load_config(self.config_path)
        result = system._execute_training_loop(config, 'cpu', False)
        
        assert result is True
        mock_train.assert_called_once()

    @patch('cardgame_ai.zhuchengxu.optimized_training_integrated.train_efficient_zero')
    def test_training_execution_failure_raises_exception(self, mock_train):
        """测试训练执行失败时抛出异常"""
        from cardgame_ai.zhuchengxu.optimized_training_integrated import OptimizedTrainingSystem
        
        # 模拟训练失败
        mock_train.return_value = 1
        
        system = OptimizedTrainingSystem()
        system.setup_logging()
        
        config = system.load_config(self.config_path)
        
        with pytest.raises(RuntimeError) as exc_info:
            system._execute_training_loop(config, 'cpu', False)
        
        assert "训练失败，返回码: 1" in str(exc_info.value)

    @patch('cardgame_ai.zhuchengxu.optimized_training_integrated.train_efficient_zero')
    def test_training_exception_propagation(self, mock_train):
        """测试训练异常传播"""
        from cardgame_ai.zhuchengxu.optimized_training_integrated import OptimizedTrainingSystem
        
        # 模拟训练抛出异常
        mock_train.side_effect = Exception("训练模块内部错误")
        
        system = OptimizedTrainingSystem()
        system.setup_logging()
        
        config = system.load_config(self.config_path)
        
        with pytest.raises(RuntimeError) as exc_info:
            system._execute_training_loop(config, 'cpu', False)
        
        assert "训练失败，无法继续" in str(exc_info.value)
        assert "训练模块内部错误" in str(exc_info.value)


class TestRefactoredDecisionSystem:
    """重构后决策系统功能测试"""

    def setup_method(self):
        """测试前置设置"""
        self.mock_decision_system = Mock()
        self.mock_component_manager = Mock()

    def test_decision_system_success(self):
        """测试决策系统正常工作"""
        with patch('cardgame_ai.core.optimized_integrated_system.HybridDecisionSystem'):
            with patch('cardgame_ai.core.optimized_integrated_system.ComponentManager'):
                from cardgame_ai.core.optimized_integrated_system import OptimizedIntegratedSystem
                
                system = OptimizedIntegratedSystem(['neural_network'])
                system.decision_system = self.mock_decision_system
                
                mock_state = Mock()
                mock_action = Mock()
                self.mock_decision_system.decide.return_value = mock_action
                
                result = system.decide(mock_state)
                
                assert result == mock_action
                self.mock_decision_system.decide.assert_called_once_with(mock_state)

    def test_decision_system_failure_propagation(self):
        """测试决策系统失败时异常传播"""
        with patch('cardgame_ai.core.optimized_integrated_system.HybridDecisionSystem'):
            with patch('cardgame_ai.core.optimized_integrated_system.ComponentManager'):
                from cardgame_ai.core.optimized_integrated_system import OptimizedIntegratedSystem
                
                system = OptimizedIntegratedSystem(['neural_network'])
                system.decision_system = self.mock_decision_system
                
                mock_state = Mock()
                self.mock_decision_system.decide.side_effect = Exception("决策失败")
                
                with pytest.raises(RuntimeError) as exc_info:
                    system.decide(mock_state)
                
                assert "决策系统失败，无法继续" in str(exc_info.value)
                assert "决策失败" in str(exc_info.value)

    def test_no_fallback_mechanisms(self):
        """测试确保没有备用机制"""
        with patch('cardgame_ai.core.optimized_integrated_system.HybridDecisionSystem'):
            with patch('cardgame_ai.core.optimized_integrated_system.ComponentManager'):
                from cardgame_ai.core.optimized_integrated_system import OptimizedIntegratedSystem
                
                system = OptimizedIntegratedSystem(['neural_network'])
                
                # 确保没有备用决策方法
                assert not hasattr(system, '_fallback_decision')
                assert not hasattr(system, '_backup_strategy')
                assert not hasattr(system, '_emergency_action')


class TestSystemIntegration:
    """系统集成测试"""

    def setup_method(self):
        """测试前置设置"""
        self.temp_dir = tempfile.mkdtemp()

    def teardown_method(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_end_to_end_fail_fast_behavior(self):
        """测试端到端的fail-fast行为"""
        # 创建无效配置文件
        invalid_config = os.path.join(self.temp_dir, "invalid.yaml")
        with open(invalid_config, 'w') as f:
            f.write("invalid: yaml: content: [")
        
        from cardgame_ai.zhuchengxu.optimized_training_integrated import OptimizedTrainingSystem
        
        system = OptimizedTrainingSystem()
        system.setup_logging()
        
        # 验证配置加载失败时立即抛出异常
        with pytest.raises(RuntimeError):
            system.load_config(invalid_config)

    def test_no_silent_failures_in_pipeline(self):
        """测试管道中没有静默失败"""
        from cardgame_ai.zhuchengxu.optimized_training_integrated import OptimizedTrainingSystem
        
        system = OptimizedTrainingSystem()
        system.setup_logging()
        
        # 测试各种失败场景都会抛出异常
        with pytest.raises(FileNotFoundError):
            system.load_config("non_existent.yaml")
        
        with pytest.raises(ValueError):
            system.load_config(__file__)  # 使用非YAML文件

    def test_error_message_quality(self):
        """测试错误信息质量"""
        from cardgame_ai.zhuchengxu.optimized_training_integrated import OptimizedTrainingSystem
        
        system = OptimizedTrainingSystem()
        system.setup_logging()
        
        try:
            system.load_config("non_existent.yaml")
        except FileNotFoundError as e:
            # 验证错误信息包含有用信息
            assert "配置文件不存在" in str(e)
            assert "无法继续训练" in str(e)
            assert "non_existent.yaml" in str(e)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
