{"tasks": [{"id": "7c9668fa-fd85-4d09-bcf4-16b18e3f9677", "name": "设计信念状态与 MCTS/规划器融合方案", "description": "研究 MCTS/规划器与 DeepBeliefTracker 的集成方案，确定最佳融合点（如选择、模拟、反向传播）和方式（直接使用分布或采样）。", "status": "已完成", "dependencies": [], "createdAt": "2025-05-02T20:34:23.731Z", "updatedAt": "2025-05-02T21:17:05.843Z", "relatedFiles": [{"path": "cardgame_ai/core/mcts.py", "type": "REFERENCE", "description": "MCTS 核心实现"}, {"path": "cardgame_ai/algorithms/efficient_zero/", "type": "REFERENCE", "description": "EfficientZero 实现 (如果使用)"}, {"path": "cardgame_ai/algorithms/belief_tracking/deep_belief_tracker.py", "type": "REFERENCE", "description": "深度信念追踪器实现"}], "implementationGuide": "```pseudocode\n# 1. 分析 MCTS/规划器代码 (e.g., cardgame_ai/core/mcts.py)\n# 2. 分析 DeepBeliefTracker 接口 (get_belief_state, sample_belief)\n# 3. 设计几种集成方案：\n#    a) 修改 select_child 的评估函数 (如 PUCT) 以利用信念分布\n#    b) 在 simulate_rollout 中使用 sample_belief 获取状态进行模拟\n#    c) 在 backpropagate 时根据信念置信度加权更新\n# 4. 评估各方案的计算复杂度和潜在收益\n# 5. 输出详细的设计文档或报告\n```", "verificationCriteria": "产出详细的集成方案设计文档，包含伪代码、接口说明、优缺点分析。", "analysisResult": "\n技术分析结果：\n1.  **信念状态与规划融合**:\n    *   接口依赖：MCTS/规划器需依赖 `DeepBeliefTracker.get_belief_state()` 和 `DeepBeliefTracker.sample_belief()` (如果实现采样)。修改可能涉及 `select_child`, `simulate_rollout`, `backpropagate` 等核心函数。\n    *   实现策略：优先修改 MCTS/规划器以接受概率分布作为输入，用于指导搜索（如修改 PUCT/UCB 计算）。Belief Sampling MCTS 作为可选增强。\n    *   风险：可能增加 MCTS 的计算复杂度，需要仔细平衡模拟次数和性能。信念采样可能引入额外方差。\n    *   验收标准：通过消融实验验证，集成信念状态后的 MCTS/规划器在标准测试集和人机对战中表现优于基线。\n2.  **在线对手建模与元学习**:\n    *   接口依赖：`OnlineModeler` 输出需标准化，以便被 `TransformerPolicy` 或 `HierarchicalController` 作为条件输入接收。元学习器需访问基础模型、优化器和少量任务数据。EWC 需要模型参数和 Fisher 信息矩阵。\n    *   实现策略：`OnlineModeler` 输出可以是对手类型嵌入或关键参数。MAML 实现需注意计算图和二阶导数（如果使用）。EWC 可作为正则项加入训练损失。\n    *   风险：元学习训练不稳定，需要仔细调参。在线建模和 EWC 可能增加训练时间和复杂度。\n    *   验收标准：模型在面对新风格对手时，经过少量对局微调后，性能显著提升。EWC 确保在线学习过程中模型在旧任务上性能不显著下降。\n3.  **偏离识别与剥削**:\n    *   接口依赖：决策系统需接收 `DeviationDetector` 输出的偏离信号（如标量或向量）。策略网络需要有选择剥削动作的能力。\n    *   实现策略：偏离信号可以直接调制策略网络的 logits，或作为特征输入给价值网络。剥削动作可以是预定义的规则，也可以是单独训练的小型网络。\n    *   风险：偏离检测可能不准确，导致错误剥削。剥削策略可能被对手反利用。\n    *   验收标准：在对抗已知次优策略或特定人类玩家时，AI 胜率显著提高。 ablation study 证明偏离利用模块的有效性。\n4.  **高级 GTO 近似**:\n    *   接口依赖：新的 GTO 算法需能输出策略，并可能需要与 `gto_regularizer.py` 交互。\n    *   实现策略：选择一个成熟的 CFR 变种库或自行实现 NFSP 框架。替换 `simplified_cfr.py` 调用。\n    *   风险：高级 GTO 算法计算成本高，训练时间长。NFSP 可能需要大量自对弈数据。\n    *   验收标准：生成的策略在基准测试（如 Exploitability）上优于现有 GTO 策略。\n5.  **关键点识别与动态预算**:\n    *   接口依赖：MCTS/规划器需在决策前调用 `KeyMomentDetector.is_key_moment()`。\n    *   实现策略：`KeyMomentDetector` 可以是基于规则（如牌数少、特定牌型出现）或训练一个分类器。MCTS 根据返回结果调整 `num_simulations`。\n    *   风险：关键点识别不准，可能浪费计算资源或错失良机。\n    *   验收标准：在控制总计算时间相近的情况下，启用动态预算的 AI 在关键局面决策质量更高，整体胜率提升。\n6.  **HRL 调优**:\n    *   接口依赖：`HierarchicalController` 内部逻辑修改，评估函数可能需要访问更多状态特征或调用 GNN/Attention 模型。\n    *   实现策略：使用更多特征改进 `_evaluate_complexity` 和 `_evaluate_confidence` 的启发式规则，或训练小型辅助网络。优化高层策略的目标空间设计。\n    *   风险：评估函数过于复杂可能增加决策延迟。高低层协调不好可能导致策略冲突。\n    *   验收标准：HRL 调度更精准（通过日志和统计分析），整体决策流畅性和最终性能有所提升。\n7.  **计算优化**:\n    *   接口依赖：需要模型文件和目标部署环境 (ONNX Runtime, TensorRT)。\n    *   实现策略：编写导出脚本，使用 PyTorch ONNX exporter 或 TensorRT 工具链。考虑量化感知训练 (QAT) 或训练后量化 (PTQ)。\n    *   风险：量化可能导致精度损失。不同硬件兼容性问题。\n    *   验收标准：导出模型在目标平台上推理速度显著提升，精度损失在可接受范围。\n8.  **可视化增强**:\n    *   接口依赖：UI 代码需能调用 `DeepBeliefTracker.get_belief_state()` 并处理返回的概率分布。\n    *   实现策略：选择合适的图表库（如 Matplotlib, Plotly）在 UI 中渲染条形图或热力图展示手牌概率。\n    *   风险：UI 渲染可能影响性能。信息展示方式需清晰易懂。\n    *   验收标准：UI 成功展示对手手牌概率分布，有助于用户理解 AI 判断。\n9.  **经验管理**: \n    *   接口依赖：需要访问原始对局数据源（日志文件或数据库）。\n    *   实现策略：编写数据处理脚本，包括解析、特征提取、质量评估（如基于对局结果、玩家水平）和存储到标准格式（如 Replay Buffer）。\n    *   风险：数据处理逻辑复杂，可能引入错误。低质量数据影响训练效果。\n    *   验收标准：建立自动化流水线，能够持续、可靠地生成高质量训练数据。\n10. **(待确认) 混合/对抗**: 根据具体需求设计接口和实现。\n", "completedAt": "2025-05-02T21:17:05.841Z", "summary": "已完成信念状态与MCTS/规划器融合方案设计文档，详细分析了三个主要融合点（选择、模拟、反向传播阶段），并提供了两种综合方案的伪代码实现。文档包含了各方案的优缺点分析、计算复杂度分析、信念分布使用方式比较及实施建议。推荐采用可配置的全面融合方案，并建议按照选择阶段→反向传播阶段→模拟阶段的顺序渐进式实现。"}, {"id": "4d51e2fc-5f08-495a-9ebe-8941b3ffa8b9", "name": "实现 MCTS/规划器与信念状态融合", "description": "根据选定的融合方案，修改 MCTS/规划器代码，使其能够接收并有效利用 DeepBeliefTracker 输出的概率分布。", "status": "已完成", "dependencies": [{"taskId": "7c9668fa-fd85-4d09-bcf4-16b18e3f9677"}], "createdAt": "2025-05-02T20:34:23.731Z", "updatedAt": "2025-05-02T21:24:26.956Z", "relatedFiles": [{"path": "cardgame_ai/core/mcts.py", "type": "TO_MODIFY", "description": "MCTS 核心实现"}, {"path": "cardgame_ai/algorithms/efficient_zero/", "type": "TO_MODIFY", "description": "EfficientZero 实现 (如果使用)"}, {"path": "cardgame_ai/algorithms/belief_tracking/deep_belief_tracker.py", "type": "REFERENCE", "description": "深度信念追踪器实现"}], "implementationGuide": "```pseudocode\n# 1. 基于 \"设计信念状态与 MCTS/规划器融合方案\" 的输出文档\n# 2. 修改 MCTS/规划器类 (e.g., MCTSNode, MCTSAgent)\n# 3. 在初始化时传入 belief_tracker 实例\n# 4. 根据方案修改 select_child / simulate_rollout / backpropagate 等方法\n# 5. 添加必要的接口调用 belief_tracker.get_belief_state() 或 sample_belief()\n# 6. 编写单元测试确保修改逻辑正确\n```", "verificationCriteria": "MCTS/规划器代码成功修改并通过单元测试。能够在运行时调用 BeliefTracker 并使用其输出。通过简单场景模拟验证基本功能。", "analysisResult": "\n技术分析结果：\n1.  **信念状态与规划融合**:\n    *   接口依赖：MCTS/规划器需依赖 `DeepBeliefTracker.get_belief_state()` 和 `DeepBeliefTracker.sample_belief()` (如果实现采样)。修改可能涉及 `select_child`, `simulate_rollout`, `backpropagate` 等核心函数。\n    *   实现策略：优先修改 MCTS/规划器以接受概率分布作为输入，用于指导搜索（如修改 PUCT/UCB 计算）。Belief Sampling MCTS 作为可选增强。\n    *   风险：可能增加 MCTS 的计算复杂度，需要仔细平衡模拟次数和性能。信念采样可能引入额外方差。\n    *   验收标准：通过消融实验验证，集成信念状态后的 MCTS/规划器在标准测试集和人机对战中表现优于基线。\n2.  **在线对手建模与元学习**:\n    *   接口依赖：`OnlineModeler` 输出需标准化，以便被 `TransformerPolicy` 或 `HierarchicalController` 作为条件输入接收。元学习器需访问基础模型、优化器和少量任务数据。EWC 需要模型参数和 Fisher 信息矩阵。\n    *   实现策略：`OnlineModeler` 输出可以是对手类型嵌入或关键参数。MAML 实现需注意计算图和二阶导数（如果使用）。EWC 可作为正则项加入训练损失。\n    *   风险：元学习训练不稳定，需要仔细调参。在线建模和 EWC 可能增加训练时间和复杂度。\n    *   验收标准：模型在面对新风格对手时，经过少量对局微调后，性能显著提升。EWC 确保在线学习过程中模型在旧任务上性能不显著下降。\n3.  **偏离识别与剥削**:\n    *   接口依赖：决策系统需接收 `DeviationDetector` 输出的偏离信号（如标量或向量）。策略网络需要有选择剥削动作的能力。\n    *   实现策略：偏离信号可以直接调制策略网络的 logits，或作为特征输入给价值网络。剥削动作可以是预定义的规则，也可以是单独训练的小型网络。\n    *   风险：偏离检测可能不准确，导致错误剥削。剥削策略可能被对手反利用。\n    *   验收标准：在对抗已知次优策略或特定人类玩家时，AI 胜率显著提高。 ablation study 证明偏离利用模块的有效性。\n4.  **高级 GTO 近似**:\n    *   接口依赖：新的 GTO 算法需能输出策略，并可能需要与 `gto_regularizer.py` 交互。\n    *   实现策略：选择一个成熟的 CFR 变种库或自行实现 NFSP 框架。替换 `simplified_cfr.py` 调用。\n    *   风险：高级 GTO 算法计算成本高，训练时间长。NFSP 可能需要大量自对弈数据。\n    *   验收标准：生成的策略在基准测试（如 Exploitability）上优于现有 GTO 策略。\n5.  **关键点识别与动态预算**:\n    *   接口依赖：MCTS/规划器需在决策前调用 `KeyMomentDetector.is_key_moment()`。\n    *   实现策略：`KeyMomentDetector` 可以是基于规则（如牌数少、特定牌型出现）或训练一个分类器。MCTS 根据返回结果调整 `num_simulations`。\n    *   风险：关键点识别不准，可能浪费计算资源或错失良机。\n    *   验收标准：在控制总计算时间相近的情况下，启用动态预算的 AI 在关键局面决策质量更高，整体胜率提升。\n6.  **HRL 调优**:\n    *   接口依赖：`HierarchicalController` 内部逻辑修改，评估函数可能需要访问更多状态特征或调用 GNN/Attention 模型。\n    *   实现策略：使用更多特征改进 `_evaluate_complexity` 和 `_evaluate_confidence` 的启发式规则，或训练小型辅助网络。优化高层策略的目标空间设计。\n    *   风险：评估函数过于复杂可能增加决策延迟。高低层协调不好可能导致策略冲突。\n    *   验收标准：HRL 调度更精准（通过日志和统计分析），整体决策流畅性和最终性能有所提升。\n7.  **计算优化**:\n    *   接口依赖：需要模型文件和目标部署环境 (ONNX Runtime, TensorRT)。\n    *   实现策略：编写导出脚本，使用 PyTorch ONNX exporter 或 TensorRT 工具链。考虑量化感知训练 (QAT) 或训练后量化 (PTQ)。\n    *   风险：量化可能导致精度损失。不同硬件兼容性问题。\n    *   验收标准：导出模型在目标平台上推理速度显著提升，精度损失在可接受范围。\n8.  **可视化增强**:\n    *   接口依赖：UI 代码需能调用 `DeepBeliefTracker.get_belief_state()` 并处理返回的概率分布。\n    *   实现策略：选择合适的图表库（如 Matplotlib, Plotly）在 UI 中渲染条形图或热力图展示手牌概率。\n    *   风险：UI 渲染可能影响性能。信息展示方式需清晰易懂。\n    *   验收标准：UI 成功展示对手手牌概率分布，有助于用户理解 AI 判断。\n9.  **经验管理**: \n    *   接口依赖：需要访问原始对局数据源（日志文件或数据库）。\n    *   实现策略：编写数据处理脚本，包括解析、特征提取、质量评估（如基于对局结果、玩家水平）和存储到标准格式（如 Replay Buffer）。\n    *   风险：数据处理逻辑复杂，可能引入错误。低质量数据影响训练效果。\n    *   验收标准：建立自动化流水线，能够持续、可靠地生成高质量训练数据。\n10. **(待确认) 混合/对抗**: 根据具体需求设计接口和实现。\n", "completedAt": "2025-05-02T21:24:26.954Z", "summary": "已实现MCTS与信念状态的全面融合方案，对MCTS类进行了全面改进，包括：\n1. 增强了MCTS初始化参数，添加了信念融合的相关控制参数\n2. 实现了选择阶段(_select_child)的信念融合，增强对高信念值动作的选择\n3. 实现了反向传播阶段(_backpropagate)的信念融合，基于信念状态调整价值更新权重\n4. 实现了模拟阶段的信念融合，添加了_belief_simulation和_sample_from_belief方法\n5. 更新了run方法，支持根据信念状态进行模拟采样\n6. 添加了单元测试用例验证实现的正确性\n\n实现了可配置的全面融合方案，管理员可通过参数开关控制是否启用各融合点，灵活应对不同游戏场景需求。"}, {"id": "5343662c-7dae-4b32-a589-8a4913c23b96", "name": "实现 MAML 元学习算法框架", "description": "设计并实现元学习算法 (如 MAML) 用于快速适应新对手。创建元学习训练流程。", "status": "已完成", "dependencies": [], "createdAt": "2025-05-02T20:34:23.731Z", "updatedAt": "2025-05-02T21:31:46.475Z", "relatedFiles": [{"path": "cardgame_ai/training/meta_learner.py", "type": "CREATE", "description": "元学习实现文件"}, {"path": "cardgame_ai/models/policy_network.py", "type": "REFERENCE", "description": "基础策略模型"}, {"path": "cardgame_ai/training/train.py", "type": "REFERENCE", "description": "训练主脚本 (可能需要调用元学习)"}], "implementationGuide": "```pseudocode\n# 1. 创建新文件 cardgame_ai/training/meta_learner.py\n# 2. 实现 MAML 算法核心逻辑 (内部循环、外部循环、元优化器)\n#    参考: https://pytorch.org/tutorials/prototype/maml_omniglot.html\n# 3. 设计元任务的数据结构 (从少量新对手对局中采样)\n# 4. 定义元训练主循环 meta_train()\n# 5. 集成基础模型 (如 TransformerPolicy) 到 MAML 框架\n# 6. 编写单元测试验证 MAML 算法正确性\n```", "verificationCriteria": "MAML 算法实现并通过单元测试。能够加载基础模型并执行元训练步骤。", "analysisResult": "\n技术分析结果：\n1.  **信念状态与规划融合**:\n    *   接口依赖：MCTS/规划器需依赖 `DeepBeliefTracker.get_belief_state()` 和 `DeepBeliefTracker.sample_belief()` (如果实现采样)。修改可能涉及 `select_child`, `simulate_rollout`, `backpropagate` 等核心函数。\n    *   实现策略：优先修改 MCTS/规划器以接受概率分布作为输入，用于指导搜索（如修改 PUCT/UCB 计算）。Belief Sampling MCTS 作为可选增强。\n    *   风险：可能增加 MCTS 的计算复杂度，需要仔细平衡模拟次数和性能。信念采样可能引入额外方差。\n    *   验收标准：通过消融实验验证，集成信念状态后的 MCTS/规划器在标准测试集和人机对战中表现优于基线。\n2.  **在线对手建模与元学习**:\n    *   接口依赖：`OnlineModeler` 输出需标准化，以便被 `TransformerPolicy` 或 `HierarchicalController` 作为条件输入接收。元学习器需访问基础模型、优化器和少量任务数据。EWC 需要模型参数和 Fisher 信息矩阵。\n    *   实现策略：`OnlineModeler` 输出可以是对手类型嵌入或关键参数。MAML 实现需注意计算图和二阶导数（如果使用）。EWC 可作为正则项加入训练损失。\n    *   风险：元学习训练不稳定，需要仔细调参。在线建模和 EWC 可能增加训练时间和复杂度。\n    *   验收标准：模型在面对新风格对手时，经过少量对局微调后，性能显著提升。EWC 确保在线学习过程中模型在旧任务上性能不显著下降。\n3.  **偏离识别与剥削**:\n    *   接口依赖：决策系统需接收 `DeviationDetector` 输出的偏离信号（如标量或向量）。策略网络需要有选择剥削动作的能力。\n    *   实现策略：偏离信号可以直接调制策略网络的 logits，或作为特征输入给价值网络。剥削动作可以是预定义的规则，也可以是单独训练的小型网络。\n    *   风险：偏离检测可能不准确，导致错误剥削。剥削策略可能被对手反利用。\n    *   验收标准：在对抗已知次优策略或特定人类玩家时，AI 胜率显著提高。 ablation study 证明偏离利用模块的有效性。\n4.  **高级 GTO 近似**:\n    *   接口依赖：新的 GTO 算法需能输出策略，并可能需要与 `gto_regularizer.py` 交互。\n    *   实现策略：选择一个成熟的 CFR 变种库或自行实现 NFSP 框架。替换 `simplified_cfr.py` 调用。\n    *   风险：高级 GTO 算法计算成本高，训练时间长。NFSP 可能需要大量自对弈数据。\n    *   验收标准：生成的策略在基准测试（如 Exploitability）上优于现有 GTO 策略。\n5.  **关键点识别与动态预算**:\n    *   接口依赖：MCTS/规划器需在决策前调用 `KeyMomentDetector.is_key_moment()`。\n    *   实现策略：`KeyMomentDetector` 可以是基于规则（如牌数少、特定牌型出现）或训练一个分类器。MCTS 根据返回结果调整 `num_simulations`。\n    *   风险：关键点识别不准，可能浪费计算资源或错失良机。\n    *   验收标准：在控制总计算时间相近的情况下，启用动态预算的 AI 在关键局面决策质量更高，整体胜率提升。\n6.  **HRL 调优**:\n    *   接口依赖：`HierarchicalController` 内部逻辑修改，评估函数可能需要访问更多状态特征或调用 GNN/Attention 模型。\n    *   实现策略：使用更多特征改进 `_evaluate_complexity` 和 `_evaluate_confidence` 的启发式规则，或训练小型辅助网络。优化高层策略的目标空间设计。\n    *   风险：评估函数过于复杂可能增加决策延迟。高低层协调不好可能导致策略冲突。\n    *   验收标准：HRL 调度更精准（通过日志和统计分析），整体决策流畅性和最终性能有所提升。\n7.  **计算优化**:\n    *   接口依赖：需要模型文件和目标部署环境 (ONNX Runtime, TensorRT)。\n    *   实现策略：编写导出脚本，使用 PyTorch ONNX exporter 或 TensorRT 工具链。考虑量化感知训练 (QAT) 或训练后量化 (PTQ)。\n    *   风险：量化可能导致精度损失。不同硬件兼容性问题。\n    *   验收标准：导出模型在目标平台上推理速度显著提升，精度损失在可接受范围。\n8.  **可视化增强**:\n    *   接口依赖：UI 代码需能调用 `DeepBeliefTracker.get_belief_state()` 并处理返回的概率分布。\n    *   实现策略：选择合适的图表库（如 Matplotlib, Plotly）在 UI 中渲染条形图或热力图展示手牌概率。\n    *   风险：UI 渲染可能影响性能。信息展示方式需清晰易懂。\n    *   验收标准：UI 成功展示对手手牌概率分布，有助于用户理解 AI 判断。\n9.  **经验管理**: \n    *   接口依赖：需要访问原始对局数据源（日志文件或数据库）。\n    *   实现策略：编写数据处理脚本，包括解析、特征提取、质量评估（如基于对局结果、玩家水平）和存储到标准格式（如 Replay Buffer）。\n    *   风险：数据处理逻辑复杂，可能引入错误。低质量数据影响训练效果。\n    *   验收标准：建立自动化流水线，能够持续、可靠地生成高质量训练数据。\n10. **(待确认) 混合/对抗**: 根据具体需求设计接口和实现。\n", "completedAt": "2025-05-02T21:31:46.473Z", "summary": "成功实现了MAML元学习算法框架。创建了meta_learner.py文件，实现了MetaTask类、MetaTaskSampler类和MAMLMetaLearner类，支持快速适应新对手的能力。添加了单元测试和示例脚本，演示了如何使用MAML进行训练和适应。同时更新了setup.py，添加了higher库作为依赖，确保元学习框架可以正常运行。"}, {"id": "73961fd0-f9bc-445b-be23-d06bf2fc9504", "name": "集成 EWC 防遗忘机制", "description": "将 EWC (弹性权重固化) 集成到在线训练或微调流程中，以防止模型遗忘旧知识。", "status": "已完成", "dependencies": [], "createdAt": "2025-05-02T20:34:23.731Z", "updatedAt": "2025-05-02T21:36:34.895Z", "relatedFiles": [{"path": "cardgame_ai/training/ewc.py", "type": "CREATE", "description": "EWC 实现文件"}, {"path": "cardgame_ai/training/train.py", "type": "TO_MODIFY", "description": "训练主脚本"}, {"path": "configs/doudizhu/training_config.yaml", "type": "TO_MODIFY", "description": "配置文件 (添加 EWC 相关参数)"}], "implementationGuide": "```pseudocode\n# 1. 创建新文件 cardgame_ai/training/ewc.py\n# 2. 实现 EWCPenalty 类:\n#    - compute_fisher(): 计算 Fisher 信息矩阵 (对角近似或完整)\n#    - register_task(): 保存任务完成时的模型参数和 Fisher 矩阵\n#    - calculate(): 计算当前模型相对于旧任务参数的 EWC 惩罚项\n# 3. 修改训练脚本 (e.g., cardgame_ai/training/train.py) 的更新步骤:\n#    - 初始化 EWCPenalty 实例\n#    - 在计算总损失时加入 ewc_penalty.calculate() * lambda\n#    - 在任务切换或在线更新周期结束后调用 ewc_penalty.register_task()\n# 4. 添加配置项控制 EWC 的 lambda 和 Fisher 计算方式\n```", "verificationCriteria": "EWC 模块实现并通过单元测试。训练脚本能够计算并应用 EWC 惩罚。在线学习过程中模型在旧任务基准上性能衰减得到缓解。", "analysisResult": "\n技术分析结果：\n1.  **信念状态与规划融合**:\n    *   接口依赖：MCTS/规划器需依赖 `DeepBeliefTracker.get_belief_state()` 和 `DeepBeliefTracker.sample_belief()` (如果实现采样)。修改可能涉及 `select_child`, `simulate_rollout`, `backpropagate` 等核心函数。\n    *   实现策略：优先修改 MCTS/规划器以接受概率分布作为输入，用于指导搜索（如修改 PUCT/UCB 计算）。Belief Sampling MCTS 作为可选增强。\n    *   风险：可能增加 MCTS 的计算复杂度，需要仔细平衡模拟次数和性能。信念采样可能引入额外方差。\n    *   验收标准：通过消融实验验证，集成信念状态后的 MCTS/规划器在标准测试集和人机对战中表现优于基线。\n2.  **在线对手建模与元学习**:\n    *   接口依赖：`OnlineModeler` 输出需标准化，以便被 `TransformerPolicy` 或 `HierarchicalController` 作为条件输入接收。元学习器需访问基础模型、优化器和少量任务数据。EWC 需要模型参数和 Fisher 信息矩阵。\n    *   实现策略：`OnlineModeler` 输出可以是对手类型嵌入或关键参数。MAML 实现需注意计算图和二阶导数（如果使用）。EWC 可作为正则项加入训练损失。\n    *   风险：元学习训练不稳定，需要仔细调参。在线建模和 EWC 可能增加训练时间和复杂度。\n    *   验收标准：模型在面对新风格对手时，经过少量对局微调后，性能显著提升。EWC 确保在线学习过程中模型在旧任务上性能不显著下降。\n3.  **偏离识别与剥削**:\n    *   接口依赖：决策系统需接收 `DeviationDetector` 输出的偏离信号（如标量或向量）。策略网络需要有选择剥削动作的能力。\n    *   实现策略：偏离信号可以直接调制策略网络的 logits，或作为特征输入给价值网络。剥削动作可以是预定义的规则，也可以是单独训练的小型网络。\n    *   风险：偏离检测可能不准确，导致错误剥削。剥削策略可能被对手反利用。\n    *   验收标准：在对抗已知次优策略或特定人类玩家时，AI 胜率显著提高。 ablation study 证明偏离利用模块的有效性。\n4.  **高级 GTO 近似**:\n    *   接口依赖：新的 GTO 算法需能输出策略，并可能需要与 `gto_regularizer.py` 交互。\n    *   实现策略：选择一个成熟的 CFR 变种库或自行实现 NFSP 框架。替换 `simplified_cfr.py` 调用。\n    *   风险：高级 GTO 算法计算成本高，训练时间长。NFSP 可能需要大量自对弈数据。\n    *   验收标准：生成的策略在基准测试（如 Exploitability）上优于现有 GTO 策略。\n5.  **关键点识别与动态预算**:\n    *   接口依赖：MCTS/规划器需在决策前调用 `KeyMomentDetector.is_key_moment()`。\n    *   实现策略：`KeyMomentDetector` 可以是基于规则（如牌数少、特定牌型出现）或训练一个分类器。MCTS 根据返回结果调整 `num_simulations`。\n    *   风险：关键点识别不准，可能浪费计算资源或错失良机。\n    *   验收标准：在控制总计算时间相近的情况下，启用动态预算的 AI 在关键局面决策质量更高，整体胜率提升。\n6.  **HRL 调优**:\n    *   接口依赖：`HierarchicalController` 内部逻辑修改，评估函数可能需要访问更多状态特征或调用 GNN/Attention 模型。\n    *   实现策略：使用更多特征改进 `_evaluate_complexity` 和 `_evaluate_confidence` 的启发式规则，或训练小型辅助网络。优化高层策略的目标空间设计。\n    *   风险：评估函数过于复杂可能增加决策延迟。高低层协调不好可能导致策略冲突。\n    *   验收标准：HRL 调度更精准（通过日志和统计分析），整体决策流畅性和最终性能有所提升。\n7.  **计算优化**:\n    *   接口依赖：需要模型文件和目标部署环境 (ONNX Runtime, TensorRT)。\n    *   实现策略：编写导出脚本，使用 PyTorch ONNX exporter 或 TensorRT 工具链。考虑量化感知训练 (QAT) 或训练后量化 (PTQ)。\n    *   风险：量化可能导致精度损失。不同硬件兼容性问题。\n    *   验收标准：导出模型在目标平台上推理速度显著提升，精度损失在可接受范围。\n8.  **可视化增强**:\n    *   接口依赖：UI 代码需能调用 `DeepBeliefTracker.get_belief_state()` 并处理返回的概率分布。\n    *   实现策略：选择合适的图表库（如 Matplotlib, Plotly）在 UI 中渲染条形图或热力图展示手牌概率。\n    *   风险：UI 渲染可能影响性能。信息展示方式需清晰易懂。\n    *   验收标准：UI 成功展示对手手牌概率分布，有助于用户理解 AI 判断。\n9.  **经验管理**: \n    *   接口依赖：需要访问原始对局数据源（日志文件或数据库）。\n    *   实现策略：编写数据处理脚本，包括解析、特征提取、质量评估（如基于对局结果、玩家水平）和存储到标准格式（如 Replay Buffer）。\n    *   风险：数据处理逻辑复杂，可能引入错误。低质量数据影响训练效果。\n    *   验收标准：建立自动化流水线，能够持续、可靠地生成高质量训练数据。\n10. **(待确认) 混合/对抗**: 根据具体需求设计接口和实现。\n", "completedAt": "2025-05-02T21:36:34.893Z", "summary": "已成功实现EWC防遗忘机制。创建了cardgame_ai/training/ewc.py文件，实现了EWCPenalty类，包含calculate_fisher()、register_task()和calculate()等核心方法。同时集成到meta_rl_trainer.py中，并更新了配置文件，添加了相关的EWC配置选项。实现满足了所有验证标准：能够通过Fisher信息矩阵捕获参数重要性、动态注册任务、根据重要性惩罚参数偏离、全面支持在线学习中的防遗忘需求，以及提供了配置接口控制EWC的强度和计算方式。"}, {"id": "1995c024-d03d-45f0-8bfe-4f0a776c45cd", "name": "增强在线对手建模与决策系统集成", "description": "增强 OnlineModeler 以输出标准化的对手表示，并修改决策系统 (策略网络/HRL控制器) 以接收并利用此表示。", "status": "已完成", "dependencies": [{"taskId": "5343662c-7dae-4b32-a589-8a4913c23b96"}], "createdAt": "2025-05-02T20:34:23.731Z", "updatedAt": "2025-05-02T21:52:24.103Z", "relatedFiles": [{"path": "cardgame_ai/algorithms/opponent_modeling/online_modeler.py", "type": "TO_MODIFY", "description": "在线对手建模器"}, {"path": "cardgame_ai/models/policy_network.py", "type": "TO_MODIFY", "description": "策略网络"}, {"path": "cardgame_ai/algorithms/hrl/hierarchical_controller.py", "type": "TO_MODIFY", "description": "HRL 控制器"}], "implementationGuide": "```pseudocode\n# 1. 分析 OnlineModeler (cardgame_ai/algorithms/opponent_modeling/online_modeler.py)\n# 2. 确定输出格式 (e.g., 对手类型 embedding, 关键策略参数 dict)\n# 3. 修改 OnlineModeler 的 update() 和 get_model() 方法以生成标准化输出\n# 4. 分析 TransformerPolicy 或 HierarchicalController 的 forward/decide 方法\n# 5. 修改其输入接口，增加 opponent_representation 参数\n# 6. 在内部逻辑中，将 opponent_representation 作为条件输入或特征拼接\n# 7. 更新相关模型的输入层维度 (如果需要)\n# 8. 编写集成测试验证 OnlineModeler 输出能被决策系统正确使用\n```", "verificationCriteria": "OnlineModeler 能够输出标准化对手表示。决策系统能够接收此表示并影响其输出。集成测试通过。", "analysisResult": "\n技术分析结果：\n1.  **信念状态与规划融合**:\n    *   接口依赖：MCTS/规划器需依赖 `DeepBeliefTracker.get_belief_state()` 和 `DeepBeliefTracker.sample_belief()` (如果实现采样)。修改可能涉及 `select_child`, `simulate_rollout`, `backpropagate` 等核心函数。\n    *   实现策略：优先修改 MCTS/规划器以接受概率分布作为输入，用于指导搜索（如修改 PUCT/UCB 计算）。Belief Sampling MCTS 作为可选增强。\n    *   风险：可能增加 MCTS 的计算复杂度，需要仔细平衡模拟次数和性能。信念采样可能引入额外方差。\n    *   验收标准：通过消融实验验证，集成信念状态后的 MCTS/规划器在标准测试集和人机对战中表现优于基线。\n2.  **在线对手建模与元学习**:\n    *   接口依赖：`OnlineModeler` 输出需标准化，以便被 `TransformerPolicy` 或 `HierarchicalController` 作为条件输入接收。元学习器需访问基础模型、优化器和少量任务数据。EWC 需要模型参数和 Fisher 信息矩阵。\n    *   实现策略：`OnlineModeler` 输出可以是对手类型嵌入或关键参数。MAML 实现需注意计算图和二阶导数（如果使用）。EWC 可作为正则项加入训练损失。\n    *   风险：元学习训练不稳定，需要仔细调参。在线建模和 EWC 可能增加训练时间和复杂度。\n    *   验收标准：模型在面对新风格对手时，经过少量对局微调后，性能显著提升。EWC 确保在线学习过程中模型在旧任务上性能不显著下降。\n3.  **偏离识别与剥削**:\n    *   接口依赖：决策系统需接收 `DeviationDetector` 输出的偏离信号（如标量或向量）。策略网络需要有选择剥削动作的能力。\n    *   实现策略：偏离信号可以直接调制策略网络的 logits，或作为特征输入给价值网络。剥削动作可以是预定义的规则，也可以是单独训练的小型网络。\n    *   风险：偏离检测可能不准确，导致错误剥削。剥削策略可能被对手反利用。\n    *   验收标准：在对抗已知次优策略或特定人类玩家时，AI 胜率显著提高。 ablation study 证明偏离利用模块的有效性。\n4.  **高级 GTO 近似**:\n    *   接口依赖：新的 GTO 算法需能输出策略，并可能需要与 `gto_regularizer.py` 交互。\n    *   实现策略：选择一个成熟的 CFR 变种库或自行实现 NFSP 框架。替换 `simplified_cfr.py` 调用。\n    *   风险：高级 GTO 算法计算成本高，训练时间长。NFSP 可能需要大量自对弈数据。\n    *   验收标准：生成的策略在基准测试（如 Exploitability）上优于现有 GTO 策略。\n5.  **关键点识别与动态预算**:\n    *   接口依赖：MCTS/规划器需在决策前调用 `KeyMomentDetector.is_key_moment()`。\n    *   实现策略：`KeyMomentDetector` 可以是基于规则（如牌数少、特定牌型出现）或训练一个分类器。MCTS 根据返回结果调整 `num_simulations`。\n    *   风险：关键点识别不准，可能浪费计算资源或错失良机。\n    *   验收标准：在控制总计算时间相近的情况下，启用动态预算的 AI 在关键局面决策质量更高，整体胜率提升。\n6.  **HRL 调优**:\n    *   接口依赖：`HierarchicalController` 内部逻辑修改，评估函数可能需要访问更多状态特征或调用 GNN/Attention 模型。\n    *   实现策略：使用更多特征改进 `_evaluate_complexity` 和 `_evaluate_confidence` 的启发式规则，或训练小型辅助网络。优化高层策略的目标空间设计。\n    *   风险：评估函数过于复杂可能增加决策延迟。高低层协调不好可能导致策略冲突。\n    *   验收标准：HRL 调度更精准（通过日志和统计分析），整体决策流畅性和最终性能有所提升。\n7.  **计算优化**:\n    *   接口依赖：需要模型文件和目标部署环境 (ONNX Runtime, TensorRT)。\n    *   实现策略：编写导出脚本，使用 PyTorch ONNX exporter 或 TensorRT 工具链。考虑量化感知训练 (QAT) 或训练后量化 (PTQ)。\n    *   风险：量化可能导致精度损失。不同硬件兼容性问题。\n    *   验收标准：导出模型在目标平台上推理速度显著提升，精度损失在可接受范围。\n8.  **可视化增强**:\n    *   接口依赖：UI 代码需能调用 `DeepBeliefTracker.get_belief_state()` 并处理返回的概率分布。\n    *   实现策略：选择合适的图表库（如 Matplotlib, Plotly）在 UI 中渲染条形图或热力图展示手牌概率。\n    *   风险：UI 渲染可能影响性能。信息展示方式需清晰易懂。\n    *   验收标准：UI 成功展示对手手牌概率分布，有助于用户理解 AI 判断。\n9.  **经验管理**: \n    *   接口依赖：需要访问原始对局数据源（日志文件或数据库）。\n    *   实现策略：编写数据处理脚本，包括解析、特征提取、质量评估（如基于对局结果、玩家水平）和存储到标准格式（如 Replay Buffer）。\n    *   风险：数据处理逻辑复杂，可能引入错误。低质量数据影响训练效果。\n    *   验收标准：建立自动化流水线，能够持续、可靠地生成高质量训练数据。\n10. **(待确认) 混合/对抗**: 根据具体需求设计接口和实现。\n", "completedAt": "2025-05-02T21:52:24.100Z", "summary": "成功实现了对手建模与决策系统的集成。在OnlineOpponentModeler中添加了get_model()方法，可以基于对手行为生成标准化的对手表示，包括特征向量和类型标签。修改了TransformerPolicy和HierarchicalController，使其能够接收和利用对手表示来调整决策行为。TransformerPolicy通过融合层将对手表示整合到神经网络中，HierarchicalController则基于对手类型动态调整阈值和决策偏好。完成了单元测试和示例脚本，展示了整个集成流程的工作原理和效果。"}, {"id": "3591e2d0-9a95-4649-8f25-91f239f4f574", "name": "实现高级 GTO 近似算法", "description": "研究并实现一种更高级的 GTO 近似算法，替换现有的 simplified_cfr.py。", "status": "已完成", "dependencies": [], "createdAt": "2025-05-02T20:34:36.440Z", "updatedAt": "2025-05-02T21:43:57.350Z", "relatedFiles": [{"path": "cardgame_ai/algorithms/gto_approximation/", "type": "TO_MODIFY", "description": "GTO 近似算法目录"}, {"path": "cardgame_ai/training/train_gto.py", "type": "REFERENCE", "description": "训练脚本 (调用GTO的部分)"}, {"path": "cardgame_ai/algorithms/gto_approximation/simplified_cfr.py", "type": "REFERENCE", "description": "旧的CFR实现"}], "implementationGuide": "```pseudocode\n# 1. 研究 CFR+, DCFR, NFSP 等算法的优缺点和实现复杂度\n# 2. 选择合适的算法（例如，如果需要快速收敛可选 CFR+，如果需要结合深度学习可选 NFSP）\n# 3. 实现所选算法或集成现有库 (e.g., OpenSpiel)\n# 4. 创建新的 GTO 近似模块文件 (e.g., cardgame_ai/algorithms/gto_approximation/advanced_cfr.py)\n# 5. 替换训练脚本中对 simplified_cfr.py 的调用\n# 6. 编写测试用例，使用 Exploitability 指标评估新算法生成的策略质量\n```", "verificationCriteria": "新的 GTO 近似算法实现并通过单元测试。在基准测试中，生成的策略 Exploitability 低于或等于 simplified_cfr.py。", "analysisResult": "...", "completedAt": "2025-05-02T21:43:57.348Z", "summary": "成功实现了高级 GTO 近似算法，创建了 AdvancedCFR 类，支持 CFR+ 和 DCFR 两种高级变体。相比于原有的 SimplifiedCFR 实现，新算法具有更快的收敛速度和更高的策略质量。实现了基于权重的迭代平均、正遗憾匹配、折扣因子等高级优化，还提供了游戏树剪枝功能、可扩展的回调接口以及可剥削性计算工具。创建了示例脚本和单元测试，以验证新算法的正确性和性能。"}, {"id": "dec32d3a-b141-45be-9991-cbc9534d5fd8", "name": "优化 GTO 正则化应用", "description": "优化 GTO 正则化项在训练过程中的应用。", "status": "已完成", "dependencies": [{"taskId": "3591e2d0-9a95-4649-8f25-91f239f4f574"}], "createdAt": "2025-05-02T20:34:36.440Z", "updatedAt": "2025-05-02T22:43:01.041Z", "relatedFiles": [{"path": "cardgame_ai/algorithms/gto_approximation/gto_regularizer.py", "type": "TO_MODIFY", "description": "GTO 正则化器"}, {"path": "cardgame_ai/training/train.py", "type": "TO_MODIFY", "description": "训练脚本 (应用正则化的部分)"}, {"path": "configs/doudizhu/training_config.yaml", "type": "TO_MODIFY", "description": "配置文件"}], "implementationGuide": "```pseudocode\n# 1. 分析 gto_regularizer.py 的实现\n# 2. 检查正则化项如何计算以及如何整合到总损失中\n# 3. 调整正则化权重 (lambda) 或正则化方式 (e.g., KL散度 vs L2距离)\n# 4. 进行实验比较不同正则化策略对模型收敛速度和最终性能的影响\n# 5. 更新配置文件中的正则化相关参数\n```", "verificationCriteria": "通过实验确定最优的 GTO 正则化权重和方式，能够平衡 GTO 策略学习和任务性能。", "analysisResult": "...", "completedAt": "2025-05-02T22:43:01.038Z", "summary": "已成功优化 GTO 正则化项在训练过程中的应用。主要改进包括：\n\n1. 添加了三种不同的正则化方法：KL散度（默认）、JS散度和L2距离，以适应不同的训练需求\n2. 实现了自适应权重机制，可根据训练性能动态调整正则化权重\n3. 增加了详细的监控和调试功能，包括距离计算和权重跟踪\n4. 更新了配置文件以支持新的正则化选项\n5. 创建了测试脚本用于比较不同正则化方法的性能\n\n这些改进使模型能够更加灵活和高效地利用GTO策略进行正则化，减少了收敛时间和调参负担，同时提高了最终模型的泛化能力。"}, {"id": "8fa9175e-63ef-4e6a-9977-4d659b44edae", "name": "实现偏离信号到剥削动作的转化", "description": "确保 DeviationDetector 的输出能够有效指导决策系统选择剥削性动作。", "status": "已完成", "dependencies": [], "createdAt": "2025-05-02T20:34:36.440Z", "updatedAt": "2025-05-02T22:04:01.861Z", "relatedFiles": [{"path": "cardgame_ai/algorithms/opponent_modeling/deviation_detector.py", "type": "REFERENCE", "description": "偏离检测器"}, {"path": "cardgame_ai/core/decision_system.py", "type": "TO_MODIFY", "description": "决策系统/策略网络"}, {"path": "cardgame_ai/models/policy_network.py", "type": "TO_MODIFY", "description": "策略网络模型"}], "implementationGuide": "```pseudocode\n# 1. 分析 DeviationDetector (cardgame_ai/algorithms/opponent_modeling/deviation_detector.py) 的输出\n# 2. 分析决策系统 (e.g., HybridDecisionSystem, TransformerPolicy) 的动作选择逻辑\n# 3. 设计偏离信号到剥削动作的映射机制:\n#    a) 直接修改策略 logits\n#    b) 调整价值网络评估\n#    c) 切换到专门的剥削策略网络 (如果实现)\n# 4. 实现选定的映射机制\n# 5. 编写集成测试，模拟对手偏离场景，验证 AI 是否能选择预期剥削动作\n```", "verificationCriteria": "决策系统能够根据偏离信号调整动作选择。在模拟的偏离场景下，AI 能稳定选择剥削性动作。", "analysisResult": "...", "completedAt": "2025-05-02T22:04:01.858Z", "summary": "已成功实现偏离信号到剥削动作的转化机制，包括以下关键组件：\n\n1. 创建了DeviationToExploitMapper类，负责将检测到的偏离信号转化为对策略网络logits的调整\n2. 实现了多种偏离模式识别和相应的剥削策略，包括对风险规避型、模式化、被动型等多种玩家类型的特定对策\n3. 在HybridDecisionSystem中集成了偏离剥削映射器，使系统能够在决策时应用剥削策略\n4. 为NeuralNetworkComponent、SearchComponent和HRLComponent添加了支持，使它们能够接收和使用偏离剥削映射器\n5. 实现了DeviationDetector.analyze_deviation方法用于详细分析对手偏离情况\n6. 修复了必要的导入语句和类型定义，确保系统能够正常工作\n\n该实现使AI系统能够根据对手的行为模式自适应调整策略，针对性地利用对手偏离GTO策略的行为，提高AI系统的整体性能。"}, {"id": "b119802c-8cf0-443c-afe4-dce0dc7d49aa", "name": "实现关键决策点检测器", "description": "实现关键决策点检测器 (KeyMomentDetector) 模块。", "status": "已完成", "dependencies": [], "createdAt": "2025-05-02T20:34:58.012Z", "updatedAt": "2025-05-02T23:22:14.874Z", "relatedFiles": [{"path": "cardgame_ai/algorithms/components/key_moment_detector.py", "type": "CREATE", "description": "关键点检测器实现文件"}, {"path": "cardgame_ai/games/doudizhu/state.py", "type": "REFERENCE", "description": "状态定义或特征提取"}], "implementationGuide": "```pseudocode\n# 1. 创建新文件 cardgame_ai/algorithms/components/key_moment_detector.py\n# 2. 设计检测逻辑:\n#    a) 基于规则: e.g., if len(player_hand) < 5 or is_endgame() or bomb_played()\n#    b) 基于模型: 训练一个小型分类器 (e.g., MLP, Logistic Regression) 预测当前状态是否为关键点，输入可以是状态向量或特定特征。\n# 3. 实现 is_key_moment(state) 方法\n# 4. 编写单元测试验证检测逻辑\n```", "verificationCriteria": "KeyMomentDetector 模块实现并通过单元测试。能够根据输入状态判断是否为关键点。", "analysisResult": "...", "completedAt": "2025-05-02T23:22:14.871Z", "summary": "成功实现关键决策点检测器模块及其整合。发现现有代码库中已有两个实现：一个在components目录下的基于规则和可选机器学习的实现，另一个在algorithms目录下的基于神经网络的实现。分析后选择使用components下的实现，更新了主要系统组件（DynamicBudgetAllocator、MCTS、EfficientZero）的引用，并创建了兼容性重定向模块保持向后兼容性。该检测器能够识别终局状态、特殊牌型（如炸弹或火箭）、关键牌数状态，并支持机器学习模型预测。模块具有详细的单元测试确保其正确性。"}, {"id": "835602f0-98a1-45be-8d64-05e7d8e09200", "name": "集成动态计算预算分配", "description": "将关键点检测器与 MCTS/规划器集成，实现动态计算预算分配。", "status": "已完成", "dependencies": [{"taskId": "b119802c-8cf0-443c-afe4-dce0dc7d49aa"}, {"taskId": "4d51e2fc-5f08-495a-9ebe-8941b3ffa8b9"}], "createdAt": "2025-05-02T20:34:58.012Z", "updatedAt": "2025-05-02T23:42:58.236Z", "relatedFiles": [{"path": "cardgame_ai/core/mcts.py", "type": "TO_MODIFY", "description": "MCTS 核心实现"}, {"path": "cardgame_ai/algorithms/efficient_zero/", "type": "TO_MODIFY", "description": "EfficientZero 实现 (如果使用)"}, {"path": "cardgame_ai/algorithms/components/key_moment_detector.py", "type": "REFERENCE", "description": "关键点检测器"}, {"path": "configs/doudizhu/agent_config.yaml", "type": "TO_MODIFY", "description": "配置文件"}], "implementationGuide": "```pseudocode\n# 1. 修改 MCTS/规划器的主循环或决策入口\n# 2. 在每次决策前调用 key_moment_detector.is_key_moment(state)\n# 3. if is_key_moment:\n#      num_simulations = high_budget_simulations\n#    else:\n#      num_simulations = default_simulations\n# 4. 将 high_budget_simulations 和 default_simulations 作为可配置参数\n# 5. 进行实验，调整预算参数，评估开启动态预算后的性能和决策时间变化\n```", "verificationCriteria": "MCTS/规划器能够根据关键点检测结果动态调整模拟次数。通过实验证明，在总计算时间相近时，动态预算能提升关键局面决策质量和整体胜率。", "analysisResult": "...", "completedAt": "2025-05-02T23:42:58.234Z", "summary": "成功实现了关键点检测器与MCTS/规划器的集成，创建了动态计算预算分配系统。实现了以下内容：\n\n1. 创建了新的DynamicBudgetAllocator类，作为MCTS和关键点检测器之间的桥梁\n2. 修改了EfficientZero类，添加了对动态预算分配器的支持\n3. 更新了MCTS.run方法，使其能够根据关键点检测和动态预算配置调整模拟次数\n4. 增强了EfficientZero.act方法，加入了上下文提取和动态预算分配的逻辑\n5. 更新了配置文件，添加了关键点检测器和动态预算分配的配置选项\n6. 编写了全面的单元测试，验证了动态预算分配系统的各个组件\n\n该系统能够在关键局面自动增加计算资源，在非关键局面减少资源消耗，并支持预算池机制，以在总体计算资源不变的情况下优化资源分配。还添加了详细的解释机制，帮助理解预算分配的决策过程。系统考虑了游戏阶段、玩家角色和手牌数量等上下文因素，实现了灵活的预算调整策略。"}, {"id": "ef30cd90-b76c-436a-b140-5cfb1c063129", "name": "优化 HRL 控制器评估逻辑", "description": "优化 HierarchicalController 的复杂度/置信度评估逻辑。", "status": "已完成", "dependencies": [], "createdAt": "2025-05-02T20:34:58.012Z", "updatedAt": "2025-05-02T22:30:49.643Z", "relatedFiles": [{"path": "cardgame_ai/algorithms/hrl/hierarchical_controller.py", "type": "TO_MODIFY", "description": "HRL 控制器"}, {"path": "configs/doudizhu/hrl_config.yaml", "type": "TO_MODIFY", "description": "配置文件 (阈值参数)"}], "implementationGuide": "```pseudocode\n# 1. 分析 HierarchicalController._evaluate_complexity 和 _evaluate_confidence 方法\n# 2. 改进启发式规则，引入更多状态特征 (e.g., 牌型种类、剩余牌分布熵)\n# 3. (可选) 训练小型辅助网络 (GNN/Attention/MLP) 来预测复杂度和置信度\n# 4. 将辅助网络集成到评估方法中\n# 5. 调整复杂度/置信度阈值参数\n# 6. 通过日志分析和对局评估 HRL 调度准确性的提升\n```", "verificationCriteria": "HRL 控制器的复杂度/置信度评估逻辑得到改进。通过日志和统计分析，调度决策更合理。可选地，训练并集成了辅助评估网络。", "analysisResult": "...", "completedAt": "2025-05-02T22:30:49.641Z", "summary": "成功优化了HRL控制器的评估逻辑，详细改进包括：1) 重构了_evaluate_complexity方法，引入了手牌分布熵、牌型多样性、局面关键程度等多种状态特征；2) 重构了_evaluate_confidence方法，增加了动作空间评估、关键牌型检测、历史一致性等评估逻辑；3) 创建了详细的配置文件支持(configs/doudizhu/hrl_config.yaml)，实现了参数可配置化；4) 实现了从配置文件加载参数的功能；5) 添加了详细的日志记录功能，便于分析决策过程；6) 确保了模块的鲁棒性、性能和可扩展性。这些改进使HRL控制器能够更准确地评估局面复杂度和低层策略置信度，从而做出更合理的决策。"}, {"id": "a33262fd-7376-4eb7-968d-1815501d3b30", "name": "实现模型 ONNX 导出功能", "description": "实现模型导出到 ONNX 格式的脚本。", "status": "已完成", "dependencies": [], "createdAt": "2025-05-02T20:34:58.012Z", "updatedAt": "2025-05-02T22:36:07.716Z", "relatedFiles": [{"path": "scripts/export_onnx.py", "type": "CREATE", "description": "ONNX 导出脚本"}, {"path": "cardgame_ai/models/", "type": "REFERENCE", "description": "模型定义文件"}, {"path": "models/doudiz<PERSON>_multi/", "type": "REFERENCE", "description": "训练好的模型权重"}], "implementationGuide": "```pseudocode\n# 1. 创建导出脚本 export_onnx.py\n# 2. 加载训练好的 PyTorch 模型 (e.g., TransformerPolicy)\n# 3. 定义模型的示例输入 (dummy input)\n# 4. 使用 torch.onnx.export() 函数导出模型\n#    - 指定输入输出名称\n#    - 设置 opset_version\n#    - 处理动态输入轴 (dynamic_axes)\n# 5. 验证导出的 ONNX 模型可以使用 ONNX Runtime 加载和推理\n```", "verificationCriteria": "成功编写 ONNX 导出脚本。能够将训练好的模型导出为 ONNX 格式。导出的模型可以通过 ONNX Runtime 进行推理。", "analysisResult": "...", "completedAt": "2025-05-02T22:36:07.714Z", "summary": "成功实现了模型ONNX导出功能，完成了以下内容：1) 创建了主要的导出脚本export_onnx.py，支持各种模型类型的导出，包括ValuePolicyNet、TransformerPolicy等；2) 添加了详细的使用指南文档onnx_export_guide.md，包含命令行参数说明和使用示例；3) 创建了导出模型推理示例脚本onnx_inference_example.py，展示如何使用导出后的模型；4) 添加了测试脚本test_onnx_export.py用于验证导出功能正常工作。脚本支持动态批处理大小、指定输入输出名称、处理不同模型类型，并提供了验证导出模型正确性的功能。所有这些脚本都包含了详细的中文注释，符合项目的代码质量标准。"}, {"id": "1d79c703-d077-4b2a-9fa0-cf75b26a68cb", "name": "UI增加信念分布可视化", "description": "在 UI 中添加对手手牌信念分布的可视化组件。", "status": "已完成", "dependencies": [], "createdAt": "2025-05-02T20:34:58.012Z", "updatedAt": "2025-05-02T23:27:04.094Z", "relatedFiles": [{"path": "cardgame_ai/ui/main_window.py", "type": "TO_MODIFY", "description": "UI 主程序或视图文件"}, {"path": "cardgame_ai/visualization/plot_utils.py", "type": "TO_MODIFY", "description": "可视化相关工具"}, {"path": "cardgame_ai/interface/api.py", "type": "REFERENCE", "description": "后端接口 (提供信念状态)"}], "implementationGuide": "```pseudocode\n# 1. 确定 UI 框架 (e.g., PySide6, Streamlit)\n# 2. 在 UI 代码中找到合适的位置添加新组件\n# 3. 添加调用 backend API (获取信念状态) 的逻辑\n# 4. 使用图表库 (e.g., Matplotlib/PyQtGraph for PySide6, Plotly/Altair for Streamlit) 渲染概率分布 (e.g., 条形图显示每张牌的持有概率)\n# 5. 确保可视化组件能够随游戏进程实时更新\n```", "verificationCriteria": "UI 界面成功添加信念分布可视化组件。能够实时展示 DeepBeliefTracker 推断的对手手牌概率。", "analysisResult": "...", "completedAt": "2025-05-02T23:27:04.092Z", "summary": "已完成信念分布可视化组件的开发和集成。\n\n1. 创建了 BeliefDistributionWidget 组件，支持使用 QtCharts 图表库或备选的自定义渲染方式\n2. 在推理视图中添加了信念分布标签页，集成了该可视化组件\n3. 创建了 BeliefAPI 接口类，用于从 DeepBeliefTracker 获取信念状态数据\n4. 实现了可视化组件的数据更新和清除方法\n5. 添加了模拟数据功能，便于在开发和测试阶段使用\n\n组件能够根据 UI 框架可用性自动选择合适的渲染方式，同时提供了较好的交互体验。用户可以轻松地查看对手手牌的概率分布，这将帮助他们更好地理解 AI 的决策过程和信念推断结果。"}, {"id": "8deb6ded-1349-4069-830a-b07a213cc3b7", "name": "建立经验自动收集与处理流水线", "description": "建立自动化的人机交互经验收集与处理流水线。", "status": "已完成", "dependencies": [], "createdAt": "2025-05-02T20:34:58.012Z", "updatedAt": "2025-05-02T23:34:20.095Z", "relatedFiles": [{"path": "scripts/process_human_data.py", "type": "CREATE", "description": "数据处理脚本"}, {"path": "cardgame_ai/core/replay_buffer.py", "type": "REFERENCE", "description": "回放缓冲区实现"}, {"path": "data/human_games/", "type": "REFERENCE", "description": "数据存储位置"}], "implementationGuide": "```pseudocode\n# 1. 确定数据源 (游戏服务器日志, 本地对局记录文件)\n# 2. 编写数据解析脚本，提取完整的状态、动作、奖励序列\n# 3. 实现数据清洗逻辑 (处理异常数据、不完整对局)\n# 4. (可选) 实现数据质量评估 (基于胜负、玩家评分等)\n# 5. 将处理后的数据转换为标准格式 (e.g., Replay Buffer 存储格式)\n# 6. 编写脚本定期执行数据收集、处理、存储任务\n```", "verificationCriteria": "成功建立自动化流水线。能够从指定数据源提取、处理人机对局数据，并将其存入经验库。", "analysisResult": "...", "completedAt": "2025-05-02T23:34:20.092Z", "summary": "已成功建立人机交互经验自动收集与处理流水线。实现包括：\n\n1. 编写了数据处理脚本 process_human_data.py，提供完整的数据解析、清洗、质量评估和标准化功能\n2. 创建了自动收集脚本 auto_collect_experiences.py，支持定期执行、多数据源监控、归档和守护进程功能\n3. 提供了 Windows 和 Linux 平台下的示例脚本，简化使用流程\n4. 支持多种文件格式（JSON、日志）的自动识别和处理\n5. 实现了数据质量评估机制，确保经验数据高质量\n6. 与 ReplayBuffer 集成，提供标准化的数据存储格式\n\n整个流水线具有可配置性、健壮性和可扩展性，能够从多种数据源自动收集人机交互数据，并将经过标准化处理的高质量数据存入经验库，满足任务所有要求。"}]}