"""
简单生成合成人类对局数据
"""
import os
import numpy as np
import json
import random
import logging
from typing import List, Dict, Any, Tuple

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    # 参数
    num_samples = 1000
    output_path = "cardgame_ai/data/human_logs/synthetic_data.jsonl"
    state_dim = 627
    
    logger.info(f"生成{num_samples}个合成人类对局数据样本")
    
    # 确保目录存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # 生成数据
    with open(output_path, 'w', encoding='utf-8') as f:
        for i in range(num_samples):
            # 生成随机状态
            state = np.random.rand(state_dim).tolist()
            
            # 生成随机动作（0-14之间的整数）
            action = random.randint(0, 14)
            
            # 创建数据项
            data_item = {
                "game_state": state,
                "human_action": action
            }
            
            # 写入文件
            f.write(json.dumps(data_item) + '\n')
            
            # 每100个样本打印一次进度
            if (i + 1) % 100 == 0:
                logger.info(f"已生成 {i + 1}/{num_samples} 个样本")
    
    logger.info(f"数据已保存至: {output_path}")

if __name__ == "__main__":
    main()
