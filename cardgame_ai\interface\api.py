#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
API接口模块

提供UI和游戏逻辑之间的接口。
"""

import logging
from typing import Dict, List, Any, Optional, Union, Tuple
import numpy as np

# 导入必要的游戏和算法模块
from cardgame_ai.games.common.belief_state import BeliefState
from cardgame_ai.games.doudizhu.card import Card

logger = logging.getLogger(__name__)


class BeliefAPI:
    """信念状态API接口类"""
    
    @staticmethod
    def get_belief_distribution(
        game_state: Optional[Any] = None,
        belief_tracker: Optional[Any] = None
    ) -> Dict[str, Dict]:
        """
        获取信念分布数据
        
        Args:
            game_state: 游戏状态
            belief_tracker: 信念追踪器实例
            
        Returns:
            Dict: 包含信念分布的字典，格式为:
                {
                    'distribution': {牌ID: 概率值},
                    'card_names': {牌ID: 牌名称}
                }
        """
        try:
            if belief_tracker is None:
                logger.warning("没有提供信念追踪器实例")
                return {
                    'distribution': {},
                    'card_names': {}
                }
            
            # 获取信念状态
            belief_state = belief_tracker.get_belief_state()
            if belief_state is None:
                logger.warning("信念状态为空")
                return {
                    'distribution': {},
                    'card_names': {}
                }
            
            # 提取概率分布
            distribution = {}
            
            # 遍历所有可能的牌
            for card_id, prob in belief_state.probabilities.items():
                distribution[str(card_id)] = float(prob)
            
            # 创建牌ID到牌名的映射
            card_names = {}
            for card_id in distribution:
                # 这里可能需要根据具体游戏进行定制
                try:
                    if isinstance(card_id, str) and card_id.isdigit():
                        card = Card(int(card_id))
                        card_names[card_id] = str(card)
                    else:
                        card_names[card_id] = str(card_id)
                except:
                    card_names[card_id] = str(card_id)
            
            return {
                'distribution': distribution,
                'card_names': card_names
            }
            
        except Exception as e:
            logger.error(f"获取信念分布时出错: {e}")
            return {
                'distribution': {},
                'card_names': {}
            }
    
    @staticmethod
    def get_most_likely_cards(
        belief_tracker: Optional[Any] = None,
        top_n: int = 10
    ) -> List[Tuple[str, float]]:
        """
        获取最可能的手牌
        
        Args:
            belief_tracker: 信念追踪器实例
            top_n: 返回的前N张牌
            
        Returns:
            List[Tuple[str, float]]: 最可能手牌的列表，格式为 [(牌ID, 概率), ...]
        """
        try:
            if belief_tracker is None:
                logger.warning("没有提供信念追踪器实例")
                return []
            
            # 获取信念状态
            belief_state = belief_tracker.get_belief_state()
            if belief_state is None:
                logger.warning("信念状态为空")
                return []
            
            # 提取概率分布并排序
            distribution = {str(card_id): float(prob) for card_id, prob in belief_state.probabilities.items()}
            sorted_cards = sorted(distribution.items(), key=lambda x: x[1], reverse=True)
            
            # 返回前N张牌
            return sorted_cards[:top_n]
            
        except Exception as e:
            logger.error(f"获取最可能手牌时出错: {e}")
            return [] 