#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
重要性加权训练示例脚本

展示如何使用关键决策点检测器对样本进行加权训练，提高模型在关键决策点的表现。
"""

import os
import sys
import argparse
import logging
import numpy as np
import torch
from typing import Dict, List, Any, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from cardgame_ai.algorithms.efficient_zero import EfficientZero
from cardgame_ai.algorithms.key_moment_detector import KeyMomentDetector
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.games.doudizhu.state import DouDizhuState
from cardgame_ai.core.base import Action, Experience

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='重要性加权训练示例')
    
    parser.add_argument('--key_moment_model', type=str, default=None,
                        help='关键决策点检测器模型路径')
    parser.add_argument('--model_path', type=str, default=None,
                        help='EfficientZero模型路径')
    parser.add_argument('--save_path', type=str, default='models/weighted_training',
                        help='模型保存路径')
    parser.add_argument('--num_episodes', type=int, default=10,
                        help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=32,
                        help='批次大小')
    parser.add_argument('--importance_weight_scale', type=float, default=2.0,
                        help='重要性权重缩放因子')
    parser.add_argument('--seed', type=int, default=None,
                        help='随机种子')
    
    return parser.parse_args()


def collect_experiences(env, num_steps=1000):
    """
    收集经验数据
    
    Args:
        env: 游戏环境
        num_steps: 收集步数
        
    Returns:
        List[Experience]: 经验列表
    """
    experiences = []
    state = env.reset()
    
    for _ in range(num_steps):
        # 随机选择动作
        legal_actions = env.get_legal_actions(state)
        action = np.random.choice(legal_actions)
        action_obj = Action(action_id=action)
        
        # 执行动作
        next_state, reward, done, _ = env.step(action)
        
        # 创建经验
        exp = Experience(state, action_obj, reward, next_state, done)
        experiences.append(exp)
        
        # 更新状态
        state = next_state
        
        if done:
            state = env.reset()
    
    return experiences


def create_batch(experiences, batch_size=32, device='cuda'):
    """
    创建训练批次
    
    Args:
        experiences: 经验列表
        batch_size: 批次大小
        device: 计算设备
        
    Returns:
        Dict: 批次数据
    """
    # 随机采样
    indices = np.random.choice(len(experiences), batch_size, replace=False)
    batch_experiences = [experiences[i] for i in indices]
    
    # 创建批次数据
    batch = {
        'observations': torch.FloatTensor(np.array([exp.state for exp in batch_experiences])).to(device),
        'actions': torch.LongTensor(np.array([exp.action.action_id for exp in batch_experiences])).to(device),
        'rewards': torch.FloatTensor(np.array([exp.reward for exp in batch_experiences])).to(device),
        'next_observations': torch.FloatTensor(np.array([exp.next_state for exp in batch_experiences])).to(device),
        'dones': torch.FloatTensor(np.array([float(exp.done) for exp in batch_experiences])).to(device)
    }
    
    return batch


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 设置随机种子
    if args.seed is not None:
        np.random.seed(args.seed)
        torch.manual_seed(args.seed)
    
    # 设置设备
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    logger.info(f"使用设备: {device}")
    
    # 创建游戏环境
    env = DouDizhuEnvironment()
    
    # 获取观察和动作空间
    observation_shape = env.observation_space.shape
    action_shape = (env.action_space.n,)
    
    # 创建关键决策点检测器
    if args.key_moment_model:
        # 加载预训练的关键决策点检测器
        key_moment_detector = KeyMomentDetector.load(args.key_moment_model, device=device)
        logger.info(f"已加载关键决策点检测器: {args.key_moment_model}")
    else:
        # 创建新的关键决策点检测器
        key_moment_detector = KeyMomentDetector(
            state_dim=observation_shape[0],
            hidden_dims=[256, 128, 64],
            output_dim=1,
            use_attention=True,
            use_history=True,
            device=device
        )
        logger.info("已创建新的关键决策点检测器")
    
    # 创建两个模型，一个使用重要性加权训练，另一个不使用
    model_weighted = EfficientZero(
        state_shape=observation_shape,
        action_shape=action_shape,
        hidden_dim=128,
        state_dim=64,
        use_resnet=False,
        batch_size=args.batch_size,
        num_simulations=50,
        use_importance_weighting=True,
        importance_weight_scale=args.importance_weight_scale,
        device=device
    )
    
    model_no_weighted = EfficientZero(
        state_shape=observation_shape,
        action_shape=action_shape,
        hidden_dim=128,
        state_dim=64,
        use_resnet=False,
        batch_size=args.batch_size,
        num_simulations=50,
        use_importance_weighting=False,
        device=device
    )
    
    # 如果有预训练模型，加载参数
    if args.model_path:
        model_weighted.load(args.model_path)
        model_no_weighted.load(args.model_path)
        logger.info(f"已加载预训练模型: {args.model_path}")
    
    # 设置关键决策点检测器
    model_weighted.set_key_moment_detector(key_moment_detector)
    
    # 收集经验数据
    logger.info("正在收集经验数据...")
    experiences = collect_experiences(env, num_steps=5000)
    logger.info(f"已收集 {len(experiences)} 条经验")
    
    # 训练模型
    logger.info("开始训练...")
    weighted_losses_history = []
    no_weighted_losses_history = []
    
    for episode in range(args.num_episodes):
        # 创建批次数据
        batch = create_batch(experiences, batch_size=args.batch_size, device=device)
        
        # 使用重要性加权训练
        losses_weighted = model_weighted.train(batch)
        weighted_losses_history.append(losses_weighted)
        
        # 不使用重要性加权训练
        losses_no_weighted = model_no_weighted.train(batch)
        no_weighted_losses_history.append(losses_no_weighted)
        
        # 打印训练信息
        if (episode + 1) % 10 == 0 or episode == 0:
            logger.info(f"Episode {episode + 1}/{args.num_episodes}")
            logger.info(f"  加权训练损失: value={losses_weighted['value_loss']:.4f}, policy={losses_weighted['policy_loss']:.4f}, total={losses_weighted['total_loss']:.4f}")
            logger.info(f"  普通训练损失: value={losses_no_weighted['value_loss']:.4f}, policy={losses_no_weighted['policy_loss']:.4f}, total={losses_no_weighted['total_loss']:.4f}")
            
            if 'importance_weight_mean' in losses_weighted:
                logger.info(f"  重要性权重: mean={losses_weighted['importance_weight_mean']:.4f}, max={losses_weighted['importance_weight_max']:.4f}, min={losses_weighted['importance_weight_min']:.4f}")
    
    # 保存模型
    if args.save_path:
        # 创建保存目录
        os.makedirs(args.save_path, exist_ok=True)
        
        # 保存加权训练模型
        weighted_model_path = os.path.join(args.save_path, 'weighted_model.pt')
        model_weighted.save(weighted_model_path)
        logger.info(f"已保存加权训练模型: {weighted_model_path}")
        
        # 保存普通训练模型
        no_weighted_model_path = os.path.join(args.save_path, 'no_weighted_model.pt')
        model_no_weighted.save(no_weighted_model_path)
        logger.info(f"已保存普通训练模型: {no_weighted_model_path}")
    
    # 比较两种训练方法
    logger.info("\n训练结果比较:")
    
    # 计算平均损失
    weighted_avg_losses = {
        key: np.mean([loss[key] for loss in weighted_losses_history]) 
        for key in ['value_loss', 'policy_loss', 'total_loss']
    }
    
    no_weighted_avg_losses = {
        key: np.mean([loss[key] for loss in no_weighted_losses_history]) 
        for key in ['value_loss', 'policy_loss', 'total_loss']
    }
    
    logger.info(f"加权训练平均损失: value={weighted_avg_losses['value_loss']:.4f}, policy={weighted_avg_losses['policy_loss']:.4f}, total={weighted_avg_losses['total_loss']:.4f}")
    logger.info(f"普通训练平均损失: value={no_weighted_avg_losses['value_loss']:.4f}, policy={no_weighted_avg_losses['policy_loss']:.4f}, total={no_weighted_avg_losses['total_loss']:.4f}")
    
    # 评估模型在关键决策点的表现
    logger.info("\n关键决策点表现评估:")
    
    # 收集一些测试数据
    test_experiences = collect_experiences(env, num_steps=1000)
    
    # 筛选关键决策点
    key_moments = []
    for exp in test_experiences:
        score, is_key = key_moment_detector.predict(exp.state, threshold=0.7)
        if is_key:
            key_moments.append((exp, score))
    
    logger.info(f"测试数据中的关键决策点数量: {len(key_moments)}")
    
    if len(key_moments) > 0:
        # 评估两个模型在关键决策点的表现
        weighted_correct = 0
        no_weighted_correct = 0
        
        for exp, score in key_moments:
            # 获取最优动作（这里简化处理，假设reward最大的动作是最优的）
            legal_actions = env.get_legal_actions(exp.state)
            best_reward = -float('inf')
            best_action = None
            
            for action in legal_actions:
                state_copy = exp.state.clone()
                _, reward, _, _ = env.step_with_state(state_copy, action)
                if reward > best_reward:
                    best_reward = reward
                    best_action = action
            
            # 使用两个模型预测动作
            weighted_action, _ = model_weighted.act(exp.state)
            no_weighted_action, _ = model_no_weighted.act(exp.state)
            
            # 检查是否与最优动作一致
            if weighted_action == best_action:
                weighted_correct += 1
            
            if no_weighted_action == best_action:
                no_weighted_correct += 1
        
        # 计算准确率
        weighted_accuracy = weighted_correct / len(key_moments)
        no_weighted_accuracy = no_weighted_correct / len(key_moments)
        
        logger.info(f"加权训练模型在关键决策点的准确率: {weighted_accuracy:.4f}")
        logger.info(f"普通训练模型在关键决策点的准确率: {no_weighted_accuracy:.4f}")
        
        # 计算提升比例
        improvement = (weighted_accuracy - no_weighted_accuracy) / no_weighted_accuracy * 100
        logger.info(f"加权训练提升: {improvement:.2f}%")


if __name__ == "__main__":
    main()
