#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
训练监控组件

显示训练进度、损失曲线、奖励曲线等。
"""

import os
import logging
import numpy as np
from typing import Dict, List, Any, Optional, Tuple

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTabWidget, QGroupBox, QFormLayout, QSpacerItem, QSizePolicy,
    QComboBox, QCheckBox, QFileDialog, QMessageBox
)
from PySide6.QtCore import Qt, Signal, Slot, QSize
from PySide6.QtGui import QFont, QColor, QPainter, QPen

# 尝试导入图表库
try:
    from PySide6.QtCharts import QChart, QChartView, QLineSeries, QValueAxis
    QTCHARTS_AVAILABLE = True
except ImportError:
    QTCHARTS_AVAILABLE = False

    # 尝试导入matplotlib作为备选
    try:
        import matplotlib
        matplotlib.use('Qt5Agg')
        from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
        from matplotlib.figure import Figure
        import matplotlib.pyplot as plt
        MATPLOTLIB_AVAILABLE = True
    except ImportError:
        MATPLOTLIB_AVAILABLE = False

logger = logging.getLogger(__name__)


class TrainingMonitor(QWidget):
    """训练监控组件类"""

    def __init__(self, config, parent=None):
        """
        初始化训练监控组件

        Args:
            config: 客户端配置
            parent: 父部件
        """
        super().__init__(parent)

        # 保存配置
        self.config = config

        # 设置对象名称
        self.setObjectName("trainingMonitor")

        # 训练数据
        self.epochs = []
        self.losses = []
        self.rewards = []
        self.win_rates = []

        # 图表类型
        if QTCHARTS_AVAILABLE:
            self.chart_type = "qtcharts"
        elif MATPLOTLIB_AVAILABLE:
            self.chart_type = "matplotlib"
        else:
            self.chart_type = "none"
            logger.warning("没有可用的图表库，将使用简单文本显示")

        # 设置图表字体大小
        self.chart_font = QFont()
        self.chart_font.setPointSize(9)  # 使用适中的字体大小

        # 初始化UI
        self.setup_ui()

        logger.info(f"训练监控组件初始化完成，使用图表库：{self.chart_type}")

    def setup_ui(self):
        """设置UI布局"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(10)

        # 创建标题标签
        title_label = QLabel("训练监控")
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)

        # 创建图表区域
        self.tab_widget = QTabWidget()
        self.tab_widget.setMinimumHeight(350)
        self.tab_widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # 创建损失曲线标签页
        loss_tab = QWidget()
        loss_layout = QVBoxLayout(loss_tab)
        loss_layout.setContentsMargins(5, 5, 5, 5)  # 设置较小的边距
        loss_layout.setSpacing(0)  # 减小间距
        self.loss_chart_view = self.create_chart_view("损失曲线", "回合", "损失值")
        loss_layout.addWidget(self.loss_chart_view)
        self.tab_widget.addTab(loss_tab, "损失曲线")

        # 创建奖励曲线标签页
        reward_tab = QWidget()
        reward_layout = QVBoxLayout(reward_tab)
        reward_layout.setContentsMargins(5, 5, 5, 5)  # 设置较小的边距
        reward_layout.setSpacing(0)  # 减小间距
        self.reward_chart_view = self.create_chart_view("奖励曲线", "回合", "奖励值")
        reward_layout.addWidget(self.reward_chart_view)
        self.tab_widget.addTab(reward_tab, "奖励曲线")

        # 创建胜率曲线标签页
        win_rate_tab = QWidget()
        win_rate_layout = QVBoxLayout(win_rate_tab)
        win_rate_layout.setContentsMargins(5, 5, 5, 5)  # 设置较小的边距
        win_rate_layout.setSpacing(0)  # 减小间距
        self.win_rate_chart_view = self.create_chart_view("胜率曲线", "回合", "胜率 (%)")
        win_rate_layout.addWidget(self.win_rate_chart_view)
        self.tab_widget.addTab(win_rate_tab, "胜率曲线")

        # 添加图表区域到主布局
        main_layout.addWidget(self.tab_widget)

        # 创建训练指标区域
        metrics_group = QGroupBox("训练指标")
        metrics_layout = QFormLayout(metrics_group)

        # 创建训练指标标签
        self.episode_label = QLabel("0/0")
        metrics_layout.addRow("当前回合:", self.episode_label)

        self.loss_label = QLabel("0.0")
        metrics_layout.addRow("当前损失:", self.loss_label)

        self.reward_label = QLabel("0.0")
        metrics_layout.addRow("当前奖励:", self.reward_label)

        self.win_rate_label = QLabel("0.0%")
        metrics_layout.addRow("当前胜率:", self.win_rate_label)

        self.avg_loss_label = QLabel("0.0")
        metrics_layout.addRow("平均损失:", self.avg_loss_label)

        self.avg_reward_label = QLabel("0.0")
        metrics_layout.addRow("平均奖励:", self.avg_reward_label)

        self.avg_win_rate_label = QLabel("0.0%")
        metrics_layout.addRow("平均胜率:", self.avg_win_rate_label)

        # 添加训练指标区域到主布局
        main_layout.addWidget(metrics_group)

        # 创建按钮区域
        button_layout = QHBoxLayout()

        # 创建保存图表按钮
        save_chart_button = QPushButton("保存图表")
        save_chart_button.clicked.connect(self.save_chart)
        button_layout.addWidget(save_chart_button)

        # 创建清除数据按钮
        clear_button = QPushButton("清除数据")
        clear_button.clicked.connect(self.clear_data)
        button_layout.addWidget(clear_button)

        # 添加按钮区域到主布局
        main_layout.addLayout(button_layout)

        logger.info("训练监控组件UI布局设置完成")

    def create_chart_view(self, title: str, x_label: str, y_label: str) -> QWidget:
        """
        创建图表视图

        Args:
            title (str): 图表标题
            x_label (str): X轴标签
            y_label (str): Y轴标签

        Returns:
            QWidget: 图表视图部件
        """
        if self.chart_type == "qtcharts":
            return self.create_qtcharts_view(title, x_label, y_label)
        elif self.chart_type == "matplotlib":
            return self.create_matplotlib_view(title, x_label, y_label)
        else:
            # 创建简单文本显示
            label = QLabel("图表不可用，请安装QtCharts或Matplotlib")
            label.setAlignment(Qt.AlignCenter)
            return label

    def create_qtcharts_view(self, title: str, x_label: str, y_label: str) -> QWidget:
        """
        创建QtCharts图表视图

        Args:
            title (str): 图表标题
            x_label (str): X轴标签
            y_label (str): Y轴标签

        Returns:
            QWidget: 图表视图部件
        """
        # 创建图表
        chart = QChart()
        chart.setTitle(title)

        # 设置图表字体
        chart.setTitleFont(self.chart_font)

        # 创建数据系列
        series = QLineSeries()
        series.setName(title)

        # 添加数据系列到图表
        chart.addSeries(series)

        # 创建坐标轴
        axis_x = QValueAxis()
        axis_x.setTitleText(x_label)
        axis_x.setLabelFormat("%d")
        axis_x.setTickCount(5)
        axis_x.setRange(0, 10)

        # 设置X轴字体
        axis_x.setLabelsFont(self.chart_font)
        axis_x.setTitleFont(self.chart_font)

        axis_y = QValueAxis()
        axis_y.setTitleText(y_label)
        axis_y.setLabelFormat("%.2f")
        axis_y.setTickCount(5)
        axis_y.setRange(0, 1)

        # 设置Y轴字体
        axis_y.setLabelsFont(self.chart_font)
        axis_y.setTitleFont(self.chart_font)

        # 添加坐标轴到图表
        chart.addAxis(axis_x, Qt.AlignBottom)
        chart.addAxis(axis_y, Qt.AlignLeft)

        # 将数据系列附加到坐标轴
        series.attachAxis(axis_x)
        series.attachAxis(axis_y)

        # 创建图表视图
        chart_view = QChartView(chart)

        # 确保正确处理高DPI缩放
        chart_view.setRenderHint(QPainter.Antialiasing)
        chart_view.setRenderHint(QPainter.TextAntialiasing)
        chart_view.setRenderHint(QPainter.SmoothPixmapTransform)

        # 设置图表视图的大小策略，使其能够在有更多空间时扩展
        chart_view.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        chart_view.setMinimumSize(300, 200)  # 设置最小宽度和高度

        # 调整图表边距，为标签预留足够空间
        chart.setContentsMargins(10, 10, 10, 10)

        # 保存引用
        if title == "损失曲线":
            self.loss_chart = chart
            self.loss_series = series
            self.loss_axis_x = axis_x
            self.loss_axis_y = axis_y
        elif title == "奖励曲线":
            self.reward_chart = chart
            self.reward_series = series
            self.reward_axis_x = axis_x
            self.reward_axis_y = axis_y
        elif title == "胜率曲线":
            self.win_rate_chart = chart
            self.win_rate_series = series
            self.win_rate_axis_x = axis_x
            self.win_rate_axis_y = axis_y

        return chart_view

    def create_matplotlib_view(self, title: str, x_label: str, y_label: str) -> QWidget:
        """
        创建Matplotlib图表视图

        Args:
            title (str): 图表标题
            x_label (str): X轴标签
            y_label (str): Y轴标签

        Returns:
            QWidget: 图表视图部件
        """
        # 创建图表
        fig = Figure(figsize=(5, 4), dpi=100)
        ax = fig.add_subplot(111)
        ax.set_title(title)
        ax.set_xlabel(x_label)
        ax.set_ylabel(y_label)
        ax.grid(True)

        # 创建画布
        canvas = FigureCanvas(fig)

        # 设置画布的大小策略，使其能够在有更多空间时扩展
        canvas.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        canvas.setMinimumHeight(300)

        # 保存引用
        if title == "损失曲线":
            self.loss_fig = fig
            self.loss_ax = ax
            self.loss_canvas = canvas
        elif title == "奖励曲线":
            self.reward_fig = fig
            self.reward_ax = ax
            self.reward_canvas = canvas
        elif title == "胜率曲线":
            self.win_rate_fig = fig
            self.win_rate_ax = ax
            self.win_rate_canvas = canvas

        return canvas

    def update_data(self, epoch: int, loss: float, reward: float, win_rate: float, total_epochs: int):
        """
        更新训练数据

        Args:
            epoch (int): 当前回合
            loss (float): 损失值
            reward (float): 奖励值
            win_rate (float): 胜率
            total_epochs (int): 总回合数
        """
        # 添加数据
        self.epochs.append(epoch)
        self.losses.append(loss)
        self.rewards.append(reward)
        self.win_rates.append(win_rate)

        # 更新图表
        self.update_charts()

        # 更新指标标签
        self.update_metrics(epoch, loss, reward, win_rate, total_epochs)

        logger.info(f"更新训练数据：回合={epoch}, 损失={loss:.4f}, 奖励={reward:.4f}, 胜率={win_rate:.2f}%")

    def update_charts(self):
        """更新图表"""
        if self.chart_type == "qtcharts":
            self.update_qtcharts()
        elif self.chart_type == "matplotlib":
            self.update_matplotlib_charts()

    def update_qtcharts(self):
        """更新QtCharts图表"""
        # 检查数据是否为空
        if not self.epochs:
            return

        # 更新损失曲线
        self.loss_series.clear()
        for i, epoch in enumerate(self.epochs):
            self.loss_series.append(epoch, self.losses[i])

        # 更新奖励曲线
        self.reward_series.clear()
        for i, epoch in enumerate(self.epochs):
            self.reward_series.append(epoch, self.rewards[i])

        # 更新胜率曲线
        self.win_rate_series.clear()
        for i, epoch in enumerate(self.epochs):
            self.win_rate_series.append(epoch, self.win_rates[i])

        # 更新坐标轴范围
        max_epoch = max(self.epochs) if self.epochs else 10

        # 计算合适的刻度数量
        data_points = len(self.epochs)
        if data_points > 0:
            # 根据数据点数量动态调整刻度数量
            tick_count = min(max(5, data_points // 10), 15)

            # 损失曲线坐标轴
            self.loss_axis_x.setTickCount(tick_count)
            self.loss_axis_x.setRange(0, max_epoch + 1)

            # 奖励曲线坐标轴
            self.reward_axis_x.setTickCount(tick_count)
            self.reward_axis_x.setRange(0, max_epoch + 1)

            # 胜率曲线坐标轴
            self.win_rate_axis_x.setTickCount(tick_count)
            self.win_rate_axis_x.setRange(0, max_epoch + 1)

            # 根据数据范围优化标签格式
            if max_epoch > 1000:
                self.loss_axis_x.setLabelFormat("%.1e")  # 科学计数法
                self.reward_axis_x.setLabelFormat("%.1e")
                self.win_rate_axis_x.setLabelFormat("%.1e")
            elif max_epoch > 100:
                self.loss_axis_x.setLabelFormat("%d")  # 整数
                self.reward_axis_x.setLabelFormat("%d")
                self.win_rate_axis_x.setLabelFormat("%d")
            else:
                self.loss_axis_x.setLabelFormat("%d")  # 整数
                self.reward_axis_x.setLabelFormat("%d")
                self.win_rate_axis_x.setLabelFormat("%d")

        # 设置Y轴范围
        if self.losses:
            min_loss = min(self.losses)
            max_loss = max(self.losses)
            range_loss = max_loss - min_loss
            self.loss_axis_y.setRange(
                max(0, min_loss - range_loss * 0.1),
                max_loss + range_loss * 0.1
            )

        if self.rewards:
            min_reward = min(self.rewards)
            max_reward = max(self.rewards)
            range_reward = max_reward - min_reward
            self.reward_axis_y.setRange(
                min_reward - range_reward * 0.1,
                max_reward + range_reward * 0.1
            )

        # 胜率曲线Y轴范围固定为0-100
        self.win_rate_axis_y.setRange(0, 100)

    def update_matplotlib_charts(self):
        """更新Matplotlib图表"""
        # 检查数据是否为空
        if not self.epochs:
            return

        # 更新损失曲线
        self.loss_ax.clear()
        self.loss_ax.plot(self.epochs, self.losses, 'r-')
        self.loss_ax.set_title("损失曲线")
        self.loss_ax.set_xlabel("回合")
        self.loss_ax.set_ylabel("损失值")
        self.loss_ax.grid(True)

        # 更新坐标轴范围
        max_epoch = max(self.epochs) if self.epochs else 10
        self.loss_ax.set_xlim(0, max_epoch + 1)

        if self.losses:
            min_loss = min(self.losses)
            max_loss = max(self.losses)
            range_loss = max_loss - min_loss
            self.loss_ax.set_ylim(
                max(0, min_loss - range_loss * 0.1),
                max_loss + range_loss * 0.1
            )

        # 重绘损失曲线
        self.loss_canvas.draw()

        # 更新奖励曲线
        self.reward_ax.clear()
        self.reward_ax.plot(self.epochs, self.rewards, 'g-')
        self.reward_ax.set_title("奖励曲线")
        self.reward_ax.set_xlabel("回合")
        self.reward_ax.set_ylabel("奖励值")
        self.reward_ax.grid(True)

        # 更新坐标轴范围
        self.reward_ax.set_xlim(0, max_epoch + 1)

        if self.rewards:
            min_reward = min(self.rewards)
            max_reward = max(self.rewards)
            range_reward = max_reward - min_reward
            self.reward_ax.set_ylim(
                min_reward - range_reward * 0.1,
                max_reward + range_reward * 0.1
            )

        # 重绘奖励曲线
        self.reward_canvas.draw()

        # 更新胜率曲线
        self.win_rate_ax.clear()
        self.win_rate_ax.plot(self.epochs, self.win_rates, 'b-')
        self.win_rate_ax.set_title("胜率曲线")
        self.win_rate_ax.set_xlabel("回合")
        self.win_rate_ax.set_ylabel("胜率 (%)")
        self.win_rate_ax.grid(True)

        # 更新坐标轴范围
        self.win_rate_ax.set_xlim(0, max_epoch + 1)
        self.win_rate_ax.set_ylim(0, 100)

        # 重绘胜率曲线
        self.win_rate_canvas.draw()

    def update_metrics(self, epoch: int, loss: float, reward: float, win_rate: float, total_epochs: int):
        """
        更新训练指标

        Args:
            epoch (int): 当前回合
            loss (float): 损失值
            reward (float): 奖励值
            win_rate (float): 胜率
            total_epochs (int): 总回合数
        """
        # 更新回合标签
        self.episode_label.setText(f"{epoch}/{total_epochs}")

        # 更新当前指标
        self.loss_label.setText(f"{loss:.4f}")
        self.reward_label.setText(f"{reward:.4f}")
        self.win_rate_label.setText(f"{win_rate:.2f}%")

        # 更新平均指标
        if self.losses:
            avg_loss = sum(self.losses) / len(self.losses)
            self.avg_loss_label.setText(f"{avg_loss:.4f}")

        if self.rewards:
            avg_reward = sum(self.rewards) / len(self.rewards)
            self.avg_reward_label.setText(f"{avg_reward:.4f}")

        if self.win_rates:
            avg_win_rate = sum(self.win_rates) / len(self.win_rates)
            self.avg_win_rate_label.setText(f"{avg_win_rate:.2f}%")

    def save_chart(self):
        """保存图表"""
        # 获取当前选中的标签页
        current_tab = self.tab_widget.currentIndex()
        tab_name = self.tab_widget.tabText(current_tab)

        # 获取保存路径
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "保存图表",
            os.path.join(os.path.expanduser("~"), f"{tab_name}.png"),
            "图片文件 (*.png *.jpg *.jpeg *.bmp *.pdf)"
        )

        if not file_path:
            return

        try:
            if self.chart_type == "qtcharts":
                # 保存QtCharts图表
                if current_tab == 0:  # 损失曲线
                    pixmap = self.loss_chart_view.grab()
                    pixmap.save(file_path)
                elif current_tab == 1:  # 奖励曲线
                    pixmap = self.reward_chart_view.grab()
                    pixmap.save(file_path)
                elif current_tab == 2:  # 胜率曲线
                    pixmap = self.win_rate_chart_view.grab()
                    pixmap.save(file_path)
            elif self.chart_type == "matplotlib":
                # 保存Matplotlib图表
                if current_tab == 0:  # 损失曲线
                    self.loss_fig.savefig(file_path)
                elif current_tab == 1:  # 奖励曲线
                    self.reward_fig.savefig(file_path)
                elif current_tab == 2:  # 胜率曲线
                    self.win_rate_fig.savefig(file_path)

            logger.info(f"图表已保存：{file_path}")

            # 显示成功消息
            QMessageBox.information(self, "保存成功", f"图表已保存到：{file_path}")
        except Exception as e:
            logger.error(f"保存图表失败：{e}")

            # 显示错误消息
            QMessageBox.critical(self, "保存失败", f"保存图表失败：{e}")

    def clear_data(self):
        """清除数据"""
        # 确认对话框
        reply = QMessageBox.question(
            self,
            "确认清除",
            "确定要清除所有训练数据吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        # 清除数据
        self.epochs = []
        self.losses = []
        self.rewards = []
        self.win_rates = []

        # 更新图表
        self.update_charts()

        # 重置指标标签
        self.episode_label.setText("0/0")
        self.loss_label.setText("0.0")
        self.reward_label.setText("0.0")
        self.win_rate_label.setText("0.0%")
        self.avg_loss_label.setText("0.0")
        self.avg_reward_label.setText("0.0")
        self.avg_win_rate_label.setText("0.0%")

        logger.info("清除训练数据")

    def get_data(self) -> Dict[str, List]:
        """
        获取训练数据

        Returns:
            Dict[str, List]: 训练数据字典
        """
        return {
            "epochs": self.epochs.copy(),
            "losses": self.losses.copy(),
            "rewards": self.rewards.copy(),
            "win_rates": self.win_rates.copy()
        }

    def load_data(self, data: Dict[str, List]):
        """
        加载训练数据

        Args:
            data (Dict[str, List]): 训练数据字典
        """
        # 加载数据
        self.epochs = data.get("epochs", []).copy()
        self.losses = data.get("losses", []).copy()
        self.rewards = data.get("rewards", []).copy()
        self.win_rates = data.get("win_rates", []).copy()

        # 更新图表
        self.update_charts()

        # 更新指标标签
        if self.epochs:
            epoch = self.epochs[-1]
            loss = self.losses[-1]
            reward = self.rewards[-1]
            win_rate = self.win_rates[-1]
            total_epochs = max(self.epochs)

            self.update_metrics(epoch, loss, reward, win_rate, total_epochs)

        logger.info(f"加载训练数据：{len(self.epochs)}条记录")
