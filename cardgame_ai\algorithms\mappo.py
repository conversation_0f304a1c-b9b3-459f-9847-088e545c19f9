"""
多智能体近端策略优化算法模块

实现多智能体近端策略优化(MAPPO)算法，一种基于PPO的多智能体强化学习算法。
MAPPO使用中心化训练分散执行(CTDE)的范式，适用于斗地主等多智能体合作与对抗场景。
"""
import os
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from typing import Dict, List, Tuple, Any, Optional, Union

from cardgame_ai.core.base import State, Action, Experience, Batch
from cardgame_ai.core.algorithm import PolicyBasedAlgorithm
from cardgame_ai.core.environment import MultiAgentEnvironment
from cardgame_ai.algorithms.ppo import PPONetwork


class MAPPONetwork(nn.Module):
    """
    多智能体PPO网络
    
    支持多智能体场景的PPO网络，可以处理全局状态和局部观察，
    实现中心化训练分散执行范式。
    """
    
    def __init__(
        self, 
        obs_dim: int, 
        act_dim: int, 
        global_state_dim: Optional[int] = None,
        hidden_dims: List[int] = [256, 128], 
        shared_layers: bool = True,
        use_central_critic: bool = True
    ):
        """
        初始化多智能体PPO网络
        
        Args:
            obs_dim: 局部观察维度
            act_dim: 动作维度
            global_state_dim: 全局状态维度，默认为None（使用局部观察）
            hidden_dims: 隐藏层维度
            shared_layers: 是否共享特征提取层
            use_central_critic: 是否使用中心化评论家（全局状态输入）
        """
        super(MAPPONetwork, self).__init__()
        
        self.shared_layers = shared_layers
        self.use_central_critic = use_central_critic
        
        # 策略网络（分散执行）- 使用局部观察
        if shared_layers:
            # 共享特征提取层
            self.actor_feature_layer = nn.Sequential(
                nn.Linear(obs_dim, hidden_dims[0]),
                nn.ReLU()
            )
            
            # 策略头
            policy_layers = []
            policy_input_dim = hidden_dims[0]
            
            for i in range(1, len(hidden_dims)):
                policy_layers.append(nn.Linear(policy_input_dim, hidden_dims[i]))
                policy_layers.append(nn.ReLU())
                policy_input_dim = hidden_dims[i]
            
            policy_layers.append(nn.Linear(policy_input_dim, act_dim))
            self.policy_head = nn.Sequential(*policy_layers)
            
        else:
            # 独立的策略网络
            policy_layers = []
            policy_input_dim = obs_dim
            
            for hidden_dim in hidden_dims:
                policy_layers.append(nn.Linear(policy_input_dim, hidden_dim))
                policy_layers.append(nn.ReLU())
                policy_input_dim = hidden_dim
            
            policy_layers.append(nn.Linear(policy_input_dim, act_dim))
            self.policy_net = nn.Sequential(*policy_layers)
        
        # 价值网络（中心化训练）- 使用全局状态或局部观察
        critic_input_dim = global_state_dim if use_central_critic and global_state_dim is not None else obs_dim
        
        if shared_layers:
            self.critic_feature_layer = nn.Sequential(
                nn.Linear(critic_input_dim, hidden_dims[0]),
                nn.ReLU()
            )
            
            # 价值头
            value_layers = []
            value_input_dim = hidden_dims[0]
            
            for i in range(1, len(hidden_dims)):
                value_layers.append(nn.Linear(value_input_dim, hidden_dims[i]))
                value_layers.append(nn.ReLU())
                value_input_dim = hidden_dims[i]
            
            value_layers.append(nn.Linear(value_input_dim, 1))
            self.value_head = nn.Sequential(*value_layers)
        else:
            # 独立的价值网络
            value_layers = []
            value_input_dim = critic_input_dim
            
            for hidden_dim in hidden_dims:
                value_layers.append(nn.Linear(value_input_dim, hidden_dim))
                value_layers.append(nn.ReLU())
                value_input_dim = hidden_dim
            
            value_layers.append(nn.Linear(value_input_dim, 1))
            self.value_net = nn.Sequential(*value_layers)
    
    def forward_actor(self, obs: torch.Tensor) -> torch.Tensor:
        """
        前向传播 - 策略网络
        
        Args:
            obs: 局部观察张量
            
        Returns:
            动作对数概率
        """
        if self.shared_layers:
            features = self.actor_feature_layer(obs)
            action_logits = self.policy_head(features)
        else:
            action_logits = self.policy_net(obs)
        
        return action_logits
    
    def forward_critic(self, state: torch.Tensor) -> torch.Tensor:
        """
        前向传播 - 价值网络
        
        Args:
            state: 状态张量（全局状态或局部观察）
            
        Returns:
            状态价值
        """
        if self.shared_layers:
            features = self.critic_feature_layer(state)
            state_values = self.value_head(features)
        else:
            state_values = self.value_net(state)
        
        return state_values
    
    def get_action_probs(self, obs: torch.Tensor) -> torch.Tensor:
        """
        获取动作概率分布
        
        Args:
            obs: 局部观察张量
            
        Returns:
            动作概率分布
        """
        action_logits = self.forward_actor(obs)
        return F.softmax(action_logits, dim=-1)
    
    def get_action_log_probs(self, obs: torch.Tensor) -> torch.Tensor:
        """
        获取动作对数概率
        
        Args:
            obs: 局部观察张量
            
        Returns:
            动作对数概率
        """
        action_logits = self.forward_actor(obs)
        return F.log_softmax(action_logits, dim=-1)
    
    def get_value(self, state: torch.Tensor) -> torch.Tensor:
        """
        获取状态价值
        
        Args:
            state: 状态张量（全局状态或局部观察）
            
        Returns:
            状态价值
        """
        return self.forward_critic(state)
    
    def evaluate_actions(self, obs: torch.Tensor, state: torch.Tensor, actions: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        评估动作
        
        Args:
            obs: 局部观察张量
            state: 全局状态张量
            actions: 动作张量
            
        Returns:
            动作对数概率、状态价值、动作熵
        """
        action_logits = self.forward_actor(obs)
        action_log_probs = F.log_softmax(action_logits, dim=-1)
        action_probs = F.softmax(action_logits, dim=-1)
        
        action_log_probs = action_log_probs.gather(1, actions.unsqueeze(1))
        entropy = -(action_probs * action_log_probs).sum(dim=-1).mean()
        
        values = self.forward_critic(state)
        
        return action_log_probs.squeeze(-1), values.squeeze(-1), entropy


class MAPPO(PolicyBasedAlgorithm):
    """
    多智能体近端策略优化(MAPPO)算法
    
    实现MAPPO算法，使用中心化训练分散执行的范式训练多智能体系统。
    """
    
    def __init__(
        self,
        obs_shape: Tuple[int, ...],
        act_shape: Tuple[int, ...],
        global_state_shape: Optional[Tuple[int, ...]] = None,
        hidden_dims: List[int] = [256, 128],
        learning_rate: float = 0.0001,
        gamma: float = 0.99,
        gae_lambda: float = 0.95,
        clip_ratio: float = 0.2,
        value_coef: float = 0.5,
        entropy_coef: float = 0.01,
        max_grad_norm: float = 0.5,
        update_epochs: int = 4,
        shared_network: bool = True,
        use_central_critic: bool = True,
        batch_size: int = 64,
        device: str = None
    ):
        """
        初始化MAPPO算法
        
        Args:
            obs_shape: 观察形状
            act_shape: 动作形状
            global_state_shape: 全局状态形状，默认为None
            hidden_dims: 隐藏层维度
            learning_rate: 学习率
            gamma: 折扣因子
            gae_lambda: GAE平滑参数
            clip_ratio: PPO裁剪比例
            value_coef: 价值损失系数
            entropy_coef: 熵正则化系数
            max_grad_norm: 梯度裁剪范数
            update_epochs: 每次更新的轮数
            shared_network: 是否使用共享网络
            use_central_critic: 是否使用中心化评论家
            batch_size: 批次大小
            device: 计算设备
        """
        super().__init__(obs_shape, act_shape, gamma)
        
        # 设置设备
        self.device = device if device is not None else ('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 计算维度
        self.obs_dim = np.prod(obs_shape)
        self.act_dim = np.prod(act_shape)
        self.global_state_dim = np.prod(global_state_shape) if global_state_shape is not None else None
        
        # 创建网络
        self.network = MAPPONetwork(
            obs_dim=self.obs_dim,
            act_dim=self.act_dim,
            global_state_dim=self.global_state_dim,
            hidden_dims=hidden_dims,
            shared_layers=shared_network,
            use_central_critic=use_central_critic
        ).to(self.device)
        
        # 创建优化器
        self.optimizer = optim.Adam(self.network.parameters(), lr=learning_rate)
        
        # 设置超参数
        self.gae_lambda = gae_lambda
        self.clip_ratio = clip_ratio
        self.value_coef = value_coef
        self.entropy_coef = entropy_coef
        self.max_grad_norm = max_grad_norm
        self.update_epochs = update_epochs
        self.batch_size = batch_size
        self.use_central_critic = use_central_critic
        
        # 存储训练数据
        self.observations = []
        self.global_states = []
        self.actions = []
        self.action_log_probs = []
        self.rewards = []
        self.values = []
        self.dones = []
    
    def update(self, experience: Union[Experience, Batch]) -> Dict[str, float]:
        """
        使用经验数据更新模型
        
        Args:
            experience: 单个经验或经验批次
            
        Returns:
            更新指标，如损失值等
        """
        # 存储经验数据
        if isinstance(experience, Experience):
            self._store_experience(experience)
        elif isinstance(experience, Batch):
            for exp in experience.experiences:
                self._store_experience(exp)
        
        # 检查是否有足够的数据更新
        if len(self.observations) < self.batch_size:
            return {'policy_loss': 0.0, 'value_loss': 0.0, 'entropy': 0.0}
        
        # 处理所有存储的数据
        return self._update_policy()
    
    def predict(self, obs: Union[State, np.ndarray], global_state: Optional[np.ndarray] = None) -> Tuple[List[float], float]:
        """
        预测动作概率分布和价值
        
        Args:
            obs: 观察或状态
            global_state: 全局状态，用于中心化评论家
            
        Returns:
            动作概率分布、状态价值
        """
        # 处理观察
        if isinstance(obs, State):
            obs_array = obs.to_dict().get('observation', np.zeros(self.obs_dim))
            obs_tensor = torch.FloatTensor(obs_array).to(self.device)
        else:
            obs_tensor = torch.FloatTensor(obs).to(self.device)
        
        # 重塑观察
        if obs_tensor.dim() == 1:
            obs_tensor = obs_tensor.unsqueeze(0)
        
        # 处理全局状态
        if self.use_central_critic and global_state is not None:
            state_tensor = torch.FloatTensor(global_state).to(self.device)
            if state_tensor.dim() == 1:
                state_tensor = state_tensor.unsqueeze(0)
        else:
            state_tensor = obs_tensor  # 使用局部观察作为状态输入
        
        # 预测动作概率和价值
        with torch.no_grad():
            action_probs = self.network.get_action_probs(obs_tensor).cpu().numpy()[0]
            value = self.network.get_value(state_tensor).cpu().numpy()[0][0]
        
        return list(action_probs), float(value)
    
    def save(self, path: str) -> None:
        """
        保存模型
        
        Args:
            path: 保存路径
        """
        # 确保目录存在
        os.makedirs(os.path.dirname(path), exist_ok=True)
        
        # 保存模型
        torch.save({
            'network_state_dict': self.network.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict()
        }, path)
    
    def load(self, path: str) -> None:
        """
        加载模型
        
        Args:
            path: 加载路径
        """
        # 加载模型
        checkpoint = torch.load(path, map_location=self.device)
        
        self.network.load_state_dict(checkpoint['network_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
    
    @property
    def name(self) -> str:
        """
        获取算法名称
        
        Returns:
            算法名称
        """
        return "MAPPO"
    
    def _store_experience(self, experience: Experience) -> None:
        """
        存储经验数据
        
        Args:
            experience: 经验
        """
        # 处理观察
        if isinstance(experience.state, State):
            obs_array = experience.state.to_dict().get('observation', np.zeros(self.obs_dim))
            global_state_array = experience.state.to_dict().get('global_state', obs_array)
        else:
            obs_array = experience.state
            global_state_array = experience.info.get('global_state', obs_array) if experience.info else obs_array
        
        # 处理动作
        if isinstance(experience.action, Action):
            action = experience.action.to_dict().get('index', 0)
        else:
            action = experience.action
        
        # 转换为张量并预测动作概率和价值
        obs_tensor = torch.FloatTensor(obs_array).unsqueeze(0).to(self.device)
        state_tensor = torch.FloatTensor(global_state_array).unsqueeze(0).to(self.device) if self.use_central_critic else obs_tensor
        
        with torch.no_grad():
            action_log_probs = self.network.get_action_log_probs(obs_tensor)
            action_log_prob = action_log_probs[0, action].cpu().item()
            value = self.network.get_value(state_tensor).cpu().item()
        
        # 存储数据
        self.observations.append(obs_array)
        self.global_states.append(global_state_array)
        self.actions.append(action)
        self.action_log_probs.append(action_log_prob)
        self.rewards.append(experience.reward)
        self.values.append(value)
        self.dones.append(float(experience.done))
    
    def _update_policy(self) -> Dict[str, float]:
        """
        更新策略
        
        Returns:
            更新指标
        """
        # 计算优势和回报
        advantages, returns = self._compute_advantages_and_returns()
        
        # 转换为张量
        observations = torch.FloatTensor(self.observations).to(self.device)
        global_states = torch.FloatTensor(self.global_states).to(self.device) if self.use_central_critic else observations
        actions = torch.LongTensor(self.actions).to(self.device)
        old_action_log_probs = torch.FloatTensor(self.action_log_probs).to(self.device)
        returns = torch.FloatTensor(returns).to(self.device)
        advantages = torch.FloatTensor(advantages).to(self.device)
        
        # 归一化优势
        advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)
        
        # 多轮更新
        total_policy_loss = 0
        total_value_loss = 0
        total_entropy = 0
        
        for _ in range(self.update_epochs):
            # 生成批次索引
            indices = torch.randperm(len(observations))
            
            # 按批次更新
            for start_idx in range(0, len(observations), self.batch_size):
                # 获取批次索引
                batch_indices = indices[start_idx:start_idx + self.batch_size]
                
                # 提取批次数据
                batch_observations = observations[batch_indices]
                batch_global_states = global_states[batch_indices]
                batch_actions = actions[batch_indices]
                batch_old_action_log_probs = old_action_log_probs[batch_indices]
                batch_returns = returns[batch_indices]
                batch_advantages = advantages[batch_indices]
                
                # 评估动作
                action_log_probs, values, entropy = self.network.evaluate_actions(
                    batch_observations, batch_global_states, batch_actions
                )
                
                # 计算比率和裁剪目标
                ratio = torch.exp(action_log_probs - batch_old_action_log_probs)
                surr1 = ratio * batch_advantages
                surr2 = torch.clamp(ratio, 1.0 - self.clip_ratio, 1.0 + self.clip_ratio) * batch_advantages
                
                # 计算策略损失
                policy_loss = -torch.min(surr1, surr2).mean()
                
                # 计算价值损失
                value_loss = F.mse_loss(values, batch_returns)
                
                # 计算总损失
                loss = policy_loss + self.value_coef * value_loss - self.entropy_coef * entropy
                
                # 更新网络
                self.optimizer.zero_grad()
                loss.backward()
                nn.utils.clip_grad_norm_(self.network.parameters(), self.max_grad_norm)
                self.optimizer.step()
                
                # 累加损失
                total_policy_loss += policy_loss.item()
                total_value_loss += value_loss.item()
                total_entropy += entropy.item()
        
        # 清空存储的数据
        self.observations = []
        self.global_states = []
        self.actions = []
        self.action_log_probs = []
        self.rewards = []
        self.values = []
        self.dones = []
        
        # 计算平均损失
        avg_policy_loss = total_policy_loss / (self.update_epochs * (len(observations) // self.batch_size + 1))
        avg_value_loss = total_value_loss / (self.update_epochs * (len(observations) // self.batch_size + 1))
        avg_entropy = total_entropy / (self.update_epochs * (len(observations) // self.batch_size + 1))
        
        return {
            'policy_loss': avg_policy_loss,
            'value_loss': avg_value_loss,
            'entropy': avg_entropy
        }
    
    def _compute_advantages_and_returns(self) -> Tuple[np.ndarray, np.ndarray]:
        """
        计算广义优势估计(GAE)和回报
        
        Returns:
            优势和回报
        """
        # 添加最后的值作为引导值
        values = np.array(self.values + [0.0])
        rewards = np.array(self.rewards)
        dones = np.array(self.dones)
        
        # 计算TD误差和优势
        deltas = rewards + self.gamma * values[1:] * (1 - dones) - values[:-1]
        
        # 计算GAE
        advantages = np.zeros_like(rewards)
        gae = 0
        for t in reversed(range(len(rewards))):
            gae = deltas[t] + self.gamma * self.gae_lambda * (1 - dones[t]) * gae
            advantages[t] = gae
        
        # 计算回报
        returns = advantages + np.array(self.values)
        
        return advantages, returns 