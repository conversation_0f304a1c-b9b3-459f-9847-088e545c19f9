"""
GTO 剥削增强型 MCTS 使用示例

本示例展示如何配置和使用 GTO 剥削增强型 MCTS 类，通过整合偏离检测器和
剥削映射器，在 MCTS 搜索过程中直接融入对手 GTO 偏离剥削能力。
"""

import torch
import numpy as np
import logging
from typing import Dict, Any, List

# 设置日志级别
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入必要的组件
from cardgame_ai.games.doudizhu import DoudizhuEnv  # 斗地主环境
from cardgame_ai.algorithms.gto_approximation import GTOPolicy  # GTO策略
from cardgame_ai.algorithms.human_policy_network import HumanPolicyNetwork  # 人类策略网络
from cardgame_ai.algorithms.opponent_modeling.deviation_detector import DeviationDetector, DeviationToExploitMapper  # 偏离检测器和剥削映射器
from cardgame_ai.algorithms.gto_exploiting_mcts import GTOExploitingMCTS, ExploitNode  # GTO剥削增强型MCTS
from cardgame_ai.models.muzero_model import MuZeroModel  # MuZero模型

def create_deviation_detector(state_dim: int, action_dim: int, gto_policy_path: str, human_policy_path: str) -> DeviationDetector:
    """
    创建偏离检测器
    
    Args:
        state_dim: 状态维度
        action_dim: 动作维度
        gto_policy_path: GTO策略模型路径
        human_policy_path: 人类策略模型路径
        
    Returns:
        偏离检测器实例
    """
    # 加载GTO策略
    logger.info(f"加载GTO策略模型: {gto_policy_path}")
    gto_policy = GTOPolicy(
        state_dim=state_dim,
        action_dim=action_dim,
        model_path=gto_policy_path
    )
    
    # 创建偏离检测器
    logger.info("创建偏离检测器")
    detector = DeviationDetector(
        state_dim=state_dim,
        action_dim=action_dim,
        gto_policy_source=gto_policy,
        human_policy_path=human_policy_path,
        deviation_threshold=0.3
    )
    
    return detector

def create_exploit_mapper(detector: DeviationDetector) -> DeviationToExploitMapper:
    """
    创建偏离剥削映射器
    
    Args:
        detector: 偏离检测器实例
        
    Returns:
        偏离剥削映射器实例
    """
    logger.info("创建偏离剥削映射器")
    mapper = DeviationToExploitMapper(
        deviation_detector=detector,
        min_confidence=0.3,
        max_logit_adjustment=2.0,
        enable_pattern_specific_exploitation=True,
        exploitation_strength=1.0
    )
    
    return mapper

def setup_exploiting_mcts(
    detector: DeviationDetector, 
    mapper: DeviationToExploitMapper, 
    num_simulations: int = 50
) -> GTOExploitingMCTS:
    """
    设置GTO剥削增强型MCTS
    
    Args:
        detector: 偏离检测器实例
        mapper: 偏离剥削映射器实例
        num_simulations: 模拟次数
        
    Returns:
        GTO剥削增强型MCTS实例
    """
    logger.info(f"创建GTO剥削增强型MCTS (模拟次数: {num_simulations})")
    mcts = GTOExploitingMCTS(
        num_simulations=num_simulations,
        discount=0.997,
        dirichlet_alpha=0.25,
        exploration_fraction=0.25,
        root_exploration_noise=True,
        use_belief_state=True,  # 使用信念状态
        
        # GTO剥削相关参数
        deviation_detector=detector,
        exploit_mapper=mapper,
        use_gto_exploitation=True,
        exploitation_strength=1.2,
        exploitation_threshold=0.3,
        dynamic_exploitation=True,
        backprop_exploitation_weight=0.5,
        exploitation_memory_size=20,
        exploit_known_patterns=True,
        pattern_bonus_factor=1.2,
        
        # 其他MCTS参数
        use_information_value=True,
        information_value_weight=0.3,
        use_opponent_model_prior=True,
        opponent_model_prior_weight=0.5,
        use_deep_belief_tracker=True,
        deep_belief_weight=0.7
    )
    
    return mcts

def simulate_game(env: DoudizhuEnv, mcts: GTOExploitingMCTS, model: MuZeroModel, max_turns: int = 50) -> Dict[str, Any]:
    """
    模拟一局游戏，展示GTO剥削增强型MCTS的使用
    
    Args:
        env: 游戏环境
        mcts: MCTS实例
        model: 模型实例
        max_turns: 最大回合数
        
    Returns:
        游戏结果
    """
    # 初始化游戏
    state, _ = env.reset()
    done = False
    turn = 0
    
    # 创建偏离检测历史记录
    deviation_history = []
    
    logger.info("开始游戏模拟")
    
    # 游戏主循环
    while not done and turn < max_turns:
        turn += 1
        logger.info(f"回合 {turn}, 玩家 {state.player_id}")
        
        # 获取合法动作掩码
        legal_actions = env.get_legal_actions(state)
        actions_mask = np.zeros(env.action_space_size, dtype=np.int32)
        for action in legal_actions:
            actions_mask[action] = 1
        
        # 获取对手偏离信息 (如果上一步有对手行动)
        deviation_info = None
        if len(deviation_history) > 0:
            deviation_info = deviation_history[-1]
        
        # 运行MCTS搜索
        visit_counts, policy = mcts.run(
            root_state=state,
            model=model,
            temperature=1.0,
            actions_mask=actions_mask,
            deviation_info=deviation_info,
            explain=True
        )
        
        # 选择动作
        action = max(visit_counts.items(), key=lambda x: x[1])[0]
        
        # 执行动作
        next_state, reward, done, info = env.step(action)
        
        # 检测对手偏离 (假设是下一个玩家)
        if not done and state.player_id != next_state.player_id:
            # 获取对手上一步的动作
            opponent_action = info.get("last_action", None)
            if opponent_action is not None:
                # 使用偏离检测器分析对手行为
                deviation_info = mcts.deviation_detector.analyze_deviation(next_state, opponent_action)
                deviation_history.append(deviation_info)
                
                # 记录偏离信息
                if deviation_info["is_significant"]:
                    logger.info(f"检测到显著偏离! 类型: {deviation_info['deviation_type']}, 分数: {deviation_info['deviation_score']:.4f}")
        
        # 更新状态
        state = next_state
    
    logger.info(f"游戏结束, 共 {turn} 回合")
    
    # 返回游戏结果
    return {
        "turns": turn,
        "reward": reward,
        "done": done,
        "deviation_history": deviation_history
    }

def main():
    """主函数"""
    try:
        # 配置参数
        state_dim = 512  # 状态维度
        action_dim = 27472  # 斗地主动作空间大小
        gto_policy_path = "models/muzero_doudizhu/gto_policy.pt"
        human_policy_path = "models/human_policy/human_policy.pt"
        muzero_model_path = "models/muzero_doudizhu/muzero_model.pt"
        
        # 创建游戏环境
        logger.info("创建斗地主游戏环境")
        env = DoudizhuEnv()
        
        # 创建偏离检测器
        detector = create_deviation_detector(state_dim, action_dim, gto_policy_path, human_policy_path)
        
        # 创建偏离剥削映射器
        mapper = create_exploit_mapper(detector)
        
        # 设置GTO剥削增强型MCTS
        mcts = setup_exploiting_mcts(detector, mapper, num_simulations=100)
        
        # 加载MuZero模型
        logger.info(f"加载MuZero模型: {muzero_model_path}")
        model = MuZeroModel(state_dim=state_dim, action_dim=action_dim)
        try:
            model.load_state_dict(torch.load(muzero_model_path))
            logger.info("MuZero模型加载成功")
        except Exception as e:
            logger.error(f"MuZero模型加载失败: {e}")
            logger.info("将使用随机初始化的模型进行演示")
        
        # 模拟游戏
        result = simulate_game(env, mcts, model, max_turns=50)
        
        # 输出结果
        logger.info(f"游戏模拟结果: {result}")
        
        # 分析偏离历史
        if result["deviation_history"]:
            significant_deviations = [d for d in result["deviation_history"] if d["is_significant"]]
            logger.info(f"显著偏离次数: {len(significant_deviations)}/{len(result['deviation_history'])}")
            
            if significant_deviations:
                avg_score = sum(d["deviation_score"] for d in significant_deviations) / len(significant_deviations)
                logger.info(f"平均偏离分数: {avg_score:.4f}")
                
                deviation_types = [d["deviation_type"] for d in significant_deviations]
                type_counts = {t: deviation_types.count(t) for t in set(deviation_types)}
                logger.info(f"偏离类型分布: {type_counts}")
        
    except Exception as e:
        logger.error(f"运行过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 