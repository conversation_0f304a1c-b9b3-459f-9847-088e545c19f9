"""
GTO正则化监控工具

提供用于监控和可视化GTO正则化性能的工具函数。
"""

import os
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Any, Optional
import pickle
import json

import torch

from cardgame_ai.algorithms.gto_approximation.gto_regularizer import GTORegularizer


class GTOMonitor:
    """
    GTO监控器
    
    用于跟踪和可视化模型与GTO策略的距离，以及GTO正则化效果。
    """
    
    def __init__(
        self,
        save_path: str,
        regularizer: Optional[GTORegularizer] = None,
        log_interval: int = 10,
        visualization_interval: int = 100
    ):
        """
        初始化GTO监控器
        
        Args:
            save_path: 保存路径
            regularizer: GTO正则化器实例
            log_interval: 日志记录间隔（步数）
            visualization_interval: 可视化生成间隔（步数）
        """
        self.save_path = save_path
        self.regularizer = regularizer
        self.log_interval = log_interval
        self.visualization_interval = visualization_interval
        
        # 创建保存目录
        os.makedirs(os.path.join(save_path, 'gto_monitoring'), exist_ok=True)
        
        # 初始化指标记录
        self.metrics = {
            'steps': [],
            'distances': [],  # 与GTO策略的距离
            'weights': [],    # 正则化权重
            'losses': []      # GTO损失值
        }
        
        # 初始化日志文件
        self.log_file = os.path.join(save_path, 'gto_monitoring', 'gto_metrics.jsonl')
        
    def set_regularizer(self, regularizer: GTORegularizer) -> None:
        """
        设置GTO正则化器
        
        Args:
            regularizer: GTO正则化器实例
        """
        self.regularizer = regularizer
    
    def log_metrics(
        self,
        step: int,
        distance: Optional[float] = None,
        weight: Optional[float] = None,
        loss: Optional[float] = None,
        states: Optional[List[Any]] = None,
        legal_actions_list: Optional[List[List[int]]] = None,
        predicted_policies: Optional[torch.Tensor] = None
    ) -> None:
        """
        记录GTO指标
        
        Args:
            step: 当前训练步数
            distance: 与GTO策略的距离（可选）
            weight: 当前正则化权重（可选）
            loss: 当前GTO损失值（可选）
            states: 状态列表（可选，用于计算距离）
            legal_actions_list: 合法动作列表（可选，用于计算距离）
            predicted_policies: 预测的策略分布（可选，用于计算距离）
        """
        if step % self.log_interval != 0:
            return
        
        # 如果未提供距离且有正则化器和策略，则计算距离
        if distance is None and self.regularizer is not None and states is not None and predicted_policies is not None:
            try:
                mean_dist, max_dist, min_dist = self.regularizer.compute_policy_distance(
                    states=states,
                    legal_actions_list=legal_actions_list,
                    predicted_policies=predicted_policies
                )
                distance = mean_dist
            except Exception as e:
                print(f"计算GTO距离时出错: {e}")
                distance = None
        
        # 如果未提供权重且有正则化器，则获取当前权重
        if weight is None and self.regularizer is not None:
            weight = self.regularizer.get_current_weight()
        
        # 记录指标
        self.metrics['steps'].append(step)
        self.metrics['distances'].append(distance)
        self.metrics['weights'].append(weight)
        self.metrics['losses'].append(loss)
        
        # 写入日志文件
        with open(self.log_file, 'a', encoding='utf-8') as f:
            metrics_dict = {
                'step': step,
                'distance': distance,
                'weight': weight,
                'loss': loss
            }
            f.write(json.dumps(metrics_dict) + '\n')
        
        # 定期保存完整指标
        if step % (self.log_interval * 10) == 0:
            self._save_metrics()
        
        # 定期生成可视化
        if step % self.visualization_interval == 0:
            self.visualize()
    
    def _save_metrics(self) -> None:
        """保存完整指标到文件"""
        metrics_path = os.path.join(self.save_path, 'gto_monitoring', 'all_metrics.pkl')
        with open(metrics_path, 'wb') as f:
            pickle.dump(self.metrics, f)
    
    def visualize(self) -> None:
        """生成GTO指标的可视化图表"""
        # 检查是否有足够的数据点
        if len(self.metrics['steps']) < 2:
            return
        
        output_dir = os.path.join(self.save_path, 'gto_monitoring')
        
        # 1. 绘制与GTO策略的距离变化
        if any(d is not None for d in self.metrics['distances']):
            plt.figure(figsize=(10, 6))
            valid_indices = [i for i, d in enumerate(self.metrics['distances']) if d is not None]
            steps = [self.metrics['steps'][i] for i in valid_indices]
            distances = [self.metrics['distances'][i] for i in valid_indices]
            
            plt.plot(steps, distances, 'b-', label='与GTO策略的距离')
            plt.title('训练过程中与GTO策略的距离变化')
            plt.xlabel('训练步数')
            plt.ylabel('距离')
            plt.grid(True, linestyle='--', alpha=0.7)
            plt.legend()
            plt.tight_layout()
            plt.savefig(os.path.join(output_dir, 'gto_distance.png'))
            plt.close()
        
        # 2. 绘制GTO正则化权重变化
        if any(w is not None for w in self.metrics['weights']):
            plt.figure(figsize=(10, 6))
            valid_indices = [i for i, w in enumerate(self.metrics['weights']) if w is not None]
            steps = [self.metrics['steps'][i] for i in valid_indices]
            weights = [self.metrics['weights'][i] for i in valid_indices]
            
            plt.plot(steps, weights, 'r-', label='GTO正则化权重')
            plt.title('训练过程中GTO正则化权重变化')
            plt.xlabel('训练步数')
            plt.ylabel('权重')
            plt.grid(True, linestyle='--', alpha=0.7)
            plt.legend()
            plt.tight_layout()
            plt.savefig(os.path.join(output_dir, 'gto_weight.png'))
            plt.close()
        
        # 3. 绘制GTO损失值变化
        if any(l is not None for l in self.metrics['losses']):
            plt.figure(figsize=(10, 6))
            valid_indices = [i for i, l in enumerate(self.metrics['losses']) if l is not None]
            steps = [self.metrics['steps'][i] for i in valid_indices]
            losses = [self.metrics['losses'][i] for i in valid_indices]
            
            plt.plot(steps, losses, 'g-', label='GTO损失')
            plt.title('训练过程中GTO损失变化')
            plt.xlabel('训练步数')
            plt.ylabel('损失值')
            plt.grid(True, linestyle='--', alpha=0.7)
            plt.legend()
            plt.tight_layout()
            plt.savefig(os.path.join(output_dir, 'gto_loss.png'))
            plt.close()
        
        # 4. 绘制综合图表（包含所有指标）
        plt.figure(figsize=(12, 8))
        
        # 创建多个y轴
        ax1 = plt.gca()
        ax2 = ax1.twinx()
        ax3 = ax1.twinx()
        ax3.spines['right'].set_position(('outward', 60))
        
        # 绘制距离
        if any(d is not None for d in self.metrics['distances']):
            valid_indices = [i for i, d in enumerate(self.metrics['distances']) if d is not None]
            steps = [self.metrics['steps'][i] for i in valid_indices]
            distances = [self.metrics['distances'][i] for i in valid_indices]
            ax1.plot(steps, distances, 'b-', label='与GTO策略的距离')
            ax1.set_ylabel('距离', color='b')
            ax1.tick_params(axis='y', labelcolor='b')
        
        # 绘制权重
        if any(w is not None for w in self.metrics['weights']):
            valid_indices = [i for i, w in enumerate(self.metrics['weights']) if w is not None]
            steps = [self.metrics['steps'][i] for i in valid_indices]
            weights = [self.metrics['weights'][i] for i in valid_indices]
            ax2.plot(steps, weights, 'r-', label='GTO正则化权重')
            ax2.set_ylabel('权重', color='r')
            ax2.tick_params(axis='y', labelcolor='r')
        
        # 绘制损失
        if any(l is not None for l in self.metrics['losses']):
            valid_indices = [i for i, l in enumerate(self.metrics['losses']) if l is not None]
            steps = [self.metrics['steps'][i] for i in valid_indices]
            losses = [self.metrics['losses'][i] for i in valid_indices]
            ax3.plot(steps, losses, 'g-', label='GTO损失')
            ax3.set_ylabel('损失值', color='g')
            ax3.tick_params(axis='y', labelcolor='g')
        
        plt.title('GTO指标综合图表')
        plt.xlabel('训练步数')
        plt.grid(True, linestyle='--', alpha=0.7)
        
        # 创建图例
        lines1, labels1 = ax1.get_legend_handles_labels()
        lines2, labels2 = ax2.get_legend_handles_labels()
        lines3, labels3 = ax3.get_legend_handles_labels()
        lines = lines1 + lines2 + lines3
        labels = labels1 + labels2 + labels3
        ax1.legend(lines, labels, loc='upper center', bbox_to_anchor=(0.5, -0.1), ncol=3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'gto_combined.png'))
        plt.close()
    
    def load(self) -> None:
        """从文件加载指标"""
        metrics_path = os.path.join(self.save_path, 'gto_monitoring', 'all_metrics.pkl')
        if os.path.exists(metrics_path):
            try:
                with open(metrics_path, 'rb') as f:
                    self.metrics = pickle.load(f)
                print(f"成功加载GTO监控指标，共 {len(self.metrics['steps'])} 个数据点")
            except Exception as e:
                print(f"加载GTO监控指标时出错: {e}")


def compute_gto_policy_diversity(gto_policy, env, num_samples: int = 100) -> float:
    """
    计算GTO策略的多样性
    
    利用信息熵度量策略的多样性，值越高表示策略越多样化
    
    Args:
        gto_policy: GTO策略实例
        env: 游戏环境
        num_samples: 采样数量
    
    Returns:
        float: 策略多样性得分（0-1之间，越高越多样）
    """
    diversities = []
    
    # 生成多个不同状态
    for _ in range(num_samples):
        state = env.reset()
        # 随机前进几步
        steps = np.random.randint(1, 10)
        for _ in range(steps):
            legal_actions = env.get_legal_actions()
            if not legal_actions:
                break
            action = np.random.choice(legal_actions)
            state, _, done, _ = env.step(action)
            if done:
                break
        
        # 获取该状态下的GTO策略
        legal_actions = env.get_legal_actions()
        if not legal_actions:
            continue
            
        policy = gto_policy.get_policy(state, legal_actions)
        
        # 计算策略的信息熵
        if policy is not None and len(policy) > 0:
            # 过滤掉概率为0的动作
            valid_probs = [p for p in policy if p > 0]
            if valid_probs:
                # 归一化概率
                valid_probs = np.array(valid_probs) / sum(valid_probs)
                # 计算信息熵
                entropy = -np.sum(valid_probs * np.log2(valid_probs))
                # 归一化熵值（除以最大可能熵，即均匀分布的熵）
                max_entropy = np.log2(len(valid_probs))
                if max_entropy > 0:
                    normalized_entropy = entropy / max_entropy
                    diversities.append(normalized_entropy)
    
    # 计算平均多样性
    if diversities:
        return np.mean(diversities)
    else:
        return 0.0


def visualize_gto_distance_distribution(
    regularizer: GTORegularizer,
    states: List[Any],
    legal_actions_list: List[List[int]],
    predicted_policies: torch.Tensor,
    output_path: str
) -> None:
    """
    可视化模型策略与GTO策略的距离分布
    
    Args:
        regularizer: GTO正则化器
        states: 状态列表
        legal_actions_list: 合法动作列表
        predicted_policies: 预测的策略分布
        output_path: 输出图像路径
    """
    # 获取距离
    distances = []
    pred_probs = torch.nn.functional.softmax(predicted_policies, dim=1)
    
    for i in range(len(states)):
        state = states[i]
        legal_actions = legal_actions_list[i] if i < len(legal_actions_list) else None
        pred_prob = pred_probs[i]
        
        # 获取GTO策略
        gto_policy = regularizer.gto_policy.get_policy(state, legal_actions)
        if gto_policy is None or len(gto_policy) != len(pred_prob):
            continue
        
        # 转换为张量
        gto_policy_tensor = torch.FloatTensor(gto_policy).to(pred_prob.device)
        
        # 根据正则化方法计算距离
        if regularizer.regularization_method == 'kl':
            # KL散度
            distance = torch.nn.functional.kl_div(
                torch.log(pred_prob + 1e-10),
                gto_policy_tensor,
                reduction='sum'
            ).item()
        elif regularizer.regularization_method == 'js':
            # JS散度
            mean_policy = 0.5 * (pred_prob + gto_policy_tensor)
            kl_pred_mean = torch.nn.functional.kl_div(
                torch.log(pred_prob + 1e-10),
                mean_policy,
                reduction='sum'
            ).item()
            kl_gto_mean = torch.nn.functional.kl_div(
                torch.log(gto_policy_tensor + 1e-10),
                mean_policy,
                reduction='sum'
            ).item()
            distance = 0.5 * (kl_pred_mean + kl_gto_mean)
        elif regularizer.regularization_method == 'l2':
            # L2距离
            distance = torch.sum((pred_prob - gto_policy_tensor) ** 2).item()
        else:
            continue
        
        distances.append(distance)
    
    # 绘制距离分布直方图
    if distances:
        plt.figure(figsize=(10, 6))
        plt.hist(distances, bins=20, alpha=0.75, color='blue')
        plt.title(f'模型策略与GTO策略的{regularizer.regularization_method}距离分布')
        plt.xlabel('距离')
        plt.ylabel('频数')
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.tight_layout()
        plt.savefig(output_path)
        plt.close()


def visualize_gto_policy_heatmap(
    gto_policy,
    env,
    num_states: int = 10,
    output_path: str = None
) -> None:
    """
    可视化多个状态下的GTO策略分布热力图
    
    Args:
        gto_policy: GTO策略实例
        env: 游戏环境
        num_states: 要采样的状态数量
        output_path: 输出图像路径
    """
    # 生成多个不同状态
    states = []
    legal_actions_lists = []
    
    for _ in range(num_states):
        state = env.reset()
        # 随机前进几步
        steps = np.random.randint(1, 10)
        for _ in range(steps):
            legal_actions = env.get_legal_actions()
            if not legal_actions:
                break
            action = np.random.choice(legal_actions)
            state, _, done, _ = env.step(action)
            if done:
                break
        
        legal_actions = env.get_legal_actions()
        if legal_actions:
            states.append(state)
            legal_actions_lists.append(legal_actions)
    
    # 如果没有收集到足够的状态，则返回
    if len(states) < 2:
        return
    
    # 获取每个状态下的GTO策略
    policies = []
    for i, state in enumerate(states):
        legal_actions = legal_actions_lists[i]
        policy = gto_policy.get_policy(state, legal_actions)
        if policy is not None:
            # 只保留合法动作的概率
            legal_policy = {}
            for j, action in enumerate(legal_actions):
                if j < len(policy):
                    legal_policy[action] = policy[j]
            
            # 转换为相对排名
            ranked_actions = sorted(legal_policy.keys(), key=lambda a: legal_policy[a], reverse=True)
            rank_policy = {a: rank+1 for rank, a in enumerate(ranked_actions)}
            
            policies.append((legal_policy, rank_policy))
    
    # 如果没有有效的策略，则返回
    if not policies:
        return
    
    # 创建热力图
    max_actions = max([max(p[0].keys()) for p in policies if p[0]]) + 1
    policy_matrix = np.zeros((len(policies), max_actions))
    
    for i, (prob_policy, _) in enumerate(policies):
        for action, prob in prob_policy.items():
            if action < max_actions:
                policy_matrix[i, action] = prob
    
    # 绘制热力图
    plt.figure(figsize=(12, 8))
    plt.imshow(policy_matrix, cmap='viridis', aspect='auto')
    plt.colorbar(label='概率')
    plt.title('不同状态下的GTO策略分布热力图')
    plt.xlabel('动作ID')
    plt.ylabel('状态索引')
    plt.tight_layout()
    
    # 保存图像
    if output_path:
        plt.savefig(output_path)
        plt.close()
    else:
        plt.show() 