"""
RLlib兼容的斗地主环境包装器

将斗地主环境包装为RLlib兼容的环境，支持多智能体训练。
"""

import gym
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Union
from gym.spaces import Discrete, Box

from ray.rllib.env.multi_agent_env import MultiAgentEnv
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.games.doudizhu.state import DouDizhuState, GamePhase
from cardgame_ai.games.doudizhu.card_group import CardGroup
from cardgame_ai.games.doudizhu.action import BidAction, GrabAction


class RLlibDouDizhuEnv(MultiAgentEnv):
    """
    RLlib兼容的斗地主环境
    
    将斗地主环境包装为RLlib兼容的多智能体环境，支持分布式训练。
    """
    
    def __init__(self, config=None):
        """
        初始化环境
        
        Args:
            config: 环境配置
        """
        config = config or {}
        self.env = DouDizhuEnvironment(seed=config.get("seed", None))
        self.num_players = 3
        
        # 定义观察空间和动作空间
        self.observation_space = Box(
            low=0, high=1, shape=(656,), dtype=np.float32
        )
        
        # 动作空间包括所有可能的出牌组合、叫分动作和抢地主动作
        # 我们使用一个较大的离散空间来表示所有可能的动作
        # 实际上，合法动作会在每一步中动态确定
        self.action_space = Discrete(1000)  # 一个足够大的数字来覆盖所有可能的动作
        
        # 动作映射：将离散动作ID映射到实际的游戏动作
        self.action_map = self._create_action_map()
        
        # 反向动作映射：将实际的游戏动作映射到离散动作ID
        self.reverse_action_map = {str(v): k for k, v in self.action_map.items()}
        
        # 当前状态
        self.state = None
        
        # 当前合法动作
        self.current_legal_actions = []
        
        # 当前合法动作的离散ID
        self.current_legal_action_ids = []
        
    def _create_action_map(self) -> Dict[int, Union[CardGroup, BidAction, GrabAction]]:
        """
        创建动作映射
        
        将离散动作ID映射到实际的游戏动作
        
        Returns:
            Dict[int, Union[CardGroup, BidAction, GrabAction]]: 动作映射
        """
        action_map = {}
        action_id = 0
        
        # 添加叫分动作
        for bid_action in [BidAction.PASS, BidAction.BID_1, BidAction.BID_2, BidAction.BID_3]:
            action_map[action_id] = bid_action
            action_id += 1
            
        # 添加抢地主动作
        for grab_action in [GrabAction.PASS, GrabAction.GRAB]:
            action_map[action_id] = grab_action
            action_id += 1
            
        # 添加"不出"动作
        action_map[action_id] = CardGroup([])
        action_id += 1
        
        # 添加常见的出牌组合
        # 这里只是一个简化的示例，实际实现中需要添加所有可能的出牌组合
        # 单牌
        # 对子
        # 三张
        # 三带一
        # 三带二
        # 顺子
        # 连对
        # 飞机
        # 飞机带翅膀
        # 四带二
        # 炸弹
        # 王炸
        
        # 注意：这里的实现是简化的，实际上需要生成所有可能的出牌组合
        # 可以通过枚举所有可能的牌型来实现
        
        return action_map
        
    def reset(self) -> Dict[str, np.ndarray]:
        """
        重置环境
        
        Returns:
            Dict[str, np.ndarray]: 每个智能体的初始观察
        """
        self.state = self.env.reset()
        self.current_legal_actions = self.env.get_legal_actions(self.state)
        self.current_legal_action_ids = self._get_legal_action_ids(self.current_legal_actions)
        
        # 返回每个智能体的观察
        observations = {}
        for i in range(self.num_players):
            observations[i] = self.state.get_observation(i)
            
        return observations
        
    def step(self, action_dict: Dict[int, int]) -> Tuple[Dict[str, np.ndarray], Dict[str, float], Dict[str, bool], Dict[str, Dict]]:
        """
        执行动作
        
        Args:
            action_dict: 每个智能体的动作
            
        Returns:
            Tuple[Dict[str, np.ndarray], Dict[str, float], Dict[str, bool], Dict[str, Dict]]: 
                每个智能体的观察、奖励、是否结束和额外信息
        """
        # 获取当前玩家
        current_player = self.state.current_player
        
        # 获取当前玩家的动作
        action_id = action_dict[current_player]
        
        # 将离散动作ID转换为实际的游戏动作
        if action_id in self.action_map:
            action = self.action_map[action_id]
        else:
            # 如果动作ID无效，选择一个合法动作
            action_id = self.current_legal_action_ids[0] if self.current_legal_action_ids else 0
            action = self.action_map[action_id]
            
        # 执行动作
        next_state, reward, done, info = self.env.step(action)
        self.state = next_state
        
        # 更新合法动作
        self.current_legal_actions = self.env.get_legal_actions(self.state)
        self.current_legal_action_ids = self._get_legal_action_ids(self.current_legal_actions)
        
        # 构建每个智能体的观察、奖励和是否结束
        observations = {}
        rewards = {}
        dones = {}
        infos = {}
        
        for i in range(self.num_players):
            observations[i] = self.state.get_observation(i)
            
            # 如果游戏结束，根据角色分配奖励
            if done:
                if self.state.landlord is not None:
                    # 地主获胜
                    if len(self.state.hands[self.state.landlord]) == 0:
                        rewards[i] = 2.0 if i == self.state.landlord else -1.0
                    # 农民获胜
                    else:
                        rewards[i] = -2.0 if i == self.state.landlord else 1.0
                else:
                    # 游戏异常结束
                    rewards[i] = 0.0
            else:
                # 游戏进行中，使用环境返回的奖励
                rewards[i] = reward if i == current_player else 0.0
                
            dones[i] = done
            
            # 添加合法动作信息
            if i == self.state.current_player:
                infos[i] = {
                    "legal_actions": self.current_legal_action_ids,
                    "action_mask": self._get_action_mask()
                }
            else:
                infos[i] = {}
                
        # 添加全局done标志
        dones["__all__"] = done
        
        return observations, rewards, dones, infos
        
    def _get_legal_action_ids(self, legal_actions: List[Union[CardGroup, BidAction, GrabAction]]) -> List[int]:
        """
        获取合法动作的离散ID
        
        Args:
            legal_actions: 合法动作列表
            
        Returns:
            List[int]: 合法动作的离散ID列表
        """
        legal_action_ids = []
        for action in legal_actions:
            action_str = str(action)
            if action_str in self.reverse_action_map:
                legal_action_ids.append(self.reverse_action_map[action_str])
                
        return legal_action_ids
        
    def _get_action_mask(self) -> np.ndarray:
        """
        获取动作掩码
        
        Returns:
            np.ndarray: 动作掩码，1表示合法动作，0表示非法动作
        """
        mask = np.zeros(self.action_space.n, dtype=np.float32)
        for action_id in self.current_legal_action_ids:
            mask[action_id] = 1.0
            
        return mask
        
    def get_legal_actions(self) -> List[int]:
        """
        获取当前合法动作的离散ID
        
        Returns:
            List[int]: 合法动作的离散ID列表
        """
        return self.current_legal_action_ids
        
    def render(self, mode="human"):
        """
        渲染环境
        
        Args:
            mode: 渲染模式
            
        Returns:
            渲染结果
        """
        return self.env.render(mode)
        
    def close(self):
        """
        关闭环境
        """
        self.env.close()
