#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
实验运行脚本

运行不同配置的模型实验并比较性能，支持消融实验、基准比较等，
用于评估新功能(DeepBeliefTracker, OnlineOpponentModeler, RLHF)带来的性能提升。
"""

import os
import sys
import time
import json
import yaml
import logging
import argparse
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime
from pathlib import Path
from tqdm import tqdm

# 确保可以导入项目模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from cardgame_ai.utils.config import ConfigManager
from cardgame_ai.algorithms.efficient_zero import EfficientZero
from cardgame_ai.algorithms.belief_tracking.deep_belief_tracker import DeepBeliefTracker
from cardgame_ai.algorithms.opponent_modeling.online_modeler import OnlineOpponentModeler
from cardgame_ai.algorithms.random_agent import RandomAgent
from cardgame_ai.algorithms.rule_based import RuleBasedAgent
from cardgame_ai.core.environment import Environment
from cardgame_ai.core.evaluation import Evaluator
from cardgame_ai.games.doudizhu.env import DouDizhuEnv
from cardgame_ai.games.doudizhu.game import DouDizhuGame
from cardgame_ai.utils.logger import setup_logger


def load_config(config_path: str) -> Dict[str, Any]:
    """
    加载配置文件
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        Dict[str, Any]: 配置信息
    """
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
    config_manager = ConfigManager(config_path)
    return config_manager.config


def create_experiment_variants(config: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
    """
    创建实验变体
    
    基于基础配置和消融实验设置，创建不同的实验配置变体
    
    Args:
        config: 基础配置
        
    Returns:
        Dict[str, Dict[str, Any]]: 实验变体字典，键为变体名称，值为配置
    """
    variants = {}
    
    # 创建基础变体
    variants["base"] = config.copy()
    
    # 获取消融实验设置
    if "experiment" in config and "ablation_studies" in config["experiment"]:
        ablation_studies = config["experiment"]["ablation_studies"]
        
        for study in ablation_studies:
            name = study.get("name", "unnamed")
            variant_config = config.copy()
            
            # 应用消融实验配置修改
            for key, value in study.items():
                if key not in ["name", "description"]:
                    # 处理嵌套键
                    if "." in key:
                        parts = key.split(".")
                        current = variant_config
                        for part in parts[:-1]:
                            if part not in current:
                                current[part] = {}
                            current = current[part]
                        current[parts[-1]] = value
                    else:
                        variant_config[key] = value
            
            variants[name] = variant_config
    
    return variants


def create_agent(config: Dict[str, Any], agent_type: str, player_id: str = "0") -> Any:
    """
    创建智能体
    
    Args:
        config: 配置信息
        agent_type: 智能体类型
        player_id: 玩家ID
        
    Returns:
        Any: 创建的智能体
    """
    if agent_type == "efficient_zero":
        # 创建EfficientZero智能体
        agent = EfficientZero(
            state_shape=(108,),  # 假设状态是一个108维的向量
            action_shape=(27,),  # 假设有27种可能的动作
            hidden_dim=config["model"]["hidden_dim"],
            state_dim=config["model"]["state_dim"],
            use_resnet=config["model"]["model_type"] == "resnet",
            projection_dim=config["model"]["projection_dim"],
            prediction_dim=config["model"]["prediction_dim"],
            num_simulations=config["mcts"]["num_simulations"],
            discount=config["mcts"]["discount"],
            dirichlet_alpha=config["mcts"]["dirichlet_alpha"],
            exploration_fraction=config["mcts"]["exploration_fraction"],
            pb_c_base=config["mcts"]["pb_c_base"],
            pb_c_init=config["mcts"]["pb_c_init"],
            batch_size=config["training"]["batch_size"],
            num_unroll_steps=config["training"]["num_unroll_steps"],
            td_steps=config["training"]["td_steps"],
            value_loss_weight=config["training"]["value_loss_weight"],
            policy_loss_weight=config["training"]["policy_loss_weight"],
            consistency_loss_weight=config["training"]["consistency_loss_weight"],
            self_supervised_loss_weight=config["training"]["self_supervised_loss_weight"],
            learning_rate=config["training"]["learning_rate"],
            weight_decay=config["training"]["weight_decay"],
            use_distributional_value=config["model"].get("use_distributional_value", True),
            value_support_size=config["model"].get("value_support_size", 601),
            value_min=config["model"].get("value_min", -300),
            value_max=config["model"].get("value_max", 300),
            risk_alpha=config["mcts"].get("risk_alpha", 0.05),
            risk_beta=config["mcts"].get("risk_beta", 0.1),
            use_rlhf=config["training"].get("use_rlhf", False),
            rlhf_loss_weight=config["training"].get("rlhf_loss_weight", 0.5),
            rlhf_preference_weight=config["training"].get("rlhf_preference_weight", 1.0),
            rlhf_feedback_weight=config["training"].get("rlhf_feedback_weight", 1.0),
            rlhf_imitation_weight=config["training"].get("rlhf_imitation_weight", 1.0)
        )
        
        # 加载模型（如果需要）
        model_dir = "models/muzero_doudizhu"
        agent.load(f"{model_dir}/model.pt")
        
        # 配置信念追踪器（如果启用）
        if config["mcts"].get("use_deep_belief_tracker", False) and config["mcts"].get("deep_belief_tracker_enabled", False):
            belief_tracker = DeepBeliefTracker(
                player_id=player_id
            )
            # 加载预训练的信念追踪器（如果指定）
            belief_tracker_path = config["mcts"].get("deep_belief_tracker_path")
            if belief_tracker_path and os.path.exists(belief_tracker_path):
                belief_tracker.load_model(belief_tracker_path)
            
            # 将信念追踪器关联到智能体
            agent.belief_tracker = belief_tracker
        
        # 配置对手建模器（如果启用）
        if config["opponent_modeling"].get("enabled", False) and config["mcts"].get("opponent_modeler_enabled", False):
            opponent_modeler = OnlineOpponentModeler(
                window_size=config["opponent_modeling"].get("window_size", 10),
                decay_factor=config["opponent_modeling"].get("decay_factor", 0.95),
                enable_logging=config["opponent_modeling"].get("enable_logging", False)
            )
            
            # 将对手建模器关联到智能体
            agent.opponent_modeler = opponent_modeler
        
        return agent
    
    elif agent_type == "rule_based":
        # 创建基于规则的智能体
        return RuleBasedAgent()
    
    elif agent_type == "random":
        # 创建随机智能体
        return RandomAgent()
    
    else:
        raise ValueError(f"不支持的智能体类型: {agent_type}")


def run_evaluation(config: Dict[str, Any], variant_name: str, results_dir: str) -> Dict[str, Any]:
    """
    运行评估
    
    Args:
        config: 配置信息
        variant_name: 变体名称
        results_dir: 结果保存目录
        
    Returns:
        Dict[str, Any]: 评估结果
    """
    print(f"正在评估变体: {variant_name}")
    
    # 创建环境
    env = DouDizhuEnv()
    
    # 确定运行配置
    run_configs = [{"name": "standard", "num_games": 100, "opponent_types": ["rule_based", "random"]}]
    if "experiment" in config and "run_configs" in config["experiment"]:
        run_configs = config["experiment"]["run_configs"]
    
    all_results = []
    
    # 为每个运行配置创建并评估智能体
    for run_config in run_configs:
        run_name = run_config["name"]
        num_games = run_config["num_games"]
        opponent_types = run_config["opponent_types"]
        
        print(f"  运行配置: {run_name}, 游戏数: {num_games}, 对手类型: {opponent_types}")
        
        # 创建主智能体（EfficientZero）
        main_agent = create_agent(config, "efficient_zero", "0")
        
        # 对每种对手类型进行评估
        for opponent_type in opponent_types:
            print(f"    对手类型: {opponent_type}")
            
            # 创建对手智能体
            opponent1 = create_agent(config, opponent_type, "1")
            opponent2 = create_agent(config, opponent_type, "2")
            
            # 创建评估器
            evaluator = Evaluator(env)
            
            # 运行评估
            results = evaluator.evaluate(
                agents=[main_agent, opponent1, opponent2],
                num_episodes=num_games,
                render=False,
                verbose=True
            )
            
            # 记录结果
            result_entry = {
                "variant": variant_name,
                "run_config": run_name,
                "opponent_type": opponent_type,
                "win_rate": results["win_rate"],
                "avg_reward": results["avg_reward"],
                "avg_turns": results["avg_turns"],
                "completion_time": results["avg_time"],
                "num_games": num_games
            }
            
            all_results.append(result_entry)
            
            # 打印结果
            print(f"      胜率: {results['win_rate']:.2f}%")
            print(f"      平均奖励: {results['avg_reward']:.2f}")
            print(f"      平均回合数: {results['avg_turns']:.2f}")
            print(f"      平均完成时间: {results['avg_time']:.2f}秒")
    
    # 保存结果
    results_path = os.path.join(results_dir, f"{variant_name}_results.json")
    with open(results_path, "w", encoding="utf-8") as f:
        json.dump(all_results, f, indent=2, ensure_ascii=False)
    
    return all_results


def generate_reports(all_results: List[Dict[str, Any]], results_dir: str) -> None:
    """
    生成报告
    
    Args:
        all_results: 所有评估结果
        results_dir: 结果保存目录
    """
    # 转换为DataFrame
    df = pd.DataFrame(all_results)
    
    # 保存CSV
    csv_path = os.path.join(results_dir, "all_results.csv")
    df.to_csv(csv_path, index=False)
    
    # 创建图表目录
    charts_dir = os.path.join(results_dir, "charts")
    os.makedirs(charts_dir, exist_ok=True)
    
    # 设置可视化样式
    sns.set(style="whitegrid")
    plt.rcParams["figure.figsize"] = (12, 8)
    plt.rcParams["font.size"] = 12
    
    # 按对手类型分组生成胜率柱状图
    for opponent_type in df["opponent_type"].unique():
        opponent_df = df[df["opponent_type"] == opponent_type]
        
        plt.figure()
        ax = sns.barplot(x="variant", y="win_rate", data=opponent_df)
        
        plt.title(f"不同变体对阵 {opponent_type} 的胜率")
        plt.xlabel("模型变体")
        plt.ylabel("胜率 (%)")
        plt.xticks(rotation=45)
        
        # 添加数值标签
        for p in ax.patches:
            ax.annotate(f"{p.get_height():.1f}%", 
                        (p.get_x() + p.get_width() / 2., p.get_height()), 
                        ha='center', va='bottom', fontsize=10)
        
        plt.tight_layout()
        plt.savefig(os.path.join(charts_dir, f"win_rate_{opponent_type}.png"))
        plt.close()
    
    # 生成综合性能雷达图
    metrics = ["win_rate", "avg_reward", "avg_turns", "completion_time"]
    for opponent_type in df["opponent_type"].unique():
        opponent_df = df[df["opponent_type"] == opponent_type]
        
        # 对指标进行归一化
        normalized_df = opponent_df.copy()
        for metric in metrics:
            if metric in ["avg_turns", "completion_time"]:  # 这些指标越小越好
                max_val = normalized_df[metric].max()
                min_val = normalized_df[metric].min()
                if max_val != min_val:
                    normalized_df[metric] = 1 - (normalized_df[metric] - min_val) / (max_val - min_val)
                else:
                    normalized_df[metric] = 1.0
            else:  # 这些指标越大越好
                max_val = normalized_df[metric].max()
                min_val = normalized_df[metric].min()
                if max_val != min_val:
                    normalized_df[metric] = (normalized_df[metric] - min_val) / (max_val - min_val)
                else:
                    normalized_df[metric] = 1.0
        
        # 创建雷达图
        plt.figure(figsize=(8, 8))
        
        # 设置雷达图的角度
        N = len(metrics)
        angles = [n / float(N) * 2 * np.pi for n in range(N)]
        angles += angles[:1]  # 闭合雷达图
        
        # 初始化雷达图
        ax = plt.subplot(111, polar=True)
        
        # 添加每个变体的数据
        for variant in normalized_df["variant"].unique():
            variant_data = normalized_df[normalized_df["variant"] == variant]
            values = variant_data[metrics].values.flatten().tolist()
            values += values[:1]  # 闭合数据
            
            ax.plot(angles, values, linewidth=2, label=variant)
            ax.fill(angles, values, alpha=0.1)
        
        # 设置标签
        plt.xticks(angles[:-1], [metric.replace("_", " ").title() for metric in metrics])
        
        # 添加图例
        plt.legend(loc="upper right", bbox_to_anchor=(0.1, 0.1))
        
        plt.title(f"不同变体对阵 {opponent_type} 的性能雷达图")
        plt.tight_layout()
        plt.savefig(os.path.join(charts_dir, f"radar_{opponent_type}.png"))
        plt.close()
    
    # 生成消融实验对比柱状图
    plt.figure(figsize=(15, 10))
    
    # 计算每个变体在所有对手类型上的平均胜率
    variant_avg_win_rate = df.groupby("variant")["win_rate"].mean().reset_index()
    
    ax = sns.barplot(x="variant", y="win_rate", data=variant_avg_win_rate)
    
    plt.title("消融实验对比：各变体平均胜率")
    plt.xlabel("模型变体")
    plt.ylabel("平均胜率 (%)")
    plt.xticks(rotation=45)
    
    # 添加数值标签
    for p in ax.patches:
        ax.annotate(f"{p.get_height():.1f}%", 
                    (p.get_x() + p.get_width() / 2., p.get_height()), 
                    ha='center', va='bottom', fontsize=10)
    
    plt.tight_layout()
    plt.savefig(os.path.join(charts_dir, "ablation_study_win_rate.png"))
    plt.close()
    
    # 生成HTML报告
    html_report = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>实验结果报告</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            h1, h2, h3 {{ color: #333; }}
            table {{ border-collapse: collapse; width: 100%; margin-bottom: 20px; }}
            th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            th {{ background-color: #f2f2f2; }}
            tr:nth-child(even) {{ background-color: #f9f9f9; }}
            .chart-container {{ margin: 20px 0; }}
            .chart {{ max-width: 100%; height: auto; }}
        </style>
    </head>
    <body>
        <h1>实验结果报告</h1>
        <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        
        <h2>结果摘要</h2>
        <table>
            <tr>
                <th>变体</th>
                <th>平均胜率 (%)</th>
                <th>平均奖励</th>
                <th>平均回合数</th>
                <th>平均完成时间 (秒)</th>
            </tr>
    """
    
    # 添加结果摘要
    summary = df.groupby("variant").agg({
        "win_rate": "mean",
        "avg_reward": "mean",
        "avg_turns": "mean",
        "completion_time": "mean"
    }).reset_index()
    
    for _, row in summary.iterrows():
        html_report += f"""
            <tr>
                <td>{row['variant']}</td>
                <td>{row['win_rate']:.2f}</td>
                <td>{row['avg_reward']:.2f}</td>
                <td>{row['avg_turns']:.2f}</td>
                <td>{row['completion_time']:.2f}</td>
            </tr>
        """
    
    html_report += """
        </table>
        
        <h2>详细结果</h2>
    """
    
    # 添加对手类型分组结果
    for opponent_type in df["opponent_type"].unique():
        html_report += f"""
        <h3>对阵 {opponent_type}</h3>
        <table>
            <tr>
                <th>变体</th>
                <th>胜率 (%)</th>
                <th>平均奖励</th>
                <th>平均回合数</th>
                <th>平均完成时间 (秒)</th>
            </tr>
        """
        
        opponent_df = df[df["opponent_type"] == opponent_type]
        for _, row in opponent_df.iterrows():
            html_report += f"""
                <tr>
                    <td>{row['variant']}</td>
                    <td>{row['win_rate']:.2f}</td>
                    <td>{row['avg_reward']:.2f}</td>
                    <td>{row['avg_turns']:.2f}</td>
                    <td>{row['completion_time']:.2f}</td>
                </tr>
            """
        
        html_report += """
        </table>
        """
    
    # 添加图表
    html_report += """
        <h2>可视化结果</h2>
    """
    
    # 添加胜率柱状图
    for opponent_type in df["opponent_type"].unique():
        html_report += f"""
        <div class="chart-container">
            <h3>对阵 {opponent_type} 的胜率</h3>
            <img class="chart" src="charts/win_rate_{opponent_type}.png" alt="胜率柱状图">
        </div>
        """
    
    # 添加雷达图
    for opponent_type in df["opponent_type"].unique():
        html_report += f"""
        <div class="chart-container">
            <h3>对阵 {opponent_type} 的性能雷达图</h3>
            <img class="chart" src="charts/radar_{opponent_type}.png" alt="性能雷达图">
        </div>
        """
    
    # 添加消融实验对比图
    html_report += """
        <div class="chart-container">
            <h3>消融实验对比：各变体平均胜率</h3>
            <img class="chart" src="charts/ablation_study_win_rate.png" alt="消融实验对比">
        </div>
    """
    
    # 添加结论部分
    html_report += """
        <h2>结论</h2>
        <p>
            通过对比不同变体的表现，可以得出以下结论：
        </p>
        <ul>
    """
    
    # 计算功能贡献
    base_win_rate = summary[summary["variant"] == "base"]["win_rate"].values[0]
    
    for _, row in summary.iterrows():
        if row["variant"] != "base" and row["variant"] != "full_model":
            feature_name = row["variant"].replace("no_", "")
            win_rate_diff = base_win_rate - row["win_rate"]
            
            if win_rate_diff > 0:
                html_report += f"""
                <li>
                    <strong>{feature_name}</strong> 功能显著提升了模型性能，
                    禁用该功能后胜率下降了 {abs(win_rate_diff):.2f}%。
                </li>
                """
            else:
                html_report += f"""
                <li>
                    <strong>{feature_name}</strong> 功能对模型性能的影响不明显或者略有负面影响，
                    禁用该功能后胜率提高了 {abs(win_rate_diff):.2f}%，可能需要进一步调整。
                </li>
                """
    
    # 添加全功能模型的结论
    if "full_model" in summary["variant"].values:
        full_model_win_rate = summary[summary["variant"] == "full_model"]["win_rate"].values[0]
        win_rate_diff = full_model_win_rate - base_win_rate
        
        if win_rate_diff > 0:
            html_report += f"""
            <li>
                <strong>完整模型</strong>（包含所有新功能）相比基础模型提升了 {win_rate_diff:.2f}% 的胜率，
                证明了新功能的综合价值。
            </li>
            """
        else:
            html_report += f"""
            <li>
                <strong>完整模型</strong>（包含所有新功能）相比基础模型降低了 {abs(win_rate_diff):.2f}% 的胜率，
                这表明不同功能之间可能存在干扰，需要进一步优化集成方式。
            </li>
            """
    
    html_report += """
        </ul>
    </body>
    </html>
    """
    
    # 保存HTML报告
    html_path = os.path.join(results_dir, "report.html")
    with open(html_path, "w", encoding="utf-8") as f:
        f.write(html_report)
    
    print(f"报告已生成: {html_path}")


def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="运行模型实验")
    parser.add_argument("--config", type=str, default="configs/doudizhu/efficient_zero_config.yaml",
                        help="配置文件路径")
    parser.add_argument("--results-dir", type=str, default="results/experiments",
                        help="结果保存目录")
    parser.add_argument("--variants", type=str, nargs="+", default=None,
                        help="要评估的变体列表，如果为空则评估所有变体")
    parser.add_argument("--seed", type=int, default=42,
                        help="随机种子")
    args = parser.parse_args()
    
    # 设置随机种子
    np.random.seed(args.seed)
    
    # 设置日志
    setup_logger(level=logging.INFO)
    
    # 创建结果目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_dir = os.path.join(args.results_dir, f"experiment_{timestamp}")
    os.makedirs(results_dir, exist_ok=True)
    
    # 加载配置
    config = load_config(args.config)
    
    # 保存使用的配置
    config_path = os.path.join(results_dir, "config.yaml")
    with open(config_path, "w", encoding="utf-8") as f:
        yaml.dump(config, f, default_flow_style=False)
    
    # 创建实验变体
    variants = create_experiment_variants(config)
    
    # 过滤变体
    if args.variants:
        variants = {k: v for k, v in variants.items() if k in args.variants}
    
    if not variants:
        print("没有找到要评估的变体")
        return
    
    print(f"找到 {len(variants)} 个要评估的变体: {', '.join(variants.keys())}")
    
    # 运行评估
    all_results = []
    for variant_name, variant_config in variants.items():
        variant_results = run_evaluation(variant_config, variant_name, results_dir)
        all_results.extend(variant_results)
    
    # 生成报告
    generate_reports(all_results, results_dir)
    
    print(f"实验完成，结果保存在 {results_dir}")


if __name__ == "__main__":
    main() 