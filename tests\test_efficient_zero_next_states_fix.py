"""
测试EfficientZero算法中next_states为None时的错误修复

该测试验证当batch数据中next_states为None时，
算法能够正确抛出ValueError而不是静默崩溃。
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import numpy as np
from cardgame_ai.algorithms.efficient_zero_algorithm import EfficientZero


class TestEfficientZeroNextStatesFix:
    """测试EfficientZero算法的next_states修复"""
    
    def setup_method(self):
        """设置测试环境"""
        # 创建简单的EfficientZero实例用于测试
        self.state_shape = (84, 84, 3)
        self.action_shape = (10,)
        
        self.algorithm = EfficientZero(
            state_shape=self.state_shape,
            action_shape=self.action_shape,
            hidden_dim=64,
            state_dim=32,
            batch_size=4,
            num_unroll_steps=3,
            device='cpu'
        )
    
    def test_next_states_none_raises_error(self):
        """测试当next_states为None时抛出ValueError"""
        # 创建包含None next_states的batch数据
        batch_size = 4
        batch = {
            'observations': torch.randn(batch_size, *self.state_shape),
            'actions': torch.randint(0, self.action_shape[0], (batch_size,)),
            'rewards': torch.randn(batch_size),
            'dones': torch.zeros(batch_size),
            'next_states': None,  # 这里设置为None，应该触发错误
            'target_policies': None,
            'target_values': None
        }

        # 验证抛出正确的错误
        try:
            self.algorithm.train(batch)
            raise AssertionError("应该抛出ValueError，但没有抛出任何错误")
        except ValueError as e:
            if "next_states required to compute target values" not in str(e):
                raise AssertionError(f"抛出了ValueError，但错误消息不正确: {e}")
        except Exception as e:
            raise AssertionError(f"抛出了错误的异常类型: {type(e).__name__}: {e}")
    
    def test_next_states_valid_no_error(self):
        """测试当next_states有效时不抛出错误"""
        # 创建包含有效next_states的batch数据
        batch_size = 4
        batch = {
            'observations': torch.randn(batch_size, *self.state_shape),
            'actions': torch.randint(0, self.action_shape[0], (batch_size,)),
            'rewards': torch.randn(batch_size),
            'dones': torch.zeros(batch_size),
            'next_states': torch.randn(batch_size, *self.state_shape),  # 有效的next_states
            'target_policies': None,
            'target_values': None
        }
        
        # 这应该不会抛出错误（虽然可能因为其他原因失败，但不应该是next_states的问题）
        try:
            result = self.algorithm.train(batch)
            # 如果成功，result应该是一个包含损失的字典
            assert isinstance(result, dict)
            assert 'total_loss' in result
        except Exception as e:
            # 如果失败，确保不是因为next_states为None的问题
            assert "next_states required to compute target values" not in str(e)
    
    def test_batch_with_target_values_no_error(self):
        """测试当提供target_values时，即使next_states为None也不会出错"""
        # 创建包含target_values的batch数据
        batch_size = 4
        num_unroll_steps = 3
        batch = {
            'observations': torch.randn(batch_size, *self.state_shape),
            'actions': torch.randint(0, self.action_shape[0], (batch_size,)),
            'rewards': torch.randn(batch_size),
            'dones': torch.zeros(batch_size),
            'next_states': None,  # None，但有target_values
            'target_policies': torch.randn(batch_size, num_unroll_steps, self.action_shape[0]),
            'target_values': torch.randn(batch_size, num_unroll_steps)
        }
        
        # 这应该不会抛出next_states相关的错误，因为已经提供了target_values
        try:
            result = self.algorithm.train(batch)
            # 如果成功，result应该是一个包含损失的字典
            assert isinstance(result, dict)
            assert 'total_loss' in result
        except Exception as e:
            # 如果失败，确保不是因为next_states为None的问题
            assert "next_states required to compute target values" not in str(e)


if __name__ == "__main__":
    # 运行测试
    test_instance = TestEfficientZeroNextStatesFix()
    test_instance.setup_method()
    
    print("测试1: next_states为None时应该抛出错误...")
    try:
        test_instance.test_next_states_none_raises_error()
        print("✓ 测试1通过：正确抛出ValueError")
    except Exception as e:
        print(f"✗ 测试1失败：{e}")
    
    print("\n测试2: next_states有效时不应该抛出next_states相关错误...")
    try:
        test_instance.test_next_states_valid_no_error()
        print("✓ 测试2通过：没有抛出next_states相关错误")
    except Exception as e:
        print(f"✗ 测试2失败：{e}")
    
    print("\n测试3: 提供target_values时，next_states为None不应该出错...")
    try:
        test_instance.test_batch_with_target_values_no_error()
        print("✓ 测试3通过：没有抛出next_states相关错误")
    except Exception as e:
        print(f"✗ 测试3失败：{e}")
    
    print("\n所有测试完成！")
